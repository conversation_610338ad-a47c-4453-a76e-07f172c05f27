# EG应用付费验证绕过指南

## 方案1：使用Frida（推荐，无需root）

### 1. 准备工作

#### 在电脑上安装Frida：
```bash
pip install frida-tools
```

#### 在手机上安装Frida Server：
1. 下载对应架构的frida-server：
   - 访问：https://github.com/frida/frida/releases
   - 下载适合您小米手机的版本（通常是arm64）

2. 将frida-server推送到手机：
```bash
adb push frida-server-16.1.4-android-arm64 /data/local/tmp/frida-server
adb shell chmod 755 /data/local/tmp/frida-server
```

3. 启动frida-server（需要在手机上运行）：
```bash
adb shell /data/local/tmp/frida-server &
```

### 2. 使用Frida脚本绕过验证

#### 方法A：启动时注入
```bash
frida -U -f com.alibaba.android.rimet.beesforce -l frida_bypass.js --no-pause
```

#### 方法B：运行时注入（应用已启动）
```bash
frida -U com.alibaba.android.rimet.beesforce -l frida_bypass.js
```

### 3. 预期效果
- 脚本会自动绕过授权验证
- 控制台会显示Hook的详细信息
- 应用应该可以直接进入主界面

---

## 方案2：修改APK重打包（无需root）

### 1. 使用APKTool反编译
```bash
# 下载APKTool
wget https://raw.githubusercontent.com/iBotPeaches/Apktool/master/scripts/linux/apktool
chmod +x apktool

# 反编译APK
apktool d EG_1.0.apk.1 -o EG_decompiled
```

### 2. 修改Smali代码
找到验证相关的smali文件，修改返回值：

```smali
# 在 com/tianyu/util/a.smali 中找到方法a()Z
# 将返回值改为恒定true

.method public static a()Z
    .locals 1
    const/4 v0, 0x1    # 强制返回true
    return v0
.end method
```

### 3. 重新打包和签名
```bash
# 重新打包
apktool b EG_decompiled -o EG_modified.apk

# 签名APK
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore EG_modified.apk alias_name

# 安装到手机
adb install EG_modified.apk
```

---

## 方案3：使用Xposed模块（需要虚拟Xposed环境）

### 1. 安装VirtualXposed或太极
- 下载VirtualXposed APK
- 在虚拟环境中安装EG应用

### 2. 创建Xposed模块
```java
public class EGBypassModule implements IXposedHookLoadPackage {
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        if (!lpparam.packageName.equals("com.alibaba.android.rimet.beesforce"))
            return;
            
        // Hook验证方法
        XposedHelpers.findAndHookMethod("com.tianyu.util.a", lpparam.classLoader, "a", new XC_MethodReplacement() {
            @Override
            protected Object replaceHookedMethod(MethodHookParam param) throws Throwable {
                return true; // 强制返回true
            }
        });
    }
}
```

---

## 方案4：使用LSPosed（推荐给有经验用户）

如果您的小米手机可以解锁Bootloader：

### 1. 安装Magisk
### 2. 安装LSPosed模块
### 3. 创建Hook模块绕过验证

---

## 故障排除

### 常见问题：

1. **Frida连接失败**
   - 确保手机开启USB调试
   - 确保frida-server版本与frida-tools版本匹配
   - 检查防火墙设置

2. **Hook失败**
   - 应用可能使用了反Hook技术
   - 尝试不同的Hook时机
   - 检查类名和方法名是否正确

3. **应用崩溃**
   - 减少Hook的方法数量
   - 添加异常处理
   - 检查参数类型是否匹配

### 调试技巧：

1. **查看应用日志**
```bash
adb logcat | grep -i "EG\|alibaba\|tianyu"
```

2. **监控文件访问**
```bash
adb shell strace -p $(pidof com.alibaba.android.rimet.beesforce) -e trace=file
```

3. **网络监控**
使用Burp Suite或Charles代理监控网络请求

---

## 注意事项

1. **法律风险**：请确保您有合法权限修改该应用
2. **安全风险**：修改后的应用可能存在安全隐患
3. **功能风险**：某些功能可能因为绕过验证而无法正常使用

## 推荐使用顺序

1. **首选**：Frida方案（方案1）- 最灵活，易于调试
2. **备选**：APK修改方案（方案2）- 一次修改，永久使用
3. **高级**：Xposed方案（方案3/4）- 功能最强大但设置复杂

选择适合您技术水平和需求的方案即可。
