@echo off
chcp 65001
echo ================================
echo EG应用付费验证绕过自动化脚本
echo ================================
echo.

:: 检查ADB连接
echo [1/6] 检查ADB连接...
adb devices
if %errorlevel% neq 0 (
    echo 错误：ADB未找到或设备未连接
    echo 请确保：
    echo 1. 安装了Android SDK Platform Tools
    echo 2. 手机开启了USB调试
    echo 3. 手机已通过USB连接到电脑
    pause
    exit /b 1
)

:: 检查设备连接
for /f "tokens=1" %%i in ('adb devices ^| findstr /v "List"') do (
    if "%%i" neq "" (
        set DEVICE_FOUND=1
        goto :device_check_done
    )
)
:device_check_done

if not defined DEVICE_FOUND (
    echo 错误：未找到连接的设备
    echo 请确保手机已连接并授权USB调试
    pause
    exit /b 1
)

echo ✓ 设备连接正常

:: 检查应用是否已安装
echo.
echo [2/6] 检查目标应用...
adb shell pm list packages | findstr "com.alibaba.android.rimet.beesforce" >nul
if %errorlevel% equ 0 (
    echo ✓ 目标应用已安装
) else (
    echo ! 目标应用未安装，正在安装...
    if exist "EG_1.0.apk.1" (
        adb install "EG_1.0.apk.1"
        if %errorlevel% neq 0 (
            echo 错误：应用安装失败
            pause
            exit /b 1
        )
        echo ✓ 应用安装成功
    ) else (
        echo 错误：未找到APK文件 EG_1.0.apk.1
        pause
        exit /b 1
    )
)

:: 检查Python和Frida
echo.
echo [3/6] 检查Frida环境...
python -c "import frida" 2>nul
if %errorlevel% neq 0 (
    echo ! Frida未安装，正在安装...
    pip install frida-tools
    if %errorlevel% neq 0 (
        echo 错误：Frida安装失败
        echo 请手动运行：pip install frida-tools
        pause
        exit /b 1
    )
)
echo ✓ Frida环境正常

:: 获取设备架构
echo.
echo [4/6] 检查设备架构...
for /f "tokens=*" %%i in ('adb shell getprop ro.product.cpu.abi') do set ARCH=%%i
echo 设备架构：%ARCH%

:: 下载并部署frida-server
echo.
echo [5/6] 部署Frida Server...

:: 检查frida-server是否已存在
adb shell "test -f /data/local/tmp/frida-server && echo exists" | findstr "exists" >nul
if %errorlevel% equ 0 (
    echo ✓ Frida Server已存在
) else (
    echo ! 正在下载Frida Server...
    
    :: 根据架构选择下载链接
    if "%ARCH%"=="arm64-v8a" (
        set FRIDA_ARCH=arm64
    ) else if "%ARCH%"=="armeabi-v7a" (
        set FRIDA_ARCH=arm
    ) else (
        set FRIDA_ARCH=arm64
    )
    
    echo 下载架构：%FRIDA_ARCH%
    
    :: 这里需要手动下载frida-server，因为批处理下载比较复杂
    echo.
    echo 请手动完成以下步骤：
    echo 1. 访问：https://github.com/frida/frida/releases
    echo 2. 下载：frida-server-*-android-%FRIDA_ARCH%
    echo 3. 将文件重命名为：frida-server
    echo 4. 放置在当前目录
    echo.
    
    if exist "frida-server" (
        echo ✓ 找到frida-server文件
        adb push frida-server /data/local/tmp/frida-server
        adb shell chmod 755 /data/local/tmp/frida-server
        echo ✓ Frida Server部署完成
    ) else (
        echo 错误：未找到frida-server文件
        echo 请按照上述步骤下载frida-server
        pause
        exit /b 1
    )
)

:: 启动frida-server
echo.
echo [6/6] 启动Frida Server...
adb shell "pkill frida-server" 2>nul
adb shell "/data/local/tmp/frida-server &" 

:: 等待frida-server启动
timeout /t 3 /nobreak >nul

:: 验证frida-server是否运行
adb shell "ps | grep frida-server" | findstr "frida-server" >nul
if %errorlevel% equ 0 (
    echo ✓ Frida Server启动成功
) else (
    echo ! Frida Server可能启动失败，但继续尝试...
)

:: 运行Frida脚本
echo.
echo ================================
echo 开始执行Hook脚本...
echo ================================
echo.
echo 选择执行方式：
echo 1. 重新启动应用并注入（推荐）
echo 2. 对运行中的应用注入
echo.
set /p choice=请选择 (1 或 2): 

if "%choice%"=="1" (
    echo.
    echo 正在重新启动应用并注入Hook...
    frida -U -f com.alibaba.android.rimet.beesforce -l frida_bypass.js --no-pause
) else if "%choice%"=="2" (
    echo.
    echo 正在对运行中的应用注入Hook...
    frida -U com.alibaba.android.rimet.beesforce -l frida_bypass.js
) else (
    echo 无效选择，使用默认方式（重新启动应用）
    frida -U -f com.alibaba.android.rimet.beesforce -l frida_bypass.js --no-pause
)

echo.
echo ================================
echo 脚本执行完成
echo ================================
echo.
echo 如果Hook成功，您应该能看到：
echo - Hook成功的日志信息
echo - 应用直接进入主界面（跳过验证）
echo.
echo 如果遇到问题，请检查：
echo 1. 手机是否保持连接
echo 2. 应用是否正确安装
echo 3. Frida Server是否正常运行
echo.
pause
