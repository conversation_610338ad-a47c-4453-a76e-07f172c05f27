package de.robv.android.xposed.callbacks;

import android.content.pm.ApplicationInfo;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.callbacks.XCallback;

/* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
/* loaded from: assets/pine_xposed_api.dex */
public abstract class XC_LoadPackage extends XCallback implements IXposedHookLoadPackage {
    public XC_LoadPackage() {
    }

    public XC_LoadPackage(int i) {
        super(i);
    }

    /* JADX WARN: Classes with same name are omitted:
  assets/aliu_xposed_api.dex
 */
    /* loaded from: assets/pine_xposed_api.dex */
    public static final class LoadPackageParam extends XCallback.Param {
        public ApplicationInfo appInfo;
        public ClassLoader classLoader;
        public boolean isFirstApplication;
        public String packageName;
        public String processName;

        public LoadPackageParam(XposedBridge.CopyOnWriteSortedSet<XC_LoadPackage> copyOnWriteSortedSet) {
            super(copyOnWriteSortedSet);
        }
    }

    @Override // de.robv.android.xposed.callbacks.XCallback
    protected void call(XCallback.Param param) throws Throwable {
        if (param instanceof LoadPackageParam) {
            handleLoadPackage((LoadPackageParam) param);
        }
    }
}
