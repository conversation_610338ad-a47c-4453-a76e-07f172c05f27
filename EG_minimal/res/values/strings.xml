<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="ak">fb7YwlKY3ntx8hBDGMGLqX2FhlPkDMvv</string>
    <string name="androidx_startup">androidx.startup</string>
    <string name="app_agreement">用户协议</string>
    <string name="app_agreement_content">"1. 本软件专为学习 Android 开发使用，不会收集任何用户数据。 严禁利用本软件侵犯他人隐私权或者用于游戏牟利，如软件使用者不能遵守此规定， 请立即删除。 对于因用户使用本软件而造成自身或他人隐私泄露等任何不良后果，均由用户自行承担，软件作者不负任何责任。
 2. 用户不得对本软件产品进行反向工程（reverse engineer）、反向编译（decompile）或反汇编（disassemble）， 违者属于侵权行为，并自行承担由此产生的不利后果。
 3. 软件保证不含任何病毒，木马，等破坏用户数据的恶意代码，但是由于本软件产品可以通过网络等途径下载、传播，对于从非软件作者指定站点下载的本软件产品软件作者无法保证该软件是否感染计算机病毒、是否隐藏有伪装的特洛伊木马程序或者黑客软件，不承担由此引起的直接和间接损害责任。
 4. 软件会不断更新，以便及时为用户提供新功能和修正软件中的BUG。 同时软件作者保证本软件在升级过程中也不含有任何旨在破坏用户计算机数据的恶意代码。
 5. 由于用户计算机软硬件环境的差异性和复杂性，本软件所提供的各项功能并不能保证在任何情况下都能正常执行或达到用户所期望的结果。 用户使用本软件所产生的一切后果，软件作者不承担任何责任。
 6. 如果用户自行安装本软件，即表明用户信任软件作者，自愿选择安装本软件，并接受本协议所有条款。 如果用户不接受本协议，请立即删除。
"</string>
    <string name="app_agreement_privacy">已阅读《用户协议》和《隐私政策》</string>
    <string name="app_author">ZCShou</string>
    <string name="app_btn_agree">同意并继续</string>
    <string name="app_btn_disagree">不同意</string>
    <string name="app_email"><EMAIL></string>
    <string name="app_error_agreement">"必须先接受
《用户协议》和《隐私政策》"</string>
    <string name="app_error_code">注册码不能为空</string>
    <string name="app_error_dev">无法跳转到开发者选项，请先确保已开启开发者模式</string>
    <string name="app_error_gps">定位不可用，请检查 GPS 是否开启</string>
    <string name="app_error_input">输入有误，请检查输入是否合法</string>
    <string name="app_error_input_null">输入不能为空，请重新输入内容</string>
    <string name="app_error_latitude">"纬度超出限制!
取值：-90.0 ~ 90.0"</string>
    <string name="app_error_location">请先选择位置</string>
    <string name="app_error_longitude">"经度超出限制!
取值：-180.0 ~ 180.0"</string>
    <string name="app_error_network">网络不可用，请检查网络连接</string>
    <string name="app_error_param">非法参数</string>
    <string name="app_error_permission">权限不足，请授予相关权限</string>
    <string name="app_error_protocol">请先阅读并接收用户协议</string>
    <string name="app_error_read">"请依次阅读并接受
《用户协议》和《隐私政策》"</string>
    <string name="app_error_search">查找失败，请检查网络或定位</string>
    <string name="app_error_username">用户名不合法</string>
    <string name="app_history">历史定位</string>
    <string name="app_location_copy">位置已复制到剪切板</string>
    <string name="app_location_ok">位置已传送</string>
    <string name="app_location_save">位置已保存</string>
    <string name="app_name">EG</string>
    <string name="app_privacy">隐私政策</string>
    <string name="app_privacy_content">"1. 本软件专为学习 Android 开发使用，不会收集任何用户数据。
 2. 用户不得对本软件产品进行反向工程（reverse engineer）、反向编译（decompile）或反汇编（disassemble）， 违者属于侵权行为，并自行承担由此产生的不利后果。
 3. 软件保证不含任何病毒，木马，等破坏用户数据的恶意代码，但是由于本软件产品可以通过网络等途径下载、传播，对于从非软件作者指定站点下载的本软件产品软件作者无法保证该软件是否感染计算机病毒、是否隐藏有伪装的特洛伊木马程序或者黑客软件，不承担由此引起的直接和间接损害责任。
 4. 软件会不断更新，以便及时为用户提供新功能和修正软件中的BUG。 同时软件作者保证本软件在升级过程中也不含有任何旨在破坏用户计算机数据的恶意代码。
 5. 由于用户计算机软硬件环境的差异性和复杂性，本软件所提供的各项功能并不能保证在任何情况下都能正常执行或达到用户所期望的结果。 用户使用本软件所产生的一切后果，软件作者不承担任何责任。
 6. 如果用户自行安装本软件，即表明用户信任软件作者，自愿选择安装本软件，并接受本协议所有条款。 如果用户不接受本协议，请立即删除。
"</string>
    <string name="app_search_null">没有匹配结果，请重新搜索</string>
    <string name="app_search_tips">请输入查找内容</string>
    <string name="app_service_tips">EG 服务正在运行中</string>
    <string name="app_settings">设置</string>
    <string name="app_statement">仅供学习使用，否则后果自负！</string>
    <string name="appbar_scrolling_view_behavior">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string>
    <string name="bottom_sheet_behavior">com.google.android.material.bottomsheet.BottomSheetBehavior</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern">%1$d/%2$d</string>
    <string name="chip_text">Chip text</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="copy">Copy</string>
    <string name="cur_position">返回当前位置</string>
    <string name="error_icon_content_description">Error</string>
    <string name="expand_button_title">Advanced</string>
    <string name="exposed_dropdown_menu_content_description">Show dropdown menu</string>
    <string name="fab_transformation_scrim_behavior">com.google.android.material.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior">com.google.android.material.transformation.FabTransformationSheetBehavior</string>
    <string name="hide_bottom_view_on_scroll_behavior">com.google.android.material.behavior.HideBottomViewOnScrollBehavior</string>
    <string name="history_delete_error">删除记录失败</string>
    <string name="history_delete_ok">删除成功！</string>
    <string name="history_error_location">定位失败，请手动选取定位点</string>
    <string name="history_error_search">未搜索到指定内容</string>
    <string name="history_expiration">7</string>
    <string name="history_idle">暂无历史记录</string>
    <string name="history_location_default_name">地图位置</string>
    <string name="icon_content_description">Dialog Icon</string>
    <string name="input_button">输入位置</string>
    <string name="input_position_baidu">BD09 坐标系</string>
    <string name="input_position_cancel">取消</string>
    <string name="input_position_gps">GPS 坐标系</string>
    <string name="input_position_ok">传送</string>
    <string name="item_view_role_description">Tab</string>
    <string name="joystick_bike">自行车</string>
    <string name="joystick_history">历史记录</string>
    <string name="joystick_history_tips">搜索或点击历史位置</string>
    <string name="joystick_map">地图</string>
    <string name="joystick_map_tips">搜索或点击地图位置</string>
    <string name="joystick_move">移动</string>
    <string name="joystick_run">跑步</string>
    <string name="joystick_walk">步行</string>
    <string name="label_latitude">纬度</string>
    <string name="label_longitude">经度</string>
    <string name="map_pic_normal">普通图</string>
    <string name="map_pic_sate">卫星图</string>
    <string name="material_slider_range_end">Range end,</string>
    <string name="material_slider_range_start">Range start,</string>
    <string name="mtrl_badge_numberless_content_description">New notification</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="mtrl_exceed_max_badge_number_content_description">More than %1$d new notifications</string>
    <string name="mtrl_exceed_max_badge_number_suffix">%1$d%2$s</string>
    <string name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string name="mtrl_picker_cancel">@android:string/cancel</string>
    <string name="mtrl_picker_confirm">@android:string/ok</string>
    <string name="mtrl_picker_date_header_selected">%1$s</string>
    <string name="mtrl_picker_date_header_title">Select a Date</string>
    <string name="mtrl_picker_date_header_unselected">Selected date</string>
    <string name="mtrl_picker_day_of_week_column_header">Column of days: %1$s</string>
    <string name="mtrl_picker_invalid_format">Invalid format.</string>
    <string name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string name="mtrl_picker_invalid_range">Invalid range.</string>
    <string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string name="mtrl_picker_range_header_title">Select a Date Range</string>
    <string name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string name="mtrl_picker_save">Save</string>
    <string name="mtrl_picker_text_input_date_hint">Date</string>
    <string name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string name="mtrl_picker_text_input_day_abbr">d</string>
    <string name="mtrl_picker_text_input_month_abbr">m</string>
    <string name="mtrl_picker_text_input_year_abbr">y</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tap to switch to selecting a day</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tap to switch to selecting a year</string>
    <string name="nav_drawer_close">关闭导航菜单</string>
    <string name="nav_drawer_open">打开导航菜单</string>
    <string name="nav_menu_contact">联系作者</string>
    <string name="nav_menu_dev">开发人员选项</string>
    <string name="nav_menu_feedback">问题反馈</string>
    <string name="nav_menu_history">历史记录</string>
    <string name="nav_menu_more">更多</string>
    <string name="nav_menu_settings">设置</string>
    <string name="nav_menu_upgrade">检测更新</string>
    <string name="nav_user_limit_info">仅供学习使用，否则后果自负!</string>
    <string name="nav_user_name">EG</string>
    <string name="not_set">Not set</string>
    <string name="note_hide">隐藏摇杆</string>
    <string name="note_show">显示摇杆</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="preference_copied">\"%1$s\" copied to clipboard.</string>
    <string name="register_cancel">取消</string>
    <string name="register_check">我已阅读免责声明，并同意所有条款</string>
    <string name="register_limit">有效期</string>
    <string name="register_ok">注册</string>
    <string name="register_response">注册码</string>
    <string name="register_tips">长按复制以上代码发送给开发者，然后在下面填写获取到的注册码</string>
    <string name="register_title">离线注册</string>
    <string name="register_user_name">用户名</string>
    <string name="safecode">32:2F:E6:05:6C:2F:11:6D:CD:ED:AC:FF:23:02:5A:29:68:FD:45:D2;com.zcshou.gogogo</string>
    <string name="search_menu_title">Search</string>
    <string name="setting_author">开发者</string>
    <string name="setting_bike">驾驶速度(米/秒)</string>
    <string name="setting_bike_default">10.0</string>
    <string name="setting_current_value">"当前值: "</string>
    <string name="setting_group_about">关于</string>
    <string name="setting_group_log">记录</string>
    <string name="setting_group_move">移动</string>
    <string name="setting_group_sys">系统</string>
    <string name="setting_joystick">摇杆类型</string>
    <string name="setting_joystick_tips">重启摇杆后生效</string>
    <string name="setting_log_off">关闭日志</string>
    <string name="setting_pos_history">历史记录有效期(天)</string>
    <string name="setting_run">跑步速度(米/秒)</string>
    <string name="setting_run_default">3.6</string>
    <string name="setting_version">版本号</string>
    <string name="setting_walk">步行速度(米/秒)</string>
    <string name="setting_walk_default">1.2</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="summary_collapsed_preference_list">%1$s, %2$s</string>
    <string name="update_commit">提交信息：</string>
    <string name="update_download">立即下载</string>
    <string name="update_downloading">升级文件下载中。。。</string>
    <string name="update_ignore">忽略一次</string>
    <string name="update_last">恭喜，EG 已是最新版</string>
    <string name="update_time">更新时间：</string>
    <string name="update_title">新版本可用</string>
    <string name="v7_preference_off">OFF</string>
    <string name="v7_preference_on">ON</string>
    <string name="welcome_btn_txt">进入应用</string>
    <string name="zoom_in">放大</string>
    <string name="zoom_out">缩小</string>
</resources>
