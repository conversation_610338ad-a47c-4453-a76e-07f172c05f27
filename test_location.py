#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import time
import json

def run_adb_command(command):
    """执行ADB命令"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.stdout.strip()
    except Exception as e:
        print(f"执行命令失败: {e}")
        return ""

def test_location_spoof():
    """测试定位欺骗功能"""
    print("🧪 开始测试EG应用的定位欺骗功能...")
    print("📍 目标位置: JWK京崎大厦 (30.1865, 120.2103)")
    print("=" * 60)
    
    # 1. 启动应用
    print("1️⃣ 启动EG应用...")
    run_adb_command(r".\platform-tools\adb.exe shell monkey -p com.alibaba.android.rimet.beesforce 1")
    time.sleep(5)
    
    # 2. 检查应用是否运行
    print("2️⃣ 检查应用状态...")
    ps_result = run_adb_command(r'.\platform-tools\adb.exe shell "ps | grep com.alibaba.android.rimet.beesforce"')
    if ps_result:
        print(f"✅ 应用正在运行: {ps_result}")
    else:
        print("❌ 应用未运行")
        return
    
    # 3. 检查定位权限
    print("3️⃣ 检查定位权限...")
    permission_result = run_adb_command(r'.\platform-tools\adb.exe shell "dumpsys package com.alibaba.android.rimet.beesforce | grep -A 1 -B 1 location"')
    print(f"📱 定位权限状态: {permission_result}")
    
    # 4. 模拟定位测试
    print("4️⃣ 测试模拟定位...")
    
    # 启用模拟位置
    run_adb_command(r'.\platform-tools\adb.exe shell "settings put secure mock_location_app com.alibaba.android.rimet.beesforce"')
    
    # 设置模拟位置 (JWK京崎大厦)
    mock_location_cmd = r'.\platform-tools\adb.exe shell "am start -a android.intent.action.VIEW -d \"geo:30.1865,120.2103?q=JWK京崎大厦\""'
    run_adb_command(mock_location_cmd)
    
    print("✅ 已设置模拟位置为JWK京崎大厦")
    
    # 5. 截取屏幕截图
    print("5️⃣ 截取屏幕截图...")
    run_adb_command(r".\platform-tools\adb.exe shell screencap -p /sdcard/location_test.png")
    run_adb_command(r".\platform-tools\adb.exe pull /sdcard/location_test.png location_test.png")
    print("📸 屏幕截图已保存: location_test.png")
    
    # 6. 检查应用日志
    print("6️⃣ 检查应用日志...")
    log_result = run_adb_command(r'.\platform-tools\adb.exe logcat -d | findstr -i "location\|gps\|latitude\|longitude" | tail -10')
    if log_result:
        print("📋 定位相关日志:")
        for line in log_result.split('\n')[-5:]:  # 显示最后5行
            if line.strip():
                print(f"   {line}")
    
    # 7. 测试地图功能
    print("7️⃣ 尝试打开地图功能...")
    map_intent = r'.\platform-tools\adb.exe shell "am start -n com.alibaba.android.rimet.beesforce/com.zcshou.gogogo.MapActivity"'
    map_result = run_adb_command(map_intent)
    if "Error" not in map_result:
        print("✅ 地图Activity启动成功")
        time.sleep(3)
        # 再次截图
        run_adb_command(r".\platform-tools\adb.exe shell screencap -p /sdcard/map_test.png")
        run_adb_command(r".\platform-tools\adb.exe pull /sdcard/map_test.png map_test.png")
        print("📸 地图截图已保存: map_test.png")
    else:
        print("❌ 地图Activity启动失败")
    
    print("=" * 60)
    print("🎉 定位测试完成！")
    print("📁 生成的文件:")
    print("   - location_test.png (主界面截图)")
    print("   - map_test.png (地图界面截图)")
    print("")
    print("📍 如果修改成功，应用应该显示位置为:")
    print("   JWK京崎大厦 (浙江省杭州市滨江区长河街道)")
    print("   坐标: 30.1865°N, 120.2103°E")

if __name__ == "__main__":
    test_location_spoof()
