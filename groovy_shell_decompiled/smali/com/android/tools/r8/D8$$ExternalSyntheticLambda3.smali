.class public final synthetic Lcom/android/tools/r8/D8$$ExternalSyntheticLambda3;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/yu$a;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/utils/j;

.field public final synthetic f$1:Lcom/android/tools/r8/utils/w;

.field public final synthetic f$2:Ljava/util/concurrent/ExecutorService;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/utils/j;Lcom/android/tools/r8/utils/w;Ljava/util/concurrent/ExecutorService;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda3;->f$0:Lcom/android/tools/r8/utils/j;

    iput-object p2, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda3;->f$1:Lcom/android/tools/r8/utils/w;

    iput-object p3, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda3;->f$2:Ljava/util/concurrent/ExecutorService;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda3;->f$0:Lcom/android/tools/r8/utils/j;

    iget-object v1, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda3;->f$1:Lcom/android/tools/r8/utils/w;

    iget-object v2, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda3;->f$2:Ljava/util/concurrent/ExecutorService;

    invoke-static {v0, v1, v2}, Lcom/android/tools/r8/D8;->$r8$lambda$EsgmY0E_dzbtQ3O4UV5GOtj3_3I(Lcom/android/tools/r8/utils/j;Lcom/android/tools/r8/utils/w;Ljava/util/concurrent/ExecutorService;)V

    return-void
.end method
