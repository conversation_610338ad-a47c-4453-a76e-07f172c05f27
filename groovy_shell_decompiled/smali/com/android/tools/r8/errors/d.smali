.class public final Lcom/android/tools/r8/errors/d;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/internal/ZA;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    invoke-static {}, Lcom/android/tools/r8/internal/cB;->h()Lcom/android/tools/r8/internal/ZA;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/errors/d;->a:Lcom/android/tools/r8/internal/ZA;

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/errors/CheckEnumUnboxedDiagnostic;
    .locals 2

    .line 4
    new-instance v0, Lcom/android/tools/r8/errors/CheckEnumUnboxedDiagnostic;

    iget-object v1, p0, Lcom/android/tools/r8/errors/d;->a:Lcom/android/tools/r8/internal/ZA;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/ZA;->a()Lcom/android/tools/r8/internal/cB;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/android/tools/r8/errors/CheckEnumUnboxedDiagnostic;-><init>(Lcom/android/tools/r8/internal/cB;)V

    return-object v0
.end method

.method public final a(Ljava/util/ArrayList;)Lcom/android/tools/r8/errors/d;
    .locals 4

    .line 1
    sget-object v0, Lcom/android/tools/r8/errors/d$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/errors/d$$ExternalSyntheticLambda0;

    invoke-static {v0}, Ljava/util/Comparator;->comparing(Ljava/util/function/Function;)Ljava/util/Comparator;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/util/ArrayList;->sort(Ljava/util/Comparator;)V

    .line 2
    invoke-virtual {p1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/E2;

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/errors/d;->a:Lcom/android/tools/r8/internal/ZA;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->e1()Ljava/lang/String;

    move-result-object v0

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Enum "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " was not unboxed."

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    goto :goto_0

    :cond_0
    return-object p0
.end method
