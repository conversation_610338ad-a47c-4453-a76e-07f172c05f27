.class public final synthetic Lcom/android/tools/r8/R8Command$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/R8Command;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/R8Command;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/R8Command$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/R8Command;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/R8Command$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/R8Command;

    check-cast p1, Lcom/android/tools/r8/internal/As$a;

    invoke-static {v0, p1}, Lcom/android/tools/r8/R8Command;->$r8$lambda$O3Aas-CKMAQXlV4yUxugr0j5Jm4(Lcom/android/tools/r8/R8Command;Lcom/android/tools/r8/internal/As$a;)V

    return-void
.end method
