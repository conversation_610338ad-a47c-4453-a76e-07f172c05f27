.class public final synthetic Lcom/android/tools/r8/D8$$ExternalSyntheticLambda10;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/lp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/y;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda10;->f$0:Lcom/android/tools/r8/graph/y;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda10;->f$0:Lcom/android/tools/r8/graph/y;

    invoke-static {v0}, Lcom/android/tools/r8/D8;->$r8$lambda$FPmzBiboPxZMiU__1ug43fOCCyQ(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method
