.class public interface abstract Lcom/android/tools/r8/MarkerInfo;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# virtual methods
.method public abstract getMinApi()I
.end method

.method public abstract getRawEncoding()Ljava/lang/String;
.end method

.method public abstract getTool()Ljava/lang/String;
.end method

.method public abstract getVersion()Ljava/lang/String;
.end method

.method public abstract hasBackend()Z
.end method

.method public abstract hasCompilationMode()Z
.end method

.method public abstract isBackendClassFiles()Z
.end method

.method public abstract isBackendDexFiles()Z
.end method

.method public abstract isCompilationModeDebug()Z
.end method

.method public abstract isCompilationModeRelease()Z
.end method

.method public abstract isD8()Z
.end method

.method public abstract isL8()Z
.end method

.method public abstract isR8()Z
.end method

.method public abstract isR8ModeCompatibility()Z
.end method

.method public abstract isR8ModeFull()Z
.end method
