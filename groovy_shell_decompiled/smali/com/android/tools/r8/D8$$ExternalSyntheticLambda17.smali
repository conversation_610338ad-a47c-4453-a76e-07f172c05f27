.class public final synthetic Lcom/android/tools/r8/D8$$ExternalSyntheticLambda17;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/lp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/y;

.field public final synthetic f$1:Ljava/util/concurrent/ExecutorService;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/Gp0;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/y;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Gp0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda17;->f$0:Lcom/android/tools/r8/graph/y;

    iput-object p2, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda17;->f$1:Ljava/util/concurrent/ExecutorService;

    iput-object p3, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda17;->f$2:Lcom/android/tools/r8/internal/Gp0;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda17;->f$0:Lcom/android/tools/r8/graph/y;

    iget-object v1, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda17;->f$1:Ljava/util/concurrent/ExecutorService;

    iget-object v2, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda17;->f$2:Lcom/android/tools/r8/internal/Gp0;

    invoke-static {v0, v1, v2}, Lcom/android/tools/r8/D8;->$r8$lambda$Gd52CdfpX8aPbnHDwLBcvYWQAxk(Lcom/android/tools/r8/graph/y;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Gp0;)V

    return-void
.end method
