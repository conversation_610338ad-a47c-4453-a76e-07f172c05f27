.class public final Lcom/android/tools/r8/k0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/StringConsumer;


# instance fields
.field public final a:Lcom/android/tools/r8/utils/w;

.field public b:Ljava/lang/StringBuilder;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/utils/w;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/k0;->b:Ljava/lang/StringBuilder;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/k0;->a:Lcom/android/tools/r8/utils/w;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/String;Lcom/android/tools/r8/DiagnosticsHandler;)V
    .locals 0

    .line 1
    iget-object p2, p0, Lcom/android/tools/r8/k0;->b:Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method public final finished(Lcom/android/tools/r8/DiagnosticsHandler;)V
    .locals 1

    .line 1
    iget-object p1, p0, Lcom/android/tools/r8/k0;->a:Lcom/android/tools/r8/utils/w;

    iget-object v0, p0, Lcom/android/tools/r8/k0;->b:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/android/tools/r8/internal/Sn0;->f(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p1, Lcom/android/tools/r8/utils/w;->f:Ljava/util/List;

    const/4 p1, 0x0

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/k0;->b:Ljava/lang/StringBuilder;

    return-void
.end method
