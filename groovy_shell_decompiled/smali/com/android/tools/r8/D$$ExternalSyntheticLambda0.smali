.class public final synthetic Lcom/android/tools/r8/D$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/graph/A0;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/D$$ExternalSyntheticLambda0;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/D$$ExternalSyntheticLambda0;

    invoke-direct {v0}, Lcom/android/tools/r8/D$$ExternalSyntheticLambda0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/D$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/D$$ExternalSyntheticLambda0;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/E0;)Ljava/io/PrintStream;
    .locals 0

    invoke-static {p1}, Lcom/android/tools/r8/D;->a(Lcom/android/tools/r8/graph/E0;)Ljava/io/PrintStream;

    move-result-object p1

    return-object p1
.end method
