.class public final Lcom/android/tools/r8/L;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/Ot0;


# instance fields
.field public final synthetic a:Ljava/util/ArrayList;


# direct methods
.method public constructor <init>(Ljava/util/ArrayList;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/L;->a:Ljava/util/ArrayList;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/z5;)V
    .locals 0

    return-void
.end method

.method public final c(Lcom/android/tools/r8/graph/E2;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E2;->U1()Lcom/android/tools/r8/internal/Uw;

    move-result-object p1

    iget-object v0, p0, Lcom/android/tools/r8/L;->a:Ljava/util/ArrayList;

    new-instance v1, Lcom/android/tools/r8/GlobalSyntheticsGenerator$$ExternalSyntheticLambda7;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/GlobalSyntheticsGenerator$$ExternalSyntheticLambda7;-><init>(Ljava/util/List;)V

    invoke-interface {p1, v1}, Ljava/lang/Iterable;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method
