.class public final Lcom/android/tools/r8/naming/P0;
.super Lcom/android/tools/r8/naming/q0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z = true


# instance fields
.field public final d:Lcom/android/tools/r8/graph/B1;

.field public final e:Lcom/android/tools/r8/naming/r0;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 1

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/android/tools/r8/naming/q0;-><init>(Lcom/android/tools/r8/graph/B1;)V

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/naming/P0;->d:Lcom/android/tools/r8/graph/B1;

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->x()Lcom/android/tools/r8/naming/r0;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/naming/P0;->e:Lcom/android/tools/r8/naming/r0;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/naming/r0;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/utils/w;->r()I

    move-result v0

    const/4 v1, 0x3

    if-ne v0, v1, :cond_0

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1;->c2:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/h;->c(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 6
    new-instance v0, Lcom/android/tools/r8/naming/P0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/naming/P0;-><init>(Lcom/android/tools/r8/graph/y;)V

    return-object v0

    .line 8
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->x()Lcom/android/tools/r8/naming/r0;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;
    .locals 2

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/naming/P0;->d:Lcom/android/tools/r8/graph/B1;

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->c2:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_0

    .line 10
    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->d2:Lcom/android/tools/r8/graph/J2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    goto :goto_1

    .line 11
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/naming/P0;->e:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/naming/r0;->c(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;

    move-result-object v0

    :goto_1
    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/graph/a4;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/graph/I2;
    .locals 3

    .line 12
    sget-boolean v0, Lcom/android/tools/r8/naming/P0;->f:Z

    if-nez v0, :cond_2

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/a4;->b()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 13
    iget-object v1, p0, Lcom/android/tools/r8/naming/P0;->d:Lcom/android/tools/r8/graph/B1;

    iget-object v2, v1, Lcom/android/tools/r8/graph/B1;->c2:Lcom/android/tools/r8/graph/J2;

    if-ne v0, v2, :cond_0

    .line 14
    iget-object v0, v1, Lcom/android/tools/r8/graph/B1;->d2:Lcom/android/tools/r8/graph/J2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-nez v0, :cond_1

    goto :goto_1

    .line 15
    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 16
    :cond_2
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/naming/P0;->e:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/naming/r0;->a(Lcom/android/tools/r8/graph/a4;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/I2;
    .locals 1

    .line 18
    iget-object v0, p0, Lcom/android/tools/r8/naming/P0;->e:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/naming/r0;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/I2;
    .locals 1

    .line 17
    iget-object v0, p0, Lcom/android/tools/r8/naming/P0;->e:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/naming/r0;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    return-object p1
.end method

.method public final a(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    .line 19
    iget-object v0, p0, Lcom/android/tools/r8/naming/P0;->e:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/naming/r0;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public final b()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/naming/P0;->e:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v0}, Lcom/android/tools/r8/naming/r0;->b()Z

    move-result v0

    return v0
.end method

.method public final e(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/naming/P0;->e:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/naming/r0;->e(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    return-object p1
.end method
