.class public final synthetic Lcom/android/tools/r8/R8$$ExternalSyntheticLambda21;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/shaking/b2;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/y;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/shaking/b2;Lcom/android/tools/r8/graph/y;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda21;->f$0:Lcom/android/tools/r8/shaking/b2;

    iput-object p2, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda21;->f$1:Lcom/android/tools/r8/graph/y;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda21;->f$0:Lcom/android/tools/r8/shaking/b2;

    iget-object v1, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda21;->f$1:Lcom/android/tools/r8/graph/y;

    invoke-static {v0, v1}, Lcom/android/tools/r8/R8;->$r8$lambda$KJXK169j4bJgINeC-a7B4kUGA-M(Lcom/android/tools/r8/shaking/b2;Lcom/android/tools/r8/graph/y;)Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method
