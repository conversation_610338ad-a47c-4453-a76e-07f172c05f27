.class public final Lcom/android/tools/r8/J;
.super Lcom/android/tools/r8/StringConsumer$ForwardingConsumer;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic b:Ljava/util/List;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/StringConsumer;Ljava/util/List;)V
    .locals 0

    .line 1
    iput-object p2, p0, Lcom/android/tools/r8/J;->b:Ljava/util/List;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/StringConsumer$ForwardingConsumer;-><init>(Lcom/android/tools/r8/StringConsumer;)V

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/String;Lcom/android/tools/r8/DiagnosticsHandler;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/J;->b:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 2
    invoke-super {p0, p1, p2}, Lcom/android/tools/r8/StringConsumer$ForwardingConsumer;->accept(Ljava/lang/String;Lcom/android/tools/r8/DiagnosticsHandler;)V

    return-void
.end method
