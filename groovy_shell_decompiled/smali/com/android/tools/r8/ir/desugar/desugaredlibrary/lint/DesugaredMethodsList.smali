.class public Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;
.super Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field private final j:Lcom/android/tools/r8/internal/A2;

.field private final k:Z

.field private final l:Lcom/android/tools/r8/StringConsumer;


# direct methods
.method public static synthetic $r8$lambda$W_fFpSsvsF48DuBCZGMiYSV0xGw([Ljava/lang/String;)V
    .locals 0

    invoke-static {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->a([Ljava/lang/String;)V

    return-void
.end method

.method public static synthetic $r8$lambda$k5Flz2aDrr6vC2S0rcbWe2CY2-U(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;)V
    .locals 0

    invoke-static {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->a(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;)V

    return-void
.end method

.method constructor <init>(IZLcom/android/tools/r8/internal/bd0;Lcom/android/tools/r8/t0;Ljava/util/Collection;Lcom/android/tools/r8/StringConsumer;Ljava/util/Collection;)V
    .locals 6

    const/4 v4, 0x0

    move-object v0, p0

    move-object v1, p3

    move-object v2, p4

    move-object v3, p5

    move-object v5, p7

    .line 1
    invoke-direct/range {v0 .. v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;-><init>(Lcom/android/tools/r8/internal/bd0;Lcom/android/tools/r8/t0;Ljava/util/Collection;Ljava/nio/file/Path;Ljava/util/Collection;)V

    .line 2
    invoke-static {p1}, Lcom/android/tools/r8/internal/A2;->a(I)Lcom/android/tools/r8/internal/A2;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->j:Lcom/android/tools/r8/internal/A2;

    .line 3
    iput-boolean p2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->k:Z

    .line 4
    iput-object p6, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->l:Lcom/android/tools/r8/StringConsumer;

    return-void
.end method

.method private static synthetic a(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;)V
    .locals 9

    .line 1
    new-instance v8, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->getMinApi()I

    move-result v1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->isAndroidPlatformBuild()Z

    move-result v2

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->getReporter()Lcom/android/tools/r8/internal/bd0;

    move-result-object v3

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->getDesugarLibrarySpecification()Lcom/android/tools/r8/t0;

    move-result-object v4

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->getDesugarLibraryImplementation()Ljava/util/Collection;

    move-result-object v5

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->getOutputConsumer()Lcom/android/tools/r8/StringConsumer;

    move-result-object v6

    .line 8
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->getLibrary()Ljava/util/Collection;

    move-result-object v7

    move-object v0, v8

    invoke-direct/range {v0 .. v7}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;-><init>(IZLcom/android/tools/r8/internal/bd0;Lcom/android/tools/r8/t0;Ljava/util/Collection;Lcom/android/tools/r8/StringConsumer;Ljava/util/Collection;)V

    .line 9
    invoke-virtual {v8}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->run()Lcom/android/tools/r8/internal/A2;

    return-void
.end method

.method private static synthetic a([Ljava/lang/String;)V
    .locals 2

    .line 14
    :try_start_0
    invoke-static {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->run([Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p0

    .line 16
    new-instance v0, Lcom/android/tools/r8/internal/jf;

    invoke-virtual {p0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1, p0}, Lcom/android/tools/r8/internal/jf;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0
.end method

.method public static main([Ljava/lang/String;)V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList$$ExternalSyntheticLambda1;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList$$ExternalSyntheticLambda1;-><init>([Ljava/lang/String;)V

    invoke-static {v0}, Lcom/android/tools/r8/internal/yu;->a(Lcom/android/tools/r8/internal/zu;)V

    return-void
.end method

.method public static run(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/CompilationFailedException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->isHelp()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    sget-object p0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-static {}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->getUsageMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void

    .line 5
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->isVersion()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 6
    sget-object p0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-static {}, Lcom/android/tools/r8/Version;->getVersionString()Ljava/lang/String;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "DesugaredMethodsList "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void

    .line 10
    :cond_1
    invoke-virtual {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->getReporter()Lcom/android/tools/r8/internal/bd0;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;)V

    .line 11
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/yu;->b(Lcom/android/tools/r8/internal/bd0;Lcom/android/tools/r8/internal/yu$a;)V

    return-void
.end method

.method public static run([Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/CompilationFailedException;,
            Ljava/io/IOException;
        }
    .end annotation

    .line 18
    invoke-static {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;->parse([Ljava/lang/String;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;

    move-result-object p0

    invoke-static {p0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->run(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsListCommand;)V

    return-void
.end method


# virtual methods
.method final a(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;Ljava/util/ArrayList;)V
    .locals 1

    .line 10
    invoke-virtual {p3}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/String;

    .line 11
    iget-object p3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->l:Lcom/android/tools/r8/StringConsumer;

    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->a:Lcom/android/tools/r8/utils/w;

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    invoke-interface {p3, p2, v0}, Lcom/android/tools/r8/StringConsumer;->accept(Ljava/lang/String;Lcom/android/tools/r8/DiagnosticsHandler;)V

    goto :goto_0

    .line 13
    :cond_0
    iget-object p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->l:Lcom/android/tools/r8/StringConsumer;

    iget-object p2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->a:Lcom/android/tools/r8/utils/w;

    iget-object p2, p2, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    invoke-interface {p1, p2}, Lcom/android/tools/r8/I;->finished(Lcom/android/tools/r8/DiagnosticsHandler;)V

    return-void
.end method

.method public run()Lcom/android/tools/r8/internal/A2;
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 12
    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->b:Lcom/android/tools/r8/internal/Kl;

    .line 13
    invoke-interface {v0}, Lcom/android/tools/r8/internal/Kl;->d()Lcom/android/tools/r8/internal/A2;

    move-result-object v0

    .line 14
    new-instance v8, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;

    iget-object v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->a:Lcom/android/tools/r8/utils/w;

    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->f:Ljava/util/Collection;

    iget-object v5, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->j:Lcom/android/tools/r8/internal/A2;

    iget-boolean v6, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->k:Z

    const/4 v4, 0x1

    const/4 v7, 0x1

    move-object v1, v8

    invoke-direct/range {v1 .. v7}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;-><init>(Lcom/android/tools/r8/utils/w;Ljava/util/Collection;ZLcom/android/tools/r8/internal/A2;ZZ)V

    iget-object v1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->d:Ljava/util/Collection;

    iget-object v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->c:Lcom/android/tools/r8/t0;

    .line 16
    invoke-virtual {v8, v1, v2}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;->b(Ljava/util/Collection;Lcom/android/tools/r8/t0;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;

    move-result-object v1

    .line 17
    iget-object v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/DesugaredMethodsList;->j:Lcom/android/tools/r8/internal/A2;

    invoke-virtual {p0, v0, v2, v1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->a(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;)V

    return-object v0
.end method
