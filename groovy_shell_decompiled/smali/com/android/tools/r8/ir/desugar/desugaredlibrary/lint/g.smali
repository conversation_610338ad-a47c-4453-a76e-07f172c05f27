.class public final Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;
.super Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final g:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

.field public h:Z

.field public i:Z

.field public j:Z

.field public k:Z


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;-><init>(Lcom/android/tools/r8/graph/J2;)V

    const/4 p1, 0x0

    .line 2
    iput-boolean p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->h:Z

    .line 3
    iput-boolean p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->i:Z

    .line 4
    iput-boolean p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->j:Z

    .line 5
    iput-boolean p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->k:Z

    .line 9
    iput-object p2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->g:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;)Ljava/lang/String;
    .locals 3

    if-nez p1, :cond_0

    const-string p1, ""

    return-object p1

    .line 3
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 4
    iget-boolean v1, p1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->e:Z

    const/4 v2, 0x1

    if-eqz v1, :cond_1

    const-string v1, "<sup>1</sup>"

    .line 5
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 6
    iput-boolean v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->h:Z

    .line 8
    :cond_1
    iget-boolean v1, p1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->f:Z

    if-eqz v1, :cond_2

    const-string v1, "<sup>2</sup>"

    .line 9
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 10
    iput-boolean v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->i:Z

    .line 12
    :cond_2
    iget-boolean v1, p1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$b;->a:Z

    if-eqz v1, :cond_3

    const-string v1, "<sup>3</sup>"

    .line 13
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 14
    iput-boolean v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->j:Z

    .line 16
    :cond_3
    iget-boolean p1, p1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->g:Z

    if-eqz p1, :cond_4

    const-string p1, "<sup>4</sup>"

    .line 17
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 18
    iput-boolean v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->k:Z

    .line 20
    :cond_4
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public final toString()Ljava/lang/String;
    .locals 11

    .line 1
    new-instance v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;

    invoke-direct {v0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;-><init>()V

    const-string v1, "tr"

    .line 2
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->k(Ljava/lang/String;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;

    .line 3
    iget-object v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->e:Ljava/lang/String;

    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v2

    if-lez v2, :cond_0

    .line 4
    iget-object v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->e:Ljava/lang/String;

    invoke-virtual {v0, v2}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->i(Ljava/lang/String;)V

    .line 6
    :cond_0
    iget-object v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->d:Ljava/lang/String;

    invoke-virtual {p0, v2}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->g(Ljava/lang/String;)V

    const-string v2, "td"

    .line 8
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->k(Ljava/lang/String;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;

    move-result-object v3

    const-string v4, "ul style=\"list-style-position:inside; list-style-type: none !important; margin-left:0px;padding-left:0px !important;\""

    .line 9
    invoke-virtual {v3, v4}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->k(Ljava/lang/String;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;

    .line 12
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a:Ljava/util/TreeMap;

    invoke-interface {v3}, Ljava/util/Map;->isEmpty()Z

    move-result v3

    const-string v4, " "

    if-nez v3, :cond_3

    .line 13
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a:Ljava/util/TreeMap;

    invoke-virtual {v3}, Ljava/util/TreeMap;->keySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_3

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/graph/g1;

    .line 14
    iget-object v6, v5, Lcom/android/tools/r8/graph/g1;->g:Lcom/android/tools/r8/graph/h3;

    .line 15
    invoke-static {v6}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a(Lcom/android/tools/r8/graph/h3;)Ljava/lang/String;

    move-result-object v6

    .line 17
    invoke-virtual {v5}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v7

    check-cast v7, Lcom/android/tools/r8/graph/l1;

    iget-object v7, v7, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v7}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a(Lcom/android/tools/r8/graph/J2;)Ljava/lang/String;

    move-result-object v7

    .line 19
    invoke-virtual {v5}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v8

    check-cast v8, Lcom/android/tools/r8/graph/l1;

    iget-object v8, v8, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    iget-object v9, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a:Ljava/util/TreeMap;

    .line 20
    invoke-virtual {v9, v5}, Ljava/util/TreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$a;

    if-nez v5, :cond_1

    const-string v5, ""

    goto :goto_1

    .line 23
    :cond_1
    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    .line 24
    iget-boolean v5, v5, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$b;->a:Z

    if-eqz v5, :cond_2

    const-string v5, "<sup>3</sup>"

    .line 25
    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v5, 0x1

    .line 26
    iput-boolean v5, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->j:Z

    .line 28
    :cond_2
    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 29
    :goto_1
    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 30
    invoke-virtual {v0, v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->e(Ljava/lang/String;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;

    goto :goto_0

    .line 39
    :cond_3
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->b:Ljava/util/TreeMap;

    invoke-interface {v3}, Ljava/util/Map;->isEmpty()Z

    move-result v3

    if-nez v3, :cond_4

    .line 40
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->b:Ljava/util/TreeMap;

    invoke-virtual {v3}, Ljava/util/TreeMap;->keySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_4

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/graph/j1;

    .line 41
    iget-object v6, v5, Lcom/android/tools/r8/graph/j1;->g:Lcom/android/tools/r8/graph/H4;

    .line 42
    invoke-static {v6}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a(Lcom/android/tools/r8/graph/H4;)Ljava/lang/String;

    move-result-object v6

    iget-object v7, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->d:Ljava/lang/String;

    .line 44
    invoke-virtual {p0, v7}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    .line 45
    invoke-virtual {p0, v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a(Lcom/android/tools/r8/graph/j1;)Ljava/lang/String;

    move-result-object v8

    iget-object v9, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->b:Ljava/util/TreeMap;

    .line 46
    invoke-virtual {v9, v5}, Ljava/util/TreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    invoke-virtual {p0, v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->a(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;)Ljava/lang/String;

    move-result-object v5

    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 47
    invoke-virtual {v0, v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->f(Ljava/lang/String;)V

    goto :goto_2

    .line 55
    :cond_4
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->c:Ljava/util/TreeMap;

    invoke-interface {v3}, Ljava/util/Map;->isEmpty()Z

    move-result v3

    if-nez v3, :cond_5

    .line 56
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->c:Ljava/util/TreeMap;

    invoke-virtual {v3}, Ljava/util/TreeMap;->keySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_3
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_5

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/graph/j1;

    .line 57
    iget-object v6, v5, Lcom/android/tools/r8/graph/j1;->g:Lcom/android/tools/r8/graph/H4;

    .line 58
    invoke-static {v6}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a(Lcom/android/tools/r8/graph/H4;)Ljava/lang/String;

    move-result-object v6

    .line 60
    invoke-virtual {v5}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v7

    check-cast v7, Lcom/android/tools/r8/graph/x2;

    iget-object v7, v7, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v7, v7, Lcom/android/tools/r8/graph/F2;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v7}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a(Lcom/android/tools/r8/graph/J2;)Ljava/lang/String;

    move-result-object v7

    .line 62
    invoke-virtual {v5}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v8

    check-cast v8, Lcom/android/tools/r8/graph/x2;

    iget-object v8, v8, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    .line 63
    invoke-virtual {p0, v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->a(Lcom/android/tools/r8/graph/j1;)Ljava/lang/String;

    move-result-object v9

    iget-object v10, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/h;->c:Ljava/util/TreeMap;

    .line 64
    invoke-virtual {v10, v5}, Ljava/util/TreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    invoke-virtual {p0, v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->a(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;)Ljava/lang/String;

    move-result-object v5

    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v10, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 65
    invoke-virtual {v0, v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->f(Ljava/lang/String;)V

    goto :goto_3

    :cond_5
    const-string v3, "ul"

    .line 75
    invoke-virtual {v0, v3}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->j(Ljava/lang/String;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;

    move-result-object v3

    invoke-virtual {v3, v2}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->j(Ljava/lang/String;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;

    .line 76
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 77
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->g:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

    invoke-virtual {v3}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;->d()Z

    move-result v3

    if-eqz v3, :cond_6

    const-string v3, "Fully implemented class.<br>&nbsp;"

    .line 78
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 80
    :cond_6
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->g:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

    invoke-virtual {v3}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;->c()Z

    move-result v3

    if-eqz v3, :cond_7

    const-string v3, "Additional methods on existing class.<br>&nbsp;"

    .line 81
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 83
    :cond_7
    iget-boolean v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->h:Z

    if-eqz v3, :cond_8

    const-string v3, "<sup>1</sup> Supported only on devices which API level is 21 or higher.<br>&nbsp;"

    .line 85
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 89
    :cond_8
    iget-boolean v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->i:Z

    if-eqz v3, :cond_9

    const-string v3, "<sup>2</sup> Not present in Android "

    .line 91
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    .line 92
    sget-object v4, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->g:Lcom/android/tools/r8/internal/A2;

    .line 93
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, " (May not resolve at compilation).<br>&nbsp;"

    .line 94
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 97
    :cond_9
    iget-boolean v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->j:Z

    if-eqz v3, :cond_a

    const-string v3, "<sup>3</sup> Not supported at all minSDK levels.<br>&nbsp;"

    .line 99
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 103
    :cond_a
    iget-boolean v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->k:Z

    if-eqz v3, :cond_b

    const-string v3, "<sup>4</sup> Also supported with covariant return type.<br>&nbsp;"

    .line 105
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 109
    :cond_b
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->g:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

    invoke-virtual {v3}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;->a()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v3

    const-string v4, ") present in Android "

    if-nez v3, :cond_c

    const-string v3, "Some fields ("

    .line 111
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    iget-object v5, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->g:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

    .line 112
    invoke-virtual {v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;->a()Ljava/util/List;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v5

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    .line 113
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    sget-object v5, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->g:Lcom/android/tools/r8/internal/A2;

    .line 114
    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v5, " are not supported.<br>&nbsp;"

    .line 115
    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 118
    :cond_c
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->g:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

    invoke-virtual {v3}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;->b()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v3

    if-nez v3, :cond_d

    const-string v3, "Some methods ("

    .line 120
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    iget-object v5, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/g;->g:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

    .line 121
    invoke-virtual {v5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;->b()Ljava/util/List;

    move-result-object v5

    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v5

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    .line 122
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    sget-object v4, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->g:Lcom/android/tools/r8/internal/A2;

    .line 123
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, " are not supported."

    .line 124
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 126
    :cond_d
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->h(Ljava/lang/String;)V

    .line 127
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;->j(Ljava/lang/String;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/f;

    .line 128
    invoke-virtual {v0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/i;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
