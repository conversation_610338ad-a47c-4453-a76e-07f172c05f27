.class public final synthetic Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/d3;

.field public final synthetic f$2:Ljava/util/List;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;Lcom/android/tools/r8/graph/d3;Ljava/util/List;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;

    iput-object p2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/graph/d3;

    iput-object p3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda5;->f$2:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;

    iget-object v1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/graph/d3;

    iget-object v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda5;->f$2:Ljava/util/List;

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0, v1, v2, p1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;->a(Lcom/android/tools/r8/graph/d3;Ljava/util/List;Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method
