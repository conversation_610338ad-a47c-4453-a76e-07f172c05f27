.class public Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;
.super Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$b;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "c"
.end annotation


# static fields
.field public static final h:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

.field public static final i:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

.field public static final j:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

.field public static final k:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;


# instance fields
.field public final e:Z

.field public final f:Z

.field public final g:Z


# direct methods
.method static constructor <clinit>()V
    .locals 15

    .line 1
    new-instance v7, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v4, 0x0

    const/4 v5, -0x1

    const/4 v6, -0x1

    move-object v0, v7

    invoke-direct/range {v0 .. v6}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;-><init>(ZZZZII)V

    sput-object v7, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->h:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    .line 3
    new-instance v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, -0x1

    const/4 v14, -0x1

    move-object v8, v0

    invoke-direct/range {v8 .. v14}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;-><init>(ZZZZII)V

    sput-object v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->i:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    .line 5
    new-instance v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    const/4 v2, 0x1

    const/4 v3, 0x0

    const/4 v5, 0x0

    const/4 v7, -0x1

    move-object v1, v0

    invoke-direct/range {v1 .. v7}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;-><init>(ZZZZII)V

    sput-object v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->j:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    .line 7
    new-instance v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    const/4 v10, 0x1

    move-object v8, v0

    invoke-direct/range {v8 .. v14}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;-><init>(ZZZZII)V

    sput-object v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->k:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    return-void
.end method

.method public constructor <init>(ZZZZII)V
    .locals 0

    .line 1
    invoke-direct {p0, p5, p6, p4}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$b;-><init>(IIZ)V

    .line 2
    iput-boolean p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->e:Z

    .line 3
    iput-boolean p2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->f:Z

    .line 4
    iput-boolean p3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->g:Z

    return-void
.end method

.method public static a(I)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;
    .locals 8

    .line 1
    new-instance v7, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x1

    move-object v0, v7

    move v5, p0

    move v6, p0

    invoke-direct/range {v0 .. v6}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;-><init>(ZZZZII)V

    return-object v7
.end method
