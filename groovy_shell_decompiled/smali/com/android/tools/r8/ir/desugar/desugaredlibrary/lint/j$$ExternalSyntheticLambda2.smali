.class public final synthetic Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j;

.field public final synthetic f$1:Ljava/io/PrintStream;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j;Ljava/io/PrintStream;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j;

    iput-object p2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j$$ExternalSyntheticLambda2;->f$1:Ljava/io/PrintStream;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j;

    iget-object v1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j$$ExternalSyntheticLambda2;->f$1:Ljava/io/PrintStream;

    check-cast p1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/j;->b(Ljava/io/PrintStream;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;)V

    return-void
.end method
