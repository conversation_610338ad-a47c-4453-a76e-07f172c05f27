.class public Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;
.super Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field static final synthetic i:Z = true


# direct methods
.method public static synthetic $r8$lambda$-vQH78oBGdkaJtSv6ZPYOs85o4E(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;Lcom/android/tools/r8/internal/A2;Ljava/util/List;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->a(Lcom/android/tools/r8/internal/A2;Ljava/util/List;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;)V

    return-void
.end method

.method public static synthetic $r8$lambda$I3WF82yG3TeFDGrxCaZlY-BXW9M(Ljava/util/List;Ljava/lang/String;Lcom/android/tools/r8/graph/g1;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$a;)V
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->a(Ljava/util/List;Ljava/lang/String;Lcom/android/tools/r8/graph/g1;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$a;)V

    return-void
.end method

.method public static synthetic $r8$lambda$IHV5nUvYDldIkKxdAv339Tv9VFI(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;Lcom/android/tools/r8/internal/A2;Ljava/util/List;Ljava/lang/String;Lcom/android/tools/r8/graph/j1;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;)V
    .locals 0

    invoke-direct/range {p0 .. p5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->a(Lcom/android/tools/r8/internal/A2;Ljava/util/List;Ljava/lang/String;Lcom/android/tools/r8/graph/j1;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;)V

    return-void
.end method

.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/bd0;Lcom/android/tools/r8/t0;Ljava/util/Collection;Ljava/nio/file/Path;Ljava/util/Collection;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/tools/r8/internal/bd0;",
            "Lcom/android/tools/r8/t0;",
            "Ljava/util/Collection<",
            "Lcom/android/tools/r8/ProgramResourceProvider;",
            ">;",
            "Ljava/nio/file/Path;",
            "Ljava/util/Collection<",
            "Lcom/android/tools/r8/ClassFileResourceProvider;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct/range {p0 .. p5}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;-><init>(Lcom/android/tools/r8/internal/bd0;Lcom/android/tools/r8/t0;Ljava/util/Collection;Ljava/nio/file/Path;Ljava/util/Collection;)V

    return-void
.end method

.method private static a(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;)Ljava/lang/String;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/A2;->d()I

    move-result p0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/A2;->d()I

    move-result p1

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "desugared_apis_"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "_"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private a(Lcom/android/tools/r8/internal/A2;Ljava/util/List;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;)V
    .locals 2

    .line 65
    iget-object v0, p3, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->a:Lcom/android/tools/r8/graph/E0;

    .line 66
    iget-object v0, v0, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    .line 67
    iget-object v0, v0, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v0

    .line 68
    invoke-static {v0}, Lcom/android/tools/r8/internal/Nk;->i(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 69
    iget-object v1, p3, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->b:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

    .line 70
    iget-boolean v1, v1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;->b:Z

    if-nez v1, :cond_0

    .line 71
    new-instance v1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0, p1, p2, v0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;Lcom/android/tools/r8/internal/A2;Ljava/util/List;Ljava/lang/String;)V

    invoke-virtual {p3, v1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->b(Ljava/util/function/BiConsumer;)V

    .line 86
    new-instance p1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e$$ExternalSyntheticLambda1;

    invoke-direct {p1, p2, v0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e$$ExternalSyntheticLambda1;-><init>(Ljava/util/List;Ljava/lang/String;)V

    invoke-virtual {p3, p1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->a(Ljava/util/function/BiConsumer;)V

    goto :goto_0

    .line 95
    :cond_0
    invoke-interface {p2, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method private a(Lcom/android/tools/r8/internal/A2;Ljava/util/List;Ljava/lang/String;Lcom/android/tools/r8/graph/j1;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;)V
    .locals 1

    .line 53
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/j1;->q1()Z

    move-result v0

    if-nez v0, :cond_6

    invoke-virtual {p4}, Lcom/android/tools/r8/graph/j1;->m1()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    :cond_0
    if-nez p5, :cond_1

    goto :goto_0

    .line 54
    :cond_1
    iget-boolean v0, p5, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$b;->a:Z

    if-eqz v0, :cond_2

    goto :goto_1

    .line 58
    :cond_2
    iget-boolean v0, p5, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->e:Z

    if-eqz v0, :cond_3

    .line 59
    sget-object p5, Lcom/android/tools/r8/internal/A2;->w:Lcom/android/tools/r8/internal/A2;

    if-ne p1, p5, :cond_6

    goto :goto_0

    .line 61
    :cond_3
    sget-boolean p1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->i:Z

    if-nez p1, :cond_5

    iget-boolean p1, p5, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;->f:Z

    if-eqz p1, :cond_4

    goto :goto_0

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 62
    :cond_5
    :goto_0
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    iget-object p1, p1, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    .line 63
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p4

    check-cast p4, Lcom/android/tools/r8/graph/x2;

    iget-object p4, p4, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    invoke-virtual {p4}, Lcom/android/tools/r8/graph/F2;->r0()Ljava/lang/String;

    move-result-object p4

    new-instance p5, Ljava/lang/StringBuilder;

    invoke-direct {p5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p5, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string p5, "#"

    invoke-virtual {p3, p5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 64
    invoke-interface {p2, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_6
    :goto_1
    return-void
.end method

.method private static synthetic a(Ljava/util/List;Ljava/lang/String;Lcom/android/tools/r8/graph/g1;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$a;)V
    .locals 0

    if-eqz p3, :cond_0

    .line 96
    iget-boolean p3, p3, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$b;->a:Z

    if-nez p3, :cond_1

    .line 98
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/l1;

    iget-object p2, p2, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p3, "#"

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 99
    invoke-interface {p0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    return-void
.end method

.method private b(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;)Ljava/nio/file/Path;
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->e:Ljava/nio/file/Path;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/A2;->d()I

    move-result v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "compile_api_level_"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/nio/file/Path;->resolve(Ljava/lang/String;)Ljava/nio/file/Path;

    move-result-object v0

    const/4 v1, 0x0

    new-array v2, v1, [Ljava/nio/file/attribute/FileAttribute;

    .line 2
    invoke-static {v0, v2}, Ljava/nio/file/Files;->createDirectories(Ljava/nio/file/Path;[Ljava/nio/file/attribute/FileAttribute;)Ljava/nio/file/Path;

    .line 3
    sget-object v2, Ljava/io/File;->separator:Ljava/lang/String;

    .line 6
    invoke-static {p1, p2}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->a(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;)Ljava/lang/String;

    move-result-object p1

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ".txt"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    new-array p2, v1, [Ljava/lang/String;

    .line 7
    invoke-static {p1, p2}, Ljava/nio/file/Paths;->get(Ljava/lang/String;[Ljava/lang/String;)Ljava/nio/file/Path;

    move-result-object p1

    return-object p1
.end method

.method public static main([Ljava/lang/String;)V
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    array-length v0, p0

    const/4 v1, 0x1

    const/4 v2, 0x2

    const/4 v3, 0x0

    const/4 v4, 0x4

    if-ne v0, v4, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;

    new-instance v6, Lcom/android/tools/r8/internal/bd0;

    invoke-direct {v6}, Lcom/android/tools/r8/internal/bd0;-><init>()V

    aget-object v4, p0, v3

    new-array v5, v3, [Ljava/lang/String;

    .line 4
    invoke-static {v4, v5}, Ljava/nio/file/Paths;->get(Ljava/lang/String;[Ljava/lang/String;)Ljava/nio/file/Path;

    move-result-object v4

    invoke-static {v4}, Lcom/android/tools/r8/t0;->a(Ljava/nio/file/Path;)Lcom/android/tools/r8/t0$a;

    move-result-object v7

    aget-object v1, p0, v1

    new-array v4, v3, [Ljava/lang/String;

    .line 5
    invoke-static {v1, v4}, Ljava/nio/file/Paths;->get(Ljava/lang/String;[Ljava/lang/String;)Ljava/nio/file/Path;

    move-result-object v1

    invoke-static {v1}, Lcom/android/tools/r8/ArchiveProgramResourceProvider;->fromArchive(Ljava/nio/file/Path;)Lcom/android/tools/r8/ArchiveProgramResourceProvider;

    move-result-object v1

    invoke-static {v1}, Lcom/android/tools/r8/internal/cB;->c(Ljava/lang/Object;)Lcom/android/tools/r8/internal/rk0;

    move-result-object v8

    aget-object v1, p0, v2

    new-array v2, v3, [Ljava/lang/String;

    .line 6
    invoke-static {v1, v2}, Ljava/nio/file/Paths;->get(Ljava/lang/String;[Ljava/lang/String;)Ljava/nio/file/Path;

    move-result-object v9

    new-instance v1, Lcom/android/tools/r8/ArchiveClassFileProvider;

    const/4 v2, 0x3

    aget-object p0, p0, v2

    new-array v2, v3, [Ljava/lang/String;

    .line 7
    invoke-static {p0, v2}, Ljava/nio/file/Paths;->get(Ljava/lang/String;[Ljava/lang/String;)Ljava/nio/file/Path;

    move-result-object p0

    invoke-direct {v1, p0}, Lcom/android/tools/r8/ArchiveClassFileProvider;-><init>(Ljava/nio/file/Path;)V

    invoke-static {v1}, Lcom/android/tools/r8/internal/cB;->c(Ljava/lang/Object;)Lcom/android/tools/r8/internal/rk0;

    move-result-object v10

    move-object v5, v0

    invoke-direct/range {v5 .. v10}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;-><init>(Lcom/android/tools/r8/internal/bd0;Lcom/android/tools/r8/t0;Ljava/util/Collection;Ljava/nio/file/Path;Ljava/util/Collection;)V

    .line 8
    invoke-virtual {v0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->run()Lcom/android/tools/r8/internal/A2;

    return-void

    .line 11
    :cond_0
    new-instance p0, Ljava/lang/RuntimeException;

    sget-object v0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->g:Lcom/android/tools/r8/internal/A2;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Usage: GenerateDesugaredLibraryLintFiles <desugar configuration> <desugar implementation> <output directory> <android jar path for Android "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v4, " or higher>"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    new-array v2, v2, [Ljava/lang/String;

    const-string v4, "Invalid invocation."

    aput-object v4, v2, v3

    aput-object v0, v2, v1

    .line 12
    invoke-static {v2}, Lcom/android/tools/r8/internal/Sn0;->a([Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p0
.end method


# virtual methods
.method final a(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;)V
    .locals 5

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    new-instance v1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e$$ExternalSyntheticLambda2;

    invoke-direct {v1, p0, p2, v0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;Lcom/android/tools/r8/internal/A2;Ljava/util/List;)V

    invoke-virtual {p3, v1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;->a(Ljava/util/function/Consumer;)V

    .line 38
    invoke-virtual {p3}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;->a()Ljava/util/List;

    move-result-object p3

    invoke-interface {p3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :goto_0
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    .line 41
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    iget-object v2, v2, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v2

    .line 42
    invoke-static {v2}, Lcom/android/tools/r8/internal/Nk;->i(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 44
    iget-object v3, v1, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    .line 45
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/F2;->r0()Ljava/lang/String;

    move-result-object v1

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v4, "#"

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 46
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 51
    :cond_0
    invoke-static {}, Ljava/util/Comparator;->naturalOrder()Ljava/util/Comparator;

    move-result-object p3

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->sort(Ljava/util/Comparator;)V

    .line 52
    invoke-virtual {p0, p1, p2, v0}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->a(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;Ljava/util/ArrayList;)V

    return-void
.end method

.method a(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;Ljava/util/ArrayList;)V
    .locals 0

    .line 100
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->b(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;)Ljava/nio/file/Path;

    move-result-object p1

    .line 101
    invoke-static {p1, p3}, Lcom/android/tools/r8/internal/Zv;->a(Ljava/nio/file/Path;Ljava/util/List;)Ljava/nio/file/Path;

    return-void
.end method

.method public run()Lcom/android/tools/r8/internal/A2;
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->b:Lcom/android/tools/r8/internal/Kl;

    .line 2
    invoke-interface {v0}, Lcom/android/tools/r8/internal/Kl;->d()Lcom/android/tools/r8/internal/A2;

    move-result-object v0

    .line 3
    new-instance v1, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;

    iget-object v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->a:Lcom/android/tools/r8/utils/w;

    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->f:Ljava/util/Collection;

    const/4 v4, 0x1

    invoke-direct {v1, v2, v3, v4}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;-><init>(Lcom/android/tools/r8/utils/w;Ljava/util/Collection;Z)V

    iget-object v2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->d:Ljava/util/Collection;

    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/a;->c:Lcom/android/tools/r8/t0;

    .line 5
    invoke-virtual {v1, v2, v3}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;->b(Ljava/util/Collection;Lcom/android/tools/r8/t0;)Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;

    move-result-object v1

    .line 6
    sget-object v2, Lcom/android/tools/r8/internal/A2;->c:Lcom/android/tools/r8/internal/A2;

    .line 7
    invoke-virtual {p0, v0, v2, v1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->a(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;)V

    .line 8
    sget-object v2, Lcom/android/tools/r8/internal/A2;->w:Lcom/android/tools/r8/internal/A2;

    .line 9
    invoke-virtual {p0, v0, v2, v1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/e;->a(Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;)V

    return-object v0
.end method
