.class public final synthetic Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda6;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/h1;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/h1;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda6;->f$0:Lcom/android/tools/r8/graph/h1;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o$$ExternalSyntheticLambda6;->f$0:Lcom/android/tools/r8/graph/h1;

    check-cast p1, Lcom/android/tools/r8/graph/h1;

    invoke-static {v0, p1}, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/o;->a(Lcom/android/tools/r8/graph/h1;Lcom/android/tools/r8/graph/h1;)Z

    move-result p1

    return p1
.end method
