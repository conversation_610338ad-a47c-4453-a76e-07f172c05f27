.class public Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "d"
.end annotation


# instance fields
.field public final a:Lcom/android/tools/r8/graph/E0;

.field public final b:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

.field public final c:Lcom/android/tools/r8/internal/PB;

.field public final d:Lcom/android/tools/r8/internal/PB;

.field public final e:Ljava/util/HashMap;

.field public final f:Ljava/util/HashMap;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;Lcom/android/tools/r8/internal/PB;Lcom/android/tools/r8/internal/PB;Ljava/util/HashMap;Ljava/util/HashMap;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->a:Lcom/android/tools/r8/graph/E0;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->b:Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/l;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->c:Lcom/android/tools/r8/internal/PB;

    .line 5
    iput-object p4, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->d:Lcom/android/tools/r8/internal/PB;

    .line 6
    iput-object p5, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->e:Ljava/util/HashMap;

    .line 7
    iput-object p6, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->f:Ljava/util/HashMap;

    return-void
.end method


# virtual methods
.method public a(Ljava/util/function/BiConsumer;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/function/BiConsumer<",
            "Lcom/android/tools/r8/graph/g1;",
            "Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->d:Lcom/android/tools/r8/internal/PB;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/PB;->g:Lcom/android/tools/r8/internal/cB;

    .line 3
    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/g1;

    .line 4
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/l1;

    .line 5
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->f:Ljava/util/HashMap;

    invoke-virtual {v3, v2}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$a;

    .line 6
    invoke-interface {p1, v1, v2}, Ljava/util/function/BiConsumer;->accept(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public b(Ljava/util/function/BiConsumer;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/function/BiConsumer<",
            "Lcom/android/tools/r8/graph/j1;",
            "Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->c:Lcom/android/tools/r8/internal/PB;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/PB;->g:Lcom/android/tools/r8/internal/cB;

    .line 3
    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    .line 4
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/x2;

    .line 5
    iget-object v3, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;->e:Ljava/util/HashMap;

    invoke-virtual {v3, v2}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;

    .line 6
    invoke-interface {p1, v1, v2}, Ljava/util/function/BiConsumer;->accept(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    return-void
.end method
