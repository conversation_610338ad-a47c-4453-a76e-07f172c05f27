.class public Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$c;,
        Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$a;,
        Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$b;,
        Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;
    }
.end annotation


# instance fields
.field public final a:Lcom/android/tools/r8/internal/PB;

.field public final b:Ljava/util/AbstractCollection;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/PB;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;->a:Lcom/android/tools/r8/internal/PB;

    .line 3
    check-cast p2, Ljava/util/AbstractCollection;

    iput-object p2, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;->b:Ljava/util/AbstractCollection;

    return-void
.end method


# virtual methods
.method public final a()Ljava/util/List;
    .locals 1

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;->b:Ljava/util/AbstractCollection;

    return-object v0
.end method

.method public a(Ljava/util/function/Consumer;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/function/Consumer<",
            "Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m$d;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/ir/desugar/desugaredlibrary/lint/m;->a:Lcom/android/tools/r8/internal/PB;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/PB;->g:Lcom/android/tools/r8/internal/cB;

    .line 3
    invoke-interface {v0, p1}, Ljava/util/Collection;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method
