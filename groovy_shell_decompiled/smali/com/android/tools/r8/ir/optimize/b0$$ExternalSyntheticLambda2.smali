.class public final synthetic Lcom/android/tools/r8/ir/optimize/b0$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/xp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/ir/optimize/b0;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/FW;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/ir/optimize/b0;Lcom/android/tools/r8/internal/FW;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/ir/optimize/b0$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/ir/optimize/b0;

    iput-object p2, p0, Lcom/android/tools/r8/ir/optimize/b0$$ExternalSyntheticLambda2;->f$1:Lcom/android/tools/r8/internal/FW;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/ir/optimize/b0$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/ir/optimize/b0;

    iget-object v1, p0, Lcom/android/tools/r8/ir/optimize/b0$$ExternalSyntheticLambda2;->f$1:Lcom/android/tools/r8/internal/FW;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/ir/optimize/b0;->b(Lcom/android/tools/r8/internal/FW;)Lcom/android/tools/r8/internal/JS;

    move-result-object v0

    return-object v0
.end method
