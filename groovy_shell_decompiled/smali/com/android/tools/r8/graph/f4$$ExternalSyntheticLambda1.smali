.class public final synthetic Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiFunction;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;

    invoke-direct {v0}, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;-><init>()V

    sput-object v0, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    new-instance v0, Lcom/android/tools/r8/graph/r0;

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    check-cast p2, Lcom/android/tools/r8/graph/e1;

    invoke-direct {v0, p1, p2}, Lcom/android/tools/r8/graph/r0;-><init>(ILcom/android/tools/r8/graph/e1;)V

    return-object v0
.end method
