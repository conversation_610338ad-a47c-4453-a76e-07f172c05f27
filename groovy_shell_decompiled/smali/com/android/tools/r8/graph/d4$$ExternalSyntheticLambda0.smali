.class public final synthetic Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/d4;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/E0;

.field public final synthetic f$2:Lcom/android/tools/r8/graph/V;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/d4;Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/V;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/graph/d4;

    iput-object p2, p0, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/E0;

    iput-object p3, p0, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/graph/V;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/graph/d4;

    iget-object v1, p0, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/E0;

    iget-object v2, p0, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/graph/V;

    check-cast p1, Lcom/android/tools/r8/graph/a4;

    invoke-virtual {v0, v1, v2, p1}, Lcom/android/tools/r8/graph/d4;->a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/V;Lcom/android/tools/r8/graph/a4;)V

    return-void
.end method
