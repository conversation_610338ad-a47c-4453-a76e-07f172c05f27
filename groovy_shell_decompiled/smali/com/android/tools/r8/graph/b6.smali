.class public abstract Lcom/android/tools/r8/graph/b6;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic d:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Lcom/android/tools/r8/graph/F5;

.field public c:Lcom/android/tools/r8/internal/gq0;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/F5;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/fq0;->c:Lcom/android/tools/r8/internal/eq0;

    iput-object v0, p0, Lcom/android/tools/r8/graph/b6;->c:Lcom/android/tools/r8/internal/gq0;

    .line 10
    iput-object p1, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    .line 11
    iput-object p2, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 1

    .line 15
    sget-boolean v0, Lcom/android/tools/r8/graph/b6;->d:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->c:Lcom/android/tools/r8/internal/gq0;

    .line 16
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/gq0;->d()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 17
    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 18
    :cond_1
    :goto_0
    sget-object v0, Lcom/android/tools/r8/internal/dq0;->c:Lcom/android/tools/r8/internal/cq0;

    iput-object v0, p0, Lcom/android/tools/r8/graph/b6;->c:Lcom/android/tools/r8/internal/gq0;

    return-void
.end method

.method public a(I)V
    .locals 0

    return-void
.end method

.method public a(ILcom/android/tools/r8/graph/z2;)V
    .locals 0

    .line 26
    iget-object p1, p2, Lcom/android/tools/r8/graph/z2;->e:Lcom/android/tools/r8/graph/y2;

    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    move-result p1

    packed-switch p1, :pswitch_data_0

    .line 60
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 61
    :pswitch_0
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->o0()Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->g(Lcom/android/tools/r8/graph/x2;)V

    goto :goto_0

    .line 62
    :pswitch_1
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->o0()Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->b(Lcom/android/tools/r8/graph/x2;)V

    goto :goto_0

    .line 68
    :pswitch_2
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->o0()Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/graph/x2;)V

    goto :goto_0

    .line 69
    :pswitch_3
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->o0()Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    .line 70
    iget-object p2, p1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, p2}, Lcom/android/tools/r8/graph/b6;->d(Lcom/android/tools/r8/graph/J2;)V

    .line 71
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/graph/x2;)V

    goto :goto_0

    .line 72
    :pswitch_4
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->o0()Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->h(Lcom/android/tools/r8/graph/x2;)V

    goto :goto_0

    .line 75
    :pswitch_5
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->o0()Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->e(Lcom/android/tools/r8/graph/x2;)V

    goto :goto_0

    .line 76
    :pswitch_6
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->n0()Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->b(Lcom/android/tools/r8/graph/l1;)V

    goto :goto_0

    .line 79
    :pswitch_7
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->n0()Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->d(Lcom/android/tools/r8/graph/l1;)V

    goto :goto_0

    .line 82
    :pswitch_8
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->n0()Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/l1;)V

    goto :goto_0

    .line 85
    :pswitch_9
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z2;->n0()Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->h(Lcom/android/tools/r8/graph/l1;)V

    :goto_0
    return-void

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public a(Lcom/android/tools/r8/graph/D0;)V
    .locals 2

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    .line 4
    iget-object v1, p1, Lcom/android/tools/r8/graph/D0;->g:Lcom/android/tools/r8/graph/z2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/z2;->o0()Lcom/android/tools/r8/graph/x2;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/B1;->c(Lcom/android/tools/r8/graph/x2;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 7
    iget-object v0, p1, Lcom/android/tools/r8/graph/D0;->g:Lcom/android/tools/r8/graph/z2;

    const/4 v1, 0x2

    invoke-virtual {p0, v1, v0}, Lcom/android/tools/r8/graph/b6;->a(ILcom/android/tools/r8/graph/z2;)V

    .line 13
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/graph/D0;->f:Lcom/android/tools/r8/graph/F2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/F2;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    .line 14
    iget-object v0, p1, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/graph/D0;I)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/D0;I)V
    .locals 5

    .line 86
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    .line 87
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/D0;->g:Lcom/android/tools/r8/graph/z2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/z2;->o0()Lcom/android/tools/r8/graph/x2;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/B1;->c(Lcom/android/tools/r8/graph/x2;)Z

    move-result v0

    .line 90
    sget-boolean v1, Lcom/android/tools/r8/graph/b6;->d:Z

    if-nez v1, :cond_1

    .line 91
    iget-object v1, p1, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-gt p2, v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    const/4 v1, 0x0

    :goto_1
    if-ge v1, p2, :cond_d

    .line 93
    iget-object v2, p1, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/O2;

    .line 94
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->I0()Lcom/android/tools/r8/graph/S2;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    move-result v3

    const/4 v4, 0x7

    if-eq v3, v4, :cond_a

    const/16 v4, 0x8

    if-eq v3, v4, :cond_8

    const/16 v4, 0xa

    if-eq v3, v4, :cond_7

    const/16 v4, 0x12

    if-eq v3, v4, :cond_3

    .line 122
    sget-boolean v3, Lcom/android/tools/r8/graph/b6;->d:Z

    if-nez v3, :cond_b

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->U0()Z

    move-result v3

    if-nez v3, :cond_b

    .line 123
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->V0()Z

    move-result v3

    if-nez v3, :cond_b

    .line 124
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->T0()Z

    move-result v3

    if-nez v3, :cond_b

    .line 125
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->Q0()Z

    move-result v3

    if-nez v3, :cond_b

    .line 126
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->c1()Z

    move-result v3

    if-nez v3, :cond_b

    .line 127
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->a1()Z

    move-result v2

    if-eqz v2, :cond_2

    goto/16 :goto_4

    .line 128
    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 129
    :cond_3
    iget-object v3, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v3

    invoke-static {p1, v3}, Lcom/android/tools/r8/internal/Fr0;->b(Lcom/android/tools/r8/graph/D0;Lcom/android/tools/r8/graph/B1;)Z

    move-result v3

    if-eqz v3, :cond_6

    .line 135
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->t0()Lcom/android/tools/r8/graph/P2;

    move-result-object v2

    .line 136
    sget-boolean v3, Lcom/android/tools/r8/graph/b6;->d:Z

    if-nez v3, :cond_5

    iget-object v3, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    invoke-interface {v3}, Lcom/android/tools/r8/graph/o0;->I()Z

    move-result v3

    if-eqz v3, :cond_4

    goto :goto_2

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 137
    :cond_5
    :goto_2
    iget-object v3, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    invoke-interface {v3}, Lcom/android/tools/r8/graph/o0;->b()Lcom/android/tools/r8/graph/H0;

    move-result-object v3

    .line 138
    iget-object v4, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    .line 139
    invoke-static {v2, v3, v4}, Lcom/android/tools/r8/internal/Fr0;->a(Lcom/android/tools/r8/graph/P2;Lcom/android/tools/r8/graph/H0;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/graph/l1;

    move-result-object v2

    if-eqz v2, :cond_b

    .line 142
    invoke-virtual {p0, v2}, Lcom/android/tools/r8/graph/b6;->e(Lcom/android/tools/r8/graph/l1;)V

    goto :goto_4

    .line 143
    :cond_6
    new-instance p1, Lcom/android/tools/r8/internal/jf;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Unsupported const dynamic in call site "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    .line 144
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    .line 145
    invoke-interface {v0}, Lcom/android/tools/r8/graph/o0;->getOrigin()Lcom/android/tools/r8/origin/Origin;

    move-result-object v0

    invoke-direct {p1, p2, v0}, Lcom/android/tools/r8/internal/jf;-><init>(Ljava/lang/String;Lcom/android/tools/r8/origin/Origin;)V

    throw p1

    .line 146
    :cond_7
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->G0()Lcom/android/tools/r8/graph/O2$l;

    move-result-object v2

    iget-object v2, v2, Lcom/android/tools/r8/graph/O2$m;->d:Lcom/android/tools/r8/graph/Z3;

    check-cast v2, Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v2}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    goto :goto_4

    .line 147
    :cond_8
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->B0()Lcom/android/tools/r8/graph/U2;

    move-result-object v2

    iget-object v2, v2, Lcom/android/tools/r8/graph/O2$m;->d:Lcom/android/tools/r8/graph/Z3;

    check-cast v2, Lcom/android/tools/r8/graph/z2;

    if-eqz v0, :cond_9

    const/4 v3, 0x1

    goto :goto_3

    :cond_9
    const/4 v3, 0x2

    .line 152
    :goto_3
    invoke-virtual {p0, v3, v2}, Lcom/android/tools/r8/graph/b6;->a(ILcom/android/tools/r8/graph/z2;)V

    goto :goto_4

    .line 155
    :cond_a
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->C0()Lcom/android/tools/r8/graph/V2;

    move-result-object v2

    iget-object v2, v2, Lcom/android/tools/r8/graph/O2$m;->d:Lcom/android/tools/r8/graph/Z3;

    check-cast v2, Lcom/android/tools/r8/graph/F2;

    invoke-virtual {p0, v2}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/graph/F2;)V

    .line 181
    :cond_b
    :goto_4
    iget-object v2, p0, Lcom/android/tools/r8/graph/b6;->c:Lcom/android/tools/r8/internal/gq0;

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/gq0;->e()Z

    move-result v2

    if-eqz v2, :cond_c

    goto :goto_5

    :cond_c
    add-int/lit8 v1, v1, 0x1

    goto/16 :goto_1

    :cond_d
    :goto_5
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/F2;)V
    .locals 3

    .line 182
    iget-object v0, p1, Lcom/android/tools/r8/graph/F2;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    .line 183
    iget-object p1, p1, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    iget-object p1, p1, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v2, p1, v1

    .line 184
    invoke-virtual {p0, v2}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public a(Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    .line 25
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method

.method public a(Lcom/android/tools/r8/graph/J2;Ljava/util/ListIterator;Z)V
    .locals 0

    .line 23
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method

.method public a(Lcom/android/tools/r8/graph/J2;Z)V
    .locals 0

    .line 24
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method

.method public abstract a(Lcom/android/tools/r8/graph/l1;)V
.end method

.method public a(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/internal/f8;)V
    .locals 0

    .line 20
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public a(Lcom/android/tools/r8/graph/u5;)V
    .locals 0

    return-void
.end method

.method public abstract a(Lcom/android/tools/r8/graph/x2;)V
.end method

.method public a(Lcom/android/tools/r8/internal/B40;)V
    .locals 1

    .line 19
    sget-boolean v0, Lcom/android/tools/r8/graph/b6;->d:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40;->k()Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return-void
.end method

.method public a(Lcom/android/tools/r8/internal/qa;)V
    .locals 0

    .line 21
    invoke-interface {p1}, Lcom/android/tools/r8/internal/qa;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public a(Lcom/android/tools/r8/internal/sa;)V
    .locals 0

    .line 22
    invoke-interface {p1}, Lcom/android/tools/r8/internal/sa;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->e(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public b()Lcom/android/tools/r8/internal/Fy;
    .locals 2

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/graph/b6;->d:Z

    if-nez v0, :cond_1

    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    invoke-interface {v1}, Lcom/android/tools/r8/graph/o0;->I()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_1
    :goto_0
    if-nez v0, :cond_3

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    invoke-interface {v0}, Lcom/android/tools/r8/graph/o0;->I()Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_1

    :cond_2
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 3
    :cond_3
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    invoke-interface {v0}, Lcom/android/tools/r8/graph/o0;->b()Lcom/android/tools/r8/graph/H0;

    move-result-object v0

    .line 4
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/i0;->a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    return-object v0
.end method

.method public abstract b(Lcom/android/tools/r8/graph/J2;)V
.end method

.method public b(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public b(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/internal/f8;)V
    .locals 0

    .line 6
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->e(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public abstract b(Lcom/android/tools/r8/graph/x2;)V
.end method

.method public c()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->e2:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method

.method public c(Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method

.method public abstract c(Lcom/android/tools/r8/graph/l1;)V
.end method

.method public final c(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->d(Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public d(Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    .line 10
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->f(Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method

.method public d(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 9
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->c(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public d(Lcom/android/tools/r8/graph/x2;)V
    .locals 4

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/graph/b6;->d:Z

    if-nez v0, :cond_1

    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    invoke-interface {v1}, Lcom/android/tools/r8/graph/o0;->I()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 2
    :cond_1
    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    invoke-interface {v1}, Lcom/android/tools/r8/graph/o0;->b()Lcom/android/tools/r8/graph/H0;

    move-result-object v1

    .line 3
    iget-object v2, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b6;->b()Lcom/android/tools/r8/internal/Fy;

    move-result-object v3

    invoke-static {p1, v1, v2, v3}, Lcom/android/tools/r8/internal/JI;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/H0;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/JI;

    move-result-object v1

    .line 4
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/JI;->b()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 5
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/graph/x2;)V

    goto :goto_2

    :cond_2
    if-nez v0, :cond_4

    .line 7
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/JI;->e()Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 8
    :cond_4
    :goto_1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->g(Lcom/android/tools/r8/graph/x2;)V

    :goto_2
    return-void
.end method

.method public e(Lcom/android/tools/r8/graph/J2;)V
    .locals 1

    const/4 v0, 0x1

    .line 1
    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/graph/J2;Z)V

    return-void
.end method

.method public abstract e(Lcom/android/tools/r8/graph/l1;)V
.end method

.method public abstract e(Lcom/android/tools/r8/graph/x2;)V
.end method

.method public abstract f(Lcom/android/tools/r8/graph/J2;)V
.end method

.method public f(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->e(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public f(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->e(Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public abstract g(Lcom/android/tools/r8/graph/l1;)V
.end method

.method public abstract g(Lcom/android/tools/r8/graph/x2;)V
.end method

.method public h(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/b6;->g(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public abstract h(Lcom/android/tools/r8/graph/x2;)V
.end method
