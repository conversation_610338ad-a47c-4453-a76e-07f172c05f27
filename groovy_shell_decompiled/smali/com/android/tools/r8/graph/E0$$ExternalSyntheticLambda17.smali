.class public final synthetic Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda17;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/E0;

.field public final synthetic f$1:Ljava/util/List;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/ZA;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/E0;Ljava/util/List;Lcom/android/tools/r8/internal/ZA;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda17;->f$0:Lcom/android/tools/r8/graph/E0;

    iput-object p2, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda17;->f$1:Ljava/util/List;

    iput-object p3, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda17;->f$2:Lcom/android/tools/r8/internal/ZA;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda17;->f$0:Lcom/android/tools/r8/graph/E0;

    iget-object v1, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda17;->f$1:Ljava/util/List;

    iget-object v2, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda17;->f$2:Lcom/android/tools/r8/internal/ZA;

    check-cast p1, Lcom/android/tools/r8/graph/D3$e;

    invoke-virtual {v0, v1, v2, p1}, Lcom/android/tools/r8/graph/E0;->a(Ljava/util/List;Lcom/android/tools/r8/internal/ZA;Lcom/android/tools/r8/graph/D3$e;)V

    return-void
.end method
