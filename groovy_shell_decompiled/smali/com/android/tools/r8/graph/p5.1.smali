.class public abstract Lcom/android/tools/r8/graph/p5;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/graph/m5;


# static fields
.field public static final synthetic g:Z = true


# instance fields
.field public final a:Ljava/util/IdentityHashMap;

.field public final b:Ljava/util/Set;

.field public final c:Ljava/util/Set;

.field public final d:Ljava/util/Set;

.field public final e:Ljava/util/IdentityHashMap;

.field public f:Ljava/util/IdentityHashMap;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/IdentityHashMap;

    invoke-direct {v0}, Ljava/util/IdentityHashMap;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/p5;->a:Ljava/util/IdentityHashMap;

    .line 6
    invoke-static {}, Lcom/android/tools/r8/internal/kj0;->c()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/graph/p5;->b:Ljava/util/Set;

    .line 9
    invoke-static {}, Lcom/android/tools/r8/internal/kj0;->c()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/graph/p5;->c:Ljava/util/Set;

    .line 18
    invoke-static {}, Lcom/android/tools/r8/internal/kj0;->c()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/graph/p5;->d:Ljava/util/Set;

    .line 21
    new-instance v0, Ljava/util/IdentityHashMap;

    invoke-direct {v0}, Ljava/util/IdentityHashMap;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/p5;->e:Ljava/util/IdentityHashMap;

    .line 28
    new-instance v0, Ljava/util/IdentityHashMap;

    invoke-direct {v0}, Ljava/util/IdentityHashMap;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/p5;->f:Ljava/util/IdentityHashMap;

    return-void
.end method

.method public static synthetic a(Ljava/util/function/Consumer;Lcom/android/tools/r8/graph/E2;)Lcom/android/tools/r8/internal/gq0;
    .locals 0

    .line 69
    invoke-interface {p0, p1}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    .line 70
    sget-object p0, Lcom/android/tools/r8/internal/fq0;->c:Lcom/android/tools/r8/internal/eq0;

    return-object p0
.end method

.method public static synthetic a(Ljava/util/function/Consumer;Lcom/android/tools/r8/internal/DP;)Lcom/android/tools/r8/internal/gq0;
    .locals 0

    .line 71
    invoke-interface {p0, p1}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    .line 72
    sget-object p0, Lcom/android/tools/r8/internal/fq0;->c:Lcom/android/tools/r8/internal/eq0;

    return-object p0
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/h;Lcom/android/tools/r8/internal/Vu0;Lcom/android/tools/r8/graph/J2;Ljava/util/Set;)V
    .locals 0

    .line 73
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/graph/h;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p0

    if-eqz p0, :cond_0

    .line 75
    invoke-virtual {p1, p0}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Object;)Z

    .line 77
    :cond_0
    invoke-virtual {p1, p3}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Iterable;)V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/K5;Lcom/android/tools/r8/graph/j1;)Z
    .locals 0

    .line 64
    iget-object p0, p0, Lcom/android/tools/r8/graph/K5;->g:Ljava/util/Set;

    .line 65
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    invoke-interface {p0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method public static synthetic a(Ljava/util/Set;Lcom/android/tools/r8/graph/E2;)Z
    .locals 0

    .line 103
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    invoke-interface {p0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method public static synthetic a(Ljava/util/Set;Ljava/util/Map$Entry;)Z
    .locals 0

    .line 102
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    invoke-interface {p0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method public static synthetic b(Ljava/util/Set;Lcom/android/tools/r8/graph/E2;)Z
    .locals 0

    .line 14
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    invoke-interface {p0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method public static synthetic c(Ljava/util/Set;Lcom/android/tools/r8/graph/E2;)Z
    .locals 0

    .line 9
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    invoke-interface {p0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/d1;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/graph/p5;
    .locals 3

    .line 31
    new-instance v0, Lcom/android/tools/r8/graph/o5;

    const/4 v1, 0x1

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/graph/o5;-><init>(ZLcom/android/tools/r8/shaking/N0;)V

    .line 32
    invoke-virtual {v0, p0, p1, p2, p3}, Lcom/android/tools/r8/graph/o5;->a(Lcom/android/tools/r8/graph/p5;Lcom/android/tools/r8/graph/d1;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/graph/o5;

    move-result-object p2

    .line 33
    sget-boolean p3, Lcom/android/tools/r8/graph/o5;->i:Z

    if-nez p3, :cond_1

    iget-object v0, p2, Lcom/android/tools/r8/graph/o5;->h:Lcom/android/tools/r8/graph/n5;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 34
    :cond_1
    :goto_0
    iget-object v0, p2, Lcom/android/tools/r8/graph/p5;->f:Ljava/util/IdentityHashMap;

    if-nez v0, :cond_2

    .line 35
    invoke-virtual {p2, p1}, Lcom/android/tools/r8/graph/o5;->a(Lcom/android/tools/r8/graph/d1;)V

    :cond_2
    if-nez p3, :cond_3

    .line 37
    invoke-virtual {p2, p1}, Lcom/android/tools/r8/graph/o5;->b(Lcom/android/tools/r8/graph/d1;)V

    .line 38
    :cond_3
    iput-object v2, p2, Lcom/android/tools/r8/graph/o5;->h:Lcom/android/tools/r8/graph/n5;

    return-object p2
.end method

.method public final a(Lcom/android/tools/r8/graph/v0;Lcom/android/tools/r8/internal/TY;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Gp0;)Lcom/android/tools/r8/graph/p5;
    .locals 1

    .line 67
    new-instance v0, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0, p1, p2, p3}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/p5;Lcom/android/tools/r8/graph/d1;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;)V

    const-string p1, "Rewrite ObjectAllocationInfoCollectionImpl"

    invoke-virtual {p4, p1, v0}, Lcom/android/tools/r8/internal/Gp0;->a(Ljava/lang/String;Lcom/android/tools/r8/internal/xp0;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/p5;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;Ljava/util/function/Function;Ljava/util/function/Function;Lcom/android/tools/r8/graph/h;)Lcom/android/tools/r8/internal/gq0;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Vu0;

    const/4 v1, 0x2

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/Vu0;-><init>(I)V

    .line 2
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/h;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_0

    .line 4
    iget-object p1, p0, Lcom/android/tools/r8/graph/p5;->f:Ljava/util/IdentityHashMap;

    new-instance v1, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda2;

    invoke-direct {v1, p4, v0}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/graph/h;Lcom/android/tools/r8/internal/Vu0;)V

    invoke-virtual {p1, v1}, Ljava/util/IdentityHashMap;->forEach(Ljava/util/function/BiConsumer;)V

    goto :goto_0

    .line 13
    :cond_0
    invoke-virtual {p4, p1}, Lcom/android/tools/r8/graph/h;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p4

    if-nez p4, :cond_2

    .line 17
    iget-object p4, p0, Lcom/android/tools/r8/graph/p5;->f:Ljava/util/IdentityHashMap;

    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {p4, p1, v1}, Ljava/util/Map;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Ljava/lang/Iterable;

    invoke-virtual {v0, p4}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Iterable;)V

    .line 19
    iget-object p4, p0, Lcom/android/tools/r8/graph/p5;->e:Ljava/util/IdentityHashMap;

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    invoke-interface {p4, p1, v1}, Ljava/util/Map;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p4

    if-eqz p4, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Lcom/android/tools/r8/internal/DP;

    .line 20
    invoke-interface {p3, p4}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Lcom/android/tools/r8/internal/gq0;

    .line 21
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/gq0;->c()Z

    move-result p4

    if-eqz p4, :cond_1

    .line 22
    sget-object p1, Lcom/android/tools/r8/internal/dq0;->c:Lcom/android/tools/r8/internal/cq0;

    return-object p1

    .line 26
    :cond_2
    invoke-virtual {v0, p4}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Object;)Z

    .line 30
    :cond_3
    :goto_0
    new-instance p1, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda4;

    invoke-direct {p1, p0, p2, v0, p3}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda4;-><init>(Lcom/android/tools/r8/graph/p5;Ljava/util/function/Function;Lcom/android/tools/r8/internal/Vu0;Ljava/util/function/Function;)V

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Vu0;->a(Ljava/util/function/Function;)Lcom/android/tools/r8/internal/gq0;

    move-result-object p1

    return-object p1
.end method

.method public final a(Ljava/util/function/Function;Lcom/android/tools/r8/internal/Vu0;Ljava/util/function/Function;Lcom/android/tools/r8/graph/E0;)Lcom/android/tools/r8/internal/gq0;
    .locals 2

    .line 78
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/b1;->g0()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 79
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/b1;->f0()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    .line 80
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/graph/p5;->c(Lcom/android/tools/r8/graph/E2;)Z

    move-result v1

    if-nez v1, :cond_0

    .line 81
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/graph/p5;->e(Lcom/android/tools/r8/graph/E2;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 82
    :cond_0
    invoke-interface {p1, v0}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/gq0;

    .line 83
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/gq0;->c()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 84
    sget-object p1, Lcom/android/tools/r8/internal/dq0;->c:Lcom/android/tools/r8/internal/cq0;

    return-object p1

    .line 88
    :cond_1
    iget-object p1, p0, Lcom/android/tools/r8/graph/p5;->f:Ljava/util/IdentityHashMap;

    iget-object v0, p4, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    .line 89
    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {p1, v0, v1}, Ljava/util/Map;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Iterable;

    .line 90
    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Iterable;)V

    .line 93
    iget-object p1, p0, Lcom/android/tools/r8/graph/p5;->e:Ljava/util/IdentityHashMap;

    iget-object p2, p4, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p4

    invoke-interface {p1, p2, p4}, Ljava/util/Map;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/internal/DP;

    .line 94
    invoke-interface {p3, p2}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/internal/gq0;

    .line 95
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/gq0;->c()Z

    move-result p2

    if-eqz p2, :cond_2

    .line 96
    sget-object p1, Lcom/android/tools/r8/internal/dq0;->c:Lcom/android/tools/r8/internal/cq0;

    return-object p1

    .line 99
    :cond_3
    sget-object p1, Lcom/android/tools/r8/internal/fq0;->c:Lcom/android/tools/r8/internal/eq0;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;Ljava/util/function/Consumer;Ljava/util/function/Consumer;Lcom/android/tools/r8/graph/h;)V
    .locals 1

    .line 68
    new-instance v0, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda5;

    invoke-direct {v0, p2}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda5;-><init>(Ljava/util/function/Consumer;)V

    new-instance p2, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda6;

    invoke-direct {p2, p3}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda6;-><init>(Ljava/util/function/Consumer;)V

    invoke-virtual {p0, p1, v0, p2, p4}, Lcom/android/tools/r8/graph/p5;->a(Lcom/android/tools/r8/graph/J2;Ljava/util/function/Function;Ljava/util/function/Function;Lcom/android/tools/r8/graph/h;)Lcom/android/tools/r8/internal/gq0;

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/K5;)V
    .locals 2

    .line 48
    iget-object p1, p1, Lcom/android/tools/r8/graph/K5;->e:Ljava/util/Set;

    .line 49
    invoke-interface {p1}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 52
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->a:Ljava/util/IdentityHashMap;

    .line 53
    invoke-virtual {v0}, Ljava/util/IdentityHashMap;->entrySet()Ljava/util/Set;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda1;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda1;-><init>(Ljava/util/Set;)V

    .line 54
    invoke-interface {v0, v1}, Ljava/util/Set;->removeIf(Ljava/util/function/Predicate;)Z

    .line 55
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->b:Ljava/util/Set;

    new-instance v1, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda8;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda8;-><init>(Ljava/util/Set;)V

    invoke-interface {v0, v1}, Ljava/util/Set;->removeIf(Ljava/util/function/Predicate;)Z

    .line 57
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->c:Ljava/util/Set;

    new-instance v1, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda9;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda9;-><init>(Ljava/util/Set;)V

    invoke-interface {v0, v1}, Ljava/util/Set;->removeIf(Ljava/util/function/Predicate;)Z

    .line 59
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->d:Ljava/util/Set;

    new-instance v1, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda10;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda10;-><init>(Ljava/util/Set;)V

    .line 60
    invoke-interface {v0, v1}, Ljava/util/Set;->removeIf(Ljava/util/function/Predicate;)Z

    move-result v0

    .line 62
    sget-boolean v1, Lcom/android/tools/r8/graph/p5;->g:Z

    if-nez v1, :cond_2

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    const-string v0, "Unexpected removal of an interface marking an unknown hierarchy."

    invoke-direct {p1, v0}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p1

    .line 63
    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->e:Ljava/util/IdentityHashMap;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda3;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda3;-><init>(Ljava/util/Map;)V

    invoke-interface {p1, v1}, Ljava/util/Set;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public final a(Ljava/util/function/BiConsumer;)V
    .locals 1

    .line 66
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->a:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->forEach(Ljava/util/function/BiConsumer;)V

    return-void
.end method

.method public final a(Ljava/util/function/Consumer;)V
    .locals 1

    .line 100
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->e:Ljava/util/IdentityHashMap;

    invoke-virtual {v0}, Ljava/util/IdentityHashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    .line 101
    invoke-interface {v0, p1}, Ljava/util/Set;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public abstract a(Ljava/util/function/Consumer;Lcom/android/tools/r8/shaking/i;)V
.end method

.method public final a(Lcom/android/tools/r8/graph/E2;)Z
    .locals 3

    .line 39
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->f:Ljava/util/IdentityHashMap;

    iget-object v1, p1, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0, v1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    .line 42
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result v0

    const/4 v2, 0x0

    if-nez v0, :cond_1

    return v2

    .line 45
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->c:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->d:Ljava/util/Set;

    .line 46
    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 47
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/p5;->b(Lcom/android/tools/r8/graph/E2;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v1, v2

    :cond_3
    :goto_0
    return v1
.end method

.method public final b(Lcom/android/tools/r8/graph/K5;)Lcom/android/tools/r8/graph/p5;
    .locals 4

    .line 1
    iget-object v0, p1, Lcom/android/tools/r8/graph/K5;->g:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->a:Ljava/util/IdentityHashMap;

    .line 3
    invoke-virtual {v0}, Ljava/util/IdentityHashMap;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    .line 4
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 5
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 6
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Set;

    .line 7
    new-instance v3, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda7;

    invoke-direct {v3, p1}, Lcom/android/tools/r8/graph/p5$$ExternalSyntheticLambda7;-><init>(Lcom/android/tools/r8/graph/K5;)V

    invoke-interface {v2, v3}, Ljava/util/Set;->removeIf(Ljava/util/function/Predicate;)Z

    .line 10
    invoke-interface {v2}, Ljava/util/Set;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_0

    .line 11
    iget-object v2, p0, Lcom/android/tools/r8/graph/p5;->b:Ljava/util/Set;

    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/E2;

    invoke-interface {v2, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 12
    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    goto :goto_0

    :cond_1
    return-object p0
.end method

.method public final b(Lcom/android/tools/r8/graph/E2;)Z
    .locals 1

    .line 13
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->e:Ljava/util/IdentityHashMap;

    iget-object p1, p1, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final c(Lcom/android/tools/r8/graph/E2;)Z
    .locals 1

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    return p1

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->a:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 5
    sget-boolean v0, Lcom/android/tools/r8/graph/p5;->g:Z

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->a:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Set;

    invoke-interface {p1}, Ljava/util/Set;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_2
    :goto_0
    const/4 p1, 0x1

    return p1

    .line 8
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->b:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public final d(Lcom/android/tools/r8/graph/E2;)Z
    .locals 1

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/p5;->c(Lcom/android/tools/r8/graph/E2;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 2
    :cond_0
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/p5;->a(Lcom/android/tools/r8/graph/E2;)Z

    move-result p1

    if-eqz p1, :cond_2

    :cond_1
    const/4 p1, 0x1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final e(Lcom/android/tools/r8/graph/E2;)Z
    .locals 1

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/graph/p5;->d:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method
