.class public abstract Lcom/android/tools/r8/graph/m0;
.super Lcom/android/tools/r8/graph/b6;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/H0;)V
    .locals 0

    .line 1
    check-cast p2, Lcom/android/tools/r8/graph/F5;

    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/graph/b6;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/F5;)V

    return-void
.end method


# virtual methods
.method public a(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    return-void
.end method

.method public a(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    return-void
.end method

.method public b(Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    return-void
.end method

.method public b(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    return-void
.end method

.method public c(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    return-void
.end method

.method public e(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    return-void
.end method

.method public e(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    return-void
.end method

.method public f(Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    return-void
.end method

.method public g(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    return-void
.end method

.method public g(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    return-void
.end method

.method public h(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    return-void
.end method
