.class public final Lcom/android/tools/r8/graph/E1;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/internal/cB;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/B1;)V
    .locals 12

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->i3:Lcom/android/tools/r8/graph/J2;

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->C1:Lcom/android/tools/r8/graph/J2;

    const/4 v2, 0x1

    new-array v3, v2, [Lcom/android/tools/r8/graph/J2;

    const/4 v4, 0x0

    aput-object v1, v3, v4

    .line 3
    invoke-virtual {p1, v0, v3}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    const-string v3, "position"

    invoke-virtual {p1, v0, v1, v3}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object v5

    .line 4
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->i3:Lcom/android/tools/r8/graph/J2;

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->C1:Lcom/android/tools/r8/graph/J2;

    new-array v2, v2, [Lcom/android/tools/r8/graph/J2;

    aput-object v1, v2, v4

    .line 5
    invoke-virtual {p1, v0, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    const-string v2, "limit"

    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object v6

    .line 6
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->i3:Lcom/android/tools/r8/graph/J2;

    new-array v1, v4, [Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1, v0, v1}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    const-string v2, "mark"

    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object v7

    .line 7
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->i3:Lcom/android/tools/r8/graph/J2;

    new-array v1, v4, [Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1, v0, v1}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    const-string v2, "reset"

    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object v8

    .line 8
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->i3:Lcom/android/tools/r8/graph/J2;

    new-array v1, v4, [Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1, v0, v1}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    const-string v2, "clear"

    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object v9

    .line 9
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->i3:Lcom/android/tools/r8/graph/J2;

    new-array v1, v4, [Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1, v0, v1}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    const-string v2, "flip"

    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object v10

    .line 10
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->i3:Lcom/android/tools/r8/graph/J2;

    new-array v1, v4, [Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1, v0, v1}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    const-string v2, "rewind"

    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object v11

    .line 12
    invoke-static/range {v5 .. v11}, Lcom/android/tools/r8/internal/cB;->a(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/cB;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/graph/E1;->a:Lcom/android/tools/r8/internal/cB;

    return-void
.end method
