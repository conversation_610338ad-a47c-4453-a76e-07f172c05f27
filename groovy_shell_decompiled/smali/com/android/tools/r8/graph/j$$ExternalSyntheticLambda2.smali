.class public final synthetic Lcom/android/tools/r8/graph/j$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/kq0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/jq0;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/jq0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/graph/j$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/jq0;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/graph/j$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/jq0;

    check-cast p1, Lcom/android/tools/r8/graph/J2;

    check-cast p2, Lcom/android/tools/r8/graph/E0;

    check-cast p3, Ljava/lang/Boolean;

    invoke-static {v0, p1, p2, p3}, Lcom/android/tools/r8/graph/j;->a(Lcom/android/tools/r8/internal/jq0;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/E0;Ljava/lang/Boolean;)Lcom/android/tools/r8/internal/gq0;

    move-result-object p1

    return-object p1
.end method
