.class public final synthetic Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda18;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/E0;

.field public final synthetic f$1:Ljava/util/function/Consumer;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/E0;Ljava/util/function/Consumer;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda18;->f$0:Lcom/android/tools/r8/graph/E0;

    iput-object p2, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda18;->f$1:Ljava/util/function/Consumer;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda18;->f$0:Lcom/android/tools/r8/graph/E0;

    iget-object v1, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda18;->f$1:Ljava/util/function/Consumer;

    check-cast p1, Lcom/android/tools/r8/graph/g1;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/graph/E0;->a(Ljava/util/function/Consumer;Lcom/android/tools/r8/graph/g1;)V

    return-void
.end method
