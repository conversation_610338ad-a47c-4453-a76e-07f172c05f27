.class public final synthetic Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda20;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/d1;

.field public final synthetic f$1:Ljava/util/function/BiPredicate;

.field public final synthetic f$2:Ljava/util/function/BiConsumer;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/d1;Ljava/util/function/BiPredicate;Ljava/util/function/BiConsumer;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda20;->f$0:Lcom/android/tools/r8/graph/d1;

    iput-object p2, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda20;->f$1:Ljava/util/function/BiPredicate;

    iput-object p3, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda20;->f$2:Ljava/util/function/BiConsumer;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda20;->f$0:Lcom/android/tools/r8/graph/d1;

    iget-object v1, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda20;->f$1:Ljava/util/function/BiPredicate;

    iget-object v2, p0, Lcom/android/tools/r8/graph/E0$$ExternalSyntheticLambda20;->f$2:Ljava/util/function/BiConsumer;

    check-cast p1, Lcom/android/tools/r8/graph/J2;

    invoke-static {v0, v1, v2, p1}, Lcom/android/tools/r8/graph/E0;->a(Lcom/android/tools/r8/graph/d1;Ljava/util/function/BiPredicate;Ljava/util/function/BiConsumer;Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method
