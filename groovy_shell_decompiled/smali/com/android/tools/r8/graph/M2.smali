.class public abstract Lcom/android/tools/r8/graph/M2;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic a:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;
    .locals 2

    .line 18
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 20
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object p1

    .line 23
    :cond_0
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/graph/y;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object v0

    if-nez v0, :cond_2

    .line 25
    sget-boolean p0, Lcom/android/tools/r8/graph/M2;->a:Z

    if-eqz p0, :cond_1

    return-object p1

    .line 26
    :cond_1
    new-instance p0, Ljava/lang/AssertionError;

    const-string p1, "We should not have found an upper bound if the hierarchy is missing"

    invoke-direct {p0, p1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p0

    .line 29
    :cond_2
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->h0()Z

    move-result v1

    if-eqz v1, :cond_4

    .line 30
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->G()Lcom/android/tools/r8/graph/r2;

    move-result-object v1

    invoke-static {v1, p0}, Lcom/android/tools/r8/utils/a;->a(Lcom/android/tools/r8/graph/r2;Lcom/android/tools/r8/graph/y;)Z

    move-result v1

    if-nez v1, :cond_4

    .line 31
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->l1()Z

    move-result v1

    if-nez v1, :cond_3

    goto :goto_0

    .line 35
    :cond_3
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->d1()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    invoke-static {p0, p1}, Lcom/android/tools/r8/graph/M2;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    return-object p0

    :cond_4
    :goto_0
    return-object p1
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/graph/J2;
    .locals 2

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    .line 7
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 8
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->c()Lcom/android/tools/r8/internal/C50;

    move-result-object p0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/C50;->b(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    return-object p0

    .line 10
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->r()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 11
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->a()Lcom/android/tools/r8/internal/T3;

    move-result-object p1

    .line 12
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/T3;->Q()Lcom/android/tools/r8/internal/sr0;

    move-result-object v1

    invoke-static {p0, v1}, Lcom/android/tools/r8/graph/M2;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    .line 13
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/T3;->T()I

    move-result p1

    .line 14
    iget-object p0, p0, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    .line 15
    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/graph/I2;->a(ILcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/I2;

    move-result-object p0

    invoke-virtual {v0, p0}, Lcom/android/tools/r8/graph/B1;->c(Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    return-object p0

    .line 16
    :cond_1
    sget-boolean p0, Lcom/android/tools/r8/graph/M2;->a:Z

    if-nez p0, :cond_3

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->w()Z

    move-result p0

    if-eqz p0, :cond_2

    goto :goto_0

    :cond_2
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 17
    :cond_3
    :goto_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object p0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/Dd;->b(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    return-object p0
.end method

.method public static a(Ljava/lang/Iterable;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/graph/J2;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/graph/M2$$ExternalSyntheticLambda0;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/graph/M2$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 3
    new-instance v1, Lcom/android/tools/r8/internal/QI;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/internal/QI;-><init>(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/Hx;)V

    .line 4
    invoke-static {v1, p1}, Lcom/android/tools/r8/internal/sr0;->a(Ljava/lang/Iterable;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p0

    .line 5
    invoke-static {p1, p0}, Lcom/android/tools/r8/graph/M2;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/D5;)Z
    .locals 2

    .line 36
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    .line 39
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-nez v0, :cond_6

    .line 40
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->l1()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 41
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->d1()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-nez v0, :cond_6

    .line 42
    :cond_1
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->U0()Lcom/android/tools/r8/graph/L2;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/L2;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_0

    .line 48
    :cond_2
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 49
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    invoke-interface {p0, v0, p1}, Lcom/android/tools/r8/graph/d1;->a(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p1

    const/4 v0, 0x0

    if-nez p1, :cond_3

    return v0

    .line 50
    :cond_3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->h0()Z

    move-result v1

    if-eqz v1, :cond_4

    .line 51
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->G()Lcom/android/tools/r8/graph/r2;

    move-result-object p1

    invoke-static {p1, p0}, Lcom/android/tools/r8/utils/a;->a(Lcom/android/tools/r8/graph/r2;Lcom/android/tools/r8/graph/y;)Z

    move-result p0

    return p0

    .line 52
    :cond_4
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h;->h()Z

    move-result v1

    if-eqz v1, :cond_5

    .line 53
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->V()Lcom/android/tools/r8/graph/y;

    move-result-object p0

    .line 54
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j;

    invoke-static {p1, p2, p0, v0}, Lcom/android/tools/r8/graph/e;->a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/o0;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/j;)Lcom/android/tools/r8/internal/u20;

    move-result-object p0

    .line 55
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/T6;->d()Z

    move-result p0

    return p0

    :cond_5
    return v0

    :cond_6
    :goto_0
    return v1
.end method

.method public static synthetic b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/sr0;
    .locals 0

    .line 1
    invoke-virtual {p1, p0}, Lcom/android/tools/r8/graph/J2;->b(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p0

    return-object p0
.end method
