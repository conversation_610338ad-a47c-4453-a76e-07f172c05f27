.class public Lcom/android/tools/r8/graph/a4;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic e:Z = true


# instance fields
.field public final a:I

.field public final b:Lcom/android/tools/r8/graph/J2;

.field public final c:Lcom/android/tools/r8/graph/J2;

.field public final d:Lcom/android/tools/r8/graph/I2;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/I2;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/graph/a4;->e:Z

    if-nez v0, :cond_1

    if-eqz p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 3
    :cond_1
    :goto_0
    iput p1, p0, Lcom/android/tools/r8/graph/a4;->a:I

    .line 4
    iput-object p2, p0, Lcom/android/tools/r8/graph/a4;->b:Lcom/android/tools/r8/graph/J2;

    .line 5
    iput-object p3, p0, Lcom/android/tools/r8/graph/a4;->c:Lcom/android/tools/r8/graph/J2;

    .line 6
    iput-object p4, p0, Lcom/android/tools/r8/graph/a4;->d:Lcom/android/tools/r8/graph/I2;

    return-void
.end method


# virtual methods
.method public a()I
    .locals 1

    .line 6
    iget v0, p0, Lcom/android/tools/r8/graph/a4;->a:I

    return v0
.end method

.method public final a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/graph/J2;
    .locals 3

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/a4;->d()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    if-nez v0, :cond_2

    .line 9
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/a4;->b()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/graph/y;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object v1

    if-eqz v1, :cond_2

    .line 10
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/E0;->R0()Lcom/android/tools/r8/graph/f3;

    move-result-object v2

    if-eqz v2, :cond_2

    .line 11
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/E0;->R0()Lcom/android/tools/r8/graph/f3;

    move-result-object v0

    .line 12
    iget-object v1, v0, Lcom/android/tools/r8/graph/f3;->a:Lcom/android/tools/r8/graph/J2;

    if-eqz v1, :cond_0

    move-object v0, v1

    goto :goto_0

    .line 13
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/f3;->b()Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    .line 14
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/shaking/i;

    .line 15
    iget-object p1, p1, Lcom/android/tools/r8/shaking/i;->r:Ljava/util/Set;

    .line 16
    invoke-interface {p1, v0}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_1

    const/4 p1, 0x0

    return-object p1

    .line 17
    :cond_1
    iget-object v0, v0, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    :cond_2
    :goto_0
    return-object v0
.end method

.method public final a(Ljava/util/function/Consumer;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/a4;->b:Lcom/android/tools/r8/graph/J2;

    if-eqz v0, :cond_0

    .line 2
    invoke-interface {p1, v0}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/graph/a4;->c:Lcom/android/tools/r8/graph/J2;

    if-eqz v0, :cond_1

    .line 5
    invoke-interface {p1, v0}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    :cond_1
    return-void
.end method

.method public b()Lcom/android/tools/r8/graph/J2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/a4;->b:Lcom/android/tools/r8/graph/J2;

    return-object v0
.end method

.method public c()Lcom/android/tools/r8/graph/I2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/a4;->d:Lcom/android/tools/r8/graph/I2;

    return-object v0
.end method

.method public d()Lcom/android/tools/r8/graph/J2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/a4;->c:Lcom/android/tools/r8/graph/J2;

    return-object v0
.end method

.method public final e()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/a4;->d:Lcom/android/tools/r8/graph/I2;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 6

    .line 1
    iget v0, p0, Lcom/android/tools/r8/graph/a4;->a:I

    invoke-static {v0}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/graph/a4;->b:Lcom/android/tools/r8/graph/J2;

    .line 2
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/J2;->Y0()Ljava/lang/String;

    move-result-object v1

    .line 3
    iget-object v2, p0, Lcom/android/tools/r8/graph/a4;->c:Lcom/android/tools/r8/graph/J2;

    if-nez v2, :cond_0

    const-string v2, "null"

    goto :goto_0

    :cond_0
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->Y0()Ljava/lang/String;

    move-result-object v2

    .line 4
    :goto_0
    iget-object v3, p0, Lcom/android/tools/r8/graph/a4;->d:Lcom/android/tools/r8/graph/I2;

    if-nez v3, :cond_1

    const-string v3, "(anonymous)"

    goto :goto_1

    :cond_1
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v3

    :goto_1
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "[access : "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v4, ", inner: "

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", outer: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", innerName: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
