.class public final Lcom/android/tools/r8/graph/A4;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/graph/z4;


# instance fields
.field public final b:Lcom/android/tools/r8/graph/H0;

.field public final c:Lcom/android/tools/r8/graph/H0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/H0;Lcom/android/tools/r8/graph/H0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/graph/A4;->b:Lcom/android/tools/r8/graph/H0;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/graph/A4;->c:Lcom/android/tools/r8/graph/H0;

    return-void
.end method


# virtual methods
.method public final d()Lcom/android/tools/r8/graph/j1;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/A4;->b:Lcom/android/tools/r8/graph/H0;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    return-object v0
.end method

.method public final getHolder()Lcom/android/tools/r8/graph/E0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/A4;->b:Lcom/android/tools/r8/graph/H0;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->getHolder()Lcom/android/tools/r8/graph/E0;

    move-result-object v0

    return-object v0
.end method

.method public final getReference()Lcom/android/tools/r8/graph/x2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/A4;->b:Lcom/android/tools/r8/graph/H0;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    return-object v0
.end method

.method public final l()Lcom/android/tools/r8/graph/H0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/A4;->b:Lcom/android/tools/r8/graph/H0;

    return-object v0
.end method

.method public final m()Lcom/android/tools/r8/graph/H0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/A4;->c:Lcom/android/tools/r8/graph/H0;

    return-object v0
.end method
