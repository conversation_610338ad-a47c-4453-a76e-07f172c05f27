.class public final synthetic Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda17;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda17;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda17;

    invoke-direct {v0}, Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda17;-><init>()V

    sput-object v0, Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda17;->INSTANCE:Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda17;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->Y0()Ljava/util/List;

    move-result-object p1

    check-cast p1, Ljava/util/Collection;

    return-object p1
.end method
