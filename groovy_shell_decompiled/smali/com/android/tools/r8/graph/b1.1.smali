.class public abstract Lcom/android/tools/r8/graph/b1;
.super Lcom/android/tools/r8/graph/n1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic c:Z = true


# instance fields
.field public b:Lcom/android/tools/r8/graph/u0;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/u0;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/graph/n1;-><init>()V

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/graph/b1;->c:Z

    if-nez v0, :cond_1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    const-string v0, "Should use DexAnnotationSet.THE_EMPTY_ANNOTATIONS_SET"

    invoke-direct {p1, v0}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p1

    .line 3
    :cond_1
    :goto_0
    iput-object p1, p0, Lcom/android/tools/r8/graph/b1;->b:Lcom/android/tools/r8/graph/u0;

    return-void
.end method

.method public static synthetic a(Ljava/util/function/Function;Lcom/android/tools/r8/graph/b1;)Ljava/lang/Object;
    .locals 0

    .line 9
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/b1;->o0()Lcom/android/tools/r8/graph/g1;

    move-result-object p1

    invoke-interface {p0, p1}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static a(Ljava/util/stream/Stream;Ljava/util/function/Function;)Ljava/util/stream/Stream;
    .locals 2

    .line 7
    sget-object v0, Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda3;->INSTANCE:Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda3;

    new-instance v1, Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda1;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda1;-><init>(Ljava/util/function/Function;)V

    .line 8
    invoke-interface {p0, v0}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object p0

    invoke-interface {p0, v1}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Ljava/util/function/Function;Lcom/android/tools/r8/graph/b1;)Ljava/lang/Object;
    .locals 0

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/b1;->q0()Lcom/android/tools/r8/graph/j1;

    move-result-object p1

    invoke-interface {p0, p1}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static b(Ljava/util/stream/Stream;Ljava/util/function/Function;)Ljava/util/stream/Stream;
    .locals 2

    .line 1
    sget-object v0, Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda4;

    new-instance v1, Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda2;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda2;-><init>(Ljava/util/function/Function;)V

    .line 2
    invoke-interface {p0, v0}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object p0

    invoke-interface {p0, v1}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public T()Lcom/android/tools/r8/graph/J2;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->u0()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->n0()Lcom/android/tools/r8/graph/E0;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->p0()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/h1;->E0()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method public final a(Ljava/util/function/BiFunction;Lcom/android/tools/r8/graph/r0;)Lcom/android/tools/r8/graph/r0;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->getReference()Lcom/android/tools/r8/graph/G2;

    move-result-object v0

    invoke-static {v0}, Lcom/android/tools/r8/graph/p0;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/graph/p0;

    move-result-object v0

    .line 2
    invoke-interface {p1, p2, v0}, Ljava/util/function/BiFunction;->apply(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/r0;

    return-object p1
.end method

.method public a(Ljava/util/function/BiFunction;)V
    .locals 2

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->m0()Lcom/android/tools/r8/graph/u0;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0, p1}, Lcom/android/tools/r8/graph/b1$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/b1;Ljava/util/function/BiFunction;)V

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/u0;->a(Ljava/util/function/Function;)Lcom/android/tools/r8/graph/u0;

    move-result-object p1

    .line 6
    iput-object p1, p0, Lcom/android/tools/r8/graph/b1;->b:Lcom/android/tools/r8/graph/u0;

    return-void
.end method

.method public final a(Ljava/util/function/Predicate;)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->m0()Lcom/android/tools/r8/graph/u0;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/u0;->a(Ljava/util/function/Predicate;)Lcom/android/tools/r8/graph/u0;

    move-result-object p1

    .line 4
    iput-object p1, p0, Lcom/android/tools/r8/graph/b1;->b:Lcom/android/tools/r8/graph/u0;

    return-void
.end method

.method public f0()Lcom/android/tools/r8/graph/E2;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public g0()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public abstract getAccessFlags()Lcom/android/tools/r8/graph/g;
.end method

.method public abstract getReference()Lcom/android/tools/r8/graph/G2;
.end method

.method public m0()Lcom/android/tools/r8/graph/u0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/b1;->b:Lcom/android/tools/r8/graph/u0;

    return-object v0
.end method

.method public n0()Lcom/android/tools/r8/graph/E0;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public o0()Lcom/android/tools/r8/graph/g1;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public p0()Lcom/android/tools/r8/graph/h1;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public q0()Lcom/android/tools/r8/graph/j1;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public final r0()V
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/graph/u0;->n0()Lcom/android/tools/r8/graph/u0;

    move-result-object v0

    .line 2
    iput-object v0, p0, Lcom/android/tools/r8/graph/b1;->b:Lcom/android/tools/r8/graph/u0;

    return-void
.end method

.method public final s0()Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->m0()Lcom/android/tools/r8/graph/u0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/u0;->isEmpty()Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    return v0
.end method

.method public t0()Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->s0()Z

    move-result v0

    return v0
.end method

.method public u0()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public v0()Z
    .locals 1

    instance-of v0, p0, Lcom/android/tools/r8/graph/g1;

    return v0
.end method

.method public w0()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public x0()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public y()V
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/graph/u0;->n0()Lcom/android/tools/r8/graph/u0;

    move-result-object v0

    .line 2
    iput-object v0, p0, Lcom/android/tools/r8/graph/b1;->b:Lcom/android/tools/r8/graph/u0;

    return-void
.end method

.method public abstract y0()Z
.end method

.method public abstract z0()Z
.end method
