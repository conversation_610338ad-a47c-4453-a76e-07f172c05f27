.class public final synthetic Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda35;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/lang/Iterable;


# instance fields
.field public final synthetic f$0:Ljava/lang/Iterable;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/d1;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Iterable;Lcom/android/tools/r8/graph/d1;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda35;->f$0:Ljava/lang/Iterable;

    iput-object p2, p0, Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda35;->f$1:Lcom/android/tools/r8/graph/d1;

    return-void
.end method


# virtual methods
.method public final iterator()Ljava/util/Iterator;
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda35;->f$0:Ljava/lang/Iterable;

    iget-object v1, p0, Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda35;->f$1:Lcom/android/tools/r8/graph/d1;

    invoke-static {v0, v1}, Lcom/android/tools/r8/graph/E2;->a(Ljava/lang/Iterable;Lcom/android/tools/r8/graph/d1;)Ljava/util/Iterator;

    move-result-object v0

    return-object v0
.end method
