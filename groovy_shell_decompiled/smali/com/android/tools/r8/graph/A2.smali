.class public final Lcom/android/tools/r8/graph/A2;
.super Lcom/android/tools/r8/graph/C2;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final c:Lcom/android/tools/r8/graph/x2;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/graph/C2;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/graph/A2;->c:Lcom/android/tools/r8/graph/x2;

    return-void
.end method


# virtual methods
.method public final E()Lcom/android/tools/r8/internal/ho0;
    .locals 0

    return-object p0
.end method

.method public final a()Lcom/android/tools/r8/graph/I2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/A2;->c:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/s2;->w0()Lcom/android/tools/r8/graph/I2;

    move-result-object v0

    return-object v0
.end method

.method public final b()Lcom/android/tools/r8/graph/F2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/A2;->c:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/x2;->B0()Lcom/android/tools/r8/graph/F2;

    move-result-object v0

    return-object v0
.end method
