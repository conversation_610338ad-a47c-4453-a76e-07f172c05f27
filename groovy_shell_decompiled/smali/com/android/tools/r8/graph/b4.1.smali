.class public interface abstract Lcom/android/tools/r8/graph/b4;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation runtime Ljava/lang/FunctionalInterface;
.end annotation


# virtual methods
.method public abstract a(Lcom/android/tools/r8/graph/J2;Ljava/util/function/Consumer;Ljava/util/function/Consumer;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/tools/r8/graph/J2;",
            "Ljava/util/function/Consumer<",
            "Lcom/android/tools/r8/graph/E2;",
            ">;",
            "Ljava/util/function/Consumer<",
            "Lcom/android/tools/r8/internal/DP;",
            ">;)V"
        }
    .end annotation
.end method
