.class public final synthetic Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda32;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/Hx;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/E2;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/E2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda32;->f$0:Lcom/android/tools/r8/graph/E2;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/graph/E2$$ExternalSyntheticLambda32;->f$0:Lcom/android/tools/r8/graph/E2;

    check-cast p1, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/E2;->g(Lcom/android/tools/r8/graph/j1;)Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    return-object p1
.end method
