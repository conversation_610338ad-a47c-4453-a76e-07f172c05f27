.class public final Lcom/android/tools/r8/graph/a0;
.super Lcom/android/tools/r8/graph/Y;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/r2;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/graph/Y;-><init>(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/r2;)V

    return-void
.end method


# virtual methods
.method public final D()Lcom/android/tools/r8/graph/E0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/Y;->b:Lcom/android/tools/r8/graph/E0;

    return-object v0
.end method

.method public final v()Lcom/android/tools/r8/graph/E0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/Y;->c:Lcom/android/tools/r8/graph/r2;

    return-object v0
.end method
