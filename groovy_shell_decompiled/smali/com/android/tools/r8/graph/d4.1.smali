.class public Lcom/android/tools/r8/graph/d4;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic h:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/utils/w;

.field public final b:Ljava/util/concurrent/ConcurrentHashMap;

.field public final c:Ljava/util/concurrent/ConcurrentHashMap;

.field public final d:Ljava/util/concurrent/ConcurrentHashMap;

.field public final e:Lcom/android/tools/r8/graph/B;

.field public final f:Lcom/android/tools/r8/graph/y0;

.field public final g:Ljava/util/ArrayList;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/utils/w;)V
    .locals 1

    .line 14
    new-instance v0, Lcom/android/tools/r8/graph/y0;

    invoke-direct {v0}, Lcom/android/tools/r8/graph/y0;-><init>()V

    .line 15
    invoke-direct {p0, p1, v0}, Lcom/android/tools/r8/graph/d4;-><init>(Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/graph/y0;)V

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/graph/y0;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/d4;->b:Ljava/util/concurrent/ConcurrentHashMap;

    .line 3
    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/d4;->c:Ljava/util/concurrent/ConcurrentHashMap;

    .line 4
    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/d4;->d:Ljava/util/concurrent/ConcurrentHashMap;

    .line 7
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/d4;->g:Ljava/util/ArrayList;

    .line 11
    iput-object p1, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 12
    iput-object p2, p0, Lcom/android/tools/r8/graph/d4;->f:Lcom/android/tools/r8/graph/y0;

    .line 13
    invoke-static {p1}, Lcom/android/tools/r8/graph/B;->a(Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/graph/B;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/graph/d4;->e:Lcom/android/tools/r8/graph/B;

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/graph/B1;
    .locals 1

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    return-object v0
.end method

.method public final a(Ljava/lang/String;Ljava/lang/String;Lcom/android/tools/r8/graph/z2;Ljava/util/ArrayList;)Lcom/android/tools/r8/graph/D0;
    .locals 1

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 16
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/d4;->d(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    invoke-virtual {p0, p2}, Lcom/android/tools/r8/graph/d4;->c(Ljava/lang/String;)Lcom/android/tools/r8/graph/F2;

    move-result-object p2

    .line 17
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 18
    new-instance v0, Lcom/android/tools/r8/graph/D0;

    invoke-direct {v0, p1, p2, p3, p4}, Lcom/android/tools/r8/graph/D0;-><init>(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/z2;Ljava/util/List;)V

    return-object v0
.end method

.method public final a([Ljava/lang/String;)Lcom/android/tools/r8/graph/L2;
    .locals 3

    .line 6
    array-length v0, p1

    if-nez v0, :cond_0

    .line 7
    invoke-static {}, Lcom/android/tools/r8/graph/L2;->m0()Lcom/android/tools/r8/graph/L2;

    move-result-object p1

    return-object p1

    .line 9
    :cond_0
    array-length v0, p1

    new-array v0, v0, [Lcom/android/tools/r8/graph/J2;

    const/4 v1, 0x0

    .line 10
    :goto_0
    array-length v2, p1

    if-ge v1, v2, :cond_1

    .line 11
    aget-object v2, p1, v1

    invoke-virtual {p0, v2}, Lcom/android/tools/r8/graph/d4;->f(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    aput-object v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 13
    :cond_1
    new-instance p1, Lcom/android/tools/r8/graph/L2;

    invoke-direct {p1, v0}, Lcom/android/tools/r8/graph/L2;-><init>([Lcom/android/tools/r8/graph/J2;)V

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;Ljava/lang/String;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;
    .locals 1

    .line 14
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    invoke-virtual {p0, p3}, Lcom/android/tools/r8/graph/d4;->c(Ljava/lang/String;)Lcom/android/tools/r8/graph/F2;

    move-result-object p3

    invoke-virtual {p0, p2}, Lcom/android/tools/r8/graph/d4;->d(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object p2

    invoke-virtual {v0, p1, p3, p2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    return-object p1
.end method

.method public final a(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;
    .locals 2

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->b:Ljava/util/concurrent/ConcurrentHashMap;

    sget-object v1, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda1;

    invoke-virtual {v0, p1, v1}, Ljava/util/concurrent/ConcurrentHashMap;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/xq0;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/V;)V
    .locals 3

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    invoke-virtual {v0}, Lcom/android/tools/r8/utils/w;->n0()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 26
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 27
    iget-object v1, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 28
    invoke-static {v1, v0}, Lcom/android/tools/r8/internal/Nt0;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 29
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 30
    sget-object v1, Lcom/android/tools/r8/graph/V;->c:Lcom/android/tools/r8/graph/V;

    if-ne p2, v1, :cond_0

    .line 31
    iget-object v1, p0, Lcom/android/tools/r8/graph/d4;->f:Lcom/android/tools/r8/graph/y0;

    .line 32
    iget-object v2, v1, Lcom/android/tools/r8/graph/y0;->d:Lcom/android/tools/r8/internal/DB;

    .line 33
    monitor-enter v2

    .line 34
    :try_start_0
    iget-object v1, v1, Lcom/android/tools/r8/graph/y0;->d:Lcom/android/tools/r8/internal/DB;

    invoke-virtual {v1, v0}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    .line 35
    monitor-exit v2

    goto :goto_0

    :catchall_0
    move-exception p1

    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1

    .line 36
    :cond_0
    :goto_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->T0()Ljava/util/List;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0, p1, p2}, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/d4;Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/V;)V

    .line 37
    invoke-interface {v0, v1}, Ljava/util/List;->forEach(Ljava/util/function/Consumer;)V

    :cond_1
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/V;Lcom/android/tools/r8/graph/a4;)V
    .locals 4

    .line 38
    sget-boolean v0, Lcom/android/tools/r8/graph/d4;->h:Z

    if-nez v0, :cond_1

    .line 39
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/a4;->d()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    .line 40
    iget-object v2, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v2, v2, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 41
    invoke-static {v2, v1}, Lcom/android/tools/r8/internal/Nt0;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 44
    :cond_1
    :goto_0
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/a4;->b()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    .line 45
    iget-object v2, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v2, v2, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 46
    invoke-static {v2, v1}, Lcom/android/tools/r8/internal/Nt0;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    if-eqz v1, :cond_5

    .line 48
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    .line 49
    sget-object v2, Lcom/android/tools/r8/graph/V;->c:Lcom/android/tools/r8/graph/V;

    if-ne p2, v2, :cond_2

    .line 50
    iget-object v2, p0, Lcom/android/tools/r8/graph/d4;->f:Lcom/android/tools/r8/graph/y0;

    .line 51
    iget-object v3, v2, Lcom/android/tools/r8/graph/y0;->e:Lcom/android/tools/r8/internal/DB;

    .line 52
    monitor-enter v3

    .line 53
    :try_start_0
    iget-object v2, v2, Lcom/android/tools/r8/graph/y0;->e:Lcom/android/tools/r8/internal/DB;

    invoke-virtual {v2, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    .line 54
    monitor-exit v3

    goto :goto_1

    :catchall_0
    move-exception p1

    monitor-exit v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1

    :cond_2
    :goto_1
    if-nez v0, :cond_4

    .line 55
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/a4;->d()Lcom/android/tools/r8/graph/J2;

    move-result-object p3

    .line 56
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 57
    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->I2:Lcom/android/tools/r8/graph/J2;

    if-ne p3, v0, :cond_3

    goto :goto_2

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 58
    :cond_4
    :goto_2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/graph/d4;->b(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/V;)V

    :cond_5
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/V;)V
    .locals 1

    .line 19
    sget-object v0, Lcom/android/tools/r8/graph/V;->c:Lcom/android/tools/r8/graph/V;

    if-ne p2, v0, :cond_0

    .line 20
    iget-object p2, p0, Lcom/android/tools/r8/graph/d4;->f:Lcom/android/tools/r8/graph/y0;

    .line 21
    iget-object v0, p2, Lcom/android/tools/r8/graph/y0;->e:Lcom/android/tools/r8/internal/DB;

    .line 22
    monitor-enter v0

    .line 23
    :try_start_0
    iget-object p2, p2, Lcom/android/tools/r8/graph/y0;->e:Lcom/android/tools/r8/internal/DB;

    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    .line 24
    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1

    :cond_0
    :goto_0
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/VL;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->g:Ljava/util/ArrayList;

    monitor-enter v0

    .line 2
    :try_start_0
    iget-object v1, p0, Lcom/android/tools/r8/graph/d4;->g:Ljava/util/ArrayList;

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 3
    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public final b(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->c:Ljava/util/concurrent/ConcurrentHashMap;

    sget-object v1, Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/graph/d4$$ExternalSyntheticLambda2;

    invoke-virtual {v0, p1, v1}, Ljava/util/concurrent/ConcurrentHashMap;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/xq0;

    return-object p1
.end method

.method public final b(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/V;)V
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/graph/V;->c:Lcom/android/tools/r8/graph/V;

    if-ne p2, v0, :cond_0

    .line 3
    iget-object p2, p0, Lcom/android/tools/r8/graph/d4;->f:Lcom/android/tools/r8/graph/y0;

    .line 4
    iget-object v0, p2, Lcom/android/tools/r8/graph/y0;->d:Lcom/android/tools/r8/internal/DB;

    .line 5
    monitor-enter v0

    .line 6
    :try_start_0
    iget-object p2, p2, Lcom/android/tools/r8/graph/y0;->d:Lcom/android/tools/r8/internal/DB;

    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    .line 7
    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1

    :cond_0
    :goto_0
    return-void
.end method

.method public final c(Ljava/lang/String;)Lcom/android/tools/r8/graph/F2;
    .locals 5

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/graph/d4;->h:Z

    if-nez v0, :cond_1

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/d4;->b(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/xq0;->b()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 4
    :cond_1
    :goto_0
    invoke-static {p1}, Lcom/android/tools/r8/internal/Nk;->u(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 5
    invoke-static {p1}, Lcom/android/tools/r8/internal/Nk;->e(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 7
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/graph/d4;->e(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 8
    array-length v2, p1

    if-nez v2, :cond_2

    .line 9
    invoke-static {}, Lcom/android/tools/r8/graph/L2;->m0()Lcom/android/tools/r8/graph/L2;

    move-result-object p1

    goto :goto_2

    .line 11
    :cond_2
    array-length v2, p1

    new-array v2, v2, [Lcom/android/tools/r8/graph/J2;

    const/4 v3, 0x0

    .line 12
    :goto_1
    array-length v4, p1

    if-ge v3, v4, :cond_3

    .line 13
    aget-object v4, p1, v3

    invoke-virtual {p0, v4}, Lcom/android/tools/r8/graph/d4;->e(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object v4

    aput-object v4, v2, v3

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 15
    :cond_3
    new-instance p1, Lcom/android/tools/r8/graph/L2;

    invoke-direct {p1, v2}, Lcom/android/tools/r8/graph/L2;-><init>([Lcom/android/tools/r8/graph/J2;)V

    .line 16
    :goto_2
    invoke-virtual {v1, v0, p1}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/L2;)Lcom/android/tools/r8/graph/F2;

    move-result-object p1

    return-object p1
.end method

.method public final d(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->d:Ljava/util/concurrent/ConcurrentHashMap;

    iget-object v1, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/dex/t0$$ExternalSyntheticLambda1;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/dex/t0$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/graph/B1;)V

    invoke-virtual {v0, p1, v2}, Ljava/util/concurrent/ConcurrentHashMap;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/I2;

    return-object p1
.end method

.method public final e(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;
    .locals 1

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/graph/d4;->h:Z

    if-nez v0, :cond_1

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/d4;->b(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/xq0;->b()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 4
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->e:Lcom/android/tools/r8/graph/B;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/B;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/d4;->d(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/B1;->c(Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    return-object p1
.end method

.method public final f(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;
    .locals 3

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/graph/d4;->h:Z

    if-nez v0, :cond_1

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/d4;->a(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;

    move-result-object v0

    .line 3
    iget-object v1, v0, Lcom/android/tools/r8/internal/xq0;->b:Ljava/lang/String;

    .line 4
    iget v2, v0, Lcom/android/tools/r8/internal/xq0;->c:I

    iget v0, v0, Lcom/android/tools/r8/internal/xq0;->d:I

    invoke-virtual {v1, v2, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 6
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 7
    :cond_1
    :goto_0
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/d4;->a(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;

    move-result-object p1

    .line 8
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/xq0;->b()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/d4;->e(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    return-object p1
.end method
