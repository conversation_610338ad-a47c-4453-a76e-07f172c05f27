.class public abstract Lcom/android/tools/r8/graph/C2;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/ho0;


# static fields
.field public static final synthetic b:I


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/ko0;)V
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/graph/C2$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/graph/C2$$ExternalSyntheticLambda1;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ko0;->e(Ljava/util/function/Function;)Lcom/android/tools/r8/internal/ko0;

    move-result-object p0

    sget-object v0, Lcom/android/tools/r8/graph/C2$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/graph/C2$$ExternalSyntheticLambda2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ko0;->e(Ljava/util/function/Function;)Lcom/android/tools/r8/internal/ko0;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/B2;
    .locals 2

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->b()Lcom/android/tools/r8/graph/F2;

    move-result-object v0

    .line 3
    new-instance v1, Lcom/android/tools/r8/graph/B2;

    invoke-direct {v1, v0, p1}, Lcom/android/tools/r8/graph/B2;-><init>(Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)V

    return-object v1
.end method

.method public final a(Lcom/android/tools/r8/graph/L2;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/B2;
    .locals 2

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->a()Lcom/android/tools/r8/graph/I2;

    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->b()Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/F2;->q0()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    .line 6
    invoke-virtual {p2, v1, p1}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/L2;)Lcom/android/tools/r8/graph/F2;

    move-result-object p1

    .line 7
    new-instance p2, Lcom/android/tools/r8/graph/B2;

    invoke-direct {p2, p1, v0}, Lcom/android/tools/r8/graph/B2;-><init>(Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)V

    return-object p2
.end method

.method public abstract a()Lcom/android/tools/r8/graph/I2;
.end method

.method public abstract b()Lcom/android/tools/r8/graph/F2;
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/graph/C2;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/graph/C2;

    .line 5
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/C2;->a()Lcom/android/tools/r8/graph/I2;

    move-result-object v1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/C2;->b()Lcom/android/tools/r8/graph/F2;

    move-result-object p1

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->a()Lcom/android/tools/r8/graph/I2;

    move-result-object v3

    invoke-virtual {v3, v1}, Lcom/android/tools/r8/graph/I2;->g(Lcom/android/tools/r8/graph/I2;)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->b()Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 7
    invoke-static {v1, p1}, Lcom/android/tools/r8/graph/F2;->a(Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/F2;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public final hashCode()I
    .locals 4

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->a()Lcom/android/tools/r8/graph/I2;

    move-result-object v0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->b()Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object v0, v2, v3

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-static {v2}, Ljava/util/Objects;->hash([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final m()Lcom/android/tools/r8/internal/io0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/graph/C2$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/graph/C2$$ExternalSyntheticLambda0;

    return-object v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 3

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->b()Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/F2;->q0()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    .line 3
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/J2;->G0()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " "

    .line 4
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->a()Lcom/android/tools/r8/graph/I2;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "("

    .line 6
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/4 v1, 0x0

    .line 7
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->b()Lcom/android/tools/r8/graph/F2;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/F2;->o0()I

    move-result v2

    if-ge v1, v2, :cond_1

    if-eqz v1, :cond_0

    const-string v2, ", "

    .line 8
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 9
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/C2;->b()Lcom/android/tools/r8/graph/F2;

    move-result-object v2

    .line 10
    iget-object v2, v2, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    .line 11
    iget-object v2, v2, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    aget-object v2, v2, v1

    .line 12
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->G0()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    const-string v1, ")"

    .line 14
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
