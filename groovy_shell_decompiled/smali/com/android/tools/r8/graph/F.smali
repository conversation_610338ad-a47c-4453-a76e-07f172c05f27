.class public final Lcom/android/tools/r8/graph/F;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/w9;


# instance fields
.field public final synthetic a:Lcom/android/tools/r8/graph/y;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/graph/F;->a:Lcom/android/tools/r8/graph/y;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/H;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/F;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/bd0;->warning(Lcom/android/tools/r8/Diagnostic;)V

    return-void
.end method
