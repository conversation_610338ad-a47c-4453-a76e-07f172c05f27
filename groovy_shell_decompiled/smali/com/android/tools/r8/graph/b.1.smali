.class public final Lcom/android/tools/r8/graph/b;
.super Lcom/android/tools/r8/graph/d;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final a:Lcom/android/tools/r8/graph/b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/graph/b;

    invoke-direct {v0}, Lcom/android/tools/r8/graph/b;-><init>()V

    sput-object v0, Lcom/android/tools/r8/graph/b;->a:Lcom/android/tools/r8/graph/b;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/graph/d;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/K5;)Lcom/android/tools/r8/graph/d;
    .locals 0

    return-object p0
.end method

.method public final a(Lcom/android/tools/r8/graph/d1;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/graph/d;
    .locals 0

    return-object p0
.end method

.method public final a(Lcom/android/tools/r8/graph/d;)Lcom/android/tools/r8/graph/d;
    .locals 0

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    return-void
.end method

.method public final a(Ljava/util/function/Consumer;)V
    .locals 0

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/j1;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public final a(Ljava/util/function/Predicate;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public final b()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final b(Ljava/util/function/Predicate;)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method public final c()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
