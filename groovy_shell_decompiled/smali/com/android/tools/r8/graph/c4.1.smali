.class public final Lcom/android/tools/r8/graph/c4;
.super Lcom/android/tools/r8/graph/i0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final e:Lcom/android/tools/r8/graph/c4;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/graph/c4;

    invoke-direct {v0}, Lcom/android/tools/r8/graph/c4;-><init>()V

    sput-object v0, Lcom/android/tools/r8/graph/c4;->e:Lcom/android/tools/r8/graph/c4;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/graph/i0;-><init>()V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/i0;)Z
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/graph/c4;->e:Lcom/android/tools/r8/graph/c4;

    if-ne p0, v0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/UU$a;)Lcom/android/tools/r8/internal/aA;
    .locals 0

    .line 2
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public final a(Lcom/android/tools/r8/graph/j1;Lcom/android/tools/r8/internal/Th0;)Ljava/lang/String;
    .locals 1

    .line 5
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    if-eqz p1, :cond_0

    .line 7
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/j1;->l0()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 9
    :cond_0
    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/b6;)V
    .locals 0

    .line 3
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public final a(Lcom/android/tools/r8/graph/e0;Lcom/android/tools/r8/internal/gi;)V
    .locals 0

    .line 4
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public final c(Ljava/lang/Object;)Z
    .locals 0

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final m0()I
    .locals 1

    .line 1
    invoke-static {p0}, Ljava/lang/System;->identityHashCode(Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final s0()I
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 1

    const-string v0, "<invalid-code>"

    return-object v0
.end method

.method public final z0()Z
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw v0
.end method
