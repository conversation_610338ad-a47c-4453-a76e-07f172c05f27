.class public final Lcom/android/tools/r8/graph/g4;
.super Lcom/android/tools/r8/internal/Xv;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic j:Z


# instance fields
.field public final c:Lcom/android/tools/r8/graph/f4;

.field public final d:I

.field public final e:Ljava/lang/String;

.field public final f:Ljava/lang/String;

.field public final g:Ljava/lang/Object;

.field public final h:Lcom/android/tools/r8/graph/D3$e;

.field public i:Ljava/util/ArrayList;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/graph/k4;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/graph/g4;->j:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/f4;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2

    const/high16 v0, 0x90000

    const/4 v1, 0x0

    .line 1
    invoke-direct {p0, v0, v1}, Lcom/android/tools/r8/internal/Xv;-><init>(ILcom/android/tools/r8/internal/Xv;)V

    .line 2
    iput-object v1, p0, Lcom/android/tools/r8/graph/g4;->i:Ljava/util/ArrayList;

    .line 12
    iput-object p1, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    .line 13
    iput p2, p0, Lcom/android/tools/r8/graph/g4;->d:I

    .line 14
    iput-object p3, p0, Lcom/android/tools/r8/graph/g4;->e:Ljava/lang/String;

    .line 15
    iput-object p4, p0, Lcom/android/tools/r8/graph/g4;->f:Ljava/lang/String;

    .line 16
    iput-object p6, p0, Lcom/android/tools/r8/graph/g4;->g:Ljava/lang/Object;

    .line 18
    iget-object p2, p1, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object p2, p2, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 19
    iget-object p2, p1, Lcom/android/tools/r8/graph/f4;->c:Lcom/android/tools/r8/origin/Origin;

    iget-object p1, p1, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    .line 20
    iget-object p1, p1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 21
    iget-object p4, p1, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 22
    iget-object p1, p1, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    .line 23
    invoke-static {p3, p5, p2, p4, p1}, Lcom/android/tools/r8/graph/D3;->b(Ljava/lang/String;Ljava/lang/String;Lcom/android/tools/r8/origin/Origin;Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/DiagnosticsHandler;)Lcom/android/tools/r8/graph/D3$e;

    move-result-object p1

    .line 29
    iput-object p1, p0, Lcom/android/tools/r8/graph/g4;->h:Lcom/android/tools/r8/graph/D3$e;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/J2;Ljava/lang/Object;)Lcom/android/tools/r8/graph/O2;
    .locals 2

    if-nez p2, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 109
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/d4;->a()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    .line 110
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->x1:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_4

    .line 111
    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 112
    sget-boolean p2, Lcom/android/tools/r8/graph/g4;->j:Z

    const/4 v0, 0x1

    if-nez p2, :cond_2

    if-ltz p1, :cond_1

    if-gt p1, v0, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_2
    :goto_0
    if-ne p1, v0, :cond_3

    goto :goto_1

    :cond_3
    const/4 v0, 0x0

    .line 113
    :goto_1
    invoke-static {v0}, Lcom/android/tools/r8/graph/O2$c;->a(Z)Lcom/android/tools/r8/graph/O2$c;

    move-result-object p1

    return-object p1

    .line 115
    :cond_4
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->y1:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_5

    .line 116
    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->byteValue()B

    move-result p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/O2$d;->a(B)Lcom/android/tools/r8/graph/O2$d;

    move-result-object p1

    return-object p1

    .line 118
    :cond_5
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->E1:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_6

    .line 119
    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->shortValue()S

    move-result p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/O2$j;->a(S)Lcom/android/tools/r8/graph/O2$j;

    move-result-object p1

    return-object p1

    .line 121
    :cond_6
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->z1:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_7

    .line 122
    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p1

    int-to-char p1, p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/O2$e;->a(C)Lcom/android/tools/r8/graph/O2$e;

    move-result-object p1

    return-object p1

    .line 124
    :cond_7
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->C1:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_8

    .line 125
    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/O2$h;->j(I)Lcom/android/tools/r8/graph/O2$h;

    move-result-object p1

    return-object p1

    .line 127
    :cond_8
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->B1:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_9

    .line 128
    check-cast p2, Ljava/lang/Float;

    invoke-virtual {p2}, Ljava/lang/Float;->floatValue()F

    move-result p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/O2$g;->a(F)Lcom/android/tools/r8/graph/O2$g;

    move-result-object p1

    return-object p1

    .line 130
    :cond_9
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->D1:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_a

    .line 131
    check-cast p2, Ljava/lang/Long;

    invoke-virtual {p2}, Ljava/lang/Long;->longValue()J

    move-result-wide p1

    invoke-static {p1, p2}, Lcom/android/tools/r8/graph/O2$i;->a(J)Lcom/android/tools/r8/graph/O2$i;

    move-result-object p1

    return-object p1

    .line 133
    :cond_a
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->A1:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_b

    .line 134
    check-cast p2, Ljava/lang/Double;

    invoke-virtual {p2}, Ljava/lang/Double;->doubleValue()D

    move-result-wide p1

    invoke-static {p1, p2}, Lcom/android/tools/r8/graph/O2$f;->a(D)Lcom/android/tools/r8/graph/O2$f;

    move-result-object p1

    return-object p1

    .line 136
    :cond_b
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->Z1:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v1, :cond_c

    .line 137
    new-instance p1, Lcom/android/tools/r8/graph/O2$k;

    check-cast p2, Ljava/lang/String;

    invoke-virtual {v0, p2}, Lcom/android/tools/r8/graph/B1;->c(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/android/tools/r8/graph/O2$k;-><init>(Lcom/android/tools/r8/graph/I2;)V

    return-object p1

    .line 139
    :cond_c
    new-instance p2, Lcom/android/tools/r8/internal/Os0;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Unexpected static-value type "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p2
.end method

.method public final a(ILcom/android/tools/r8/internal/wr0;Ljava/lang/String;Z)Lcom/android/tools/r8/internal/K2;
    .locals 7

    .line 140
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->i:Ljava/util/ArrayList;

    if-nez v0, :cond_0

    .line 141
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/g4;->i:Ljava/util/ArrayList;

    .line 143
    :cond_0
    iget-object v3, p0, Lcom/android/tools/r8/graph/g4;->i:Ljava/util/ArrayList;

    .line 144
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v4, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    move-object v1, p3

    move v2, p4

    move v5, p1

    move-object v6, p2

    .line 145
    invoke-static/range {v1 .. v6}, Lcom/android/tools/r8/graph/k4;->a(Ljava/lang/String;ZLjava/util/List;Lcom/android/tools/r8/graph/d4;ILcom/android/tools/r8/internal/wr0;)Lcom/android/tools/r8/graph/e4;

    move-result-object p1

    return-object p1
.end method

.method public final a(Ljava/lang/String;Z)Lcom/android/tools/r8/internal/K2;
    .locals 9

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    .line 3
    iget-object v1, v1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->A1:Lcom/android/tools/r8/utils/w$q;

    iget-boolean v1, v1, Lcom/android/tools/r8/utils/w$q;->a:Z

    if-eqz v1, :cond_7

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    sget-object v1, Lcom/android/tools/r8/graph/V;->c:Lcom/android/tools/r8/graph/V;

    if-ne v0, v1, :cond_7

    if-nez p2, :cond_7

    .line 4
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    const/4 v0, -0x1

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    move-result v1

    sparse-switch v1, :sswitch_data_0

    goto :goto_0

    :sswitch_0
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/CheckRemoved;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x6

    goto :goto_0

    :sswitch_1
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/CheckOptimizedOut;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    goto :goto_0

    :cond_1
    const/4 v0, 0x5

    goto :goto_0

    :sswitch_2
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/KeepEdge;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x4

    goto :goto_0

    :sswitch_3
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/UsedByNative;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    goto :goto_0

    :cond_3
    const/4 v0, 0x3

    goto :goto_0

    :sswitch_4
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/UsedByReflection;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    goto :goto_0

    :cond_4
    const/4 v0, 0x2

    goto :goto_0

    :sswitch_5
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/KeepForApi;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    goto :goto_0

    :cond_5
    const/4 v0, 0x1

    goto :goto_0

    :sswitch_6
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/UsesReflection;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_6

    goto :goto_0

    :cond_6
    const/4 v0, 0x0

    :goto_0
    packed-switch v0, :pswitch_data_0

    goto :goto_1

    .line 5
    :pswitch_0
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->j:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/J2;->G0()Ljava/lang/String;

    move-result-object v4

    .line 6
    new-instance v0, Lcom/android/tools/r8/internal/D30;

    .line 7
    new-instance v1, Lcom/android/tools/r8/internal/C30;

    invoke-direct {v1, v4}, Lcom/android/tools/r8/internal/C30;-><init>(Ljava/lang/String;)V

    .line 8
    iget-object v2, p0, Lcom/android/tools/r8/graph/g4;->e:Ljava/lang/String;

    iget-object v3, p0, Lcom/android/tools/r8/graph/g4;->f:Ljava/lang/String;

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/D30;-><init>(Lcom/android/tools/r8/internal/C30;Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    new-instance v7, Lcom/android/tools/r8/internal/B30;

    invoke-direct {v7, v0, p1}, Lcom/android/tools/r8/internal/B30;-><init>(Lcom/android/tools/r8/internal/I30;Ljava/lang/String;)V

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v1, v0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->A1:Lcom/android/tools/r8/utils/w$q;

    iget-boolean v3, v1, Lcom/android/tools/r8/utils/w$q;->a:Z

    iget-object v5, p0, Lcom/android/tools/r8/graph/g4;->e:Ljava/lang/String;

    iget-object v6, p0, Lcom/android/tools/r8/graph/g4;->f:Ljava/lang/String;

    .line 18
    new-instance v8, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda2;

    invoke-direct {v8, v0}, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/graph/d4;)V

    move-object v1, p1

    move v2, p2

    .line 19
    invoke-static/range {v1 .. v8}, Lcom/android/tools/r8/internal/KM;->a(Ljava/lang/String;ZZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/android/tools/r8/internal/B30;Ljava/util/function/Consumer;)Lcom/android/tools/r8/internal/L2;

    move-result-object p1

    return-object p1

    .line 31
    :cond_7
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-boolean v1, v0, Lcom/android/tools/r8/graph/f4;->D:Z

    iget-object v2, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    .line 32
    iget-object v2, v2, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 33
    iget-object v2, v2, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 34
    invoke-static {v2}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    const-string v2, "Ldalvik/annotation/optimization/ReachabilitySensitive;"

    invoke-virtual {v2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    or-int/2addr v1, v2

    iput-boolean v1, v0, Lcom/android/tools/r8/graph/f4;->D:Z

    .line 35
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->i:Ljava/util/ArrayList;

    if-nez v0, :cond_8

    .line 36
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/g4;->i:Ljava/util/ArrayList;

    .line 38
    :cond_8
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->i:Ljava/util/ArrayList;

    .line 39
    iget-object v1, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v1, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    sget-object v2, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;

    .line 40
    invoke-static {p1, p2, v0, v1, v2}, Lcom/android/tools/r8/graph/k4;->a(Ljava/lang/String;ZLjava/util/List;Lcom/android/tools/r8/graph/d4;Ljava/util/function/BiFunction;)Lcom/android/tools/r8/graph/e4;

    move-result-object p1

    return-object p1

    :sswitch_data_0
    .sparse-switch
        -0x7cd639a8 -> :sswitch_6
        -0x6ba49327 -> :sswitch_5
        -0x62a354d0 -> :sswitch_4
        -0x473a5b3c -> :sswitch_3
        -0x40630f13 -> :sswitch_2
        -0x328bd488 -> :sswitch_1
        0x6b344257 -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public final a()V
    .locals 7

    .line 41
    iget v0, p0, Lcom/android/tools/r8/graph/g4;->d:I

    sget-object v1, Lcom/android/tools/r8/graph/k4;->d:[B

    const v1, -0x60001

    and-int/2addr v0, v1

    .line 42
    invoke-static {v0}, Lcom/android/tools/r8/graph/h3;->e(I)Lcom/android/tools/r8/graph/h3;

    move-result-object v0

    .line 43
    iget-object v1, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v2, v1, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v1, v1, Lcom/android/tools/r8/graph/f4;->j:Lcom/android/tools/r8/graph/J2;

    iget-object v3, p0, Lcom/android/tools/r8/graph/g4;->e:Ljava/lang/String;

    iget-object v4, p0, Lcom/android/tools/r8/graph/g4;->f:Ljava/lang/String;

    .line 44
    iget-object v5, v2, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 45
    iget-object v5, v5, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    invoke-virtual {v2, v4}, Lcom/android/tools/r8/graph/d4;->e(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object v4

    invoke-virtual {v2, v3}, Lcom/android/tools/r8/graph/d4;->d(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object v2

    invoke-virtual {v5, v1, v4, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/l1;

    move-result-object v1

    .line 46
    iget-object v2, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v3, v2, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v2, v2, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    .line 47
    iget-object v4, v3, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 48
    invoke-virtual {v4}, Lcom/android/tools/r8/utils/w;->r()I

    move-result v4

    const/4 v5, 0x3

    if-ne v4, v5, :cond_2

    .line 49
    iget-object v4, v3, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v4, v4, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 50
    sget-boolean v5, Lcom/android/tools/r8/internal/R90;->f:Z

    if-nez v5, :cond_1

    iget-object v5, v1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-static {v4, v5}, Lcom/android/tools/r8/internal/R90;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Z

    move-result v5

    if-nez v5, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    const-string v1, "The java.lang.Record class has no fields."

    invoke-direct {v0, v1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw v0

    .line 51
    :cond_1
    :goto_0
    iget-object v5, v1, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-static {v4, v5}, Lcom/android/tools/r8/internal/R90;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Z

    move-result v4

    if-eqz v4, :cond_2

    .line 52
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v4

    .line 53
    sget-object v5, Lcom/android/tools/r8/graph/V;->c:Lcom/android/tools/r8/graph/V;

    if-ne v2, v5, :cond_2

    .line 54
    iget-object v2, v3, Lcom/android/tools/r8/graph/d4;->f:Lcom/android/tools/r8/graph/y0;

    .line 55
    iget-object v3, v2, Lcom/android/tools/r8/graph/y0;->c:Lcom/android/tools/r8/internal/DB;

    .line 56
    monitor-enter v3

    .line 57
    :try_start_0
    iget-object v2, v2, Lcom/android/tools/r8/graph/y0;->c:Lcom/android/tools/r8/internal/DB;

    invoke-virtual {v2, v4}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    .line 58
    monitor-exit v3

    goto :goto_1

    :catchall_0
    move-exception v0

    monitor-exit v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0

    .line 59
    :cond_2
    :goto_1
    iget-object v2, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v3, v2, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v2, v2, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    .line 60
    iget-object v4, v3, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 61
    invoke-virtual {v4}, Lcom/android/tools/r8/utils/w;->n0()Z

    move-result v4

    const/4 v5, 0x1

    if-eqz v4, :cond_4

    .line 62
    iget-object v4, v3, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v4, v4, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 63
    iget-object v6, v1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-static {v4, v6}, Lcom/android/tools/r8/internal/Nt0;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Z

    move-result v6

    if-eqz v6, :cond_3

    move v4, v5

    goto :goto_2

    .line 66
    :cond_3
    iget-object v6, v1, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-static {v4, v6}, Lcom/android/tools/r8/internal/Nt0;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Z

    move-result v4

    :goto_2
    if-eqz v4, :cond_4

    .line 67
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v4

    invoke-virtual {v3, v4, v2}, Lcom/android/tools/r8/graph/d4;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/V;)V

    .line 68
    :cond_4
    iget-object v2, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v3, v2, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v2, v2, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    .line 69
    iget-object v4, v3, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 70
    invoke-virtual {v4}, Lcom/android/tools/r8/utils/w;->n0()Z

    move-result v4

    if-eqz v4, :cond_7

    .line 71
    iget-object v4, v3, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v4, v4, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 72
    iget-object v6, v1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-static {v4, v6}, Lcom/android/tools/r8/internal/Nt0;->b(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Z

    move-result v6

    if-eqz v6, :cond_6

    .line 73
    sget-boolean v4, Lcom/android/tools/r8/internal/Nt0;->c:Z

    if-eqz v4, :cond_5

    goto :goto_3

    :cond_5
    new-instance v0, Ljava/lang/AssertionError;

    const-string v1, "The VarHandle class has no fields."

    invoke-direct {v0, v1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw v0

    .line 76
    :cond_6
    iget-object v5, v1, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-static {v4, v5}, Lcom/android/tools/r8/internal/Nt0;->b(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Z

    move-result v5

    :goto_3
    if-eqz v5, :cond_7

    .line 77
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v4

    invoke-virtual {v3, v4, v2}, Lcom/android/tools/r8/graph/d4;->b(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/V;)V

    .line 78
    :cond_7
    sget-object v2, Lcom/android/tools/r8/internal/Ov;->a:Lcom/android/tools/r8/internal/Ov;

    .line 79
    new-instance v3, Lcom/android/tools/r8/internal/su;

    invoke-direct {v3, v2, v1}, Lcom/android/tools/r8/internal/su;-><init>(Lcom/android/tools/r8/internal/tu;Ljava/lang/Object;)V

    .line 80
    iget-object v2, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v2, v2, Lcom/android/tools/r8/graph/f4;->z:Ljava/util/HashSet;

    invoke-virtual {v2, v3}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_a

    .line 81
    iget-object v2, p0, Lcom/android/tools/r8/graph/g4;->i:Ljava/util/ArrayList;

    iget-object v3, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v3, v3, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v3, v3, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 82
    invoke-static {v2, v3}, Lcom/android/tools/r8/graph/k4;->a(Ljava/util/List;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/graph/u0;

    move-result-object v2

    .line 83
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v3

    if-eqz v3, :cond_8

    iget-object v3, p0, Lcom/android/tools/r8/graph/g4;->g:Ljava/lang/Object;

    iget-object v4, v1, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v4, v3}, Lcom/android/tools/r8/graph/g4;->a(Lcom/android/tools/r8/graph/J2;Ljava/lang/Object;)Lcom/android/tools/r8/graph/O2;

    move-result-object v3

    goto :goto_4

    :cond_8
    const/4 v3, 0x0

    .line 85
    :goto_4
    sget-object v4, Lcom/android/tools/r8/graph/g1;->o:[Lcom/android/tools/r8/graph/g1;

    .line 86
    new-instance v4, Lcom/android/tools/r8/graph/g1$a;

    const/4 v5, 0x0

    invoke-direct {v4, v5}, Lcom/android/tools/r8/graph/g1$a;-><init>(Z)V

    .line 87
    invoke-virtual {v4, v1}, Lcom/android/tools/r8/graph/g1$a;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/g1$a;

    move-result-object v1

    .line 88
    iput-object v0, v1, Lcom/android/tools/r8/graph/g1$a;->c:Lcom/android/tools/r8/graph/h3;

    .line 89
    iget-object v4, p0, Lcom/android/tools/r8/graph/g4;->h:Lcom/android/tools/r8/graph/D3$e;

    .line 90
    iput-object v4, v1, Lcom/android/tools/r8/graph/g1$a;->d:Lcom/android/tools/r8/graph/D3$e;

    .line 91
    iput-object v2, v1, Lcom/android/tools/r8/graph/g1$a;->b:Lcom/android/tools/r8/graph/u0;

    .line 92
    iput-object v3, v1, Lcom/android/tools/r8/graph/g1$a;->f:Lcom/android/tools/r8/graph/O2;

    .line 93
    iget v2, p0, Lcom/android/tools/r8/graph/g4;->d:I

    .line 94
    invoke-static {v2}, Lcom/android/tools/r8/internal/y4;->a(I)Z

    move-result v2

    .line 95
    iput-boolean v2, v1, Lcom/android/tools/r8/graph/g1$a;->j:Z

    .line 96
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/g1$a;->c()Lcom/android/tools/r8/graph/g1$a;

    move-result-object v1

    .line 97
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/g1$a;->a()Lcom/android/tools/r8/graph/g1;

    move-result-object v1

    .line 98
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v0

    if-eqz v0, :cond_9

    .line 99
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->x:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_5

    .line 101
    :cond_9
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->y:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_5

    .line 104
    :cond_a
    iget-object v0, p0, Lcom/android/tools/r8/graph/g4;->c:Lcom/android/tools/r8/graph/f4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    new-instance v2, Lcom/android/tools/r8/utils/StringDiagnostic;

    .line 106
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/l1;->l0()Ljava/lang/String;

    move-result-object v1

    new-instance v3, Ljava/lang/StringBuilder;

    const-string v4, "Field `"

    invoke-direct {v3, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "` has multiple definitions"

    .line 107
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v2, v1}, Lcom/android/tools/r8/utils/StringDiagnostic;-><init>(Ljava/lang/String;)V

    .line 108
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/bd0;->warning(Lcom/android/tools/r8/Diagnostic;)V

    :goto_5
    return-void
.end method
