.class public final Lcom/android/tools/r8/graph/h4;
.super Lcom/android/tools/r8/internal/SV;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic r:Z


# instance fields
.field public final c:Ljava/lang/String;

.field public final d:Ljava/lang/String;

.field public final e:Lcom/android/tools/r8/graph/f4;

.field public final f:I

.field public g:Ljava/util/ArrayList;

.field public h:Lcom/android/tools/r8/graph/O2;

.field public i:I

.field public j:Ljava/util/ArrayList;

.field public k:Ljava/util/ArrayList;

.field public l:Ljava/util/ArrayList;

.field public final m:Lcom/android/tools/r8/graph/D3$g;

.field public final n:Lcom/android/tools/r8/graph/x2;

.field public final o:Lcom/android/tools/r8/graph/H4;

.field public final p:Z

.field public q:Lcom/android/tools/r8/graph/q4;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/graph/k4;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/graph/h4;->r:Z

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;Lcom/android/tools/r8/graph/f4;)V
    .locals 3

    const/high16 v0, 0x90000

    const/4 v1, 0x0

    .line 1
    invoke-direct {p0, v0, v1}, Lcom/android/tools/r8/internal/SV;-><init>(ILcom/android/tools/r8/internal/SV;)V

    .line 2
    iput-object v1, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 3
    iput-object v1, p0, Lcom/android/tools/r8/graph/h4;->h:Lcom/android/tools/r8/graph/O2;

    const/4 v0, -0x1

    .line 4
    iput v0, p0, Lcom/android/tools/r8/graph/h4;->i:I

    .line 5
    iput-object v1, p0, Lcom/android/tools/r8/graph/h4;->j:Ljava/util/ArrayList;

    .line 6
    iput-object v1, p0, Lcom/android/tools/r8/graph/h4;->k:Ljava/util/ArrayList;

    .line 7
    iput-object v1, p0, Lcom/android/tools/r8/graph/h4;->l:Ljava/util/ArrayList;

    .line 12
    iput-object v1, p0, Lcom/android/tools/r8/graph/h4;->q:Lcom/android/tools/r8/graph/q4;

    .line 22
    iput-object p2, p0, Lcom/android/tools/r8/graph/h4;->c:Ljava/lang/String;

    .line 23
    iput-object p3, p0, Lcom/android/tools/r8/graph/h4;->d:Ljava/lang/String;

    .line 24
    iput-object p6, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    .line 25
    iget-object v0, p6, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v1, p6, Lcom/android/tools/r8/graph/f4;->j:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0, v1, p2, p3}, Lcom/android/tools/r8/graph/d4;->a(Lcom/android/tools/r8/graph/J2;Ljava/lang/String;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/graph/h4;->n:Lcom/android/tools/r8/graph/x2;

    .line 26
    invoke-static {p1, p2}, Lcom/android/tools/r8/graph/k4;->a(ILjava/lang/String;)Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/graph/h4;->o:Lcom/android/tools/r8/graph/H4;

    .line 27
    invoke-static {p1}, Lcom/android/tools/r8/internal/y4;->a(I)Z

    move-result p1

    iput-boolean p1, p0, Lcom/android/tools/r8/graph/h4;->p:Z

    .line 28
    invoke-static {p3}, Lcom/android/tools/r8/internal/Nk;->d(Ljava/lang/String;)I

    move-result p1

    iput p1, p0, Lcom/android/tools/r8/graph/h4;->f:I

    if-eqz p5, :cond_2

    .line 29
    array-length p1, p5

    if-lez p1, :cond_2

    .line 30
    array-length p1, p5

    new-array p1, p1, [Lcom/android/tools/r8/graph/O2;

    const/4 p3, 0x0

    .line 31
    :goto_0
    array-length v0, p5

    if-ge p3, v0, :cond_0

    .line 32
    new-instance v0, Lcom/android/tools/r8/graph/O2$l;

    iget-object v1, p6, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    aget-object v2, p5, p3

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/d4;->f(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/android/tools/r8/graph/O2$l;-><init>(Lcom/android/tools/r8/graph/J2;)V

    aput-object v0, p1, p3

    add-int/lit8 p3, p3, 0x1

    goto :goto_0

    .line 34
    :cond_0
    iget-object p3, p6, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    .line 35
    iget-object p3, p3, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 36
    iget-object p3, p3, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 37
    sget-object p5, Lcom/android/tools/r8/graph/r0;->d:[Lcom/android/tools/r8/graph/r0;

    .line 38
    iget-object p5, p3, Lcom/android/tools/r8/graph/B1;->y5:Lcom/android/tools/r8/graph/J2;

    new-instance v0, Lcom/android/tools/r8/graph/O2$b;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/graph/O2$b;-><init>([Lcom/android/tools/r8/graph/O2;)V

    invoke-static {p5, p3, v0}, Lcom/android/tools/r8/graph/r0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/O2;)Lcom/android/tools/r8/graph/r0;

    move-result-object p1

    .line 39
    iget-object p3, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    if-nez p3, :cond_1

    .line 40
    new-instance p3, Ljava/util/ArrayList;

    invoke-direct {p3}, Ljava/util/ArrayList;-><init>()V

    iput-object p3, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 42
    :cond_1
    iget-object p3, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 43
    invoke-virtual {p3, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 44
    :cond_2
    iget-object p1, p6, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object p1, p1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 45
    iget-object p1, p6, Lcom/android/tools/r8/graph/f4;->c:Lcom/android/tools/r8/origin/Origin;

    iget-object p3, p6, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    .line 46
    iget-object p3, p3, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 47
    iget-object p5, p3, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 48
    iget-object p3, p3, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    .line 49
    invoke-static {p2, p4, p1, p5, p3}, Lcom/android/tools/r8/graph/D3;->c(Ljava/lang/String;Ljava/lang/String;Lcom/android/tools/r8/origin/Origin;Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/DiagnosticsHandler;)Lcom/android/tools/r8/graph/D3$g;

    move-result-object p1

    .line 55
    iput-object p1, p0, Lcom/android/tools/r8/graph/h4;->m:Lcom/android/tools/r8/graph/D3$g;

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/K2;
    .locals 3

    .line 41
    new-instance v0, Lcom/android/tools/r8/graph/e4;

    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v1, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    new-instance v2, Lcom/android/tools/r8/graph/h4$$ExternalSyntheticLambda0;

    invoke-direct {v2, p0}, Lcom/android/tools/r8/graph/h4$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/h4;)V

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/graph/e4;-><init>(Lcom/android/tools/r8/graph/d4;Ljava/util/function/BiConsumer;)V

    return-object v0
.end method

.method public final a(ILcom/android/tools/r8/internal/wr0;Ljava/lang/String;Z)Lcom/android/tools/r8/internal/K2;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public final a(ILcom/android/tools/r8/internal/wr0;[Lcom/android/tools/r8/internal/qP;[Lcom/android/tools/r8/internal/qP;[ILjava/lang/String;Z)Lcom/android/tools/r8/internal/K2;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public final a(ILjava/lang/String;Z)Lcom/android/tools/r8/internal/K2;
    .locals 3

    .line 51
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->j:Ljava/util/ArrayList;

    if-nez v0, :cond_1

    .line 52
    iget v0, p0, Lcom/android/tools/r8/graph/h4;->i:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    .line 53
    iget v0, p0, Lcom/android/tools/r8/graph/h4;->f:I

    iput v0, p0, Lcom/android/tools/r8/graph/h4;->i:I

    .line 55
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    iget v1, p0, Lcom/android/tools/r8/graph/h4;->i:I

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lcom/android/tools/r8/graph/h4;->j:Ljava/util/ArrayList;

    const/4 v0, 0x0

    .line 56
    :goto_0
    iget v1, p0, Lcom/android/tools/r8/graph/h4;->i:I

    if-ge v0, v1, :cond_1

    .line 57
    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->j:Ljava/util/ArrayList;

    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 60
    :cond_1
    sget-boolean v0, Lcom/android/tools/r8/graph/h4;->r:Z

    if-nez v0, :cond_3

    iget-object v0, p0, Lcom/android/tools/r8/internal/SV;->b:Lcom/android/tools/r8/internal/SV;

    if-nez v0, :cond_2

    goto :goto_1

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 61
    :cond_3
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->j:Ljava/util/ArrayList;

    .line 64
    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    sget-object v1, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;

    .line 65
    invoke-static {p2, p3, p1, v0, v1}, Lcom/android/tools/r8/graph/k4;->a(Ljava/lang/String;ZLjava/util/List;Lcom/android/tools/r8/graph/d4;Ljava/util/function/BiFunction;)Lcom/android/tools/r8/graph/e4;

    move-result-object p1

    return-object p1
.end method

.method public final a(Ljava/lang/String;Z)Lcom/android/tools/r8/internal/K2;
    .locals 9

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    .line 3
    iget-object v1, v1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->A1:Lcom/android/tools/r8/utils/w$q;

    iget-boolean v1, v1, Lcom/android/tools/r8/utils/w$q;->a:Z

    if-eqz v1, :cond_7

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    sget-object v1, Lcom/android/tools/r8/graph/V;->c:Lcom/android/tools/r8/graph/V;

    if-ne v0, v1, :cond_7

    if-nez p2, :cond_7

    .line 4
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    const/4 v0, -0x1

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    move-result v1

    sparse-switch v1, :sswitch_data_0

    goto :goto_0

    :sswitch_0
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/CheckRemoved;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x6

    goto :goto_0

    :sswitch_1
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/CheckOptimizedOut;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    goto :goto_0

    :cond_1
    const/4 v0, 0x5

    goto :goto_0

    :sswitch_2
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/KeepEdge;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x4

    goto :goto_0

    :sswitch_3
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/UsedByNative;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    goto :goto_0

    :cond_3
    const/4 v0, 0x3

    goto :goto_0

    :sswitch_4
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/UsedByReflection;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    goto :goto_0

    :cond_4
    const/4 v0, 0x2

    goto :goto_0

    :sswitch_5
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/KeepForApi;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    goto :goto_0

    :cond_5
    const/4 v0, 0x1

    goto :goto_0

    :sswitch_6
    const-string v1, "Lcom/android/tools/r8/keepanno/annotations/UsesReflection;"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_6

    goto :goto_0

    :cond_6
    const/4 v0, 0x0

    :goto_0
    packed-switch v0, :pswitch_data_0

    goto :goto_1

    .line 5
    :pswitch_0
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->j:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/J2;->G0()Ljava/lang/String;

    move-result-object v4

    .line 6
    new-instance v0, Lcom/android/tools/r8/internal/G30;

    .line 7
    new-instance v1, Lcom/android/tools/r8/internal/C30;

    invoke-direct {v1, v4}, Lcom/android/tools/r8/internal/C30;-><init>(Ljava/lang/String;)V

    .line 8
    iget-object v2, p0, Lcom/android/tools/r8/graph/h4;->c:Ljava/lang/String;

    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->d:Ljava/lang/String;

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/G30;-><init>(Lcom/android/tools/r8/internal/C30;Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    new-instance v7, Lcom/android/tools/r8/internal/B30;

    invoke-direct {v7, v0, p1}, Lcom/android/tools/r8/internal/B30;-><init>(Lcom/android/tools/r8/internal/I30;Ljava/lang/String;)V

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v1, v0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->A1:Lcom/android/tools/r8/utils/w$q;

    iget-boolean v3, v1, Lcom/android/tools/r8/utils/w$q;->a:Z

    iget-object v5, p0, Lcom/android/tools/r8/graph/h4;->c:Ljava/lang/String;

    iget-object v6, p0, Lcom/android/tools/r8/graph/h4;->d:Ljava/lang/String;

    .line 18
    new-instance v8, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda2;

    invoke-direct {v8, v0}, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/graph/d4;)V

    move-object v1, p1

    move v2, p2

    .line 19
    invoke-static/range {v1 .. v8}, Lcom/android/tools/r8/internal/KM;->b(Ljava/lang/String;ZZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/android/tools/r8/internal/B30;Ljava/util/function/Consumer;)Lcom/android/tools/r8/internal/L2;

    move-result-object p1

    return-object p1

    .line 31
    :cond_7
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-boolean v1, v0, Lcom/android/tools/r8/graph/f4;->D:Z

    iget-object v2, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    .line 32
    iget-object v2, v2, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 33
    iget-object v2, v2, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 34
    invoke-static {v2}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    const-string v2, "Ldalvik/annotation/optimization/ReachabilitySensitive;"

    invoke-virtual {v2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    or-int/2addr v1, v2

    iput-boolean v1, v0, Lcom/android/tools/r8/graph/f4;->D:Z

    .line 35
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    if-nez v0, :cond_8

    .line 36
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 38
    :cond_8
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 39
    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v1, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    sget-object v2, Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/graph/f4$$ExternalSyntheticLambda1;

    .line 40
    invoke-static {p1, p2, v0, v1, v2}, Lcom/android/tools/r8/graph/k4;->a(Ljava/lang/String;ZLjava/util/List;Lcom/android/tools/r8/graph/d4;Ljava/util/function/BiFunction;)Lcom/android/tools/r8/graph/e4;

    move-result-object p1

    return-object p1

    :sswitch_data_0
    .sparse-switch
        -0x7cd639a8 -> :sswitch_6
        -0x6ba49327 -> :sswitch_5
        -0x62a354d0 -> :sswitch_4
        -0x473a5b3c -> :sswitch_3
        -0x40630f13 -> :sswitch_2
        -0x328bd488 -> :sswitch_1
        0x6b344257 -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public final a(IZ)V
    .locals 1

    .line 44
    iget p2, p0, Lcom/android/tools/r8/graph/h4;->i:I

    const/4 v0, -0x1

    if-eq p2, v0, :cond_1

    .line 48
    sget-boolean v0, Lcom/android/tools/r8/graph/h4;->r:Z

    if-nez v0, :cond_1

    if-ne p2, p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 50
    :cond_1
    :goto_0
    iput p1, p0, Lcom/android/tools/r8/graph/h4;->i:I

    return-void
.end method

.method public final synthetic a(Ljava/util/List;Ljava/util/List;)V
    .locals 1

    .line 42
    sget-boolean p1, Lcom/android/tools/r8/graph/h4;->r:Z

    if-nez p1, :cond_1

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p1

    const/4 v0, 0x1

    if-ne p1, v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    const/4 p1, 0x0

    .line 43
    invoke-interface {p2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/O2;

    iput-object p1, p0, Lcom/android/tools/r8/graph/h4;->h:Lcom/android/tools/r8/graph/O2;

    return-void
.end method

.method public final b(ILcom/android/tools/r8/internal/wr0;Ljava/lang/String;Z)Lcom/android/tools/r8/internal/K2;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public final b()V
    .locals 2

    .line 19
    new-instance v0, Lcom/android/tools/r8/internal/Os0;

    const-string v1, "visitCode() should not be called when SKIP_CODE is set"

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final b(ILjava/lang/String;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->k:Ljava/util/ArrayList;

    if-nez v0, :cond_2

    .line 3
    sget-boolean v0, Lcom/android/tools/r8/graph/h4;->r:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->l:Ljava/util/ArrayList;

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 4
    :cond_1
    :goto_0
    new-instance v0, Ljava/util/ArrayList;

    iget v1, p0, Lcom/android/tools/r8/graph/h4;->f:I

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lcom/android/tools/r8/graph/h4;->k:Ljava/util/ArrayList;

    .line 5
    new-instance v0, Ljava/util/ArrayList;

    iget v1, p0, Lcom/android/tools/r8/graph/h4;->f:I

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lcom/android/tools/r8/graph/h4;->l:Ljava/util/ArrayList;

    :cond_2
    if-nez p2, :cond_3

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->k:Ljava/util/ArrayList;

    sget-object v1, Lcom/android/tools/r8/graph/W2;->d:Lcom/android/tools/r8/graph/W2;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 12
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->k:Ljava/util/ArrayList;

    new-instance v1, Lcom/android/tools/r8/graph/O2$k;

    iget-object v2, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v2, v2, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    .line 13
    iget-object v2, v2, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 14
    iget-object v2, v2, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 15
    invoke-virtual {v2, p2}, Lcom/android/tools/r8/graph/B1;->c(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object v2

    invoke-direct {v1, v2}, Lcom/android/tools/r8/graph/O2$k;-><init>(Lcom/android/tools/r8/graph/I2;)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 17
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->l:Ljava/util/ArrayList;

    invoke-static {p1}, Lcom/android/tools/r8/graph/O2$h;->j(I)Lcom/android/tools/r8/graph/O2$h;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 18
    invoke-super {p0, p1, p2}, Lcom/android/tools/r8/internal/SV;->b(ILjava/lang/String;)V

    return-void
.end method

.method public final c(ILcom/android/tools/r8/internal/wr0;Ljava/lang/String;Z)Lcom/android/tools/r8/internal/K2;
    .locals 7

    .line 109
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    if-nez v0, :cond_0

    .line 110
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 112
    :cond_0
    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 113
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v4, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    move-object v1, p3

    move v2, p4

    move v5, p1

    move-object v6, p2

    .line 114
    invoke-static/range {v1 .. v6}, Lcom/android/tools/r8/graph/k4;->a(Ljava/lang/String;ZLjava/util/List;Lcom/android/tools/r8/graph/d4;ILcom/android/tools/r8/internal/wr0;)Lcom/android/tools/r8/graph/e4;

    move-result-object p1

    return-object p1
.end method

.method public final c()V
    .locals 7

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v2, v1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 2
    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->n:Lcom/android/tools/r8/graph/x2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    .line 3
    invoke-virtual {v2}, Lcom/android/tools/r8/utils/w;->r()I

    move-result v4

    const/4 v5, 0x3

    if-ne v4, v5, :cond_0

    .line 4
    iget-object v4, v1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v4, v4, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 5
    invoke-static {v4, v3}, Lcom/android/tools/r8/internal/R90;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/x2;)Z

    move-result v4

    if-eqz v4, :cond_0

    .line 6
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    .line 7
    sget-object v4, Lcom/android/tools/r8/graph/V;->c:Lcom/android/tools/r8/graph/V;

    if-ne v0, v4, :cond_0

    .line 8
    iget-object v0, v1, Lcom/android/tools/r8/graph/d4;->f:Lcom/android/tools/r8/graph/y0;

    .line 9
    iget-object v1, v0, Lcom/android/tools/r8/graph/y0;->c:Lcom/android/tools/r8/internal/DB;

    .line 10
    monitor-enter v1

    .line 11
    :try_start_0
    iget-object v0, v0, Lcom/android/tools/r8/graph/y0;->c:Lcom/android/tools/r8/internal/DB;

    invoke-virtual {v0, v3}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    .line 12
    monitor-exit v1

    goto :goto_0

    :catchall_0
    move-exception v0

    monitor-exit v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0

    .line 13
    :cond_0
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->n:Lcom/android/tools/r8/graph/x2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    .line 14
    iget-object v4, v1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 15
    invoke-virtual {v4}, Lcom/android/tools/r8/utils/w;->n0()Z

    move-result v4

    if-eqz v4, :cond_1

    .line 16
    iget-object v4, v1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v4, v4, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 17
    invoke-static {v4, v3}, Lcom/android/tools/r8/internal/Nt0;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/x2;)Z

    move-result v4

    if-eqz v4, :cond_1

    .line 18
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    invoke-virtual {v1, v3, v0}, Lcom/android/tools/r8/graph/d4;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/V;)V

    .line 19
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->n:Lcom/android/tools/r8/graph/x2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    .line 20
    iget-object v4, v1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 21
    invoke-virtual {v4}, Lcom/android/tools/r8/utils/w;->n0()Z

    move-result v4

    if-eqz v4, :cond_2

    .line 22
    iget-object v4, v1, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    iget-object v4, v4, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 23
    invoke-static {v4, v3}, Lcom/android/tools/r8/internal/Nt0;->b(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/x2;)Z

    move-result v4

    if-eqz v4, :cond_2

    .line 24
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    invoke-virtual {v1, v3, v0}, Lcom/android/tools/r8/graph/d4;->b(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/V;)V

    .line 25
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->o:Lcom/android/tools/r8/graph/H4;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/H4;->K()Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->o:Lcom/android/tools/r8/graph/H4;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/H4;->N()Z

    move-result v0

    if-nez v0, :cond_4

    .line 26
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v0, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    sget-object v3, Lcom/android/tools/r8/graph/V;->c:Lcom/android/tools/r8/graph/V;

    if-eq v1, v3, :cond_3

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    iget-object v0, v0, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 27
    invoke-virtual {v0}, Lcom/android/tools/r8/utils/w;->l()Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v0, Lcom/android/tools/r8/graph/f4;->d:Lcom/android/tools/r8/graph/V;

    sget-object v3, Lcom/android/tools/r8/graph/V;->d:Lcom/android/tools/r8/graph/V;

    if-ne v1, v3, :cond_4

    .line 28
    iget-object v1, v0, Lcom/android/tools/r8/graph/f4;->p:Ljava/util/ArrayList;

    .line 29
    invoke-virtual {v1}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_3

    iget-object v0, v0, Lcom/android/tools/r8/graph/f4;->o:Lcom/android/tools/r8/graph/k5;

    if-eqz v0, :cond_4

    .line 30
    :cond_3
    new-instance v0, Lcom/android/tools/r8/graph/q4;

    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v3, v1, Lcom/android/tools/r8/graph/f4;->c:Lcom/android/tools/r8/origin/Origin;

    iget-object v4, v1, Lcom/android/tools/r8/graph/f4;->g:Lcom/android/tools/r8/graph/j4;

    iget-object v1, v1, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    invoke-direct {v0, v3, v4, v1}, Lcom/android/tools/r8/graph/q4;-><init>(Lcom/android/tools/r8/origin/Origin;Lcom/android/tools/r8/graph/j4;Lcom/android/tools/r8/graph/d4;)V

    iput-object v0, p0, Lcom/android/tools/r8/graph/h4;->q:Lcom/android/tools/r8/graph/q4;

    .line 33
    :cond_4
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->j:Ljava/util/ArrayList;

    const/4 v1, 0x0

    if-nez v0, :cond_5

    .line 34
    sget-object v0, Lcom/android/tools/r8/graph/v5;->d:Lcom/android/tools/r8/graph/v5;

    goto :goto_2

    .line 35
    :cond_5
    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    new-array v0, v0, [Lcom/android/tools/r8/graph/u0;

    move v3, v1

    .line 36
    :goto_1
    iget-object v4, p0, Lcom/android/tools/r8/graph/h4;->j:Ljava/util/ArrayList;

    invoke-virtual {v4}, Ljava/util/ArrayList;->size()I

    move-result v4

    if-ge v3, v4, :cond_6

    .line 37
    iget-object v4, p0, Lcom/android/tools/r8/graph/h4;->j:Ljava/util/ArrayList;

    invoke-virtual {v4, v3}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/List;

    invoke-static {v4, v2}, Lcom/android/tools/r8/graph/k4;->a(Ljava/util/List;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/graph/u0;

    move-result-object v4

    aput-object v4, v0, v3

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 38
    :cond_6
    invoke-static {v0, v1}, Lcom/android/tools/r8/graph/v5;->a([Lcom/android/tools/r8/graph/u0;I)Lcom/android/tools/r8/graph/v5;

    move-result-object v0

    .line 39
    :goto_2
    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->k:Ljava/util/ArrayList;

    if-eqz v3, :cond_b

    .line 40
    sget-boolean v4, Lcom/android/tools/r8/graph/h4;->r:Z

    if-nez v4, :cond_8

    iget-object v4, p0, Lcom/android/tools/r8/graph/h4;->l:Ljava/util/ArrayList;

    if-eqz v4, :cond_7

    goto :goto_3

    :cond_7
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 41
    :cond_8
    :goto_3
    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    move-result v3

    iget v4, p0, Lcom/android/tools/r8/graph/h4;->f:I

    if-eq v3, v4, :cond_9

    .line 42
    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->n:Lcom/android/tools/r8/graph/x2;

    iget-object v5, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v5, v5, Lcom/android/tools/r8/graph/f4;->c:Lcom/android/tools/r8/origin/Origin;

    iget-object v6, p0, Lcom/android/tools/r8/graph/h4;->k:Ljava/util/ArrayList;

    .line 43
    invoke-virtual {v6}, Ljava/util/ArrayList;->size()I

    move-result v6

    .line 44
    invoke-virtual {v2, v3, v5, v4, v6}, Lcom/android/tools/r8/utils/w;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/origin/Origin;II)V

    .line 45
    :cond_9
    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    if-nez v3, :cond_a

    .line 46
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    iput-object v3, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 48
    :cond_a
    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 49
    iget-object v4, p0, Lcom/android/tools/r8/graph/h4;->k:Ljava/util/ArrayList;

    sget-object v5, Lcom/android/tools/r8/graph/O2;->b:[Lcom/android/tools/r8/graph/O2;

    .line 50
    invoke-virtual {v4, v5}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v4

    check-cast v4, [Lcom/android/tools/r8/graph/O2;

    iget-object v6, p0, Lcom/android/tools/r8/graph/h4;->l:Ljava/util/ArrayList;

    .line 51
    invoke-virtual {v6, v5}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v5

    check-cast v5, [Lcom/android/tools/r8/graph/O2;

    iget-object v6, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v6, v6, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    .line 52
    iget-object v6, v6, Lcom/android/tools/r8/graph/d4;->a:Lcom/android/tools/r8/utils/w;

    .line 53
    iget-object v6, v6, Lcom/android/tools/r8/utils/w;->a:Lcom/android/tools/r8/graph/B1;

    .line 54
    invoke-static {v4, v5, v6}, Lcom/android/tools/r8/graph/r0;->a([Lcom/android/tools/r8/graph/O2;[Lcom/android/tools/r8/graph/O2;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/r0;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 60
    :cond_b
    invoke-static {}, Lcom/android/tools/r8/graph/j1;->N0()Lcom/android/tools/r8/graph/j1$a;

    move-result-object v3

    iget-object v4, p0, Lcom/android/tools/r8/graph/h4;->n:Lcom/android/tools/r8/graph/x2;

    .line 61
    invoke-virtual {v3, v4}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v3

    iget-object v4, p0, Lcom/android/tools/r8/graph/h4;->o:Lcom/android/tools/r8/graph/H4;

    .line 62
    invoke-virtual {v3, v4}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/H4;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v3

    iget-object v4, p0, Lcom/android/tools/r8/graph/h4;->m:Lcom/android/tools/r8/graph/D3$g;

    .line 63
    iput-object v4, v3, Lcom/android/tools/r8/graph/j1$a;->d:Lcom/android/tools/r8/graph/D3$g;

    .line 64
    iget-object v4, p0, Lcom/android/tools/r8/graph/h4;->g:Ljava/util/ArrayList;

    .line 65
    invoke-static {v4, v2}, Lcom/android/tools/r8/graph/k4;->a(Ljava/util/List;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/graph/u0;

    move-result-object v4

    .line 66
    iput-object v4, v3, Lcom/android/tools/r8/graph/j1$a;->e:Lcom/android/tools/r8/graph/u0;

    .line 67
    iput-object v0, v3, Lcom/android/tools/r8/graph/j1$a;->g:Lcom/android/tools/r8/graph/v5;

    .line 68
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->q:Lcom/android/tools/r8/graph/q4;

    .line 69
    invoke-virtual {v3, v0}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/i0;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v0

    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v3, v3, Lcom/android/tools/r8/graph/f4;->h:Lcom/android/tools/r8/internal/pb;

    .line 70
    iput-object v3, v0, Lcom/android/tools/r8/graph/j1$a;->k:Lcom/android/tools/r8/internal/pb;

    .line 71
    iget-boolean v3, p0, Lcom/android/tools/r8/graph/h4;->p:Z

    .line 72
    iput-boolean v3, v0, Lcom/android/tools/r8/graph/j1$a;->o:Z

    .line 73
    iput-boolean v1, v0, Lcom/android/tools/r8/graph/j1$a;->r:Z

    .line 74
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1$a;->b()Lcom/android/tools/r8/graph/j1$a;

    move-result-object v0

    .line 75
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1$a;->a()Lcom/android/tools/r8/graph/j1;

    move-result-object v0

    .line 76
    sget-object v1, Lcom/android/tools/r8/internal/KV;->a:Lcom/android/tools/r8/internal/KV;

    iget-object v3, p0, Lcom/android/tools/r8/graph/h4;->n:Lcom/android/tools/r8/graph/x2;

    .line 77
    new-instance v4, Lcom/android/tools/r8/internal/su;

    invoke-direct {v4, v1, v3}, Lcom/android/tools/r8/internal/su;-><init>(Lcom/android/tools/r8/internal/tu;Ljava/lang/Object;)V

    .line 78
    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v1, Lcom/android/tools/r8/graph/f4;->C:Ljava/util/HashSet;

    invoke-virtual {v1, v4}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_e

    .line 79
    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->o:Lcom/android/tools/r8/graph/H4;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v1

    if-nez v1, :cond_d

    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->o:Lcom/android/tools/r8/graph/H4;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/H4;->M()Z

    move-result v1

    if-nez v1, :cond_d

    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->o:Lcom/android/tools/r8/graph/H4;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/g;->i()Z

    move-result v1

    if-eqz v1, :cond_c

    goto :goto_4

    .line 82
    :cond_c
    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v1, Lcom/android/tools/r8/graph/f4;->B:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_5

    .line 83
    :cond_d
    :goto_4
    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v1, v1, Lcom/android/tools/r8/graph/f4;->A:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_5

    .line 88
    :cond_e
    iget-object v0, v2, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    new-instance v1, Lcom/android/tools/r8/utils/StringDiagnostic;

    iget-object v2, p0, Lcom/android/tools/r8/graph/h4;->n:Lcom/android/tools/r8/graph/x2;

    .line 93
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/x2;->l0()Ljava/lang/String;

    move-result-object v2

    .line 94
    new-instance v3, Ljava/lang/StringBuilder;

    const-string v4, "Ignoring an implementation of the method `"

    invoke-direct {v3, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "` because it has multiple definitions"

    .line 95
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lcom/android/tools/r8/utils/StringDiagnostic;-><init>(Ljava/lang/String;)V

    .line 96
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/bd0;->warning(Lcom/android/tools/r8/Diagnostic;)V

    .line 103
    :goto_5
    iget-object v0, p0, Lcom/android/tools/r8/graph/h4;->h:Lcom/android/tools/r8/graph/O2;

    if-eqz v0, :cond_10

    .line 104
    iget-object v1, p0, Lcom/android/tools/r8/graph/h4;->e:Lcom/android/tools/r8/graph/f4;

    iget-object v2, p0, Lcom/android/tools/r8/graph/h4;->c:Ljava/lang/String;

    .line 105
    iget-object v3, v1, Lcom/android/tools/r8/graph/f4;->w:Ljava/util/ArrayList;

    if-nez v3, :cond_f

    .line 106
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    iput-object v3, v1, Lcom/android/tools/r8/graph/f4;->w:Ljava/util/ArrayList;

    .line 108
    :cond_f
    iget-object v3, v1, Lcom/android/tools/r8/graph/f4;->w:Ljava/util/ArrayList;

    new-instance v4, Lcom/android/tools/r8/graph/t0;

    iget-object v1, v1, Lcom/android/tools/r8/graph/f4;->e:Lcom/android/tools/r8/graph/d4;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/d4;->d(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object v1

    invoke-direct {v4, v1, v0}, Lcom/android/tools/r8/graph/t0;-><init>(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/O2;)V

    invoke-virtual {v3, v4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_10
    return-void
.end method
