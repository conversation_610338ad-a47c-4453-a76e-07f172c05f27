.class public final Lcom/android/tools/r8/graph/A1;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/graph/x2;

.field public final b:Lcom/android/tools/r8/internal/LB;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/B1;)V
    .locals 11

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->Z0:Lcom/android/tools/r8/graph/I2;

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->c1:Lcom/android/tools/r8/graph/I2;

    iget-object v2, p1, Lcom/android/tools/r8/graph/B1;->N0:Lcom/android/tools/r8/graph/I2;

    iget-object v3, p1, Lcom/android/tools/r8/graph/B1;->K0:Lcom/android/tools/r8/graph/I2;

    const/4 v4, 0x2

    new-array v5, v4, [Lcom/android/tools/r8/graph/I2;

    const/4 v6, 0x0

    aput-object v2, v5, v6

    const/4 v2, 0x1

    aput-object v3, v5, v2

    .line 3
    invoke-virtual {p1, v0, v1, v0, v5}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;[Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    .line 8
    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->a1:Lcom/android/tools/r8/graph/I2;

    iget-object v3, p1, Lcom/android/tools/r8/graph/B1;->c1:Lcom/android/tools/r8/graph/I2;

    iget-object v5, p1, Lcom/android/tools/r8/graph/B1;->N0:Lcom/android/tools/r8/graph/I2;

    iget-object v7, p1, Lcom/android/tools/r8/graph/B1;->K0:Lcom/android/tools/r8/graph/I2;

    new-array v8, v4, [Lcom/android/tools/r8/graph/I2;

    aput-object v5, v8, v6

    aput-object v7, v8, v2

    .line 9
    invoke-virtual {p1, v1, v3, v1, v8}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;[Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object v1

    .line 14
    iget-object v3, p1, Lcom/android/tools/r8/graph/B1;->b1:Lcom/android/tools/r8/graph/I2;

    iget-object v5, p1, Lcom/android/tools/r8/graph/B1;->c1:Lcom/android/tools/r8/graph/I2;

    iget-object v7, p1, Lcom/android/tools/r8/graph/B1;->N0:Lcom/android/tools/r8/graph/I2;

    iget-object v8, p1, Lcom/android/tools/r8/graph/B1;->K0:Lcom/android/tools/r8/graph/I2;

    const/4 v9, 0x3

    new-array v10, v9, [Lcom/android/tools/r8/graph/I2;

    aput-object v7, v10, v6

    aput-object v7, v10, v2

    aput-object v8, v10, v4

    .line 15
    invoke-virtual {p1, v3, v5, v3, v10}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;[Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/graph/A1;->a:Lcom/android/tools/r8/graph/x2;

    new-array v3, v9, [Ljava/lang/Object;

    aput-object v0, v3, v6

    aput-object v1, v3, v2

    aput-object p1, v3, v4

    .line 16
    invoke-static {v9, v9, v3}, Lcom/android/tools/r8/internal/LB;->a(II[Ljava/lang/Object;)Lcom/android/tools/r8/internal/LB;

    move-result-object p1

    .line 17
    iput-object p1, p0, Lcom/android/tools/r8/graph/A1;->b:Lcom/android/tools/r8/internal/LB;

    return-void
.end method
