.class public Lcom/android/tools/r8/A;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/android/tools/r8/A$a;
    }
.end annotation


# direct methods
.method public static synthetic a(Lcom/android/tools/r8/ProgramResource;)Ljava/util/Collection;
    .locals 0

    .line 48
    invoke-static {p0}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/A$a;)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/BaseCommand;->a()Lcom/android/tools/r8/utils/j;

    move-result-object v0

    .line 2
    new-instance v1, Lcom/android/tools/r8/utils/w;

    invoke-direct {v1}, Lcom/android/tools/r8/utils/w;-><init>()V

    const/4 v2, 0x1

    .line 4
    iput-boolean v2, v1, Lcom/android/tools/r8/utils/w;->T:Z

    .line 5
    :try_start_0
    invoke-static {p0}, Lcom/android/tools/r8/A;->b(Lcom/android/tools/r8/A$a;)Lcom/android/tools/r8/C;

    move-result-object v2
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 6
    :try_start_1
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 7
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 8
    iget-object v0, v0, Lcom/android/tools/r8/utils/j;->a:Lcom/android/tools/r8/internal/cB;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/cB;->iterator()Lcom/android/tools/r8/internal/Gs0;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/ProgramResourceProvider;

    .line 9
    invoke-interface {v4}, Lcom/android/tools/r8/ProgramResourceProvider;->getProgramResources()Ljava/util/Collection;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 10
    :cond_0
    invoke-virtual {v3}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/ProgramResource;

    .line 11
    invoke-static {p0, v2, v3, v1}, Lcom/android/tools/r8/A;->a(Lcom/android/tools/r8/A$a;Lcom/android/tools/r8/C;Lcom/android/tools/r8/ProgramResource;Lcom/android/tools/r8/utils/w;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    .line 13
    :cond_1
    :try_start_2
    invoke-interface {v2}, Ljava/io/Closeable;->close()V
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0

    return-void

    :catchall_0
    move-exception p0

    .line 14
    :try_start_3
    invoke-interface {v2}, Ljava/io/Closeable;->close()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    goto :goto_2

    :catchall_1
    move-exception v0

    :try_start_4
    invoke-virtual {p0, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_2
    throw p0
    :try_end_4
    .catch Ljava/lang/Exception; {:try_start_4 .. :try_end_4} :catch_0

    :catch_0
    move-exception p0

    .line 19
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public static a(Lcom/android/tools/r8/A$a;Lcom/android/tools/r8/C;Lcom/android/tools/r8/ProgramResource;Lcom/android/tools/r8/utils/w;)V
    .locals 8

    .line 20
    invoke-static {p3}, Lcom/android/tools/r8/internal/ep0;->a(Lcom/android/tools/r8/utils/w;)Ljava/util/concurrent/ExecutorService;

    move-result-object v0

    .line 22
    :try_start_0
    new-instance v1, Lcom/android/tools/r8/dex/c;

    .line 24
    invoke-static {}, Lcom/android/tools/r8/utils/j;->b()Lcom/android/tools/r8/utils/j$a;

    move-result-object v2

    new-instance v3, Lcom/android/tools/r8/A$$ExternalSyntheticLambda0;

    invoke-direct {v3, p2}, Lcom/android/tools/r8/A$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/ProgramResource;)V

    .line 25
    invoke-virtual {v2, v3}, Lcom/android/tools/r8/utils/j$a;->a(Lcom/android/tools/r8/ProgramResourceProvider;)Lcom/android/tools/r8/utils/j$a;

    move-result-object p2

    .line 26
    invoke-virtual {p2}, Lcom/android/tools/r8/utils/j$a;->a()Lcom/android/tools/r8/utils/j;

    move-result-object p2

    .line 28
    invoke-static {}, Lcom/android/tools/r8/internal/Gp0;->a()Lcom/android/tools/r8/internal/Gp0;

    move-result-object v2

    invoke-direct {v1, p2, p3, v2}, Lcom/android/tools/r8/dex/c;-><init>(Lcom/android/tools/r8/utils/j;Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/internal/Gp0;)V

    iget-object p2, p0, Lcom/android/tools/r8/A$a;->f:Lcom/android/tools/r8/t0$a;

    .line 29
    invoke-virtual {v1, p2, v0}, Lcom/android/tools/r8/dex/c;->a(Lcom/android/tools/r8/t0;Ljava/util/concurrent/ExecutorService;)Lcom/android/tools/r8/graph/s4;

    move-result-object p2

    .line 30
    iget-boolean p0, p0, Lcom/android/tools/r8/A$a;->g:Z

    if-eqz p0, :cond_0

    .line 31
    new-instance p0, Lcom/android/tools/r8/graph/Q5;

    invoke-direct {p0, p2, p3}, Lcom/android/tools/r8/graph/Q5;-><init>(Lcom/android/tools/r8/graph/s4;Lcom/android/tools/r8/utils/w;)V

    goto :goto_0

    .line 32
    :cond_0
    new-instance p0, Lcom/android/tools/r8/graph/C;

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x1

    move-object v2, p0

    move-object v3, p2

    move-object v4, p3

    .line 33
    invoke-direct/range {v2 .. v7}, Lcom/android/tools/r8/graph/C;-><init>(Lcom/android/tools/r8/graph/x0;Lcom/android/tools/r8/utils/w;ZZZ)V

    .line 34
    :goto_0
    invoke-interface {p1}, Lcom/android/tools/r8/C;->b()Z

    move-result p3

    if-eqz p3, :cond_1

    .line 36
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/x0;->g()Lcom/android/tools/r8/naming/b;

    move-result-object p3

    invoke-interface {p1, p3}, Lcom/android/tools/r8/C;->a(Lcom/android/tools/r8/naming/b;)Lcom/android/tools/r8/graph/A0;

    move-result-object p3

    const/4 v1, 0x0

    invoke-interface {p3, v1}, Lcom/android/tools/r8/graph/A0;->a(Lcom/android/tools/r8/graph/E0;)Ljava/io/PrintStream;

    move-result-object p3

    .line 37
    invoke-virtual {p0, p3}, Lcom/android/tools/r8/graph/B0;->e(Ljava/io/PrintStream;)V

    .line 41
    :cond_1
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/x0;->g()Lcom/android/tools/r8/naming/b;

    move-result-object p2

    invoke-interface {p1, p2}, Lcom/android/tools/r8/C;->a(Lcom/android/tools/r8/naming/b;)Lcom/android/tools/r8/graph/A0;

    move-result-object p2

    invoke-interface {p1}, Lcom/android/tools/r8/C;->a()Ljava/util/function/Consumer;

    move-result-object p1

    .line 42
    invoke-virtual {p0, p2, p1}, Lcom/android/tools/r8/graph/B0;->a(Lcom/android/tools/r8/graph/A0;Ljava/util/function/Consumer;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 45
    invoke-interface {v0}, Ljava/util/concurrent/ExecutorService;->shutdown()V

    return-void

    :catchall_0
    move-exception p0

    .line 46
    invoke-interface {v0}, Ljava/util/concurrent/ExecutorService;->shutdown()V

    .line 47
    throw p0
.end method

.method public static b(Lcom/android/tools/r8/A$a;)Lcom/android/tools/r8/C;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/A$a;->e:Ljava/nio/file/Path;

    if-nez v0, :cond_0

    .line 2
    new-instance p0, Lcom/android/tools/r8/D;

    invoke-direct {p0}, Lcom/android/tools/r8/D;-><init>()V

    return-object p0

    :cond_0
    const/4 v1, 0x0

    new-array v2, v1, [Ljava/nio/file/LinkOption;

    .line 3
    invoke-static {v0, v2}, Ljava/nio/file/Files;->isDirectory(Ljava/nio/file/Path;[Ljava/nio/file/LinkOption;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/A$a;->e:Ljava/nio/file/Path;

    .line 5
    iget-boolean p0, p0, Lcom/android/tools/r8/A$a;->g:Z

    if-eqz p0, :cond_1

    const-string p0, ".smali"

    goto :goto_0

    .line 6
    :cond_1
    sget-boolean p0, Lcom/android/tools/r8/graph/C;->l:Z

    const-string p0, ".dump"

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/nio/file/Path;->getParent()Ljava/nio/file/Path;

    move-result-object v2

    if-eqz v2, :cond_2

    new-array v1, v1, [Ljava/nio/file/attribute/FileAttribute;

    .line 9
    invoke-static {v2, v1}, Ljava/nio/file/Files;->createDirectories(Ljava/nio/file/Path;[Ljava/nio/file/attribute/FileAttribute;)Ljava/nio/file/Path;

    .line 11
    :cond_2
    new-instance v1, Lcom/android/tools/r8/z;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/z;-><init>(Ljava/lang/String;Ljava/nio/file/Path;)V

    return-object v1

    .line 12
    :cond_3
    iget-object p0, p0, Lcom/android/tools/r8/A$a;->e:Ljava/nio/file/Path;

    .line 13
    invoke-interface {p0}, Ljava/nio/file/Path;->getParent()Ljava/nio/file/Path;

    move-result-object v0

    if-eqz v0, :cond_4

    new-array v2, v1, [Ljava/nio/file/attribute/FileAttribute;

    .line 15
    invoke-static {v0, v2}, Ljava/nio/file/Files;->createDirectories(Ljava/nio/file/Path;[Ljava/nio/file/attribute/FileAttribute;)Ljava/nio/file/Path;

    .line 17
    :cond_4
    new-instance v0, Lcom/android/tools/r8/B;

    new-instance v2, Ljava/io/PrintStream;

    new-array v1, v1, [Ljava/nio/file/OpenOption;

    invoke-static {p0, v1}, Ljava/nio/file/Files;->newOutputStream(Ljava/nio/file/Path;[Ljava/nio/file/OpenOption;)Ljava/io/OutputStream;

    move-result-object p0

    invoke-direct {v2, p0}, Ljava/io/PrintStream;-><init>(Ljava/io/OutputStream;)V

    invoke-direct {v0, v2}, Lcom/android/tools/r8/B;-><init>(Ljava/io/PrintStream;)V

    return-object v0
.end method
