.class public final synthetic Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda9;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/BaseCommand$Builder;

.field public final synthetic f$1:Ljava/nio/file/Path;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/BaseCommand$Builder;Ljava/nio/file/Path;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda9;->f$0:Lcom/android/tools/r8/BaseCommand$Builder;

    iput-object p2, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda9;->f$1:Ljava/nio/file/Path;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda9;->f$0:Lcom/android/tools/r8/BaseCommand$Builder;

    iget-object v1, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda9;->f$1:Ljava/nio/file/Path;

    invoke-static {v0, v1}, Lcom/android/tools/r8/BaseCommand$Builder;->$r8$lambda$92Svnmnzb9SXUjbiUZsfJnzOnkA(Lcom/android/tools/r8/BaseCommand$Builder;Ljava/nio/file/Path;)V

    return-void
.end method
