.class public final synthetic Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder;

.field public final synthetic f$1:Ljava/util/Collection;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder;Ljava/util/Collection;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder;

    iput-object p2, p0, Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder$$ExternalSyntheticLambda0;->f$1:Ljava/util/Collection;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder;

    iget-object v1, p0, Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder$$ExternalSyntheticLambda0;->f$1:Ljava/util/Collection;

    invoke-static {v0, v1}, Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder;->$r8$lambda$Vz_O5Dg94BWfVmHthzcK_P559uU(Lcom/android/tools/r8/GlobalSyntheticsGeneratorCommand$Builder;Ljava/util/Collection;)V

    return-void
.end method
