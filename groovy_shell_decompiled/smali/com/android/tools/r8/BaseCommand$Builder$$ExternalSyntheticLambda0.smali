.class public final synthetic Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/yu$a;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/BaseCommand$Builder;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/q7;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/BaseCommand$Builder;Lcom/android/tools/r8/internal/q7;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/BaseCommand$Builder;

    iput-object p2, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/q7;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/BaseCommand$Builder;

    iget-object v1, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/q7;

    invoke-static {v0, v1}, Lcom/android/tools/r8/BaseCommand$Builder;->$r8$lambda$QUt1RvFx2otlRXkaJpTMDuB6dIc(Lcom/android/tools/r8/BaseCommand$Builder;Lcom/android/tools/r8/internal/q7;)V

    return-void
.end method
