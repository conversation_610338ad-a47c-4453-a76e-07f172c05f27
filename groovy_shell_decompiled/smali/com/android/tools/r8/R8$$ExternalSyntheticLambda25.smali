.class public final synthetic Lcom/android/tools/r8/R8$$ExternalSyntheticLambda25;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/op0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/R8;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/UU$a;

.field public final synthetic f$2:Lcom/android/tools/r8/graph/y;

.field public final synthetic f$3:Ljava/util/concurrent/ExecutorService;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/R8;Lcom/android/tools/r8/internal/UU$a;Lcom/android/tools/r8/graph/y;Ljava/util/concurrent/ExecutorService;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda25;->f$0:Lcom/android/tools/r8/R8;

    iput-object p2, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda25;->f$1:Lcom/android/tools/r8/internal/UU$a;

    iput-object p3, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda25;->f$2:Lcom/android/tools/r8/graph/y;

    iput-object p4, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda25;->f$3:Ljava/util/concurrent/ExecutorService;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 4

    iget-object v0, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda25;->f$0:Lcom/android/tools/r8/R8;

    iget-object v1, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda25;->f$1:Lcom/android/tools/r8/internal/UU$a;

    iget-object v2, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda25;->f$2:Lcom/android/tools/r8/graph/y;

    iget-object v3, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda25;->f$3:Ljava/util/concurrent/ExecutorService;

    check-cast p1, Lcom/android/tools/r8/internal/Yx;

    invoke-static {v0, v1, v2, v3, p1}, Lcom/android/tools/r8/R8;->$r8$lambda$buPS8Abkfe5EMp40YNl65qAjpnQ(Lcom/android/tools/r8/R8;Lcom/android/tools/r8/internal/UU$a;Lcom/android/tools/r8/graph/y;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Yx;)V

    return-void
.end method
