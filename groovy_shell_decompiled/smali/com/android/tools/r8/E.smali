.class public final Lcom/android/tools/r8/E;
.super Ljava/lang/RuntimeException;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final b:Lcom/android/tools/r8/CompilationFailedException;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/CompilationFailedException;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/RuntimeException;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/E;->b:Lcom/android/tools/r8/CompilationFailedException;

    return-void
.end method
