.class public final Lcom/android/tools/r8/o0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public a:Lcom/android/tools/r8/CompilationMode;

.field public b:Lcom/android/tools/r8/OutputMode;

.field public c:Ljava/nio/file/Path;

.field public d:Z

.field public e:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lcom/android/tools/r8/o0;->a:Lcom/android/tools/r8/CompilationMode;

    .line 3
    iput-object v0, p0, Lcom/android/tools/r8/o0;->b:Lcom/android/tools/r8/OutputMode;

    .line 4
    iput-object v0, p0, Lcom/android/tools/r8/o0;->c:Ljava/nio/file/Path;

    const/4 v0, 0x0

    .line 5
    iput-boolean v0, p0, Lcom/android/tools/r8/o0;->d:Z

    const/4 v0, 0x1

    .line 6
    iput-boolean v0, p0, Lcom/android/tools/r8/o0;->e:Z

    return-void
.end method
