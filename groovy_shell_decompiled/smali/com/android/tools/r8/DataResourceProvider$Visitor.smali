.class public interface abstract Lcom/android/tools/r8/DataResourceProvider$Visitor;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/DataResourceProvider;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Visitor"
.end annotation


# virtual methods
.method public abstract visit(Lcom/android/tools/r8/DataDirectoryResource;)V
.end method

.method public abstract visit(Lcom/android/tools/r8/DataEntryResource;)V
.end method
