.class public final Lcom/android/tools/r8/B;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/C;


# instance fields
.field public final a:Ljava/io/PrintStream;


# direct methods
.method public constructor <init>(Ljava/io/PrintStream;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/B;->a:Ljava/io/PrintStream;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/naming/b;)Lcom/android/tools/r8/graph/A0;
    .locals 0

    .line 2
    new-instance p1, Lcom/android/tools/r8/B$$ExternalSyntheticLambda0;

    invoke-direct {p1, p0}, Lcom/android/tools/r8/B$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/B;)V

    return-object p1
.end method

.method public final synthetic a(Lcom/android/tools/r8/graph/E0;)Ljava/io/PrintStream;
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/android/tools/r8/B;->a:Ljava/io/PrintStream;

    return-object p1
.end method

.method public final a()Ljava/util/function/Consumer;
    .locals 1

    .line 3
    invoke-static {}, Lcom/android/tools/r8/internal/Ch;->b()Ljava/util/function/Consumer;

    move-result-object v0

    return-object v0
.end method

.method public final b()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final close()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/B;->a:Ljava/io/PrintStream;

    invoke-virtual {v0}, Ljava/io/PrintStream;->flush()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/B;->a:Ljava/io/PrintStream;

    invoke-virtual {v0}, Ljava/io/PrintStream;->close()V

    return-void
.end method
