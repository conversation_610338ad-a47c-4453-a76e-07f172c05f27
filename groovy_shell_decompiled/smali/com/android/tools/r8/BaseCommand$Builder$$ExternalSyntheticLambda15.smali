.class public final synthetic Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda15;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/BaseCommand$Builder;

.field public final synthetic f$1:Z


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/BaseCommand$Builder;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda15;->f$0:Lcom/android/tools/r8/BaseCommand$Builder;

    iput-boolean p2, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda15;->f$1:Z

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda15;->f$0:Lcom/android/tools/r8/BaseCommand$Builder;

    iget-boolean v1, p0, Lcom/android/tools/r8/BaseCommand$Builder$$ExternalSyntheticLambda15;->f$1:Z

    invoke-static {v0, v1}, Lcom/android/tools/r8/BaseCommand$Builder;->$r8$lambda$S52e65_QxYoqPcpT1lZEBna-crs(Lcom/android/tools/r8/BaseCommand$Builder;Z)V

    return-void
.end method
