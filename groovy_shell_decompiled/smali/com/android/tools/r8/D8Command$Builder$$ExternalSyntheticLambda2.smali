.class public final synthetic Lcom/android/tools/r8/D8Command$Builder$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/D8Command$Builder;

.field public final synthetic f$1:Ljava/util/List;

.field public final synthetic f$2:Lcom/android/tools/r8/origin/Origin;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/D8Command$Builder;Ljava/util/List;Lcom/android/tools/r8/origin/Origin;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/D8Command$Builder$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/D8Command$Builder;

    iput-object p2, p0, Lcom/android/tools/r8/D8Command$Builder$$ExternalSyntheticLambda2;->f$1:Ljava/util/List;

    iput-object p3, p0, Lcom/android/tools/r8/D8Command$Builder$$ExternalSyntheticLambda2;->f$2:Lcom/android/tools/r8/origin/Origin;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/D8Command$Builder$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/D8Command$Builder;

    iget-object v1, p0, Lcom/android/tools/r8/D8Command$Builder$$ExternalSyntheticLambda2;->f$1:Ljava/util/List;

    iget-object v2, p0, Lcom/android/tools/r8/D8Command$Builder$$ExternalSyntheticLambda2;->f$2:Lcom/android/tools/r8/origin/Origin;

    invoke-static {v0, v1, v2}, Lcom/android/tools/r8/D8Command$Builder;->$r8$lambda$LMIAngoxDjMp9ThTDNkrEsrdV68(Lcom/android/tools/r8/D8Command$Builder;Ljava/util/List;Lcom/android/tools/r8/origin/Origin;)V

    return-void
.end method
