.class public interface abstract Lcom/android/tools/r8/MarkerInfoConsumerData;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# virtual methods
.method public abstract getInputOrigin()Lcom/android/tools/r8/origin/Origin;
.end method

.method public abstract getMarkers()Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Lcom/android/tools/r8/MarkerInfo;",
            ">;"
        }
    .end annotation
.end method

.method public abstract hasMarkers()Z
.end method
