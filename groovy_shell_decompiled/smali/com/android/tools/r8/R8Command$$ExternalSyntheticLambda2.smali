.class public final synthetic Lcom/android/tools/r8/R8Command$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/utils/w;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/utils/w;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/R8Command$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/utils/w;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/R8Command$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/utils/w;

    check-cast p1, Lcom/android/tools/r8/AndroidResourceConsumer;

    invoke-static {v0, p1}, Lcom/android/tools/r8/R8Command;->$r8$lambda$tDC2VsAARTMqDDrd7btVY8S0g9I(Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/AndroidResourceConsumer;)Lcom/android/tools/r8/naming/Q;

    move-result-object p1

    return-object p1
.end method
