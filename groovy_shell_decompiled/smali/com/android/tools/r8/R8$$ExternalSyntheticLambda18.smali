.class public final synthetic Lcom/android/tools/r8/R8$$ExternalSyntheticLambda18;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/y;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda18;->f$0:Lcom/android/tools/r8/graph/y;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda18;->f$0:Lcom/android/tools/r8/graph/y;

    invoke-static {v0}, Lcom/android/tools/r8/R8;->$r8$lambda$d9HY9kssW_bwwWpv76Y0H3kLJu0(Lcom/android/tools/r8/graph/y;)Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method
