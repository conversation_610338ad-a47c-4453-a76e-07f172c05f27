.class public interface abstract Lcom/android/tools/r8/ProgramConsumer;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# virtual methods
.method public abstract finished(Lcom/android/tools/r8/DiagnosticsHandler;)V
.end method

.method public getDataResourceConsumer()Lcom/android/tools/r8/DataResourceConsumer;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
