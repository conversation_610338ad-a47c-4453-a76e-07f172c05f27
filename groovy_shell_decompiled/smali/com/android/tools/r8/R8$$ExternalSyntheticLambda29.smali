.class public final synthetic Lcom/android/tools/r8/R8$$ExternalSyntheticLambda29;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/op0;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/R8$$ExternalSyntheticLambda29;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda29;

    invoke-direct {v0}, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda29;-><init>()V

    sput-object v0, Lcom/android/tools/r8/R8$$ExternalSyntheticLambda29;->INSTANCE:Lcom/android/tools/r8/R8$$ExternalSyntheticLambda29;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 0

    check-cast p1, Lcom/android/tools/r8/internal/Lx;

    invoke-static {p1}, Lcom/android/tools/r8/R8;->$r8$lambda$hLh8E1a3f8zz8mAoCdUx34oVpKQ(Lcom/android/tools/r8/internal/Lx;)V

    return-void
.end method
