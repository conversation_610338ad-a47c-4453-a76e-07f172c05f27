.class public final synthetic Lcom/android/tools/r8/ExtractMarker$$ExternalSyntheticLambda6;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/ExtractMarker$$ExternalSyntheticLambda6;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/ExtractMarker$$ExternalSyntheticLambda6;

    invoke-direct {v0}, Lcom/android/tools/r8/ExtractMarker$$ExternalSyntheticLambda6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/ExtractMarker$$ExternalSyntheticLambda6;->INSTANCE:Lcom/android/tools/r8/ExtractMarker$$ExternalSyntheticLambda6;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/OT;

    check-cast p1, Lcom/android/tools/r8/dex/W;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/OT;-><init>(Lcom/android/tools/r8/dex/W;)V

    check-cast v0, Lcom/android/tools/r8/MarkerInfo;

    return-object v0
.end method
