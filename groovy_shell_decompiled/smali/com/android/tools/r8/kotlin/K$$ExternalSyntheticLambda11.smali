.class public final synthetic Lcom/android/tools/r8/kotlin/K$$ExternalSyntheticLambda11;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/GO;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/GO;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/kotlin/K$$ExternalSyntheticLambda11;->f$0:Lcom/android/tools/r8/internal/GO;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/kotlin/K$$ExternalSyntheticLambda11;->f$0:Lcom/android/tools/r8/internal/GO;

    check-cast p1, Ljava/lang/String;

    invoke-static {v0, p1}, Lcom/android/tools/r8/kotlin/K;->a(Lcom/android/tools/r8/internal/GO;Ljava/lang/String;)V

    return-void
.end method
