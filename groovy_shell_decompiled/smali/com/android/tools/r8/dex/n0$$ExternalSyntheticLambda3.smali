.class public final synthetic Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda3;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Ljava/util/Set;


# direct methods
.method public synthetic constructor <init>(Ljava/util/Set;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda3;->f$0:Ljava/util/Set;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda3;->f$0:Ljava/util/Set;

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-interface {v0, p1}, <PERSON>java/util/Set;->add(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
