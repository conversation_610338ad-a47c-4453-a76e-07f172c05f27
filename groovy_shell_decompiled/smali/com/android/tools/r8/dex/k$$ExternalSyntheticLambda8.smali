.class public final synthetic Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda8;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/B1;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/B1;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda8;->f$0:Lcom/android/tools/r8/graph/B1;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda8;->f$0:Lcom/android/tools/r8/graph/B1;

    check-cast p1, Lcom/android/tools/r8/dex/W;

    invoke-static {v0, p1}, Lcom/android/tools/r8/dex/k;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/dex/W;)Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    return-object p1
.end method
