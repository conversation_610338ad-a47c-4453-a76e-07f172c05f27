.class public final Lcom/android/tools/r8/dex/n0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/dex/M;


# static fields
.field public static final synthetic n:Z


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Lcom/android/tools/r8/dex/s0;

.field public final c:Lcom/android/tools/r8/internal/jQ;

.field public final d:Ljava/util/LinkedHashSet;

.field public final e:Ljava/util/LinkedHashSet;

.field public final f:Ljava/util/LinkedHashSet;

.field public final g:Ljava/util/LinkedHashSet;

.field public final h:Ljava/util/LinkedHashMap;

.field public final i:Ljava/util/LinkedHashSet;

.field public final j:Ljava/util/LinkedHashSet;

.field public final k:Ljava/util/LinkedHashSet;

.field public final l:Lcom/android/tools/r8/dex/m0;

.field public m:Lcom/android/tools/r8/graph/E2;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/dex/t0;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/dex/n0;->n:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/dex/s0;Lcom/android/tools/r8/graph/y;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/dex/n0;->d:Ljava/util/LinkedHashSet;

    .line 3
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/dex/n0;->e:Ljava/util/LinkedHashSet;

    .line 4
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/dex/n0;->f:Ljava/util/LinkedHashSet;

    .line 5
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/dex/n0;->g:Ljava/util/LinkedHashSet;

    .line 6
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/dex/n0;->h:Ljava/util/LinkedHashMap;

    .line 7
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/dex/n0;->i:Ljava/util/LinkedHashSet;

    .line 8
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/dex/n0;->j:Ljava/util/LinkedHashSet;

    .line 9
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/dex/n0;->k:Ljava/util/LinkedHashSet;

    const/4 v0, 0x0

    .line 44
    iput-object v0, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    .line 45
    iput-object p2, p0, Lcom/android/tools/r8/dex/n0;->a:Lcom/android/tools/r8/graph/y;

    .line 46
    iput-object p1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    .line 47
    new-instance p1, Lcom/android/tools/r8/internal/jQ;

    const/4 v0, 0x0

    invoke-direct {p1, p2, v0}, Lcom/android/tools/r8/internal/jQ;-><init>(Lcom/android/tools/r8/graph/y;I)V

    iput-object p1, p0, Lcom/android/tools/r8/dex/n0;->c:Lcom/android/tools/r8/internal/jQ;

    .line 49
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/utils/w;->A1:Lcom/android/tools/r8/utils/w$q;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 51
    new-instance p1, Lcom/android/tools/r8/dex/m0;

    invoke-direct {p1}, Lcom/android/tools/r8/dex/m0;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/dex/n0;->l:Lcom/android/tools/r8/dex/m0;

    return-void
.end method

.method public static a(Ljava/util/Set;Ljava/util/function/Function;)V
    .locals 1

    .line 38
    new-instance v0, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda9;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda9;-><init>(Ljava/util/function/Function;)V

    invoke-interface {p0, v0}, Ljava/util/Set;->forEach(Ljava/util/function/Consumer;)V

    .line 42
    invoke-interface {p0}, Ljava/util/Set;->clear()V

    return-void
.end method

.method public static synthetic a(Ljava/util/function/Function;Lcom/android/tools/r8/graph/n1;)V
    .locals 0

    .line 43
    invoke-interface {p0, p1}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/Boolean;

    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p0

    .line 44
    sget-boolean p1, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez p1, :cond_1

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_1
    :goto_0
    return-void
.end method


# virtual methods
.method public final a()V
    .locals 3

    .line 45
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->d:Ljava/util/LinkedHashSet;

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda11;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda11;-><init>(Lcom/android/tools/r8/dex/s0;)V

    invoke-static {v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Ljava/util/Set;Ljava/util/function/Function;)V

    .line 46
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->e:Ljava/util/LinkedHashSet;

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda15;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda15;-><init>(Lcom/android/tools/r8/dex/s0;)V

    invoke-static {v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Ljava/util/Set;Ljava/util/function/Function;)V

    .line 47
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->f:Ljava/util/LinkedHashSet;

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda16;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda16;-><init>(Lcom/android/tools/r8/dex/s0;)V

    invoke-static {v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Ljava/util/Set;Ljava/util/function/Function;)V

    .line 49
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->h:Ljava/util/LinkedHashMap;

    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda12;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda12;-><init>(Lcom/android/tools/r8/dex/s0;)V

    invoke-static {v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Ljava/util/Set;Ljava/util/function/Function;)V

    .line 50
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->g:Ljava/util/LinkedHashSet;

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda14;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda14;-><init>(Lcom/android/tools/r8/dex/s0;)V

    invoke-static {v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Ljava/util/Set;Ljava/util/function/Function;)V

    .line 51
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->i:Ljava/util/LinkedHashSet;

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda13;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda13;-><init>(Lcom/android/tools/r8/dex/s0;)V

    invoke-static {v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Ljava/util/Set;Ljava/util/function/Function;)V

    .line 52
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->j:Ljava/util/LinkedHashSet;

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda10;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda10;-><init>(Lcom/android/tools/r8/dex/s0;)V

    invoke-static {v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Ljava/util/Set;Ljava/util/function/Function;)V

    .line 53
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->k:Ljava/util/LinkedHashSet;

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda17;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda17;-><init>(Lcom/android/tools/r8/dex/s0;)V

    invoke-static {v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Ljava/util/Set;Ljava/util/function/Function;)V

    .line 55
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->A1:Lcom/android/tools/r8/utils/w$q;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/D0;)Z
    .locals 3

    .line 32
    sget-boolean v0, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 33
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->j:Ljava/util/LinkedHashSet;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda2;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda2;-><init>(Ljava/util/Set;)V

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v0, v0, Lcom/android/tools/r8/dex/s0;->i:Ljava/util/Set;

    const/4 v2, 0x1

    .line 34
    invoke-virtual {p0, p1, v1, v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Lcom/android/tools/r8/graph/n1;Ljava/util/function/Predicate;Ljava/util/Set;Z)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/E2;)Z
    .locals 3

    .line 6
    sget-boolean v0, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 7
    :cond_1
    :goto_0
    iput-object p1, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->d:Ljava/util/LinkedHashSet;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda3;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda3;-><init>(Ljava/util/Set;)V

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v0, v0, Lcom/android/tools/r8/dex/s0;->c:Ljava/util/Set;

    const/4 v2, 0x1

    .line 9
    invoke-virtual {p0, p1, v1, v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Lcom/android/tools/r8/graph/n1;Ljava/util/function/Predicate;Ljava/util/Set;Z)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/F2;)Z
    .locals 3

    .line 21
    sget-boolean v0, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 22
    :cond_1
    :goto_0
    new-instance v0, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda1;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/dex/n0;)V

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v1, v1, Lcom/android/tools/r8/dex/s0;->d:Ljava/util/IdentityHashMap;

    .line 25
    invoke-virtual {v1}, Ljava/util/IdentityHashMap;->keySet()Ljava/util/Set;

    move-result-object v1

    const/4 v2, 0x1

    .line 26
    invoke-virtual {p0, p1, v0, v1, v2}, Lcom/android/tools/r8/dex/n0;->a(Lcom/android/tools/r8/graph/n1;Ljava/util/function/Predicate;Ljava/util/Set;Z)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/I2;)Z
    .locals 3

    .line 16
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    if-nez v0, :cond_1

    .line 18
    sget-boolean v0, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez v0, :cond_1

    const-string v0, "~~"

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/I2;->c(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 20
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->i:Ljava/util/LinkedHashSet;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda4;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda4;-><init>(Ljava/util/Set;)V

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v0, v0, Lcom/android/tools/r8/dex/s0;->h:Ljava/util/Set;

    const/4 v2, 0x0

    invoke-virtual {p0, p1, v1, v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Lcom/android/tools/r8/graph/n1;Ljava/util/function/Predicate;Ljava/util/Set;Z)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;)Z
    .locals 3

    .line 27
    sget-boolean v0, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez v0, :cond_1

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    if-nez v0, :cond_2

    .line 28
    sget-boolean v0, Lcom/android/tools/r8/synthesis/Q;->X:Z

    .line 29
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->Y0()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/android/tools/r8/synthesis/Q;->a(Ljava/lang/String;)V

    .line 30
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->g:Ljava/util/LinkedHashSet;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda5;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda5;-><init>(Ljava/util/Set;)V

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v0, v0, Lcom/android/tools/r8/dex/s0;->e:Ljava/util/Set;

    const/4 v2, 0x1

    .line 31
    invoke-virtual {p0, p1, v1, v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Lcom/android/tools/r8/graph/n1;Ljava/util/function/Predicate;Ljava/util/Set;Z)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/l1;)Z
    .locals 3

    .line 10
    sget-boolean v0, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 11
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->e:Ljava/util/LinkedHashSet;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda6;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda6;-><init>(Ljava/util/Set;)V

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v0, v0, Lcom/android/tools/r8/dex/s0;->g:Ljava/util/Set;

    const/4 v2, 0x1

    .line 12
    invoke-virtual {p0, p1, v1, v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Lcom/android/tools/r8/graph/n1;Ljava/util/function/Predicate;Ljava/util/Set;Z)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/n1;Ljava/util/function/Predicate;Ljava/util/Set;Z)Z
    .locals 0

    .line 1
    invoke-interface {p3, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p3

    if-eqz p3, :cond_0

    const/4 p1, 0x0

    return p1

    .line 4
    :cond_0
    invoke-interface {p2, p1}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result p1

    .line 5
    sget-boolean p2, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez p2, :cond_2

    if-eqz p1, :cond_2

    if-eqz p4, :cond_2

    iget-object p2, p0, Lcom/android/tools/r8/dex/n0;->d:Ljava/util/LinkedHashSet;

    iget-object p3, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    invoke-interface {p2, p3}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_2
    :goto_0
    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;)Z
    .locals 3

    .line 13
    sget-boolean v0, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 14
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->f:Ljava/util/LinkedHashSet;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda7;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda7;-><init>(Ljava/util/Set;)V

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v0, v0, Lcom/android/tools/r8/dex/s0;->f:Ljava/util/Set;

    const/4 v2, 0x1

    .line 15
    invoke-virtual {p0, p1, v1, v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Lcom/android/tools/r8/graph/n1;Ljava/util/function/Predicate;Ljava/util/Set;Z)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/z2;)Z
    .locals 3

    .line 35
    sget-boolean v0, Lcom/android/tools/r8/dex/n0;->n:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->m:Lcom/android/tools/r8/graph/E2;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 36
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->k:Ljava/util/LinkedHashSet;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v1, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda8;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda8;-><init>(Ljava/util/Set;)V

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v0, v0, Lcom/android/tools/r8/dex/s0;->j:Ljava/util/Set;

    const/4 v2, 0x1

    .line 37
    invoke-virtual {p0, p1, v1, v0, v2}, Lcom/android/tools/r8/dex/n0;->a(Lcom/android/tools/r8/graph/n1;Ljava/util/function/Predicate;Ljava/util/Set;Z)Z

    move-result p1

    return p1
.end method

.method public final b()Z
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->d:Ljava/util/LinkedHashSet;

    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->e:Ljava/util/LinkedHashSet;

    .line 3
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->f:Ljava/util/LinkedHashSet;

    .line 4
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->h:Ljava/util/LinkedHashMap;

    .line 5
    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->g:Ljava/util/LinkedHashSet;

    .line 6
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->i:Ljava/util/LinkedHashSet;

    .line 7
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->j:Ljava/util/LinkedHashSet;

    .line 8
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->k:Ljava/util/LinkedHashSet;

    .line 9
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final synthetic b(Lcom/android/tools/r8/graph/F2;)Z
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/n0;->h:Ljava/util/LinkedHashMap;

    iget-object v1, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v1, v1, Lcom/android/tools/r8/dex/s0;->b:Ljava/util/HashMap;

    new-instance v2, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda0;

    invoke-direct {v2, p0}, Lcom/android/tools/r8/dex/n0$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/dex/n0;)V

    iget-object v3, p0, Lcom/android/tools/r8/dex/n0;->b:Lcom/android/tools/r8/dex/s0;

    iget-object v3, v3, Lcom/android/tools/r8/dex/s0;->a:Lcom/android/tools/r8/graph/B1;

    invoke-static {p1, v0, v1, v2, v3}, Lcom/android/tools/r8/dex/t0;->a(Lcom/android/tools/r8/graph/F2;Ljava/util/AbstractMap;Ljava/util/HashMap;Ljava/util/function/Consumer;Lcom/android/tools/r8/graph/B1;)Z

    move-result p1

    return p1
.end method
