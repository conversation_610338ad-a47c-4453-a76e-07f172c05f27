.class public final synthetic Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/graph/E2$a;


# instance fields
.field public final synthetic f$0:Ljava/lang/Long;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Long;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda0;->f$0:Ljava/lang/Long;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/E2;)J
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda0;->f$0:Ljava/lang/Long;

    invoke-static {v0, p1}, Lcom/android/tools/r8/dex/C;->a(<PERSON>java/lang/Long;Lcom/android/tools/r8/graph/E2;)J

    move-result-wide v0

    return-wide v0
.end method
