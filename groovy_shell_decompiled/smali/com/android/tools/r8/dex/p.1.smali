.class public final Lcom/android/tools/r8/dex/p;
.super Lcom/android/tools/r8/dex/r;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Ljava/util/concurrent/ConcurrentHashMap;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/dex/r;-><init>()V

    .line 2
    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/dex/p;->b:Ljava/util/concurrent/ConcurrentHashMap;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    return-void
.end method

.method public static b()Ljava/util/Comparator;
    .locals 1

    .line 15
    sget-object v0, Lcom/android/tools/r8/dex/p$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/dex/p$$ExternalSyntheticLambda0;

    return-object v0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/E2;)V
    .locals 5

    .line 28
    iget-object v0, p1, Lcom/android/tools/r8/graph/E0;->g:Lcom/android/tools/r8/graph/J2;

    .line 29
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/p;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    const/4 v2, 0x1

    if-eqz v1, :cond_0

    .line 30
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/p;->b(Lcom/android/tools/r8/graph/J2;)V

    .line 31
    iget-object v1, p0, Lcom/android/tools/r8/dex/p;->b:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v1, v0}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/dex/o;

    iput-boolean v2, v0, Lcom/android/tools/r8/dex/o;->c:Z

    .line 32
    :cond_0
    iget-object p1, p1, Lcom/android/tools/r8/graph/E0;->h:Lcom/android/tools/r8/graph/L2;

    iget-object p1, p1, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_2

    aget-object v3, p1, v1

    .line 33
    invoke-virtual {p0, v3}, Lcom/android/tools/r8/dex/p;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v4

    if-eqz v4, :cond_1

    .line 34
    invoke-virtual {p0, v3}, Lcom/android/tools/r8/dex/p;->b(Lcom/android/tools/r8/graph/J2;)V

    .line 35
    iget-object v4, p0, Lcom/android/tools/r8/dex/p;->b:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v4, v3}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/dex/o;

    iput-boolean v2, v3, Lcom/android/tools/r8/dex/o;->c:Z

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;)V
    .locals 1

    .line 26
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/dex/p;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 27
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/dex/p;->b(Lcom/android/tools/r8/graph/J2;)V

    :cond_0
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/l1;)V
    .locals 2

    .line 16
    iget-object v0, p1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    iget-object v1, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 17
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/p;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 18
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/p;->b(Lcom/android/tools/r8/graph/J2;)V

    .line 19
    iget-object v1, p1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/J2;->H0()Z

    move-result v1

    if-nez v1, :cond_0

    .line 20
    iget-object v0, v0, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "L$-vivified-$/"

    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 21
    iget-object v0, p0, Lcom/android/tools/r8/dex/p;->b:Ljava/util/concurrent/ConcurrentHashMap;

    iget-object v1, p1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0, v1}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/dex/o;

    iget-object v0, v0, Lcom/android/tools/r8/dex/o;->a:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 24
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/p;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 25
    iget-object p1, p1, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/dex/p;->b(Lcom/android/tools/r8/graph/J2;)V

    :cond_1
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;)V
    .locals 4

    .line 2
    iget-object v0, p1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    iget-object v1, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 3
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/p;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 4
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/p;->b(Lcom/android/tools/r8/graph/J2;)V

    .line 5
    iget-object v1, p1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/J2;->H0()Z

    move-result v1

    if-nez v1, :cond_0

    .line 6
    iget-object v0, v0, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "L$-vivified-$/"

    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 7
    iget-object v0, p0, Lcom/android/tools/r8/dex/p;->b:Ljava/util/concurrent/ConcurrentHashMap;

    iget-object v1, p1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0, v1}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/dex/o;

    iget-object v0, v0, Lcom/android/tools/r8/dex/o;->b:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 10
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/F2;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/p;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 11
    iget-object v0, p1, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/F2;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/p;->b(Lcom/android/tools/r8/graph/J2;)V

    .line 13
    :cond_1
    iget-object p1, p1, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object p1, p1, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    iget-object p1, p1, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_3

    aget-object v2, p1, v1

    .line 14
    invoke-virtual {p0, v2}, Lcom/android/tools/r8/dex/p;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v3

    if-eqz v3, :cond_2

    .line 15
    invoke-virtual {p0, v2}, Lcom/android/tools/r8/dex/p;->b(Lcom/android/tools/r8/graph/J2;)V

    :cond_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method public final a(Lcom/android/tools/r8/utils/w;)V
    .locals 10

    .line 36
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 37
    invoke-static {}, Ljava/lang/System;->lineSeparator()Ljava/lang/String;

    move-result-object v1

    .line 38
    iget-object v2, p0, Lcom/android/tools/r8/dex/p;->b:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v2}, Ljava/util/concurrent/ConcurrentHashMap;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-static {}, Lcom/android/tools/r8/dex/p;->b()Ljava/util/Comparator;

    move-result-object v3

    invoke-static {v2, v3}, Lcom/android/tools/r8/internal/ze;->a(Ljava/util/Collection;Ljava/util/Comparator;)Ljava/util/Collection;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_a

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/J2;

    .line 39
    iget-object v4, p0, Lcom/android/tools/r8/dex/p;->b:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v4, v3}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/dex/o;

    const-string v5, "-keep class "

    .line 40
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    .line 41
    iget-object v6, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v6}, Lcom/android/tools/r8/graph/y;->x()Lcom/android/tools/r8/naming/r0;

    move-result-object v6

    invoke-virtual {v6, v3}, Lcom/android/tools/r8/naming/r0;->e(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;

    move-result-object v6

    if-eqz v6, :cond_0

    goto :goto_1

    .line 42
    :cond_0
    iget-object v6, v3, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    .line 43
    :goto_1
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Lcom/android/tools/r8/internal/Nk;->b(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    .line 44
    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 45
    iget-boolean v3, v4, Lcom/android/tools/r8/dex/o;->c:Z

    if-eqz v3, :cond_1

    const-string v3, " { *; }"

    .line 46
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 49
    :cond_1
    iget-object v3, v4, Lcom/android/tools/r8/dex/o;->a:Ljava/util/Set;

    invoke-interface {v3}, Ljava/util/Set;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_2

    iget-object v3, v4, Lcom/android/tools/r8/dex/o;->b:Ljava/util/Set;

    invoke-interface {v3}, Ljava/util/Set;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_2

    .line 50
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    :cond_2
    const-string v3, " {"

    .line 53
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 54
    iget-object v3, v4, Lcom/android/tools/r8/dex/o;->a:Ljava/util/Set;

    invoke-static {}, Lcom/android/tools/r8/dex/p;->b()Ljava/util/Comparator;

    move-result-object v5

    invoke-static {v3, v5}, Lcom/android/tools/r8/internal/ze;->a(Ljava/util/Collection;Ljava/util/Comparator;)Ljava/util/Collection;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    const-string v6, " "

    const-string v7, "    "

    if-eqz v5, :cond_4

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/graph/l1;

    .line 55
    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    iget-object v8, v5, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    .line 56
    iget-object v9, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v9}, Lcom/android/tools/r8/graph/y;->x()Lcom/android/tools/r8/naming/r0;

    move-result-object v9

    invoke-virtual {v9, v8}, Lcom/android/tools/r8/naming/r0;->e(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;

    move-result-object v9

    if-eqz v9, :cond_3

    goto :goto_3

    .line 57
    :cond_3
    iget-object v9, v8, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    .line 58
    :goto_3
    invoke-virtual {v9}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/android/tools/r8/internal/Nk;->b(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    .line 59
    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    .line 60
    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    iget-object v5, v5, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    .line 61
    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v6, ";"

    .line 62
    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    .line 63
    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_2

    .line 65
    :cond_4
    iget-object v3, v4, Lcom/android/tools/r8/dex/o;->b:Ljava/util/Set;

    invoke-static {}, Lcom/android/tools/r8/dex/p;->b()Ljava/util/Comparator;

    move-result-object v4

    invoke-static {v3, v4}, Lcom/android/tools/r8/internal/ze;->a(Ljava/util/Collection;Ljava/util/Comparator;)Ljava/util/Collection;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_4
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_9

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/x2;

    .line 66
    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v8, v4, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v8, v8, Lcom/android/tools/r8/graph/F2;->e:Lcom/android/tools/r8/graph/J2;

    .line 67
    iget-object v9, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v9}, Lcom/android/tools/r8/graph/y;->x()Lcom/android/tools/r8/naming/r0;

    move-result-object v9

    invoke-virtual {v9, v8}, Lcom/android/tools/r8/naming/r0;->e(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;

    move-result-object v9

    if-eqz v9, :cond_5

    goto :goto_5

    .line 68
    :cond_5
    iget-object v9, v8, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    .line 69
    :goto_5
    invoke-virtual {v9}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/android/tools/r8/internal/Nk;->b(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    .line 70
    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    .line 71
    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v8, v4, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    .line 72
    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v8, "("

    .line 73
    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v5, 0x0

    .line 74
    :goto_6
    invoke-virtual {v4}, Lcom/android/tools/r8/graph/x2;->z0()I

    move-result v8

    if-ge v5, v8, :cond_8

    if-eqz v5, :cond_6

    const-string v8, ", "

    .line 76
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 78
    :cond_6
    iget-object v8, v4, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v8, v8, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    iget-object v8, v8, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    aget-object v8, v8, v5

    .line 79
    iget-object v9, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v9}, Lcom/android/tools/r8/graph/y;->x()Lcom/android/tools/r8/naming/r0;

    move-result-object v9

    invoke-virtual {v9, v8}, Lcom/android/tools/r8/naming/r0;->e(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;

    move-result-object v9

    if-eqz v9, :cond_7

    goto :goto_7

    .line 80
    :cond_7
    iget-object v9, v8, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    .line 81
    :goto_7
    invoke-virtual {v9}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lcom/android/tools/r8/internal/Nk;->b(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    .line 82
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    goto :goto_6

    :cond_8
    const-string v4, ");"

    .line 84
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_4

    :cond_9
    const-string v3, "}"

    .line 86
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 88
    :cond_a
    iget-object v1, p1, Lcom/android/tools/r8/utils/w;->S1:Lcom/android/tools/r8/StringConsumer;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    iget-object v2, p1, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    invoke-interface {v1, v0, v2}, Lcom/android/tools/r8/StringConsumer;->accept(Ljava/lang/String;Lcom/android/tools/r8/DiagnosticsHandler;)V

    .line 89
    iget-object v0, p1, Lcom/android/tools/r8/utils/w;->S1:Lcom/android/tools/r8/StringConsumer;

    iget-object p1, p1, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    invoke-interface {v0, p1}, Lcom/android/tools/r8/I;->finished(Lcom/android/tools/r8/DiagnosticsHandler;)V

    return-void
.end method

.method public final a()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final b(Lcom/android/tools/r8/graph/J2;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->B0()I

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    .line 6
    :cond_0
    iget-object p1, p1, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    .line 7
    iget v2, p1, Lcom/android/tools/r8/graph/I2;->e:I

    sub-int/2addr v2, v1

    .line 8
    iget-object p1, p1, Lcom/android/tools/r8/graph/I2;->f:[B

    array-length v3, p1

    .line 9
    invoke-static {p1, v1, v3}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p1

    .line 10
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->c:Ljava/util/concurrent/ConcurrentHashMap;

    .line 11
    new-instance v3, Lcom/android/tools/r8/graph/I2;

    invoke-direct {v3, v2, p1}, Lcom/android/tools/r8/graph/I2;-><init>(I[B)V

    invoke-virtual {v1, v3}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/I2;

    .line 12
    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->d:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/J2;

    .line 13
    :goto_0
    iget-object v0, p1, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "L$-vivified-$/"

    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    .line 14
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/dex/p;->b:Ljava/util/concurrent/ConcurrentHashMap;

    new-instance v1, Lcom/android/tools/r8/dex/o;

    invoke-direct {v1}, Lcom/android/tools/r8/dex/o;-><init>()V

    invoke-virtual {v0, p1, v1}, Ljava/util/concurrent/ConcurrentHashMap;->putIfAbsent(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public final c(Lcom/android/tools/r8/graph/J2;)Z
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    .line 2
    iget-object v1, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->x()Lcom/android/tools/r8/naming/r0;

    move-result-object v1

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/naming/r0;->e(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;

    move-result-object v1

    if-nez v1, :cond_2

    iget-object v1, v0, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 3
    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/QS;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    if-nez v1, :cond_2

    iget-object v1, v0, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 4
    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/QS;->b(Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    if-nez v1, :cond_2

    .line 8
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->Y0()Ljava/lang/String;

    move-result-object v1

    iget-object v2, v0, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 12
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/QS;->f()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "L"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 13
    invoke-virtual {v1, v2}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    .line 20
    :cond_0
    invoke-static {p1}, Lcom/android/tools/r8/internal/hH;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 21
    iget-object v1, p0, Lcom/android/tools/r8/dex/p;->a:Lcom/android/tools/r8/graph/y;

    .line 22
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    .line 23
    invoke-static {v1, p1}, Lcom/android/tools/r8/internal/hH;->b(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 26
    :cond_1
    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/QS;->l()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1

    :cond_2
    :goto_0
    const/4 p1, 0x1

    return p1
.end method
