.class public final synthetic Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/g60;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/g60;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/g60;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/g60;

    check-cast p1, Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/bn;->a(Lcom/android/tools/r8/graph/G0;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    return-object p1
.end method
