.class public final synthetic Lcom/android/tools/r8/dex/c$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/lp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/dex/c;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/w0;

.field public final synthetic f$2:Lcom/android/tools/r8/t0;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/dex/c;Lcom/android/tools/r8/graph/w0;Lcom/android/tools/r8/t0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/dex/c$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/dex/c;

    iput-object p2, p0, Lcom/android/tools/r8/dex/c$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/w0;

    iput-object p3, p0, Lcom/android/tools/r8/dex/c$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/t0;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/dex/c$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/dex/c;

    iget-object v1, p0, Lcom/android/tools/r8/dex/c$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/w0;

    iget-object v2, p0, Lcom/android/tools/r8/dex/c$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/t0;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/dex/c;->a(Lcom/android/tools/r8/graph/w0;Lcom/android/tools/r8/t0;)V

    return-void
.end method
