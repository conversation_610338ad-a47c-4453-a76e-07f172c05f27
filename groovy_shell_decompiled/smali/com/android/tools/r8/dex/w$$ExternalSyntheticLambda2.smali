.class public final synthetic Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/ToIntFunction;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/dex/v;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/dex/v;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/dex/v;

    return-void
.end method


# virtual methods
.method public final applyAsInt(Ljava/lang/Object;)I
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/dex/v;

    check-cast p1, Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/dex/v;->a(Lcom/android/tools/r8/graph/D5;)I

    move-result p1

    return p1
.end method
