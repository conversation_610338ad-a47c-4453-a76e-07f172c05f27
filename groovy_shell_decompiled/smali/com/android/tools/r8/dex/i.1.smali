.class public abstract Lcom/android/tools/r8/dex/i;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic b:Z


# instance fields
.field public a:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/dex/k;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/dex/i;->b:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 2
    iput-boolean v0, p0, Lcom/android/tools/r8/dex/i;->a:Z

    return-void
.end method


# virtual methods
.method public abstract a()Lcom/android/tools/r8/graph/I2;
.end method
