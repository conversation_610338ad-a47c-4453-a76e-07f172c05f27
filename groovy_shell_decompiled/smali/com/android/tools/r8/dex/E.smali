.class public Lcom/android/tools/r8/dex/E;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic d:Z = true


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(IIII)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Lcom/android/tools/r8/dex/E;->a:I

    .line 3
    sget-boolean p1, Lcom/android/tools/r8/dex/E;->d:Z

    if-nez p1, :cond_1

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 4
    :cond_1
    :goto_0
    iput p3, p0, Lcom/android/tools/r8/dex/E;->b:I

    .line 5
    iput p4, p0, Lcom/android/tools/r8/dex/E;->c:I

    return-void
.end method

.method public static b(I)Ljava/lang/String;
    .locals 0

    packed-switch p0, :pswitch_data_0

    packed-switch p0, :pswitch_data_1

    packed-switch p0, :pswitch_data_2

    const-string p0, "Unknown"

    return-object p0

    :pswitch_0
    const-string p0, "ClassDefs"

    return-object p0

    :pswitch_1
    const-string p0, "Methods"

    return-object p0

    :pswitch_2
    const-string p0, "Fields"

    return-object p0

    :pswitch_3
    const-string p0, "Protos"

    return-object p0

    :pswitch_4
    const-string p0, "Types"

    return-object p0

    :pswitch_5
    const-string p0, "Strings"

    return-object p0

    :pswitch_6
    const-string p0, "Header"

    return-object p0

    :pswitch_7
    const-string p0, "AnnotationSets"

    return-object p0

    :pswitch_8
    const-string p0, "AnnotationSetRefs"

    return-object p0

    :pswitch_9
    const-string p0, "TypeLists"

    return-object p0

    :pswitch_a
    const-string p0, "Maps"

    return-object p0

    :pswitch_b
    const-string p0, "AnnotationsDirectory"

    return-object p0

    :pswitch_c
    const-string p0, "EncodedArrays"

    return-object p0

    :pswitch_d
    const-string p0, "Annotation"

    return-object p0

    :pswitch_e
    const-string p0, "DebugInfo"

    return-object p0

    :pswitch_f
    const-string p0, "StringData"

    return-object p0

    :pswitch_10
    const-string p0, "Code"

    return-object p0

    :pswitch_11
    const-string p0, "ClassData"

    return-object p0

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x1000
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x2000
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
    .end packed-switch
.end method


# virtual methods
.method public final a(I)V
    .locals 0

    return-void
.end method

.method public final toString()Ljava/lang/String;
    .locals 4

    .line 1
    iget v0, p0, Lcom/android/tools/r8/dex/E;->a:I

    invoke-static {v0}, Lcom/android/tools/r8/dex/E;->b(I)Ljava/lang/String;

    move-result-object v0

    .line 2
    iget v1, p0, Lcom/android/tools/r8/dex/E;->c:I

    iget v2, p0, Lcom/android/tools/r8/dex/E;->b:I

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v3, " @"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
