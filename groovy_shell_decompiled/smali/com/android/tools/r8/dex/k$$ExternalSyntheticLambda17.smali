.class public final synthetic Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda17;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/xp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/dex/L;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/dex/L;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda17;->f$0:Lcom/android/tools/r8/dex/L;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda17;->f$0:Lcom/android/tools/r8/dex/L;

    invoke-virtual {v0}, Lcom/android/tools/r8/dex/L;->a()Lcom/android/tools/r8/dex/L;

    move-result-object v0

    return-object v0
.end method
