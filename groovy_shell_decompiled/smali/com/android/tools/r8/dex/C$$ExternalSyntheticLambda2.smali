.class public final synthetic Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda2;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda2;

    invoke-direct {v0}, Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda2;-><init>()V

    sput-object v0, Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda2;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    invoke-static {}, Lcom/android/tools/r8/graph/u0;->n0()Lcom/android/tools/r8/graph/u0;

    move-result-object v0

    return-object v0
.end method
