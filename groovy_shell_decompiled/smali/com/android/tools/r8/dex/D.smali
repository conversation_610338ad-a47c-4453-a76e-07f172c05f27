.class public Lcom/android/tools/r8/dex/D;
.super Lcom/android/tools/r8/dex/m;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final d:Lcom/android/tools/r8/internal/Wr;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/ProgramResource;)V
    .locals 1

    .line 1
    invoke-interface {p1}, Lcom/android/tools/r8/Resource;->getOrigin()Lcom/android/tools/r8/origin/Origin;

    move-result-object v0

    invoke-interface {p1}, Lcom/android/tools/r8/ProgramResource;->getByteStream()Ljava/io/InputStream;

    move-result-object p1

    invoke-static {p1}, Lcom/android/tools/r8/internal/om0;->a(Ljava/io/InputStream;)[B

    move-result-object p1

    invoke-direct {p0, v0, p1}, Lcom/android/tools/r8/dex/m;-><init>(Lcom/android/tools/r8/origin/Origin;[B)V

    .line 2
    iget-object p1, p0, Lcom/android/tools/r8/dex/m;->b:Lcom/android/tools/r8/dex/s;

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/dex/D;->a(Lcom/android/tools/r8/dex/s;I)Lcom/android/tools/r8/internal/Wr;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/dex/D;->d:Lcom/android/tools/r8/internal/Wr;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/origin/Origin;[B)V
    .locals 0

    .line 3
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/dex/m;-><init>(Lcom/android/tools/r8/origin/Origin;[B)V

    .line 4
    iget-object p1, p0, Lcom/android/tools/r8/dex/m;->b:Lcom/android/tools/r8/dex/s;

    const/4 p2, 0x0

    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/dex/D;->a(Lcom/android/tools/r8/dex/s;I)Lcom/android/tools/r8/internal/Wr;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/dex/D;->d:Lcom/android/tools/r8/internal/Wr;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/origin/Origin;[BI)V
    .locals 0

    .line 5
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/dex/m;-><init>(Lcom/android/tools/r8/origin/Origin;[B)V

    .line 6
    iget-object p1, p0, Lcom/android/tools/r8/dex/m;->b:Lcom/android/tools/r8/dex/s;

    invoke-virtual {p0, p1, p3}, Lcom/android/tools/r8/dex/D;->a(Lcom/android/tools/r8/dex/s;I)Lcom/android/tools/r8/internal/Wr;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/dex/D;->d:Lcom/android/tools/r8/internal/Wr;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/dex/s;I)Lcom/android/tools/r8/internal/Wr;
    .locals 6

    .line 1
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/dex/s;->e()B

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/dex/s;->o()V
    :try_end_0
    .catch Ljava/nio/BufferUnderflowException; {:try_start_0 .. :try_end_0} :catch_0

    .line 7
    sget-object v0, Lcom/android/tools/r8/dex/t;->a:[B

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_3

    aget-byte v4, v0, v3

    add-int/lit8 v5, p2, 0x1

    .line 8
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/dex/s;->a(I)B

    move-result p2

    if-eq p2, v4, :cond_2

    .line 10
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 11
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Dex file has invalid header, expected "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " got "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v1, ". Next bytes are "

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :goto_1
    const/16 p2, 0xa

    if-ge v2, p2, :cond_1

    .line 18
    invoke-virtual {p1}, Lcom/android/tools/r8/dex/s;->l()Z

    move-result p2

    if-eqz p2, :cond_0

    .line 19
    invoke-virtual {p1}, Lcom/android/tools/r8/dex/s;->e()B

    move-result p2

    const/4 v1, 0x2

    invoke-static {p2, v1}, Lcom/android/tools/r8/internal/Sn0;->a(II)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p2, ","

    .line 20
    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 23
    :cond_1
    new-instance p1, Lcom/android/tools/r8/internal/jf;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    iget-object v0, p0, Lcom/android/tools/r8/dex/m;->a:Lcom/android/tools/r8/origin/Origin;

    invoke-direct {p1, p2, v0}, Lcom/android/tools/r8/internal/jf;-><init>(Ljava/lang/String;Lcom/android/tools/r8/origin/Origin;)V

    throw p1

    :cond_2
    add-int/lit8 v3, v3, 0x1

    move p2, v5

    goto :goto_0

    :cond_3
    add-int/lit8 v0, p2, 0x1

    .line 27
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/dex/s;->a(I)B

    move-result v1

    int-to-char v1, v1

    add-int/lit8 v2, p2, 0x2

    .line 28
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/dex/s;->a(I)B

    move-result v0

    int-to-char v0, v0

    add-int/lit8 p2, p2, 0x3

    .line 29
    invoke-virtual {p1, v2}, Lcom/android/tools/r8/dex/s;->a(I)B

    move-result v2

    int-to-char v2, v2

    .line 31
    invoke-static {v1, v0, v2}, Lcom/android/tools/r8/internal/Wr;->a(CCC)Ljava/util/Optional;

    move-result-object v3

    .line 32
    invoke-virtual {v3}, Ljava/util/Optional;->isPresent()Z

    move-result v4

    if-eqz v4, :cond_5

    .line 40
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/dex/s;->a(I)B

    move-result p1

    if-nez p1, :cond_4

    .line 43
    invoke-virtual {v3}, Ljava/util/Optional;->get()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Wr;

    return-object p1

    .line 44
    :cond_4
    new-instance p1, Lcom/android/tools/r8/internal/jf;

    iget-object p2, p0, Lcom/android/tools/r8/dex/m;->a:Lcom/android/tools/r8/origin/Origin;

    const-string v0, "Dex file has invalid header"

    invoke-direct {p1, v0, p2}, Lcom/android/tools/r8/internal/jf;-><init>(Ljava/lang/String;Lcom/android/tools/r8/origin/Origin;)V

    throw p1

    .line 45
    :cond_5
    new-instance p1, Lcom/android/tools/r8/internal/jf;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unsupported DEX file version: "

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    iget-object v0, p0, Lcom/android/tools/r8/dex/m;->a:Lcom/android/tools/r8/origin/Origin;

    invoke-direct {p1, p2, v0}, Lcom/android/tools/r8/internal/jf;-><init>(Ljava/lang/String;Lcom/android/tools/r8/origin/Origin;)V

    throw p1

    .line 46
    :catch_0
    new-instance p1, Lcom/android/tools/r8/internal/jf;

    iget-object p2, p0, Lcom/android/tools/r8/dex/m;->a:Lcom/android/tools/r8/origin/Origin;

    const-string v0, "Dex file is empty"

    invoke-direct {p1, v0, p2}, Lcom/android/tools/r8/internal/jf;-><init>(Ljava/lang/String;Lcom/android/tools/r8/origin/Origin;)V

    throw p1
.end method

.method public final i()Lcom/android/tools/r8/internal/Wr;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/D;->d:Lcom/android/tools/r8/internal/Wr;

    return-object v0
.end method

.method public final j()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/m;->b:Lcom/android/tools/r8/dex/s;

    sget-object v1, Ljava/nio/ByteOrder;->LITTLE_ENDIAN:Ljava/nio/ByteOrder;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/dex/s;->a(Ljava/nio/ByteOrder;)V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/dex/m;->b:Lcom/android/tools/r8/dex/s;

    const/16 v1, 0x28

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/dex/s;->b(I)I

    move-result v0

    const v1, 0x78563412

    if-ne v0, v1, :cond_0

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/dex/m;->b:Lcom/android/tools/r8/dex/s;

    sget-object v1, Ljava/nio/ByteOrder;->BIG_ENDIAN:Ljava/nio/ByteOrder;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/dex/s;->a(Ljava/nio/ByteOrder;)V

    goto :goto_0

    :cond_0
    const v1, 0x12345678

    if-ne v0, v1, :cond_1

    :goto_0
    return-void

    .line 7
    :cond_1
    new-instance v0, Lcom/android/tools/r8/internal/jf;

    const-string v1, "Unable to determine endianess for reading dex file."

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/jf;-><init>(Ljava/lang/String;)V

    throw v0
.end method
