.class public final Lcom/android/tools/r8/dex/v;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic c:Z


# instance fields
.field public a:Ljava/util/HashMap;

.field public final b:Lcom/android/tools/r8/graph/y;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/dex/w;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/dex/v;->c:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/dex/v;->b:Lcom/android/tools/r8/graph/y;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/D5;)I
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/v;->a:Ljava/util/HashMap;

    if-nez v0, :cond_2

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/dex/v;->c:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/dex/v;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    sget-object v1, Lcom/android/tools/r8/internal/A2;->G:Lcom/android/tools/r8/internal/A2;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/utils/w;->b(Lcom/android/tools/r8/internal/A2;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/j1;->V0()Lcom/android/tools/r8/graph/c3;

    move-result-object p1

    if-nez p1, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    return p1

    .line 9
    :cond_2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->V0()Lcom/android/tools/r8/graph/c3;

    move-result-object v0

    .line 10
    iget-object v1, p0, Lcom/android/tools/r8/dex/v;->b:Lcom/android/tools/r8/graph/y;

    .line 11
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    invoke-interface {v0, p1, v1}, Lcom/android/tools/r8/graph/c3;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/b3;

    move-result-object p1

    .line 12
    sget-boolean v0, Lcom/android/tools/r8/dex/v;->c:Z

    if-nez v0, :cond_4

    iget-object v0, p0, Lcom/android/tools/r8/dex/v;->a:Ljava/util/HashMap;

    invoke-virtual {v0, p1}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 13
    :cond_4
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/dex/v;->a:Ljava/util/HashMap;

    invoke-virtual {v0, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1
.end method
