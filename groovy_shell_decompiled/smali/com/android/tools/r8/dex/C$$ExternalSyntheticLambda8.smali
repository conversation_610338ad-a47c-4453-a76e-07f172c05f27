.class public final synthetic Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda8;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/dex/C;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/dex/C;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda8;->f$0:Lcom/android/tools/r8/dex/C;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/dex/C$$ExternalSyntheticLambda8;->f$0:Lcom/android/tools/r8/dex/C;

    invoke-virtual {v0}, Lcom/android/tools/r8/dex/C;->d()Lcom/android/tools/r8/dex/z;

    move-result-object v0

    return-object v0
.end method
