.class public Lcom/android/tools/r8/dex/w;
.super Lcom/android/tools/r8/dex/Y;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic d:Z = true


# instance fields
.field public final b:Lcom/android/tools/r8/graph/y;

.field public final c:Lcom/android/tools/r8/dex/J;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/dex/J;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/dex/Y;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/dex/w;->b:Lcom/android/tools/r8/graph/y;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/naming/b;)Ljava/lang/String;
    .locals 1

    if-eqz p1, :cond_0

    .line 58
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/naming/b;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/naming/V$b;

    move-result-object v0

    .line 59
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    invoke-virtual {p1, p0}, Lcom/android/tools/r8/naming/b;->a(Lcom/android/tools/r8/graph/J2;)Ljava/lang/String;

    move-result-object p0

    goto :goto_0

    .line 61
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-static {p1}, Lcom/android/tools/r8/naming/V$b;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/naming/V$b;

    move-result-object v0

    .line 62
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/J2;->l0()Ljava/lang/String;

    move-result-object p0

    .line 64
    :goto_0
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/wb0;)Ljava/util/ArrayList;
    .locals 6

    .line 4
    invoke-static {}, Lcom/android/tools/r8/internal/g60;->k()Lcom/android/tools/r8/internal/g60;

    move-result-object v0

    .line 5
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 6
    new-instance v2, Lcom/android/tools/r8/dex/v;

    iget-object v3, p0, Lcom/android/tools/r8/dex/w;->b:Lcom/android/tools/r8/graph/y;

    invoke-direct {v2, v3}, Lcom/android/tools/r8/dex/v;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 7
    check-cast p1, Lcom/android/tools/r8/internal/za0;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/za0;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    move-object v3, p1

    check-cast v3, Lcom/android/tools/r8/internal/Ca0;

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Ca0;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    move-object v3, p1

    check-cast v3, Lcom/android/tools/r8/internal/ya0;

    .line 8
    iget-object v4, v3, Lcom/android/tools/r8/internal/ya0;->g:Lcom/android/tools/r8/internal/Ea0;

    iget-object v4, v4, Lcom/android/tools/r8/internal/Ea0;->c:[Ljava/lang/Object;

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Ca0;->b()I

    move-result v3

    aget-object v3, v4, v3

    .line 9
    check-cast v3, Lcom/android/tools/r8/graph/E2;

    .line 10
    sget-object v4, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda16;->INSTANCE:Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda16;

    new-instance v5, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda0;

    invoke-direct {v5, p0, v2, v1, v0}, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/dex/w;Lcom/android/tools/r8/dex/v;Ljava/util/List;Lcom/android/tools/r8/internal/g60;)V

    invoke-virtual {v3, v5, v4}, Lcom/android/tools/r8/graph/E2;->h(Ljava/util/function/Consumer;Ljava/util/function/Predicate;)V

    goto :goto_0

    .line 26
    :cond_0
    new-instance p1, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda1;

    invoke-direct {p1, v0}, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/g60;)V

    invoke-static {p1}, Ljava/util/Comparator;->comparing(Ljava/util/function/Function;)Ljava/util/Comparator;

    move-result-object p1

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 28
    sget-object v3, Lcom/android/tools/r8/internal/A2;->G:Lcom/android/tools/r8/internal/A2;

    invoke-virtual {v0, v3}, Lcom/android/tools/r8/utils/w;->b(Lcom/android/tools/r8/internal/A2;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 29
    new-instance v0, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda2;

    invoke-direct {v0, v2}, Lcom/android/tools/r8/dex/w$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/dex/v;)V

    invoke-static {v0}, Ljava/util/Comparator;->comparingInt(Ljava/util/function/ToIntFunction;)Ljava/util/Comparator;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Comparator;->thenComparing(Ljava/util/Comparator;)Ljava/util/Comparator;

    move-result-object p1

    .line 30
    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->sort(Ljava/util/Comparator;)V

    goto :goto_1

    .line 33
    :cond_1
    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->sort(Ljava/util/Comparator;)V

    :goto_1
    return-object v1
.end method

.method public a()Ljava/util/Collection;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/dex/J;->h:Lcom/android/tools/r8/internal/l00;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/l00;->j()Lcom/android/tools/r8/internal/N10;

    move-result-object v0

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/dex/v;Ljava/util/List;Lcom/android/tools/r8/internal/g60;Lcom/android/tools/r8/graph/D5;)V
    .locals 3

    .line 34
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->V0()Lcom/android/tools/r8/graph/c3;

    move-result-object v0

    .line 35
    sget-boolean v1, Lcom/android/tools/r8/dex/w;->d:Z

    if-nez v1, :cond_1

    if-nez v0, :cond_1

    invoke-virtual {p4}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/j1;->E1()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    if-eqz v0, :cond_7

    .line 37
    iget-object v1, p0, Lcom/android/tools/r8/dex/w;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 38
    sget-object v2, Lcom/android/tools/r8/internal/A2;->G:Lcom/android/tools/r8/internal/A2;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/utils/w;->b(Lcom/android/tools/r8/internal/A2;)Z

    move-result v1

    if-eqz v1, :cond_6

    .line 39
    sget-boolean v1, Lcom/android/tools/r8/dex/v;->c:Z

    if-nez v1, :cond_3

    .line 40
    iget-object v1, p1, Lcom/android/tools/r8/dex/v;->b:Lcom/android/tools/r8/graph/y;

    .line 41
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 42
    invoke-virtual {v1, v2}, Lcom/android/tools/r8/utils/w;->b(Lcom/android/tools/r8/internal/A2;)Z

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_1

    .line 43
    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 44
    :cond_3
    :goto_1
    iget-object v1, p1, Lcom/android/tools/r8/dex/v;->a:Ljava/util/HashMap;

    if-nez v1, :cond_4

    .line 45
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p1, Lcom/android/tools/r8/dex/v;->a:Ljava/util/HashMap;

    .line 47
    :cond_4
    iget-object v1, p1, Lcom/android/tools/r8/dex/v;->b:Lcom/android/tools/r8/graph/y;

    .line 48
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    invoke-interface {v0, p4, v1}, Lcom/android/tools/r8/graph/c3;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/b3;

    move-result-object v0

    .line 49
    iget-object v1, p1, Lcom/android/tools/r8/dex/v;->a:Ljava/util/HashMap;

    invoke-virtual {v1, v0}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x1

    if-nez v1, :cond_5

    .line 50
    iget-object p1, p1, Lcom/android/tools/r8/dex/v;->a:Ljava/util/HashMap;

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {p1, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    .line 52
    :cond_5
    iget-object p1, p1, Lcom/android/tools/r8/dex/v;->a:Ljava/util/HashMap;

    invoke-virtual {p1, v0}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Integer;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    add-int/2addr v1, v2

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {p1, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 53
    :cond_6
    :goto_2
    invoke-interface {p2, p4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 54
    iget-object p1, p0, Lcom/android/tools/r8/dex/w;->b:Lcom/android/tools/r8/graph/y;

    .line 55
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->f()Lcom/android/tools/r8/graph/x0;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/x0;->g()Lcom/android/tools/r8/naming/b;

    move-result-object p1

    invoke-static {p4, p1}, Lcom/android/tools/r8/dex/w;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/naming/b;)Ljava/lang/String;

    move-result-object p1

    .line 56
    invoke-virtual {p3, p4}, Lcom/android/tools/r8/internal/g60;->b(Lcom/android/tools/r8/graph/G0;)Lcom/android/tools/r8/internal/su;

    move-result-object p2

    .line 57
    iget-object p3, p3, Lcom/android/tools/r8/internal/bn;->b:Ljava/util/Map;

    invoke-interface {p3, p2, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_7
    return-void
.end method

.method public b()Ljava/util/Collection;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/dex/J;->e:Lcom/android/tools/r8/internal/l00;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/l00;->j()Lcom/android/tools/r8/internal/N10;

    move-result-object v0

    return-object v0
.end method

.method public c()Ljava/util/Collection;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/dex/J;->f:Lcom/android/tools/r8/internal/l00;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/l00;->j()Lcom/android/tools/r8/internal/N10;

    move-result-object v0

    return-object v0
.end method

.method public d()Ljava/util/Collection;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/dex/J;->g:Lcom/android/tools/r8/internal/l00;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/l00;->j()Lcom/android/tools/r8/internal/N10;

    move-result-object v0

    return-object v0
.end method

.method public e()Ljava/util/Collection;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/dex/J;->i:Lcom/android/tools/r8/internal/Ea0;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Ea0;->j()Lcom/android/tools/r8/internal/wb0;

    move-result-object v0

    return-object v0
.end method

.method public f()Ljava/util/Collection;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/dex/J;->i:Lcom/android/tools/r8/internal/Ea0;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Ea0;->j()Lcom/android/tools/r8/internal/wb0;

    move-result-object v0

    .line 4
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/dex/w;->a(Lcom/android/tools/r8/internal/wb0;)Ljava/util/ArrayList;

    move-result-object v0

    return-object v0
.end method

.method public g()Ljava/util/Collection;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/dex/J;->j:Lcom/android/tools/r8/internal/l00;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/l00;->j()Lcom/android/tools/r8/internal/N10;

    move-result-object v0

    return-object v0
.end method

.method public h()Ljava/util/Collection;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/dex/J;->d:Lcom/android/tools/r8/internal/Ea0;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Ea0;->j()Lcom/android/tools/r8/internal/wb0;

    move-result-object v0

    return-object v0
.end method

.method public i()Ljava/util/Collection;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/dex/w;->c:Lcom/android/tools/r8/dex/J;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/dex/J;->c:Lcom/android/tools/r8/internal/l00;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/l00;->j()Lcom/android/tools/r8/internal/N10;

    move-result-object v0

    return-object v0
.end method
