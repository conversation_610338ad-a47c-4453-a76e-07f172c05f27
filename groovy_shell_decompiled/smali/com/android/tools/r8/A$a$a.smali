.class public Lcom/android/tools/r8/A$a$a;
.super Lcom/android/tools/r8/BaseCommand$Builder;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/A$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/android/tools/r8/BaseCommand$Builder<",
        "Lcom/android/tools/r8/A$a;",
        "Lcom/android/tools/r8/A$a$a;",
        ">;"
    }
.end annotation


# instance fields
.field public f:Ljava/nio/file/Path;

.field public g:Ljava/nio/file/Path;

.field public h:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/utils/j;->b()Lcom/android/tools/r8/utils/j$a;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/android/tools/r8/BaseCommand$Builder;-><init>(Lcom/android/tools/r8/utils/j$a;)V

    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lcom/android/tools/r8/A$a$a;->f:Ljava/nio/file/Path;

    .line 3
    iput-object v0, p0, Lcom/android/tools/r8/A$a$a;->g:Ljava/nio/file/Path;

    const/4 v0, 0x0

    .line 4
    iput-boolean v0, p0, Lcom/android/tools/r8/A$a$a;->h:Z

    return-void
.end method


# virtual methods
.method public c(Ljava/nio/file/Path;)Lcom/android/tools/r8/A$a$a;
    .locals 0

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/A$a$a;->f:Ljava/nio/file/Path;

    return-object p0
.end method

.method public c(Z)Lcom/android/tools/r8/A$a$a;
    .locals 0

    .line 3
    iput-boolean p1, p0, Lcom/android/tools/r8/A$a$a;->h:Z

    return-object p0
.end method

.method public final c()Lcom/android/tools/r8/BaseCommand$Builder;
    .locals 0

    return-object p0
.end method

.method public d(Ljava/nio/file/Path;)Lcom/android/tools/r8/A$a$a;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/A$a$a;->g:Ljava/nio/file/Path;

    return-object p0
.end method

.method public final makeCommand()Lcom/android/tools/r8/BaseCommand;
    .locals 5

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/BaseCommand$Builder;->isPrintHelp()Z

    move-result v0

    if-nez v0, :cond_2

    invoke-virtual {p0}, Lcom/android/tools/r8/BaseCommand$Builder;->isPrintVersion()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    .line 4
    :cond_0
    new-instance v0, Lcom/android/tools/r8/A$a;

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/BaseCommand$Builder;->a()Lcom/android/tools/r8/utils/j$a;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/utils/j$a;->a()Lcom/android/tools/r8/utils/j;

    move-result-object v1

    .line 6
    iget-object v2, p0, Lcom/android/tools/r8/A$a$a;->f:Ljava/nio/file/Path;

    .line 7
    iget-object v3, p0, Lcom/android/tools/r8/A$a$a;->g:Ljava/nio/file/Path;

    if-nez v3, :cond_1

    const/4 v3, 0x0

    goto :goto_0

    :cond_1
    invoke-static {v3}, Lcom/android/tools/r8/t0;->a(Ljava/nio/file/Path;)Lcom/android/tools/r8/t0$a;

    move-result-object v3

    :goto_0
    iget-boolean v4, p0, Lcom/android/tools/r8/A$a$a;->h:Z

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/android/tools/r8/A$a;-><init>(Lcom/android/tools/r8/utils/j;Ljava/nio/file/Path;Lcom/android/tools/r8/t0$a;Z)V

    goto :goto_2

    .line 8
    :cond_2
    :goto_1
    new-instance v0, Lcom/android/tools/r8/A$a;

    invoke-virtual {p0}, Lcom/android/tools/r8/BaseCommand$Builder;->isPrintHelp()Z

    move-result v1

    invoke-virtual {p0}, Lcom/android/tools/r8/BaseCommand$Builder;->isPrintVersion()Z

    move-result v2

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/A$a;-><init>(ZZ)V

    :goto_2
    return-object v0
.end method
