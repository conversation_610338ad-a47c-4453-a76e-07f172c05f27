.class public final synthetic Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiConsumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/y;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/pI;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/Q10;

.field public final synthetic f$3:Lcom/android/tools/r8/graph/D5;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/pI;Lcom/android/tools/r8/internal/Q10;Lcom/android/tools/r8/graph/D5;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/graph/y;

    iput-object p2, p0, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/pI;

    iput-object p3, p0, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/internal/Q10;

    iput-object p4, p0, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;->f$3:Lcom/android/tools/r8/graph/D5;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 6

    iget-object v0, p0, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/graph/y;

    iget-object v1, p0, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/pI;

    iget-object v2, p0, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/internal/Q10;

    iget-object v3, p0, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;->f$3:Lcom/android/tools/r8/graph/D5;

    move-object v4, p1

    check-cast v4, Lcom/android/tools/r8/graph/F0;

    move-object v5, p2

    check-cast v5, Lcom/android/tools/r8/internal/MC;

    invoke-static/range {v0 .. v5}, Lcom/android/tools/r8/internal/S10;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/pI;Lcom/android/tools/r8/internal/Q10;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/F0;Lcom/android/tools/r8/internal/MC;)V

    return-void
.end method
