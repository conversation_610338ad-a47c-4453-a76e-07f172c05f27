.class public abstract Lcom/android/tools/r8/internal/rF;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public b:Lcom/android/tools/r8/internal/fF;

.field public c:Lcom/android/tools/r8/internal/fF;

.field public d:Lcom/android/tools/r8/internal/fF;

.field public e:I

.field public final synthetic f:Lcom/android/tools/r8/internal/tF;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/tF;)V
    .locals 1

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/rF;->f:Lcom/android/tools/r8/internal/tF;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 2
    iput v0, p0, Lcom/android/tools/r8/internal/rF;->e:I

    .line 4
    iget-object p1, p1, Lcom/android/tools/r8/internal/tF;->d:Lcom/android/tools/r8/internal/fF;

    iput-object p1, p0, Lcom/android/tools/r8/internal/rF;->c:Lcom/android/tools/r8/internal/fF;

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/fF;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rF;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->c:Lcom/android/tools/r8/internal/fF;

    iput-object v0, p0, Lcom/android/tools/r8/internal/rF;->b:Lcom/android/tools/r8/internal/fF;

    iput-object v0, p0, Lcom/android/tools/r8/internal/rF;->d:Lcom/android/tools/r8/internal/fF;

    .line 3
    iget v0, p0, Lcom/android/tools/r8/internal/rF;->e:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lcom/android/tools/r8/internal/rF;->e:I

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rF;->d()V

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->d:Lcom/android/tools/r8/internal/fF;

    return-object v0

    .line 6
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public final b()Lcom/android/tools/r8/internal/fF;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rF;->hasPrevious()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->b:Lcom/android/tools/r8/internal/fF;

    iput-object v0, p0, Lcom/android/tools/r8/internal/rF;->c:Lcom/android/tools/r8/internal/fF;

    iput-object v0, p0, Lcom/android/tools/r8/internal/rF;->d:Lcom/android/tools/r8/internal/fF;

    .line 3
    iget v0, p0, Lcom/android/tools/r8/internal/rF;->e:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lcom/android/tools/r8/internal/rF;->e:I

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rF;->e()V

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->d:Lcom/android/tools/r8/internal/fF;

    return-object v0

    .line 6
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public d()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->c:Lcom/android/tools/r8/internal/fF;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/fF;->b()Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/rF;->c:Lcom/android/tools/r8/internal/fF;

    return-void
.end method

.method public e()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->b:Lcom/android/tools/r8/internal/fF;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/fF;->d()Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/rF;->b:Lcom/android/tools/r8/internal/fF;

    return-void
.end method

.method public final hasNext()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->c:Lcom/android/tools/r8/internal/fF;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final hasPrevious()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->b:Lcom/android/tools/r8/internal/fF;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public next()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rF;->a()Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    return-object v0
.end method

.method public final nextIndex()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/rF;->e:I

    return v0
.end method

.method public previous()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rF;->b()Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    return-object v0
.end method

.method public final previousIndex()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/rF;->e:I

    add-int/lit8 v0, v0, -0x1

    return v0
.end method

.method public final remove()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->d:Lcom/android/tools/r8/internal/fF;

    if-eqz v0, :cond_1

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/rF;->b:Lcom/android/tools/r8/internal/fF;

    if-ne v0, v1, :cond_0

    iget v1, p0, Lcom/android/tools/r8/internal/rF;->e:I

    add-int/lit8 v1, v1, -0x1

    iput v1, p0, Lcom/android/tools/r8/internal/rF;->e:I

    .line 5
    :cond_0
    iput-object v0, p0, Lcom/android/tools/r8/internal/rF;->b:Lcom/android/tools/r8/internal/fF;

    iput-object v0, p0, Lcom/android/tools/r8/internal/rF;->c:Lcom/android/tools/r8/internal/fF;

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rF;->e()V

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rF;->d()V

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/internal/rF;->f:Lcom/android/tools/r8/internal/tF;

    iget-object v1, p0, Lcom/android/tools/r8/internal/rF;->d:Lcom/android/tools/r8/internal/fF;

    iget v1, v1, Lcom/android/tools/r8/internal/Q;->b:I

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/tF;->remove(I)Ljava/lang/Object;

    const/4 v0, 0x0

    .line 9
    iput-object v0, p0, Lcom/android/tools/r8/internal/rF;->d:Lcom/android/tools/r8/internal/fF;

    return-void

    .line 10
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method
