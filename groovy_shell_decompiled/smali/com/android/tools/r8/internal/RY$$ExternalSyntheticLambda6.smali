.class public final synthetic Lcom/android/tools/r8/internal/RY$$ExternalSyntheticLambda6;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiConsumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Fy;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Fy;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/RY$$ExternalSyntheticLambda6;->f$0:Lcom/android/tools/r8/internal/Fy;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/RY$$ExternalSyntheticLambda6;->f$0:Lcom/android/tools/r8/internal/Fy;

    check-cast p1, Lcom/android/tools/r8/internal/Rl0;

    check-cast p2, Lcom/android/tools/r8/internal/Ml0$a;

    invoke-static {v0, p1, p2}, Lcom/android/tools/r8/internal/RY;->a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Rl0;Lcom/android/tools/r8/internal/Ml0$a;)V

    return-void
.end method
