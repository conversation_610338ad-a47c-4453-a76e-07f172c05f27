.class public final Lcom/android/tools/r8/internal/w3;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Lcom/android/tools/r8/internal/x50;

.field public final c:Lcom/android/tools/r8/internal/Pv;

.field public final d:Lcom/android/tools/r8/internal/OV;

.field public final e:Lcom/android/tools/r8/internal/WB;

.field public final f:Lcom/android/tools/r8/graph/Y3;

.field public final g:Ljava/util/ArrayList;

.field public final h:Ljava/util/function/BiConsumer;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/x50;Lcom/android/tools/r8/graph/Y3;Lcom/android/tools/r8/internal/Pv;Lcom/android/tools/r8/internal/OV;Lcom/android/tools/r8/internal/WB;Ljava/util/ArrayList;Ljava/util/function/BiConsumer;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/w3;->a:Lcom/android/tools/r8/graph/y;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/w3;->b:Lcom/android/tools/r8/internal/x50;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/internal/w3;->f:Lcom/android/tools/r8/graph/Y3;

    .line 5
    iput-object p4, p0, Lcom/android/tools/r8/internal/w3;->c:Lcom/android/tools/r8/internal/Pv;

    .line 6
    iput-object p5, p0, Lcom/android/tools/r8/internal/w3;->d:Lcom/android/tools/r8/internal/OV;

    .line 7
    iput-object p6, p0, Lcom/android/tools/r8/internal/w3;->e:Lcom/android/tools/r8/internal/WB;

    .line 8
    iput-object p7, p0, Lcom/android/tools/r8/internal/w3;->g:Ljava/util/ArrayList;

    .line 9
    iput-object p8, p0, Lcom/android/tools/r8/internal/w3;->h:Ljava/util/function/BiConsumer;

    return-void
.end method


# virtual methods
.method public final a(Ljava/util/Set;)V
    .locals 5

    .line 41
    new-instance v0, Lcom/android/tools/r8/internal/iH;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w3;->a:Lcom/android/tools/r8/graph/y;

    iget-object v2, p0, Lcom/android/tools/r8/internal/w3;->f:Lcom/android/tools/r8/graph/Y3;

    iget-object v3, p0, Lcom/android/tools/r8/internal/w3;->d:Lcom/android/tools/r8/internal/OV;

    new-instance v4, Lcom/android/tools/r8/internal/w3$$ExternalSyntheticLambda1;

    invoke-direct {v4, p0, p1}, Lcom/android/tools/r8/internal/w3$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/w3;Ljava/util/Set;)V

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/android/tools/r8/internal/iH;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/Y3;Lcom/android/tools/r8/internal/OV;Ljava/util/function/Consumer;)V

    .line 47
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/iH;->a(Ljava/util/Collection;)V

    .line 60
    new-instance v0, Lcom/android/tools/r8/internal/nu0;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w3;->a:Lcom/android/tools/r8/graph/y;

    iget-object v2, p0, Lcom/android/tools/r8/internal/w3;->f:Lcom/android/tools/r8/graph/Y3;

    iget-object v3, p0, Lcom/android/tools/r8/internal/w3;->d:Lcom/android/tools/r8/internal/OV;

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/nu0;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/Y3;Lcom/android/tools/r8/internal/OV;)V

    .line 61
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/nu0;->a(Ljava/util/Collection;)V

    return-void
.end method

.method public final synthetic a(Ljava/util/Set;Lcom/android/tools/r8/graph/C2;)V
    .locals 1

    .line 62
    iget-object v0, p0, Lcom/android/tools/r8/internal/w3;->h:Ljava/util/function/BiConsumer;

    invoke-interface {v0, p1, p2}, Ljava/util/function/BiConsumer;->accept(Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method

.method public final a(Ljava/util/Set;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Gp0;)V
    .locals 8

    const-string v0, "Propagate argument information for virtual methods"

    .line 1
    invoke-virtual {p3, v0}, Lcom/android/tools/r8/internal/Gp0;->a(Ljava/lang/String;)V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w3;->g:Ljava/util/ArrayList;

    new-instance v1, Lcom/android/tools/r8/internal/w3$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/w3$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/w3;)V

    iget-object v2, p0, Lcom/android/tools/r8/internal/w3;->a:Lcom/android/tools/r8/graph/y;

    .line 5
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/utils/w;->O()Lcom/android/tools/r8/threading/ThreadingModule;

    move-result-object v2

    .line 6
    invoke-static {v0, v1, v2, p2}, Lcom/android/tools/r8/internal/ep0;->a(Ljava/util/Collection;Ljava/util/function/Consumer;Lcom/android/tools/r8/threading/ThreadingModule;Ljava/util/concurrent/ExecutorService;)V

    .line 11
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/Gp0;->b()V

    const-string v0, "Solve flow constraints"

    .line 14
    invoke-virtual {p3, v0}, Lcom/android/tools/r8/internal/Gp0;->a(Ljava/lang/String;)V

    .line 15
    new-instance v0, Lcom/android/tools/r8/internal/XB;

    iget-object v2, p0, Lcom/android/tools/r8/internal/w3;->a:Lcom/android/tools/r8/graph/y;

    iget-object v4, p0, Lcom/android/tools/r8/internal/w3;->b:Lcom/android/tools/r8/internal/x50;

    iget-object v5, p0, Lcom/android/tools/r8/internal/w3;->c:Lcom/android/tools/r8/internal/Pv;

    iget-object v6, p0, Lcom/android/tools/r8/internal/w3;->d:Lcom/android/tools/r8/internal/OV;

    iget-object v7, p0, Lcom/android/tools/r8/internal/w3;->e:Lcom/android/tools/r8/internal/WB;

    move-object v1, v0

    move-object v3, p1

    invoke-direct/range {v1 .. v7}, Lcom/android/tools/r8/internal/XB;-><init>(Lcom/android/tools/r8/graph/y;Ljava/util/Set;Lcom/android/tools/r8/internal/fA;Lcom/android/tools/r8/internal/Pv;Lcom/android/tools/r8/internal/OV;Lcom/android/tools/r8/internal/WB;)V

    .line 16
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/XB;->a()Ljava/util/List;

    move-result-object p1

    .line 17
    sget-boolean v1, Lcom/android/tools/r8/internal/XB;->g:Z

    if-nez v1, :cond_0

    .line 18
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/Iw;

    .line 19
    invoke-static {v2}, Lcom/android/tools/r8/internal/YB;->a(Lcom/android/tools/r8/internal/Iw;)V

    goto :goto_0

    .line 20
    :cond_0
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/XB;->a(Ljava/util/List;Ljava/util/concurrent/ExecutorService;)V

    .line 28
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/XB;->b(Ljava/util/List;)V

    .line 29
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/XB;->a(Ljava/util/List;)Lcom/android/tools/r8/internal/Li;

    move-result-object v1

    .line 30
    invoke-virtual {v1, p2}, Lcom/android/tools/r8/internal/Li;->a(Ljava/util/concurrent/ExecutorService;)Ljava/util/Map;

    move-result-object v1

    .line 31
    invoke-virtual {v0, v1, p2}, Lcom/android/tools/r8/internal/XB;->a(Ljava/util/Map;Ljava/util/concurrent/ExecutorService;)V

    .line 36
    invoke-virtual {v0, p2}, Lcom/android/tools/r8/internal/XB;->a(Ljava/util/concurrent/ExecutorService;)V

    .line 39
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/XB;->b(Ljava/util/List;)V

    .line 40
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/Gp0;->b()V

    return-void
.end method
