.class public final Lcom/android/tools/r8/internal/RC;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/r;


# static fields
.field public static final synthetic c:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/internal/E5;

.field public final b:Lcom/android/tools/r8/graph/l1;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/E5;Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/RC;->a:Lcom/android/tools/r8/internal/E5;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/UB;Lcom/android/tools/r8/internal/WB;)I
    .locals 2

    .line 35
    invoke-interface {p1}, Lcom/android/tools/r8/internal/UB;->v()Lcom/android/tools/r8/internal/RC;

    move-result-object p1

    .line 36
    iget-object v0, p0, Lcom/android/tools/r8/internal/RC;->a:Lcom/android/tools/r8/internal/E5;

    iget-object v1, p1, Lcom/android/tools/r8/internal/RC;->a:Lcom/android/tools/r8/internal/E5;

    invoke-interface {v0, v1, p2}, Lcom/android/tools/r8/internal/UB;->b(Lcom/android/tools/r8/internal/UB;Lcom/android/tools/r8/internal/WB;)I

    move-result p2

    if-nez p2, :cond_0

    .line 38
    iget-object p2, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    iget-object p1, p1, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-interface {p2, p1}, Lcom/android/tools/r8/internal/ho0;->a(Lcom/android/tools/r8/internal/ho0;)I

    move-result p2

    :cond_0
    return p2
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Qw;Lcom/android/tools/r8/internal/bg;)Lcom/android/tools/r8/internal/Gt0;
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/android/tools/r8/internal/RC;->a:Lcom/android/tools/r8/internal/E5;

    new-instance p3, Lcom/android/tools/r8/internal/RC$$ExternalSyntheticLambda0;

    invoke-direct {p3, p0}, Lcom/android/tools/r8/internal/RC$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/RC;)V

    invoke-interface {p2, p1, p3}, Lcom/android/tools/r8/internal/Qw;->a(Lcom/android/tools/r8/internal/E5;Ljava/util/function/Supplier;)Lcom/android/tools/r8/internal/Gt0;

    move-result-object p1

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Gt0;->h()Z

    move-result p3

    if-eqz p3, :cond_0

    .line 3
    iget-object p1, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-static {p1}, Lcom/android/tools/r8/internal/Gt0;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/internal/p7;

    move-result-object p1

    return-object p1

    .line 4
    :cond_0
    instance-of p3, p1, Lcom/android/tools/r8/internal/Of;

    if-nez p3, :cond_1

    .line 5
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/RC;->a(Lcom/android/tools/r8/internal/Qw;)Lcom/android/tools/r8/internal/Gt0;

    move-result-object p1

    return-object p1

    .line 7
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Gt0;->b()Lcom/android/tools/r8/internal/Of;

    move-result-object p3

    .line 8
    iget-object p3, p3, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    .line 9
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/Gs;->d()Lcom/android/tools/r8/internal/qZ;

    move-result-object p3

    .line 10
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/qZ;->e()Z

    move-result p3

    if-eqz p3, :cond_2

    .line 11
    iget-object p1, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-static {p1}, Lcom/android/tools/r8/internal/Gt0;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/internal/p7;

    move-result-object p1

    return-object p1

    :cond_2
    const/4 p3, 0x0

    .line 13
    invoke-virtual {p1, p3}, Lcom/android/tools/r8/internal/Gt0;->a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/E1;

    move-result-object p1

    .line 14
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/E1;->C()Z

    move-result p3

    if-nez p3, :cond_3

    .line 15
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/RC;->a(Lcom/android/tools/r8/internal/Qw;)Lcom/android/tools/r8/internal/Gt0;

    move-result-object p1

    return-object p1

    .line 17
    :cond_3
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/E1;->z()Lcom/android/tools/r8/internal/R10;

    move-result-object p1

    iget-object p3, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-virtual {p1, p3}, Lcom/android/tools/r8/internal/R10;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/internal/E1;

    move-result-object p1

    .line 18
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result p3

    if-eqz p3, :cond_4

    .line 19
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/RC;->a(Lcom/android/tools/r8/internal/Qw;)Lcom/android/tools/r8/internal/Gt0;

    move-result-object p1

    return-object p1

    .line 21
    :cond_4
    iget-object p2, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p2

    .line 22
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/J2;->H0()Z

    move-result p3

    if-eqz p3, :cond_5

    .line 23
    sget-object p1, Lcom/android/tools/r8/internal/Fs0;->b:Lcom/android/tools/r8/internal/Fs0;

    goto :goto_1

    .line 24
    :cond_5
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/J2;->L0()Z

    move-result p3

    if-eqz p3, :cond_6

    .line 25
    invoke-static {}, Lcom/android/tools/r8/internal/Gs;->m()Lcom/android/tools/r8/internal/Is;

    move-result-object p2

    .line 26
    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object p3

    invoke-static {p1, p2, p3}, Lcom/android/tools/r8/internal/Of;->a(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/Gs;Ljava/util/Set;)Lcom/android/tools/r8/internal/SY;

    move-result-object p1

    goto :goto_1

    .line 27
    :cond_6
    sget-boolean p3, Lcom/android/tools/r8/internal/bg;->c:Z

    if-nez p3, :cond_8

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result p2

    if-eqz p2, :cond_7

    goto :goto_0

    :cond_7
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 28
    :cond_8
    :goto_0
    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object p2

    .line 29
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result p3

    if-eqz p3, :cond_9

    .line 30
    sget-object p1, Lcom/android/tools/r8/internal/Fs0;->b:Lcom/android/tools/r8/internal/Fs0;

    goto :goto_1

    .line 31
    :cond_9
    new-instance p3, Lcom/android/tools/r8/internal/Xf;

    invoke-direct {p3, p1, p2}, Lcom/android/tools/r8/internal/Xf;-><init>(Lcom/android/tools/r8/internal/E1;Ljava/util/Set;)V

    move-object p1, p3

    :goto_1
    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/Qw;)Lcom/android/tools/r8/internal/Gt0;
    .locals 2

    .line 33
    new-instance v0, Lcom/android/tools/r8/internal/Sv;

    iget-object v1, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/Sv;-><init>(Lcom/android/tools/r8/graph/l1;)V

    const/4 v1, 0x0

    invoke-interface {p1, v0, v1}, Lcom/android/tools/r8/internal/Qw;->a(Lcom/android/tools/r8/internal/E5;Ljava/util/function/Supplier;)Lcom/android/tools/r8/internal/Gt0;

    move-result-object p1

    .line 34
    sget-boolean v0, Lcom/android/tools/r8/internal/RC;->c:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Gt0;->i()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Gt0;->c()Lcom/android/tools/r8/internal/bg;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/bg;->r()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/E5;)Z
    .locals 1

    .line 32
    sget-boolean v0, Lcom/android/tools/r8/internal/RC;->c:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/RC;->a:Lcom/android/tools/r8/internal/E5;

    invoke-virtual {p1, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-interface {p1, v0}, Lcom/android/tools/r8/internal/UB;->a(Lcom/android/tools/r8/graph/l1;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    return p1
.end method

.method public final getKind()I
    .locals 1

    const/4 v0, 0x4

    return v0
.end method

.method public final k()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final synthetic n()Lcom/android/tools/r8/internal/Gt0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-static {v0}, Lcom/android/tools/r8/internal/Gt0;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/internal/p7;

    move-result-object v0

    return-object v0
.end method

.method public final p()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/RC;->a:Lcom/android/tools/r8/internal/E5;

    iget-object v1, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/l1;->l0()Ljava/lang/String;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Read("

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ", "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final u()Ljava/lang/Iterable;
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/RC;->a:Lcom/android/tools/r8/internal/E5;

    new-instance v1, Lcom/android/tools/r8/internal/Sv;

    iget-object v2, p0, Lcom/android/tools/r8/internal/RC;->b:Lcom/android/tools/r8/graph/l1;

    invoke-direct {v1, v2}, Lcom/android/tools/r8/internal/Sv;-><init>(Lcom/android/tools/r8/graph/l1;)V

    const/4 v2, 0x2

    new-array v3, v2, [Lcom/android/tools/r8/internal/E5;

    const/4 v4, 0x0

    aput-object v0, v3, v4

    const/4 v0, 0x1

    aput-object v1, v3, v0

    .line 2
    invoke-static {v2}, Lcom/android/tools/r8/internal/VR;->a(I)I

    move-result v0

    .line 3
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1, v0}, Ljava/util/ArrayList;-><init>(I)V

    .line 4
    invoke-static {v1, v3}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    return-object v1
.end method

.method public final v()Lcom/android/tools/r8/internal/RC;
    .locals 0

    return-object p0
.end method
