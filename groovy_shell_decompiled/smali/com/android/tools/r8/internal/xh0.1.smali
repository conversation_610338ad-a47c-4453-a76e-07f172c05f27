.class public final Lcom/android/tools/r8/internal/xh0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/retrace/RetraceStackTraceContext;


# instance fields
.field public final a:Lcom/android/tools/r8/references/ClassReference;

.field public final b:Ljava/util/OptionalInt;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/references/ClassReference;Ljava/util/OptionalInt;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/xh0;->a:Lcom/android/tools/r8/references/ClassReference;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/xh0;->b:Ljava/util/OptionalInt;

    return-void
.end method
