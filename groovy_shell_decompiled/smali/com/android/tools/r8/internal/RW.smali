.class public abstract Lcom/android/tools/r8/internal/RW;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic b:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/internal/Ml0;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/Ml0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/RW;->a:Lcom/android/tools/r8/internal/Ml0;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/internal/Ml0;)Lcom/android/tools/r8/internal/RW;
    .locals 2

    .line 18
    invoke-virtual {p0}, Lcom/android/tools/r8/utils/w;->M()Lcom/android/tools/r8/internal/Ll0;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ll0;->a()Ljava/lang/String;

    move-result-object p0

    if-nez p0, :cond_0

    .line 19
    new-instance p0, Lcom/android/tools/r8/internal/PW;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/PW;-><init>(Lcom/android/tools/r8/internal/Ml0;)V

    return-object p0

    .line 20
    :cond_0
    invoke-virtual {p0}, Ljava/lang/String;->hashCode()I

    const/4 v0, -0x1

    invoke-virtual {p0}, Ljava/lang/String;->hashCode()I

    move-result v1

    sparse-switch v1, :sswitch_data_0

    goto :goto_0

    :sswitch_0
    const-string v1, "packageByName"

    invoke-virtual {p0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    goto :goto_0

    :cond_1
    const/4 v0, 0x4

    goto :goto_0

    :sswitch_1
    const-string v1, "classByNumberOfStartupMethodsMinusNumberOfNonStartupMethods"

    invoke-virtual {p0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x3

    goto :goto_0

    :sswitch_2
    const-string v1, "classByName"

    invoke-virtual {p0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    goto :goto_0

    :cond_3
    const/4 v0, 0x2

    goto :goto_0

    :sswitch_3
    const-string v1, "packageByNumberOfStartupMethods"

    invoke-virtual {p0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    goto :goto_0

    :cond_4
    const/4 v0, 0x1

    goto :goto_0

    :sswitch_4
    const-string v1, "classByNumberOfStartupMethods"

    invoke-virtual {p0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    goto :goto_0

    :cond_5
    const/4 v0, 0x0

    :goto_0
    packed-switch v0, :pswitch_data_0

    .line 49
    new-instance p1, Ljava/lang/IllegalArgumentException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Unexpected multi startup dex distribution strategy: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {p1, p0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 50
    :pswitch_0
    new-instance p0, Lcom/android/tools/r8/internal/QW;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/QW;-><init>(Lcom/android/tools/r8/internal/Ml0;)V

    return-object p0

    .line 51
    :pswitch_1
    new-instance p0, Lcom/android/tools/r8/internal/NW;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/NW;-><init>(Lcom/android/tools/r8/internal/Ml0;)V

    return-object p0

    .line 52
    :pswitch_2
    new-instance p0, Lcom/android/tools/r8/internal/PW;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/PW;-><init>(Lcom/android/tools/r8/internal/Ml0;)V

    return-object p0

    .line 53
    :pswitch_3
    new-instance p0, Lcom/android/tools/r8/internal/fs0;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/fs0;-><init>()V

    throw p0

    .line 54
    :pswitch_4
    new-instance p0, Lcom/android/tools/r8/internal/MW;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/MW;-><init>(Lcom/android/tools/r8/internal/Ml0;)V

    return-object p0

    :sswitch_data_0
    .sparse-switch
        -0x6ff03f9c -> :sswitch_4
        -0x4a50bece -> :sswitch_3
        -0x49a36ce6 -> :sswitch_2
        0x2cf21a76 -> :sswitch_1
        0x42904b68 -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static a(Ljava/util/ArrayList;Lcom/android/tools/r8/dex/t0;Lcom/android/tools/r8/dex/r0;)V
    .locals 8

    .line 1
    invoke-virtual {p0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/E2;

    .line 2
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/dex/t0;->a(Lcom/android/tools/r8/graph/E2;)V

    const/high16 v1, 0x10000

    .line 3
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/dex/t0;->a(I)Z

    move-result v2

    if-nez v2, :cond_0

    .line 4
    iget-object v0, p1, Lcom/android/tools/r8/dex/t0;->c:Lcom/android/tools/r8/dex/n0;

    invoke-virtual {v0}, Lcom/android/tools/r8/dex/n0;->a()V

    goto :goto_0

    .line 5
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/dex/t0;->a()V

    .line 6
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 7
    new-instance p1, Lcom/android/tools/r8/dex/t0;

    iget-object v2, p2, Lcom/android/tools/r8/dex/r0;->d:Lcom/android/tools/r8/internal/yG;

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/yG;->b()I

    move-result v3

    iget-object v4, p2, Lcom/android/tools/r8/dex/r0;->c:Lcom/android/tools/r8/graph/y;

    iget-object v6, p2, Lcom/android/tools/r8/dex/r0;->g:Lcom/android/tools/r8/FeatureSplit;

    .line 8
    invoke-static {}, Lcom/android/tools/r8/internal/Ml0;->b()Lcom/android/tools/r8/internal/Ml0;

    move-result-object v7

    const/4 v5, 0x0

    move-object v2, p1

    invoke-direct/range {v2 .. v7}, Lcom/android/tools/r8/dex/t0;-><init>(ILcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/FeatureSplit;Lcom/android/tools/r8/internal/Ml0;)V

    .line 9
    iget-object v2, p2, Lcom/android/tools/r8/dex/r0;->a:Ljava/util/ArrayList;

    invoke-virtual {v2, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 10
    iget-object v2, p2, Lcom/android/tools/r8/dex/r0;->b:Ljava/util/ArrayList;

    invoke-virtual {v2, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 11
    iget-object v2, p2, Lcom/android/tools/r8/dex/r0;->b:Ljava/util/ArrayList;

    invoke-static {v2}, Lcom/android/tools/r8/internal/hJ;->a(Ljava/lang/Iterable;)Lcom/android/tools/r8/internal/XI;

    move-result-object v2

    iput-object v2, p2, Lcom/android/tools/r8/dex/r0;->e:Lcom/android/tools/r8/internal/XI;

    .line 12
    invoke-virtual {p2}, Lcom/android/tools/r8/dex/r0;->c()V

    .line 13
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/dex/t0;->a(Lcom/android/tools/r8/graph/E2;)V

    .line 14
    sget-boolean v0, Lcom/android/tools/r8/internal/RW;->b:Z

    if-nez v0, :cond_2

    .line 15
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/dex/t0;->a(I)Z

    move-result v0

    if-nez v0, :cond_1

    goto :goto_1

    .line 16
    :cond_1
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 17
    :cond_2
    :goto_1
    iget-object v0, p1, Lcom/android/tools/r8/dex/t0;->c:Lcom/android/tools/r8/dex/n0;

    invoke-virtual {v0}, Lcom/android/tools/r8/dex/n0;->a()V

    goto :goto_0

    :cond_3
    return-void
.end method


# virtual methods
.method public abstract a(Ljava/util/ArrayList;Lcom/android/tools/r8/dex/q0;Lcom/android/tools/r8/dex/t0;Lcom/android/tools/r8/dex/r0;)V
.end method
