.class public final synthetic Lcom/android/tools/r8/internal/w2$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiPredicate;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/w2;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/w2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/w2$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/w2;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/w2$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/w2;

    check-cast p1, <PERSON><PERSON><PERSON>/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    check-cast p2, [B

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/w2;->a(I[B)Z

    move-result p1

    return p1
.end method
