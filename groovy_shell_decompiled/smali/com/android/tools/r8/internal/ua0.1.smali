.class public final Lcom/android/tools/r8/internal/ua0;
.super Lcom/android/tools/r8/internal/g1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/va0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/va0;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/ua0;->b:Lcom/android/tools/r8/internal/va0;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/g1;-><init>()V

    return-void
.end method


# virtual methods
.method public final contains(Ljava/lang/Object;)Z
    .locals 3

    .line 1
    instance-of v0, p1, Ljava/util/Map$Entry;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 2
    :cond_0
    check-cast p1, Ljava/util/Map$Entry;

    .line 3
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/Integer;

    if-nez v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    .line 5
    iget-object v2, p0, Lcom/android/tools/r8/internal/ua0;->b:Lcom/android/tools/r8/internal/va0;

    invoke-virtual {v2, v0}, Lcom/android/tools/r8/internal/va0;->containsKey(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Lcom/android/tools/r8/internal/ua0;->b:Lcom/android/tools/r8/internal/va0;

    invoke-virtual {v2, v0}, Lcom/android/tools/r8/internal/va0;->b(Ljava/lang/Object;)I

    move-result v0

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    if-ne v0, p1, :cond_2

    const/4 v1, 0x1

    :cond_2
    :goto_0
    return v1
.end method

.method public final iterator()Lcom/android/tools/r8/internal/B10;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/ta0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/ta0;-><init>(Lcom/android/tools/r8/internal/ua0;)V

    return-object v0
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/ta0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/ta0;-><init>(Lcom/android/tools/r8/internal/ua0;)V

    return-object v0
.end method

.method public final remove(Ljava/lang/Object;)Z
    .locals 4

    .line 1
    instance-of v0, p1, Ljava/util/Map$Entry;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 2
    :cond_0
    check-cast p1, Ljava/util/Map$Entry;

    .line 3
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/Integer;

    if-nez v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    .line 5
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 6
    iget-object v2, p0, Lcom/android/tools/r8/internal/ua0;->b:Lcom/android/tools/r8/internal/va0;

    .line 7
    invoke-virtual {v2, v0}, Lcom/android/tools/r8/internal/va0;->d(Ljava/lang/Object;)I

    move-result v0

    const/4 v2, -0x1

    if-eq v0, v2, :cond_3

    .line 8
    iget-object v2, p0, Lcom/android/tools/r8/internal/ua0;->b:Lcom/android/tools/r8/internal/va0;

    .line 9
    iget-object v3, v2, Lcom/android/tools/r8/internal/va0;->d:[I

    .line 10
    aget v3, v3, v0

    if-eq p1, v3, :cond_2

    goto :goto_0

    .line 11
    :cond_2
    iget p1, v2, Lcom/android/tools/r8/internal/va0;->e:I

    sub-int/2addr p1, v0

    const/4 v1, 0x1

    sub-int/2addr p1, v1

    .line 12
    iget-object v2, v2, Lcom/android/tools/r8/internal/va0;->c:[Ljava/lang/Object;

    add-int/lit8 v3, v0, 0x1

    .line 13
    invoke-static {v2, v3, v2, v0, p1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 14
    iget-object v2, p0, Lcom/android/tools/r8/internal/ua0;->b:Lcom/android/tools/r8/internal/va0;

    .line 15
    iget-object v2, v2, Lcom/android/tools/r8/internal/va0;->d:[I

    .line 16
    invoke-static {v2, v3, v2, v0, p1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 17
    iget-object p1, p0, Lcom/android/tools/r8/internal/ua0;->b:Lcom/android/tools/r8/internal/va0;

    .line 18
    iget v0, p1, Lcom/android/tools/r8/internal/va0;->e:I

    sub-int/2addr v0, v1

    iput v0, p1, Lcom/android/tools/r8/internal/va0;->e:I

    .line 19
    iget-object p1, p1, Lcom/android/tools/r8/internal/va0;->c:[Ljava/lang/Object;

    const/4 v2, 0x0

    .line 20
    aput-object v2, p1, v0

    :cond_3
    :goto_0
    return v1
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/ua0;->b:Lcom/android/tools/r8/internal/va0;

    .line 2
    iget v0, v0, Lcom/android/tools/r8/internal/va0;->e:I

    return v0
.end method
