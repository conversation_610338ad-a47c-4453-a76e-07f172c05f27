.class public final Lcom/android/tools/r8/internal/W60;
.super Lcom/android/tools/r8/internal/Qx;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final j:Lcom/android/tools/r8/internal/W60;

.field public static final k:Lcom/android/tools/r8/internal/U60;


# instance fields
.field public final c:Lcom/android/tools/r8/internal/Y7;

.field public d:I

.field public e:I

.field public f:Ljava/util/List;

.field public g:Ljava/util/List;

.field public h:B

.field public i:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/U60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/U60;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/W60;->k:Lcom/android/tools/r8/internal/U60;

    .line 670
    new-instance v0, Lcom/android/tools/r8/internal/W60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/W60;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/W60;->j:Lcom/android/tools/r8/internal/W60;

    const/4 v1, 0x6

    .line 671
    iput v1, v0, Lcom/android/tools/r8/internal/W60;->e:I

    .line 672
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, v0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    .line 673
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, v0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 261
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Qx;-><init>()V

    const/4 v0, -0x1

    .line 476
    iput-byte v0, p0, Lcom/android/tools/r8/internal/W60;->h:B

    .line 515
    iput v0, p0, Lcom/android/tools/r8/internal/W60;->i:I

    .line 516
    sget-object v0, Lcom/android/tools/r8/internal/Y7;->b:Lcom/android/tools/r8/internal/XR;

    iput-object v0, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/V60;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Qx;-><init>(Lcom/android/tools/r8/internal/Ox;)V

    const/4 v0, -0x1

    .line 219
    iput-byte v0, p0, Lcom/android/tools/r8/internal/W60;->h:B

    .line 258
    iput v0, p0, Lcom/android/tools/r8/internal/W60;->i:I

    .line 259
    iget-object p1, p1, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    .line 260
    iput-object p1, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)V
    .locals 10

    .line 517
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Qx;-><init>()V

    const/4 v0, -0x1

    .line 717
    iput-byte v0, p0, Lcom/android/tools/r8/internal/W60;->h:B

    .line 756
    iput v0, p0, Lcom/android/tools/r8/internal/W60;->i:I

    const/4 v0, 0x6

    .line 757
    iput v0, p0, Lcom/android/tools/r8/internal/W60;->e:I

    .line 758
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    .line 759
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 760
    new-instance v0, Lcom/android/tools/r8/internal/W7;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/W7;-><init>()V

    .line 761
    new-instance v1, Lcom/android/tools/r8/internal/ie;

    const/4 v2, 0x1

    new-array v3, v2, [B

    invoke-direct {v1, v0, v3}, Lcom/android/tools/r8/internal/ie;-><init>(Ljava/io/OutputStream;[B)V

    const/4 v3, 0x0

    move v4, v3

    :cond_0
    :goto_0
    const/4 v5, 0x2

    const/4 v6, 0x4

    if-nez v3, :cond_c

    .line 762
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->i()I

    move-result v7

    if-eqz v7, :cond_9

    const/16 v8, 0x8

    if-eq v7, v8, :cond_8

    const/16 v8, 0x12

    if-eq v7, v8, :cond_6

    const/16 v8, 0xf8

    if-eq v7, v8, :cond_4

    const/16 v8, 0xfa

    if-eq v7, v8, :cond_1

    .line 768
    invoke-virtual {p0, p1, v1, p2, v7}, Lcom/android/tools/r8/internal/Qx;->a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/ie;Lcom/android/tools/r8/internal/Ku;I)Z

    move-result v5

    if-nez v5, :cond_0

    goto/16 :goto_2

    .line 796
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v7

    .line 797
    invoke-virtual {p1, v7}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v7

    and-int/lit8 v8, v4, 0x4

    if-eq v8, v6, :cond_2

    .line 798
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v8

    if-lez v8, :cond_2

    .line 799
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    iput-object v8, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    or-int/lit8 v4, v4, 0x4

    .line 802
    :cond_2
    :goto_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v8

    if-lez v8, :cond_3

    .line 803
    iget-object v8, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 804
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 805
    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    invoke-interface {v8, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 806
    :cond_3
    iput v7, p1, Lcom/android/tools/r8/internal/be;->h:I

    .line 807
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->j()V

    goto :goto_0

    :cond_4
    and-int/lit8 v7, v4, 0x4

    if-eq v7, v6, :cond_5

    .line 808
    new-instance v7, Ljava/util/ArrayList;

    invoke-direct {v7}, Ljava/util/ArrayList;-><init>()V

    iput-object v7, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    or-int/lit8 v4, v4, 0x4

    .line 811
    :cond_5
    iget-object v7, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 812
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v8

    .line 813
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v8

    invoke-interface {v7, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_6
    and-int/lit8 v7, v4, 0x2

    if-eq v7, v5, :cond_7

    .line 814
    new-instance v7, Ljava/util/ArrayList;

    invoke-direct {v7}, Ljava/util/ArrayList;-><init>()V

    iput-object v7, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    or-int/lit8 v4, v4, 0x2

    .line 817
    :cond_7
    iget-object v7, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    sget-object v8, Lcom/android/tools/r8/internal/a80;->n:Lcom/android/tools/r8/internal/Y70;

    invoke-virtual {p1, v8, p2}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v8

    invoke-interface {v7, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 818
    :cond_8
    iget v7, p0, Lcom/android/tools/r8/internal/W60;->d:I

    or-int/2addr v7, v2

    iput v7, p0, Lcom/android/tools/r8/internal/W60;->d:I

    .line 819
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v7

    .line 820
    iput v7, p0, Lcom/android/tools/r8/internal/W60;->e:I
    :try_end_0
    .catch Lcom/android/tools/r8/internal/kI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto/16 :goto_0

    :cond_9
    :goto_2
    move v3, v2

    goto/16 :goto_0

    :catchall_0
    move-exception p1

    goto :goto_3

    :catch_0
    move-exception p1

    .line 857
    :try_start_1
    new-instance p2, Lcom/android/tools/r8/internal/kI;

    .line 858
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/kI;-><init>(Ljava/lang/String;)V

    .line 859
    iput-object p0, p2, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 860
    throw p2

    :catch_1
    move-exception p1

    .line 861
    iput-object p0, p1, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 862
    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :goto_3
    and-int/lit8 p2, v4, 0x2

    if-ne p2, v5, :cond_a

    .line 868
    iget-object p2, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    :cond_a
    and-int/lit8 p2, v4, 0x4

    if-ne p2, v6, :cond_b

    .line 871
    iget-object p2, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 874
    :cond_b
    :try_start_2
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/ie;->a()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 878
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    goto :goto_4

    :catchall_1
    move-exception p1

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    .line 879
    throw p1

    .line 880
    :catch_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    .line 881
    :goto_4
    iget-object p2, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Mv;->a()V

    .line 882
    throw p1

    :cond_c
    and-int/lit8 p1, v4, 0x2

    if-ne p1, v5, :cond_d

    .line 883
    iget-object p1, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    :cond_d
    and-int/lit8 p1, v4, 0x4

    if-ne p1, v6, :cond_e

    .line 886
    iget-object p1, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 889
    :cond_e
    :try_start_3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/ie;->a()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    .line 893
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    goto :goto_5

    :catchall_2
    move-exception p1

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    .line 894
    throw p1

    .line 895
    :catch_3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    .line 896
    :goto_5
    iget-object p1, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Mv;->a()V

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 5

    .line 16
    iget v0, p0, Lcom/android/tools/r8/internal/W60;->i:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    .line 20
    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/W60;->d:I

    const/4 v1, 0x1

    and-int/2addr v0, v1

    const/4 v2, 0x0

    if-ne v0, v1, :cond_1

    .line 21
    iget v0, p0, Lcom/android/tools/r8/internal/W60;->e:I

    .line 22
    invoke-static {v1, v0}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v0

    goto :goto_0

    :cond_1
    move v0, v2

    :goto_0
    move v1, v2

    .line 24
    :goto_1
    iget-object v3, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    const/4 v4, 0x2

    if-ge v1, v3, :cond_2

    .line 25
    iget-object v3, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    .line 26
    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/N0;

    invoke-static {v4, v3}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v3

    add-int/2addr v0, v3

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_2
    move v1, v2

    .line 30
    :goto_2
    iget-object v3, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v2, v3, :cond_4

    .line 31
    iget-object v3, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 32
    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    if-ltz v3, :cond_3

    .line 33
    invoke-static {v3}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v3

    goto :goto_3

    :cond_3
    const/16 v3, 0xa

    :goto_3
    add-int/2addr v1, v3

    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    :cond_4
    add-int/2addr v0, v1

    .line 34
    iget-object v1, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 35
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    mul-int/2addr v1, v4

    add-int/2addr v1, v0

    .line 37
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Qx;->e()I

    move-result v0

    add-int/2addr v0, v1

    .line 38
    iget-object v1, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Y7;->size()I

    move-result v1

    add-int/2addr v1, v0

    .line 39
    iput v1, p0, Lcom/android/tools/r8/internal/W60;->i:I

    return v1
.end method

.method public final a(Lcom/android/tools/r8/internal/ie;)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/W60;->a()I

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Px;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/Px;-><init>(Lcom/android/tools/r8/internal/Qx;)V

    .line 3
    iget v1, p0, Lcom/android/tools/r8/internal/W60;->d:I

    const/4 v2, 0x1

    and-int/2addr v1, v2

    const/4 v3, 0x0

    if-ne v1, v2, :cond_0

    .line 4
    iget v1, p0, Lcom/android/tools/r8/internal/W60;->e:I

    .line 5
    invoke-virtual {p1, v2, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 6
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->d(I)V

    :cond_0
    move v1, v3

    .line 7
    :goto_0
    iget-object v2, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_1

    .line 8
    iget-object v2, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/N0;

    const/4 v4, 0x2

    invoke-virtual {p1, v4, v2}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    move v1, v3

    .line 10
    :goto_1
    iget-object v2, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_2

    const/16 v2, 0x1f

    .line 11
    iget-object v4, p0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    .line 12
    invoke-virtual {p1, v2, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 13
    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_2
    const/16 v1, 0x4a38

    .line 14
    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Px;->a(ILcom/android/tools/r8/internal/ie;)V

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/ie;->a(Lcom/android/tools/r8/internal/Y7;)V

    return-void
.end method

.method public final b()Lcom/android/tools/r8/internal/Nx;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/V60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/V60;-><init>()V

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/Nx;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/V60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/V60;-><init>()V

    .line 2
    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/V60;->a(Lcom/android/tools/r8/internal/W60;)Lcom/android/tools/r8/internal/V60;

    move-result-object v0

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/N0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/W60;->j:Lcom/android/tools/r8/internal/W60;

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 4

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/internal/W60;->h:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v2, 0x0

    if-nez v0, :cond_1

    return v2

    :cond_1
    move v0, v2

    .line 2
    :goto_0
    iget-object v3, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_3

    .line 3
    iget-object v3, p0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/a80;

    .line 4
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/a80;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_2

    .line 5
    iput-byte v2, p0, Lcom/android/tools/r8/internal/W60;->h:B

    return v2

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 9
    :cond_3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Qx;->d()Z

    move-result v0

    if-nez v0, :cond_4

    .line 10
    iput-byte v2, p0, Lcom/android/tools/r8/internal/W60;->h:B

    return v2

    .line 13
    :cond_4
    iput-byte v1, p0, Lcom/android/tools/r8/internal/W60;->h:B

    return v1
.end method
