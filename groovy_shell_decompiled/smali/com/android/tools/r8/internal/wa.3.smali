.class public final Lcom/android/tools/r8/internal/wa;
.super Lcom/android/tools/r8/internal/xa;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Ljava/util/ArrayList;


# direct methods
.method public constructor <init>(Ljava/util/ArrayList;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/xa;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/wa;->a:Ljava/util/ArrayList;

    return-void
.end method


# virtual methods
.method public final a(Ljava/util/Collection;Lcom/android/tools/r8/internal/Aa;Ljava/util/concurrent/ExecutorService;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/wa;->a:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/ua;

    .line 2
    invoke-interface {v1, p1, p2, p3}, Lcom/android/tools/r8/internal/ua;->a(Ljava/util/Collection;Lcom/android/tools/r8/internal/Aa;Ljava/util/concurrent/ExecutorService;)V

    goto :goto_0

    :cond_0
    return-void
.end method
