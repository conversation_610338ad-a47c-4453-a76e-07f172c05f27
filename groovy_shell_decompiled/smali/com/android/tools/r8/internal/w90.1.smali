.class public final Lcom/android/tools/r8/internal/w90;
.super Lcom/android/tools/r8/internal/z90;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final E:Lcom/android/tools/r8/graph/x2;

.field public final F:Lcom/android/tools/r8/internal/sr0;

.field public final G:Lcom/android/tools/r8/internal/sr0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/internal/sr0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/z90;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/w90;->E:Lcom/android/tools/r8/graph/x2;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/w90;->F:Lcom/android/tools/r8/internal/sr0;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/internal/w90;->G:Lcom/android/tools/r8/internal/sr0;

    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w90;->E:Lcom/android/tools/r8/graph/x2;

    const-class v1, Lcom/android/tools/r8/internal/w90;

    invoke-static {v1, v0}, Lcom/android/tools/r8/internal/cB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/cB;

    move-result-object v0

    return-object v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w90;->E:Lcom/android/tools/r8/graph/x2;

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/x2;->l0()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/w90;->F:Lcom/android/tools/r8/internal/sr0;

    .line 3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->w()Z

    move-result v2

    if-eqz v2, :cond_0

    .line 4
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Dd;->Q()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/J2;->l0()Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    .line 6
    :cond_0
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->toString()Ljava/lang/String;

    move-result-object v1

    .line 7
    :goto_0
    iget-object v2, p0, Lcom/android/tools/r8/internal/w90;->G:Lcom/android/tools/r8/internal/sr0;

    .line 8
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/sr0;->w()Z

    move-result v3

    if-eqz v3, :cond_1

    .line 9
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Dd;->Q()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->l0()Ljava/lang/String;

    move-result-object v2

    goto :goto_1

    .line 11
    :cond_1
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/sr0;->toString()Ljava/lang/String;

    move-result-object v2

    .line 12
    :goto_1
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "NonComparableElements("

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v3, " - "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " vs "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
