.class public abstract Lcom/android/tools/r8/internal/u40;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic a:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/ZA;)V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Lo0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/Lo0;-><init>(Lcom/android/tools/r8/graph/y;)V

    new-instance p0, Lcom/android/tools/r8/internal/zi0;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/zi0;-><init>()V

    .line 2
    invoke-static {v0, p0}, Lcom/android/tools/r8/internal/cB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/cB;

    move-result-object p0

    .line 3
    invoke-interface {p0}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object p0

    sget-object v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda2;

    invoke-interface {p0, v0}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object p0

    new-instance v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda0;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/ZA;)V

    invoke-interface {p0, v0}, Ljava/util/stream/Stream;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public static a(Ljava/util/List;)V
    .locals 2

    .line 4
    sget-object v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda4;

    .line 5
    invoke-static {p0, v0}, Lcom/android/tools/r8/internal/QR;->b(Ljava/util/List;Ljava/util/function/Predicate;)I

    move-result v0

    if-ltz v0, :cond_2

    add-int/lit8 v0, v0, 0x1

    .line 9
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v1

    invoke-interface {p0, v0, v1}, Ljava/util/List;->subList(II)Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/s40;

    .line 10
    sget-boolean v1, Lcom/android/tools/r8/internal/u40;->a:Z

    if-nez v1, :cond_0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/s40;->g()Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_2
    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/s40;)Z
    .locals 0

    .line 11
    instance-of p0, p0, Lcom/android/tools/r8/internal/i20;

    return p0
.end method

.method public static b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/ZA;)V
    .locals 13

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/AY;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/AY;-><init>()V

    new-instance v1, Lcom/android/tools/r8/internal/LX;

    invoke-direct {v1}, Lcom/android/tools/r8/internal/LX;-><init>()V

    new-instance v2, Lcom/android/tools/r8/internal/cY;

    const/4 v3, 0x0

    .line 2
    invoke-direct {v2, p0, v3}, Lcom/android/tools/r8/internal/cY;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/shaking/u4;)V

    .line 3
    new-instance v3, Lcom/android/tools/r8/internal/fY;

    invoke-direct {v3, p0}, Lcom/android/tools/r8/internal/fY;-><init>(Lcom/android/tools/r8/graph/y;)V

    new-instance v4, Lcom/android/tools/r8/internal/nY;

    invoke-direct {v4, p0}, Lcom/android/tools/r8/internal/nY;-><init>(Lcom/android/tools/r8/graph/y;)V

    new-instance v5, Lcom/android/tools/r8/internal/lY;

    invoke-direct {v5}, Lcom/android/tools/r8/internal/lY;-><init>()V

    new-instance v6, Lcom/android/tools/r8/internal/mY;

    invoke-direct {v6}, Lcom/android/tools/r8/internal/mY;-><init>()V

    new-instance v7, Lcom/android/tools/r8/internal/tY;

    invoke-direct {v7}, Lcom/android/tools/r8/internal/tY;-><init>()V

    new-instance v8, Lcom/android/tools/r8/internal/wY;

    invoke-direct {v8}, Lcom/android/tools/r8/internal/wY;-><init>()V

    new-instance v9, Lcom/android/tools/r8/internal/CY;

    invoke-direct {v9, p0}, Lcom/android/tools/r8/internal/CY;-><init>(Lcom/android/tools/r8/graph/y;)V

    new-instance p0, Lcom/android/tools/r8/internal/zY;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/zY;-><init>()V

    .line 4
    sget v10, Lcom/android/tools/r8/internal/cB;->c:I

    const/16 v10, 0xb

    new-array v11, v10, [Ljava/lang/Object;

    const/4 v12, 0x0

    aput-object v0, v11, v12

    const/4 v0, 0x1

    aput-object v1, v11, v0

    const/4 v0, 0x2

    aput-object v2, v11, v0

    const/4 v0, 0x3

    aput-object v3, v11, v0

    const/4 v0, 0x4

    aput-object v4, v11, v0

    const/4 v0, 0x5

    aput-object v5, v11, v0

    const/4 v0, 0x6

    aput-object v6, v11, v0

    const/4 v0, 0x7

    aput-object v7, v11, v0

    const/16 v0, 0x8

    aput-object v8, v11, v0

    const/16 v0, 0x9

    aput-object v9, v11, v0

    const/16 v0, 0xa

    aput-object p0, v11, v0

    .line 6
    invoke-static {v10, v11}, Lcom/android/tools/r8/internal/v10;->a(I[Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p0

    .line 7
    array-length v0, p0

    invoke-static {v0, p0}, Lcom/android/tools/r8/internal/cB;->b(I[Ljava/lang/Object;)Lcom/android/tools/r8/internal/cB;

    move-result-object p0

    .line 8
    invoke-interface {p0}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object p0

    sget-object v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;->INSTANCE:Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;

    invoke-interface {p0, v0}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object p0

    new-instance v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda1;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/ZA;)V

    invoke-interface {p0, v0}, Ljava/util/stream/Stream;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public static c(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/ZA;)V
    .locals 11

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/AY;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/AY;-><init>()V

    new-instance v1, Lcom/android/tools/r8/internal/LX;

    invoke-direct {v1}, Lcom/android/tools/r8/internal/LX;-><init>()V

    new-instance v2, Lcom/android/tools/r8/internal/cY;

    const/4 v3, 0x0

    .line 2
    invoke-direct {v2, p0, v3}, Lcom/android/tools/r8/internal/cY;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/shaking/u4;)V

    .line 3
    new-instance v3, Lcom/android/tools/r8/internal/nY;

    invoke-direct {v3, p0}, Lcom/android/tools/r8/internal/nY;-><init>(Lcom/android/tools/r8/graph/y;)V

    new-instance p0, Lcom/android/tools/r8/internal/lY;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/lY;-><init>()V

    new-instance v4, Lcom/android/tools/r8/internal/mY;

    invoke-direct {v4}, Lcom/android/tools/r8/internal/mY;-><init>()V

    new-instance v5, Lcom/android/tools/r8/internal/tY;

    invoke-direct {v5}, Lcom/android/tools/r8/internal/tY;-><init>()V

    new-instance v6, Lcom/android/tools/r8/internal/wY;

    invoke-direct {v6}, Lcom/android/tools/r8/internal/wY;-><init>()V

    new-instance v7, Lcom/android/tools/r8/internal/zY;

    invoke-direct {v7}, Lcom/android/tools/r8/internal/zY;-><init>()V

    .line 4
    sget v8, Lcom/android/tools/r8/internal/cB;->c:I

    const/16 v8, 0x9

    new-array v9, v8, [Ljava/lang/Object;

    const/4 v10, 0x0

    aput-object v0, v9, v10

    const/4 v0, 0x1

    aput-object v1, v9, v0

    const/4 v0, 0x2

    aput-object v2, v9, v0

    const/4 v0, 0x3

    aput-object v3, v9, v0

    const/4 v0, 0x4

    aput-object p0, v9, v0

    const/4 p0, 0x5

    aput-object v4, v9, p0

    const/4 p0, 0x6

    aput-object v5, v9, p0

    const/4 p0, 0x7

    aput-object v6, v9, p0

    const/16 p0, 0x8

    aput-object v7, v9, p0

    .line 6
    invoke-static {v8, v9}, Lcom/android/tools/r8/internal/v10;->a(I[Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p0

    .line 7
    array-length v0, p0

    invoke-static {v0, p0}, Lcom/android/tools/r8/internal/cB;->b(I[Ljava/lang/Object;)Lcom/android/tools/r8/internal/cB;

    move-result-object p0

    .line 8
    invoke-interface {p0}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object p0

    sget-object v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;->INSTANCE:Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;

    invoke-interface {p0, v0}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object p0

    new-instance v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda1;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/ZA;)V

    invoke-interface {p0, v0}, Ljava/util/stream/Stream;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method
