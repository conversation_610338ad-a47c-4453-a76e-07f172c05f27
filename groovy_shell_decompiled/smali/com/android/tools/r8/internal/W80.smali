.class public final Lcom/android/tools/r8/internal/W80;
.super Lcom/android/tools/r8/internal/pU;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final c:Lcom/android/tools/r8/internal/p20;

.field public static final synthetic d:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 3
    sget-object v0, Lcom/android/tools/r8/internal/p20;->b:Lcom/android/tools/r8/internal/p20;

    sput-object v0, Lcom/android/tools/r8/internal/W80;->c:Lcom/android/tools/r8/internal/p20;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/pU;-><init>(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/internal/Lx;)Ljava/lang/Boolean;
    .locals 0

    .line 266
    invoke-virtual {p1, p0}, Lcom/android/tools/r8/internal/Lx;->a(Lcom/android/tools/r8/graph/l1;)Z

    move-result p0

    invoke-static {p0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/ir/optimize/a;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/uI;)Lcom/android/tools/r8/internal/uD;
    .locals 15

    move-object v7, p0

    move-object/from16 v8, p1

    move-object/from16 v9, p2

    move-object/from16 v10, p5

    move-object/from16 v11, p6

    .line 52
    invoke-virtual/range {p6 .. p6}, Lcom/android/tools/r8/internal/rD;->f1()Z

    move-result v0

    if-nez v0, :cond_0

    return-object v10

    .line 53
    :cond_0
    invoke-virtual/range {p6 .. p6}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v12

    .line 54
    invoke-virtual {v12}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 55
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/J2;->L0()Z

    move-result v0

    if-nez v0, :cond_1

    return-object v10

    .line 59
    :cond_1
    iget-object v0, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 61
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 62
    invoke-virtual {v0, v12}, Lcom/android/tools/r8/graph/j;->e(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/V4;

    move-result-object v0

    .line 63
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/V4;->o()Lcom/android/tools/r8/graph/V4$c;

    move-result-object v13

    if-nez v13, :cond_2

    return-object v10

    .line 68
    :cond_2
    iget-object v0, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v11, v0, v9}, Lcom/android/tools/r8/internal/uI;->f(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/graph/H0;

    move-result-object v14

    .line 69
    iget-object v0, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-static {v0, v13, v14}, Lcom/android/tools/r8/internal/E4;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/V4$c;Lcom/android/tools/r8/graph/H0;)Lcom/android/tools/r8/internal/D4;

    move-result-object v6

    move-object v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p3

    move-object/from16 v3, p4

    move-object/from16 v4, p5

    move-object/from16 v5, p6

    .line 70
    invoke-virtual/range {v0 .. v6}, Lcom/android/tools/r8/internal/pU;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/ir/optimize/a;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/internal/D4;)Z

    move-result v0

    if-eqz v0, :cond_3

    return-object v10

    :cond_3
    if-eqz v14, :cond_6

    .line 71
    invoke-interface {v14}, Lcom/android/tools/r8/graph/o0;->c0()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 72
    invoke-interface {v14}, Lcom/android/tools/r8/graph/o0;->P()Lcom/android/tools/r8/graph/C5;

    move-result-object v0

    .line 73
    iget-object v1, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 74
    invoke-interface {v0}, Lcom/android/tools/r8/graph/o0;->K()Z

    move-result v2

    if-eqz v2, :cond_4

    .line 75
    invoke-interface {v0}, Lcom/android/tools/r8/graph/o0;->Y()Lcom/android/tools/r8/graph/B5;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/B5;)Lcom/android/tools/r8/shaking/k1;

    move-result-object v1

    goto :goto_0

    .line 76
    :cond_4
    invoke-interface {v0}, Lcom/android/tools/r8/graph/o0;->H()Lcom/android/tools/r8/graph/D5;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object v1

    .line 77
    :goto_0
    iget-object v2, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, v2, v0}, Lcom/android/tools/r8/shaking/s1;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/C5;)Z

    move-result v0

    goto :goto_1

    .line 79
    :cond_5
    iget-object v0, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 80
    iget-object v0, v0, Lcom/android/tools/r8/graph/y;->e:Lcom/android/tools/r8/shaking/l;

    .line 81
    iget-object v0, v0, Lcom/android/tools/r8/shaking/l;->a:Ljava/util/IdentityHashMap;

    .line 82
    invoke-virtual {v14}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    :goto_1
    if-nez v0, :cond_6

    return-object v10

    .line 83
    :cond_6
    invoke-virtual {v12}, Lcom/android/tools/r8/graph/x2;->C0()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    iget-object v1, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/y;)Z

    move-result v0

    if-eqz v0, :cond_7

    .line 84
    iget-object v0, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 85
    iget-object v0, v0, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 86
    invoke-virtual {v12}, Lcom/android/tools/r8/graph/x2;->C0()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-static {v1}, Lcom/android/tools/r8/internal/F1;->b(Lcom/android/tools/r8/graph/J2;)V

    sget-object v0, Lcom/android/tools/r8/internal/gk0;->b:Lcom/android/tools/r8/internal/gk0;

    goto :goto_2

    .line 88
    :cond_7
    iget-object v0, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 89
    invoke-virtual {v13, v0, v11, v14}, Lcom/android/tools/r8/graph/V4$c;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/uI;Lcom/android/tools/r8/graph/H0;)Lcom/android/tools/r8/internal/iV;

    move-result-object v0

    .line 90
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/iV;->i()Lcom/android/tools/r8/internal/E1;

    move-result-object v0

    .line 93
    :goto_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/E1;->f()Z

    move-result v1

    if-eqz v1, :cond_b

    .line 94
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/E1;->i()Lcom/android/tools/r8/internal/ok0;

    move-result-object v0

    .line 95
    iget-object v1, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0, v1, v9}, Lcom/android/tools/r8/internal/ok0;->b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z

    move-result v1

    if-eqz v1, :cond_b

    .line 96
    iget-object v1, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 97
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v2

    invoke-virtual {v0, v1, v2, v8, v11}, Lcom/android/tools/r8/internal/ok0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/xt0;Lcom/android/tools/r8/internal/UT;)[Lcom/android/tools/r8/internal/rD;

    move-result-object v0

    .line 98
    invoke-static {v0}, Lcom/android/tools/r8/internal/U3;->b([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/rD;

    .line 99
    invoke-virtual {v11, v1}, Lcom/android/tools/r8/internal/rD;->c(Lcom/android/tools/r8/internal/rD;)V

    .line 100
    invoke-virtual/range {p6 .. p6}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    move-object/from16 v3, p3

    invoke-virtual {v2, v1, v3}, Lcom/android/tools/r8/internal/vt0;->a(Lcom/android/tools/r8/internal/vt0;Ljava/util/Set;)V

    const/4 v1, 0x0

    .line 101
    invoke-virtual {v11, v1}, Lcom/android/tools/r8/internal/rD;->d(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/vt0;

    .line 102
    invoke-virtual/range {p6 .. p6}, Lcom/android/tools/r8/internal/rD;->X1()Z

    move-result v1

    if-eqz v1, :cond_8

    .line 103
    iget-object v1, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-interface {v10, v1, v9}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z

    goto :goto_3

    .line 104
    :cond_8
    invoke-virtual/range {p6 .. p6}, Lcom/android/tools/r8/internal/rD;->a2()Z

    move-result v1

    if-eqz v1, :cond_9

    if-eqz v14, :cond_9

    .line 105
    iget-object v1, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 106
    invoke-virtual {v14}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    .line 107
    invoke-interface {v10, v1, v8, v2}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/graph/J2;)Z

    .line 112
    :cond_9
    :goto_3
    iget-object v1, v7, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 114
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    move-object/from16 v2, p4

    .line 115
    invoke-interface {v10, v8, v2, v0, v1}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/O5;[Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/internal/uD;

    move-result-object v0

    if-eqz v14, :cond_a

    .line 119
    invoke-virtual {v14}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/j1;->Y0()Lcom/android/tools/r8/internal/jX;

    move-result-object v1

    .line 120
    iget v2, v1, Lcom/android/tools/r8/internal/jX;->u:I

    or-int/lit16 v2, v2, 0x100

    .line 121
    iput v2, v1, Lcom/android/tools/r8/internal/jX;->u:I

    :cond_a
    move-object v10, v0

    :cond_b
    return-object v10
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/ir/optimize/a;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/VC;)Lcom/android/tools/r8/internal/uD;
    .locals 0

    .line 122
    invoke-virtual/range {p0 .. p5}, Lcom/android/tools/r8/internal/W80;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/ir/optimize/a;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/sv;)Lcom/android/tools/r8/internal/uD;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/ir/optimize/a;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/im0;)Lcom/android/tools/r8/internal/uD;
    .locals 0

    .line 123
    invoke-virtual/range {p0 .. p5}, Lcom/android/tools/r8/internal/W80;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/ir/optimize/a;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/sv;)Lcom/android/tools/r8/internal/uD;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/ir/optimize/a;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/sv;)Lcom/android/tools/r8/internal/uD;
    .locals 15

    move-object v8, p0

    move-object/from16 v0, p1

    move-object/from16 v9, p4

    move-object/from16 v10, p5

    .line 124
    invoke-virtual/range {p5 .. p5}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v11

    .line 127
    iget-object v1, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 128
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v1, v11}, Lcom/android/tools/r8/graph/j;->c(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/z3;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/z3;->l()Lcom/android/tools/r8/graph/z3$a;

    move-result-object v1

    if-nez v1, :cond_1

    .line 130
    iget-object v1, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    new-instance v2, Lcom/android/tools/r8/internal/W80$$ExternalSyntheticLambda0;

    invoke-direct {v2, v11}, Lcom/android/tools/r8/internal/W80$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/l1;)V

    .line 132
    sget-object v3, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 133
    invoke-virtual {v1, v3, v2}, Lcom/android/tools/r8/graph/y;->a(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Boolean;

    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 136
    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 137
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->m()Lcom/android/tools/r8/internal/yb0;

    move-result-object v1

    const-wide/16 v2, 0x0

    invoke-virtual {v0, v2, v3, v1}, Lcom/android/tools/r8/internal/aA;->a(JLcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v0

    .line 138
    invoke-interface {v9, v0}, Lcom/android/tools/r8/internal/uD;->b(Lcom/android/tools/r8/internal/rD;)V

    :cond_0
    return-object v9

    .line 143
    :cond_1
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v2

    iget-object v3, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, v2, v3}, Lcom/android/tools/r8/graph/F4;->a(Lcom/android/tools/r8/graph/z5;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/u20;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/T6;->b()Z

    move-result v2

    if-eqz v2, :cond_2

    return-object v9

    .line 144
    :cond_2
    iget-object v2, v1, Lcom/android/tools/r8/graph/z3$a;->c:Lcom/android/tools/r8/graph/E0;

    iget-object v1, v1, Lcom/android/tools/r8/graph/z3$a;->d:Lcom/android/tools/r8/graph/g1;

    invoke-static {v2, v1}, Lcom/android/tools/r8/graph/F0;->a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/g1;)Lcom/android/tools/r8/graph/F0;

    move-result-object v12

    .line 145
    invoke-virtual {v12}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v1

    move-object v13, v1

    check-cast v13, Lcom/android/tools/r8/graph/g1;

    .line 146
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/g1;->y0()Z

    move-result v1

    .line 147
    instance-of v14, v10, Lcom/android/tools/r8/internal/im0;

    if-eq v1, v14, :cond_3

    return-object v9

    :cond_3
    if-eqz v14, :cond_4

    .line 148
    invoke-virtual/range {p5 .. p5}, Lcom/android/tools/r8/internal/rD;->f1()Z

    move-result v1

    if-nez v1, :cond_4

    .line 149
    iget-object v1, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 150
    invoke-virtual {v11}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    .line 151
    invoke-interface {v9, v1, v0, v2}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/graph/J2;)Z

    return-object v9

    .line 152
    :cond_4
    invoke-interface {v12}, Lcom/android/tools/r8/graph/o0;->c0()Z

    move-result v1

    if-eqz v1, :cond_6

    .line 153
    invoke-interface {v12}, Lcom/android/tools/r8/graph/o0;->P()Lcom/android/tools/r8/graph/C5;

    move-result-object v1

    .line 154
    iget-object v2, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 155
    invoke-interface {v1}, Lcom/android/tools/r8/graph/o0;->K()Z

    move-result v3

    if-eqz v3, :cond_5

    .line 156
    invoke-interface {v1}, Lcom/android/tools/r8/graph/o0;->Y()Lcom/android/tools/r8/graph/B5;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/B5;)Lcom/android/tools/r8/shaking/k1;

    move-result-object v2

    goto :goto_0

    .line 157
    :cond_5
    invoke-interface {v1}, Lcom/android/tools/r8/graph/o0;->H()Lcom/android/tools/r8/graph/D5;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object v2

    .line 158
    :goto_0
    iget-object v3, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v2, v3, v1}, Lcom/android/tools/r8/shaking/s1;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/C5;)Z

    move-result v1

    goto :goto_1

    .line 160
    :cond_6
    iget-object v1, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 161
    iget-object v1, v1, Lcom/android/tools/r8/graph/y;->e:Lcom/android/tools/r8/shaking/l;

    .line 162
    iget-object v1, v1, Lcom/android/tools/r8/shaking/l;->a:Ljava/util/IdentityHashMap;

    .line 163
    invoke-virtual {v12}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    :goto_1
    if-nez v1, :cond_7

    return-object v9

    .line 164
    :cond_7
    iget-object v1, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 165
    iget-object v1, v1, Lcom/android/tools/r8/graph/y;->e:Lcom/android/tools/r8/shaking/l;

    .line 166
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 167
    invoke-virtual {v12}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/shaking/l;->a(Lcom/android/tools/r8/graph/s2;)Lcom/android/tools/r8/internal/D4;

    move-result-object v7

    move-object v1, p0

    move-object/from16 v2, p1

    move-object/from16 v3, p2

    move-object/from16 v4, p3

    move-object/from16 v5, p4

    move-object/from16 v6, p5

    .line 168
    invoke-virtual/range {v1 .. v7}, Lcom/android/tools/r8/internal/pU;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/ir/optimize/a;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/internal/D4;)Z

    move-result v1

    if-eqz v1, :cond_8

    return-object v9

    .line 173
    :cond_8
    invoke-virtual {v11}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    iget-object v2, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/y;)Z

    move-result v1

    if-eqz v1, :cond_9

    .line 174
    iget-object v1, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 175
    iget-object v1, v1, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 176
    invoke-virtual {v11}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-static {v2}, Lcom/android/tools/r8/internal/F1;->b(Lcom/android/tools/r8/graph/J2;)V

    sget-object v1, Lcom/android/tools/r8/internal/gk0;->b:Lcom/android/tools/r8/internal/gk0;

    goto/16 :goto_3

    .line 177
    :cond_9
    iget-object v1, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    .line 178
    sget-boolean v2, Lcom/android/tools/r8/shaking/i;->J:Z

    if-nez v2, :cond_a

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h;->c()V

    .line 179
    :cond_a
    iget-object v1, v1, Lcom/android/tools/r8/shaking/i;->s:Lcom/android/tools/r8/graph/k3;

    .line 180
    invoke-virtual {v12}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/k3;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/l3;

    move-result-object v1

    const/4 v2, 0x1

    if-eqz v1, :cond_b

    .line 181
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/l3;->f()Z

    move-result v1

    if-eqz v1, :cond_b

    goto :goto_2

    .line 182
    :cond_b
    instance-of v1, v12, Lcom/android/tools/r8/graph/B5;

    xor-int/2addr v2, v1

    :goto_2
    if-eqz v2, :cond_c

    .line 183
    iget-object v1, v13, Lcom/android/tools/r8/graph/g1;->l:Lcom/android/tools/r8/internal/yv;

    .line 184
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/yv;->f()Lcom/android/tools/r8/internal/E1;

    move-result-object v1

    .line 185
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/g1;->y0()Z

    move-result v2

    if-nez v2, :cond_f

    .line 187
    invoke-virtual/range {p5 .. p5}, Lcom/android/tools/r8/internal/rD;->c()Lcom/android/tools/r8/internal/VC;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/VC;->m()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    iget-object v3, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v4

    .line 188
    sget-object v5, Lcom/android/tools/r8/internal/L1;->a:Lcom/android/tools/r8/internal/K1;

    .line 189
    invoke-virtual {v2, v3, v4, v5}, Lcom/android/tools/r8/internal/vt0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/L1;)Lcom/android/tools/r8/internal/E1;

    move-result-object v2

    .line 190
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/E1;->C()Z

    move-result v3

    if-eqz v3, :cond_f

    .line 192
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/E1;->z()Lcom/android/tools/r8/internal/R10;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 193
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v2, v3}, Lcom/android/tools/r8/internal/R10;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/internal/E1;

    move-result-object v2

    .line 194
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result v3

    if-nez v3, :cond_f

    move-object v1, v2

    goto :goto_3

    .line 202
    :cond_c
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/g1;->y0()Z

    move-result v1

    if-eqz v1, :cond_e

    .line 204
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/g1;->R0()Lcom/android/tools/r8/graph/O2;

    move-result-object v1

    iget-object v2, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 205
    iget-object v2, v2, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 206
    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/O2;->a(Lcom/android/tools/r8/internal/F1;)Lcom/android/tools/r8/internal/E1;

    move-result-object v1

    .line 208
    sget-boolean v2, Lcom/android/tools/r8/internal/W80;->d:Z

    if-nez v2, :cond_f

    iget-object v3, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 209
    iget-object v4, v13, Lcom/android/tools/r8/graph/g1;->l:Lcom/android/tools/r8/internal/yv;

    .line 210
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/yv;->f()Lcom/android/tools/r8/internal/E1;

    move-result-object v4

    .line 212
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/g1;->R0()Lcom/android/tools/r8/graph/O2;

    move-result-object v5

    .line 213
    iget-object v6, v3, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 214
    invoke-virtual {v5, v6}, Lcom/android/tools/r8/graph/O2;->a(Lcom/android/tools/r8/internal/F1;)Lcom/android/tools/r8/internal/E1;

    move-result-object v5

    if-nez v2, :cond_f

    .line 215
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result v2

    if-nez v2, :cond_f

    .line 216
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/g1;->S0()Z

    move-result v2

    if-eqz v2, :cond_f

    .line 217
    iget-object v2, v3, Lcom/android/tools/r8/graph/y;->u:Lcom/android/tools/r8/internal/G1;

    .line 218
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v6

    check-cast v6, Lcom/android/tools/r8/graph/l1;

    .line 219
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v6

    .line 220
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v7

    invoke-static {v6, v7, v3}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v3

    .line 221
    invoke-virtual {v2, v5, v4, v3}, Lcom/android/tools/r8/internal/G1;->b(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/E1;

    move-result-object v2

    .line 222
    invoke-virtual {v2, v4}, Lcom/android/tools/r8/internal/E1;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_d

    goto :goto_3

    .line 223
    :cond_d
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 224
    :cond_e
    iget-object v1, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 225
    iget-object v1, v1, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 226
    invoke-virtual {v11}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/F1;->a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/ak0;

    move-result-object v1

    .line 229
    :cond_f
    :goto_3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/E1;->f()Z

    move-result v2

    if-eqz v2, :cond_14

    .line 230
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/E1;->i()Lcom/android/tools/r8/internal/ok0;

    move-result-object v1

    .line 231
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/E1;->L()Z

    move-result v2

    if-eqz v2, :cond_10

    .line 232
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/E1;->s()Lcom/android/tools/r8/internal/ek0;

    move-result-object v2

    .line 233
    iget-object v2, v2, Lcom/android/tools/r8/internal/ek0;->b:Lcom/android/tools/r8/graph/l1;

    if-ne v2, v11, :cond_10

    return-object v9

    .line 234
    :cond_10
    iget-object v2, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Lcom/android/tools/r8/internal/ok0;->b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z

    move-result v2

    if-eqz v2, :cond_14

    .line 235
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v2

    .line 238
    iget-object v3, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 239
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v4

    invoke-virtual {v1, v3, v4, v0, v10}, Lcom/android/tools/r8/internal/ok0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/xt0;Lcom/android/tools/r8/internal/UT;)[Lcom/android/tools/r8/internal/rD;

    move-result-object v1

    .line 240
    invoke-static {v1}, Lcom/android/tools/r8/internal/U3;->b([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/rD;

    .line 241
    invoke-virtual/range {p5 .. p5}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v4

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    move-object/from16 v5, p2

    invoke-virtual {v4, v3, v5}, Lcom/android/tools/r8/internal/vt0;->a(Lcom/android/tools/r8/internal/vt0;Ljava/util/Set;)V

    .line 242
    instance-of v3, v10, Lcom/android/tools/r8/internal/VC;

    if-eqz v3, :cond_11

    .line 243
    iget-object v3, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-interface {v9, v3, v2}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z

    goto :goto_5

    .line 245
    :cond_11
    sget-boolean v2, Lcom/android/tools/r8/internal/W80;->d:Z

    if-nez v2, :cond_13

    if-eqz v14, :cond_12

    goto :goto_4

    :cond_12
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 246
    :cond_13
    :goto_4
    iget-object v2, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 247
    invoke-virtual {v12}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    .line 248
    invoke-interface {v9, v2, v0, v3}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/graph/J2;)Z

    .line 253
    :goto_5
    iget-object v2, v8, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 255
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v2

    move-object/from16 v3, p3

    .line 256
    invoke-interface {v9, v0, v3, v1, v2}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/O5;[Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/internal/uD;

    move-result-object v0

    .line 259
    sget-object v1, Lcom/android/tools/r8/internal/W80;->c:Lcom/android/tools/r8/internal/p20;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 260
    monitor-enter v13

    .line 261
    :try_start_0
    iget-object v1, v13, Lcom/android/tools/r8/graph/g1;->l:Lcom/android/tools/r8/internal/yv;

    invoke-interface {v1}, Lcom/android/tools/r8/internal/aU;->a()Lcom/android/tools/r8/internal/aU;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/iX;

    .line 262
    iput-object v1, v13, Lcom/android/tools/r8/graph/g1;->l:Lcom/android/tools/r8/internal/yv;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v13

    .line 263
    iget v2, v1, Lcom/android/tools/r8/internal/iX;->b:I

    or-int/lit8 v2, v2, 0x4

    .line 264
    iput v2, v1, Lcom/android/tools/r8/internal/iX;->b:I

    goto :goto_6

    :catchall_0
    move-exception v0

    .line 265
    monitor-exit v13

    throw v0

    :cond_14
    move-object v0, v9

    :goto_6
    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/jD;)V
    .locals 2

    .line 267
    iget-object v0, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {p3}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object p3

    invoke-virtual {v0, p3}, Lcom/android/tools/r8/graph/j;->c(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/z3;

    move-result-object p3

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/z3;->p()Lcom/android/tools/r8/graph/F0;

    move-result-object p3

    if-eqz p3, :cond_3

    .line 268
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/F0;->w()Lcom/android/tools/r8/graph/h3;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    .line 269
    :cond_0
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 270
    iget-object v1, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/y;)Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v0, p3}, Lcom/android/tools/r8/shaking/i;->a(Lcom/android/tools/r8/graph/F0;)Z

    move-result v0

    if-nez v0, :cond_1

    goto :goto_0

    .line 275
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 276
    iget-object v1, v0, Lcom/android/tools/r8/graph/y;->e:Lcom/android/tools/r8/shaking/l;

    .line 277
    invoke-virtual {v1, v0, p3}, Lcom/android/tools/r8/shaking/l;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/F0;)Z

    move-result p3

    if-eqz p3, :cond_3

    .line 278
    iget-object p3, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    invoke-interface {p2, p3, p1}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z

    goto :goto_1

    .line 279
    :cond_2
    :goto_0
    iget-object p3, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    invoke-interface {p2, p3, p1}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z

    :cond_3
    :goto_1
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/jm0;)V
    .locals 2

    .line 280
    iget-object v0, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {p3}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object p3

    invoke-virtual {v0, p3}, Lcom/android/tools/r8/graph/j;->c(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/z3;

    move-result-object p3

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/z3;->p()Lcom/android/tools/r8/graph/F0;

    move-result-object p3

    if-eqz p3, :cond_3

    .line 281
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/F0;->w()Lcom/android/tools/r8/graph/h3;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_1

    .line 282
    :cond_0
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 283
    iget-object v1, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/y;)Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v0, p3}, Lcom/android/tools/r8/shaking/i;->a(Lcom/android/tools/r8/graph/F0;)Z

    move-result v0

    if-nez v0, :cond_1

    goto :goto_0

    .line 289
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 290
    iget-object v1, v0, Lcom/android/tools/r8/graph/y;->e:Lcom/android/tools/r8/shaking/l;

    .line 291
    invoke-virtual {v1, v0, p3}, Lcom/android/tools/r8/shaking/l;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/F0;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 292
    iget-object v0, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 293
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object p3

    .line 294
    invoke-interface {p2, v0, p1, p3}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/graph/J2;)Z

    goto :goto_1

    .line 295
    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 296
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object p3

    .line 297
    invoke-interface {p2, v0, p1, p3}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/graph/J2;)Z

    :cond_3
    :goto_1
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/ir/optimize/a;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/K3;)V
    .locals 5

    .line 1
    invoke-virtual {p5}, Lcom/android/tools/r8/internal/G3;->K2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/sr0;->r()Z

    move-result v1

    if-nez v1, :cond_0

    return-void

    .line 7
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/sr0;->a()Lcom/android/tools/r8/internal/T3;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/T3;->R()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/sr0;->w()Z

    move-result v1

    if-nez v1, :cond_1

    return-void

    :cond_1
    const/4 v1, 0x0

    .line 14
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object v2

    .line 15
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Dd;->Q()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    iget-object v4, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v3, v4}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/y;)Z

    move-result v3

    if-eqz v3, :cond_2

    const/4 v1, 0x1

    goto :goto_0

    .line 17
    :cond_2
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Dd;->R()Lcom/android/tools/r8/internal/fH;

    move-result-object v3

    .line 18
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/fH;->c()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    if-eqz v3, :cond_3

    .line 19
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Dd;->R()Lcom/android/tools/r8/internal/fH;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/fH;->c()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    iget-object v2, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/y;)Z

    move-result v1

    :cond_3
    :goto_0
    if-nez v1, :cond_4

    return-void

    .line 27
    :cond_4
    invoke-virtual {p5}, Lcom/android/tools/r8/internal/rD;->b()Lcom/android/tools/r8/internal/K5;

    move-result-object v1

    .line 30
    iget-object v2, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 31
    iget-object v2, v2, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 32
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 33
    sget-boolean v2, Lcom/android/tools/r8/internal/F1;->f:Z

    if-nez v2, :cond_6

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v0

    if-eqz v0, :cond_5

    goto :goto_1

    :cond_5
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 34
    :cond_6
    :goto_1
    sget-object v0, Lcom/android/tools/r8/internal/gk0;->b:Lcom/android/tools/r8/internal/gk0;

    iget-object v0, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 35
    sget-boolean v2, Lcom/android/tools/r8/internal/gk0;->c:Z

    if-nez v2, :cond_8

    invoke-interface {p5}, Lcom/android/tools/r8/internal/UT;->a()Lcom/android/tools/r8/internal/sr0;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v2

    if-eqz v2, :cond_7

    goto :goto_2

    :cond_7
    new-instance p1, Ljava/lang/AssertionError;

    invoke-interface {p5}, Lcom/android/tools/r8/internal/UT;->a()Lcom/android/tools/r8/internal/sr0;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p1

    .line 36
    :cond_8
    :goto_2
    sget-boolean v2, Lcom/android/tools/r8/internal/Sg;->k:Z

    .line 37
    new-instance v2, Lcom/android/tools/r8/internal/Rg;

    invoke-direct {v2}, Lcom/android/tools/r8/internal/Rg;-><init>()V

    .line 38
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->m()Lcom/android/tools/r8/internal/yb0;

    move-result-object v3

    invoke-interface {p5}, Lcom/android/tools/r8/internal/UT;->o()Lcom/android/tools/r8/graph/j0;

    move-result-object v4

    invoke-virtual {v2, p1, v3, v4}, Lcom/android/tools/r8/internal/kD;->a(Lcom/android/tools/r8/internal/xt0;Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/j0;)Lcom/android/tools/r8/internal/kD;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/Rg;

    .line 39
    invoke-interface {p5}, Lcom/android/tools/r8/internal/UT;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v3

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    invoke-virtual {v2, v3, v0}, Lcom/android/tools/r8/internal/kD;->a(Lcom/android/tools/r8/internal/B40;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/internal/kD;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/Rg;

    .line 40
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Rg;->c()Lcom/android/tools/r8/internal/Sg;

    move-result-object v0

    .line 41
    invoke-virtual {p5}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/vt0;->a()Lcom/android/tools/r8/ir/optimize/a;

    move-result-object v2

    invoke-interface {p2, v2}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 42
    invoke-virtual {p5}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object p2

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object p5

    invoke-virtual {p2, p5}, Lcom/android/tools/r8/internal/vt0;->f(Lcom/android/tools/r8/internal/vt0;)V

    .line 45
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/K5;->x()Z

    move-result p2

    if-eqz p2, :cond_9

    .line 46
    iget-object p2, p0, Lcom/android/tools/r8/internal/pU;->a:Lcom/android/tools/r8/graph/y;

    .line 47
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p2

    invoke-interface {p4, p1, p3, p2}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/internal/K5;

    move-result-object p2

    .line 48
    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/K5;->a(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/N5;

    move-result-object p1

    .line 49
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/N5;->f(Lcom/android/tools/r8/internal/rD;)V

    goto :goto_3

    .line 51
    :cond_9
    invoke-interface {p4, v0}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    :goto_3
    return-void
.end method
