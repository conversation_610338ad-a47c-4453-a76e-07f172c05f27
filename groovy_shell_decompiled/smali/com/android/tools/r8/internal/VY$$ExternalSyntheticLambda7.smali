.class public final synthetic Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda7;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/synthesis/L;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/VY;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/D5;

.field public final synthetic f$2:Lcom/android/tools/r8/synthesis/M;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/VY;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/synthesis/M;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda7;->f$0:Lcom/android/tools/r8/internal/VY;

    iput-object p2, p0, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda7;->f$1:Lcom/android/tools/r8/graph/D5;

    iput-object p3, p0, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda7;->f$2:Lcom/android/tools/r8/synthesis/M;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/i0;
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda7;->f$0:Lcom/android/tools/r8/internal/VY;

    iget-object v1, p0, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda7;->f$1:Lcom/android/tools/r8/graph/D5;

    iget-object v2, p0, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda7;->f$2:Lcom/android/tools/r8/synthesis/M;

    invoke-virtual {v0, v1, v2, p1}, Lcom/android/tools/r8/internal/VY;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/synthesis/M;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/i0;

    move-result-object p1

    return-object p1
.end method
