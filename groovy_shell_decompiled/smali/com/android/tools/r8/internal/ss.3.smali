.class public final Lcom/android/tools/r8/internal/ss;
.super Lcom/android/tools/r8/internal/z50;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/z50;-><init>(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/E1;)Z
    .locals 0

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    instance-of p1, p1, Lcom/android/tools/r8/internal/Gj0;

    return p1
.end method

.method public final b()Lcom/android/tools/r8/graph/x2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/z50;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->A4:Lcom/android/tools/r8/graph/J1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/J1;->e:Lcom/android/tools/r8/graph/x2;

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/graph/x2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/z50;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->A4:Lcom/android/tools/r8/graph/J1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/J1;->b:Lcom/android/tools/r8/graph/x2;

    return-object v0
.end method

.method public final getType()Lcom/android/tools/r8/graph/J2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/z50;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->R1:Lcom/android/tools/r8/graph/J2;

    return-object v0
.end method
