.class public Lcom/android/tools/r8/internal/sn;
.super Lcom/android/tools/r8/internal/jo;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/bk0;


# direct methods
.method public constructor <init>(II)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/jo;-><init>(II)V

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Zo;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/jo;-><init>(ILcom/android/tools/r8/internal/Zo;)V

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    .line 4
    iget-char v0, p0, Lcom/android/tools/r8/internal/jo;->g:C

    shl-int/lit8 v0, v0, 0x10

    return v0
.end method

.method public final a(Lcom/android/tools/r8/internal/Th0;)Ljava/lang/String;
    .locals 4

    .line 5
    iget-short p1, p0, Lcom/android/tools/r8/internal/jo;->f:S

    .line 6
    iget-char v0, p0, Lcom/android/tools/r8/internal/jo;->g:C

    shl-int/lit8 v0, v0, 0x10

    const/16 v1, 0x8

    .line 7
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/Sn0;->a(II)Ljava/lang/String;

    move-result-object v0

    .line 8
    iget-char v1, p0, Lcom/android/tools/r8/internal/jo;->g:C

    shl-int/lit8 v1, v1, 0x10

    .line 9
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "v"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, ", "

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "  # "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 10
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Yo;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/Vz;)V
    .locals 5

    .line 1
    iget-char v0, p0, Lcom/android/tools/r8/internal/jo;->g:C

    shl-int/lit8 v0, v0, 0x10

    if-nez v0, :cond_0

    .line 2
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->p()Lcom/android/tools/r8/internal/Lp0;

    move-result-object v1

    goto :goto_0

    :cond_0
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->o()Lcom/android/tools/r8/internal/jk0;

    move-result-object v1

    .line 3
    :goto_0
    iget-short v2, p0, Lcom/android/tools/r8/internal/jo;->f:S

    int-to-long v3, v0

    invoke-virtual {p1, v1, v2, v3, v4}, Lcom/android/tools/r8/internal/Vz;->a(Lcom/android/tools/r8/internal/sr0;IJ)V

    return-void
.end method

.method public final b(Lcom/android/tools/r8/internal/Th0;)Ljava/lang/String;
    .locals 4

    .line 1
    iget-short p1, p0, Lcom/android/tools/r8/internal/jo;->f:S

    .line 2
    iget-char v0, p0, Lcom/android/tools/r8/internal/jo;->g:C

    shl-int/lit8 v0, v0, 0x10

    const/16 v1, 0x8

    .line 3
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/Sn0;->a(II)Ljava/lang/String;

    move-result-object v0

    .line 4
    iget-char v1, p0, Lcom/android/tools/r8/internal/jo;->g:C

    shl-int/lit8 v1, v1, 0x10

    .line 5
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "v"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, ", "

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " ("

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ")"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 6
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Yo;->b(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public final l()Ljava/lang/String;
    .locals 1

    const-string v0, "ConstHigh16"

    return-object v0
.end method

.method public final s()I
    .locals 1

    const/16 v0, 0x15

    return v0
.end method

.method public final v()Ljava/lang/String;
    .locals 1

    const-string v0, "const/high16"

    return-object v0
.end method
