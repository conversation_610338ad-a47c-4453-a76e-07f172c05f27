.class public abstract Lcom/android/tools/r8/internal/sV;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Lcom/android/tools/r8/internal/sf;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract a(Lcom/android/tools/r8/graph/D5;)Z
.end method

.method public b()Lcom/android/tools/r8/internal/P40;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract b(Lcom/android/tools/r8/graph/D5;)V
.end method

.method public c()Lcom/android/tools/r8/internal/w50;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract c(Lcom/android/tools/r8/graph/D5;)Z
.end method

.method public abstract d()Lcom/android/tools/r8/internal/q8;
.end method

.method public abstract e()Lcom/android/tools/r8/internal/uV;
.end method

.method public f()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/ei;

    return v0
.end method

.method public g()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method
