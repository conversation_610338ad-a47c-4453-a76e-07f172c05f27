.class public final synthetic Lcom/android/tools/r8/internal/MB$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/IntFunction;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/cB;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/cB;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/MB$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/cB;

    return-void
.end method


# virtual methods
.method public final apply(I)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/MB$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/cB;

    invoke-interface {v0, p1}, <PERSON>java/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
