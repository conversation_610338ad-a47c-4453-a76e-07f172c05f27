.class public final Lcom/android/tools/r8/internal/uH;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:I

.field public final b:Lcom/android/tools/r8/graph/J2;

.field public final c:Lcom/android/tools/r8/graph/J2;


# direct methods
.method public constructor <init>(ILcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Lcom/android/tools/r8/internal/uH;->a:I

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/uH;->b:Lcom/android/tools/r8/graph/J2;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/internal/uH;->c:Lcom/android/tools/r8/graph/J2;

    return-void
.end method
