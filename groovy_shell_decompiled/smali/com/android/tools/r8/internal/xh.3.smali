.class public final Lcom/android/tools/r8/internal/xh;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Ljava/util/Map;

.field public final b:Ljava/util/List;


# direct methods
.method public constructor <init>(Ljava/util/List;Ljava/util/Map;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/xh;->a:Ljava/util/Map;

    .line 4
    iput-object p1, p0, Lcom/android/tools/r8/internal/xh;->b:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Hr0;)Lcom/android/tools/r8/internal/A10;
    .locals 6

    .line 1
    iget-object v0, p1, Lcom/android/tools/r8/internal/Hr0;->b:Ljava/lang/reflect/Type;

    .line 2
    iget-object p1, p1, Lcom/android/tools/r8/internal/Hr0;->a:Ljava/lang/Class;

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/internal/xh;->a:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    if-nez v1, :cond_15

    .line 14
    iget-object v1, p0, Lcom/android/tools/r8/internal/xh;->a:Ljava/util/Map;

    .line 15
    invoke-interface {v1, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    if-nez v1, :cond_14

    .line 16
    const-class v1, Ljava/util/EnumSet;

    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    .line 17
    new-instance v1, Lcom/android/tools/r8/internal/th;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/internal/th;-><init>(Ljava/lang/reflect/Type;)V

    goto :goto_0

    .line 36
    :cond_0
    const-class v1, Ljava/util/EnumMap;

    if-ne p1, v1, :cond_1

    .line 37
    new-instance v1, Lcom/android/tools/r8/internal/uh;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/internal/uh;-><init>(Ljava/lang/reflect/Type;)V

    goto :goto_0

    :cond_1
    move-object v1, v2

    :goto_0
    if-eqz v1, :cond_2

    return-object v1

    .line 38
    :cond_2
    iget-object v1, p0, Lcom/android/tools/r8/internal/xh;->b:Ljava/util/List;

    .line 39
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-nez v3, :cond_13

    .line 40
    invoke-virtual {p1}, Ljava/lang/Class;->getModifiers()I

    move-result v1

    invoke-static {v1}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result v1

    if-eqz v1, :cond_3

    goto :goto_2

    .line 46
    :cond_3
    :try_start_0
    invoke-virtual {p1, v2}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v1
    :try_end_0
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_1

    .line 70
    sget-object v3, Lcom/android/tools/r8/internal/Zb0;->a:Lcom/android/tools/r8/internal/Wb0;

    const/4 v3, 0x1

    .line 71
    :try_start_1
    invoke-virtual {v1, v3}, Ljava/lang/reflect/Constructor;->setAccessible(Z)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    move-object v3, v2

    goto :goto_1

    :catch_0
    move-exception v3

    .line 74
    new-instance v4, Ljava/lang/StringBuilder;

    const-string v5, "Failed making constructor \'"

    invoke-direct {v4, v5}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-static {v1}, Lcom/android/tools/r8/internal/Zb0;->a(Ljava/lang/reflect/Constructor;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, "\' accessible; either increase its visibility or write a custom InstanceCreator or TypeAdapter for its declaring type: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    .line 77
    invoke-virtual {v3}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    :goto_1
    if-eqz v3, :cond_4

    .line 78
    new-instance v1, Lcom/android/tools/r8/internal/vh;

    invoke-direct {v1, v3}, Lcom/android/tools/r8/internal/vh;-><init>(Ljava/lang/String;)V

    goto :goto_3

    .line 90
    :cond_4
    new-instance v3, Lcom/android/tools/r8/internal/wh;

    invoke-direct {v3, v1}, Lcom/android/tools/r8/internal/wh;-><init>(Ljava/lang/reflect/Constructor;)V

    move-object v1, v3

    goto :goto_3

    :catch_1
    :goto_2
    move-object v1, v2

    :goto_3
    if-eqz v1, :cond_5

    return-object v1

    .line 91
    :cond_5
    const-class v1, Ljava/util/Collection;

    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_9

    .line 92
    const-class v0, Ljava/util/SortedSet;

    invoke-virtual {v0, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 93
    new-instance v0, Lcom/android/tools/r8/internal/ih;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/ih;-><init>()V

    goto/16 :goto_4

    .line 98
    :cond_6
    const-class v0, Ljava/util/Set;

    invoke-virtual {v0, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_7

    .line 99
    new-instance v0, Lcom/android/tools/r8/internal/jh;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/jh;-><init>()V

    goto/16 :goto_4

    .line 104
    :cond_7
    const-class v0, Ljava/util/Queue;

    invoke-virtual {v0, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_8

    .line 105
    new-instance v0, Lcom/android/tools/r8/internal/kh;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/kh;-><init>()V

    goto :goto_4

    .line 111
    :cond_8
    new-instance v0, Lcom/android/tools/r8/internal/lh;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/lh;-><init>()V

    goto :goto_4

    .line 119
    :cond_9
    const-class v1, Ljava/util/Map;

    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_e

    .line 120
    const-class v1, Ljava/util/concurrent/ConcurrentNavigableMap;

    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_a

    .line 121
    new-instance v0, Lcom/android/tools/r8/internal/mh;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/mh;-><init>()V

    goto :goto_4

    .line 126
    :cond_a
    const-class v1, Ljava/util/concurrent/ConcurrentMap;

    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_b

    .line 127
    new-instance v0, Lcom/android/tools/r8/internal/nh;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/nh;-><init>()V

    goto :goto_4

    .line 132
    :cond_b
    const-class v1, Ljava/util/SortedMap;

    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-eqz v1, :cond_c

    .line 133
    new-instance v0, Lcom/android/tools/r8/internal/oh;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/oh;-><init>()V

    goto :goto_4

    .line 138
    :cond_c
    instance-of v1, v0, Ljava/lang/reflect/ParameterizedType;

    if-eqz v1, :cond_d

    const-class v1, Ljava/lang/String;

    check-cast v0, Ljava/lang/reflect/ParameterizedType;

    .line 139
    invoke-interface {v0}, Ljava/lang/reflect/ParameterizedType;->getActualTypeArguments()[Ljava/lang/reflect/Type;

    move-result-object v0

    const/4 v3, 0x0

    aget-object v0, v0, v3

    .line 140
    new-instance v3, Lcom/android/tools/r8/internal/Hr0;

    invoke-direct {v3, v0}, Lcom/android/tools/r8/internal/Hr0;-><init>(Ljava/lang/reflect/Type;)V

    .line 141
    iget-object v0, v3, Lcom/android/tools/r8/internal/Hr0;->a:Ljava/lang/Class;

    .line 142
    invoke-virtual {v1, v0}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    if-nez v0, :cond_d

    .line 144
    new-instance v0, Lcom/android/tools/r8/internal/ph;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/ph;-><init>()V

    goto :goto_4

    .line 150
    :cond_d
    new-instance v0, Lcom/android/tools/r8/internal/qh;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/qh;-><init>()V

    goto :goto_4

    :cond_e
    move-object v0, v2

    :goto_4
    if-eqz v0, :cond_f

    return-object v0

    .line 151
    :cond_f
    invoke-virtual {p1}, Ljava/lang/Class;->getModifiers()I

    move-result v0

    .line 152
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isInterface(I)Z

    move-result v1

    if-eqz v1, :cond_10

    .line 154
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Interfaces can\'t be instantiated! Register an InstanceCreator or a TypeAdapter for this type. Interface name: "

    invoke-virtual {v1, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    goto :goto_5

    .line 156
    :cond_10
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    move-result v0

    if-eqz v0, :cond_11

    .line 158
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Abstract classes can\'t be instantiated! Register an InstanceCreator or a TypeAdapter for this type. Class name: "

    invoke-virtual {v1, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    :cond_11
    :goto_5
    if-eqz v2, :cond_12

    .line 159
    new-instance p1, Lcom/android/tools/r8/internal/sh;

    invoke-direct {p1, v2}, Lcom/android/tools/r8/internal/sh;-><init>(Ljava/lang/String;)V

    return-object p1

    .line 160
    :cond_12
    new-instance v0, Lcom/android/tools/r8/internal/rh;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/rh;-><init>(Ljava/lang/Class;)V

    return-object v0

    .line 161
    :cond_13
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    .line 162
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 163
    new-instance p1, Ljava/lang/ClassCastException;

    invoke-direct {p1}, Ljava/lang/ClassCastException;-><init>()V

    throw p1

    .line 164
    :cond_14
    new-instance p1, Ljava/lang/ClassCastException;

    invoke-direct {p1}, Ljava/lang/ClassCastException;-><init>()V

    throw p1

    .line 165
    :cond_15
    new-instance p1, Ljava/lang/ClassCastException;

    invoke-direct {p1}, Ljava/lang/ClassCastException;-><init>()V

    throw p1
.end method

.method public final toString()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/xh;->a:Ljava/util/Map;

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
