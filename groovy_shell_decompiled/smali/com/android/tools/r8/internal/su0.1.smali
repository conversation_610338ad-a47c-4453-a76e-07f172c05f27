.class public final Lcom/android/tools/r8/internal/su0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z


# instance fields
.field public final a:Lcom/android/tools/r8/internal/su0;

.field public final b:Lcom/android/tools/r8/graph/D5;

.field public c:Ljava/util/Set;

.field public d:Ljava/util/List;

.field public e:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/tu0;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/su0;->f:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/su0;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/su0;->c:Ljava/util/Set;

    .line 3
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/su0;->d:Ljava/util/List;

    const/4 v0, 0x0

    .line 4
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/su0;->e:Z

    .line 11
    sget-boolean v0, Lcom/android/tools/r8/internal/su0;->f:Z

    if-nez v0, :cond_1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 12
    :cond_1
    :goto_0
    iput-object p2, p0, Lcom/android/tools/r8/internal/su0;->a:Lcom/android/tools/r8/internal/su0;

    .line 13
    iput-object p1, p0, Lcom/android/tools/r8/internal/su0;->b:Lcom/android/tools/r8/graph/D5;

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/su0;
    .locals 1

    .line 12
    iget-object v0, p0, Lcom/android/tools/r8/internal/su0;->a:Lcom/android/tools/r8/internal/su0;

    if-eqz v0, :cond_0

    .line 13
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/su0;->a()Lcom/android/tools/r8/internal/su0;

    move-result-object v0

    goto :goto_0

    :cond_0
    move-object v0, p0

    :goto_0
    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/internal/su0;)V
    .locals 3

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/su0;->f:Z

    if-nez v0, :cond_1

    .line 2
    iget-object v1, p1, Lcom/android/tools/r8/internal/su0;->b:Lcom/android/tools/r8/graph/D5;

    .line 3
    iget-object v2, p0, Lcom/android/tools/r8/internal/su0;->b:Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/H0;->b(Lcom/android/tools/r8/graph/H0;)Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    if-nez v0, :cond_3

    .line 4
    iget-object v0, p1, Lcom/android/tools/r8/internal/su0;->b:Lcom/android/tools/r8/graph/D5;

    .line 5
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    iget-object v1, p0, Lcom/android/tools/r8/internal/su0;->b:Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/x2;->d(Lcom/android/tools/r8/graph/x2;)Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_1

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 6
    :cond_3
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/su0;->c:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_4

    .line 7
    invoke-static {}, Lcom/android/tools/r8/internal/kj0;->c()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/su0;->c:Ljava/util/Set;

    .line 9
    :cond_4
    iget-object v0, p0, Lcom/android/tools/r8/internal/su0;->c:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/internal/su0;->a:Lcom/android/tools/r8/internal/su0;

    if-eqz v0, :cond_5

    .line 11
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/su0;->a(Lcom/android/tools/r8/internal/su0;)V

    :cond_5
    return-void
.end method

.method public final a(Ljava/util/function/Consumer;)V
    .locals 1

    .line 14
    invoke-interface {p1, p0}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/internal/su0;->c:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method
