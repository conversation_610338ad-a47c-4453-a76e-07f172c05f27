.class public Lcom/android/tools/r8/internal/Te;
.super Lcom/android/tools/r8/internal/Se;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final b:Lcom/android/tools/r8/naming/r0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/naming/r0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Se;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/Te;->b:Lcom/android/tools/r8/naming/r0;

    return-void
.end method


# virtual methods
.method public a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)I
    .locals 1

    if-ne p1, p2, :cond_0

    const/4 p1, 0x0

    return p1

    .line 1
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Te;->b:Lcom/android/tools/r8/naming/r0;

    .line 3
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/naming/r0;->c(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    iget-object v0, p0, Lcom/android/tools/r8/internal/Te;->b:Lcom/android/tools/r8/naming/r0;

    .line 4
    invoke-virtual {v0, p2}, Lcom/android/tools/r8/naming/r0;->c(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/I2;

    move-result-object p2

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Se;->a(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;)I

    move-result p1

    return p1
.end method

.method public a(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/l1;)I
    .locals 2

    if-ne p1, p2, :cond_0

    const/4 p1, 0x0

    return p1

    .line 6
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    iget-object v1, p2, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 7
    invoke-virtual {p0, v0, v1}, Lcom/android/tools/r8/internal/Te;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)I

    move-result v0

    if-eqz v0, :cond_1

    return v0

    .line 8
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Te;->b:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/naming/r0;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/I2;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/Te;->b:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v1, p2}, Lcom/android/tools/r8/naming/r0;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/I2;

    move-result-object v1

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 9
    invoke-virtual {p0, v0, v1}, Lcom/android/tools/r8/internal/Se;->a(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;)I

    move-result v0

    if-eqz v0, :cond_2

    return v0

    .line 10
    :cond_2
    iget-object p1, p1, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    iget-object p2, p2, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 11
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Te;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)I

    move-result p1

    return p1
.end method

.method public a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)I
    .locals 2

    if-ne p1, p2, :cond_0

    const/4 p1, 0x0

    return p1

    .line 12
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    iget-object v1, p2, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 13
    invoke-virtual {p0, v0, v1}, Lcom/android/tools/r8/internal/Te;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)I

    move-result v0

    if-eqz v0, :cond_1

    return v0

    .line 14
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Te;->b:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/naming/r0;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/I2;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/Te;->b:Lcom/android/tools/r8/naming/r0;

    invoke-virtual {v1, p2}, Lcom/android/tools/r8/naming/r0;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/I2;

    move-result-object v1

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 15
    invoke-virtual {p0, v0, v1}, Lcom/android/tools/r8/internal/Se;->a(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;)I

    move-result v0

    if-eqz v0, :cond_2

    return v0

    .line 16
    :cond_2
    iget-object p1, p1, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object p2, p2, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    invoke-interface {p1, p2, p0}, Lcom/android/tools/r8/internal/ho0;->a(Lcom/android/tools/r8/internal/ho0;Lcom/android/tools/r8/internal/Qe;)I

    move-result p1

    return p1
.end method
