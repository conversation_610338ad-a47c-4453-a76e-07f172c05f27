.class public abstract Lcom/android/tools/r8/internal/sr0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic a:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 16
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/qZ;)Lcom/android/tools/r8/internal/Dd;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/tools/r8/graph/y<",
            "*>;",
            "Lcom/android/tools/r8/internal/qZ;",
            ")",
            "Lcom/android/tools/r8/internal/Dd;"
        }
    .end annotation

    .line 57
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->Z1:Lcom/android/tools/r8/graph/J2;

    invoke-static {v0, p1, p0}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object p0

    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/tools/r8/graph/J2;",
            "Lcom/android/tools/r8/internal/qZ;",
            "Lcom/android/tools/r8/graph/y<",
            "*>;)",
            "Lcom/android/tools/r8/internal/sr0;"
        }
    .end annotation

    .line 58
    sget-object v0, Lcom/android/tools/r8/graph/B1;->p6:Lcom/android/tools/r8/graph/J2;

    if-ne p0, v0, :cond_2

    .line 59
    sget-boolean p0, Lcom/android/tools/r8/internal/sr0;->a:Z

    if-nez p0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/qZ;->d()Z

    move-result p0

    if-nez p0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 60
    :cond_1
    :goto_0
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->m()Lcom/android/tools/r8/internal/yb0;

    move-result-object p0

    goto :goto_2

    .line 62
    :cond_2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 63
    sget-boolean p1, Lcom/android/tools/r8/internal/C50;->b:Z

    if-nez p1, :cond_4

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result p1

    if-eqz p1, :cond_3

    goto :goto_1

    :cond_3
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 64
    :cond_4
    :goto_1
    iget-object p0, p0, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    iget-object p0, p0, Lcom/android/tools/r8/graph/I2;->f:[B

    const/4 p1, 0x0

    aget-byte p0, p0, p1

    int-to-char p0, p0

    invoke-static {p0, p1}, Lcom/android/tools/r8/internal/C50;->a(CZ)Lcom/android/tools/r8/internal/C50;

    move-result-object p0

    goto :goto_2

    .line 65
    :cond_5
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    invoke-virtual {v0, p0, p1, p2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/yb0;

    move-result-object p0

    :goto_2
    return-object p0
.end method

.method public static a(Ljava/lang/Iterable;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Iterable<",
            "Lcom/android/tools/r8/internal/sr0;",
            ">;",
            "Lcom/android/tools/r8/graph/y<",
            "*>;)",
            "Lcom/android/tools/r8/internal/sr0;"
        }
    .end annotation

    .line 45
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->f()Lcom/android/tools/r8/internal/n7;

    move-result-object v0

    .line 46
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/sr0;

    .line 47
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public static e()Lcom/android/tools/r8/internal/b7;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/b7;->d:Lcom/android/tools/r8/internal/b7;

    return-object v0
.end method

.method public static f()Lcom/android/tools/r8/internal/n7;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/n7;->b:Lcom/android/tools/r8/internal/n7;

    return-object v0
.end method

.method public static g()Lcom/android/tools/r8/internal/a8;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/a8;->d:Lcom/android/tools/r8/internal/a8;

    return-object v0
.end method

.method public static h()Lcom/android/tools/r8/internal/Ib;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Ib;->d:Lcom/android/tools/r8/internal/Ib;

    return-object v0
.end method

.method public static i()Lcom/android/tools/r8/internal/ts;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/ts;->d:Lcom/android/tools/r8/internal/ts;

    return-object v0
.end method

.method public static j()Lcom/android/tools/r8/internal/Hw;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Hw;->d:Lcom/android/tools/r8/internal/Hw;

    return-object v0
.end method

.method public static k()Lcom/android/tools/r8/internal/dH;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/dH;->d:Lcom/android/tools/r8/internal/dH;

    return-object v0
.end method

.method public static l()Lcom/android/tools/r8/internal/MS;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/MS;->d:Lcom/android/tools/r8/internal/MS;

    return-object v0
.end method

.method public static m()Lcom/android/tools/r8/internal/yb0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/yb0;->c:Lcom/android/tools/r8/internal/xb0;

    return-object v0
.end method

.method public static n()Lcom/android/tools/r8/internal/qj0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/qj0;->d:Lcom/android/tools/r8/internal/qj0;

    return-object v0
.end method

.method public static o()Lcom/android/tools/r8/internal/jk0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/jk0;->c:Lcom/android/tools/r8/internal/jk0;

    return-object v0
.end method

.method public static p()Lcom/android/tools/r8/internal/Lp0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Lp0;->b:Lcom/android/tools/r8/internal/Lp0;

    return-object v0
.end method

.method public static q()Lcom/android/tools/r8/internal/Eu0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Eu0;->c:Lcom/android/tools/r8/internal/Eu0;

    return-object v0
.end method


# virtual methods
.method public final A()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/b7;

    if-nez v0, :cond_1

    .line 2
    instance-of v0, p0, Lcom/android/tools/r8/internal/a8;

    if-nez v0, :cond_1

    .line 3
    instance-of v0, p0, Lcom/android/tools/r8/internal/qj0;

    if-nez v0, :cond_1

    .line 4
    instance-of v0, p0, Lcom/android/tools/r8/internal/Ib;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public B()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/Hw;

    return v0
.end method

.method public C()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/dH;

    return v0
.end method

.method public D()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/MS;

    return v0
.end method

.method public E()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/xb0;

    return v0
.end method

.method public F()Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->N()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qZ;->g()Z

    move-result v0

    return v0
.end method

.method public final G()Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->r()Z

    move-result v0

    if-nez v0, :cond_1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->w()Z

    move-result v0

    if-nez v0, :cond_1

    .line 3
    instance-of v0, p0, Lcom/android/tools/r8/internal/xb0;

    if-nez v0, :cond_1

    .line 4
    instance-of v0, p0, Lcom/android/tools/r8/internal/dH;

    if-nez v0, :cond_1

    .line 5
    instance-of v0, p0, Lcom/android/tools/r8/internal/Hw;

    if-nez v0, :cond_1

    .line 6
    instance-of v0, p0, Lcom/android/tools/r8/internal/MS;

    if-nez v0, :cond_1

    .line 7
    instance-of v0, p0, Lcom/android/tools/r8/internal/ts;

    if-nez v0, :cond_1

    .line 8
    instance-of v0, p0, Lcom/android/tools/r8/internal/n7;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public H()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public I()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public J()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/qj0;

    return v0
.end method

.method public K()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public L()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/Lp0;

    return v0
.end method

.method public M()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public abstract N()Lcom/android/tools/r8/internal/qZ;
.end method

.method public O()I
    .locals 1

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/sr0;->a:Z

    if-nez v0, :cond_1

    .line 2
    instance-of v0, p0, Lcom/android/tools/r8/internal/n7;

    if-nez v0, :cond_0

    .line 3
    instance-of v0, p0, Lcom/android/tools/r8/internal/Lp0;

    if-nez v0, :cond_0

    goto :goto_0

    .line 4
    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_1
    :goto_0
    const/4 v0, 0x1

    return v0
.end method

.method public a()Lcom/android/tools/r8/internal/T3;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Ljava/util/Set;)Lcom/android/tools/r8/internal/sr0;
    .locals 1

    .line 15
    new-instance v0, Lcom/android/tools/r8/internal/sr0$$ExternalSyntheticLambda0;

    invoke-direct {v0, p2, p3}, Lcom/android/tools/r8/internal/sr0$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;)V

    invoke-virtual {p0, p1, v0, p4}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/y;Ljava/util/function/Function;Ljava/util/Set;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/TY;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/sr0;
    .locals 1

    .line 14
    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object v0

    invoke-virtual {p0, p1, p2, p3, v0}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Ljava/util/Set;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/sr0;
    .locals 2

    if-eq p0, p2, :cond_17

    .line 17
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 18
    instance-of v0, p2, Lcom/android/tools/r8/internal/n7;

    if-eqz v0, :cond_0

    goto/16 :goto_9

    .line 19
    :cond_0
    instance-of v0, p0, Lcom/android/tools/r8/internal/n7;

    if-eqz v0, :cond_1

    return-object p2

    .line 20
    :cond_1
    instance-of v0, p0, Lcom/android/tools/r8/internal/Lp0;

    if-nez v0, :cond_16

    instance-of v0, p2, Lcom/android/tools/r8/internal/Lp0;

    if-nez v0, :cond_16

    .line 21
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v0

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v1

    if-eq v0, v1, :cond_2

    goto/16 :goto_8

    .line 24
    :cond_2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v0

    if-eqz v0, :cond_d

    .line 25
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->c()Lcom/android/tools/r8/internal/C50;

    move-result-object p1

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->c()Lcom/android/tools/r8/internal/C50;

    move-result-object p2

    if-ne p1, p2, :cond_3

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    goto :goto_3

    .line 26
    :cond_3
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->K()Z

    move-result v0

    if-eqz v0, :cond_7

    .line 27
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->K()Z

    move-result p1

    if-eqz p1, :cond_4

    .line 28
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->o()Lcom/android/tools/r8/internal/jk0;

    move-result-object p1

    goto :goto_3

    .line 30
    :cond_4
    sget-boolean p1, Lcom/android/tools/r8/internal/C50;->b:Z

    if-nez p1, :cond_6

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->M()Z

    move-result p1

    if-eqz p1, :cond_5

    goto :goto_0

    :cond_5
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 31
    :cond_6
    :goto_0
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->p()Lcom/android/tools/r8/internal/Lp0;

    move-result-object p1

    goto :goto_3

    .line 33
    :cond_7
    sget-boolean v0, Lcom/android/tools/r8/internal/C50;->b:Z

    if-nez v0, :cond_9

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->M()Z

    move-result p1

    if-eqz p1, :cond_8

    goto :goto_1

    :cond_8
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 34
    :cond_9
    :goto_1
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->M()Z

    move-result p1

    if-eqz p1, :cond_a

    .line 35
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->q()Lcom/android/tools/r8/internal/Eu0;

    move-result-object p1

    goto :goto_3

    :cond_a
    if-nez v0, :cond_c

    .line 37
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->K()Z

    move-result p1

    if-eqz p1, :cond_b

    goto :goto_2

    :cond_b
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 38
    :cond_c
    :goto_2
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->p()Lcom/android/tools/r8/internal/Lp0;

    move-result-object p1

    :goto_3
    return-object p1

    .line 39
    :cond_d
    sget-boolean v0, Lcom/android/tools/r8/internal/sr0;->a:Z

    if-nez v0, :cond_f

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v1

    if-eqz v1, :cond_e

    goto :goto_4

    :cond_e
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_f
    :goto_4
    if-nez v0, :cond_11

    .line 40
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->G()Z

    move-result v1

    if-eqz v1, :cond_10

    goto :goto_5

    :cond_10
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_11
    :goto_5
    if-nez v0, :cond_13

    .line 41
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v1

    if-eqz v1, :cond_12

    goto :goto_6

    :cond_12
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_13
    :goto_6
    if-nez v0, :cond_15

    .line 42
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->G()Z

    move-result v0

    if-eqz v0, :cond_14

    goto :goto_7

    :cond_14
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 43
    :cond_15
    :goto_7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->d()Lcom/android/tools/r8/internal/yb0;

    move-result-object v0

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->d()Lcom/android/tools/r8/internal/yb0;

    move-result-object p2

    invoke-virtual {v0, p2, p1}, Lcom/android/tools/r8/internal/yb0;->a(Lcom/android/tools/r8/internal/yb0;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/yb0;

    move-result-object p1

    return-object p1

    .line 44
    :cond_16
    :goto_8
    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->p()Lcom/android/tools/r8/internal/Lp0;

    move-result-object p1

    return-object p1

    :cond_17
    :goto_9
    return-object p0
.end method

.method public a(Lcom/android/tools/r8/graph/y;Ljava/util/function/Function;Ljava/util/Set;)Lcom/android/tools/r8/internal/sr0;
    .locals 0

    return-object p0
.end method

.method public final a(Lcom/android/tools/r8/graph/B1;)Z
    .locals 0

    .line 56
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->Z1:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;)Z
    .locals 1

    .line 54
    sget-boolean v0, Lcom/android/tools/r8/internal/sr0;->a:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->L0()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 55
    :cond_1
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->w()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Dd;->Q()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    if-ne v0, p1, :cond_2

    const/4 p1, 0x1

    goto :goto_1

    :cond_2
    const/4 p1, 0x0

    :goto_1
    return p1
.end method

.method public a(Lcom/android/tools/r8/graph/y;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public final a(Lcom/android/tools/r8/internal/sr0;)Z
    .locals 3

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    return p1

    .line 1
    :cond_0
    instance-of v0, p0, Lcom/android/tools/r8/internal/n7;

    .line 2
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    instance-of v1, p1, Lcom/android/tools/r8/internal/n7;

    const/4 v2, 0x0

    if-eq v0, v1, :cond_1

    return v2

    .line 4
    :cond_1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v0

    if-nez v0, :cond_5

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_1

    .line 7
    :cond_2
    sget-boolean v0, Lcom/android/tools/r8/internal/sr0;->a:Z

    if-nez v0, :cond_4

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_0

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 9
    :cond_4
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->d()Lcom/android/tools/r8/internal/yb0;

    move-result-object v0

    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/yb0;->a(Lcom/android/tools/r8/internal/qZ;)Lcom/android/tools/r8/internal/yb0;

    move-result-object v0

    .line 11
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->d()Lcom/android/tools/r8/internal/yb0;

    move-result-object p1

    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/yb0;->a(Lcom/android/tools/r8/internal/qZ;)Lcom/android/tools/r8/internal/yb0;

    move-result-object p1

    .line 12
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/yb0;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    :cond_5
    :goto_1
    return v2
.end method

.method public a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/y;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/tools/r8/internal/sr0;",
            "Lcom/android/tools/r8/graph/y<",
            "*>;)Z"
        }
    .end annotation

    .line 48
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 49
    invoke-virtual {p0, p2, p1}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p2

    .line 50
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public a(Ljava/util/function/Predicate;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public b()Lcom/android/tools/r8/internal/Dd;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public b(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/y;)Z
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/tools/r8/internal/sr0;",
            "Lcom/android/tools/r8/graph/y<",
            "*>;)Z"
        }
    .end annotation

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p0, Lcom/android/tools/r8/internal/Lp0;

    if-eqz v1, :cond_1

    .line 2
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    instance-of p1, p1, Lcom/android/tools/r8/internal/Lp0;

    return p1

    .line 4
    :cond_1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    instance-of v1, p1, Lcom/android/tools/r8/internal/Lp0;

    if-eqz v1, :cond_2

    return v0

    .line 6
    :cond_2
    instance-of v1, p0, Lcom/android/tools/r8/internal/n7;

    if-eqz v1, :cond_3

    return v0

    .line 7
    :cond_3
    instance-of v0, p1, Lcom/android/tools/r8/internal/n7;

    if-eqz v0, :cond_4

    const/4 p1, 0x0

    return p1

    .line 8
    :cond_4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 10
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/y;)Z

    move-result p1

    return p1

    .line 12
    :cond_5
    sget-boolean v0, Lcom/android/tools/r8/internal/sr0;->a:Z

    if-nez v0, :cond_7

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v0

    if-eqz v0, :cond_6

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v0

    if-eqz v0, :cond_6

    goto :goto_0

    :cond_6
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 14
    :cond_7
    :goto_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->F()Z

    move-result v0

    if-eqz v0, :cond_8

    .line 15
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->d()Lcom/android/tools/r8/internal/yb0;

    move-result-object p1

    goto :goto_1

    .line 16
    :cond_8
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->d()Lcom/android/tools/r8/internal/yb0;

    move-result-object p1

    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/yb0;->a(Lcom/android/tools/r8/internal/qZ;)Lcom/android/tools/r8/internal/yb0;

    move-result-object p1

    .line 17
    :goto_1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/y;)Z

    move-result p1

    return p1
.end method

.method public c()Lcom/android/tools/r8/internal/C50;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public c(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/y;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/tools/r8/internal/sr0;",
            "Lcom/android/tools/r8/graph/y<",
            "*>;)Z"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2
    invoke-virtual {p0, p2, p1}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p2

    .line 3
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public d()Lcom/android/tools/r8/internal/yb0;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract equals(Ljava/lang/Object;)Z
.end method

.method public abstract hashCode()I
.end method

.method public r()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public s()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/b7;

    return v0
.end method

.method public t()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/n7;

    return v0
.end method

.method public abstract toString()Ljava/lang/String;
.end method

.method public u()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/a8;

    return v0
.end method

.method public v()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/Ib;

    return v0
.end method

.method public w()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public x()Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->N()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qZ;->d()Z

    move-result v0

    return v0
.end method

.method public final y()Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sr0;->N()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qZ;->e()Z

    move-result v0

    return v0
.end method

.method public z()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/ts;

    return v0
.end method
