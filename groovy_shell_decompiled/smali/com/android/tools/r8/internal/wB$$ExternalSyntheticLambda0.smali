.class public final synthetic Lcom/android/tools/r8/internal/wB$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/ObjIntConsumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/wB;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/wB;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/wB$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/wB;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;I)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/wB$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/wB;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/wB;->a(Ljava/lang/Object;I)V

    return-void
.end method
