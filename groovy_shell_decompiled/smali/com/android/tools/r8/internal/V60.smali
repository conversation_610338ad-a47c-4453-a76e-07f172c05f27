.class public final Lcom/android/tools/r8/internal/V60;
.super Lcom/android/tools/r8/internal/Ox;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public e:I

.field public f:I

.field public g:Ljava/util/List;

.field public h:Ljava/util/List;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Ox;-><init>()V

    const/4 v0, 0x6

    .line 124
    iput v0, p0, Lcom/android/tools/r8/internal/V60;->f:I

    .line 185
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/V60;->g:Ljava/util/List;

    .line 309
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/V60;->h:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/N0;
    .locals 2

    .line 39
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/V60;->c()Lcom/android/tools/r8/internal/W60;

    move-result-object v0

    .line 40
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W60;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 41
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/hs0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/hs0;-><init>()V

    .line 42
    throw v0
.end method

.method public final bridge synthetic a(Lcom/android/tools/r8/internal/Vx;)Lcom/android/tools/r8/internal/Nx;
    .locals 0

    .line 38
    check-cast p1, Lcom/android/tools/r8/internal/W60;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/V60;->a(Lcom/android/tools/r8/internal/W60;)Lcom/android/tools/r8/internal/V60;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/Nx;
    .locals 2

    const/4 v0, 0x0

    .line 43
    :try_start_0
    sget-object v1, Lcom/android/tools/r8/internal/W60;->k:Lcom/android/tools/r8/internal/U60;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 44
    new-instance v1, Lcom/android/tools/r8/internal/W60;

    invoke-direct {v1, p1, p2}, Lcom/android/tools/r8/internal/W60;-><init>(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)V
    :try_end_0
    .catch Lcom/android/tools/r8/internal/kI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 45
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/V60;->a(Lcom/android/tools/r8/internal/W60;)Lcom/android/tools/r8/internal/V60;

    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 46
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 47
    check-cast p2, Lcom/android/tools/r8/internal/W60;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 48
    :try_start_2
    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_0

    .line 51
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/V60;->a(Lcom/android/tools/r8/internal/W60;)Lcom/android/tools/r8/internal/V60;

    .line 53
    :cond_0
    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/W60;)Lcom/android/tools/r8/internal/V60;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/W60;->j:Lcom/android/tools/r8/internal/W60;

    if-ne p1, v0, :cond_0

    return-object p0

    .line 2
    :cond_0
    iget v0, p1, Lcom/android/tools/r8/internal/W60;->d:I

    const/4 v1, 0x1

    and-int/2addr v0, v1

    if-ne v0, v1, :cond_1

    .line 3
    iget v0, p1, Lcom/android/tools/r8/internal/W60;->e:I

    .line 4
    iget v2, p0, Lcom/android/tools/r8/internal/V60;->e:I

    or-int/2addr v1, v2

    iput v1, p0, Lcom/android/tools/r8/internal/V60;->e:I

    .line 5
    iput v0, p0, Lcom/android/tools/r8/internal/V60;->f:I

    .line 6
    :cond_1
    iget-object v0, p1, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    .line 7
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_4

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/internal/V60;->g:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 9
    iget-object v0, p1, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    .line 10
    iput-object v0, p0, Lcom/android/tools/r8/internal/V60;->g:Ljava/util/List;

    .line 11
    iget v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    and-int/lit8 v0, v0, -0x3

    iput v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    goto :goto_0

    .line 12
    :cond_2
    iget v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    const/4 v1, 0x2

    and-int/2addr v0, v1

    if-eq v0, v1, :cond_3

    .line 13
    new-instance v0, Ljava/util/ArrayList;

    iget-object v2, p0, Lcom/android/tools/r8/internal/V60;->g:Ljava/util/List;

    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/V60;->g:Ljava/util/List;

    .line 14
    iget v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    or-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    .line 15
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/internal/V60;->g:Ljava/util/List;

    .line 16
    iget-object v1, p1, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    .line 17
    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 18
    :cond_4
    :goto_0
    iget-object v0, p1, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 19
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_7

    .line 20
    iget-object v0, p0, Lcom/android/tools/r8/internal/V60;->h:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 21
    iget-object v0, p1, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 22
    iput-object v0, p0, Lcom/android/tools/r8/internal/V60;->h:Ljava/util/List;

    .line 23
    iget v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    and-int/lit8 v0, v0, -0x5

    iput v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    goto :goto_1

    .line 24
    :cond_5
    iget v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    const/4 v1, 0x4

    and-int/2addr v0, v1

    if-eq v0, v1, :cond_6

    .line 25
    new-instance v0, Ljava/util/ArrayList;

    iget-object v2, p0, Lcom/android/tools/r8/internal/V60;->h:Ljava/util/List;

    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/V60;->h:Ljava/util/List;

    .line 26
    iget v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    or-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/V60;->e:I

    .line 27
    :cond_6
    iget-object v0, p0, Lcom/android/tools/r8/internal/V60;->h:Ljava/util/List;

    .line 28
    iget-object v1, p1, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 29
    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 33
    :cond_7
    :goto_1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Ox;->a(Lcom/android/tools/r8/internal/Qx;)V

    .line 34
    iget-object v0, p0, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    .line 35
    iget-object p1, p1, Lcom/android/tools/r8/internal/W60;->c:Lcom/android/tools/r8/internal/Y7;

    .line 36
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Y7;->a(Lcom/android/tools/r8/internal/Y7;)Lcom/android/tools/r8/internal/Y7;

    move-result-object p1

    .line 37
    iput-object p1, p0, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    return-object p0
.end method

.method public final b()Lcom/android/tools/r8/internal/Vx;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/W60;->j:Lcom/android/tools/r8/internal/W60;

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/W60;
    .locals 4

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/W60;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/W60;-><init>(Lcom/android/tools/r8/internal/V60;)V

    .line 2
    iget v1, p0, Lcom/android/tools/r8/internal/V60;->e:I

    and-int/lit8 v2, v1, 0x1

    const/4 v3, 0x1

    if-ne v2, v3, :cond_0

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    .line 7
    :goto_0
    iget v2, p0, Lcom/android/tools/r8/internal/V60;->f:I

    .line 8
    iput v2, v0, Lcom/android/tools/r8/internal/W60;->e:I

    const/4 v2, 0x2

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_1

    .line 9
    iget-object v1, p0, Lcom/android/tools/r8/internal/V60;->g:Ljava/util/List;

    invoke-static {v1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/V60;->g:Ljava/util/List;

    .line 10
    iget v1, p0, Lcom/android/tools/r8/internal/V60;->e:I

    and-int/lit8 v1, v1, -0x3

    iput v1, p0, Lcom/android/tools/r8/internal/V60;->e:I

    .line 12
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/V60;->g:Ljava/util/List;

    .line 13
    iput-object v1, v0, Lcom/android/tools/r8/internal/W60;->f:Ljava/util/List;

    .line 14
    iget v1, p0, Lcom/android/tools/r8/internal/V60;->e:I

    const/4 v2, 0x4

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_2

    .line 15
    iget-object v1, p0, Lcom/android/tools/r8/internal/V60;->h:Ljava/util/List;

    invoke-static {v1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/V60;->h:Ljava/util/List;

    .line 16
    iget v1, p0, Lcom/android/tools/r8/internal/V60;->e:I

    and-int/lit8 v1, v1, -0x5

    iput v1, p0, Lcom/android/tools/r8/internal/V60;->e:I

    .line 18
    :cond_2
    iget-object v1, p0, Lcom/android/tools/r8/internal/V60;->h:Ljava/util/List;

    .line 19
    iput-object v1, v0, Lcom/android/tools/r8/internal/W60;->g:Ljava/util/List;

    .line 20
    iput v3, v0, Lcom/android/tools/r8/internal/W60;->d:I

    return-object v0
.end method

.method public final clone()Ljava/lang/Object;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/V60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/V60;-><init>()V

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/V60;->c()Lcom/android/tools/r8/internal/W60;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/V60;->a(Lcom/android/tools/r8/internal/W60;)Lcom/android/tools/r8/internal/V60;

    move-result-object v0

    return-object v0
.end method
