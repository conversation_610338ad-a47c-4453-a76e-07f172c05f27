.class public final Lcom/android/tools/r8/internal/U70;
.super Lcom/android/tools/r8/internal/Qx;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final n:Lcom/android/tools/r8/internal/U70;

.field public static final o:Lcom/android/tools/r8/internal/R70;


# instance fields
.field public final c:Lcom/android/tools/r8/internal/Y7;

.field public d:I

.field public e:I

.field public f:I

.field public g:Z

.field public h:Lcom/android/tools/r8/internal/T70;

.field public i:Ljava/util/List;

.field public j:Ljava/util/List;

.field public k:I

.field public l:B

.field public m:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/R70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/R70;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/U70;->o:Lcom/android/tools/r8/internal/R70;

    .line 874
    new-instance v0, Lcom/android/tools/r8/internal/U70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/U70;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/U70;->n:Lcom/android/tools/r8/internal/U70;

    const/4 v1, 0x0

    .line 875
    iput v1, v0, Lcom/android/tools/r8/internal/U70;->e:I

    .line 876
    iput v1, v0, Lcom/android/tools/r8/internal/U70;->f:I

    .line 877
    iput-boolean v1, v0, Lcom/android/tools/r8/internal/U70;->g:Z

    .line 878
    sget-object v1, Lcom/android/tools/r8/internal/T70;->e:Lcom/android/tools/r8/internal/T70;

    iput-object v1, v0, Lcom/android/tools/r8/internal/U70;->h:Lcom/android/tools/r8/internal/T70;

    .line 879
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, v0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    .line 880
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, v0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 392
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Qx;-><init>()V

    const/4 v0, -0x1

    .line 707
    iput v0, p0, Lcom/android/tools/r8/internal/U70;->k:I

    .line 717
    iput-byte v0, p0, Lcom/android/tools/r8/internal/U70;->l:B

    .line 777
    iput v0, p0, Lcom/android/tools/r8/internal/U70;->m:I

    .line 778
    sget-object v0, Lcom/android/tools/r8/internal/Y7;->b:Lcom/android/tools/r8/internal/XR;

    iput-object v0, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/S70;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Qx;-><init>(Lcom/android/tools/r8/internal/Ox;)V

    const/4 v0, -0x1

    .line 319
    iput v0, p0, Lcom/android/tools/r8/internal/U70;->k:I

    .line 329
    iput-byte v0, p0, Lcom/android/tools/r8/internal/U70;->l:B

    .line 389
    iput v0, p0, Lcom/android/tools/r8/internal/U70;->m:I

    .line 390
    iget-object p1, p1, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    .line 391
    iput-object p1, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)V
    .locals 12

    .line 779
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Qx;-><init>()V

    const/4 v0, -0x1

    .line 1079
    iput v0, p0, Lcom/android/tools/r8/internal/U70;->k:I

    .line 1089
    iput-byte v0, p0, Lcom/android/tools/r8/internal/U70;->l:B

    .line 1149
    iput v0, p0, Lcom/android/tools/r8/internal/U70;->m:I

    const/4 v0, 0x0

    .line 1150
    iput v0, p0, Lcom/android/tools/r8/internal/U70;->e:I

    .line 1151
    iput v0, p0, Lcom/android/tools/r8/internal/U70;->f:I

    .line 1152
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/U70;->g:Z

    .line 1153
    sget-object v1, Lcom/android/tools/r8/internal/T70;->e:Lcom/android/tools/r8/internal/T70;

    iput-object v1, p0, Lcom/android/tools/r8/internal/U70;->h:Lcom/android/tools/r8/internal/T70;

    .line 1154
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    .line 1155
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    .line 1156
    new-instance v1, Lcom/android/tools/r8/internal/W7;

    invoke-direct {v1}, Lcom/android/tools/r8/internal/W7;-><init>()V

    .line 1157
    new-instance v2, Lcom/android/tools/r8/internal/ie;

    const/4 v3, 0x1

    new-array v4, v3, [B

    invoke-direct {v2, v1, v4}, Lcom/android/tools/r8/internal/ie;-><init>(Ljava/io/OutputStream;[B)V

    move v4, v0

    move v5, v4

    :cond_0
    :goto_0
    const/16 v6, 0x10

    const/16 v7, 0x20

    if-nez v4, :cond_14

    .line 1158
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->i()I

    move-result v8

    if-eqz v8, :cond_11

    const/16 v9, 0x8

    if-eq v8, v9, :cond_10

    const/4 v10, 0x2

    if-eq v8, v6, :cond_f

    const/16 v11, 0x18

    if-eq v8, v11, :cond_d

    if-eq v8, v7, :cond_8

    const/16 v9, 0x2a

    if-eq v8, v9, :cond_6

    const/16 v9, 0x30

    if-eq v8, v9, :cond_4

    const/16 v9, 0x32

    if-eq v8, v9, :cond_1

    .line 1164
    invoke-virtual {p0, p1, v2, p2, v8}, Lcom/android/tools/r8/internal/Qx;->a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/ie;Lcom/android/tools/r8/internal/Ku;I)Z

    move-result v6

    if-nez v6, :cond_0

    goto/16 :goto_4

    .line 1214
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v8

    .line 1215
    invoke-virtual {p1, v8}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v8

    and-int/lit8 v9, v5, 0x20

    if-eq v9, v7, :cond_2

    .line 1216
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v9

    if-lez v9, :cond_2

    .line 1217
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    iput-object v9, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    or-int/lit8 v5, v5, 0x20

    .line 1220
    :cond_2
    :goto_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v9

    if-lez v9, :cond_3

    .line 1221
    iget-object v9, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    .line 1222
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v10

    .line 1223
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v10

    invoke-interface {v9, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 1224
    :cond_3
    iput v8, p1, Lcom/android/tools/r8/internal/be;->h:I

    .line 1225
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->j()V

    goto :goto_0

    :cond_4
    and-int/lit8 v8, v5, 0x20

    if-eq v8, v7, :cond_5

    .line 1226
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    iput-object v8, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    or-int/lit8 v5, v5, 0x20

    .line 1229
    :cond_5
    iget-object v8, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    .line 1230
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 1231
    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    invoke-interface {v8, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_6
    and-int/lit8 v8, v5, 0x10

    if-eq v8, v6, :cond_7

    .line 1232
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    iput-object v8, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    or-int/lit8 v5, v5, 0x10

    .line 1235
    :cond_7
    iget-object v8, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    sget-object v9, Lcom/android/tools/r8/internal/N70;->v:Lcom/android/tools/r8/internal/H70;

    invoke-virtual {p1, v9, p2}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v9

    invoke-interface {v8, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 1236
    :cond_8
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v11

    if-eqz v11, :cond_b

    if-eq v11, v3, :cond_a

    if-eq v11, v10, :cond_9

    const/4 v10, 0x0

    goto :goto_2

    .line 1237
    :cond_9
    sget-object v10, Lcom/android/tools/r8/internal/T70;->e:Lcom/android/tools/r8/internal/T70;

    goto :goto_2

    .line 1238
    :cond_a
    sget-object v10, Lcom/android/tools/r8/internal/T70;->d:Lcom/android/tools/r8/internal/T70;

    goto :goto_2

    .line 1239
    :cond_b
    sget-object v10, Lcom/android/tools/r8/internal/T70;->c:Lcom/android/tools/r8/internal/T70;

    :goto_2
    if-nez v10, :cond_c

    .line 1240
    invoke-virtual {v2, v8}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 1241
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/ie;->g(I)V

    goto/16 :goto_0

    .line 1243
    :cond_c
    iget v8, p0, Lcom/android/tools/r8/internal/U70;->d:I

    or-int/2addr v8, v9

    iput v8, p0, Lcom/android/tools/r8/internal/U70;->d:I

    .line 1244
    iput-object v10, p0, Lcom/android/tools/r8/internal/U70;->h:Lcom/android/tools/r8/internal/T70;

    goto/16 :goto_0

    .line 1245
    :cond_d
    iget v8, p0, Lcom/android/tools/r8/internal/U70;->d:I

    or-int/lit8 v8, v8, 0x4

    iput v8, p0, Lcom/android/tools/r8/internal/U70;->d:I

    .line 1246
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->g()J

    move-result-wide v8

    const-wide/16 v10, 0x0

    cmp-long v8, v8, v10

    if-eqz v8, :cond_e

    move v8, v3

    goto :goto_3

    :cond_e
    move v8, v0

    .line 1247
    :goto_3
    iput-boolean v8, p0, Lcom/android/tools/r8/internal/U70;->g:Z

    goto/16 :goto_0

    .line 1248
    :cond_f
    iget v8, p0, Lcom/android/tools/r8/internal/U70;->d:I

    or-int/2addr v8, v10

    iput v8, p0, Lcom/android/tools/r8/internal/U70;->d:I

    .line 1249
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v8

    .line 1250
    iput v8, p0, Lcom/android/tools/r8/internal/U70;->f:I

    goto/16 :goto_0

    .line 1251
    :cond_10
    iget v8, p0, Lcom/android/tools/r8/internal/U70;->d:I

    or-int/2addr v8, v3

    iput v8, p0, Lcom/android/tools/r8/internal/U70;->d:I

    .line 1252
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v8

    .line 1253
    iput v8, p0, Lcom/android/tools/r8/internal/U70;->e:I
    :try_end_0
    .catch Lcom/android/tools/r8/internal/kI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto/16 :goto_0

    :cond_11
    :goto_4
    move v4, v3

    goto/16 :goto_0

    :catchall_0
    move-exception p1

    goto :goto_5

    :catch_0
    move-exception p1

    .line 1312
    :try_start_1
    new-instance p2, Lcom/android/tools/r8/internal/kI;

    .line 1313
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/kI;-><init>(Ljava/lang/String;)V

    .line 1314
    iput-object p0, p2, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 1315
    throw p2

    :catch_1
    move-exception p1

    .line 1316
    iput-object p0, p1, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 1317
    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :goto_5
    and-int/lit8 p2, v5, 0x10

    if-ne p2, v6, :cond_12

    .line 1323
    iget-object p2, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    :cond_12
    and-int/lit8 p2, v5, 0x20

    if-ne p2, v7, :cond_13

    .line 1326
    iget-object p2, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    .line 1329
    :cond_13
    :try_start_2
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/ie;->a()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 1333
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    goto :goto_6

    :catchall_1
    move-exception p1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    .line 1334
    throw p1

    .line 1335
    :catch_2
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    .line 1336
    :goto_6
    iget-object p2, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Mv;->a()V

    .line 1337
    throw p1

    :cond_14
    and-int/lit8 p1, v5, 0x10

    if-ne p1, v6, :cond_15

    .line 1338
    iget-object p1, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    :cond_15
    and-int/lit8 p1, v5, 0x20

    if-ne p1, v7, :cond_16

    .line 1341
    iget-object p1, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    .line 1344
    :cond_16
    :try_start_3
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/ie;->a()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    .line 1348
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    goto :goto_7

    :catchall_2
    move-exception p1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    .line 1349
    throw p1

    .line 1350
    :catch_3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    .line 1351
    :goto_7
    iget-object p1, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Mv;->a()V

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 5

    .line 32
    iget v0, p0, Lcom/android/tools/r8/internal/U70;->m:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    .line 36
    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/U70;->d:I

    const/4 v1, 0x1

    and-int/2addr v0, v1

    const/4 v2, 0x0

    if-ne v0, v1, :cond_1

    .line 37
    iget v0, p0, Lcom/android/tools/r8/internal/U70;->e:I

    .line 38
    invoke-static {v1, v0}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v0

    goto :goto_0

    :cond_1
    move v0, v2

    .line 40
    :goto_0
    iget v3, p0, Lcom/android/tools/r8/internal/U70;->d:I

    const/4 v4, 0x2

    and-int/2addr v3, v4

    if-ne v3, v4, :cond_2

    .line 41
    iget v3, p0, Lcom/android/tools/r8/internal/U70;->f:I

    .line 42
    invoke-static {v4, v3}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v3

    add-int/2addr v0, v3

    .line 44
    :cond_2
    iget v3, p0, Lcom/android/tools/r8/internal/U70;->d:I

    const/4 v4, 0x4

    and-int/2addr v3, v4

    if-ne v3, v4, :cond_3

    const/4 v3, 0x3

    .line 45
    invoke-static {v3}, Lcom/android/tools/r8/internal/ie;->c(I)I

    move-result v3

    add-int/2addr v3, v1

    add-int/2addr v0, v3

    .line 46
    :cond_3
    iget v1, p0, Lcom/android/tools/r8/internal/U70;->d:I

    const/16 v3, 0x8

    and-int/2addr v1, v3

    if-ne v1, v3, :cond_4

    .line 47
    iget-object v1, p0, Lcom/android/tools/r8/internal/U70;->h:Lcom/android/tools/r8/internal/T70;

    .line 48
    iget v1, v1, Lcom/android/tools/r8/internal/T70;->b:I

    .line 49
    invoke-static {v4}, Lcom/android/tools/r8/internal/ie;->c(I)I

    move-result v3

    invoke-static {v1}, Lcom/android/tools/r8/internal/ie;->a(I)I

    move-result v1

    add-int/2addr v1, v3

    add-int/2addr v0, v1

    :cond_4
    move v1, v2

    .line 50
    :goto_1
    iget-object v3, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v1, v3, :cond_5

    .line 51
    iget-object v3, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    .line 52
    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/N0;

    const/4 v4, 0x5

    invoke-static {v4, v3}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v3

    add-int/2addr v0, v3

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_5
    move v1, v2

    .line 56
    :goto_2
    iget-object v3, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    const/16 v4, 0xa

    if-ge v2, v3, :cond_7

    .line 57
    iget-object v3, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    .line 58
    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    if-ltz v3, :cond_6

    .line 59
    invoke-static {v3}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v4

    :cond_6
    add-int/2addr v1, v4

    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    :cond_7
    add-int/2addr v0, v1

    .line 60
    iget-object v2, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    .line 61
    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_9

    add-int/lit8 v0, v0, 0x1

    if-ltz v1, :cond_8

    .line 62
    invoke-static {v1}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v4

    :cond_8
    add-int/2addr v0, v4

    .line 63
    :cond_9
    iput v1, p0, Lcom/android/tools/r8/internal/U70;->k:I

    .line 65
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Qx;->e()I

    move-result v1

    add-int/2addr v1, v0

    .line 66
    iget-object v0, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Y7;->size()I

    move-result v0

    add-int/2addr v0, v1

    .line 67
    iput v0, p0, Lcom/android/tools/r8/internal/U70;->m:I

    return v0
.end method

.method public final a(Lcom/android/tools/r8/internal/ie;)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/U70;->a()I

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Px;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/Px;-><init>(Lcom/android/tools/r8/internal/Qx;)V

    .line 3
    iget v1, p0, Lcom/android/tools/r8/internal/U70;->d:I

    const/4 v2, 0x1

    and-int/2addr v1, v2

    const/4 v3, 0x0

    if-ne v1, v2, :cond_0

    .line 4
    iget v1, p0, Lcom/android/tools/r8/internal/U70;->e:I

    .line 5
    invoke-virtual {p1, v2, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 6
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 7
    :cond_0
    iget v1, p0, Lcom/android/tools/r8/internal/U70;->d:I

    const/4 v2, 0x2

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_1

    .line 8
    iget v1, p0, Lcom/android/tools/r8/internal/U70;->f:I

    .line 9
    invoke-virtual {p1, v2, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 10
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 11
    :cond_1
    iget v1, p0, Lcom/android/tools/r8/internal/U70;->d:I

    const/4 v2, 0x4

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_2

    .line 12
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/U70;->g:Z

    const/4 v4, 0x3

    .line 13
    invoke-virtual {p1, v4, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 14
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->e(I)V

    .line 15
    :cond_2
    iget v1, p0, Lcom/android/tools/r8/internal/U70;->d:I

    const/16 v4, 0x8

    and-int/2addr v1, v4

    if-ne v1, v4, :cond_3

    .line 16
    iget-object v1, p0, Lcom/android/tools/r8/internal/U70;->h:Lcom/android/tools/r8/internal/T70;

    .line 17
    iget v1, v1, Lcom/android/tools/r8/internal/T70;->b:I

    .line 18
    invoke-virtual {p1, v2, v1}, Lcom/android/tools/r8/internal/ie;->b(II)V

    :cond_3
    move v1, v3

    .line 20
    :goto_0
    iget-object v2, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_4

    .line 21
    iget-object v2, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/N0;

    const/4 v4, 0x5

    invoke-virtual {p1, v4, v2}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 22
    :cond_4
    iget-object v1, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    .line 23
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_5

    const/16 v1, 0x32

    .line 24
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 25
    iget v1, p0, Lcom/android/tools/r8/internal/U70;->k:I

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 27
    :cond_5
    :goto_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v3, v1, :cond_6

    .line 28
    iget-object v1, p0, Lcom/android/tools/r8/internal/U70;->j:Ljava/util/List;

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Integer;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_6
    const/16 v1, 0x3e8

    .line 30
    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Px;->a(ILcom/android/tools/r8/internal/ie;)V

    .line 31
    iget-object v0, p0, Lcom/android/tools/r8/internal/U70;->c:Lcom/android/tools/r8/internal/Y7;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/ie;->a(Lcom/android/tools/r8/internal/Y7;)V

    return-void
.end method

.method public final b()Lcom/android/tools/r8/internal/Nx;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/S70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/S70;-><init>()V

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/Nx;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/S70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/S70;-><init>()V

    .line 2
    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/S70;->a(Lcom/android/tools/r8/internal/U70;)Lcom/android/tools/r8/internal/S70;

    move-result-object v0

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/N0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/U70;->n:Lcom/android/tools/r8/internal/U70;

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 4

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/internal/U70;->l:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v2, 0x0

    if-nez v0, :cond_1

    return v2

    .line 2
    :cond_1
    iget v0, p0, Lcom/android/tools/r8/internal/U70;->d:I

    and-int/lit8 v3, v0, 0x1

    if-ne v3, v1, :cond_6

    const/4 v3, 0x2

    and-int/2addr v0, v3

    if-ne v0, v3, :cond_5

    move v0, v2

    .line 3
    :goto_0
    iget-object v3, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_3

    .line 4
    iget-object v3, p0, Lcom/android/tools/r8/internal/U70;->i:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/N70;

    .line 5
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/N70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_2

    .line 6
    iput-byte v2, p0, Lcom/android/tools/r8/internal/U70;->l:B

    return v2

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 10
    :cond_3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Qx;->d()Z

    move-result v0

    if-nez v0, :cond_4

    .line 11
    iput-byte v2, p0, Lcom/android/tools/r8/internal/U70;->l:B

    return v2

    .line 14
    :cond_4
    iput-byte v1, p0, Lcom/android/tools/r8/internal/U70;->l:B

    return v1

    .line 15
    :cond_5
    iput-byte v2, p0, Lcom/android/tools/r8/internal/U70;->l:B

    return v2

    .line 16
    :cond_6
    iput-byte v2, p0, Lcom/android/tools/r8/internal/U70;->l:B

    return v2
.end method
