.class public final Lcom/android/tools/r8/internal/sy;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/internal/Ok;

.field public final b:[Lcom/android/tools/r8/internal/hy;

.field public c:[Ljava/lang/String;

.field public final d:[Lcom/android/tools/r8/internal/iy;

.field public volatile e:Z


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Ok;[Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/sy;->a:Lcom/android/tools/r8/internal/Ok;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    .line 4
    iget-object p2, p1, Lcom/android/tools/r8/internal/Ok;->g:[Lcom/android/tools/r8/internal/al;

    .line 5
    invoke-static {p2}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p2

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    .line 6
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    new-array p2, p2, [Lcom/android/tools/r8/internal/hy;

    iput-object p2, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    .line 7
    iget-object p1, p1, Lcom/android/tools/r8/internal/Ok;->j:[Lcom/android/tools/r8/internal/el;

    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    .line 8
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    new-array p1, p1, [Lcom/android/tools/r8/internal/iy;

    iput-object p1, p0, Lcom/android/tools/r8/internal/sy;->d:[Lcom/android/tools/r8/internal/iy;

    const/4 p1, 0x0

    .line 9
    iput-boolean p1, p0, Lcom/android/tools/r8/internal/sy;->e:Z

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/sy;Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/hy;
    .locals 2

    .line 11
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 12
    iget-object v0, p1, Lcom/android/tools/r8/internal/al;->i:Lcom/android/tools/r8/internal/Ok;

    .line 13
    iget-object v1, p0, Lcom/android/tools/r8/internal/sy;->a:Lcom/android/tools/r8/internal/Ok;

    if-ne v0, v1, :cond_1

    .line 14
    iget-object v0, p1, Lcom/android/tools/r8/internal/al;->c:Lcom/android/tools/r8/internal/Qj;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Qj;->f()Z

    move-result v0

    if-nez v0, :cond_0

    .line 15
    iget-object p0, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    .line 16
    iget p1, p1, Lcom/android/tools/r8/internal/al;->b:I

    .line 17
    aget-object p0, p0, p1

    return-object p0

    .line 18
    :cond_0
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string p1, "This type does not have extensions."

    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0

    .line 19
    :cond_1
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string p1, "FieldDescriptor does not match message type."

    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static a(Lcom/android/tools/r8/internal/sy;Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/iy;
    .locals 2

    .line 1
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    iget-object v0, p1, Lcom/android/tools/r8/internal/el;->f:Lcom/android/tools/r8/internal/Ok;

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/internal/sy;->a:Lcom/android/tools/r8/internal/Ok;

    if-ne v0, v1, :cond_0

    .line 7
    iget-object p0, p0, Lcom/android/tools/r8/internal/sy;->d:[Lcom/android/tools/r8/internal/iy;

    .line 8
    iget p1, p1, Lcom/android/tools/r8/internal/el;->b:I

    .line 9
    aget-object p0, p0, p1

    return-object p0

    .line 10
    :cond_0
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string p1, "OneofDescriptor does not match message type."

    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0
.end method


# virtual methods
.method public final a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;
    .locals 12

    .line 20
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/sy;->e:Z

    if-eqz v0, :cond_0

    return-object p0

    .line 21
    :cond_0
    monitor-enter p0

    .line 22
    :try_start_0
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/sy;->e:Z

    if-eqz v0, :cond_1

    monitor-exit p0

    return-object p0

    .line 23
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    array-length v0, v0

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    const/4 v3, 0x0

    if-ge v2, v0, :cond_a

    .line 25
    iget-object v4, p0, Lcom/android/tools/r8/internal/sy;->a:Lcom/android/tools/r8/internal/Ok;

    .line 26
    iget-object v4, v4, Lcom/android/tools/r8/internal/Ok;->g:[Lcom/android/tools/r8/internal/al;

    .line 27
    invoke-static {v4}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v4

    invoke-static {v4}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v4

    .line 28
    invoke-interface {v4, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    move-object v6, v4

    check-cast v6, Lcom/android/tools/r8/internal/al;

    .line 29
    iget-object v4, v6, Lcom/android/tools/r8/internal/al;->k:Lcom/android/tools/r8/internal/el;

    if-eqz v4, :cond_2

    .line 30
    iget-object v5, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    .line 31
    iget v4, v4, Lcom/android/tools/r8/internal/el;->b:I

    add-int/2addr v4, v0

    .line 32
    aget-object v4, v5, v4

    move-object v10, v4

    goto :goto_1

    :cond_2
    move-object v10, v3

    .line 34
    :goto_1
    invoke-virtual {v6}, Lcom/android/tools/r8/internal/al;->l()Z

    move-result v4

    if-eqz v4, :cond_6

    .line 35
    iget-object v4, v6, Lcom/android/tools/r8/internal/al;->h:Lcom/android/tools/r8/internal/Zk;

    .line 36
    iget-object v4, v4, Lcom/android/tools/r8/internal/Zk;->b:Lcom/android/tools/r8/internal/Yk;

    .line 37
    sget-object v5, Lcom/android/tools/r8/internal/Yk;->k:Lcom/android/tools/r8/internal/Yk;

    if-ne v4, v5, :cond_4

    .line 38
    invoke-virtual {v6}, Lcom/android/tools/r8/internal/al;->i()Z

    move-result v4

    if-nez v4, :cond_3

    .line 42
    iget-object v3, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    new-instance v4, Lcom/android/tools/r8/internal/my;

    iget-object v5, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    aget-object v5, v5, v2

    invoke-direct {v4, v5, p1, p2}, Lcom/android/tools/r8/internal/my;-><init>(Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;)V

    aput-object v4, v3, v2

    goto/16 :goto_2

    .line 43
    :cond_3
    iget-object p2, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    aget-object p2, p2, v2

    const-string p2, "getDefaultInstance"

    new-array v0, v1, [Ljava/lang/Class;

    .line 45
    invoke-static {p1, p2, v0}, Lcom/android/tools/r8/internal/uy;->access$1000(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object p1

    new-array p2, v1, [Ljava/lang/Object;

    .line 47
    invoke-static {p1, v3, p2}, Lcom/android/tools/r8/internal/uy;->access$1100(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/uy;

    .line 48
    iget-object p2, v6, Lcom/android/tools/r8/internal/al;->c:Lcom/android/tools/r8/internal/Qj;

    .line 49
    iget p2, p2, Lcom/android/tools/r8/internal/Qj;->d:I

    .line 50
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/uy;->internalGetMapField(I)Lcom/android/tools/r8/internal/gT;

    .line 51
    throw v3

    .line 52
    :cond_4
    sget-object v3, Lcom/android/tools/r8/internal/Yk;->j:Lcom/android/tools/r8/internal/Yk;

    if-ne v4, v3, :cond_5

    .line 53
    iget-object v3, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    new-instance v4, Lcom/android/tools/r8/internal/jy;

    iget-object v5, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    aget-object v5, v5, v2

    invoke-direct {v4, v6, v5, p1, p2}, Lcom/android/tools/r8/internal/jy;-><init>(Lcom/android/tools/r8/internal/al;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;)V

    aput-object v4, v3, v2

    goto/16 :goto_2

    .line 56
    :cond_5
    iget-object v3, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    new-instance v4, Lcom/android/tools/r8/internal/ly;

    iget-object v5, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    aget-object v5, v5, v2

    invoke-direct {v4, v5, p1, p2}, Lcom/android/tools/r8/internal/ly;-><init>(Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;)V

    aput-object v4, v3, v2

    goto :goto_2

    .line 57
    :cond_6
    iget-object v3, v6, Lcom/android/tools/r8/internal/al;->h:Lcom/android/tools/r8/internal/Zk;

    .line 58
    iget-object v3, v3, Lcom/android/tools/r8/internal/Zk;->b:Lcom/android/tools/r8/internal/Yk;

    .line 59
    sget-object v4, Lcom/android/tools/r8/internal/Yk;->k:Lcom/android/tools/r8/internal/Yk;

    if-ne v3, v4, :cond_7

    .line 60
    iget-object v3, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    new-instance v4, Lcom/android/tools/r8/internal/qy;

    iget-object v5, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    aget-object v7, v5, v2

    move-object v5, v4

    move-object v8, p1

    move-object v9, p2

    invoke-direct/range {v5 .. v10}, Lcom/android/tools/r8/internal/qy;-><init>(Lcom/android/tools/r8/internal/al;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V

    aput-object v4, v3, v2

    goto :goto_2

    .line 63
    :cond_7
    sget-object v4, Lcom/android/tools/r8/internal/Yk;->j:Lcom/android/tools/r8/internal/Yk;

    if-ne v3, v4, :cond_8

    .line 64
    iget-object v3, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    new-instance v4, Lcom/android/tools/r8/internal/ny;

    iget-object v5, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    aget-object v7, v5, v2

    move-object v5, v4

    move-object v8, p1

    move-object v9, p2

    invoke-direct/range {v5 .. v10}, Lcom/android/tools/r8/internal/ny;-><init>(Lcom/android/tools/r8/internal/al;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V

    aput-object v4, v3, v2

    goto :goto_2

    .line 67
    :cond_8
    sget-object v4, Lcom/android/tools/r8/internal/Yk;->h:Lcom/android/tools/r8/internal/Yk;

    if-ne v3, v4, :cond_9

    .line 68
    iget-object v3, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    new-instance v4, Lcom/android/tools/r8/internal/ry;

    iget-object v5, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    aget-object v7, v5, v2

    move-object v5, v4

    move-object v8, p1

    move-object v9, p2

    invoke-direct/range {v5 .. v10}, Lcom/android/tools/r8/internal/ry;-><init>(Lcom/android/tools/r8/internal/al;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V

    aput-object v4, v3, v2

    goto :goto_2

    .line 72
    :cond_9
    iget-object v3, p0, Lcom/android/tools/r8/internal/sy;->b:[Lcom/android/tools/r8/internal/hy;

    new-instance v4, Lcom/android/tools/r8/internal/py;

    iget-object v5, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    aget-object v7, v5, v2

    move-object v5, v4

    move-object v8, p1

    move-object v9, p2

    invoke-direct/range {v5 .. v10}, Lcom/android/tools/r8/internal/py;-><init>(Lcom/android/tools/r8/internal/al;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V

    aput-object v4, v3, v2

    :goto_2
    add-int/lit8 v2, v2, 0x1

    goto/16 :goto_0

    .line 79
    :cond_a
    iget-object v2, p0, Lcom/android/tools/r8/internal/sy;->d:[Lcom/android/tools/r8/internal/iy;

    array-length v2, v2

    :goto_3
    if-ge v1, v2, :cond_b

    .line 81
    iget-object v10, p0, Lcom/android/tools/r8/internal/sy;->d:[Lcom/android/tools/r8/internal/iy;

    new-instance v11, Lcom/android/tools/r8/internal/iy;

    iget-object v5, p0, Lcom/android/tools/r8/internal/sy;->a:Lcom/android/tools/r8/internal/Ok;

    iget-object v4, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    add-int v6, v1, v0

    aget-object v7, v4, v6

    move-object v4, v11

    move v6, v1

    move-object v8, p1

    move-object v9, p2

    invoke-direct/range {v4 .. v9}, Lcom/android/tools/r8/internal/iy;-><init>(Lcom/android/tools/r8/internal/Ok;ILjava/lang/String;Ljava/lang/Class;Ljava/lang/Class;)V

    aput-object v11, v10, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    :cond_b
    const/4 p1, 0x1

    .line 85
    iput-boolean p1, p0, Lcom/android/tools/r8/internal/sy;->e:Z

    .line 86
    iput-object v3, p0, Lcom/android/tools/r8/internal/sy;->c:[Ljava/lang/String;

    .line 87
    monitor-exit p0

    return-object p0

    :catchall_0
    move-exception p1

    .line 88
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method
