.class public final Lcom/android/tools/r8/internal/Ub;
.super Lcom/android/tools/r8/internal/p2;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final j:Lcom/android/tools/r8/internal/S5;

.field public k:Lcom/android/tools/r8/internal/IC;

.field public l:I


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/S5;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/p2;-><init>(Lcom/android/tools/r8/internal/S5;)V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/Ub;->j:Lcom/android/tools/r8/internal/S5;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/fx;Ljava/lang/Object;)Lcom/android/tools/r8/internal/R5;
    .locals 3

    const/4 v0, 0x0

    .line 92
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    if-ne p2, v0, :cond_0

    .line 93
    sget-object p1, Lcom/android/tools/r8/internal/S5;->a:Lcom/android/tools/r8/internal/xq0;

    .line 94
    sget-object p1, Lcom/android/tools/r8/internal/R5;->b:Lcom/android/tools/r8/internal/R5;

    return-object p1

    :cond_0
    const/4 v0, 0x1

    .line 95
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    if-ne p2, v1, :cond_1

    .line 96
    sget-object p1, Lcom/android/tools/r8/internal/R5;->c:Lcom/android/tools/r8/internal/R5;

    return-object p1

    :cond_1
    const/4 v1, 0x2

    .line 97
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    if-ne p2, v1, :cond_2

    .line 98
    sget-object p1, Lcom/android/tools/r8/internal/R5;->d:Lcom/android/tools/r8/internal/R5;

    return-object p1

    :cond_2
    const/4 v1, 0x4

    .line 99
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    if-ne p2, v1, :cond_3

    .line 100
    sget-object p1, Lcom/android/tools/r8/internal/R5;->e:Lcom/android/tools/r8/internal/R5;

    return-object p1

    :cond_3
    const/4 v1, 0x3

    .line 101
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    if-ne p2, v1, :cond_4

    .line 102
    sget-object p1, Lcom/android/tools/r8/internal/R5;->f:Lcom/android/tools/r8/internal/R5;

    return-object p1

    :cond_4
    const/4 v1, 0x5

    .line 103
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    if-ne p2, v1, :cond_5

    .line 104
    iget-object p1, p0, Lcom/android/tools/r8/internal/Ub;->j:Lcom/android/tools/r8/internal/S5;

    new-instance p2, Lcom/android/tools/r8/internal/JC;

    invoke-direct {p2, v0}, Lcom/android/tools/r8/internal/JC;-><init>(I)V

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/S5;->a(Lcom/android/tools/r8/internal/G;)Lcom/android/tools/r8/internal/R5;

    move-result-object p1

    return-object p1

    :cond_5
    const/4 v0, 0x6

    .line 105
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    if-ne p2, v0, :cond_6

    const-string p1, "dummy"

    .line 106
    invoke-static {p1}, Lcom/android/tools/r8/internal/xq0;->e(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;

    move-result-object p1

    .line 107
    invoke-static {p1}, Lcom/android/tools/r8/internal/S5;->a(Lcom/android/tools/r8/internal/xq0;)Lcom/android/tools/r8/internal/R5;

    move-result-object p1

    return-object p1

    .line 108
    :cond_6
    instance-of v0, p2, Ljava/lang/String;

    if-eqz v0, :cond_7

    .line 109
    check-cast p2, Ljava/lang/String;

    invoke-static {p2}, Lcom/android/tools/r8/internal/xq0;->e(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;

    move-result-object p1

    .line 110
    invoke-static {p1}, Lcom/android/tools/r8/internal/S5;->a(Lcom/android/tools/r8/internal/xq0;)Lcom/android/tools/r8/internal/R5;

    move-result-object p1

    return-object p1

    .line 111
    :cond_7
    instance-of v0, p2, Lcom/android/tools/r8/internal/rP;

    if-eqz v0, :cond_b

    .line 112
    check-cast p2, Lcom/android/tools/r8/internal/rP;

    :goto_0
    if-eqz p2, :cond_9

    .line 113
    iget v0, p2, Lcom/android/tools/r8/internal/G;->a:I

    if-ltz v0, :cond_8

    goto :goto_1

    .line 114
    :cond_8
    iget-object p2, p2, Lcom/android/tools/r8/internal/G;->e:Lcom/android/tools/r8/internal/G;

    goto :goto_0

    :cond_9
    :goto_1
    if-eqz p2, :cond_a

    .line 115
    iget v0, p2, Lcom/android/tools/r8/internal/G;->a:I

    const/16 v1, 0xbb

    if-ne v0, v1, :cond_a

    .line 116
    check-cast p2, Lcom/android/tools/r8/internal/tr0;

    iget-object p1, p2, Lcom/android/tools/r8/internal/tr0;->g:Ljava/lang/String;

    invoke-static {p1}, Lcom/android/tools/r8/internal/xq0;->e(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;

    move-result-object p1

    .line 117
    invoke-static {p1}, Lcom/android/tools/r8/internal/S5;->a(Lcom/android/tools/r8/internal/xq0;)Lcom/android/tools/r8/internal/R5;

    move-result-object p1

    return-object p1

    .line 118
    :cond_a
    new-instance p2, Lcom/android/tools/r8/internal/q2;

    const-string v0, "LabelNode does not designate a NEW instruction"

    invoke-direct {p2, p1, v0}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw p2

    .line 122
    :cond_b
    new-instance v0, Lcom/android/tools/r8/internal/q2;

    .line 123
    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Illegal stack map frame value "

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    .line 124
    invoke-direct {v0, p1, p2}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw v0
.end method

.method public final a(Lcom/android/tools/r8/internal/dx;Lcom/android/tools/r8/internal/fx;)Lcom/android/tools/r8/internal/dx;
    .locals 7

    .line 1
    invoke-static {p1}, Lcom/android/tools/r8/internal/p2;->a(Lcom/android/tools/r8/internal/dx;)Lcom/android/tools/r8/internal/dx;

    move-result-object p1

    .line 2
    iget-object v0, p2, Lcom/android/tools/r8/internal/fx;->h:Ljava/util/ArrayList;

    if-nez v0, :cond_0

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    .line 3
    :cond_0
    iget v1, p0, Lcom/android/tools/r8/internal/Ub;->l:I

    .line 4
    iget v2, p2, Lcom/android/tools/r8/internal/fx;->g:I

    const/4 v3, 0x0

    const/4 v4, -0x1

    const/4 v5, 0x2

    if-eq v2, v4, :cond_5

    if-eqz v2, :cond_5

    const/4 v4, 0x1

    if-eq v2, v4, :cond_6

    if-eq v2, v5, :cond_2

    const/4 v0, 0x3

    if-eq v2, v0, :cond_9

    const/4 v0, 0x4

    if-ne v2, v0, :cond_1

    goto/16 :goto_2

    .line 37
    :cond_1
    new-instance p1, Lcom/android/tools/r8/internal/q2;

    iget v0, p2, Lcom/android/tools/r8/internal/fx;->g:I

    const-string v1, "Illegal frame type "

    .line 38
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/S40;->a(ILjava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 39
    invoke-direct {p1, p2, v0}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw p1

    .line 40
    :cond_2
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_9

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    if-lez v1, :cond_4

    if-le v1, v4, :cond_3

    add-int/lit8 v2, v1, -0x2

    .line 44
    invoke-virtual {p1, v2}, Lcom/android/tools/r8/internal/dx;->a(I)Lcom/android/tools/r8/internal/ut0;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/R5;

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/R5;->a()I

    move-result v2

    if-ne v2, v5, :cond_3

    add-int/lit8 v1, v1, -0x2

    goto :goto_0

    :cond_3
    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    .line 45
    :cond_4
    new-instance p1, Lcom/android/tools/r8/internal/q2;

    const-string v0, "Cannot chop more locals than defined"

    invoke-direct {p1, p2, v0}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw p1

    :cond_5
    move v1, v3

    .line 46
    :cond_6
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_9

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 47
    invoke-virtual {p0, p2, v2}, Lcom/android/tools/r8/internal/Ub;->a(Lcom/android/tools/r8/internal/fx;Ljava/lang/Object;)Lcom/android/tools/r8/internal/R5;

    move-result-object v2

    .line 48
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/R5;->a()I

    move-result v4

    add-int/2addr v4, v1

    .line 49
    iget v6, p1, Lcom/android/tools/r8/internal/dx;->c:I

    if-gt v4, v6, :cond_8

    add-int/lit8 v4, v1, 0x1

    .line 50
    invoke-virtual {p1, v1, v2}, Lcom/android/tools/r8/internal/dx;->a(ILcom/android/tools/r8/internal/R5;)V

    .line 51
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/R5;->a()I

    move-result v2

    if-ne v2, v5, :cond_7

    add-int/lit8 v1, v1, 0x2

    .line 52
    sget-object v2, Lcom/android/tools/r8/internal/S5;->a:Lcom/android/tools/r8/internal/xq0;

    .line 53
    sget-object v2, Lcom/android/tools/r8/internal/R5;->b:Lcom/android/tools/r8/internal/R5;

    .line 54
    invoke-virtual {p1, v4, v2}, Lcom/android/tools/r8/internal/dx;->a(ILcom/android/tools/r8/internal/R5;)V

    goto :goto_1

    :cond_7
    move v1, v4

    goto :goto_1

    .line 55
    :cond_8
    new-instance p1, Lcom/android/tools/r8/internal/q2;

    const-string v0, "Cannot append more locals than maxLocals"

    invoke-direct {p1, p2, v0}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw p1

    .line 81
    :cond_9
    :goto_2
    iput v1, p0, Lcom/android/tools/r8/internal/Ub;->l:I

    .line 82
    :goto_3
    iget v0, p1, Lcom/android/tools/r8/internal/dx;->c:I

    if-ge v1, v0, :cond_a

    add-int/lit8 v0, v1, 0x1

    .line 83
    sget-object v2, Lcom/android/tools/r8/internal/S5;->a:Lcom/android/tools/r8/internal/xq0;

    .line 84
    sget-object v2, Lcom/android/tools/r8/internal/R5;->b:Lcom/android/tools/r8/internal/R5;

    .line 85
    invoke-virtual {p1, v1, v2}, Lcom/android/tools/r8/internal/dx;->a(ILcom/android/tools/r8/internal/R5;)V

    move v1, v0

    goto :goto_3

    .line 88
    :cond_a
    iget-object v0, p2, Lcom/android/tools/r8/internal/fx;->i:Ljava/util/ArrayList;

    if-nez v0, :cond_b

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    .line 89
    :cond_b
    iput v3, p1, Lcom/android/tools/r8/internal/dx;->d:I

    .line 90
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_4
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_c

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    .line 91
    invoke-virtual {p0, p2, v1}, Lcom/android/tools/r8/internal/Ub;->a(Lcom/android/tools/r8/internal/fx;Ljava/lang/Object;)Lcom/android/tools/r8/internal/R5;

    move-result-object v1

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/dx;->a(Lcom/android/tools/r8/internal/R5;)V

    goto :goto_4

    :cond_c
    return-object p1
.end method

.method public final a(I)V
    .locals 3

    .line 125
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/IC;->j(I)Lcom/android/tools/r8/internal/G;

    move-result-object v0

    .line 126
    iget-object v0, v0, Lcom/android/tools/r8/internal/G;->e:Lcom/android/tools/r8/internal/G;

    :goto_0
    if-eqz v0, :cond_3

    .line 127
    iget v1, v0, Lcom/android/tools/r8/internal/G;->a:I

    if-ltz v1, :cond_0

    goto :goto_1

    .line 128
    :cond_0
    instance-of v1, v0, Lcom/android/tools/r8/internal/fx;

    if-eqz v1, :cond_2

    .line 129
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/p2;->e:[Lcom/android/tools/r8/internal/dx;

    add-int/lit8 p1, p1, 0x1

    .line 130
    aget-object v0, v0, p1

    if-eqz v0, :cond_1

    goto :goto_2

    .line 131
    :cond_1
    new-instance v0, Lcom/android/tools/r8/internal/q2;

    const/4 v1, 0x0

    const-string v2, "Expected stack map frame at instruction "

    .line 132
    invoke-static {p1, v2}, Lcom/android/tools/r8/internal/S40;->a(ILjava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 581
    invoke-direct {v0, v1, p1}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw v0

    .line 582
    :cond_2
    iget-object v0, v0, Lcom/android/tools/r8/internal/G;->e:Lcom/android/tools/r8/internal/G;

    goto :goto_0

    :cond_3
    :goto_2
    return-void
.end method

.method public final a(ILcom/android/tools/r8/internal/dx;Z)V
    .locals 8

    .line 587
    iget-object v0, p0, Lcom/android/tools/r8/internal/p2;->e:[Lcom/android/tools/r8/internal/dx;

    .line 588
    aget-object v1, v0, p1

    const/4 v2, 0x0

    if-nez v1, :cond_1

    if-nez p3, :cond_0

    .line 593
    invoke-static {p2}, Lcom/android/tools/r8/internal/p2;->a(Lcom/android/tools/r8/internal/dx;)Lcom/android/tools/r8/internal/dx;

    move-result-object p2

    aput-object p2, v0, p1

    goto/16 :goto_3

    .line 594
    :cond_0
    new-instance p2, Lcom/android/tools/r8/internal/q2;

    const-string p3, "Expected stack map frame at instruction "

    .line 595
    invoke-static {p1, p3}, Lcom/android/tools/r8/internal/S40;->a(ILjava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 973
    invoke-direct {p2, v2, p1}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw p2

    .line 974
    :cond_1
    iget p3, p2, Lcom/android/tools/r8/internal/dx;->c:I

    .line 975
    iget v0, v1, Lcom/android/tools/r8/internal/dx;->c:I

    if-ne p3, v0, :cond_a

    const/4 v0, 0x0

    move v3, v0

    :goto_0
    const-string v4, " and "

    const-string v5, ": "

    if-ge v3, p3, :cond_4

    .line 976
    invoke-virtual {p2, v3}, Lcom/android/tools/r8/internal/dx;->a(I)Lcom/android/tools/r8/internal/ut0;

    move-result-object v6

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/dx;->a(I)Lcom/android/tools/r8/internal/ut0;

    move-result-object v7

    .line 977
    check-cast v6, Lcom/android/tools/r8/internal/R5;

    check-cast v7, Lcom/android/tools/r8/internal/R5;

    .line 978
    invoke-virtual {v6, v7}, Lcom/android/tools/r8/internal/R5;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-nez v7, :cond_2

    .line 979
    sget-object v6, Lcom/android/tools/r8/internal/R5;->b:Lcom/android/tools/r8/internal/R5;

    .line 980
    :cond_2
    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/dx;->a(I)Lcom/android/tools/r8/internal/ut0;

    move-result-object v7

    invoke-virtual {v6, v7}, Lcom/android/tools/r8/internal/R5;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-nez v6, :cond_3

    .line 984
    invoke-virtual {p2, v3}, Lcom/android/tools/r8/internal/dx;->a(I)Lcom/android/tools/r8/internal/ut0;

    move-result-object p2

    .line 986
    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/dx;->a(I)Lcom/android/tools/r8/internal/ut0;

    move-result-object p3

    .line 987
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "incompatible types at local "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    goto :goto_2

    :cond_3
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 988
    :cond_4
    iget p3, p2, Lcom/android/tools/r8/internal/dx;->d:I

    .line 989
    iget v3, v1, Lcom/android/tools/r8/internal/dx;->d:I

    if-eq p3, v3, :cond_5

    const-string p2, "incompatible stack heights"

    goto :goto_2

    :cond_5
    :goto_1
    if-ge v0, p3, :cond_8

    .line 991
    iget-object v3, p2, Lcom/android/tools/r8/internal/dx;->b:[Lcom/android/tools/r8/internal/ut0;

    iget v6, p2, Lcom/android/tools/r8/internal/dx;->c:I

    add-int/2addr v6, v0

    aget-object v3, v3, v6

    iget-object v6, v1, Lcom/android/tools/r8/internal/dx;->b:[Lcom/android/tools/r8/internal/ut0;

    iget v7, v1, Lcom/android/tools/r8/internal/dx;->c:I

    add-int/2addr v7, v0

    aget-object v6, v6, v7

    .line 992
    check-cast v3, Lcom/android/tools/r8/internal/R5;

    check-cast v6, Lcom/android/tools/r8/internal/R5;

    .line 993
    invoke-virtual {v3, v6}, Lcom/android/tools/r8/internal/R5;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-nez v6, :cond_6

    .line 994
    sget-object v3, Lcom/android/tools/r8/internal/R5;->b:Lcom/android/tools/r8/internal/R5;

    .line 995
    :cond_6
    iget-object v6, v1, Lcom/android/tools/r8/internal/dx;->b:[Lcom/android/tools/r8/internal/ut0;

    iget v7, v1, Lcom/android/tools/r8/internal/dx;->c:I

    add-int/2addr v7, v0

    aget-object v6, v6, v7

    .line 996
    invoke-virtual {v3, v6}, Lcom/android/tools/r8/internal/R5;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_7

    .line 997
    iget-object p3, p2, Lcom/android/tools/r8/internal/dx;->b:[Lcom/android/tools/r8/internal/ut0;

    iget p2, p2, Lcom/android/tools/r8/internal/dx;->c:I

    add-int/2addr p2, v0

    aget-object p2, p3, p2

    .line 998
    iget-object p3, v1, Lcom/android/tools/r8/internal/dx;->b:[Lcom/android/tools/r8/internal/ut0;

    iget v1, v1, Lcom/android/tools/r8/internal/dx;->c:I

    add-int/2addr v1, v0

    aget-object p3, p3, v1

    .line 999
    new-instance v1, Ljava/lang/StringBuilder;

    const-string v3, "incompatible types at stack item "

    invoke-direct {v1, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    goto :goto_2

    :cond_7
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_8
    move-object p2, v2

    :goto_2
    if-nez p2, :cond_9

    :goto_3
    return-void

    .line 1000
    :cond_9
    new-instance p3, Lcom/android/tools/r8/internal/q2;

    .line 1001
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "Stack map frame incompatible with frame at instruction "

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " ("

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ")"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 1002
    invoke-direct {p3, v2, p1}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw p3

    .line 1003
    :cond_a
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1
.end method

.method public final d(Lcom/android/tools/r8/internal/Vb;)V
    .locals 16

    move-object/from16 v1, p0

    move-object/from16 v0, p1

    .line 1
    iget-object v2, v0, Lcom/android/tools/r8/internal/fV;->q:Lcom/android/tools/r8/internal/IC;

    iput-object v2, v1, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    .line 2
    iget-object v2, v0, Lcom/android/tools/r8/internal/fV;->e:Ljava/lang/String;

    invoke-static {v2}, Lcom/android/tools/r8/internal/xq0;->c(Ljava/lang/String;)I

    move-result v2

    shr-int/lit8 v2, v2, 0x2

    iput v2, v1, Lcom/android/tools/r8/internal/Ub;->l:I

    .line 3
    iget v3, v0, Lcom/android/tools/r8/internal/fV;->c:I

    const/16 v4, 0x8

    and-int/2addr v3, v4

    const/4 v5, 0x1

    if-eqz v3, :cond_0

    sub-int/2addr v2, v5

    .line 4
    iput v2, v1, Lcom/android/tools/r8/internal/Ub;->l:I

    .line 5
    :cond_0
    iget-object v2, v1, Lcom/android/tools/r8/internal/p2;->e:[Lcom/android/tools/r8/internal/dx;

    const/4 v3, 0x0

    .line 6
    aget-object v6, v2, v3

    const/4 v7, -0x1

    .line 7
    iget-object v8, v0, Lcom/android/tools/r8/internal/fV;->q:Lcom/android/tools/r8/internal/IC;

    invoke-virtual {v8}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 8
    iget v9, v8, Lcom/android/tools/r8/internal/IC;->b:I

    if-ltz v9, :cond_1b

    if-nez v9, :cond_1

    const/4 v8, 0x0

    goto :goto_0

    .line 9
    :cond_1
    iget-object v8, v8, Lcom/android/tools/r8/internal/IC;->c:Lcom/android/tools/r8/internal/G;

    .line 10
    iget-object v9, v8, Lcom/android/tools/r8/internal/G;->d:Lcom/android/tools/r8/internal/G;

    :goto_0
    move v11, v3

    move-object v9, v6

    :goto_1
    if-eqz v8, :cond_2

    move v12, v5

    goto :goto_2

    :cond_2
    move v12, v3

    :goto_2
    const-string v13, ": "

    const-string v14, "Error at instruction "

    if-eqz v12, :cond_7

    if-eqz v8, :cond_6

    .line 11
    iget-object v12, v8, Lcom/android/tools/r8/internal/G;->e:Lcom/android/tools/r8/internal/G;

    .line 12
    instance-of v15, v8, Lcom/android/tools/r8/internal/fx;

    if-eqz v15, :cond_3

    .line 14
    :try_start_0
    move-object v10, v8

    check-cast v10, Lcom/android/tools/r8/internal/fx;

    invoke-virtual {v1, v9, v10}, Lcom/android/tools/r8/internal/Ub;->a(Lcom/android/tools/r8/internal/dx;Lcom/android/tools/r8/internal/fx;)Lcom/android/tools/r8/internal/dx;

    move-result-object v9
    :try_end_0
    .catch Lcom/android/tools/r8/internal/q2; {:try_start_0 .. :try_end_0} :catch_0

    add-int/lit8 v10, v7, 0x1

    :goto_3
    if-gt v10, v11, :cond_3

    .line 15
    iget-object v13, v1, Lcom/android/tools/r8/internal/p2;->e:[Lcom/android/tools/r8/internal/dx;

    .line 16
    aput-object v9, v13, v10

    add-int/lit8 v10, v10, 0x1

    goto :goto_3

    :catch_0
    move-exception v0

    .line 17
    new-instance v2, Lcom/android/tools/r8/internal/q2;

    iget-object v3, v0, Lcom/android/tools/r8/internal/q2;->b:Lcom/android/tools/r8/internal/G;

    .line 18
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v4

    .line 19
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5, v14}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v5, v11}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .line 20
    invoke-direct {v2, v3, v4, v0}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;Ljava/lang/Exception;)V

    throw v2

    .line 21
    :cond_3
    iget v8, v8, Lcom/android/tools/r8/internal/G;->a:I

    if-ltz v8, :cond_4

    goto :goto_4

    :cond_4
    if-eqz v15, :cond_5

    :goto_4
    move v7, v11

    :cond_5
    add-int/lit8 v11, v11, 0x1

    move-object v8, v12

    goto :goto_1

    .line 22
    :cond_6
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0

    :cond_7
    move v7, v3

    .line 23
    :goto_5
    iget-object v8, v1, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    .line 24
    iget v8, v8, Lcom/android/tools/r8/internal/IC;->b:I

    if-ge v7, v8, :cond_1a

    .line 25
    aget-object v8, v2, v7

    .line 30
    :try_start_1
    iget-object v9, v0, Lcom/android/tools/r8/internal/fV;->q:Lcom/android/tools/r8/internal/IC;

    invoke-virtual {v9, v7}, Lcom/android/tools/r8/internal/IC;->j(I)Lcom/android/tools/r8/internal/G;

    move-result-object v9
    :try_end_1
    .catch Lcom/android/tools/r8/internal/q2; {:try_start_1 .. :try_end_1} :catch_3
    .catch Ljava/lang/RuntimeException; {:try_start_1 .. :try_end_1} :catch_2

    .line 31
    :try_start_2
    iget v10, v9, Lcom/android/tools/r8/internal/G;->a:I

    .line 32
    invoke-virtual {v9}, Lcom/android/tools/r8/internal/G;->a()I

    move-result v11

    if-eq v11, v4, :cond_13

    const/16 v12, 0xf

    if-eq v11, v12, :cond_13

    const/16 v12, 0xe

    if-ne v11, v12, :cond_8

    goto/16 :goto_8

    .line 39
    :cond_8
    invoke-virtual {v6, v8}, Lcom/android/tools/r8/internal/dx;->a(Lcom/android/tools/r8/internal/dx;)Lcom/android/tools/r8/internal/dx;

    move-result-object v11

    iget-object v12, v1, Lcom/android/tools/r8/internal/Ub;->j:Lcom/android/tools/r8/internal/S5;

    invoke-virtual {v11, v9, v12}, Lcom/android/tools/r8/internal/dx;->a(Lcom/android/tools/r8/internal/G;Lcom/android/tools/r8/internal/S5;)V

    .line 41
    instance-of v11, v9, Lcom/android/tools/r8/internal/NJ;

    if-eqz v11, :cond_b

    const/16 v11, 0xa8

    if-eq v10, v11, :cond_a

    .line 45
    move-object v11, v9

    check-cast v11, Lcom/android/tools/r8/internal/NJ;

    .line 46
    iget-object v12, v1, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    iget-object v11, v11, Lcom/android/tools/r8/internal/NJ;->g:Lcom/android/tools/r8/internal/rP;

    invoke-virtual {v12, v11}, Lcom/android/tools/r8/internal/IC;->b(Lcom/android/tools/r8/internal/G;)I

    move-result v11

    .line 47
    invoke-virtual {v1, v11, v6, v5}, Lcom/android/tools/r8/internal/Ub;->a(ILcom/android/tools/r8/internal/dx;Z)V

    const/16 v11, 0xa7

    if-ne v10, v11, :cond_9

    .line 49
    invoke-virtual {v1, v7}, Lcom/android/tools/r8/internal/Ub;->a(I)V

    goto/16 :goto_9

    :cond_9
    add-int/lit8 v10, v7, 0x1

    .line 51
    invoke-virtual {v1, v10, v6, v3}, Lcom/android/tools/r8/internal/Ub;->a(ILcom/android/tools/r8/internal/dx;Z)V

    goto/16 :goto_9

    .line 52
    :cond_a
    new-instance v0, Lcom/android/tools/r8/internal/q2;

    const-string v2, "JSR instructions are unsupported"

    invoke-direct {v0, v9, v2}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw v0

    .line 62
    :cond_b
    instance-of v11, v9, Lcom/android/tools/r8/internal/OS;

    if-eqz v11, :cond_d

    .line 63
    move-object v10, v9

    check-cast v10, Lcom/android/tools/r8/internal/OS;

    .line 64
    iget-object v11, v1, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    iget-object v12, v10, Lcom/android/tools/r8/internal/OS;->g:Lcom/android/tools/r8/internal/rP;

    invoke-virtual {v11, v12}, Lcom/android/tools/r8/internal/IC;->b(Lcom/android/tools/r8/internal/G;)I

    move-result v11

    .line 65
    invoke-virtual {v1, v11, v6, v5}, Lcom/android/tools/r8/internal/Ub;->a(ILcom/android/tools/r8/internal/dx;Z)V

    move v11, v3

    .line 66
    :goto_6
    iget-object v12, v10, Lcom/android/tools/r8/internal/OS;->i:Ljava/util/ArrayList;

    invoke-virtual {v12}, Ljava/util/ArrayList;->size()I

    move-result v12

    if-ge v11, v12, :cond_c

    .line 67
    iget-object v12, v10, Lcom/android/tools/r8/internal/OS;->i:Ljava/util/ArrayList;

    invoke-virtual {v12, v11}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v12

    check-cast v12, Lcom/android/tools/r8/internal/rP;

    .line 68
    iget-object v15, v1, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    invoke-virtual {v15, v12}, Lcom/android/tools/r8/internal/IC;->b(Lcom/android/tools/r8/internal/G;)I

    move-result v12

    .line 70
    invoke-virtual {v1, v12, v6, v5}, Lcom/android/tools/r8/internal/Ub;->a(ILcom/android/tools/r8/internal/dx;Z)V

    add-int/lit8 v11, v11, 0x1

    goto :goto_6

    .line 72
    :cond_c
    invoke-virtual {v1, v7}, Lcom/android/tools/r8/internal/Ub;->a(I)V

    goto :goto_9

    .line 73
    :cond_d
    instance-of v11, v9, Lcom/android/tools/r8/internal/Qo0;

    if-eqz v11, :cond_f

    .line 74
    move-object v10, v9

    check-cast v10, Lcom/android/tools/r8/internal/Qo0;

    .line 75
    iget-object v11, v1, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    iget-object v12, v10, Lcom/android/tools/r8/internal/Qo0;->i:Lcom/android/tools/r8/internal/rP;

    invoke-virtual {v11, v12}, Lcom/android/tools/r8/internal/IC;->b(Lcom/android/tools/r8/internal/G;)I

    move-result v11

    .line 77
    invoke-virtual {v1, v11, v6, v5}, Lcom/android/tools/r8/internal/Ub;->a(ILcom/android/tools/r8/internal/dx;Z)V

    move v11, v3

    .line 79
    :goto_7
    iget-object v12, v10, Lcom/android/tools/r8/internal/Qo0;->j:Ljava/util/ArrayList;

    invoke-virtual {v12}, Ljava/util/ArrayList;->size()I

    move-result v12

    if-ge v11, v12, :cond_e

    .line 80
    iget-object v12, v10, Lcom/android/tools/r8/internal/Qo0;->j:Ljava/util/ArrayList;

    invoke-virtual {v12, v11}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v12

    check-cast v12, Lcom/android/tools/r8/internal/rP;

    .line 82
    iget-object v15, v1, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    invoke-virtual {v15, v12}, Lcom/android/tools/r8/internal/IC;->b(Lcom/android/tools/r8/internal/G;)I

    move-result v12

    .line 83
    invoke-virtual {v1, v12, v6, v5}, Lcom/android/tools/r8/internal/Ub;->a(ILcom/android/tools/r8/internal/dx;Z)V

    add-int/lit8 v11, v11, 0x1

    goto :goto_7

    .line 85
    :cond_e
    invoke-virtual {v1, v7}, Lcom/android/tools/r8/internal/Ub;->a(I)V

    goto :goto_9

    :cond_f
    const/16 v11, 0xa9

    if-eq v10, v11, :cond_12

    const/16 v11, 0xbf

    if-eq v10, v11, :cond_11

    const/16 v11, 0xac

    if-lt v10, v11, :cond_10

    const/16 v11, 0xb1

    if-le v10, v11, :cond_11

    :cond_10
    add-int/lit8 v10, v7, 0x1

    .line 89
    invoke-virtual {v1, v10, v6, v3}, Lcom/android/tools/r8/internal/Ub;->a(ILcom/android/tools/r8/internal/dx;Z)V

    goto :goto_9

    .line 91
    :cond_11
    invoke-virtual {v1, v7}, Lcom/android/tools/r8/internal/Ub;->a(I)V

    goto :goto_9

    .line 92
    :cond_12
    new-instance v0, Lcom/android/tools/r8/internal/q2;

    const-string v2, "RET instructions are unsupported"

    invoke-direct {v0, v9, v2}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;)V

    throw v0

    :cond_13
    :goto_8
    add-int/lit8 v10, v7, 0x1

    .line 93
    invoke-virtual {v1, v10, v8, v3}, Lcom/android/tools/r8/internal/Ub;->a(ILcom/android/tools/r8/internal/dx;Z)V

    .line 94
    :goto_9
    iget-object v10, v1, Lcom/android/tools/r8/internal/p2;->d:[Ljava/util/List;

    aget-object v10, v10, v7

    if-eqz v10, :cond_15

    .line 95
    invoke-interface {v10}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v10

    :goto_a
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    move-result v11

    if-eqz v11, :cond_15

    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lcom/android/tools/r8/internal/tq0;

    .line 97
    iget-object v12, v11, Lcom/android/tools/r8/internal/tq0;->d:Ljava/lang/String;

    if-nez v12, :cond_14

    const-string v12, "java/lang/Throwable"

    .line 98
    invoke-static {v12}, Lcom/android/tools/r8/internal/xq0;->e(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;

    move-result-object v12

    goto :goto_b

    .line 100
    :cond_14
    invoke-static {v12}, Lcom/android/tools/r8/internal/xq0;->e(Ljava/lang/String;)Lcom/android/tools/r8/internal/xq0;

    move-result-object v12

    .line 102
    :goto_b
    invoke-static {v8}, Lcom/android/tools/r8/internal/p2;->a(Lcom/android/tools/r8/internal/dx;)Lcom/android/tools/r8/internal/dx;

    move-result-object v15

    .line 103
    iput v3, v15, Lcom/android/tools/r8/internal/dx;->d:I

    .line 104
    invoke-static {v12}, Lcom/android/tools/r8/internal/S5;->a(Lcom/android/tools/r8/internal/xq0;)Lcom/android/tools/r8/internal/R5;

    move-result-object v12

    .line 105
    invoke-virtual {v15, v12}, Lcom/android/tools/r8/internal/dx;->a(Lcom/android/tools/r8/internal/R5;)V

    .line 106
    iget-object v12, v1, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    iget-object v11, v11, Lcom/android/tools/r8/internal/tq0;->c:Lcom/android/tools/r8/internal/rP;

    invoke-virtual {v12, v11}, Lcom/android/tools/r8/internal/IC;->b(Lcom/android/tools/r8/internal/G;)I

    move-result v11

    invoke-virtual {v1, v11, v15, v5}, Lcom/android/tools/r8/internal/Ub;->a(ILcom/android/tools/r8/internal/dx;Z)V

    goto :goto_a

    .line 107
    :cond_15
    iget-object v8, v1, Lcom/android/tools/r8/internal/Ub;->k:Lcom/android/tools/r8/internal/IC;

    invoke-virtual {v8, v7}, Lcom/android/tools/r8/internal/IC;->j(I)Lcom/android/tools/r8/internal/G;

    move-result-object v8

    .line 108
    iget-object v8, v8, Lcom/android/tools/r8/internal/G;->e:Lcom/android/tools/r8/internal/G;

    :goto_c
    if-eqz v8, :cond_18

    .line 109
    iget v10, v8, Lcom/android/tools/r8/internal/G;->a:I

    if-ltz v10, :cond_16

    goto :goto_d

    .line 110
    :cond_16
    instance-of v10, v8, Lcom/android/tools/r8/internal/fx;

    if-eqz v10, :cond_17

    :goto_d
    move v8, v5

    goto :goto_e

    .line 111
    :cond_17
    iget-object v8, v8, Lcom/android/tools/r8/internal/G;->e:Lcom/android/tools/r8/internal/G;
    :try_end_2
    .catch Lcom/android/tools/r8/internal/q2; {:try_start_2 .. :try_end_2} :catch_3
    .catch Ljava/lang/RuntimeException; {:try_start_2 .. :try_end_2} :catch_1

    goto :goto_c

    :cond_18
    move v8, v3

    :goto_e
    if-nez v8, :cond_19

    goto :goto_10

    :cond_19
    add-int/lit8 v7, v7, 0x1

    goto/16 :goto_5

    :catch_1
    move-exception v0

    move-object v10, v9

    goto :goto_f

    :catch_2
    move-exception v0

    const/4 v10, 0x0

    .line 112
    :goto_f
    new-instance v2, Lcom/android/tools/r8/internal/q2;

    .line 113
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v3

    .line 114
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4, v14}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 115
    invoke-direct {v2, v10, v3, v0}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;Ljava/lang/Exception;)V

    throw v2

    :catch_3
    move-exception v0

    .line 116
    new-instance v2, Lcom/android/tools/r8/internal/q2;

    iget-object v3, v0, Lcom/android/tools/r8/internal/q2;->b:Lcom/android/tools/r8/internal/G;

    .line 117
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v4

    .line 118
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5, v14}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .line 119
    invoke-direct {v2, v3, v4, v0}, Lcom/android/tools/r8/internal/q2;-><init>(Lcom/android/tools/r8/internal/G;Ljava/lang/String;Ljava/lang/Exception;)V

    throw v2

    :cond_1a
    :goto_10
    return-void

    .line 120
    :cond_1b
    new-instance v0, Ljava/lang/IndexOutOfBoundsException;

    invoke-direct {v0}, Ljava/lang/IndexOutOfBoundsException;-><init>()V

    throw v0
.end method
