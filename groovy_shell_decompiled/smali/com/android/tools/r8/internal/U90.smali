.class public final Lcom/android/tools/r8/internal/U90;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/graph/I2;

.field public final b:Lcom/android/tools/r8/graph/F2;

.field public final c:Lcom/android/tools/r8/graph/I2;

.field public final d:[Lcom/android/tools/r8/graph/l1;

.field public final e:Lcom/android/tools/r8/graph/E2;

.field public final f:Lcom/android/tools/r8/graph/J2;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;[Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/U90;->a:Lcom/android/tools/r8/graph/I2;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/U90;->b:Lcom/android/tools/r8/graph/F2;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/internal/U90;->c:Lcom/android/tools/r8/graph/I2;

    .line 5
    iput-object p4, p0, Lcom/android/tools/r8/internal/U90;->d:[Lcom/android/tools/r8/graph/l1;

    .line 6
    iput-object p5, p0, Lcom/android/tools/r8/internal/U90;->e:Lcom/android/tools/r8/graph/E2;

    .line 7
    iput-object p6, p0, Lcom/android/tools/r8/internal/U90;->f:Lcom/android/tools/r8/graph/J2;

    return-void
.end method
