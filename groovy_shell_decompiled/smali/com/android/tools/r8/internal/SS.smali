.class public final Lcom/android/tools/r8/internal/SS;
.super Lcom/android/tools/r8/internal/Az;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final j:Ljava/util/function/Function;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/B1;Ljava/util/function/Function;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Az;-><init>(Lcom/android/tools/r8/graph/B1;)V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/SS;->j:Ljava/util/function/Function;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/SS;->j:Ljava/util/function/Function;

    invoke-interface {v0, p1}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/J2;

    return-object p1
.end method
