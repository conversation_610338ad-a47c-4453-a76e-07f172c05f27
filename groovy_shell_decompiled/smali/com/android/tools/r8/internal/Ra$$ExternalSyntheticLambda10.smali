.class public final synthetic Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda10;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiFunction;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda10;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda10;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda10;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda10;->INSTANCE:Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda10;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lcom/android/tools/r8/internal/r9;

    check-cast p2, Lcom/android/tools/r8/internal/R40;

    invoke-static {p1, p2}, Lcom/android/tools/r8/internal/Ix;->a(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/r9;

    return-object p1
.end method
