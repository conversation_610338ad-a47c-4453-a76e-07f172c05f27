.class public final synthetic Lcom/android/tools/r8/internal/T50$$ExternalSyntheticLambda8;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/D5;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/D5;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/T50$$ExternalSyntheticLambda8;->f$0:Lcom/android/tools/r8/graph/D5;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/T50$$ExternalSyntheticLambda8;->f$0:Lcom/android/tools/r8/graph/D5;

    check-cast p1, Lcom/android/tools/r8/internal/J50;

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/T50;->i(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/J50;)V

    return-void
.end method
