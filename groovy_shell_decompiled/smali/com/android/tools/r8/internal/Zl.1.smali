.class public Lcom/android/tools/r8/internal/Zl;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/internal/Vl;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Vl;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/Zl;->a:Lcom/android/tools/r8/internal/Vl;

    return-void
.end method

.method public static a(Ljava/nio/file/Path;)Lcom/android/tools/r8/internal/Zl;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Zl;

    new-instance v1, Lcom/android/tools/r8/internal/Vl;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/Vl;-><init>(Ljava/nio/file/Path;)V

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/Zl;-><init>(Lcom/android/tools/r8/internal/Vl;)V

    return-object v0
.end method

.method public static a(Lcom/android/tools/r8/graph/E2;)Ljava/lang/String;
    .locals 2

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/J2;->l0()Ljava/lang/String;

    move-result-object v0

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/E0;->d0()Lcom/android/tools/r8/graph/J4;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/J4;->d()Ljava/lang/String;

    move-result-object p0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static a(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    const-string v0, "\r"

    const-string v1, "<CR>"

    .line 44
    invoke-virtual {p0, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/internal/Wl;Lcom/android/tools/r8/graph/E2;)V
    .locals 2

    .line 26
    invoke-static {p1}, Lcom/android/tools/r8/internal/Zl;->a(Lcom/android/tools/r8/graph/E2;)Ljava/lang/String;

    move-result-object v0

    .line 27
    invoke-interface {p0, v0}, Lcom/android/tools/r8/internal/Wl;->a(Ljava/lang/String;)V

    .line 30
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->C1()Ljava/lang/Iterable;

    move-result-object p1

    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    .line 31
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/x2;->l0()Ljava/lang/String;

    move-result-object v1

    .line 32
    invoke-interface {p0, v1}, Lcom/android/tools/r8/internal/Wl;->a(Ljava/lang/String;)V

    .line 35
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->i1()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 36
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/i0;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/android/tools/r8/internal/Sn0;->f(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    .line 37
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    .line 38
    invoke-interface {p0, v1}, Lcom/android/tools/r8/internal/Wl;->a(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    const-string v0, "<nocode>"

    .line 43
    invoke-interface {p0, v0}, Lcom/android/tools/r8/internal/Wl;->a(Ljava/lang/String;)V

    goto :goto_0

    :cond_2
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/y;)V
    .locals 3

    .line 9
    :try_start_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Zl;->a:Lcom/android/tools/r8/internal/Vl;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Vl;->a()Lcom/android/tools/r8/internal/Wl;

    move-result-object v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 10
    :try_start_1
    new-instance v1, Ljava/util/ArrayList;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object p1

    invoke-direct {v1, p1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 11
    sget-object p1, Lcom/android/tools/r8/internal/Zl$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/Zl$$ExternalSyntheticLambda0;

    invoke-static {p1}, Ljava/util/Comparator;->comparing(Ljava/util/function/Function;)Ljava/util/Comparator;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->sort(Ljava/util/Comparator;)V

    const/4 p1, 0x0

    .line 12
    :goto_0
    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v2

    if-ge p1, v2, :cond_0

    .line 13
    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/E2;

    .line 14
    invoke-static {v0, v2}, Lcom/android/tools/r8/internal/Zl;->a(Lcom/android/tools/r8/internal/Wl;Lcom/android/tools/r8/graph/E2;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 16
    :cond_0
    :try_start_2
    invoke-interface {v0}, Ljava/io/Closeable;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    return-void

    :catchall_0
    move-exception p1

    .line 17
    :try_start_3
    invoke-interface {v0}, Ljava/io/Closeable;->close()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    goto :goto_1

    :catchall_1
    move-exception v0

    :try_start_4
    invoke-virtual {p1, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_1
    throw p1
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_0

    :catch_0
    move-exception p1

    .line 25
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public final a(Lcom/android/tools/r8/internal/op0;)V
    .locals 1

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/Zl;->a:Lcom/android/tools/r8/internal/Vl;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Vl;->a()Lcom/android/tools/r8/internal/Wl;

    move-result-object v0

    .line 6
    :try_start_0
    invoke-interface {p1, v0}, Lcom/android/tools/r8/internal/op0;->accept(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 7
    invoke-interface {v0}, Ljava/io/Closeable;->close()V

    return-void

    :catchall_0
    move-exception p1

    .line 8
    :try_start_1
    invoke-interface {v0}, Ljava/io/Closeable;->close()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    goto :goto_0

    :catchall_1
    move-exception v0

    invoke-virtual {p1, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_0
    throw p1
.end method
