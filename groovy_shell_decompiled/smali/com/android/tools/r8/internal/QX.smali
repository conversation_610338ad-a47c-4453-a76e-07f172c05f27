.class public final Lcom/android/tools/r8/internal/QX;
.super Lcom/android/tools/r8/internal/G4;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/G4;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/E2;)Z
    .locals 0

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/b1;->s0()Z

    move-result p1

    return p1
.end method

.method public final f()Ljava/lang/String;
    .locals 1

    const-string v0, "NoClassAnnotationCollisions"

    return-object v0
.end method
