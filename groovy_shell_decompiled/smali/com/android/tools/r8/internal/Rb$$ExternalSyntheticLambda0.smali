.class public final synthetic Lcom/android/tools/r8/internal/Rb$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/a2;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Rb;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/sV;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Rb;Lcom/android/tools/r8/internal/sV;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Rb$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Rb;

    iput-object p2, p0, Lcom/android/tools/r8/internal/Rb$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/sV;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/Rb$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Rb;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Rb$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/sV;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Rb;->a(Lcom/android/tools/r8/internal/sV;)V

    return-void
.end method
