.class public Lcom/android/tools/r8/internal/Uf;
.super Lcom/android/tools/r8/internal/O30;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic c:Z = true


# instance fields
.field public final b:Ljava/util/Map;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/O30;-><init>()V

    .line 2
    invoke-static {}, Ljava/util/Collections;->emptyMap()Ljava/util/Map;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>(Ljava/util/Map;)V
    .locals 0

    .line 3
    invoke-direct {p0}, Lcom/android/tools/r8/internal/O30;-><init>()V

    .line 4
    iput-object p1, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/zf;Z)Lcom/android/tools/r8/internal/O30;
    .locals 2

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/R30;

    if-eqz v0, :cond_3

    .line 8
    sget-object p1, Lcom/android/tools/r8/internal/R30;->d:Lcom/android/tools/r8/internal/R30;

    if-ne v0, p1, :cond_0

    return-object p0

    :cond_0
    if-eqz p2, :cond_1

    .line 9
    sget-object p1, Lcom/android/tools/r8/internal/R30;->c:Lcom/android/tools/r8/internal/R30;

    goto :goto_0

    :cond_1
    sget-object p1, Lcom/android/tools/r8/internal/R30;->b:Lcom/android/tools/r8/internal/R30;

    :goto_0
    if-ne v0, p1, :cond_2

    return-object p0

    .line 10
    :cond_2
    sget-object p1, Lcom/android/tools/r8/internal/k7;->b:Lcom/android/tools/r8/internal/k7;

    return-object p1

    .line 14
    :cond_3
    new-instance v0, Ljava/util/HashMap;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    .line 15
    invoke-interface {v1}, Ljava/util/Map;->size()I

    move-result v1

    add-int/lit8 v1, v1, 0x1

    invoke-direct {v0, v1}, Ljava/util/HashMap;-><init>(I)V

    .line 16
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-virtual {v0, v1}, Ljava/util/HashMap;->putAll(Ljava/util/Map;)V

    if-eqz p2, :cond_4

    .line 17
    sget-object p2, Lcom/android/tools/r8/internal/R30;->c:Lcom/android/tools/r8/internal/R30;

    goto :goto_1

    :cond_4
    sget-object p2, Lcom/android/tools/r8/internal/R30;->b:Lcom/android/tools/r8/internal/R30;

    .line 18
    :goto_1
    invoke-virtual {v0, p1, p2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 19
    new-instance p1, Lcom/android/tools/r8/internal/Uf;

    invoke-direct {p1, v0}, Lcom/android/tools/r8/internal/Uf;-><init>(Ljava/util/Map;)V

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/C1;)Z
    .locals 1

    .line 1
    check-cast p2, Lcom/android/tools/r8/internal/O30;

    .line 2
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/O30;->e()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/O30;->d()Lcom/android/tools/r8/internal/Uf;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Uf;->a(Lcom/android/tools/r8/internal/Uf;)Z

    move-result p1

    goto :goto_0

    .line 4
    :cond_0
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/O30;->b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/C1;)Lcom/android/tools/r8/internal/C1;

    move-result-object p1

    .line 5
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Uf;->equals(Ljava/lang/Object;)Z

    move-result p1

    :goto_0
    return p1
.end method

.method public final a(Lcom/android/tools/r8/internal/Uf;)Z
    .locals 5

    .line 20
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    iget-object v1, p1, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->size()I

    move-result v1

    const/4 v2, 0x0

    if-ge v0, v1, :cond_0

    return v2

    .line 23
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/zf;

    .line 24
    iget-object v3, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-interface {v3, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    return v2

    .line 29
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 30
    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/zf;

    .line 31
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/R30;

    .line 32
    iget-object v4, p1, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-interface {v4, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/R30;

    .line 33
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    if-ne v1, v3, :cond_4

    move-object v3, v1

    goto :goto_0

    .line 34
    :cond_4
    sget-object v3, Lcom/android/tools/r8/internal/R30;->d:Lcom/android/tools/r8/internal/R30;

    :goto_0
    if-eq v1, v3, :cond_3

    return v2

    :cond_5
    const/4 p1, 0x1

    return p1
.end method

.method public final d()Lcom/android/tools/r8/internal/Uf;
    .locals 0

    return-object p0
.end method

.method public final e()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 1

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    return p1

    .line 1
    :cond_0
    instance-of v0, p1, Lcom/android/tools/r8/internal/Uf;

    if-nez v0, :cond_1

    const/4 p1, 0x0

    return p1

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/Uf;

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    iget-object p1, p1, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public f()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Lcom/android/tools/r8/internal/zf;",
            "Lcom/android/tools/r8/internal/R30;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    return-object v0
.end method

.method public final hashCode()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf;->b:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->hashCode()I

    move-result v0

    return v0
.end method
