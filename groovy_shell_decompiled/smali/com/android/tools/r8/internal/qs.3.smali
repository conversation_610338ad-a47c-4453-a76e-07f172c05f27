.class public Lcom/android/tools/r8/internal/qs;
.super Lcom/android/tools/r8/internal/Du0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final c:Lcom/android/tools/r8/internal/qs;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/qs;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/qs;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/qs;->c:Lcom/android/tools/r8/internal/qs;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Du0;-><init>()V

    return-void
.end method


# virtual methods
.method public final bridge synthetic J()Lcom/android/tools/r8/internal/Du0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/rs;->d:Lcom/android/tools/r8/internal/rs;

    return-object v0
.end method

.method public final bridge synthetic K()Lcom/android/tools/r8/internal/Du0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/qs;->c:Lcom/android/tools/r8/internal/qs;

    return-object v0
.end method

.method public a(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 1
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->A1:Lcom/android/tools/r8/graph/J2;

    return-object p1
.end method

.method public a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/naming/r0;)Ljava/lang/Object;
    .locals 0

    const/4 p1, 0x3

    .line 2
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1
.end method

.method public g()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/rs;

    xor-int/lit8 v0, v0, 0x1

    return v0
.end method

.method public getTypeName()Ljava/lang/String;
    .locals 1

    const-string v0, "double"

    return-object v0
.end method

.method public s()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/rs;

    return v0
.end method
