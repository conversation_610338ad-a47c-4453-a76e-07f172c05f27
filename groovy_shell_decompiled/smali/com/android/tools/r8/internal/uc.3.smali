.class public final Lcom/android/tools/r8/internal/uc;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic b:Z


# instance fields
.field public final a:Lcom/android/tools/r8/internal/ZA;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/wc;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/uc;->b:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    invoke-static {}, Lcom/android/tools/r8/internal/cB;->h()Lcom/android/tools/r8/internal/ZA;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/uc;->a:Lcom/android/tools/r8/internal/ZA;

    return-void
.end method
