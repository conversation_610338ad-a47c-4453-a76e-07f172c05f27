.class public final synthetic Lcom/android/tools/r8/internal/V6$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/o5;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Y9;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Y9;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/V6$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Y9;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/H9;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/V6$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Y9;

    invoke-static {v0, p1, p2}, Lcom/android/tools/r8/internal/V6;->a(Lcom/android/tools/r8/internal/Y9;Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/H9;

    move-result-object p1

    return-object p1
.end method
