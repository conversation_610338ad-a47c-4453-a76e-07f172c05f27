.class public final Lcom/android/tools/r8/internal/tc0;
.super Lcom/android/tools/r8/internal/QA;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final l:Lcom/android/tools/r8/internal/tc0;


# instance fields
.field public final transient f:[Lcom/android/tools/r8/internal/lB;

.field public final transient g:[Lcom/android/tools/r8/internal/lB;

.field public final transient h:[Ljava/util/Map$Entry;

.field public final transient i:I

.field public final transient j:I

.field public transient k:Lcom/android/tools/r8/internal/sc0;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 1
    new-instance v6, Lcom/android/tools/r8/internal/tc0;

    sget-object v3, Lcom/android/tools/r8/internal/iB;->e:[Ljava/util/Map$Entry;

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lcom/android/tools/r8/internal/tc0;-><init>([Lcom/android/tools/r8/internal/lB;[Lcom/android/tools/r8/internal/lB;[Ljava/util/Map$Entry;II)V

    sput-object v6, Lcom/android/tools/r8/internal/tc0;->l:Lcom/android/tools/r8/internal/tc0;

    return-void
.end method

.method public constructor <init>([Lcom/android/tools/r8/internal/lB;[Lcom/android/tools/r8/internal/lB;[Ljava/util/Map$Entry;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/QA;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/tc0;->f:[Lcom/android/tools/r8/internal/lB;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/tc0;->g:[Lcom/android/tools/r8/internal/lB;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/internal/tc0;->h:[Ljava/util/Map$Entry;

    .line 5
    iput p4, p0, Lcom/android/tools/r8/internal/tc0;->i:I

    .line 6
    iput p5, p0, Lcom/android/tools/r8/internal/tc0;->j:I

    return-void
.end method


# virtual methods
.method public final forEach(Ljava/util/function/BiConsumer;)V
    .locals 5

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/tc0;->h:[Ljava/util/Map$Entry;

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    .line 3
    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    invoke-interface {p1, v4, v3}, Ljava/util/function/BiConsumer;->accept(Ljava/lang/Object;Ljava/lang/Object;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public final get(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/tc0;->f:[Lcom/android/tools/r8/internal/lB;

    iget v1, p0, Lcom/android/tools/r8/internal/tc0;->i:I

    sget-object v2, Lcom/android/tools/r8/internal/yc0;->i:Lcom/android/tools/r8/internal/yc0;

    if-eqz p1, :cond_2

    if-nez v0, :cond_0

    goto :goto_1

    .line 2
    :cond_0
    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result v2

    invoke-static {v2}, Lcom/android/tools/r8/internal/lz;->a(I)I

    move-result v2

    and-int/2addr v1, v2

    .line 3
    aget-object v0, v0, v1

    :goto_0
    if-eqz v0, :cond_2

    .line 4
    iget-object v1, v0, Lcom/android/tools/r8/internal/UA;->b:Ljava/lang/Object;

    .line 5
    invoke-virtual {p1, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 6
    iget-object p1, v0, Lcom/android/tools/r8/internal/UA;->c:Ljava/lang/Object;

    goto :goto_2

    .line 7
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/lB;->b()Lcom/android/tools/r8/internal/lB;

    move-result-object v0

    goto :goto_0

    :cond_2
    :goto_1
    const/4 p1, 0x0

    :goto_2
    return-object p1
.end method

.method public final hashCode()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/tc0;->j:I

    return v0
.end method

.method public final i()Lcom/android/tools/r8/internal/LB;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/iB;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    sget v0, Lcom/android/tools/r8/internal/LB;->c:I

    .line 3
    sget-object v0, Lcom/android/tools/r8/internal/Bc0;->j:Lcom/android/tools/r8/internal/Bc0;

    goto :goto_0

    .line 4
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/mB;

    iget-object v1, p0, Lcom/android/tools/r8/internal/tc0;->h:[Ljava/util/Map$Entry;

    invoke-direct {v0, p0, v1}, Lcom/android/tools/r8/internal/mB;-><init>(Lcom/android/tools/r8/internal/iB;[Ljava/util/Map$Entry;)V

    :goto_0
    return-object v0
.end method

.method public final j()Lcom/android/tools/r8/internal/LB;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/oB;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/oB;-><init>(Lcom/android/tools/r8/internal/iB;)V

    return-object v0
.end method

.method public final m()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final s()Lcom/android/tools/r8/internal/QA;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/iB;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/tc0;->l:Lcom/android/tools/r8/internal/tc0;

    return-object v0

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/tc0;->k:Lcom/android/tools/r8/internal/sc0;

    if-nez v0, :cond_1

    .line 5
    new-instance v0, Lcom/android/tools/r8/internal/sc0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/sc0;-><init>(Lcom/android/tools/r8/internal/tc0;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/tc0;->k:Lcom/android/tools/r8/internal/sc0;

    :cond_1
    return-object v0
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/tc0;->h:[Ljava/util/Map$Entry;

    array-length v0, v0

    return v0
.end method
