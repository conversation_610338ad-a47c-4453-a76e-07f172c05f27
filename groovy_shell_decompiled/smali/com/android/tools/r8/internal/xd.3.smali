.class public Lcom/android/tools/r8/internal/xd;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final a:Ljava/util/Comparator;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/xd$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/xd$$ExternalSyntheticLambda0;

    .line 2
    invoke-static {v0}, Ljava/util/Comparator;->comparing(Ljava/util/function/Function;)Ljava/util/Comparator;

    move-result-object v0

    sput-object v0, Lcom/android/tools/r8/internal/xd;->a:Ljava/util/Comparator;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/references/ClassReference;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/references/ClassReference;->getDescriptor()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1, p0}, Lcom/android/tools/r8/graph/B1;->e(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/references/ClassReference;)Ljava/lang/String;
    .locals 0

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/references/ClassReference;->getDescriptor()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static a()Ljava/util/Comparator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Comparator<",
            "Lcom/android/tools/r8/references/ClassReference;",
            ">;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/xd;->a:Ljava/util/Comparator;

    return-object v0
.end method
