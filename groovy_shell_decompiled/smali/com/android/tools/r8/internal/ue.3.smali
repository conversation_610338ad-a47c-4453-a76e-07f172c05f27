.class public abstract Lcom/android/tools/r8/internal/ue;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/x2;ILcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/G;
    .locals 14

    move-object v0, p0

    move/from16 v3, p2

    move-object/from16 v1, p3

    .line 1
    invoke-static {}, Lcom/android/tools/r8/internal/cB;->h()Lcom/android/tools/r8/internal/ZA;

    move-result-object v2

    .line 2
    new-instance v4, Lcom/android/tools/r8/internal/j9;

    int-to-long v5, v3

    sget-object v7, Lcom/android/tools/r8/internal/It0;->c:Lcom/android/tools/r8/internal/It0;

    invoke-direct {v4, v5, v6, v7}, Lcom/android/tools/r8/internal/j9;-><init>(JLcom/android/tools/r8/internal/It0;)V

    new-instance v5, Lcom/android/tools/r8/internal/fa;

    iget-object v6, v0, Lcom/android/tools/r8/graph/B1;->e2:Lcom/android/tools/r8/graph/J2;

    invoke-direct {v5, v6}, Lcom/android/tools/r8/internal/fa;-><init>(Lcom/android/tools/r8/graph/J2;)V

    const/4 v6, 0x2

    new-array v7, v6, [Lcom/android/tools/r8/internal/H9;

    const/4 v8, 0x0

    aput-object v4, v7, v8

    const/4 v4, 0x1

    aput-object v5, v7, v4

    .line 3
    invoke-static {v6, v7}, Lcom/android/tools/r8/internal/v10;->a(I[Ljava/lang/Object;)[Ljava/lang/Object;

    .line 4
    invoke-virtual {v2, v6, v7}, Lcom/android/tools/r8/internal/ZA;->a(I[Ljava/lang/Object;)V

    move v5, v8

    :goto_0
    if-ge v5, v3, :cond_0

    .line 5
    new-instance v7, Lcom/android/tools/r8/internal/Ra;

    sget-object v9, Lcom/android/tools/r8/internal/Ra$a;->e:Lcom/android/tools/r8/internal/Ra$a;

    invoke-direct {v7, v9}, Lcom/android/tools/r8/internal/Ra;-><init>(Lcom/android/tools/r8/internal/Ra$a;)V

    new-instance v9, Lcom/android/tools/r8/internal/j9;

    int-to-long v10, v5

    sget-object v12, Lcom/android/tools/r8/internal/It0;->c:Lcom/android/tools/r8/internal/It0;

    invoke-direct {v9, v10, v11, v12}, Lcom/android/tools/r8/internal/j9;-><init>(JLcom/android/tools/r8/internal/It0;)V

    new-instance v10, Lcom/android/tools/r8/internal/X9;

    sget-object v11, Lcom/android/tools/r8/internal/It0;->b:Lcom/android/tools/r8/internal/It0;

    invoke-direct {v10, v11, v5}, Lcom/android/tools/r8/internal/X9;-><init>(Lcom/android/tools/r8/internal/It0;I)V

    new-instance v11, Lcom/android/tools/r8/internal/J8;

    sget-object v12, Lcom/android/tools/r8/internal/oU;->b:Lcom/android/tools/r8/internal/oU;

    invoke-direct {v11, v12}, Lcom/android/tools/r8/internal/J8;-><init>(Lcom/android/tools/r8/internal/oU;)V

    const/4 v12, 0x4

    new-array v13, v12, [Lcom/android/tools/r8/internal/H9;

    aput-object v7, v13, v8

    aput-object v9, v13, v4

    aput-object v10, v13, v6

    const/4 v7, 0x3

    aput-object v11, v13, v7

    .line 6
    invoke-static {v12, v13}, Lcom/android/tools/r8/internal/v10;->a(I[Ljava/lang/Object;)[Ljava/lang/Object;

    .line 7
    invoke-virtual {v2, v12, v13}, Lcom/android/tools/r8/internal/ZA;->a(I[Ljava/lang/Object;)V

    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 8
    :cond_0
    new-instance v5, Lcom/android/tools/r8/internal/O9;

    iget-object v7, v0, Lcom/android/tools/r8/graph/B1;->e2:Lcom/android/tools/r8/graph/J2;

    new-array v9, v4, [Lcom/android/tools/r8/graph/J2;

    aput-object v7, v9, v8

    .line 13
    invoke-virtual {p0, v1, v9}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v7

    const-string v9, "of"

    .line 14
    invoke-virtual {p0, v9}, Lcom/android/tools/r8/graph/B1;->c(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object v9

    .line 15
    invoke-virtual {p0, v1, v7, v9}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    const/16 v1, 0xb8

    invoke-direct {v5, v1, v0, v8}, Lcom/android/tools/r8/internal/O9;-><init>(ILcom/android/tools/r8/graph/x2;Z)V

    new-instance v0, Lcom/android/tools/r8/internal/Ha;

    sget-object v1, Lcom/android/tools/r8/internal/It0;->b:Lcom/android/tools/r8/internal/It0;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/Ha;-><init>(Lcom/android/tools/r8/internal/It0;)V

    new-array v1, v6, [Lcom/android/tools/r8/internal/H9;

    aput-object v5, v1, v8

    aput-object v0, v1, v4

    .line 16
    invoke-static {v6, v1}, Lcom/android/tools/r8/internal/v10;->a(I[Ljava/lang/Object;)[Ljava/lang/Object;

    .line 17
    invoke-virtual {v2, v6, v1}, Lcom/android/tools/r8/internal/ZA;->a(I[Ljava/lang/Object;)V

    .line 18
    new-instance v7, Lcom/android/tools/r8/graph/G;

    move-object v0, p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    const/4 v4, 0x4

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/ZA;->a()Lcom/android/tools/r8/internal/cB;

    move-result-object v5

    .line 19
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v6

    .line 20
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v8

    move-object v0, v7

    move v2, v4

    move/from16 v3, p2

    move-object v4, v5

    move-object v5, v6

    move-object v6, v8

    .line 21
    invoke-direct/range {v0 .. v6}, Lcom/android/tools/r8/graph/G;-><init>(Lcom/android/tools/r8/graph/J2;IILjava/util/List;Ljava/util/List;Ljava/util/List;)V

    return-object v7
.end method
