.class public abstract Lcom/android/tools/r8/internal/T;
.super Lcom/android/tools/r8/internal/c0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/U;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/U;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/c0;-><init>()V

    return-void
.end method


# virtual methods
.method public final L()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-interface {v0}, Lcom/android/tools/r8/internal/mG;->a()I

    move-result v0

    return v0
.end method

.method public final a(I)Lcom/android/tools/r8/internal/ZG;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-interface {v0, p1}, Lcom/android/tools/r8/internal/mG;->b(I)Lcom/android/tools/r8/internal/mG;

    move-result-object p1

    invoke-interface {p1}, Lcom/android/tools/r8/internal/mG;->keySet()Lcom/android/tools/r8/internal/ZG;

    move-result-object p1

    return-object p1
.end method

.method public final clear()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/P;->clear()V

    return-void
.end method

.method public final comparator()Ljava/util/Comparator;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-interface {v0}, Lcom/android/tools/r8/internal/mG;->comparator()Lcom/android/tools/r8/internal/XD;

    move-result-object v0

    return-object v0
.end method

.method public final d(II)Lcom/android/tools/r8/internal/ZG;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-interface {v0, p1, p2}, Lcom/android/tools/r8/internal/mG;->a(II)Lcom/android/tools/r8/internal/mG;

    move-result-object p1

    invoke-interface {p1}, Lcom/android/tools/r8/internal/mG;->keySet()Lcom/android/tools/r8/internal/ZG;

    move-result-object p1

    return-object p1
.end method

.method public final e(I)Lcom/android/tools/r8/internal/ZG;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-interface {v0, p1}, Lcom/android/tools/r8/internal/mG;->c(I)Lcom/android/tools/r8/internal/mG;

    move-result-object p1

    invoke-interface {p1}, Lcom/android/tools/r8/internal/mG;->keySet()Lcom/android/tools/r8/internal/ZG;

    move-result-object p1

    return-object p1
.end method

.method public final h(I)Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/S;->a(I)Z

    move-result p1

    return p1
.end method

.method public final p()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-interface {v0}, Lcom/android/tools/r8/internal/mG;->d()I

    move-result v0

    return v0
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T;->b:Lcom/android/tools/r8/internal/U;

    invoke-interface {v0}, Lcom/android/tools/r8/internal/vx;->size()I

    move-result v0

    return v0
.end method
