.class public final Lcom/android/tools/r8/internal/Sy;
.super Lcom/android/tools/r8/internal/F0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public b:Lcom/android/tools/r8/internal/Ry;

.field public final synthetic c:Lcom/android/tools/r8/internal/Ty;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Ty;Lcom/android/tools/r8/internal/Ry;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/Sy;->c:Lcom/android/tools/r8/internal/Ty;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/F0;-><init>()V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/Sy;->b:Lcom/android/tools/r8/internal/Ry;

    return-void
.end method


# virtual methods
.method public final getKey()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Sy;->b:Lcom/android/tools/r8/internal/Ry;

    iget-object v0, v0, Lcom/android/tools/r8/internal/UA;->c:Ljava/lang/Object;

    return-object v0
.end method

.method public final getValue()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Sy;->b:Lcom/android/tools/r8/internal/Ry;

    iget-object v0, v0, Lcom/android/tools/r8/internal/UA;->b:Ljava/lang/Object;

    return-object v0
.end method

.method public final setValue(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Sy;->b:Lcom/android/tools/r8/internal/Ry;

    iget-object v0, v0, Lcom/android/tools/r8/internal/UA;->b:Ljava/lang/Object;

    .line 2
    invoke-static {p1}, Lcom/android/tools/r8/internal/lz;->a(Ljava/lang/Object;)I

    move-result v1

    .line 3
    iget-object v2, p0, Lcom/android/tools/r8/internal/Sy;->b:Lcom/android/tools/r8/internal/Ry;

    iget v2, v2, Lcom/android/tools/r8/internal/Ry;->d:I

    if-ne v1, v2, :cond_0

    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/W10;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    return-object p1

    .line 6
    :cond_0
    iget-object v2, p0, Lcom/android/tools/r8/internal/Sy;->c:Lcom/android/tools/r8/internal/Ty;

    iget-object v2, v2, Lcom/android/tools/r8/internal/Ty;->g:Lcom/android/tools/r8/internal/Wy;

    iget-object v2, v2, Lcom/android/tools/r8/internal/Wy;->b:Lcom/android/tools/r8/internal/az;

    sget v3, Lcom/android/tools/r8/internal/az;->j:I

    .line 7
    invoke-virtual {v2, v1, p1}, Lcom/android/tools/r8/internal/az;->a(ILjava/lang/Object;)Lcom/android/tools/r8/internal/Ry;

    move-result-object v2

    if-nez v2, :cond_1

    const/4 v2, 0x1

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :goto_0
    const-string v3, "value already present: %s"

    .line 8
    invoke-static {v2, v3, p1}, Lcom/android/tools/r8/internal/T40;->a(ZLjava/lang/String;Ljava/lang/Object;)V

    .line 9
    iget-object v2, p0, Lcom/android/tools/r8/internal/Sy;->c:Lcom/android/tools/r8/internal/Ty;

    iget-object v2, v2, Lcom/android/tools/r8/internal/Ty;->g:Lcom/android/tools/r8/internal/Wy;

    iget-object v2, v2, Lcom/android/tools/r8/internal/Wy;->b:Lcom/android/tools/r8/internal/az;

    iget-object v3, p0, Lcom/android/tools/r8/internal/Sy;->b:Lcom/android/tools/r8/internal/Ry;

    .line 10
    invoke-virtual {v2, v3}, Lcom/android/tools/r8/internal/az;->a(Lcom/android/tools/r8/internal/Ry;)V

    .line 11
    new-instance v2, Lcom/android/tools/r8/internal/Ry;

    iget-object v3, p0, Lcom/android/tools/r8/internal/Sy;->b:Lcom/android/tools/r8/internal/Ry;

    iget-object v4, v3, Lcom/android/tools/r8/internal/UA;->c:Ljava/lang/Object;

    iget v3, v3, Lcom/android/tools/r8/internal/Ry;->e:I

    invoke-direct {v2, p1, v1, v4, v3}, Lcom/android/tools/r8/internal/Ry;-><init>(Ljava/lang/Object;ILjava/lang/Object;I)V

    .line 13
    iput-object v2, p0, Lcom/android/tools/r8/internal/Sy;->b:Lcom/android/tools/r8/internal/Ry;

    .line 14
    iget-object p1, p0, Lcom/android/tools/r8/internal/Sy;->c:Lcom/android/tools/r8/internal/Ty;

    iget-object p1, p1, Lcom/android/tools/r8/internal/Ty;->g:Lcom/android/tools/r8/internal/Wy;

    iget-object p1, p1, Lcom/android/tools/r8/internal/Wy;->b:Lcom/android/tools/r8/internal/az;

    const/4 v1, 0x0

    .line 15
    invoke-virtual {p1, v2, v1}, Lcom/android/tools/r8/internal/az;->a(Lcom/android/tools/r8/internal/Ry;Lcom/android/tools/r8/internal/Ry;)V

    .line 16
    iget-object p1, p0, Lcom/android/tools/r8/internal/Sy;->c:Lcom/android/tools/r8/internal/Ty;

    iget-object v1, p1, Lcom/android/tools/r8/internal/Ty;->g:Lcom/android/tools/r8/internal/Wy;

    iget-object v1, v1, Lcom/android/tools/r8/internal/Wy;->b:Lcom/android/tools/r8/internal/az;

    .line 17
    iget v1, v1, Lcom/android/tools/r8/internal/az;->h:I

    .line 18
    iput v1, p1, Lcom/android/tools/r8/internal/Xy;->d:I

    return-object v0
.end method
