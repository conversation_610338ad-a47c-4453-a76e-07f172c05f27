.class public final synthetic Lcom/android/tools/r8/internal/Vz$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiConsumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Vz;

.field public final synthetic f$1:Ljava/util/List;

.field public final synthetic f$2:Ljava/util/Set;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Vz;Ljava/util/List;Ljava/util/Set;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Vz$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Vz;

    iput-object p2, p0, Lcom/android/tools/r8/internal/Vz$$ExternalSyntheticLambda0;->f$1:Ljava/util/List;

    iput-object p3, p0, Lcom/android/tools/r8/internal/Vz$$ExternalSyntheticLambda0;->f$2:Ljava/util/Set;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/internal/Vz$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Vz;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Vz$$ExternalSyntheticLambda0;->f$1:Ljava/util/List;

    iget-object v2, p0, Lcom/android/tools/r8/internal/Vz$$ExternalSyntheticLambda0;->f$2:Ljava/util/Set;

    check-cast p1, Lcom/android/tools/r8/graph/J2;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {v0, v1, v2, p1, p2}, Lcom/android/tools/r8/internal/Vz;->a(Ljava/util/List;Ljava/util/Set;Lcom/android/tools/r8/graph/J2;Ljava/lang/Integer;)V

    return-void
.end method
