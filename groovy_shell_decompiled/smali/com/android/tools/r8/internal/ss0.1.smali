.class public final Lcom/android/tools/r8/internal/ss0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:I


# instance fields
.field public a:Ljava/util/List;

.field public b:Ljava/util/List;

.field public c:Ljava/util/List;

.field public d:Ljava/util/List;

.field public e:Ljava/util/List;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/rs0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/rs0;-><init>()V

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rs0;->a()Lcom/android/tools/r8/internal/ss0;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final equals(Ljava/lang/Object;)Z
    .locals 11

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/ss0;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 2
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/ss0;->a:Ljava/util/List;

    iget-object v3, p0, Lcom/android/tools/r8/internal/ss0;->b:Ljava/util/List;

    iget-object v4, p0, Lcom/android/tools/r8/internal/ss0;->c:Ljava/util/List;

    iget-object v5, p0, Lcom/android/tools/r8/internal/ss0;->d:Ljava/util/List;

    iget-object v6, p0, Lcom/android/tools/r8/internal/ss0;->e:Ljava/util/List;

    const/4 v7, 0x5

    new-array v8, v7, [Ljava/lang/Object;

    aput-object v1, v8, v2

    aput-object v3, v8, v0

    const/4 v1, 0x2

    aput-object v4, v8, v1

    const/4 v3, 0x3

    aput-object v5, v8, v3

    const/4 v4, 0x4

    aput-object v6, v8, v4

    .line 3
    check-cast p1, Lcom/android/tools/r8/internal/ss0;

    .line 4
    iget-object v5, p1, Lcom/android/tools/r8/internal/ss0;->a:Ljava/util/List;

    .line 5
    iget-object v6, p1, Lcom/android/tools/r8/internal/ss0;->b:Ljava/util/List;

    iget-object v9, p1, Lcom/android/tools/r8/internal/ss0;->c:Ljava/util/List;

    iget-object v10, p1, Lcom/android/tools/r8/internal/ss0;->d:Ljava/util/List;

    iget-object p1, p1, Lcom/android/tools/r8/internal/ss0;->e:Ljava/util/List;

    new-array v7, v7, [Ljava/lang/Object;

    aput-object v5, v7, v2

    aput-object v6, v7, v0

    aput-object v9, v7, v1

    aput-object v10, v7, v3

    aput-object p1, v7, v4

    .line 6
    invoke-static {v8, v7}, Ljava/util/Arrays;->equals([Ljava/lang/Object;[Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public final hashCode()I
    .locals 7

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/ss0;->a:Ljava/util/List;

    iget-object v1, p0, Lcom/android/tools/r8/internal/ss0;->b:Ljava/util/List;

    iget-object v2, p0, Lcom/android/tools/r8/internal/ss0;->c:Ljava/util/List;

    iget-object v3, p0, Lcom/android/tools/r8/internal/ss0;->d:Ljava/util/List;

    iget-object v4, p0, Lcom/android/tools/r8/internal/ss0;->e:Ljava/util/List;

    const/4 v5, 0x5

    new-array v5, v5, [Ljava/lang/Object;

    const/4 v6, 0x0

    aput-object v0, v5, v6

    const/4 v0, 0x1

    aput-object v1, v5, v0

    const/4 v0, 0x2

    aput-object v2, v5, v0

    const/4 v0, 0x3

    aput-object v3, v5, v0

    const/4 v0, 0x4

    aput-object v4, v5, v0

    .line 2
    invoke-static {v5}, Ljava/util/Arrays;->hashCode([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
