.class public final Lcom/android/tools/r8/internal/S3;
.super Lcom/android/tools/r8/internal/zq0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final c:Lcom/android/tools/r8/internal/R3;


# instance fields
.field public final a:Ljava/lang/Class;

.field public final b:Lcom/android/tools/r8/internal/Bq0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/R3;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/R3;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/S3;->c:Lcom/android/tools/r8/internal/R3;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/Ly;Lcom/android/tools/r8/internal/zq0;Ljava/lang/Class;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/zq0;-><init>()V

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Bq0;

    invoke-direct {v0, p1, p2, p3}, Lcom/android/tools/r8/internal/Bq0;-><init>(Lcom/android/tools/r8/internal/Ly;Lcom/android/tools/r8/internal/zq0;Ljava/lang/reflect/Type;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/S3;->b:Lcom/android/tools/r8/internal/Bq0;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/internal/S3;->a:Ljava/lang/Class;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/JJ;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/JJ;->s()I

    move-result v0

    const/16 v1, 0x9

    if-ne v0, v1, :cond_0

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/JJ;->p()V

    const/4 p1, 0x0

    return-object p1

    .line 6
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 7
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/JJ;->c()V

    .line 8
    :goto_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/JJ;->i()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 9
    iget-object v1, p0, Lcom/android/tools/r8/internal/S3;->b:Lcom/android/tools/r8/internal/Bq0;

    .line 10
    iget-object v1, v1, Lcom/android/tools/r8/internal/Bq0;->b:Lcom/android/tools/r8/internal/zq0;

    .line 11
    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/zq0;->a(Lcom/android/tools/r8/internal/JJ;)Ljava/lang/Object;

    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 14
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/JJ;->g()V

    .line 16
    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result p1

    .line 18
    iget-object v1, p0, Lcom/android/tools/r8/internal/S3;->a:Ljava/lang/Class;

    invoke-virtual {v1}, Ljava/lang/Class;->isPrimitive()Z

    move-result v1

    if-eqz v1, :cond_3

    .line 19
    iget-object v1, p0, Lcom/android/tools/r8/internal/S3;->a:Ljava/lang/Class;

    invoke-static {v1, p1}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;I)Ljava/lang/Object;

    move-result-object v1

    const/4 v2, 0x0

    :goto_1
    if-ge v2, p1, :cond_2

    .line 21
    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    invoke-static {v1, v2, v3}, Ljava/lang/reflect/Array;->set(Ljava/lang/Object;ILjava/lang/Object;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    :cond_2
    return-object v1

    .line 28
    :cond_3
    iget-object v1, p0, Lcom/android/tools/r8/internal/S3;->a:Ljava/lang/Class;

    invoke-static {v1, p1}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    .line 29
    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/MJ;Ljava/lang/Object;)V
    .locals 4

    if-nez p2, :cond_0

    .line 30
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/MJ;->i()Lcom/android/tools/r8/internal/MJ;

    return-void

    .line 34
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/MJ;->d()V

    const/4 v0, 0x0

    .line 35
    invoke-static {p2}, Ljava/lang/reflect/Array;->getLength(Ljava/lang/Object;)I

    move-result v1

    :goto_0
    if-ge v0, v1, :cond_1

    .line 37
    invoke-static {p2, v0}, Ljava/lang/reflect/Array;->get(Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v2

    .line 38
    iget-object v3, p0, Lcom/android/tools/r8/internal/S3;->b:Lcom/android/tools/r8/internal/Bq0;

    invoke-virtual {v3, p1, v2}, Lcom/android/tools/r8/internal/Bq0;->a(Lcom/android/tools/r8/internal/MJ;Ljava/lang/Object;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 40
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/MJ;->f()V

    return-void
.end method
