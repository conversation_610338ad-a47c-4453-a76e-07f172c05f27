.class public final synthetic Lcom/android/tools/r8/internal/W50$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/D5;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/D5;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/W50$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/graph/D5;

    iput-object p2, p0, Lcom/android/tools/r8/internal/W50$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/D5;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/W50$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/graph/D5;

    iget-object v1, p0, Lcom/android/tools/r8/internal/W50$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/D5;

    check-cast p1, Lcom/android/tools/r8/internal/J50;

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/W50;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/J50;)V

    return-void
.end method
