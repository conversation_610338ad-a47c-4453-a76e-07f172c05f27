.class public final Lcom/android/tools/r8/internal/U50;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/yX;


# instance fields
.field public final a:Lcom/android/tools/r8/internal/Yf;

.field public final b:Lcom/android/tools/r8/internal/yX;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Yf;Lcom/android/tools/r8/internal/yX;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/U50;->a:Lcom/android/tools/r8/internal/Yf;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/U50;->b:Lcom/android/tools/r8/internal/yX;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/J50;)V
    .locals 0

    .line 14
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/E0;->getReference()Lcom/android/tools/r8/graph/G2;

    move-result-object p0

    invoke-interface {p2, p0}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/internal/J50;

    move-result-object p0

    .line 15
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    .line 16
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/internal/J50;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/K50;)V
    .locals 0

    .line 17
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 18
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    invoke-virtual {p2, p0}, Lcom/android/tools/r8/internal/K50;->a(Lcom/android/tools/r8/graph/J2;)V

    .line 19
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/K50;->f()Lcom/android/tools/r8/internal/K50;

    move-result-object p0

    .line 20
    sget-object p2, Lcom/android/tools/r8/internal/E50$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/E50$$ExternalSyntheticLambda0;

    .line 21
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/K50;->a(Lcom/android/tools/r8/graph/x2;Ljava/util/function/Consumer;)Lcom/android/tools/r8/internal/K50;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/B5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V
    .locals 1

    .line 22
    iget-object v0, p0, Lcom/android/tools/r8/internal/U50;->a:Lcom/android/tools/r8/internal/Yf;

    invoke-virtual {v0, p2, p3}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V

    .line 23
    iget-object v0, p0, Lcom/android/tools/r8/internal/U50;->b:Lcom/android/tools/r8/internal/yX;

    invoke-interface {v0, p1, p2, p3}, Lcom/android/tools/r8/internal/yX;->a(Lcom/android/tools/r8/graph/B5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/H0;)V
    .locals 3

    .line 1
    invoke-virtual {p4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    instance-of v0, p4, Lcom/android/tools/r8/graph/D5;

    if-eqz v0, :cond_0

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/U50;->a:Lcom/android/tools/r8/internal/Yf;

    .line 4
    invoke-interface {p4}, Lcom/android/tools/r8/graph/o0;->H()Lcom/android/tools/r8/graph/D5;

    move-result-object v1

    new-instance v2, Lcom/android/tools/r8/internal/U50$$ExternalSyntheticLambda0;

    invoke-direct {v2, p3, p2}, Lcom/android/tools/r8/internal/U50$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/D5;)V

    .line 5
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/x2;Ljava/util/function/Consumer;)V

    goto :goto_0

    .line 7
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/U50;->a:Lcom/android/tools/r8/internal/Yf;

    new-instance v1, Lcom/android/tools/r8/internal/U50$$ExternalSyntheticLambda1;

    invoke-direct {v1, p3, p2}, Lcom/android/tools/r8/internal/U50$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/D5;)V

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Yf;->a(Ljava/util/function/Consumer;)V

    .line 13
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/U50;->b:Lcom/android/tools/r8/internal/yX;

    invoke-interface {v0, p1, p2, p3, p4}, Lcom/android/tools/r8/internal/yX;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/H0;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V
    .locals 1

    .line 24
    iget-object v0, p0, Lcom/android/tools/r8/internal/U50;->a:Lcom/android/tools/r8/internal/Yf;

    invoke-virtual {v0, p2, p3}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/internal/U50;->b:Lcom/android/tools/r8/internal/yX;

    invoke-interface {v0, p1, p2, p3}, Lcom/android/tools/r8/internal/yX;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V

    return-void
.end method

.method public final b(Lcom/android/tools/r8/graph/B5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/U50;->a:Lcom/android/tools/r8/internal/Yf;

    invoke-virtual {v0, p2, p3}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/U50;->b:Lcom/android/tools/r8/internal/yX;

    invoke-interface {v0, p1, p2, p3}, Lcom/android/tools/r8/internal/yX;->b(Lcom/android/tools/r8/graph/B5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V

    return-void
.end method
