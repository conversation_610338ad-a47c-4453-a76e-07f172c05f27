.class public final synthetic Lcom/android/tools/r8/internal/Ug0$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/op0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Ug0;

.field public final synthetic f$1:Ljava/util/Map;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Ug0;Ljava/util/Map;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Ug0$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Ug0;

    iput-object p2, p0, Lcom/android/tools/r8/internal/Ug0$$ExternalSyntheticLambda0;->f$1:Ljava/util/Map;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/Ug0$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Ug0;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ug0$$ExternalSyntheticLambda0;->f$1:Ljava/util/Map;

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Ug0;->a(Ljava/util/Map;Lcom/android/tools/r8/graph/E2;)V

    return-void
.end method
