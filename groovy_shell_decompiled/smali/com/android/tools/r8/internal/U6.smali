.class public final Lcom/android/tools/r8/internal/U6;
.super Lcom/android/tools/r8/internal/z50;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/z50;-><init>(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/O5;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/uI;Lcom/android/tools/r8/graph/H0;Lcom/android/tools/r8/ir/optimize/a;Ljava/util/Set;)Lcom/android/tools/r8/internal/uD;
    .locals 0

    .line 3
    invoke-virtual {p5}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/x2;

    iget-object p7, p0, Lcom/android/tools/r8/internal/z50;->b:Lcom/android/tools/r8/graph/B1;

    iget-object p7, p7, Lcom/android/tools/r8/graph/B1;->m4:Lcom/android/tools/r8/graph/B1$a;

    iget-object p7, p7, Lcom/android/tools/r8/graph/B1$a;->e:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p2, p7}, Lcom/android/tools/r8/graph/x2;->a(Lcom/android/tools/r8/graph/x2;)Z

    move-result p2

    if-eqz p2, :cond_0

    .line 4
    invoke-virtual {p0, p1, p3, p4}, Lcom/android/tools/r8/internal/U6;->b(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/uI;)V

    goto :goto_0

    .line 5
    :cond_0
    invoke-virtual {p5}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/U6;->c()Lcom/android/tools/r8/graph/x2;

    move-result-object p7

    invoke-virtual {p2, p7}, Lcom/android/tools/r8/graph/x2;->a(Lcom/android/tools/r8/graph/x2;)Z

    move-result p2

    if-eqz p2, :cond_1

    .line 6
    invoke-virtual {p0, p1, p3, p4}, Lcom/android/tools/r8/internal/z50;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/uI;)V

    goto :goto_0

    .line 7
    :cond_1
    invoke-virtual {p5}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/U6;->b()Lcom/android/tools/r8/graph/x2;

    move-result-object p5

    invoke-virtual {p2, p5}, Lcom/android/tools/r8/graph/x2;->a(Lcom/android/tools/r8/graph/x2;)Z

    move-result p2

    if-eqz p2, :cond_2

    .line 8
    invoke-virtual {p0, p1, p3, p4, p6}, Lcom/android/tools/r8/internal/U6;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/uI;Lcom/android/tools/r8/ir/optimize/a;)V

    :cond_2
    :goto_0
    return-object p3
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/uI;Lcom/android/tools/r8/ir/optimize/a;)V
    .locals 4

    .line 9
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/rD;->U0()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 10
    iget-object v1, p0, Lcom/android/tools/r8/internal/z50;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v2

    .line 11
    sget-object v3, Lcom/android/tools/r8/internal/L1;->a:Lcom/android/tools/r8/internal/K1;

    .line 12
    invoke-virtual {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/vt0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/L1;)Lcom/android/tools/r8/internal/E1;

    move-result-object v0

    .line 13
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 14
    instance-of v1, v0, Lcom/android/tools/r8/internal/hk0;

    if-eqz v1, :cond_1

    .line 15
    iget-object p3, p0, Lcom/android/tools/r8/internal/z50;->a:Lcom/android/tools/r8/graph/y;

    .line 18
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/E1;->d()Lcom/android/tools/r8/internal/hk0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/hk0;->S()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 19
    iget-object v0, p0, Lcom/android/tools/r8/internal/z50;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->m4:Lcom/android/tools/r8/graph/B1$a;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1$a;->b:Lcom/android/tools/r8/graph/l1;

    goto :goto_0

    .line 20
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/z50;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->m4:Lcom/android/tools/r8/graph/B1$a;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1$a;->a:Lcom/android/tools/r8/graph/l1;

    .line 21
    :goto_0
    invoke-interface {p2, p3, p1, v0, p4}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/graph/l1;Ljava/util/Set;)V

    return-void

    .line 30
    :cond_1
    invoke-super {p0, p1, p2, p3, p4}, Lcom/android/tools/r8/internal/z50;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/uI;Lcom/android/tools/r8/ir/optimize/a;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/E1;)Z
    .locals 0

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    instance-of p1, p1, Lcom/android/tools/r8/internal/Dj0;

    return p1
.end method

.method public final b()Lcom/android/tools/r8/graph/x2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/z50;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->m4:Lcom/android/tools/r8/graph/B1$a;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1$a;->f:Lcom/android/tools/r8/graph/x2;

    return-object v0
.end method

.method public final b(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/uD;Lcom/android/tools/r8/internal/uI;)V
    .locals 3

    const/4 v0, 0x0

    .line 2
    invoke-virtual {p3, v0}, Lcom/android/tools/r8/internal/mI;->b(I)Lcom/android/tools/r8/internal/vt0;

    move-result-object p3

    .line 3
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/vt0;->l()Lcom/android/tools/r8/internal/vt0;

    move-result-object p3

    .line 4
    sget-object v1, Lcom/android/tools/r8/internal/Jn0$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/Jn0$$ExternalSyntheticLambda0;

    invoke-virtual {p3, v1}, Lcom/android/tools/r8/internal/vt0;->c(Ljava/util/function/Predicate;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 5
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/vt0;->r()Lcom/android/tools/r8/internal/rD;

    move-result-object p3

    invoke-virtual {p3}, Lcom/android/tools/r8/internal/rD;->I()Lcom/android/tools/r8/internal/Xg;

    move-result-object p3

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/z50;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v2

    invoke-virtual {p3, v1, v2}, Lcom/android/tools/r8/internal/rD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z

    move-result v1

    if-nez v1, :cond_1

    .line 7
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/Xg;->L2()Lcom/android/tools/r8/graph/I2;

    move-result-object p3

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-static {p3}, Lcom/android/tools/r8/internal/Sn0;->i(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p3

    const-string v1, "true"

    .line 8
    invoke-virtual {p3, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p3, 0x1

    .line 9
    invoke-interface {p2, p1, p3}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/aA;I)V

    goto :goto_0

    :cond_0
    const-string v1, "false"

    .line 10
    invoke-virtual {p3, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p3

    if-eqz p3, :cond_1

    .line 11
    invoke-interface {p2, p1, v0}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/aA;I)V

    :cond_1
    :goto_0
    return-void
.end method

.method public final c()Lcom/android/tools/r8/graph/x2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/z50;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->m4:Lcom/android/tools/r8/graph/B1$a;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1$a;->d:Lcom/android/tools/r8/graph/x2;

    return-object v0
.end method

.method public final getType()Lcom/android/tools/r8/graph/J2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/z50;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->O1:Lcom/android/tools/r8/graph/J2;

    return-object v0
.end method
