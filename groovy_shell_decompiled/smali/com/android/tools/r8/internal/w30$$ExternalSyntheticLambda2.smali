.class public final synthetic Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/vt0;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/Vu0;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/Vu0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/vt0;

    iput-object p2, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda2;->f$1:Lcom/android/tools/r8/internal/Vu0;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/vt0;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda2;->f$1:Lcom/android/tools/r8/internal/Vu0;

    check-cast p1, Lcom/android/tools/r8/internal/K5;

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/w30;->a(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/Vu0;Lcom/android/tools/r8/internal/K5;)Lcom/android/tools/r8/internal/gq0;

    move-result-object p1

    return-object p1
.end method
