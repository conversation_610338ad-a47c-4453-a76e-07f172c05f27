.class public final synthetic Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/pp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/u3;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/O40;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/Fy;

.field public final synthetic f$3:Lcom/android/tools/r8/internal/r3;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/u3;Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/r3;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/u3;

    iput-object p2, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/O40;

    iput-object p3, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/internal/Fy;

    iput-object p4, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;->f$3:Lcom/android/tools/r8/internal/r3;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    iget-object v0, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/u3;

    iget-object v1, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/O40;

    iget-object v2, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/internal/Fy;

    iget-object v3, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;->f$3:Lcom/android/tools/r8/internal/r3;

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v0, v1, v2, v3, p1}, Lcom/android/tools/r8/internal/u3;->a(Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/r3;Lcom/android/tools/r8/graph/E2;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method
