.class public final synthetic Lcom/android/tools/r8/internal/s3$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/uD;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/uD;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/s3$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/uD;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/s3$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/uD;

    check-cast p1, Lcom/android/tools/r8/internal/rD;

    invoke-interface {v0, p1}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    return-void
.end method
