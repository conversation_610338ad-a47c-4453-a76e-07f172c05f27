.class public final synthetic Lcom/android/tools/r8/internal/V30$$ExternalSyntheticLambda4;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/uD;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/uD;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/V30$$ExternalSyntheticLambda4;->f$0:Lcom/android/tools/r8/internal/uD;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/V30$$ExternalSyntheticLambda4;->f$0:Lcom/android/tools/r8/internal/uD;

    invoke-interface {v0}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/rD;

    return-object v0
.end method
