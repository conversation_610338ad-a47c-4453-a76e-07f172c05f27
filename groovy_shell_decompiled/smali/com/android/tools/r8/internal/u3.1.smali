.class public final Lcom/android/tools/r8/internal/u3;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic c:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Lcom/android/tools/r8/internal/A3;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/A3;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/u3;->b:Lcom/android/tools/r8/internal/A3;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Ljava/util/List;)V
    .locals 0

    .line 105
    invoke-virtual {p0, p2, p1}, Lcom/android/tools/r8/internal/O40;->a(Ljava/util/Collection;Lcom/android/tools/r8/internal/Fy;)V

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/j1;)Z
    .locals 1

    .line 289
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/j1;->i1()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/i0;->D0()Z

    move-result p0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/nR;)Lcom/android/tools/r8/internal/nR;
    .locals 10

    .line 310
    iget-object v0, p2, Lcom/android/tools/r8/internal/nR;->g:[Lcom/android/tools/r8/internal/pR;

    .line 311
    new-instance v1, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda6;

    invoke-direct {v1, p0, p1}, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda6;-><init>(Lcom/android/tools/r8/internal/u3;Lcom/android/tools/r8/graph/D5;)V

    .line 312
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/U3;->a([Ljava/lang/Object;Ljava/util/function/Predicate;)Z

    move-result v0

    if-nez v0, :cond_0

    return-object p2

    .line 313
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    .line 314
    invoke-static {v0}, Lcom/android/tools/r8/internal/UU;->c(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/UU$a;

    move-result-object v1

    invoke-virtual {p2, p1, v0, v1}, Lcom/android/tools/r8/internal/nR;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/UU$a;)Lcom/android/tools/r8/internal/aA;

    move-result-object p2

    .line 315
    new-instance v0, Lcom/android/tools/r8/ir/optimize/a;

    invoke-direct {v0}, Lcom/android/tools/r8/ir/optimize/a;-><init>()V

    .line 316
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/aA;->v()Lcom/android/tools/r8/internal/O5;

    move-result-object v1

    .line 317
    invoke-static {}, Lcom/android/tools/r8/internal/kj0;->c()Ljava/util/Set;

    move-result-object v8

    .line 318
    :cond_1
    :goto_0
    iget-object v2, v1, Lcom/android/tools/r8/internal/O5;->c:Ljava/util/ListIterator;

    .line 319
    invoke-interface {v2}, Ljava/util/ListIterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_7

    .line 320
    iget-object v2, v1, Lcom/android/tools/r8/internal/O5;->c:Ljava/util/ListIterator;

    invoke-interface {v2}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/K5;

    iput-object v2, v1, Lcom/android/tools/r8/internal/O5;->d:Lcom/android/tools/r8/internal/K5;

    .line 321
    invoke-interface {v8, v2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    goto :goto_0

    .line 324
    :cond_2
    invoke-virtual {v2, p2}, Lcom/android/tools/r8/internal/K5;->a(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/N5;

    move-result-object v9

    .line 325
    :cond_3
    :goto_1
    invoke-interface {v9}, Ljava/util/ListIterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    .line 326
    invoke-interface {v9}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/rD;

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/rD;->Q()Lcom/android/tools/r8/internal/sv;

    move-result-object v2

    if-nez v2, :cond_4

    goto :goto_1

    .line 330
    :cond_4
    iget-object v3, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    .line 331
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/j;

    iget-object v4, v2, Lcom/android/tools/r8/internal/sv;->i:Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 332
    iget-object v5, v4, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v3, v5, v4, p1}, Lcom/android/tools/r8/graph/j;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/graph/z3;

    move-result-object v3

    .line 333
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/z3;->o()Lcom/android/tools/r8/graph/B5;

    move-result-object v3

    if-eqz v3, :cond_3

    .line 334
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/F0;->x()Lcom/android/tools/r8/internal/yv;

    move-result-object v4

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/yv;->f()Lcom/android/tools/r8/internal/E1;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 335
    instance-of v4, v4, Lcom/android/tools/r8/internal/o7;

    if-nez v4, :cond_5

    goto :goto_1

    .line 336
    :cond_5
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/rD;->w2()Z

    move-result v2

    .line 337
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/F0;->w()Lcom/android/tools/r8/graph/h3;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v3

    if-eq v2, v3, :cond_6

    .line 339
    invoke-interface {v9}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    .line 340
    iget-object v3, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    move-object v2, v9

    move-object v4, p2

    move-object v5, v1

    move-object v6, v8

    move-object v7, v0

    invoke-interface/range {v2 .. v7}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Ljava/util/ListIterator;Ljava/util/Set;Lcom/android/tools/r8/ir/optimize/a;)V

    goto :goto_1

    .line 343
    :cond_6
    iget-object v3, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    move-object v2, v9

    move-object v4, p2

    move-object v5, v1

    move-object v6, v8

    move-object v7, v0

    invoke-interface/range {v2 .. v7}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Ljava/util/ListIterator;Ljava/util/Set;Lcom/android/tools/r8/ir/optimize/a;)V

    goto :goto_1

    .line 348
    :cond_7
    invoke-virtual {p2, v8}, Lcom/android/tools/r8/internal/aA;->b(Ljava/util/Collection;)V

    .line 349
    iget-object p1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    .line 350
    invoke-static {}, Lcom/android/tools/r8/internal/Ch;->b()Ljava/util/function/Consumer;

    move-result-object v1

    invoke-virtual {v0, p1, p2, v1}, Lcom/android/tools/r8/ir/optimize/a;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Ljava/util/function/Consumer;)V

    .line 351
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/aA;->A()V

    .line 352
    new-instance p1, Lcom/android/tools/r8/internal/pA;

    iget-object v0, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-direct {p1, v0}, Lcom/android/tools/r8/internal/pA;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 353
    invoke-static {}, Lcom/android/tools/r8/internal/i8;->b()Lcom/android/tools/r8/internal/i8;

    move-result-object v0

    invoke-static {}, Lcom/android/tools/r8/internal/Gp0;->a()Lcom/android/tools/r8/internal/Gp0;

    move-result-object v1

    invoke-virtual {p1, p2, v0, v1}, Lcom/android/tools/r8/internal/gA;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/i8;Lcom/android/tools/r8/internal/Gp0;)Lcom/android/tools/r8/graph/i0;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/nR;

    return-object p1
.end method

.method public final synthetic a(Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/r3;Lcom/android/tools/r8/graph/E2;)Ljava/util/List;
    .locals 9

    .line 103
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 104
    sget-object v7, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda16;->INSTANCE:Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda16;

    new-instance v8, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda4;

    move-object v0, v8

    move-object v1, p0

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move-object v5, v6

    invoke-direct/range {v0 .. v5}, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda4;-><init>(Lcom/android/tools/r8/internal/u3;Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/r3;Ljava/util/List;)V

    invoke-virtual {p4, v8, v7}, Lcom/android/tools/r8/graph/E2;->h(Ljava/util/function/Consumer;Ljava/util/function/Predicate;)V

    return-object v6
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;)V
    .locals 2

    .line 290
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object v0

    .line 291
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/i0;->C0()Z

    move-result v1

    if-nez v1, :cond_2

    .line 292
    sget-boolean v0, Lcom/android/tools/r8/internal/u3;->c:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 293
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/j1;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    .line 294
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return-void

    .line 297
    :cond_2
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/i0;->q0()Lcom/android/tools/r8/internal/nR;

    move-result-object v0

    .line 298
    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/internal/u3;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/nR;)Lcom/android/tools/r8/internal/nR;

    move-result-object v1

    .line 299
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/V10;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 300
    iget-object v0, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/graph/D5;->a(Lcom/android/tools/r8/graph/i0;Lcom/android/tools/r8/graph/y;)V

    :cond_3
    return-void
.end method

.method public final synthetic a(Lcom/android/tools/r8/graph/E2;)V
    .locals 2

    .line 288
    sget-object v0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda7;->INSTANCE:Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda7;

    new-instance v1, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda2;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/internal/u3;)V

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/graph/E2;->h(Ljava/util/function/Consumer;Ljava/util/function/Predicate;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/r3;Ljava/util/List;Lcom/android/tools/r8/graph/D5;)V
    .locals 1

    .line 48
    iget-object p1, p1, Lcom/android/tools/r8/internal/O40;->a:Lcom/android/tools/r8/internal/JS;

    .line 49
    sget-boolean v0, Lcom/android/tools/r8/internal/JS;->e:Z

    if-nez v0, :cond_2

    if-nez v0, :cond_1

    .line 50
    iget-object v0, p1, Lcom/android/tools/r8/internal/JS;->c:Lcom/android/tools/r8/internal/Fy;

    if-ne v0, p2, :cond_0

    goto :goto_0

    .line 51
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 52
    :cond_1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 53
    :cond_2
    :goto_0
    iget-object p1, p1, Lcom/android/tools/r8/internal/JS;->d:Ljava/util/Set;

    .line 54
    invoke-virtual {p5}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_5

    .line 55
    new-instance p1, Lcom/android/tools/r8/internal/t3;

    iget-object p2, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-direct {p1, p2, p5, p3}, Lcom/android/tools/r8/internal/t3;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/r3;)V

    .line 56
    invoke-virtual {p5, p1}, Lcom/android/tools/r8/graph/D5;->a(Lcom/android/tools/r8/graph/b6;)V

    .line 57
    iget-object p1, p1, Lcom/android/tools/r8/graph/c6;->e:Ljava/lang/Boolean;

    .line 58
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    if-eqz p1, :cond_5

    .line 59
    sget-boolean p1, Lcom/android/tools/r8/internal/u3;->c:Z

    if-nez p1, :cond_4

    invoke-virtual {p5}, Lcom/android/tools/r8/graph/H0;->D()Lcom/android/tools/r8/internal/iV;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/iV;->w()Z

    move-result p1

    if-nez p1, :cond_3

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 60
    :cond_4
    :goto_1
    invoke-interface {p4, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_5
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/r3;Lcom/android/tools/r8/internal/O40;)V
    .locals 12

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    .line 2
    iget-object v1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v7

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v8

    :goto_0
    invoke-interface {v8}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v8}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    move-object v9, v1

    check-cast v9, Lcom/android/tools/r8/graph/E2;

    .line 4
    sget-object v10, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda16;->INSTANCE:Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda16;

    new-instance v11, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;

    move-object v1, v11

    move-object v2, p0

    move-object v3, p1

    move-object v4, v7

    move-object v5, p2

    move-object v6, v0

    invoke-direct/range {v1 .. v6}, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;-><init>(Lcom/android/tools/r8/internal/u3;Lcom/android/tools/r8/internal/r3;Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;)V

    invoke-virtual {v9, v11, v10}, Lcom/android/tools/r8/graph/E2;->h(Ljava/util/function/Consumer;Ljava/util/function/Predicate;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/r3;Lcom/android/tools/r8/internal/O40;Ljava/util/concurrent/ExecutorService;)V
    .locals 3

    .line 61
    iget-object v0, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    .line 62
    iget-object v1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    .line 64
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v1

    new-instance v2, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;

    invoke-direct {v2, p0, p2, v0, p1}, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/u3;Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/r3;)V

    iget-object p1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    .line 81
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/utils/w;->O()Lcom/android/tools/r8/threading/ThreadingModule;

    move-result-object p1

    .line 82
    invoke-static {v1, v2, p1, p3}, Lcom/android/tools/r8/internal/ep0;->a(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/pp0;Lcom/android/tools/r8/threading/ThreadingModule;Ljava/util/concurrent/ExecutorService;)Ljava/util/ArrayList;

    move-result-object p1

    .line 102
    new-instance p3, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda1;

    invoke-direct {p3, p2, v0}, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;)V

    invoke-virtual {p1, p3}, Ljava/util/ArrayList;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/r3;Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/D5;)V
    .locals 5

    .line 5
    invoke-virtual {p5}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/i0;->D0()Z

    move-result p2

    if-eqz p2, :cond_0

    return-void

    :cond_0
    if-eqz p1, :cond_3

    .line 11
    invoke-virtual {p5}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/x2;

    .line 12
    iget-object v0, p1, Lcom/android/tools/r8/internal/zX;->i:Lcom/android/tools/r8/internal/Z5;

    invoke-interface {v0, p2, p2}, Lcom/android/tools/r8/internal/Z5;->a(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/x2;

    .line 13
    iget-object p1, p1, Lcom/android/tools/r8/internal/r3;->n:Ljava/util/IdentityHashMap;

    invoke-virtual {p1, p2}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_3

    .line 14
    sget-boolean p1, Lcom/android/tools/r8/internal/u3;->c:Z

    if-nez p1, :cond_2

    iget-object p1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1, p5}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object p1

    invoke-virtual {p1, p5}, Lcom/android/tools/r8/shaking/v1;->a(Lcom/android/tools/r8/graph/D5;)Z

    move-result p1

    if-eqz p1, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 15
    :cond_2
    :goto_0
    iget-object p1, p3, Lcom/android/tools/r8/internal/O40;->a:Lcom/android/tools/r8/internal/JS;

    .line 16
    invoke-virtual {p1, p4, p5}, Lcom/android/tools/r8/internal/JS;->a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/D5;)V

    .line 17
    iget-object p1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->R()Lcom/android/tools/r8/utils/w$q;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/utils/w$q;->Z0:Ljava/util/function/Consumer;

    invoke-interface {p1, p5}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    return-void

    .line 23
    :cond_3
    invoke-virtual {p5}, Lcom/android/tools/r8/graph/H0;->D()Lcom/android/tools/r8/internal/iV;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/iV;->j()Lcom/android/tools/r8/internal/r8;

    move-result-object p1

    .line 24
    iget-object p2, p0, Lcom/android/tools/r8/internal/u3;->b:Lcom/android/tools/r8/internal/A3;

    .line 25
    iget-object p2, p2, Lcom/android/tools/r8/internal/A3;->b:Ljava/util/IdentityHashMap;

    .line 26
    invoke-virtual {p5}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    .line 27
    sget-object v1, Lcom/android/tools/r8/internal/xV;->b:Lcom/android/tools/r8/internal/xV;

    .line 28
    invoke-interface {p2, v0, v1}, Ljava/util/Map;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/internal/xV;

    .line 29
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 30
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 31
    instance-of v0, p1, Lcom/android/tools/r8/internal/Mf;

    if-nez v0, :cond_4

    goto :goto_3

    .line 32
    :cond_4
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/r8;->a()Lcom/android/tools/r8/internal/Mf;

    move-result-object v0

    const/4 v1, 0x0

    .line 34
    :goto_1
    invoke-virtual {p5}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/j1;

    .line 35
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v3

    .line 36
    check-cast v3, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/j1;->y0()Z

    move-result v2

    invoke-virtual {v3, v2}, Lcom/android/tools/r8/graph/x2;->a(Z)I

    move-result v2

    if-ge v1, v2, :cond_7

    .line 37
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/r8;->a(I)Lcom/android/tools/r8/internal/E1;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/E1;->f()Z

    move-result v2

    if-eqz v2, :cond_5

    goto :goto_2

    .line 38
    :cond_5
    iget-object v2, p2, Lcom/android/tools/r8/internal/xV;->a:Lcom/android/tools/r8/internal/TF;

    .line 39
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    sget-object v4, Lcom/android/tools/r8/internal/n2;->a:Lcom/android/tools/r8/internal/n2;

    .line 40
    invoke-interface {v2, v3, v4}, Ljava/util/Map;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/s30;

    .line 41
    invoke-virtual {p5, v1}, Lcom/android/tools/r8/graph/H0;->a(I)Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    .line 42
    invoke-virtual {v2, p5, v0, v1, v3}, Lcom/android/tools/r8/internal/s30;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/Mf;ILcom/android/tools/r8/graph/J2;)Z

    move-result v2

    if-eqz v2, :cond_6

    .line 43
    :goto_2
    iget-object p1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    .line 44
    invoke-virtual {p1, p5}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object p1

    invoke-virtual {p1, p5}, Lcom/android/tools/r8/shaking/v1;->a(Lcom/android/tools/r8/graph/D5;)Z

    move-result p1

    if-eqz p1, :cond_7

    .line 45
    iget-object p1, p3, Lcom/android/tools/r8/internal/O40;->a:Lcom/android/tools/r8/internal/JS;

    .line 46
    invoke-virtual {p1, p4, p5}, Lcom/android/tools/r8/internal/JS;->a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/D5;)V

    .line 47
    iget-object p1, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->R()Lcom/android/tools/r8/utils/w$q;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/utils/w$q;->Z0:Ljava/util/function/Consumer;

    invoke-interface {p1, p5}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    goto :goto_3

    :cond_6
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_7
    :goto_3
    return-void
.end method

.method public final a(Ljava/util/concurrent/ExecutorService;)V
    .locals 3

    .line 106
    iget-object v0, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    .line 107
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda3;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda3;-><init>(Lcom/android/tools/r8/internal/u3;)V

    iget-object v2, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    .line 108
    invoke-static {v2, v0, v1, p1}, Lcom/android/tools/r8/K;->a(Lcom/android/tools/r8/graph/y;Ljava/util/Collection;Ljava/util/function/Consumer;Ljava/util/concurrent/ExecutorService;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/pR;)Z
    .locals 2

    .line 301
    instance-of v0, p2, Lcom/android/tools/r8/graph/l1;

    if-nez v0, :cond_0

    goto :goto_0

    .line 304
    :cond_0
    check-cast p2, Lcom/android/tools/r8/graph/l1;

    .line 305
    iget-object v0, p0, Lcom/android/tools/r8/internal/u3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 306
    iget-object v1, p2, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v0, v1, p2, p1}, Lcom/android/tools/r8/graph/j;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/graph/z3;

    move-result-object p1

    .line 307
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/z3;->o()Lcom/android/tools/r8/graph/B5;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 308
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/F0;->x()Lcom/android/tools/r8/internal/yv;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/yv;->f()Lcom/android/tools/r8/internal/E1;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 309
    instance-of p1, p1, Lcom/android/tools/r8/internal/o7;

    if-eqz p1, :cond_1

    const/4 p1, 0x1

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x0

    :goto_1
    return p1
.end method
