.class public final Lcom/android/tools/r8/internal/Ui0;
.super Lcom/android/tools/r8/internal/sP;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/lx;


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/sP;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/lx;)V
    .locals 0

    .line 1
    check-cast p1, Lcom/android/tools/r8/internal/sP;

    iput-object p1, p0, Lcom/android/tools/r8/internal/Ui0;->b:Lcom/android/tools/r8/internal/sP;

    const/4 p1, 0x1

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/sP;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ui0;->b:Lcom/android/tools/r8/internal/sP;

    invoke-interface {v0, p1}, Lcom/android/tools/r8/internal/lx;->a(Ljava/lang/Object;)Ljava/lang/Object;

    return-object p1
.end method
