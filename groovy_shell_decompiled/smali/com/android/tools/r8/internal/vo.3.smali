.class public abstract Lcom/android/tools/r8/internal/vo;
.super Lcom/android/tools/r8/internal/Hm;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic h:Z = true


# instance fields
.field public final f:S

.field public g:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(I)V
    .locals 1

    .line 4
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Hm;-><init>()V

    .line 5
    sget-boolean v0, Lcom/android/tools/r8/internal/vo;->h:Z

    if-nez v0, :cond_1

    if-ltz p1, :cond_0

    const/16 v0, 0xff

    if-gt p1, v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    int-to-short p1, p1

    .line 6
    iput-short p1, p0, Lcom/android/tools/r8/internal/vo;->f:S

    const/4 p1, -0x1

    .line 7
    iput p1, p0, Lcom/android/tools/r8/internal/vo;->g:I

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Zo;)V
    .locals 0

    .line 1
    invoke-direct {p0, p2}, Lcom/android/tools/r8/internal/Hm;-><init>(Lcom/android/tools/r8/internal/Zo;)V

    int-to-short p1, p1

    .line 2
    iput-short p1, p0, Lcom/android/tools/r8/internal/vo;->f:S

    .line 3
    invoke-static {p2}, Lcom/android/tools/r8/internal/Yo;->a(Lcom/android/tools/r8/internal/Zo;)I

    move-result p1

    iput p1, p0, Lcom/android/tools/r8/internal/vo;->g:I

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/vo;)I
    .locals 0

    .line 1
    iget-short p0, p0, Lcom/android/tools/r8/internal/vo;->f:S

    return p0
.end method

.method public static a(Lcom/android/tools/r8/internal/ko0;)V
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/vo$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/vo$$ExternalSyntheticLambda1;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ko0;->a(Ljava/util/function/ToIntFunction;)Lcom/android/tools/r8/internal/ko0;

    move-result-object p0

    sget-object v0, Lcom/android/tools/r8/internal/vo$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/internal/vo$$ExternalSyntheticLambda2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ko0;->a(Ljava/util/function/ToIntFunction;)Lcom/android/tools/r8/internal/ko0;

    return-void
.end method

.method public static synthetic b(Lcom/android/tools/r8/internal/vo;)I
    .locals 0

    .line 1
    iget p0, p0, Lcom/android/tools/r8/internal/vo;->g:I

    return p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Yo;Lcom/android/tools/r8/internal/Qe;)I
    .locals 1

    .line 7
    check-cast p1, Lcom/android/tools/r8/internal/vo;

    sget-object v0, Lcom/android/tools/r8/internal/vo$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/vo$$ExternalSyntheticLambda0;

    invoke-virtual {p2, p0, p1, v0}, Lcom/android/tools/r8/internal/Qe;->a(Ljava/lang/Object;Ljava/lang/Object;Lcom/android/tools/r8/internal/io0;)I

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/s5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/jQ;Ljava/nio/ShortBuffer;)V
    .locals 0

    .line 3
    iget-short p1, p0, Lcom/android/tools/r8/internal/vo;->f:S

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result p2

    invoke-static {p1, p2, p6}, Lcom/android/tools/r8/internal/Yo;->a(IILjava/nio/ShortBuffer;)V

    .line 5
    sget-boolean p1, Lcom/android/tools/r8/internal/vo;->h:Z

    if-nez p1, :cond_1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Yo;->o()I

    move-result p1

    iget p2, p0, Lcom/android/tools/r8/internal/vo;->g:I

    add-int/2addr p1, p2

    rem-int/lit8 p1, p1, 0x2

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 6
    :cond_1
    :goto_0
    iget p1, p0, Lcom/android/tools/r8/internal/vo;->g:I

    int-to-long p1, p1

    invoke-static {p1, p2, p6}, Lcom/android/tools/r8/internal/Yo;->a(JLjava/nio/ShortBuffer;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/dex/M;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/jQ;)V
    .locals 0

    return-void
.end method

.method public final b(Lcom/android/tools/r8/internal/Th0;)Ljava/lang/String;
    .locals 3

    .line 4
    iget-short p1, p0, Lcom/android/tools/r8/internal/vo;->f:S

    iget v0, p0, Lcom/android/tools/r8/internal/vo;->g:I

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/Yo;->e(I)Ljava/lang/String;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "v"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ", "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Yo;->b(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public final b(Lcom/android/tools/r8/internal/mz;)V
    .locals 2

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/vo$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/vo$$ExternalSyntheticLambda0;

    check-cast p1, Lcom/android/tools/r8/internal/oz;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    new-instance v1, Lcom/android/tools/r8/internal/nz;

    invoke-direct {v1, p0, p1}, Lcom/android/tools/r8/internal/nz;-><init>(Ljava/lang/Object;Lcom/android/tools/r8/internal/oz;)V

    invoke-interface {v0, v1}, Lcom/android/tools/r8/internal/io0;->a(Lcom/android/tools/r8/internal/ko0;)V

    return-void
.end method

.method public final hashCode()I
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/vo;->g:I

    shl-int/lit8 v0, v0, 0x8

    iget-short v1, p0, Lcom/android/tools/r8/internal/vo;->f:S

    or-int/2addr v0, v1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    xor-int/2addr v0, v1

    return v0
.end method

.method public final t()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/vo;->g:I

    return v0
.end method

.method public final y()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
