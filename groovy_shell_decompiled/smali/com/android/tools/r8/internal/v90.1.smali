.class public final Lcom/android/tools/r8/internal/v90;
.super Lcom/android/tools/r8/internal/z90;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final E:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/z90;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/v90;->E:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/Object;
    .locals 0

    return-object p0
.end method

.method public final toString()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/v90;->E:Ljava/lang/String;

    return-object v0
.end method
