.class public final Lcom/android/tools/r8/internal/sZ;
.super Lcom/android/tools/r8/internal/vZ;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final c:Lcom/android/tools/r8/graph/J2;

.field public final d:Lcom/android/tools/r8/graph/J2;

.field public final e:Lcom/android/tools/r8/graph/x2;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/vZ;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;)V

    .line 2
    iput-object p3, p0, Lcom/android/tools/r8/internal/sZ;->c:Lcom/android/tools/r8/graph/J2;

    .line 3
    iput-object p4, p0, Lcom/android/tools/r8/internal/sZ;->d:Lcom/android/tools/r8/graph/J2;

    .line 4
    iput-object p5, p0, Lcom/android/tools/r8/internal/sZ;->e:Lcom/android/tools/r8/graph/x2;

    return-void
.end method
