.class public abstract Lcom/android/tools/r8/internal/s1;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a()Lcom/android/tools/r8/internal/EB;
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 1

    if-ne p1, p0, :cond_0

    const/4 p1, 0x1

    return p1

    .line 1
    :cond_0
    instance-of v0, p1, Lcom/android/tools/r8/internal/s1;

    if-eqz v0, :cond_1

    .line 2
    check-cast p1, Lcom/android/tools/r8/internal/s1;

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/s1;->a()Lcom/android/tools/r8/internal/EB;

    move-result-object v0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/s1;->a()Lcom/android/tools/r8/internal/EB;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Set;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public final hashCode()I
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/s1;->a()Lcom/android/tools/r8/internal/EB;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->hashCode()I

    move-result v0

    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/s1;->a()Lcom/android/tools/r8/internal/EB;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
