.class public final Lcom/android/tools/r8/internal/V9;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic c:Z = true


# instance fields
.field public a:Ljava/util/HashMap;

.field public final b:Lcom/android/tools/r8/utils/j;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/utils/j;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lcom/android/tools/r8/internal/V9;->a:Ljava/util/HashMap;

    .line 7
    iput-object p1, p0, Lcom/android/tools/r8/internal/V9;->b:Lcom/android/tools/r8/utils/j;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/T9;Lcom/android/tools/r8/ProgramResource;)V
    .locals 2

    .line 24
    invoke-interface {p1}, Lcom/android/tools/r8/ProgramResource;->getKind()Lcom/android/tools/r8/ProgramResource$Kind;

    move-result-object v0

    sget-object v1, Lcom/android/tools/r8/ProgramResource$Kind;->CF:Lcom/android/tools/r8/ProgramResource$Kind;

    if-eq v0, v1, :cond_0

    return-void

    .line 28
    :cond_0
    :try_start_0
    new-instance v0, Lcom/android/tools/r8/internal/vd;

    invoke-interface {p1}, Lcom/android/tools/r8/ProgramResource;->getByteStream()Ljava/io/InputStream;

    move-result-object p1

    invoke-static {p1}, Lcom/android/tools/r8/internal/om0;->a(Ljava/io/InputStream;)[B

    move-result-object p1

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/vd;-><init>([B)V

    const/4 p1, 0x4

    const/4 v1, 0x0

    new-array v1, v1, [Lcom/android/tools/r8/internal/I4;

    .line 29
    invoke-virtual {v0, p0, v1, p1}, Lcom/android/tools/r8/internal/vd;->a(Lcom/android/tools/r8/internal/Gd;[Lcom/android/tools/r8/internal/I4;I)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Lcom/android/tools/r8/ResourceException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method


# virtual methods
.method public final a()V
    .locals 6

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/T9;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/T9;-><init>(Lcom/android/tools/r8/internal/V9;)V

    .line 2
    iget-object v1, p0, Lcom/android/tools/r8/internal/V9;->b:Lcom/android/tools/r8/utils/j;

    invoke-virtual {v1}, Lcom/android/tools/r8/utils/j;->i()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/ProgramResourceProvider;

    .line 3
    instance-of v3, v2, Lcom/android/tools/r8/utils/ArchiveResourceProvider;

    if-eqz v3, :cond_1

    .line 4
    check-cast v2, Lcom/android/tools/r8/utils/ArchiveResourceProvider;

    .line 5
    new-instance v3, Lcom/android/tools/r8/internal/V9$$ExternalSyntheticLambda0;

    invoke-direct {v3, v0}, Lcom/android/tools/r8/internal/V9$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/T9;)V

    invoke-virtual {v2, v3}, Lcom/android/tools/r8/utils/ArchiveResourceProvider;->accept(Ljava/util/function/Consumer;)V

    goto :goto_0

    .line 19
    :cond_1
    invoke-interface {v2}, Lcom/android/tools/r8/ProgramResourceProvider;->getProgramResources()Ljava/util/Collection;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :catch_0
    :cond_2
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/ProgramResource;

    .line 20
    invoke-interface {v3}, Lcom/android/tools/r8/ProgramResource;->getKind()Lcom/android/tools/r8/ProgramResource$Kind;

    move-result-object v4

    sget-object v5, Lcom/android/tools/r8/ProgramResource$Kind;->CF:Lcom/android/tools/r8/ProgramResource$Kind;

    if-ne v4, v5, :cond_2

    .line 22
    :try_start_0
    new-instance v4, Lcom/android/tools/r8/internal/vd;

    invoke-interface {v3}, Lcom/android/tools/r8/ProgramResource;->getByteStream()Ljava/io/InputStream;

    move-result-object v3

    invoke-static {v3}, Lcom/android/tools/r8/internal/om0;->a(Ljava/io/InputStream;)[B

    move-result-object v3

    invoke-direct {v4, v3}, Lcom/android/tools/r8/internal/vd;-><init>([B)V

    const/4 v3, 0x4

    const/4 v5, 0x0

    new-array v5, v5, [Lcom/android/tools/r8/internal/I4;

    .line 23
    invoke-virtual {v4, v0, v5, v3}, Lcom/android/tools/r8/internal/vd;->a(Lcom/android/tools/r8/internal/Gd;[Lcom/android/tools/r8/internal/I4;I)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :cond_3
    return-void
.end method
