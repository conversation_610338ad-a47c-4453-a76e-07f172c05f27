.class public final synthetic Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda13;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/Yw;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Rc;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Rc;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda13;->f$0:Lcom/android/tools/r8/internal/Rc;

    return-void
.end method


# virtual methods
.method public final forEach(Ljava/util/function/Consumer;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda13;->f$0:Lcom/android/tools/r8/internal/Rc;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Rc;->a(Ljava/util/function/Consumer;)V

    return-void
.end method
