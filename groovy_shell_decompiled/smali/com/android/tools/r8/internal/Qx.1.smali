.class public abstract Lcom/android/tools/r8/internal/Qx;
.super Lcom/android/tools/r8/internal/Vx;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final b:Lcom/android/tools/r8/internal/Mv;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Vx;-><init>()V

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Mv;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Mv;-><init>()V

    .line 3
    iput-object v0, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/Ox;)V
    .locals 1

    .line 4
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Vx;-><init>()V

    .line 5
    iget-object v0, p1, Lcom/android/tools/r8/internal/Ox;->c:Lcom/android/tools/r8/internal/Mv;

    .line 6
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Mv;->a()V

    const/4 v0, 0x0

    .line 7
    iput-boolean v0, p1, Lcom/android/tools/r8/internal/Ox;->d:Z

    .line 8
    iget-object p1, p1, Lcom/android/tools/r8/internal/Ox;->c:Lcom/android/tools/r8/internal/Mv;

    .line 9
    iput-object p1, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Tx;)Ljava/lang/Object;
    .locals 3

    .line 159
    iget-object v0, p1, Lcom/android/tools/r8/internal/Tx;->a:Lcom/android/tools/r8/internal/Qx;

    .line 160
    invoke-interface {p0}, Lcom/android/tools/r8/internal/BU;->getDefaultInstanceForType()Lcom/android/tools/r8/internal/N0;

    move-result-object v1

    if-ne v0, v1, :cond_4

    .line 161
    iget-object v0, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    iget-object v1, p1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 162
    iget-object v0, v0, Lcom/android/tools/r8/internal/Mv;->a:Lcom/android/tools/r8/internal/tk0;

    .line 163
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/tk0;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    .line 164
    iget-object p1, p1, Lcom/android/tools/r8/internal/Tx;->b:Ljava/lang/Object;

    return-object p1

    .line 165
    :cond_0
    iget-object v1, p1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 166
    iget-boolean v2, v1, Lcom/android/tools/r8/internal/Sx;->d:Z

    if-eqz v2, :cond_2

    .line 167
    iget-object v1, v1, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 168
    iget-object v1, v1, Lcom/android/tools/r8/internal/Pu0;->b:Lcom/android/tools/r8/internal/Ru0;

    .line 169
    sget-object v2, Lcom/android/tools/r8/internal/Ru0;->j:Lcom/android/tools/r8/internal/Ru0;

    if-ne v1, v2, :cond_3

    .line 170
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 171
    check-cast v0, Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 172
    invoke-virtual {p1, v2}, Lcom/android/tools/r8/internal/Tx;->a(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    move-object v0, v1

    goto :goto_1

    .line 179
    :cond_2
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/Tx;->a(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    :cond_3
    :goto_1
    return-object v0

    .line 180
    :cond_4
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "This extension is for a different message type.  Please make sure that you are not suppressing any generics type warnings."

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/ie;Lcom/android/tools/r8/internal/Ku;I)Z
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    invoke-interface {p0}, Lcom/android/tools/r8/internal/BU;->getDefaultInstanceForType()Lcom/android/tools/r8/internal/N0;

    move-result-object v1

    and-int/lit8 v2, p4, 0x7

    ushr-int/lit8 v3, p4, 0x3

    .line 2
    iget-object v4, p3, Lcom/android/tools/r8/internal/Ku;->a:Ljava/util/Map;

    new-instance v5, Lcom/android/tools/r8/internal/Ju;

    invoke-direct {v5, v3, v1}, Lcom/android/tools/r8/internal/Ju;-><init>(ILcom/android/tools/r8/internal/N0;)V

    invoke-interface {v4, v5}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/Tx;

    const/4 v3, 0x0

    const/4 v4, 0x1

    if-nez v1, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    iget-object v5, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 4
    iget-object v6, v5, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 5
    sget-object v7, Lcom/android/tools/r8/internal/Mv;->c:Lcom/android/tools/r8/internal/Mv;

    .line 6
    iget v7, v6, Lcom/android/tools/r8/internal/Pu0;->c:I

    if-ne v2, v7, :cond_1

    move v2, v3

    goto :goto_1

    .line 7
    :cond_1
    iget-boolean v5, v5, Lcom/android/tools/r8/internal/Sx;->d:Z

    if-eqz v5, :cond_2

    invoke-virtual {v6}, Lcom/android/tools/r8/internal/Pu0;->a()Z

    move-result v5

    if-eqz v5, :cond_2

    iget-object v5, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 8
    iget-object v5, v5, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    const/4 v5, 0x2

    if-ne v2, v5, :cond_2

    move v2, v4

    goto :goto_1

    :cond_2
    :goto_0
    move v2, v3

    move v3, v4

    :goto_1
    if-eqz v3, :cond_3

    .line 9
    invoke-virtual {p1, p4, p2}, Lcom/android/tools/r8/internal/be;->a(ILcom/android/tools/r8/internal/ie;)Z

    move-result v4

    goto/16 :goto_6

    :cond_3
    const/4 p2, 0x0

    if-eqz v2, :cond_7

    .line 13
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result p3

    .line 14
    invoke-virtual {p1, p3}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result p3

    .line 15
    iget-object p4, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 16
    iget-object p4, p4, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 17
    sget-object v2, Lcom/android/tools/r8/internal/Pu0;->h:Lcom/android/tools/r8/internal/Pu0;

    if-ne p4, v2, :cond_5

    .line 18
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result p4

    if-gtz p4, :cond_4

    goto :goto_3

    .line 19
    :cond_4
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    .line 20
    iget-object p1, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 21
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 22
    throw p2

    .line 33
    :cond_5
    :goto_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result p2

    if-lez p2, :cond_6

    .line 34
    iget-object p2, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 35
    iget-object p2, p2, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 36
    invoke-static {p1, p2}, Lcom/android/tools/r8/internal/Mv;->a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Pu0;)Ljava/lang/Object;

    move-result-object p2

    .line 40
    iget-object p4, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    invoke-virtual {v0, p4, p2}, Lcom/android/tools/r8/internal/Mv;->a(Lcom/android/tools/r8/internal/Sx;Ljava/lang/Object;)V

    goto :goto_2

    .line 41
    :cond_6
    :goto_3
    iput p3, p1, Lcom/android/tools/r8/internal/be;->h:I

    .line 42
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->j()V

    goto/16 :goto_6

    .line 43
    :cond_7
    iget-object p4, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 44
    iget-object p4, p4, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 45
    iget-object p4, p4, Lcom/android/tools/r8/internal/Pu0;->b:Lcom/android/tools/r8/internal/Ru0;

    .line 46
    invoke-virtual {p4}, Ljava/lang/Enum;->ordinal()I

    move-result p4

    const/4 v2, 0x7

    if-eq p4, v2, :cond_11

    const/16 v2, 0x8

    if-eq p4, v2, :cond_8

    .line 83
    iget-object p2, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 84
    iget-object p2, p2, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 85
    invoke-static {p1, p2}, Lcom/android/tools/r8/internal/Mv;->a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Pu0;)Ljava/lang/Object;

    move-result-object p1

    goto/16 :goto_5

    .line 86
    :cond_8
    iget-object p4, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 87
    iget-boolean v2, p4, Lcom/android/tools/r8/internal/Sx;->d:Z

    if-nez v2, :cond_9

    .line 88
    iget-object v2, v0, Lcom/android/tools/r8/internal/Mv;->a:Lcom/android/tools/r8/internal/tk0;

    invoke-virtual {v2, p4}, Lcom/android/tools/r8/internal/tk0;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p4

    .line 89
    check-cast p4, Lcom/android/tools/r8/internal/N0;

    if-eqz p4, :cond_9

    .line 92
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/N0;->c()Lcom/android/tools/r8/internal/Nx;

    move-result-object p2

    :cond_9
    if-nez p2, :cond_a

    .line 93
    iget-object p2, v1, Lcom/android/tools/r8/internal/Tx;->c:Lcom/android/tools/r8/internal/Vx;

    .line 94
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/N0;->b()Lcom/android/tools/r8/internal/Nx;

    move-result-object p2

    .line 97
    :cond_a
    iget-object p4, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 98
    iget-object v2, p4, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 99
    sget-object v3, Lcom/android/tools/r8/internal/Pu0;->f:Lcom/android/tools/r8/internal/Ju0;

    const-string v5, "Protocol message end-group tag did not match expected tag."

    const-string v6, "Protocol message had too many levels of nesting.  May be malicious.  Use CodedInputStream.setRecursionLimit() to increase the depth limit."

    const/16 v7, 0x40

    if-ne v2, v3, :cond_d

    .line 100
    iget p4, p4, Lcom/android/tools/r8/internal/Sx;->b:I

    .line 101
    iget v2, p1, Lcom/android/tools/r8/internal/be;->i:I

    if-ge v2, v7, :cond_c

    add-int/2addr v2, v4

    .line 104
    iput v2, p1, Lcom/android/tools/r8/internal/be;->i:I

    .line 105
    invoke-virtual {p2, p1, p3}, Lcom/android/tools/r8/internal/Nx;->a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/Nx;

    shl-int/lit8 p3, p4, 0x3

    or-int/lit8 p3, p3, 0x4

    .line 106
    iget p4, p1, Lcom/android/tools/r8/internal/be;->f:I

    if-ne p4, p3, :cond_b

    .line 107
    iget p3, p1, Lcom/android/tools/r8/internal/be;->i:I

    sub-int/2addr p3, v4

    iput p3, p1, Lcom/android/tools/r8/internal/be;->i:I

    goto :goto_4

    .line 108
    :cond_b
    new-instance p1, Lcom/android/tools/r8/internal/kI;

    invoke-direct {p1, v5}, Lcom/android/tools/r8/internal/kI;-><init>(Ljava/lang/String;)V

    .line 109
    throw p1

    .line 110
    :cond_c
    new-instance p1, Lcom/android/tools/r8/internal/kI;

    invoke-direct {p1, v6}, Lcom/android/tools/r8/internal/kI;-><init>(Ljava/lang/String;)V

    .line 111
    throw p1

    .line 112
    :cond_d
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result p4

    .line 113
    iget v2, p1, Lcom/android/tools/r8/internal/be;->i:I

    if-ge v2, v7, :cond_10

    .line 116
    invoke-virtual {p1, p4}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result p4

    .line 117
    iget v2, p1, Lcom/android/tools/r8/internal/be;->i:I

    add-int/2addr v2, v4

    iput v2, p1, Lcom/android/tools/r8/internal/be;->i:I

    .line 118
    invoke-virtual {p2, p1, p3}, Lcom/android/tools/r8/internal/Nx;->a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/Nx;

    .line 119
    iget p3, p1, Lcom/android/tools/r8/internal/be;->f:I

    if-nez p3, :cond_f

    .line 120
    iget p3, p1, Lcom/android/tools/r8/internal/be;->i:I

    sub-int/2addr p3, v4

    iput p3, p1, Lcom/android/tools/r8/internal/be;->i:I

    .line 121
    iput p4, p1, Lcom/android/tools/r8/internal/be;->h:I

    .line 122
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->j()V

    .line 123
    :goto_4
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Nx;->a()Lcom/android/tools/r8/internal/N0;

    move-result-object p1

    .line 145
    :goto_5
    iget-object p2, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 146
    iget-boolean p3, p2, Lcom/android/tools/r8/internal/Sx;->d:Z

    if-eqz p3, :cond_e

    .line 147
    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/Tx;->b(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {v0, p2, p1}, Lcom/android/tools/r8/internal/Mv;->a(Lcom/android/tools/r8/internal/Sx;Ljava/lang/Object;)V

    goto :goto_6

    .line 150
    :cond_e
    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/Tx;->b(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {v0, p2, p1}, Lcom/android/tools/r8/internal/Mv;->c(Lcom/android/tools/r8/internal/Sx;Ljava/lang/Object;)V

    :goto_6
    return v4

    .line 151
    :cond_f
    new-instance p1, Lcom/android/tools/r8/internal/kI;

    invoke-direct {p1, v5}, Lcom/android/tools/r8/internal/kI;-><init>(Ljava/lang/String;)V

    .line 152
    throw p1

    .line 153
    :cond_10
    new-instance p1, Lcom/android/tools/r8/internal/kI;

    invoke-direct {p1, v6}, Lcom/android/tools/r8/internal/kI;-><init>(Ljava/lang/String;)V

    .line 154
    throw p1

    .line 155
    :cond_11
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    .line 156
    iget-object p1, v1, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 157
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 158
    throw p2
.end method

.method public final d()Z
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    const/4 v1, 0x0

    move v2, v1

    .line 2
    :goto_0
    iget-object v3, v0, Lcom/android/tools/r8/internal/Mv;->a:Lcom/android/tools/r8/internal/tk0;

    .line 3
    iget-object v3, v3, Lcom/android/tools/r8/internal/tk0;->c:Ljava/util/List;

    .line 4
    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v2, v3, :cond_1

    .line 5
    iget-object v3, v0, Lcom/android/tools/r8/internal/Mv;->a:Lcom/android/tools/r8/internal/tk0;

    .line 6
    iget-object v3, v3, Lcom/android/tools/r8/internal/tk0;->c:Ljava/util/List;

    .line 7
    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 8
    invoke-static {v3}, Lcom/android/tools/r8/internal/Mv;->a(Ljava/util/Map$Entry;)Z

    move-result v3

    if-nez v3, :cond_0

    goto :goto_2

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 13
    :cond_1
    iget-object v0, v0, Lcom/android/tools/r8/internal/Mv;->a:Lcom/android/tools/r8/internal/tk0;

    .line 14
    iget-object v2, v0, Lcom/android/tools/r8/internal/tk0;->d:Ljava/util/Map;

    .line 15
    invoke-interface {v2}, Ljava/util/Map;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_2

    sget-object v0, Lcom/android/tools/r8/internal/zk0;->b:Lcom/android/tools/r8/internal/xk0;

    goto :goto_1

    :cond_2
    iget-object v0, v0, Lcom/android/tools/r8/internal/tk0;->d:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    .line 16
    :goto_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_4

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map$Entry;

    .line 17
    invoke-static {v2}, Lcom/android/tools/r8/internal/Mv;->a(Ljava/util/Map$Entry;)Z

    move-result v2

    if-nez v2, :cond_3

    goto :goto_2

    :cond_4
    const/4 v1, 0x1

    :goto_2
    return v1
.end method

.method public final e()I
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    const/4 v1, 0x0

    move v2, v1

    .line 2
    :goto_0
    iget-object v3, v0, Lcom/android/tools/r8/internal/Mv;->a:Lcom/android/tools/r8/internal/tk0;

    .line 3
    iget-object v3, v3, Lcom/android/tools/r8/internal/tk0;->c:Ljava/util/List;

    .line 4
    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v1, v3, :cond_0

    .line 5
    iget-object v3, v0, Lcom/android/tools/r8/internal/Mv;->a:Lcom/android/tools/r8/internal/tk0;

    .line 6
    iget-object v3, v3, Lcom/android/tools/r8/internal/tk0;->c:Ljava/util/List;

    .line 7
    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 8
    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/Sx;

    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    invoke-static {v4, v3}, Lcom/android/tools/r8/internal/Mv;->b(Lcom/android/tools/r8/internal/Sx;Ljava/lang/Object;)I

    move-result v3

    add-int/2addr v2, v3

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 11
    :cond_0
    iget-object v0, v0, Lcom/android/tools/r8/internal/Mv;->a:Lcom/android/tools/r8/internal/tk0;

    .line 12
    iget-object v1, v0, Lcom/android/tools/r8/internal/tk0;->d:Ljava/util/Map;

    .line 13
    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_1

    sget-object v0, Lcom/android/tools/r8/internal/zk0;->b:Lcom/android/tools/r8/internal/xk0;

    goto :goto_1

    :cond_1
    iget-object v0, v0, Lcom/android/tools/r8/internal/tk0;->d:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    .line 14
    :goto_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 15
    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/Sx;

    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    invoke-static {v3, v1}, Lcom/android/tools/r8/internal/Mv;->b(Lcom/android/tools/r8/internal/Sx;Ljava/lang/Object;)I

    move-result v1

    add-int/2addr v2, v1

    goto :goto_2

    :cond_2
    return v2
.end method
