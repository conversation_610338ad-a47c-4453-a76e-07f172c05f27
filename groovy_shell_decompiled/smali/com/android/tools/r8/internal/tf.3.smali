.class public final Lcom/android/tools/r8/internal/tf;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/graph/I2;

.field public final b:Lcom/android/tools/r8/graph/J2;

.field public final c:Lcom/android/tools/r8/graph/J2;

.field public final d:Lcom/android/tools/r8/graph/x2;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/B1;)V
    .locals 4

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "$$changed"

    .line 2
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/B1;->c(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/tf;->a:Lcom/android/tools/r8/graph/I2;

    const-string v0, "Landroidx/compose/runtime/Composable;"

    .line 4
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/B1;->e(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/tf;->b:Lcom/android/tools/r8/graph/J2;

    const-string v0, "Landroidx/compose/runtime/Composer;"

    .line 5
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/B1;->e(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/tf;->c:Lcom/android/tools/r8/graph/J2;

    const-string v0, "Landroidx/compose/runtime/RecomposeScopeImplKt;"

    .line 9
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/B1;->e(Ljava/lang/String;)Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->C1:Lcom/android/tools/r8/graph/J2;

    const/4 v2, 0x1

    new-array v2, v2, [Lcom/android/tools/r8/graph/J2;

    const/4 v3, 0x0

    aput-object v1, v2, v3

    .line 10
    invoke-virtual {p1, v1, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    const-string v2, "updateChangedFlags"

    .line 11
    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/tf;->d:Lcom/android/tools/r8/graph/x2;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 12
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 13
    iput-object p1, p0, Lcom/android/tools/r8/internal/tf;->a:Lcom/android/tools/r8/graph/I2;

    .line 14
    iput-object p2, p0, Lcom/android/tools/r8/internal/tf;->b:Lcom/android/tools/r8/graph/J2;

    .line 15
    iput-object p3, p0, Lcom/android/tools/r8/internal/tf;->c:Lcom/android/tools/r8/graph/J2;

    .line 16
    iput-object p4, p0, Lcom/android/tools/r8/internal/tf;->d:Lcom/android/tools/r8/graph/x2;

    return-void
.end method
