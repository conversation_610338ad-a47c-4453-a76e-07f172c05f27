.class public final synthetic Lcom/android/tools/r8/internal/P50$$ExternalSyntheticLambda10;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/BP;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/AP;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/BP;Lcom/android/tools/r8/internal/AP;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/P50$$ExternalSyntheticLambda10;->f$0:Lcom/android/tools/r8/internal/BP;

    iput-object p2, p0, Lcom/android/tools/r8/internal/P50$$ExternalSyntheticLambda10;->f$1:Lcom/android/tools/r8/internal/AP;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/P50$$ExternalSyntheticLambda10;->f$0:Lcom/android/tools/r8/internal/BP;

    iget-object v1, p0, Lcom/android/tools/r8/internal/P50$$ExternalSyntheticLambda10;->f$1:Lcom/android/tools/r8/internal/AP;

    check-cast p1, Lcom/android/tools/r8/internal/J50;

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/P50;->b(Lcom/android/tools/r8/internal/BP;Lcom/android/tools/r8/internal/AP;Lcom/android/tools/r8/internal/J50;)V

    return-void
.end method
