.class public final Lcom/android/tools/r8/internal/v70;
.super Lcom/android/tools/r8/internal/Ox;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public e:I

.field public f:I

.field public g:I

.field public h:I

.field public i:Lcom/android/tools/r8/internal/N70;

.field public j:I

.field public k:Ljava/util/List;

.field public l:Lcom/android/tools/r8/internal/N70;

.field public m:I

.field public n:Ljava/util/List;

.field public o:Ljava/util/List;

.field public p:Lcom/android/tools/r8/internal/a80;

.field public q:I

.field public r:I

.field public s:Ljava/util/List;


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Ox;-><init>()V

    const/16 v0, 0x206

    .line 267
    iput v0, p0, Lcom/android/tools/r8/internal/v70;->f:I

    const/16 v0, 0x806

    .line 363
    iput v0, p0, Lcom/android/tools/r8/internal/v70;->g:I

    .line 364
    sget-object v0, Lcom/android/tools/r8/internal/N70;->u:Lcom/android/tools/r8/internal/N70;

    .line 365
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->i:Lcom/android/tools/r8/internal/N70;

    .line 458
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/v70;->k:Ljava/util/List;

    .line 582
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->l:Lcom/android/tools/r8/internal/N70;

    .line 675
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->n:Ljava/util/List;

    .line 799
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->o:Ljava/util/List;

    .line 800
    sget-object v0, Lcom/android/tools/r8/internal/a80;->m:Lcom/android/tools/r8/internal/a80;

    .line 801
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->p:Lcom/android/tools/r8/internal/a80;

    .line 973
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->s:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/N0;
    .locals 2

    .line 115
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/v70;->c()Lcom/android/tools/r8/internal/w70;

    move-result-object v0

    .line 116
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/w70;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 117
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/hs0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/hs0;-><init>()V

    .line 118
    throw v0
.end method

.method public final bridge synthetic a(Lcom/android/tools/r8/internal/Vx;)Lcom/android/tools/r8/internal/Nx;
    .locals 0

    .line 114
    check-cast p1, Lcom/android/tools/r8/internal/w70;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/v70;->a(Lcom/android/tools/r8/internal/w70;)Lcom/android/tools/r8/internal/v70;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/Nx;
    .locals 2

    const/4 v0, 0x0

    .line 119
    :try_start_0
    sget-object v1, Lcom/android/tools/r8/internal/w70;->w:Lcom/android/tools/r8/internal/u70;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 120
    new-instance v1, Lcom/android/tools/r8/internal/w70;

    invoke-direct {v1, p1, p2}, Lcom/android/tools/r8/internal/w70;-><init>(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)V
    :try_end_0
    .catch Lcom/android/tools/r8/internal/kI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 121
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/v70;->a(Lcom/android/tools/r8/internal/w70;)Lcom/android/tools/r8/internal/v70;

    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 122
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 123
    check-cast p2, Lcom/android/tools/r8/internal/w70;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 124
    :try_start_2
    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_0

    .line 127
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/v70;->a(Lcom/android/tools/r8/internal/w70;)Lcom/android/tools/r8/internal/v70;

    .line 129
    :cond_0
    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/w70;)Lcom/android/tools/r8/internal/v70;
    .locals 6

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/w70;->v:Lcom/android/tools/r8/internal/w70;

    if-ne p1, v0, :cond_0

    return-object p0

    .line 2
    :cond_0
    iget v0, p1, Lcom/android/tools/r8/internal/w70;->d:I

    and-int/lit8 v1, v0, 0x1

    const/4 v2, 0x1

    if-ne v1, v2, :cond_1

    .line 3
    iget v1, p1, Lcom/android/tools/r8/internal/w70;->e:I

    .line 4
    iget v3, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v2, v3

    iput v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 5
    iput v1, p0, Lcom/android/tools/r8/internal/v70;->f:I

    :cond_1
    and-int/lit8 v1, v0, 0x2

    const/4 v2, 0x2

    if-ne v1, v2, :cond_2

    .line 6
    iget v1, p1, Lcom/android/tools/r8/internal/w70;->f:I

    .line 7
    iget v3, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v2, v3

    iput v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 8
    iput v1, p0, Lcom/android/tools/r8/internal/v70;->g:I

    :cond_2
    and-int/lit8 v1, v0, 0x4

    const/4 v2, 0x4

    if-ne v1, v2, :cond_3

    .line 9
    iget v1, p1, Lcom/android/tools/r8/internal/w70;->g:I

    .line 10
    iget v3, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v2, v3

    iput v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 11
    iput v1, p0, Lcom/android/tools/r8/internal/v70;->h:I

    :cond_3
    const/16 v1, 0x8

    and-int/2addr v0, v1

    if-ne v0, v1, :cond_5

    .line 12
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->h:Lcom/android/tools/r8/internal/N70;

    .line 13
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/2addr v2, v1

    if-ne v2, v1, :cond_4

    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->i:Lcom/android/tools/r8/internal/N70;

    .line 14
    sget-object v3, Lcom/android/tools/r8/internal/N70;->u:Lcom/android/tools/r8/internal/N70;

    if-eq v2, v3, :cond_4

    .line 15
    invoke-static {v2}, Lcom/android/tools/r8/internal/N70;->a(Lcom/android/tools/r8/internal/N70;)Lcom/android/tools/r8/internal/M70;

    move-result-object v2

    invoke-virtual {v2, v0}, Lcom/android/tools/r8/internal/M70;->a(Lcom/android/tools/r8/internal/N70;)Lcom/android/tools/r8/internal/M70;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/M70;->d()Lcom/android/tools/r8/internal/N70;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->i:Lcom/android/tools/r8/internal/N70;

    goto :goto_0

    .line 17
    :cond_4
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->i:Lcom/android/tools/r8/internal/N70;

    .line 20
    :goto_0
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 21
    :cond_5
    iget v0, p1, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v1, 0x10

    and-int/2addr v0, v1

    if-ne v0, v1, :cond_6

    .line 22
    iget v0, p1, Lcom/android/tools/r8/internal/w70;->i:I

    .line 23
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v1, v2

    iput v1, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 24
    iput v0, p0, Lcom/android/tools/r8/internal/v70;->j:I

    .line 25
    :cond_6
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    .line 26
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    const/16 v1, 0x20

    if-nez v0, :cond_9

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/internal/v70;->k:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_7

    .line 28
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    .line 29
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->k:Ljava/util/List;

    .line 30
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/lit8 v0, v0, -0x21

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    goto :goto_1

    .line 31
    :cond_7
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/2addr v0, v1

    if-eq v0, v1, :cond_8

    .line 32
    new-instance v0, Ljava/util/ArrayList;

    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->k:Ljava/util/List;

    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->k:Ljava/util/List;

    .line 33
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 34
    :cond_8
    iget-object v0, p0, Lcom/android/tools/r8/internal/v70;->k:Ljava/util/List;

    .line 35
    iget-object v2, p1, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    .line 36
    invoke-interface {v0, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 37
    :cond_9
    :goto_1
    iget v0, p1, Lcom/android/tools/r8/internal/w70;->d:I

    and-int/2addr v0, v1

    const/16 v2, 0x40

    if-ne v0, v1, :cond_b

    .line 38
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->k:Lcom/android/tools/r8/internal/N70;

    .line 39
    iget v1, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_a

    iget-object v1, p0, Lcom/android/tools/r8/internal/v70;->l:Lcom/android/tools/r8/internal/N70;

    .line 40
    sget-object v3, Lcom/android/tools/r8/internal/N70;->u:Lcom/android/tools/r8/internal/N70;

    if-eq v1, v3, :cond_a

    .line 41
    invoke-static {v1}, Lcom/android/tools/r8/internal/N70;->a(Lcom/android/tools/r8/internal/N70;)Lcom/android/tools/r8/internal/M70;

    move-result-object v1

    invoke-virtual {v1, v0}, Lcom/android/tools/r8/internal/M70;->a(Lcom/android/tools/r8/internal/N70;)Lcom/android/tools/r8/internal/M70;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/M70;->d()Lcom/android/tools/r8/internal/N70;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->l:Lcom/android/tools/r8/internal/N70;

    goto :goto_2

    .line 43
    :cond_a
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->l:Lcom/android/tools/r8/internal/N70;

    .line 46
    :goto_2
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v0, v2

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 47
    :cond_b
    iget v0, p1, Lcom/android/tools/r8/internal/w70;->d:I

    and-int/2addr v0, v2

    const/16 v1, 0x80

    if-ne v0, v2, :cond_c

    .line 48
    iget v0, p1, Lcom/android/tools/r8/internal/w70;->l:I

    .line 49
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v2, v1

    iput v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 50
    iput v0, p0, Lcom/android/tools/r8/internal/v70;->m:I

    .line 51
    :cond_c
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    .line 52
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    const/16 v2, 0x100

    if-nez v0, :cond_f

    .line 53
    iget-object v0, p0, Lcom/android/tools/r8/internal/v70;->n:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_d

    .line 54
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    .line 55
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->n:Ljava/util/List;

    .line 56
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/lit16 v0, v0, -0x101

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    goto :goto_3

    .line 57
    :cond_d
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/2addr v0, v2

    if-eq v0, v2, :cond_e

    .line 58
    new-instance v0, Ljava/util/ArrayList;

    iget-object v3, p0, Lcom/android/tools/r8/internal/v70;->n:Ljava/util/List;

    invoke-direct {v0, v3}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->n:Ljava/util/List;

    .line 59
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v0, v2

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 60
    :cond_e
    iget-object v0, p0, Lcom/android/tools/r8/internal/v70;->n:Ljava/util/List;

    .line 61
    iget-object v3, p1, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    .line 62
    invoke-interface {v0, v3}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 63
    :cond_f
    :goto_3
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    .line 64
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    const/16 v3, 0x200

    if-nez v0, :cond_12

    .line 65
    iget-object v0, p0, Lcom/android/tools/r8/internal/v70;->o:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_10

    .line 66
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    .line 67
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->o:Ljava/util/List;

    .line 68
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/lit16 v0, v0, -0x201

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    goto :goto_4

    .line 69
    :cond_10
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/2addr v0, v3

    if-eq v0, v3, :cond_11

    .line 70
    new-instance v0, Ljava/util/ArrayList;

    iget-object v4, p0, Lcom/android/tools/r8/internal/v70;->o:Ljava/util/List;

    invoke-direct {v0, v4}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->o:Ljava/util/List;

    .line 71
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v0, v3

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 72
    :cond_11
    iget-object v0, p0, Lcom/android/tools/r8/internal/v70;->o:Ljava/util/List;

    .line 73
    iget-object v4, p1, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    .line 74
    invoke-interface {v0, v4}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 75
    :cond_12
    :goto_4
    iget v0, p1, Lcom/android/tools/r8/internal/w70;->d:I

    and-int/2addr v0, v1

    if-ne v0, v1, :cond_14

    .line 76
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->p:Lcom/android/tools/r8/internal/a80;

    .line 77
    iget v1, p0, Lcom/android/tools/r8/internal/v70;->e:I

    const/16 v4, 0x400

    and-int/2addr v1, v4

    if-ne v1, v4, :cond_13

    iget-object v1, p0, Lcom/android/tools/r8/internal/v70;->p:Lcom/android/tools/r8/internal/a80;

    .line 78
    sget-object v5, Lcom/android/tools/r8/internal/a80;->m:Lcom/android/tools/r8/internal/a80;

    if-eq v1, v5, :cond_13

    .line 79
    new-instance v5, Lcom/android/tools/r8/internal/Z70;

    invoke-direct {v5}, Lcom/android/tools/r8/internal/Z70;-><init>()V

    .line 80
    invoke-virtual {v5, v1}, Lcom/android/tools/r8/internal/Z70;->a(Lcom/android/tools/r8/internal/a80;)Lcom/android/tools/r8/internal/Z70;

    move-result-object v1

    .line 81
    invoke-virtual {v1, v0}, Lcom/android/tools/r8/internal/Z70;->a(Lcom/android/tools/r8/internal/a80;)Lcom/android/tools/r8/internal/Z70;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Z70;->c()Lcom/android/tools/r8/internal/a80;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->p:Lcom/android/tools/r8/internal/a80;

    goto :goto_5

    .line 83
    :cond_13
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->p:Lcom/android/tools/r8/internal/a80;

    .line 86
    :goto_5
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v0, v4

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 87
    :cond_14
    iget v0, p1, Lcom/android/tools/r8/internal/w70;->d:I

    and-int/lit16 v1, v0, 0x100

    if-ne v1, v2, :cond_15

    .line 88
    iget v1, p1, Lcom/android/tools/r8/internal/w70;->q:I

    .line 89
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/lit16 v2, v2, 0x800

    iput v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 90
    iput v1, p0, Lcom/android/tools/r8/internal/v70;->q:I

    :cond_15
    and-int/2addr v0, v3

    if-ne v0, v3, :cond_16

    .line 91
    iget v0, p1, Lcom/android/tools/r8/internal/w70;->r:I

    .line 92
    iget v1, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/lit16 v1, v1, 0x1000

    iput v1, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 93
    iput v0, p0, Lcom/android/tools/r8/internal/v70;->r:I

    .line 94
    :cond_16
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 95
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_19

    .line 96
    iget-object v0, p0, Lcom/android/tools/r8/internal/v70;->s:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_17

    .line 97
    iget-object v0, p1, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 98
    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->s:Ljava/util/List;

    .line 99
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/lit16 v0, v0, -0x2001

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    goto :goto_6

    .line 100
    :cond_17
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    const/16 v1, 0x2000

    and-int/2addr v0, v1

    if-eq v0, v1, :cond_18

    .line 101
    new-instance v0, Ljava/util/ArrayList;

    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->s:Ljava/util/List;

    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/v70;->s:Ljava/util/List;

    .line 102
    iget v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    or-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 103
    :cond_18
    iget-object v0, p0, Lcom/android/tools/r8/internal/v70;->s:Ljava/util/List;

    .line 104
    iget-object v1, p1, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 105
    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 109
    :cond_19
    :goto_6
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Ox;->a(Lcom/android/tools/r8/internal/Qx;)V

    .line 110
    iget-object v0, p0, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    .line 111
    iget-object p1, p1, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    .line 112
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Y7;->a(Lcom/android/tools/r8/internal/Y7;)Lcom/android/tools/r8/internal/Y7;

    move-result-object p1

    .line 113
    iput-object p1, p0, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    return-object p0
.end method

.method public final b()Lcom/android/tools/r8/internal/Vx;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/w70;->v:Lcom/android/tools/r8/internal/w70;

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/w70;
    .locals 5

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/w70;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/w70;-><init>(Lcom/android/tools/r8/internal/v70;)V

    .line 2
    iget v1, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/lit8 v2, v1, 0x1

    const/4 v3, 0x1

    if-ne v2, v3, :cond_0

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    .line 7
    :goto_0
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->f:I

    .line 8
    iput v2, v0, Lcom/android/tools/r8/internal/w70;->e:I

    and-int/lit8 v2, v1, 0x2

    const/4 v4, 0x2

    if-ne v2, v4, :cond_1

    or-int/lit8 v3, v3, 0x2

    .line 9
    :cond_1
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->g:I

    .line 10
    iput v2, v0, Lcom/android/tools/r8/internal/w70;->f:I

    and-int/lit8 v2, v1, 0x4

    const/4 v4, 0x4

    if-ne v2, v4, :cond_2

    or-int/lit8 v3, v3, 0x4

    .line 11
    :cond_2
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->h:I

    .line 12
    iput v2, v0, Lcom/android/tools/r8/internal/w70;->g:I

    and-int/lit8 v2, v1, 0x8

    const/16 v4, 0x8

    if-ne v2, v4, :cond_3

    or-int/lit8 v3, v3, 0x8

    .line 13
    :cond_3
    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->i:Lcom/android/tools/r8/internal/N70;

    .line 14
    iput-object v2, v0, Lcom/android/tools/r8/internal/w70;->h:Lcom/android/tools/r8/internal/N70;

    and-int/lit8 v2, v1, 0x10

    const/16 v4, 0x10

    if-ne v2, v4, :cond_4

    or-int/lit8 v3, v3, 0x10

    .line 15
    :cond_4
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->j:I

    .line 16
    iput v2, v0, Lcom/android/tools/r8/internal/w70;->i:I

    and-int/lit8 v2, v1, 0x20

    const/16 v4, 0x20

    if-ne v2, v4, :cond_5

    .line 17
    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->k:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, p0, Lcom/android/tools/r8/internal/v70;->k:Ljava/util/List;

    .line 18
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/lit8 v2, v2, -0x21

    iput v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 20
    :cond_5
    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->k:Ljava/util/List;

    .line 21
    iput-object v2, v0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    and-int/lit8 v2, v1, 0x40

    const/16 v4, 0x40

    if-ne v2, v4, :cond_6

    or-int/lit8 v3, v3, 0x20

    .line 22
    :cond_6
    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->l:Lcom/android/tools/r8/internal/N70;

    .line 23
    iput-object v2, v0, Lcom/android/tools/r8/internal/w70;->k:Lcom/android/tools/r8/internal/N70;

    and-int/lit16 v2, v1, 0x80

    const/16 v4, 0x80

    if-ne v2, v4, :cond_7

    or-int/lit8 v3, v3, 0x40

    .line 24
    :cond_7
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->m:I

    .line 25
    iput v2, v0, Lcom/android/tools/r8/internal/w70;->l:I

    .line 26
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    const/16 v4, 0x100

    and-int/2addr v2, v4

    if-ne v2, v4, :cond_8

    .line 27
    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->n:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, p0, Lcom/android/tools/r8/internal/v70;->n:Ljava/util/List;

    .line 28
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/lit16 v2, v2, -0x101

    iput v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 30
    :cond_8
    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->n:Ljava/util/List;

    .line 31
    iput-object v2, v0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    .line 32
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    const/16 v4, 0x200

    and-int/2addr v2, v4

    if-ne v2, v4, :cond_9

    .line 33
    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->o:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, p0, Lcom/android/tools/r8/internal/v70;->o:Ljava/util/List;

    .line 34
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/lit16 v2, v2, -0x201

    iput v2, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 36
    :cond_9
    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->o:Ljava/util/List;

    .line 37
    iput-object v2, v0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    and-int/lit16 v2, v1, 0x400

    const/16 v4, 0x400

    if-ne v2, v4, :cond_a

    or-int/lit16 v3, v3, 0x80

    .line 38
    :cond_a
    iget-object v2, p0, Lcom/android/tools/r8/internal/v70;->p:Lcom/android/tools/r8/internal/a80;

    .line 39
    iput-object v2, v0, Lcom/android/tools/r8/internal/w70;->p:Lcom/android/tools/r8/internal/a80;

    and-int/lit16 v2, v1, 0x800

    const/16 v4, 0x800

    if-ne v2, v4, :cond_b

    or-int/lit16 v3, v3, 0x100

    .line 40
    :cond_b
    iget v2, p0, Lcom/android/tools/r8/internal/v70;->q:I

    .line 41
    iput v2, v0, Lcom/android/tools/r8/internal/w70;->q:I

    const/16 v2, 0x1000

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_c

    or-int/lit16 v3, v3, 0x200

    .line 42
    :cond_c
    iget v1, p0, Lcom/android/tools/r8/internal/v70;->r:I

    .line 43
    iput v1, v0, Lcom/android/tools/r8/internal/w70;->r:I

    .line 44
    iget v1, p0, Lcom/android/tools/r8/internal/v70;->e:I

    const/16 v2, 0x2000

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_d

    .line 45
    iget-object v1, p0, Lcom/android/tools/r8/internal/v70;->s:Ljava/util/List;

    invoke-static {v1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/v70;->s:Ljava/util/List;

    .line 46
    iget v1, p0, Lcom/android/tools/r8/internal/v70;->e:I

    and-int/lit16 v1, v1, -0x2001

    iput v1, p0, Lcom/android/tools/r8/internal/v70;->e:I

    .line 48
    :cond_d
    iget-object v1, p0, Lcom/android/tools/r8/internal/v70;->s:Ljava/util/List;

    .line 49
    iput-object v1, v0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 50
    iput v3, v0, Lcom/android/tools/r8/internal/w70;->d:I

    return-object v0
.end method

.method public final clone()Ljava/lang/Object;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/v70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/v70;-><init>()V

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/v70;->c()Lcom/android/tools/r8/internal/w70;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/v70;->a(Lcom/android/tools/r8/internal/w70;)Lcom/android/tools/r8/internal/v70;

    move-result-object v0

    return-object v0
.end method
