.class public final Lcom/android/tools/r8/internal/W20;
.super Lcom/android/tools/r8/internal/b30;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic i:Z


# instance fields
.field public final c:Lcom/android/tools/r8/graph/x2;

.field public final d:Lcom/android/tools/r8/internal/JI;

.field public final e:Z

.field public final f:Lcom/android/tools/r8/graph/F2;

.field public final g:Z

.field public final h:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/h30;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/W20;->i:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Z[Lcom/android/tools/r8/internal/It0;Lcom/android/tools/r8/graph/F2;Z)V
    .locals 3

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/b30;-><init>()V

    .line 2
    array-length v0, p4

    iget-object v1, p1, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    array-length v1, v1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    move v0, v2

    :goto_0
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/W20;->g:Z

    .line 3
    sget-boolean v1, Lcom/android/tools/r8/internal/W20;->i:Z

    if-nez v1, :cond_2

    if-eqz v0, :cond_2

    aget-object p4, p4, v2

    invoke-virtual {p4}, Lcom/android/tools/r8/internal/It0;->a()Z

    move-result p4

    if-eqz p4, :cond_1

    goto :goto_1

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 4
    :cond_2
    :goto_1
    iput-object p1, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    .line 5
    iput-object p2, p0, Lcom/android/tools/r8/internal/W20;->d:Lcom/android/tools/r8/internal/JI;

    .line 6
    iput-boolean p3, p0, Lcom/android/tools/r8/internal/W20;->e:Z

    .line 7
    iput-object p5, p0, Lcom/android/tools/r8/internal/W20;->f:Lcom/android/tools/r8/graph/F2;

    .line 8
    iput-boolean p6, p0, Lcom/android/tools/r8/internal/W20;->h:Z

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Vz;Lcom/android/tools/r8/internal/Y20;I)I
    .locals 6

    .line 2
    new-instance v4, Ljava/util/ArrayList;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/W20;->e()I

    move-result v0

    invoke-direct {v4, v0}, Ljava/util/ArrayList;-><init>(I)V

    const/4 v0, 0x0

    .line 3
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/W20;->e()I

    move-result v1

    if-ge v0, v1, :cond_3

    .line 4
    iget-object v1, p2, Lcom/android/tools/r8/internal/Y20;->c:Ljava/util/ArrayList;

    add-int/lit8 v2, p3, 0x1

    invoke-virtual {v1, p3}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Ljava/lang/Integer;

    invoke-virtual {p3}, Ljava/lang/Integer;->intValue()I

    move-result p3

    const/4 v1, -0x1

    if-ne p3, v1, :cond_0

    .line 5
    iget-object p3, p2, Lcom/android/tools/r8/internal/Y20;->b:Ljava/util/ArrayList;

    invoke-virtual {p3}, Ljava/util/ArrayList;->size()I

    move-result p3

    .line 6
    :cond_0
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/W20;->g:Z

    if-eqz v1, :cond_2

    if-nez v0, :cond_1

    .line 8
    sget-object v1, Lcom/android/tools/r8/internal/Kt0;->b:Lcom/android/tools/r8/internal/Kt0;

    goto :goto_1

    .line 9
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    add-int/lit8 v3, v0, -0x1

    aget-object v1, v1, v3

    invoke-static {v1}, Lcom/android/tools/r8/internal/Kt0;->a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/Kt0;

    move-result-object v1

    goto :goto_1

    .line 11
    :cond_2
    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    aget-object v1, v1, v0

    invoke-static {v1}, Lcom/android/tools/r8/internal/Kt0;->a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/Kt0;

    move-result-object v1

    .line 12
    :goto_1
    invoke-virtual {p1, p3, v1}, Lcom/android/tools/r8/internal/Vz;->b(ILcom/android/tools/r8/internal/Kt0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object p3

    invoke-virtual {v4, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    add-int/lit8 v0, v0, 0x1

    move p3, v2

    goto :goto_0

    :cond_3
    const/4 v0, 0x0

    .line 15
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/W20;->e:Z

    if-eqz v1, :cond_4

    .line 16
    iget-object v0, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/F2;->e:Lcom/android/tools/r8/graph/J2;

    .line 17
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    iget-object v2, p1, Lcom/android/tools/r8/internal/Vz;->p:Lcom/android/tools/r8/graph/y;

    invoke-static {v0, v1, v2}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    .line 18
    iget-object p2, p2, Lcom/android/tools/r8/internal/Y20;->b:Ljava/util/ArrayList;

    .line 19
    invoke-virtual {p2}, Ljava/util/ArrayList;->size()I

    move-result p2

    const/4 v1, 0x2

    .line 20
    invoke-virtual {p1, p2, v1, v0}, Lcom/android/tools/r8/internal/Vz;->a(IILcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object p2

    move-object v3, p2

    goto :goto_2

    :cond_4
    move-object v3, v0

    .line 23
    :goto_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/W20;->d:Lcom/android/tools/r8/internal/JI;

    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    iget-object v2, p0, Lcom/android/tools/r8/internal/W20;->f:Lcom/android/tools/r8/graph/F2;

    iget-boolean v5, p0, Lcom/android/tools/r8/internal/W20;->h:Z

    .line 24
    invoke-static/range {v0 .. v5}, Lcom/android/tools/r8/internal/mI;->a(Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/graph/n1;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/internal/vt0;Ljava/util/List;Z)Lcom/android/tools/r8/internal/mI;

    move-result-object p2

    .line 25
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/Vz;->a(Lcom/android/tools/r8/internal/rD;)V

    return p3
.end method

.method public final a()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/x2;->l0()Ljava/lang/String;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "; method: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/internal/Fy;)Z
    .locals 2

    .line 26
    iget-object v0, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 27
    invoke-static {}, Lcom/android/tools/r8/internal/Fy;->g()Lcom/android/tools/r8/internal/Fy;

    move-result-object v1

    .line 28
    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/Fy;->b(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    .line 29
    iget-object v0, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/x2;->a(Lcom/android/tools/r8/graph/x2;)Z

    move-result p1

    xor-int/lit8 p1, p1, 0x1

    return p1
.end method

.method public final b()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/W20;->d:Lcom/android/tools/r8/internal/JI;

    invoke-virtual {v0}, Ljava/lang/Enum;->name()Ljava/lang/String;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "INVOKE"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "-"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/a30;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/a30;->g:Lcom/android/tools/r8/internal/a30;

    return-object v0
.end method

.method public final compareTo(Ljava/lang/Object;)I
    .locals 3

    .line 1
    check-cast p1, Lcom/android/tools/r8/internal/b30;

    .line 2
    instance-of v0, p1, Lcom/android/tools/r8/internal/W20;

    if-nez v0, :cond_0

    .line 3
    sget-object v0, Lcom/android/tools/r8/internal/a30;->g:Lcom/android/tools/r8/internal/a30;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/b30;->c()Lcom/android/tools/r8/internal/a30;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/Enum;->compareTo(Ljava/lang/Enum;)I

    move-result p1

    goto :goto_2

    .line 5
    :cond_0
    move-object v0, p1

    check-cast v0, Lcom/android/tools/r8/internal/W20;

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    iget-object v2, v0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    invoke-interface {v1, v2}, Lcom/android/tools/r8/internal/ho0;->a(Lcom/android/tools/r8/internal/ho0;)I

    move-result v1

    if-eqz v1, :cond_1

    :goto_0
    move p1, v1

    goto :goto_2

    .line 10
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->d:Lcom/android/tools/r8/internal/JI;

    iget-object v2, v0, Lcom/android/tools/r8/internal/W20;->d:Lcom/android/tools/r8/internal/JI;

    invoke-virtual {v1, v2}, Ljava/lang/Enum;->compareTo(Ljava/lang/Enum;)I

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_0

    .line 14
    :cond_2
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/W20;->e:Z

    iget-boolean v2, v0, Lcom/android/tools/r8/internal/W20;->e:Z

    invoke-static {v1, v2}, Ljava/lang/Boolean;->compare(ZZ)I

    move-result v1

    if-eqz v1, :cond_3

    goto :goto_0

    .line 18
    :cond_3
    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->f:Lcom/android/tools/r8/graph/F2;

    if-eqz v1, :cond_4

    .line 19
    iget-object v2, v0, Lcom/android/tools/r8/internal/W20;->f:Lcom/android/tools/r8/graph/F2;

    invoke-interface {v1, v2}, Lcom/android/tools/r8/internal/ho0;->a(Lcom/android/tools/r8/internal/ho0;)I

    move-result v1

    if-eqz v1, :cond_4

    goto :goto_0

    .line 24
    :cond_4
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/W20;->h:Z

    iget-boolean v0, v0, Lcom/android/tools/r8/internal/W20;->h:Z

    invoke-static {v1, v0}, Ljava/lang/Boolean;->compare(ZZ)I

    move-result v0

    if-eqz v0, :cond_5

    move p1, v0

    goto :goto_2

    .line 28
    :cond_5
    sget-boolean v0, Lcom/android/tools/r8/internal/W20;->i:Z

    if-nez v0, :cond_7

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/W20;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_6

    goto :goto_1

    :cond_6
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_7
    :goto_1
    const/4 p1, 0x0

    :goto_2
    return p1
.end method

.method public final d()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/W20;->e:Z

    return v0
.end method

.method public final e()I
    .locals 2

    .line 1
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/W20;->g:Z

    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    array-length v1, v1

    add-int/2addr v0, v1

    return v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/W20;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/W20;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    iget-object v3, p1, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/graph/x2;->a(Lcom/android/tools/r8/graph/x2;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->d:Lcom/android/tools/r8/internal/JI;

    iget-object v3, p1, Lcom/android/tools/r8/internal/W20;->d:Lcom/android/tools/r8/internal/JI;

    if-ne v1, v3, :cond_2

    iget-boolean v1, p0, Lcom/android/tools/r8/internal/W20;->e:Z

    iget-boolean v3, p1, Lcom/android/tools/r8/internal/W20;->e:Z

    if-ne v1, v3, :cond_2

    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->f:Lcom/android/tools/r8/graph/F2;

    iget-object v3, p1, Lcom/android/tools/r8/internal/W20;->f:Lcom/android/tools/r8/graph/F2;

    .line 8
    invoke-static {v1, v3}, Lcom/android/tools/r8/graph/F2;->a(Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/F2;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-boolean v1, p0, Lcom/android/tools/r8/internal/W20;->h:Z

    iget-boolean p1, p1, Lcom/android/tools/r8/internal/W20;->h:Z

    if-ne v1, p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public final hashCode()I
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/W20;->c:Lcom/android/tools/r8/graph/x2;

    iget-object v1, p0, Lcom/android/tools/r8/internal/W20;->d:Lcom/android/tools/r8/internal/JI;

    iget-boolean v2, p0, Lcom/android/tools/r8/internal/W20;->e:Z

    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    iget-object v3, p0, Lcom/android/tools/r8/internal/W20;->f:Lcom/android/tools/r8/graph/F2;

    iget-boolean v4, p0, Lcom/android/tools/r8/internal/W20;->h:Z

    invoke-static {v4}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v4

    const/4 v5, 0x6

    new-array v5, v5, [Ljava/lang/Object;

    sget-object v6, Lcom/android/tools/r8/internal/a30;->g:Lcom/android/tools/r8/internal/a30;

    const/4 v7, 0x0

    aput-object v6, v5, v7

    const/4 v6, 0x1

    aput-object v0, v5, v6

    const/4 v0, 0x2

    aput-object v1, v5, v0

    const/4 v0, 0x3

    aput-object v2, v5, v0

    const/4 v0, 0x4

    aput-object v3, v5, v0

    const/4 v0, 0x5

    aput-object v4, v5, v0

    invoke-static {v5}, Ljava/util/Objects;->hash([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
