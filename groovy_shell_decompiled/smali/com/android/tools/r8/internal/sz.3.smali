.class public final Lcom/android/tools/r8/internal/sz;
.super Lcom/android/tools/r8/internal/Tc;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic p:Z = true


# instance fields
.field public final n:Ljava/util/IdentityHashMap;

.field public final o:Lcom/android/tools/r8/internal/xz;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/xz;Ljava/util/IdentityHashMap;Lcom/android/tools/r8/internal/c6;Lcom/android/tools/r8/internal/a6;Lcom/android/tools/r8/internal/c6;)V
    .locals 6

    .line 1
    iget-object v3, p5, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    .line 2
    iget-object v4, p2, Lcom/android/tools/r8/internal/xz;->a:Lcom/android/tools/r8/internal/d6;

    move-object v0, p0

    move-object v1, p1

    move-object v2, p4

    move-object v5, p6

    .line 3
    invoke-direct/range {v0 .. v5}, Lcom/android/tools/r8/internal/Tc;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/d6;Ljava/util/Map;Lcom/android/tools/r8/internal/Z5;Lcom/android/tools/r8/internal/d6;)V

    .line 9
    iput-object p3, p0, Lcom/android/tools/r8/internal/sz;->n:Ljava/util/IdentityHashMap;

    .line 10
    iput-object p2, p0, Lcom/android/tools/r8/internal/sz;->o:Lcom/android/tools/r8/internal/xz;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/proto/j;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/proto/j;
    .locals 2

    .line 12
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/x2;->z0()I

    move-result v0

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/x2;->z0()I

    move-result v1

    if-le v0, v1, :cond_3

    .line 13
    sget-boolean v0, Lcom/android/tools/r8/internal/sz;->p:Z

    if-nez v0, :cond_1

    .line 14
    iget-object v0, p0, Lcom/android/tools/r8/internal/TY;->c:Lcom/android/tools/r8/graph/B1;

    .line 15
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 16
    iget-object v1, p2, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->d1:Lcom/android/tools/r8/graph/I2;

    if-ne v1, v0, :cond_0

    goto :goto_0

    .line 17
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 20
    :cond_1
    :goto_0
    invoke-static {p2, p3}, Lcom/android/tools/r8/internal/Tu;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)Ljava/util/List;

    move-result-object p3

    .line 21
    sget-object v0, Lcom/android/tools/r8/graph/proto/j;->d:Lcom/android/tools/r8/graph/proto/j;

    const/4 v0, 0x0

    .line 22
    sget-object v1, Lcom/android/tools/r8/graph/proto/c;->f:Lcom/android/tools/r8/graph/proto/c;

    .line 23
    invoke-static {p3, v0, v1}, Lcom/android/tools/r8/graph/proto/j;->a(Ljava/util/List;Lcom/android/tools/r8/graph/proto/k;Lcom/android/tools/r8/graph/proto/c;)Lcom/android/tools/r8/graph/proto/j;

    move-result-object p3

    .line 24
    iget-object v0, p0, Lcom/android/tools/r8/internal/sz;->n:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p2}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/internal/Mu;

    if-eqz p2, :cond_2

    .line 25
    iget-object v0, p3, Lcom/android/tools/r8/graph/proto/j;->a:Ljava/util/List;

    const/4 v1, 0x0

    .line 26
    invoke-interface {v0, v1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 28
    :cond_2
    invoke-virtual {p1, p3}, Lcom/android/tools/r8/graph/proto/j;->a(Lcom/android/tools/r8/graph/proto/j;)Lcom/android/tools/r8/graph/proto/j;

    move-result-object p1

    return-object p1

    .line 30
    :cond_3
    sget-boolean v0, Lcom/android/tools/r8/internal/sz;->p:Z

    if-nez v0, :cond_5

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/x2;->z0()I

    move-result p3

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/x2;->z0()I

    move-result p2

    if-ne p3, p2, :cond_4

    goto :goto_1

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_5
    :goto_1
    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Ey;)Lcom/android/tools/r8/internal/cV;
    .locals 0

    if-ne p0, p4, :cond_0

    .line 1
    sget-object p2, Lcom/android/tools/r8/graph/proto/j;->d:Lcom/android/tools/r8/graph/proto/j;

    .line 2
    new-instance p4, Lcom/android/tools/r8/internal/cV;

    invoke-direct {p4, p1, p1, p3, p2}, Lcom/android/tools/r8/internal/cV;-><init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/graph/proto/j;)V

    .line 3
    invoke-virtual {p4, p0}, Lcom/android/tools/r8/internal/cV;->a(Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    .line 4
    invoke-interface {p5, p1}, Lcom/android/tools/r8/internal/Ey;->a(Lcom/android/tools/r8/internal/cV;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    return-object p1

    .line 6
    :cond_0
    invoke-super/range {p0 .. p5}, Lcom/android/tools/r8/internal/TY;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Ey;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/cV;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;
    .locals 1

    .line 7
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/YT;->b()Z

    move-result v0

    if-nez v0, :cond_0

    .line 8
    invoke-super {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/zX;->a(Lcom/android/tools/r8/internal/cV;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    return-object p1

    .line 10
    :cond_0
    sget-boolean v0, Lcom/android/tools/r8/internal/sz;->p:Z

    if-nez v0, :cond_2

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/YT;->b()Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 11
    :cond_2
    :goto_0
    invoke-super {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/zX;->a(Lcom/android/tools/r8/internal/cV;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    return-object p1
.end method

.method public final b()Lcom/android/tools/r8/internal/sz;
    .locals 0

    return-object p0
.end method

.method public final b(Lcom/android/tools/r8/internal/tv;)Lcom/android/tools/r8/internal/tv;
    .locals 4

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/zX;->b(Lcom/android/tools/r8/internal/tv;)Lcom/android/tools/r8/internal/tv;

    move-result-object v0

    .line 3
    iget-object v1, v0, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 4
    check-cast v1, Lcom/android/tools/r8/graph/l1;

    .line 5
    iget-object v2, p1, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 6
    check-cast v2, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/l1;->a(Lcom/android/tools/r8/graph/l1;)Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 7
    :cond_0
    iget-object v1, v0, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 8
    check-cast v1, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    .line 9
    iget-object v2, p1, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 10
    check-cast v2, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {p0, v2}, Lcom/android/tools/r8/internal/TY;->e(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    .line 11
    iget-object v3, v0, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 12
    check-cast v3, Lcom/android/tools/r8/graph/l1;

    .line 13
    iget-object v0, v0, Lcom/android/tools/r8/internal/YT;->b:Lcom/android/tools/r8/graph/s2;

    .line 14
    check-cast v0, Lcom/android/tools/r8/graph/l1;

    .line 15
    invoke-virtual {v2, v1}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    if-nez v1, :cond_1

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    .line 16
    :goto_0
    new-instance v1, Lcom/android/tools/r8/internal/sz$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/sz$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/sz;)V

    .line 18
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/tv;->b(Ljava/util/function/Function;)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 19
    new-instance v1, Lcom/android/tools/r8/internal/tv;

    invoke-direct {v1, v3, v0, v2, p1}, Lcom/android/tools/r8/internal/tv;-><init>(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)V

    return-object v1
.end method

.method public final e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/proto/j;
    .locals 3

    if-ne p0, p1, :cond_0

    .line 1
    invoke-static {}, Lcom/android/tools/r8/internal/Fy;->g()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/proto/j;

    move-result-object p1

    return-object p1

    .line 2
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/zX;->i:Lcom/android/tools/r8/internal/Z5;

    invoke-interface {v0, p2, p2}, Lcom/android/tools/r8/internal/Z5;->b(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/internal/TY;->d:Lcom/android/tools/r8/internal/Fy;

    .line 4
    invoke-virtual {v1, p1, v0}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/proto/j;

    move-result-object p1

    .line 5
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/x2;->z0()I

    move-result v1

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/x2;->z0()I

    move-result v2

    if-le v1, v2, :cond_3

    .line 6
    sget-boolean v1, Lcom/android/tools/r8/internal/sz;->p:Z

    if-nez v1, :cond_2

    .line 7
    iget-object v1, p0, Lcom/android/tools/r8/internal/TY;->c:Lcom/android/tools/r8/graph/B1;

    .line 8
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 9
    iget-object v2, v0, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1;->d1:Lcom/android/tools/r8/graph/I2;

    if-ne v2, v1, :cond_1

    goto :goto_0

    .line 10
    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 13
    :cond_2
    :goto_0
    invoke-static {v0, p2}, Lcom/android/tools/r8/internal/Tu;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)Ljava/util/List;

    move-result-object p2

    .line 14
    sget-object v0, Lcom/android/tools/r8/graph/proto/j;->d:Lcom/android/tools/r8/graph/proto/j;

    const/4 v0, 0x0

    .line 15
    sget-object v1, Lcom/android/tools/r8/graph/proto/c;->f:Lcom/android/tools/r8/graph/proto/c;

    .line 16
    invoke-static {p2, v0, v1}, Lcom/android/tools/r8/graph/proto/j;->a(Ljava/util/List;Lcom/android/tools/r8/graph/proto/k;Lcom/android/tools/r8/graph/proto/c;)Lcom/android/tools/r8/graph/proto/j;

    move-result-object p2

    .line 17
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/graph/proto/j;->a(Lcom/android/tools/r8/graph/proto/j;)Lcom/android/tools/r8/graph/proto/j;

    move-result-object p1

    :cond_3
    return-object p1
.end method

.method public final f(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    return-object p1
.end method

.method public final g(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/x2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/zX;->g:Ljava/util/function/Function;

    invoke-interface {v0, p1}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    if-eqz v0, :cond_0

    move-object p1, v0

    :cond_0
    return-object p1
.end method

.method public final g(Lcom/android/tools/r8/graph/J2;)Ljava/lang/Iterable;
    .locals 3

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/sz;->o:Lcom/android/tools/r8/internal/xz;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/xz;->d(Lcom/android/tools/r8/graph/J2;)Ljava/util/Set;

    move-result-object v0

    .line 3
    invoke-static {p1}, Lcom/android/tools/r8/internal/OI;->b(Ljava/lang/Object;)Ljava/lang/Iterable;

    move-result-object p1

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Iterable;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const/4 p1, 0x1

    aput-object v0, v1, p1

    .line 4
    invoke-static {v1}, Lcom/android/tools/r8/internal/Vw;->a([Ljava/lang/Iterable;)Lcom/android/tools/r8/internal/Uw;

    move-result-object p1

    return-object p1
.end method
