.class public final Lcom/android/tools/r8/internal/VD;
.super Lcom/android/tools/r8/internal/UD;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/EG;


# instance fields
.field public final synthetic h:Lcom/android/tools/r8/internal/WD;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/WD;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/VD;->h:Lcom/android/tools/r8/internal/WD;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/UD;-><init>(Lcom/android/tools/r8/internal/WD;)V

    return-void
.end method


# virtual methods
.method public final next()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/VD;->h:Lcom/android/tools/r8/internal/WD;

    iget-object v0, v0, Lcom/android/tools/r8/internal/WD;->d:[I

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/UD;->a()I

    move-result v1

    aget v0, v0, v1

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    return-object v0
.end method

.method public final r()I
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/VD;->h:Lcom/android/tools/r8/internal/WD;

    iget-object v0, v0, Lcom/android/tools/r8/internal/WD;->d:[I

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/UD;->a()I

    move-result v1

    aget v0, v0, v1

    return v0
.end method
