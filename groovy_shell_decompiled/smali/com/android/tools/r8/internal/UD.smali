.class public abstract Lcom/android/tools/r8/internal/UD;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public b:I

.field public c:I

.field public d:I

.field public e:Z

.field public f:Lcom/android/tools/r8/internal/sG;

.field public final synthetic g:Lcom/android/tools/r8/internal/WD;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/WD;)V
    .locals 1

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    iget v0, p1, Lcom/android/tools/r8/internal/WD;->g:I

    iput v0, p0, Lcom/android/tools/r8/internal/UD;->b:I

    const/4 v0, -0x1

    .line 8
    iput v0, p0, Lcom/android/tools/r8/internal/UD;->c:I

    .line 10
    iget v0, p1, Lcom/android/tools/r8/internal/WD;->i:I

    iput v0, p0, Lcom/android/tools/r8/internal/UD;->d:I

    .line 12
    iget-boolean p1, p1, Lcom/android/tools/r8/internal/WD;->f:Z

    iput-boolean p1, p0, Lcom/android/tools/r8/internal/UD;->e:Z

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 4

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/UD;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_4

    .line 2
    iget v0, p0, Lcom/android/tools/r8/internal/UD;->d:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lcom/android/tools/r8/internal/UD;->d:I

    .line 3
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/UD;->e:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    .line 4
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/UD;->e:Z

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    iget v0, v0, Lcom/android/tools/r8/internal/WD;->g:I

    iput v0, p0, Lcom/android/tools/r8/internal/UD;->c:I

    return v0

    .line 7
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    iget-object v0, v0, Lcom/android/tools/r8/internal/WD;->c:[I

    .line 9
    :cond_1
    iget v1, p0, Lcom/android/tools/r8/internal/UD;->b:I

    add-int/lit8 v1, v1, -0x1

    iput v1, p0, Lcom/android/tools/r8/internal/UD;->b:I

    if-gez v1, :cond_3

    const/high16 v2, -0x80000000

    .line 11
    iput v2, p0, Lcom/android/tools/r8/internal/UD;->c:I

    .line 12
    iget-object v2, p0, Lcom/android/tools/r8/internal/UD;->f:Lcom/android/tools/r8/internal/sG;

    neg-int v1, v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {v2, v1}, Lcom/android/tools/r8/internal/sG;->c(I)I

    move-result v1

    .line 13
    invoke-static {v1}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v2

    iget-object v3, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    iget v3, v3, Lcom/android/tools/r8/internal/WD;->e:I

    :goto_0
    and-int/2addr v2, v3

    .line 14
    aget v3, v0, v2

    if-eq v1, v3, :cond_2

    add-int/lit8 v2, v2, 0x1

    iget-object v3, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    iget v3, v3, Lcom/android/tools/r8/internal/WD;->e:I

    goto :goto_0

    :cond_2
    return v2

    .line 17
    :cond_3
    aget v2, v0, v1

    if-eqz v2, :cond_1

    iput v1, p0, Lcom/android/tools/r8/internal/UD;->c:I

    return v1

    .line 18
    :cond_4
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public final hasNext()Z
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/UD;->d:I

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public remove()V
    .locals 8

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/UD;->c:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_8

    .line 2
    iget-object v2, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    iget v3, v2, Lcom/android/tools/r8/internal/WD;->g:I

    const/4 v4, 0x0

    if-ne v0, v3, :cond_0

    .line 3
    iput-boolean v4, v2, Lcom/android/tools/r8/internal/WD;->f:Z

    goto :goto_2

    .line 5
    :cond_0
    iget v3, p0, Lcom/android/tools/r8/internal/UD;->b:I

    if-ltz v3, :cond_7

    .line 6
    iget-object v5, v2, Lcom/android/tools/r8/internal/WD;->c:[I

    :goto_0
    add-int/lit8 v2, v0, 0x1

    .line 8
    iget-object v3, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    iget v3, v3, Lcom/android/tools/r8/internal/WD;->e:I

    and-int/2addr v2, v3

    .line 10
    :goto_1
    aget v3, v5, v2

    if-nez v3, :cond_1

    .line 11
    aput v4, v5, v0

    .line 12
    :goto_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    iget v2, v0, Lcom/android/tools/r8/internal/WD;->i:I

    add-int/lit8 v2, v2, -0x1

    iput v2, v0, Lcom/android/tools/r8/internal/WD;->i:I

    .line 13
    iput v1, p0, Lcom/android/tools/r8/internal/UD;->c:I

    return-void

    .line 14
    :cond_1
    invoke-static {v3}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v6

    iget-object v7, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    iget v7, v7, Lcom/android/tools/r8/internal/WD;->e:I

    and-int/2addr v6, v7

    if-gt v0, v2, :cond_2

    if-ge v0, v6, :cond_3

    if-le v6, v2, :cond_6

    goto :goto_3

    :cond_2
    if-lt v0, v6, :cond_6

    if-le v6, v2, :cond_6

    :cond_3
    :goto_3
    if-ge v2, v0, :cond_5

    .line 19
    iget-object v6, p0, Lcom/android/tools/r8/internal/UD;->f:Lcom/android/tools/r8/internal/sG;

    if-nez v6, :cond_4

    new-instance v6, Lcom/android/tools/r8/internal/sG;

    const/4 v7, 0x2

    invoke-direct {v6, v7}, Lcom/android/tools/r8/internal/sG;-><init>(I)V

    iput-object v6, p0, Lcom/android/tools/r8/internal/UD;->f:Lcom/android/tools/r8/internal/sG;

    .line 20
    :cond_4
    iget-object v6, p0, Lcom/android/tools/r8/internal/UD;->f:Lcom/android/tools/r8/internal/sG;

    aget v7, v5, v2

    invoke-virtual {v6, v7}, Lcom/android/tools/r8/internal/sG;->add(I)Z

    .line 22
    :cond_5
    aput v3, v5, v0

    .line 23
    iget-object v3, p0, Lcom/android/tools/r8/internal/UD;->g:Lcom/android/tools/r8/internal/WD;

    iget-object v3, v3, Lcom/android/tools/r8/internal/WD;->d:[I

    aget v6, v3, v2

    aput v6, v3, v0

    move v0, v2

    goto :goto_0

    :cond_6
    add-int/lit8 v2, v2, 0x1

    and-int/2addr v2, v7

    goto :goto_1

    .line 24
    :cond_7
    iget-object v0, p0, Lcom/android/tools/r8/internal/UD;->f:Lcom/android/tools/r8/internal/sG;

    neg-int v3, v3

    add-int/lit8 v3, v3, -0x1

    invoke-virtual {v0, v3}, Lcom/android/tools/r8/internal/sG;->c(I)I

    move-result v0

    invoke-virtual {v2, v0}, Lcom/android/tools/r8/internal/WD;->remove(I)I

    .line 25
    iput v1, p0, Lcom/android/tools/r8/internal/UD;->c:I

    return-void

    .line 26
    :cond_8
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method
