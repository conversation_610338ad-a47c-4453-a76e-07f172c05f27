.class public final Lcom/android/tools/r8/internal/TD;
.super Lcom/android/tools/r8/internal/g1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/WD;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/WD;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/g1;-><init>()V

    return-void
.end method


# virtual methods
.method public final clear()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/WD;->clear()V

    return-void
.end method

.method public final contains(Ljava/lang/Object;)Z
    .locals 7

    .line 1
    instance-of v0, p1, Ljava/util/Map$Entry;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 2
    :cond_0
    check-cast p1, Ljava/util/Map$Entry;

    .line 3
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_9

    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/Integer;

    if-nez v0, :cond_1

    goto :goto_0

    .line 4
    :cond_1
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_9

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/Integer;

    if-nez v0, :cond_2

    goto :goto_0

    .line 5
    :cond_2
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    .line 6
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    const/4 v2, 0x1

    if-nez v0, :cond_4

    .line 7
    iget-object v0, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    iget-boolean v3, v0, Lcom/android/tools/r8/internal/WD;->f:Z

    if-eqz v3, :cond_3

    iget-object v3, v0, Lcom/android/tools/r8/internal/WD;->d:[I

    iget v0, v0, Lcom/android/tools/r8/internal/WD;->g:I

    aget v0, v3, v0

    if-ne v0, p1, :cond_3

    move v1, v2

    :cond_3
    return v1

    .line 9
    :cond_4
    iget-object v3, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    iget-object v3, v3, Lcom/android/tools/r8/internal/WD;->c:[I

    .line 12
    invoke-static {v0}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v4

    iget-object v5, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    iget v6, v5, Lcom/android/tools/r8/internal/WD;->e:I

    and-int/2addr v4, v6

    aget v6, v3, v4

    if-nez v6, :cond_5

    return v1

    :cond_5
    if-ne v0, v6, :cond_7

    .line 13
    iget-object v0, v5, Lcom/android/tools/r8/internal/WD;->d:[I

    aget v0, v0, v4

    if-ne v0, p1, :cond_6

    move v1, v2

    :cond_6
    return v1

    :cond_7
    add-int/2addr v4, v2

    .line 16
    iget-object v5, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    iget v6, v5, Lcom/android/tools/r8/internal/WD;->e:I

    and-int/2addr v4, v6

    aget v6, v3, v4

    if-nez v6, :cond_8

    return v1

    :cond_8
    if-ne v0, v6, :cond_7

    .line 17
    iget-object v0, v5, Lcom/android/tools/r8/internal/WD;->d:[I

    aget v0, v0, v4

    if-ne v0, p1, :cond_9

    move v1, v2

    :cond_9
    :goto_0
    return v1
.end method

.method public final iterator()Lcom/android/tools/r8/internal/B10;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/PD;

    iget-object v1, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/PD;-><init>(Lcom/android/tools/r8/internal/WD;)V

    return-object v0
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 2

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/PD;

    iget-object v1, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/PD;-><init>(Lcom/android/tools/r8/internal/WD;)V

    return-object v0
.end method

.method public final remove(Ljava/lang/Object;)Z
    .locals 7

    .line 1
    instance-of v0, p1, Ljava/util/Map$Entry;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 2
    :cond_0
    check-cast p1, Ljava/util/Map$Entry;

    .line 3
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_a

    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/Integer;

    if-nez v0, :cond_1

    goto/16 :goto_0

    .line 4
    :cond_1
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_a

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/Integer;

    if-nez v0, :cond_2

    goto/16 :goto_0

    .line 5
    :cond_2
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    .line 6
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    const/4 v2, 0x1

    if-nez v0, :cond_5

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    iget-boolean v3, v0, Lcom/android/tools/r8/internal/WD;->f:Z

    if-eqz v3, :cond_4

    iget-object v3, v0, Lcom/android/tools/r8/internal/WD;->d:[I

    iget v4, v0, Lcom/android/tools/r8/internal/WD;->g:I

    aget v3, v3, v4

    if-ne v3, p1, :cond_4

    .line 9
    iput-boolean v1, v0, Lcom/android/tools/r8/internal/WD;->f:Z

    .line 11
    iget p1, v0, Lcom/android/tools/r8/internal/WD;->i:I

    sub-int/2addr p1, v2

    iput p1, v0, Lcom/android/tools/r8/internal/WD;->i:I

    .line 12
    iget v1, v0, Lcom/android/tools/r8/internal/WD;->h:I

    div-int/lit8 v1, v1, 0x4

    if-ge p1, v1, :cond_3

    const/16 p1, 0x10

    if-le v4, p1, :cond_3

    div-int/lit8 v4, v4, 0x2

    invoke-virtual {v0, v4}, Lcom/android/tools/r8/internal/WD;->e(I)V

    :cond_3
    return v2

    :cond_4
    return v1

    .line 13
    :cond_5
    iget-object v3, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    iget-object v3, v3, Lcom/android/tools/r8/internal/WD;->c:[I

    .line 16
    invoke-static {v0}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v4

    iget-object v5, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    iget v6, v5, Lcom/android/tools/r8/internal/WD;->e:I

    and-int/2addr v4, v6

    aget v6, v3, v4

    if-nez v6, :cond_6

    return v1

    :cond_6
    if-ne v6, v0, :cond_8

    .line 18
    iget-object v0, v5, Lcom/android/tools/r8/internal/WD;->d:[I

    aget v0, v0, v4

    if-ne v0, p1, :cond_7

    .line 19
    invoke-virtual {v5, v4}, Lcom/android/tools/r8/internal/WD;->f(I)I

    return v2

    :cond_7
    return v1

    :cond_8
    add-int/2addr v4, v2

    .line 20
    iget-object v5, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    iget v6, v5, Lcom/android/tools/r8/internal/WD;->e:I

    and-int/2addr v4, v6

    aget v6, v3, v4

    if-nez v6, :cond_9

    return v1

    :cond_9
    if-ne v6, v0, :cond_8

    .line 22
    iget-object v6, v5, Lcom/android/tools/r8/internal/WD;->d:[I

    aget v6, v6, v4

    if-ne v6, p1, :cond_8

    .line 23
    invoke-virtual {v5, v4}, Lcom/android/tools/r8/internal/WD;->f(I)I

    return v2

    :cond_a
    :goto_0
    return v1
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TD;->b:Lcom/android/tools/r8/internal/WD;

    iget v0, v0, Lcom/android/tools/r8/internal/WD;->i:I

    return v0
.end method
