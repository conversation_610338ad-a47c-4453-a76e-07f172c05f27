.class public final synthetic Lcom/android/tools/r8/internal/RY$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/xp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/RY;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/Fy;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/RY;Lcom/android/tools/r8/internal/Fy;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/RY$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/RY;

    iput-object p2, p0, Lcom/android/tools/r8/internal/RY$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/Fy;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/RY$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/RY;

    iget-object v1, p0, Lcom/android/tools/r8/internal/RY$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/RY;->a(Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/Ml0;

    move-result-object v0

    return-object v0
.end method
