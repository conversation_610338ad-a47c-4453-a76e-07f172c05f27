.class public final Lcom/android/tools/r8/internal/Sw;
.super Lcom/android/tools/r8/internal/Vw;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/QI;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/QI;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/Sw;->b:Lcom/android/tools/r8/internal/QI;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/Vw;-><init>()V

    return-void
.end method


# virtual methods
.method public final iterator()Ljava/util/Iterator;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Sw;->b:Lcom/android/tools/r8/internal/QI;

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/internal/QI;->b:Ljava/lang/Iterable;

    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v1

    iget-object v0, v0, Lcom/android/tools/r8/internal/QI;->c:Lcom/android/tools/r8/internal/Hx;

    invoke-static {v1, v0}, Lcom/android/tools/r8/internal/hJ;->a(Ljava/util/Iterator;Lcom/android/tools/r8/internal/Hx;)Lcom/android/tools/r8/internal/aJ;

    move-result-object v0

    .line 3
    sget-object v1, Lcom/android/tools/r8/internal/Sw$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/Sw$$ExternalSyntheticLambda0;

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/hJ;->a(Ljava/util/Iterator;Lcom/android/tools/r8/internal/Hx;)Lcom/android/tools/r8/internal/aJ;

    move-result-object v0

    .line 4
    new-instance v1, Lcom/android/tools/r8/internal/eJ;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/internal/eJ;-><init>(Ljava/util/Iterator;)V

    return-object v1
.end method
