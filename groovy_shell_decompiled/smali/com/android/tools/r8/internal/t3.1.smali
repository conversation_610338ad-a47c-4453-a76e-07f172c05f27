.class public final Lcom/android/tools/r8/internal/t3;
.super Lcom/android/tools/r8/graph/n0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final f:Lcom/android/tools/r8/graph/y;

.field public final g:Lcom/android/tools/r8/internal/r3;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/r3;)V
    .locals 1

    .line 1
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Bo<PERSON>an;

    invoke-direct {p0, p1, p2, v0}, Lcom/android/tools/r8/graph/n0;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Ljava/lang/Boolean;)V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/t3;->f:Lcom/android/tools/r8/graph/y;

    .line 3
    iput-object p3, p0, Lcom/android/tools/r8/internal/t3;->g:Lcom/android/tools/r8/internal/r3;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t3;->i(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t3;->i(Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public final b(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t3;->i(Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public final c(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t3;->i(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public final e(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t3;->i(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public final e(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t3;->i(Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public final g(Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t3;->i(Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public final g(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t3;->i(Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public final h(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t3;->i(Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public final i(Lcom/android/tools/r8/graph/l1;)V
    .locals 2

    .line 19
    iget-object v0, p0, Lcom/android/tools/r8/internal/t3;->f:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/j;->c(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/z3;

    move-result-object p1

    .line 20
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/z3;->r()Lcom/android/tools/r8/graph/B5;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 24
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/z3;->r()Lcom/android/tools/r8/graph/B5;

    move-result-object p1

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/internal/t3;->g:Lcom/android/tools/r8/internal/r3;

    .line 26
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/l1;

    .line 27
    iget-object v0, v0, Lcom/android/tools/r8/internal/zX;->f:Lcom/android/tools/r8/internal/d6;

    invoke-interface {v0, v1, v1}, Lcom/android/tools/r8/internal/b6;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/l1;

    .line 28
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    if-eq v0, p1, :cond_1

    .line 29
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 30
    iput-object p1, p0, Lcom/android/tools/r8/graph/c6;->e:Ljava/lang/Boolean;

    .line 31
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b6;->a()V

    :cond_1
    return-void
.end method

.method public final i(Lcom/android/tools/r8/graph/x2;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/t3;->f:Lcom/android/tools/r8/graph/y;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 4
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/j;->e(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/V4;

    move-result-object p1

    .line 5
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/V4;->o()Lcom/android/tools/r8/graph/V4$c;

    move-result-object p1

    if-eqz p1, :cond_2

    .line 6
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/V4$c;->d()Lcom/android/tools/r8/graph/E0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/b1;->g0()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 10
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/V4$c;->r()Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    .line 11
    iget-object v0, p0, Lcom/android/tools/r8/internal/t3;->g:Lcom/android/tools/r8/internal/r3;

    .line 12
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    .line 13
    iget-object v0, v0, Lcom/android/tools/r8/internal/zX;->i:Lcom/android/tools/r8/internal/Z5;

    invoke-interface {v0, v1, v1}, Lcom/android/tools/r8/internal/Z5;->a(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    .line 14
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    if-ne v0, p1, :cond_1

    iget-object p1, p0, Lcom/android/tools/r8/internal/t3;->g:Lcom/android/tools/r8/internal/r3;

    .line 15
    iget-object p1, p1, Lcom/android/tools/r8/internal/r3;->n:Ljava/util/IdentityHashMap;

    invoke-virtual {p1, v0}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    .line 16
    :cond_1
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 17
    iput-object p1, p0, Lcom/android/tools/r8/graph/c6;->e:Ljava/lang/Boolean;

    .line 18
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b6;->a()V

    :cond_2
    :goto_0
    return-void
.end method
