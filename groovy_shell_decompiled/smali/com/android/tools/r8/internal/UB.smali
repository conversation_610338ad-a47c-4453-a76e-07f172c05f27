.class public interface abstract Lcom/android/tools/r8/internal/UB;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# virtual methods
.method public abstract a(Lcom/android/tools/r8/internal/UB;Lcom/android/tools/r8/internal/WB;)I
.end method

.method public a()Lcom/android/tools/r8/internal/E5;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public a(Lcom/android/tools/r8/graph/l1;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public b(Lcom/android/tools/r8/internal/UB;Lcom/android/tools/r8/internal/WB;)I
    .locals 2

    .line 1
    invoke-interface {p0}, Lcom/android/tools/r8/internal/UB;->getKind()I

    move-result v0

    invoke-interface {p1}, Lcom/android/tools/r8/internal/UB;->getKind()I

    move-result v1

    if-ne v0, v1, :cond_0

    .line 2
    invoke-interface {p0, p1, p2}, Lcom/android/tools/r8/internal/UB;->a(Lcom/android/tools/r8/internal/UB;Lcom/android/tools/r8/internal/WB;)I

    move-result p1

    return p1

    .line 4
    :cond_0
    invoke-interface {p0}, Lcom/android/tools/r8/internal/UB;->getKind()I

    move-result p2

    invoke-static {p2}, Lcom/android/tools/r8/c;->b(I)I

    move-result p2

    invoke-interface {p1}, Lcom/android/tools/r8/internal/UB;->getKind()I

    move-result p1

    invoke-static {p1}, Lcom/android/tools/r8/c;->b(I)I

    move-result p1

    sub-int/2addr p2, p1

    return p2
.end method

.method public b()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public c()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public d()Lcom/android/tools/r8/internal/r;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public e()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public f()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public g()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public abstract getKind()I
.end method

.method public h()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public i()Lcom/android/tools/r8/internal/C20;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public j()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public k()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public l()Lcom/android/tools/r8/internal/jt0;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public m()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public q()Lcom/android/tools/r8/internal/AA;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public r()Lcom/android/tools/r8/internal/pV;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public s()Lcom/android/tools/r8/internal/Sv;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public t()Lcom/android/tools/r8/internal/w8;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public v()Lcom/android/tools/r8/internal/RC;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
