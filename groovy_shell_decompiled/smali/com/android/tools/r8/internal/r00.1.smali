.class public final Lcom/android/tools/r8/internal/r00;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/m00;
.implements Ljava/util/Map$Entry;


# instance fields
.field public b:I

.field public final synthetic c:Lcom/android/tools/r8/internal/v00;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/v00;I)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p2, p0, Lcom/android/tools/r8/internal/r00;->b:I

    return-void
.end method


# virtual methods
.method public final equals(Ljava/lang/Object;)Z
    .locals 3

    .line 1
    instance-of v0, p1, Ljava/util/Map$Entry;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 2
    :cond_0
    check-cast p1, Ljava/util/Map$Entry;

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v0, v0, Lcom/android/tools/r8/internal/v00;->c:[Ljava/lang/Object;

    iget v2, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget-object v0, v0, v2

    if-nez v0, :cond_1

    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_2

    goto :goto_0

    :cond_1
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v0, v0, Lcom/android/tools/r8/internal/v00;->d:[I

    iget v2, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget v0, v0, v2

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    if-ne v0, p1, :cond_2

    const/4 v1, 0x1

    :cond_2
    return v1
.end method

.method public final getIntValue()I
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v0, v0, Lcom/android/tools/r8/internal/v00;->d:[I

    iget v1, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget v0, v0, v1

    return v0
.end method

.method public final getKey()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v0, v0, Lcom/android/tools/r8/internal/v00;->c:[Ljava/lang/Object;

    iget v1, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget-object v0, v0, v1

    return-object v0
.end method

.method public final getValue()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v0, v0, Lcom/android/tools/r8/internal/v00;->d:[I

    iget v1, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget v0, v0, v1

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    return-object v0
.end method

.method public final hashCode()I
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v0, v0, Lcom/android/tools/r8/internal/v00;->c:[Ljava/lang/Object;

    iget v1, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget-object v0, v0, v1

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v1, v1, Lcom/android/tools/r8/internal/v00;->d:[I

    iget v2, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget v1, v1, v2

    xor-int/2addr v0, v1

    return v0
.end method

.method public final setValue(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    check-cast p1, Ljava/lang/Integer;

    .line 2
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v0, v0, Lcom/android/tools/r8/internal/v00;->d:[I

    iget v1, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget v2, v0, v1

    .line 4
    aput p1, v0, v1

    .line 5
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1
.end method

.method public final toString()Ljava/lang/String;
    .locals 3

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v1, v1, Lcom/android/tools/r8/internal/v00;->c:[Ljava/lang/Object;

    iget v2, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget-object v1, v1, v2

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "=>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/r00;->c:Lcom/android/tools/r8/internal/v00;

    iget-object v1, v1, Lcom/android/tools/r8/internal/v00;->d:[I

    iget v2, p0, Lcom/android/tools/r8/internal/r00;->b:I

    aget v1, v1, v2

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
