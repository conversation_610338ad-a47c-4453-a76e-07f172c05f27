.class public final synthetic Lcom/android/tools/r8/internal/v5$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/jl;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/v5;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/p5;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/O9;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/v5;Lcom/android/tools/r8/internal/p5;Lcom/android/tools/r8/internal/O9;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/v5$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/v5;

    iput-object p2, p0, Lcom/android/tools/r8/internal/v5$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/p5;

    iput-object p3, p0, Lcom/android/tools/r8/internal/v5$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/internal/O9;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/B40;Lcom/android/tools/r8/internal/ix;Lcom/android/tools/r8/internal/jS;Lcom/android/tools/r8/internal/n9;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/internal/J9;Lcom/android/tools/r8/graph/B1;)Ljava/util/Collection;
    .locals 13

    move-object v0, p0

    iget-object v1, v0, Lcom/android/tools/r8/internal/v5$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/v5;

    iget-object v2, v0, Lcom/android/tools/r8/internal/v5$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/p5;

    iget-object v3, v0, Lcom/android/tools/r8/internal/v5$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/internal/O9;

    move-object v4, p1

    move-object v5, p2

    move-object/from16 v6, p3

    move-object/from16 v7, p4

    move-object/from16 v8, p5

    move-object/from16 v9, p6

    move-object/from16 v10, p7

    move-object/from16 v11, p8

    move-object/from16 v12, p9

    invoke-virtual/range {v1 .. v12}, Lcom/android/tools/r8/internal/v5;->a(Lcom/android/tools/r8/internal/p5;Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/internal/B40;Lcom/android/tools/r8/internal/ix;Lcom/android/tools/r8/internal/jS;Lcom/android/tools/r8/internal/n9;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/internal/J9;Lcom/android/tools/r8/graph/B1;)Ljava/util/Collection;

    move-result-object v1

    return-object v1
.end method
