.class public final Lcom/android/tools/r8/internal/s50;
.super Lcom/android/tools/r8/internal/fA;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic K:Z = true


# instance fields
.field public final J:Lcom/android/tools/r8/internal/Gp0;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Gp0;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/fA;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/s50;->J:Lcom/android/tools/r8/internal/Gp0;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/ei;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/hi;)V
    .locals 0

    .line 282
    invoke-virtual {p2, p0, p1}, Lcom/android/tools/r8/internal/hi;->a(Lcom/android/tools/r8/internal/ei;Ljava/util/concurrent/ExecutorService;)V

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/j1;)Z
    .locals 1

    .line 236
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/j1;->i1()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/i0;->v0()Z

    move-result p0

    if-eqz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static synthetic d(Lcom/android/tools/r8/graph/D5;)Z
    .locals 0

    const/4 p0, 0x1

    return p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/l20;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/internal/Gp0;
    .locals 6

    .line 263
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/i0;->v0()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 266
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/J9;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/M9;)V

    .line 267
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/J9;->a(Lcom/android/tools/r8/graph/D5;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 268
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    invoke-virtual {v0, p1, p5, p2}, Lcom/android/tools/r8/internal/J9;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/internal/M9;)V

    .line 269
    :cond_1
    :goto_0
    iget-object p2, p0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    .line 274
    invoke-static {p2}, Lcom/android/tools/r8/internal/UU;->b(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/UU$a;

    move-result-object v5

    move-object v0, p0

    move-object v1, p1

    move-object v2, p3

    move-object v3, p4

    move-object v4, p5

    .line 275
    invoke-virtual/range {v0 .. v5}, Lcom/android/tools/r8/internal/fA;->d(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/l20;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/internal/UU$a;)Lcom/android/tools/r8/internal/Gp0;

    move-result-object p1

    return-object p1
.end method

.method public final a()V
    .locals 2

    .line 276
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    sget-object v1, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda2;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/J9;->a(Lcom/android/tools/r8/internal/op0;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/l20;Lcom/android/tools/r8/internal/ei;Lcom/android/tools/r8/internal/ef;)V
    .locals 10

    .line 187
    iget-object v0, p1, Lcom/android/tools/r8/graph/G0;->b:Lcom/android/tools/r8/graph/E0;

    .line 188
    iget-object v0, v0, Lcom/android/tools/r8/graph/E0;->d:Lcom/android/tools/r8/origin/Origin;

    .line 189
    new-instance v1, Lcom/android/tools/r8/position/MethodPosition;

    .line 190
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/x2;->y0()Lcom/android/tools/r8/references/MethodReference;

    move-result-object v2

    invoke-direct {v1, v2}, Lcom/android/tools/r8/position/MethodPosition;-><init>(Lcom/android/tools/r8/references/MethodReference;)V

    new-instance v2, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda9;

    move-object v3, v2

    move-object v4, p0

    move-object v5, p1

    move-object v6, p2

    move-object v7, p3

    move-object v8, p4

    move-object v9, p5

    invoke-direct/range {v3 .. v9}, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda9;-><init>(Lcom/android/tools/r8/internal/s50;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/l20;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)V

    .line 191
    invoke-static {v0, v1, v2}, Lcom/android/tools/r8/internal/yu;->a(Lcom/android/tools/r8/origin/Origin;Lcom/android/tools/r8/position/Position;Ljava/util/function/Supplier;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Gp0;

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/ei;Lcom/android/tools/r8/internal/sH;)V
    .locals 3

    .line 238
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->N0()Lcom/android/tools/r8/graph/j1;

    move-result-object v0

    .line 239
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/E2;->i(Lcom/android/tools/r8/graph/j1;)Lcom/android/tools/r8/graph/D5;

    move-result-object v0

    .line 240
    new-instance v1, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda0;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/E2;)V

    invoke-static {v1}, Lcom/android/tools/r8/internal/QR;->a(Lcom/android/tools/r8/internal/Yw;)Ljava/util/ArrayList;

    move-result-object v1

    if-eqz v0, :cond_0

    .line 242
    invoke-virtual {p3, v0, p2}, Lcom/android/tools/r8/internal/ei;->b(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/M9;)V

    .line 245
    :cond_0
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/D5;

    .line 246
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/j1;->m1()Z

    move-result v2

    if-nez v2, :cond_1

    .line 247
    invoke-virtual {p3, v1, p2}, Lcom/android/tools/r8/internal/ei;->b(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/M9;)V

    if-eqz p4, :cond_1

    .line 249
    invoke-virtual {p4, v1, p2}, Lcom/android/tools/r8/internal/sH;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/M9;)V

    goto :goto_0

    .line 256
    :cond_2
    iget-object p2, p0, Lcom/android/tools/r8/internal/fA;->j:Lcom/android/tools/r8/utils/w;

    .line 257
    iget-object p2, p2, Lcom/android/tools/r8/utils/w;->k:Lcom/android/tools/r8/ProgramConsumer;

    .line 258
    instance-of p2, p2, Lcom/android/tools/r8/ClassFileConsumer;

    if-eqz p2, :cond_3

    .line 259
    iget-object p2, p1, Lcom/android/tools/r8/graph/E2;->v:Lcom/android/tools/r8/internal/pb;

    if-eqz p2, :cond_3

    .line 260
    iget-object p2, p0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    .line 261
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p2

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E2;->K1()Lcom/android/tools/r8/internal/pb;

    move-result-object p3

    invoke-virtual {p2, p3}, Lcom/android/tools/r8/utils/w;->a(Lcom/android/tools/r8/internal/pb;)Lcom/android/tools/r8/internal/pb;

    move-result-object p2

    .line 262
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/graph/E2;->a(Lcom/android/tools/r8/internal/pb;)V

    :cond_3
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Ljava/util/concurrent/ExecutorService;)V
    .locals 32

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    .line 1
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v3

    iget-object v3, v3, Lcom/android/tools/r8/utils/w;->J0:Lcom/android/tools/r8/utils/w$g;

    invoke-virtual {v3}, Lcom/android/tools/r8/utils/w$g;->b()Z

    move-result v3

    if-eqz v3, :cond_0

    .line 2
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v3

    invoke-static {v1, v3}, Lcom/android/tools/r8/internal/EP;->a(Lcom/android/tools/r8/graph/y;Ljava/util/Collection;)V

    .line 3
    :cond_0
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/fA;->a(Ljava/util/concurrent/ExecutorService;)V

    .line 4
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/h;->a()Lcom/android/tools/r8/graph/x0;

    move-result-object v3

    .line 6
    invoke-static/range {p1 .. p1}, Lcom/android/tools/r8/internal/L50;->b(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/L50;

    move-result-object v4

    .line 7
    new-instance v5, Lcom/android/tools/r8/internal/ei;

    invoke-direct {v5, v4, v0, v2}, Lcom/android/tools/r8/internal/ei;-><init>(Lcom/android/tools/r8/internal/L50;Lcom/android/tools/r8/internal/s50;Ljava/util/concurrent/ExecutorService;)V

    .line 8
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v6

    invoke-static {v6}, Lcom/android/tools/r8/internal/hH;->a(Lcom/android/tools/r8/utils/w;)I

    move-result v6

    const/4 v7, 0x3

    if-ne v6, v7, :cond_1

    const/4 v7, 0x0

    goto :goto_0

    .line 12
    :cond_1
    new-instance v7, Lcom/android/tools/r8/internal/sH;

    invoke-direct {v7, v1, v6}, Lcom/android/tools/r8/internal/sH;-><init>(Lcom/android/tools/r8/graph/y;I)V

    .line 13
    :goto_0
    iget-object v6, v0, Lcom/android/tools/r8/internal/s50;->J:Lcom/android/tools/r8/internal/Gp0;

    const-string v9, "IR conversion"

    invoke-virtual {v6, v9}, Lcom/android/tools/r8/internal/Gp0;->a(Ljava/lang/String;)V

    .line 15
    invoke-virtual {v0, v5, v7, v2}, Lcom/android/tools/r8/internal/s50;->a(Lcom/android/tools/r8/internal/ei;Lcom/android/tools/r8/internal/sH;Ljava/util/concurrent/ExecutorService;)V

    .line 17
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/s50;->b()V

    .line 18
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/s50;->a()V

    .line 19
    iget-object v6, v1, Lcom/android/tools/r8/graph/y;->a:Lcom/android/tools/r8/graph/h;

    .line 20
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/h;->g()Lcom/android/tools/r8/synthesis/I;

    move-result-object v6

    .line 21
    invoke-virtual {v6}, Lcom/android/tools/r8/synthesis/I;->b()Z

    move-result v6

    if-eqz v6, :cond_2

    .line 22
    new-instance v6, Lcom/android/tools/r8/graph/h;

    .line 24
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v9

    invoke-virtual {v9}, Lcom/android/tools/r8/graph/h;->g()Lcom/android/tools/r8/synthesis/I;

    move-result-object v9

    invoke-virtual {v9, v3}, Lcom/android/tools/r8/synthesis/I;->a(Lcom/android/tools/r8/graph/x0;)Lcom/android/tools/r8/synthesis/a;

    move-result-object v3

    .line 25
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v9

    invoke-virtual {v9}, Lcom/android/tools/r8/graph/h;->f()Lcom/android/tools/r8/shaking/b2;

    move-result-object v9

    invoke-direct {v6, v3, v9}, Lcom/android/tools/r8/graph/h;-><init>(Lcom/android/tools/r8/synthesis/a;Lcom/android/tools/r8/shaking/b2;)V

    .line 26
    invoke-virtual {v1, v6}, Lcom/android/tools/r8/graph/y;->b(Lcom/android/tools/r8/graph/h;)Lcom/android/tools/r8/graph/y;

    .line 30
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/h;->a()Lcom/android/tools/r8/graph/x0;

    move-result-object v3

    .line 31
    :cond_2
    invoke-virtual {v0, v5, v7, v2}, Lcom/android/tools/r8/internal/s50;->b(Lcom/android/tools/r8/internal/ei;Lcom/android/tools/r8/internal/sH;Ljava/util/concurrent/ExecutorService;)V

    .line 32
    iget-object v2, v1, Lcom/android/tools/r8/graph/y;->a:Lcom/android/tools/r8/graph/h;

    .line 33
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/h;->g()Lcom/android/tools/r8/synthesis/I;

    move-result-object v2

    .line 34
    invoke-virtual {v2}, Lcom/android/tools/r8/synthesis/I;->b()Z

    move-result v2

    if-eqz v2, :cond_3

    .line 35
    new-instance v2, Lcom/android/tools/r8/graph/h;

    .line 37
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v5

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/h;->g()Lcom/android/tools/r8/synthesis/I;

    move-result-object v5

    invoke-virtual {v5, v3}, Lcom/android/tools/r8/synthesis/I;->a(Lcom/android/tools/r8/graph/x0;)Lcom/android/tools/r8/synthesis/a;

    move-result-object v3

    .line 38
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v5

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/h;->f()Lcom/android/tools/r8/shaking/b2;

    move-result-object v5

    invoke-direct {v2, v3, v5}, Lcom/android/tools/r8/graph/h;-><init>(Lcom/android/tools/r8/synthesis/a;Lcom/android/tools/r8/shaking/b2;)V

    .line 39
    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/y;->b(Lcom/android/tools/r8/graph/h;)Lcom/android/tools/r8/graph/y;

    .line 43
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/h;->a()Lcom/android/tools/r8/graph/x0;

    move-result-object v3

    .line 44
    :cond_3
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/x0;->c()Lcom/android/tools/r8/graph/w0;

    move-result-object v2

    .line 46
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v3

    .line 47
    iget-object v3, v3, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 48
    iget-boolean v3, v3, Lcom/android/tools/r8/internal/QS;->a:Z

    if-eqz v3, :cond_20

    .line 49
    new-instance v3, Lcom/android/tools/r8/internal/lt;

    invoke-direct {v3, v1}, Lcom/android/tools/r8/internal/lt;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 50
    sget-boolean v5, Lcom/android/tools/r8/internal/lt;->c:Z

    if-nez v5, :cond_5

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v5

    .line 51
    iget-object v5, v5, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 52
    iget-boolean v5, v5, Lcom/android/tools/r8/internal/QS;->a:Z

    if-eqz v5, :cond_4

    goto :goto_1

    .line 53
    :cond_4
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 54
    :cond_5
    :goto_1
    new-instance v5, Ljava/util/ArrayList;

    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    .line 55
    iget-object v6, v2, Lcom/android/tools/r8/graph/w0;->a:Ljava/util/ArrayList;

    .line 56
    invoke-virtual {v6}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :goto_2
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_19

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/android/tools/r8/graph/E2;

    .line 57
    iget-object v9, v3, Lcom/android/tools/r8/internal/lt;->b:Ljava/util/IdentityHashMap;

    iget-object v10, v7, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v9, v10}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_18

    .line 58
    iget-object v9, v3, Lcom/android/tools/r8/internal/lt;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v9, v7}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/E2;)Z

    move-result v9

    if-eqz v9, :cond_6

    move-object/from16 p2, v6

    goto/16 :goto_c

    .line 61
    :cond_6
    iget-object v9, v3, Lcom/android/tools/r8/internal/lt;->b:Ljava/util/IdentityHashMap;

    iget-object v10, v7, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v9, v10}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    move-object v11, v9

    check-cast v11, Lcom/android/tools/r8/graph/J2;

    .line 62
    sget-boolean v9, Lcom/android/tools/r8/internal/lt;->c:Z

    if-nez v9, :cond_8

    if-eqz v11, :cond_7

    goto :goto_3

    :cond_7
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 63
    :cond_8
    :goto_3
    invoke-virtual {v3, v7, v11}, Lcom/android/tools/r8/internal/lt;->a(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/J2;)[Lcom/android/tools/r8/graph/j1;

    move-result-object v15

    .line 64
    sget-object v14, Lcom/android/tools/r8/graph/j1;->u:[Lcom/android/tools/r8/graph/j1;

    if-nez v9, :cond_a

    .line 65
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/E0;->d1()Lcom/android/tools/r8/graph/J2;

    move-result-object v10

    iget-object v12, v3, Lcom/android/tools/r8/internal/lt;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v12}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v12

    iget-object v12, v12, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    if-ne v10, v12, :cond_9

    goto :goto_4

    :cond_9
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    :cond_a
    :goto_4
    if-nez v9, :cond_c

    .line 66
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/E2;->O1()Z

    move-result v10

    if-nez v10, :cond_b

    goto :goto_5

    :cond_b
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    :cond_c
    :goto_5
    if-nez v9, :cond_e

    .line 67
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/E0;->W0()Lcom/android/tools/r8/graph/J2;

    move-result-object v10

    if-nez v10, :cond_d

    goto :goto_6

    :cond_d
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    :cond_e
    :goto_6
    if-nez v9, :cond_10

    .line 68
    iget-object v10, v7, Lcom/android/tools/r8/graph/E0;->p:Ljava/util/List;

    invoke-interface {v10}, Ljava/util/List;->isEmpty()Z

    move-result v10

    if-eqz v10, :cond_f

    goto :goto_7

    .line 69
    :cond_f
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    :cond_10
    :goto_7
    if-nez v9, :cond_12

    .line 70
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/E2;->O1()Z

    move-result v9

    if-nez v9, :cond_11

    goto :goto_8

    :cond_11
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 71
    :cond_12
    :goto_8
    new-instance v9, Lcom/android/tools/r8/graph/E2;

    move-object v10, v9

    .line 72
    iget-object v12, v7, Lcom/android/tools/r8/graph/E2;->u:Lcom/android/tools/r8/ProgramResource$Kind;

    .line 73
    iget-object v13, v7, Lcom/android/tools/r8/graph/E0;->d:Lcom/android/tools/r8/origin/Origin;

    .line 74
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/E0;->M0()Lcom/android/tools/r8/graph/Q;

    move-result-object v16

    move-object v8, v14

    move-object/from16 v14, v16

    move-object/from16 p2, v6

    iget-object v6, v3, Lcom/android/tools/r8/internal/lt;->a:Lcom/android/tools/r8/graph/y;

    .line 75
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v6

    iget-object v6, v6, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    move-object v1, v15

    move-object v15, v6

    .line 76
    invoke-static {}, Lcom/android/tools/r8/graph/L2;->m0()Lcom/android/tools/r8/graph/L2;

    move-result-object v16

    .line 77
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/E0;->c1()Lcom/android/tools/r8/graph/I2;

    move-result-object v17

    const/16 v18, 0x0

    .line 79
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v19

    .line 80
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v20

    .line 81
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v21

    const/16 v22, 0x0

    .line 83
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v23

    .line 84
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/E0;->P0()Lcom/android/tools/r8/graph/D3$b;

    move-result-object v24

    .line 85
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/b1;->m0()Lcom/android/tools/r8/graph/u0;

    move-result-object v25

    sget-object v27, Lcom/android/tools/r8/graph/g1;->o:[Lcom/android/tools/r8/graph/g1;

    move-object/from16 v26, v27

    .line 88
    invoke-static {v8, v1}, Lcom/android/tools/r8/graph/J4$a;->a([Lcom/android/tools/r8/graph/j1;[Lcom/android/tools/r8/graph/j1;)Lcom/android/tools/r8/graph/J4$a;

    move-result-object v28

    const/16 v29, 0x0

    .line 89
    iget-object v1, v7, Lcom/android/tools/r8/graph/E2;->z:Lcom/android/tools/r8/graph/E2$a;

    move-object/from16 v30, v1

    .line 90
    sget-object v31, Lcom/android/tools/r8/internal/j90;->b:Lcom/android/tools/r8/internal/j90;

    invoke-direct/range {v10 .. v31}, Lcom/android/tools/r8/graph/E2;-><init>(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/ProgramResource$Kind;Lcom/android/tools/r8/origin/Origin;Lcom/android/tools/r8/graph/Q;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/L2;Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/k5;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lcom/android/tools/r8/graph/f3;Ljava/util/List;Lcom/android/tools/r8/graph/D3$b;Lcom/android/tools/r8/graph/u0;[Lcom/android/tools/r8/graph/g1;[Lcom/android/tools/r8/graph/g1;Lcom/android/tools/r8/graph/J4$a;ZLcom/android/tools/r8/graph/E2$a;Lcom/android/tools/r8/internal/j90;)V

    .line 91
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 92
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/E0;->P0()Lcom/android/tools/r8/graph/D3$b;

    move-result-object v6

    const/4 v8, 0x0

    .line 93
    :goto_9
    iget-object v10, v7, Lcom/android/tools/r8/graph/E0;->h:Lcom/android/tools/r8/graph/L2;

    invoke-virtual {v10}, Lcom/android/tools/r8/graph/L2;->size()I

    move-result v10

    if-ge v8, v10, :cond_17

    .line 94
    iget-object v10, v7, Lcom/android/tools/r8/graph/E0;->h:Lcom/android/tools/r8/graph/L2;

    iget-object v10, v10, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    aget-object v10, v10, v8

    .line 95
    iget-object v11, v3, Lcom/android/tools/r8/internal/lt;->b:Ljava/util/IdentityHashMap;

    invoke-virtual {v11, v10}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_16

    if-nez v6, :cond_13

    .line 98
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v11

    goto :goto_b

    .line 101
    :cond_13
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/D3$b;->e()Ljava/util/List;

    move-result-object v11

    invoke-interface {v11, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lcom/android/tools/r8/graph/D3$c;

    .line 102
    sget-boolean v12, Lcom/android/tools/r8/internal/lt;->c:Z

    if-nez v12, :cond_15

    invoke-virtual {v11}, Lcom/android/tools/r8/graph/D3$c;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object v12

    if-ne v10, v12, :cond_14

    goto :goto_a

    :cond_14
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 103
    :cond_15
    :goto_a
    invoke-virtual {v11}, Lcom/android/tools/r8/graph/D3$c;->r()Ljava/util/List;

    move-result-object v11

    .line 105
    :goto_b
    new-instance v12, Lcom/android/tools/r8/graph/D3$c;

    iget-object v13, v3, Lcom/android/tools/r8/internal/lt;->b:Ljava/util/IdentityHashMap;

    .line 106
    invoke-virtual {v13, v10}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lcom/android/tools/r8/graph/J2;

    .line 107
    sget-object v13, Lcom/android/tools/r8/graph/D3$k;->b:Lcom/android/tools/r8/graph/D3$k;

    const/4 v14, 0x0

    invoke-direct {v12, v10, v11, v14, v13}, Lcom/android/tools/r8/graph/D3$c;-><init>(Lcom/android/tools/r8/graph/J2;Ljava/util/List;Lcom/android/tools/r8/graph/D3$c;Lcom/android/tools/r8/graph/D3$k;)V

    .line 108
    invoke-virtual {v1, v12}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_16
    add-int/lit8 v8, v8, 0x1

    goto :goto_9

    .line 109
    :cond_17
    iget-object v6, v3, Lcom/android/tools/r8/internal/lt;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v6}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v6

    .line 110
    invoke-virtual {v9, v1, v6}, Lcom/android/tools/r8/graph/E2;->a(Ljava/util/List;Lcom/android/tools/r8/graph/B1;)V

    move-object v7, v9

    .line 111
    :goto_c
    invoke-virtual {v5, v7}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    move-object/from16 v1, p1

    move-object/from16 v6, p2

    goto/16 :goto_2

    :cond_18
    move-object/from16 p2, v6

    .line 113
    invoke-virtual {v5, v7}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    move-object/from16 v1, p1

    goto/16 :goto_2

    .line 116
    :cond_19
    invoke-virtual {v2, v5}, Lcom/android/tools/r8/graph/w0;->a(Ljava/util/AbstractCollection;)Lcom/android/tools/r8/graph/w0;

    .line 117
    sget-boolean v1, Lcom/android/tools/r8/internal/nP;->a:Z

    if-nez v1, :cond_1b

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    .line 118
    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 119
    iget-boolean v1, v1, Lcom/android/tools/r8/internal/QS;->a:Z

    if-eqz v1, :cond_1a

    goto :goto_d

    .line 120
    :cond_1a
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 121
    :cond_1b
    :goto_d
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1c
    :goto_e
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_20

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/E2;

    .line 122
    iget-object v5, v3, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    .line 123
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v6

    iget-object v6, v6, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 125
    invoke-virtual {v6}, Lcom/android/tools/r8/internal/QS;->m()Ljava/util/Map;

    move-result-object v6

    .line 126
    invoke-interface {v6, v5}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v5

    .line 127
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/E0;->R0()Lcom/android/tools/r8/graph/f3;

    move-result-object v6

    if-eqz v6, :cond_1d

    .line 128
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/E0;->R0()Lcom/android/tools/r8/graph/f3;

    move-result-object v6

    .line 129
    iget-object v6, v6, Lcom/android/tools/r8/graph/f3;->a:Lcom/android/tools/r8/graph/J2;

    if-eqz v6, :cond_1d

    .line 130
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v7

    iget-object v7, v7, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 132
    invoke-virtual {v7}, Lcom/android/tools/r8/internal/QS;->m()Ljava/util/Map;

    move-result-object v7

    .line 133
    invoke-interface {v7, v6}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v6

    if-eq v6, v5, :cond_1d

    const/4 v6, 0x0

    .line 134
    iput-object v6, v3, Lcom/android/tools/r8/graph/E0;->m:Lcom/android/tools/r8/graph/f3;

    goto :goto_f

    :cond_1d
    const/4 v6, 0x0

    .line 135
    :goto_f
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/E0;->T0()Ljava/util/List;

    move-result-object v7

    invoke-interface {v7}, Ljava/util/List;->isEmpty()Z

    move-result v7

    if-nez v7, :cond_1c

    .line 136
    new-instance v7, Ljava/util/ArrayList;

    invoke-direct {v7}, Ljava/util/ArrayList;-><init>()V

    .line 137
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/E0;->T0()Ljava/util/List;

    move-result-object v8

    invoke-interface {v8}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v8

    :cond_1e
    :goto_10
    invoke-interface {v8}, Ljava/util/Iterator;->hasNext()Z

    move-result v9

    if-eqz v9, :cond_1f

    invoke-interface {v8}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lcom/android/tools/r8/graph/a4;

    .line 138
    invoke-virtual {v9}, Lcom/android/tools/r8/graph/a4;->b()Lcom/android/tools/r8/graph/J2;

    move-result-object v10

    .line 139
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v11

    iget-object v11, v11, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 141
    invoke-virtual {v11}, Lcom/android/tools/r8/internal/QS;->m()Ljava/util/Map;

    move-result-object v11

    .line 142
    invoke-interface {v11, v10}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v10

    if-ne v10, v5, :cond_1e

    .line 143
    invoke-virtual {v7, v9}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_10

    .line 146
    :cond_1f
    invoke-virtual {v7}, Ljava/util/ArrayList;->size()I

    move-result v5

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/E0;->T0()Ljava/util/List;

    move-result-object v8

    invoke-interface {v8}, Ljava/util/List;->size()I

    move-result v8

    if-eq v5, v8, :cond_1c

    .line 147
    iput-object v7, v3, Lcom/android/tools/r8/graph/E0;->n:Ljava/util/List;

    goto/16 :goto_e

    .line 148
    :cond_20
    iget-object v1, v0, Lcom/android/tools/r8/internal/fA;->q:Lcom/android/tools/r8/internal/Mh;

    if-eqz v1, :cond_24

    .line 149
    instance-of v3, v4, Lcom/android/tools/r8/internal/cZ;

    if-eqz v3, :cond_21

    .line 150
    sget-object v3, Lcom/android/tools/r8/internal/Nh;->a:Lcom/android/tools/r8/internal/Nh;

    goto :goto_11

    .line 153
    :cond_21
    sget-object v5, Lcom/android/tools/r8/internal/Nh;->a:Lcom/android/tools/r8/internal/Nh;

    if-eqz v3, :cond_22

    move-object v3, v5

    goto :goto_11

    .line 154
    :cond_22
    new-instance v3, Lcom/android/tools/r8/internal/R50;

    .line 155
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/L50;->a()Lcom/android/tools/r8/internal/Yf;

    move-result-object v5

    invoke-direct {v3, v5}, Lcom/android/tools/r8/internal/R50;-><init>(Lcom/android/tools/r8/internal/Yf;)V

    .line 156
    :goto_11
    new-instance v5, Ljava/util/LinkedList;

    invoke-direct {v5}, Ljava/util/LinkedList;-><init>()V

    .line 157
    new-instance v6, Ljava/util/LinkedList;

    invoke-direct {v6}, Ljava/util/LinkedList;-><init>()V

    .line 158
    iget-object v7, v2, Lcom/android/tools/r8/graph/w0;->a:Ljava/util/ArrayList;

    .line 159
    invoke-virtual {v7}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v7

    :goto_12
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    move-result v8

    if-eqz v8, :cond_24

    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lcom/android/tools/r8/graph/E2;

    .line 161
    invoke-virtual {v1, v8, v5, v6, v3}, Lcom/android/tools/r8/internal/Mh;->a(Lcom/android/tools/r8/graph/E2;Ljava/util/LinkedList;Ljava/util/LinkedList;Lcom/android/tools/r8/internal/Oh;)V

    .line 166
    invoke-interface {v6}, Ljava/util/List;->isEmpty()Z

    move-result v9

    if-eqz v9, :cond_23

    goto :goto_12

    .line 169
    :cond_23
    invoke-virtual {v1, v8, v5, v6}, Lcom/android/tools/r8/internal/Mh;->a(Lcom/android/tools/r8/graph/E2;Ljava/util/LinkedList;Ljava/util/LinkedList;)V

    .line 172
    invoke-virtual {v5}, Ljava/util/LinkedList;->clear()V

    .line 173
    invoke-virtual {v6}, Ljava/util/LinkedList;->clear()V

    goto :goto_12

    .line 174
    :cond_24
    iget-object v1, v0, Lcom/android/tools/r8/internal/s50;->J:Lcom/android/tools/r8/internal/Gp0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Gp0;->b()V

    .line 176
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/w0;->b()Lcom/android/tools/r8/graph/x0;

    move-result-object v1

    .line 177
    new-instance v2, Lcom/android/tools/r8/graph/h;

    .line 179
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/h;->g()Lcom/android/tools/r8/synthesis/I;

    move-result-object v3

    invoke-virtual {v3, v1}, Lcom/android/tools/r8/synthesis/I;->a(Lcom/android/tools/r8/graph/x0;)Lcom/android/tools/r8/synthesis/a;

    move-result-object v1

    .line 180
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/h;->f()Lcom/android/tools/r8/shaking/b2;

    move-result-object v3

    invoke-direct {v2, v1, v3}, Lcom/android/tools/r8/graph/h;-><init>(Lcom/android/tools/r8/synthesis/a;Lcom/android/tools/r8/shaking/b2;)V

    move-object/from16 v1, p1

    .line 181
    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/y;->b(Lcom/android/tools/r8/graph/h;)Lcom/android/tools/r8/graph/y;

    .line 186
    invoke-virtual {v4, v1}, Lcom/android/tools/r8/internal/L50;->a(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method

.method public final synthetic a(Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/Y50;Lcom/android/tools/r8/graph/D5;)V
    .locals 1

    .line 237
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    invoke-virtual {v0, p1, p2, p3}, Lcom/android/tools/r8/internal/J9;->a(Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/Y50;Lcom/android/tools/r8/graph/D5;)V

    return-void
.end method

.method public final synthetic a(Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/Y50;Lcom/android/tools/r8/graph/E2;)V
    .locals 2

    .line 235
    sget-object v0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda8;->INSTANCE:Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda8;

    new-instance v1, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda4;

    invoke-direct {v1, p0, p1, p2}, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda4;-><init>(Lcom/android/tools/r8/internal/s50;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/Y50;)V

    invoke-virtual {p3, v1, v0}, Lcom/android/tools/r8/graph/E2;->h(Ljava/util/function/Consumer;Ljava/util/function/Predicate;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/M9;Ljava/util/concurrent/ExecutorService;)V
    .locals 4

    .line 220
    new-instance v0, Lcom/android/tools/r8/internal/Y50;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Y50;-><init>()V

    .line 221
    iget-object v1, p0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/utils/w;->O()Lcom/android/tools/r8/threading/ThreadingModule;

    move-result-object v1

    .line 222
    iget-object v2, p0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    .line 223
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v2

    new-instance v3, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda5;

    invoke-direct {v3, p0, p1, v0}, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda5;-><init>(Lcom/android/tools/r8/internal/s50;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/Y50;)V

    .line 224
    invoke-static {v2, v3, v1, p2}, Lcom/android/tools/r8/internal/ep0;->a(Ljava/util/Collection;Ljava/util/function/Consumer;Lcom/android/tools/r8/threading/ThreadingModule;Ljava/util/concurrent/ExecutorService;)V

    .line 234
    invoke-virtual {v0, v1, p2}, Lcom/android/tools/r8/internal/Y50;->a(Lcom/android/tools/r8/threading/ThreadingModule;Ljava/util/concurrent/ExecutorService;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/ei;Lcom/android/tools/r8/internal/sH;Ljava/util/concurrent/ExecutorService;)V
    .locals 2

    .line 192
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    .line 193
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/utils/w;->s()Lcom/android/tools/r8/utils/w$f;

    move-result-object v1

    iget-boolean v1, v1, Lcom/android/tools/r8/utils/w$f;->b:Z

    if-eqz v1, :cond_0

    .line 194
    new-instance v1, Lcom/android/tools/r8/internal/jc;

    invoke-direct {v1, v0, p0, p1, p2}, Lcom/android/tools/r8/internal/jc;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/s50;Lcom/android/tools/r8/internal/ei;Lcom/android/tools/r8/internal/sH;)V

    goto :goto_0

    .line 196
    :cond_0
    new-instance v1, Lcom/android/tools/r8/internal/ic;

    invoke-direct {v1, v0, p0, p1, p2}, Lcom/android/tools/r8/internal/ic;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/s50;Lcom/android/tools/r8/internal/ei;Lcom/android/tools/r8/internal/sH;)V

    .line 197
    :goto_0
    new-instance p2, Lcom/android/tools/r8/internal/lc;

    invoke-direct {p2}, Lcom/android/tools/r8/internal/lc;-><init>()V

    .line 198
    invoke-virtual {v1, p2, p3}, Lcom/android/tools/r8/internal/kc;->a(Lcom/android/tools/r8/internal/lc;Ljava/util/concurrent/ExecutorService;)V

    .line 199
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/kc;->a()V

    .line 200
    iget-object p2, p2, Lcom/android/tools/r8/internal/lc;->a:Ljava/util/IdentityHashMap;

    .line 201
    invoke-virtual {p0, p1, p3}, Lcom/android/tools/r8/internal/s50;->a(Lcom/android/tools/r8/internal/ei;Ljava/util/concurrent/ExecutorService;)V

    .line 202
    sget-boolean p3, Lcom/android/tools/r8/internal/ei;->h:Z

    if-nez p3, :cond_2

    iget-object v0, p1, Lcom/android/tools/r8/internal/ei;->e:Lcom/android/tools/r8/internal/Go0;

    .line 203
    iget-object v0, v0, Lcom/android/tools/r8/internal/Ro0;->c:Ljava/util/ArrayList;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_1

    goto :goto_1

    .line 204
    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_2
    :goto_1
    if-nez p3, :cond_4

    .line 205
    iget-object p1, p1, Lcom/android/tools/r8/internal/ei;->f:Lcom/android/tools/r8/internal/Go0;

    .line 206
    iget-object p1, p1, Lcom/android/tools/r8/internal/Ro0;->c:Ljava/util/ArrayList;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    if-nez p1, :cond_3

    goto :goto_2

    .line 207
    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 208
    :cond_4
    :goto_2
    iget-object p1, p0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    .line 209
    invoke-virtual {p2}, Ljava/util/IdentityHashMap;->isEmpty()Z

    move-result p3

    if-eqz p3, :cond_5

    goto :goto_4

    .line 212
    :cond_5
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_6
    :goto_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_7

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/graph/E2;

    .line 213
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/E0;->h1()Z

    move-result v0

    if-eqz v0, :cond_6

    .line 214
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/E0;->R0()Lcom/android/tools/r8/graph/f3;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/f3;->b()Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    .line 215
    invoke-virtual {p2, v0}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    if-eqz v0, :cond_6

    .line 217
    new-instance v1, Lcom/android/tools/r8/graph/f3;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/graph/f3;-><init>(Lcom/android/tools/r8/graph/x2;)V

    .line 218
    iput-object v1, p3, Lcom/android/tools/r8/graph/E0;->m:Lcom/android/tools/r8/graph/f3;

    goto :goto_3

    .line 219
    :cond_7
    :goto_4
    iget-object p1, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    sget-object p2, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda6;->INSTANCE:Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda6;

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/J9;->a(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/ei;Ljava/util/concurrent/ExecutorService;)V
    .locals 2

    .line 277
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    new-instance v1, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda1;

    invoke-direct {v1, p1, p2}, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/ei;Ljava/util/concurrent/ExecutorService;)V

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/J9;->a(Lcom/android/tools/r8/internal/op0;)V

    .line 281
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ei;->h()V

    return-void
.end method

.method public final b()V
    .locals 2

    .line 18
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    sget-object v1, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda3;->INSTANCE:Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda3;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/J9;->a(Lcom/android/tools/r8/internal/op0;)V

    return-void
.end method

.method public final b(Lcom/android/tools/r8/internal/ei;Lcom/android/tools/r8/internal/sH;Ljava/util/concurrent/ExecutorService;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    .line 2
    iget-object v1, p1, Lcom/android/tools/r8/internal/ei;->a:Lcom/android/tools/r8/internal/L50;

    .line 3
    iget-object v2, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    .line 4
    new-instance v3, Lcom/android/tools/r8/internal/ya;

    invoke-direct {v3, p1, v2}, Lcom/android/tools/r8/internal/ya;-><init>(Lcom/android/tools/r8/internal/ei;Lcom/android/tools/r8/internal/J9;)V

    .line 5
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    instance-of v2, v1, Lcom/android/tools/r8/internal/cZ;

    if-eqz v2, :cond_0

    goto :goto_0

    .line 7
    :cond_0
    new-instance v2, Lcom/android/tools/r8/internal/Q50;

    .line 8
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/L50;->a()Lcom/android/tools/r8/internal/Yf;

    move-result-object v1

    .line 9
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/utils/w;->x()Lcom/android/tools/r8/internal/q4;

    move-result-object v0

    invoke-direct {v2, v1, v0, v3}, Lcom/android/tools/r8/internal/Q50;-><init>(Lcom/android/tools/r8/internal/Yf;Lcom/android/tools/r8/internal/q4;Lcom/android/tools/r8/internal/Aa;)V

    move-object v3, v2

    .line 10
    :goto_0
    iget-object v0, p1, Lcom/android/tools/r8/internal/ei;->b:Lcom/android/tools/r8/internal/s50;

    iget-object v0, v0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->n()Lcom/android/tools/r8/internal/ff;

    move-result-object v0

    iput-object v0, p1, Lcom/android/tools/r8/internal/ei;->g:Lcom/android/tools/r8/internal/ff;

    .line 11
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->e:Lcom/android/tools/r8/internal/J9;

    .line 12
    invoke-virtual {v0, p2}, Lcom/android/tools/r8/internal/J9;->a(Lcom/android/tools/r8/internal/sH;)Lcom/android/tools/r8/internal/mH;

    move-result-object p2

    .line 14
    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    sget-object v1, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda7;->INSTANCE:Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda7;

    invoke-static {v0, p2, v1}, Lcom/android/tools/r8/internal/xa;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/mH;Ljava/util/function/Predicate;)Lcom/android/tools/r8/internal/xa;

    move-result-object p2

    iget-object v0, p0, Lcom/android/tools/r8/internal/fA;->a:Lcom/android/tools/r8/graph/y;

    .line 15
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v0

    invoke-virtual {p2, v0, v3, p3}, Lcom/android/tools/r8/internal/xa;->a(Ljava/util/Collection;Lcom/android/tools/r8/internal/Aa;Ljava/util/concurrent/ExecutorService;)V

    .line 16
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ei;->h()V

    .line 17
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Aa;->a()V

    return-void
.end method
