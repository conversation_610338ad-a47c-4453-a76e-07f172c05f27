.class public final Lcom/android/tools/r8/internal/t10;
.super Lcom/android/tools/r8/internal/g1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/io/Serializable;


# instance fields
.field public transient b:[Ljava/lang/Object;

.field public c:I


# direct methods
.method public constructor <init>(I[Ljava/lang/Object;)V
    .locals 3

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/g1;-><init>()V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    .line 3
    iput p1, p0, Lcom/android/tools/r8/internal/t10;->c:I

    .line 4
    array-length v0, p2

    if-gt p1, v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "The provided size ("

    const-string v2, ") is larger than or equal to the array size ("

    .line 5
    invoke-static {p1, v1, v2}, Lcom/android/tools/r8/internal/Nq0;->a(ILjava/lang/String;Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 82
    array-length p2, p2

    const-string v1, ")"

    .line 83
    invoke-static {p2, v1, p1}, Lcom/android/tools/r8/internal/dj0;->a(ILjava/lang/String;Ljava/lang/StringBuilder;)Ljava/lang/String;

    move-result-object p1

    .line 160
    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method


# virtual methods
.method public final add(Ljava/lang/Object;)Z
    .locals 3

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t10;->c(Ljava/lang/Object;)I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    const/4 p1, 0x0

    return p1

    .line 3
    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/t10;->c:I

    iget-object v1, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    array-length v1, v1

    if-ne v0, v1, :cond_3

    if-nez v0, :cond_1

    const/4 v1, 0x2

    goto :goto_0

    :cond_1
    mul-int/lit8 v1, v0, 0x2

    .line 4
    :goto_0
    new-array v1, v1, [Ljava/lang/Object;

    :goto_1
    add-int/lit8 v2, v0, -0x1

    if-eqz v0, :cond_2

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    aget-object v0, v0, v2

    aput-object v0, v1, v2

    move v0, v2

    goto :goto_1

    .line 6
    :cond_2
    iput-object v1, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    .line 8
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    iget v1, p0, Lcom/android/tools/r8/internal/t10;->c:I

    add-int/lit8 v2, v1, 0x1

    iput v2, p0, Lcom/android/tools/r8/internal/t10;->c:I

    aput-object p1, v0, v1

    const/4 p1, 0x1

    return p1
.end method

.method public final c(Ljava/lang/Object;)I
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/t10;->c:I

    :goto_0
    add-int/lit8 v1, v0, -0x1

    if-eqz v0, :cond_2

    iget-object v0, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    aget-object v0, v0, v1

    if-nez v0, :cond_0

    if-nez p1, :cond_1

    goto :goto_1

    :cond_0
    invoke-virtual {v0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    :goto_1
    return v1

    :cond_1
    move v0, v1

    goto :goto_0

    :cond_2
    const/4 p1, -0x1

    return p1
.end method

.method public final clear()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    iget v1, p0, Lcom/android/tools/r8/internal/t10;->c:I

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-static {v0, v2, v1, v3}, Ljava/util/Arrays;->fill([Ljava/lang/Object;IILjava/lang/Object;)V

    .line 2
    iput v2, p0, Lcom/android/tools/r8/internal/t10;->c:I

    return-void
.end method

.method public final clone()Ljava/lang/Object;
    .locals 2

    .line 1
    :try_start_0
    invoke-super {p0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/t10;
    :try_end_0
    .catch Ljava/lang/CloneNotSupportedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    invoke-virtual {v1}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Ljava/lang/Object;

    iput-object v1, v0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    return-object v0

    .line 7
    :catch_0
    new-instance v0, Ljava/lang/InternalError;

    invoke-direct {v0}, Ljava/lang/InternalError;-><init>()V

    throw v0
.end method

.method public final contains(Ljava/lang/Object;)Z
    .locals 1

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t10;->c(Ljava/lang/Object;)I

    move-result p1

    const/4 v0, -0x1

    if-eq p1, v0, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final isEmpty()Z
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/t10;->c:I

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final iterator()Lcom/android/tools/r8/internal/B10;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/s10;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/s10;-><init>(Lcom/android/tools/r8/internal/t10;)V

    return-object v0
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/s10;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/s10;-><init>(Lcom/android/tools/r8/internal/t10;)V

    return-object v0
.end method

.method public final remove(Ljava/lang/Object;)Z
    .locals 6

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/t10;->c(Ljava/lang/Object;)I

    move-result p1

    const/4 v0, 0x0

    const/4 v1, -0x1

    if-ne p1, v1, :cond_0

    return v0

    .line 3
    :cond_0
    iget v1, p0, Lcom/android/tools/r8/internal/t10;->c:I

    sub-int/2addr v1, p1

    const/4 v2, 0x1

    sub-int/2addr v1, v2

    :goto_0
    if-ge v0, v1, :cond_1

    .line 4
    iget-object v3, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    add-int v4, p1, v0

    add-int/lit8 v5, v4, 0x1

    aget-object v5, v3, v5

    aput-object v5, v3, v4

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 5
    :cond_1
    iget p1, p0, Lcom/android/tools/r8/internal/t10;->c:I

    sub-int/2addr p1, v2

    iput p1, p0, Lcom/android/tools/r8/internal/t10;->c:I

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    const/4 v1, 0x0

    aput-object v1, v0, p1

    return v2
.end method

.method public final size()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/t10;->c:I

    return v0
.end method
