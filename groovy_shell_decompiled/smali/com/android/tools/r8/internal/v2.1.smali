.class public final Lcom/android/tools/r8/internal/v2;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final c:Lcom/android/tools/r8/internal/v2;

.field public static final synthetic d:Z


# instance fields
.field public final a:I

.field public final b:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/w2;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/v2;->d:Z

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/v2;

    const/4 v1, 0x0

    invoke-direct {v0, v1, v1}, Lcom/android/tools/r8/internal/v2;-><init>(II)V

    sput-object v0, Lcom/android/tools/r8/internal/v2;->c:Lcom/android/tools/r8/internal/v2;

    return-void
.end method

.method public constructor <init>(II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Lcom/android/tools/r8/internal/v2;->a:I

    .line 3
    iput p2, p0, Lcom/android/tools/r8/internal/v2;->b:I

    return-void
.end method
