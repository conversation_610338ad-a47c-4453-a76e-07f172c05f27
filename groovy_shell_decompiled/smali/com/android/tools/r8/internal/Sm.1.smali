.class public final Lcom/android/tools/r8/internal/Sm;
.super Lcom/android/tools/r8/internal/Rm;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z


# instance fields
.field public e:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/Wm;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/Sm;->f:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/pW;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Rm;-><init>(Lcom/android/tools/r8/internal/rD;)V

    const/4 p1, -0x1

    .line 2
    iput p1, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Wm;)I
    .locals 3

    .line 102
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Sm;->c(Lcom/android/tools/r8/internal/Wm;)I

    move-result v0

    .line 103
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Sm;->b(Lcom/android/tools/r8/internal/Wm;)I

    move-result p1

    const/4 v1, 0x1

    if-ne v0, p1, :cond_0

    .line 105
    iput v1, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    goto :goto_0

    :cond_0
    const/16 v2, 0xf

    if-gt v0, v2, :cond_1

    if-gt p1, v2, :cond_1

    .line 107
    iput v1, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    goto :goto_0

    :cond_1
    const/16 v0, 0xff

    if-gt p1, v0, :cond_2

    const/4 p1, 0x2

    .line 109
    iput p1, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    goto :goto_0

    :cond_2
    const/4 p1, 0x3

    .line 111
    iput p1, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    .line 113
    :goto_0
    iget p1, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    return p1
.end method

.method public final a(Lcom/android/tools/r8/internal/Wm;Ljava/util/ArrayList;)V
    .locals 6

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Rm;->a:Lcom/android/tools/r8/internal/rD;

    .line 2
    check-cast v0, Lcom/android/tools/r8/internal/pW;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->a()Lcom/android/tools/r8/internal/sr0;

    move-result-object v1

    .line 4
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Sm;->c(Lcom/android/tools/r8/internal/Wm;)I

    move-result v2

    .line 5
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Sm;->b(Lcom/android/tools/r8/internal/Wm;)I

    move-result p1

    .line 7
    iget v3, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    const-string v4, "Unexpected type: "

    const/4 v5, 0x1

    if-eq v3, v5, :cond_8

    const/4 v5, 0x2

    if-eq v3, v5, :cond_4

    const/4 v5, 0x3

    if-ne v3, v5, :cond_3

    .line 35
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->K()Z

    move-result v3

    if-eqz v3, :cond_0

    .line 36
    new-instance v0, Lcom/android/tools/r8/internal/Qp;

    invoke-direct {v0, p1, v2}, Lcom/android/tools/r8/internal/Qp;-><init>(II)V

    goto/16 :goto_0

    .line 37
    :cond_0
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->M()Z

    move-result v3

    if-eqz v3, :cond_1

    .line 38
    new-instance v0, Lcom/android/tools/r8/internal/aq;

    invoke-direct {v0, p1, v2}, Lcom/android/tools/r8/internal/aq;-><init>(II)V

    goto/16 :goto_0

    .line 39
    :cond_1
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 40
    new-instance v0, Lcom/android/tools/r8/internal/Up;

    invoke-direct {v0, p1, v2}, Lcom/android/tools/r8/internal/Up;-><init>(II)V

    goto/16 :goto_0

    .line 42
    :cond_2
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->I2()Lcom/android/tools/r8/internal/It0;

    move-result-object p2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1

    .line 46
    :cond_3
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    iget p2, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Unexpected size: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1

    .line 47
    :cond_4
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->K()Z

    move-result v3

    if-eqz v3, :cond_5

    .line 48
    new-instance v0, Lcom/android/tools/r8/internal/Tp;

    invoke-direct {v0, p1, v2}, Lcom/android/tools/r8/internal/Tp;-><init>(II)V

    goto :goto_0

    .line 49
    :cond_5
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->M()Z

    move-result v3

    if-eqz v3, :cond_6

    .line 50
    new-instance v0, Lcom/android/tools/r8/internal/cq;

    invoke-direct {v0, p1, v2}, Lcom/android/tools/r8/internal/cq;-><init>(II)V

    goto :goto_0

    .line 51
    :cond_6
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v1

    if-eqz v1, :cond_7

    .line 52
    new-instance v0, Lcom/android/tools/r8/internal/Wp;

    invoke-direct {v0, p1, v2}, Lcom/android/tools/r8/internal/Wp;-><init>(II)V

    goto :goto_0

    .line 54
    :cond_7
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->I2()Lcom/android/tools/r8/internal/It0;

    move-result-object p2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_8
    if-ne v2, p1, :cond_9

    .line 55
    new-instance v0, Lcom/android/tools/r8/internal/uq;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/uq;-><init>()V

    goto :goto_0

    .line 58
    :cond_9
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->K()Z

    move-result v3

    if-eqz v3, :cond_a

    .line 59
    new-instance v0, Lcom/android/tools/r8/internal/Rp;

    invoke-direct {v0, p1, v2}, Lcom/android/tools/r8/internal/Rp;-><init>(II)V

    goto :goto_0

    .line 60
    :cond_a
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->M()Z

    move-result v3

    if-eqz v3, :cond_b

    .line 61
    new-instance v0, Lcom/android/tools/r8/internal/bq;

    invoke-direct {v0, p1, v2}, Lcom/android/tools/r8/internal/bq;-><init>(II)V

    goto :goto_0

    .line 62
    :cond_b
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v1

    if-eqz v1, :cond_c

    .line 63
    new-instance v0, Lcom/android/tools/r8/internal/Vp;

    invoke-direct {v0, p1, v2}, Lcom/android/tools/r8/internal/Vp;-><init>(II)V

    .line 93
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Rm;->b()I

    move-result p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Yo;->g(I)V

    .line 94
    invoke-virtual {p2, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void

    .line 95
    :cond_c
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->I2()Lcom/android/tools/r8/internal/It0;

    move-result-object p2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/Rm;Lcom/android/tools/r8/internal/Wm;)Z
    .locals 3

    .line 96
    instance-of v0, p1, Lcom/android/tools/r8/internal/Sm;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 99
    :cond_0
    check-cast p1, Lcom/android/tools/r8/internal/Sm;

    .line 100
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/Sm;->c(Lcom/android/tools/r8/internal/Wm;)I

    move-result v0

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/Sm;->c(Lcom/android/tools/r8/internal/Wm;)I

    move-result v2

    if-ne v0, v2, :cond_1

    .line 101
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/Sm;->b(Lcom/android/tools/r8/internal/Wm;)I

    move-result v0

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/Sm;->b(Lcom/android/tools/r8/internal/Wm;)I

    move-result p1

    if-ne v0, p1, :cond_1

    const/4 v1, 0x1

    :cond_1
    return v1
.end method

.method public final b(Lcom/android/tools/r8/internal/Wm;)I
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Rm;->a:Lcom/android/tools/r8/internal/rD;

    .line 2
    check-cast v0, Lcom/android/tools/r8/internal/pW;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/pW;->K2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/Rm;->a:Lcom/android/tools/r8/internal/rD;

    .line 5
    check-cast v1, Lcom/android/tools/r8/internal/pW;

    .line 6
    iget v1, v1, Lcom/android/tools/r8/internal/rD;->e:I

    .line 7
    iget-object p1, p1, Lcom/android/tools/r8/internal/Wm;->d:Lcom/android/tools/r8/internal/ic0;

    .line 8
    invoke-interface {p1, v0, v1}, Lcom/android/tools/r8/internal/ic0;->b(Lcom/android/tools/r8/internal/vt0;I)I

    move-result p1

    return p1
.end method

.method public final c()I
    .locals 1

    .line 9
    sget-boolean v0, Lcom/android/tools/r8/internal/Sm;->f:Z

    if-nez v0, :cond_1

    iget v0, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    if-lez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 10
    :cond_1
    :goto_0
    iget v0, p0, Lcom/android/tools/r8/internal/Sm;->e:I

    return v0
.end method

.method public final c(Lcom/android/tools/r8/internal/Wm;)I
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Rm;->a:Lcom/android/tools/r8/internal/rD;

    .line 2
    check-cast v0, Lcom/android/tools/r8/internal/pW;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/pW;->L2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/Rm;->a:Lcom/android/tools/r8/internal/rD;

    .line 5
    check-cast v1, Lcom/android/tools/r8/internal/pW;

    .line 6
    iget v1, v1, Lcom/android/tools/r8/internal/rD;->e:I

    .line 7
    iget-object p1, p1, Lcom/android/tools/r8/internal/Wm;->d:Lcom/android/tools/r8/internal/ic0;

    .line 8
    invoke-interface {p1, v0, v1}, Lcom/android/tools/r8/internal/ic0;->a(Lcom/android/tools/r8/internal/vt0;I)I

    move-result p1

    return p1
.end method

.method public final d()I
    .locals 2

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/Sm;->f:Z

    if-nez v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/Qp;

    const/4 v1, 0x0

    invoke-direct {v0, v1, v1}, Lcom/android/tools/r8/internal/Qp;-><init>(II)V

    :cond_0
    const/4 v0, 0x3

    return v0
.end method

.method public final e()I
    .locals 2

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/Sm;->f:Z

    if-nez v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/uq;

    new-instance v0, Lcom/android/tools/r8/internal/Rp;

    const/4 v1, 0x0

    invoke-direct {v0, v1, v1}, Lcom/android/tools/r8/internal/Rp;-><init>(II)V

    :cond_0
    const/4 v0, 0x1

    return v0
.end method
