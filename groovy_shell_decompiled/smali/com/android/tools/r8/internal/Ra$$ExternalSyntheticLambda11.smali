.class public final synthetic Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda11;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/kq0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/A8;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/A8;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda11;->f$0:Lcom/android/tools/r8/internal/A8;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda11;->f$0:Lcom/android/tools/r8/internal/A8;

    check-cast p1, Lcom/android/tools/r8/internal/r9;

    check-cast p2, Lcom/android/tools/r8/internal/R40;

    check-cast p3, Lcom/android/tools/r8/internal/R40;

    invoke-static {v0, p1, p2, p3}, Lcom/android/tools/r8/internal/Ra;->b(Lcom/android/tools/r8/internal/A8;Lcom/android/tools/r8/internal/r9;Lcom/android/tools/r8/internal/R40;Lcom/android/tools/r8/internal/R40;)Lcom/android/tools/r8/internal/r9;

    move-result-object p1

    return-object p1
.end method
