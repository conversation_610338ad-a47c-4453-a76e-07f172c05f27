.class public Lcom/android/tools/r8/internal/VC;
.super Lcom/android/tools/r8/internal/sv;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/pv;
.implements Lcom/android/tools/r8/internal/QC;


# static fields
.field public static final synthetic k:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)V
    .locals 0

    .line 1
    invoke-static {p3}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p3

    invoke-direct {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/sv;-><init>(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/internal/vt0;Ljava/util/List;)V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/VC;)Lcom/android/tools/r8/internal/VC;
    .locals 3

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->a()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->o()Lcom/android/tools/r8/graph/j0;

    move-result-object v1

    invoke-virtual {p0, v0, v1}, Lcom/android/tools/r8/internal/aA;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/j0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object p0

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/VC;->k:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    if-eq p0, v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 3
    :cond_1
    :goto_0
    new-instance v0, Lcom/android/tools/r8/internal/UC;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/UC;-><init>()V

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v1

    .line 5
    iput-object v1, v0, Lcom/android/tools/r8/internal/UC;->d:Lcom/android/tools/r8/graph/l1;

    .line 6
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/VC;->m()Lcom/android/tools/r8/internal/vt0;

    move-result-object p1

    .line 7
    iput-object p0, v0, Lcom/android/tools/r8/internal/kD;->a:Lcom/android/tools/r8/internal/vt0;

    .line 8
    new-instance v1, Lcom/android/tools/r8/internal/VC;

    iget-object v2, v0, Lcom/android/tools/r8/internal/UC;->d:Lcom/android/tools/r8/graph/l1;

    invoke-direct {v1, v2, p0, p1}, Lcom/android/tools/r8/internal/VC;-><init>(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)V

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/kD;->a(Lcom/android/tools/r8/internal/rD;)Lcom/android/tools/r8/internal/rD;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/internal/VC;

    return-object p0
.end method


# virtual methods
.method public final F2()I
    .locals 1

    const/16 v0, 0xf

    return v0
.end method

.method public final G2()I
    .locals 1

    const/16 v0, 0xf

    return v0
.end method

.method public final H2()I
    .locals 1

    const/16 v0, 0x1c

    return v0
.end method

.method public final J2()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final P()Lcom/android/tools/r8/internal/pv;
    .locals 0

    return-object p0
.end method

.method public final V()Lcom/android/tools/r8/internal/QC;
    .locals 0

    return-object p0
.end method

.method public final W0()Lcom/android/tools/r8/internal/vt0;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/VC;->m()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Pr0;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 90
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;
    .locals 2

    .line 89
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/ir/optimize/X;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/ir/optimize/O;
    .locals 1

    .line 86
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v0

    .line 87
    invoke-virtual {p1, v0, p2}, Lcom/android/tools/r8/ir/optimize/X;->a(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/ir/optimize/O;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/tC;)Ljava/lang/Object;
    .locals 0

    .line 88
    invoke-virtual {p1, p0}, Lcom/android/tools/r8/internal/tC;->a(Lcom/android/tools/r8/internal/sv;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/N5;Lcom/android/tools/r8/internal/gS;)V
    .locals 0

    .line 91
    invoke-virtual {p2, p0, p1}, Lcom/android/tools/r8/internal/gS;->a(Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/internal/uD;)V

    .line 92
    invoke-virtual {p2, p0, p1}, Lcom/android/tools/r8/internal/gS;->b(Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/internal/uD;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/R8;)V
    .locals 4

    .line 93
    new-instance v0, Lcom/android/tools/r8/internal/E9;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v2

    .line 94
    iget-object v3, p1, Lcom/android/tools/r8/internal/R8;->a:Lcom/android/tools/r8/graph/y;

    .line 95
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/y;->h()Lcom/android/tools/r8/graph/j;

    move-result-object v3

    invoke-virtual {v3, v2}, Lcom/android/tools/r8/graph/j;->c(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/z3;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/z3;->q()Lcom/android/tools/r8/graph/g1;

    move-result-object v3

    if-nez v3, :cond_0

    goto :goto_0

    .line 96
    :cond_0
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/l1;

    .line 97
    :goto_0
    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/E9;-><init>(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/l1;)V

    invoke-virtual {p1, v0, p0}, Lcom/android/tools/r8/internal/R8;->a(Lcom/android/tools/r8/internal/H9;Lcom/android/tools/r8/internal/rD;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/Wm;)V
    .locals 4

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/internal/rD;->b:Lcom/android/tools/r8/internal/vt0;

    .line 11
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->X0()I

    move-result v1

    invoke-virtual {p1, v0, v1}, Lcom/android/tools/r8/internal/Wm;->a(Lcom/android/tools/r8/internal/vt0;I)I

    move-result v0

    .line 12
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/VC;->m()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->X0()I

    move-result v2

    invoke-virtual {p1, v1, v2}, Lcom/android/tools/r8/internal/Wm;->a(Lcom/android/tools/r8/internal/vt0;I)I

    move-result v1

    .line 14
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v2

    .line 15
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->K2()Lcom/android/tools/r8/internal/uv;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    move-result v3

    packed-switch v3, :pswitch_data_0

    .line 40
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->K2()Lcom/android/tools/r8/internal/uv;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Unexpected type: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1

    .line 41
    :pswitch_0
    new-instance v3, Lcom/android/tools/r8/internal/Wo;

    invoke-direct {v3, v0, v1, v2}, Lcom/android/tools/r8/internal/Wo;-><init>(IILcom/android/tools/r8/graph/l1;)V

    goto :goto_0

    .line 42
    :pswitch_1
    new-instance v3, Lcom/android/tools/r8/internal/Po;

    invoke-direct {v3, v0, v1, v2}, Lcom/android/tools/r8/internal/Po;-><init>(IILcom/android/tools/r8/graph/l1;)V

    goto :goto_0

    .line 61
    :pswitch_2
    new-instance v3, Lcom/android/tools/r8/internal/Vo;

    invoke-direct {v3, v0, v1, v2}, Lcom/android/tools/r8/internal/Vo;-><init>(IILcom/android/tools/r8/graph/l1;)V

    goto :goto_0

    .line 62
    :pswitch_3
    new-instance v3, Lcom/android/tools/r8/internal/So;

    invoke-direct {v3, v0, v1, v2}, Lcom/android/tools/r8/internal/So;-><init>(IILcom/android/tools/r8/graph/l1;)V

    goto :goto_0

    .line 63
    :pswitch_4
    new-instance v3, Lcom/android/tools/r8/internal/Ro;

    invoke-direct {v3, v0, v1, v2}, Lcom/android/tools/r8/internal/Ro;-><init>(IILcom/android/tools/r8/graph/l1;)V

    goto :goto_0

    .line 64
    :pswitch_5
    new-instance v3, Lcom/android/tools/r8/internal/Qo;

    invoke-direct {v3, v0, v1, v2}, Lcom/android/tools/r8/internal/Qo;-><init>(IILcom/android/tools/r8/graph/l1;)V

    goto :goto_0

    .line 65
    :pswitch_6
    new-instance v3, Lcom/android/tools/r8/internal/To;

    invoke-direct {v3, v0, v1, v2}, Lcom/android/tools/r8/internal/To;-><init>(IILcom/android/tools/r8/graph/l1;)V

    .line 82
    :goto_0
    invoke-virtual {p1, p0, v3}, Lcom/android/tools/r8/internal/Wm;->a(Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/internal/Yo;)V

    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public final a(Lcom/android/tools/r8/internal/gR;)V
    .locals 3

    .line 102
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/VC;->m()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 103
    invoke-static {v0}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    invoke-static {v1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    const/16 v2, 0xb4

    .line 104
    invoke-virtual {p1, v2, v0, v1}, Lcom/android/tools/r8/internal/gR;->a(ILjava/util/List;Ljava/util/List;)Lcom/android/tools/r8/internal/gR;

    return-void
.end method

.method public final a(Lcom/android/tools/r8/shaking/u;)V
    .locals 1

    .line 100
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v0

    .line 101
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/shaking/u;->a(Lcom/android/tools/r8/graph/G2;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/y;II)Z
    .locals 0

    .line 99
    invoke-static {p0, p1, p3, p4, p5}, Lcom/android/tools/r8/internal/rc;->a(Lcom/android/tools/r8/internal/sv;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/y;II)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/vt0;)Z
    .locals 0

    .line 98
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/VC;->m()Lcom/android/tools/r8/internal/vt0;

    move-result-object p1

    if-ne p1, p3, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/vt0;)Z
    .locals 0

    .line 83
    sget-boolean p1, Lcom/android/tools/r8/internal/VC;->k:Z

    if-nez p1, :cond_1

    if-eqz p2, :cond_0

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result p2

    if-eqz p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    if-nez p1, :cond_3

    .line 84
    iget-object p1, p0, Lcom/android/tools/r8/internal/rD;->b:Lcom/android/tools/r8/internal/vt0;

    if-eqz p1, :cond_2

    goto :goto_1

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 85
    :cond_3
    :goto_1
    iget-object p1, p0, Lcom/android/tools/r8/internal/rD;->b:Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result p1

    return p1
.end method

.method public final a(Ljava/util/Set;)Z
    .locals 0

    .line 9
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->I0()Z

    move-result p1

    return p1
.end method

.method public final b(Lcom/android/tools/r8/internal/rD;)Z
    .locals 3

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    instance-of v0, p1, Lcom/android/tools/r8/internal/VC;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 3
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->c()Lcom/android/tools/r8/internal/VC;

    move-result-object p1

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v2

    if-ne v0, v2, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sv;->K2()Lcom/android/tools/r8/internal/uv;

    move-result-object p1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->K2()Lcom/android/tools/r8/internal/uv;

    move-result-object v0

    if-ne p1, v0, :cond_1

    const/4 v1, 0x1

    :cond_1
    return v1
.end method

.method public final c()Lcom/android/tools/r8/internal/VC;
    .locals 0

    return-object p0
.end method

.method public final c(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public final h1()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final m()Lcom/android/tools/r8/internal/vt0;
    .locals 2

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/VC;->k:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 2
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    return-object v0
.end method

.method public final n()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/android/tools/r8/internal/rD;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/l1;->l0()Ljava/lang/String;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "; field: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final value()Lcom/android/tools/r8/internal/vt0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rD;->b:Lcom/android/tools/r8/internal/vt0;

    return-object v0
.end method
