.class public abstract Lcom/android/tools/r8/internal/sv;
.super Lcom/android/tools/r8/internal/rD;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic j:Z = true


# instance fields
.field public final i:Lcom/android/tools/r8/graph/l1;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/internal/vt0;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0, p3, p2}, Lcom/android/tools/r8/internal/rD;-><init>(Ljava/util/List;Lcom/android/tools/r8/internal/vt0;)V

    .line 2
    sget-boolean p2, Lcom/android/tools/r8/internal/sv;->j:Z

    if-nez p2, :cond_1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 3
    :cond_1
    :goto_0
    iput-object p1, p0, Lcom/android/tools/r8/internal/sv;->i:Lcom/android/tools/r8/graph/l1;

    return-void
.end method


# virtual methods
.method public final J1()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final K2()Lcom/android/tools/r8/internal/uv;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sv;->i:Lcom/android/tools/r8/graph/l1;

    iget-object v0, v0, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/I2;->f:[B

    const/4 v1, 0x0

    aget-byte v0, v0, v1

    int-to-char v0, v0

    invoke-static {v0}, Lcom/android/tools/r8/internal/uv;->a(C)Lcom/android/tools/r8/internal/uv;

    move-result-object v0

    return-object v0
.end method

.method public final Q()Lcom/android/tools/r8/internal/sv;
    .locals 0

    return-object p0
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/L1;)Lcom/android/tools/r8/internal/E1;
    .locals 0

    .line 90
    sget-boolean p2, Lcom/android/tools/r8/internal/sv;->j:Z

    if-nez p2, :cond_1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->I1()Z

    move-result p2

    if-eqz p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 91
    :cond_1
    :goto_0
    iget-object p2, p0, Lcom/android/tools/r8/internal/rD;->b:Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/vt0;->A()Z

    move-result p2

    if-nez p2, :cond_5

    .line 92
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p2

    .line 93
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/h;->h()Z

    move-result p2

    if-nez p2, :cond_2

    goto :goto_1

    .line 94
    :cond_2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->V()Lcom/android/tools/r8/graph/y;

    move-result-object p2

    .line 96
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object p3

    invoke-virtual {p2, p3}, Lcom/android/tools/r8/graph/j;->c(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/z3;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/z3;->q()Lcom/android/tools/r8/graph/g1;

    move-result-object p2

    if-nez p2, :cond_3

    .line 98
    sget-object p1, Lcom/android/tools/r8/internal/Es0;->a:Lcom/android/tools/r8/internal/Es0;

    return-object p1

    .line 99
    :cond_3
    iget-object p1, p1, Lcom/android/tools/r8/graph/y;->e:Lcom/android/tools/r8/shaking/l;

    .line 100
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p3

    invoke-virtual {p1, p3}, Lcom/android/tools/r8/shaking/l;->a(Lcom/android/tools/r8/graph/s2;)Lcom/android/tools/r8/internal/D4;

    move-result-object p1

    .line 101
    iget-object p1, p1, Lcom/android/tools/r8/internal/D4;->b:Lcom/android/tools/r8/internal/E1;

    .line 102
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result p3

    if-nez p3, :cond_4

    return-object p1

    .line 103
    :cond_4
    iget-object p1, p2, Lcom/android/tools/r8/graph/g1;->l:Lcom/android/tools/r8/internal/yv;

    .line 104
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/yv;->f()Lcom/android/tools/r8/internal/E1;

    move-result-object p1

    return-object p1

    .line 105
    :cond_5
    :goto_1
    sget-object p1, Lcom/android/tools/r8/internal/Es0;->a:Lcom/android/tools/r8/internal/Es0;

    return-object p1
.end method

.method public a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/L1;Lcom/android/tools/r8/internal/qD;)Z
    .locals 2

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p3

    iget-object v0, p0, Lcom/android/tools/r8/internal/sv;->i:Lcom/android/tools/r8/graph/l1;

    invoke-virtual {p3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p3, v1, v0, p2}, Lcom/android/tools/r8/graph/h;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/graph/z3;

    move-result-object p3

    .line 3
    invoke-virtual {p0, p1, p2, p4, p3}, Lcom/android/tools/r8/internal/sv;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/qD;Lcom/android/tools/r8/graph/z3;)Z

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/qD;Lcom/android/tools/r8/graph/z3;)Z
    .locals 4

    .line 4
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/z3;->y()Z

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_0

    return v1

    .line 9
    :cond_0
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/z3;->l()Lcom/android/tools/r8/graph/z3$a;

    move-result-object p4

    .line 10
    iget-object v0, p4, Lcom/android/tools/r8/graph/z3$a;->c:Lcom/android/tools/r8/graph/E0;

    .line 11
    iget-object v2, p4, Lcom/android/tools/r8/graph/z3$a;->d:Lcom/android/tools/r8/graph/g1;

    invoke-static {v0, v2}, Lcom/android/tools/r8/graph/F0;->a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/g1;)Lcom/android/tools/r8/graph/F0;

    move-result-object v0

    .line 12
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/F0;->w()Lcom/android/tools/r8/graph/h3;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v2

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->w2()Z

    move-result v3

    if-eq v2, v3, :cond_1

    return v1

    .line 16
    :cond_1
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/z3$a;->d()Lcom/android/tools/r8/graph/E0;

    move-result-object v2

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v3

    if-eq v2, v3, :cond_2

    .line 18
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->V()Lcom/android/tools/r8/graph/y;

    move-result-object v2

    invoke-virtual {p4, p2, v2}, Lcom/android/tools/r8/graph/F4;->a(Lcom/android/tools/r8/graph/z5;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/u20;

    move-result-object p4

    .line 19
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/T6;->b()Z

    move-result p4

    if-eqz p4, :cond_2

    return v1

    .line 20
    :cond_2
    instance-of p4, p0, Lcom/android/tools/r8/internal/VC;

    const/4 v2, 0x0

    if-nez p4, :cond_3

    .line 21
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->h()Z

    move-result p4

    if-eqz p4, :cond_5

    .line 22
    :cond_3
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/qD;->c()Z

    move-result p4

    if-nez p4, :cond_5

    .line 23
    iget-object p4, p0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {p4, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Lcom/android/tools/r8/internal/vt0;

    .line 24
    invoke-virtual {p4, p1}, Lcom/android/tools/r8/internal/vt0;->c(Lcom/android/tools/r8/graph/y;)Z

    move-result v3

    if-nez v3, :cond_4

    iget-object p4, p4, Lcom/android/tools/r8/internal/vt0;->o:Lcom/android/tools/r8/internal/sr0;

    invoke-virtual {p4}, Lcom/android/tools/r8/internal/sr0;->F()Z

    move-result p4

    if-eqz p4, :cond_5

    :cond_4
    return v1

    .line 31
    :cond_5
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->p()Z

    move-result p4

    if-nez p4, :cond_6

    return v2

    .line 35
    :cond_6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->w2()Z

    move-result p4

    if-eqz p4, :cond_8

    invoke-virtual {p3}, Lcom/android/tools/r8/internal/qD;->a()Z

    move-result p3

    if-nez p3, :cond_8

    .line 36
    iget-object p3, p1, Lcom/android/tools/r8/graph/y;->e:Lcom/android/tools/r8/shaking/l;

    .line 37
    invoke-virtual {p3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 38
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p4

    .line 39
    invoke-virtual {p3, p4}, Lcom/android/tools/r8/shaking/l;->a(Lcom/android/tools/r8/graph/s2;)Lcom/android/tools/r8/internal/D4;

    move-result-object p3

    .line 40
    iget-boolean p3, p3, Lcom/android/tools/r8/internal/D4;->c:Z

    if-eqz p3, :cond_7

    return v2

    .line 41
    :cond_7
    iget-object p3, p0, Lcom/android/tools/r8/internal/sv;->i:Lcom/android/tools/r8/graph/l1;

    iget-object p3, p3, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p3, p1, p2}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/z5;)Z

    move-result p1

    if-eqz p1, :cond_8

    return v1

    :cond_8
    return v2
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/F0;)Z
    .locals 4

    .line 42
    sget-boolean v0, Lcom/android/tools/r8/internal/sv;->j:Z

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->K1()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 44
    :cond_1
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->value()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    .line 45
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/sr0;->r()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/sr0;->a()Lcom/android/tools/r8/internal/T3;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/T3;->Q()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    .line 46
    :cond_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/sr0;->w()Z

    move-result v1

    const/4 v2, 0x0

    if-nez v1, :cond_3

    return v2

    .line 50
    :cond_3
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/F0;->x()Lcom/android/tools/r8/internal/yv;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/yv;->f()Lcom/android/tools/r8/internal/E1;

    move-result-object p2

    .line 51
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/E1;->f()Z

    move-result v1

    if-eqz v1, :cond_8

    .line 52
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/E1;->K()Z

    move-result v1

    if-eqz v1, :cond_4

    return v2

    .line 55
    :cond_4
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/E1;->L()Z

    move-result v1

    if-eqz v1, :cond_8

    .line 56
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/E1;->s()Lcom/android/tools/r8/internal/ek0;

    move-result-object p2

    .line 57
    iget-object p2, p2, Lcom/android/tools/r8/internal/ek0;->b:Lcom/android/tools/r8/graph/l1;

    .line 58
    iget-object p2, p2, Lcom/android/tools/r8/graph/l1;->i:Lcom/android/tools/r8/graph/J2;

    .line 59
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/J2;->L0()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 61
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    invoke-static {p2, v0, p1}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object p2

    .line 62
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 63
    iget-object v0, v0, Lcom/android/tools/r8/shaking/i;->t:Lcom/android/tools/r8/graph/p5;

    .line 64
    invoke-static {p1, p2, v0}, Lcom/android/tools/r8/shaking/E2;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Dd;Lcom/android/tools/r8/graph/p5;)Z

    move-result v2

    goto :goto_1

    .line 65
    :cond_5
    sget-boolean p1, Lcom/android/tools/r8/internal/ek0;->c:Z

    if-nez p1, :cond_7

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/J2;->H0()Z

    move-result p1

    if-nez p1, :cond_7

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result p1

    if-eqz p1, :cond_6

    goto :goto_1

    :cond_6
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_7
    :goto_1
    return v2

    .line 66
    :cond_8
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/shaking/i;

    .line 67
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->value()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->l()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    .line 68
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->i()Z

    move-result v3

    if-nez v3, :cond_c

    iget-object v3, v1, Lcom/android/tools/r8/internal/vt0;->c:Lcom/android/tools/r8/internal/rD;

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/rD;->o2()Z

    move-result v3

    if-eqz v3, :cond_c

    .line 69
    iget-object v0, v1, Lcom/android/tools/r8/internal/vt0;->c:Lcom/android/tools/r8/internal/rD;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->u0()Lcom/android/tools/r8/internal/HX;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/internal/HX;->i:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/y;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object v0

    const/4 v1, 0x1

    if-nez v0, :cond_9

    return v1

    .line 73
    :cond_9
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object p1

    .line 74
    iget-object v3, p1, Lcom/android/tools/r8/graph/B1;->u4:Lcom/android/tools/r8/graph/B1$b;

    iget-object v3, v3, Lcom/android/tools/r8/graph/B1$b;->g:Lcom/android/tools/r8/graph/x2;

    .line 76
    invoke-virtual {p2, v0, v3}, Lcom/android/tools/r8/graph/j;->e(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/V4;

    move-result-object p2

    .line 77
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/V4;->s()Lcom/android/tools/r8/graph/j1;

    move-result-object p2

    if-nez p2, :cond_a

    return v2

    .line 81
    :cond_a
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/h1;->E0()Lcom/android/tools/r8/graph/J2;

    move-result-object p2

    .line 82
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    .line 83
    invoke-virtual {p2, v0}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-nez v0, :cond_b

    .line 84
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->g2:Lcom/android/tools/r8/graph/J2;

    .line 85
    invoke-virtual {p2, p1}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result p1

    if-nez p1, :cond_b

    move v2, v1

    :cond_b
    return v2

    .line 86
    :cond_c
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object p2

    .line 87
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 88
    iget-object v0, v0, Lcom/android/tools/r8/shaking/i;->t:Lcom/android/tools/r8/graph/p5;

    .line 89
    invoke-static {p1, p2, v0}, Lcom/android/tools/r8/shaking/E2;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Dd;Lcom/android/tools/r8/graph/p5;)Z

    move-result p1

    return p1
.end method

.method public final c1()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final d(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/internal/q;
    .locals 2

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/rD;->c(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z

    move-result p2

    if-eqz p2, :cond_0

    .line 3
    sget-object p1, Lcom/android/tools/r8/internal/us0;->a:Lcom/android/tools/r8/internal/us0;

    return-object p1

    .line 6
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->I1()Z

    move-result p2

    if-eqz p2, :cond_4

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sv;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object p2

    const/4 v0, 0x0

    .line 9
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->p()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 10
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/graph/j;->c(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/z3;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/z3;->q()Lcom/android/tools/r8/graph/g1;

    move-result-object v0

    goto :goto_0

    .line 12
    :cond_1
    iget-object v1, p2, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/graph/y;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p1

    if-eqz p1, :cond_2

    .line 14
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/graph/E0;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/g1;

    move-result-object v0

    :cond_2
    :goto_0
    if-eqz v0, :cond_3

    .line 18
    new-instance p1, Lcom/android/tools/r8/internal/Tf;

    invoke-direct {p1, v0}, Lcom/android/tools/r8/internal/Tf;-><init>(Lcom/android/tools/r8/graph/g1;)V

    return-object p1

    .line 20
    :cond_3
    sget-object p1, Lcom/android/tools/r8/internal/us0;->a:Lcom/android/tools/r8/internal/us0;

    return-object p1

    .line 23
    :cond_4
    sget-boolean p1, Lcom/android/tools/r8/internal/sv;->j:Z

    if-nez p1, :cond_6

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->K1()Z

    move-result p1

    if-eqz p1, :cond_5

    goto :goto_1

    :cond_5
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 24
    :cond_6
    :goto_1
    sget-object p1, Lcom/android/tools/r8/internal/Vs;->a:Lcom/android/tools/r8/internal/Vs;

    return-object p1
.end method

.method public getField()Lcom/android/tools/r8/graph/l1;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sv;->i:Lcom/android/tools/r8/graph/l1;

    return-object v0
.end method

.method public abstract value()Lcom/android/tools/r8/internal/vt0;
.end method
