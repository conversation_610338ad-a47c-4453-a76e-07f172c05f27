.class public interface abstract Lcom/android/tools/r8/internal/tE;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/vx;


# virtual methods
.method public abstract a(ILjava/lang/Object;)Ljava/lang/Object;
.end method

.method public abstract a(I)Z
.end method

.method public abstract get(I)Ljava/lang/Object;
.end method
