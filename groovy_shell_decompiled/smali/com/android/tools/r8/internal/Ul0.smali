.class public abstract Lcom/android/tools/r8/internal/Ul0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/q1;
.implements Ljava/lang/Comparable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lcom/android/tools/r8/internal/q1;",
        "Ljava/lang/Comparable<",
        "Lcom/android/tools/r8/internal/Ul0;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a(Lcom/android/tools/r8/internal/op0;Lcom/android/tools/r8/internal/op0;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E1:",
            "Ljava/lang/Exception;",
            "E2:",
            "Ljava/lang/Exception;",
            ">(",
            "Lcom/android/tools/r8/internal/op0<",
            "-",
            "Lcom/android/tools/r8/internal/Pl0;",
            "TE1;>;",
            "Lcom/android/tools/r8/internal/op0<",
            "-",
            "Lcom/android/tools/r8/internal/Rl0;",
            "TE2;>;)V^TE1;^TE2;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;,
            Ljava/lang/Exception;
        }
    .end annotation
.end method

.method public abstract a(Ljava/lang/StringBuilder;)V
.end method

.method public abstract c()Lcom/android/tools/r8/graph/G2;
.end method

.method public final compareTo(Ljava/lang/Object;)I
    .locals 1

    .line 1
    check-cast p1, Lcom/android/tools/r8/internal/Ul0;

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ul0;->c()Lcom/android/tools/r8/graph/G2;

    move-result-object v0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Ul0;->c()Lcom/android/tools/r8/graph/G2;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/G2;->b(Lcom/android/tools/r8/graph/G2;)I

    move-result p1

    return p1
.end method
