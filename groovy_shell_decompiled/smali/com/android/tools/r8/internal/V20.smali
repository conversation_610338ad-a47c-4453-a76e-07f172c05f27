.class public final Lcom/android/tools/r8/internal/V20;
.super Lcom/android/tools/r8/internal/b30;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final c:Lcom/android/tools/r8/internal/a30;

.field public final d:Lcom/android/tools/r8/internal/UZ;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/a30;Lcom/android/tools/r8/internal/UZ;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/b30;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Vz;Lcom/android/tools/r8/internal/Y20;I)I
    .locals 6

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    const/4 v1, 0x2

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    .line 6
    iget-object v4, p2, Lcom/android/tools/r8/internal/Y20;->c:Ljava/util/ArrayList;

    add-int/lit8 v5, p3, 0x1

    invoke-virtual {v4, p3}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Ljava/lang/Integer;

    invoke-virtual {p3}, Ljava/lang/Integer;->intValue()I

    move-result p3

    const/4 v4, -0x1

    if-ne p3, v4, :cond_0

    .line 8
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Y20;->a()I

    move-result p3

    .line 10
    :cond_0
    iget-object v4, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    .line 11
    invoke-static {v4}, Lcom/android/tools/r8/internal/Kt0;->a(Lcom/android/tools/r8/internal/UZ;)Lcom/android/tools/r8/internal/Kt0;

    move-result-object v4

    invoke-virtual {p1, p3, v4}, Lcom/android/tools/r8/internal/Vz;->b(ILcom/android/tools/r8/internal/Kt0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object p3

    .line 12
    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    add-int/lit8 v3, v3, 0x1

    move p3, v5

    goto :goto_0

    .line 15
    :cond_1
    iget-object v3, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    invoke-static {v3}, Lcom/android/tools/r8/internal/C50;->a(Lcom/android/tools/r8/internal/UZ;)Lcom/android/tools/r8/internal/C50;

    move-result-object v3

    .line 17
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Y20;->a()I

    move-result p2

    invoke-virtual {p1, p2, v1, v3}, Lcom/android/tools/r8/internal/Vz;->a(IILcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object p2

    .line 19
    iget-object v3, p0, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    move-result v3

    const/4 v4, 0x1

    if-eqz v3, :cond_6

    if-eq v3, v4, :cond_5

    if-eq v3, v1, :cond_4

    const/4 v1, 0x3

    if-eq v3, v1, :cond_3

    const/4 v1, 0x4

    if-ne v3, v1, :cond_2

    .line 37
    new-instance v1, Lcom/android/tools/r8/internal/Fc0;

    iget-object v3, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {v0, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    invoke-direct {v1, v3, p2, v2, v0}, Lcom/android/tools/r8/internal/Fc0;-><init>(Lcom/android/tools/r8/internal/UZ;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)V

    goto :goto_1

    .line 36
    :cond_2
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    iget-object p2, p0, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Invalid binary operation type: "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1

    .line 38
    :cond_3
    new-instance v1, Lcom/android/tools/r8/internal/gs;

    iget-object v3, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {v0, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    invoke-direct {v1, v3, p2, v2, v0}, Lcom/android/tools/r8/internal/gs;-><init>(Lcom/android/tools/r8/internal/UZ;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)V

    goto :goto_1

    .line 39
    :cond_4
    iget-object v1, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {v0, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    invoke-static {v1, p2, v2, v0}, Lcom/android/tools/r8/internal/tW;->a(Lcom/android/tools/r8/internal/UZ;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/tW;

    move-result-object v1

    goto :goto_1

    .line 42
    :cond_5
    new-instance v1, Lcom/android/tools/r8/internal/lo0;

    iget-object v3, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {v0, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    invoke-direct {v1, v3, p2, v2, v0}, Lcom/android/tools/r8/internal/lo0;-><init>(Lcom/android/tools/r8/internal/UZ;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)V

    goto :goto_1

    .line 43
    :cond_6
    iget-object v1, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {v0, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    invoke-static {v1, p2, v2, v0}, Lcom/android/tools/r8/internal/c2;->a(Lcom/android/tools/r8/internal/UZ;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/c2;

    move-result-object v1

    .line 60
    :goto_1
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/Vz;->a(Lcom/android/tools/r8/internal/rD;)V

    return p3
.end method

.method public final a()Ljava/lang/String;
    .locals 1

    const-string v0, ""

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/internal/Fy;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public final b()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    invoke-virtual {v0}, Ljava/lang/Enum;->name()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v1}, Ljava/lang/Enum;->name()Ljava/lang/String;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "-"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/a30;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    return-object v0
.end method

.method public final compareTo(Ljava/lang/Object;)I
    .locals 2

    .line 1
    check-cast p1, Lcom/android/tools/r8/internal/b30;

    .line 2
    instance-of v0, p1, Lcom/android/tools/r8/internal/V20;

    if-nez v0, :cond_0

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/b30;->c()Lcom/android/tools/r8/internal/a30;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/Enum;->compareTo(Ljava/lang/Enum;)I

    move-result p1

    goto :goto_0

    .line 5
    :cond_0
    check-cast p1, Lcom/android/tools/r8/internal/V20;

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    iget-object v1, p1, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    invoke-virtual {v0, v1}, Ljava/lang/Enum;->compareTo(Ljava/lang/Enum;)I

    move-result v0

    if-eqz v0, :cond_1

    move p1, v0

    goto :goto_0

    .line 10
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    iget-object p1, p1, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v0, p1}, Ljava/lang/Enum;->compareTo(Ljava/lang/Enum;)I

    move-result p1

    :goto_0
    return p1
.end method

.method public final d()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final e()I
    .locals 1

    const/4 v0, 0x2

    return v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/V20;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/V20;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    iget-object v3, p1, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    invoke-virtual {v1, v3}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    iget-object p1, p1, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v1, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public final hashCode()I
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/V20;->c:Lcom/android/tools/r8/internal/a30;

    iget-object v1, p0, Lcom/android/tools/r8/internal/V20;->d:Lcom/android/tools/r8/internal/UZ;

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object v0, v2, v3

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-static {v2}, Ljava/util/Objects;->hash([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
