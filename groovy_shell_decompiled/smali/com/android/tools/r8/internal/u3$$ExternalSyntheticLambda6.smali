.class public final synthetic Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda6;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/u3;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/D5;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/u3;Lcom/android/tools/r8/graph/D5;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda6;->f$0:Lcom/android/tools/r8/internal/u3;

    iput-object p2, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda6;->f$1:Lcom/android/tools/r8/graph/D5;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda6;->f$0:Lcom/android/tools/r8/internal/u3;

    iget-object v1, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda6;->f$1:Lcom/android/tools/r8/graph/D5;

    check-cast p1, Lcom/android/tools/r8/internal/pR;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/u3;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/pR;)Z

    move-result p1

    return p1
.end method
