.class public final synthetic Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda10;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Xc;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Xc;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda10;->f$0:Lcom/android/tools/r8/internal/Xc;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda10;->f$0:Lcom/android/tools/r8/internal/Xc;

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc;->a(Lcom/android/tools/r8/graph/x2;)Z

    move-result p1

    return p1
.end method
