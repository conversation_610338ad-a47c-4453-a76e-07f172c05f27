.class public final Lcom/android/tools/r8/internal/t4;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleBuilder;


# instance fields
.field public final synthetic a:Lcom/android/tools/r8/internal/q7;

.field public final synthetic b:Lcom/android/tools/r8/internal/u4;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/u4;Lcom/android/tools/r8/internal/q7;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/t4;->b:Lcom/android/tools/r8/internal/u4;

    iput-object p2, p0, Lcom/android/tools/r8/internal/t4;->a:Lcom/android/tools/r8/internal/q7;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final setMethodReference(Lcom/android/tools/r8/references/MethodReference;)Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleBuilder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/t4;->a:Lcom/android/tools/r8/internal/q7;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/q7;->a(Ljava/lang/Object;)V

    return-object p0
.end method

.method public final setMethodRuleInfo(Ljava/util/function/Consumer;)Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleBuilder;
    .locals 2

    .line 1
    invoke-static {}, Lcom/android/tools/r8/internal/p4;->a()Lcom/android/tools/r8/internal/p4$a;

    move-result-object v0

    .line 2
    invoke-interface {p1, v0}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    .line 4
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/p4$a;->a()Lcom/android/tools/r8/internal/p4;

    move-result-object p1

    .line 6
    :try_start_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/t4;->b:Lcom/android/tools/r8/internal/u4;

    iget-object v0, v0, Lcom/android/tools/r8/internal/u4;->a:Ljava/io/OutputStreamWriter;

    .line 7
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/p4;->isHot()Z

    move-result v1

    if-eqz v1, :cond_0

    const/16 v1, 0x48

    .line 8
    invoke-virtual {v0, v1}, Ljava/io/OutputStreamWriter;->write(I)V

    .line 10
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/p4;->isStartup()Z

    move-result v1

    if-eqz v1, :cond_1

    const/16 v1, 0x53

    .line 11
    invoke-virtual {v0, v1}, Ljava/io/OutputStreamWriter;->write(I)V

    .line 13
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/p4;->isPostStartup()Z

    move-result p1

    if-eqz p1, :cond_2

    const/16 p1, 0x50

    .line 14
    invoke-virtual {v0, p1}, Ljava/io/OutputStreamWriter;->write(I)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    :cond_2
    return-object p0

    :catch_0
    move-exception p1

    .line 15
    new-instance v0, Ljava/io/UncheckedIOException;

    invoke-direct {v0, p1}, Ljava/io/UncheckedIOException;-><init>(Ljava/io/IOException;)V

    throw v0
.end method
