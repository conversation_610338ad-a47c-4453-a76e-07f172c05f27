.class public Lcom/android/tools/r8/internal/T1;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public a:Z

.field public final b:Lcom/android/tools/r8/utils/w;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/utils/w;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/T1;->a:Z

    .line 7
    iput-object p1, p0, Lcom/android/tools/r8/internal/T1;->b:Lcom/android/tools/r8/utils/w;

    return-void
.end method


# virtual methods
.method public a(Z)V
    .locals 0

    .line 8
    iput-boolean p1, p0, Lcom/android/tools/r8/internal/T1;->a:Z

    return-void
.end method

.method public final a()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T1;->b:Lcom/android/tools/r8/utils/w;

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/utils/w;->h:Lcom/android/tools/r8/shaking/b3;

    const/4 v2, 0x1

    if-eqz v1, :cond_0

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/utils/w;->J()Lcom/android/tools/r8/shaking/b3;

    move-result-object v0

    .line 4
    iget-boolean v0, v0, Lcom/android/tools/r8/shaking/b3;->f:Z

    if-eqz v0, :cond_0

    return v2

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/T1;->b:Lcom/android/tools/r8/utils/w;

    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->i0:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/T1;->b:Lcom/android/tools/r8/utils/w;

    iget-boolean v1, v0, Lcom/android/tools/r8/utils/w;->b1:Z

    if-nez v1, :cond_1

    .line 7
    invoke-virtual {v0}, Lcom/android/tools/r8/utils/w;->e0()Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :goto_0
    return v2
.end method
