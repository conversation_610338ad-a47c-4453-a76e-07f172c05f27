.class public final synthetic Lcom/android/tools/r8/internal/U4;
.super Lcom/android/tools/r8/internal/kX;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final h:Lcom/android/tools/r8/internal/U4;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/U4;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/U4;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/U4;->h:Lcom/android/tools/r8/internal/U4;

    return-void
.end method

.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/kX;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(<PERSON>ja<PERSON>/lang/Integer;Ljava/lang/Object;)V
    .locals 0

    .line 1
    check-cast p2, Lcom/android/tools/r8/internal/rO;

    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    move-result p1

    .line 2
    iput p1, p2, Lcom/android/tools/r8/internal/rO;->a:I

    return-void
.end method

.method public final b(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lcom/android/tools/r8/internal/rO;

    .line 2
    iget p1, p1, Lcom/android/tools/r8/internal/rO;->a:I

    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1
.end method

.method public final b()Ljava/lang/String;
    .locals 1

    const-string v0, "flags"

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/TK;
    .locals 1

    const-class v0, Lcom/android/tools/r8/internal/rO;

    invoke-static {v0}, Lcom/android/tools/r8/internal/Rb0;->a(Ljava/lang/Class;)Lcom/android/tools/r8/internal/wd;

    move-result-object v0

    return-object v0
.end method

.method public final d()Ljava/lang/String;
    .locals 1

    const-string v0, "getFlags$kotlin_metadata()I"

    return-object v0
.end method
