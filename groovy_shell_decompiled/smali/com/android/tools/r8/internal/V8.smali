.class public final Lcom/android/tools/r8/internal/V8;
.super Lcom/android/tools/r8/internal/W8;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic c:Z


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Ljava/util/ArrayList;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/W8;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/V8;->c:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Ljava/util/ArrayList;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/W8;-><init>()V

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/V8;->c:Z

    if-nez v0, :cond_1

    invoke-virtual {p2}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 3
    :cond_1
    :goto_0
    iput-object p1, p0, Lcom/android/tools/r8/internal/V8;->a:Lcom/android/tools/r8/graph/y;

    .line 4
    iput-object p2, p0, Lcom/android/tools/r8/internal/V8;->b:Ljava/util/ArrayList;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/ff;Lcom/android/tools/r8/internal/Y8;Lcom/android/tools/r8/internal/T8;)V
    .locals 2

    .line 21
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 22
    new-instance v0, Lcom/android/tools/r8/internal/bf;

    invoke-direct {v0, p0, p2}, Lcom/android/tools/r8/internal/bf;-><init>(Lcom/android/tools/r8/internal/ff;Lcom/android/tools/r8/internal/T8;)V

    .line 24
    sget-boolean v1, Lcom/android/tools/r8/internal/ff;->c:Z

    if-nez v1, :cond_0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ff;->a(Lcom/android/tools/r8/internal/cf;)V

    .line 25
    :cond_0
    invoke-interface {p2, v0, p1}, Lcom/android/tools/r8/internal/T8;->a(Lcom/android/tools/r8/internal/bf;Lcom/android/tools/r8/internal/Y8;)V

    return-void
.end method


# virtual methods
.method public final a(Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Y8;)V
    .locals 3

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/V8;->c:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/V8;->b:Ljava/util/ArrayList;

    invoke-interface {v0}, Ljava/util/Collection;->stream()Ljava/util/stream/Stream;

    move-result-object v0

    sget-object v1, Lcom/android/tools/r8/internal/V8$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/V8$$ExternalSyntheticLambda1;

    .line 2
    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object v0

    .line 3
    invoke-static {}, Ljava/util/stream/Collectors;->toSet()Ljava/util/stream/Collector;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->collect(Ljava/util/stream/Collector;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Set;

    .line 4
    invoke-interface {v0}, Ljava/util/Set;->size()I

    move-result v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/V8;->b:Ljava/util/ArrayList;

    .line 5
    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v1

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 6
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 11
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/V8;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->n()Lcom/android/tools/r8/internal/ff;

    move-result-object v0

    .line 12
    iget-object v1, p0, Lcom/android/tools/r8/internal/V8;->b:Ljava/util/ArrayList;

    new-instance v2, Lcom/android/tools/r8/internal/V8$$ExternalSyntheticLambda0;

    invoke-direct {v2, v0, p2}, Lcom/android/tools/r8/internal/V8$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/ff;Lcom/android/tools/r8/internal/Y8;)V

    iget-object p2, p0, Lcom/android/tools/r8/internal/V8;->a:Lcom/android/tools/r8/graph/y;

    .line 19
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/utils/w;->O()Lcom/android/tools/r8/threading/ThreadingModule;

    move-result-object p2

    .line 20
    invoke-static {v1, v2, p2, p1}, Lcom/android/tools/r8/internal/ep0;->a(Ljava/util/Collection;Ljava/util/function/Consumer;Lcom/android/tools/r8/threading/ThreadingModule;Ljava/util/concurrent/ExecutorService;)V

    return-void
.end method
