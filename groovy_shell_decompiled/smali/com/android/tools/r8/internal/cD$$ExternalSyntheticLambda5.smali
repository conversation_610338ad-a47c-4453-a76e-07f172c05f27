.class public final synthetic Lcom/android/tools/r8/internal/cD$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Xc;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/x2;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Xc;Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/cD$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/internal/Xc;

    iput-object p2, p0, Lcom/android/tools/r8/internal/cD$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/graph/x2;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/cD$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/internal/Xc;

    iget-object v1, p0, Lcom/android/tools/r8/internal/cD$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/graph/x2;

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/cD;->a(Lcom/android/tools/r8/internal/Xc;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)Z

    move-result p1

    return p1
.end method
