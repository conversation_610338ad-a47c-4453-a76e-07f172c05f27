.class public final Lcom/android/tools/r8/internal/s10;
.super Lcom/android/tools/r8/internal/b1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public b:I

.field public final synthetic c:Lcom/android/tools/r8/internal/t10;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/t10;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/s10;->c:Lcom/android/tools/r8/internal/t10;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/b1;-><init>()V

    const/4 p1, 0x0

    .line 2
    iput p1, p0, Lcom/android/tools/r8/internal/s10;->b:I

    return-void
.end method


# virtual methods
.method public final hasNext()Z
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/s10;->b:I

    iget-object v1, p0, Lcom/android/tools/r8/internal/s10;->c:Lcom/android/tools/r8/internal/t10;

    .line 2
    iget v1, v1, Lcom/android/tools/r8/internal/t10;->c:I

    if-ge v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final next()Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/s10;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/s10;->c:Lcom/android/tools/r8/internal/t10;

    .line 3
    iget-object v0, v0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    .line 4
    iget v1, p0, Lcom/android/tools/r8/internal/s10;->b:I

    add-int/lit8 v2, v1, 0x1

    iput v2, p0, Lcom/android/tools/r8/internal/s10;->b:I

    aget-object v0, v0, v1

    return-object v0

    .line 5
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public final remove()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/s10;->c:Lcom/android/tools/r8/internal/t10;

    .line 2
    iget v1, v0, Lcom/android/tools/r8/internal/t10;->c:I

    add-int/lit8 v2, v1, -0x1

    iput v2, v0, Lcom/android/tools/r8/internal/t10;->c:I

    .line 3
    iget v2, p0, Lcom/android/tools/r8/internal/s10;->b:I

    add-int/lit8 v3, v2, -0x1

    iput v3, p0, Lcom/android/tools/r8/internal/s10;->b:I

    sub-int/2addr v1, v2

    .line 4
    iget-object v0, v0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    .line 5
    invoke-static {v0, v2, v0, v3, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/s10;->c:Lcom/android/tools/r8/internal/t10;

    .line 7
    iget-object v1, v0, Lcom/android/tools/r8/internal/t10;->b:[Ljava/lang/Object;

    .line 8
    iget v0, v0, Lcom/android/tools/r8/internal/t10;->c:I

    const/4 v2, 0x0

    .line 9
    aput-object v2, v1, v0

    return-void
.end method
