.class public final synthetic Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda4;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda4;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda4;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda4;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda4;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    invoke-static {}, Lcom/android/tools/r8/internal/W90;->a()Lcom/android/tools/r8/internal/VV;

    move-result-object v0

    return-object v0
.end method
