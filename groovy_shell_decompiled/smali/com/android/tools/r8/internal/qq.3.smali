.class public final Lcom/android/tools/r8/internal/qq;
.super Lcom/android/tools/r8/internal/go;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(II)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/go;-><init>(II)V

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Zo;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/go;-><init>(ILcom/android/tools/r8/internal/j8;)V

    return-void
.end method


# virtual methods
.method public final bridge synthetic a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/dex/M;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/jQ;)V
    .locals 0

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/Vz;)V
    .locals 3

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/UZ;->f:Lcom/android/tools/r8/internal/UZ;

    iget-byte v1, p0, Lcom/android/tools/r8/internal/go;->f:B

    iget-byte v2, p0, Lcom/android/tools/r8/internal/go;->g:B

    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/internal/Vz;->a(Lcom/android/tools/r8/internal/UZ;II)V

    return-void
.end method

.method public final l()Ljava/lang/String;
    .locals 1

    const-string v0, "NegLong"

    return-object v0
.end method

.method public final s()I
    .locals 1

    const/16 v0, 0x7d

    return v0
.end method

.method public final v()Ljava/lang/String;
    .locals 1

    const-string v0, "neg-long"

    return-object v0
.end method
