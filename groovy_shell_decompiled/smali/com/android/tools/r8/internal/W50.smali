.class public final Lcom/android/tools/r8/internal/W50;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/shaking/i4;


# instance fields
.field public final a:Lcom/android/tools/r8/internal/Yf;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Yf;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/W50;->a:Lcom/android/tools/r8/internal/Yf;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/J50;)V
    .locals 1

    .line 1
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    .line 3
    invoke-interface {p2, v0}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/internal/J50;

    move-result-object p2

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    .line 5
    invoke-interface {v0}, Lcom/android/tools/r8/graph/o0;->getReference()Lcom/android/tools/r8/graph/G2;

    move-result-object v0

    invoke-interface {p2, v0}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/internal/J50;

    move-result-object p2

    .line 6
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-interface {p2, p0, p1}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public static b(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/J50;)V
    .locals 1

    .line 1
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    .line 3
    invoke-interface {p2, v0}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/internal/J50;

    move-result-object p2

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    .line 5
    invoke-interface {v0}, Lcom/android/tools/r8/graph/o0;->getReference()Lcom/android/tools/r8/graph/G2;

    move-result-object v0

    invoke-interface {p2, v0}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/internal/J50;

    move-result-object p2

    .line 6
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-interface {p2, p0, p1}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method


# virtual methods
.method public final i(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/W50;->a:Lcom/android/tools/r8/internal/Yf;

    new-instance v1, Lcom/android/tools/r8/internal/W50$$ExternalSyntheticLambda1;

    invoke-direct {v1, p2, p1}, Lcom/android/tools/r8/internal/W50$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;)V

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/x2;Ljava/util/function/Consumer;)V

    return-void
.end method

.method public final j(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/W50;->a:Lcom/android/tools/r8/internal/Yf;

    invoke-virtual {v0, p2, p1}, Lcom/android/tools/r8/internal/Yf;->b(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;)V

    return-void
.end method

.method public final q(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/W50;->a:Lcom/android/tools/r8/internal/Yf;

    invoke-virtual {v0, p2, p1}, Lcom/android/tools/r8/internal/Yf;->b(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;)V

    return-void
.end method

.method public final t(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/W50;->a:Lcom/android/tools/r8/internal/Yf;

    new-instance v1, Lcom/android/tools/r8/internal/W50$$ExternalSyntheticLambda0;

    invoke-direct {v1, p2, p1}, Lcom/android/tools/r8/internal/W50$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/D5;)V

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/x2;Ljava/util/function/Consumer;)V

    return-void
.end method
