.class public final synthetic Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/op0;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda2;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda2;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda2;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda2;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 0

    check-cast p1, Lcom/android/tools/r8/internal/hi;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/hi;->b()V

    return-void
.end method
