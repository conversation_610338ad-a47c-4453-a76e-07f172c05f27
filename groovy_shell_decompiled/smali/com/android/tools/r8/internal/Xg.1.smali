.class public Lcom/android/tools/r8/internal/Xg;
.super Lcom/android/tools/r8/internal/Ng;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic k:Z = true


# instance fields
.field public final j:Lcom/android/tools/r8/graph/I2;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/graph/I2;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Ng;-><init>(Lcom/android/tools/r8/internal/vt0;)V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    return-void
.end method

.method public static K2()Lcom/android/tools/r8/internal/Wg;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Wg;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Wg;-><init>()V

    return-object v0
.end method

.method public static a(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/Xg;)Lcom/android/tools/r8/internal/Xg;
    .locals 1

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/Xg;->k:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    if-eq p0, v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 3
    :cond_1
    :goto_0
    new-instance v0, Lcom/android/tools/r8/internal/Xg;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Xg;->L2()Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    invoke-direct {v0, p0, p1}, Lcom/android/tools/r8/internal/Xg;-><init>(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/graph/I2;)V

    return-object v0
.end method


# virtual methods
.method public final B1()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final F2()I
    .locals 2

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/Xg;->k:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    const-string v1, "ConstString has no register arguments."

    invoke-direct {v0, v1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw v0
.end method

.method public final G2()I
    .locals 1

    const/16 v0, 0xff

    return v0
.end method

.method public final H2()I
    .locals 1

    const/16 v0, 0x10

    return v0
.end method

.method public final I()Lcom/android/tools/r8/internal/Xg;
    .locals 0

    return-object p0
.end method

.method public L2()Lcom/android/tools/r8/graph/I2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    return-object v0
.end method

.method public final M2()Z
    .locals 2

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;
    :try_end_0
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v0, 0x0

    return v0

    :catch_0
    move-exception v0

    .line 3
    invoke-virtual {v0}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object v1

    instance-of v1, v1, Ljava/io/UTFDataFormatException;

    if-eqz v1, :cond_0

    const/4 v0, 0x1

    return v0

    .line 6
    :cond_0
    throw v0
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Pr0;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 25
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->Z1:Lcom/android/tools/r8/graph/J2;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/L1;)Lcom/android/tools/r8/internal/E1;
    .locals 0

    .line 9
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xg;->M2()Z

    move-result p2

    if-nez p2, :cond_0

    .line 10
    iget-object p1, p1, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 11
    iget-object p2, p0, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/F1;->a(Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/internal/nk0;

    move-result-object p1

    return-object p1

    .line 13
    :cond_0
    sget-object p1, Lcom/android/tools/r8/internal/Es0;->a:Lcom/android/tools/r8/internal/Es0;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;
    .locals 1

    .line 26
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->b()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/qZ;)Lcom/android/tools/r8/internal/Dd;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/ir/optimize/E;
    .locals 1

    .line 15
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    .line 16
    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->k:Lcom/android/tools/r8/ProgramConsumer;

    .line 17
    instance-of v0, v0, Lcom/android/tools/r8/ClassFileConsumer;

    if-nez v0, :cond_1

    .line 18
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/rD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z

    move-result p1

    if-nez p1, :cond_0

    goto :goto_0

    .line 21
    :cond_0
    sget-object p1, Lcom/android/tools/r8/ir/optimize/E;->b:Lcom/android/tools/r8/ir/optimize/C;

    return-object p1

    .line 22
    :cond_1
    :goto_0
    sget-object p1, Lcom/android/tools/r8/ir/optimize/E;->a:Lcom/android/tools/r8/ir/optimize/B;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/tC;)Ljava/lang/Object;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/St0;)V
    .locals 0

    .line 27
    sget-boolean p2, Lcom/android/tools/r8/internal/Xg;->k:Z

    .line 28
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->b()Lcom/android/tools/r8/internal/qZ;

    move-result-object p3

    invoke-static {p1, p3}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/qZ;)Lcom/android/tools/r8/internal/Dd;

    move-result-object p1

    if-nez p2, :cond_1

    .line 29
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->a()Lcom/android/tools/r8/internal/sr0;

    move-result-object p2

    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/N5;Lcom/android/tools/r8/internal/gS;)V
    .locals 0

    .line 23
    invoke-virtual {p2, p0, p1}, Lcom/android/tools/r8/internal/gS;->b(Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/internal/uD;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/R8;)V
    .locals 2

    .line 24
    new-instance v0, Lcom/android/tools/r8/internal/k9;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/k9;-><init>(Lcom/android/tools/r8/graph/I2;)V

    invoke-virtual {p1, v0, p0}, Lcom/android/tools/r8/internal/R8;->a(Lcom/android/tools/r8/internal/H9;Lcom/android/tools/r8/internal/rD;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/Wm;)V
    .locals 3

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/rD;->b:Lcom/android/tools/r8/internal/vt0;

    .line 5
    iget v1, p0, Lcom/android/tools/r8/internal/rD;->e:I

    .line 6
    iget-object v2, p1, Lcom/android/tools/r8/internal/Wm;->d:Lcom/android/tools/r8/internal/ic0;

    .line 7
    invoke-interface {v2, v0, v1}, Lcom/android/tools/r8/internal/ic0;->b(Lcom/android/tools/r8/internal/vt0;I)I

    move-result v0

    .line 8
    new-instance v1, Lcom/android/tools/r8/internal/vn;

    iget-object v2, p0, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    invoke-direct {v1, v0, v2}, Lcom/android/tools/r8/internal/vn;-><init>(ILcom/android/tools/r8/graph/I2;)V

    invoke-virtual {p1, p0, v1}, Lcom/android/tools/r8/internal/Wm;->a(Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/internal/Yo;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/gR;)V
    .locals 1

    .line 30
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/gR;->a(Lcom/android/tools/r8/graph/I2;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/L1;Lcom/android/tools/r8/internal/qD;)Z
    .locals 0

    .line 14
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xg;->M2()Z

    move-result p1

    return p1
.end method

.method public final b(Lcom/android/tools/r8/internal/rD;)Z
    .locals 1

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->B1()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->I()Lcom/android/tools/r8/internal/Xg;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    iget-object v0, p0, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    if-ne p1, v0, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final h1()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final n()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final r2()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/android/tools/r8/internal/rD;->toString()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/Xg;->j:Lcom/android/tools/r8/graph/I2;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " \""

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
