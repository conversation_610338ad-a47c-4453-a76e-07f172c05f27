.class public abstract Lcom/android/tools/r8/internal/w2;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic b:Z = true


# instance fields
.field public a:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 193
    iput v0, p0, Lcom/android/tools/r8/internal/w2;->a:I

    return-void
.end method

.method public static a()I
    .locals 1

    const/4 v0, 0x4

    return v0
.end method

.method public static a(I)I
    .locals 2

    .line 66
    invoke-static {}, Lcom/android/tools/r8/internal/w2;->c()I

    move-result v0

    const/4 v1, 0x1

    shl-int v0, v1, v0

    mul-int/lit8 v0, v0, 0x6

    .line 67
    invoke-static {p0}, Lcom/android/tools/r8/internal/w2;->b(I)I

    move-result p0

    add-int/2addr p0, v0

    return p0
.end method

.method public static a(Lcom/android/tools/r8/graph/G2;)I
    .locals 2

    .line 59
    invoke-static {}, Lcom/android/tools/r8/internal/w2;->b()I

    move-result v0

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    shl-int v0, v1, v0

    .line 61
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/E;->hashCode()I

    move-result p0

    rem-int/2addr p0, v0

    add-int/2addr p0, v0

    return p0
.end method

.method public static a(Lcom/android/tools/r8/graph/I2;)I
    .locals 2

    .line 62
    invoke-static {}, Lcom/android/tools/r8/internal/w2;->c()I

    move-result v0

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    shl-int v0, v1, v0

    .line 64
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/E;->hashCode()I

    move-result p0

    rem-int/2addr p0, v0

    add-int/2addr p0, v0

    return p0
.end method

.method public static a(Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/DiagnosticsHandler;)Lcom/android/tools/r8/internal/w2;
    .locals 3

    .line 2
    const-class v0, Lcom/android/tools/r8/internal/w2;

    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    const-string v2, "resources/new_api_database.ser"

    invoke-virtual {v1, v2}, Ljava/lang/ClassLoader;->getResource(Ljava/lang/String;)Ljava/net/URL;

    move-result-object v1

    if-nez v1, :cond_0

    .line 4
    new-instance p0, Lcom/android/tools/r8/utils/StringDiagnostic;

    const-string v0, "Could not find the api database at resources/new_api_database.ser"

    invoke-direct {p0, v0}, Lcom/android/tools/r8/utils/StringDiagnostic;-><init>(Ljava/lang/String;)V

    invoke-interface {p1, p0}, Lcom/android/tools/r8/DiagnosticsHandler;->warning(Lcom/android/tools/r8/Diagnostic;)V

    .line 6
    new-instance p0, Lcom/android/tools/r8/internal/u2;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/u2;-><init>()V

    return-object p0

    .line 8
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/utils/w;->a()Lcom/android/tools/r8/utils/w$a;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 38
    :try_start_0
    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object p0

    invoke-virtual {p0, v2}, Ljava/lang/ClassLoader;->getResourceAsStream(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object p0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    if-nez p0, :cond_2

    .line 40
    :try_start_1
    new-instance v0, Lcom/android/tools/r8/utils/StringDiagnostic;

    const-string v1, "Could not open the api database at resources/new_api_database.ser"

    invoke-direct {v0, v1}, Lcom/android/tools/r8/utils/StringDiagnostic;-><init>(Ljava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/android/tools/r8/DiagnosticsHandler;->warning(Lcom/android/tools/r8/Diagnostic;)V

    .line 42
    new-instance v0, Lcom/android/tools/r8/internal/u2;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/u2;-><init>()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-eqz p0, :cond_1

    .line 45
    :try_start_2
    invoke-virtual {p0}, Ljava/io/InputStream;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    :cond_1
    return-object v0

    .line 46
    :cond_2
    :try_start_3
    new-instance v0, Lcom/android/tools/r8/internal/t2;

    invoke-static {p0}, Lcom/android/tools/r8/internal/P7;->a(Ljava/io/InputStream;)[B

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/t2;-><init>([B)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 47
    :try_start_4
    invoke-virtual {p0}, Ljava/io/InputStream;->close()V
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_0

    return-object v0

    :catchall_0
    move-exception v0

    if-eqz p0, :cond_3

    .line 48
    :try_start_5
    invoke-virtual {p0}, Ljava/io/InputStream;->close()V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    goto :goto_0

    :catchall_1
    move-exception p0

    :try_start_6
    invoke-virtual {v0, p0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :cond_3
    :goto_0
    throw v0
    :try_end_6
    .catch Ljava/io/IOException; {:try_start_6 .. :try_end_6} :catch_0

    :catch_0
    move-exception p0

    .line 57
    new-instance v0, Lcom/android/tools/r8/utils/ExceptionDiagnostic;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/utils/ExceptionDiagnostic;-><init>(Ljava/lang/Throwable;)V

    invoke-interface {p1, v0}, Lcom/android/tools/r8/DiagnosticsHandler;->warning(Lcom/android/tools/r8/Diagnostic;)V

    .line 58
    new-instance p0, Lcom/android/tools/r8/internal/u2;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/u2;-><init>()V

    return-object p0
.end method

.method public static a(Ljava/lang/String;)Z
    .locals 1

    const-string v0, "resources/new_api_database.ser"

    .line 1
    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method public static b()I
    .locals 1

    const/16 v0, 0x12

    return v0
.end method

.method public static b(I)I
    .locals 1

    mul-int/lit8 p0, p0, 0x6

    .line 2
    invoke-static {}, Lcom/android/tools/r8/internal/w2;->a()I

    move-result v0

    add-int/2addr v0, p0

    return v0
.end method

.method public static c()I
    .locals 1

    const/16 v0, 0x11

    return v0
.end method


# virtual methods
.method public abstract a(II[BLjava/util/function/BiPredicate;)I
.end method

.method public final a(I[B)Z
    .locals 3

    .line 68
    invoke-static {}, Lcom/android/tools/r8/internal/w2;->a()I

    move-result v0

    mul-int/lit8 p1, p1, 0x6

    add-int/2addr p1, v0

    .line 69
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/w2;->c(I)Lcom/android/tools/r8/internal/v2;

    move-result-object p1

    .line 70
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 71
    sget-object v0, Lcom/android/tools/r8/internal/v2;->c:Lcom/android/tools/r8/internal/v2;

    const/4 v1, 0x0

    if-ne p1, v0, :cond_0

    return v1

    .line 72
    :cond_0
    array-length v0, p2

    .line 73
    iget v2, p1, Lcom/android/tools/r8/internal/v2;->b:I

    if-eq v0, v2, :cond_1

    return v1

    .line 74
    :cond_1
    iget v0, p0, Lcom/android/tools/r8/internal/w2;->a:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_2

    .line 75
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w2;->d()I

    move-result v0

    iput v0, p0, Lcom/android/tools/r8/internal/w2;->a:I

    .line 77
    :cond_2
    iget v0, p0, Lcom/android/tools/r8/internal/w2;->a:I

    .line 78
    invoke-static {}, Lcom/android/tools/r8/internal/w2;->b()I

    move-result v1

    const/4 v2, 0x1

    shl-int v1, v2, v1

    mul-int/lit8 v1, v1, 0x6

    .line 79
    invoke-static {v0}, Lcom/android/tools/r8/internal/w2;->a(I)I

    move-result v0

    add-int/2addr v0, v1

    .line 80
    iget v1, p1, Lcom/android/tools/r8/internal/v2;->a:I

    add-int/2addr v0, v1

    .line 81
    iget p1, p1, Lcom/android/tools/r8/internal/v2;->b:I

    .line 82
    invoke-virtual {p0, p2, v0, p1}, Lcom/android/tools/r8/internal/w2;->a([BII)Z

    move-result p1

    return p1
.end method

.method public abstract a([BII)Z
.end method

.method public abstract b([BII)B
.end method

.method public final b(Lcom/android/tools/r8/graph/I2;)I
    .locals 5

    .line 3
    invoke-static {p1}, Lcom/android/tools/r8/internal/w2;->a(Lcom/android/tools/r8/graph/I2;)I

    move-result v0

    .line 4
    iget v1, p0, Lcom/android/tools/r8/internal/w2;->a:I

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w2;->d()I

    move-result v1

    iput v1, p0, Lcom/android/tools/r8/internal/w2;->a:I

    .line 7
    :cond_0
    iget v1, p0, Lcom/android/tools/r8/internal/w2;->a:I

    .line 8
    invoke-static {v1}, Lcom/android/tools/r8/internal/w2;->b(I)I

    move-result v1

    mul-int/lit8 v0, v0, 0x6

    add-int/2addr v0, v1

    .line 9
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/w2;->c(I)Lcom/android/tools/r8/internal/v2;

    move-result-object v0

    .line 10
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 11
    sget-object v1, Lcom/android/tools/r8/internal/v2;->c:Lcom/android/tools/r8/internal/v2;

    if-ne v0, v1, :cond_1

    return v2

    .line 12
    :cond_1
    iget v1, v0, Lcom/android/tools/r8/internal/v2;->a:I

    .line 13
    iget v0, v0, Lcom/android/tools/r8/internal/v2;->b:I

    if-gez v1, :cond_5

    .line 14
    sget-boolean v0, Lcom/android/tools/r8/internal/w2;->b:Z

    if-nez v0, :cond_3

    if-gez v1, :cond_2

    goto :goto_0

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 15
    :cond_3
    :goto_0
    sget-boolean v0, Lcom/android/tools/r8/internal/b8;->a:Z

    const v0, 0x7fffffff

    and-int/2addr v0, v1

    .line 16
    iget-object p1, p1, Lcom/android/tools/r8/graph/I2;->f:[B

    invoke-virtual {p0, v0, p1}, Lcom/android/tools/r8/internal/w2;->a(I[B)Z

    move-result p1

    if-eqz p1, :cond_4

    return v0

    :cond_4
    return v2

    .line 20
    :cond_5
    sget-boolean v3, Lcom/android/tools/r8/internal/w2;->b:Z

    if-nez v3, :cond_7

    if-lez v0, :cond_6

    goto :goto_1

    :cond_6
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 21
    :cond_7
    :goto_1
    iget v3, p0, Lcom/android/tools/r8/internal/w2;->a:I

    if-ne v3, v2, :cond_8

    .line 22
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w2;->d()I

    move-result v2

    iput v2, p0, Lcom/android/tools/r8/internal/w2;->a:I

    .line 24
    :cond_8
    iget v2, p0, Lcom/android/tools/r8/internal/w2;->a:I

    .line 25
    invoke-static {}, Lcom/android/tools/r8/internal/w2;->b()I

    move-result v3

    const/4 v4, 0x1

    shl-int v3, v4, v3

    mul-int/lit8 v3, v3, 0x6

    .line 26
    invoke-static {v2}, Lcom/android/tools/r8/internal/w2;->a(I)I

    move-result v2

    add-int/2addr v2, v3

    add-int/2addr v2, v1

    .line 27
    iget-object p1, p1, Lcom/android/tools/r8/graph/I2;->f:[B

    new-instance v1, Lcom/android/tools/r8/internal/w2$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/w2$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/w2;)V

    .line 28
    invoke-virtual {p0, v2, v0, p1, v1}, Lcom/android/tools/r8/internal/w2;->a(II[BLjava/util/function/BiPredicate;)I

    move-result p1

    return p1
.end method

.method public abstract c(I)Lcom/android/tools/r8/internal/v2;
.end method

.method public abstract d()I
.end method
