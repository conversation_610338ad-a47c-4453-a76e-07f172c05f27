.class public final synthetic Lcom/android/tools/r8/internal/pf$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Ljava/util/Set;


# direct methods
.method public synthetic constructor <init>(Ljava/util/Set;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/pf$$ExternalSyntheticLambda5;->f$0:Ljava/util/Set;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/pf$$ExternalSyntheticLambda5;->f$0:Ljava/util/Set;

    check-cast p1, Lcom/android/tools/r8/internal/of;

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/pf;->d(Ljava/util/Set;Lcom/android/tools/r8/internal/of;)V

    return-void
.end method
