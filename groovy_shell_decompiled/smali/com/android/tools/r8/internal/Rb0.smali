.class public abstract Lcom/android/tools/r8/internal/Rb0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final a:Lcom/android/tools/r8/internal/Vb0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    :try_start_0
    const-string v0, "kotlin.reflect.jvm.internal.ReflectionFactoryImpl"

    .line 1
    invoke-static {v0}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    .line 2
    invoke-virtual {v0}, Ljava/lang/Class;->newInstance()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/Vb0;
    :try_end_0
    .catch Ljava/lang/ClassCastException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/InstantiationException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_0

    goto :goto_1

    .line 9
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/Vb0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Vb0;-><init>()V

    :goto_1
    sput-object v0, Lcom/android/tools/r8/internal/Rb0;->a:Lcom/android/tools/r8/internal/Vb0;

    return-void
.end method

.method public static a(Ljava/lang/Class;Ljava/lang/String;)Lcom/android/tools/r8/internal/m30;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Rb0;->a:Lcom/android/tools/r8/internal/Vb0;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/m30;

    invoke-direct {v0, p0, p1}, Lcom/android/tools/r8/internal/m30;-><init>(Ljava/lang/Class;Ljava/lang/String;)V

    return-object v0
.end method

.method public static a(Ljava/lang/Class;)Lcom/android/tools/r8/internal/wd;
    .locals 1

    .line 3
    sget-object v0, Lcom/android/tools/r8/internal/Rb0;->a:Lcom/android/tools/r8/internal/Vb0;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 4
    new-instance v0, Lcom/android/tools/r8/internal/wd;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/wd;-><init>(Ljava/lang/Class;)V

    return-object v0
.end method
