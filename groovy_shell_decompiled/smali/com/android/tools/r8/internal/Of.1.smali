.class public final Lcom/android/tools/r8/internal/Of;
.super Lcom/android/tools/r8/internal/ag;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic g:Z = true


# instance fields
.field public e:Lcom/android/tools/r8/internal/E1;

.field public f:Lcom/android/tools/r8/internal/Gs;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/Gs;Ljava/util/Set;)V
    .locals 0

    .line 1
    invoke-direct {p0, p3}, Lcom/android/tools/r8/internal/ag;-><init>(Ljava/util/Set;)V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    .line 4
    sget-boolean p1, Lcom/android/tools/r8/internal/Of;->g:Z

    if-nez p1, :cond_1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/bg;->s()Z

    move-result p2

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    const-string p2, "Must use BottomClassTypeParameterState instead"

    invoke-direct {p1, p2}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p1

    :cond_1
    :goto_0
    if-nez p1, :cond_3

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Of;->u()Z

    move-result p1

    if-nez p1, :cond_2

    goto :goto_1

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    const-string p2, "Must use UnknownParameterState instead"

    invoke-direct {p1, p2}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p1

    :cond_3
    :goto_1
    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/Gs;Ljava/util/Set;)Lcom/android/tools/r8/internal/SY;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Gs;->l()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    sget-object p0, Lcom/android/tools/r8/internal/Fs0;->b:Lcom/android/tools/r8/internal/Fs0;

    goto :goto_0

    .line 3
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/Of;

    invoke-direct {v0, p0, p1, p2}, Lcom/android/tools/r8/internal/Of;-><init>(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/Gs;Ljava/util/Set;)V

    move-object p0, v0

    :goto_0
    return-object p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/E1;
    .locals 1

    .line 13
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    .line 14
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Gs;->d()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    .line 15
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qZ;->e()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 16
    sget-boolean v0, Lcom/android/tools/r8/internal/Of;->g:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 17
    instance-of v0, v0, Lcom/android/tools/r8/internal/gk0;

    if-nez v0, :cond_1

    .line 18
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    .line 19
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 20
    instance-of v0, v0, Lcom/android/tools/r8/internal/oZ;

    if-nez v0, :cond_1

    .line 21
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    .line 22
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 23
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 24
    :cond_1
    :goto_0
    iget-object p1, p1, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 25
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    sget-object p1, Lcom/android/tools/r8/internal/gk0;->b:Lcom/android/tools/r8/internal/gk0;

    return-object p1

    .line 27
    :cond_2
    iget-object p1, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/Gt0;
    .locals 2

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    invoke-static {p1, p2, v0}, Lcom/android/tools/r8/internal/ag;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/Gs;)Lcom/android/tools/r8/internal/Gs;

    move-result-object p1

    .line 5
    iget-object p2, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/Gs;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_0

    return-object p0

    .line 8
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Gs;->g()Z

    move-result p2

    if-eqz p2, :cond_1

    .line 9
    sget-object p1, Lcom/android/tools/r8/internal/f7;->b:Lcom/android/tools/r8/internal/f7;

    return-object p1

    .line 11
    :cond_1
    sget-boolean p2, Lcom/android/tools/r8/internal/Of;->g:Z

    if-nez p2, :cond_3

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Gs;->h()Z

    move-result p2

    if-eqz p2, :cond_2

    goto :goto_0

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 12
    :cond_3
    :goto_0
    new-instance p2, Lcom/android/tools/r8/internal/Of;

    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/bg;->o()Ljava/util/Set;

    move-result-object v1

    invoke-direct {p2, v0, p1, v1}, Lcom/android/tools/r8/internal/Of;-><init>(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/Gs;Ljava/util/Set;)V

    return-object p2
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/ag;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/a2;)Lcom/android/tools/r8/internal/SY;
    .locals 6

    .line 29
    sget-boolean v0, Lcom/android/tools/r8/internal/Of;->g:Z

    if-nez v0, :cond_1

    invoke-virtual {p4}, Lcom/android/tools/r8/graph/J2;->L0()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 31
    :cond_1
    :goto_0
    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/Gt0;->a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/E1;

    move-result-object v0

    .line 32
    iget-object v1, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    .line 33
    iget-object v2, p1, Lcom/android/tools/r8/graph/y;->w:Lcom/android/tools/r8/internal/I1;

    .line 34
    iget-object v3, v2, Lcom/android/tools/r8/internal/J1;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p4, v3}, Lcom/android/tools/r8/graph/J2;->b(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v3

    .line 35
    invoke-virtual {v2, v1, v0, v3}, Lcom/android/tools/r8/internal/J1;->a(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/E1;

    move-result-object v4

    .line 36
    sget-boolean v5, Lcom/android/tools/r8/internal/I1;->c:Z

    if-nez v5, :cond_3

    invoke-virtual {v2, v0, v1, v3}, Lcom/android/tools/r8/internal/J1;->a(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/E1;

    move-result-object v0

    invoke-virtual {v4, v0}, Lcom/android/tools/r8/internal/E1;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_1

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 37
    :cond_3
    :goto_1
    iput-object v4, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    .line 38
    invoke-virtual {v4, v1}, Lcom/android/tools/r8/internal/E1;->equals(Ljava/lang/Object;)Z

    move-result v0

    .line 39
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/ag;->x()Lcom/android/tools/r8/internal/Gs;

    move-result-object v1

    .line 40
    iget-object v2, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    .line 42
    invoke-virtual {v2, p1, v1, p3, p4}, Lcom/android/tools/r8/internal/Gs;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Gs;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/Gs;

    move-result-object p3

    .line 43
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    .line 44
    invoke-static {p1, p3, p4, v1}, Lcom/android/tools/r8/internal/Fu0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Gs;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;)Lcom/android/tools/r8/internal/Gs;

    move-result-object p3

    .line 45
    iput-object p3, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    .line 46
    invoke-virtual {p3, v2}, Lcom/android/tools/r8/internal/Gs;->equals(Ljava/lang/Object;)Z

    move-result p3

    .line 47
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Of;->u()Z

    move-result p4

    if-eqz p4, :cond_4

    .line 48
    sget-object p1, Lcom/android/tools/r8/internal/Fs0;->b:Lcom/android/tools/r8/internal/Fs0;

    return-object p1

    .line 50
    :cond_4
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/bg;->a(Lcom/android/tools/r8/internal/bg;)Z

    move-result p2

    .line 51
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/bg;->b(Lcom/android/tools/r8/graph/y;)Z

    move-result p1

    if-eqz p1, :cond_5

    .line 52
    sget-object p1, Lcom/android/tools/r8/internal/Fs0;->b:Lcom/android/tools/r8/internal/Fs0;

    return-object p1

    :cond_5
    if-eqz v0, :cond_6

    if-eqz p3, :cond_6

    if-eqz p2, :cond_7

    .line 55
    :cond_6
    invoke-interface {p5}, Lcom/android/tools/r8/internal/a2;->a()V

    :cond_7
    return-object p0
.end method

.method public final a(Ljava/util/function/Supplier;)Lcom/android/tools/r8/internal/bg;
    .locals 3

    .line 28
    new-instance v0, Lcom/android/tools/r8/internal/Of;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    iget-object v2, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    invoke-interface {p1}, Ljava/util/function/Supplier;->get()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Set;

    invoke-direct {v0, v1, v2, p1}, Lcom/android/tools/r8/internal/Of;-><init>(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/Gs;Ljava/util/Set;)V

    return-object v0
.end method

.method public final b()Lcom/android/tools/r8/internal/Of;
    .locals 0

    return-object p0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/Of;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/Of;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    iget-object v3, p1, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/E1;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    iget-object v3, p1, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    .line 6
    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/Gs;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/bg;->q()Ljava/util/Set;

    move-result-object v1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/bg;->q()Ljava/util/Set;

    move-result-object p1

    invoke-interface {v1, p1}, Ljava/util/Set;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public final hashCode()I
    .locals 6

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/bg;->q()Ljava/util/Set;

    move-result-object v2

    const/4 v3, 0x4

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    const-class v5, Lcom/android/tools/r8/internal/Of;

    aput-object v5, v3, v4

    const/4 v4, 0x1

    aput-object v0, v3, v4

    const/4 v0, 0x2

    aput-object v1, v3, v0

    const/4 v0, 0x3

    aput-object v2, v3, v0

    invoke-static {v3}, Ljava/util/Objects;->hash([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final p()Lcom/android/tools/r8/internal/p7;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/f7;->b:Lcom/android/tools/r8/internal/f7;

    return-object v0
.end method

.method public final t()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    instance-of v0, v0, Lcom/android/tools/r8/internal/o7;

    if-eqz v0, :cond_0

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Gs;->g()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 4

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/Of;->g:Z

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/bg;->r()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 2
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "ClassState(type: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ", value: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final u()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->e:Lcom/android/tools/r8/internal/E1;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Gs;->l()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final x()Lcom/android/tools/r8/internal/Gs;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    return-object v0
.end method

.method public final y()Lcom/android/tools/r8/internal/qZ;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Gs;->d()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    return-object v0
.end method
