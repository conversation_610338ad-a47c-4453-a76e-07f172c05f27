.class public final Lcom/android/tools/r8/internal/u0;
.super Lcom/android/tools/r8/internal/y0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/util/RandomAccess;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/z0;Ljava/lang/Object;Ljava/util/List;Lcom/android/tools/r8/internal/w0;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, Lcom/android/tools/r8/internal/y0;-><init>(Lcom/android/tools/r8/internal/z0;Ljava/lang/Object;Ljava/util/List;Lcom/android/tools/r8/internal/w0;)V

    return-void
.end method
