.class public final Lcom/android/tools/r8/internal/Uc;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/internal/cB;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 3

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object p1

    .line 4
    invoke-static {}, Lcom/android/tools/r8/internal/cB;->h()Lcom/android/tools/r8/internal/ZA;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->x1:Lcom/android/tools/r8/graph/J2;

    .line 5
    new-instance v2, Lcom/android/tools/r8/internal/to0;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/to0;-><init>(Lcom/android/tools/r8/graph/J2;)V

    .line 6
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->y1:Lcom/android/tools/r8/graph/J2;

    .line 7
    new-instance v2, Lcom/android/tools/r8/internal/to0;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/to0;-><init>(Lcom/android/tools/r8/graph/J2;)V

    .line 8
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->z1:Lcom/android/tools/r8/graph/J2;

    .line 9
    new-instance v2, Lcom/android/tools/r8/internal/to0;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/to0;-><init>(Lcom/android/tools/r8/graph/J2;)V

    .line 10
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->C1:Lcom/android/tools/r8/graph/J2;

    .line 11
    new-instance v2, Lcom/android/tools/r8/internal/to0;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/to0;-><init>(Lcom/android/tools/r8/graph/J2;)V

    .line 12
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    move-result-object v0

    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->E1:Lcom/android/tools/r8/graph/J2;

    .line 13
    new-instance v1, Lcom/android/tools/r8/internal/to0;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/internal/to0;-><init>(Lcom/android/tools/r8/graph/J2;)V

    .line 14
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    move-result-object p1

    .line 15
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ZA;->a()Lcom/android/tools/r8/internal/cB;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/Uc;->a:Lcom/android/tools/r8/internal/cB;

    return-void
.end method
