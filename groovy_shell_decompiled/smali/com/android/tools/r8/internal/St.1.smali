.class public final Lcom/android/tools/r8/internal/St;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic e:Z


# instance fields
.field public final a:Lcom/android/tools/r8/graph/E2;

.field public final b:Lcom/android/tools/r8/internal/JS;

.field public final c:Ljava/util/Set;

.field public final d:Lcom/android/tools/r8/internal/LB;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/Tt;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/St;->e:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/internal/LB;Lcom/android/tools/r8/internal/Fy;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-static {}, Lcom/android/tools/r8/internal/Yi0;->a()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/St;->c:Ljava/util/Set;

    const/4 v0, 0x0

    .line 4
    iput-object v0, p0, Lcom/android/tools/r8/internal/St;->d:Lcom/android/tools/r8/internal/LB;

    .line 11
    sget-boolean v0, Lcom/android/tools/r8/internal/St;->e:Z

    if-nez v0, :cond_1

    .line 12
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object p1

    if-ne p1, p4, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 13
    :cond_1
    :goto_0
    iput-object p2, p0, Lcom/android/tools/r8/internal/St;->a:Lcom/android/tools/r8/graph/E2;

    .line 14
    iput-object p3, p0, Lcom/android/tools/r8/internal/St;->d:Lcom/android/tools/r8/internal/LB;

    .line 16
    invoke-static {p4}, Lcom/android/tools/r8/internal/JS;->a(Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/JS;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/St;->b:Lcom/android/tools/r8/internal/JS;

    return-void
.end method
