.class public final Lcom/android/tools/r8/internal/rz;
.super Lcom/android/tools/r8/internal/Sc;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic h:Z


# instance fields
.field public final a:Lcom/android/tools/r8/internal/c6;

.field public final b:Lcom/android/tools/r8/internal/a6;

.field public final c:Lcom/android/tools/r8/internal/c6;

.field public final d:Ljava/util/IdentityHashMap;

.field public final e:Lcom/android/tools/r8/internal/a6;

.field public final f:Lcom/android/tools/r8/internal/c6;

.field public final g:Lcom/android/tools/r8/internal/c6;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/sz;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/rz;->h:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Sc;-><init>()V

    .line 2
    invoke-static {}, Lcom/android/tools/r8/internal/c6;->a()Lcom/android/tools/r8/internal/c6;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/rz;->a:Lcom/android/tools/r8/internal/c6;

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/a6;

    new-instance v1, Ljava/util/IdentityHashMap;

    invoke-direct {v1}, Ljava/util/IdentityHashMap;-><init>()V

    new-instance v2, Ljava/util/IdentityHashMap;

    invoke-direct {v2}, Ljava/util/IdentityHashMap;-><init>()V

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/a6;-><init>(Ljava/util/AbstractMap;Ljava/util/AbstractMap;)V

    .line 4
    iput-object v0, p0, Lcom/android/tools/r8/internal/rz;->b:Lcom/android/tools/r8/internal/a6;

    .line 6
    invoke-static {}, Lcom/android/tools/r8/internal/c6;->a()Lcom/android/tools/r8/internal/c6;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/rz;->c:Lcom/android/tools/r8/internal/c6;

    .line 7
    new-instance v0, Ljava/util/IdentityHashMap;

    invoke-direct {v0}, Ljava/util/IdentityHashMap;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/internal/rz;->d:Ljava/util/IdentityHashMap;

    .line 8
    new-instance v0, Lcom/android/tools/r8/internal/a6;

    new-instance v1, Ljava/util/IdentityHashMap;

    invoke-direct {v1}, Ljava/util/IdentityHashMap;-><init>()V

    new-instance v2, Ljava/util/IdentityHashMap;

    invoke-direct {v2}, Ljava/util/IdentityHashMap;-><init>()V

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/a6;-><init>(Ljava/util/AbstractMap;Ljava/util/AbstractMap;)V

    .line 9
    iput-object v0, p0, Lcom/android/tools/r8/internal/rz;->e:Lcom/android/tools/r8/internal/a6;

    .line 12
    invoke-static {}, Lcom/android/tools/r8/internal/c6;->a()Lcom/android/tools/r8/internal/c6;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/rz;->f:Lcom/android/tools/r8/internal/c6;

    .line 15
    invoke-static {}, Lcom/android/tools/r8/internal/c6;->a()Lcom/android/tools/r8/internal/c6;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/rz;->g:Lcom/android/tools/r8/internal/c6;

    return-void
.end method

.method public static b(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/l1;)Z
    .locals 0

    .line 1
    invoke-virtual {p1, p0}, Lcom/android/tools/r8/graph/l1;->a(Lcom/android/tools/r8/graph/l1;)Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method


# virtual methods
.method public final bridge synthetic a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sU;)Lcom/android/tools/r8/internal/Tc;
    .locals 0

    .line 1
    check-cast p2, Lcom/android/tools/r8/internal/xz;

    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/rz;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/xz;)Lcom/android/tools/r8/internal/sz;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/xz;)Lcom/android/tools/r8/internal/sz;
    .locals 8

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/rz;->h:Z

    if-nez v0, :cond_1

    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->e:Lcom/android/tools/r8/internal/a6;

    .line 3
    iget-object v1, v1, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    .line 4
    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    if-nez v0, :cond_3

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->f:Lcom/android/tools/r8/internal/c6;

    .line 7
    iget-object v1, v1, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    .line 8
    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_1

    .line 9
    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_3
    :goto_1
    if-nez v0, :cond_5

    .line 10
    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->g:Lcom/android/tools/r8/internal/c6;

    .line 11
    iget-object v1, v1, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    .line 12
    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_4

    goto :goto_2

    .line 13
    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_5
    :goto_2
    if-nez v0, :cond_7

    .line 14
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->c:Lcom/android/tools/r8/internal/c6;

    .line 15
    iget-object v0, v0, Lcom/android/tools/r8/internal/a6;->c:Ljava/util/AbstractMap;

    .line 16
    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    .line 17
    invoke-interface {v0}, Ljava/util/Set;->stream()Ljava/util/stream/Stream;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/internal/rz$$ExternalSyntheticLambda3;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/rz$$ExternalSyntheticLambda3;-><init>(Lcom/android/tools/r8/internal/rz;)V

    .line 18
    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->allMatch(Ljava/util/function/Predicate;)Z

    move-result v0

    if-eqz v0, :cond_6

    goto :goto_3

    .line 19
    :cond_6
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 26
    :cond_7
    :goto_3
    new-instance v7, Lcom/android/tools/r8/internal/sz;

    iget-object v3, p0, Lcom/android/tools/r8/internal/rz;->d:Ljava/util/IdentityHashMap;

    iget-object v4, p0, Lcom/android/tools/r8/internal/rz;->a:Lcom/android/tools/r8/internal/c6;

    iget-object v5, p0, Lcom/android/tools/r8/internal/rz;->b:Lcom/android/tools/r8/internal/a6;

    iget-object v6, p0, Lcom/android/tools/r8/internal/rz;->c:Lcom/android/tools/r8/internal/c6;

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v6}, Lcom/android/tools/r8/internal/sz;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/xz;Ljava/util/IdentityHashMap;Lcom/android/tools/r8/internal/c6;Lcom/android/tools/r8/internal/a6;Lcom/android/tools/r8/internal/c6;)V

    return-object v7
.end method

.method public final a()V
    .locals 3

    .line 80
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->b:Lcom/android/tools/r8/internal/a6;

    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->e:Lcom/android/tools/r8/internal/a6;

    .line 81
    iget-object v1, v1, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    .line 82
    invoke-interface {v1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v1

    .line 83
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/a6;->b(Ljava/util/Set;)V

    .line 84
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->e:Lcom/android/tools/r8/internal/a6;

    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->b:Lcom/android/tools/r8/internal/a6;

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/internal/rz$$ExternalSyntheticLambda0;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/rz$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/gX;)V

    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/a6;->a(Ljava/util/function/BiConsumer;)V

    .line 85
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->e:Lcom/android/tools/r8/internal/a6;

    .line 86
    iget-object v1, v0, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    .line 87
    invoke-interface {v1}, Ljava/util/Map;->clear()V

    .line 88
    iget-object v0, v0, Lcom/android/tools/r8/internal/a6;->c:Ljava/util/AbstractMap;

    invoke-interface {v0}, Ljava/util/Map;->clear()V

    .line 89
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->a:Lcom/android/tools/r8/internal/c6;

    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->f:Lcom/android/tools/r8/internal/c6;

    .line 90
    iget-object v2, v1, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    .line 91
    invoke-interface {v2}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v2

    .line 92
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/a6;->b(Ljava/util/Set;)V

    .line 93
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/c6;->a(Lcom/android/tools/r8/internal/c6;)V

    .line 94
    iget-object v0, v1, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    invoke-interface {v0}, Ljava/util/Map;->clear()V

    .line 95
    iget-object v0, v1, Lcom/android/tools/r8/internal/a6;->c:Ljava/util/AbstractMap;

    invoke-interface {v0}, Ljava/util/Map;->clear()V

    .line 96
    iget-object v0, v1, Lcom/android/tools/r8/internal/c6;->e:Ljava/util/IdentityHashMap;

    invoke-virtual {v0}, Ljava/util/IdentityHashMap;->clear()V

    .line 97
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->c:Lcom/android/tools/r8/internal/c6;

    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->g:Lcom/android/tools/r8/internal/c6;

    .line 98
    iget-object v2, v1, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    .line 99
    invoke-interface {v2}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v2

    .line 100
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/a6;->b(Ljava/util/Set;)V

    .line 101
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/c6;->a(Lcom/android/tools/r8/internal/c6;)V

    .line 102
    iget-object v0, v1, Lcom/android/tools/r8/internal/a6;->b:Ljava/util/AbstractMap;

    invoke-interface {v0}, Ljava/util/Map;->clear()V

    .line 103
    iget-object v0, v1, Lcom/android/tools/r8/internal/a6;->c:Ljava/util/AbstractMap;

    invoke-interface {v0}, Ljava/util/Map;->clear()V

    .line 104
    iget-object v0, v1, Lcom/android/tools/r8/internal/c6;->e:Ljava/util/IdentityHashMap;

    invoke-virtual {v0}, Ljava/util/IdentityHashMap;->clear()V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/l1;)V
    .locals 4

    .line 57
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->a:Lcom/android/tools/r8/internal/c6;

    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->f:Lcom/android/tools/r8/internal/c6;

    .line 58
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/a6;->d(Ljava/lang/Object;)Ljava/util/Set;

    move-result-object v2

    .line 59
    invoke-interface {v2}, Ljava/util/Set;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_0

    .line 60
    invoke-virtual {v1, p1, p2}, Lcom/android/tools/r8/internal/a6;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 62
    :cond_0
    invoke-virtual {v1, v2, p2}, Lcom/android/tools/r8/internal/a6;->a(Ljava/lang/Iterable;Ljava/lang/Object;)V

    .line 63
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/c6;->c(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/s2;

    if-eqz p1, :cond_1

    .line 64
    iget-object v0, v1, Lcom/android/tools/r8/internal/c6;->e:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p2, p1}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    :goto_0
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)V
    .locals 4

    .line 67
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->b:Lcom/android/tools/r8/internal/a6;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/a6;->d(Ljava/lang/Object;)Ljava/util/Set;

    move-result-object v0

    .line 68
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 69
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->e:Lcom/android/tools/r8/internal/a6;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/a6;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 71
    :cond_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->e:Lcom/android/tools/r8/internal/a6;

    invoke-virtual {v1, v0, p2}, Lcom/android/tools/r8/internal/a6;->a(Ljava/lang/Iterable;Ljava/lang/Object;)V

    .line 72
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->c:Lcom/android/tools/r8/internal/c6;

    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->g:Lcom/android/tools/r8/internal/c6;

    .line 73
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/a6;->d(Ljava/lang/Object;)Ljava/util/Set;

    move-result-object v2

    .line 74
    invoke-interface {v2}, Ljava/util/Set;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_1

    .line 75
    invoke-virtual {v1, p1, p2}, Lcom/android/tools/r8/internal/a6;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 77
    :cond_1
    invoke-virtual {v1, v2, p2}, Lcom/android/tools/r8/internal/a6;->a(Ljava/lang/Iterable;Ljava/lang/Object;)V

    .line 78
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/c6;->c(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/s2;

    if-eqz p1, :cond_2

    .line 79
    iget-object v0, v1, Lcom/android/tools/r8/internal/c6;->e:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p2, p1}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    :goto_1
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Z)V
    .locals 1

    .line 65
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->b:Lcom/android/tools/r8/internal/a6;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/a6;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 66
    invoke-virtual {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/rz;->b(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Z)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/QI;Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/l1;)V
    .locals 4

    .line 31
    sget-boolean v0, Lcom/android/tools/r8/internal/rz;->h:Z

    if-nez v0, :cond_1

    invoke-static {p1}, Lcom/android/tools/r8/internal/um0;->a(Ljava/lang/Iterable;)Ljava/util/stream/Stream;

    move-result-object v1

    new-instance v2, Lcom/android/tools/r8/internal/rz$$ExternalSyntheticLambda1;

    invoke-direct {v2, p2}, Lcom/android/tools/r8/internal/rz$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/graph/l1;)V

    .line 32
    invoke-interface {v1, v2}, Ljava/util/stream/Stream;->anyMatch(Ljava/util/function/Predicate;)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    .line 33
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    if-nez v0, :cond_3

    .line 35
    invoke-static {p1}, Lcom/android/tools/r8/internal/um0;->a(Ljava/lang/Iterable;)Ljava/util/stream/Stream;

    move-result-object v1

    iget-object v2, p0, Lcom/android/tools/r8/internal/rz;->a:Lcom/android/tools/r8/internal/c6;

    invoke-static {v2}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v3, Lcom/android/tools/r8/internal/rz$$ExternalSyntheticLambda2;

    invoke-direct {v3, v2}, Lcom/android/tools/r8/internal/rz$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/internal/c6;)V

    invoke-interface {v1, v3}, Ljava/util/stream/Stream;->noneMatch(Ljava/util/function/Predicate;)Z

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_1

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_3
    :goto_1
    if-nez v0, :cond_9

    .line 36
    instance-of v0, p1, Ljava/util/Collection;

    if-eqz v0, :cond_4

    .line 37
    move-object v0, p1

    check-cast v0, Ljava/util/Collection;

    .line 38
    :try_start_0
    invoke-interface {v0, p3}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result v0
    :try_end_0
    .catch Ljava/lang/ClassCastException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/NullPointerException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_3

    .line 39
    :cond_4
    iget-object v0, p1, Lcom/android/tools/r8/internal/QI;->b:Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/internal/QI;->c:Lcom/android/tools/r8/internal/Hx;

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/hJ;->a(Ljava/util/Iterator;Lcom/android/tools/r8/internal/Hx;)Lcom/android/tools/r8/internal/aJ;

    move-result-object v0

    if-nez p3, :cond_6

    .line 40
    :cond_5
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Wp0;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_7

    .line 41
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Wp0;->next()Ljava/lang/Object;

    move-result-object v1

    if-nez v1, :cond_5

    goto :goto_2

    .line 46
    :cond_6
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Wp0;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_7

    .line 47
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Wp0;->next()Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {p3, v1}, Lcom/android/tools/r8/graph/E;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    :goto_2
    const/4 v0, 0x1

    goto :goto_3

    :catch_0
    :cond_7
    const/4 v0, 0x0

    :goto_3
    if-eqz v0, :cond_8

    goto :goto_4

    .line 48
    :cond_8
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 49
    :cond_9
    :goto_4
    iget-object v0, p1, Lcom/android/tools/r8/internal/QI;->b:Ljava/lang/Iterable;

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    iget-object p1, p1, Lcom/android/tools/r8/internal/QI;->c:Lcom/android/tools/r8/internal/Hx;

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/hJ;->a(Ljava/util/Iterator;Lcom/android/tools/r8/internal/Hx;)Lcom/android/tools/r8/internal/aJ;

    move-result-object p1

    .line 50
    :goto_5
    iget-object v0, p1, Lcom/android/tools/r8/internal/Wp0;->b:Ljava/util/Iterator;

    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_a

    .line 51
    iget-object v0, p1, Lcom/android/tools/r8/internal/Wp0;->b:Ljava/util/Iterator;

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/aJ;->a(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    .line 52
    check-cast v0, Lcom/android/tools/r8/graph/l1;

    .line 53
    iget-object v1, p0, Lcom/android/tools/r8/internal/rz;->a:Lcom/android/tools/r8/internal/c6;

    invoke-virtual {v1, v0, p2}, Lcom/android/tools/r8/internal/a6;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_5

    .line 54
    :cond_a
    iget-object p1, p0, Lcom/android/tools/r8/internal/rz;->a:Lcom/android/tools/r8/internal/c6;

    .line 55
    iget-object p1, p1, Lcom/android/tools/r8/internal/c6;->e:Ljava/util/IdentityHashMap;

    .line 56
    invoke-virtual {p1, p2, p3}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;)Z
    .locals 2

    .line 27
    sget-boolean v0, Lcom/android/tools/r8/internal/rz;->h:Z

    const/4 v1, 0x1

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->c:Lcom/android/tools/r8/internal/c6;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/a6;->d(Ljava/lang/Object;)Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->size()I

    move-result v0

    if-eq v0, v1, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->c:Lcom/android/tools/r8/internal/c6;

    .line 28
    iget-object v0, v0, Lcom/android/tools/r8/internal/c6;->e:Ljava/util/IdentityHashMap;

    .line 29
    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    .line 30
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return v1
.end method

.method public final b(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)V
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->b:Lcom/android/tools/r8/internal/a6;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/a6;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public final b(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Z)V
    .locals 1

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/rz;->c:Lcom/android/tools/r8/internal/c6;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/a6;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz p3, :cond_0

    .line 5
    iget-object p3, p0, Lcom/android/tools/r8/internal/rz;->c:Lcom/android/tools/r8/internal/c6;

    .line 6
    iget-object p3, p3, Lcom/android/tools/r8/internal/c6;->e:Ljava/util/IdentityHashMap;

    .line 7
    invoke-virtual {p3, p2, p1}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-void
.end method

.method public final c(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)V
    .locals 1

    const/4 v0, 0x0

    .line 1
    invoke-virtual {p0, p1, p2, v0}, Lcom/android/tools/r8/internal/rz;->b(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Z)V

    return-void
.end method
