.class public final synthetic Lcom/android/tools/r8/internal/Bj0$$ExternalSyntheticLambda8;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Bj0;

.field public final synthetic f$1:I

.field public final synthetic f$2:Z


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Bj0;IZ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Bj0$$ExternalSyntheticLambda8;->f$0:Lcom/android/tools/r8/internal/Bj0;

    iput p2, p0, Lcom/android/tools/r8/internal/Bj0$$ExternalSyntheticLambda8;->f$1:I

    iput-boolean p3, p0, Lcom/android/tools/r8/internal/Bj0$$ExternalSyntheticLambda8;->f$2:Z

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/internal/Bj0$$ExternalSyntheticLambda8;->f$0:Lcom/android/tools/r8/internal/Bj0;

    iget v1, p0, Lcom/android/tools/r8/internal/Bj0$$ExternalSyntheticLambda8;->f$1:I

    iget-boolean v2, p0, Lcom/android/tools/r8/internal/Bj0$$ExternalSyntheticLambda8;->f$2:Z

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/Bj0;->b(IZ)Lcom/android/tools/r8/internal/nu;

    move-result-object v0

    return-object v0
.end method
