.class public final synthetic Lcom/android/tools/r8/internal/ve$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/o5;


# instance fields
.field public final synthetic f$0:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/ve$$ExternalSyntheticLambda0;->f$0:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/H9;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/ve$$ExternalSyntheticLambda0;->f$0:Ljava/lang/String;

    invoke-static {v0, p1, p2}, Lcom/android/tools/r8/internal/ve;->a(Ljava/lang/String;Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/H9;

    move-result-object p1

    return-object p1
.end method
