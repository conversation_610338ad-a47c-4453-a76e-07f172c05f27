.class public Lcom/android/tools/r8/internal/Cz;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/android/tools/r8/internal/Cz$a;
    }
.end annotation


# static fields
.field public static final synthetic e:Z = true


# instance fields
.field public final a:Ljava/util/function/Consumer;

.field public final b:Lcom/android/tools/r8/profile/art/ArtProfileBuilder;

.field public final c:Lcom/android/tools/r8/profile/art/ArtProfileRulePredicate;

.field public final d:Lcom/android/tools/r8/internal/bd0;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Ljava/util/function/Consumer;Lcom/android/tools/r8/profile/art/ArtProfileBuilder;Lcom/android/tools/r8/profile/art/ArtProfileRulePredicate;Lcom/android/tools/r8/internal/bd0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/Cz;->a:Ljava/util/function/Consumer;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/Cz;->b:Lcom/android/tools/r8/profile/art/ArtProfileBuilder;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/internal/Cz;->c:Lcom/android/tools/r8/profile/art/ArtProfileRulePredicate;

    .line 5
    iput-object p4, p0, Lcom/android/tools/r8/internal/Cz;->d:Lcom/android/tools/r8/internal/bd0;

    return-void
.end method

.method public static a()Lcom/android/tools/r8/internal/Cz$a;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Cz$a;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Cz$a;-><init>()V

    return-object v0
.end method

.method public static a(Ljava/lang/String;Lcom/android/tools/r8/internal/p4$a;)Ljava/lang/String;
    .locals 7

    .line 41
    new-instance v0, Lcom/android/tools/r8/internal/tb;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/tb;-><init>()V

    :goto_0
    const/16 v1, 0x48

    .line 45
    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v2, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda1;

    invoke-direct {v2, p1}, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/p4$a;)V

    .line 46
    invoke-virtual {p0}, Ljava/lang/String;->isEmpty()Z

    move-result v3

    const/4 v4, 0x1

    const/4 v5, 0x0

    if-nez v3, :cond_0

    invoke-virtual {p0, v5}, Ljava/lang/String;->charAt(I)C

    move-result v3

    if-ne v3, v1, :cond_0

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/tb;->a(C)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 47
    invoke-interface {v2}, Lcom/android/tools/r8/internal/a2;->a()V

    .line 48
    invoke-virtual {p0, v4}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    :cond_0
    move-object v1, p0

    :goto_1
    const/16 v2, 0x53

    .line 49
    new-instance v3, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda3;

    invoke-direct {v3, p1}, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda3;-><init>(Lcom/android/tools/r8/internal/p4$a;)V

    .line 50
    invoke-virtual {v1}, Ljava/lang/String;->isEmpty()Z

    move-result v6

    if-nez v6, :cond_1

    invoke-virtual {v1, v5}, Ljava/lang/String;->charAt(I)C

    move-result v6

    if-ne v6, v2, :cond_1

    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/tb;->a(C)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 51
    invoke-interface {v3}, Lcom/android/tools/r8/internal/a2;->a()V

    .line 52
    invoke-virtual {v1, v4}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v1

    :cond_1
    const/16 v2, 0x50

    .line 53
    new-instance v3, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda2;

    invoke-direct {v3, p1}, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/internal/p4$a;)V

    .line 54
    invoke-virtual {v1}, Ljava/lang/String;->isEmpty()Z

    move-result v6

    if-nez v6, :cond_2

    invoke-virtual {v1, v5}, Ljava/lang/String;->charAt(I)C

    move-result v5

    if-ne v5, v2, :cond_2

    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/tb;->a(C)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 55
    invoke-interface {v3}, Lcom/android/tools/r8/internal/a2;->a()V

    .line 56
    invoke-virtual {v1, v4}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v1

    .line 57
    :cond_2
    invoke-virtual {v1, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_3

    return-object v1

    :cond_3
    move-object p0, v1

    goto :goto_0
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/p4;Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleInfoBuilder;)V
    .locals 1

    .line 99
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/p4;->isHot()Z

    move-result v0

    invoke-interface {p1, v0}, Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleInfoBuilder;->setIsHot(Z)Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleInfoBuilder;

    move-result-object p1

    .line 100
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/p4;->isStartup()Z

    move-result v0

    invoke-interface {p1, v0}, Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleInfoBuilder;->setIsStartup(Z)Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleInfoBuilder;

    move-result-object p1

    .line 101
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/p4;->isPostStartup()Z

    move-result p0

    invoke-interface {p1, p0}, Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleInfoBuilder;->setIsPostStartup(Z)Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleInfoBuilder;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/references/ClassReference;Lcom/android/tools/r8/profile/art/ArtProfileClassRuleBuilder;)V
    .locals 0

    .line 75
    invoke-interface {p1, p0}, Lcom/android/tools/r8/profile/art/ArtProfileClassRuleBuilder;->setClassReference(Lcom/android/tools/r8/references/ClassReference;)Lcom/android/tools/r8/profile/art/ArtProfileClassRuleBuilder;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/references/MethodReference;Lcom/android/tools/r8/internal/p4;Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleBuilder;)V
    .locals 0

    .line 97
    invoke-interface {p2, p0}, Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleBuilder;->setMethodReference(Lcom/android/tools/r8/references/MethodReference;)Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleBuilder;

    move-result-object p0

    new-instance p2, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda4;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda4;-><init>(Lcom/android/tools/r8/internal/p4;)V

    .line 98
    invoke-interface {p0, p2}, Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleBuilder;->setMethodRuleInfo(Ljava/util/function/Consumer;)Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleBuilder;

    return-void
.end method


# virtual methods
.method public a(Lcom/android/tools/r8/TextInputStream;Lcom/android/tools/r8/origin/Origin;)V
    .locals 8

    .line 2
    :try_start_0
    new-instance v0, Ljava/io/InputStreamReader;

    .line 4
    invoke-interface {p1}, Lcom/android/tools/r8/TextInputStream;->getInputStream()Ljava/io/InputStream;

    move-result-object v1

    invoke-interface {p1}, Lcom/android/tools/r8/TextInputStream;->getCharset()Ljava/nio/charset/Charset;

    move-result-object p1

    invoke-direct {v0, v1, p1}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;Ljava/nio/charset/Charset;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 5
    :try_start_1
    new-instance p1, Ljava/io/BufferedReader;

    invoke-direct {p1, v0}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    const/4 v1, 0x1

    move v2, v1

    .line 7
    :goto_0
    :try_start_2
    invoke-virtual {p1}, Ljava/io/BufferedReader;->ready()Z

    move-result v3

    if-eqz v3, :cond_5

    .line 8
    invoke-virtual {p1}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v3

    const/16 v4, 0x23

    .line 9
    invoke-virtual {v3, v4}, Ljava/lang/String;->indexOf(I)I

    move-result v4

    const/4 v5, 0x0

    if-ltz v4, :cond_0

    .line 11
    invoke-virtual {v3, v5, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticBackport0;->m(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    goto :goto_1

    :cond_0
    move-object v4, v3

    :goto_1
    move v6, v5

    .line 12
    :goto_2
    invoke-virtual {v4}, Ljava/lang/String;->length()I

    move-result v7

    if-ge v6, v7, :cond_2

    .line 13
    invoke-virtual {v4, v6}, Ljava/lang/String;->charAt(I)C

    move-result v7

    invoke-static {v7}, Ljava/lang/Character;->isWhitespace(C)Z

    move-result v7

    if-nez v7, :cond_1

    goto :goto_3

    :cond_1
    add-int/lit8 v6, v6, 0x1

    goto :goto_2

    :cond_2
    move v5, v1

    :goto_3
    if-eqz v5, :cond_3

    goto :goto_4

    .line 14
    :cond_3
    invoke-virtual {p0, v4}, Lcom/android/tools/r8/internal/Cz;->b(Ljava/lang/String;)Z

    move-result v4

    if-nez v4, :cond_4

    .line 15
    iget-object v4, p0, Lcom/android/tools/r8/internal/Cz;->a:Ljava/util/function/Consumer;

    if-eqz v4, :cond_4

    .line 16
    new-instance v5, Lcom/android/tools/r8/profile/art/diagnostic/HumanReadableArtProfileParserErrorDiagnostic;

    invoke-direct {v5, v3, v2, p2}, Lcom/android/tools/r8/profile/art/diagnostic/HumanReadableArtProfileParserErrorDiagnostic;-><init>(Ljava/lang/String;ILcom/android/tools/r8/origin/Origin;)V

    invoke-interface {v4, v5}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :cond_4
    :goto_4
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 17
    :cond_5
    :try_start_3
    invoke-virtual {p1}, Ljava/io/BufferedReader;->close()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    :try_start_4
    invoke-virtual {v0}, Ljava/io/InputStreamReader;->close()V

    .line 18
    iget-object p1, p0, Lcom/android/tools/r8/internal/Cz;->d:Lcom/android/tools/r8/internal/bd0;

    if-eqz p1, :cond_6

    .line 19
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/bd0;->a()V
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_0

    :cond_6
    return-void

    :catchall_0
    move-exception p2

    .line 20
    :try_start_5
    invoke-virtual {p1}, Ljava/io/BufferedReader;->close()V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    goto :goto_5

    :catchall_1
    move-exception p1

    :try_start_6
    invoke-virtual {p2, p1}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_5
    throw p2
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_2

    :catchall_2
    move-exception p1

    :try_start_7
    invoke-virtual {v0}, Ljava/io/InputStreamReader;->close()V
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_3

    goto :goto_6

    :catchall_3
    move-exception p2

    :try_start_8
    invoke-virtual {p1, p2}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_6
    throw p1
    :try_end_8
    .catch Ljava/io/IOException; {:try_start_8 .. :try_end_8} :catch_0

    :catch_0
    move-exception p1

    .line 40
    new-instance p2, Ljava/io/UncheckedIOException;

    invoke-direct {p2, p1}, Ljava/io/UncheckedIOException;-><init>(Ljava/io/IOException;)V

    throw p2
.end method

.method public final a(Ljava/lang/String;)Z
    .locals 4

    .line 58
    sget-object v0, Lcom/android/tools/r8/internal/Nk;->a:Lcom/android/tools/r8/internal/iB;

    const/4 v0, -0x1

    :goto_0
    add-int/lit8 v1, v0, 0x1

    .line 59
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v2

    if-ge v1, v2, :cond_1

    invoke-virtual {p1, v1}, Ljava/lang/String;->charAt(I)C

    move-result v2

    const/16 v3, 0x5b

    if-eq v2, v3, :cond_0

    goto :goto_1

    :cond_0
    move v0, v1

    goto :goto_0

    :cond_1
    :goto_1
    if-ltz v0, :cond_2

    .line 65
    invoke-virtual {p1, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 66
    :cond_2
    invoke-static {p1}, Lcom/android/tools/r8/internal/Nk;->C(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_3

    const/4 p1, 0x0

    return p1

    .line 69
    :cond_3
    invoke-static {p1}, Lcom/android/tools/r8/references/Reference;->typeFromDescriptor(Ljava/lang/String;)Lcom/android/tools/r8/references/TypeReference;

    move-result-object p1

    .line 70
    sget-boolean v0, Lcom/android/tools/r8/internal/Cz;->e:Z

    if-nez v0, :cond_5

    if-eqz p1, :cond_4

    goto :goto_2

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_5
    :goto_2
    if-nez v0, :cond_7

    .line 71
    invoke-interface {p1}, Lcom/android/tools/r8/references/TypeReference;->isClass()Z

    move-result v0

    if-eqz v0, :cond_6

    goto :goto_3

    :cond_6
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 72
    :cond_7
    :goto_3
    invoke-interface {p1}, Lcom/android/tools/r8/references/TypeReference;->asClass()Lcom/android/tools/r8/references/ClassReference;

    move-result-object p1

    .line 73
    iget-object v0, p0, Lcom/android/tools/r8/internal/Cz;->c:Lcom/android/tools/r8/profile/art/ArtProfileRulePredicate;

    sget-object v1, Lcom/android/tools/r8/internal/h4;->a:Lcom/android/tools/r8/internal/h4;

    invoke-interface {v0, p1, v1}, Lcom/android/tools/r8/profile/art/ArtProfileRulePredicate;->testClassRule(Lcom/android/tools/r8/references/ClassReference;Lcom/android/tools/r8/profile/art/ArtProfileClassRuleInfo;)Z

    move-result v0

    if-eqz v0, :cond_8

    .line 74
    iget-object v0, p0, Lcom/android/tools/r8/internal/Cz;->b:Lcom/android/tools/r8/profile/art/ArtProfileBuilder;

    new-instance v1, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda5;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda5;-><init>(Lcom/android/tools/r8/references/ClassReference;)V

    invoke-interface {v0, v1}, Lcom/android/tools/r8/profile/art/ArtProfileBuilder;->addClassRule(Ljava/util/function/Consumer;)Lcom/android/tools/r8/profile/art/ArtProfileBuilder;

    :cond_8
    const/4 p1, 0x1

    return p1
.end method

.method public final a(Ljava/lang/String;Lcom/android/tools/r8/internal/p4;I)Z
    .locals 2

    add-int/lit8 v0, p3, 0x2

    const/16 v1, 0x2b

    .line 76
    invoke-virtual {p1, v1, v0}, Ljava/lang/String;->indexOf(II)I

    move-result v0

    const/4 v1, 0x0

    if-lez v0, :cond_0

    .line 77
    invoke-virtual {p1, v1, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p1

    .line 79
    :cond_0
    invoke-static {p3, p1}, Lcom/android/tools/r8/internal/wV;->a(ILjava/lang/String;)Lcom/android/tools/r8/references/MethodReference;

    move-result-object p1

    if-nez p1, :cond_1

    return v1

    .line 83
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/references/MethodReference;->getHolderClass()Lcom/android/tools/r8/references/ClassReference;

    move-result-object p3

    invoke-virtual {p3}, Lcom/android/tools/r8/references/ClassReference;->getDescriptor()Ljava/lang/String;

    move-result-object p3

    invoke-static {p3}, Lcom/android/tools/r8/internal/Nk;->C(Ljava/lang/String;)Z

    move-result p3

    if-nez p3, :cond_2

    return v1

    .line 86
    :cond_2
    invoke-virtual {p1}, Lcom/android/tools/r8/references/MethodReference;->getFormalTypes()Ljava/util/List;

    move-result-object p3

    invoke-interface {p3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :cond_3
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/references/TypeReference;

    .line 87
    invoke-interface {v0}, Lcom/android/tools/r8/references/TypeReference;->getDescriptor()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/android/tools/r8/internal/Nk;->D(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_3

    return v1

    .line 91
    :cond_4
    invoke-virtual {p1}, Lcom/android/tools/r8/references/MethodReference;->getReturnType()Lcom/android/tools/r8/references/TypeReference;

    move-result-object p3

    if-eqz p3, :cond_5

    .line 92
    invoke-virtual {p1}, Lcom/android/tools/r8/references/MethodReference;->getReturnType()Lcom/android/tools/r8/references/TypeReference;

    move-result-object p3

    invoke-interface {p3}, Lcom/android/tools/r8/references/TypeReference;->getDescriptor()Ljava/lang/String;

    move-result-object p3

    invoke-static {p3}, Lcom/android/tools/r8/internal/Nk;->D(Ljava/lang/String;)Z

    move-result p3

    if-nez p3, :cond_5

    return v1

    .line 95
    :cond_5
    iget-object p3, p0, Lcom/android/tools/r8/internal/Cz;->c:Lcom/android/tools/r8/profile/art/ArtProfileRulePredicate;

    invoke-interface {p3, p1, p2}, Lcom/android/tools/r8/profile/art/ArtProfileRulePredicate;->testMethodRule(Lcom/android/tools/r8/references/MethodReference;Lcom/android/tools/r8/profile/art/ArtProfileMethodRuleInfo;)Z

    move-result p3

    if-eqz p3, :cond_6

    .line 96
    iget-object p3, p0, Lcom/android/tools/r8/internal/Cz;->b:Lcom/android/tools/r8/profile/art/ArtProfileBuilder;

    new-instance v0, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda6;

    invoke-direct {v0, p1, p2}, Lcom/android/tools/r8/internal/Cz$$ExternalSyntheticLambda6;-><init>(Lcom/android/tools/r8/references/MethodReference;Lcom/android/tools/r8/internal/p4;)V

    invoke-interface {p3, v0}, Lcom/android/tools/r8/profile/art/ArtProfileBuilder;->addMethodRule(Ljava/util/function/Consumer;)Lcom/android/tools/r8/profile/art/ArtProfileBuilder;

    :cond_6
    const/4 p1, 0x1

    return p1
.end method

.method public b(Ljava/lang/String;)Z
    .locals 3

    const/4 v0, 0x0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/android/tools/r8/internal/p4;->a()Lcom/android/tools/r8/internal/p4$a;

    move-result-object v1

    .line 2
    invoke-static {p1, v1}, Lcom/android/tools/r8/internal/Cz;->a(Ljava/lang/String;Lcom/android/tools/r8/internal/p4$a;)Ljava/lang/String;

    move-result-object p1

    .line 3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/p4$a;->a()Lcom/android/tools/r8/internal/p4;

    move-result-object v1

    const-string v2, "->"

    .line 4
    invoke-virtual {p1, v2}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v2

    if-ltz v2, :cond_0

    .line 6
    invoke-virtual {p0, p1, v1, v2}, Lcom/android/tools/r8/internal/Cz;->a(Ljava/lang/String;Lcom/android/tools/r8/internal/p4;I)Z

    move-result p1

    :goto_0
    move v0, p1

    goto :goto_1

    .line 7
    :cond_0
    iget v1, v1, Lcom/android/tools/r8/internal/p4;->a:I

    if-nez v1, :cond_1

    .line 8
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Cz;->a(Ljava/lang/String;)Z

    move-result p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    :cond_1
    :goto_1
    return v0
.end method
