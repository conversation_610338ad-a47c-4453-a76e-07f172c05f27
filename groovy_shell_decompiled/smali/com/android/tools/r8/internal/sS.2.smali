.class public final Lcom/android/tools/r8/internal/sS;
.super Lcom/android/tools/r8/internal/i0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/lang/Cloneable;
.implements Ljava/util/Set;


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/xS;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/xS;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/sS;->b:Lcom/android/tools/r8/internal/xS;

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/internal/i0;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/ES;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/rS;

    iget-object v1, p0, Lcom/android/tools/r8/internal/sS;->b:Lcom/android/tools/r8/internal/xS;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/rS;-><init>(Lcom/android/tools/r8/internal/xS;)V

    return-object v0
.end method

.method public final b(J)Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sS;->b:Lcom/android/tools/r8/internal/xS;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/xS;->a(J)Z

    move-result p1

    return p1
.end method

.method public final c(J)Z
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sS;->b:Lcom/android/tools/r8/internal/xS;

    iget v1, v0, Lcom/android/tools/r8/internal/xS;->h:I

    .line 2
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/xS;->c(J)Ljava/lang/Object;

    .line 3
    iget-object p1, p0, Lcom/android/tools/r8/internal/sS;->b:Lcom/android/tools/r8/internal/xS;

    iget p1, p1, Lcom/android/tools/r8/internal/xS;->h:I

    if-eq p1, v1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final clear()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sS;->b:Lcom/android/tools/r8/internal/xS;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/xS;->clear()V

    return-void
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 3

    if-ne p1, p0, :cond_0

    const/4 p1, 0x1

    return p1

    .line 1
    :cond_0
    instance-of v0, p1, Ljava/util/Set;

    const/4 v1, 0x0

    if-nez v0, :cond_1

    return v1

    .line 2
    :cond_1
    check-cast p1, Ljava/util/Set;

    .line 3
    invoke-interface {p1}, Ljava/util/Set;->size()I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sS;->size()I

    move-result v2

    if-eq v0, v2, :cond_2

    return v1

    .line 4
    :cond_2
    invoke-virtual {p0, p1}, Ljava/util/AbstractCollection;->containsAll(Ljava/util/Collection;)Z

    move-result p1

    return p1
.end method

.method public final hashCode()I
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sS;->b:Lcom/android/tools/r8/internal/xS;

    iget v1, v0, Lcom/android/tools/r8/internal/xS;->h:I

    .line 2
    new-instance v2, Lcom/android/tools/r8/internal/rS;

    invoke-direct {v2, v0}, Lcom/android/tools/r8/internal/rS;-><init>(Lcom/android/tools/r8/internal/xS;)V

    const/4 v0, 0x0

    :goto_0
    add-int/lit8 v3, v1, -0x1

    if-eqz v1, :cond_0

    .line 3
    iget-object v1, v2, Lcom/android/tools/r8/internal/rS;->h:Lcom/android/tools/r8/internal/xS;

    iget-object v1, v1, Lcom/android/tools/r8/internal/xS;->b:[J

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/vS;->a()I

    move-result v4

    aget-wide v4, v1, v4

    const/16 v1, 0x20

    ushr-long v6, v4, v1

    xor-long/2addr v4, v6

    long-to-int v1, v4

    add-int/2addr v0, v1

    move v1, v3

    goto :goto_0

    :cond_0
    return v0
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/rS;

    iget-object v1, p0, Lcom/android/tools/r8/internal/sS;->b:Lcom/android/tools/r8/internal/xS;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/rS;-><init>(Lcom/android/tools/r8/internal/xS;)V

    return-object v0
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sS;->b:Lcom/android/tools/r8/internal/xS;

    iget v0, v0, Lcom/android/tools/r8/internal/xS;->h:I

    return v0
.end method
