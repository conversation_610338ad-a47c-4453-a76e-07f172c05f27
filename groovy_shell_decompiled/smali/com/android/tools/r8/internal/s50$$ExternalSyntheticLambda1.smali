.class public final synthetic Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/op0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/ei;

.field public final synthetic f$1:Ljava/util/concurrent/ExecutorService;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/ei;Ljava/util/concurrent/ExecutorService;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/ei;

    iput-object p2, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda1;->f$1:Ljava/util/concurrent/ExecutorService;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/ei;

    iget-object v1, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda1;->f$1:Ljava/util/concurrent/ExecutorService;

    check-cast p1, Lcom/android/tools/r8/internal/hi;

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/s50;->a(Lcom/android/tools/r8/internal/ei;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/hi;)V

    return-void
.end method
