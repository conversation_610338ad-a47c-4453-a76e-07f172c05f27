.class public final Lcom/android/tools/r8/internal/v8;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/internal/B40;

.field public final b:Ljava/util/HashMap;

.field public final c:Lcom/android/tools/r8/internal/B40;

.field public final d:Z

.field public e:Lcom/android/tools/r8/internal/B40;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/B40;ILcom/android/tools/r8/graph/x2;ZLcom/android/tools/r8/internal/B40;)V
    .locals 4

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/HashMap;

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-nez p1, :cond_0

    move v3, v2

    goto :goto_0

    :cond_0
    move v3, v1

    :goto_0
    add-int/2addr v3, v1

    add-int/2addr v3, p2

    .line 3
    invoke-direct {v0, v3}, Ljava/util/HashMap;-><init>(I)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/v8;->b:Ljava/util/HashMap;

    if-nez p5, :cond_1

    .line 6
    invoke-static {}, Lcom/android/tools/r8/internal/B40$c;->t()Lcom/android/tools/r8/internal/B40$c$a;

    move-result-object p2

    .line 7
    invoke-virtual {p2, v2}, Lcom/android/tools/r8/internal/B40$a;->a(I)Lcom/android/tools/r8/internal/B40$a;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/internal/B40$c$a;

    .line 8
    invoke-virtual {p2, p3}, Lcom/android/tools/r8/internal/B40$a;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/internal/B40$a;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/internal/B40$c$a;

    .line 9
    iput-boolean p4, p2, Lcom/android/tools/r8/internal/B40$a;->e:Z

    .line 10
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/B40$c$a;->d()Lcom/android/tools/r8/internal/B40$c;

    move-result-object p5

    :cond_1
    if-eqz p1, :cond_2

    .line 13
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/v8;->c(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/v8;->a:Lcom/android/tools/r8/internal/B40;

    .line 14
    iput-boolean p4, p0, Lcom/android/tools/r8/internal/v8;->d:Z

    .line 17
    invoke-static {p1, p5, p4}, Lcom/android/tools/r8/graph/i0;->a(Lcom/android/tools/r8/internal/B40;Lcom/android/tools/r8/internal/B40;Z)Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    .line 18
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/v8;->c(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/v8;->c:Lcom/android/tools/r8/internal/B40;

    goto :goto_1

    :cond_2
    const/4 p1, 0x0

    .line 21
    iput-object p1, p0, Lcom/android/tools/r8/internal/v8;->a:Lcom/android/tools/r8/internal/B40;

    .line 22
    iput-boolean v2, p0, Lcom/android/tools/r8/internal/v8;->d:Z

    .line 23
    invoke-virtual {p0, p5}, Lcom/android/tools/r8/internal/v8;->c(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/v8;->c:Lcom/android/tools/r8/internal/B40;

    :goto_1
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;
    .locals 2

    if-nez p1, :cond_0

    .line 1
    iget-object p1, p0, Lcom/android/tools/r8/internal/v8;->a:Lcom/android/tools/r8/internal/B40;

    return-object p1

    .line 3
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/internal/B40;->d:Lcom/android/tools/r8/internal/B40;

    if-nez v0, :cond_1

    iget-object v1, p0, Lcom/android/tools/r8/internal/v8;->a:Lcom/android/tools/r8/internal/B40;

    if-nez v1, :cond_1

    .line 5
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/v8;->c(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    return-object p1

    :cond_1
    if-nez v0, :cond_2

    .line 7
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/v8;->d:Z

    if-eqz v1, :cond_2

    .line 10
    iget-object p1, p0, Lcom/android/tools/r8/internal/v8;->a:Lcom/android/tools/r8/internal/B40;

    return-object p1

    .line 12
    :cond_2
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/v8;->a(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;

    move-result-object v0

    .line 14
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40;->o()Z

    move-result v1

    if-eqz v1, :cond_3

    .line 15
    invoke-static {}, Lcom/android/tools/r8/internal/B40$b;->t()Lcom/android/tools/r8/internal/B40$b$a;

    move-result-object v1

    iget-object p1, p1, Lcom/android/tools/r8/internal/B40;->c:Lcom/android/tools/r8/graph/x2;

    .line 16
    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/B40$a;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/internal/B40$a;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/B40$b$a;

    .line 17
    iput-object v0, p1, Lcom/android/tools/r8/internal/B40$a;->c:Lcom/android/tools/r8/internal/B40;

    const/4 v0, 0x1

    .line 18
    iput-boolean v0, p1, Lcom/android/tools/r8/internal/B40$a;->f:Z

    .line 19
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40$b$a;->d()Lcom/android/tools/r8/internal/B40$b;

    move-result-object p1

    goto :goto_0

    .line 20
    :cond_3
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40;->b()Lcom/android/tools/r8/internal/B40$a;

    move-result-object p1

    .line 21
    iput-object v0, p1, Lcom/android/tools/r8/internal/B40$a;->c:Lcom/android/tools/r8/internal/B40;

    .line 22
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40$a;->c()Lcom/android/tools/r8/internal/B40$a;

    move-result-object p1

    .line 23
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40$a;->a()Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    .line 24
    :goto_0
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/v8;->c(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    return-object p1
.end method

.method public final a(ZLjava/util/function/Supplier;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/internal/B40;
    .locals 4

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/internal/v8;->e:Lcom/android/tools/r8/internal/B40;

    if-nez v0, :cond_6

    if-eqz p1, :cond_5

    const/high16 p1, -0x80000000

    .line 29
    invoke-interface {p2}, Ljava/util/function/Supplier;->get()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/Iterable;

    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p2

    const v0, 0x7fffffff

    move v1, v0

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/B40;

    .line 31
    sget-boolean v3, Lcom/android/tools/r8/internal/v8;->f:Z

    if-nez v3, :cond_1

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/B40;->h()Lcom/android/tools/r8/internal/B40;

    move-result-object v3

    if-ne v2, v3, :cond_0

    goto :goto_1

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 32
    :cond_1
    :goto_1
    iget v2, v2, Lcom/android/tools/r8/internal/B40;->b:I

    .line 33
    invoke-static {v1, v2}, Ljava/lang/Math;->min(II)I

    move-result v1

    .line 34
    invoke-static {p1, v2}, Ljava/lang/Math;->max(II)I

    move-result p1

    goto :goto_0

    :cond_2
    if-ne v1, v0, :cond_3

    .line 35
    iget-object p1, p0, Lcom/android/tools/r8/internal/v8;->c:Lcom/android/tools/r8/internal/B40;

    goto :goto_2

    .line 36
    :cond_3
    invoke-static {}, Lcom/android/tools/r8/internal/B40$c;->t()Lcom/android/tools/r8/internal/B40$c$a;

    move-result-object p2

    if-ge v1, p1, :cond_4

    add-int/lit8 v1, v1, -0x1

    .line 37
    :cond_4
    invoke-virtual {p2, v1}, Lcom/android/tools/r8/internal/B40$a;->a(I)Lcom/android/tools/r8/internal/B40$a;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/B40$c$a;

    .line 38
    invoke-virtual {p1, p3}, Lcom/android/tools/r8/internal/B40$a;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/internal/B40$a;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/B40$c$a;

    iget-object p2, p0, Lcom/android/tools/r8/internal/v8;->a:Lcom/android/tools/r8/internal/B40;

    .line 39
    iput-object p2, p1, Lcom/android/tools/r8/internal/B40$a;->c:Lcom/android/tools/r8/internal/B40;

    .line 40
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40$c$a;->d()Lcom/android/tools/r8/internal/B40$c;

    move-result-object p1

    :goto_2
    iput-object p1, p0, Lcom/android/tools/r8/internal/v8;->e:Lcom/android/tools/r8/internal/B40;

    goto :goto_3

    .line 46
    :cond_5
    sget-boolean p1, Lcom/android/tools/r8/internal/B40;->g:Z

    .line 47
    sget-object p1, Lcom/android/tools/r8/internal/B40$c;->h:Lcom/android/tools/r8/internal/B40$c;

    .line 48
    iput-object p1, p0, Lcom/android/tools/r8/internal/v8;->e:Lcom/android/tools/r8/internal/B40;

    .line 51
    :cond_6
    :goto_3
    iget-object p1, p0, Lcom/android/tools/r8/internal/v8;->e:Lcom/android/tools/r8/internal/B40;

    return-object p1
.end method

.method public final b(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;
    .locals 2

    .line 1
    iget-boolean v0, p1, Lcom/android/tools/r8/internal/B40;->f:Z

    if-eqz v0, :cond_2

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/v8;->a:Lcom/android/tools/r8/internal/B40;

    if-eqz v0, :cond_2

    .line 3
    sget-boolean v0, Lcom/android/tools/r8/internal/v8;->f:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40;->k()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 4
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/v8;->a:Lcom/android/tools/r8/internal/B40;

    const/4 v1, 0x1

    invoke-static {v0, p1, v1}, Lcom/android/tools/r8/graph/i0;->a(Lcom/android/tools/r8/internal/B40;Lcom/android/tools/r8/internal/B40;Z)Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/v8;->c(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    return-object p1

    .line 8
    :cond_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40;->b()Lcom/android/tools/r8/internal/B40$a;

    move-result-object v0

    .line 9
    iget-object p1, p1, Lcom/android/tools/r8/internal/B40;->d:Lcom/android/tools/r8/internal/B40;

    .line 10
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/v8;->a(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    .line 11
    iput-object p1, v0, Lcom/android/tools/r8/internal/B40$a;->c:Lcom/android/tools/r8/internal/B40;

    .line 12
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/B40$a;->c()Lcom/android/tools/r8/internal/B40$a;

    move-result-object p1

    .line 13
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/B40$a;->a()Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    .line 14
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/v8;->c(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;

    move-result-object p1

    return-object p1
.end method

.method public final c(Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/B40;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/v8;->b:Ljava/util/HashMap;

    invoke-virtual {v0, p1, p1}, Ljava/util/HashMap;->putIfAbsent(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/B40;

    if-eqz v0, :cond_0

    move-object p1, v0

    :cond_0
    return-object p1
.end method
