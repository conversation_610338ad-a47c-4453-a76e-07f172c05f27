.class public final Lcom/android/tools/r8/internal/Ss;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/io/Serializable;


# static fields
.field public static final b:Lcom/android/tools/r8/internal/Ss;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Ss;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Ss;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Ss;->b:Lcom/android/tools/r8/internal/Ss;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final hashCode()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 1

    const-string v0, "EmptyCoroutineContext"

    return-object v0
.end method
