.class public final Lcom/android/tools/r8/internal/v60;
.super Lcom/android/tools/r8/retrace/ProguardMappingSupplier$Builder;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public a:Lcom/android/tools/r8/retrace/ProguardMapProducer;

.field public b:Z

.field public c:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/retrace/ProguardMappingSupplier$Builder;-><init>()V

    const/4 v0, 0x0

    .line 4
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/v60;->b:Z

    const/4 v0, 0x1

    .line 5
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/v60;->c:Z

    return-void
.end method


# virtual methods
.method public final build()Lcom/android/tools/r8/retrace/MappingSupplier;
    .locals 4

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/w60;

    iget-object v1, p0, Lcom/android/tools/r8/internal/v60;->a:Lcom/android/tools/r8/retrace/ProguardMapProducer;

    iget-boolean v2, p0, Lcom/android/tools/r8/internal/v60;->b:Z

    iget-boolean v3, p0, Lcom/android/tools/r8/internal/v60;->c:Z

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/w60;-><init>(Lcom/android/tools/r8/retrace/ProguardMapProducer;ZZ)V

    return-object v0
.end method

.method public final self()Lcom/android/tools/r8/retrace/MappingSupplierBuilder;
    .locals 0

    return-object p0
.end method

.method public final setAllowExperimental(Z)Lcom/android/tools/r8/retrace/MappingSupplierBuilder;
    .locals 0

    .line 1
    iput-boolean p1, p0, Lcom/android/tools/r8/internal/v60;->b:Z

    return-object p0
.end method

.method public final setLoadAllDefinitions(Z)Lcom/android/tools/r8/retrace/ProguardMappingSupplier$Builder;
    .locals 0

    .line 1
    iput-boolean p1, p0, Lcom/android/tools/r8/internal/v60;->c:Z

    return-object p0
.end method

.method public final setProguardMapProducer(Lcom/android/tools/r8/retrace/ProguardMapProducer;)Lcom/android/tools/r8/retrace/ProguardMappingSupplier$Builder;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/v60;->a:Lcom/android/tools/r8/retrace/ProguardMapProducer;

    return-object p0
.end method
