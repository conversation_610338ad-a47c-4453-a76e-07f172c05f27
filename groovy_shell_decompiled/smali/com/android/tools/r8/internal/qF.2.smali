.class public final Lcom/android/tools/r8/internal/qF;
.super Lcom/android/tools/r8/internal/U;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final b:I

.field public final c:I

.field public final d:Z

.field public final e:Z

.field public transient f:Lcom/android/tools/r8/internal/jF;

.field public transient g:Lcom/android/tools/r8/internal/lF;

.field public transient h:Lcom/android/tools/r8/internal/kF;

.field public final synthetic i:Lcom/android/tools/r8/internal/tF;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/tF;IZIZ)V
    .locals 1

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/U;-><init>()V

    if-nez p3, :cond_1

    if-nez p5, :cond_1

    .line 2
    invoke-virtual {p1, p2, p4}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result v0

    if-gtz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    new-instance p3, Ljava/lang/StringBuilder;

    const-string p5, "Start key ("

    invoke-direct {p3, p5}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, ") is larger than end key ("

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, ")"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 3
    :cond_1
    :goto_0
    iput p2, p0, Lcom/android/tools/r8/internal/qF;->b:I

    .line 4
    iput-boolean p3, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    .line 5
    iput p4, p0, Lcom/android/tools/r8/internal/qF;->c:I

    .line 6
    iput-boolean p5, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    .line 7
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    .line 1334
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/qF;->i()Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 1336
    iget v0, v0, Lcom/android/tools/r8/internal/Q;->b:I

    return v0

    .line 1337
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public final a(II)Lcom/android/tools/r8/internal/mG;
    .locals 8

    .line 1329
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    if-eqz v0, :cond_0

    iget-boolean v1, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    if-eqz v1, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/qF;

    iget-object v3, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    const/4 v5, 0x0

    const/4 v7, 0x0

    move-object v2, v0

    move v4, p1

    move v6, p2

    invoke-direct/range {v2 .. v7}, Lcom/android/tools/r8/internal/qF;-><init>(Lcom/android/tools/r8/internal/tF;IZIZ)V

    return-object v0

    :cond_0
    if-nez v0, :cond_2

    .line 1330
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v1, p0, Lcom/android/tools/r8/internal/qF;->c:I

    invoke-virtual {v0, p2, v1}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result v0

    if-gez v0, :cond_1

    goto :goto_0

    :cond_1
    iget p2, p0, Lcom/android/tools/r8/internal/qF;->c:I

    :cond_2
    :goto_0
    move v4, p2

    .line 1331
    iget-boolean p2, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    if-nez p2, :cond_4

    iget-object p2, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v0, p0, Lcom/android/tools/r8/internal/qF;->b:I

    invoke-virtual {p2, p1, v0}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result p2

    if-lez p2, :cond_3

    goto :goto_1

    :cond_3
    iget p1, p0, Lcom/android/tools/r8/internal/qF;->b:I

    :cond_4
    :goto_1
    move v2, p1

    .line 1332
    iget-boolean p1, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    if-nez p1, :cond_5

    iget-boolean p1, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    if-nez p1, :cond_5

    iget p1, p0, Lcom/android/tools/r8/internal/qF;->b:I

    if-ne v2, p1, :cond_5

    iget p1, p0, Lcom/android/tools/r8/internal/qF;->c:I

    if-ne v4, p1, :cond_5

    return-object p0

    .line 1333
    :cond_5
    new-instance p1, Lcom/android/tools/r8/internal/qF;

    iget-object v1, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    const/4 v3, 0x0

    const/4 v5, 0x0

    move-object v0, p1

    invoke-direct/range {v0 .. v5}, Lcom/android/tools/r8/internal/qF;-><init>(Lcom/android/tools/r8/internal/tF;IZIZ)V

    return-object p1
.end method

.method public final a(ILjava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    const/4 v1, 0x0

    iput-boolean v1, v0, Lcom/android/tools/r8/internal/tF;->i:Z

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/qF;->d(I)Z

    move-result v0

    if-nez v0, :cond_2

    new-instance p2, Ljava/lang/IllegalArgumentException;

    const-string v0, "Key ("

    const-string v1, ") out of range ["

    .line 4
    invoke-static {p1, v0, v1}, Lcom/android/tools/r8/internal/Nq0;->a(ILjava/lang/String;Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1322
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    const-string v1, "-"

    if-eqz v0, :cond_0

    move-object v0, v1

    goto :goto_0

    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/qF;->b:I

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    :goto_0
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-boolean v0, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    if-eqz v0, :cond_1

    goto :goto_1

    :cond_1
    iget v0, p0, Lcom/android/tools/r8/internal/qF;->c:I

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    :goto_1
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ")"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p2

    .line 1323
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/tF;->a(ILjava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    .line 1324
    iget-object p2, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget-boolean p2, p2, Lcom/android/tools/r8/internal/tF;->i:Z

    if-eqz p2, :cond_3

    const/4 p1, 0x0

    :cond_3
    return-object p1
.end method

.method public final a(I)Z
    .locals 1

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/qF;->d(I)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/tF;->a(I)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final b(I)Lcom/android/tools/r8/internal/mG;
    .locals 7

    .line 1
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    if-eqz v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/qF;

    iget-object v2, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v5, p0, Lcom/android/tools/r8/internal/qF;->c:I

    iget-boolean v6, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    const/4 v4, 0x0

    move-object v1, v0

    move v3, p1

    invoke-direct/range {v1 .. v6}, Lcom/android/tools/r8/internal/qF;-><init>(Lcom/android/tools/r8/internal/tF;IZIZ)V

    return-object v0

    .line 2
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v1, p0, Lcom/android/tools/r8/internal/qF;->b:I

    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result v0

    if-lez v0, :cond_1

    new-instance v0, Lcom/android/tools/r8/internal/qF;

    iget-object v2, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v5, p0, Lcom/android/tools/r8/internal/qF;->c:I

    iget-boolean v6, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    const/4 v4, 0x0

    move-object v1, v0

    move v3, p1

    invoke-direct/range {v1 .. v6}, Lcom/android/tools/r8/internal/qF;-><init>(Lcom/android/tools/r8/internal/tF;IZIZ)V

    goto :goto_0

    :cond_1
    move-object v0, p0

    :goto_0
    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/N10;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->f:Lcom/android/tools/r8/internal/jF;

    if-nez v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/jF;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/jF;-><init>(Lcom/android/tools/r8/internal/qF;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/qF;->f:Lcom/android/tools/r8/internal/jF;

    .line 56
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->f:Lcom/android/tools/r8/internal/jF;

    return-object v0
.end method

.method public final c(I)Lcom/android/tools/r8/internal/mG;
    .locals 7

    .line 57
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    if-eqz v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/qF;

    iget-object v2, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v3, p0, Lcom/android/tools/r8/internal/qF;->b:I

    iget-boolean v4, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    const/4 v6, 0x0

    move-object v1, v0

    move v5, p1

    invoke-direct/range {v1 .. v6}, Lcom/android/tools/r8/internal/qF;-><init>(Lcom/android/tools/r8/internal/tF;IZIZ)V

    return-object v0

    .line 58
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v1, p0, Lcom/android/tools/r8/internal/qF;->c:I

    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result v0

    if-gez v0, :cond_1

    new-instance v0, Lcom/android/tools/r8/internal/qF;

    iget-object v2, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v3, p0, Lcom/android/tools/r8/internal/qF;->b:I

    iget-boolean v4, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    const/4 v6, 0x0

    move-object v1, v0

    move v5, p1

    invoke-direct/range {v1 .. v6}, Lcom/android/tools/r8/internal/qF;-><init>(Lcom/android/tools/r8/internal/tF;IZIZ)V

    goto :goto_0

    :cond_1
    move-object v0, p0

    :goto_0
    return-object v0
.end method

.method public final clear()V
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/nF;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/nF;-><init>(Lcom/android/tools/r8/internal/qF;)V

    .line 2
    :goto_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rF;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rF;->a()Lcom/android/tools/r8/internal/fF;

    .line 4
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rF;->remove()V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public final comparator()Lcom/android/tools/r8/internal/XD;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 v0, 0x0

    return-object v0
.end method

.method public final comparator()Ljava/util/Comparator;
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 v0, 0x0

    return-object v0
.end method

.method public final containsValue(Ljava/lang/Object;)Z
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/nF;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/nF;-><init>(Lcom/android/tools/r8/internal/qF;)V

    .line 3
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rF;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 4
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rF;->a()Lcom/android/tools/r8/internal/fF;

    move-result-object v1

    iget-object v1, v1, Lcom/android/tools/r8/internal/Q;->c:Ljava/lang/Object;

    if-ne v1, p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public final d()I
    .locals 1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/qF;->j()Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 5
    iget v0, v0, Lcom/android/tools/r8/internal/Q;->b:I

    return v0

    .line 6
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public final d(I)Z
    .locals 2

    .line 1
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v1, p0, Lcom/android/tools/r8/internal/qF;->b:I

    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result v0

    if-ltz v0, :cond_1

    :cond_0
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v1, p0, Lcom/android/tools/r8/internal/qF;->c:I

    .line 2
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result p1

    if-gez p1, :cond_1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    goto :goto_1

    :cond_2
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public final get(I)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/qF;->d(I)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/tF;->d(I)Lcom/android/tools/r8/internal/fF;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object p1, p1, Lcom/android/tools/r8/internal/Q;->c:Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method public final i()Lcom/android/tools/r8/internal/fF;
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget-object v1, v0, Lcom/android/tools/r8/internal/tF;->b:Lcom/android/tools/r8/internal/fF;

    const/4 v2, 0x0

    if-nez v1, :cond_0

    return-object v2

    .line 4
    :cond_0
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    if-eqz v1, :cond_1

    iget-object v0, v0, Lcom/android/tools/r8/internal/tF;->d:Lcom/android/tools/r8/internal/fF;

    goto :goto_0

    .line 6
    :cond_1
    iget v1, p0, Lcom/android/tools/r8/internal/qF;->b:I

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/tF;->e(I)Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    .line 8
    iget-object v1, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v3, v0, Lcom/android/tools/r8/internal/Q;->b:I

    iget v4, p0, Lcom/android/tools/r8/internal/qF;->b:I

    invoke-virtual {v1, v3, v4}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result v1

    if-gez v1, :cond_2

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/fF;->b()Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    :cond_2
    :goto_0
    if-eqz v0, :cond_4

    .line 11
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    if-nez v1, :cond_3

    iget-object v1, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v3, v0, Lcom/android/tools/r8/internal/Q;->b:I

    iget v4, p0, Lcom/android/tools/r8/internal/qF;->c:I

    invoke-virtual {v1, v3, v4}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result v1

    if-ltz v1, :cond_3

    goto :goto_1

    :cond_3
    return-object v0

    :cond_4
    :goto_1
    return-object v2
.end method

.method public final isEmpty()Z
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/tF;->d:Lcom/android/tools/r8/internal/fF;

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/qF;->i()Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    move v0, v1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    xor-int/2addr v0, v1

    return v0
.end method

.method public final j()Lcom/android/tools/r8/internal/fF;
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget-object v1, v0, Lcom/android/tools/r8/internal/tF;->b:Lcom/android/tools/r8/internal/fF;

    const/4 v2, 0x0

    if-nez v1, :cond_0

    return-object v2

    .line 4
    :cond_0
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/qF;->e:Z

    if-eqz v1, :cond_1

    iget-object v0, v0, Lcom/android/tools/r8/internal/tF;->e:Lcom/android/tools/r8/internal/fF;

    goto :goto_0

    .line 6
    :cond_1
    iget v1, p0, Lcom/android/tools/r8/internal/qF;->c:I

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/tF;->e(I)Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    .line 8
    iget-object v1, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v3, v0, Lcom/android/tools/r8/internal/Q;->b:I

    iget v4, p0, Lcom/android/tools/r8/internal/qF;->c:I

    invoke-virtual {v1, v3, v4}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result v1

    if-ltz v1, :cond_2

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/fF;->d()Lcom/android/tools/r8/internal/fF;

    move-result-object v0

    :cond_2
    :goto_0
    if-eqz v0, :cond_4

    .line 11
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/qF;->d:Z

    if-nez v1, :cond_3

    iget-object v1, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget v3, v0, Lcom/android/tools/r8/internal/Q;->b:I

    iget v4, p0, Lcom/android/tools/r8/internal/qF;->b:I

    invoke-virtual {v1, v3, v4}, Lcom/android/tools/r8/internal/tF;->c(II)I

    move-result v1

    if-gez v1, :cond_3

    goto :goto_1

    :cond_3
    return-object v0

    :cond_4
    :goto_1
    return-object v2
.end method

.method public final keySet()Lcom/android/tools/r8/internal/ZG;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->g:Lcom/android/tools/r8/internal/lF;

    if-nez v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/lF;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/lF;-><init>(Lcom/android/tools/r8/internal/qF;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/qF;->g:Lcom/android/tools/r8/internal/lF;

    .line 2
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->g:Lcom/android/tools/r8/internal/lF;

    return-object v0
.end method

.method public final remove(I)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    const/4 v1, 0x0

    iput-boolean v1, v0, Lcom/android/tools/r8/internal/tF;->i:Z

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/qF;->d(I)Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    .line 3
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/tF;->remove(I)Ljava/lang/Object;

    move-result-object p1

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->i:Lcom/android/tools/r8/internal/tF;

    iget-boolean v0, v0, Lcom/android/tools/r8/internal/tF;->i:Z

    if-eqz v0, :cond_1

    move-object v1, p1

    :cond_1
    return-object v1
.end method

.method public final size()I
    .locals 3

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/nF;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/nF;-><init>(Lcom/android/tools/r8/internal/qF;)V

    const/4 v1, 0x0

    .line 3
    :goto_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rF;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    add-int/lit8 v1, v1, 0x1

    .line 5
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rF;->a()Lcom/android/tools/r8/internal/fF;

    goto :goto_0

    :cond_0
    return v1
.end method

.method public final values()Lcom/android/tools/r8/internal/mb0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->h:Lcom/android/tools/r8/internal/kF;

    if-nez v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/kF;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/kF;-><init>(Lcom/android/tools/r8/internal/qF;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/qF;->h:Lcom/android/tools/r8/internal/kF;

    .line 18
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/qF;->h:Lcom/android/tools/r8/internal/kF;

    return-object v0
.end method
