.class public abstract Lcom/android/tools/r8/internal/v20;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static synthetic a(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 7
    iget-object p0, p0, Lcom/android/tools/r8/graph/B1;->b3:Lcom/android/tools/r8/graph/J2;

    return-object p0
.end method

.method public static synthetic a(Ljava/util/function/Function;Ljava/lang/String;Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/H9;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/O9;

    .line 5
    invoke-interface {p0, p3}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/O9;->U()Lcom/android/tools/r8/graph/x2;

    move-result-object p2

    iget-object p2, p2, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    .line 6
    invoke-virtual {p3, p0, p2, p1}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Ljava/lang/String;)Lcom/android/tools/r8/graph/x2;

    move-result-object p0

    const/16 p1, 0xb6

    const/4 p2, 0x0

    invoke-direct {v0, p1, p0, p2}, Lcom/android/tools/r8/internal/O9;-><init>(ILcom/android/tools/r8/graph/x2;Z)V

    return-object v0
.end method

.method public static a()Lcom/android/tools/r8/internal/o5;
    .locals 2

    .line 8
    sget-object v0, Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda1;

    const-string v1, "getAsDouble"

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/v20;->a(Ljava/util/function/Function;Ljava/lang/String;)Lcom/android/tools/r8/internal/o5;

    move-result-object v0

    return-object v0
.end method

.method public static a(Ljava/util/function/Function;Ljava/lang/String;)Lcom/android/tools/r8/internal/o5;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0, p1}, Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda0;-><init>(Ljava/util/function/Function;Ljava/lang/String;)V

    return-object v0
.end method

.method public static synthetic b(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/android/tools/r8/graph/B1;->c3:Lcom/android/tools/r8/graph/J2;

    return-object p0
.end method

.method public static b()Lcom/android/tools/r8/internal/o5;
    .locals 2

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda2;

    const-string v1, "getAsInt"

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/v20;->a(Ljava/util/function/Function;Ljava/lang/String;)Lcom/android/tools/r8/internal/o5;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic c(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/android/tools/r8/graph/B1;->d3:Lcom/android/tools/r8/graph/J2;

    return-object p0
.end method

.method public static c()Lcom/android/tools/r8/internal/o5;
    .locals 2

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda3;->INSTANCE:Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda3;

    const-string v1, "getAsLong"

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/v20;->a(Ljava/util/function/Function;Ljava/lang/String;)Lcom/android/tools/r8/internal/o5;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic d(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/android/tools/r8/graph/B1;->a3:Lcom/android/tools/r8/graph/J2;

    return-object p0
.end method

.method public static d()Lcom/android/tools/r8/internal/o5;
    .locals 2

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/internal/v20$$ExternalSyntheticLambda4;

    const-string v1, "get"

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/v20;->a(Ljava/util/function/Function;Ljava/lang/String;)Lcom/android/tools/r8/internal/o5;

    move-result-object v0

    return-object v0
.end method
