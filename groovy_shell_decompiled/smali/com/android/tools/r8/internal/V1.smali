.class public final Lcom/android/tools/r8/internal/V1;
.super Lcom/android/tools/r8/internal/W1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final a:Lcom/android/tools/r8/internal/V1;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/V1;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/V1;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/V1;->a:Lcom/android/tools/r8/internal/V1;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/W1;-><init>()V

    return-void
.end method
