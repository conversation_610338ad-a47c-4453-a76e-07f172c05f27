.class public abstract Lcom/android/tools/r8/internal/s40;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic a:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a(Ljava/util/Collection;)Ljava/util/Collection;
    .locals 1

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/s40;->a:Z

    if-nez v0, :cond_1

    instance-of v0, p0, Ljava/util/ArrayList;

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 3
    :cond_1
    :goto_0
    sget-object v0, Lcom/android/tools/r8/internal/qz$$ExternalSyntheticLambda8;->INSTANCE:Lcom/android/tools/r8/internal/qz$$ExternalSyntheticLambda8;

    invoke-interface {p0, v0}, Ljava/util/Collection;->removeIf(Ljava/util/function/Predicate;)Z

    return-object p0
.end method


# virtual methods
.method public a()Lcom/android/tools/r8/internal/JW;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public final a(ZILjava/util/Collection;)V
    .locals 2

    .line 4
    sget-boolean p1, Lcom/android/tools/r8/internal/s40;->a:Z

    if-nez p1, :cond_1

    const/4 p1, 0x2

    if-lt p2, p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    add-int/lit8 p2, p2, -0x1

    const/4 p1, 0x0

    .line 7
    invoke-interface {p3}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :cond_2
    :goto_1
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vz;

    .line 8
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vz;->k()Z

    move-result v1

    if-nez v1, :cond_2

    .line 9
    iget-object v0, v0, Lcom/android/tools/r8/internal/vz;->b:Ljava/util/LinkedList;

    invoke-virtual {v0}, Ljava/util/LinkedList;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    add-int/2addr p1, v0

    goto :goto_1

    .line 10
    :cond_3
    sget-boolean p3, Lcom/android/tools/r8/internal/s40;->a:Z

    if-nez p3, :cond_5

    if-lt p2, p1, :cond_4

    goto :goto_2

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 12
    :cond_5
    :goto_2
    monitor-enter p0

    .line 18
    :try_start_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public b()Lcom/android/tools/r8/internal/KW;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public c()Lcom/android/tools/r8/internal/Yj0;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public d()Lcom/android/tools/r8/internal/eu0;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public e()V
    .locals 0

    return-void
.end method

.method public abstract f()Ljava/lang/String;
.end method

.method public g()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/iw;

    return v0
.end method

.method public h()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public i()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public j()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public k()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public l()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method
