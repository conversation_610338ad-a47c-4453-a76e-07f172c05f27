.class public final synthetic Lcom/android/tools/r8/internal/R8$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/lp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/R8;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/R8;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/R8$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/R8;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/R8$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/R8;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/R8;->a()V

    return-void
.end method
