.class public final enum Lcom/android/tools/r8/internal/vy$a;
.super Ljava/lang/Enum;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/internal/vy;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/android/tools/r8/internal/vy$a;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum b:Lcom/android/tools/r8/internal/vy$a;

.field public static final enum c:Lcom/android/tools/r8/internal/vy$a;

.field public static final enum d:Lcom/android/tools/r8/internal/vy$a;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/vy$a;

    const/4 v1, 0x0

    const-string v2, "CLASS_SUPER_OR_INTERFACE_ANNOTATION"

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/vy$a;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/android/tools/r8/internal/vy$a;->b:Lcom/android/tools/r8/internal/vy$a;

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/vy$a;

    const/4 v1, 0x1

    const-string v2, "ENCLOSING_INNER_OR_TYPE_ANNOTATION"

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/vy$a;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/android/tools/r8/internal/vy$a;->c:Lcom/android/tools/r8/internal/vy$a;

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/vy$a;

    const/4 v1, 0x2

    const-string v2, "MEMBER_ANNOTATION"

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/vy$a;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/android/tools/r8/internal/vy$a;->d:Lcom/android/tools/r8/internal/vy$a;

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p2, p1}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method
