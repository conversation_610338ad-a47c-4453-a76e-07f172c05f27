.class public final Lcom/android/tools/r8/internal/xe0;
.super Lcom/android/tools/r8/internal/dy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# instance fields
.field public b:Ljava/lang/String;

.field public c:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/dy;-><init>()V

    const-string v0, ""

    .line 134
    iput-object v0, p0, Lcom/android/tools/r8/internal/xe0;->b:Ljava/lang/String;

    const/4 v0, 0x0

    .line 230
    iput v0, p0, Lcom/android/tools/r8/internal/xe0;->c:I

    .line 231
    sget-object v0, Lcom/android/tools/r8/internal/ze0;->e:Lcom/android/tools/r8/internal/ze0;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/ay;)V
    .locals 0

    .line 232
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/dy;-><init>(Lcom/android/tools/r8/internal/ey;)V

    const-string p1, ""

    .line 359
    iput-object p1, p0, Lcom/android/tools/r8/internal/xe0;->b:Ljava/lang/String;

    const/4 p1, 0x0

    .line 455
    iput p1, p0, Lcom/android/tools/r8/internal/xe0;->c:I

    .line 456
    sget-object p1, Lcom/android/tools/r8/internal/ze0;->e:Lcom/android/tools/r8/internal/ze0;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/xe0;
    .locals 2

    const/4 v0, 0x0

    .line 1
    :try_start_0
    sget-object v1, Lcom/android/tools/r8/internal/ze0;->f:Lcom/android/tools/r8/internal/we0;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    new-instance v1, Lcom/android/tools/r8/internal/ze0;

    invoke-direct {v1, p1, p2}, Lcom/android/tools/r8/internal/ze0;-><init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/xe0;->a(Lcom/android/tools/r8/internal/ze0;)Lcom/android/tools/r8/internal/xe0;

    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 4
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 5
    check-cast p2, Lcom/android/tools/r8/internal/ze0;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 6
    :try_start_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p1

    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_0

    .line 9
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/xe0;->a(Lcom/android/tools/r8/internal/ze0;)Lcom/android/tools/r8/internal/xe0;

    .line 11
    :cond_0
    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/ze0;)Lcom/android/tools/r8/internal/xe0;
    .locals 1

    .line 12
    sget-object v0, Lcom/android/tools/r8/internal/ze0;->e:Lcom/android/tools/r8/internal/ze0;

    if-ne p1, v0, :cond_0

    return-object p0

    .line 13
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ze0;->a()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_1

    .line 14
    iget-object v0, p1, Lcom/android/tools/r8/internal/ze0;->b:Ljava/lang/String;

    iput-object v0, p0, Lcom/android/tools/r8/internal/xe0;->b:Ljava/lang/String;

    .line 15
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 17
    :cond_1
    iget v0, p1, Lcom/android/tools/r8/internal/ze0;->c:I

    if-eqz v0, :cond_2

    .line 18
    iput v0, p0, Lcom/android/tools/r8/internal/xe0;->c:I

    .line 19
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 20
    :cond_2
    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 21
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/xe0;

    .line 22
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    return-object p0
.end method

.method public final addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/xe0;

    return-object p1
.end method

.method public final b()Lcom/android/tools/r8/internal/ze0;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/ze0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/ze0;-><init>(Lcom/android/tools/r8/internal/xe0;)V

    .line 2
    iget-object v1, p0, Lcom/android/tools/r8/internal/xe0;->b:Ljava/lang/String;

    iput-object v1, v0, Lcom/android/tools/r8/internal/ze0;->b:Ljava/lang/String;

    .line 3
    iget v1, p0, Lcom/android/tools/r8/internal/xe0;->c:I

    iput v1, v0, Lcom/android/tools/r8/internal/ze0;->c:I

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onBuilt()V

    return-object v0
.end method

.method public final build()Lcom/android/tools/r8/internal/AU;
    .locals 2

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/xe0;->b()Lcom/android/tools/r8/internal/ze0;

    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/ze0;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 6
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public final build()Lcom/android/tools/r8/internal/vU;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/xe0;->b()Lcom/android/tools/r8/internal/ze0;

    move-result-object v0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/ze0;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 3
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public final bridge synthetic buildPartial()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/xe0;->b()Lcom/android/tools/r8/internal/ze0;

    move-result-object v0

    return-object v0
.end method

.method public final clone()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->clone()Lcom/android/tools/r8/internal/dy;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/xe0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/ze0;->e:Lcom/android/tools/r8/internal/ze0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/ze0;->e:Lcom/android/tools/r8/internal/ze0;

    return-object v0
.end method

.method public final getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->e0:Lcom/android/tools/r8/internal/Ok;

    return-object v0
.end method

.method public final internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->f0:Lcom/android/tools/r8/internal/sy;

    .line 2
    const-class v1, Lcom/android/tools/r8/internal/ze0;

    const-class v2, Lcom/android/tools/r8/internal/xe0;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/xe0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/xe0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 4
    instance-of v0, p1, Lcom/android/tools/r8/internal/ze0;

    if-eqz v0, :cond_0

    .line 5
    check-cast p1, Lcom/android/tools/r8/internal/ze0;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/xe0;->a(Lcom/android/tools/r8/internal/ze0;)Lcom/android/tools/r8/internal/xe0;

    move-result-object p1

    goto :goto_0

    .line 7
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    move-object p1, p0

    :goto_0
    return-object p1
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/M0;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/xe0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/xe0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 8
    instance-of v0, p1, Lcom/android/tools/r8/internal/ze0;

    if-eqz v0, :cond_0

    .line 9
    check-cast p1, Lcom/android/tools/r8/internal/ze0;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/xe0;->a(Lcom/android/tools/r8/internal/ze0;)Lcom/android/tools/r8/internal/xe0;

    move-result-object p1

    goto :goto_0

    .line 11
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    move-object p1, p0

    :goto_0
    return-object p1
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/zU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/xe0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/xe0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/xe0;

    return-object p1
.end method

.method public final setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/xe0;

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/xe0;

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/xe0;

    return-object p1
.end method
