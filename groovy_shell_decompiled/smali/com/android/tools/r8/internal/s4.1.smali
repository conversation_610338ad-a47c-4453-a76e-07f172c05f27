.class public final Lcom/android/tools/r8/internal/s4;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/profile/art/ArtProfileClassRuleBuilder;


# instance fields
.field public final synthetic a:Lcom/android/tools/r8/internal/u4;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/u4;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/s4;->a:Lcom/android/tools/r8/internal/u4;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final setClassReference(Lcom/android/tools/r8/references/ClassReference;)Lcom/android/tools/r8/profile/art/ArtProfileClassRuleBuilder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/s4;->a:Lcom/android/tools/r8/internal/u4;

    iget-object v0, v0, Lcom/android/tools/r8/internal/u4;->a:Ljava/io/OutputStreamWriter;

    .line 2
    invoke-static {p1}, Lcom/android/tools/r8/internal/xd;->a(Lcom/android/tools/r8/references/ClassReference;)Ljava/lang/String;

    move-result-object p1

    .line 3
    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/v4;->a(Ljava/io/OutputStreamWriter;Ljava/lang/String;)V

    return-object p0
.end method
