.class public final Lcom/android/tools/r8/internal/Ve;
.super Lcom/android/tools/r8/internal/Se;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final b:Lcom/android/tools/r8/internal/cd0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/cd0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Se;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/Ve;->b:Lcom/android/tools/r8/internal/cd0;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)I
    .locals 1

    if-ne p1, p2, :cond_0

    const/4 p1, 0x0

    return p1

    .line 1
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ve;->b:Lcom/android/tools/r8/internal/cd0;

    invoke-interface {v0, p1}, Lcom/android/tools/r8/internal/cd0;->a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ve;->b:Lcom/android/tools/r8/internal/cd0;

    invoke-interface {v0, p2}, Lcom/android/tools/r8/internal/cd0;->a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p2

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->y0()Lcom/android/tools/r8/graph/I2;

    move-result-object p1

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/J2;->y0()Lcom/android/tools/r8/graph/I2;

    move-result-object p2

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 4
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Se;->a(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;)I

    move-result p1

    return p1
.end method
