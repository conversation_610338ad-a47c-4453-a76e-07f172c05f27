.class public final Lcom/android/tools/r8/internal/sY;
.super Lcom/android/tools/r8/internal/bu0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final b:Lcom/android/tools/r8/graph/y;

.field public final c:Lcom/android/tools/r8/utils/w;

.field public final d:Ljava/util/Set;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/bu0;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/sY;->c:Lcom/android/tools/r8/utils/w;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sY;->m()Ljava/util/Set;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/sY;->d:Ljava/util/Set;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/j1;)Z
    .locals 0

    .line 11
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/j1;->R0()Lcom/android/tools/r8/graph/H4;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/H4;->N()Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/G2;Ljava/util/Set;)V
    .locals 3

    .line 20
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G2;->T()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    invoke-virtual {p0, v0, p2}, Lcom/android/tools/r8/internal/sY;->a(Lcom/android/tools/r8/graph/J2;Ljava/util/Set;)V

    .line 22
    invoke-static {}, Lcom/android/tools/r8/internal/Ch;->b()Ljava/util/function/Consumer;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda2;

    invoke-direct {v1, p0, p2}, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/internal/sY;Ljava/util/Set;)V

    new-instance v2, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda3;

    invoke-direct {v2, p0, p2}, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda3;-><init>(Lcom/android/tools/r8/internal/sY;Ljava/util/Set;)V

    .line 23
    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/graph/G2;->a(Ljava/util/function/Consumer;Ljava/util/function/Consumer;Ljava/util/function/Consumer;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;Ljava/util/Set;)V
    .locals 2

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 28
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->L0()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 32
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    .line 33
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/h;->c(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/E2;->b(Lcom/android/tools/r8/graph/E0;)Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 34
    iget-object v0, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    .line 35
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->u()Lcom/android/tools/r8/shaking/p1;

    move-result-object v0

    .line 36
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/shaking/p1;->a(Lcom/android/tools/r8/graph/E2;)Lcom/android/tools/r8/shaking/f1;

    move-result-object v0

    .line 37
    iget-object v1, p0, Lcom/android/tools/r8/internal/sY;->c:Lcom/android/tools/r8/utils/w;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/shaking/n1;->d(Lcom/android/tools/r8/shaking/L0;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 38
    invoke-interface {p2, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    :cond_1
    return-void
.end method

.method public final synthetic a(Ljava/util/Set;Lcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 24
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lcom/android/tools/r8/internal/sY;->a(Lcom/android/tools/r8/graph/J2;Ljava/util/Set;)V

    return-void
.end method

.method public final synthetic a(Ljava/util/Set;Lcom/android/tools/r8/graph/x2;)V
    .locals 1

    .line 25
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/x2;->x0()Ljava/lang/Iterable;

    move-result-object p2

    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/J2;

    .line 26
    invoke-virtual {p0, v0, p1}, Lcom/android/tools/r8/internal/sY;->a(Lcom/android/tools/r8/graph/J2;Ljava/util/Set;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public final synthetic a(Ljava/util/Set;Lcom/android/tools/r8/internal/o4;)V
    .locals 3

    .line 12
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/o4;->e()Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    .line 13
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/J2;->H0()Z

    move-result v1

    if-eqz v1, :cond_0

    return-void

    .line 16
    :cond_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    .line 17
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/h;->c(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object v1

    .line 18
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/x2;->d(Lcom/android/tools/r8/graph/E0;)Lcom/android/tools/r8/graph/j1;

    move-result-object v0

    if-nez v0, :cond_1

    .line 19
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/o4;->e()Lcom/android/tools/r8/graph/x2;

    move-result-object p2

    invoke-virtual {p0, p2, p1}, Lcom/android/tools/r8/internal/sY;->a(Lcom/android/tools/r8/graph/G2;Ljava/util/Set;)V

    :cond_1
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/iu0;)Z
    .locals 3

    .line 1
    iget-object p1, p1, Lcom/android/tools/r8/internal/iu0;->b:Lcom/android/tools/r8/graph/E2;

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->u()Lcom/android/tools/r8/shaking/p1;

    move-result-object v0

    .line 4
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/shaking/p1;->a(Lcom/android/tools/r8/graph/E2;)Lcom/android/tools/r8/shaking/f1;

    move-result-object v0

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/sY;->c:Lcom/android/tools/r8/utils/w;

    .line 6
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/shaking/n1;->c(Lcom/android/tools/r8/shaking/L0;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 7
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/shaking/n1;->e(Lcom/android/tools/r8/shaking/L0;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 8
    iget-boolean v0, v0, Lcom/android/tools/r8/shaking/f1;->p:Z

    if-eqz v0, :cond_0

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/internal/sY;->d:Ljava/util/Set;

    .line 10
    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final f()Ljava/lang/String;
    .locals 1

    const-string v0, "NoKeptClassesPolicy"

    return-object v0
.end method

.method public final m()Ljava/util/Set;
    .locals 5

    .line 1
    invoke-static {}, Lcom/android/tools/r8/internal/kj0;->c()Ljava/util/Set;

    move-result-object v0

    .line 7
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 8
    iget-object v2, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->u()Lcom/android/tools/r8/shaking/p1;

    move-result-object v2

    .line 9
    new-instance v3, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda6;

    invoke-direct {v3, v1}, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda6;-><init>(Ljava/util/List;)V

    iget-object v4, p0, Lcom/android/tools/r8/internal/sY;->c:Lcom/android/tools/r8/utils/w;

    invoke-virtual {v2, v3, v4}, Lcom/android/tools/r8/shaking/p1;->c(Ljava/util/function/Consumer;Lcom/android/tools/r8/utils/w;)V

    .line 10
    new-instance v3, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda5;

    invoke-direct {v3, v1}, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda5;-><init>(Ljava/util/List;)V

    iget-object v4, p0, Lcom/android/tools/r8/internal/sY;->c:Lcom/android/tools/r8/utils/w;

    invoke-virtual {v2, v3, v4}, Lcom/android/tools/r8/shaking/p1;->b(Ljava/util/function/Consumer;Lcom/android/tools/r8/utils/w;)V

    .line 11
    new-instance v3, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda4;

    invoke-direct {v3, v1}, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda4;-><init>(Ljava/util/List;)V

    iget-object v4, p0, Lcom/android/tools/r8/internal/sY;->c:Lcom/android/tools/r8/utils/w;

    invoke-virtual {v2, v3, v4}, Lcom/android/tools/r8/shaking/p1;->a(Ljava/util/function/Consumer;Lcom/android/tools/r8/utils/w;)V

    .line 12
    invoke-virtual {v1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/G2;

    .line 13
    invoke-virtual {p0, v2, v0}, Lcom/android/tools/r8/internal/sY;->a(Lcom/android/tools/r8/graph/G2;Ljava/util/Set;)V

    goto :goto_0

    .line 14
    :cond_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/E2;

    .line 15
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/E0;->C1()Ljava/lang/Iterable;

    move-result-object v3

    sget-object v4, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda0;

    invoke-static {v3, v4}, Lcom/android/tools/r8/internal/TI;->b(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 16
    invoke-interface {v0, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 17
    :cond_2
    iget-object v1, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    .line 18
    iget-object v1, v1, Lcom/android/tools/r8/shaking/i;->q:Ljava/util/Set;

    .line 19
    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/x2;

    .line 20
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {p0, v2, v0}, Lcom/android/tools/r8/internal/sY;->a(Lcom/android/tools/r8/graph/J2;Ljava/util/Set;)V

    goto :goto_2

    .line 25
    :cond_3
    iget-object v1, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    .line 26
    iget-object v1, v1, Lcom/android/tools/r8/shaking/i;->n:Ljava/util/Set;

    .line 27
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_4

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/G2;

    .line 28
    invoke-virtual {p0, v2, v0}, Lcom/android/tools/r8/internal/sY;->a(Lcom/android/tools/r8/graph/G2;Ljava/util/Set;)V

    goto :goto_3

    .line 29
    :cond_4
    iget-object v1, p0, Lcom/android/tools/r8/internal/sY;->b:Lcom/android/tools/r8/graph/y;

    .line 30
    iget-object v1, v1, Lcom/android/tools/r8/graph/y;->d:Lcom/android/tools/r8/internal/i4;

    .line 31
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_4
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_5

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/Z3;

    .line 33
    invoke-static {}, Lcom/android/tools/r8/internal/Ch;->c()Lcom/android/tools/r8/internal/op0;

    move-result-object v3

    new-instance v4, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda1;

    invoke-direct {v4, p0, v0}, Lcom/android/tools/r8/internal/sY$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/sY;Ljava/util/Set;)V

    .line 34
    invoke-virtual {v2, v3, v4}, Lcom/android/tools/r8/internal/Z3;->a(Lcom/android/tools/r8/internal/op0;Lcom/android/tools/r8/internal/op0;)V

    goto :goto_4

    :cond_5
    return-object v0
.end method
