.class public final Lcom/android/tools/r8/internal/xI;
.super Lcom/android/tools/r8/internal/uI;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic m:Z = true


# instance fields
.field public final l:Lcom/android/tools/r8/graph/F2;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/internal/vt0;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p3, p4}, Lcom/android/tools/r8/internal/uI;-><init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/vt0;Ljava/util/List;)V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/xI;->l:Lcom/android/tools/r8/graph/F2;

    return-void
.end method


# virtual methods
.method public final H2()I
    .locals 1

    const/16 v0, 0x25

    return v0
.end method

.method public final O2()Lcom/android/tools/r8/graph/J2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/xI;->l:Lcom/android/tools/r8/graph/F2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/F2;->e:Lcom/android/tools/r8/graph/J2;

    return-object v0
.end method

.method public final P2()Lcom/android/tools/r8/internal/JI;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/JI;->k:Lcom/android/tools/r8/internal/JI;

    return-object v0
.end method

.method public final Q2()Ljava/lang/String;
    .locals 1

    const-string v0, "Polymorphic"

    return-object v0
.end method

.method public final T2()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final V2()Lcom/android/tools/r8/graph/F2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/xI;->l:Lcom/android/tools/r8/graph/F2;

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/ir/optimize/X;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/ir/optimize/O;
    .locals 0

    .line 43
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 44
    sget-object p1, Lcom/android/tools/r8/ir/optimize/O;->c:Lcom/android/tools/r8/ir/optimize/O;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/ir/optimize/H;Lcom/android/tools/r8/internal/sc;Lcom/android/tools/r8/internal/zu0;)Lcom/android/tools/r8/ir/optimize/P;
    .locals 0

    .line 45
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/tC;)Ljava/lang/Object;
    .locals 0

    .line 46
    invoke-virtual {p1, p0}, Lcom/android/tools/r8/internal/tC;->a(Lcom/android/tools/r8/internal/mI;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/R8;)V
    .locals 4

    .line 27
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    .line 28
    iget-object v1, p1, Lcom/android/tools/r8/internal/R8;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    .line 33
    iget-object v2, v0, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    .line 34
    iget-object v3, p0, Lcom/android/tools/r8/internal/xI;->l:Lcom/android/tools/r8/graph/F2;

    .line 35
    iget-object v0, v0, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v1, v2, v3, v0}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    .line 36
    new-instance v1, Lcom/android/tools/r8/internal/O9;

    const/16 v2, 0xb6

    const/4 v3, 0x0

    invoke-direct {v1, v2, v0, v3}, Lcom/android/tools/r8/internal/O9;-><init>(ILcom/android/tools/r8/graph/x2;Z)V

    invoke-virtual {p1, v1, p0}, Lcom/android/tools/r8/internal/R8;->a(Lcom/android/tools/r8/internal/H9;Lcom/android/tools/r8/internal/rD;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/Wm;)V
    .locals 11

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/mI;->R2()I

    move-result v0

    .line 2
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/Wm;->a(I)V

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/mI;->d(Lcom/android/tools/r8/internal/Wm;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 4
    sget-boolean v1, Lcom/android/tools/r8/internal/xI;->m:Z

    if-nez v1, :cond_0

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/mI;->c(Lcom/android/tools/r8/internal/Wm;)V

    .line 5
    :cond_0
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/mI;->b(Lcom/android/tools/r8/internal/Wm;)I

    move-result v1

    .line 6
    new-instance v2, Lcom/android/tools/r8/internal/qp;

    .line 8
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v3

    .line 9
    iget-object v4, p0, Lcom/android/tools/r8/internal/xI;->l:Lcom/android/tools/r8/graph/F2;

    .line 10
    invoke-direct {v2, v1, v0, v3, v4}, Lcom/android/tools/r8/internal/qp;-><init>(IILcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/F2;)V

    goto :goto_0

    :cond_1
    const/4 v0, 0x5

    new-array v0, v0, [I

    .line 13
    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/internal/mI;->a(Lcom/android/tools/r8/internal/Wm;[I)I

    move-result v2

    .line 14
    new-instance v10, Lcom/android/tools/r8/internal/pp;

    .line 17
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v3

    .line 18
    iget-object v4, p0, Lcom/android/tools/r8/internal/xI;->l:Lcom/android/tools/r8/graph/F2;

    const/4 v1, 0x0

    .line 19
    aget v5, v0, v1

    const/4 v1, 0x1

    aget v6, v0, v1

    const/4 v1, 0x2

    aget v7, v0, v1

    const/4 v1, 0x3

    aget v8, v0, v1

    const/4 v1, 0x4

    aget v9, v0, v1

    move-object v1, v10

    invoke-direct/range {v1 .. v9}, Lcom/android/tools/r8/internal/pp;-><init>(ILcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/F2;IIIII)V

    move-object v2, v10

    .line 26
    :goto_0
    invoke-virtual {p0, v2, p1}, Lcom/android/tools/r8/internal/mI;->a(Lcom/android/tools/r8/internal/Yo;Lcom/android/tools/r8/internal/Wm;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/gR;)V
    .locals 3

    .line 37
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    .line 38
    iget-object v1, p0, Lcom/android/tools/r8/internal/xI;->l:Lcom/android/tools/r8/graph/F2;

    .line 39
    iget-object v2, p0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 40
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 41
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/cB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/cB;

    move-result-object v0

    const/16 v1, 0xde

    .line 42
    invoke-virtual {p1, v1, v0, v2}, Lcom/android/tools/r8/internal/gR;->a(ILjava/util/List;Ljava/util/List;)Lcom/android/tools/r8/internal/gR;

    return-void
.end method

.method public final b(Lcom/android/tools/r8/internal/rD;)Z
    .locals 2

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    instance-of v0, p1, Lcom/android/tools/r8/internal/xI;

    if-eqz v0, :cond_0

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/xI;->l:Lcom/android/tools/r8/graph/F2;

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->f0()Lcom/android/tools/r8/internal/xI;

    move-result-object v1

    iget-object v1, v1, Lcom/android/tools/r8/internal/xI;->l:Lcom/android/tools/r8/graph/F2;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/E;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 5
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/uI;->b(Lcom/android/tools/r8/internal/rD;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final e(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/internal/l60;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public final f0()Lcom/android/tools/r8/internal/xI;
    .locals 0

    return-object p0
.end method
