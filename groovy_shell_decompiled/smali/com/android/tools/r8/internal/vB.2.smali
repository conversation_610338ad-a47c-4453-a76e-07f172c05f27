.class public final Lcom/android/tools/r8/internal/vB;
.super Lcom/android/tools/r8/internal/Gs0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public b:I

.field public c:Ljava/lang/Object;

.field public final synthetic d:Lcom/android/tools/r8/internal/Gs0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Gs0;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/vB;->d:Lcom/android/tools/r8/internal/Gs0;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/Gs0;-><init>()V

    return-void
.end method


# virtual methods
.method public final hasNext()Z
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/vB;->b:I

    if-gtz v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/vB;->d:Lcom/android/tools/r8/internal/Gs0;

    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public final next()Ljava/lang/Object;
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/vB;->b:I

    if-gtz v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/vB;->d:Lcom/android/tools/r8/internal/Gs0;

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/ZW;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/ZW;->b()Ljava/lang/Object;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/vB;->c:Ljava/lang/Object;

    .line 4
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/ZW;->a()I

    move-result v0

    iput v0, p0, Lcom/android/tools/r8/internal/vB;->b:I

    .line 6
    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/vB;->b:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lcom/android/tools/r8/internal/vB;->b:I

    .line 11
    iget-object v0, p0, Lcom/android/tools/r8/internal/vB;->c:Ljava/lang/Object;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
