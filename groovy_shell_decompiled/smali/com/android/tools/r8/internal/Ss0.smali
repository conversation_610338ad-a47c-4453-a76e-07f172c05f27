.class public abstract Lcom/android/tools/r8/internal/Ss0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic e:Z


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Ljava/lang/String;

.field public final c:Lcom/android/tools/r8/internal/A2;

.field public final d:Ljava/util/Set;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/Vs0;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/Ss0;->e:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Ljava/lang/String;Lcom/android/tools/r8/internal/A2;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-static {}, Lcom/android/tools/r8/internal/Yi0;->a()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/Ss0;->d:Ljava/util/Set;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/internal/Ss0;->a:Lcom/android/tools/r8/graph/y;

    .line 6
    iput-object p2, p0, Lcom/android/tools/r8/internal/Ss0;->b:Ljava/lang/String;

    .line 7
    iput-object p3, p0, Lcom/android/tools/r8/internal/Ss0;->c:Lcom/android/tools/r8/internal/A2;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/internal/ZA;)V
    .locals 1

    .line 28
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/F2;->p0()Lcom/android/tools/r8/graph/L2;

    move-result-object p0

    new-instance v0, Lcom/android/tools/r8/internal/Ss0$$ExternalSyntheticLambda0;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/Ss0$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/ZA;)V

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/graph/L2;->d(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/ZA;Lcom/android/tools/r8/graph/J2;)V
    .locals 1

    .line 26
    sget-boolean v0, Lcom/android/tools/r8/internal/Ss0;->e:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->V0()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 27
    :cond_1
    :goto_0
    new-instance v0, Lcom/android/tools/r8/internal/Ra;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->W0()Z

    move-result p1

    if-eqz p1, :cond_2

    sget-object p1, Lcom/android/tools/r8/internal/Ra$a;->d:Lcom/android/tools/r8/internal/Ra$a;

    goto :goto_1

    :cond_2
    sget-object p1, Lcom/android/tools/r8/internal/Ra$a;->c:Lcom/android/tools/r8/internal/Ra$a;

    :goto_1
    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/Ra;-><init>(Lcom/android/tools/r8/internal/Ra$a;)V

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    return-void
.end method

.method public static b(Lcom/android/tools/r8/internal/ZA;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/ZA;
    .locals 4

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->V0()Z

    move-result v0

    if-nez v0, :cond_5

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/Ss0;->e:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->V0()Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 3
    :cond_1
    :goto_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 4
    new-instance v0, Lcom/android/tools/r8/internal/j9;

    const-wide/16 v1, 0x0

    sget-object v3, Lcom/android/tools/r8/internal/It0;->b:Lcom/android/tools/r8/internal/It0;

    .line 5
    iget-object p1, p1, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    iget-object p1, p1, Lcom/android/tools/r8/graph/I2;->f:[B

    const/4 v3, 0x0

    aget-byte p1, p1, v3

    int-to-char p1, p1

    invoke-static {p1}, Lcom/android/tools/r8/internal/It0;->a(C)Lcom/android/tools/r8/internal/It0;

    move-result-object p1

    .line 6
    invoke-direct {v0, v1, v2, p1}, Lcom/android/tools/r8/internal/j9;-><init>(JLcom/android/tools/r8/internal/It0;)V

    goto :goto_2

    :cond_2
    if-nez v0, :cond_4

    .line 8
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->T0()Z

    move-result p1

    if-eqz p1, :cond_3

    goto :goto_1

    :cond_3
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 9
    :cond_4
    :goto_1
    new-instance v0, Lcom/android/tools/r8/internal/h9;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/h9;-><init>()V

    .line 10
    :goto_2
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    :cond_5
    return-object p0
.end method


# virtual methods
.method public abstract a(Lcom/android/tools/r8/origin/Origin;Lcom/android/tools/r8/position/MethodPosition;)Lcom/android/tools/r8/errors/UnsupportedFeatureDiagnostic;
.end method

.method public final a()Lcom/android/tools/r8/internal/k9;
    .locals 6

    .line 19
    new-instance v0, Lcom/android/tools/r8/internal/k9;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ss0;->a:Lcom/android/tools/r8/graph/y;

    .line 21
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    iget-object v2, p0, Lcom/android/tools/r8/internal/Ss0;->a:Lcom/android/tools/r8/graph/y;

    .line 24
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/utils/w;->H()Lcom/android/tools/r8/internal/A2;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/A2;->c()Lcom/android/tools/r8/internal/Wr;

    move-result-object v2

    iget-object v3, p0, Lcom/android/tools/r8/internal/Ss0;->b:Ljava/lang/String;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Instruction is unrepresentable in DEX "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v4, ": "

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 25
    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/B1;->c(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/k9;-><init>(Lcom/android/tools/r8/graph/I2;)V

    return-object v0
.end method

.method public abstract a(Lcom/android/tools/r8/internal/H9;)Lcom/android/tools/r8/internal/ll;
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ss0;->d:Ljava/util/Set;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 2
    iget-object v0, p1, Lcom/android/tools/r8/graph/G0;->b:Lcom/android/tools/r8/graph/E0;

    iget-object v0, v0, Lcom/android/tools/r8/graph/E0;->d:Lcom/android/tools/r8/origin/Origin;

    .line 3
    invoke-static {p1}, Lcom/android/tools/r8/position/MethodPosition;->create(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/position/MethodPosition;

    move-result-object p1

    invoke-virtual {p0, v0, p1}, Lcom/android/tools/r8/internal/Ss0;->a(Lcom/android/tools/r8/origin/Origin;Lcom/android/tools/r8/position/MethodPosition;)Lcom/android/tools/r8/errors/UnsupportedFeatureDiagnostic;

    move-result-object p1

    .line 4
    sget-boolean v0, Lcom/android/tools/r8/internal/Ss0;->e:Z

    if-nez v0, :cond_2

    invoke-virtual {p1}, Lcom/android/tools/r8/errors/UnsupportedFeatureDiagnostic;->getSupportedApiLevel()I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/internal/Ss0;->c:Lcom/android/tools/r8/internal/A2;

    if-eqz v0, :cond_2

    .line 5
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/errors/UnsupportedFeatureDiagnostic;->getSupportedApiLevel()I

    move-result v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ss0;->c:Lcom/android/tools/r8/internal/A2;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/A2;->d()I

    move-result v1

    if-ne v0, v1, :cond_1

    goto :goto_0

    .line 6
    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 8
    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ss0;->a:Lcom/android/tools/r8/graph/y;

    .line 9
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    .line 10
    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    .line 11
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/bd0;->warning(Lcom/android/tools/r8/Diagnostic;)V

    :cond_3
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/ZA;)V
    .locals 4

    .line 12
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ss0;->a:Lcom/android/tools/r8/graph/y;

    .line 13
    invoke-static {v0, p2, p1}, Lcom/android/tools/r8/ir/optimize/J0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/ir/optimize/I0;

    move-result-object p1

    .line 14
    iget-object p1, p1, Lcom/android/tools/r8/ir/optimize/I0;->a:Lcom/android/tools/r8/graph/D5;

    .line 15
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ss0;->a()Lcom/android/tools/r8/internal/k9;

    move-result-object p2

    new-instance v0, Lcom/android/tools/r8/internal/O9;

    .line 16
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    const/16 v1, 0xb8

    const/4 v2, 0x0

    invoke-direct {v0, v1, p1, v2}, Lcom/android/tools/r8/internal/O9;-><init>(ILcom/android/tools/r8/graph/x2;Z)V

    new-instance p1, Lcom/android/tools/r8/internal/Ra;

    sget-object v1, Lcom/android/tools/r8/internal/Ra$a;->c:Lcom/android/tools/r8/internal/Ra$a;

    invoke-direct {p1, v1}, Lcom/android/tools/r8/internal/Ra;-><init>(Lcom/android/tools/r8/internal/Ra$a;)V

    const/4 v1, 0x3

    new-array v3, v1, [Lcom/android/tools/r8/internal/H9;

    aput-object p2, v3, v2

    const/4 p2, 0x1

    aput-object v0, v3, p2

    const/4 p2, 0x2

    aput-object p1, v3, p2

    .line 17
    invoke-static {v1, v3}, Lcom/android/tools/r8/internal/v10;->a(I[Ljava/lang/Object;)[Ljava/lang/Object;

    .line 18
    invoke-virtual {p3, v1, v3}, Lcom/android/tools/r8/internal/ZA;->a(I[Ljava/lang/Object;)V

    return-void
.end method
