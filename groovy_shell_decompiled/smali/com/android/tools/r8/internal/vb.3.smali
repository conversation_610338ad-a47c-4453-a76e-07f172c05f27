.class public final Lcom/android/tools/r8/internal/vb;
.super Lcom/android/tools/r8/internal/zb;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final c:Lcom/android/tools/r8/internal/vb;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/vb;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/vb;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/vb;->c:Lcom/android/tools/r8/internal/vb;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const-string v0, "CharMatcher.any()"

    .line 1
    invoke-direct {p0, v0}, Lcom/android/tools/r8/internal/zb;-><init>(Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/Db;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Bb;->c:Lcom/android/tools/r8/internal/Bb;

    return-object v0
.end method

.method public final b(C)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method public final negate()Ljava/util/function/Predicate;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Bb;->c:Lcom/android/tools/r8/internal/Bb;

    return-object v0
.end method
