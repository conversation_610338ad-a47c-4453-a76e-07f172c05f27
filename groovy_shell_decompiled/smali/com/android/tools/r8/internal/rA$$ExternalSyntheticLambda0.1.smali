.class public final synthetic Lcom/android/tools/r8/internal/rA$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/rA$$ExternalSyntheticLambda0;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/rA$$ExternalSyntheticLambda0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/rA$$ExternalSyntheticLambda0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/rA$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/rA$$ExternalSyntheticLambda0;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/Kp;

    check-cast p1, Lcom/android/tools/r8/graph/s2;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/Kp;-><init>(Lcom/android/tools/r8/graph/s2;)V

    return-object v0
.end method
