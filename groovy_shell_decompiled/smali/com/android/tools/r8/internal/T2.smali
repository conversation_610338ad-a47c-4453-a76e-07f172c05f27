.class public interface abstract Lcom/android/tools/r8/internal/T2;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# virtual methods
.method public abstract a(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/r2;)V
.end method

.method public abstract a(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/r2;Lcom/android/tools/r8/graph/E2;)V
.end method

.method public a(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    return-void
.end method

.method public abstract isEmpty()Z
.end method
