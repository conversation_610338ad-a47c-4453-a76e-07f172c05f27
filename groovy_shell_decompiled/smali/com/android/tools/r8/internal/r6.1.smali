.class public final enum Lcom/android/tools/r8/internal/r6;
.super Lcom/android/tools/r8/internal/u6;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 3

    const/4 v0, 0x7

    const-string v1, "XOR"

    const/4 v2, 0x1

    .line 1
    invoke-direct {p0, v0, v1, v2}, Lcom/android/tools/r8/internal/u6;-><init>(ILjava/lang/String;Z)V

    return-void
.end method


# virtual methods
.method public final a(JJ)J
    .locals 0

    xor-long/2addr p1, p3

    return-wide p1
.end method

.method public final a(Lcom/android/tools/r8/internal/UZ;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/j6;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/bv0;

    invoke-direct {v0, p1, p2, p3, p4}, Lcom/android/tools/r8/internal/bv0;-><init>(Lcom/android/tools/r8/internal/UZ;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)V

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->O2()V

    return-object v0
.end method

.method public final b(Z)Ljava/lang/Integer;
    .locals 0

    const/4 p1, 0x0

    .line 1
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1
.end method

.method public final d(II)I
    .locals 0

    xor-int/2addr p1, p2

    return p1
.end method

.method public final d(Z)Ljava/lang/Integer;
    .locals 0

    const/4 p1, 0x0

    .line 1
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1
.end method
