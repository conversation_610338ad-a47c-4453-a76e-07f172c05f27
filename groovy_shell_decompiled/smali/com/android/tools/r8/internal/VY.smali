.class public final Lcom/android/tools/r8/internal/VY;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Lcom/android/tools/r8/graph/B1;

.field public final c:Lcom/android/tools/r8/internal/WY;

.field public final d:Lcom/android/tools/r8/internal/Ml0;

.field public final e:Lcom/android/tools/r8/internal/i60;


# direct methods
.method public static synthetic $r8$lambda$E2McIAGPWBO62AkYPcfatUmDGDA()Ljava/util/ArrayList;
    .locals 1

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    sget v0, Lcom/android/tools/r8/internal/XY;->n:I

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/WY;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/WY;-><init>()V

    .line 4
    iput-object v0, p0, Lcom/android/tools/r8/internal/VY;->c:Lcom/android/tools/r8/internal/WY;

    .line 9
    iput-object p1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 10
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/VY;->b:Lcom/android/tools/r8/graph/B1;

    .line 11
    iget-object p1, p1, Lcom/android/tools/r8/graph/y;->q:Lcom/android/tools/r8/internal/Ml0;

    .line 12
    iput-object p1, p0, Lcom/android/tools/r8/internal/VY;->d:Lcom/android/tools/r8/internal/Ml0;

    .line 13
    sget-object p1, Lcom/android/tools/r8/internal/l60;->d:Lcom/android/tools/r8/internal/j60;

    .line 14
    new-instance p1, Lcom/android/tools/r8/internal/i60;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/i60;-><init>()V

    .line 15
    iput-object p1, p0, Lcom/android/tools/r8/internal/VY;->e:Lcom/android/tools/r8/internal/i60;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/synthesis/Q;)Lcom/android/tools/r8/synthesis/Q$b;
    .locals 0

    .line 119
    iget-object p0, p0, Lcom/android/tools/r8/synthesis/Q;->U:Lcom/android/tools/r8/synthesis/Q$b;

    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/bx;)V
    .locals 0

    .line 108
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/graph/x2;

    .line 109
    iput-object p0, p1, Lcom/android/tools/r8/internal/bx;->b:Lcom/android/tools/r8/graph/x2;

    const/4 p0, 0x1

    .line 110
    iput-boolean p0, p1, Lcom/android/tools/r8/internal/bx;->e:Z

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/D5;ZLcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/J50;)V
    .locals 2

    .line 105
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    invoke-interface {p3, v0}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/J50;

    move-result-object v0

    .line 106
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    invoke-interface {v0, v1}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/internal/J50;

    if-eqz p1, :cond_0

    .line 107
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-interface {p3, p0, p1}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/x2;)V

    :cond_0
    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/shaking/o1;Lcom/android/tools/r8/graph/D5;)V
    .locals 1

    .line 152
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/shaking/o1;->a(Lcom/android/tools/r8/graph/D5;)V

    .line 153
    sget-object v0, Lcom/android/tools/r8/internal/L9$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/internal/L9$$ExternalSyntheticLambda2;

    invoke-virtual {p0, v0, p1}, Lcom/android/tools/r8/shaking/o1;->a(Ljava/util/function/Consumer;Lcom/android/tools/r8/graph/D5;)V

    return-void
.end method

.method public static synthetic a(Ljava/util/Map;Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/D5;)V
    .locals 1

    .line 56
    sget-object v0, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda4;

    invoke-static {v0}, Lcom/android/tools/r8/internal/lT;->a(Ljava/util/function/Supplier;)Ljava/util/function/Function;

    move-result-object v0

    invoke-interface {p0, p1, v0}, Ljava/util/Map;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/util/List;

    invoke-interface {p0, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public static b(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/bx;)V
    .locals 0

    .line 73
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/graph/x2;

    .line 74
    iput-object p0, p1, Lcom/android/tools/r8/internal/bx;->b:Lcom/android/tools/r8/graph/x2;

    const/4 p0, 0x0

    .line 75
    iput-boolean p0, p1, Lcom/android/tools/r8/internal/bx;->e:Z

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/graph/D5;
    .locals 5

    const/16 v0, 0x1009

    const/4 v1, 0x0

    .line 41
    invoke-static {v0, v1}, Lcom/android/tools/r8/graph/H4;->b(IZ)Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    .line 42
    invoke-virtual {p0, p1, p2, v0}, Lcom/android/tools/r8/internal/VY;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/graph/H4;)Lcom/android/tools/r8/graph/D5;

    move-result-object p2

    .line 46
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->b:Lcom/android/tools/r8/graph/B1;

    .line 47
    new-instance v2, Lcom/android/tools/r8/internal/bx;

    invoke-direct {v2, v0}, Lcom/android/tools/r8/internal/bx;-><init>(Lcom/android/tools/r8/graph/B1;)V

    .line 48
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v0

    new-instance v3, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda8;

    invoke-direct {v3, p1}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda8;-><init>(Lcom/android/tools/r8/graph/D5;)V

    new-instance v4, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda9;

    invoke-direct {v4, p1}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda9;-><init>(Lcom/android/tools/r8/graph/D5;)V

    .line 49
    invoke-virtual {v2, v0, v3, v4}, Lcom/android/tools/r8/internal/bx;->a(ZLjava/util/function/Consumer;Ljava/util/function/Consumer;)Lcom/android/tools/r8/internal/bx;

    move-result-object v0

    .line 53
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0, v2, v1}, Lcom/android/tools/r8/internal/bx;->a(Lcom/android/tools/r8/graph/x2;Z)Lcom/android/tools/r8/internal/bx;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 54
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/bx;->a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/nR;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 55
    invoke-virtual {p1, v0, v1}, Lcom/android/tools/r8/graph/D5;->a(Lcom/android/tools/r8/graph/i0;Lcom/android/tools/r8/graph/y;)V

    return-object p2
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/graph/H4;)Lcom/android/tools/r8/graph/D5;
    .locals 4

    .line 111
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 112
    iget-object v0, v0, Lcom/android/tools/r8/graph/y;->a:Lcom/android/tools/r8/graph/h;

    .line 113
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/h;->g()Lcom/android/tools/r8/synthesis/I;

    move-result-object v0

    .line 114
    sget-object v1, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda6;->INSTANCE:Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda6;

    .line 117
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/ef;->a()Lcom/android/tools/r8/internal/hf;

    move-result-object p2

    iget-object v2, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    new-instance v3, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda12;

    invoke-direct {v3, p0, p3, p1}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda12;-><init>(Lcom/android/tools/r8/internal/VY;Lcom/android/tools/r8/graph/H4;Lcom/android/tools/r8/graph/D5;)V

    .line 118
    invoke-virtual {v0, v1, p2, v2, v3}, Lcom/android/tools/r8/synthesis/I;->b(Lcom/android/tools/r8/synthesis/H;Lcom/android/tools/r8/internal/hf;Lcom/android/tools/r8/graph/y;Ljava/util/function/Consumer;)Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/synthesis/M;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/i0;
    .locals 6

    .line 120
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/i0;->E0()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 121
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object v0

    .line 123
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/h1;->I0()Z

    move-result v2

    if-nez v2, :cond_0

    .line 124
    iput-object v1, p2, Lcom/android/tools/r8/synthesis/M;->h:Lcom/android/tools/r8/graph/x2;

    goto :goto_0

    .line 125
    :cond_0
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    goto :goto_0

    .line 130
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j1;

    .line 131
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object v0

    .line 135
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    move-object v3, p2

    check-cast v3, Lcom/android/tools/r8/graph/x2;

    .line 136
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/h1;->I0()Z

    move-result v4

    iget-object v5, p0, Lcom/android/tools/r8/internal/VY;->b:Lcom/android/tools/r8/graph/B1;

    const/4 v2, 0x1

    move-object v1, p3

    .line 137
    invoke-virtual/range {v0 .. v5}, Lcom/android/tools/r8/graph/i0;->a(Lcom/android/tools/r8/graph/x2;ZLcom/android/tools/r8/graph/x2;ZLcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/i0;

    move-result-object v0

    .line 144
    :goto_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result p1

    if-nez p1, :cond_2

    .line 146
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/x2;->z0()I

    move-result p1

    iget-object p2, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 147
    invoke-static {v0, p1, p2}, Lcom/android/tools/r8/graph/j1;->a(Lcom/android/tools/r8/graph/i0;ILcom/android/tools/r8/graph/y;)V

    :cond_2
    return-object v0
.end method

.method public final a(Ljava/util/concurrent/ExecutorService;)Ljava/util/concurrent/ConcurrentHashMap;
    .locals 4

    .line 36
    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    .line 37
    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 38
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v1

    new-instance v2, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda13;

    invoke-direct {v2, p0, v0}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda13;-><init>(Lcom/android/tools/r8/internal/VY;Ljava/util/Map;)V

    iget-object v3, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 39
    invoke-static {v3, v1, v2, p1}, Lcom/android/tools/r8/K;->a(Lcom/android/tools/r8/graph/y;Ljava/util/Collection;Ljava/util/function/Consumer;Ljava/util/concurrent/ExecutorService;)V

    return-object v0
.end method

.method public final a()V
    .locals 2

    .line 148
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 149
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->u()Lcom/android/tools/r8/shaking/p1;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda11;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda11;-><init>(Lcom/android/tools/r8/internal/VY;)V

    .line 150
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/shaking/p1;->a(Ljava/util/function/Consumer;)Lcom/android/tools/r8/shaking/o1;

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/H4;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/synthesis/M;)V
    .locals 2

    .line 69
    iput-object p1, p3, Lcom/android/tools/r8/synthesis/M;->i:Lcom/android/tools/r8/graph/H4;

    .line 70
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/j1;

    .line 71
    iget-object p1, p1, Lcom/android/tools/r8/graph/j1;->o:Lcom/android/tools/r8/androidapi/f;

    .line 72
    iput-object p1, p3, Lcom/android/tools/r8/synthesis/M;->n:Lcom/android/tools/r8/androidapi/f;

    .line 73
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/j1;

    .line 74
    iget-object p1, p1, Lcom/android/tools/r8/graph/h1;->e:Lcom/android/tools/r8/androidapi/f;

    .line 75
    iput-object p1, p3, Lcom/android/tools/r8/synthesis/M;->m:Lcom/android/tools/r8/androidapi/f;

    .line 76
    iget-object p1, p0, Lcom/android/tools/r8/internal/VY;->b:Lcom/android/tools/r8/graph/B1;

    .line 79
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v1

    .line 80
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    if-nez v1, :cond_0

    .line 81
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/x2;->B0()Lcom/android/tools/r8/graph/F2;

    move-result-object v1

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    invoke-virtual {v1, p1, v0}, Lcom/android/tools/r8/graph/F2;->b(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object p1

    goto :goto_0

    .line 82
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/x2;->B0()Lcom/android/tools/r8/graph/F2;

    move-result-object p1

    .line 83
    :goto_0
    iput-object p1, p3, Lcom/android/tools/r8/synthesis/M;->e:Lcom/android/tools/r8/graph/F2;

    .line 84
    new-instance p1, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda7;

    invoke-direct {p1, p0, p2, p3}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda7;-><init>(Lcom/android/tools/r8/internal/VY;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/synthesis/M;)V

    .line 85
    iput-object p1, p3, Lcom/android/tools/r8/synthesis/M;->g:Lcom/android/tools/r8/synthesis/L;

    return-void
.end method

.method public final synthetic a(Lcom/android/tools/r8/internal/l60;Lcom/android/tools/r8/internal/ff;Lcom/android/tools/r8/internal/L50;Lcom/android/tools/r8/graph/E2;Ljava/util/List;)V
    .locals 0

    .line 104
    invoke-virtual/range {p0 .. p5}, Lcom/android/tools/r8/internal/VY;->b(Lcom/android/tools/r8/internal/l60;Lcom/android/tools/r8/internal/ff;Lcom/android/tools/r8/internal/L50;Lcom/android/tools/r8/graph/E2;Ljava/util/List;)V

    return-void
.end method

.method public final synthetic a(Lcom/android/tools/r8/shaking/o1;)V
    .locals 2

    .line 151
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->e:Lcom/android/tools/r8/internal/i60;

    new-instance v1, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda2;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/shaking/o1;)V

    invoke-interface {v0, v1}, Ljava/util/Collection;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public final synthetic a(Ljava/util/Map;Lcom/android/tools/r8/graph/E2;)V
    .locals 1

    .line 40
    new-instance v0, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda3;

    invoke-direct {v0, p1, p2}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda3;-><init>(Ljava/util/Map;Lcom/android/tools/r8/graph/E2;)V

    invoke-virtual {p0, v0, p2}, Lcom/android/tools/r8/internal/VY;->a(Ljava/util/function/Consumer;Lcom/android/tools/r8/graph/E2;)V

    return-void
.end method

.method public final a(Ljava/util/concurrent/ConcurrentHashMap;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/L50;)V
    .locals 4

    .line 86
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->n()Lcom/android/tools/r8/internal/ff;

    move-result-object v0

    .line 87
    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 88
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/j;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v2

    invoke-static {v1, v2}, Lcom/android/tools/r8/graph/Y3;->a(Lcom/android/tools/r8/graph/y;Ljava/util/Collection;)Lcom/android/tools/r8/graph/Y3;

    move-result-object v2

    .line 89
    new-instance v3, Lcom/android/tools/r8/internal/a60;

    invoke-direct {v3, v1, v2}, Lcom/android/tools/r8/internal/a60;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/Y3;)V

    .line 91
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/X5;->a()Ljava/util/ArrayList;

    move-result-object v3

    .line 92
    invoke-static {v1, v2, v3, p2}, Lcom/android/tools/r8/internal/nW;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/Y3;Ljava/util/ArrayList;Ljava/util/concurrent/ExecutorService;)Lcom/android/tools/r8/internal/i60;

    move-result-object v1

    .line 93
    new-instance v2, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda5;

    invoke-direct {v2, p0, v1, v0, p3}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda5;-><init>(Lcom/android/tools/r8/internal/VY;Lcom/android/tools/r8/internal/l60;Lcom/android/tools/r8/internal/ff;Lcom/android/tools/r8/internal/L50;)V

    iget-object p3, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 102
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p3

    invoke-virtual {p3}, Lcom/android/tools/r8/utils/w;->O()Lcom/android/tools/r8/threading/ThreadingModule;

    move-result-object p3

    .line 103
    invoke-static {p1, v2, p3, p2}, Lcom/android/tools/r8/internal/ep0;->a(Ljava/util/Map;Lcom/android/tools/r8/internal/mp0;Lcom/android/tools/r8/threading/ThreadingModule;Ljava/util/concurrent/ExecutorService;)V

    return-void
.end method

.method public final a(Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Gp0;)V
    .locals 3

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/VY;->a(Ljava/util/concurrent/ExecutorService;)Ljava/util/concurrent/ConcurrentHashMap;

    move-result-object v0

    .line 2
    invoke-virtual {v0}, Ljava/util/concurrent/ConcurrentHashMap;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    goto/16 :goto_1

    .line 5
    :cond_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 6
    invoke-static {v1}, Lcom/android/tools/r8/internal/L50;->b(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/L50;

    move-result-object v1

    .line 7
    invoke-virtual {p0, v0, p1, v1}, Lcom/android/tools/r8/internal/VY;->a(Ljava/util/concurrent/ConcurrentHashMap;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/L50;)V

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, v0}, Lcom/android/tools/r8/internal/L50;->a(Lcom/android/tools/r8/graph/y;)V

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 10
    iget-object v0, v0, Lcom/android/tools/r8/graph/y;->a:Lcom/android/tools/r8/graph/h;

    .line 11
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/h;->g()Lcom/android/tools/r8/synthesis/I;

    move-result-object v0

    .line 12
    invoke-virtual {v0}, Lcom/android/tools/r8/synthesis/I;->b()Z

    move-result v1

    if-nez v1, :cond_1

    goto :goto_0

    .line 15
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->f()Lcom/android/tools/r8/graph/x0;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/synthesis/I;->a(Lcom/android/tools/r8/graph/x0;)Lcom/android/tools/r8/synthesis/a;

    move-result-object v0

    .line 16
    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 17
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    .line 18
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h;->i()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 19
    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 20
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->W()Lcom/android/tools/r8/graph/y;

    move-result-object v1

    iget-object v2, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 21
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->i()Lcom/android/tools/r8/shaking/i;

    move-result-object v2

    invoke-virtual {v2, v0}, Lcom/android/tools/r8/shaking/i;->b(Lcom/android/tools/r8/synthesis/a;)Lcom/android/tools/r8/shaking/i;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/android/tools/r8/graph/y;->b(Lcom/android/tools/r8/graph/h;)Lcom/android/tools/r8/graph/y;

    goto :goto_0

    .line 23
    :cond_2
    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 24
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->V()Lcom/android/tools/r8/graph/y;

    move-result-object v1

    iget-object v2, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 25
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/j;

    invoke-virtual {v2, v0}, Lcom/android/tools/r8/graph/j;->a(Lcom/android/tools/r8/synthesis/a;)Lcom/android/tools/r8/graph/j;

    move-result-object v0

    invoke-virtual {v1, v0}, Lcom/android/tools/r8/graph/y;->b(Lcom/android/tools/r8/graph/h;)Lcom/android/tools/r8/graph/y;

    .line 26
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/VY;->a()V

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->c:Lcom/android/tools/r8/internal/WY;

    .line 28
    iget-object v0, v0, Lcom/android/tools/r8/internal/WY;->a:Lcom/android/tools/r8/internal/g6;

    .line 29
    iget-object v0, v0, Lcom/android/tools/r8/internal/g6;->b:Lcom/android/tools/r8/internal/az;

    .line 30
    invoke-virtual {v0}, Ljava/util/AbstractMap;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_1

    .line 31
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->c:Lcom/android/tools/r8/internal/WY;

    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 32
    new-instance v2, Lcom/android/tools/r8/internal/XY;

    iget-object v0, v0, Lcom/android/tools/r8/internal/WY;->a:Lcom/android/tools/r8/internal/g6;

    invoke-direct {v2, v1, v0}, Lcom/android/tools/r8/internal/XY;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/g6;)V

    .line 33
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 34
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->f()Lcom/android/tools/r8/graph/x0;

    move-result-object v1

    .line 35
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/x0;->a()Lcom/android/tools/r8/graph/d3;

    move-result-object v1

    invoke-virtual {v0, v2, v1, p1, p2}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/internal/TY;Lcom/android/tools/r8/graph/d3;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Gp0;)V

    :goto_1
    return-void
.end method

.method public final a(Ljava/util/function/Consumer;Lcom/android/tools/r8/graph/D5;)V
    .locals 2

    .line 61
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->d:Lcom/android/tools/r8/internal/Ml0;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    invoke-interface {v0, v1}, Lcom/android/tools/r8/internal/l1;->a(Lcom/android/tools/r8/graph/x2;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 62
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->p1()Z

    move-result v0

    if-nez v0, :cond_0

    .line 63
    new-instance v0, Lcom/android/tools/r8/internal/UY;

    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    .line 64
    invoke-direct {v0, p0, v1, p2}, Lcom/android/tools/r8/internal/UY;-><init>(Lcom/android/tools/r8/internal/VY;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)V

    .line 65
    invoke-virtual {p2, v0}, Lcom/android/tools/r8/graph/D5;->a(Lcom/android/tools/r8/graph/b6;)V

    .line 66
    iget-object v0, v0, Lcom/android/tools/r8/graph/c6;->e:Ljava/lang/Boolean;

    .line 67
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 68
    invoke-interface {p1, p2}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public final a(Ljava/util/function/Consumer;Lcom/android/tools/r8/graph/E2;)V
    .locals 2

    .line 57
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->d:Lcom/android/tools/r8/internal/Ml0;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Ml0;->b(Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 60
    :cond_0
    sget-object v0, Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda16;->INSTANCE:Lcom/android/tools/r8/dex/k$$ExternalSyntheticLambda16;

    new-instance v1, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda1;

    invoke-direct {v1, p0, p1}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/VY;Ljava/util/function/Consumer;)V

    invoke-virtual {p2, v1, v0}, Lcom/android/tools/r8/graph/E2;->h(Ljava/util/function/Consumer;Ljava/util/function/Predicate;)V

    return-void
.end method

.method public final b(Lcom/android/tools/r8/internal/l60;Lcom/android/tools/r8/internal/ff;Lcom/android/tools/r8/internal/L50;Lcom/android/tools/r8/graph/E2;Ljava/util/List;)V
    .locals 9

    .line 4
    invoke-static {}, Lcom/android/tools/r8/internal/kj0;->c()Ljava/util/Set;

    move-result-object v0

    .line 5
    invoke-interface {p5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p5

    :goto_0
    invoke-interface {p5}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_9

    invoke-interface {p5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/D5;

    .line 7
    invoke-virtual {p2, v1}, Lcom/android/tools/r8/internal/ff;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/internal/ef;

    move-result-object v2

    .line 8
    sget-boolean v3, Lcom/android/tools/r8/internal/VY;->f:Z

    if-nez v3, :cond_1

    iget-object v4, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v4}, Lcom/android/tools/r8/graph/y;->p()Z

    move-result v4

    if-eqz v4, :cond_0

    goto :goto_1

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 9
    :cond_1
    :goto_1
    iget-object v4, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v4, v1}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object v4

    iget-object v5, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v5

    invoke-virtual {v4, v5}, Lcom/android/tools/r8/shaking/n1;->e(Lcom/android/tools/r8/shaking/L0;)Z

    move-result v4

    const/4 v5, 0x1

    if-nez v4, :cond_2

    goto :goto_4

    .line 13
    :cond_2
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v4

    invoke-virtual {v4}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v4

    if-eqz v4, :cond_3

    goto :goto_3

    .line 18
    :cond_3
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v4

    invoke-virtual {v4}, Lcom/android/tools/r8/graph/g;->i()Z

    move-result v4

    if-eqz v4, :cond_4

    goto :goto_3

    :cond_4
    if-nez v3, :cond_6

    .line 23
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v3

    .line 24
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/H4;->H()Z

    move-result v3

    if-nez v3, :cond_5

    goto :goto_2

    .line 25
    :cond_5
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 26
    :cond_6
    :goto_2
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/j1;->r1()Lcom/android/tools/r8/internal/u20;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/T6;->a()Z

    move-result v3

    if-eqz v3, :cond_7

    .line 27
    iget-object v3, p1, Lcom/android/tools/r8/internal/hn;->b:Ljava/util/Map;

    .line 28
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_7

    :goto_3
    move v3, v5

    goto :goto_5

    :cond_7
    :goto_4
    const/4 v3, 0x0

    :goto_5
    if-eqz v3, :cond_8

    .line 29
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v4

    .line 30
    new-instance v6, Lcom/android/tools/r8/graph/H4;

    .line 31
    iget v7, v4, Lcom/android/tools/r8/graph/g;->b:I

    .line 32
    iget v4, v4, Lcom/android/tools/r8/graph/g;->c:I

    invoke-direct {v6, v7, v4}, Lcom/android/tools/r8/graph/H4;-><init>(II)V

    .line 33
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/g;->s()Lcom/android/tools/r8/graph/g;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/H4;

    .line 34
    iget v6, v4, Lcom/android/tools/r8/graph/g;->c:I

    or-int/lit8 v6, v6, 0x8

    .line 35
    iput v6, v4, Lcom/android/tools/r8/graph/g;->c:I

    .line 36
    invoke-virtual {v4}, Lcom/android/tools/r8/graph/H4;->t()Lcom/android/tools/r8/graph/g;

    move-result-object v4

    .line 37
    check-cast v4, Lcom/android/tools/r8/graph/H4;

    .line 38
    invoke-virtual {p0, v1, v2, v4}, Lcom/android/tools/r8/internal/VY;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/graph/H4;)Lcom/android/tools/r8/graph/D5;

    move-result-object v2

    .line 44
    iget-object v4, p0, Lcom/android/tools/r8/internal/VY;->c:Lcom/android/tools/r8/internal/WY;

    monitor-enter v4

    .line 45
    :try_start_0
    iget-object v6, v4, Lcom/android/tools/r8/internal/WY;->a:Lcom/android/tools/r8/internal/g6;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v7

    check-cast v7, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v8

    check-cast v8, Lcom/android/tools/r8/graph/x2;

    .line 46
    iget-object v6, v6, Lcom/android/tools/r8/internal/g6;->b:Lcom/android/tools/r8/internal/az;

    .line 47
    invoke-virtual {v6, v7, v8, v5}, Lcom/android/tools/r8/internal/az;->a(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 48
    monitor-exit v4

    .line 49
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/j1;

    invoke-interface {v0, v4}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_6

    :catchall_0
    move-exception p1

    .line 50
    monitor-exit v4

    throw p1

    .line 51
    :cond_8
    invoke-virtual {p0, v1, v2}, Lcom/android/tools/r8/internal/VY;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/graph/D5;

    move-result-object v2

    .line 54
    :goto_6
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/x2;

    new-instance v5, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda10;

    invoke-direct {v5, v2, v3, v1}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda10;-><init>(Lcom/android/tools/r8/graph/D5;ZLcom/android/tools/r8/graph/D5;)V

    .line 55
    invoke-virtual {p3, v4, v5}, Lcom/android/tools/r8/internal/L50;->a(Lcom/android/tools/r8/graph/x2;Ljava/util/function/Consumer;)V

    .line 65
    iget-object v1, p0, Lcom/android/tools/r8/internal/VY;->e:Lcom/android/tools/r8/internal/i60;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/hn;->a(Lcom/android/tools/r8/graph/H0;)Z

    goto/16 :goto_0

    .line 67
    :cond_9
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/E0;->d0()Lcom/android/tools/r8/graph/J4;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 68
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result p2

    if-eqz p2, :cond_a

    goto :goto_7

    .line 71
    :cond_a
    iget-object p2, p1, Lcom/android/tools/r8/graph/J4;->b:Lcom/android/tools/r8/graph/K4;

    invoke-virtual {p2, v0}, Lcom/android/tools/r8/graph/K4;->a(Ljava/util/Set;)V

    .line 72
    sget-object p2, Lcom/android/tools/r8/graph/j1;->v:Lcom/android/tools/r8/graph/j1;

    iput-object p2, p1, Lcom/android/tools/r8/graph/J4;->c:Lcom/android/tools/r8/graph/j1;

    :goto_7
    return-void
.end method

.method public final b(Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Gp0;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->d:Lcom/android/tools/r8/internal/Ml0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Ml0;->c()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/internal/VY;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/utils/w;->M()Lcom/android/tools/r8/internal/Ll0;

    move-result-object v0

    .line 2
    iget-boolean v0, v0, Lcom/android/tools/r8/internal/Ll0;->a:Z

    if-eqz v0, :cond_0

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0, p1, p2}, Lcom/android/tools/r8/internal/VY$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/VY;Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Gp0;)V

    const-string p1, "NonStartupInStartupOutliner"

    invoke-virtual {p2, p1, v0}, Lcom/android/tools/r8/internal/Gp0;->a(Ljava/lang/String;Lcom/android/tools/r8/internal/lp0;)V

    :cond_0
    return-void
.end method
