.class public final Lcom/android/tools/r8/internal/W7;
.super Ljava/io/OutputStream;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final f:[B


# instance fields
.field public final a:I

.field public final b:Ljava/util/ArrayList;

.field public c:I

.field public d:[B

.field public e:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x0

    new-array v0, v0, [B

    .line 1
    sput-object v0, Lcom/android/tools/r8/internal/W7;->f:[B

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/io/OutputStream;-><init>()V

    const/16 v0, 0x80

    .line 5
    iput v0, p0, Lcom/android/tools/r8/internal/W7;->a:I

    .line 6
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lcom/android/tools/r8/internal/W7;->b:Ljava/util/ArrayList;

    new-array v0, v0, [B

    .line 7
    iput-object v0, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    return-void
.end method


# virtual methods
.method public final declared-synchronized c()Lcom/android/tools/r8/internal/Y7;
    .locals 5

    monitor-enter p0

    .line 1
    :try_start_0
    iget v0, p0, Lcom/android/tools/r8/internal/W7;->e:I

    iget-object v1, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    array-length v2, v1

    const/4 v3, 0x0

    if-ge v0, v2, :cond_0

    if-lez v0, :cond_1

    .line 2
    new-array v2, v0, [B

    .line 3
    array-length v4, v1

    invoke-static {v4, v0}, Ljava/lang/Math;->min(II)I

    move-result v0

    invoke-static {v1, v3, v2, v3, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/W7;->b:Ljava/util/ArrayList;

    new-instance v1, Lcom/android/tools/r8/internal/XR;

    invoke-direct {v1, v2}, Lcom/android/tools/r8/internal/XR;-><init>([B)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/W7;->b:Ljava/util/ArrayList;

    new-instance v2, Lcom/android/tools/r8/internal/XR;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/XR;-><init>([B)V

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 15
    sget-object v0, Lcom/android/tools/r8/internal/W7;->f:[B

    iput-object v0, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    .line 17
    :cond_1
    :goto_0
    iget v0, p0, Lcom/android/tools/r8/internal/W7;->c:I

    iget v1, p0, Lcom/android/tools/r8/internal/W7;->e:I

    add-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/W7;->c:I

    .line 18
    iput v3, p0, Lcom/android/tools/r8/internal/W7;->e:I

    .line 19
    iget-object v0, p0, Lcom/android/tools/r8/internal/W7;->b:Ljava/util/ArrayList;

    .line 20
    instance-of v1, v0, Ljava/util/Collection;

    if-nez v1, :cond_3

    .line 21
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 22
    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/Y7;

    .line 23
    invoke-virtual {v1, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_2
    move-object v0, v1

    .line 29
    :cond_3
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_4

    .line 30
    sget-object v0, Lcom/android/tools/r8/internal/Y7;->b:Lcom/android/tools/r8/internal/XR;

    goto :goto_2

    .line 32
    :cond_4
    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    invoke-interface {v0}, Ljava/util/Collection;->size()I

    move-result v0

    invoke-static {v1, v0}, Lcom/android/tools/r8/internal/Y7;->a(Ljava/util/Iterator;I)Lcom/android/tools/r8/internal/Y7;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :goto_2
    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 5

    const-string v0, "<ByteString.Output@%s size=%d>"

    .line 1
    invoke-static {p0}, Ljava/lang/System;->identityHashCode(Ljava/lang/Object;)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v1

    monitor-enter p0

    .line 2
    :try_start_0
    iget v2, p0, Lcom/android/tools/r8/internal/W7;->c:I

    iget v3, p0, Lcom/android/tools/r8/internal/W7;->e:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    add-int/2addr v2, v3

    monitor-exit p0

    .line 3
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object v1, v3, v4

    const/4 v1, 0x1

    aput-object v2, v3, v1

    invoke-static {v0, v3}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public final declared-synchronized write(I)V
    .locals 3

    monitor-enter p0

    .line 1
    :try_start_0
    iget v0, p0, Lcom/android/tools/r8/internal/W7;->e:I

    iget-object v1, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    array-length v2, v1

    if-ne v0, v2, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/W7;->b:Ljava/util/ArrayList;

    new-instance v2, Lcom/android/tools/r8/internal/XR;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/XR;-><init>([B)V

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 3
    iget v0, p0, Lcom/android/tools/r8/internal/W7;->c:I

    iget-object v1, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    array-length v1, v1

    add-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/W7;->c:I

    .line 7
    iget v1, p0, Lcom/android/tools/r8/internal/W7;->a:I

    const/4 v2, 0x1

    ushr-int/2addr v0, v2

    invoke-static {v2, v0}, Ljava/lang/Math;->max(II)I

    move-result v0

    invoke-static {v1, v0}, Ljava/lang/Math;->max(II)I

    move-result v0

    .line 9
    new-array v0, v0, [B

    iput-object v0, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    const/4 v0, 0x0

    .line 10
    iput v0, p0, Lcom/android/tools/r8/internal/W7;->e:I

    .line 11
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    iget v1, p0, Lcom/android/tools/r8/internal/W7;->e:I

    add-int/lit8 v2, v1, 0x1

    iput v2, p0, Lcom/android/tools/r8/internal/W7;->e:I

    int-to-byte p1, p1

    aput-byte p1, v0, v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public final declared-synchronized write([BII)V
    .locals 3

    monitor-enter p0

    .line 12
    :try_start_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    array-length v1, v0

    iget v2, p0, Lcom/android/tools/r8/internal/W7;->e:I

    sub-int/2addr v1, v2

    if-gt p3, v1, :cond_0

    .line 14
    invoke-static {p1, p2, v0, v2, p3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 15
    iget p1, p0, Lcom/android/tools/r8/internal/W7;->e:I

    add-int/2addr p1, p3

    iput p1, p0, Lcom/android/tools/r8/internal/W7;->e:I

    goto :goto_0

    .line 18
    :cond_0
    array-length v1, v0

    sub-int/2addr v1, v2

    .line 19
    invoke-static {p1, p2, v0, v2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    add-int/2addr p2, v1

    sub-int/2addr p3, v1

    .line 20
    iget-object v0, p0, Lcom/android/tools/r8/internal/W7;->b:Ljava/util/ArrayList;

    new-instance v1, Lcom/android/tools/r8/internal/XR;

    iget-object v2, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    invoke-direct {v1, v2}, Lcom/android/tools/r8/internal/XR;-><init>([B)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 21
    iget v0, p0, Lcom/android/tools/r8/internal/W7;->c:I

    iget-object v1, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    array-length v1, v1

    add-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/W7;->c:I

    .line 25
    iget v1, p0, Lcom/android/tools/r8/internal/W7;->a:I

    ushr-int/lit8 v0, v0, 0x1

    invoke-static {p3, v0}, Ljava/lang/Math;->max(II)I

    move-result v0

    invoke-static {v1, v0}, Ljava/lang/Math;->max(II)I

    move-result v0

    .line 27
    new-array v0, v0, [B

    iput-object v0, p0, Lcom/android/tools/r8/internal/W7;->d:[B

    const/4 v1, 0x0

    .line 28
    iput v1, p0, Lcom/android/tools/r8/internal/W7;->e:I

    .line 29
    invoke-static {p1, p2, v0, v1, p3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 30
    iput p3, p0, Lcom/android/tools/r8/internal/W7;->e:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :goto_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method
