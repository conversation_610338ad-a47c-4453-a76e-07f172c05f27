.class public final Lcom/android/tools/r8/internal/Vd;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Ljava/util/List;


# direct methods
.method public constructor <init>(Ljava/util/ArrayList;)V
    .locals 0

    .line 4
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/internal/Vd;->a:Ljava/util/List;

    return-void
.end method

.method public varargs constructor <init>([Lcom/android/tools/r8/internal/Ud;)V
    .locals 0

    .line 1
    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lcom/android/tools/r8/internal/Vd;->a:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/internal/Gp0;Ljava/lang/String;Lcom/android/tools/r8/utils/w;)Lcom/android/tools/r8/internal/o30;
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Vd;->a:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/Ud;

    .line 3
    invoke-virtual {v2, p1, p2, p3, p4}, Lcom/android/tools/r8/internal/Ud;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/internal/Gp0;)Lcom/android/tools/r8/internal/Xd;

    move-result-object v3

    .line 4
    invoke-interface {v3}, Lcom/android/tools/r8/internal/Xd;->a()Lcom/android/tools/r8/internal/u20;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/T6;->d()Z

    move-result v3

    or-int/2addr v1, v3

    .line 7
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Ud;->b()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "IR after "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 8
    invoke-static {p1, v2, p5, p6}, Lcom/android/tools/r8/internal/fA;->a(Lcom/android/tools/r8/internal/aA;Ljava/lang/String;Ljava/lang/String;Lcom/android/tools/r8/utils/w;)Ljava/lang/String;

    move-result-object p5

    goto :goto_0

    .line 11
    :cond_0
    new-instance p1, Lcom/android/tools/r8/internal/o30;

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p2

    invoke-direct {p1, p2, p5}, Lcom/android/tools/r8/internal/o30;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object p1
.end method
