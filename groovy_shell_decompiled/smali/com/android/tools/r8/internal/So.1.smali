.class public Lcom/android/tools/r8/internal/So;
.super Lcom/android/tools/r8/internal/Uo;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/qa;


# direct methods
.method public constructor <init>(IILcom/android/tools/r8/graph/l1;)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/Uo;-><init>(IILcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Zo;Lcom/android/tools/r8/graph/t5;)V
    .locals 0

    .line 1
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/t5;->a()[Lcom/android/tools/r8/graph/l1;

    move-result-object p3

    invoke-direct {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/Uo;-><init>(ILcom/android/tools/r8/internal/j8;[Lcom/android/tools/r8/graph/l1;)V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/b6;)V
    .locals 0

    .line 1
    invoke-virtual {p1, p0}, Lcom/android/tools/r8/graph/b6;->a(Lcom/android/tools/r8/internal/qa;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/Vz;)V
    .locals 3

    .line 2
    iget-byte v0, p0, Lcom/android/tools/r8/internal/no;->f:B

    iget-byte v1, p0, Lcom/android/tools/r8/internal/no;->g:B

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uo;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v2

    invoke-virtual {p1, v0, v1, v2}, Lcom/android/tools/r8/internal/Vz;->a(IILcom/android/tools/r8/graph/l1;)V

    return-void
.end method

.method public final h()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final l()Ljava/lang/String;
    .locals 1

    const-string v0, "IgetChar"

    return-object v0
.end method

.method public final s()I
    .locals 1

    const/16 v0, 0x57

    return v0
.end method

.method public final v()Ljava/lang/String;
    .locals 1

    const-string v0, "iget-char"

    return-object v0
.end method
