.class public final synthetic Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/s50;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/M9;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/Y50;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/s50;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/Y50;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/internal/s50;

    iput-object p2, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/internal/M9;

    iput-object p3, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda5;->f$2:Lcom/android/tools/r8/internal/Y50;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/internal/s50;

    iget-object v1, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/internal/M9;

    iget-object v2, p0, Lcom/android/tools/r8/internal/s50$$ExternalSyntheticLambda5;->f$2:Lcom/android/tools/r8/internal/Y50;

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v0, v1, v2, p1}, Lcom/android/tools/r8/internal/s50;->a(Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/Y50;Lcom/android/tools/r8/graph/E2;)V

    return-void
.end method
