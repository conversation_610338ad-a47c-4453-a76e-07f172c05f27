.class public final Lcom/android/tools/r8/internal/Xf0;
.super Lcom/android/tools/r8/internal/dy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# instance fields
.field public b:I

.field public c:Ljava/lang/String;

.field public d:Ljava/util/List;

.field public e:Lcom/android/tools/r8/internal/Xc0;


# direct methods
.method public constructor <init>()V
    .locals 5

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/dy;-><init>()V

    const-string v0, ""

    .line 172
    iput-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->c:Ljava/lang/String;

    .line 249
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    .line 250
    invoke-static {}, Lcom/android/tools/r8/internal/bg0;->a()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 251
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_1

    .line 252
    new-instance v0, Lcom/android/tools/r8/internal/Xc0;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    iget v2, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    const/4 v3, 0x1

    and-int/2addr v2, v3

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    .line 256
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v2

    .line 257
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v4

    invoke-direct {v0, v1, v3, v2, v4}, Lcom/android/tools/r8/internal/Xc0;-><init>(Ljava/util/List;ZLcom/android/tools/r8/internal/ey;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    const/4 v0, 0x0

    .line 258
    iput-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    :cond_1
    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/ay;)V
    .locals 4

    .line 259
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/dy;-><init>(Lcom/android/tools/r8/internal/ey;)V

    const-string p1, ""

    .line 424
    iput-object p1, p0, Lcom/android/tools/r8/internal/Xf0;->c:Ljava/lang/String;

    .line 501
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    .line 502
    invoke-static {}, Lcom/android/tools/r8/internal/bg0;->a()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 503
    iget-object p1, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    if-nez p1, :cond_1

    .line 504
    new-instance p1, Lcom/android/tools/r8/internal/Xc0;

    iget-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    iget v1, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    const/4 v2, 0x1

    and-int/2addr v1, v2

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    .line 508
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v1

    .line 509
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v3

    invoke-direct {p1, v0, v2, v1, v3}, Lcom/android/tools/r8/internal/Xc0;-><init>(Ljava/util/List;ZLcom/android/tools/r8/internal/ey;Z)V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    const/4 p1, 0x0

    .line 510
    iput-object p1, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    :cond_1
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/bg0;)Lcom/android/tools/r8/internal/Xf0;
    .locals 6

    .line 12
    sget-object v0, Lcom/android/tools/r8/internal/bg0;->e:Lcom/android/tools/r8/internal/bg0;

    if-ne p1, v0, :cond_0

    return-object p0

    .line 13
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/internal/bg0;->b:Ljava/lang/String;

    .line 14
    instance-of v1, v0, Ljava/lang/String;

    if-eqz v1, :cond_1

    goto :goto_0

    .line 17
    :cond_1
    check-cast v0, Lcom/android/tools/r8/internal/Z7;

    .line 19
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v0

    .line 20
    iput-object v0, p1, Lcom/android/tools/r8/internal/bg0;->b:Ljava/lang/String;

    .line 21
    :goto_0
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_2

    .line 22
    iget-object v0, p1, Lcom/android/tools/r8/internal/bg0;->b:Ljava/lang/String;

    iput-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->c:Ljava/lang/String;

    .line 23
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 25
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    const/4 v1, 0x1

    if-nez v0, :cond_5

    .line 26
    iget-object v0, p1, Lcom/android/tools/r8/internal/bg0;->c:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_a

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 28
    iget-object v0, p1, Lcom/android/tools/r8/internal/bg0;->c:Ljava/util/List;

    iput-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    .line 29
    iget v0, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    goto :goto_1

    .line 30
    :cond_3
    iget v0, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    and-int/2addr v0, v1

    if-nez v0, :cond_4

    .line 31
    new-instance v0, Ljava/util/ArrayList;

    iget-object v2, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    .line 32
    iget v0, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    or-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    .line 33
    :cond_4
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    iget-object v1, p1, Lcom/android/tools/r8/internal/bg0;->c:Ljava/util/List;

    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 35
    :goto_1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_3

    .line 38
    :cond_5
    iget-object v0, p1, Lcom/android/tools/r8/internal/bg0;->c:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_a

    .line 39
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    .line 40
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xc0;->b:Ljava/util/List;

    .line 41
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_9

    .line 42
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    const/4 v2, 0x0

    .line 43
    iput-object v2, v0, Lcom/android/tools/r8/internal/Xc0;->a:Lcom/android/tools/r8/internal/ey;

    .line 44
    iput-object v2, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    .line 45
    iget-object v0, p1, Lcom/android/tools/r8/internal/bg0;->c:Ljava/util/List;

    iput-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    .line 46
    iget v0, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    .line 47
    sget-boolean v0, Lcom/android/tools/r8/internal/uy;->alwaysUseFieldBuilders:Z

    if-eqz v0, :cond_8

    .line 48
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_7

    .line 49
    new-instance v0, Lcom/android/tools/r8/internal/Xc0;

    iget-object v3, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    iget v4, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    and-int/2addr v4, v1

    if-eqz v4, :cond_6

    goto :goto_2

    :cond_6
    const/4 v1, 0x0

    .line 53
    :goto_2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v4

    .line 54
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v5

    invoke-direct {v0, v3, v1, v4, v5}, Lcom/android/tools/r8/internal/Xc0;-><init>(Ljava/util/List;ZLcom/android/tools/r8/internal/ey;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    .line 55
    iput-object v2, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    .line 57
    :cond_7
    iget-object v2, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    .line 58
    :cond_8
    iput-object v2, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    goto :goto_3

    .line 60
    :cond_9
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    iget-object v1, p1, Lcom/android/tools/r8/internal/bg0;->c:Ljava/util/List;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Xc0;->a(Ljava/lang/Iterable;)V

    .line 61
    :cond_a
    :goto_3
    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 62
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Xf0;

    .line 63
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    return-object p0
.end method

.method public final a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/Xf0;
    .locals 2

    const/4 v0, 0x0

    .line 1
    :try_start_0
    sget-object v1, Lcom/android/tools/r8/internal/bg0;->f:Lcom/android/tools/r8/internal/Wf0;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    new-instance v1, Lcom/android/tools/r8/internal/bg0;

    invoke-direct {v1, p1, p2}, Lcom/android/tools/r8/internal/bg0;-><init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/Xf0;->a(Lcom/android/tools/r8/internal/bg0;)Lcom/android/tools/r8/internal/Xf0;

    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 4
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 5
    check-cast p2, Lcom/android/tools/r8/internal/bg0;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 6
    :try_start_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p1

    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_0

    .line 9
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/Xf0;->a(Lcom/android/tools/r8/internal/bg0;)Lcom/android/tools/r8/internal/Xf0;

    .line 11
    :cond_0
    throw p1
.end method

.method public final addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Xf0;

    return-object p1
.end method

.method public final b()Lcom/android/tools/r8/internal/bg0;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/bg0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/bg0;-><init>(Lcom/android/tools/r8/internal/Xf0;)V

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xf0;->c:Ljava/lang/String;

    iput-object v1, v0, Lcom/android/tools/r8/internal/bg0;->b:Ljava/lang/String;

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xf0;->e:Lcom/android/tools/r8/internal/Xc0;

    if-nez v1, :cond_1

    .line 5
    iget v1, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    and-int/lit8 v1, v1, 0x1

    if-eqz v1, :cond_0

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    invoke-static {v1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    .line 7
    iget v1, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    and-int/lit8 v1, v1, -0x2

    iput v1, p0, Lcom/android/tools/r8/internal/Xf0;->b:I

    .line 9
    :cond_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xf0;->d:Ljava/util/List;

    iput-object v1, v0, Lcom/android/tools/r8/internal/bg0;->c:Ljava/util/List;

    goto :goto_0

    .line 11
    :cond_1
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Xc0;->b()Ljava/util/List;

    move-result-object v1

    iput-object v1, v0, Lcom/android/tools/r8/internal/bg0;->c:Ljava/util/List;

    .line 13
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onBuilt()V

    return-object v0
.end method

.method public final build()Lcom/android/tools/r8/internal/AU;
    .locals 2

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xf0;->b()Lcom/android/tools/r8/internal/bg0;

    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/bg0;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 6
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public final build()Lcom/android/tools/r8/internal/vU;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xf0;->b()Lcom/android/tools/r8/internal/bg0;

    move-result-object v0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/bg0;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 3
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public final bridge synthetic buildPartial()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xf0;->b()Lcom/android/tools/r8/internal/bg0;

    move-result-object v0

    return-object v0
.end method

.method public final clone()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->clone()Lcom/android/tools/r8/internal/dy;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/Xf0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/bg0;->e:Lcom/android/tools/r8/internal/bg0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/bg0;->e:Lcom/android/tools/r8/internal/bg0;

    return-object v0
.end method

.method public final getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->S0:Lcom/android/tools/r8/internal/Ok;

    return-object v0
.end method

.method public final internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->T0:Lcom/android/tools/r8/internal/sy;

    .line 2
    const-class v1, Lcom/android/tools/r8/internal/bg0;

    const-class v2, Lcom/android/tools/r8/internal/Xf0;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Xf0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/Xf0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 4
    instance-of v0, p1, Lcom/android/tools/r8/internal/bg0;

    if-eqz v0, :cond_0

    .line 5
    check-cast p1, Lcom/android/tools/r8/internal/bg0;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Xf0;->a(Lcom/android/tools/r8/internal/bg0;)Lcom/android/tools/r8/internal/Xf0;

    move-result-object p1

    goto :goto_0

    .line 7
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    move-object p1, p0

    :goto_0
    return-object p1
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/M0;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Xf0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/Xf0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 8
    instance-of v0, p1, Lcom/android/tools/r8/internal/bg0;

    if-eqz v0, :cond_0

    .line 9
    check-cast p1, Lcom/android/tools/r8/internal/bg0;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Xf0;->a(Lcom/android/tools/r8/internal/bg0;)Lcom/android/tools/r8/internal/Xf0;

    move-result-object p1

    goto :goto_0

    .line 11
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    move-object p1, p0

    :goto_0
    return-object p1
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/zU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Xf0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/Xf0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Xf0;

    return-object p1
.end method

.method public final setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Xf0;

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Xf0;

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Xf0;

    return-object p1
.end method
