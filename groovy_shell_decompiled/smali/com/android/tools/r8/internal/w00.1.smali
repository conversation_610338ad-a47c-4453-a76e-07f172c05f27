.class public final Lcom/android/tools/r8/internal/w00;
.super Lcom/android/tools/r8/internal/i0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/E00;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/E00;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/w00;->b:Lcom/android/tools/r8/internal/E00;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/i0;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/ES;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/D00;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w00;->b:Lcom/android/tools/r8/internal/E00;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/D00;-><init>(Lcom/android/tools/r8/internal/E00;)V

    return-object v0
.end method

.method public final b(J)Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w00;->b:Lcom/android/tools/r8/internal/E00;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/E00;->a(J)Z

    move-result p1

    return p1
.end method

.method public final clear()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w00;->b:Lcom/android/tools/r8/internal/E00;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/E00;->clear()V

    return-void
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/D00;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w00;->b:Lcom/android/tools/r8/internal/E00;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/D00;-><init>(Lcom/android/tools/r8/internal/E00;)V

    return-object v0
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w00;->b:Lcom/android/tools/r8/internal/E00;

    iget v0, v0, Lcom/android/tools/r8/internal/E00;->h:I

    return v0
.end method
