.class public final Lcom/android/tools/r8/internal/v6;
.super Lcom/android/tools/r8/internal/Ud;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z = true


# instance fields
.field public final e:Lcom/android/tools/r8/internal/iB;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 2

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Ud;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 2
    invoke-static {}, Lcom/android/tools/r8/internal/iB;->e()Lcom/android/tools/r8/internal/fB;

    move-result-object p1

    .line 3
    sget-object v0, Lcom/android/tools/r8/internal/u6;->c:Lcom/android/tools/r8/internal/l6;

    const-class v1, Lcom/android/tools/r8/internal/c2;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 4
    sget-object v0, Lcom/android/tools/r8/internal/u6;->d:Lcom/android/tools/r8/internal/m6;

    const-class v1, Lcom/android/tools/r8/internal/lo0;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 5
    sget-object v0, Lcom/android/tools/r8/internal/u6;->e:Lcom/android/tools/r8/internal/n6;

    const-class v1, Lcom/android/tools/r8/internal/tW;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 6
    sget-object v0, Lcom/android/tools/r8/internal/u6;->f:Lcom/android/tools/r8/internal/o6;

    const-class v1, Lcom/android/tools/r8/internal/gs;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 7
    sget-object v0, Lcom/android/tools/r8/internal/u6;->g:Lcom/android/tools/r8/internal/u6;

    const-class v1, Lcom/android/tools/r8/internal/Fc0;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 8
    sget-object v0, Lcom/android/tools/r8/internal/u6;->h:Lcom/android/tools/r8/internal/p6;

    const-class v1, Lcom/android/tools/r8/internal/r2;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 9
    sget-object v0, Lcom/android/tools/r8/internal/u6;->i:Lcom/android/tools/r8/internal/q6;

    const-class v1, Lcom/android/tools/r8/internal/B20;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 10
    sget-object v0, Lcom/android/tools/r8/internal/u6;->j:Lcom/android/tools/r8/internal/r6;

    const-class v1, Lcom/android/tools/r8/internal/bv0;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 11
    sget-object v0, Lcom/android/tools/r8/internal/u6;->k:Lcom/android/tools/r8/internal/s6;

    const-class v1, Lcom/android/tools/r8/internal/nj0;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 12
    sget-object v0, Lcom/android/tools/r8/internal/u6;->l:Lcom/android/tools/r8/internal/t6;

    const-class v1, Lcom/android/tools/r8/internal/rj0;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 13
    sget-object v0, Lcom/android/tools/r8/internal/u6;->m:Lcom/android/tools/r8/internal/k6;

    const-class v1, Lcom/android/tools/r8/internal/kt0;

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    .line 14
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/fB;->b()Lcom/android/tools/r8/internal/iB;

    move-result-object p1

    .line 15
    iput-object p1, p0, Lcom/android/tools/r8/internal/v6;->e:Lcom/android/tools/r8/internal/iB;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;
    .locals 9

    .line 31
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->P()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->n()Lcom/android/tools/r8/internal/Ng;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->A1()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 32
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->n()Lcom/android/tools/r8/internal/Ng;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->H()Lcom/android/tools/r8/internal/Sg;

    move-result-object v0

    goto :goto_0

    :cond_0
    move-object v0, v1

    :goto_0
    if-eqz v0, :cond_1

    return-object v0

    .line 33
    :cond_1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->i()Z

    move-result v0

    if-eqz v0, :cond_c

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->k()Lcom/android/tools/r8/internal/f40;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/f40;->h0()Ljava/util/List;

    move-result-object v0

    const/4 v2, 0x0

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    .line 34
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->P()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->n()Lcom/android/tools/r8/internal/Ng;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/rD;->A1()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 35
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->n()Lcom/android/tools/r8/internal/Ng;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->H()Lcom/android/tools/r8/internal/Sg;

    move-result-object v0

    goto :goto_1

    :cond_2
    move-object v0, v1

    :goto_1
    if-eqz v0, :cond_c

    .line 36
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->k()Lcom/android/tools/r8/internal/f40;

    move-result-object p0

    .line 37
    new-instance v0, Lcom/android/tools/r8/internal/Vu0;

    const/4 v2, 0x2

    invoke-direct {v0, v2}, Lcom/android/tools/r8/internal/Vu0;-><init>(I)V

    .line 38
    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Object;)Z

    move-object p0, v1

    .line 39
    :cond_3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Vu0;->b()Z

    move-result v2

    if-eqz v2, :cond_b

    .line 40
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Vu0;->d()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/f40;

    .line 41
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/f40;->h0()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_4
    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/vt0;

    .line 42
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/vt0;->P()Z

    move-result v4

    if-eqz v4, :cond_5

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/vt0;->n()Lcom/android/tools/r8/internal/Ng;

    move-result-object v4

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->A1()Z

    move-result v4

    if-eqz v4, :cond_5

    .line 43
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/vt0;->n()Lcom/android/tools/r8/internal/Ng;

    move-result-object v4

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->H()Lcom/android/tools/r8/internal/Sg;

    move-result-object v4

    goto :goto_3

    :cond_5
    move-object v4, v1

    :goto_3
    if-eqz v4, :cond_9

    if-nez p0, :cond_6

    move-object p0, v4

    goto :goto_2

    .line 44
    :cond_6
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/Sg;->P2()J

    move-result-wide v5

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Sg;->P2()J

    move-result-wide v7

    cmp-long v3, v5, v7

    if-nez v3, :cond_8

    .line 45
    sget-boolean v3, Lcom/android/tools/r8/internal/v6;->f:Z

    if-nez v3, :cond_4

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->a()Lcom/android/tools/r8/internal/sr0;

    move-result-object v3

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->a()Lcom/android/tools/r8/internal/sr0;

    move-result-object v4

    if-ne v3, v4, :cond_7

    goto :goto_2

    :cond_7
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_8
    return-object v1

    .line 50
    :cond_9
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/vt0;->i()Z

    move-result v4

    if-eqz v4, :cond_a

    .line 51
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/vt0;->k()Lcom/android/tools/r8/internal/f40;

    move-result-object v3

    invoke-virtual {v0, v3}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Object;)Z

    goto :goto_2

    :cond_a
    return-object v1

    :cond_b
    return-object p0

    :cond_c
    return-object v1
.end method

.method public static a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/u6;)Lcom/android/tools/r8/internal/j6;
    .locals 2

    .line 5
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    instance-of v0, v0, Lcom/android/tools/r8/internal/dH;

    if-eqz v0, :cond_0

    .line 7
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    :goto_0
    const/4 v1, 0x0

    .line 8
    invoke-interface {p0, v0, v1}, Lcom/android/tools/r8/internal/xt0;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/j0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object p0

    .line 9
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 10
    instance-of v0, v0, Lcom/android/tools/r8/internal/dH;

    if-eqz v0, :cond_1

    .line 11
    sget-object v0, Lcom/android/tools/r8/internal/UZ;->e:Lcom/android/tools/r8/internal/UZ;

    goto :goto_1

    :cond_1
    sget-object v0, Lcom/android/tools/r8/internal/UZ;->f:Lcom/android/tools/r8/internal/UZ;

    .line 12
    :goto_1
    invoke-virtual {p3, v0, p0, p1, p2}, Lcom/android/tools/r8/internal/u6;->a(Lcom/android/tools/r8/internal/UZ;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/j6;

    move-result-object p0

    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/internal/j6;Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/Sg;Ljava/lang/Integer;Lcom/android/tools/r8/internal/vt0;Ljava/lang/Integer;Lcom/android/tools/r8/internal/vt0;)Z
    .locals 6

    .line 52
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 53
    instance-of v0, v0, Lcom/android/tools/r8/internal/dH;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 54
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Sg;->N2()I

    move-result p2

    goto :goto_1

    .line 56
    :cond_0
    sget-boolean v0, Lcom/android/tools/r8/internal/v6;->f:Z

    if-nez v0, :cond_2

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 57
    instance-of v0, v0, Lcom/android/tools/r8/internal/MS;

    if-eqz v0, :cond_1

    goto :goto_0

    .line 58
    :cond_1
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 59
    :cond_2
    :goto_0
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Sg;->O2()J

    move-result-wide v2

    long-to-int p2, v2

    int-to-long v4, p2

    cmp-long v0, v4, v2

    if-eqz v0, :cond_3

    return v1

    :cond_3
    :goto_1
    const/4 v0, 0x1

    if-eqz p3, :cond_4

    .line 65
    invoke-virtual {p3}, Ljava/lang/Integer;->intValue()I

    move-result p3

    if-ne p3, p2, :cond_4

    .line 66
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object p0

    invoke-virtual {p0, p4}, Lcom/android/tools/r8/internal/vt0;->f(Lcom/android/tools/r8/internal/vt0;)V

    .line 67
    invoke-interface {p1}, Lcom/android/tools/r8/internal/uD;->remove()V

    return v0

    :cond_4
    if-eqz p5, :cond_5

    .line 70
    invoke-virtual {p5}, Ljava/lang/Integer;->intValue()I

    move-result p3

    if-ne p3, p2, :cond_5

    .line 71
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object p0

    invoke-virtual {p0, p6}, Lcom/android/tools/r8/internal/vt0;->f(Lcom/android/tools/r8/internal/vt0;)V

    .line 72
    invoke-interface {p1}, Lcom/android/tools/r8/internal/uD;->remove()V

    return v0

    :cond_5
    return v1
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/vt0;ZLcom/android/tools/r8/internal/aA;)V
    .locals 8

    move-object v0, p2

    .line 13
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 14
    instance-of v1, v1, Lcom/android/tools/r8/internal/dH;

    if-eqz v1, :cond_0

    .line 15
    invoke-virtual {p5}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    goto :goto_0

    :cond_0
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    :goto_0
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v1

    .line 17
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 18
    instance-of v1, v1, Lcom/android/tools/r8/internal/dH;

    if-eqz v1, :cond_1

    .line 19
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/Sg;->N2()I

    move-result v1

    invoke-virtual {p5}, Lcom/android/tools/r8/internal/Sg;->N2()I

    move-result v2

    invoke-virtual {p2, v1, v2}, Lcom/android/tools/r8/internal/u6;->d(II)I

    move-result v0

    int-to-long v0, v0

    goto :goto_1

    .line 20
    :cond_1
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/Sg;->O2()J

    move-result-wide v1

    invoke-virtual {p5}, Lcom/android/tools/r8/internal/Sg;->O2()J

    move-result-wide v3

    invoke-virtual {p2, v1, v2, v3, v4}, Lcom/android/tools/r8/internal/u6;->a(JJ)J

    move-result-wide v0

    :goto_1
    move-wide v5, v0

    .line 21
    invoke-interface {p1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    move-object v0, p0

    .line 22
    iget-object v1, v0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    .line 24
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v4

    invoke-virtual {p4}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v7

    move-object v2, p1

    move-object/from16 v3, p8

    .line 25
    invoke-interface/range {v2 .. v7}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/utils/w;JLcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    .line 27
    invoke-interface {p1}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    if-eqz p7, :cond_2

    move-object v2, v1

    goto :goto_2

    :cond_2
    move-object v2, p6

    :goto_2
    move-object v3, p3

    if-eqz p7, :cond_3

    move-object v1, p6

    :cond_3
    move-object/from16 v4, p8

    .line 28
    invoke-static {v4, v2, v1, p3}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/u6;)Lcom/android/tools/r8/internal/j6;

    move-result-object v1

    const/4 v2, 0x0

    move-object v3, p1

    .line 29
    invoke-interface {p1, v1, v2}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/rD;Ljava/util/Set;)V

    .line 30
    invoke-interface {p1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;)Z
    .locals 0

    .line 1
    iget-object p2, p0, Lcom/android/tools/r8/internal/Ud;->c:Lcom/android/tools/r8/utils/w;

    iget-object p2, p2, Lcom/android/tools/r8/utils/w;->A1:Lcom/android/tools/r8/utils/w$q;

    iget-boolean p2, p2, Lcom/android/tools/r8/utils/w$q;->p:Z

    if-eqz p2, :cond_0

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object p2

    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/Ud;->a(Lcom/android/tools/r8/graph/D5;)Z

    move-result p2

    if-nez p2, :cond_0

    .line 3
    iget-object p1, p1, Lcom/android/tools/r8/internal/aA;->i:Lcom/android/tools/r8/internal/hA;

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/hA;->a()Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final b(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/Xd;
    .locals 19

    move-object/from16 v9, p0

    move-object/from16 v10, p1

    .line 2
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->r()Lcom/android/tools/r8/internal/cA;

    move-result-object v11

    const/4 v13, 0x0

    move v14, v13

    .line 3
    :goto_0
    invoke-interface {v11}, Ljava/util/ListIterator;->hasNext()Z

    move-result v0

    const/4 v7, 0x0

    if-eqz v0, :cond_32

    .line 4
    invoke-interface {v11}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/rD;

    .line 5
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->u1()Z

    move-result v1

    if-eqz v1, :cond_31

    .line 6
    instance-of v1, v0, Lcom/android/tools/r8/internal/Td;

    if-nez v1, :cond_31

    .line 7
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->A()Lcom/android/tools/r8/internal/j6;

    move-result-object v8

    .line 8
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->K2()Lcom/android/tools/r8/internal/UZ;

    move-result-object v0

    sget-object v1, Lcom/android/tools/r8/internal/UZ;->e:Lcom/android/tools/r8/internal/UZ;

    if-eq v0, v1, :cond_0

    .line 9
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->K2()Lcom/android/tools/r8/internal/UZ;

    move-result-object v0

    sget-object v1, Lcom/android/tools/r8/internal/UZ;->f:Lcom/android/tools/r8/internal/UZ;

    if-ne v0, v1, :cond_31

    .line 10
    :cond_0
    iget-object v0, v9, Lcom/android/tools/r8/internal/v6;->e:Lcom/android/tools/r8/internal/iB;

    invoke-virtual {v8}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object v15, v0

    check-cast v15, Lcom/android/tools/r8/internal/u6;

    .line 11
    sget-boolean v16, Lcom/android/tools/r8/internal/v6;->f:Z

    if-nez v16, :cond_2

    if-eqz v15, :cond_1

    goto :goto_1

    :cond_1
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 12
    :cond_2
    :goto_1
    iget-object v0, v8, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 13
    invoke-virtual {v0, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    .line 14
    invoke-static {v0}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 16
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 17
    invoke-virtual {v0, v7}, Lcom/android/tools/r8/internal/vt0;->b(Ljava/util/Set;)Z

    move-result v0

    .line 18
    invoke-virtual {v15, v0}, Lcom/android/tools/r8/internal/u6;->b(Z)Ljava/lang/Integer;

    move-result-object v3

    .line 19
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v4

    .line 20
    invoke-virtual {v15, v0}, Lcom/android/tools/r8/internal/u6;->a(Z)Ljava/lang/Integer;

    move-result-object v5

    .line 21
    iget-object v0, v8, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v0, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    move-object v6, v0

    check-cast v6, Lcom/android/tools/r8/internal/vt0;

    move-object v0, v8

    move-object v1, v11

    .line 22
    invoke-static/range {v0 .. v6}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/j6;Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/Sg;Ljava/lang/Integer;Lcom/android/tools/r8/internal/vt0;Ljava/lang/Integer;Lcom/android/tools/r8/internal/vt0;)Z

    move-result v0

    if-eqz v0, :cond_3

    goto/16 :goto_3

    .line 33
    :cond_3
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-static {v0}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v2

    if-eqz v2, :cond_4

    .line 35
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 36
    invoke-virtual {v0, v7}, Lcom/android/tools/r8/internal/vt0;->b(Ljava/util/Set;)Z

    move-result v0

    .line 37
    invoke-virtual {v15, v0}, Lcom/android/tools/r8/internal/u6;->d(Z)Ljava/lang/Integer;

    move-result-object v3

    .line 38
    iget-object v1, v8, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v1, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    move-object v4, v1

    check-cast v4, Lcom/android/tools/r8/internal/vt0;

    .line 39
    invoke-virtual {v15, v0}, Lcom/android/tools/r8/internal/u6;->c(Z)Ljava/lang/Integer;

    move-result-object v5

    .line 40
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v6

    move-object v0, v8

    move-object v1, v11

    .line 41
    invoke-static/range {v0 .. v6}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/j6;Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/Sg;Ljava/lang/Integer;Lcom/android/tools/r8/internal/vt0;Ljava/lang/Integer;Lcom/android/tools/r8/internal/vt0;)Z

    move-result v0

    if-eqz v0, :cond_4

    goto :goto_3

    .line 42
    :cond_4
    iget-object v0, v8, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v0, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    .line 43
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    const-wide/16 v2, 0x0

    if-ne v0, v1, :cond_9

    .line 44
    instance-of v0, v8, Lcom/android/tools/r8/internal/bv0;

    if-nez v0, :cond_7

    .line 45
    instance-of v0, v8, Lcom/android/tools/r8/internal/lo0;

    if-eqz v0, :cond_5

    goto :goto_2

    .line 46
    :cond_5
    instance-of v0, v8, Lcom/android/tools/r8/internal/r2;

    if-nez v0, :cond_6

    .line 47
    instance-of v0, v8, Lcom/android/tools/r8/internal/B20;

    if-eqz v0, :cond_8

    .line 48
    :cond_6
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 49
    iget-object v1, v8, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v1, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/vt0;

    .line 50
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/vt0;->f(Lcom/android/tools/r8/internal/vt0;)V

    .line 51
    invoke-interface {v11}, Lcom/android/tools/r8/internal/uD;->remove()V

    goto :goto_3

    .line 52
    :cond_7
    :goto_2
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    invoke-virtual {v10, v2, v3, v0}, Lcom/android/tools/r8/internal/aA;->a(JLcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v0

    .line 53
    invoke-interface {v11, v0, v7}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/rD;Ljava/util/Set;)V

    :cond_8
    :goto_3
    const/4 v14, 0x1

    goto/16 :goto_0

    .line 54
    :cond_9
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->z()Z

    move-result v0

    if-eqz v0, :cond_a

    goto/16 :goto_f

    .line 55
    :cond_a
    iget-object v0, v8, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v0, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    .line 56
    invoke-static {v0}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v0

    .line 57
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    invoke-static {v1}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v1

    if-eqz v0, :cond_b

    if-nez v1, :cond_c

    :cond_b
    if-nez v0, :cond_1b

    if-nez v1, :cond_1b

    .line 58
    :cond_c
    instance-of v0, v8, Lcom/android/tools/r8/internal/r2;

    if-nez v0, :cond_d

    .line 59
    instance-of v0, v8, Lcom/android/tools/r8/internal/B20;

    if-nez v0, :cond_d

    goto/16 :goto_f

    .line 60
    :cond_d
    iget-object v0, v8, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v0, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    .line 61
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->i()Z

    move-result v0

    if-nez v0, :cond_30

    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->i()Z

    move-result v0

    if-eqz v0, :cond_e

    goto/16 :goto_f

    .line 62
    :cond_e
    iget-object v0, v8, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v0, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    .line 63
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->r()Lcom/android/tools/r8/internal/rD;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->l0()Lcom/android/tools/r8/internal/oS;

    move-result-object v0

    .line 64
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->r()Lcom/android/tools/r8/internal/rD;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->l0()Lcom/android/tools/r8/internal/oS;

    move-result-object v1

    if-eqz v0, :cond_30

    if-eqz v1, :cond_30

    .line 67
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    if-ne v2, v3, :cond_30

    .line 68
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->K2()Lcom/android/tools/r8/internal/UZ;

    move-result-object v2

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/j6;->K2()Lcom/android/tools/r8/internal/UZ;

    move-result-object v3

    if-eq v2, v3, :cond_f

    goto/16 :goto_f

    .line 69
    :cond_f
    instance-of v2, v0, Lcom/android/tools/r8/internal/r2;

    if-nez v2, :cond_16

    .line 70
    instance-of v2, v0, Lcom/android/tools/r8/internal/B20;

    if-eqz v2, :cond_10

    goto/16 :goto_4

    .line 71
    :cond_10
    instance-of v2, v0, Lcom/android/tools/r8/internal/nj0;

    if-nez v2, :cond_11

    .line 72
    instance-of v2, v0, Lcom/android/tools/r8/internal/rj0;

    if-nez v2, :cond_11

    .line 73
    instance-of v2, v0, Lcom/android/tools/r8/internal/kt0;

    if-eqz v2, :cond_30

    .line 74
    :cond_11
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    invoke-static {v2}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v2

    if-eqz v2, :cond_13

    .line 77
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    invoke-static {v3}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v3

    if-nez v3, :cond_12

    goto/16 :goto_f

    .line 81
    :cond_12
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Sg;->P2()J

    move-result-wide v3

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Sg;->P2()J

    move-result-wide v5

    cmp-long v2, v3, v5

    if-eqz v2, :cond_14

    goto/16 :goto_f

    .line 86
    :cond_13
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    if-eq v2, v3, :cond_14

    goto/16 :goto_f

    .line 93
    :cond_14
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v2

    iget-object v3, v9, Lcom/android/tools/r8/internal/v6;->e:Lcom/android/tools/r8/internal/iB;

    .line 95
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/u6;

    .line 96
    iget-object v4, v0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v4, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/vt0;

    .line 97
    iget-object v1, v1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 98
    invoke-virtual {v1, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/vt0;

    .line 99
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v6

    .line 100
    invoke-static {v4}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v5

    if-eqz v5, :cond_15

    .line 102
    invoke-static {v1}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v8

    if-eqz v8, :cond_15

    const/4 v7, 0x1

    move-object/from16 v0, p0

    move-object v1, v11

    move-object v2, v15

    move-object v4, v5

    move-object v5, v8

    move-object/from16 v8, p1

    .line 104
    invoke-virtual/range {v0 .. v8}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/vt0;ZLcom/android/tools/r8/internal/aA;)V

    goto/16 :goto_e

    .line 109
    :cond_15
    invoke-static {v10, v4, v1, v15}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/u6;)Lcom/android/tools/r8/internal/j6;

    move-result-object v0

    .line 110
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/rD;->b(Lcom/android/tools/r8/internal/B40;)V

    .line 111
    invoke-interface {v11}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 112
    invoke-interface {v11, v0}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    .line 113
    invoke-interface {v11}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    .line 114
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 115
    invoke-static {v10, v0, v6, v3}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/u6;)Lcom/android/tools/r8/internal/j6;

    move-result-object v0

    .line 116
    invoke-interface {v11, v0, v7}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/rD;Ljava/util/Set;)V

    .line 117
    invoke-interface {v11}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 118
    invoke-interface {v11}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    goto/16 :goto_e

    .line 119
    :cond_16
    :goto_4
    iget-object v2, v0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 120
    invoke-virtual {v2, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    .line 121
    iget-object v3, v1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 122
    invoke-virtual {v3, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/vt0;

    if-ne v2, v3, :cond_17

    .line 123
    iget-object v2, v0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v2, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    .line 124
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    .line 125
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    :goto_5
    move-object v6, v2

    goto :goto_6

    .line 126
    :cond_17
    iget-object v2, v0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v2, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    .line 127
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    if-ne v2, v3, :cond_18

    .line 128
    iget-object v2, v0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v2, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    .line 129
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    .line 130
    iget-object v1, v1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v1, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/vt0;

    goto :goto_5

    .line 131
    :cond_18
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    .line 132
    iget-object v3, v1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v3, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/vt0;

    if-ne v2, v3, :cond_19

    .line 133
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    .line 134
    iget-object v3, v0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v3, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/vt0;

    .line 135
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    goto :goto_5

    .line 136
    :cond_19
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    if-ne v2, v3, :cond_30

    .line 137
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    .line 138
    iget-object v3, v0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v3, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/vt0;

    .line 139
    iget-object v1, v1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v1, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/vt0;

    goto :goto_5

    .line 140
    :goto_6
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v2

    iget-object v4, v9, Lcom/android/tools/r8/internal/v6;->e:Lcom/android/tools/r8/internal/iB;

    .line 142
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-interface {v4, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object v4, v0

    check-cast v4, Lcom/android/tools/r8/internal/u6;

    .line 143
    invoke-static {v3}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v5

    if-eqz v5, :cond_1a

    .line 145
    invoke-static {v1}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v8

    if-eqz v8, :cond_1a

    const/4 v7, 0x1

    move-object/from16 v0, p0

    move-object v1, v11

    move-object v2, v15

    move-object v3, v4

    move-object v4, v5

    move-object v5, v8

    move-object/from16 v8, p1

    .line 147
    invoke-virtual/range {v0 .. v8}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/vt0;ZLcom/android/tools/r8/internal/aA;)V

    goto/16 :goto_e

    .line 152
    :cond_1a
    invoke-static {v10, v3, v1, v15}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/u6;)Lcom/android/tools/r8/internal/j6;

    move-result-object v0

    .line 153
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/rD;->b(Lcom/android/tools/r8/internal/B40;)V

    .line 154
    invoke-interface {v11}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 155
    invoke-interface {v11, v0}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    .line 156
    invoke-interface {v11}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    .line 157
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 158
    invoke-static {v10, v0, v6, v4}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/u6;)Lcom/android/tools/r8/internal/j6;

    move-result-object v0

    .line 159
    invoke-interface {v11, v0, v7}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/rD;Ljava/util/Set;)V

    .line 160
    invoke-interface {v11}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 161
    invoke-interface {v11}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    goto/16 :goto_e

    :cond_1b
    if-nez v0, :cond_1c

    .line 162
    iget-object v4, v8, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v4, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/vt0;

    goto :goto_7

    .line 163
    :cond_1c
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v4

    .line 164
    :goto_7
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/vt0;->i()Z

    move-result v5

    if-nez v5, :cond_30

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/vt0;->r()Lcom/android/tools/r8/internal/rD;

    move-result-object v5

    invoke-virtual {v5}, Lcom/android/tools/r8/internal/rD;->u1()Z

    move-result v5

    if-nez v5, :cond_1d

    goto/16 :goto_f

    .line 167
    :cond_1d
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/vt0;->r()Lcom/android/tools/r8/internal/rD;

    move-result-object v4

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->A()Lcom/android/tools/r8/internal/j6;

    move-result-object v4

    .line 168
    iget-object v5, v4, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 169
    invoke-virtual {v5, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/internal/vt0;

    .line 170
    invoke-static {v5}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v5

    .line 171
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v6

    invoke-static {v6}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v6

    if-eqz v5, :cond_1e

    if-nez v6, :cond_30

    :cond_1e
    if-nez v5, :cond_1f

    if-nez v6, :cond_1f

    goto/16 :goto_f

    :cond_1f
    if-nez v0, :cond_20

    move-object/from16 v17, v1

    goto :goto_8

    :cond_20
    move-object/from16 v17, v0

    :goto_8
    if-nez v5, :cond_21

    move-object/from16 v18, v6

    goto :goto_9

    :cond_21
    move-object/from16 v18, v5

    :goto_9
    if-nez v5, :cond_22

    .line 172
    iget-object v0, v4, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v0, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    goto :goto_a

    .line 173
    :cond_22
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/j6;->P2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 175
    :goto_a
    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v12

    invoke-virtual {v8}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v13

    if-ne v12, v13, :cond_2d

    .line 176
    iget-boolean v4, v15, Lcom/android/tools/r8/internal/u6;->b:Z

    if-eqz v4, :cond_25

    if-nez v16, :cond_24

    .line 178
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/j6;->L2()Z

    move-result v1

    if-eqz v1, :cond_23

    goto :goto_b

    :cond_23
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_24
    :goto_b
    const/4 v7, 0x1

    move-object v12, v0

    move-object/from16 v0, p0

    move-object v1, v11

    move-object v2, v15

    move-object v3, v15

    move-object/from16 v4, v17

    move-object/from16 v5, v18

    move-object v6, v12

    move-object/from16 v8, p1

    .line 179
    invoke-virtual/range {v0 .. v8}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/vt0;ZLcom/android/tools/r8/internal/aA;)V

    goto/16 :goto_e

    :cond_25
    move-object v12, v0

    .line 182
    invoke-virtual {v15}, Lcom/android/tools/r8/internal/u6;->a()Z

    move-result v0

    if-eqz v0, :cond_2b

    if-eqz v1, :cond_30

    if-eqz v6, :cond_30

    if-nez v16, :cond_27

    .line 183
    instance-of v0, v8, Lcom/android/tools/r8/internal/nj0;

    if-nez v0, :cond_27

    .line 184
    instance-of v0, v8, Lcom/android/tools/r8/internal/rj0;

    if-nez v0, :cond_27

    .line 185
    instance-of v0, v8, Lcom/android/tools/r8/internal/kt0;

    if-eqz v0, :cond_26

    goto :goto_c

    .line 186
    :cond_26
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 187
    :cond_27
    :goto_c
    invoke-virtual {v12}, Lcom/android/tools/r8/internal/vt0;->d0()Lcom/android/tools/r8/internal/It0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/It0;->b()Z

    move-result v0

    if-eqz v0, :cond_28

    const/16 v0, 0x3f

    goto :goto_d

    :cond_28
    const/16 v0, 0x1f

    .line 188
    :goto_d
    invoke-virtual {v6}, Lcom/android/tools/r8/internal/Sg;->N2()I

    move-result v4

    and-int/2addr v4, v0

    .line 189
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Sg;->N2()I

    move-result v1

    and-int/2addr v1, v0

    add-int/2addr v4, v1

    if-le v4, v0, :cond_2a

    .line 190
    instance-of v0, v8, Lcom/android/tools/r8/internal/rj0;

    if-eqz v0, :cond_29

    goto/16 :goto_f

    .line 191
    :cond_29
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v0

    invoke-virtual {v10, v2, v3, v0}, Lcom/android/tools/r8/internal/aA;->a(JLcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/Sg;

    move-result-object v0

    .line 192
    invoke-interface {v11, v0, v7}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/rD;Ljava/util/Set;)V

    goto :goto_e

    .line 193
    :cond_2a
    invoke-interface {v11}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 194
    iget-object v0, v9, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    .line 196
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v2

    int-to-long v3, v4

    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->k()Lcom/android/tools/r8/internal/dH;

    move-result-object v5

    move-object v0, v11

    move-object/from16 v1, p1

    .line 197
    invoke-interface/range {v0 .. v5}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/utils/w;JLcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 199
    invoke-interface {v11}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    .line 200
    invoke-static {v10, v12, v0, v15}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/u6;)Lcom/android/tools/r8/internal/j6;

    move-result-object v0

    .line 201
    invoke-interface {v11, v0, v7}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/rD;Ljava/util/Set;)V

    .line 202
    invoke-interface {v11}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    :goto_e
    const/4 v0, 0x1

    goto/16 :goto_10

    .line 203
    :cond_2b
    instance-of v0, v8, Lcom/android/tools/r8/internal/lo0;

    if-eqz v0, :cond_30

    if-eqz v1, :cond_30

    if-nez v6, :cond_2c

    .line 204
    sget-object v3, Lcom/android/tools/r8/internal/u6;->d:Lcom/android/tools/r8/internal/m6;

    const/4 v7, 0x1

    move-object/from16 v0, p0

    move-object v1, v11

    move-object v2, v3

    move-object/from16 v4, v18

    move-object/from16 v5, v17

    move-object v6, v12

    move-object/from16 v8, p1

    invoke-virtual/range {v0 .. v8}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/vt0;ZLcom/android/tools/r8/internal/aA;)V

    goto :goto_e

    .line 207
    :cond_2c
    sget-object v2, Lcom/android/tools/r8/internal/u6;->c:Lcom/android/tools/r8/internal/l6;

    sget-object v3, Lcom/android/tools/r8/internal/u6;->d:Lcom/android/tools/r8/internal/m6;

    const/4 v7, 0x0

    move-object/from16 v0, p0

    move-object v1, v11

    move-object/from16 v4, v17

    move-object/from16 v5, v18

    move-object v6, v12

    move-object/from16 v8, p1

    invoke-virtual/range {v0 .. v8}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/vt0;ZLcom/android/tools/r8/internal/aA;)V

    goto :goto_e

    :cond_2d
    move-object v12, v0

    .line 208
    instance-of v0, v8, Lcom/android/tools/r8/internal/lo0;

    if-eqz v0, :cond_2e

    .line 209
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->i1()Z

    move-result v0

    if-eqz v0, :cond_2e

    if-eqz v1, :cond_2e

    .line 213
    sget-object v2, Lcom/android/tools/r8/internal/u6;->d:Lcom/android/tools/r8/internal/m6;

    sget-object v3, Lcom/android/tools/r8/internal/u6;->c:Lcom/android/tools/r8/internal/l6;

    const/4 v7, 0x1

    move-object/from16 v0, p0

    move-object v1, v11

    move-object/from16 v4, v18

    move-object/from16 v5, v17

    move-object v6, v12

    move-object/from16 v8, p1

    invoke-virtual/range {v0 .. v8}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/vt0;ZLcom/android/tools/r8/internal/aA;)V

    goto :goto_e

    .line 215
    :cond_2e
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->i1()Z

    move-result v0

    if-eqz v0, :cond_30

    .line 216
    instance-of v0, v4, Lcom/android/tools/r8/internal/lo0;

    if-eqz v0, :cond_30

    if-nez v5, :cond_2f

    .line 217
    sget-object v3, Lcom/android/tools/r8/internal/u6;->d:Lcom/android/tools/r8/internal/m6;

    const/4 v7, 0x0

    move-object/from16 v0, p0

    move-object v1, v11

    move-object v2, v3

    move-object/from16 v4, v18

    move-object/from16 v5, v17

    move-object v6, v12

    move-object/from16 v8, p1

    invoke-virtual/range {v0 .. v8}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/vt0;ZLcom/android/tools/r8/internal/aA;)V

    goto :goto_e

    .line 220
    :cond_2f
    sget-object v2, Lcom/android/tools/r8/internal/u6;->c:Lcom/android/tools/r8/internal/l6;

    sget-object v3, Lcom/android/tools/r8/internal/u6;->d:Lcom/android/tools/r8/internal/m6;

    const/4 v7, 0x1

    move-object/from16 v0, p0

    move-object v1, v11

    move-object/from16 v4, v17

    move-object/from16 v5, v18

    move-object v6, v12

    move-object/from16 v8, p1

    invoke-virtual/range {v0 .. v8}, Lcom/android/tools/r8/internal/v6;->a(Lcom/android/tools/r8/internal/cA;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/u6;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/Sg;Lcom/android/tools/r8/internal/vt0;ZLcom/android/tools/r8/internal/aA;)V

    goto/16 :goto_e

    :cond_30
    :goto_f
    const/4 v0, 0x0

    :goto_10
    or-int/2addr v14, v0

    :cond_31
    const/4 v13, 0x0

    goto/16 :goto_0

    :cond_32
    if-eqz v14, :cond_33

    .line 221
    invoke-virtual {v10, v7, v7}, Lcom/android/tools/r8/internal/aA;->a(Lcom/android/tools/r8/internal/Vz;Lcom/android/tools/r8/ir/optimize/a;)Z

    .line 222
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->A()V

    .line 224
    :cond_33
    invoke-static {v14}, Lcom/android/tools/r8/internal/Xd;->a(Z)Lcom/android/tools/r8/internal/Wd;

    move-result-object v0

    return-object v0
.end method

.method public final b()Ljava/lang/String;
    .locals 1

    const-string v0, "BinopRewriter"

    return-object v0
.end method
