.class public final synthetic Lcom/android/tools/r8/internal/W40$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Ljava/util/function/Predicate;


# direct methods
.method public synthetic constructor <init>(Ljava/util/function/Predicate;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/W40$$ExternalSyntheticLambda1;->f$0:Ljava/util/function/Predicate;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/W40$$ExternalSyntheticLambda1;->f$0:Ljava/util/function/Predicate;

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/W40;->a(<PERSON>java/util/function/Predicate;Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
