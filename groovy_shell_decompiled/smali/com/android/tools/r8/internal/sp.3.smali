.class public Lcom/android/tools/r8/internal/sp;
.super Lcom/android/tools/r8/internal/op;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(IILcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/op;-><init>(IILcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Zo;Lcom/android/tools/r8/graph/t5;)V
    .locals 0

    .line 1
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/t5;->b()[Lcom/android/tools/r8/graph/x2;

    move-result-object p3

    invoke-direct {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/op;-><init>(ILcom/android/tools/r8/internal/Zo;[Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method


# virtual methods
.method public final J()Lcom/android/tools/r8/internal/JI;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/JI;->f:Lcom/android/tools/r8/internal/JI;

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/graph/b6;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/op;->k()Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/b6;->e(Lcom/android/tools/r8/graph/x2;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/Vz;)V
    .locals 6

    .line 2
    sget-object v1, Lcom/android/tools/r8/internal/JI;->f:Lcom/android/tools/r8/internal/JI;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/op;->k()Lcom/android/tools/r8/graph/x2;

    move-result-object v2

    iget-short v4, p0, Lcom/android/tools/r8/internal/yo;->f:S

    iget-char v5, p0, Lcom/android/tools/r8/internal/yo;->g:C

    const/4 v3, 0x0

    move-object v0, p1

    invoke-virtual/range {v0 .. v5}, Lcom/android/tools/r8/internal/Vz;->a(Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/F2;II)V

    return-void
.end method

.method public final h()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final l()Ljava/lang/String;
    .locals 1

    const-string v0, "InvokeStaticRange"

    return-object v0
.end method

.method public final s()I
    .locals 1

    const/16 v0, 0x77

    return v0
.end method

.method public final v()Ljava/lang/String;
    .locals 1

    const-string v0, "invoke-static/range"

    return-object v0
.end method
