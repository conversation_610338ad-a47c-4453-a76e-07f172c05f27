.class public final synthetic Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/U40;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/vz;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/vz;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/vz;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/vz;

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/vz;->a(Lcom/android/tools/r8/graph/E2;)Z

    move-result p1

    return p1
.end method
