.class public final Lcom/android/tools/r8/internal/w8;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/r;


# static fields
.field public static final synthetic c:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/internal/E5;

.field public final b:Lcom/android/tools/r8/graph/J2;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/E5;Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/w8;->a:Lcom/android/tools/r8/internal/E5;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/w8;->b:Lcom/android/tools/r8/graph/J2;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/UB;Lcom/android/tools/r8/internal/WB;)I
    .locals 2

    .line 3
    invoke-interface {p1}, Lcom/android/tools/r8/internal/UB;->t()Lcom/android/tools/r8/internal/w8;

    move-result-object p1

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/w8;->a:Lcom/android/tools/r8/internal/E5;

    iget-object v1, p1, Lcom/android/tools/r8/internal/w8;->a:Lcom/android/tools/r8/internal/E5;

    if-eq v0, v1, :cond_2

    .line 5
    invoke-interface {v0, v1, p2}, Lcom/android/tools/r8/internal/UB;->b(Lcom/android/tools/r8/internal/UB;Lcom/android/tools/r8/internal/WB;)I

    move-result p1

    .line 6
    sget-boolean p2, Lcom/android/tools/r8/internal/w8;->c:Z

    if-nez p2, :cond_1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return p1

    .line 9
    :cond_2
    iget-object p2, p0, Lcom/android/tools/r8/internal/w8;->b:Lcom/android/tools/r8/graph/J2;

    iget-object p1, p1, Lcom/android/tools/r8/internal/w8;->b:Lcom/android/tools/r8/graph/J2;

    invoke-interface {p2, p1}, Lcom/android/tools/r8/internal/ho0;->a(Lcom/android/tools/r8/internal/ho0;)I

    move-result p1

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Qw;Lcom/android/tools/r8/internal/bg;)Lcom/android/tools/r8/internal/Gt0;
    .locals 0

    .line 1
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/Gt0;->g()Lcom/android/tools/r8/internal/ag;

    move-result-object p2

    iget-object p3, p0, Lcom/android/tools/r8/internal/w8;->b:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p2, p1, p3}, Lcom/android/tools/r8/internal/ag;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/Gt0;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/E5;)Z
    .locals 1

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/w8;->c:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/w8;->a:Lcom/android/tools/r8/internal/E5;

    invoke-virtual {p1, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    return p1
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/w8;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/w8;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/w8;->a:Lcom/android/tools/r8/internal/E5;

    iget-object v3, p1, Lcom/android/tools/r8/internal/w8;->a:Lcom/android/tools/r8/internal/E5;

    invoke-virtual {v1, v3}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/android/tools/r8/internal/w8;->b:Lcom/android/tools/r8/graph/J2;

    iget-object p1, p1, Lcom/android/tools/r8/internal/w8;->b:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public final getKind()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final hashCode()I
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w8;->a:Lcom/android/tools/r8/internal/E5;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w8;->b:Lcom/android/tools/r8/graph/J2;

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object v0, v2, v3

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-static {v2}, Ljava/util/Objects;->hash([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final j()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final t()Lcom/android/tools/r8/internal/w8;
    .locals 0

    return-object p0
.end method

.method public final toString()Ljava/lang/String;
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w8;->a:Lcom/android/tools/r8/internal/E5;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w8;->b:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/J2;->G0()Ljava/lang/String;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Cast("

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ", "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final u()Ljava/lang/Iterable;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w8;->a:Lcom/android/tools/r8/internal/E5;

    invoke-static {v0}, Ljava/util/Collections;->singleton(Ljava/lang/Object;)Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method
