.class public final enum Lcom/android/tools/r8/internal/Sn0$a;
.super Ljava/lang/Enum;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/internal/Sn0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/android/tools/r8/internal/Sn0$a;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum b:Lcom/android/tools/r8/internal/Sn0$a;

.field public static final enum c:Lcom/android/tools/r8/internal/Sn0$a;

.field public static final enum d:Lcom/android/tools/r8/internal/Sn0$a;

.field public static final enum e:Lcom/android/tools/r8/internal/Sn0$a;

.field public static final synthetic f:[Lcom/android/tools/r8/internal/Sn0$a;


# direct methods
.method static constructor <clinit>()V
    .locals 9

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Sn0$a;

    const/4 v1, 0x0

    const-string v2, "PARENS"

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/Sn0$a;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/android/tools/r8/internal/Sn0$a;->b:Lcom/android/tools/r8/internal/Sn0$a;

    .line 2
    new-instance v2, Lcom/android/tools/r8/internal/Sn0$a;

    const/4 v3, 0x1

    const-string v4, "SQUARE"

    invoke-direct {v2, v3, v4}, Lcom/android/tools/r8/internal/Sn0$a;-><init>(ILjava/lang/String;)V

    sput-object v2, Lcom/android/tools/r8/internal/Sn0$a;->c:Lcom/android/tools/r8/internal/Sn0$a;

    .line 3
    new-instance v4, Lcom/android/tools/r8/internal/Sn0$a;

    const/4 v5, 0x2

    const-string v6, "TUBORG"

    invoke-direct {v4, v5, v6}, Lcom/android/tools/r8/internal/Sn0$a;-><init>(ILjava/lang/String;)V

    sput-object v4, Lcom/android/tools/r8/internal/Sn0$a;->d:Lcom/android/tools/r8/internal/Sn0$a;

    .line 4
    new-instance v6, Lcom/android/tools/r8/internal/Sn0$a;

    const/4 v7, 0x3

    const-string v8, "NONE"

    invoke-direct {v6, v7, v8}, Lcom/android/tools/r8/internal/Sn0$a;-><init>(ILjava/lang/String;)V

    sput-object v6, Lcom/android/tools/r8/internal/Sn0$a;->e:Lcom/android/tools/r8/internal/Sn0$a;

    const/4 v8, 0x4

    new-array v8, v8, [Lcom/android/tools/r8/internal/Sn0$a;

    aput-object v0, v8, v1

    aput-object v2, v8, v3

    aput-object v4, v8, v5

    aput-object v6, v8, v7

    .line 5
    sput-object v8, Lcom/android/tools/r8/internal/Sn0$a;->f:[Lcom/android/tools/r8/internal/Sn0$a;

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p2, p1}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/String;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Rn0;->a:[I

    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    aget v0, v0, v1

    const/4 v1, 0x1

    if-eq v0, v1, :cond_3

    const/4 v1, 0x2

    if-eq v0, v1, :cond_2

    const/4 v1, 0x3

    if-eq v0, v1, :cond_1

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    const-string v0, ""

    return-object v0

    .line 6
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/Os0;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Invalid brace type: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_1
    const-string v0, "{"

    return-object v0

    :cond_2
    const-string v0, "["

    return-object v0

    :cond_3
    const-string v0, "("

    return-object v0
.end method

.method public final b()Ljava/lang/String;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Rn0;->a:[I

    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    aget v0, v0, v1

    const/4 v1, 0x1

    if-eq v0, v1, :cond_3

    const/4 v1, 0x2

    if-eq v0, v1, :cond_2

    const/4 v1, 0x3

    if-eq v0, v1, :cond_1

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    const-string v0, ""

    return-object v0

    .line 6
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/Os0;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Invalid brace type: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_1
    const-string v0, "}"

    return-object v0

    :cond_2
    const-string v0, "]"

    return-object v0

    :cond_3
    const-string v0, ")"

    return-object v0
.end method
