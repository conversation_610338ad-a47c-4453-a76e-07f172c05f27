.class public abstract Lcom/android/tools/r8/internal/V90;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic a:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/D0;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/U90;
    .locals 9

    .line 77
    iget-object v0, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/O2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/O2;->G0()Lcom/android/tools/r8/graph/O2$l;

    move-result-object v0

    .line 78
    iget-object v1, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    const/4 v2, 0x1

    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/O2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/O2;->F0()Lcom/android/tools/r8/graph/O2$k;

    move-result-object v1

    .line 79
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/O2$k;->h1()Lcom/android/tools/r8/graph/Z3;

    move-result-object v1

    move-object v5, v1

    check-cast v5, Lcom/android/tools/r8/graph/I2;

    .line 80
    iget-object v1, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x2

    sub-int/2addr v1, v2

    new-array v6, v1, [Lcom/android/tools/r8/graph/l1;

    .line 81
    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v2, v1, :cond_0

    .line 82
    iget-object v1, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/O2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/O2;->B0()Lcom/android/tools/r8/graph/U2;

    move-result-object v1

    add-int/lit8 v3, v2, -0x2

    .line 83
    iget-object v1, v1, Lcom/android/tools/r8/graph/U2;->d:Lcom/android/tools/r8/graph/Z3;

    check-cast v1, Lcom/android/tools/r8/graph/z2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/z2;->f:Lcom/android/tools/r8/graph/s2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G2;->n0()Lcom/android/tools/r8/graph/l1;

    move-result-object v1

    aput-object v1, v6, v3

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 85
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/O2$l;->h1()Lcom/android/tools/r8/graph/Z3;

    move-result-object v0

    move-object v8, v0

    check-cast v8, Lcom/android/tools/r8/graph/J2;

    .line 89
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    invoke-static {}, Lcom/android/tools/r8/internal/Fy;->g()Lcom/android/tools/r8/internal/Fy;

    move-result-object v1

    invoke-virtual {v0, v1, v8}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/y;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p1

    .line 90
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/b1;->f0()Lcom/android/tools/r8/graph/E2;

    move-result-object v7

    .line 91
    new-instance p1, Lcom/android/tools/r8/internal/U90;

    iget-object v3, p0, Lcom/android/tools/r8/graph/D0;->e:Lcom/android/tools/r8/graph/I2;

    iget-object v4, p0, Lcom/android/tools/r8/graph/D0;->f:Lcom/android/tools/r8/graph/F2;

    move-object v2, p1

    invoke-direct/range {v2 .. v8}, Lcom/android/tools/r8/internal/U90;-><init>(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;[Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/J2;)V

    return-object p1
.end method

.method public static a(Lcom/android/tools/r8/graph/D0;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z
    .locals 7

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/graph/D0;->g:Lcom/android/tools/r8/graph/z2;

    .line 4
    iget-object v2, v1, Lcom/android/tools/r8/graph/z2;->e:Lcom/android/tools/r8/graph/y2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y2;->f()Z

    move-result v2

    const/4 v3, 0x0

    if-nez v2, :cond_0

    return v3

    .line 7
    :cond_0
    iget-object v2, v1, Lcom/android/tools/r8/graph/z2;->f:Lcom/android/tools/r8/graph/s2;

    iget-object v4, v0, Lcom/android/tools/r8/graph/B1;->Y5:Lcom/android/tools/r8/graph/f2;

    iget-object v4, v4, Lcom/android/tools/r8/graph/f2;->a:Lcom/android/tools/r8/graph/x2;

    if-eq v2, v4, :cond_1

    return v3

    .line 13
    :cond_1
    iget-boolean v1, v1, Lcom/android/tools/r8/graph/z2;->g:Z

    if-eqz v1, :cond_3

    .line 14
    sget-boolean p0, Lcom/android/tools/r8/internal/V90;->a:Z

    if-eqz p0, :cond_2

    return v3

    :cond_2
    new-instance p0, Ljava/lang/AssertionError;

    const-string p1, "Invoke-dynamic invoking non interface method ObjectMethods#bootstrap as an interface method."

    invoke-direct {p0, p1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p0

    .line 21
    :cond_3
    iget-object v1, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x2

    if-ge v1, v2, :cond_5

    .line 22
    sget-boolean p0, Lcom/android/tools/r8/internal/V90;->a:Z

    if-eqz p0, :cond_4

    return v3

    :cond_4
    new-instance p0, Ljava/lang/AssertionError;

    const-string p1, "Invoke-dynamic invoking method ObjectMethods#bootstrap with less than 2 parameters."

    invoke-direct {p0, p1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p0

    .line 26
    :cond_5
    iget-object v1, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/O2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/O2;->G0()Lcom/android/tools/r8/graph/O2$l;

    move-result-object v1

    if-nez v1, :cond_7

    .line 28
    sget-boolean p0, Lcom/android/tools/r8/internal/V90;->a:Z

    if-eqz p0, :cond_6

    return v3

    :cond_6
    new-instance p0, Ljava/lang/AssertionError;

    const-string p1, "Invoke-dynamic invoking method ObjectMethods#bootstrap with an invalid type."

    invoke-direct {p0, p1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p0

    .line 31
    :cond_7
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/O2$l;->h1()Lcom/android/tools/r8/graph/Z3;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/J2;

    .line 34
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v4

    .line 35
    iget-object v5, p1, Lcom/android/tools/r8/graph/y;->g:Lcom/android/tools/r8/internal/Fy;

    .line 36
    invoke-virtual {v4, v5, v1}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object v4

    .line 37
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object p2

    invoke-interface {p1, p2, v4}, Lcom/android/tools/r8/graph/d1;->a(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p1

    if-eqz p1, :cond_1b

    .line 38
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->y1()Z

    move-result p2

    if-eqz p2, :cond_8

    goto/16 :goto_7

    .line 41
    :cond_8
    iget-object p2, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    const/4 v4, 0x1

    invoke-interface {p2, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/O2;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/O2;->F0()Lcom/android/tools/r8/graph/O2$k;

    move-result-object p2

    if-nez p2, :cond_a

    .line 43
    sget-boolean p0, Lcom/android/tools/r8/internal/V90;->a:Z

    if-eqz p0, :cond_9

    return v3

    :cond_9
    new-instance p0, Ljava/lang/AssertionError;

    const-string p1, "Invoke-dynamic invoking method ObjectMethods#bootstrap with invalid field names."

    invoke-direct {p0, p1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p0

    .line 47
    :cond_a
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/O2$k;->h1()Lcom/android/tools/r8/graph/Z3;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/I2;

    .line 48
    sget-boolean v5, Lcom/android/tools/r8/internal/V90;->a:Z

    if-nez v5, :cond_c

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/String;->isEmpty()Z

    move-result v6

    if-nez v6, :cond_c

    .line 49
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v6, ";"

    invoke-virtual {p2, v6}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p2

    array-length p2, p2

    iget-object v6, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    sub-int/2addr v6, v2

    if-ne p2, v6, :cond_b

    goto :goto_0

    .line 50
    :cond_b
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_c
    :goto_0
    if-nez v5, :cond_e

    .line 52
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->m1()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    iget-object p2, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    sub-int/2addr p2, v2

    if-gt p1, p2, :cond_d

    goto :goto_1

    :cond_d
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_e
    :goto_1
    move p1, v2

    .line 53
    :goto_2
    iget-object p2, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    if-ge p1, p2, :cond_12

    .line 54
    iget-object p2, p0, Lcom/android/tools/r8/graph/D0;->h:Ljava/util/List;

    invoke-interface {p2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/O2;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/O2;->B0()Lcom/android/tools/r8/graph/U2;

    move-result-object p2

    if-eqz p2, :cond_10

    .line 55
    iget-object p2, p2, Lcom/android/tools/r8/graph/U2;->d:Lcom/android/tools/r8/graph/Z3;

    check-cast p2, Lcom/android/tools/r8/graph/z2;

    iget-object v5, p2, Lcom/android/tools/r8/graph/z2;->e:Lcom/android/tools/r8/graph/y2;

    .line 56
    sget-object v6, Lcom/android/tools/r8/graph/y2;->f:Lcom/android/tools/r8/graph/y2;

    if-ne v5, v6, :cond_10

    .line 57
    iget-object p2, p2, Lcom/android/tools/r8/graph/z2;->f:Lcom/android/tools/r8/graph/s2;

    .line 58
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G2;->r0()Z

    move-result p2

    if-nez p2, :cond_f

    goto :goto_3

    :cond_f
    add-int/lit8 p1, p1, 0x1

    goto :goto_2

    .line 59
    :cond_10
    :goto_3
    sget-boolean p0, Lcom/android/tools/r8/internal/V90;->a:Z

    if-eqz p0, :cond_11

    return v3

    :cond_11
    new-instance p0, Ljava/lang/AssertionError;

    const-string p1, "Invoke-dynamic invoking method ObjectMethods#bootstrap with invalid getters."

    invoke-direct {p0, p1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p0

    .line 65
    :cond_12
    iget-object p1, p0, Lcom/android/tools/r8/graph/D0;->e:Lcom/android/tools/r8/graph/I2;

    iget-object p2, v0, Lcom/android/tools/r8/graph/B1;->l0:Lcom/android/tools/r8/graph/I2;

    if-ne p1, p2, :cond_15

    .line 66
    sget-boolean p1, Lcom/android/tools/r8/internal/V90;->a:Z

    if-nez p1, :cond_14

    iget-object p0, p0, Lcom/android/tools/r8/graph/D0;->f:Lcom/android/tools/r8/graph/F2;

    iget-object p1, v0, Lcom/android/tools/r8/graph/B1;->Z1:Lcom/android/tools/r8/graph/J2;

    new-array p2, v4, [Lcom/android/tools/r8/graph/J2;

    aput-object v1, p2, v3

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object p1

    if-ne p0, p1, :cond_13

    goto :goto_4

    :cond_13
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_14
    :goto_4
    return v4

    .line 69
    :cond_15
    iget-object p2, v0, Lcom/android/tools/r8/graph/B1;->X:Lcom/android/tools/r8/graph/I2;

    if-ne p1, p2, :cond_18

    .line 70
    sget-boolean p1, Lcom/android/tools/r8/internal/V90;->a:Z

    if-nez p1, :cond_17

    iget-object p0, p0, Lcom/android/tools/r8/graph/D0;->f:Lcom/android/tools/r8/graph/F2;

    iget-object p1, v0, Lcom/android/tools/r8/graph/B1;->C1:Lcom/android/tools/r8/graph/J2;

    new-array p2, v4, [Lcom/android/tools/r8/graph/J2;

    aput-object v1, p2, v3

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object p1

    if-ne p0, p1, :cond_16

    goto :goto_5

    :cond_16
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_17
    :goto_5
    return v4

    .line 73
    :cond_18
    iget-object p2, v0, Lcom/android/tools/r8/graph/B1;->W:Lcom/android/tools/r8/graph/I2;

    if-ne p1, p2, :cond_1b

    .line 74
    sget-boolean p1, Lcom/android/tools/r8/internal/V90;->a:Z

    if-nez p1, :cond_1a

    iget-object p0, p0, Lcom/android/tools/r8/graph/D0;->f:Lcom/android/tools/r8/graph/F2;

    iget-object p1, v0, Lcom/android/tools/r8/graph/B1;->x1:Lcom/android/tools/r8/graph/J2;

    iget-object p2, v0, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    new-array v2, v2, [Lcom/android/tools/r8/graph/J2;

    aput-object v1, v2, v3

    aput-object p2, v2, v4

    .line 75
    invoke-virtual {v0, p1, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object p1

    if-ne p0, p1, :cond_19

    goto :goto_6

    .line 76
    :cond_19
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_1a
    :goto_6
    return v4

    :cond_1b
    :goto_7
    return v3
.end method
