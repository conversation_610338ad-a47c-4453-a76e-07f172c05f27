.class public Lcom/android/tools/r8/internal/sq;
.super Lcom/android/tools/r8/internal/io;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/android/tools/r8/internal/io<",
        "Lcom/android/tools/r8/graph/J2;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(ILcom/android/tools/r8/graph/J2;)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/io;-><init>(ILcom/android/tools/r8/graph/Z3;)V

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Zo;Lcom/android/tools/r8/graph/t5;)V
    .locals 0

    .line 1
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/t5;->c()[Lcom/android/tools/r8/graph/J2;

    move-result-object p3

    invoke-direct {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/io;-><init>(ILcom/android/tools/r8/internal/j8;[Lcom/android/tools/r8/graph/Z3;)V

    return-void
.end method

.method public static synthetic b(Lcom/android/tools/r8/internal/io;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/android/tools/r8/internal/io;->g:Lcom/android/tools/r8/graph/Z3;

    check-cast p0, Lcom/android/tools/r8/graph/J2;

    return-object p0
.end method


# virtual methods
.method public J()Lcom/android/tools/r8/graph/J2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/io;->g:Lcom/android/tools/r8/graph/Z3;

    check-cast v0, Lcom/android/tools/r8/graph/J2;

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/graph/b6;)V
    .locals 1

    .line 8
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sq;->J()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/b6;->d(Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/s5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/jQ;Ljava/nio/ShortBuffer;)V
    .locals 0

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sq;->J()Lcom/android/tools/r8/graph/J2;

    move-result-object p2

    invoke-virtual {p3, p4, p2}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p2

    .line 5
    iget-short p3, p0, Lcom/android/tools/r8/internal/io;->f:S

    const/16 p4, 0x22

    .line 6
    invoke-static {p3, p4, p6}, Lcom/android/tools/r8/internal/Yo;->a(IILjava/nio/ShortBuffer;)V

    .line 7
    invoke-static {p2, p6, p1}, Lcom/android/tools/r8/internal/Yo;->a(Lcom/android/tools/r8/graph/Z3;Ljava/nio/ShortBuffer;Lcom/android/tools/r8/graph/s5;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/dex/M;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/jQ;)V
    .locals 0

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object p4

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sq;->J()Lcom/android/tools/r8/graph/J2;

    move-result-object p5

    invoke-virtual {p4, p2, p5}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p2

    .line 3
    invoke-virtual {p2, p1, p3}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/dex/M;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/Vz;)V
    .locals 4

    .line 9
    iget-short v0, p0, Lcom/android/tools/r8/internal/io;->f:S

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/sq;->J()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 10
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->b()Lcom/android/tools/r8/internal/qZ;

    move-result-object v2

    iget-object v3, p1, Lcom/android/tools/r8/internal/Vz;->p:Lcom/android/tools/r8/graph/y;

    invoke-static {v1, v2, v3}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v2

    const/4 v3, 0x2

    .line 11
    invoke-virtual {p1, v0, v3, v2}, Lcom/android/tools/r8/internal/Vz;->a(IILcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 12
    new-instance v2, Lcom/android/tools/r8/internal/HX;

    invoke-direct {v2, v1, v0}, Lcom/android/tools/r8/internal/HX;-><init>(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/vt0;)V

    .line 13
    iget-object v0, p1, Lcom/android/tools/r8/internal/Vz;->u:Lcom/android/tools/r8/internal/Nk0;

    invoke-interface {v0}, Lcom/android/tools/r8/internal/Nk0;->g()Lcom/android/tools/r8/internal/B40;

    move-result-object v0

    invoke-virtual {p1, v0, v2}, Lcom/android/tools/r8/internal/Vz;->a(Lcom/android/tools/r8/internal/B40;Lcom/android/tools/r8/internal/rD;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/ko0;)V
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/sq$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/sq$$ExternalSyntheticLambda0;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/ko0;->e(Ljava/util/function/Function;)Lcom/android/tools/r8/internal/ko0;

    return-void
.end method

.method public final h()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final l()Ljava/lang/String;
    .locals 1

    const-string v0, "NewInstance"

    return-object v0
.end method

.method public final s()I
    .locals 1

    const/16 v0, 0x22

    return v0
.end method

.method public final v()Ljava/lang/String;
    .locals 1

    const-string v0, "new-instance"

    return-object v0
.end method
