.class public abstract Lcom/android/tools/r8/internal/w0;
.super Ljava/util/AbstractCollection;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final b:Ljava/lang/Object;

.field public c:Ljava/util/Collection;

.field public final d:Lcom/android/tools/r8/internal/w0;

.field public final e:Ljava/util/Collection;

.field public final synthetic f:Lcom/android/tools/r8/internal/z0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/z0;Ljava/lang/Object;Ljava/util/List;Lcom/android/tools/r8/internal/w0;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    invoke-direct {p0}, Ljava/util/AbstractCollection;-><init>()V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/w0;->b:Ljava/lang/Object;

    .line 3
    iput-object p3, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    .line 4
    iput-object p4, p0, Lcom/android/tools/r8/internal/w0;->d:Lcom/android/tools/r8/internal/w0;

    if-nez p4, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    .line 5
    :cond_0
    iget-object p1, p4, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    .line 6
    :goto_0
    iput-object p1, p0, Lcom/android/tools/r8/internal/w0;->e:Ljava/util/Collection;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->d:Lcom/android/tools/r8/internal/w0;

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/w0;->a()V

    goto :goto_0

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    .line 5
    iget-object v0, v0, Lcom/android/tools/r8/internal/z0;->f:Ljava/util/HashMap;

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->b:Ljava/lang/Object;

    iget-object v2, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_0
    return-void
.end method

.method public final add(Ljava/lang/Object;)Z
    .locals 3

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v1, p1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    .line 6
    iget v2, v1, Lcom/android/tools/r8/internal/z0;->g:I

    add-int/lit8 v2, v2, 0x1

    iput v2, v1, Lcom/android/tools/r8/internal/z0;->g:I

    if-eqz v0, :cond_0

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->a()V

    :cond_0
    return p1
.end method

.method public final addAll(Ljava/util/Collection;)Z
    .locals 4

    .line 1
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    return p1

    .line 2
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->size()I

    move-result v0

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v1, p1}, Ljava/util/Collection;->addAll(Ljava/util/Collection;)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v1}, Ljava/util/Collection;->size()I

    move-result v1

    .line 7
    iget-object v2, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    sub-int/2addr v1, v0

    .line 8
    iget v3, v2, Lcom/android/tools/r8/internal/z0;->g:I

    add-int/2addr v3, v1

    iput v3, v2, Lcom/android/tools/r8/internal/z0;->g:I

    if-nez v0, :cond_1

    .line 9
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->a()V

    :cond_1
    return p1
.end method

.method public final b()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->d:Lcom/android/tools/r8/internal/w0;

    if-eqz v0, :cond_1

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->d:Lcom/android/tools/r8/internal/w0;

    .line 4
    iget-object v0, v0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->e:Ljava/util/Collection;

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 6
    :cond_0
    new-instance v0, Ljava/util/ConcurrentModificationException;

    invoke-direct {v0}, Ljava/util/ConcurrentModificationException;-><init>()V

    throw v0

    .line 8
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    .line 10
    iget-object v0, v0, Lcom/android/tools/r8/internal/z0;->f:Ljava/util/HashMap;

    .line 11
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->b:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Collection;

    if-eqz v0, :cond_2

    .line 13
    iput-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    :cond_2
    :goto_0
    return-void
.end method

.method public final c()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->d:Lcom/android/tools/r8/internal/w0;

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/w0;->c()V

    goto :goto_0

    .line 3
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    .line 5
    iget-object v0, v0, Lcom/android/tools/r8/internal/z0;->f:Ljava/util/HashMap;

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->b:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    :goto_0
    return-void
.end method

.method public final clear()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->size()I

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 3
    :cond_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v1}, Ljava/util/Collection;->clear()V

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    .line 5
    iget v2, v1, Lcom/android/tools/r8/internal/z0;->g:I

    sub-int/2addr v2, v0

    iput v2, v1, Lcom/android/tools/r8/internal/z0;->g:I

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->c()V

    return-void
.end method

.method public final contains(Ljava/lang/Object;)Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0, p1}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public final containsAll(Ljava/util/Collection;)Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0, p1}, Ljava/util/Collection;->containsAll(Ljava/util/Collection;)Z

    move-result p1

    return p1
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 1

    if-ne p1, p0, :cond_0

    const/4 p1, 0x1

    return p1

    .line 1
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0, p1}, Ljava/util/Collection;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public final hashCode()I
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->hashCode()I

    move-result v0

    return v0
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/v0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/v0;-><init>(Lcom/android/tools/r8/internal/w0;)V

    return-object v0
.end method

.method public final remove(Ljava/lang/Object;)Z
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0, p1}, Ljava/util/Collection;->remove(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    .line 5
    iget v1, v0, Lcom/android/tools/r8/internal/z0;->g:I

    add-int/lit8 v1, v1, -0x1

    iput v1, v0, Lcom/android/tools/r8/internal/z0;->g:I

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->c()V

    :cond_0
    return p1
.end method

.method public final removeAll(Ljava/util/Collection;)Z
    .locals 3

    .line 1
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    return p1

    .line 2
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->size()I

    move-result v0

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v1, p1}, Ljava/util/Collection;->removeAll(Ljava/util/Collection;)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v1}, Ljava/util/Collection;->size()I

    move-result v1

    .line 7
    iget-object v2, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    sub-int/2addr v1, v0

    .line 8
    iget v0, v2, Lcom/android/tools/r8/internal/z0;->g:I

    add-int/2addr v0, v1

    iput v0, v2, Lcom/android/tools/r8/internal/z0;->g:I

    .line 9
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->c()V

    :cond_1
    return p1
.end method

.method public final retainAll(Ljava/util/Collection;)Z
    .locals 3

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->size()I

    move-result v0

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v1, p1}, Ljava/util/Collection;->retainAll(Ljava/util/Collection;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v1}, Ljava/util/Collection;->size()I

    move-result v1

    .line 7
    iget-object v2, p0, Lcom/android/tools/r8/internal/w0;->f:Lcom/android/tools/r8/internal/z0;

    sub-int/2addr v1, v0

    .line 8
    iget v0, v2, Lcom/android/tools/r8/internal/z0;->g:I

    add-int/2addr v0, v1

    iput v0, v2, Lcom/android/tools/r8/internal/z0;->g:I

    .line 9
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->c()V

    :cond_0
    return p1
.end method

.method public final size()I
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->size()I

    move-result v0

    return v0
.end method

.method public final spliterator()Ljava/util/Spliterator;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->spliterator()Ljava/util/Spliterator;

    move-result-object v0

    return-object v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w0;->b()V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w0;->c:Ljava/util/Collection;

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
