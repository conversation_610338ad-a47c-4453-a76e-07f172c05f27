.class public final Lcom/android/tools/r8/internal/Ua;
.super Lcom/android/tools/r8/internal/Za;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic d:Z


# instance fields
.field public final c:Lcom/android/tools/r8/internal/Va;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/bb;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/Ua;->d:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/Za;Lcom/android/tools/r8/internal/Ya;)V
    .locals 2

    .line 1
    iget v0, p1, Lcom/android/tools/r8/internal/Za;->b:I

    add-int/lit8 v0, v0, 0x1

    invoke-direct {p0, v0, p1}, Lcom/android/tools/r8/internal/Za;-><init>(ILcom/android/tools/r8/internal/Za;)V

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Za;->d()I

    move-result p1

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/Va;

    const v1, 0x186a0

    add-int/2addr p1, v1

    invoke-direct {v0, p1, p2}, Lcom/android/tools/r8/internal/Va;-><init>(ILcom/android/tools/r8/internal/Ya;)V

    .line 4
    iput-object v0, p0, Lcom/android/tools/r8/internal/Ua;->c:Lcom/android/tools/r8/internal/Va;

    .line 5
    sget-boolean p2, Lcom/android/tools/r8/internal/Ua;->d:Z

    if-nez p2, :cond_1

    if-gt v1, p1, :cond_0

    const p2, 0x30d40

    if-ge p1, p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Sa;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Za;->a:Lcom/android/tools/r8/internal/Za;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Za;->a(Lcom/android/tools/r8/internal/Sa;)V

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ua;->c:Lcom/android/tools/r8/internal/Va;

    .line 3
    iget v0, v0, Lcom/android/tools/r8/internal/Va;->a:I

    .line 4
    invoke-static {v0}, Lcom/android/tools/r8/internal/Va;->b(I)I

    move-result v0

    .line 5
    iget-object p1, p1, Lcom/android/tools/r8/internal/Sa;->d:[Lcom/android/tools/r8/internal/Ya;

    array-length v1, p1

    if-ge v0, v1, :cond_0

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ua;->c:Lcom/android/tools/r8/internal/Va;

    .line 7
    iget v0, v0, Lcom/android/tools/r8/internal/Va;->a:I

    .line 8
    invoke-static {v0}, Lcom/android/tools/r8/internal/Va;->b(I)I

    move-result v0

    .line 9
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ua;->c:Lcom/android/tools/r8/internal/Va;

    iget-object v1, v1, Lcom/android/tools/r8/internal/Va;->d:Lcom/android/tools/r8/internal/Ya;

    aput-object v1, p1, v0

    :cond_0
    return-void
.end method

.method public final b(I)Lcom/android/tools/r8/internal/Va;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ua;->c:Lcom/android/tools/r8/internal/Va;

    .line 2
    iget v0, v0, Lcom/android/tools/r8/internal/Va;->a:I

    .line 3
    invoke-static {v0}, Lcom/android/tools/r8/internal/Va;->b(I)I

    move-result v0

    if-ne p1, v0, :cond_0

    .line 4
    iget-object p1, p0, Lcom/android/tools/r8/internal/Ua;->c:Lcom/android/tools/r8/internal/Va;

    goto :goto_0

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Za;->a:Lcom/android/tools/r8/internal/Za;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Za;->b(I)Lcom/android/tools/r8/internal/Va;

    move-result-object p1

    :goto_0
    return-object p1
.end method

.method public final c()Lcom/android/tools/r8/internal/Va;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ua;->c:Lcom/android/tools/r8/internal/Va;

    return-object v0
.end method

.method public final d()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ua;->c:Lcom/android/tools/r8/internal/Va;

    .line 2
    iget v0, v0, Lcom/android/tools/r8/internal/Va;->a:I

    .line 3
    invoke-static {v0}, Lcom/android/tools/r8/internal/Va;->b(I)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Za;->a:Lcom/android/tools/r8/internal/Za;

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ua;->c:Lcom/android/tools/r8/internal/Va;

    iget-object v1, v1, Lcom/android/tools/r8/internal/Va;->d:Lcom/android/tools/r8/internal/Ya;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "; push("

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
