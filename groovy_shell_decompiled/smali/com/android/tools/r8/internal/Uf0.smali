.class public final Lcom/android/tools/r8/internal/Uf0;
.super Lcom/android/tools/r8/internal/uy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# static fields
.field public static final g:Lcom/android/tools/r8/internal/Uf0;

.field public static final h:Lcom/android/tools/r8/internal/Sf0;


# instance fields
.field public b:Lcom/android/tools/r8/internal/Df0;

.field public volatile c:Ljava/lang/String;

.field public d:Lcom/android/tools/r8/internal/xf0;

.field public e:Lcom/android/tools/r8/internal/Fe0;

.field public f:B


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Uf0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Uf0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Uf0;->g:Lcom/android/tools/r8/internal/Uf0;

    .line 9
    new-instance v0, Lcom/android/tools/r8/internal/Sf0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Sf0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Uf0;->h:Lcom/android/tools/r8/internal/Sf0;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 275
    invoke-direct {p0}, Lcom/android/tools/r8/internal/uy;-><init>()V

    const/4 v0, -0x1

    .line 546
    iput-byte v0, p0, Lcom/android/tools/r8/internal/Uf0;->f:B

    const-string v0, ""

    .line 547
    iput-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/Tf0;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/uy;-><init>(Lcom/android/tools/r8/internal/dy;)V

    const/4 p1, -0x1

    .line 274
    iput-byte p1, p0, Lcom/android/tools/r8/internal/Uf0;->f:B

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    .locals 5

    .line 548
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Uf0;-><init>()V

    .line 549
    invoke-static {p2}, Lcom/android/tools/r8/internal/qg;->a(Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/qs0;

    move-result-object v0

    const/4 v1, 0x0

    :cond_0
    :goto_0
    if-nez v1, :cond_9

    .line 37463
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->s()I

    move-result v2

    if-eqz v2, :cond_8

    const/16 v3, 0xa

    const/4 v4, 0x0

    if-eq v2, v3, :cond_6

    const/16 v3, 0x12

    if-eq v2, v3, :cond_5

    const/16 v3, 0x1a

    if-eq v2, v3, :cond_3

    const/16 v3, 0x22

    if-eq v2, v3, :cond_1

    .line 37514
    invoke-virtual {p0, p1, v0, p2, v2}, Lcom/android/tools/r8/internal/uy;->parseUnknownField(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/qs0;Lcom/android/tools/r8/internal/Lu;I)Z

    move-result v2

    if-nez v2, :cond_0

    goto :goto_1

    .line 37515
    :cond_1
    iget-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->e:Lcom/android/tools/r8/internal/Fe0;

    if-eqz v2, :cond_2

    .line 37516
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Fe0;->d()Lcom/android/tools/r8/internal/Ee0;

    move-result-object v4

    .line 37517
    :cond_2
    sget-object v2, Lcom/android/tools/r8/internal/Fe0;->f:Lcom/android/tools/r8/internal/De0;

    .line 37518
    invoke-virtual {p1, v2, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/Fe0;

    iput-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->e:Lcom/android/tools/r8/internal/Fe0;

    if-eqz v4, :cond_0

    .line 37520
    invoke-virtual {v4, v2}, Lcom/android/tools/r8/internal/Ee0;->a(Lcom/android/tools/r8/internal/Fe0;)Lcom/android/tools/r8/internal/Ee0;

    .line 37521
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/Ee0;->c()Lcom/android/tools/r8/internal/Fe0;

    move-result-object v2

    iput-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->e:Lcom/android/tools/r8/internal/Fe0;

    goto :goto_0

    .line 37522
    :cond_3
    iget-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->d:Lcom/android/tools/r8/internal/xf0;

    if-eqz v2, :cond_4

    .line 37523
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/xf0;->c()Lcom/android/tools/r8/internal/vf0;

    move-result-object v4

    .line 37524
    :cond_4
    sget-object v2, Lcom/android/tools/r8/internal/xf0;->k:Lcom/android/tools/r8/internal/uf0;

    .line 37525
    invoke-virtual {p1, v2, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/xf0;

    iput-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->d:Lcom/android/tools/r8/internal/xf0;

    if-eqz v4, :cond_0

    .line 37527
    invoke-virtual {v4, v2}, Lcom/android/tools/r8/internal/vf0;->a(Lcom/android/tools/r8/internal/xf0;)Lcom/android/tools/r8/internal/vf0;

    .line 37528
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/vf0;->b()Lcom/android/tools/r8/internal/xf0;

    move-result-object v2

    iput-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->d:Lcom/android/tools/r8/internal/xf0;

    goto :goto_0

    .line 37529
    :cond_5
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->r()Ljava/lang/String;

    move-result-object v2

    .line 37531
    iput-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    goto :goto_0

    .line 37532
    :cond_6
    iget-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->b:Lcom/android/tools/r8/internal/Df0;

    if-eqz v2, :cond_7

    .line 37533
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Df0;->b()Lcom/android/tools/r8/internal/Cf0;

    move-result-object v4

    .line 37534
    :cond_7
    sget-object v2, Lcom/android/tools/r8/internal/Df0;->f:Lcom/android/tools/r8/internal/Bf0;

    .line 37535
    invoke-virtual {p1, v2, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/Df0;

    iput-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->b:Lcom/android/tools/r8/internal/Df0;

    if-eqz v4, :cond_0

    .line 37537
    invoke-virtual {v4, v2}, Lcom/android/tools/r8/internal/Cf0;->a(Lcom/android/tools/r8/internal/Df0;)Lcom/android/tools/r8/internal/Cf0;

    .line 37538
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/Cf0;->b()Lcom/android/tools/r8/internal/Df0;

    move-result-object v2

    iput-object v2, p0, Lcom/android/tools/r8/internal/Uf0;->b:Lcom/android/tools/r8/internal/Df0;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto/16 :goto_0

    :cond_8
    :goto_1
    const/4 v1, 0x1

    goto/16 :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :catch_0
    move-exception p1

    .line 37587
    :try_start_1
    new-instance p2, Lcom/android/tools/r8/internal/lI;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/lI;-><init>(Ljava/io/IOException;)V

    .line 37588
    iput-object p0, p2, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 37589
    throw p2

    :catch_1
    move-exception p1

    .line 37590
    iput-object p0, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 37591
    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 37596
    :goto_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 37597
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    .line 37598
    throw p1

    .line 37599
    :cond_9
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 37600
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/Fe0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->e:Lcom/android/tools/r8/internal/Fe0;

    if-nez v0, :cond_0

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/Fe0;->e:Lcom/android/tools/r8/internal/Fe0;

    :cond_0
    return-object v0
.end method

.method public final b()Lcom/android/tools/r8/internal/xf0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->d:Lcom/android/tools/r8/internal/xf0;

    if-nez v0, :cond_0

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/xf0;->j:Lcom/android/tools/r8/internal/xf0;

    :cond_0
    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/Df0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->b:Lcom/android/tools/r8/internal/Df0;

    if-nez v0, :cond_0

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/Df0;->e:Lcom/android/tools/r8/internal/Df0;

    :cond_0
    return-object v0
.end method

.method public final d()Lcom/android/tools/r8/internal/Tf0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Uf0;->g:Lcom/android/tools/r8/internal/Uf0;

    if-ne p0, v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Tf0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Tf0;-><init>()V

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/Tf0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Tf0;-><init>()V

    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/Tf0;->a(Lcom/android/tools/r8/internal/Uf0;)Lcom/android/tools/r8/internal/Tf0;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 5

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/Uf0;

    if-nez v1, :cond_1

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/J0;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/Uf0;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->b:Lcom/android/tools/r8/internal/Df0;

    const/4 v2, 0x0

    if-eqz v1, :cond_2

    move v3, v0

    goto :goto_0

    :cond_2
    move v3, v2

    .line 6
    :goto_0
    iget-object v4, p1, Lcom/android/tools/r8/internal/Uf0;->b:Lcom/android/tools/r8/internal/Df0;

    if-eqz v4, :cond_3

    move v4, v0

    goto :goto_1

    :cond_3
    move v4, v2

    :goto_1
    if-eq v3, v4, :cond_4

    return v2

    :cond_4
    if-eqz v1, :cond_5

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->c()Lcom/android/tools/r8/internal/Df0;

    move-result-object v1

    .line 8
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Uf0;->c()Lcom/android/tools/r8/internal/Df0;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/Df0;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    return v2

    .line 9
    :cond_5
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    .line 10
    instance-of v3, v1, Ljava/lang/String;

    if-eqz v3, :cond_6

    goto :goto_2

    .line 13
    :cond_6
    check-cast v1, Lcom/android/tools/r8/internal/Z7;

    .line 15
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v1

    .line 16
    iput-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    .line 17
    :goto_2
    iget-object v3, p1, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    .line 18
    instance-of v4, v3, Ljava/lang/String;

    if-eqz v4, :cond_7

    goto :goto_3

    .line 21
    :cond_7
    check-cast v3, Lcom/android/tools/r8/internal/Z7;

    .line 23
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v3

    .line 24
    iput-object v3, p1, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    .line 25
    :goto_3
    invoke-virtual {v1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_8

    return v2

    .line 26
    :cond_8
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->d:Lcom/android/tools/r8/internal/xf0;

    if-eqz v1, :cond_9

    move v3, v0

    goto :goto_4

    :cond_9
    move v3, v2

    :goto_4
    iget-object v4, p1, Lcom/android/tools/r8/internal/Uf0;->d:Lcom/android/tools/r8/internal/xf0;

    if-eqz v4, :cond_a

    move v4, v0

    goto :goto_5

    :cond_a
    move v4, v2

    :goto_5
    if-eq v3, v4, :cond_b

    return v2

    :cond_b
    if-eqz v1, :cond_c

    .line 27
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->b()Lcom/android/tools/r8/internal/xf0;

    move-result-object v1

    .line 28
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Uf0;->b()Lcom/android/tools/r8/internal/xf0;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/xf0;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_c

    return v2

    .line 29
    :cond_c
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->e:Lcom/android/tools/r8/internal/Fe0;

    if-eqz v1, :cond_d

    move v3, v0

    goto :goto_6

    :cond_d
    move v3, v2

    :goto_6
    iget-object v4, p1, Lcom/android/tools/r8/internal/Uf0;->e:Lcom/android/tools/r8/internal/Fe0;

    if-eqz v4, :cond_e

    move v4, v0

    goto :goto_7

    :cond_e
    move v4, v2

    :goto_7
    if-eq v3, v4, :cond_f

    return v2

    :cond_f
    if-eqz v1, :cond_10

    .line 30
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->a()Lcom/android/tools/r8/internal/Fe0;

    move-result-object v1

    .line 31
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Uf0;->a()Lcom/android/tools/r8/internal/Fe0;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/Fe0;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_10

    return v2

    .line 33
    :cond_10
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/vs0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_11

    return v2

    :cond_11
    return v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/Uf0;->g:Lcom/android/tools/r8/internal/Uf0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Uf0;->g:Lcom/android/tools/r8/internal/Uf0;

    return-object v0
.end method

.method public final getSerializedSize()I
    .locals 3

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    :cond_0
    const/4 v0, 0x0

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->b:Lcom/android/tools/r8/internal/Df0;

    if-eqz v1, :cond_1

    const/4 v0, 0x1

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->c()Lcom/android/tools/r8/internal/Df0;

    move-result-object v1

    .line 8
    invoke-static {v0}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v0

    invoke-static {v1}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/AU;)I

    move-result v1

    add-int/2addr v0, v1

    .line 9
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    invoke-static {v1}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    .line 10
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    const/4 v2, 0x2

    invoke-static {v2, v1}, Lcom/android/tools/r8/internal/uy;->computeStringSize(ILjava/lang/Object;)I

    move-result v1

    add-int/2addr v0, v1

    .line 12
    :cond_2
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->d:Lcom/android/tools/r8/internal/xf0;

    if-eqz v1, :cond_3

    const/4 v1, 0x3

    .line 14
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->b()Lcom/android/tools/r8/internal/xf0;

    move-result-object v2

    .line 15
    invoke-static {v1}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v1

    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/AU;)I

    move-result v2

    add-int/2addr v2, v1

    add-int/2addr v0, v2

    .line 16
    :cond_3
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->e:Lcom/android/tools/r8/internal/Fe0;

    if-eqz v1, :cond_4

    const/4 v1, 0x4

    .line 18
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->a()Lcom/android/tools/r8/internal/Fe0;

    move-result-object v2

    .line 19
    invoke-static {v1}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v1

    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/AU;)I

    move-result v2

    add-int/2addr v2, v1

    add-int/2addr v0, v2

    .line 20
    :cond_4
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vs0;->getSerializedSize()I

    move-result v1

    add-int/2addr v1, v0

    .line 21
    iput v1, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    return v1
.end method

.method public final getUnknownFields()Lcom/android/tools/r8/internal/vs0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    return-object v0
.end method

.method public final hashCode()I
    .locals 5

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    if-eqz v0, :cond_0

    return v0

    .line 2
    :cond_0
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->s0:Lcom/android/tools/r8/internal/Ok;

    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    add-int/lit16 v0, v0, 0x30b

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->b:Lcom/android/tools/r8/internal/Df0;

    const/16 v2, 0x35

    const/16 v3, 0x25

    if-eqz v1, :cond_1

    const/4 v1, 0x1

    .line 5
    invoke-static {v0, v3, v1, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->c()Lcom/android/tools/r8/internal/Df0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Df0;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    :cond_1
    const/4 v1, 0x2

    .line 8
    invoke-static {v0, v3, v1, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    .line 9
    iget-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    .line 10
    instance-of v4, v1, Ljava/lang/String;

    if-eqz v4, :cond_2

    goto :goto_0

    .line 13
    :cond_2
    check-cast v1, Lcom/android/tools/r8/internal/Z7;

    .line 15
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v1

    .line 16
    iput-object v1, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    .line 17
    :goto_0
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    .line 18
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->d:Lcom/android/tools/r8/internal/xf0;

    if-eqz v0, :cond_3

    const/4 v0, 0x3

    .line 19
    invoke-static {v1, v3, v0, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->b()Lcom/android/tools/r8/internal/xf0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/xf0;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    .line 20
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->e:Lcom/android/tools/r8/internal/Fe0;

    if-eqz v0, :cond_4

    const/4 v0, 0x4

    .line 21
    invoke-static {v1, v3, v0, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->a()Lcom/android/tools/r8/internal/Fe0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Fe0;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    :cond_4
    mul-int/lit8 v1, v1, 0x1d

    .line 23
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vs0;->hashCode()I

    move-result v0

    add-int/2addr v0, v1

    .line 24
    iput v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    return v0
.end method

.method public final internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->t0:Lcom/android/tools/r8/internal/sy;

    .line 2
    const-class v1, Lcom/android/tools/r8/internal/Uf0;

    const-class v2, Lcom/android/tools/r8/internal/Tf0;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 2

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/internal/Uf0;->f:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    if-nez v0, :cond_1

    const/4 v0, 0x0

    return v0

    .line 5
    :cond_1
    iput-byte v1, p0, Lcom/android/tools/r8/internal/Uf0;->f:B

    return v1
.end method

.method public final newBuilderForType()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Uf0;->g:Lcom/android/tools/r8/internal/Uf0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Uf0;->d()Lcom/android/tools/r8/internal/Tf0;

    move-result-object v0

    return-object v0
.end method

.method public final newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Tf0;

    check-cast p1, Lcom/android/tools/r8/internal/ay;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/Tf0;-><init>(Lcom/android/tools/r8/internal/ay;)V

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->d()Lcom/android/tools/r8/internal/Tf0;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->d()Lcom/android/tools/r8/internal/Tf0;

    move-result-object v0

    return-object v0
.end method

.method public final writeTo(Lcom/android/tools/r8/internal/je;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->b:Lcom/android/tools/r8/internal/Df0;

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->c()Lcom/android/tools/r8/internal/Df0;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    invoke-static {v0}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->c:Ljava/lang/String;

    const/4 v1, 0x2

    invoke-static {p1, v1, v0}, Lcom/android/tools/r8/internal/uy;->writeString(Lcom/android/tools/r8/internal/je;ILjava/lang/Object;)V

    .line 7
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->d:Lcom/android/tools/r8/internal/xf0;

    if-eqz v0, :cond_2

    .line 8
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->b()Lcom/android/tools/r8/internal/xf0;

    move-result-object v0

    const/4 v1, 0x3

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    .line 10
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uf0;->e:Lcom/android/tools/r8/internal/Fe0;

    if-eqz v0, :cond_3

    .line 11
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Uf0;->a()Lcom/android/tools/r8/internal/Fe0;

    move-result-object v0

    const/4 v1, 0x4

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    .line 13
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/vs0;->writeTo(Lcom/android/tools/r8/internal/je;)V

    return-void
.end method
