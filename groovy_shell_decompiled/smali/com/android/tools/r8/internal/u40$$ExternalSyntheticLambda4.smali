.class public final synthetic Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda4;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda4;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda4;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda4;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda4;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 0

    check-cast p1, Lcom/android/tools/r8/internal/s40;

    invoke-static {p1}, Lcom/android/tools/r8/internal/u40;->a(Lcom/android/tools/r8/internal/s40;)Z

    move-result p1

    return p1
.end method
