.class public final Lcom/android/tools/r8/internal/t0;
.super Lcom/android/tools/r8/internal/IT;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic c:Lcom/android/tools/r8/internal/z0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/z0;Ljava/util/HashMap;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/t0;->c:Lcom/android/tools/r8/internal/z0;

    .line 2
    invoke-direct {p0, p2}, Lcom/android/tools/r8/internal/IT;-><init>(Ljava/util/AbstractMap;)V

    return-void
.end method


# virtual methods
.method public final clear()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/t0;->iterator()Ljava/util/Iterator;

    move-result-object v0

    .line 2
    :goto_0
    move-object v1, v0

    check-cast v1, Lcom/android/tools/r8/internal/s0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/s0;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    .line 3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/s0;->next()Ljava/lang/Object;

    .line 4
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/s0;->remove()V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public final containsAll(Ljava/util/Collection;)Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/IT;->b:Ljava/util/AbstractMap;

    .line 2
    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Set;->containsAll(Ljava/util/Collection;)Z

    move-result p1

    return p1
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 1

    if-eq p0, p1, :cond_1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/IT;->b:Ljava/util/AbstractMap;

    .line 2
    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Set;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public final hashCode()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/IT;->b:Ljava/util/AbstractMap;

    .line 2
    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->hashCode()I

    move-result v0

    return v0
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/IT;->b:Ljava/util/AbstractMap;

    .line 2
    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    .line 3
    new-instance v1, Lcom/android/tools/r8/internal/s0;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/internal/s0;-><init>(Lcom/android/tools/r8/internal/t0;Ljava/util/Iterator;)V

    return-object v1
.end method

.method public final remove(Ljava/lang/Object;)Z
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/IT;->b:Ljava/util/AbstractMap;

    .line 2
    invoke-interface {v0, p1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Collection;

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    .line 4
    invoke-interface {p1}, Ljava/util/Collection;->size()I

    move-result v1

    .line 5
    invoke-interface {p1}, Ljava/util/Collection;->clear()V

    .line 6
    iget-object p1, p0, Lcom/android/tools/r8/internal/t0;->c:Lcom/android/tools/r8/internal/z0;

    .line 7
    iget v2, p1, Lcom/android/tools/r8/internal/z0;->g:I

    sub-int/2addr v2, v1

    iput v2, p1, Lcom/android/tools/r8/internal/z0;->g:I

    goto :goto_0

    :cond_0
    move v1, v0

    :goto_0
    if-lez v1, :cond_1

    const/4 v0, 0x1

    :cond_1
    return v0
.end method

.method public final spliterator()Ljava/util/Spliterator;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/IT;->b:Ljava/util/AbstractMap;

    .line 2
    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->spliterator()Ljava/util/Spliterator;

    move-result-object v0

    return-object v0
.end method
