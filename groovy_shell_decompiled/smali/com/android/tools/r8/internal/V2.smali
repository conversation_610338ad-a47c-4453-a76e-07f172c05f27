.class public final Lcom/android/tools/r8/internal/V2;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/shaking/f2;

.field public final b:Ljava/util/concurrent/ConcurrentLinkedDeque;


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Lcom/android/tools/r8/shaking/f2;

    new-instance v1, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v1}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    invoke-direct {v0, v1}, Lcom/android/tools/r8/shaking/f2;-><init>(Ljava/util/Map;)V

    .line 3
    iput-object v0, p0, Lcom/android/tools/r8/internal/V2;->a:Lcom/android/tools/r8/shaking/f2;

    .line 4
    new-instance v0, Ljava/util/concurrent/ConcurrentLinkedDeque;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentLinkedDeque;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/internal/V2;->b:Ljava/util/concurrent/ConcurrentLinkedDeque;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Y30;Lcom/android/tools/r8/shaking/M;)V
    .locals 5

    .line 1
    iget-object p1, p1, Lcom/android/tools/r8/internal/X30;->b:Ljava/util/ArrayList;

    .line 2
    invoke-virtual {p1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_4

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/z5;

    .line 3
    invoke-interface {p2}, Lcom/android/tools/r8/graph/o0;->M()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 4
    invoke-interface {p2}, Lcom/android/tools/r8/graph/o0;->Y()Lcom/android/tools/r8/graph/B5;

    move-result-object p2

    .line 5
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    .line 6
    check-cast v0, Lcom/android/tools/r8/graph/g1;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 7
    sget-boolean v1, Lcom/android/tools/r8/graph/g1;->p:Z

    if-nez v1, :cond_2

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/V10;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    goto :goto_1

    :cond_1
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 8
    :cond_2
    :goto_1
    new-instance v1, Lcom/android/tools/r8/graph/u5;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/l1;

    invoke-direct {v1, v2}, Lcom/android/tools/r8/graph/u5;-><init>(Lcom/android/tools/r8/graph/l1;)V

    .line 9
    iput-object v1, v0, Lcom/android/tools/r8/graph/g1;->i:Lcom/android/tools/r8/graph/u5;

    .line 10
    iget-object v1, v0, Lcom/android/tools/r8/graph/g1;->l:Lcom/android/tools/r8/internal/yv;

    .line 12
    invoke-interface {v1}, Lcom/android/tools/r8/internal/aU;->a()Lcom/android/tools/r8/internal/aU;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/iX;

    .line 13
    sget-object v2, Lcom/android/tools/r8/internal/M1;->a:Lcom/android/tools/r8/internal/M1;

    .line 14
    iget-object v3, v1, Lcom/android/tools/r8/internal/iX;->a:Lcom/android/tools/r8/internal/E1;

    .line 15
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result v3

    if-eqz v3, :cond_3

    .line 16
    iput-object v2, v1, Lcom/android/tools/r8/internal/iX;->a:Lcom/android/tools/r8/internal/E1;

    goto :goto_2

    .line 17
    :cond_3
    new-instance v3, Lcom/android/tools/r8/internal/H1;

    invoke-direct {v3, p0}, Lcom/android/tools/r8/internal/H1;-><init>(Lcom/android/tools/r8/graph/y;)V

    iget-object v4, v1, Lcom/android/tools/r8/internal/iX;->a:Lcom/android/tools/r8/internal/E1;

    .line 18
    invoke-virtual {v3, v4, v2, p2}, Lcom/android/tools/r8/internal/H1;->a(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/graph/B5;)Lcom/android/tools/r8/internal/E1;

    move-result-object p2

    .line 19
    iput-object p2, v1, Lcom/android/tools/r8/internal/iX;->a:Lcom/android/tools/r8/internal/E1;

    .line 20
    :goto_2
    iput-object v1, v0, Lcom/android/tools/r8/graph/g1;->l:Lcom/android/tools/r8/internal/yv;

    goto :goto_0

    :cond_4
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/X2;
    .locals 4

    .line 23
    iget-object v0, p0, Lcom/android/tools/r8/internal/V2;->a:Lcom/android/tools/r8/shaking/f2;

    .line 24
    iget-object v0, v0, Lcom/android/tools/r8/shaking/f2;->a:Ljava/util/Map;

    .line 25
    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 26
    iget-object v0, p0, Lcom/android/tools/r8/internal/V2;->b:Ljava/util/concurrent/ConcurrentLinkedDeque;

    invoke-virtual {v0}, Ljava/util/concurrent/ConcurrentLinkedDeque;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 27
    sget-object p1, Lcom/android/tools/r8/internal/W2;->a:Lcom/android/tools/r8/internal/W2;

    return-object p1

    .line 29
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/Y2;

    iget-object v1, p0, Lcom/android/tools/r8/internal/V2;->a:Lcom/android/tools/r8/shaking/f2;

    new-instance v2, Ljava/util/ArrayList;

    iget-object v3, p0, Lcom/android/tools/r8/internal/V2;->b:Ljava/util/concurrent/ConcurrentLinkedDeque;

    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    new-instance v3, Lcom/android/tools/r8/internal/V2$$ExternalSyntheticLambda0;

    invoke-direct {v3, p1}, Lcom/android/tools/r8/internal/V2$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/y;)V

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/Y2;-><init>(Lcom/android/tools/r8/shaking/f2;Ljava/util/List;Ljava/util/function/BiConsumer;)V

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/internal/Y30;)V
    .locals 1

    .line 22
    iget-object v0, p0, Lcom/android/tools/r8/internal/V2;->b:Ljava/util/concurrent/ConcurrentLinkedDeque;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/ConcurrentLinkedDeque;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public final a(Ljava/util/function/Consumer;)V
    .locals 1

    .line 21
    iget-object v0, p0, Lcom/android/tools/r8/internal/V2;->a:Lcom/android/tools/r8/shaking/f2;

    invoke-interface {p1, v0}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    return-void
.end method
