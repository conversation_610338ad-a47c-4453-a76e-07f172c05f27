.class public final Lcom/android/tools/r8/internal/tP;
.super Lcom/android/tools/r8/internal/AP;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic j:Z


# instance fields
.field public final f:Lcom/android/tools/r8/graph/y;

.field public final g:Lcom/android/tools/r8/graph/x2;

.field public final h:Z

.field public final i:Lcom/android/tools/r8/graph/y2;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/BP;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/tP;->j:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/x2;ZLcom/android/tools/r8/graph/y2;Lcom/android/tools/r8/graph/x2;ZLcom/android/tools/r8/graph/y;)V
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/JI;->f:Lcom/android/tools/r8/internal/JI;

    invoke-direct {p0, p4, v0, p5}, Lcom/android/tools/r8/internal/AP;-><init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Z)V

    .line 2
    iput-object p6, p0, Lcom/android/tools/r8/internal/tP;->f:Lcom/android/tools/r8/graph/y;

    .line 3
    iput-object p1, p0, Lcom/android/tools/r8/internal/tP;->g:Lcom/android/tools/r8/graph/x2;

    .line 4
    iput-boolean p2, p0, Lcom/android/tools/r8/internal/tP;->h:Z

    .line 5
    iput-object p3, p0, Lcom/android/tools/r8/internal/tP;->i:Lcom/android/tools/r8/graph/y2;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/z2;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/tP;
    .locals 8

    .line 1
    new-instance v7, Lcom/android/tools/r8/internal/tP;

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/z2;->o0()Lcom/android/tools/r8/graph/x2;

    move-result-object v1

    iget-boolean v2, p0, Lcom/android/tools/r8/graph/z2;->g:Z

    iget-object v3, p0, Lcom/android/tools/r8/graph/z2;->e:Lcom/android/tools/r8/graph/y2;

    .line 6
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result v5

    move-object v0, v7

    move-object v4, p2

    move-object v6, p3

    invoke-direct/range {v0 .. v6}, Lcom/android/tools/r8/internal/tP;-><init>(Lcom/android/tools/r8/graph/x2;ZLcom/android/tools/r8/graph/y2;Lcom/android/tools/r8/graph/x2;ZLcom/android/tools/r8/graph/y;)V

    return-object v7
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Zw;Ljava/util/function/Consumer;)Lcom/android/tools/r8/graph/D5;
    .locals 9

    .line 7
    iget-object p1, p0, Lcom/android/tools/r8/internal/tP;->f:Lcom/android/tools/r8/graph/y;

    iget-object v0, p0, Lcom/android/tools/r8/internal/AP;->a:Lcom/android/tools/r8/graph/x2;

    iget-object v0, v0, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-interface {p1, v0}, Lcom/android/tools/r8/graph/d1;->b(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    .line 8
    sget-boolean v0, Lcom/android/tools/r8/internal/tP;->j:Z

    if-nez v0, :cond_1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 11
    :cond_1
    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/AP;->a:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/graph/E2;->f(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/D5;

    move-result-object v1

    if-eqz v1, :cond_8

    if-nez v0, :cond_3

    .line 13
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/g;->p()Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_1

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_3
    :goto_1
    if-nez v0, :cond_5

    .line 14
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/g;->n()Z

    move-result p1

    if-eqz p1, :cond_4

    goto :goto_2

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_5
    :goto_2
    if-nez v0, :cond_7

    .line 15
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result p1

    if-eqz p1, :cond_6

    goto :goto_3

    :cond_6
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_7
    :goto_3
    return-object v1

    .line 21
    :cond_8
    new-instance v0, Lcom/android/tools/r8/graph/D5;

    .line 24
    sget-object v1, Lcom/android/tools/r8/graph/j1;->u:[Lcom/android/tools/r8/graph/j1;

    .line 25
    new-instance v1, Lcom/android/tools/r8/graph/j1$a;

    const/4 v2, 0x1

    invoke-direct {v1, v2}, Lcom/android/tools/r8/graph/j1$a;-><init>(Z)V

    .line 26
    iget-object v3, p0, Lcom/android/tools/r8/internal/AP;->a:Lcom/android/tools/r8/graph/x2;

    .line 27
    invoke-virtual {v1, v3}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v1

    const/16 v3, 0x1009

    const/4 v4, 0x0

    .line 28
    invoke-static {v3, v4}, Lcom/android/tools/r8/graph/H4;->b(IZ)Lcom/android/tools/r8/graph/H4;

    move-result-object v3

    .line 29
    invoke-virtual {v1, v3}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/H4;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v1

    iget-object v3, p0, Lcom/android/tools/r8/internal/tP;->g:Lcom/android/tools/r8/graph/x2;

    iget-boolean v4, p0, Lcom/android/tools/r8/internal/tP;->h:Z

    iget-object v5, p0, Lcom/android/tools/r8/internal/tP;->i:Lcom/android/tools/r8/graph/y2;

    iget-object v6, p0, Lcom/android/tools/r8/internal/AP;->a:Lcom/android/tools/r8/graph/x2;

    iget-object v7, p0, Lcom/android/tools/r8/internal/tP;->f:Lcom/android/tools/r8/graph/y;

    .line 30
    invoke-virtual {v7}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v7

    .line 31
    new-instance v8, Lcom/android/tools/r8/internal/bx;

    invoke-direct {v8, v7}, Lcom/android/tools/r8/internal/bx;-><init>(Lcom/android/tools/r8/graph/B1;)V

    .line 32
    iput-object v6, v8, Lcom/android/tools/r8/internal/bx;->b:Lcom/android/tools/r8/graph/x2;

    .line 33
    iput-boolean v2, v8, Lcom/android/tools/r8/internal/bx;->e:Z

    .line 34
    invoke-virtual {v5}, Ljava/lang/Enum;->ordinal()I

    move-result v5

    packed-switch v5, :pswitch_data_0

    .line 57
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1

    .line 58
    :pswitch_0
    iput-object v3, v8, Lcom/android/tools/r8/internal/bx;->c:Lcom/android/tools/r8/graph/x2;

    .line 59
    sget-object v2, Lcom/android/tools/r8/internal/ax;->e:Lcom/android/tools/r8/internal/ax;

    iput-object v2, v8, Lcom/android/tools/r8/internal/bx;->f:Lcom/android/tools/r8/internal/ax;

    .line 60
    invoke-static {v4}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    iput-object v2, v8, Lcom/android/tools/r8/internal/bx;->g:Ljava/lang/Boolean;

    goto :goto_5

    .line 61
    :pswitch_1
    sget-boolean v4, Lcom/android/tools/r8/internal/bx;->k:Z

    if-nez v4, :cond_a

    invoke-virtual {v3, v7}, Lcom/android/tools/r8/graph/x2;->b(Lcom/android/tools/r8/graph/B1;)Z

    move-result v4

    if-eqz v4, :cond_9

    goto :goto_4

    :cond_9
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 62
    :cond_a
    :goto_4
    iput-object v3, v8, Lcom/android/tools/r8/internal/bx;->c:Lcom/android/tools/r8/graph/x2;

    .line 63
    iput-boolean v2, v8, Lcom/android/tools/r8/internal/bx;->i:Z

    .line 64
    sget-object v2, Lcom/android/tools/r8/internal/ax;->e:Lcom/android/tools/r8/internal/ax;

    iput-object v2, v8, Lcom/android/tools/r8/internal/bx;->f:Lcom/android/tools/r8/internal/ax;

    .line 65
    sget-object v2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    iput-object v2, v8, Lcom/android/tools/r8/internal/bx;->g:Ljava/lang/Boolean;

    goto :goto_5

    .line 66
    :pswitch_2
    invoke-virtual {v8, v3, v4}, Lcom/android/tools/r8/internal/bx;->b(Lcom/android/tools/r8/graph/x2;Z)Lcom/android/tools/r8/internal/bx;

    goto :goto_5

    .line 71
    :pswitch_3
    invoke-virtual {v8, v3, v4}, Lcom/android/tools/r8/internal/bx;->a(Lcom/android/tools/r8/graph/x2;Z)Lcom/android/tools/r8/internal/bx;

    .line 87
    :goto_5
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/bx;->a()Lcom/android/tools/r8/graph/G;

    move-result-object v2

    .line 88
    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/i0;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v1

    .line 92
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/j1$a;->b()Lcom/android/tools/r8/graph/j1$a;

    move-result-object v1

    .line 93
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/j1$a;->a()Lcom/android/tools/r8/graph/j1;

    move-result-object v1

    invoke-direct {v0, p1, v1}, Lcom/android/tools/r8/graph/D5;-><init>(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/j1;)V

    .line 94
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/graph/E0;->a(Lcom/android/tools/r8/graph/j1;)V

    .line 95
    invoke-interface {p2, v0}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    return-object v0

    :pswitch_data_0
    .packed-switch 0x4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
        :pswitch_2
    .end packed-switch
.end method

.method public final a()Lcom/android/tools/r8/graph/x2;
    .locals 1

    .line 96
    iget-object v0, p0, Lcom/android/tools/r8/internal/tP;->g:Lcom/android/tools/r8/graph/x2;

    return-object v0
.end method
