.class public final synthetic Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/I90;

.field public final synthetic f$1:Ljava/util/Collection;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/J90;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/I90;Ljava/util/Collection;Lcom/android/tools/r8/internal/J90;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/I90;

    iput-object p2, p0, Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda2;->f$1:Ljava/util/Collection;

    iput-object p3, p0, Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda2;->f$2:Lcom/android/tools/r8/internal/J90;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/I90;

    iget-object v1, p0, Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda2;->f$1:Ljava/util/Collection;

    iget-object v2, p0, Lcom/android/tools/r8/internal/W90$$ExternalSyntheticLambda2;->f$2:Lcom/android/tools/r8/internal/J90;

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-static {v0, v1, v2, p1}, Lcom/android/tools/r8/internal/W90;->a(Lcom/android/tools/r8/internal/I90;Ljava/util/Collection;Lcom/android/tools/r8/internal/J90;Lcom/android/tools/r8/graph/E2;)V

    return-void
.end method
