.class public final Lcom/android/tools/r8/internal/UF;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/util/Comparator;


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/VF;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/VF;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/UF;->b:Lcom/android/tools/r8/internal/VF;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 1

    .line 1
    check-cast p1, Lcom/android/tools/r8/internal/HF;

    check-cast p2, Lcom/android/tools/r8/internal/HF;

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/UF;->b:Lcom/android/tools/r8/internal/VF;

    iget-object v0, v0, Lcom/android/tools/r8/internal/VF;->c:Lcom/android/tools/r8/internal/lG;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-interface {p1}, Lcom/android/tools/r8/internal/HF;->a()I

    invoke-interface {p2}, Lcom/android/tools/r8/internal/HF;->a()I

    const/4 p1, 0x0

    throw p1
.end method
