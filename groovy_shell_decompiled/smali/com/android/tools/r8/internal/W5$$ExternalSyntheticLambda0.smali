.class public final synthetic Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiPredicate;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda0;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda0;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    invoke-static {p1, p2}, Lcom/android/tools/r8/internal/W5;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
