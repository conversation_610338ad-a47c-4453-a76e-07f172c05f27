.class public final Lcom/android/tools/r8/internal/Ui;
.super Lcom/android/tools/r8/internal/j20;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final b:Lcom/android/tools/r8/internal/Ui;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Ui;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Ui;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Ui;->b:Lcom/android/tools/r8/internal/Ui;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/j20;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/K5;Lcom/android/tools/r8/internal/Gp0;)Lcom/android/tools/r8/internal/j20;
    .locals 0

    return-object p0
.end method

.method public final a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Gp0;)Lcom/android/tools/r8/internal/j20;
    .locals 0

    return-object p0
.end method

.method public final a()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final a(Lcom/android/tools/r8/graph/E0;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method
