.class public final Lcom/android/tools/r8/internal/vA;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/r;


# static fields
.field public static final a:Lcom/android/tools/r8/internal/vA;

.field public static final synthetic b:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/vA;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/vA;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/vA;->a:Lcom/android/tools/r8/internal/vA;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/UB;Lcom/android/tools/r8/internal/WB;)I
    .locals 0

    .line 3
    sget-boolean p2, Lcom/android/tools/r8/internal/vA;->b:Z

    if-nez p2, :cond_1

    if-ne p0, p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    const/4 p1, 0x0

    return p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Qw;Lcom/android/tools/r8/internal/bg;)Lcom/android/tools/r8/internal/Gt0;
    .locals 0

    return-object p3
.end method

.method public final a(Lcom/android/tools/r8/internal/E5;)Z
    .locals 0

    .line 2
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public final getKind()I
    .locals 1

    const/4 v0, 0x2

    return v0
.end method

.method public final o()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final u()Ljava/lang/Iterable;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw v0
.end method
