.class public final synthetic Lcom/android/tools/r8/internal/N40$$ExternalSyntheticLambda10;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/N40$$ExternalSyntheticLambda10;->f$0:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/N40$$ExternalSyntheticLambda10;->f$0:Ljava/lang/String;

    check-cast p1, Lcom/android/tools/r8/internal/Kd0;

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/N40;->b(Ljava/lang/String;Lcom/android/tools/r8/internal/Kd0;)Z

    move-result p1

    return p1
.end method
