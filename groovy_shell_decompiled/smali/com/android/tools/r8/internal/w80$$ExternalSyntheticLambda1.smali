.class public final synthetic Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Ljava/util/function/Function;


# direct methods
.method public synthetic constructor <init>(Ljava/util/function/Function;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda1;->f$0:Ljava/util/function/Function;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda1;->f$0:Ljava/util/function/Function;

    check-cast p1, Lcom/android/tools/r8/graph/E0;

    invoke-interface {v0, p1}, Ljava/util/function/Function;->apply(<PERSON>ja<PERSON>/lang/Object;)Ljava/lang/Object;

    return-void
.end method
