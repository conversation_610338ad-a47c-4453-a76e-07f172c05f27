.class public final Lcom/android/tools/r8/internal/u2;
.super Lcom/android/tools/r8/internal/w2;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/w2;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(II[BLjava/util/function/BiPredicate;)I
    .locals 0

    .line 2
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public final a([BII)Z
    .locals 0

    .line 1
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public final b([BII)B
    .locals 0

    .line 1
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public final c(I)Lcom/android/tools/r8/internal/v2;
    .locals 0

    .line 1
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public final d()I
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw v0
.end method
