.class public final Lcom/android/tools/r8/internal/U9;
.super Lcom/android/tools/r8/internal/SV;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final c:Ljava/lang/String;

.field public final d:Ljava/util/Map;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/util/Map;)V
    .locals 2

    const/high16 v0, 0x90000

    const/4 v1, 0x0

    .line 1
    invoke-direct {p0, v0, v1}, Lcom/android/tools/r8/internal/SV;-><init>(ILcom/android/tools/r8/internal/SV;)V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/U9;->c:Ljava/lang/String;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/U9;->d:Ljava/util/Map;

    return-void
.end method


# virtual methods
.method public final b(ILcom/android/tools/r8/internal/qP;)V
    .locals 1

    .line 1
    iget-object p2, p0, Lcom/android/tools/r8/internal/U9;->d:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    iget-object v0, p0, Lcom/android/tools/r8/internal/U9;->c:Ljava/lang/String;

    invoke-interface {p2, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method
