.class public abstract synthetic Lcom/android/tools/r8/internal/Vc;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/A2;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    new-instance p0, Lcom/android/tools/r8/graph/A2;

    .line 3
    invoke-direct {p0, p1}, Lcom/android/tools/r8/graph/A2;-><init>(Lcom/android/tools/r8/graph/x2;)V

    return-object p0
.end method
