.class public final Lcom/android/tools/r8/internal/Tx;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/internal/Qx;

.field public final b:Ljava/lang/Object;

.field public final c:Lcom/android/tools/r8/internal/Vx;

.field public final d:Lcom/android/tools/r8/internal/Sx;

.field public final e:Ljava/lang/reflect/Method;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Qx;Ljava/lang/Object;Lcom/android/tools/r8/internal/Vx;Lcom/android/tools/r8/internal/Sx;Ljava/lang/Class;)V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    if-eqz p1, :cond_3

    .line 2
    iget-object v0, p4, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 3
    sget-object v1, Lcom/android/tools/r8/internal/Pu0;->g:Lcom/android/tools/r8/internal/Lu0;

    if-ne v0, v1, :cond_1

    if-eqz p3, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Null messageDefaultInstance"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 8
    :cond_1
    :goto_0
    iput-object p1, p0, Lcom/android/tools/r8/internal/Tx;->a:Lcom/android/tools/r8/internal/Qx;

    .line 9
    iput-object p2, p0, Lcom/android/tools/r8/internal/Tx;->b:Ljava/lang/Object;

    .line 10
    iput-object p3, p0, Lcom/android/tools/r8/internal/Tx;->c:Lcom/android/tools/r8/internal/Vx;

    .line 11
    iput-object p4, p0, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 16
    const-class p1, Lcom/android/tools/r8/internal/yH;

    invoke-virtual {p1, p5}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p1

    if-eqz p1, :cond_2

    const-string p1, "valueOf"

    const/4 p2, 0x1

    new-array p2, p2, [Ljava/lang/Class;

    .line 17
    sget-object p3, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    const/4 p4, 0x0

    aput-object p3, p2, p4

    .line 18
    :try_start_0
    invoke-virtual {p5, p1, p2}, Ljava/lang/Class;->getMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_0

    .line 19
    iput-object p1, p0, Lcom/android/tools/r8/internal/Tx;->e:Ljava/lang/reflect/Method;

    goto :goto_1

    :catch_0
    move-exception p1

    .line 20
    new-instance p2, Ljava/lang/RuntimeException;

    invoke-virtual {p5}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p3

    new-instance p4, Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/String;->length()I

    move-result p5

    add-int/lit8 p5, p5, 0x34

    invoke-direct {p4, p5}, Ljava/lang/StringBuilder;-><init>(I)V

    const-string p5, "Generated message class \""

    invoke-virtual {p4, p5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p4

    invoke-virtual {p4, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string p4, "\" missing method \"valueOf\"."

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-direct {p2, p3, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw p2

    :cond_2
    const/4 p1, 0x0

    .line 21
    iput-object p1, p0, Lcom/android/tools/r8/internal/Tx;->e:Ljava/lang/reflect/Method;

    :goto_1
    return-void

    .line 22
    :cond_3
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Null containingTypeDefaultInstance"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method public final a(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 3
    iget-object v0, v0, Lcom/android/tools/r8/internal/Pu0;->b:Lcom/android/tools/r8/internal/Ru0;

    .line 4
    sget-object v1, Lcom/android/tools/r8/internal/Ru0;->j:Lcom/android/tools/r8/internal/Ru0;

    if-ne v0, v1, :cond_2

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/Tx;->e:Ljava/lang/reflect/Method;

    const/4 v1, 0x0

    check-cast p1, Ljava/lang/Integer;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    .line 6
    :try_start_0
    invoke-virtual {v0, v1, v2}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 12
    invoke-virtual {p1}, Ljava/lang/reflect/InvocationTargetException;->getCause()Ljava/lang/Throwable;

    move-result-object p1

    .line 13
    instance-of v0, p1, Ljava/lang/RuntimeException;

    if-nez v0, :cond_1

    .line 15
    instance-of v0, p1, Ljava/lang/Error;

    if-eqz v0, :cond_0

    .line 16
    check-cast p1, Ljava/lang/Error;

    throw p1

    .line 18
    :cond_0
    new-instance v0, Ljava/lang/RuntimeException;

    const-string v1, "Unexpected exception thrown by generated accessor method."

    invoke-direct {v0, v1, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0

    .line 19
    :cond_1
    check-cast p1, Ljava/lang/RuntimeException;

    throw p1

    :catch_1
    move-exception p1

    .line 20
    new-instance v0, Ljava/lang/RuntimeException;

    const-string v1, "Couldn\'t use Java reflection to implement protocol message reflection."

    invoke-direct {v0, v1, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0

    :cond_2
    return-object p1
.end method

.method public final b(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Tx;->d:Lcom/android/tools/r8/internal/Sx;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 3
    iget-object v0, v0, Lcom/android/tools/r8/internal/Pu0;->b:Lcom/android/tools/r8/internal/Ru0;

    .line 4
    sget-object v1, Lcom/android/tools/r8/internal/Ru0;->j:Lcom/android/tools/r8/internal/Ru0;

    if-ne v0, v1, :cond_0

    .line 5
    check-cast p1, Lcom/android/tools/r8/internal/yH;

    invoke-interface {p1}, Lcom/android/tools/r8/internal/yH;->getNumber()I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    :cond_0
    return-object p1
.end method
