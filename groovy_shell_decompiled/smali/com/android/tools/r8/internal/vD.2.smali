.class public interface abstract Lcom/android/tools/r8/internal/vD;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# virtual methods
.method public abstract b()Lcom/android/tools/r8/internal/K5;
.end method

.method public i()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public j()Lcom/android/tools/r8/internal/rD;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public k()Lcom/android/tools/r8/internal/f40;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
