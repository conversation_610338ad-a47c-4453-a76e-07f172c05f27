.class public enum Lcom/android/tools/r8/internal/u6;
.super Ljava/lang/Enum;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final enum c:Lcom/android/tools/r8/internal/l6;

.field public static final enum d:Lcom/android/tools/r8/internal/m6;

.field public static final enum e:Lcom/android/tools/r8/internal/n6;

.field public static final enum f:Lcom/android/tools/r8/internal/o6;

.field public static final enum g:Lcom/android/tools/r8/internal/u6;

.field public static final enum h:Lcom/android/tools/r8/internal/p6;

.field public static final enum i:Lcom/android/tools/r8/internal/q6;

.field public static final enum j:Lcom/android/tools/r8/internal/r6;

.field public static final enum k:Lcom/android/tools/r8/internal/s6;

.field public static final enum l:Lcom/android/tools/r8/internal/t6;

.field public static final enum m:Lcom/android/tools/r8/internal/k6;


# instance fields
.field public final b:Z


# direct methods
.method static constructor <clinit>()V
    .locals 4

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/l6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/l6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->c:Lcom/android/tools/r8/internal/l6;

    .line 27
    new-instance v0, Lcom/android/tools/r8/internal/m6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/m6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->d:Lcom/android/tools/r8/internal/m6;

    .line 48
    new-instance v0, Lcom/android/tools/r8/internal/n6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/n6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->e:Lcom/android/tools/r8/internal/n6;

    .line 85
    new-instance v0, Lcom/android/tools/r8/internal/o6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/o6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->f:Lcom/android/tools/r8/internal/o6;

    .line 91
    new-instance v0, Lcom/android/tools/r8/internal/u6;

    const/4 v1, 0x4

    const-string v2, "REM"

    const/4 v3, 0x0

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/u6;-><init>(ILjava/lang/String;Z)V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->g:Lcom/android/tools/r8/internal/u6;

    .line 92
    new-instance v0, Lcom/android/tools/r8/internal/p6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/p6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->h:Lcom/android/tools/r8/internal/p6;

    .line 128
    new-instance v0, Lcom/android/tools/r8/internal/q6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/q6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->i:Lcom/android/tools/r8/internal/q6;

    .line 164
    new-instance v0, Lcom/android/tools/r8/internal/r6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/r6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->j:Lcom/android/tools/r8/internal/r6;

    .line 190
    new-instance v0, Lcom/android/tools/r8/internal/s6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/s6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->k:Lcom/android/tools/r8/internal/s6;

    .line 211
    new-instance v0, Lcom/android/tools/r8/internal/t6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/t6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->l:Lcom/android/tools/r8/internal/t6;

    .line 232
    new-instance v0, Lcom/android/tools/r8/internal/k6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/k6;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u6;->m:Lcom/android/tools/r8/internal/k6;

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p2, p1}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    iput-boolean p3, p0, Lcom/android/tools/r8/internal/u6;->b:Z

    return-void
.end method


# virtual methods
.method public a(JJ)J
    .locals 0

    .line 4
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public a(Lcom/android/tools/r8/internal/UZ;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/j6;
    .locals 0

    .line 2
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public a(Z)Ljava/lang/Integer;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public a()Z
    .locals 1

    .line 1
    instance-of v0, p0, Lcom/android/tools/r8/internal/k6;

    return v0
.end method

.method public b(Z)Ljava/lang/Integer;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public c(Z)Ljava/lang/Integer;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public d(II)I
    .locals 0

    .line 2
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw p1
.end method

.method public d(Z)Ljava/lang/Integer;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method
