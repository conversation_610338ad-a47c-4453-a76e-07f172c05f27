.class public abstract synthetic Lcom/android/tools/r8/internal/W9;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic a:[I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/It0;->g:[Lcom/android/tools/r8/internal/It0;

    invoke-virtual {v0}, [Lcom/android/tools/r8/internal/It0;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/android/tools/r8/internal/It0;

    .line 2
    array-length v0, v0

    new-array v0, v0, [I

    sput-object v0, Lcom/android/tools/r8/internal/W9;->a:[I

    :try_start_0
    sget-object v1, Lcom/android/tools/r8/internal/It0;->b:Lcom/android/tools/r8/internal/It0;

    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    const/4 v2, 0x1

    aput v2, v0, v1
    :try_end_0
    .catch Ljava/lang/NoSuchFieldError; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    const/4 v0, 0x2

    :try_start_1
    sget-object v1, Lcom/android/tools/r8/internal/W9;->a:[I

    sget-object v2, Lcom/android/tools/r8/internal/It0;->c:Lcom/android/tools/r8/internal/It0;

    invoke-virtual {v2}, Ljava/lang/Enum;->ordinal()I

    move-result v2

    aput v0, v1, v2
    :try_end_1
    .catch Ljava/lang/NoSuchFieldError; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    :try_start_2
    sget-object v1, Lcom/android/tools/r8/internal/W9;->a:[I

    sget-object v2, Lcom/android/tools/r8/internal/It0;->b:Lcom/android/tools/r8/internal/It0;

    const/4 v2, 0x3

    aput v2, v1, v0
    :try_end_2
    .catch Ljava/lang/NoSuchFieldError; {:try_start_2 .. :try_end_2} :catch_2

    :catch_2
    const/4 v0, 0x4

    :try_start_3
    sget-object v1, Lcom/android/tools/r8/internal/W9;->a:[I

    sget-object v2, Lcom/android/tools/r8/internal/It0;->e:Lcom/android/tools/r8/internal/It0;

    invoke-virtual {v2}, Ljava/lang/Enum;->ordinal()I

    move-result v2

    aput v0, v1, v2
    :try_end_3
    .catch Ljava/lang/NoSuchFieldError; {:try_start_3 .. :try_end_3} :catch_3

    :catch_3
    :try_start_4
    sget-object v1, Lcom/android/tools/r8/internal/W9;->a:[I

    sget-object v2, Lcom/android/tools/r8/internal/It0;->b:Lcom/android/tools/r8/internal/It0;

    const/4 v2, 0x5

    aput v2, v1, v0
    :try_end_4
    .catch Ljava/lang/NoSuchFieldError; {:try_start_4 .. :try_end_4} :catch_4

    :catch_4
    return-void
.end method
