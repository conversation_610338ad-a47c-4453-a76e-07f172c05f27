.class public final Lcom/android/tools/r8/internal/UE;
.super Lcom/android/tools/r8/internal/SE;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/H10;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/VE;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/SE;-><init>(Lcom/android/tools/r8/internal/VE;)V

    return-void
.end method


# virtual methods
.method public final add(Ljava/lang/Object;)V
    .locals 0

    .line 1
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public final next()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/WE;->a()Lcom/android/tools/r8/internal/KE;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/internal/L;->c:Ljava/lang/Object;

    return-object v0
.end method

.method public final previous()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/WE;->b()Lcom/android/tools/r8/internal/KE;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/internal/L;->c:Ljava/lang/Object;

    return-object v0
.end method

.method public final set(Ljava/lang/Object;)V
    .locals 0

    .line 1
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method
