.class public abstract Lcom/android/tools/r8/internal/U7;
.super Lcom/android/tools/r8/internal/Z7;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Z7;-><init>()V

    return-void
.end method


# virtual methods
.method public final iterator()Ljava/util/Iterator;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Q7;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/Q7;-><init>(Lcom/android/tools/r8/internal/U7;)V

    return-object v0
.end method
