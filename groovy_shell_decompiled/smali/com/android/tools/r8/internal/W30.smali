.class public final Lcom/android/tools/r8/internal/W30;
.super Lcom/android/tools/r8/internal/X30;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/shaking/f2;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/X30;-><init>(Lcom/android/tools/r8/shaking/f2;Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)Ljava/util/List;
    .locals 0

    return-object p1
.end method

.method public final a(Ljava/lang/Object;Lcom/android/tools/r8/shaking/M;)Z
    .locals 0

    .line 1
    check-cast p1, Lcom/android/tools/r8/graph/G2;

    .line 2
    iget-object p2, p2, Lcom/android/tools/r8/shaking/M;->F:Ljava/util/Set;

    .line 3
    invoke-interface {p2, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
