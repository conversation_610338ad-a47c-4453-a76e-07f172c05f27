.class public final Lcom/android/tools/r8/internal/rB;
.super Lcom/android/tools/r8/internal/SA;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final c:Lcom/android/tools/r8/internal/iB;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/iB;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/SA;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/rB;->c:Lcom/android/tools/r8/internal/iB;

    return-void
.end method

.method public static synthetic a(Ljava/util/function/Consumer;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0

    .line 3
    invoke-interface {p0, p2}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/cB;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rB;->c:Lcom/android/tools/r8/internal/iB;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/iB;->l()Lcom/android/tools/r8/internal/LB;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/SA;->a()Lcom/android/tools/r8/internal/cB;

    move-result-object v0

    .line 2
    new-instance v1, Lcom/android/tools/r8/internal/qB;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/internal/qB;-><init>(Lcom/android/tools/r8/internal/rB;Lcom/android/tools/r8/internal/cB;)V

    return-object v1
.end method

.method public final contains(Ljava/lang/Object;)Z
    .locals 2

    if-eqz p1, :cond_1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rB;->c:Lcom/android/tools/r8/internal/iB;

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/iB;->l()Lcom/android/tools/r8/internal/LB;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/SA;->iterator()Lcom/android/tools/r8/internal/Gs0;

    move-result-object v0

    .line 3
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 4
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    .line 5
    invoke-virtual {p1, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final forEach(Ljava/util/function/Consumer;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/rB;->c:Lcom/android/tools/r8/internal/iB;

    new-instance v1, Lcom/android/tools/r8/internal/rB$$ExternalSyntheticLambda0;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/internal/rB$$ExternalSyntheticLambda0;-><init>(Ljava/util/function/Consumer;)V

    invoke-interface {v0, v1}, Ljava/util/Map;->forEach(Ljava/util/function/BiConsumer;)V

    return-void
.end method

.method public final g()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final iterator()Lcom/android/tools/r8/internal/Gs0;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/pB;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/pB;-><init>(Lcom/android/tools/r8/internal/rB;)V

    return-object v0
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/pB;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/pB;-><init>(Lcom/android/tools/r8/internal/rB;)V

    return-object v0
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rB;->c:Lcom/android/tools/r8/internal/iB;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    return v0
.end method

.method public final spliterator()Ljava/util/Spliterator;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/rB;->c:Lcom/android/tools/r8/internal/iB;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/iB;->l()Lcom/android/tools/r8/internal/LB;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/SA;->spliterator()Ljava/util/Spliterator;

    move-result-object v0

    sget-object v1, Lcom/android/tools/r8/internal/rB$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/rB$$ExternalSyntheticLambda1;

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/te;->a(Ljava/util/Spliterator;Ljava/util/function/Function;)Lcom/android/tools/r8/internal/ne;

    move-result-object v0

    return-object v0
.end method
