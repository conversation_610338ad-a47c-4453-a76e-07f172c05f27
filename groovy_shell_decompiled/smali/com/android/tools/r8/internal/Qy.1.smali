.class public final Lcom/android/tools/r8/internal/Qy;
.super Lcom/android/tools/r8/internal/Xy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic g:Lcom/android/tools/r8/internal/az;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/az;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/Qy;->g:Lcom/android/tools/r8/internal/az;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Xy;-><init>(Lcom/android/tools/r8/internal/az;)V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Ry;)Ljava/lang/Object;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Py;

    invoke-direct {v0, p0, p1}, Lcom/android/tools/r8/internal/Py;-><init>(Lcom/android/tools/r8/internal/Qy;Lcom/android/tools/r8/internal/Ry;)V

    return-object v0
.end method
