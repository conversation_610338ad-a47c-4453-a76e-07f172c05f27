.class public final enum Lcom/android/tools/r8/internal/ug;
.super Ljava/lang/Enum;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/zH;


# static fields
.field public static final enum c:Lcom/android/tools/r8/internal/ug;

.field public static final enum d:Lcom/android/tools/r8/internal/ug;


# instance fields
.field public final b:I


# direct methods
.method static constructor <clinit>()V
    .locals 4

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/ug;

    const-string v1, "HDR_UNSET"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/ug;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/ug;->c:Lcom/android/tools/r8/internal/ug;

    .line 10
    new-instance v0, Lcom/android/tools/r8/internal/ug;

    const-string v1, "UNRECOGNIZED"

    const/4 v2, 0x3

    const/4 v3, -0x1

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/ug;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/ug;->d:Lcom/android/tools/r8/internal/ug;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    iput p3, p0, Lcom/android/tools/r8/internal/ug;->b:I

    return-void
.end method


# virtual methods
.method public final getNumber()I
    .locals 2

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/ug;->d:Lcom/android/tools/r8/internal/ug;

    if-eq p0, v0, :cond_0

    .line 5
    iget v0, p0, Lcom/android/tools/r8/internal/ug;->b:I

    return v0

    .line 6
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "Can\'t get the number of an unknown enum value."

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
