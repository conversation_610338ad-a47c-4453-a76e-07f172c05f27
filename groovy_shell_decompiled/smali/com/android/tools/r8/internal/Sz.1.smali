.class public final Lcom/android/tools/r8/internal/Sz;
.super Lcom/android/tools/r8/internal/Uz;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final d:I

.field public final e:I

.field public final f:Lcom/android/tools/r8/internal/B40;


# direct methods
.method public constructor <init>(ILcom/android/tools/r8/internal/K5;Lcom/android/tools/r8/internal/B40;II)V
    .locals 0

    .line 1
    invoke-direct {p0, p2, p1}, Lcom/android/tools/r8/internal/Uz;-><init>(Lcom/android/tools/r8/internal/K5;I)V

    .line 2
    iput-object p3, p0, Lcom/android/tools/r8/internal/Sz;->f:Lcom/android/tools/r8/internal/B40;

    .line 3
    iput p4, p0, Lcom/android/tools/r8/internal/Sz;->d:I

    .line 4
    iput p5, p0, Lcom/android/tools/r8/internal/Sz;->e:I

    return-void
.end method
