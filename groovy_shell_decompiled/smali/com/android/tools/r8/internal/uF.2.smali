.class public final Lcom/android/tools/r8/internal/uF;
.super Lcom/android/tools/r8/internal/b1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public b:I

.field public c:I

.field public final synthetic d:Lcom/android/tools/r8/internal/vF;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/vF;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/uF;->d:Lcom/android/tools/r8/internal/vF;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/b1;-><init>()V

    const/4 p1, -0x1

    .line 2
    iput p1, p0, Lcom/android/tools/r8/internal/uF;->b:I

    const/4 p1, 0x0

    iput p1, p0, Lcom/android/tools/r8/internal/uF;->c:I

    return-void
.end method


# virtual methods
.method public final hasNext()Z
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/uF;->c:I

    iget-object v1, p0, Lcom/android/tools/r8/internal/uF;->d:Lcom/android/tools/r8/internal/vF;

    iget-object v1, v1, Lcom/android/tools/r8/internal/vF;->b:Lcom/android/tools/r8/internal/wF;

    .line 2
    iget v1, v1, Lcom/android/tools/r8/internal/wF;->d:I

    if-ge v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final next()Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uF;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Q;

    iget-object v1, p0, Lcom/android/tools/r8/internal/uF;->d:Lcom/android/tools/r8/internal/vF;

    iget-object v1, v1, Lcom/android/tools/r8/internal/vF;->b:Lcom/android/tools/r8/internal/wF;

    .line 3
    iget-object v2, v1, Lcom/android/tools/r8/internal/wF;->b:[I

    .line 4
    iget v3, p0, Lcom/android/tools/r8/internal/uF;->c:I

    iput v3, p0, Lcom/android/tools/r8/internal/uF;->b:I

    aget v2, v2, v3

    .line 5
    iget-object v1, v1, Lcom/android/tools/r8/internal/wF;->c:[Ljava/lang/Object;

    add-int/lit8 v4, v3, 0x1

    .line 6
    iput v4, p0, Lcom/android/tools/r8/internal/uF;->c:I

    aget-object v1, v1, v3

    invoke-direct {v0, v2, v1}, Lcom/android/tools/r8/internal/Q;-><init>(ILjava/lang/Object;)V

    return-object v0

    .line 7
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public final remove()V
    .locals 4

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/uF;->b:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    .line 2
    iput v1, p0, Lcom/android/tools/r8/internal/uF;->b:I

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/uF;->d:Lcom/android/tools/r8/internal/vF;

    iget-object v0, v0, Lcom/android/tools/r8/internal/vF;->b:Lcom/android/tools/r8/internal/wF;

    .line 4
    iget v1, v0, Lcom/android/tools/r8/internal/wF;->d:I

    add-int/lit8 v2, v1, -0x1

    iput v2, v0, Lcom/android/tools/r8/internal/wF;->d:I

    .line 5
    iget v2, p0, Lcom/android/tools/r8/internal/uF;->c:I

    add-int/lit8 v3, v2, -0x1

    iput v3, p0, Lcom/android/tools/r8/internal/uF;->c:I

    sub-int/2addr v1, v2

    .line 6
    iget-object v0, v0, Lcom/android/tools/r8/internal/wF;->b:[I

    .line 7
    invoke-static {v0, v2, v0, v3, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/internal/uF;->d:Lcom/android/tools/r8/internal/vF;

    iget-object v0, v0, Lcom/android/tools/r8/internal/vF;->b:Lcom/android/tools/r8/internal/wF;

    .line 9
    iget-object v0, v0, Lcom/android/tools/r8/internal/wF;->c:[Ljava/lang/Object;

    .line 10
    iget v2, p0, Lcom/android/tools/r8/internal/uF;->c:I

    add-int/lit8 v3, v2, 0x1

    invoke-static {v0, v3, v0, v2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 11
    iget-object v0, p0, Lcom/android/tools/r8/internal/uF;->d:Lcom/android/tools/r8/internal/vF;

    iget-object v0, v0, Lcom/android/tools/r8/internal/vF;->b:Lcom/android/tools/r8/internal/wF;

    .line 12
    iget-object v1, v0, Lcom/android/tools/r8/internal/wF;->c:[Ljava/lang/Object;

    .line 13
    iget v0, v0, Lcom/android/tools/r8/internal/wF;->d:I

    const/4 v2, 0x0

    .line 14
    aput-object v2, v1, v0

    return-void

    .line 15
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method
