.class public abstract Lcom/android/tools/r8/internal/SU;
.super Lcom/android/tools/r8/internal/ij;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final f:Lcom/android/tools/r8/internal/OV;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/Y3;Lcom/android/tools/r8/internal/OV;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/ij;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/Y3;)V

    .line 2
    iput-object p3, p0, Lcom/android/tools/r8/internal/SU;->f:Lcom/android/tools/r8/internal/OV;

    return-void
.end method
