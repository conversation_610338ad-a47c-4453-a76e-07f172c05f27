.class public final synthetic Lcom/android/tools/r8/internal/e20$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/QR$a;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/e20;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/d20;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/e20;Lcom/android/tools/r8/internal/d20;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/e20$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/e20;

    iput-object p2, p0, Lcom/android/tools/r8/internal/e20$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/d20;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;I)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/e20$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/e20;

    iget-object v1, p0, Lcom/android/tools/r8/internal/e20$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/d20;

    check-cast p1, Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v0, v1, p1, p2}, Lcom/android/tools/r8/internal/e20;->a(Lcom/android/tools/r8/internal/d20;Lcom/android/tools/r8/graph/D5;I)V

    return-void
.end method
