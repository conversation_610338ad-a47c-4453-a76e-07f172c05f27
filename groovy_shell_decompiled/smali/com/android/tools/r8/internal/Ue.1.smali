.class public Lcom/android/tools/r8/internal/Ue;
.super Lcom/android/tools/r8/internal/Te;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final c:Ljava/util/function/ToIntFunction;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/naming/r0;Ljava/util/function/ToIntFunction;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Te;-><init>(Lcom/android/tools/r8/naming/r0;)V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/Ue;->c:Ljava/util/function/ToIntFunction;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/I2;Lcom/android/tools/r8/graph/I2;)I
    .locals 1

    if-ne p1, p2, :cond_0

    const/4 p1, 0x0

    return p1

    .line 1
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ue;->c:Ljava/util/function/ToIntFunction;

    invoke-interface {v0, p1}, Ljava/util/function/ToIntFunction;->applyAsInt(Ljava/lang/Object;)I

    move-result p1

    iget-object v0, p0, Lcom/android/tools/r8/internal/Ue;->c:Ljava/util/function/ToIntFunction;

    invoke-interface {v0, p2}, Ljava/util/function/ToIntFunction;->applyAsInt(Ljava/lang/Object;)I

    move-result p2

    .line 2
    invoke-static {p1, p2}, Ljava/lang/Integer;->compare(II)I

    move-result p1

    return p1
.end method
