.class public final synthetic Lcom/android/tools/r8/internal/T3$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/sr0;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/qZ;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/internal/qZ;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/T3$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/sr0;

    iput-object p2, p0, Lcom/android/tools/r8/internal/T3$$ExternalSyntheticLambda1;->f$1:Lcom/android/tools/r8/internal/qZ;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/T3$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/sr0;

    iget-object v1, p0, Lcom/android/tools/r8/internal/T3$$ExternalSyntheticLambda1;->f$1:Lcom/android/tools/r8/internal/qZ;

    check-cast p1, Lcom/android/tools/r8/internal/rZ;

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/T3;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/internal/rZ;)Lcom/android/tools/r8/internal/T3;

    move-result-object p1

    return-object p1
.end method
