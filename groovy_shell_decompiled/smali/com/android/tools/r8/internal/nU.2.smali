.class public final Lcom/android/tools/r8/internal/nU;
.super Lcom/android/tools/r8/internal/Ti;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic g:I


# instance fields
.field public final f:Ljava/util/IdentityHashMap;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;Ljava/util/IdentityHashMap;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Ti;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 2
    iput-object p2, p0, Lcom/android/tools/r8/internal/nU;->f:Ljava/util/IdentityHashMap;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/cV;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;
    .locals 4

    .line 1
    iget-object p2, p0, Lcom/android/tools/r8/internal/nU;->f:Ljava/util/IdentityHashMap;

    .line 2
    iget-object p3, p1, Lcom/android/tools/r8/internal/cV;->c:Lcom/android/tools/r8/internal/JI;

    .line 3
    invoke-static {}, Ljava/util/Collections;->emptyMap()Ljava/util/Map;

    move-result-object v0

    invoke-interface {p2, p3, v0}, Ljava/util/Map;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/util/Map;

    .line 4
    iget-object p3, p1, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 5
    move-object v0, p3

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    invoke-interface {p2, p3, v0}, Ljava/util/Map;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/x2;

    .line 6
    sget-object p3, Lcom/android/tools/r8/graph/proto/j;->d:Lcom/android/tools/r8/graph/proto/j;

    .line 7
    iget-object p3, p1, Lcom/android/tools/r8/internal/cV;->d:Lcom/android/tools/r8/graph/proto/j;

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/internal/TY;->b:Lcom/android/tools/r8/graph/y;

    .line 9
    iget-object v1, p1, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 10
    check-cast v1, Lcom/android/tools/r8/graph/x2;

    .line 11
    iget-object p1, p1, Lcom/android/tools/r8/internal/cV;->c:Lcom/android/tools/r8/internal/JI;

    .line 12
    sget-object v2, Lcom/android/tools/r8/internal/zX;->j:Lcom/android/tools/r8/internal/Qs;

    .line 13
    sget-object v2, Lcom/android/tools/r8/internal/JI;->h:Lcom/android/tools/r8/internal/JI;

    if-eq p1, v2, :cond_0

    sget-object v3, Lcom/android/tools/r8/internal/JI;->e:Lcom/android/tools/r8/internal/JI;

    if-ne p1, v3, :cond_6

    .line 15
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    invoke-interface {v0, v3}, Lcom/android/tools/r8/graph/d1;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object v3

    if-nez v3, :cond_1

    goto :goto_2

    .line 19
    :cond_1
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    invoke-interface {v0, v1}, Lcom/android/tools/r8/graph/d1;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object v0

    if-eqz v0, :cond_4

    .line 21
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result v0

    sget-object v1, Lcom/android/tools/r8/internal/JI;->e:Lcom/android/tools/r8/internal/JI;

    if-ne p1, v1, :cond_2

    const/4 p1, 0x1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    :goto_0
    xor-int/2addr p1, v0

    if-eqz p1, :cond_4

    .line 24
    iget-object p1, v3, Lcom/android/tools/r8/graph/E0;->f:Lcom/android/tools/r8/graph/Q;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/Q;->L()Z

    move-result p1

    if-eqz p1, :cond_3

    goto :goto_1

    :cond_3
    move-object p1, v1

    goto :goto_2

    .line 26
    :cond_4
    iget-object p1, v3, Lcom/android/tools/r8/graph/E0;->f:Lcom/android/tools/r8/graph/Q;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/Q;->L()Z

    move-result p1

    if-eqz p1, :cond_5

    sget-object p1, Lcom/android/tools/r8/internal/JI;->e:Lcom/android/tools/r8/internal/JI;

    goto :goto_2

    :cond_5
    :goto_1
    move-object p1, v2

    .line 27
    :cond_6
    :goto_2
    new-instance v0, Lcom/android/tools/r8/internal/cV;

    invoke-direct {v0, p2, p2, p1, p3}, Lcom/android/tools/r8/internal/cV;-><init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/graph/proto/j;)V

    .line 28
    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/cV;->a(Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    return-object p1
.end method

.method public final b(Lcom/android/tools/r8/internal/tv;)Lcom/android/tools/r8/internal/tv;
    .locals 0

    return-object p1
.end method

.method public final b(Lcom/android/tools/r8/internal/Fy;)Z
    .locals 1

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    return p1

    .line 1
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/TY;->d:Lcom/android/tools/r8/internal/Fy;

    .line 2
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Fy;->b(Lcom/android/tools/r8/internal/Fy;)Z

    move-result p1

    return p1
.end method
