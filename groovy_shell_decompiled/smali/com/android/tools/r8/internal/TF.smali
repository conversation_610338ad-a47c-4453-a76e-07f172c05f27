.class public final Lcom/android/tools/r8/internal/TF;
.super Lcom/android/tools/r8/internal/S;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/lang/Cloneable;


# instance fields
.field public transient b:[I

.field public transient c:[Ljava/lang/Object;

.field public transient d:I

.field public transient e:Z

.field public transient f:I

.field public transient g:I

.field public h:I

.field public final i:F

.field public transient j:Lcom/android/tools/r8/internal/QF;

.field public transient k:Lcom/android/tools/r8/internal/OF;

.field public transient l:Lcom/android/tools/r8/internal/LF;


# direct methods
.method public constructor <init>()V
    .locals 1

    const/16 v0, 0x10

    .line 11
    invoke-direct {p0, v0}, Lcom/android/tools/r8/internal/TF;-><init>(I)V

    return-void
.end method

.method public constructor <init>(I)V
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/S;-><init>()V

    if-ltz p1, :cond_0

    const/high16 v0, 0x3f400000    # 0.75f

    .line 4
    iput v0, p0, Lcom/android/tools/r8/internal/TF;->i:F

    .line 5
    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/ez;->a(IF)I

    move-result p1

    iput p1, p0, Lcom/android/tools/r8/internal/TF;->f:I

    add-int/lit8 v1, p1, -0x1

    .line 6
    iput v1, p0, Lcom/android/tools/r8/internal/TF;->d:I

    .line 7
    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/ez;->b(IF)I

    move-result p1

    iput p1, p0, Lcom/android/tools/r8/internal/TF;->g:I

    .line 8
    iget p1, p0, Lcom/android/tools/r8/internal/TF;->f:I

    add-int/lit8 p1, p1, 0x1

    new-array v0, p1, [I

    iput-object v0, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    .line 9
    new-array p1, p1, [Ljava/lang/Object;

    iput-object p1, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    return-void

    .line 10
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "The expected number of elements must be nonnegative"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/IF;)V
    .locals 1

    .line 12
    invoke-interface {p1}, Lcom/android/tools/r8/internal/vx;->size()I

    move-result v0

    invoke-direct {p0, v0}, Lcom/android/tools/r8/internal/TF;-><init>(I)V

    .line 13
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/TF;->putAll(Ljava/util/Map;)V

    return-void
.end method


# virtual methods
.method public final a(ILjava/lang/Object;)Ljava/lang/Object;
    .locals 4

    const/4 v0, 0x1

    if-nez p1, :cond_1

    .line 1
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    if-eqz v1, :cond_0

    iget p1, p0, Lcom/android/tools/r8/internal/TF;->f:I

    goto :goto_2

    .line 2
    :cond_0
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    .line 3
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->f:I

    goto :goto_1

    .line 7
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    .line 9
    invoke-static {p1}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v2

    iget v3, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v2, v3

    aget v3, v1, v2

    if-eqz v3, :cond_3

    if-ne v3, p1, :cond_2

    :goto_0
    move p1, v2

    goto :goto_2

    :cond_2
    add-int/2addr v2, v0

    .line 11
    iget v3, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v2, v3

    aget v3, v1, v2

    if-eqz v3, :cond_3

    if-ne v3, p1, :cond_2

    goto :goto_0

    :cond_3
    move v0, v2

    .line 15
    :goto_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    aput p1, v1, v0

    .line 16
    iget-object p1, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    aput-object p2, p1, v0

    .line 17
    iget p1, p0, Lcom/android/tools/r8/internal/TF;->h:I

    add-int/lit8 v0, p1, 0x1

    iput v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    iget v0, p0, Lcom/android/tools/r8/internal/TF;->g:I

    if-lt p1, v0, :cond_4

    add-int/lit8 p1, p1, 0x2

    iget v0, p0, Lcom/android/tools/r8/internal/TF;->i:F

    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/ez;->a(IF)I

    move-result p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/TF;->d(I)V

    :cond_4
    const/4 p1, -0x1

    :goto_2
    if-gez p1, :cond_5

    const/4 p1, 0x0

    return-object p1

    .line 18
    :cond_5
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    aget-object v1, v0, p1

    .line 19
    aput-object p2, v0, p1

    return-object v1
.end method

.method public final a(I)Z
    .locals 5

    if-nez p1, :cond_0

    .line 20
    iget-boolean p1, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    return p1

    .line 22
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    .line 25
    invoke-static {p1}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v1

    iget v2, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v1, v2

    aget v2, v0, v1

    const/4 v3, 0x0

    if-nez v2, :cond_1

    return v3

    :cond_1
    const/4 v4, 0x1

    if-ne p1, v2, :cond_2

    return v4

    :cond_2
    add-int/2addr v1, v4

    .line 29
    iget v2, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v1, v2

    aget v2, v0, v1

    if-nez v2, :cond_3

    return v3

    :cond_3
    if-ne p1, v2, :cond_2

    return v4
.end method

.method public final c()Lcom/android/tools/r8/internal/J10;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->j:Lcom/android/tools/r8/internal/QF;

    if-nez v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/QF;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/QF;-><init>(Lcom/android/tools/r8/internal/TF;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/TF;->j:Lcom/android/tools/r8/internal/QF;

    .line 2
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->j:Lcom/android/tools/r8/internal/QF;

    return-object v0
.end method

.method public final clear()V
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x0

    .line 2
    iput v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    .line 3
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    invoke-static {v1, v0}, Ljava/util/Arrays;->fill([II)V

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Ljava/util/Arrays;->fill([Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method

.method public final clone()Ljava/lang/Object;
    .locals 2

    .line 1
    :try_start_0
    invoke-super {p0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/TF;
    :try_end_0
    .catch Ljava/lang/CloneNotSupportedException; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v1, 0x0

    .line 6
    iput-object v1, v0, Lcom/android/tools/r8/internal/TF;->k:Lcom/android/tools/r8/internal/OF;

    .line 7
    iput-object v1, v0, Lcom/android/tools/r8/internal/TF;->l:Lcom/android/tools/r8/internal/LF;

    .line 8
    iput-object v1, v0, Lcom/android/tools/r8/internal/TF;->j:Lcom/android/tools/r8/internal/QF;

    .line 9
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    iput-boolean v1, v0, Lcom/android/tools/r8/internal/TF;->e:Z

    .line 10
    iget-object v1, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    invoke-virtual {v1}, [I->clone()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [I

    iput-object v1, v0, Lcom/android/tools/r8/internal/TF;->b:[I

    .line 11
    iget-object v1, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    invoke-virtual {v1}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Ljava/lang/Object;

    iput-object v1, v0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    return-object v0

    .line 12
    :catch_0
    new-instance v0, Ljava/lang/InternalError;

    invoke-direct {v0}, Ljava/lang/InternalError;-><init>()V

    throw v0
.end method

.method public final containsValue(Ljava/lang/Object;)Z
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    .line 2
    iget-object v1, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    .line 3
    iget-boolean v2, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    const/4 v3, 0x1

    if-eqz v2, :cond_0

    iget v2, p0, Lcom/android/tools/r8/internal/TF;->f:I

    aget-object v2, v0, v2

    if-ne v2, p1, :cond_0

    return v3

    .line 4
    :cond_0
    iget v2, p0, Lcom/android/tools/r8/internal/TF;->f:I

    :goto_0
    add-int/lit8 v4, v2, -0x1

    if-eqz v2, :cond_2

    aget v2, v1, v4

    if-eqz v2, :cond_1

    aget-object v2, v0, v4

    if-ne v2, p1, :cond_1

    return v3

    :cond_1
    move v2, v4

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    return p1
.end method

.method public final d(I)V
    .locals 9

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    .line 2
    iget-object v1, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    add-int/lit8 v2, p1, -0x1

    add-int/lit8 v3, p1, 0x1

    .line 4
    new-array v4, v3, [I

    .line 5
    new-array v3, v3, [Ljava/lang/Object;

    .line 6
    iget v5, p0, Lcom/android/tools/r8/internal/TF;->f:I

    .line 7
    iget-boolean v6, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    if-eqz v6, :cond_0

    iget v6, p0, Lcom/android/tools/r8/internal/TF;->h:I

    add-int/lit8 v6, v6, -0x1

    goto :goto_0

    :cond_0
    iget v6, p0, Lcom/android/tools/r8/internal/TF;->h:I

    :goto_0
    add-int/lit8 v7, v6, -0x1

    if-eqz v6, :cond_3

    :goto_1
    add-int/lit8 v5, v5, -0x1

    .line 8
    aget v6, v0, v5

    if-nez v6, :cond_1

    goto :goto_1

    .line 9
    :cond_1
    invoke-static {v6}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v6

    and-int/2addr v6, v2

    aget v8, v4, v6

    if-eqz v8, :cond_2

    :goto_2
    add-int/lit8 v6, v6, 0x1

    and-int/2addr v6, v2

    .line 10
    aget v8, v4, v6

    if-eqz v8, :cond_2

    goto :goto_2

    .line 11
    :cond_2
    aget v8, v0, v5

    aput v8, v4, v6

    .line 12
    aget-object v8, v1, v5

    aput-object v8, v3, v6

    move v6, v7

    goto :goto_0

    .line 14
    :cond_3
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->f:I

    aget-object v0, v1, v0

    aput-object v0, v3, p1

    .line 15
    iput p1, p0, Lcom/android/tools/r8/internal/TF;->f:I

    .line 16
    iput v2, p0, Lcom/android/tools/r8/internal/TF;->d:I

    .line 17
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->i:F

    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/ez;->b(IF)I

    move-result p1

    iput p1, p0, Lcom/android/tools/r8/internal/TF;->g:I

    .line 18
    iput-object v4, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    .line 19
    iput-object v3, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    return-void
.end method

.method public final e(I)Ljava/lang/Object;
    .locals 7

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    aget-object v1, v0, p1

    const/4 v2, 0x0

    .line 2
    aput-object v2, v0, p1

    .line 3
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    :goto_0
    add-int/lit8 v3, p1, 0x1

    .line 6
    iget v4, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v3, v4

    .line 8
    :goto_1
    aget v4, v0, v3

    if-nez v4, :cond_1

    const/4 v3, 0x0

    .line 9
    aput v3, v0, p1

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    aput-object v2, v0, p1

    .line 11
    iget p1, p0, Lcom/android/tools/r8/internal/TF;->h:I

    iget v0, p0, Lcom/android/tools/r8/internal/TF;->g:I

    div-int/lit8 v0, v0, 0x4

    if-ge p1, v0, :cond_0

    iget p1, p0, Lcom/android/tools/r8/internal/TF;->f:I

    const/16 v0, 0x10

    if-le p1, v0, :cond_0

    div-int/lit8 p1, p1, 0x2

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/TF;->d(I)V

    :cond_0
    return-object v1

    .line 12
    :cond_1
    invoke-static {v4}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v5

    iget v6, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v5, v6

    if-gt p1, v3, :cond_2

    if-ge p1, v5, :cond_3

    if-le v5, v3, :cond_4

    goto :goto_2

    :cond_2
    if-lt p1, v5, :cond_4

    if-le v5, v3, :cond_4

    .line 16
    :cond_3
    :goto_2
    aput v4, v0, p1

    .line 17
    iget-object v4, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    aget-object v5, v4, v3

    aput-object v5, v4, p1

    move p1, v3

    goto :goto_0

    :cond_4
    add-int/lit8 v3, v3, 0x1

    and-int/2addr v3, v6

    goto :goto_1
.end method

.method public final get(I)Ljava/lang/Object;
    .locals 4

    const/4 v0, 0x0

    if-nez p1, :cond_1

    .line 1
    iget-boolean p1, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    iget v0, p0, Lcom/android/tools/r8/internal/TF;->f:I

    aget-object v0, p1, v0

    :cond_0
    return-object v0

    .line 3
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    .line 6
    invoke-static {p1}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v2

    iget v3, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v2, v3

    aget v3, v1, v2

    if-nez v3, :cond_2

    return-object v0

    :cond_2
    if-ne p1, v3, :cond_3

    .line 7
    iget-object p1, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    aget-object p1, p1, v2

    return-object p1

    :cond_3
    add-int/lit8 v2, v2, 0x1

    .line 10
    iget v3, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v2, v3

    aget v3, v1, v2

    if-nez v3, :cond_4

    return-object v0

    :cond_4
    if-ne p1, v3, :cond_3

    .line 11
    iget-object p1, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    aget-object p1, p1, v2

    return-object p1
.end method

.method public final hashCode()I
    .locals 6

    .line 1
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    if-eqz v0, :cond_0

    iget v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    :goto_0
    const/4 v1, 0x0

    move v2, v1

    move v3, v2

    :goto_1
    add-int/lit8 v4, v0, -0x1

    if-eqz v0, :cond_4

    .line 2
    :goto_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    aget v0, v0, v2

    if-nez v0, :cond_1

    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    .line 4
    :cond_1
    iget-object v5, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    aget-object v5, v5, v2

    if-eq p0, v5, :cond_3

    if-nez v5, :cond_2

    move v5, v1

    goto :goto_3

    .line 5
    :cond_2
    invoke-static {v5}, Ljava/lang/System;->identityHashCode(Ljava/lang/Object;)I

    move-result v5

    :goto_3
    xor-int/2addr v0, v5

    :cond_3
    add-int/2addr v3, v0

    add-int/lit8 v2, v2, 0x1

    move v0, v4

    goto :goto_1

    .line 10
    :cond_4
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    if-eqz v0, :cond_6

    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    iget v2, p0, Lcom/android/tools/r8/internal/TF;->f:I

    aget-object v0, v0, v2

    if-nez v0, :cond_5

    goto :goto_4

    :cond_5
    invoke-static {v0}, Ljava/lang/System;->identityHashCode(Ljava/lang/Object;)I

    move-result v1

    :goto_4
    add-int/2addr v3, v1

    :cond_6
    return v3
.end method

.method public final isEmpty()Z
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final keySet()Lcom/android/tools/r8/internal/WG;
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->k:Lcom/android/tools/r8/internal/OF;

    if-nez v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/OF;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/OF;-><init>(Lcom/android/tools/r8/internal/TF;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/TF;->k:Lcom/android/tools/r8/internal/OF;

    .line 3
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->k:Lcom/android/tools/r8/internal/OF;

    return-object v0
.end method

.method public final bridge synthetic keySet()Ljava/util/Set;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/TF;->keySet()Lcom/android/tools/r8/internal/WG;

    move-result-object v0

    return-object v0
.end method

.method public final putAll(Ljava/util/Map;)V
    .locals 4

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->i:F

    float-to-double v0, v0

    const-wide/high16 v2, 0x3fe0000000000000L    # 0.5

    cmpg-double v0, v0, v2

    if-gtz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Map;->size()I

    move-result v0

    .line 2
    iget v1, p0, Lcom/android/tools/r8/internal/TF;->i:F

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/ez;->a(IF)I

    move-result v0

    .line 3
    iget v1, p0, Lcom/android/tools/r8/internal/TF;->f:I

    if-le v0, v1, :cond_1

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/TF;->d(I)V

    goto :goto_0

    .line 4
    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    .line 5
    invoke-interface {p1}, Ljava/util/Map;->size()I

    move-result v1

    add-int/2addr v1, v0

    int-to-long v0, v1

    long-to-float v0, v0

    .line 6
    iget v1, p0, Lcom/android/tools/r8/internal/TF;->i:F

    div-float/2addr v0, v1

    float-to-double v0, v0

    invoke-static {v0, v1}, Ljava/lang/Math;->ceil(D)D

    move-result-wide v0

    double-to-long v0, v0

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/ez;->b(J)J

    move-result-wide v0

    const-wide/16 v2, 0x2

    invoke-static {v2, v3, v0, v1}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    const-wide/32 v2, 0x40000000

    invoke-static {v2, v3, v0, v1}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v0

    long-to-int v0, v0

    .line 7
    iget v1, p0, Lcom/android/tools/r8/internal/TF;->f:I

    if-le v0, v1, :cond_1

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/TF;->d(I)V

    .line 8
    :cond_1
    :goto_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/S;->putAll(Ljava/util/Map;)V

    return-void
.end method

.method public final remove(I)Ljava/lang/Object;
    .locals 4

    const/4 v0, 0x0

    if-nez p1, :cond_2

    .line 1
    iget-boolean p1, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    if-eqz p1, :cond_1

    const/4 p1, 0x0

    .line 2
    iput-boolean p1, p0, Lcom/android/tools/r8/internal/TF;->e:Z

    .line 3
    iget-object p1, p0, Lcom/android/tools/r8/internal/TF;->c:[Ljava/lang/Object;

    iget v1, p0, Lcom/android/tools/r8/internal/TF;->f:I

    aget-object v2, p1, v1

    .line 4
    aput-object v0, p1, v1

    .line 5
    iget p1, p0, Lcom/android/tools/r8/internal/TF;->h:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lcom/android/tools/r8/internal/TF;->h:I

    .line 6
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->g:I

    div-int/lit8 v0, v0, 0x4

    if-ge p1, v0, :cond_0

    const/16 p1, 0x10

    if-le v1, p1, :cond_0

    div-int/lit8 v1, v1, 0x2

    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/TF;->d(I)V

    :cond_0
    return-object v2

    :cond_1
    return-object v0

    .line 7
    :cond_2
    iget-object v1, p0, Lcom/android/tools/r8/internal/TF;->b:[I

    .line 10
    invoke-static {p1}, Lcom/android/tools/r8/internal/ez;->a(I)I

    move-result v2

    iget v3, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v2, v3

    aget v3, v1, v2

    if-nez v3, :cond_3

    return-object v0

    :cond_3
    if-ne p1, v3, :cond_4

    .line 11
    invoke-virtual {p0, v2}, Lcom/android/tools/r8/internal/TF;->e(I)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_4
    add-int/lit8 v2, v2, 0x1

    .line 13
    iget v3, p0, Lcom/android/tools/r8/internal/TF;->d:I

    and-int/2addr v2, v3

    aget v3, v1, v2

    if-nez v3, :cond_5

    return-object v0

    :cond_5
    if-ne p1, v3, :cond_4

    .line 14
    invoke-virtual {p0, v2}, Lcom/android/tools/r8/internal/TF;->e(I)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final size()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/TF;->h:I

    return v0
.end method

.method public final values()Lcom/android/tools/r8/internal/mb0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->l:Lcom/android/tools/r8/internal/LF;

    if-nez v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/LF;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/LF;-><init>(Lcom/android/tools/r8/internal/TF;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/TF;->l:Lcom/android/tools/r8/internal/LF;

    .line 19
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/TF;->l:Lcom/android/tools/r8/internal/LF;

    return-object v0
.end method
