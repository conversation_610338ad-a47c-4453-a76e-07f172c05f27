.class public final synthetic Lcom/android/tools/r8/internal/RC$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/RC;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/RC;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/RC$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/RC;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/RC$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/RC;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/RC;->n()Lcom/android/tools/r8/internal/Gt0;

    move-result-object v0

    return-object v0
.end method
