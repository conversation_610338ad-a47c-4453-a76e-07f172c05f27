.class public abstract Lcom/android/tools/r8/internal/v30;
.super Lcom/android/tools/r8/internal/C1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/C1;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a(I)Lcom/android/tools/r8/internal/u30;
.end method

.method public abstract a(ILcom/android/tools/r8/internal/u30;)Lcom/android/tools/r8/internal/v30;
.end method

.method public final b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/C1;)Lcom/android/tools/r8/internal/C1;
    .locals 0

    .line 1
    check-cast p2, Lcom/android/tools/r8/internal/v30;

    .line 2
    instance-of p1, p0, Lcom/android/tools/r8/internal/j7;

    if-eqz p1, :cond_0

    goto :goto_1

    .line 3
    :cond_0
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 4
    instance-of p1, p2, Lcom/android/tools/r8/internal/j7;

    if-eqz p1, :cond_1

    move-object p2, p0

    goto :goto_1

    .line 5
    :cond_1
    instance-of p1, p0, Lcom/android/tools/r8/internal/Cs0;

    if-nez p1, :cond_3

    instance-of p1, p2, Lcom/android/tools/r8/internal/Cs0;

    if-eqz p1, :cond_2

    goto :goto_0

    .line 6
    :cond_2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/v30;->d()Lcom/android/tools/r8/internal/QY;

    move-result-object p1

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/v30;->d()Lcom/android/tools/r8/internal/QY;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/QY;->a(Lcom/android/tools/r8/internal/QY;)Lcom/android/tools/r8/internal/QY;

    move-result-object p2

    goto :goto_1

    .line 7
    :cond_3
    :goto_0
    sget-object p2, Lcom/android/tools/r8/internal/Cs0;->b:Lcom/android/tools/r8/internal/Cs0;

    :goto_1
    return-object p2
.end method

.method public final c()Lcom/android/tools/r8/internal/C1;
    .locals 0

    return-object p0
.end method

.method public d()Lcom/android/tools/r8/internal/QY;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract e()Lcom/android/tools/r8/internal/v30;
.end method
