.class public final synthetic Lcom/android/tools/r8/internal/Xh$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Xh;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/Yh;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Xh;Lcom/android/tools/r8/internal/Yh;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Xh$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/Xh;

    iput-object p2, p0, Lcom/android/tools/r8/internal/Xh$$ExternalSyntheticLambda2;->f$1:Lcom/android/tools/r8/internal/Yh;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/Xh$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/Xh;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Xh$$ExternalSyntheticLambda2;->f$1:Lcom/android/tools/r8/internal/Yh;

    check-cast p1, Lcom/android/tools/r8/internal/ci;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Xh;->a(Lcom/android/tools/r8/internal/Yh;Lcom/android/tools/r8/internal/ci;)V

    return-void
.end method
