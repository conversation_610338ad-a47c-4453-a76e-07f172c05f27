.class public final synthetic Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;->INSTANCE:Lcom/android/tools/r8/internal/u40$$ExternalSyntheticLambda3;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/Rt0;

    check-cast p1, Lcom/android/tools/r8/internal/Yj0;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/Rt0;-><init>(Lcom/android/tools/r8/internal/Yj0;)V

    return-object v0
.end method
