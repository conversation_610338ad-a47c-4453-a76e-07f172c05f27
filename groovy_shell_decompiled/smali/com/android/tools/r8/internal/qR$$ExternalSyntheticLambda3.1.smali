.class public final synthetic Lcom/android/tools/r8/internal/qR$$ExternalSyntheticLambda3;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/y;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/jQ;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/jQ;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/qR$$ExternalSyntheticLambda3;->f$0:Lcom/android/tools/r8/graph/y;

    iput-object p2, p0, Lcom/android/tools/r8/internal/qR$$ExternalSyntheticLambda3;->f$1:Lcom/android/tools/r8/internal/jQ;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/qR$$ExternalSyntheticLambda3;->f$0:Lcom/android/tools/r8/graph/y;

    iget-object v1, p0, Lcom/android/tools/r8/internal/qR$$ExternalSyntheticLambda3;->f$1:Lcom/android/tools/r8/internal/jQ;

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/qR;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/jQ;Lcom/android/tools/r8/graph/E2;)V

    return-void
.end method
