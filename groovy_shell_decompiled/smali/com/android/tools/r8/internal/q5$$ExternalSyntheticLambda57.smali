.class public final synthetic Lcom/android/tools/r8/internal/q5$$ExternalSyntheticLambda57;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/t5;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/q5$$ExternalSyntheticLambda57;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/q5$$ExternalSyntheticLambda57;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/q5$$ExternalSyntheticLambda57;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/q5$$ExternalSyntheticLambda57;->INSTANCE:Lcom/android/tools/r8/internal/q5$$ExternalSyntheticLambda57;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/G;
    .locals 0

    invoke-static {p1, p2}, Lcom/android/tools/r8/internal/w5;->S(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/G;

    move-result-object p1

    return-object p1
.end method
