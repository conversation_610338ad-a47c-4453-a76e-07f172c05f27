.class public final Lcom/android/tools/r8/internal/w80;
.super Lcom/android/tools/r8/internal/fj;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic i:Z


# instance fields
.field public final synthetic f:Lcom/android/tools/r8/internal/x80;

.field public final synthetic g:Lcom/android/tools/r8/internal/A80;

.field public final synthetic h:Lcom/android/tools/r8/internal/z80;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/z80;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/w80;->i:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/z80;Lcom/android/tools/r8/internal/x80;Lcom/android/tools/r8/internal/A80;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    iput-object p2, p0, Lcom/android/tools/r8/internal/w80;->f:Lcom/android/tools/r8/internal/x80;

    iput-object p3, p0, Lcom/android/tools/r8/internal/w80;->g:Lcom/android/tools/r8/internal/A80;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/fj;-><init>()V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/proto/j;Lcom/android/tools/r8/graph/j1;Lcom/android/tools/r8/graph/j1$a;)V
    .locals 1

    .line 117
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/proto/j;->g()Z

    move-result v0

    if-nez v0, :cond_0

    .line 119
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/graph/proto/j;->a(Lcom/android/tools/r8/graph/j1;)Ljava/util/function/Consumer;

    move-result-object p0

    invoke-virtual {p2, p0}, Lcom/android/tools/r8/graph/j1$a;->a(Ljava/util/function/Consumer;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object p0

    .line 120
    invoke-static {}, Lcom/android/tools/r8/graph/D3$g;->d()Lcom/android/tools/r8/graph/D3$g;

    move-result-object p1

    .line 121
    iput-object p1, p0, Lcom/android/tools/r8/graph/j1$a;->d:Lcom/android/tools/r8/graph/D3$g;

    :cond_0
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/internal/y80;Ljava/util/Map;Lcom/android/tools/r8/internal/x80;Lcom/android/tools/r8/internal/A80;Lcom/android/tools/r8/graph/j1;)Lcom/android/tools/r8/graph/j1;
    .locals 6

    .line 48
    invoke-virtual {p6}, Lcom/android/tools/r8/graph/j1;->g1()Lcom/android/tools/r8/graph/C2;

    move-result-object v0

    .line 49
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/b1;->g0()Z

    move-result v1

    const/4 v2, 0x1

    if-nez v1, :cond_1

    .line 52
    iget-object p1, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    iget-object p1, p1, Lcom/android/tools/r8/internal/z80;->b:Lcom/android/tools/r8/graph/B1;

    check-cast v0, Lcom/android/tools/r8/graph/A2;

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 53
    iget-object p3, v0, Lcom/android/tools/r8/graph/A2;->c:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/s2;->w0()Lcom/android/tools/r8/graph/I2;

    move-result-object p3

    .line 54
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->d1:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {p3, p1}, Lcom/android/tools/r8/graph/E;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_0

    .line 55
    iget-object p1, p2, Lcom/android/tools/r8/internal/y80;->b:Lcom/android/tools/r8/internal/g6;

    .line 56
    iget-object p1, p1, Lcom/android/tools/r8/internal/g6;->b:Lcom/android/tools/r8/internal/az;

    .line 57
    invoke-virtual {p1, v0, v0, v2}, Lcom/android/tools/r8/internal/az;->a(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object;

    :cond_0
    return-object p6

    .line 58
    :cond_1
    sget-boolean v1, Lcom/android/tools/r8/internal/w80;->i:Z

    if-nez v1, :cond_3

    if-eqz p3, :cond_2

    goto :goto_0

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 60
    :cond_3
    :goto_0
    invoke-virtual {p6}, Lcom/android/tools/r8/graph/j1;->q1()Z

    move-result v1

    if-eqz v1, :cond_4

    .line 61
    invoke-interface {p3, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/graph/C2;

    goto :goto_1

    .line 62
    :cond_4
    iget-object p3, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    iget-object p3, p3, Lcom/android/tools/r8/internal/z80;->b:Lcom/android/tools/r8/graph/B1;

    .line 63
    move-object v1, v0

    check-cast v1, Lcom/android/tools/r8/graph/A2;

    invoke-virtual {p2, v1, p3, p4, v2}, Lcom/android/tools/r8/internal/y80;->a(Lcom/android/tools/r8/graph/A2;Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/internal/x80;Z)Lcom/android/tools/r8/graph/C2;

    move-result-object p3

    .line 64
    :goto_1
    invoke-virtual {v0, p3}, Lcom/android/tools/r8/graph/C2;->equals(Ljava/lang/Object;)Z

    move-result p4

    if-eqz p4, :cond_6

    .line 67
    iget-object p1, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    iget-object p1, p1, Lcom/android/tools/r8/internal/z80;->b:Lcom/android/tools/r8/graph/B1;

    check-cast v0, Lcom/android/tools/r8/graph/A2;

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 68
    iget-object p3, v0, Lcom/android/tools/r8/graph/A2;->c:Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/s2;->w0()Lcom/android/tools/r8/graph/I2;

    move-result-object p3

    .line 69
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->d1:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {p3, p1}, Lcom/android/tools/r8/graph/E;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_5

    .line 70
    iget-object p1, p2, Lcom/android/tools/r8/internal/y80;->b:Lcom/android/tools/r8/internal/g6;

    .line 71
    iget-object p1, p1, Lcom/android/tools/r8/internal/g6;->b:Lcom/android/tools/r8/internal/az;

    .line 72
    invoke-virtual {p1, v0, v0, v2}, Lcom/android/tools/r8/internal/az;->a(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object;

    :cond_5
    return-object p6

    .line 73
    :cond_6
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/b1;->f0()Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    iget-object p2, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    iget-object p2, p2, Lcom/android/tools/r8/internal/z80;->b:Lcom/android/tools/r8/graph/B1;

    invoke-virtual {p3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 74
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 75
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/C2;->b()Lcom/android/tools/r8/graph/F2;

    move-result-object p4

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/C2;->a()Lcom/android/tools/r8/graph/I2;

    move-result-object p3

    invoke-virtual {p2, p1, p4, p3}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    .line 76
    sget-boolean p2, Lcom/android/tools/r8/internal/A80;->d:Z

    if-nez p2, :cond_8

    .line 77
    invoke-virtual {p5}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 78
    invoke-virtual {p6}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    if-eq p2, p1, :cond_7

    goto :goto_2

    :cond_7
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 79
    :cond_8
    :goto_2
    iget-object p2, p5, Lcom/android/tools/r8/internal/A80;->b:Lcom/android/tools/r8/internal/g6;

    .line 80
    invoke-virtual {p6}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/graph/x2;

    .line 81
    iget-object p2, p2, Lcom/android/tools/r8/internal/g6;->b:Lcom/android/tools/r8/internal/az;

    .line 82
    invoke-virtual {p2, p3, p1, v2}, Lcom/android/tools/r8/internal/az;->a(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object;

    .line 83
    invoke-virtual {p6}, Lcom/android/tools/r8/graph/j1;->c1()Lcom/android/tools/r8/graph/L2;

    move-result-object p2

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/x2;->A0()Lcom/android/tools/r8/graph/L2;

    move-result-object p3

    invoke-virtual {p2, p3}, Lcom/android/tools/r8/graph/L2;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_11

    .line 84
    invoke-virtual {p6}, Lcom/android/tools/r8/graph/j1;->y0()Z

    move-result p2

    xor-int/2addr p2, v2

    .line 85
    invoke-virtual {p6}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p6}, Lcom/android/tools/r8/graph/j1;->y0()Z

    move-result p4

    invoke-virtual {p3, p4}, Lcom/android/tools/r8/graph/x2;->a(Z)I

    move-result p3

    const/16 p4, 0x1e

    if-gt p3, p4, :cond_9

    .line 86
    new-instance p4, Lcom/android/tools/r8/internal/BD;

    invoke-direct {p4}, Lcom/android/tools/r8/internal/BD;-><init>()V

    goto :goto_3

    :cond_9
    new-instance p4, Lcom/android/tools/r8/internal/WD;

    const/16 v0, 0x10

    .line 87
    invoke-direct {p4, v0}, Lcom/android/tools/r8/internal/WD;-><init>(I)V

    :goto_3
    const/4 v0, -0x1

    .line 88
    iput v0, p4, Lcom/android/tools/r8/internal/J;->b:I

    .line 89
    new-array v0, p3, [Z

    move v1, p2

    :goto_4
    if-ge v1, p3, :cond_d

    .line 90
    invoke-virtual {p6}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p6}, Lcom/android/tools/r8/graph/j1;->y0()Z

    move-result v4

    invoke-virtual {v3, v1, v4}, Lcom/android/tools/r8/graph/x2;->a(IZ)Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    move v4, p2

    :goto_5
    if-ge v4, p3, :cond_c

    .line 91
    invoke-virtual {p6}, Lcom/android/tools/r8/graph/j1;->R0()Lcom/android/tools/r8/graph/H4;

    move-result-object v5

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/g;->o()Z

    move-result v5

    .line 92
    invoke-virtual {p1, v4, v5}, Lcom/android/tools/r8/graph/x2;->a(IZ)Lcom/android/tools/r8/graph/J2;

    move-result-object v5

    if-ne v3, v5, :cond_b

    .line 94
    aget-boolean v5, v0, v4

    if-nez v5, :cond_b

    if-eq v1, v4, :cond_a

    .line 95
    invoke-interface {p4, v1, v4}, Lcom/android/tools/r8/internal/CD;->b(II)I

    goto :goto_6

    .line 97
    :cond_a
    invoke-interface {p4, v1}, Lcom/android/tools/r8/internal/CD;->remove(I)I

    .line 98
    :goto_6
    aput-boolean v2, v0, v4

    goto :goto_7

    :cond_b
    add-int/lit8 v4, v4, 0x1

    goto :goto_5

    :cond_c
    :goto_7
    add-int/lit8 v1, v1, 0x1

    goto :goto_4

    .line 99
    :cond_d
    invoke-interface {p4}, Ljava/util/Map;->isEmpty()Z

    move-result p2

    if-eqz p2, :cond_e

    .line 100
    sget-object p2, Lcom/android/tools/r8/graph/proto/f;->a:Lcom/android/tools/r8/graph/proto/f;

    goto :goto_8

    .line 102
    :cond_e
    new-instance p2, Lcom/android/tools/r8/graph/proto/e;

    invoke-direct {p2, p4}, Lcom/android/tools/r8/graph/proto/e;-><init>(Lcom/android/tools/r8/internal/J;)V

    .line 103
    :goto_8
    sget-boolean p4, Lcom/android/tools/r8/internal/A80;->d:Z

    if-nez p4, :cond_10

    .line 104
    instance-of p4, p2, Lcom/android/tools/r8/graph/proto/f;

    if-nez p4, :cond_f

    goto :goto_9

    .line 105
    :cond_f
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 107
    :cond_10
    :goto_9
    invoke-static {}, Lcom/android/tools/r8/graph/proto/c;->a()Lcom/android/tools/r8/graph/proto/c$a;

    move-result-object p4

    .line 108
    invoke-virtual {p4, p3}, Lcom/android/tools/r8/graph/proto/c$a;->a(I)Lcom/android/tools/r8/graph/proto/c$a;

    move-result-object p3

    .line 109
    iput-object p2, p3, Lcom/android/tools/r8/graph/proto/c$a;->c:Lcom/android/tools/r8/graph/proto/d;

    .line 110
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/c$a;->a()Lcom/android/tools/r8/graph/proto/c;

    move-result-object p2

    .line 111
    sget p3, Lcom/android/tools/r8/internal/cB;->c:I

    .line 112
    sget-object p3, Lcom/android/tools/r8/internal/uc0;->e:Lcom/android/tools/r8/internal/uc0;

    const/4 p4, 0x0

    .line 113
    invoke-static {p3, p4, p2}, Lcom/android/tools/r8/graph/proto/j;->a(Ljava/util/List;Lcom/android/tools/r8/graph/proto/k;Lcom/android/tools/r8/graph/proto/c;)Lcom/android/tools/r8/graph/proto/j;

    move-result-object p2

    .line 114
    iget-object p3, p5, Lcom/android/tools/r8/internal/A80;->c:Ljava/util/IdentityHashMap;

    invoke-virtual {p3, p1, p2}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_a

    .line 115
    :cond_11
    sget-object p2, Lcom/android/tools/r8/graph/proto/j;->d:Lcom/android/tools/r8/graph/proto/j;

    .line 116
    :goto_a
    iget-object p3, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    iget-object p3, p3, Lcom/android/tools/r8/internal/z80;->b:Lcom/android/tools/r8/graph/B1;

    new-instance p4, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda0;

    invoke-direct {p4, p2, p6}, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/proto/j;Lcom/android/tools/r8/graph/j1;)V

    invoke-virtual {p6, p1, p3, p4}, Lcom/android/tools/r8/graph/j1;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/B1;Ljava/util/function/Consumer;)Lcom/android/tools/r8/graph/j1;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/dj;Ljava/util/List;)Lcom/android/tools/r8/internal/fq0;
    .locals 9

    .line 1
    iget-object v0, p1, Lcom/android/tools/r8/internal/cj;->a:Ljava/lang/Object;

    .line 2
    move-object v3, v0

    check-cast v3, Lcom/android/tools/r8/graph/E0;

    .line 3
    sget-boolean v0, Lcom/android/tools/r8/internal/w80;->i:Z

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-nez v0, :cond_3

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v4

    if-ge v4, v2, :cond_3

    .line 4
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v4

    iget-object v5, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    iget-object v6, v5, Lcom/android/tools/r8/internal/z80;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v6, v6, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    if-eq v4, v6, :cond_3

    iget-object v4, v5, Lcom/android/tools/r8/internal/z80;->a:Lcom/android/tools/r8/graph/y;

    .line 5
    invoke-virtual {v4}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/j;

    .line 6
    iget-object v5, v3, Lcom/android/tools/r8/graph/E0;->g:Lcom/android/tools/r8/graph/J2;

    if-eqz v5, :cond_0

    invoke-virtual {v4, v5}, Lcom/android/tools/r8/graph/j;->i(Lcom/android/tools/r8/graph/J2;)Z

    move-result v5

    if-eqz v5, :cond_0

    goto :goto_1

    .line 9
    :cond_0
    iget-object v5, v3, Lcom/android/tools/r8/graph/E0;->h:Lcom/android/tools/r8/graph/L2;

    iget-object v5, v5, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    array-length v6, v5

    move v7, v1

    :goto_0
    if-ge v7, v6, :cond_2

    aget-object v8, v5, v7

    .line 10
    invoke-virtual {v4, v8}, Lcom/android/tools/r8/graph/j;->i(Lcom/android/tools/r8/graph/J2;)Z

    move-result v8

    if-eqz v8, :cond_1

    goto :goto_1

    :cond_1
    add-int/lit8 v7, v7, 0x1

    goto :goto_0

    .line 11
    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_3
    :goto_1
    if-nez v0, :cond_5

    .line 15
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v0

    if-le v0, v2, :cond_5

    iget-object v0, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    iget-object v0, v0, Lcom/android/tools/r8/internal/z80;->c:Lcom/android/tools/r8/utils/w;

    iget-boolean v0, v0, Lcom/android/tools/r8/utils/w;->R0:Z

    if-eqz v0, :cond_4

    goto :goto_2

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 16
    :cond_5
    :goto_2
    iget-object p1, p1, Lcom/android/tools/r8/internal/dj;->d:Ljava/lang/Object;

    .line 17
    check-cast p1, Lcom/android/tools/r8/internal/y80;

    .line 18
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_6

    .line 19
    invoke-interface {p2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/internal/dj;

    .line 20
    iget-object p2, p2, Lcom/android/tools/r8/internal/dj;->d:Ljava/lang/Object;

    .line 21
    check-cast p2, Lcom/android/tools/r8/internal/y80;

    .line 22
    iget-object v0, p1, Lcom/android/tools/r8/internal/y80;->a:Ljava/util/ArrayList;

    .line 23
    invoke-virtual {v0, p2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 24
    :cond_6
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/b1;->g0()Z

    move-result p2

    if-eqz p2, :cond_7

    .line 25
    iget-object p2, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    .line 26
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/b1;->f0()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/w80;->f:Lcom/android/tools/r8/internal/x80;

    .line 27
    invoke-virtual {p2, v0, p1, v1}, Lcom/android/tools/r8/internal/z80;->b(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/internal/y80;Lcom/android/tools/r8/internal/x80;)Ljava/util/Map;

    move-result-object p2

    goto :goto_3

    :cond_7
    const/4 p2, 0x0

    :goto_3
    move-object v5, p2

    .line 31
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/E0;->d0()Lcom/android/tools/r8/graph/J4;

    move-result-object p2

    iget-object v6, p0, Lcom/android/tools/r8/internal/w80;->f:Lcom/android/tools/r8/internal/x80;

    iget-object v7, p0, Lcom/android/tools/r8/internal/w80;->g:Lcom/android/tools/r8/internal/A80;

    new-instance v0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;

    move-object v1, v0

    move-object v2, p0

    move-object v4, p1

    invoke-direct/range {v1 .. v7}, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/internal/w80;Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/internal/y80;Ljava/util/Map;Lcom/android/tools/r8/internal/x80;Lcom/android/tools/r8/internal/A80;)V

    .line 32
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 33
    sget-object v1, Lcom/android/tools/r8/graph/j1;->v:Lcom/android/tools/r8/graph/j1;

    iput-object v1, p2, Lcom/android/tools/r8/graph/J4;->c:Lcom/android/tools/r8/graph/j1;

    .line 34
    iget-object p2, p2, Lcom/android/tools/r8/graph/J4;->b:Lcom/android/tools/r8/graph/K4;

    invoke-virtual {p2, v0}, Lcom/android/tools/r8/graph/K4;->e(Ljava/util/function/Function;)V

    .line 35
    new-instance p2, Lcom/android/tools/r8/internal/fq0;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/fq0;-><init>(Ljava/lang/Object;)V

    return-object p2
.end method

.method public final a(Lcom/android/tools/r8/internal/dj;Ljava/util/function/Function;)Lcom/android/tools/r8/internal/gq0;
    .locals 2

    .line 36
    iget-object v0, p1, Lcom/android/tools/r8/internal/cj;->a:Ljava/lang/Object;

    .line 37
    check-cast v0, Lcom/android/tools/r8/graph/E0;

    .line 38
    new-instance v1, Lcom/android/tools/r8/internal/y80;

    invoke-direct {v1}, Lcom/android/tools/r8/internal/y80;-><init>()V

    .line 39
    iput-object v1, p1, Lcom/android/tools/r8/internal/dj;->d:Ljava/lang/Object;

    .line 40
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->d1()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 41
    iget-object p1, p0, Lcom/android/tools/r8/internal/w80;->h:Lcom/android/tools/r8/internal/z80;

    iget-object p1, p1, Lcom/android/tools/r8/internal/z80;->a:Lcom/android/tools/r8/graph/y;

    .line 42
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->d1()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    .line 43
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p1

    .line 44
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/h;->f(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/b0;

    move-result-object p1

    .line 45
    invoke-static {p2}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda1;

    invoke-direct {v0, p2}, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda1;-><init>(Ljava/util/function/Function;)V

    invoke-interface {p1, v0}, Lcom/android/tools/r8/graph/b0;->a(Ljava/util/function/Consumer;)V

    .line 47
    :cond_0
    sget-object p1, Lcom/android/tools/r8/internal/fq0;->c:Lcom/android/tools/r8/internal/eq0;

    return-object p1
.end method
