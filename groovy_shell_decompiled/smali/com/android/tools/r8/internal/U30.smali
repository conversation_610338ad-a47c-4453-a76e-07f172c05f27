.class public abstract Lcom/android/tools/r8/internal/U30;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic a:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/rD;)I
    .locals 3

    .line 24
    iget-object v0, p0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 25
    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    const/4 v1, 0x0

    :goto_0
    if-ltz v0, :cond_1

    .line 26
    iget-object v2, p0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 27
    invoke-virtual {v2, v0}, Lja<PERSON>/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/vt0;->V()Z

    move-result v2

    if-eqz v2, :cond_0

    add-int/lit8 v1, v1, 0x1

    :cond_0
    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_1
    return v1
.end method

.method public static a(Ljava/util/function/Predicate;)Ljava/util/function/Predicate;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/U30$$ExternalSyntheticLambda2;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/U30$$ExternalSyntheticLambda2;-><init>(Ljava/util/function/Predicate;)V

    return-object v0
.end method

.method public static a(Lcom/android/tools/r8/internal/AQ;I)V
    .locals 2

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p1, :cond_0

    .line 5
    invoke-interface {p0}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/rD;

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/AQ;Ljava/util/List;)V
    .locals 5

    .line 6
    sget-boolean v0, Lcom/android/tools/r8/internal/U30;->a:Z

    if-nez v0, :cond_1

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 7
    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/rD;

    .line 8
    iget-object v2, v1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 9
    invoke-virtual {v2}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/vt0;

    .line 10
    iget-object v4, v3, Lcom/android/tools/r8/internal/vt0;->d:Ljava/util/LinkedList;

    .line 11
    invoke-virtual {v4, v1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    const/4 v4, 0x0

    .line 12
    iput-object v4, v3, Lcom/android/tools/r8/internal/vt0;->e:Lcom/android/tools/r8/internal/LB;

    goto :goto_2

    .line 13
    :cond_2
    invoke-interface {p0, v1}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    goto :goto_1

    .line 15
    :cond_3
    new-instance v0, Lcom/android/tools/r8/internal/U30$$ExternalSyntheticLambda0;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/U30$$ExternalSyntheticLambda0;-><init>(Ljava/util/List;)V

    invoke-interface {p0, v0}, Lcom/android/tools/r8/internal/JX;->a(Ljava/util/function/Predicate;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/rD;

    const/4 v1, 0x0

    .line 16
    :goto_3
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_6

    .line 17
    sget-boolean v2, Lcom/android/tools/r8/internal/U30;->a:Z

    if-nez v2, :cond_5

    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    if-ne v0, v2, :cond_4

    goto :goto_4

    :cond_4
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 18
    :cond_5
    :goto_4
    invoke-interface {p0}, Lcom/android/tools/r8/internal/uD;->i()V

    .line 19
    invoke-interface {p0}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/rD;

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    .line 21
    :cond_6
    new-instance v0, Lcom/android/tools/r8/internal/U30$$ExternalSyntheticLambda1;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/U30$$ExternalSyntheticLambda1;-><init>(Ljava/util/List;)V

    invoke-interface {p0, v0}, Lcom/android/tools/r8/internal/r50;->d(Ljava/util/function/Predicate;)V

    .line 22
    invoke-interface {p0}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    return-void
.end method

.method public static synthetic a(Ljava/util/List;Lcom/android/tools/r8/internal/rD;)Z
    .locals 1

    const/4 v0, 0x0

    .line 23
    invoke-interface {p0, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    if-ne p1, p0, :cond_0

    const/4 v0, 0x1

    :cond_0
    return v0
.end method

.method public static synthetic a(Ljava/util/function/Predicate;Lcom/android/tools/r8/internal/rD;)Z
    .locals 0

    .line 2
    invoke-interface {p0, p1}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_1

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->b1()Z

    move-result p0

    if-nez p0, :cond_1

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object p0

    if-eqz p0, :cond_0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->A()Z

    move-result p0

    if-nez p0, :cond_1

    :cond_0
    const/4 p0, 0x1

    goto :goto_0

    :cond_1
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static b(Lcom/android/tools/r8/internal/rD;)I
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object p0

    .line 2
    instance-of v0, p0, Lcom/android/tools/r8/internal/Hl0;

    if-eqz v0, :cond_0

    .line 3
    check-cast p0, Lcom/android/tools/r8/internal/Hl0;

    .line 4
    iget-object p0, p0, Lcom/android/tools/r8/internal/Hl0;->r:[Lcom/android/tools/r8/internal/Gl0;

    .line 5
    array-length p0, p0

    return p0

    :cond_0
    if-eqz p0, :cond_1

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->V()Z

    move-result p0

    if-eqz p0, :cond_1

    const/4 p0, 0x1

    return p0

    :cond_1
    const/4 p0, 0x0

    return p0
.end method

.method public static synthetic b(Ljava/util/List;Lcom/android/tools/r8/internal/rD;)Z
    .locals 2

    .line 7
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    invoke-interface {p0, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    if-ne p1, p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    return v1
.end method
