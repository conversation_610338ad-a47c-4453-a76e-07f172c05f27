.class public final synthetic Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/O40;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/Fy;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/O40;

    iput-object p2, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda1;->f$1:Lcom/android/tools/r8/internal/Fy;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/O40;

    iget-object v1, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda1;->f$1:Lcom/android/tools/r8/internal/Fy;

    check-cast p1, Ljava/util/List;

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/u3;->a(Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Ljava/util/List;)V

    return-void
.end method
