.class public final Lcom/android/tools/r8/internal/Va;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic e:Z


# instance fields
.field public final a:I

.field public final b:Lcom/android/tools/r8/internal/It0;

.field public final c:Lcom/android/tools/r8/graph/J2;

.field public final d:Lcom/android/tools/r8/internal/Ya;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/internal/bb;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/internal/Va;->e:Z

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Ya;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Lcom/android/tools/r8/internal/Va;->a:I

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/Va;->d:Lcom/android/tools/r8/internal/Ya;

    .line 4
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Ya;->a()Lcom/android/tools/r8/internal/It0;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/Va;->b:Lcom/android/tools/r8/internal/It0;

    .line 5
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Ya;->b()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/Va;->c:Lcom/android/tools/r8/graph/J2;

    return-void
.end method

.method public static a(I)Z
    .locals 1

    const v0, 0x186a0

    if-lt p0, v0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static b(I)I
    .locals 2

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/Va;->e:Z

    if-nez v0, :cond_1

    invoke-static {p0}, Lcom/android/tools/r8/internal/Va;->a(I)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_1
    :goto_0
    const v1, 0x186a0

    if-nez v0, :cond_3

    if-lt p0, v1, :cond_2

    goto :goto_1

    .line 2
    :cond_2
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_3
    :goto_1
    sub-int/2addr p0, v1

    return p0
.end method


# virtual methods
.method public final a()Z
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/Va;->d:Lcom/android/tools/r8/internal/Ya;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    instance-of v0, v0, Lcom/android/tools/r8/internal/Xa;

    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 5

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/Va;->a:I

    const-string v1, "="

    const v2, 0x186a0

    if-ge v0, v2, :cond_0

    .line 2
    iget-object v2, p0, Lcom/android/tools/r8/internal/Va;->d:Lcom/android/tools/r8/internal/Ya;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    sub-int/2addr v0, v2

    .line 3
    iget-object v2, p0, Lcom/android/tools/r8/internal/Va;->d:Lcom/android/tools/r8/internal/Ya;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "s"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    :goto_0
    return-object v0
.end method
