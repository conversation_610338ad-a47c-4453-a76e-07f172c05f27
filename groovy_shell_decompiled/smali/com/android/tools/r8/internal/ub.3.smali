.class public final Lcom/android/tools/r8/internal/ub;
.super Lcom/android/tools/r8/internal/ik0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final c:Lcom/android/tools/r8/internal/ub;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/ub;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/ub;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/ub;->c:Lcom/android/tools/r8/internal/ub;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/ik0;-><init>()V

    return-void
.end method


# virtual methods
.method public final J()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final a(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 1
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->z1:Lcom/android/tools/r8/graph/J2;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/naming/r0;)Ljava/lang/Object;
    .locals 0

    .line 2
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "Unexpected value type: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final getTypeName()Ljava/lang/String;
    .locals 1

    const-string v0, "char"

    return-object v0
.end method
