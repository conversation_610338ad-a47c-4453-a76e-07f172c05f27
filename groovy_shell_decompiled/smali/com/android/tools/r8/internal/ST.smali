.class public final Lcom/android/tools/r8/internal/ST;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final c:Lcom/android/tools/r8/internal/ST;


# instance fields
.field public final a:Lcom/android/tools/r8/shaking/f2;

.field public final b:Ljava/util/List;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/ST;

    .line 2
    sget-object v1, Lcom/android/tools/r8/shaking/f2;->b:Lcom/android/tools/r8/shaking/f2;

    .line 3
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/ST;-><init>(Lcom/android/tools/r8/shaking/f2;Ljava/util/List;)V

    sput-object v0, Lcom/android/tools/r8/internal/ST;->c:Lcom/android/tools/r8/internal/ST;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/shaking/f2;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/ST;->a:Lcom/android/tools/r8/shaking/f2;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/ST;->b:Ljava/util/List;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/K5;Lcom/android/tools/r8/internal/RT;)Z
    .locals 1

    .line 1
    iget-object v0, p1, Lcom/android/tools/r8/internal/RT;->b:Lcom/android/tools/r8/shaking/f2;

    .line 2
    invoke-virtual {v0, p0}, Lcom/android/tools/r8/shaking/f2;->a(Lcom/android/tools/r8/graph/K5;)V

    .line 3
    iget-object p0, p1, Lcom/android/tools/r8/internal/RT;->b:Lcom/android/tools/r8/shaking/f2;

    .line 4
    iget-object p0, p0, Lcom/android/tools/r8/shaking/f2;->a:Ljava/util/Map;

    .line 5
    invoke-interface {p0}, Ljava/util/Map;->isEmpty()Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/X2;
    .locals 4

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/ST;->a:Lcom/android/tools/r8/shaking/f2;

    .line 7
    iget-object v0, v0, Lcom/android/tools/r8/shaking/f2;->a:Ljava/util/Map;

    .line 8
    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/internal/ST;->b:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 10
    sget-object v0, Lcom/android/tools/r8/internal/W2;->a:Lcom/android/tools/r8/internal/W2;

    return-object v0

    .line 12
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/Y2;

    iget-object v1, p0, Lcom/android/tools/r8/internal/ST;->a:Lcom/android/tools/r8/shaking/f2;

    iget-object v2, p0, Lcom/android/tools/r8/internal/ST;->b:Ljava/util/List;

    sget-object v3, Lcom/android/tools/r8/internal/ST$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/ST$$ExternalSyntheticLambda0;

    .line 14
    invoke-static {v2, v3}, Lcom/android/tools/r8/internal/QR;->a(Ljava/util/Collection;Ljava/util/function/Function;)Ljava/util/List;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/Y2;-><init>(Lcom/android/tools/r8/shaking/f2;Ljava/util/List;)V

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/graph/K5;)V
    .locals 2

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/internal/ST;->a:Lcom/android/tools/r8/shaking/f2;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/shaking/f2;->a(Lcom/android/tools/r8/graph/K5;)V

    .line 16
    iget-object v0, p0, Lcom/android/tools/r8/internal/ST;->b:Ljava/util/List;

    new-instance v1, Lcom/android/tools/r8/internal/ST$$ExternalSyntheticLambda1;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/internal/ST$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/graph/K5;)V

    invoke-interface {v0, v1}, Ljava/util/List;->removeIf(Ljava/util/function/Predicate;)Z

    return-void
.end method
