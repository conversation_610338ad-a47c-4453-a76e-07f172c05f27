.class public final Lcom/android/tools/r8/internal/sQ;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/internal/iB;

.field public final b:Lcom/android/tools/r8/internal/LB;

.field public final c:Lcom/android/tools/r8/internal/LB;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 5

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    .line 3
    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/sQ;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/iB;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/sQ;->a:Lcom/android/tools/r8/internal/iB;

    .line 4
    sget p1, Lcom/android/tools/r8/internal/LB;->c:I

    .line 5
    new-instance p1, Lcom/android/tools/r8/internal/DB;

    invoke-direct {p1}, Lcom/android/tools/r8/internal/DB;-><init>()V

    .line 6
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->m4:Lcom/android/tools/r8/graph/B1$a;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$a;->d:Lcom/android/tools/r8/graph/x2;

    .line 7
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->m4:Lcom/android/tools/r8/graph/B1$a;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$a;->g:Lcom/android/tools/r8/graph/x2;

    .line 8
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->m4:Lcom/android/tools/r8/graph/B1$a;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$a;->f:Lcom/android/tools/r8/graph/x2;

    .line 9
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->n4:Lcom/android/tools/r8/graph/F1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/F1;->b:Lcom/android/tools/r8/graph/x2;

    .line 10
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->n4:Lcom/android/tools/r8/graph/F1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/F1;->c:Lcom/android/tools/r8/graph/x2;

    .line 11
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->n4:Lcom/android/tools/r8/graph/F1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/F1;->d:Lcom/android/tools/r8/graph/x2;

    .line 12
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->D4:Lcom/android/tools/r8/graph/H1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/H1;->a:Lcom/android/tools/r8/graph/x2;

    .line 13
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->o4:Lcom/android/tools/r8/graph/G1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/G1;->b:Lcom/android/tools/r8/graph/x2;

    .line 14
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->o4:Lcom/android/tools/r8/graph/G1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/G1;->c:Lcom/android/tools/r8/graph/x2;

    .line 15
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->o4:Lcom/android/tools/r8/graph/G1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/G1;->d:Lcom/android/tools/r8/graph/x2;

    .line 16
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->A4:Lcom/android/tools/r8/graph/J1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/J1;->b:Lcom/android/tools/r8/graph/x2;

    .line 17
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->A4:Lcom/android/tools/r8/graph/J1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/J1;->d:Lcom/android/tools/r8/graph/x2;

    .line 18
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->A4:Lcom/android/tools/r8/graph/J1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/J1;->e:Lcom/android/tools/r8/graph/x2;

    .line 19
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->G4:Lcom/android/tools/r8/graph/K1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/K1;->k:Lcom/android/tools/r8/graph/x2;

    .line 20
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->p4:Lcom/android/tools/r8/graph/L1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/L1;->b:Lcom/android/tools/r8/graph/x2;

    .line 21
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->p4:Lcom/android/tools/r8/graph/L1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/L1;->c:Lcom/android/tools/r8/graph/x2;

    .line 22
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->p4:Lcom/android/tools/r8/graph/L1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/L1;->d:Lcom/android/tools/r8/graph/x2;

    .line 23
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->q4:Lcom/android/tools/r8/graph/O1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/O1;->b:Lcom/android/tools/r8/graph/x2;

    .line 24
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->q4:Lcom/android/tools/r8/graph/O1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/O1;->c:Lcom/android/tools/r8/graph/x2;

    .line 25
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->q4:Lcom/android/tools/r8/graph/O1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/O1;->d:Lcom/android/tools/r8/graph/x2;

    .line 26
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->r4:Lcom/android/tools/r8/graph/c2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/c2;->b:Lcom/android/tools/r8/graph/x2;

    .line 27
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->r4:Lcom/android/tools/r8/graph/c2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/c2;->c:Lcom/android/tools/r8/graph/x2;

    .line 28
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->r4:Lcom/android/tools/r8/graph/c2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/c2;->d:Lcom/android/tools/r8/graph/x2;

    .line 29
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->K4:Lcom/android/tools/r8/graph/e2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/e2;->a:Lcom/android/tools/r8/graph/x2;

    .line 30
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->K4:Lcom/android/tools/r8/graph/e2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/e2;->b:Lcom/android/tools/r8/graph/x2;

    .line 31
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->w4:Lcom/android/tools/r8/graph/k2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/k2;->a:Lcom/android/tools/r8/graph/x2;

    .line 32
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->u4:Lcom/android/tools/r8/graph/B1$b;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$b;->f:Lcom/android/tools/r8/graph/x2;

    .line 33
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->u4:Lcom/android/tools/r8/graph/B1$b;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$b;->d:Lcom/android/tools/r8/graph/x2;

    .line 34
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->t4:Lcom/android/tools/r8/graph/g2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/g2;->d:Lcom/android/tools/r8/graph/x2;

    .line 35
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->t4:Lcom/android/tools/r8/graph/g2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/g2;->e:Lcom/android/tools/r8/graph/x2;

    .line 36
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->x4:Lcom/android/tools/r8/graph/m2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/m2;->b:Lcom/android/tools/r8/graph/x2;

    .line 37
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->x4:Lcom/android/tools/r8/graph/m2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/m2;->c:Lcom/android/tools/r8/graph/x2;

    .line 38
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->x4:Lcom/android/tools/r8/graph/m2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/m2;->d:Lcom/android/tools/r8/graph/x2;

    .line 39
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->l4:Lcom/android/tools/r8/graph/n2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/n2;->r:Lcom/android/tools/r8/graph/x2;

    .line 40
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->k4:Lcom/android/tools/r8/graph/n2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/n2;->r:Lcom/android/tools/r8/graph/x2;

    .line 41
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->y4:Lcom/android/tools/r8/graph/B1$c;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$c;->c:Lcom/android/tools/r8/graph/x2;

    .line 42
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->y4:Lcom/android/tools/r8/graph/B1$c;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$c;->x:Lcom/android/tools/r8/graph/x2;

    .line 43
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->y4:Lcom/android/tools/r8/graph/B1$c;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$c;->b:Lcom/android/tools/r8/graph/x2;

    .line 44
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->y4:Lcom/android/tools/r8/graph/B1$c;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$c;->B:Lcom/android/tools/r8/graph/x2;

    .line 45
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->y4:Lcom/android/tools/r8/graph/B1$c;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$c;->C:Lcom/android/tools/r8/graph/x2;

    .line 46
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->D4:Lcom/android/tools/r8/graph/H1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/H1;->p:Lcom/android/tools/r8/internal/LB;

    .line 47
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/RA;->a(Ljava/lang/Iterable;)V

    .line 48
    iget-object v1, v0, Lcom/android/tools/r8/graph/B1;->D3:Lcom/android/tools/r8/graph/x2;

    .line 50
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    move-result-object p1

    .line 51
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/DB;->a()Lcom/android/tools/r8/internal/LB;

    move-result-object p1

    .line 52
    iput-object p1, p0, Lcom/android/tools/r8/internal/sQ;->b:Lcom/android/tools/r8/internal/LB;

    .line 53
    iget-object p1, v0, Lcom/android/tools/r8/graph/B1;->u4:Lcom/android/tools/r8/graph/B1$b;

    iget-object v0, p1, Lcom/android/tools/r8/graph/B1$b;->c:Lcom/android/tools/r8/graph/x2;

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1$b;->e:Lcom/android/tools/r8/graph/x2;

    iget-object p1, p1, Lcom/android/tools/r8/graph/B1$b;->h:Lcom/android/tools/r8/graph/x2;

    const/4 v2, 0x3

    new-array v3, v2, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object v0, v3, v4

    const/4 v0, 0x1

    aput-object v1, v3, v0

    const/4 v0, 0x2

    aput-object p1, v3, v0

    .line 54
    invoke-static {v2, v2, v3}, Lcom/android/tools/r8/internal/LB;->a(II[Ljava/lang/Object;)Lcom/android/tools/r8/internal/LB;

    move-result-object p1

    .line 55
    iput-object p1, p0, Lcom/android/tools/r8/internal/sQ;->c:Lcom/android/tools/r8/internal/LB;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/iB;
    .locals 3

    .line 1
    invoke-static {}, Lcom/android/tools/r8/internal/iB;->e()Lcom/android/tools/r8/internal/fB;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->n4:Lcom/android/tools/r8/graph/F1;

    iget-object v1, v1, Lcom/android/tools/r8/graph/F1;->b:Lcom/android/tools/r8/graph/x2;

    sget-object v2, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda3;->INSTANCE:Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda3;

    .line 2
    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->t4:Lcom/android/tools/r8/graph/g2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/g2;->k:Lcom/android/tools/r8/graph/x2;

    new-instance v2, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda1;

    invoke-direct {v2, p0}, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 5
    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->y4:Lcom/android/tools/r8/graph/B1$c;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$c;->e:Lcom/android/tools/r8/graph/x2;

    sget-object v2, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda4;

    .line 10
    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->y4:Lcom/android/tools/r8/graph/B1$c;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1$c;->A:Lcom/android/tools/r8/graph/x2;

    new-instance v2, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda2;

    invoke-direct {v2, p0}, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 13
    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    move-result-object p0

    .line 18
    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->l4:Lcom/android/tools/r8/graph/n2;

    iget-object v1, v0, Lcom/android/tools/r8/graph/n2;->u:Lcom/android/tools/r8/internal/LB;

    .line 21
    new-instance v2, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda0;

    invoke-direct {v2, v0}, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/n2;)V

    .line 22
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    .line 23
    invoke-virtual {p0, v1, v2}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    goto :goto_0

    .line 24
    :cond_0
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->k4:Lcom/android/tools/r8/graph/n2;

    iget-object v0, p1, Lcom/android/tools/r8/graph/n2;->u:Lcom/android/tools/r8/internal/LB;

    .line 27
    new-instance v1, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda0;

    invoke-direct {v1, p1}, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/n2;)V

    .line 28
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    .line 29
    invoke-virtual {p0, v0, v1}, Lcom/android/tools/r8/internal/fB;->a(Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/fB;

    goto :goto_1

    .line 30
    :cond_1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/fB;->a()Lcom/android/tools/r8/internal/iB;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/x2;Ljava/util/List;)Z
    .locals 0

    .line 31
    invoke-static {p0, p2}, Lcom/android/tools/r8/internal/nJ;->a(Lcom/android/tools/r8/graph/y;Ljava/util/List;)Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static synthetic b(Lcom/android/tools/r8/graph/x2;Ljava/util/List;)Z
    .locals 0

    const/4 p0, 0x0

    .line 1
    invoke-interface {p1, p0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->S()Z

    move-result p0

    return p0
.end method

.method public static synthetic b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/x2;Ljava/util/List;)Z
    .locals 0

    .line 2
    invoke-static {p0, p2}, Lcom/android/tools/r8/internal/nJ;->a(Lcom/android/tools/r8/graph/y;Ljava/util/List;)Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static synthetic c(Lcom/android/tools/r8/graph/x2;Ljava/util/List;)Z
    .locals 0

    const/4 p0, 0x1

    .line 1
    invoke-interface {p1, p0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vt0;->S()Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final a(Ljava/util/function/Consumer;)V
    .locals 1

    .line 32
    iget-object v0, p0, Lcom/android/tools/r8/internal/sQ;->b:Lcom/android/tools/r8/internal/LB;

    invoke-interface {v0, p1}, Ljava/util/Set;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;Ljava/util/List;)Z
    .locals 2

    .line 33
    iget-object v0, p0, Lcom/android/tools/r8/internal/sQ;->b:Lcom/android/tools/r8/internal/LB;

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/android/tools/r8/internal/sQ;->a:Lcom/android/tools/r8/internal/iB;

    .line 35
    invoke-static {}, Lcom/android/tools/r8/internal/W5;->a()Ljava/util/function/BiPredicate;

    move-result-object v1

    .line 36
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/iB;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    move-object v1, v0

    .line 37
    :cond_0
    check-cast v1, Ljava/util/function/BiPredicate;

    .line 38
    invoke-interface {v1, p1, p2}, Ljava/util/function/BiPredicate;->test(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    goto :goto_1

    :cond_2
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public final a(Lcom/android/tools/r8/internal/uI;Lcom/android/tools/r8/graph/w4;)Z
    .locals 1

    .line 39
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    .line 40
    iget-object p1, p1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 41
    invoke-virtual {p0, v0, p1}, Lcom/android/tools/r8/internal/sQ;->a(Lcom/android/tools/r8/graph/x2;Ljava/util/List;)Z

    move-result p1

    if-nez p1, :cond_1

    iget-object p1, p0, Lcom/android/tools/r8/internal/sQ;->c:Lcom/android/tools/r8/internal/LB;

    .line 42
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method
