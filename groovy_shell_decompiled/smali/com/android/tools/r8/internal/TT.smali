.class public final Lcom/android/tools/r8/internal/TT;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/UT;


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/graph/j0;

.field public final synthetic c:Lcom/android/tools/r8/internal/sr0;

.field public final synthetic d:Lcom/android/tools/r8/internal/B40;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/j0;Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/internal/B40;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/TT;->b:Lcom/android/tools/r8/graph/j0;

    iput-object p2, p0, Lcom/android/tools/r8/internal/TT;->c:Lcom/android/tools/r8/internal/sr0;

    iput-object p3, p0, Lcom/android/tools/r8/internal/TT;->d:Lcom/android/tools/r8/internal/B40;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/sr0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TT;->c:Lcom/android/tools/r8/internal/sr0;

    return-object v0
.end method

.method public final getPosition()Lcom/android/tools/r8/internal/B40;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TT;->d:Lcom/android/tools/r8/internal/B40;

    return-object v0
.end method

.method public final o()Lcom/android/tools/r8/graph/j0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/TT;->b:Lcom/android/tools/r8/graph/j0;

    return-object v0
.end method
