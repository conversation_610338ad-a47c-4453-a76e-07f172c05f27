.class public final Lcom/android/tools/r8/internal/v80;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/graph/D5;

.field public final b:I

.field public final c:Ljava/util/LinkedList;

.field public final d:Ljava/util/LinkedList;

.field public final e:Ljava/util/LinkedList;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/D5;ILjava/util/LinkedList;Ljava/util/LinkedList;Ljava/util/LinkedList;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/v80;->a:Lcom/android/tools/r8/graph/D5;

    .line 3
    iput p2, p0, Lcom/android/tools/r8/internal/v80;->b:I

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/internal/v80;->c:Ljava/util/LinkedList;

    .line 5
    iput-object p4, p0, Lcom/android/tools/r8/internal/v80;->d:Ljava/util/LinkedList;

    .line 6
    iput-object p5, p0, Lcom/android/tools/r8/internal/v80;->e:Ljava/util/LinkedList;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/internal/t80;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/t80;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/t80;-><init>(Lcom/android/tools/r8/graph/D5;)V

    return-object v0
.end method


# virtual methods
.method public final a()Z
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/v80;->c:Ljava/util/LinkedList;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/util/AbstractCollection;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 4

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "ProtoMessageInfo(fields=["

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/v80;->a()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/internal/v80;->c:Ljava/util/LinkedList;

    invoke-virtual {v1}, Ljava/util/AbstractCollection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    .line 4
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 5
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    const-string v2, ", "

    .line 6
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    goto :goto_0

    :cond_0
    const-string v1, "])"

    .line 9
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
