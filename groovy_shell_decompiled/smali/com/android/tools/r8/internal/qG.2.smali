.class public final Lcom/android/tools/r8/internal/qG;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/ho0;


# static fields
.field public static final synthetic d:Z = true


# instance fields
.field public final b:[I

.field public final c:Lcom/android/tools/r8/internal/cB;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>([ILcom/android/tools/r8/internal/cB;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/qG;->b:[I

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/qG;->c:Lcom/android/tools/r8/internal/cB;

    .line 4
    sget-boolean v0, Lcom/android/tools/r8/internal/qG;->d:Z

    if-nez v0, :cond_1

    array-length p1, p1

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    if-ne p1, p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/ko0;)V
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/qG$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/qG$$ExternalSyntheticLambda1;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ko0;->d(Ljava/util/function/Function;)Lcom/android/tools/r8/internal/ko0;

    move-result-object p0

    sget-object v0, Lcom/android/tools/r8/internal/qG$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/internal/qG$$ExternalSyntheticLambda2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ko0;->h(Ljava/util/function/Function;)Lcom/android/tools/r8/internal/ko0;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/qG;)[I
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/android/tools/r8/internal/qG;->b:[I

    return-object p0
.end method

.method public static synthetic b(Lcom/android/tools/r8/internal/qG;)Ljava/util/Collection;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/android/tools/r8/internal/qG;->c:Lcom/android/tools/r8/internal/cB;

    return-object p0
.end method


# virtual methods
.method public final E()Lcom/android/tools/r8/internal/ho0;
    .locals 0

    return-object p0
.end method

.method public final a(Ljava/util/function/BiConsumer;)V
    .locals 3

    const/4 v0, 0x0

    .line 3
    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/qG;->b:[I

    array-length v2, v1

    if-ge v0, v2, :cond_0

    .line 4
    aget v1, v1, v0

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    iget-object v2, p0, Lcom/android/tools/r8/internal/qG;->c:Lcom/android/tools/r8/internal/cB;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/ho0;

    invoke-interface {p1, v1, v2}, Ljava/util/function/BiConsumer;->accept(Ljava/lang/Object;Ljava/lang/Object;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 1

    .line 1
    instance-of v0, p1, Lcom/android/tools/r8/internal/qG;

    if-eqz v0, :cond_0

    check-cast p1, Lcom/android/tools/r8/internal/qG;

    .line 2
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/ho0;->a(Lcom/android/tools/r8/internal/ho0;)I

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final hashCode()I
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qG;->b:[I

    invoke-static {v0}, Ljava/util/Arrays;->hashCode([I)I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/qG;->c:Lcom/android/tools/r8/internal/cB;

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object v0, v2, v3

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-static {v2}, Ljava/util/Objects;->hash([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final m()Lcom/android/tools/r8/internal/io0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/qG$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/qG$$ExternalSyntheticLambda0;

    return-object v0
.end method
