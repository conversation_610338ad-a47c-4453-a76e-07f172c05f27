.class public final synthetic Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda9;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiFunction;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/A8;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/R40;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/R40;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/A8;Lcom/android/tools/r8/internal/R40;Lcom/android/tools/r8/internal/R40;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda9;->f$0:Lcom/android/tools/r8/internal/A8;

    iput-object p2, p0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda9;->f$1:Lcom/android/tools/r8/internal/R40;

    iput-object p3, p0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda9;->f$2:Lcom/android/tools/r8/internal/R40;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda9;->f$0:Lcom/android/tools/r8/internal/A8;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda9;->f$1:Lcom/android/tools/r8/internal/R40;

    iget-object v2, p0, Lcom/android/tools/r8/internal/Ra$$ExternalSyntheticLambda9;->f$2:Lcom/android/tools/r8/internal/R40;

    check-cast p1, Lcom/android/tools/r8/internal/r9;

    check-cast p2, Lcom/android/tools/r8/internal/R40;

    invoke-static {v0, v1, v2, p1, p2}, Lcom/android/tools/r8/internal/Ra;->b(Lcom/android/tools/r8/internal/A8;Lcom/android/tools/r8/internal/R40;Lcom/android/tools/r8/internal/R40;Lcom/android/tools/r8/internal/r9;Lcom/android/tools/r8/internal/R40;)Lcom/android/tools/r8/internal/r9;

    move-result-object p1

    return-object p1
.end method
