.class public abstract Lcom/android/tools/r8/internal/R9;
.super Lcom/android/tools/r8/internal/H9;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/H9;-><init>()V

    return-void
.end method


# virtual methods
.method public final N()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public U()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final l()Lcom/android/tools/r8/internal/R9;
    .locals 0

    return-object p0
.end method
