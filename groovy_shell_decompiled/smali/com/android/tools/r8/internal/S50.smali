.class public final Lcom/android/tools/r8/internal/S50;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/gU;


# instance fields
.field public final a:Lcom/android/tools/r8/internal/Yf;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Yf;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/S50;->a:Lcom/android/tools/r8/internal/Yf;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/S50;->a:Lcom/android/tools/r8/internal/Yf;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/H0;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/nU;)V
    .locals 0

    .line 2
    iget-object p2, p0, Lcom/android/tools/r8/internal/S50;->a:Lcom/android/tools/r8/internal/Yf;

    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method
