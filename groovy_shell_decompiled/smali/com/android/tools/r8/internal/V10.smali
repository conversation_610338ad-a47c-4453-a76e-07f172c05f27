.class public abstract Lcom/android/tools/r8/internal/V10;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static a(Lcom/android/tools/r8/graph/E0;Ljava/util/function/Predicate;Z)Z
    .locals 0

    if-eqz p0, :cond_0

    .line 1
    invoke-interface {p1, p0}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result p0

    return p0

    :cond_0
    return p2
.end method

.method public static a(Lcom/android/tools/r8/internal/sr0;Ljava/lang/Object;)Z
    .locals 0

    .line 3
    invoke-static {p0, p1}, Lcom/android/tools/r8/internal/V10;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static a(<PERSON>java/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    if-ne p0, p1, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method
