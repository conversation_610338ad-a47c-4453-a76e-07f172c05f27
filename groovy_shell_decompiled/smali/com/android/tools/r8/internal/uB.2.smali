.class public final Lcom/android/tools/r8/internal/uB;
.super Lcom/android/tools/r8/internal/zB;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic f:Lcom/android/tools/r8/internal/dB;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/dB;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/uB;->f:Lcom/android/tools/r8/internal/dB;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/zB;-><init>()V

    return-void
.end method


# virtual methods
.method public final b(Ljava/lang/Object;)I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uB;->f:Lcom/android/tools/r8/internal/dB;

    iget-object v0, v0, Lcom/android/tools/r8/internal/dB;->f:Lcom/android/tools/r8/internal/iB;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/iB;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Collection;

    if-nez p1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    .line 2
    :cond_0
    invoke-interface {p1}, Ljava/util/Collection;->size()I

    move-result p1

    :goto_0
    return p1
.end method

.method public final contains(Ljava/lang/Object;)Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uB;->f:Lcom/android/tools/r8/internal/dB;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/dB;->f:Lcom/android/tools/r8/internal/iB;

    .line 3
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/iB;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public final g()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final h()Lcom/android/tools/r8/internal/LB;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uB;->f:Lcom/android/tools/r8/internal/dB;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/dB;->f:Lcom/android/tools/r8/internal/iB;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/iB;->o()Lcom/android/tools/r8/internal/LB;

    move-result-object v0

    return-object v0
.end method

.method public final j(I)Lcom/android/tools/r8/internal/ZW;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uB;->f:Lcom/android/tools/r8/internal/dB;

    iget-object v0, v0, Lcom/android/tools/r8/internal/dB;->f:Lcom/android/tools/r8/internal/iB;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/iB;->l()Lcom/android/tools/r8/internal/LB;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/SA;->a()Lcom/android/tools/r8/internal/cB;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Map$Entry;

    .line 2
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Collection;

    invoke-interface {p1}, Ljava/util/Collection;->size()I

    move-result p1

    .line 3
    new-instance v1, Lcom/android/tools/r8/internal/bX;

    invoke-direct {v1, p1, v0}, Lcom/android/tools/r8/internal/bX;-><init>(ILjava/lang/Object;)V

    return-object v1
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uB;->f:Lcom/android/tools/r8/internal/dB;

    .line 2
    iget v0, v0, Lcom/android/tools/r8/internal/dB;->g:I

    return v0
.end method
