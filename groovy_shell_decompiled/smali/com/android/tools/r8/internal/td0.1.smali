.class public abstract Lcom/android/tools/r8/internal/td0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/b90;
    .locals 6

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    .line 2
    new-instance v1, Lcom/android/tools/r8/internal/b90;

    new-instance v2, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda0;

    invoke-direct {v2, p0}, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/y;)V

    iget-object v3, v0, Lcom/android/tools/r8/utils/w;->g:Lcom/android/tools/r8/ResourceShrinkerConfiguration;

    .line 6
    invoke-virtual {v3}, Lcom/android/tools/r8/ResourceShrinkerConfiguration;->getDebugConsumer()Lcom/android/tools/r8/StringConsumer;

    move-result-object v3

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v4

    iget-object v4, v4, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    if-nez v3, :cond_0

    .line 8
    sget-object v3, Lcom/android/tools/r8/internal/XX;->a:Lcom/android/tools/r8/internal/XX;

    goto :goto_0

    .line 10
    :cond_0
    new-instance v5, Lcom/android/tools/r8/internal/rd0;

    invoke-direct {v5, v3, v4}, Lcom/android/tools/r8/internal/rd0;-><init>(Lcom/android/tools/r8/StringConsumer;Lcom/android/tools/r8/DiagnosticsHandler;)V

    move-object v3, v5

    .line 11
    :goto_0
    invoke-direct {v1, v2, v3}, Lcom/android/tools/r8/internal/b90;-><init>(Ljava/util/function/Function;Lcom/android/tools/r8/internal/sj0;)V

    .line 13
    iget-object v2, v0, Lcom/android/tools/r8/utils/w;->g:Lcom/android/tools/r8/ResourceShrinkerConfiguration;

    invoke-virtual {v2}, Lcom/android/tools/r8/ResourceShrinkerConfiguration;->isOptimizedShrinking()Z

    move-result v2

    if-eqz v2, :cond_4

    iget-object v2, v0, Lcom/android/tools/r8/utils/w;->d:Lcom/android/tools/r8/AndroidResourceProvider;

    if-eqz v2, :cond_4

    .line 16
    :try_start_0
    sget-object v3, Lcom/android/tools/r8/FeatureSplit;->BASE:Lcom/android/tools/r8/FeatureSplit;

    invoke-static {p0, v1, v2, v3}, Lcom/android/tools/r8/internal/td0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/b90;Lcom/android/tools/r8/AndroidResourceProvider;Lcom/android/tools/r8/FeatureSplit;)V

    .line 17
    iget-object v0, v0, Lcom/android/tools/r8/utils/w;->q:Lcom/android/tools/r8/internal/cv;

    if-eqz v0, :cond_1

    const/4 v2, 0x1

    goto :goto_1

    :cond_1
    const/4 v2, 0x0

    :goto_1
    if-eqz v2, :cond_3

    .line 18
    iget-object v0, v0, Lcom/android/tools/r8/internal/cv;->a:Ljava/util/ArrayList;

    .line 19
    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_2
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/FeatureSplit;

    .line 20
    invoke-virtual {v2}, Lcom/android/tools/r8/FeatureSplit;->getAndroidResourceProvider()Lcom/android/tools/r8/AndroidResourceProvider;

    move-result-object v3

    if-eqz v3, :cond_2

    .line 21
    invoke-virtual {v2}, Lcom/android/tools/r8/FeatureSplit;->getAndroidResourceProvider()Lcom/android/tools/r8/AndroidResourceProvider;

    move-result-object v3

    invoke-static {p0, v1, v3, v2}, Lcom/android/tools/r8/internal/td0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/b90;Lcom/android/tools/r8/AndroidResourceProvider;Lcom/android/tools/r8/FeatureSplit;)V
    :try_end_0
    .catch Lcom/android/tools/r8/ResourceException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    .line 28
    :cond_3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/b90;->c()V

    goto :goto_3

    .line 29
    :catch_0
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p0

    iget-object p0, p0, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    const-string v0, "Failed initializing resource table"

    .line 30
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/bd0;->b(Ljava/lang/String;)Ljava/lang/RuntimeException;

    move-result-object p0

    throw p0

    :cond_4
    :goto_3
    return-object v1
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)Ljava/io/InputStream;
    .locals 0

    .line 74
    invoke-static {p0, p1}, Lcom/android/tools/r8/internal/td0;->e(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)Ljava/io/InputStream;

    move-result-object p0

    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Ljava/lang/Exception;)Ljava/lang/RuntimeException;
    .locals 1

    .line 75
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p0

    .line 76
    iget-object p0, p0, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    .line 77
    new-instance v0, Lcom/android/tools/r8/utils/ExceptionDiagnostic;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/utils/ExceptionDiagnostic;-><init>(Ljava/lang/Throwable;)V

    const/4 p1, 0x0

    .line 78
    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/internal/bd0;->a(Lcom/android/tools/r8/DiagnosticsLevel;Lcom/android/tools/r8/Diagnostic;)V

    .line 79
    iget-object p0, p0, Lcom/android/tools/r8/internal/bd0;->c:Lcom/android/tools/r8/internal/g;

    throw p0
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/b90;Lcom/android/tools/r8/AndroidResourceProvider;Lcom/android/tools/r8/FeatureSplit;)V
    .locals 4

    .line 31
    invoke-interface {p2}, Lcom/android/tools/r8/AndroidResourceProvider;->getAndroidResources()Ljava/util/Collection;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_5

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/AndroidResourceInput;

    .line 32
    sget-object v1, Lcom/android/tools/r8/internal/sd0;->a:[I

    invoke-interface {v0}, Lcom/android/tools/r8/AndroidResourceInput;->getKind()Lcom/android/tools/r8/AndroidResourceInput$Kind;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Enum;->ordinal()I

    move-result v2

    aget v1, v1, v2

    const/4 v2, 0x1

    if-eq v1, v2, :cond_4

    const/4 v3, 0x2

    if-eq v1, v3, :cond_3

    const/4 v2, 0x3

    if-eq v1, v2, :cond_2

    const/4 v2, 0x4

    if-eq v1, v2, :cond_1

    const/4 v2, 0x5

    if-eq v1, v2, :cond_0

    goto :goto_0

    .line 50
    :cond_0
    new-instance v1, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda4;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda4;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)V

    .line 52
    invoke-interface {v0}, Lcom/android/tools/r8/AndroidResourceInput;->getPath()Lcom/android/tools/r8/ResourcePath;

    move-result-object v0

    invoke-interface {v0}, Lcom/android/tools/r8/ResourcePath;->location()Ljava/lang/String;

    move-result-object v0

    .line 53
    iget-object v2, p1, Lcom/android/tools/r8/internal/b90;->f:Ljava/util/HashMap;

    .line 54
    invoke-virtual {v2, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 55
    :cond_1
    new-instance v1, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda3;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda3;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)V

    .line 56
    iget-object v0, p1, Lcom/android/tools/r8/internal/b90;->d:Ljava/util/ArrayList;

    .line 57
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 58
    :cond_2
    new-instance v1, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda2;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)V

    .line 60
    invoke-interface {v0}, Lcom/android/tools/r8/AndroidResourceInput;->getPath()Lcom/android/tools/r8/ResourcePath;

    move-result-object v0

    invoke-interface {v0}, Lcom/android/tools/r8/ResourcePath;->location()Ljava/lang/String;

    move-result-object v0

    .line 61
    iget-object v2, p1, Lcom/android/tools/r8/internal/b90;->c:Ljava/util/HashMap;

    .line 62
    invoke-virtual {v2, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 63
    :cond_3
    invoke-interface {v0}, Lcom/android/tools/r8/AndroidResourceInput;->getByteStream()Ljava/io/InputStream;

    move-result-object v0

    .line 64
    iget-object v1, p1, Lcom/android/tools/r8/internal/b90;->g:Ljava/util/HashMap;

    .line 65
    iget-object v3, p1, Lcom/android/tools/r8/internal/b90;->b:Lcom/android/tools/r8/internal/a90;

    .line 66
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 67
    :try_start_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/Af0;->a(Ljava/io/InputStream;)Lcom/android/tools/r8/internal/Af0;

    move-result-object v0

    .line 68
    invoke-virtual {v3, v0, v2}, Lcom/android/tools/r8/internal/a90;->a(Lcom/android/tools/r8/internal/Af0;Z)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 69
    invoke-virtual {v1, v0, p3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :catch_0
    move-exception p0

    .line 70
    new-instance p1, Ljava/lang/RuntimeException;

    invoke-direct {p1, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw p1

    .line 71
    :cond_4
    new-instance v1, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda1;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/internal/td0$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)V

    .line 72
    iget-object v0, p1, Lcom/android/tools/r8/internal/b90;->e:Ljava/util/ArrayList;

    .line 73
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :cond_5
    return-void
.end method

.method public static synthetic b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)Ljava/io/InputStream;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lcom/android/tools/r8/internal/td0;->e(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)Ljava/io/InputStream;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)Ljava/io/InputStream;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lcom/android/tools/r8/internal/td0;->e(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)Ljava/io/InputStream;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)Ljava/io/InputStream;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lcom/android/tools/r8/internal/td0;->e(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)Ljava/io/InputStream;

    move-result-object p0

    return-object p0
.end method

.method public static e(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/AndroidResourceInput;)Ljava/io/InputStream;
    .locals 2

    .line 1
    :try_start_0
    invoke-interface {p1}, Lcom/android/tools/r8/AndroidResourceInput;->getByteStream()Ljava/io/InputStream;

    move-result-object p0
    :try_end_0
    .catch Lcom/android/tools/r8/ResourceException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    .line 3
    :catch_0
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->P()Lcom/android/tools/r8/internal/bd0;

    move-result-object p0

    invoke-interface {p1}, Lcom/android/tools/r8/AndroidResourceInput;->getPath()Lcom/android/tools/r8/ResourcePath;

    move-result-object p1

    invoke-interface {p1}, Lcom/android/tools/r8/ResourcePath;->location()Ljava/lang/String;

    move-result-object p1

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Failed reading "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/bd0;->b(Ljava/lang/String;)Ljava/lang/RuntimeException;

    move-result-object p0

    throw p0
.end method
