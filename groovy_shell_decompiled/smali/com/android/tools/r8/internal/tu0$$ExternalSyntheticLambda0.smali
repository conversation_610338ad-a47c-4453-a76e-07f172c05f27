.class public final synthetic Lcom/android/tools/r8/internal/tu0$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiConsumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/tu0;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/Mp;

.field public final synthetic f$2:Lcom/android/tools/r8/graph/E2;

.field public final synthetic f$3:Lcom/android/tools/r8/graph/E2;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/tu0;Lcom/android/tools/r8/internal/Mp;Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/E2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/tu0$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/tu0;

    iput-object p2, p0, Lcom/android/tools/r8/internal/tu0$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/Mp;

    iput-object p3, p0, Lcom/android/tools/r8/internal/tu0$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/graph/E2;

    iput-object p4, p0, Lcom/android/tools/r8/internal/tu0$$ExternalSyntheticLambda0;->f$3:Lcom/android/tools/r8/graph/E2;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 6

    iget-object v0, p0, Lcom/android/tools/r8/internal/tu0$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/tu0;

    iget-object v1, p0, Lcom/android/tools/r8/internal/tu0$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/Mp;

    iget-object v2, p0, Lcom/android/tools/r8/internal/tu0$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/graph/E2;

    iget-object v3, p0, Lcom/android/tools/r8/internal/tu0$$ExternalSyntheticLambda0;->f$3:Lcom/android/tools/r8/graph/E2;

    move-object v4, p1

    check-cast v4, Lcom/android/tools/r8/graph/C2;

    move-object v5, p2

    check-cast v5, Lcom/android/tools/r8/internal/su0;

    invoke-virtual/range {v0 .. v5}, Lcom/android/tools/r8/internal/tu0;->a(Lcom/android/tools/r8/internal/Mp;Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/C2;Lcom/android/tools/r8/internal/su0;)V

    return-void
.end method
