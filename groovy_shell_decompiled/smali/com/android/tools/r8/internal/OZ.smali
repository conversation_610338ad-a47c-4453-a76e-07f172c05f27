.class public final Lcom/android/tools/r8/internal/OZ;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/ir/optimize/A;


# static fields
.field public static final synthetic c:Z = true


# instance fields
.field public final b:Lcom/android/tools/r8/graph/y;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/OZ;->b:Lcom/android/tools/r8/graph/y;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/graph/proto/k;Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/FI;
    .locals 3

    const/4 v0, 0x0

    if-nez p3, :cond_0

    return-object v0

    .line 101
    :cond_0
    sget-boolean v1, Lcom/android/tools/r8/internal/OZ;->c:Z

    if-nez v1, :cond_2

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->g()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->T0()Z

    move-result v2

    if-eqz v2, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_2
    :goto_0
    if-nez v1, :cond_4

    .line 102
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->f()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result v2

    if-eqz v2, :cond_3

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_4
    :goto_1
    if-nez v1, :cond_6

    .line 103
    iget-object v1, p0, Lcom/android/tools/r8/internal/OZ;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1;->d6:Lcom/android/tools/r8/internal/az;

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->g()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/az;->containsValue(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_5

    goto :goto_2

    :cond_5
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 104
    :cond_6
    :goto_2
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->g()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    iget-object v2, p0, Lcom/android/tools/r8/internal/OZ;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/J2;->b(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v1

    .line 105
    invoke-interface {p1, v1, v0}, Lcom/android/tools/r8/internal/xt0;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/j0;)Lcom/android/tools/r8/internal/vt0;

    move-result-object p1

    .line 106
    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/vt0;->f(Lcom/android/tools/r8/internal/vt0;)V

    .line 107
    sget-boolean v0, Lcom/android/tools/r8/internal/FI;->m:Z

    .line 108
    new-instance v0, Lcom/android/tools/r8/internal/EI;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/EI;-><init>()V

    .line 109
    iput-object p1, v0, Lcom/android/tools/r8/internal/kD;->a:Lcom/android/tools/r8/internal/vt0;

    .line 110
    iget-object p1, p0, Lcom/android/tools/r8/internal/OZ;->b:Lcom/android/tools/r8/graph/y;

    .line 111
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object p1

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->f()Lcom/android/tools/r8/graph/J2;

    move-result-object p3

    invoke-virtual {p1, p3}, Lcom/android/tools/r8/graph/B1;->c(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    .line 112
    iput-object p1, v0, Lcom/android/tools/r8/internal/tI;->d:Lcom/android/tools/r8/graph/x2;

    .line 113
    invoke-virtual {v0, p2}, Lcom/android/tools/r8/internal/tI;->a(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/tI;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/EI;

    .line 114
    iput-object p4, p1, Lcom/android/tools/r8/internal/kD;->b:Lcom/android/tools/r8/internal/B40;

    .line 115
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/EI;->c()Lcom/android/tools/r8/internal/FI;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/graph/proto/j;Lcom/android/tools/r8/internal/TY;)Ljava/util/Set;
    .locals 10

    .line 1
    sget-boolean p2, Lcom/android/tools/r8/internal/OZ;->c:Z

    if-nez p2, :cond_1

    .line 2
    instance-of v0, p4, Lcom/android/tools/r8/internal/LZ;

    if-eqz v0, :cond_0

    goto :goto_0

    .line 3
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 4
    :cond_1
    :goto_0
    invoke-static {}, Lcom/android/tools/r8/internal/kj0;->c()Ljava/util/Set;

    move-result-object v0

    .line 5
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 6
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->j()Lcom/android/tools/r8/internal/K5;

    move-result-object v2

    invoke-virtual {v2, p1}, Lcom/android/tools/r8/internal/K5;->a(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/N5;

    move-result-object v2

    .line 7
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/N5;->h()Lcom/android/tools/r8/internal/rD;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v3

    const v4, 0x7fffffff

    if-nez p2, :cond_3

    .line 8
    iget-object p2, p3, Lcom/android/tools/r8/graph/proto/j;->b:Lcom/android/tools/r8/graph/proto/c;

    .line 9
    iget-object p2, p2, Lcom/android/tools/r8/graph/proto/c;->a:Lcom/android/tools/r8/internal/YE;

    .line 10
    invoke-static {v4, p2}, Lcom/android/tools/r8/graph/proto/c;->a(ILcom/android/tools/r8/internal/ZE;)I

    move-result p2

    if-nez p2, :cond_2

    goto :goto_1

    .line 11
    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 12
    :cond_3
    :goto_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->n()I

    move-result p2

    const/4 v5, 0x0

    move v6, v5

    :goto_2
    if-ge v6, p2, :cond_7

    .line 13
    iget-object v7, p3, Lcom/android/tools/r8/graph/proto/j;->b:Lcom/android/tools/r8/graph/proto/c;

    .line 14
    invoke-virtual {v7, v6}, Lcom/android/tools/r8/graph/proto/c;->a(I)Lcom/android/tools/r8/graph/proto/b;

    move-result-object v7

    .line 15
    invoke-interface {v2}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lcom/android/tools/r8/internal/rD;

    .line 16
    sget-boolean v9, Lcom/android/tools/r8/internal/OZ;->c:Z

    if-nez v9, :cond_5

    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->l1()Z

    move-result v9

    if-eqz v9, :cond_4

    goto :goto_3

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 17
    :cond_5
    :goto_3
    invoke-virtual {v7}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 18
    instance-of v9, v7, Lcom/android/tools/r8/graph/proto/k;

    if-eqz v9, :cond_6

    .line 19
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v8

    invoke-virtual {v7}, Lcom/android/tools/r8/graph/proto/b;->b()Lcom/android/tools/r8/graph/proto/k;

    move-result-object v7

    .line 20
    invoke-virtual {p0, p1, v8, v7, v3}, Lcom/android/tools/r8/internal/OZ;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/graph/proto/k;Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/FI;

    move-result-object v7

    if-eqz v7, :cond_6

    .line 23
    invoke-virtual {v1, v7}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_6
    add-int/lit8 v6, v6, 0x1

    goto :goto_2

    .line 27
    :cond_7
    sget-boolean p2, Lcom/android/tools/r8/internal/OZ;->c:Z

    if-nez p2, :cond_9

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/N5;->h()Lcom/android/tools/r8/internal/rD;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/rD;->l1()Z

    move-result p2

    if-nez p2, :cond_8

    goto :goto_4

    :cond_8
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 28
    :cond_9
    :goto_4
    invoke-virtual {v1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_5
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_a

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/FI;

    .line 29
    invoke-interface {v2, v1}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    .line 30
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->f0()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    goto :goto_5

    .line 31
    :cond_a
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->r()Lcom/android/tools/r8/internal/cA;

    move-result-object p2

    .line 32
    :cond_b
    :goto_6
    invoke-interface {p2}, Ljava/util/ListIterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_13

    .line 33
    invoke-interface {p2}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/rD;

    .line 34
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->W1()Z

    move-result v2

    if-eqz v2, :cond_12

    .line 35
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->c0()Lcom/android/tools/r8/internal/uI;

    move-result-object v1

    .line 40
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v2

    .line 41
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/x2;

    .line 42
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/mI;->P2()Lcom/android/tools/r8/internal/JI;

    move-result-object v6

    .line 43
    iget-object v7, p4, Lcom/android/tools/r8/internal/TY;->d:Lcom/android/tools/r8/internal/Fy;

    .line 44
    invoke-virtual {p4, v2, v3, v6, v7}, Lcom/android/tools/r8/internal/TY;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object v2

    .line 45
    iget-object v2, v2, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 46
    check-cast v2, Lcom/android/tools/r8/graph/x2;

    .line 47
    sget-boolean v3, Lcom/android/tools/r8/internal/OZ;->c:Z

    if-nez v3, :cond_d

    if-eqz v2, :cond_c

    goto :goto_7

    :cond_c
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 48
    :cond_d
    :goto_7
    iget-object v6, p4, Lcom/android/tools/r8/internal/TY;->d:Lcom/android/tools/r8/internal/Fy;

    .line 49
    invoke-virtual {p4, v6, v2}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/proto/j;

    move-result-object v2

    .line 51
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/proto/j;->g()Z

    move-result v6

    if-nez v6, :cond_b

    if-nez v3, :cond_f

    .line 52
    iget-object v3, v2, Lcom/android/tools/r8/graph/proto/j;->b:Lcom/android/tools/r8/graph/proto/c;

    .line 53
    iget-object v3, v3, Lcom/android/tools/r8/graph/proto/c;->a:Lcom/android/tools/r8/internal/YE;

    .line 54
    invoke-static {v4, v3}, Lcom/android/tools/r8/graph/proto/c;->a(ILcom/android/tools/r8/internal/ZE;)I

    move-result v3

    if-nez v3, :cond_e

    goto :goto_8

    .line 55
    :cond_e
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_f
    :goto_8
    move v3, v5

    .line 56
    :goto_9
    iget-object v6, v1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 57
    invoke-virtual {v6}, Ljava/util/ArrayList;->size()I

    move-result v6

    if-ge v3, v6, :cond_11

    .line 58
    iget-object v6, v2, Lcom/android/tools/r8/graph/proto/j;->b:Lcom/android/tools/r8/graph/proto/c;

    .line 59
    invoke-virtual {v6, v3}, Lcom/android/tools/r8/graph/proto/c;->a(I)Lcom/android/tools/r8/graph/proto/b;

    move-result-object v6

    .line 60
    invoke-virtual {v6}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 61
    instance-of v7, v6, Lcom/android/tools/r8/graph/proto/k;

    if-eqz v7, :cond_10

    .line 62
    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/mI;->b(I)Lcom/android/tools/r8/internal/vt0;

    move-result-object v7

    .line 65
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/proto/b;->b()Lcom/android/tools/r8/graph/proto/k;

    move-result-object v6

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v8

    .line 66
    invoke-virtual {p0, p1, v7, v6, v8}, Lcom/android/tools/r8/internal/OZ;->b(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/graph/proto/k;Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/LI;

    move-result-object v6

    if-eqz v6, :cond_10

    .line 67
    invoke-interface {p2}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 68
    invoke-interface {p2, v6}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    .line 69
    invoke-interface {p2}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    .line 70
    invoke-virtual {v6}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v6

    invoke-virtual {v1, v3, v6}, Lcom/android/tools/r8/internal/rD;->a(ILcom/android/tools/r8/internal/vt0;)V

    :cond_10
    add-int/lit8 v3, v3, 0x1

    goto :goto_9

    .line 74
    :cond_11
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->d1()Z

    move-result v3

    if-eqz v3, :cond_b

    .line 78
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    .line 79
    iget-object v2, v2, Lcom/android/tools/r8/graph/proto/j;->c:Lcom/android/tools/r8/graph/proto/k;

    .line 80
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v1

    .line 81
    invoke-virtual {p0, p1, v3, v2, v1}, Lcom/android/tools/r8/internal/OZ;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/graph/proto/k;Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/FI;

    move-result-object v1

    if-eqz v1, :cond_b

    .line 87
    invoke-interface {p2, v1}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    .line 88
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->f0()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    goto/16 :goto_6

    .line 89
    :cond_12
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->v2()Z

    move-result v2

    if-eqz v2, :cond_b

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->E0()Lcom/android/tools/r8/internal/Wh0;

    move-result-object v2

    .line 90
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Wh0;->L2()Z

    move-result v2

    if-nez v2, :cond_b

    .line 91
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->E0()Lcom/android/tools/r8/internal/Wh0;

    move-result-object v1

    .line 92
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Wh0;->M2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    .line 93
    iget-object v3, p3, Lcom/android/tools/r8/graph/proto/j;->c:Lcom/android/tools/r8/graph/proto/k;

    .line 94
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v6

    .line 95
    invoke-virtual {p0, p1, v2, v3, v6}, Lcom/android/tools/r8/internal/OZ;->b(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/graph/proto/k;Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/LI;

    move-result-object v2

    if-eqz v2, :cond_b

    .line 96
    invoke-interface {p2}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 97
    invoke-interface {p2, v2}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    .line 98
    invoke-interface {p2}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    .line 99
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Wh0;->M2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v2

    const/4 v6, 0x0

    .line 100
    invoke-virtual {v1, v3, v2, v6}, Lcom/android/tools/r8/internal/rD;->a(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;Ljava/util/Set;)V

    goto/16 :goto_6

    :cond_13
    return-object v0
.end method

.method public final b(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/graph/proto/k;Lcom/android/tools/r8/internal/B40;)Lcom/android/tools/r8/internal/LI;
    .locals 4

    const/4 v0, 0x0

    if-nez p3, :cond_0

    return-object v0

    .line 1
    :cond_0
    sget-boolean v1, Lcom/android/tools/r8/internal/OZ;->c:Z

    if-nez v1, :cond_2

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->g()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->T0()Z

    move-result v2

    if-eqz v2, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_2
    :goto_0
    if-nez v1, :cond_4

    .line 2
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->f()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result v2

    if-eqz v2, :cond_3

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_4
    :goto_1
    if-nez v1, :cond_6

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/internal/OZ;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1;->d6:Lcom/android/tools/r8/internal/az;

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->g()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/az;->containsValue(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_5

    goto :goto_2

    :cond_5
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 4
    :cond_6
    :goto_2
    sget-boolean v1, Lcom/android/tools/r8/internal/LI;->m:Z

    .line 5
    new-instance v1, Lcom/android/tools/r8/internal/KI;

    invoke-direct {v1}, Lcom/android/tools/r8/internal/KI;-><init>()V

    .line 6
    iget-object v2, p0, Lcom/android/tools/r8/internal/OZ;->b:Lcom/android/tools/r8/graph/y;

    .line 7
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v2

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->f()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    invoke-virtual {v2, v3}, Lcom/android/tools/r8/graph/B1;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/x2;

    move-result-object v2

    .line 8
    iput-object v2, v1, Lcom/android/tools/r8/internal/tI;->d:Lcom/android/tools/r8/graph/x2;

    .line 9
    iget-object p1, p1, Lcom/android/tools/r8/internal/aA;->e:Lcom/android/tools/r8/internal/AZ;

    .line 11
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/proto/k;->f()Lcom/android/tools/r8/graph/J2;

    move-result-object p3

    iget-object v2, p0, Lcom/android/tools/r8/internal/OZ;->b:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p3, v2}, Lcom/android/tools/r8/graph/J2;->b(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p3

    .line 12
    invoke-virtual {v1, p1, p3, v0}, Lcom/android/tools/r8/internal/kD;->a(Lcom/android/tools/r8/internal/xt0;Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/j0;)Lcom/android/tools/r8/internal/kD;

    move-result-object p1

    .line 13
    check-cast p1, Lcom/android/tools/r8/internal/KI;

    .line 15
    sget p3, Lcom/android/tools/r8/internal/cB;->c:I

    .line 16
    new-instance p3, Lcom/android/tools/r8/internal/rk0;

    invoke-direct {p3, p2}, Lcom/android/tools/r8/internal/rk0;-><init>(Ljava/lang/Object;)V

    .line 17
    invoke-virtual {p1, p3}, Lcom/android/tools/r8/internal/tI;->a(Ljava/util/List;)Lcom/android/tools/r8/internal/tI;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/KI;

    .line 18
    iput-object p4, p1, Lcom/android/tools/r8/internal/kD;->b:Lcom/android/tools/r8/internal/B40;

    .line 19
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/KI;->c()Lcom/android/tools/r8/internal/LI;

    move-result-object p1

    return-object p1
.end method
