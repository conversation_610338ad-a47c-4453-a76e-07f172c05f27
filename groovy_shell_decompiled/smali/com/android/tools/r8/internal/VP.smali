.class public Lcom/android/tools/r8/internal/VP;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/Kl;


# instance fields
.field public final a:Z

.field public final b:Lcom/android/tools/r8/internal/eQ;

.field public final c:Lcom/android/tools/r8/internal/bQ;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/eQ;Lcom/android/tools/r8/internal/bQ;Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-boolean p3, p0, Lcom/android/tools/r8/internal/VP;->a:Z

    .line 3
    iput-object p1, p0, Lcom/android/tools/r8/internal/VP;->b:Lcom/android/tools/r8/internal/eQ;

    .line 4
    iput-object p2, p0, Lcom/android/tools/r8/internal/VP;->c:Lcom/android/tools/r8/internal/bQ;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/x0;Lcom/android/tools/r8/internal/Gp0;)Lcom/android/tools/r8/internal/QS;
    .locals 6

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/cQ;

    invoke-direct {v0, p2}, Lcom/android/tools/r8/internal/cQ;-><init>(Lcom/android/tools/r8/internal/Gp0;)V

    .line 4
    iget-object v1, v0, Lcom/android/tools/r8/internal/cQ;->a:Lcom/android/tools/r8/internal/Gp0;

    const-string v2, "Legacy to Human convert"

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/Gp0;->a(Ljava/lang/String;)V

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/VP;->b()Z

    move-result v1

    .line 8
    iget-object v2, p0, Lcom/android/tools/r8/internal/VP;->b:Lcom/android/tools/r8/internal/eQ;

    .line 9
    iget-object v2, v2, Lcom/android/tools/r8/internal/eQ;->a:Lcom/android/tools/r8/internal/A2;

    .line 10
    invoke-static {p1, v1, v2}, Lcom/android/tools/r8/internal/vQ;->a(Lcom/android/tools/r8/graph/x0;ZLcom/android/tools/r8/internal/A2;)V

    .line 11
    iget-object v1, p0, Lcom/android/tools/r8/internal/VP;->b:Lcom/android/tools/r8/internal/eQ;

    .line 12
    invoke-static {v1}, Lcom/android/tools/r8/internal/cQ;->a(Lcom/android/tools/r8/internal/eQ;)Lcom/android/tools/r8/internal/Nz;

    move-result-object v1

    .line 17
    invoke-static {}, Lcom/android/tools/r8/origin/Origin;->unknown()Lcom/android/tools/r8/origin/Origin;

    move-result-object v2

    .line 18
    iget-object v3, p0, Lcom/android/tools/r8/internal/VP;->c:Lcom/android/tools/r8/internal/bQ;

    .line 19
    invoke-virtual {v0, v3, p1, v2}, Lcom/android/tools/r8/internal/cQ;->a(Lcom/android/tools/r8/internal/bQ;Lcom/android/tools/r8/graph/x0;Lcom/android/tools/r8/origin/Origin;)Lcom/android/tools/r8/internal/Dz;

    move-result-object v3

    .line 20
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/VP;->b()Z

    move-result v4

    if-eqz v4, :cond_0

    .line 21
    iget-object v4, v0, Lcom/android/tools/r8/internal/cQ;->a:Lcom/android/tools/r8/internal/Gp0;

    const-string v5, "Legacy hacks"

    invoke-virtual {v4, v5}, Lcom/android/tools/r8/internal/Gp0;->a(Ljava/lang/String;)V

    .line 22
    iget-object v4, p1, Lcom/android/tools/r8/graph/x0;->d:Lcom/android/tools/r8/utils/w;

    iget-object v4, v4, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    .line 23
    invoke-virtual {v3, v4, v2}, Lcom/android/tools/r8/internal/Dz;->b(Lcom/android/tools/r8/internal/bd0;Lcom/android/tools/r8/origin/Origin;)Lcom/android/tools/r8/internal/Dz$a;

    move-result-object v2

    .line 24
    iget-object v3, p0, Lcom/android/tools/r8/internal/VP;->b:Lcom/android/tools/r8/internal/eQ;

    .line 25
    iget-object v3, v3, Lcom/android/tools/r8/internal/eQ;->c:Ljava/lang/String;

    .line 26
    iget-object v4, p1, Lcom/android/tools/r8/graph/x0;->e:Lcom/android/tools/r8/graph/B1;

    .line 27
    iget-object v5, p1, Lcom/android/tools/r8/graph/x0;->d:Lcom/android/tools/r8/utils/w;

    invoke-virtual {v5}, Lcom/android/tools/r8/utils/w;->H()Lcom/android/tools/r8/internal/A2;

    move-result-object v5

    .line 28
    invoke-static {v3, v4, v5, v2}, Lcom/android/tools/r8/internal/cQ;->a(Ljava/lang/String;Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/internal/A2;Lcom/android/tools/r8/internal/Dz$a;)V

    .line 30
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Dz$a;->a()Lcom/android/tools/r8/internal/Dz;

    move-result-object v3

    .line 31
    iget-object v2, v0, Lcom/android/tools/r8/internal/cQ;->a:Lcom/android/tools/r8/internal/Gp0;

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Gp0;->b()V

    .line 33
    :cond_0
    iget-object v2, p1, Lcom/android/tools/r8/graph/x0;->d:Lcom/android/tools/r8/utils/w;

    iget-object v2, v2, Lcom/android/tools/r8/utils/w;->i:Lcom/android/tools/r8/internal/bd0;

    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/cQ;->a(Lcom/android/tools/r8/internal/bd0;)V

    .line 34
    iget-object v0, v0, Lcom/android/tools/r8/internal/cQ;->a:Lcom/android/tools/r8/internal/Gp0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Gp0;->b()V

    .line 35
    new-instance v0, Lcom/android/tools/r8/internal/yz;

    .line 36
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/VP;->b()Z

    move-result v2

    invoke-direct {v0, v1, v3, v2}, Lcom/android/tools/r8/internal/yz;-><init>(Lcom/android/tools/r8/internal/Nz;Lcom/android/tools/r8/internal/Dz;Z)V

    .line 37
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/yz;->a(Lcom/android/tools/r8/graph/x0;Lcom/android/tools/r8/internal/Gp0;)Lcom/android/tools/r8/internal/QS;

    move-result-object p1

    return-object p1
.end method

.method public final a()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/VP;->b:Lcom/android/tools/r8/internal/eQ;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/eQ;->d:Ljava/lang/String;

    return-object v0
.end method

.method public b()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/VP;->a:Z

    return v0
.end method

.method public final c()Ljava/util/Set;
    .locals 1

    .line 1
    sget v0, Lcom/android/tools/r8/internal/LB;->c:I

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/Bc0;->j:Lcom/android/tools/r8/internal/Bc0;

    return-object v0
.end method

.method public final d()Lcom/android/tools/r8/internal/A2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/VP;->b:Lcom/android/tools/r8/internal/eQ;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/eQ;->a:Lcom/android/tools/r8/internal/A2;

    return-object v0
.end method

.method public final e()Ljava/util/List;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/VP;->b:Lcom/android/tools/r8/internal/eQ;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/eQ;->f:Ljava/util/AbstractCollection;

    return-object v0
.end method

.method public final f()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/VP;->b:Lcom/android/tools/r8/internal/eQ;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/eQ;->b:Ljava/lang/String;

    return-object v0
.end method

.method public g()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Lcom/android/tools/r8/graph/J2;",
            "Lcom/android/tools/r8/graph/J2;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/VP;->c:Lcom/android/tools/r8/internal/bQ;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/bQ;->e:Lcom/android/tools/r8/internal/iB;

    return-object v0
.end method

.method public final isEmpty()Z
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/VP;->c:Lcom/android/tools/r8/internal/bQ;

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/internal/bQ;->a:Lcom/android/tools/r8/internal/iB;

    .line 3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/iB;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    iget-object v1, v0, Lcom/android/tools/r8/internal/bQ;->b:Lcom/android/tools/r8/internal/iB;

    .line 4
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/iB;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    iget-object v0, v0, Lcom/android/tools/r8/internal/bQ;->c:Lcom/android/tools/r8/internal/iB;

    .line 5
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/iB;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method
