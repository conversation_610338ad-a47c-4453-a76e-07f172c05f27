.class public final Lcom/android/tools/r8/internal/v40;
.super Lcom/android/tools/r8/internal/rD;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic i:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/vt0;)V
    .locals 1

    const/4 v0, 0x0

    .line 1
    invoke-direct {p0, v0, p1}, Lcom/android/tools/r8/internal/rD;-><init>(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/vt0;)V

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/v40;->i:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/vt0;->V()Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/ic0;Lcom/android/tools/r8/internal/nC;Lcom/android/tools/r8/internal/nC;)Z
    .locals 0

    .line 17
    invoke-virtual {p1, p2, p0}, Lcom/android/tools/r8/internal/rD;->a(Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/internal/ic0;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method public final A0()Lcom/android/tools/r8/internal/v40;
    .locals 0

    return-object p0
.end method

.method public final F2()I
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw v0
.end method

.method public final G2()I
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Os0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Os0;-><init>()V

    throw v0
.end method

.method public final H2()I
    .locals 1

    const/16 v0, 0x36

    return v0
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/ir/optimize/E;
    .locals 0

    .line 33
    sget-object p1, Lcom/android/tools/r8/ir/optimize/E;->b:Lcom/android/tools/r8/ir/optimize/C;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/ir/optimize/X;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/ir/optimize/O;
    .locals 0

    .line 34
    sget-object p1, Lcom/android/tools/r8/ir/optimize/O;->d:Lcom/android/tools/r8/ir/optimize/O;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/tC;)Ljava/lang/Object;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/N5;Lcom/android/tools/r8/internal/gS;)V
    .locals 0

    .line 32
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    const-string p2, "This IR must not be inserted before load and store insertion."

    invoke-direct {p1, p2}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/R8;)V
    .locals 2

    .line 29
    iget-object v0, p0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vt0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->d0()Lcom/android/tools/r8/internal/It0;

    move-result-object v0

    .line 30
    new-instance v1, Lcom/android/tools/r8/internal/Ra;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/It0;->b()Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object v0, Lcom/android/tools/r8/internal/Ra$a;->d:Lcom/android/tools/r8/internal/Ra$a;

    goto :goto_0

    :cond_0
    sget-object v0, Lcom/android/tools/r8/internal/Ra$a;->c:Lcom/android/tools/r8/internal/Ra$a;

    :goto_0
    invoke-direct {v1, v0}, Lcom/android/tools/r8/internal/Ra;-><init>(Lcom/android/tools/r8/internal/Ra$a;)V

    .line 31
    invoke-virtual {p1, v1, p0}, Lcom/android/tools/r8/internal/R8;->a(Lcom/android/tools/r8/internal/H9;Lcom/android/tools/r8/internal/rD;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/Wm;)V
    .locals 1

    .line 28
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    const-string v0, "This classfile-specific IR should not be inserted in the Dex backend."

    invoke-direct {p1, v0}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/gR;)V
    .locals 1

    .line 27
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    const-string v0, "This classfile-specific IR should not be used in LIR."

    invoke-direct {p1, v0}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/rD;Lcom/android/tools/r8/internal/ic0;)Z
    .locals 3

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/v40;->i:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    instance-of v1, p1, Lcom/android/tools/r8/internal/v40;

    if-eqz v1, :cond_0

    goto :goto_0

    .line 4
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 6
    :cond_1
    :goto_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->A0()Lcom/android/tools/r8/internal/v40;

    move-result-object v1

    new-instance v2, Lcom/android/tools/r8/internal/v40$$ExternalSyntheticLambda0;

    invoke-direct {v2, p2}, Lcom/android/tools/r8/internal/v40$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/ic0;)V

    .line 7
    invoke-virtual {p0, v1, v2}, Lcom/android/tools/r8/internal/v40;->a(Lcom/android/tools/r8/internal/v40;Ljava/util/function/BiPredicate;)Z

    move-result v1

    if-nez v1, :cond_2

    const/4 p1, 0x0

    return p1

    :cond_2
    if-nez v0, :cond_4

    .line 13
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/v40;->b(Lcom/android/tools/r8/internal/rD;)Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 14
    :cond_4
    :goto_1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->n()Z

    move-result v0

    if-nez v0, :cond_6

    invoke-interface {p2}, Lcom/android/tools/r8/internal/ic0;->e()Lcom/android/tools/r8/utils/w;

    move-result-object p2

    iget-boolean p2, p2, Lcom/android/tools/r8/utils/w;->f1:Z

    if-eqz p2, :cond_5

    goto :goto_2

    :cond_5
    const/4 p1, 0x1

    goto :goto_3

    .line 15
    :cond_6
    :goto_2
    iget-object p2, p0, Lcom/android/tools/r8/internal/rD;->g:Lcom/android/tools/r8/internal/B40;

    iget-object p1, p1, Lcom/android/tools/r8/internal/rD;->g:Lcom/android/tools/r8/internal/B40;

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 16
    invoke-static {p2, p1}, Lcom/android/tools/r8/internal/ru;->a(Lcom/android/tools/r8/internal/ru;Ljava/lang/Object;)Z

    move-result p1

    :goto_3
    return p1
.end method

.method public final a(Lcom/android/tools/r8/internal/v40;Ljava/util/function/BiPredicate;)Z
    .locals 3

    .line 18
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->U0()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    sget-object v1, Lcom/android/tools/r8/internal/YR$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/YR$$ExternalSyntheticLambda0;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/vt0;->c(Ljava/util/function/Predicate;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 19
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->U0()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->r()Lcom/android/tools/r8/internal/rD;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/rD;->U()Lcom/android/tools/r8/internal/nC;

    move-result-object v0

    .line 20
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->U0()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    sget-object v2, Lcom/android/tools/r8/internal/YR$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/YR$$ExternalSyntheticLambda0;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/vt0;->c(Ljava/util/function/Predicate;)Z

    move-result v1

    if-nez v1, :cond_0

    const/4 p1, 0x0

    return p1

    .line 23
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->U0()Lcom/android/tools/r8/internal/vt0;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/vt0;->r()Lcom/android/tools/r8/internal/rD;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->U()Lcom/android/tools/r8/internal/nC;

    move-result-object p1

    .line 24
    invoke-interface {p2, v0, p1}, Ljava/util/function/BiPredicate;->test(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 26
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->U0()Lcom/android/tools/r8/internal/vt0;

    move-result-object p1

    sget-object p2, Lcom/android/tools/r8/internal/YR$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/YR$$ExternalSyntheticLambda0;

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/vt0;->c(Ljava/util/function/Predicate;)Z

    move-result p1

    xor-int/lit8 p1, p1, 0x1

    return p1
.end method

.method public final b(Lcom/android/tools/r8/internal/vt0;)V
    .locals 1

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/vt0;->G()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/rD;->b(Lcom/android/tools/r8/internal/vt0;)V

    goto :goto_0

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public final b(Lcom/android/tools/r8/internal/rD;)Z
    .locals 1

    .line 6
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 7
    instance-of v0, p1, Lcom/android/tools/r8/internal/v40;

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return p1

    .line 8
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->A0()Lcom/android/tools/r8/internal/v40;

    move-result-object p1

    sget-object v0, Lcom/android/tools/r8/internal/v40$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/v40$$ExternalSyntheticLambda1;

    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/internal/v40;->a(Lcom/android/tools/r8/internal/v40;Ljava/util/function/BiPredicate;)Z

    move-result p1

    return p1
.end method

.method public final c(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public final c1()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
