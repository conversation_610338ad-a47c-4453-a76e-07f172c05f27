.class public final Lcom/android/tools/r8/internal/u5;
.super Lcom/android/tools/r8/internal/n5;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/x2;)V
    .locals 2

    .line 1
    iget-object v0, p1, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-direct {p0, p1, v0, v1}, Lcom/android/tools/r8/internal/n5;-><init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/t5;I)V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/synthesis/V;)V
    .locals 17

    move-object/from16 v0, p1

    .line 27
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v1

    .line 28
    iget-object v9, v0, Lcom/android/tools/r8/synthesis/l;->b:Lcom/android/tools/r8/graph/J2;

    .line 29
    iget-object v2, v1, Lcom/android/tools/r8/graph/B1;->W2:Lcom/android/tools/r8/graph/J2;

    const-string v3, "initialValueSupplier"

    .line 31
    invoke-virtual {v1, v3}, Lcom/android/tools/r8/graph/B1;->c(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object v3

    .line 32
    invoke-virtual {v1, v9, v2, v3}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/l1;

    move-result-object v10

    .line 36
    iget-object v2, v1, Lcom/android/tools/r8/graph/B1;->F1:Lcom/android/tools/r8/graph/J2;

    iget-object v3, v1, Lcom/android/tools/r8/graph/B1;->W2:Lcom/android/tools/r8/graph/J2;

    const/4 v11, 0x1

    new-array v4, v11, [Lcom/android/tools/r8/graph/J2;

    const/4 v12, 0x0

    aput-object v3, v4, v12

    .line 39
    invoke-virtual {v1, v2, v4}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v2

    iget-object v3, v1, Lcom/android/tools/r8/graph/B1;->d1:Lcom/android/tools/r8/graph/I2;

    .line 40
    invoke-virtual {v1, v9, v2, v3}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object v2

    .line 44
    iget-object v3, v1, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    new-array v4, v12, [Lcom/android/tools/r8/graph/J2;

    .line 46
    invoke-virtual {v1, v3, v4}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v3

    const-string v4, "initialValue"

    invoke-virtual {v1, v4}, Lcom/android/tools/r8/graph/B1;->c(Ljava/lang/String;)Lcom/android/tools/r8/graph/I2;

    move-result-object v4

    .line 47
    invoke-virtual {v1, v9, v3, v4}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object v13

    .line 50
    iget-object v3, v1, Lcom/android/tools/r8/graph/B1;->f3:Lcom/android/tools/r8/graph/J2;

    .line 51
    iput-object v3, v0, Lcom/android/tools/r8/synthesis/l;->i:Lcom/android/tools/r8/graph/J2;

    .line 52
    sget-object v3, Lcom/android/tools/r8/graph/g1;->o:[Lcom/android/tools/r8/graph/g1;

    .line 53
    new-instance v3, Lcom/android/tools/r8/graph/g1$a;

    invoke-direct {v3, v11}, Lcom/android/tools/r8/graph/g1$a;-><init>(Z)V

    .line 54
    invoke-virtual {v3, v10}, Lcom/android/tools/r8/graph/g1$a;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/g1$a;

    move-result-object v3

    const/16 v4, 0x1011

    .line 55
    invoke-static {v4}, Lcom/android/tools/r8/graph/h3;->g(I)Lcom/android/tools/r8/graph/h3;

    move-result-object v4

    .line 56
    iput-object v4, v3, Lcom/android/tools/r8/graph/g1$a;->c:Lcom/android/tools/r8/graph/h3;

    .line 57
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/g1$a;->c()Lcom/android/tools/r8/graph/g1$a;

    move-result-object v3

    .line 58
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/g1$a;->a()Lcom/android/tools/r8/graph/g1;

    move-result-object v3

    .line 59
    sget v4, Lcom/android/tools/r8/internal/cB;->c:I

    .line 60
    new-instance v4, Lcom/android/tools/r8/internal/rk0;

    invoke-direct {v4, v3}, Lcom/android/tools/r8/internal/rk0;-><init>(Ljava/lang/Object;)V

    .line 61
    iget-object v3, v0, Lcom/android/tools/r8/synthesis/l;->n:Ljava/util/ArrayList;

    invoke-virtual {v3}, Ljava/util/ArrayList;->clear()V

    .line 62
    iget-object v3, v0, Lcom/android/tools/r8/synthesis/l;->n:Ljava/util/ArrayList;

    invoke-virtual {v3, v4}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 63
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 64
    new-instance v3, Lcom/android/tools/r8/internal/X9;

    sget-object v14, Lcom/android/tools/r8/internal/It0;->b:Lcom/android/tools/r8/internal/It0;

    invoke-direct {v3, v14, v12}, Lcom/android/tools/r8/internal/X9;-><init>(Lcom/android/tools/r8/internal/It0;I)V

    invoke-virtual {v6, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 65
    new-instance v3, Lcom/android/tools/r8/internal/Ra;

    sget-object v4, Lcom/android/tools/r8/internal/Ra$a;->e:Lcom/android/tools/r8/internal/Ra$a;

    invoke-direct {v3, v4}, Lcom/android/tools/r8/internal/Ra;-><init>(Lcom/android/tools/r8/internal/Ra$a;)V

    invoke-virtual {v6, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 66
    new-instance v3, Lcom/android/tools/r8/internal/O9;

    iget-object v4, v1, Lcom/android/tools/r8/graph/B1;->f3:Lcom/android/tools/r8/graph/J2;

    iget-object v5, v1, Lcom/android/tools/r8/graph/B1;->F1:Lcom/android/tools/r8/graph/J2;

    new-array v7, v12, [Lcom/android/tools/r8/graph/J2;

    .line 71
    invoke-virtual {v1, v5, v7}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v5

    iget-object v7, v1, Lcom/android/tools/r8/graph/B1;->d1:Lcom/android/tools/r8/graph/I2;

    .line 72
    invoke-virtual {v1, v4, v5, v7}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object v4

    const/16 v5, 0xb7

    invoke-direct {v3, v5, v4, v12}, Lcom/android/tools/r8/internal/O9;-><init>(ILcom/android/tools/r8/graph/x2;Z)V

    .line 73
    invoke-virtual {v6, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 81
    new-instance v3, Lcom/android/tools/r8/internal/X9;

    iget-object v4, v1, Lcom/android/tools/r8/graph/B1;->W2:Lcom/android/tools/r8/graph/J2;

    invoke-static {v4}, Lcom/android/tools/r8/internal/It0;->a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/It0;

    move-result-object v4

    invoke-direct {v3, v4, v11}, Lcom/android/tools/r8/internal/X9;-><init>(Lcom/android/tools/r8/internal/It0;I)V

    invoke-virtual {v6, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 82
    new-instance v3, Lcom/android/tools/r8/internal/F9;

    invoke-direct {v3, v10}, Lcom/android/tools/r8/internal/F9;-><init>(Lcom/android/tools/r8/graph/l1;)V

    invoke-virtual {v6, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 83
    new-instance v3, Lcom/android/tools/r8/internal/Ia;

    invoke-direct {v3}, Lcom/android/tools/r8/internal/Ia;-><init>()V

    invoke-virtual {v6, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 87
    sget-object v3, Lcom/android/tools/r8/graph/j1;->u:[Lcom/android/tools/r8/graph/j1;

    .line 88
    invoke-static {v2, v11}, Lcom/android/tools/r8/internal/Oc;->a(Lcom/android/tools/r8/graph/x2;Z)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v2

    const/16 v3, 0x1001

    .line 89
    invoke-static {v3, v11}, Lcom/android/tools/r8/graph/H4;->b(IZ)Lcom/android/tools/r8/graph/H4;

    move-result-object v3

    .line 90
    invoke-virtual {v2, v3}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/H4;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v15

    new-instance v8, Lcom/android/tools/r8/graph/G;

    .line 91
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v7

    .line 92
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v16

    const/4 v4, 0x2

    const/4 v5, 0x2

    move-object v2, v8

    move-object v3, v9

    move-object v11, v8

    move-object/from16 v8, v16

    .line 93
    invoke-direct/range {v2 .. v8}, Lcom/android/tools/r8/graph/G;-><init>(Lcom/android/tools/r8/graph/J2;IILjava/util/List;Ljava/util/List;Ljava/util/List;)V

    .line 94
    invoke-virtual {v15, v11}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/i0;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v2

    .line 95
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/j1$a;->b()Lcom/android/tools/r8/graph/j1$a;

    move-result-object v2

    .line 96
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/j1$a;->a()Lcom/android/tools/r8/graph/j1;

    move-result-object v2

    .line 97
    new-instance v3, Lcom/android/tools/r8/internal/rk0;

    invoke-direct {v3, v2}, Lcom/android/tools/r8/internal/rk0;-><init>(Ljava/lang/Object;)V

    .line 98
    invoke-virtual {v0, v3}, Lcom/android/tools/r8/synthesis/l;->a(Ljava/util/AbstractCollection;)Lcom/android/tools/r8/synthesis/l;

    .line 99
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 100
    new-instance v2, Lcom/android/tools/r8/internal/X9;

    invoke-direct {v2, v14, v12}, Lcom/android/tools/r8/internal/X9;-><init>(Lcom/android/tools/r8/internal/It0;I)V

    invoke-virtual {v6, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 101
    new-instance v2, Lcom/android/tools/r8/internal/E9;

    invoke-direct {v2, v10}, Lcom/android/tools/r8/internal/E9;-><init>(Lcom/android/tools/r8/graph/l1;)V

    invoke-virtual {v6, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 102
    new-instance v2, Lcom/android/tools/r8/internal/O9;

    iget-object v1, v1, Lcom/android/tools/r8/graph/B1;->z4:Lcom/android/tools/r8/graph/p2;

    iget-object v1, v1, Lcom/android/tools/r8/graph/p2;->a:Lcom/android/tools/r8/graph/x2;

    const/16 v3, 0xb9

    const/4 v4, 0x1

    invoke-direct {v2, v3, v1, v4}, Lcom/android/tools/r8/internal/O9;-><init>(ILcom/android/tools/r8/graph/x2;Z)V

    invoke-virtual {v6, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 103
    new-instance v1, Lcom/android/tools/r8/internal/Ha;

    invoke-direct {v1, v14}, Lcom/android/tools/r8/internal/Ha;-><init>(Lcom/android/tools/r8/internal/It0;)V

    invoke-virtual {v6, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 104
    new-instance v1, Lcom/android/tools/r8/graph/j1$a;

    invoke-direct {v1, v4}, Lcom/android/tools/r8/graph/j1$a;-><init>(Z)V

    .line 105
    invoke-virtual {v1, v13}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v1

    const/16 v2, 0x1004

    .line 107
    invoke-static {v2, v12}, Lcom/android/tools/r8/graph/H4;->b(IZ)Lcom/android/tools/r8/graph/H4;

    move-result-object v2

    .line 108
    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/H4;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v1

    new-instance v10, Lcom/android/tools/r8/graph/G;

    .line 109
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v7

    .line 110
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v8

    const/4 v4, 0x1

    const/4 v5, 0x1

    move-object v2, v10

    move-object v3, v9

    .line 111
    invoke-direct/range {v2 .. v8}, Lcom/android/tools/r8/graph/G;-><init>(Lcom/android/tools/r8/graph/J2;IILjava/util/List;Ljava/util/List;Ljava/util/List;)V

    .line 112
    invoke-virtual {v1, v10}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/i0;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v1

    .line 113
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/j1$a;->b()Lcom/android/tools/r8/graph/j1$a;

    move-result-object v1

    .line 114
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/j1$a;->a()Lcom/android/tools/r8/graph/j1;

    move-result-object v1

    .line 115
    new-instance v2, Lcom/android/tools/r8/internal/rk0;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/rk0;-><init>(Ljava/lang/Object;)V

    .line 116
    invoke-virtual {v0, v2}, Lcom/android/tools/r8/synthesis/l;->b(Ljava/util/AbstractCollection;)V

    return-void
.end method

.method public static synthetic b(Lcom/android/tools/r8/synthesis/Q;)Lcom/android/tools/r8/synthesis/Q$b;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/android/tools/r8/synthesis/Q;->s:Lcom/android/tools/r8/synthesis/Q$b;

    return-object p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/synthesis/Q;)Lcom/android/tools/r8/synthesis/Q$b;
    .locals 0

    .line 1
    iget-object p1, p1, Lcom/android/tools/r8/synthesis/Q;->s:Lcom/android/tools/r8/synthesis/Q$b;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/B40;Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/M9;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/internal/jS;)Ljava/util/Collection;
    .locals 4

    .line 2
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object p1

    .line 3
    iget-object p2, p3, Lcom/android/tools/r8/graph/y;->a:Lcom/android/tools/r8/graph/h;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/h;->g()Lcom/android/tools/r8/synthesis/I;

    move-result-object p2

    .line 4
    sget-object v0, Lcom/android/tools/r8/internal/u5$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/u5$$ExternalSyntheticLambda0;

    .line 7
    invoke-virtual {p5}, Lcom/android/tools/r8/internal/ef;->a()Lcom/android/tools/r8/internal/hf;

    move-result-object v1

    new-instance v2, Lcom/android/tools/r8/internal/u5$$ExternalSyntheticLambda1;

    invoke-direct {v2, p3}, Lcom/android/tools/r8/internal/u5$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/graph/y;)V

    .line 8
    invoke-virtual {p2, v0, v1, p3, v2}, Lcom/android/tools/r8/synthesis/I;->a(Lcom/android/tools/r8/synthesis/H;Lcom/android/tools/r8/internal/hf;Lcom/android/tools/r8/graph/y;Ljava/util/function/Consumer;)Lcom/android/tools/r8/graph/E2;

    move-result-object p2

    .line 9
    iget-object p3, p5, Lcom/android/tools/r8/internal/ef;->c:Lcom/android/tools/r8/graph/D5;

    .line 10
    invoke-interface {p4, p2, p3}, Lcom/android/tools/r8/internal/j5;->c(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/D5;)V

    const/4 p3, 0x2

    .line 12
    invoke-interface {p6, p3}, Lcom/android/tools/r8/internal/jS;->a(I)V

    .line 13
    new-instance p3, Lcom/android/tools/r8/internal/ea;

    iget-object p4, p2, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    invoke-direct {p3, p4}, Lcom/android/tools/r8/internal/ea;-><init>(Lcom/android/tools/r8/graph/J2;)V

    new-instance p4, Lcom/android/tools/r8/internal/Ra;

    sget-object p5, Lcom/android/tools/r8/internal/Ra$a;->f:Lcom/android/tools/r8/internal/Ra$a;

    invoke-direct {p4, p5}, Lcom/android/tools/r8/internal/Ra;-><init>(Lcom/android/tools/r8/internal/Ra$a;)V

    new-instance p5, Lcom/android/tools/r8/internal/Ra;

    sget-object p6, Lcom/android/tools/r8/internal/Ra$a;->k:Lcom/android/tools/r8/internal/Ra$a;

    invoke-direct {p5, p6}, Lcom/android/tools/r8/internal/Ra;-><init>(Lcom/android/tools/r8/internal/Ra$a;)V

    new-instance p6, Lcom/android/tools/r8/internal/O9;

    iget-object p2, p2, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    iget-object v0, p1, Lcom/android/tools/r8/graph/B1;->F1:Lcom/android/tools/r8/graph/J2;

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->W2:Lcom/android/tools/r8/graph/J2;

    const/4 v2, 0x1

    new-array v2, v2, [Lcom/android/tools/r8/graph/J2;

    const/4 v3, 0x0

    aput-object v1, v2, v3

    .line 24
    invoke-virtual {p1, v0, v2}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;[Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/F2;

    move-result-object v0

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->d1:Lcom/android/tools/r8/graph/I2;

    .line 25
    invoke-virtual {p1, p2, v0, v1}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    const/16 p2, 0xb7

    invoke-direct {p6, p2, p1, v3}, Lcom/android/tools/r8/internal/O9;-><init>(ILcom/android/tools/r8/graph/x2;Z)V

    .line 26
    invoke-static {p3, p4, p5, p6}, Lcom/android/tools/r8/internal/cB;->a(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Lcom/android/tools/r8/internal/cB;

    move-result-object p1

    return-object p1
.end method
