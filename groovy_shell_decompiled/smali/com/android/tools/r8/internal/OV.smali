.class public Lcom/android/tools/r8/internal/OV;
.super Lcom/android/tools/r8/internal/NV;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/android/tools/r8/internal/NV;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ljava/util/concurrent/ConcurrentHashMap;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/NV;-><init>(Ljava/util/AbstractMap;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;)Lcom/android/tools/r8/internal/LV;
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/NV;->a(Ljava/lang/Object;)Lcom/android/tools/r8/internal/LV;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;)Ljava/lang/Object;
    .locals 0

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    return-object p1
.end method

.method public final b(Ljava/lang/Object;)Lcom/android/tools/r8/graph/C2;
    .locals 0

    .line 1
    check-cast p1, Lcom/android/tools/r8/graph/x2;

    .line 2
    invoke-static {p1, p1}, Lcom/android/tools/r8/internal/Vc;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/A2;

    move-result-object p1

    return-object p1
.end method
