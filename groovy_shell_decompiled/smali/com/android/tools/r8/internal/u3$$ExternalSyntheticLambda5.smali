.class public final synthetic Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/u3;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/r3;

.field public final synthetic f$2:Lcom/android/tools/r8/utils/w;

.field public final synthetic f$3:Lcom/android/tools/r8/internal/O40;

.field public final synthetic f$4:Lcom/android/tools/r8/internal/Fy;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/u3;Lcom/android/tools/r8/internal/r3;Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/internal/u3;

    iput-object p2, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/internal/r3;

    iput-object p3, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$2:Lcom/android/tools/r8/utils/w;

    iput-object p4, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$3:Lcom/android/tools/r8/internal/O40;

    iput-object p5, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$4:Lcom/android/tools/r8/internal/Fy;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 6

    iget-object v0, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/internal/u3;

    iget-object v1, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/internal/r3;

    iget-object v2, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$2:Lcom/android/tools/r8/utils/w;

    iget-object v3, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$3:Lcom/android/tools/r8/internal/O40;

    iget-object v4, p0, Lcom/android/tools/r8/internal/u3$$ExternalSyntheticLambda5;->f$4:Lcom/android/tools/r8/internal/Fy;

    move-object v5, p1

    check-cast v5, Lcom/android/tools/r8/graph/D5;

    invoke-virtual/range {v0 .. v5}, Lcom/android/tools/r8/internal/u3;->a(Lcom/android/tools/r8/internal/r3;Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/internal/O40;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/D5;)V

    return-void
.end method
