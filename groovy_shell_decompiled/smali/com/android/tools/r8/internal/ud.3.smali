.class public abstract Lcom/android/tools/r8/internal/ud;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/graph/V;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/V;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/ud;->a:Lcom/android/tools/r8/graph/V;

    return-void
.end method


# virtual methods
.method public a(Lcom/android/tools/r8/internal/sk0;)Lcom/android/tools/r8/internal/sd;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/sd;

    iget-object v1, p0, Lcom/android/tools/r8/internal/ud;->a:Lcom/android/tools/r8/graph/V;

    invoke-direct {v0, v1, p0, p1}, Lcom/android/tools/r8/internal/sd;-><init>(Lcom/android/tools/r8/graph/V;Lcom/android/tools/r8/internal/ud;Lcom/android/tools/r8/internal/LB;)V

    return-object v0
.end method

.method public abstract a()Ljava/util/Collection;
.end method

.method public abstract a(Lcom/android/tools/r8/graph/J2;Ljava/util/function/Consumer;)V
.end method
