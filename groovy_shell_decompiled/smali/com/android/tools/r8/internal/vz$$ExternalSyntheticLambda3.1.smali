.class public final synthetic Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda3;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/vz;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/y;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/gX;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/vz;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/gX;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda3;->f$0:Lcom/android/tools/r8/internal/vz;

    iput-object p2, p0, Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda3;->f$1:Lcom/android/tools/r8/graph/y;

    iput-object p3, p0, Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda3;->f$2:Lcom/android/tools/r8/internal/gX;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda3;->f$0:Lcom/android/tools/r8/internal/vz;

    iget-object v1, p0, Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda3;->f$1:Lcom/android/tools/r8/graph/y;

    iget-object v2, p0, Lcom/android/tools/r8/internal/vz$$ExternalSyntheticLambda3;->f$2:Lcom/android/tools/r8/internal/gX;

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v0, v1, v2, p1}, Lcom/android/tools/r8/internal/vz;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/gX;Lcom/android/tools/r8/graph/E2;)V

    return-void
.end method
