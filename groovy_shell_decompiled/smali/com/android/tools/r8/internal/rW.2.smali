.class public final Lcom/android/tools/r8/internal/rW;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/Q5;


# static fields
.field public static final synthetic f:Z = true


# instance fields
.field public a:Lcom/android/tools/r8/internal/vt0;

.field public b:I

.field public c:Lcom/android/tools/r8/internal/rD;

.field public final d:Lcom/android/tools/r8/internal/r40;

.field public final e:Lcom/android/tools/r8/internal/V30;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 4

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 3
    iput-object v0, p0, Lcom/android/tools/r8/internal/rW;->a:Lcom/android/tools/r8/internal/vt0;

    const/4 v1, 0x0

    .line 4
    iput v1, p0, Lcom/android/tools/r8/internal/rW;->b:I

    .line 5
    iput-object v0, p0, Lcom/android/tools/r8/internal/rW;->c:Lcom/android/tools/r8/internal/rD;

    .line 12
    new-instance v0, Lcom/android/tools/r8/internal/r40;

    new-instance v2, Lcom/android/tools/r8/internal/rW$$ExternalSyntheticLambda0;

    invoke-direct {v2, p0}, Lcom/android/tools/r8/internal/rW$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/rW;)V

    invoke-direct {v0, v2}, Lcom/android/tools/r8/internal/r40;-><init>(Ljava/util/function/Predicate;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/rW;->d:Lcom/android/tools/r8/internal/r40;

    .line 25
    new-instance v2, Lcom/android/tools/r8/internal/Gu0;

    new-instance v3, Lcom/android/tools/r8/internal/rW$$ExternalSyntheticLambda1;

    invoke-direct {v3, p0}, Lcom/android/tools/r8/internal/rW$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/rW;)V

    invoke-direct {v2, v3}, Lcom/android/tools/r8/internal/Gu0;-><init>(Ljava/util/function/Predicate;)V

    const/4 v3, 0x2

    new-array v3, v3, [Lcom/android/tools/r8/internal/T30;

    aput-object v0, v3, v1

    const/4 v0, 0x1

    aput-object v2, v3, v0

    .line 44
    new-instance v1, Lcom/android/tools/r8/internal/V30;

    invoke-direct {v1, v0, v3}, Lcom/android/tools/r8/internal/V30;-><init>(Z[Lcom/android/tools/r8/internal/T30;)V

    .line 45
    iput-object v1, p0, Lcom/android/tools/r8/internal/rW;->e:Lcom/android/tools/r8/internal/V30;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/AQ;)Z
    .locals 8

    const/4 v0, 0x0

    .line 6
    iput v0, p0, Lcom/android/tools/r8/internal/rW;->b:I

    const/4 v1, 0x0

    .line 7
    iput-object v1, p0, Lcom/android/tools/r8/internal/rW;->c:Lcom/android/tools/r8/internal/rD;

    .line 8
    iget-object v2, p0, Lcom/android/tools/r8/internal/rW;->e:Lcom/android/tools/r8/internal/V30;

    invoke-virtual {v2, p1}, Lcom/android/tools/r8/internal/V30;->a(Lcom/android/tools/r8/internal/AQ;)Lcom/android/tools/r8/internal/QT;

    move-result-object v2

    if-eqz v2, :cond_9

    .line 9
    iget-object v3, p0, Lcom/android/tools/r8/internal/rW;->c:Lcom/android/tools/r8/internal/rD;

    if-eqz v3, :cond_9

    .line 10
    invoke-interface {p1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 11
    invoke-interface {p1}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/rD;

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/rD;->k0()Lcom/android/tools/r8/internal/cS;

    move-result-object v3

    .line 12
    invoke-interface {p1}, Ljava/util/ListIterator;->hasNext()Z

    move-result v4

    if-nez v4, :cond_0

    goto/16 :goto_2

    .line 15
    :cond_0
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v3

    .line 16
    invoke-interface {p1}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/rD;

    .line 17
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v5

    if-ne v3, v5, :cond_4

    .line 18
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->A1()Z

    move-result v5

    if-eqz v5, :cond_4

    .line 19
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->a()Lcom/android/tools/r8/internal/sr0;

    move-result-object v5

    invoke-static {}, Lcom/android/tools/r8/internal/sr0;->k()Lcom/android/tools/r8/internal/dH;

    move-result-object v6

    if-ne v5, v6, :cond_4

    .line 20
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->H()Lcom/android/tools/r8/internal/Sg;

    move-result-object v5

    invoke-virtual {v5}, Lcom/android/tools/r8/internal/Sg;->N2()I

    move-result v5

    const/16 v6, -0x80

    if-lt v5, v6, :cond_4

    .line 21
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->H()Lcom/android/tools/r8/internal/Sg;

    move-result-object v4

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/Sg;->N2()I

    move-result v4

    const/16 v5, 0x7f

    if-gt v4, v5, :cond_4

    .line 22
    invoke-interface {p1}, Ljava/util/ListIterator;->hasNext()Z

    move-result v4

    if-nez v4, :cond_1

    goto :goto_1

    .line 26
    :cond_1
    invoke-interface {p1}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/rD;

    .line 27
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v5

    if-ne v3, v5, :cond_3

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->i1()Z

    move-result v4

    if-eqz v4, :cond_3

    invoke-interface {p1}, Ljava/util/ListIterator;->hasNext()Z

    move-result v4

    if-nez v4, :cond_2

    goto :goto_0

    .line 31
    :cond_2
    invoke-interface {p1}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/rD;

    const/4 v5, 0x4

    .line 32
    invoke-static {p1, v5}, Lcom/android/tools/r8/internal/U30;->a(Lcom/android/tools/r8/internal/AQ;I)V

    .line 33
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v5

    if-ne v3, v5, :cond_5

    .line 34
    instance-of v3, v4, Lcom/android/tools/r8/internal/km0;

    if-eqz v3, :cond_5

    goto/16 :goto_6

    :cond_3
    :goto_0
    const/4 v3, 0x3

    .line 35
    invoke-static {p1, v3}, Lcom/android/tools/r8/internal/U30;->a(Lcom/android/tools/r8/internal/AQ;I)V

    goto :goto_2

    :cond_4
    :goto_1
    const/4 v3, 0x2

    .line 36
    invoke-static {p1, v3}, Lcom/android/tools/r8/internal/U30;->a(Lcom/android/tools/r8/internal/AQ;I)V

    .line 37
    :cond_5
    :goto_2
    iget-object v3, p0, Lcom/android/tools/r8/internal/rW;->d:Lcom/android/tools/r8/internal/r40;

    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 38
    iget-object v2, v2, Lcom/android/tools/r8/internal/QT;->a:Ljava/util/ArrayList;

    iget v3, v3, Lcom/android/tools/r8/internal/r40;->b:I

    invoke-virtual {v2, v3}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/rD;

    .line 39
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/rD;->k0()Lcom/android/tools/r8/internal/cS;

    move-result-object v2

    .line 40
    sget-boolean v3, Lcom/android/tools/r8/internal/rW;->f:Z

    if-nez v3, :cond_7

    .line 41
    iget-object v3, v2, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 42
    invoke-virtual {v3, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/vt0;

    .line 43
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/vt0;->A()Z

    move-result v3

    if-nez v3, :cond_6

    goto :goto_3

    :cond_6
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 45
    :cond_7
    :goto_3
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/Gl0;

    .line 46
    iget v4, v3, Lcom/android/tools/r8/internal/Gl0;->r:I

    .line 47
    new-instance v5, Lcom/android/tools/r8/internal/Gl0;

    iget-object v6, v3, Lcom/android/tools/r8/internal/Gl0;->s:Lcom/android/tools/r8/internal/Or0;

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v7

    invoke-direct {v5, v6, v7, v4}, Lcom/android/tools/r8/internal/Gl0;-><init>(Lcom/android/tools/r8/internal/Or0;Lcom/android/tools/r8/internal/sr0;I)V

    .line 48
    invoke-virtual {v3, v5}, Lcom/android/tools/r8/internal/vt0;->f(Lcom/android/tools/r8/internal/vt0;)V

    .line 49
    iget-object v3, v2, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v3, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/vt0;

    .line 50
    iget-object v4, v3, Lcom/android/tools/r8/internal/vt0;->d:Ljava/util/LinkedList;

    .line 51
    invoke-virtual {v4, v2}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    .line 52
    iput-object v1, v3, Lcom/android/tools/r8/internal/vt0;->e:Lcom/android/tools/r8/internal/LB;

    .line 53
    invoke-interface {p1}, Lcom/android/tools/r8/internal/uD;->i()V

    .line 56
    invoke-interface {p1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/rD;

    const/4 v3, 0x1

    move v4, v3

    .line 58
    :goto_4
    iget-object v6, p0, Lcom/android/tools/r8/internal/rW;->c:Lcom/android/tools/r8/internal/rD;

    if-eq v1, v6, :cond_8

    .line 59
    invoke-interface {p1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/rD;

    add-int/lit8 v4, v4, 0x1

    goto :goto_4

    .line 64
    :cond_8
    new-instance v1, Lcom/android/tools/r8/internal/cS;

    .line 65
    iget-object v2, v2, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {v2, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/vt0;

    .line 66
    invoke-direct {v1, v5, v2}, Lcom/android/tools/r8/internal/cS;-><init>(Lcom/android/tools/r8/internal/Gl0;Lcom/android/tools/r8/internal/vt0;)V

    .line 67
    iget-object v2, p0, Lcom/android/tools/r8/internal/rW;->c:Lcom/android/tools/r8/internal/rD;

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/rD;->getPosition()Lcom/android/tools/r8/internal/B40;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/rD;->b(Lcom/android/tools/r8/internal/B40;)V

    .line 68
    invoke-interface {p1, v1}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    add-int/2addr v4, v3

    move v1, v0

    :goto_5
    if-ge v1, v4, :cond_9

    .line 69
    invoke-interface {p1}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    add-int/lit8 v1, v1, 0x1

    goto :goto_5

    :cond_9
    :goto_6
    return v0
.end method

.method public final a(Lcom/android/tools/r8/internal/rD;)Z
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/dS$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/dS$$ExternalSyntheticLambda0;

    invoke-static {v0}, Lcom/android/tools/r8/internal/U30;->a(Ljava/util/function/Predicate;)Ljava/util/function/Predicate;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->k0()Lcom/android/tools/r8/internal/cS;

    move-result-object p1

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->U0()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->Z()I

    move-result v0

    const/16 v2, 0x14

    if-le v0, v2, :cond_0

    return v1

    .line 4
    :cond_0
    iget-object p1, p1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    invoke-virtual {p1, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/vt0;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/internal/rW;->a:Lcom/android/tools/r8/internal/vt0;

    const/4 p1, 0x1

    return p1

    :cond_1
    return v1
.end method

.method public final b(Lcom/android/tools/r8/internal/rD;)Z
    .locals 3

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->l1()Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_3

    .line 2
    instance-of v0, p1, Lcom/android/tools/r8/internal/qW;

    if-nez v0, :cond_3

    .line 3
    instance-of v0, p1, Lcom/android/tools/r8/internal/km0;

    if-eqz v0, :cond_0

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    iget-object v2, p0, Lcom/android/tools/r8/internal/rW;->a:Lcom/android/tools/r8/internal/vt0;

    if-ne v0, v2, :cond_0

    goto :goto_0

    .line 7
    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/rW;->b:I

    invoke-static {p1}, Lcom/android/tools/r8/internal/U30;->b(Lcom/android/tools/r8/internal/rD;)I

    move-result v2

    add-int/2addr v2, v0

    iput v2, p0, Lcom/android/tools/r8/internal/rW;->b:I

    if-lez v2, :cond_1

    return v1

    .line 11
    :cond_1
    invoke-static {p1}, Lcom/android/tools/r8/internal/U30;->a(Lcom/android/tools/r8/internal/rD;)I

    move-result v0

    sub-int/2addr v2, v0

    iput v2, p0, Lcom/android/tools/r8/internal/rW;->b:I

    if-nez v2, :cond_2

    .line 12
    instance-of v0, p1, Lcom/android/tools/r8/internal/wi;

    if-nez v0, :cond_2

    .line 13
    iput-object p1, p0, Lcom/android/tools/r8/internal/rW;->c:Lcom/android/tools/r8/internal/rD;

    :cond_2
    const/4 p1, 0x1

    return p1

    :cond_3
    :goto_0
    return v1
.end method
