.class public final Lcom/android/tools/r8/internal/Td0;
.super Lcom/android/tools/r8/internal/dy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# instance fields
.field public b:I

.field public c:Ljava/util/List;

.field public d:Lcom/android/tools/r8/internal/Xc0;


# direct methods
.method public constructor <init>()V
    .locals 5

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/dy;-><init>()V

    .line 166
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    .line 167
    invoke-static {}, Lcom/android/tools/r8/internal/Xd0;->a()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 168
    iget-object v0, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_1

    .line 169
    new-instance v0, Lcom/android/tools/r8/internal/Xc0;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    iget v2, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    const/4 v3, 0x1

    and-int/2addr v2, v3

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    .line 173
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v2

    .line 174
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v4

    invoke-direct {v0, v1, v3, v2, v4}, Lcom/android/tools/r8/internal/Xc0;-><init>(Ljava/util/List;ZLcom/android/tools/r8/internal/ey;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    const/4 v0, 0x0

    .line 175
    iput-object v0, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    :cond_1
    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/ay;)V
    .locals 4

    .line 176
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/dy;-><init>(Lcom/android/tools/r8/internal/ey;)V

    .line 335
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    .line 336
    invoke-static {}, Lcom/android/tools/r8/internal/Xd0;->a()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 337
    iget-object p1, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    if-nez p1, :cond_1

    .line 338
    new-instance p1, Lcom/android/tools/r8/internal/Xc0;

    iget-object v0, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    iget v1, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    const/4 v2, 0x1

    and-int/2addr v1, v2

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    .line 342
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v1

    .line 343
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v3

    invoke-direct {p1, v0, v2, v1, v3}, Lcom/android/tools/r8/internal/Xc0;-><init>(Ljava/util/List;ZLcom/android/tools/r8/internal/ey;Z)V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    const/4 p1, 0x0

    .line 344
    iput-object p1, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    :cond_1
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Xd0;)Lcom/android/tools/r8/internal/Td0;
    .locals 6

    .line 12
    sget-object v0, Lcom/android/tools/r8/internal/Xd0;->d:Lcom/android/tools/r8/internal/Xd0;

    if-ne p1, v0, :cond_0

    return-object p0

    .line 13
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    const/4 v1, 0x1

    if-nez v0, :cond_3

    .line 14
    iget-object v0, p1, Lcom/android/tools/r8/internal/Xd0;->b:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_8

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 16
    iget-object v0, p1, Lcom/android/tools/r8/internal/Xd0;->b:Ljava/util/List;

    iput-object v0, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    .line 17
    iget v0, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    goto :goto_0

    .line 18
    :cond_1
    iget v0, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    and-int/2addr v0, v1

    if-nez v0, :cond_2

    .line 19
    new-instance v0, Ljava/util/ArrayList;

    iget-object v2, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    .line 20
    iget v0, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    or-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    .line 21
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    iget-object v1, p1, Lcom/android/tools/r8/internal/Xd0;->b:Ljava/util/List;

    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 23
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_2

    .line 26
    :cond_3
    iget-object v0, p1, Lcom/android/tools/r8/internal/Xd0;->b:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_8

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    .line 28
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xc0;->b:Ljava/util/List;

    .line 29
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_7

    .line 30
    iget-object v0, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    const/4 v2, 0x0

    .line 31
    iput-object v2, v0, Lcom/android/tools/r8/internal/Xc0;->a:Lcom/android/tools/r8/internal/ey;

    .line 32
    iput-object v2, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    .line 33
    iget-object v0, p1, Lcom/android/tools/r8/internal/Xd0;->b:Ljava/util/List;

    iput-object v0, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    .line 34
    iget v0, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    .line 35
    sget-boolean v0, Lcom/android/tools/r8/internal/uy;->alwaysUseFieldBuilders:Z

    if-eqz v0, :cond_6

    .line 36
    iget-object v0, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_5

    .line 37
    new-instance v0, Lcom/android/tools/r8/internal/Xc0;

    iget-object v3, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    iget v4, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    and-int/2addr v4, v1

    if-eqz v4, :cond_4

    goto :goto_1

    :cond_4
    const/4 v1, 0x0

    .line 41
    :goto_1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v4

    .line 42
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v5

    invoke-direct {v0, v3, v1, v4, v5}, Lcom/android/tools/r8/internal/Xc0;-><init>(Ljava/util/List;ZLcom/android/tools/r8/internal/ey;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    .line 43
    iput-object v2, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    .line 45
    :cond_5
    iget-object v2, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    .line 46
    :cond_6
    iput-object v2, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    goto :goto_2

    .line 48
    :cond_7
    iget-object v0, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    iget-object v1, p1, Lcom/android/tools/r8/internal/Xd0;->b:Ljava/util/List;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Xc0;->a(Ljava/lang/Iterable;)V

    .line 49
    :cond_8
    :goto_2
    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 50
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Td0;

    .line 51
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    return-object p0
.end method

.method public final a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/Td0;
    .locals 2

    const/4 v0, 0x0

    .line 1
    :try_start_0
    sget-object v1, Lcom/android/tools/r8/internal/Xd0;->e:Lcom/android/tools/r8/internal/Sd0;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    new-instance v1, Lcom/android/tools/r8/internal/Xd0;

    invoke-direct {v1, p1, p2}, Lcom/android/tools/r8/internal/Xd0;-><init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/Td0;->a(Lcom/android/tools/r8/internal/Xd0;)Lcom/android/tools/r8/internal/Td0;

    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 4
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 5
    check-cast p2, Lcom/android/tools/r8/internal/Xd0;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 6
    :try_start_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p1

    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_0

    .line 9
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/Td0;->a(Lcom/android/tools/r8/internal/Xd0;)Lcom/android/tools/r8/internal/Td0;

    .line 11
    :cond_0
    throw p1
.end method

.method public final addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Td0;

    return-object p1
.end method

.method public final b()Lcom/android/tools/r8/internal/Xd0;
    .locals 3

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Xd0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/Xd0;-><init>(Lcom/android/tools/r8/internal/Td0;)V

    .line 2
    iget v1, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    .line 3
    iget-object v2, p0, Lcom/android/tools/r8/internal/Td0;->d:Lcom/android/tools/r8/internal/Xc0;

    if-nez v2, :cond_1

    and-int/lit8 v1, v1, 0x1

    if-eqz v1, :cond_0

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    invoke-static {v1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    .line 6
    iget v1, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    and-int/lit8 v1, v1, -0x2

    iput v1, p0, Lcom/android/tools/r8/internal/Td0;->b:I

    .line 8
    :cond_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/Td0;->c:Ljava/util/List;

    iput-object v1, v0, Lcom/android/tools/r8/internal/Xd0;->b:Ljava/util/List;

    goto :goto_0

    .line 10
    :cond_1
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Xc0;->b()Ljava/util/List;

    move-result-object v1

    iput-object v1, v0, Lcom/android/tools/r8/internal/Xd0;->b:Ljava/util/List;

    .line 12
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onBuilt()V

    return-object v0
.end method

.method public final build()Lcom/android/tools/r8/internal/AU;
    .locals 2

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Td0;->b()Lcom/android/tools/r8/internal/Xd0;

    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xd0;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 6
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public final build()Lcom/android/tools/r8/internal/vU;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Td0;->b()Lcom/android/tools/r8/internal/Xd0;

    move-result-object v0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xd0;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 3
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public final bridge synthetic buildPartial()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Td0;->b()Lcom/android/tools/r8/internal/Xd0;

    move-result-object v0

    return-object v0
.end method

.method public final clone()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->clone()Lcom/android/tools/r8/internal/dy;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/Td0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/Xd0;->d:Lcom/android/tools/r8/internal/Xd0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Xd0;->d:Lcom/android/tools/r8/internal/Xd0;

    return-object v0
.end method

.method public final getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->y0:Lcom/android/tools/r8/internal/Ok;

    return-object v0
.end method

.method public final internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->z0:Lcom/android/tools/r8/internal/sy;

    .line 2
    const-class v1, Lcom/android/tools/r8/internal/Xd0;

    const-class v2, Lcom/android/tools/r8/internal/Td0;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Td0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/Td0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 4
    instance-of v0, p1, Lcom/android/tools/r8/internal/Xd0;

    if-eqz v0, :cond_0

    .line 5
    check-cast p1, Lcom/android/tools/r8/internal/Xd0;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Td0;->a(Lcom/android/tools/r8/internal/Xd0;)Lcom/android/tools/r8/internal/Td0;

    move-result-object p1

    goto :goto_0

    .line 7
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    move-object p1, p0

    :goto_0
    return-object p1
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/M0;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Td0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/Td0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 8
    instance-of v0, p1, Lcom/android/tools/r8/internal/Xd0;

    if-eqz v0, :cond_0

    .line 9
    check-cast p1, Lcom/android/tools/r8/internal/Xd0;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Td0;->a(Lcom/android/tools/r8/internal/Xd0;)Lcom/android/tools/r8/internal/Td0;

    move-result-object p1

    goto :goto_0

    .line 11
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    move-object p1, p0

    :goto_0
    return-object p1
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/zU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Td0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/Td0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Td0;

    return-object p1
.end method

.method public final setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Td0;

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Td0;

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Td0;

    return-object p1
.end method
