.class public abstract synthetic Lcom/android/tools/r8/internal/Rd;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic a:[I

.field public static final synthetic b:[I


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    invoke-static {}, Lcom/android/tools/r8/internal/Sd;->a()[Lcom/android/tools/r8/internal/Sd;

    move-result-object v0

    array-length v0, v0

    new-array v0, v0, [I

    sput-object v0, Lcom/android/tools/r8/internal/Rd;->b:[I

    const/4 v1, 0x1

    :try_start_0
    sget-object v2, Lcom/android/tools/r8/internal/Sd;->b:Lcom/android/tools/r8/internal/Sd;

    invoke-virtual {v2}, Ljava/lang/Enum;->ordinal()I

    move-result v2

    aput v1, v0, v2
    :try_end_0
    .catch Ljava/lang/NoSuchFieldError; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    const/4 v0, 0x2

    :try_start_1
    sget-object v2, Lcom/android/tools/r8/internal/Rd;->b:[I

    sget-object v3, Lcom/android/tools/r8/internal/Sd;->c:Lcom/android/tools/r8/internal/Sd;

    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    move-result v3

    aput v0, v2, v3
    :try_end_1
    .catch Ljava/lang/NoSuchFieldError; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    const/4 v2, 0x3

    :try_start_2
    sget-object v3, Lcom/android/tools/r8/internal/Rd;->b:[I

    sget-object v4, Lcom/android/tools/r8/internal/Sd;->d:Lcom/android/tools/r8/internal/Sd;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    aput v2, v3, v4
    :try_end_2
    .catch Ljava/lang/NoSuchFieldError; {:try_start_2 .. :try_end_2} :catch_2

    .line 2
    :catch_2
    sget-object v3, Lcom/android/tools/r8/internal/UZ;->i:[Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v3}, [Lcom/android/tools/r8/internal/UZ;->clone()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, [Lcom/android/tools/r8/internal/UZ;

    .line 3
    array-length v3, v3

    new-array v3, v3, [I

    sput-object v3, Lcom/android/tools/r8/internal/Rd;->a:[I

    const/4 v4, 0x6

    :try_start_3
    aput v1, v3, v4
    :try_end_3
    .catch Ljava/lang/NoSuchFieldError; {:try_start_3 .. :try_end_3} :catch_3

    :catch_3
    :try_start_4
    sget-object v1, Lcom/android/tools/r8/internal/Rd;->a:[I

    const/4 v3, 0x5

    aput v0, v1, v3
    :try_end_4
    .catch Ljava/lang/NoSuchFieldError; {:try_start_4 .. :try_end_4} :catch_4

    :catch_4
    :try_start_5
    sget-object v0, Lcom/android/tools/r8/internal/Rd;->a:[I

    sget-object v1, Lcom/android/tools/r8/internal/UZ;->f:Lcom/android/tools/r8/internal/UZ;

    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    aput v2, v0, v1
    :try_end_5
    .catch Ljava/lang/NoSuchFieldError; {:try_start_5 .. :try_end_5} :catch_5

    :catch_5
    return-void
.end method
