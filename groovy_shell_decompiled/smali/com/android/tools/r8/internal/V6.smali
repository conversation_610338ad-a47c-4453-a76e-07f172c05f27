.class public abstract Lcom/android/tools/r8/internal/V6;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static synthetic a(Lcom/android/tools/r8/internal/Y9;Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/H9;
    .locals 0

    .line 2
    new-instance p1, Lcom/android/tools/r8/internal/Z9;

    sget-object p2, Lcom/android/tools/r8/internal/UZ;->e:Lcom/android/tools/r8/internal/UZ;

    invoke-direct {p1, p0, p2}, Lcom/android/tools/r8/internal/Z9;-><init>(Lcom/android/tools/r8/internal/Y9;Lcom/android/tools/r8/internal/UZ;)V

    return-object p1
.end method

.method public static a(Lcom/android/tools/r8/internal/Y9;)Lcom/android/tools/r8/internal/o5;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/V6$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/V6$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/Y9;)V

    return-object v0
.end method
