.class public final Lcom/android/tools/r8/internal/T60;
.super Lcom/android/tools/r8/internal/Qx;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final K:Lcom/android/tools/r8/internal/T60;

.field public static final L:Lcom/android/tools/r8/internal/Q60;


# instance fields
.field public A:Ljava/util/List;

.field public B:I

.field public C:Ljava/util/List;

.field public D:Ljava/util/List;

.field public E:I

.field public F:Lcom/android/tools/r8/internal/X70;

.field public G:Ljava/util/List;

.field public H:Lcom/android/tools/r8/internal/i80;

.field public I:B

.field public J:I

.field public final c:Lcom/android/tools/r8/internal/Y7;

.field public d:I

.field public e:I

.field public f:I

.field public g:I

.field public h:Ljava/util/List;

.field public i:Ljava/util/List;

.field public j:Ljava/util/List;

.field public k:I

.field public l:Ljava/util/List;

.field public m:I

.field public n:Ljava/util/List;

.field public o:Ljava/util/List;

.field public p:I

.field public q:Ljava/util/List;

.field public r:Ljava/util/List;

.field public s:Ljava/util/List;

.field public t:Ljava/util/List;

.field public u:Ljava/util/List;

.field public v:Ljava/util/List;

.field public w:I

.field public x:I

.field public y:Lcom/android/tools/r8/internal/N70;

.field public z:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Q60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Q60;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/T60;->L:Lcom/android/tools/r8/internal/Q60;

    .line 3729
    new-instance v0, Lcom/android/tools/r8/internal/T60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/T60;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/T60;->K:Lcom/android/tools/r8/internal/T60;

    .line 3730
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/T60;->f()V

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1370
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Qx;-><init>()V

    const/4 v0, -0x1

    .line 2046
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->k:I

    .line 2069
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->m:I

    .line 2127
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->p:I

    .line 2325
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->w:I

    .line 2393
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->B:I

    .line 2451
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->E:I

    .line 2543
    iput-byte v0, p0, Lcom/android/tools/r8/internal/T60;->I:B

    .line 2733
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->J:I

    .line 2734
    sget-object v0, Lcom/android/tools/r8/internal/Y7;->b:Lcom/android/tools/r8/internal/XR;

    iput-object v0, p0, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/R60;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Qx;-><init>(Lcom/android/tools/r8/internal/Ox;)V

    const/4 v0, -0x1

    .line 680
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->k:I

    .line 703
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->m:I

    .line 761
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->p:I

    .line 959
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->w:I

    .line 1027
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->B:I

    .line 1085
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->E:I

    .line 1177
    iput-byte v0, p0, Lcom/android/tools/r8/internal/T60;->I:B

    .line 1367
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->J:I

    .line 1368
    iget-object p1, p1, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    .line 1369
    iput-object p1, p0, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)V
    .locals 17

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object/from16 v3, p2

    .line 2735
    invoke-direct/range {p0 .. p0}, Lcom/android/tools/r8/internal/Qx;-><init>()V

    const/4 v4, -0x1

    .line 3396
    iput v4, v1, Lcom/android/tools/r8/internal/T60;->k:I

    .line 3419
    iput v4, v1, Lcom/android/tools/r8/internal/T60;->m:I

    .line 3477
    iput v4, v1, Lcom/android/tools/r8/internal/T60;->p:I

    .line 3675
    iput v4, v1, Lcom/android/tools/r8/internal/T60;->w:I

    .line 3743
    iput v4, v1, Lcom/android/tools/r8/internal/T60;->B:I

    .line 3801
    iput v4, v1, Lcom/android/tools/r8/internal/T60;->E:I

    .line 3893
    iput-byte v4, v1, Lcom/android/tools/r8/internal/T60;->I:B

    .line 4083
    iput v4, v1, Lcom/android/tools/r8/internal/T60;->J:I

    .line 4084
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/T60;->f()V

    .line 4087
    invoke-static {}, Lcom/android/tools/r8/internal/Y7;->d()Lcom/android/tools/r8/internal/W7;

    move-result-object v4

    .line 4089
    invoke-static {v4}, Lcom/android/tools/r8/internal/ie;->a(Lcom/android/tools/r8/internal/W7;)Lcom/android/tools/r8/internal/ie;

    move-result-object v5

    const/4 v7, 0x0

    move v8, v7

    :cond_0
    :goto_0
    const/high16 v9, 0x80000

    const/high16 v6, 0x400000

    const/high16 v14, 0x100000

    const/high16 v13, 0x40000

    const/16 v11, 0x80

    const/16 v10, 0x40

    if-nez v7, :cond_35

    .line 4094
    :try_start_0
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->i()I

    move-result v15

    const/16 v16, 0x0

    sparse-switch v15, :sswitch_data_0

    const/4 v12, 0x1

    .line 4100
    invoke-virtual {v1, v2, v5, v3, v15}, Lcom/android/tools/r8/internal/Qx;->a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/ie;Lcom/android/tools/r8/internal/Ku;I)Z

    move-result v6

    goto/16 :goto_9

    .line 4378
    :sswitch_0
    iget v15, v1, Lcom/android/tools/r8/internal/T60;->d:I

    and-int/2addr v15, v11

    if-ne v15, v11, :cond_1

    .line 4379
    iget-object v15, v1, Lcom/android/tools/r8/internal/T60;->H:Lcom/android/tools/r8/internal/i80;

    invoke-virtual {v15}, Lcom/android/tools/r8/internal/i80;->d()Lcom/android/tools/r8/internal/h80;

    move-result-object v16

    :cond_1
    move-object/from16 v15, v16

    .line 4381
    sget-object v12, Lcom/android/tools/r8/internal/i80;->g:Lcom/android/tools/r8/internal/g80;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    check-cast v12, Lcom/android/tools/r8/internal/i80;

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->H:Lcom/android/tools/r8/internal/i80;

    if-eqz v15, :cond_2

    .line 4383
    invoke-virtual {v15, v12}, Lcom/android/tools/r8/internal/h80;->a(Lcom/android/tools/r8/internal/i80;)Lcom/android/tools/r8/internal/h80;

    .line 4384
    invoke-virtual {v15}, Lcom/android/tools/r8/internal/h80;->c()Lcom/android/tools/r8/internal/i80;

    move-result-object v12

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->H:Lcom/android/tools/r8/internal/i80;

    .line 4386
    :cond_2
    iget v12, v1, Lcom/android/tools/r8/internal/T60;->d:I

    or-int/2addr v12, v11

    iput v12, v1, Lcom/android/tools/r8/internal/T60;->d:I

    goto/16 :goto_7

    .line 4387
    :sswitch_1
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v12

    .line 4388
    invoke-virtual {v2, v12}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v12

    and-int v15, v8, v6

    if-eq v15, v6, :cond_3

    .line 4389
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v15

    if-lez v15, :cond_3

    .line 4390
    new-instance v15, Ljava/util/ArrayList;

    invoke-direct {v15}, Ljava/util/ArrayList;-><init>()V

    iput-object v15, v1, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    or-int/2addr v8, v6

    .line 4393
    :cond_3
    :goto_1
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v15

    if-lez v15, :cond_4

    .line 4394
    iget-object v15, v1, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v16

    invoke-static/range {v16 .. v16}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v11

    invoke-interface {v15, v11}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/16 v11, 0x80

    goto :goto_1

    .line 4396
    :cond_4
    invoke-virtual {v2, v12}, Lcom/android/tools/r8/internal/be;->a(I)V

    goto :goto_0

    :sswitch_2
    and-int v11, v8, v6

    if-eq v11, v6, :cond_5

    .line 4397
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    or-int/2addr v8, v6

    .line 4400
    :cond_5
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v12

    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 4401
    :sswitch_3
    iget v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    and-int/2addr v11, v10

    if-ne v11, v10, :cond_6

    .line 4402
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->F:Lcom/android/tools/r8/internal/X70;

    invoke-virtual {v11}, Lcom/android/tools/r8/internal/X70;->d()Lcom/android/tools/r8/internal/W70;

    move-result-object v16

    :cond_6
    move-object/from16 v11, v16

    .line 4404
    sget-object v12, Lcom/android/tools/r8/internal/X70;->i:Lcom/android/tools/r8/internal/V70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    check-cast v12, Lcom/android/tools/r8/internal/X70;

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->F:Lcom/android/tools/r8/internal/X70;

    if-eqz v11, :cond_7

    .line 4406
    invoke-virtual {v11, v12}, Lcom/android/tools/r8/internal/W70;->a(Lcom/android/tools/r8/internal/X70;)Lcom/android/tools/r8/internal/W70;

    .line 4407
    invoke-virtual {v11}, Lcom/android/tools/r8/internal/W70;->c()Lcom/android/tools/r8/internal/X70;

    move-result-object v11

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->F:Lcom/android/tools/r8/internal/X70;

    .line 4409
    :cond_7
    iget v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    or-int/2addr v11, v10

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    goto/16 :goto_7

    .line 4410
    :sswitch_4
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v11

    .line 4411
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v11

    and-int v12, v8, v14

    if-eq v12, v14, :cond_8

    .line 4412
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_8

    .line 4413
    new-instance v12, Ljava/util/ArrayList;

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    or-int/2addr v8, v14

    .line 4416
    :cond_8
    :goto_2
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_9

    .line 4417
    iget-object v12, v1, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v15

    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    invoke-interface {v12, v15}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 4419
    :cond_9
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->a(I)V

    goto/16 :goto_0

    :sswitch_5
    and-int v11, v8, v14

    if-eq v11, v14, :cond_a

    .line 4420
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    or-int/2addr v8, v14

    .line 4423
    :cond_a
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v12

    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_6
    and-int v11, v8, v9

    if-eq v11, v9, :cond_b

    .line 4424
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    or-int/2addr v8, v9

    .line 4427
    :cond_b
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    sget-object v12, Lcom/android/tools/r8/internal/N70;->v:Lcom/android/tools/r8/internal/H70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 4428
    :sswitch_7
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v11

    .line 4429
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v11

    and-int v12, v8, v13

    if-eq v12, v13, :cond_c

    .line 4430
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_c

    .line 4431
    new-instance v12, Ljava/util/ArrayList;

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    or-int/2addr v8, v13

    .line 4434
    :cond_c
    :goto_3
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_d

    .line 4435
    iget-object v12, v1, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v15

    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    invoke-interface {v12, v15}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_3

    .line 4437
    :cond_d
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->a(I)V

    goto/16 :goto_0

    :sswitch_8
    and-int v11, v8, v13

    if-eq v11, v13, :cond_e

    .line 4438
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    or-int/2addr v8, v13

    .line 4441
    :cond_e
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v12

    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 4442
    :sswitch_9
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v11

    .line 4443
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v11

    and-int/lit16 v12, v8, 0x100

    const/16 v15, 0x100

    if-eq v12, v15, :cond_f

    .line 4444
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_f

    .line 4445
    new-instance v12, Ljava/util/ArrayList;

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    or-int/lit16 v8, v8, 0x100

    .line 4448
    :cond_f
    :goto_4
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_10

    .line 4449
    iget-object v12, v1, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v15

    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    invoke-interface {v12, v15}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_4

    .line 4451
    :cond_10
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->a(I)V

    goto/16 :goto_0

    :sswitch_a
    and-int/lit16 v11, v8, 0x100

    const/16 v12, 0x100

    if-eq v11, v12, :cond_11

    .line 4452
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    or-int/lit16 v8, v8, 0x100

    .line 4455
    :cond_11
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v12

    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_b
    and-int/lit16 v11, v8, 0x80

    const/16 v12, 0x80

    if-eq v11, v12, :cond_12

    .line 4456
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    or-int/lit16 v8, v8, 0x80

    .line 4459
    :cond_12
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    sget-object v12, Lcom/android/tools/r8/internal/N70;->v:Lcom/android/tools/r8/internal/H70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 4460
    :sswitch_c
    iget v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v12, 0x20

    or-int/2addr v11, v12

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    .line 4461
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v11

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->z:I

    goto/16 :goto_7

    .line 4462
    :sswitch_d
    iget v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v12, 0x10

    and-int/2addr v11, v12

    if-ne v11, v12, :cond_13

    .line 4463
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->y:Lcom/android/tools/r8/internal/N70;

    invoke-virtual {v11}, Lcom/android/tools/r8/internal/N70;->g()Lcom/android/tools/r8/internal/M70;

    move-result-object v16

    :cond_13
    move-object/from16 v11, v16

    .line 4465
    sget-object v12, Lcom/android/tools/r8/internal/N70;->v:Lcom/android/tools/r8/internal/H70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    check-cast v12, Lcom/android/tools/r8/internal/N70;

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->y:Lcom/android/tools/r8/internal/N70;

    if-eqz v11, :cond_14

    .line 4467
    invoke-virtual {v11, v12}, Lcom/android/tools/r8/internal/M70;->a(Lcom/android/tools/r8/internal/N70;)Lcom/android/tools/r8/internal/M70;

    .line 4468
    invoke-virtual {v11}, Lcom/android/tools/r8/internal/M70;->d()Lcom/android/tools/r8/internal/N70;

    move-result-object v11

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->y:Lcom/android/tools/r8/internal/N70;

    .line 4470
    :cond_14
    iget v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v12, 0x10

    or-int/2addr v11, v12

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    goto/16 :goto_7

    .line 4471
    :sswitch_e
    iget v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v12, 0x8

    or-int/2addr v11, v12

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    .line 4472
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v11

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->x:I

    goto/16 :goto_7

    .line 4473
    :sswitch_f
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v11

    .line 4474
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v11

    and-int/lit16 v12, v8, 0x4000

    const/16 v15, 0x4000

    if-eq v12, v15, :cond_15

    .line 4475
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_15

    .line 4476
    new-instance v12, Ljava/util/ArrayList;

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    or-int/lit16 v8, v8, 0x4000

    .line 4479
    :cond_15
    :goto_5
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_16

    .line 4480
    iget-object v12, v1, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v15

    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    invoke-interface {v12, v15}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_5

    .line 4482
    :cond_16
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->a(I)V

    goto/16 :goto_0

    :sswitch_10
    and-int/lit16 v11, v8, 0x4000

    const/16 v12, 0x4000

    if-eq v11, v12, :cond_17

    .line 4483
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    or-int/lit16 v8, v8, 0x4000

    .line 4486
    :cond_17
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v12

    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_11
    and-int/lit16 v11, v8, 0x2000

    const/16 v12, 0x2000

    if-eq v11, v12, :cond_18

    .line 4487
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    or-int/lit16 v8, v8, 0x2000

    .line 4490
    :cond_18
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    sget-object v12, Lcom/android/tools/r8/internal/h70;->i:Lcom/android/tools/r8/internal/f70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_12
    and-int/lit16 v11, v8, 0x1000

    const/16 v12, 0x1000

    if-eq v11, v12, :cond_19

    .line 4491
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    or-int/lit16 v8, v8, 0x1000

    .line 4494
    :cond_19
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    sget-object v12, Lcom/android/tools/r8/internal/Q70;->q:Lcom/android/tools/r8/internal/O70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_13
    and-int/lit16 v11, v8, 0x800

    const/16 v12, 0x800

    if-eq v11, v12, :cond_1a

    .line 4495
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    or-int/lit16 v8, v8, 0x800

    .line 4498
    :cond_1a
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    sget-object v12, Lcom/android/tools/r8/internal/w70;->w:Lcom/android/tools/r8/internal/u70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_14
    and-int/lit16 v11, v8, 0x400

    const/16 v12, 0x400

    if-eq v11, v12, :cond_1b

    .line 4499
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    or-int/lit16 v8, v8, 0x400

    .line 4502
    :cond_1b
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    sget-object v12, Lcom/android/tools/r8/internal/o70;->w:Lcom/android/tools/r8/internal/m70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_15
    and-int/lit16 v11, v8, 0x200

    const/16 v12, 0x200

    if-eq v11, v12, :cond_1c

    .line 4503
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    or-int/lit16 v8, v8, 0x200

    .line 4506
    :cond_1c
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    sget-object v12, Lcom/android/tools/r8/internal/W60;->k:Lcom/android/tools/r8/internal/U60;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 4507
    :sswitch_16
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v11

    .line 4508
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v11

    and-int/lit8 v12, v8, 0x40

    if-eq v12, v10, :cond_1d

    .line 4509
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_1d

    .line 4510
    new-instance v12, Ljava/util/ArrayList;

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    or-int/lit8 v8, v8, 0x40

    .line 4513
    :cond_1d
    :goto_6
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_1e

    .line 4514
    iget-object v12, v1, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v15

    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    invoke-interface {v12, v15}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_6

    .line 4516
    :cond_1e
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->a(I)V

    goto/16 :goto_0

    :sswitch_17
    and-int/lit8 v11, v8, 0x40

    if-eq v11, v10, :cond_1f

    .line 4517
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    or-int/lit8 v8, v8, 0x40

    .line 4520
    :cond_1f
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v12

    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_18
    and-int/lit8 v11, v8, 0x10

    const/16 v12, 0x10

    if-eq v11, v12, :cond_20

    .line 4521
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    or-int/lit8 v8, v8, 0x10

    .line 4524
    :cond_20
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    sget-object v12, Lcom/android/tools/r8/internal/N70;->v:Lcom/android/tools/r8/internal/H70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_19
    and-int/lit8 v11, v8, 0x8

    const/16 v12, 0x8

    if-eq v11, v12, :cond_21

    .line 4525
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    or-int/lit8 v8, v8, 0x8

    .line 4528
    :cond_21
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    sget-object v12, Lcom/android/tools/r8/internal/U70;->o:Lcom/android/tools/r8/internal/R70;

    invoke-virtual {v2, v12, v3}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 4529
    :sswitch_1a
    iget v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    or-int/lit8 v11, v11, 0x4

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    .line 4530
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v11

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->g:I

    goto :goto_7

    .line 4531
    :sswitch_1b
    iget v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    or-int/lit8 v11, v11, 0x2

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    .line 4532
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v11

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->f:I

    :goto_7
    const/4 v12, 0x1

    goto/16 :goto_0

    .line 4533
    :sswitch_1c
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v11

    .line 4534
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v11

    and-int/lit8 v12, v8, 0x20

    const/16 v15, 0x20

    if-eq v12, v15, :cond_22

    .line 4535
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_22

    .line 4536
    new-instance v12, Ljava/util/ArrayList;

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    iput-object v12, v1, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    or-int/lit8 v8, v8, 0x20

    .line 4539
    :cond_22
    :goto_8
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v12

    if-lez v12, :cond_23

    .line 4540
    iget-object v12, v1, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v15

    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    invoke-interface {v12, v15}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_8

    .line 4542
    :cond_23
    invoke-virtual {v2, v11}, Lcom/android/tools/r8/internal/be;->a(I)V

    goto/16 :goto_0

    :sswitch_1d
    and-int/lit8 v11, v8, 0x20

    const/16 v12, 0x20

    if-eq v11, v12, :cond_24

    .line 4543
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    iput-object v11, v1, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    or-int/lit8 v8, v8, 0x20

    .line 4546
    :cond_24
    iget-object v11, v1, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v12

    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    invoke-interface {v11, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 4547
    :sswitch_1e
    iget v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    const/4 v12, 0x1

    or-int/2addr v11, v12

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->d:I

    .line 4548
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/be;->c()I

    move-result v11

    iput v11, v1, Lcom/android/tools/r8/internal/T60;->e:I
    :try_end_0
    .catch Lcom/android/tools/r8/internal/kI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto/16 :goto_0

    :sswitch_1f
    const/4 v12, 0x1

    goto :goto_a

    :goto_9
    if-nez v6, :cond_0

    :goto_a
    move v7, v12

    goto/16 :goto_0

    :catchall_0
    move-exception v0

    move-object v2, v0

    goto :goto_b

    :catch_0
    move-exception v0

    move-object v2, v0

    .line 4834
    :try_start_1
    new-instance v3, Lcom/android/tools/r8/internal/kI;

    .line 4835
    invoke-virtual {v2}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v3, v2}, Lcom/android/tools/r8/internal/kI;-><init>(Ljava/lang/String;)V

    .line 4836
    iput-object v1, v3, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 4837
    throw v3

    :catch_1
    move-exception v0

    move-object v2, v0

    .line 4838
    iput-object v1, v2, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 4839
    throw v2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :goto_b
    and-int/lit8 v3, v8, 0x20

    const/16 v7, 0x20

    if-ne v3, v7, :cond_25

    .line 4845
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    :cond_25
    and-int/lit8 v3, v8, 0x8

    const/16 v7, 0x8

    if-ne v3, v7, :cond_26

    .line 4848
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    :cond_26
    and-int/lit8 v3, v8, 0x10

    const/16 v7, 0x10

    if-ne v3, v7, :cond_27

    .line 4851
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    :cond_27
    and-int/lit8 v3, v8, 0x40

    if-ne v3, v10, :cond_28

    .line 4854
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    :cond_28
    and-int/lit16 v3, v8, 0x200

    const/16 v7, 0x200

    if-ne v3, v7, :cond_29

    .line 4857
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    :cond_29
    and-int/lit16 v3, v8, 0x400

    const/16 v7, 0x400

    if-ne v3, v7, :cond_2a

    .line 4860
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    :cond_2a
    and-int/lit16 v3, v8, 0x800

    const/16 v7, 0x800

    if-ne v3, v7, :cond_2b

    .line 4863
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    :cond_2b
    and-int/lit16 v3, v8, 0x1000

    const/16 v7, 0x1000

    if-ne v3, v7, :cond_2c

    .line 4866
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    :cond_2c
    and-int/lit16 v3, v8, 0x2000

    const/16 v7, 0x2000

    if-ne v3, v7, :cond_2d

    .line 4869
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    :cond_2d
    and-int/lit16 v3, v8, 0x4000

    const/16 v7, 0x4000

    if-ne v3, v7, :cond_2e

    .line 4872
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    :cond_2e
    and-int/lit16 v3, v8, 0x80

    const/16 v7, 0x80

    if-ne v3, v7, :cond_2f

    .line 4875
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    :cond_2f
    and-int/lit16 v3, v8, 0x100

    const/16 v7, 0x100

    if-ne v3, v7, :cond_30

    .line 4878
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    :cond_30
    and-int v3, v8, v13

    if-ne v3, v13, :cond_31

    .line 4881
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    :cond_31
    and-int v3, v8, v9

    if-ne v3, v9, :cond_32

    .line 4884
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    :cond_32
    and-int v3, v8, v14

    if-ne v3, v14, :cond_33

    .line 4887
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    :cond_33
    and-int v3, v8, v6

    if-ne v3, v6, :cond_34

    .line 4890
    iget-object v3, v1, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    invoke-static {v3}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    .line 4893
    :cond_34
    :try_start_2
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/ie;->a()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 4897
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    goto :goto_c

    :catchall_1
    move-exception v0

    move-object v2, v0

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    .line 4898
    throw v2

    .line 4899
    :catch_2
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    .line 4900
    :goto_c
    iget-object v3, v1, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Mv;->a()V

    .line 4901
    throw v2

    :cond_35
    and-int/lit8 v2, v8, 0x20

    const/16 v3, 0x20

    if-ne v2, v3, :cond_36

    .line 4902
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    :cond_36
    and-int/lit8 v2, v8, 0x8

    const/16 v3, 0x8

    if-ne v2, v3, :cond_37

    .line 4905
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    :cond_37
    and-int/lit8 v2, v8, 0x10

    const/16 v3, 0x10

    if-ne v2, v3, :cond_38

    .line 4908
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    :cond_38
    and-int/lit8 v2, v8, 0x40

    if-ne v2, v10, :cond_39

    .line 4911
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    :cond_39
    and-int/lit16 v2, v8, 0x200

    const/16 v3, 0x200

    if-ne v2, v3, :cond_3a

    .line 4914
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    :cond_3a
    and-int/lit16 v2, v8, 0x400

    const/16 v3, 0x400

    if-ne v2, v3, :cond_3b

    .line 4917
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    :cond_3b
    and-int/lit16 v2, v8, 0x800

    const/16 v3, 0x800

    if-ne v2, v3, :cond_3c

    .line 4920
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    :cond_3c
    and-int/lit16 v2, v8, 0x1000

    const/16 v3, 0x1000

    if-ne v2, v3, :cond_3d

    .line 4923
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    :cond_3d
    and-int/lit16 v2, v8, 0x2000

    const/16 v3, 0x2000

    if-ne v2, v3, :cond_3e

    .line 4926
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    :cond_3e
    and-int/lit16 v2, v8, 0x4000

    const/16 v3, 0x4000

    if-ne v2, v3, :cond_3f

    .line 4929
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    :cond_3f
    and-int/lit16 v2, v8, 0x80

    const/16 v3, 0x80

    if-ne v2, v3, :cond_40

    .line 4932
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    :cond_40
    and-int/lit16 v2, v8, 0x100

    const/16 v3, 0x100

    if-ne v2, v3, :cond_41

    .line 4935
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    :cond_41
    and-int v2, v8, v13

    if-ne v2, v13, :cond_42

    .line 4938
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    :cond_42
    and-int v2, v8, v9

    if-ne v2, v9, :cond_43

    .line 4941
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    :cond_43
    and-int v2, v8, v14

    if-ne v2, v14, :cond_44

    .line 4944
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    :cond_44
    and-int v2, v8, v6

    if-ne v2, v6, :cond_45

    .line 4947
    iget-object v2, v1, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    .line 4950
    :cond_45
    :try_start_3
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/ie;->a()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    .line 4954
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    goto :goto_d

    :catchall_2
    move-exception v0

    move-object v2, v0

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object v3

    iput-object v3, v1, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    .line 4955
    throw v2

    .line 4956
    :catch_3
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object v2

    iput-object v2, v1, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    .line 4957
    :goto_d
    iget-object v2, v1, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Mv;->a()V

    return-void

    nop

    :sswitch_data_0
    .sparse-switch
        0x0 -> :sswitch_1f
        0x8 -> :sswitch_1e
        0x10 -> :sswitch_1d
        0x12 -> :sswitch_1c
        0x18 -> :sswitch_1b
        0x20 -> :sswitch_1a
        0x2a -> :sswitch_19
        0x32 -> :sswitch_18
        0x38 -> :sswitch_17
        0x3a -> :sswitch_16
        0x42 -> :sswitch_15
        0x4a -> :sswitch_14
        0x52 -> :sswitch_13
        0x5a -> :sswitch_12
        0x6a -> :sswitch_11
        0x80 -> :sswitch_10
        0x82 -> :sswitch_f
        0x88 -> :sswitch_e
        0x92 -> :sswitch_d
        0x98 -> :sswitch_c
        0xa2 -> :sswitch_b
        0xa8 -> :sswitch_a
        0xaa -> :sswitch_9
        0xb0 -> :sswitch_8
        0xb2 -> :sswitch_7
        0xba -> :sswitch_6
        0xc0 -> :sswitch_5
        0xc2 -> :sswitch_4
        0xf2 -> :sswitch_3
        0xf8 -> :sswitch_2
        0xfa -> :sswitch_1
        0x102 -> :sswitch_0
    .end sparse-switch
.end method


# virtual methods
.method public final a()I
    .locals 8

    .line 108
    iget v0, p0, Lcom/android/tools/r8/internal/T60;->J:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    .line 112
    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/4 v1, 0x1

    and-int/2addr v0, v1

    const/4 v2, 0x0

    if-ne v0, v1, :cond_1

    .line 113
    iget v0, p0, Lcom/android/tools/r8/internal/T60;->e:I

    .line 114
    invoke-static {v1, v0}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v0

    goto :goto_0

    :cond_1
    move v0, v2

    :goto_0
    move v1, v2

    move v3, v1

    .line 118
    :goto_1
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    const/16 v5, 0xa

    if-ge v1, v4, :cond_3

    .line 119
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    .line 120
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    if-ltz v4, :cond_2

    .line 121
    invoke-static {v4}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v5

    :cond_2
    add-int/2addr v3, v5

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_3
    add-int/2addr v0, v3

    .line 122
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    .line 123
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_5

    add-int/lit8 v0, v0, 0x1

    if-ltz v3, :cond_4

    .line 124
    invoke-static {v3}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v1

    goto :goto_2

    :cond_4
    move v1, v5

    :goto_2
    add-int/2addr v0, v1

    .line 125
    :cond_5
    iput v3, p0, Lcom/android/tools/r8/internal/T60;->k:I

    .line 127
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/4 v3, 0x2

    and-int/2addr v1, v3

    if-ne v1, v3, :cond_6

    .line 128
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->f:I

    const/4 v4, 0x3

    .line 129
    invoke-static {v4, v1}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v1

    add-int/2addr v0, v1

    .line 131
    :cond_6
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/4 v4, 0x4

    and-int/2addr v1, v4

    if-ne v1, v4, :cond_7

    .line 132
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->g:I

    .line 133
    invoke-static {v4, v1}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v1

    add-int/2addr v0, v1

    :cond_7
    move v1, v2

    .line 135
    :goto_3
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_8

    .line 136
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    .line 137
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/4 v6, 0x5

    invoke-static {v6, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    :cond_8
    move v1, v2

    .line 139
    :goto_4
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_9

    .line 140
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    .line 141
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/4 v6, 0x6

    invoke-static {v6, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    add-int/lit8 v1, v1, 0x1

    goto :goto_4

    :cond_9
    move v1, v2

    move v4, v1

    .line 145
    :goto_5
    iget-object v6, p0, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    if-ge v1, v6, :cond_b

    .line 146
    iget-object v6, p0, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    .line 147
    invoke-interface {v6, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/Integer;

    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    move-result v6

    if-ltz v6, :cond_a

    .line 148
    invoke-static {v6}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v6

    goto :goto_6

    :cond_a
    move v6, v5

    :goto_6
    add-int/2addr v4, v6

    add-int/lit8 v1, v1, 0x1

    goto :goto_5

    :cond_b
    add-int/2addr v0, v4

    .line 149
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    .line 150
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_d

    add-int/lit8 v0, v0, 0x1

    if-ltz v4, :cond_c

    .line 151
    invoke-static {v4}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v1

    goto :goto_7

    :cond_c
    move v1, v5

    :goto_7
    add-int/2addr v0, v1

    .line 152
    :cond_d
    iput v4, p0, Lcom/android/tools/r8/internal/T60;->m:I

    move v1, v2

    .line 154
    :goto_8
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    const/16 v6, 0x8

    if-ge v1, v4, :cond_e

    .line 155
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    .line 156
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    invoke-static {v6, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    add-int/lit8 v1, v1, 0x1

    goto :goto_8

    :cond_e
    move v1, v2

    .line 158
    :goto_9
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_f

    .line 159
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    .line 160
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v7, 0x9

    invoke-static {v7, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    add-int/lit8 v1, v1, 0x1

    goto :goto_9

    :cond_f
    move v1, v2

    .line 162
    :goto_a
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_10

    .line 163
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    .line 164
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    invoke-static {v5, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    add-int/lit8 v1, v1, 0x1

    goto :goto_a

    :cond_10
    move v1, v2

    .line 166
    :goto_b
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_11

    .line 167
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    .line 168
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v7, 0xb

    invoke-static {v7, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    add-int/lit8 v1, v1, 0x1

    goto :goto_b

    :cond_11
    move v1, v2

    .line 170
    :goto_c
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_12

    .line 171
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    .line 172
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v7, 0xd

    invoke-static {v7, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    add-int/lit8 v1, v1, 0x1

    goto :goto_c

    :cond_12
    move v1, v2

    move v4, v1

    .line 176
    :goto_d
    iget-object v7, p0, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    invoke-interface {v7}, Ljava/util/List;->size()I

    move-result v7

    if-ge v1, v7, :cond_14

    .line 177
    iget-object v7, p0, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    .line 178
    invoke-interface {v7, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/Integer;

    invoke-virtual {v7}, Ljava/lang/Integer;->intValue()I

    move-result v7

    if-ltz v7, :cond_13

    .line 179
    invoke-static {v7}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v7

    goto :goto_e

    :cond_13
    move v7, v5

    :goto_e
    add-int/2addr v4, v7

    add-int/lit8 v1, v1, 0x1

    goto :goto_d

    :cond_14
    add-int/2addr v0, v4

    .line 180
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    .line 181
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_16

    add-int/lit8 v0, v0, 0x2

    if-ltz v4, :cond_15

    .line 182
    invoke-static {v4}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v1

    goto :goto_f

    :cond_15
    move v1, v5

    :goto_f
    add-int/2addr v0, v1

    .line 183
    :cond_16
    iput v4, p0, Lcom/android/tools/r8/internal/T60;->w:I

    .line 185
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    and-int/2addr v1, v6

    if-ne v1, v6, :cond_17

    .line 186
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->x:I

    const/16 v4, 0x11

    .line 187
    invoke-static {v4, v1}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v1

    add-int/2addr v0, v1

    .line 189
    :cond_17
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v4, 0x10

    and-int/2addr v1, v4

    if-ne v1, v4, :cond_18

    .line 190
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->y:Lcom/android/tools/r8/internal/N70;

    const/16 v4, 0x12

    .line 191
    invoke-static {v4, v1}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v1

    add-int/2addr v0, v1

    .line 193
    :cond_18
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v4, 0x20

    and-int/2addr v1, v4

    if-ne v1, v4, :cond_19

    .line 194
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->z:I

    const/16 v6, 0x13

    .line 195
    invoke-static {v6, v1}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v1

    add-int/2addr v0, v1

    :cond_19
    move v1, v2

    .line 197
    :goto_10
    iget-object v6, p0, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    if-ge v1, v6, :cond_1a

    .line 198
    iget-object v6, p0, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    .line 199
    invoke-interface {v6, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lcom/android/tools/r8/internal/N0;

    const/16 v7, 0x14

    invoke-static {v7, v6}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v6

    add-int/2addr v0, v6

    add-int/lit8 v1, v1, 0x1

    goto :goto_10

    :cond_1a
    move v1, v2

    move v6, v1

    .line 203
    :goto_11
    iget-object v7, p0, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    invoke-interface {v7}, Ljava/util/List;->size()I

    move-result v7

    if-ge v1, v7, :cond_1c

    .line 204
    iget-object v7, p0, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    .line 205
    invoke-interface {v7, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/Integer;

    invoke-virtual {v7}, Ljava/lang/Integer;->intValue()I

    move-result v7

    if-ltz v7, :cond_1b

    .line 206
    invoke-static {v7}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v7

    goto :goto_12

    :cond_1b
    move v7, v5

    :goto_12
    add-int/2addr v6, v7

    add-int/lit8 v1, v1, 0x1

    goto :goto_11

    :cond_1c
    add-int/2addr v0, v6

    .line 207
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    .line 208
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_1e

    add-int/lit8 v0, v0, 0x2

    if-ltz v6, :cond_1d

    .line 209
    invoke-static {v6}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v1

    goto :goto_13

    :cond_1d
    move v1, v5

    :goto_13
    add-int/2addr v0, v1

    .line 210
    :cond_1e
    iput v6, p0, Lcom/android/tools/r8/internal/T60;->p:I

    move v1, v2

    move v6, v1

    .line 214
    :goto_14
    iget-object v7, p0, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    invoke-interface {v7}, Ljava/util/List;->size()I

    move-result v7

    if-ge v1, v7, :cond_20

    .line 215
    iget-object v7, p0, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    .line 216
    invoke-interface {v7, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/Integer;

    invoke-virtual {v7}, Ljava/lang/Integer;->intValue()I

    move-result v7

    if-ltz v7, :cond_1f

    .line 217
    invoke-static {v7}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v7

    goto :goto_15

    :cond_1f
    move v7, v5

    :goto_15
    add-int/2addr v6, v7

    add-int/lit8 v1, v1, 0x1

    goto :goto_14

    :cond_20
    add-int/2addr v0, v6

    .line 218
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    .line 219
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_22

    add-int/lit8 v0, v0, 0x2

    if-ltz v6, :cond_21

    .line 220
    invoke-static {v6}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v1

    goto :goto_16

    :cond_21
    move v1, v5

    :goto_16
    add-int/2addr v0, v1

    .line 221
    :cond_22
    iput v6, p0, Lcom/android/tools/r8/internal/T60;->B:I

    move v1, v2

    .line 223
    :goto_17
    iget-object v6, p0, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    if-ge v1, v6, :cond_23

    .line 224
    iget-object v6, p0, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    .line 225
    invoke-interface {v6, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lcom/android/tools/r8/internal/N0;

    const/16 v7, 0x17

    invoke-static {v7, v6}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v6

    add-int/2addr v0, v6

    add-int/lit8 v1, v1, 0x1

    goto :goto_17

    :cond_23
    move v1, v2

    move v6, v1

    .line 229
    :goto_18
    iget-object v7, p0, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    invoke-interface {v7}, Ljava/util/List;->size()I

    move-result v7

    if-ge v1, v7, :cond_25

    .line 230
    iget-object v7, p0, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    .line 231
    invoke-interface {v7, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/Integer;

    invoke-virtual {v7}, Ljava/lang/Integer;->intValue()I

    move-result v7

    if-ltz v7, :cond_24

    .line 232
    invoke-static {v7}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v7

    goto :goto_19

    :cond_24
    move v7, v5

    :goto_19
    add-int/2addr v6, v7

    add-int/lit8 v1, v1, 0x1

    goto :goto_18

    :cond_25
    add-int/2addr v0, v6

    .line 233
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    .line 234
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_27

    add-int/lit8 v0, v0, 0x2

    if-ltz v6, :cond_26

    .line 235
    invoke-static {v6}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v1

    goto :goto_1a

    :cond_26
    move v1, v5

    :goto_1a
    add-int/2addr v0, v1

    .line 236
    :cond_27
    iput v6, p0, Lcom/android/tools/r8/internal/T60;->E:I

    .line 238
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v6, 0x40

    and-int/2addr v1, v6

    if-ne v1, v6, :cond_28

    .line 239
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->F:Lcom/android/tools/r8/internal/X70;

    const/16 v6, 0x1e

    .line 240
    invoke-static {v6, v1}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v1

    add-int/2addr v0, v1

    :cond_28
    move v1, v2

    .line 244
    :goto_1b
    iget-object v6, p0, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    if-ge v2, v6, :cond_2a

    .line 245
    iget-object v6, p0, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    .line 246
    invoke-interface {v6, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/Integer;

    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    move-result v6

    if-ltz v6, :cond_29

    .line 247
    invoke-static {v6}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v6

    goto :goto_1c

    :cond_29
    move v6, v5

    :goto_1c
    add-int/2addr v1, v6

    add-int/lit8 v2, v2, 0x1

    goto :goto_1b

    :cond_2a
    add-int/2addr v0, v1

    .line 248
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    .line 249
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    mul-int/2addr v1, v3

    add-int/2addr v1, v0

    .line 251
    iget v0, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v2, 0x80

    and-int/2addr v0, v2

    if-ne v0, v2, :cond_2b

    .line 252
    iget-object v0, p0, Lcom/android/tools/r8/internal/T60;->H:Lcom/android/tools/r8/internal/i80;

    .line 253
    invoke-static {v4, v0}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v0

    add-int/2addr v1, v0

    .line 255
    :cond_2b
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Qx;->e()I

    move-result v0

    add-int/2addr v0, v1

    .line 256
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Y7;->size()I

    move-result v1

    add-int/2addr v1, v0

    .line 257
    iput v1, p0, Lcom/android/tools/r8/internal/T60;->J:I

    return v1
.end method

.method public final a(Lcom/android/tools/r8/internal/ie;)V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/T60;->a()I

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Px;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/Px;-><init>(Lcom/android/tools/r8/internal/Qx;)V

    .line 3
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/4 v2, 0x1

    and-int/2addr v1, v2

    const/4 v3, 0x0

    if-ne v1, v2, :cond_0

    .line 4
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->e:I

    .line 5
    invoke-virtual {p1, v2, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 6
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 7
    :cond_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    .line 8
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    const/16 v2, 0x12

    if-lez v1, :cond_1

    .line 9
    invoke-virtual {p1, v2}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 10
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->k:I

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    :cond_1
    move v1, v3

    .line 12
    :goto_0
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_2

    .line 13
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 15
    :cond_2
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/4 v4, 0x2

    and-int/2addr v1, v4

    if-ne v1, v4, :cond_3

    const/4 v1, 0x3

    .line 16
    iget v4, p0, Lcom/android/tools/r8/internal/T60;->f:I

    .line 17
    invoke-virtual {p1, v1, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 18
    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 19
    :cond_3
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/4 v4, 0x4

    and-int/2addr v1, v4

    if-ne v1, v4, :cond_4

    .line 20
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->g:I

    .line 21
    invoke-virtual {p1, v4, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 22
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->d(I)V

    :cond_4
    move v1, v3

    .line 23
    :goto_1
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_5

    .line 24
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/4 v5, 0x5

    invoke-virtual {p1, v5, v4}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_5
    move v1, v3

    .line 26
    :goto_2
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_6

    .line 27
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/4 v5, 0x6

    invoke-virtual {p1, v5, v4}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_2

    .line 28
    :cond_6
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    .line 29
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_7

    const/16 v1, 0x3a

    .line 30
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 31
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->m:I

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    :cond_7
    move v1, v3

    .line 33
    :goto_3
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_8

    .line 34
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    :cond_8
    move v1, v3

    .line 36
    :goto_4
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    const/16 v5, 0x8

    if-ge v1, v4, :cond_9

    .line 37
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    invoke-virtual {p1, v5, v4}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_4

    :cond_9
    move v1, v3

    .line 39
    :goto_5
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_a

    .line 40
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v6, 0x9

    invoke-virtual {p1, v6, v4}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_5

    :cond_a
    move v1, v3

    .line 42
    :goto_6
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_b

    .line 43
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v6, 0xa

    invoke-virtual {p1, v6, v4}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_6

    :cond_b
    move v1, v3

    .line 45
    :goto_7
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_c

    .line 46
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v6, 0xb

    invoke-virtual {p1, v6, v4}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_7

    :cond_c
    move v1, v3

    .line 48
    :goto_8
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_d

    .line 49
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v6, 0xd

    invoke-virtual {p1, v6, v4}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_8

    .line 50
    :cond_d
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    .line 51
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_e

    const/16 v1, 0x82

    .line 52
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 53
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->w:I

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    :cond_e
    move v1, v3

    .line 55
    :goto_9
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_f

    .line 56
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_9

    .line 58
    :cond_f
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    and-int/2addr v1, v5

    if-ne v1, v5, :cond_10

    const/16 v1, 0x11

    .line 59
    iget v4, p0, Lcom/android/tools/r8/internal/T60;->x:I

    .line 60
    invoke-virtual {p1, v1, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 61
    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 62
    :cond_10
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v4, 0x10

    and-int/2addr v1, v4

    if-ne v1, v4, :cond_11

    .line 63
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->y:Lcom/android/tools/r8/internal/N70;

    invoke-virtual {p1, v2, v1}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    .line 65
    :cond_11
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v2, 0x20

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_12

    const/16 v1, 0x13

    .line 66
    iget v4, p0, Lcom/android/tools/r8/internal/T60;->z:I

    .line 67
    invoke-virtual {p1, v1, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 68
    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    :cond_12
    move v1, v3

    .line 69
    :goto_a
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_13

    .line 70
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v5, 0x14

    invoke-virtual {p1, v5, v4}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_a

    .line 71
    :cond_13
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    .line 72
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_14

    const/16 v1, 0xaa

    .line 73
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 74
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->p:I

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    :cond_14
    move v1, v3

    .line 76
    :goto_b
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_15

    .line 77
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_b

    .line 78
    :cond_15
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    .line 79
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_16

    const/16 v1, 0xb2

    .line 80
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 81
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->B:I

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    :cond_16
    move v1, v3

    .line 83
    :goto_c
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_17

    .line 84
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_c

    :cond_17
    move v1, v3

    .line 86
    :goto_d
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_18

    .line 87
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v5, 0x17

    invoke-virtual {p1, v5, v4}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_d

    .line 88
    :cond_18
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    .line 89
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_19

    const/16 v1, 0xc2

    .line 90
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 91
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->E:I

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    :cond_19
    move v1, v3

    .line 93
    :goto_e
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_1a

    .line 94
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_e

    .line 96
    :cond_1a
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v4, 0x40

    and-int/2addr v1, v4

    if-ne v1, v4, :cond_1b

    .line 97
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->F:Lcom/android/tools/r8/internal/X70;

    const/16 v4, 0x1e

    invoke-virtual {p1, v4, v1}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    :cond_1b
    move v1, v3

    .line 99
    :goto_f
    iget-object v4, p0, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_1c

    const/16 v4, 0x1f

    .line 100
    iget-object v5, p0, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    invoke-interface {v5, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/Integer;

    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    move-result v5

    .line 101
    invoke-virtual {p1, v4, v3}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 102
    invoke-virtual {p1, v5}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_f

    .line 103
    :cond_1c
    iget v1, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v3, 0x80

    and-int/2addr v1, v3

    if-ne v1, v3, :cond_1d

    .line 104
    iget-object v1, p0, Lcom/android/tools/r8/internal/T60;->H:Lcom/android/tools/r8/internal/i80;

    invoke-virtual {p1, v2, v1}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    :cond_1d
    const/16 v1, 0x4a38

    .line 106
    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Px;->a(ILcom/android/tools/r8/internal/ie;)V

    .line 107
    iget-object v0, p0, Lcom/android/tools/r8/internal/T60;->c:Lcom/android/tools/r8/internal/Y7;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/ie;->a(Lcom/android/tools/r8/internal/Y7;)V

    return-void
.end method

.method public final b()Lcom/android/tools/r8/internal/Nx;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/R60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/R60;-><init>()V

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/Nx;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/R60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/R60;-><init>()V

    .line 2
    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/R60;->a(Lcom/android/tools/r8/internal/T60;)Lcom/android/tools/r8/internal/R60;

    move-result-object v0

    return-object v0
.end method

.method public final f()V
    .locals 2

    const/4 v0, 0x6

    .line 1
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->e:I

    const/4 v0, 0x0

    .line 2
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->f:I

    .line 3
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->g:I

    .line 4
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    .line 5
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    .line 6
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->j:Ljava/util/List;

    .line 7
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->l:Ljava/util/List;

    .line 8
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    .line 9
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->o:Ljava/util/List;

    .line 10
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    .line 11
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    .line 12
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    .line 13
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    .line 14
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    .line 15
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->v:Ljava/util/List;

    .line 16
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->x:I

    .line 17
    sget-object v1, Lcom/android/tools/r8/internal/N70;->u:Lcom/android/tools/r8/internal/N70;

    .line 18
    iput-object v1, p0, Lcom/android/tools/r8/internal/T60;->y:Lcom/android/tools/r8/internal/N70;

    .line 19
    iput v0, p0, Lcom/android/tools/r8/internal/T60;->z:I

    .line 20
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/T60;->A:Ljava/util/List;

    .line 21
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    .line 22
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/T60;->D:Ljava/util/List;

    .line 23
    sget-object v0, Lcom/android/tools/r8/internal/X70;->h:Lcom/android/tools/r8/internal/X70;

    .line 24
    iput-object v0, p0, Lcom/android/tools/r8/internal/T60;->F:Lcom/android/tools/r8/internal/X70;

    .line 25
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/T60;->G:Ljava/util/List;

    .line 26
    sget-object v0, Lcom/android/tools/r8/internal/i80;->f:Lcom/android/tools/r8/internal/i80;

    .line 27
    iput-object v0, p0, Lcom/android/tools/r8/internal/T60;->H:Lcom/android/tools/r8/internal/i80;

    return-void
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/N0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/T60;->K:Lcom/android/tools/r8/internal/T60;

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 4

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/internal/T60;->I:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v2, 0x0

    if-nez v0, :cond_1

    return v2

    .line 2
    :cond_1
    iget v0, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/4 v3, 0x2

    and-int/2addr v0, v3

    if-ne v0, v3, :cond_17

    move v0, v2

    .line 3
    :goto_0
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_3

    .line 4
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->h:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/U70;

    .line 5
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/U70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_2

    .line 6
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_3
    move v0, v2

    .line 7
    :goto_1
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_5

    .line 8
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->i:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/N70;

    .line 9
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/N70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_4

    .line 10
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_4
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_5
    move v0, v2

    .line 11
    :goto_2
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_7

    .line 12
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->n:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/N70;

    .line 13
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/N70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_6

    .line 14
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_6
    add-int/lit8 v0, v0, 0x1

    goto :goto_2

    :cond_7
    move v0, v2

    .line 15
    :goto_3
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_9

    .line 16
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->q:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/W60;

    .line 17
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/W60;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_8

    .line 18
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_8
    add-int/lit8 v0, v0, 0x1

    goto :goto_3

    :cond_9
    move v0, v2

    .line 19
    :goto_4
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_b

    .line 20
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->r:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/o70;

    .line 21
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/o70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_a

    .line 22
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_a
    add-int/lit8 v0, v0, 0x1

    goto :goto_4

    :cond_b
    move v0, v2

    .line 23
    :goto_5
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_d

    .line 24
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->s:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/w70;

    .line 25
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/w70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_c

    .line 26
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_c
    add-int/lit8 v0, v0, 0x1

    goto :goto_5

    :cond_d
    move v0, v2

    .line 27
    :goto_6
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_f

    .line 28
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->t:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/Q70;

    .line 29
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Q70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_e

    .line 30
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_e
    add-int/lit8 v0, v0, 0x1

    goto :goto_6

    :cond_f
    move v0, v2

    .line 31
    :goto_7
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_11

    .line 32
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->u:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/h70;

    .line 33
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/h70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_10

    .line 34
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_10
    add-int/lit8 v0, v0, 0x1

    goto :goto_7

    .line 35
    :cond_11
    iget v0, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v3, 0x10

    and-int/2addr v0, v3

    if-ne v0, v3, :cond_12

    .line 36
    iget-object v0, p0, Lcom/android/tools/r8/internal/T60;->y:Lcom/android/tools/r8/internal/N70;

    .line 37
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/N70;->isInitialized()Z

    move-result v0

    if-nez v0, :cond_12

    .line 38
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_12
    move v0, v2

    .line 39
    :goto_8
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_14

    .line 40
    iget-object v3, p0, Lcom/android/tools/r8/internal/T60;->C:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/N70;

    .line 41
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/N70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_13

    .line 42
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    :cond_13
    add-int/lit8 v0, v0, 0x1

    goto :goto_8

    .line 43
    :cond_14
    iget v0, p0, Lcom/android/tools/r8/internal/T60;->d:I

    const/16 v3, 0x40

    and-int/2addr v0, v3

    if-ne v0, v3, :cond_15

    .line 44
    iget-object v0, p0, Lcom/android/tools/r8/internal/T60;->F:Lcom/android/tools/r8/internal/X70;

    .line 45
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/X70;->isInitialized()Z

    move-result v0

    if-nez v0, :cond_15

    .line 46
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    .line 50
    :cond_15
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Qx;->d()Z

    move-result v0

    if-nez v0, :cond_16

    .line 51
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2

    .line 54
    :cond_16
    iput-byte v1, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v1

    .line 55
    :cond_17
    iput-byte v2, p0, Lcom/android/tools/r8/internal/T60;->I:B

    return v2
.end method
