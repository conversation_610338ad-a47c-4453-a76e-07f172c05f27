.class public final enum Lcom/android/tools/r8/internal/UZ;
.super Ljava/lang/Enum;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/android/tools/r8/internal/UZ;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum b:Lcom/android/tools/r8/internal/UZ;

.field public static final enum c:Lcom/android/tools/r8/internal/UZ;

.field public static final enum d:Lcom/android/tools/r8/internal/UZ;

.field public static final enum e:Lcom/android/tools/r8/internal/UZ;

.field public static final enum f:Lcom/android/tools/r8/internal/UZ;

.field public static final enum g:Lcom/android/tools/r8/internal/UZ;

.field public static final enum h:Lcom/android/tools/r8/internal/UZ;

.field public static final synthetic i:[Lcom/android/tools/r8/internal/UZ;


# direct methods
.method static constructor <clinit>()V
    .locals 15

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/UZ;

    const/4 v1, 0x0

    const-string v2, "BYTE"

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/UZ;-><init>(ILjava/lang/String;)V

    sput-object v0, Lcom/android/tools/r8/internal/UZ;->b:Lcom/android/tools/r8/internal/UZ;

    .line 2
    new-instance v2, Lcom/android/tools/r8/internal/UZ;

    const/4 v3, 0x1

    const-string v4, "CHAR"

    invoke-direct {v2, v3, v4}, Lcom/android/tools/r8/internal/UZ;-><init>(ILjava/lang/String;)V

    sput-object v2, Lcom/android/tools/r8/internal/UZ;->c:Lcom/android/tools/r8/internal/UZ;

    .line 3
    new-instance v4, Lcom/android/tools/r8/internal/UZ;

    const/4 v5, 0x2

    const-string v6, "SHORT"

    invoke-direct {v4, v5, v6}, Lcom/android/tools/r8/internal/UZ;-><init>(ILjava/lang/String;)V

    sput-object v4, Lcom/android/tools/r8/internal/UZ;->d:Lcom/android/tools/r8/internal/UZ;

    .line 4
    new-instance v6, Lcom/android/tools/r8/internal/UZ;

    const/4 v7, 0x3

    const-string v8, "INT"

    invoke-direct {v6, v7, v8}, Lcom/android/tools/r8/internal/UZ;-><init>(ILjava/lang/String;)V

    sput-object v6, Lcom/android/tools/r8/internal/UZ;->e:Lcom/android/tools/r8/internal/UZ;

    .line 5
    new-instance v8, Lcom/android/tools/r8/internal/UZ;

    const/4 v9, 0x4

    const-string v10, "LONG"

    invoke-direct {v8, v9, v10}, Lcom/android/tools/r8/internal/UZ;-><init>(ILjava/lang/String;)V

    sput-object v8, Lcom/android/tools/r8/internal/UZ;->f:Lcom/android/tools/r8/internal/UZ;

    .line 6
    new-instance v10, Lcom/android/tools/r8/internal/UZ;

    const/4 v11, 0x5

    const-string v12, "FLOAT"

    invoke-direct {v10, v11, v12}, Lcom/android/tools/r8/internal/UZ;-><init>(ILjava/lang/String;)V

    sput-object v10, Lcom/android/tools/r8/internal/UZ;->g:Lcom/android/tools/r8/internal/UZ;

    .line 7
    new-instance v12, Lcom/android/tools/r8/internal/UZ;

    const/4 v13, 0x6

    const-string v14, "DOUBLE"

    invoke-direct {v12, v13, v14}, Lcom/android/tools/r8/internal/UZ;-><init>(ILjava/lang/String;)V

    sput-object v12, Lcom/android/tools/r8/internal/UZ;->h:Lcom/android/tools/r8/internal/UZ;

    const/4 v14, 0x7

    new-array v14, v14, [Lcom/android/tools/r8/internal/UZ;

    aput-object v0, v14, v1

    aput-object v2, v14, v3

    aput-object v4, v14, v5

    aput-object v6, v14, v7

    aput-object v8, v14, v9

    aput-object v10, v14, v11

    aput-object v12, v14, v13

    .line 8
    sput-object v14, Lcom/android/tools/r8/internal/UZ;->i:[Lcom/android/tools/r8/internal/UZ;

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p2, p1}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/UZ;
    .locals 1

    .line 25
    iget-object p0, p0, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    iget-object p0, p0, Lcom/android/tools/r8/graph/I2;->f:[B

    const/4 v0, 0x0

    aget-byte p0, p0, v0

    const/16 v0, 0x46

    if-eq p0, v0, :cond_3

    const/16 v0, 0x53

    if-eq p0, v0, :cond_2

    const/16 v0, 0x49

    if-eq p0, v0, :cond_1

    const/16 v0, 0x4a

    if-eq p0, v0, :cond_0

    packed-switch p0, :pswitch_data_0

    const/4 p0, 0x0

    return-object p0

    .line 39
    :pswitch_0
    sget-object p0, Lcom/android/tools/r8/internal/UZ;->h:Lcom/android/tools/r8/internal/UZ;

    return-object p0

    .line 40
    :pswitch_1
    sget-object p0, Lcom/android/tools/r8/internal/UZ;->c:Lcom/android/tools/r8/internal/UZ;

    return-object p0

    .line 41
    :pswitch_2
    sget-object p0, Lcom/android/tools/r8/internal/UZ;->b:Lcom/android/tools/r8/internal/UZ;

    return-object p0

    .line 51
    :cond_0
    sget-object p0, Lcom/android/tools/r8/internal/UZ;->f:Lcom/android/tools/r8/internal/UZ;

    return-object p0

    .line 52
    :cond_1
    sget-object p0, Lcom/android/tools/r8/internal/UZ;->e:Lcom/android/tools/r8/internal/UZ;

    return-object p0

    .line 53
    :cond_2
    sget-object p0, Lcom/android/tools/r8/internal/UZ;->d:Lcom/android/tools/r8/internal/UZ;

    return-object p0

    .line 59
    :cond_3
    sget-object p0, Lcom/android/tools/r8/internal/UZ;->g:Lcom/android/tools/r8/internal/UZ;

    return-object p0

    nop

    :pswitch_data_0
    .packed-switch 0x42
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;
    .locals 2

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/TZ;->a:[I

    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    aget v0, v0, v1

    packed-switch v0, :pswitch_data_0

    .line 17
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Invalid numeric type \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1

    .line 18
    :pswitch_0
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->A1:Lcom/android/tools/r8/graph/J2;

    return-object p1

    .line 19
    :pswitch_1
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->B1:Lcom/android/tools/r8/graph/J2;

    return-object p1

    .line 20
    :pswitch_2
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->D1:Lcom/android/tools/r8/graph/J2;

    return-object p1

    .line 21
    :pswitch_3
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->C1:Lcom/android/tools/r8/graph/J2;

    return-object p1

    .line 22
    :pswitch_4
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->E1:Lcom/android/tools/r8/graph/J2;

    return-object p1

    .line 23
    :pswitch_5
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->z1:Lcom/android/tools/r8/graph/J2;

    return-object p1

    .line 24
    :pswitch_6
    iget-object p1, p1, Lcom/android/tools/r8/graph/B1;->y1:Lcom/android/tools/r8/graph/J2;

    return-object p1

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public a()Z
    .locals 1

    .line 60
    sget-object v0, Lcom/android/tools/r8/internal/UZ;->f:Lcom/android/tools/r8/internal/UZ;

    if-eq p0, v0, :cond_1

    sget-object v0, Lcom/android/tools/r8/internal/UZ;->h:Lcom/android/tools/r8/internal/UZ;

    if-ne p0, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method
