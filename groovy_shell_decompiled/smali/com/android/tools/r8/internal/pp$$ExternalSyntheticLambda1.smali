.class public final synthetic Lcom/android/tools/r8/internal/pp$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/pp$$ExternalSyntheticLambda1;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/pp$$ExternalSyntheticLambda1;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/pp$$ExternalSyntheticLambda1;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/pp$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/pp$$ExternalSyntheticLambda1;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lcom/android/tools/r8/internal/pp;

    invoke-static {p1}, Lcom/android/tools/r8/internal/pp;->g(Lcom/android/tools/r8/internal/pp;)Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    return-object p1
.end method
