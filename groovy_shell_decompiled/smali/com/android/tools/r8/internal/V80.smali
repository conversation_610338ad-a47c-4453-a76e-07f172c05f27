.class public final Lcom/android/tools/r8/internal/V80;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/metadata/R8BuildMetadata;


# instance fields
.field public final a:Ljava/lang/String;
    .annotation runtime Lcom/android/tools/r8/internal/Xi0;
        value = "version"
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "8.7.18"

    .line 2
    iput-object v0, p0, Lcom/android/tools/r8/internal/V80;->a:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final getVersion()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/V80;->a:Ljava/lang/String;

    return-object v0
.end method

.method public final toJson()Ljava/lang/String;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Ly;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Ly;-><init>()V

    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/Ly;->a(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
