.class public abstract Lcom/android/tools/r8/internal/v4;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static a(Ljava/nio/file/Path;)Lcom/android/tools/r8/internal/r4;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/r4;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/r4;-><init>(Ljava/nio/file/Path;)V

    return-object v0
.end method

.method public static a(Lcom/android/tools/r8/profile/art/ArtProfileProvider;)Ljava/lang/String;
    .locals 3

    .line 2
    new-instance v0, Ljava/io/ByteArrayOutputStream;

    invoke-direct {v0}, Ljava/io/ByteArrayOutputStream;-><init>()V

    .line 3
    new-instance v1, Ljava/io/OutputStreamWriter;

    sget-object v2, Ljava/nio/charset/StandardCharsets;->UTF_8:Ljava/nio/charset/Charset;

    invoke-direct {v1, v0, v2}, Ljava/io/OutputStreamWriter;-><init>(Ljava/io/OutputStream;Ljava/nio/charset/Charset;)V

    .line 5
    :try_start_0
    new-instance v2, Lcom/android/tools/r8/internal/u4;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/u4;-><init>(Ljava/io/OutputStreamWriter;)V

    invoke-interface {p0, v2}, Lcom/android/tools/r8/profile/art/ArtProfileProvider;->getArtProfile(Lcom/android/tools/r8/profile/art/ArtProfileBuilder;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 81
    invoke-virtual {v1}, Ljava/io/OutputStreamWriter;->close()V

    .line 82
    invoke-virtual {v0}, Ljava/io/ByteArrayOutputStream;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :catchall_0
    move-exception p0

    .line 83
    :try_start_1
    invoke-virtual {v1}, Ljava/io/OutputStreamWriter;->close()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    goto :goto_0

    :catchall_1
    move-exception v0

    invoke-virtual {p0, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_0
    throw p0
.end method

.method public static a(Ljava/io/OutputStreamWriter;Ljava/lang/String;)V
    .locals 0

    .line 84
    :try_start_0
    invoke-virtual {p0, p1}, Ljava/io/Writer;->write(Ljava/lang/String;)V

    const/16 p1, 0xa

    .line 85
    invoke-virtual {p0, p1}, Ljava/io/OutputStreamWriter;->write(I)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p0

    .line 87
    new-instance p1, Ljava/io/UncheckedIOException;

    invoke-direct {p1, p0}, Ljava/io/UncheckedIOException;-><init>(Ljava/io/IOException;)V

    throw p1
.end method
