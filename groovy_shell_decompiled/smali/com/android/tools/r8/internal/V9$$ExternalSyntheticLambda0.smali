.class public final synthetic Lcom/android/tools/r8/internal/V9$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Consumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/T9;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/T9;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/V9$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/T9;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/V9$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/T9;

    check-cast p1, Lcom/android/tools/r8/ProgramResource;

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/V9;->a(Lcom/android/tools/r8/internal/T9;Lcom/android/tools/r8/ProgramResource;)V

    return-void
.end method
