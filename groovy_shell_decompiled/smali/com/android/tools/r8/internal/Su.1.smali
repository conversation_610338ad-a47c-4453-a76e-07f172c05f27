.class public final Lcom/android/tools/r8/internal/Su;
.super Lcom/android/tools/r8/internal/Tu;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic c:Z = true


# instance fields
.field public final b:Lcom/android/tools/r8/graph/J2;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/J2;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Tu;-><init>()V

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/Su;->c:Z

    if-nez v0, :cond_1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->T0()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, <PERSON>java/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 3
    :cond_1
    :goto_0
    iput-object p1, p0, Lcom/android/tools/r8/internal/Su;->b:Lcom/android/tools/r8/graph/J2;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    .line 1
    sget-boolean p1, Lcom/android/tools/r8/internal/Su;->c:Z

    if-nez p1, :cond_1

    iget-object p1, p0, Lcom/android/tools/r8/internal/Su;->b:Lcom/android/tools/r8/graph/J2;

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 2
    :cond_1
    :goto_0
    iget-object p1, p0, Lcom/android/tools/r8/internal/Su;->b:Lcom/android/tools/r8/graph/J2;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/ak0;
    .locals 1

    .line 4
    iget-object p1, p1, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/Su;->b:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-static {v0}, Lcom/android/tools/r8/internal/F1;->b(Lcom/android/tools/r8/graph/J2;)V

    .line 6
    sget-object p1, Lcom/android/tools/r8/internal/gk0;->b:Lcom/android/tools/r8/internal/gk0;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/sr0;
    .locals 1

    .line 3
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    invoke-static {p2, v0, p1}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object p1

    return-object p1
.end method
