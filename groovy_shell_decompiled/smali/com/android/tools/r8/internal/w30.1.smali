.class public final Lcom/android/tools/r8/internal/w30;
.super Lcom/android/tools/r8/internal/Ud;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z = true


# instance fields
.field public e:Ljava/util/List;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Ud;-><init>(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/Vu0;Lcom/android/tools/r8/internal/K5;)Lcom/android/tools/r8/internal/gq0;
    .locals 2

    .line 42
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/K5;->k()Ljava/util/LinkedList;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/vt0;)V

    .line 43
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/TI;->b(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result p0

    if-eqz p0, :cond_0

    .line 46
    sget-object p0, Lcom/android/tools/r8/internal/dq0;->c:Lcom/android/tools/r8/internal/cq0;

    return-object p0

    .line 48
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/K5;->s()Ljava/util/List;

    move-result-object p0

    invoke-virtual {p1, p0}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Iterable;)V

    .line 49
    sget-object p0, Lcom/android/tools/r8/internal/fq0;->c:Lcom/android/tools/r8/internal/eq0;

    return-object p0
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/K5;Lcom/android/tools/r8/internal/rD;)V
    .locals 0

    .line 28
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/K5;->k()Ljava/util/LinkedList;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/LinkedList;->removeFirst()Ljava/lang/Object;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/pI;Ljava/util/ArrayDeque;)Z
    .locals 4

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->b()Lcom/android/tools/r8/internal/K5;

    move-result-object v0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/K5;->B()Z

    move-result v1

    const/4 v2, 0x0

    if-nez v1, :cond_0

    return v2

    .line 6
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/K5;->v()Lcom/android/tools/r8/internal/K5;

    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/K5;->C()Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/K5;->x()Z

    move-result v3

    if-eqz v3, :cond_1

    goto :goto_1

    .line 12
    :cond_1
    new-instance v2, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda1;

    invoke-direct {v2, v0}, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/K5;)V

    invoke-virtual {p2, v2}, Ljava/util/ArrayDeque;->forEach(Ljava/util/function/Consumer;)V

    .line 13
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/K5;->k()Ljava/util/LinkedList;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/LinkedList;->removeFirst()Ljava/lang/Object;

    .line 17
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/K5;->k()Ljava/util/LinkedList;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/LinkedList;->size()I

    move-result v0

    const/4 v2, 0x1

    sub-int/2addr v0, v2

    invoke-virtual {v1, p0, v0}, Lcom/android/tools/r8/internal/K5;->a(Lcom/android/tools/r8/internal/aA;I)Lcom/android/tools/r8/internal/N5;

    move-result-object p0

    .line 18
    invoke-static {p0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v0, Lcom/android/tools/r8/internal/s3$$ExternalSyntheticLambda1;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/s3$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/uD;)V

    invoke-virtual {p2, v0}, Ljava/util/ArrayDeque;->forEach(Ljava/util/function/Consumer;)V

    .line 19
    invoke-interface {p0, p1}, Ljava/util/ListIterator;->add(Ljava/lang/Object;)V

    .line 23
    invoke-virtual {p2}, Ljava/util/ArrayDeque;->size()I

    move-result v0

    neg-int v0, v0

    sub-int/2addr v0, v2

    invoke-static {p0, v0}, Lcom/android/tools/r8/internal/VI;->a(Lcom/android/tools/r8/internal/uD;I)V

    .line 24
    sget-boolean v0, Lcom/android/tools/r8/internal/w30;->f:Z

    if-nez v0, :cond_4

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/N5;->h()Lcom/android/tools/r8/internal/rD;

    move-result-object p0

    .line 25
    invoke-virtual {p2}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_0

    .line 26
    :cond_2
    invoke-virtual {p2}, Ljava/util/ArrayDeque;->iterator()Ljava/util/Iterator;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    :goto_0
    if-ne p0, p1, :cond_3

    goto :goto_1

    .line 27
    :cond_3
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    :cond_4
    :goto_1
    return v2
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/pI;Lcom/android/tools/r8/internal/rD;)Z
    .locals 0

    if-ne p1, p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static a(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/rD;)Z
    .locals 1

    .line 60
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->h()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->l()Lcom/android/tools/r8/internal/jD;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/jD;->m()Lcom/android/tools/r8/internal/vt0;

    move-result-object p1

    if-ne p1, p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static b(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/rD;)Z
    .locals 1

    .line 53
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->h()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/rD;->l()Lcom/android/tools/r8/internal/jD;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/jD;->m()Lcom/android/tools/r8/internal/vt0;

    move-result-object p1

    if-ne p1, p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;)Z
    .locals 2

    .line 29
    iget-object p2, p0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    .line 30
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p2

    .line 31
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/h;->h()Z

    move-result p2

    const/4 v0, 0x0

    if-nez p2, :cond_0

    return v0

    .line 32
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object p2

    .line 33
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/j1;->q1()Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ud;->c:Lcom/android/tools/r8/utils/w;

    .line 34
    invoke-virtual {v1}, Lcom/android/tools/r8/utils/w;->h()Z

    move-result v1

    if-nez v1, :cond_1

    goto :goto_0

    .line 37
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, p2}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object p2

    .line 38
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ud;->c:Lcom/android/tools/r8/utils/w;

    invoke-virtual {p2, v1}, Lcom/android/tools/r8/shaking/n1;->c(Lcom/android/tools/r8/shaking/L0;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ud;->c:Lcom/android/tools/r8/utils/w;

    .line 39
    invoke-virtual {p2, v1}, Lcom/android/tools/r8/shaking/n1;->e(Lcom/android/tools/r8/shaking/L0;)Z

    move-result p2

    if-eqz p2, :cond_2

    .line 40
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/w30;->d(Lcom/android/tools/r8/internal/aA;)Z

    move-result p1

    if-eqz p1, :cond_2

    const/4 v0, 0x1

    :cond_2
    :goto_0
    return v0
.end method

.method public final synthetic a(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/rD;)Z
    .locals 2

    .line 50
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ud;->b:Lcom/android/tools/r8/graph/B1;

    invoke-virtual {p3, v0}, Lcom/android/tools/r8/internal/rD;->a(Lcom/android/tools/r8/graph/B1;)Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 53
    :cond_0
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/rD;->a0()Lcom/android/tools/r8/internal/pI;

    move-result-object p3

    .line 54
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/vI;->V2()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    if-eq v0, p1, :cond_1

    return v1

    .line 57
    :cond_1
    iget-object p1, p0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object p2

    invoke-virtual {p3, p1, p2}, Lcom/android/tools/r8/internal/uI;->f(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/graph/H0;

    move-result-object p1

    if-eqz p1, :cond_2

    .line 59
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->D()Lcom/android/tools/r8/internal/iV;

    move-result-object p1

    invoke-virtual {p1, p3}, Lcom/android/tools/r8/internal/iV;->a(Lcom/android/tools/r8/internal/uI;)Z

    move-result p1

    if-nez p1, :cond_2

    const/4 v1, 0x1

    :cond_2
    return v1
.end method

.method public final b(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/Xd;
    .locals 7

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w30;->e:Ljava/util/List;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/w30;->c(Lcom/android/tools/r8/internal/aA;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/w30;->e:Ljava/util/List;

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/w30;->e:Ljava/util/List;

    .line 6
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_9

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/pI;

    .line 7
    new-instance v2, Ljava/util/ArrayDeque;

    invoke-direct {v2}, Ljava/util/ArrayDeque;-><init>()V

    .line 8
    :cond_2
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->b()Lcom/android/tools/r8/internal/K5;

    move-result-object v3

    invoke-virtual {v3, p1}, Lcom/android/tools/r8/internal/K5;->a(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/N5;

    move-result-object v3

    .line 9
    invoke-virtual {v2}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v4

    if-eqz v4, :cond_3

    move-object v4, v1

    goto :goto_1

    .line 10
    :cond_3
    invoke-virtual {v2}, Ljava/util/ArrayDeque;->iterator()Ljava/util/Iterator;

    move-result-object v4

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    .line 11
    :goto_1
    check-cast v4, Lcom/android/tools/r8/internal/rD;

    .line 12
    invoke-interface {v3, v4}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/rD;)V

    .line 14
    :goto_2
    invoke-interface {v3}, Lcom/android/tools/r8/internal/sD;->hasPrevious()Z

    move-result v4

    if-eqz v4, :cond_7

    .line 15
    invoke-interface {v3}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/rD;

    .line 16
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->l1()Z

    move-result v5

    if-eqz v5, :cond_4

    goto :goto_3

    .line 20
    :cond_4
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->d1()Z

    move-result v5

    if-eqz v5, :cond_6

    .line 21
    iget-object v5, v1, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 22
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/util/ArrayList;->contains(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_6

    .line 23
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->A1()Z

    move-result v5

    if-nez v5, :cond_5

    invoke-virtual {v4}, Lcom/android/tools/r8/internal/rD;->B1()Z

    move-result v5

    if-eqz v5, :cond_7

    .line 25
    :cond_5
    invoke-virtual {v2, v4}, Ljava/util/ArrayDeque;->addFirst(Ljava/lang/Object;)V

    goto :goto_2

    .line 34
    :cond_6
    invoke-virtual {v2}, Ljava/util/ArrayDeque;->size()I

    move-result v5

    add-int/lit8 v5, v5, 0x2

    sget v6, Lcom/android/tools/r8/internal/cB;->c:I

    const-string v6, "expectedSize"

    .line 35
    invoke-static {v5, v6}, Lcom/android/tools/r8/internal/me;->a(ILjava/lang/String;)V

    .line 36
    new-instance v6, Lcom/android/tools/r8/internal/ZA;

    invoke-direct {v6, v5}, Lcom/android/tools/r8/internal/ZA;-><init>(I)V

    .line 37
    invoke-virtual {v6, v2}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Iterable;)Lcom/android/tools/r8/internal/ZA;

    move-result-object v5

    .line 38
    invoke-virtual {v5, v1}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    move-result-object v5

    .line 39
    invoke-virtual {v5, v4}, Lcom/android/tools/r8/internal/ZA;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/ZA;

    move-result-object v4

    .line 40
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/ZA;->a()Lcom/android/tools/r8/internal/cB;

    move-result-object v4

    .line 41
    invoke-interface {v3}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    .line 42
    invoke-interface {v3, v4}, Lcom/android/tools/r8/internal/uD;->a(Ljava/util/Collection;)V

    .line 43
    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    neg-int v4, v4

    add-int/lit8 v4, v4, -0x1

    invoke-static {v3, v4}, Lcom/android/tools/r8/internal/VI;->a(Lcom/android/tools/r8/internal/uD;I)V

    goto :goto_2

    .line 44
    :cond_7
    :goto_3
    invoke-virtual {v2}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_8

    move-object v3, v1

    goto :goto_4

    .line 45
    :cond_8
    invoke-virtual {v2}, Ljava/util/ArrayDeque;->iterator()Ljava/util/Iterator;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    .line 46
    :goto_4
    check-cast v3, Lcom/android/tools/r8/internal/rD;

    .line 47
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->b()Lcom/android/tools/r8/internal/K5;

    move-result-object v4

    .line 48
    iget-object v4, v4, Lcom/android/tools/r8/internal/K5;->f:Ljava/util/LinkedList;

    const/4 v5, 0x0

    .line 49
    invoke-virtual {v4, v5}, Ljava/util/LinkedList;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/rD;

    if-ne v4, v3, :cond_1

    .line 50
    invoke-static {p1, v1, v2}, Lcom/android/tools/r8/internal/w30;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/pI;Ljava/util/ArrayDeque;)Z

    move-result v3

    if-nez v3, :cond_2

    goto/16 :goto_0

    .line 51
    :cond_9
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->A()V

    .line 52
    sget-object p1, Lcom/android/tools/r8/internal/Xd;->c:Lcom/android/tools/r8/internal/Xd;

    return-object p1
.end method

.method public final b()Ljava/lang/String;
    .locals 1

    const-string v0, "Parent constructor hoisting pass"

    return-object v0
.end method

.method public final c(Lcom/android/tools/r8/internal/aA;)Ljava/util/List;
    .locals 3

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->o()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vt0;->g0()Ljava/util/Set;

    move-result-object v1

    new-instance v2, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda5;

    invoke-direct {v2, p0, v0, p1}, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda5;-><init>(Lcom/android/tools/r8/internal/w30;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/aA;)V

    .line 4
    invoke-static {v1, v2}, Lcom/android/tools/r8/internal/QR;->a(Ljava/util/Collection;Ljava/util/function/Predicate;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public final d(Lcom/android/tools/r8/internal/aA;)Z
    .locals 6

    .line 1
    iget-object v0, p1, Lcom/android/tools/r8/internal/aA;->i:Lcom/android/tools/r8/internal/hA;

    const/16 v1, 0x1e

    .line 2
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/hA;->a(I)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return p1

    .line 3
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->o()Lcom/android/tools/r8/internal/vt0;

    move-result-object v0

    .line 4
    new-instance v1, Lcom/android/tools/r8/internal/Vu0;

    const/4 v2, 0x2

    invoke-direct {v1, v2}, Lcom/android/tools/r8/internal/Vu0;-><init>(I)V

    .line 5
    iget-object v2, p0, Lcom/android/tools/r8/internal/w30;->e:Ljava/util/List;

    if-nez v2, :cond_1

    .line 6
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/w30;->c(Lcom/android/tools/r8/internal/aA;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/w30;->e:Ljava/util/List;

    .line 8
    :cond_1
    iget-object p1, p0, Lcom/android/tools/r8/internal/w30;->e:Ljava/util/List;

    .line 9
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/pI;

    .line 13
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/rD;->b()Lcom/android/tools/r8/internal/K5;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/K5;->k()Ljava/util/LinkedList;

    move-result-object v3

    new-instance v4, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda4;

    invoke-direct {v4, v0}, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda4;-><init>(Lcom/android/tools/r8/internal/vt0;)V

    new-instance v5, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda3;

    invoke-direct {v5, v2}, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda3;-><init>(Lcom/android/tools/r8/internal/pI;)V

    .line 14
    invoke-static {v3, v4, v5}, Lcom/android/tools/r8/internal/OI;->a(Ljava/util/LinkedList;Ljava/util/function/Predicate;Ljava/util/function/Predicate;)Z

    move-result v3

    if-eqz v3, :cond_2

    const/4 p1, 0x1

    return p1

    .line 22
    :cond_2
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/rD;->b()Lcom/android/tools/r8/internal/K5;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/K5;->s()Ljava/util/List;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Iterable;)V

    goto :goto_0

    .line 24
    :cond_3
    new-instance p1, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda2;

    invoke-direct {p1, v0, v1}, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/Vu0;)V

    .line 25
    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/Vu0;->a(Ljava/util/function/Function;)Lcom/android/tools/r8/internal/gq0;

    move-result-object p1

    .line 26
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/gq0;->c()Z

    move-result p1

    return p1
.end method
