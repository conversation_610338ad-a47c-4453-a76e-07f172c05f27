.class public final Lcom/android/tools/r8/internal/rZ;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic e:Z = true


# instance fields
.field public a:Lcom/android/tools/r8/internal/yb0;

.field public b:Lcom/android/tools/r8/internal/yb0;

.field public c:Lcom/android/tools/r8/internal/yb0;

.field public d:Lcom/android/tools/r8/internal/yb0;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/qZ;Ljava/util/function/Function;)Lcom/android/tools/r8/internal/yb0;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/rZ;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/rZ;-><init>()V

    .line 2
    invoke-interface {p1, v0}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/yb0;

    .line 3
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    if-ne p0, v1, :cond_0

    .line 4
    iput-object p1, v0, Lcom/android/tools/r8/internal/rZ;->a:Lcom/android/tools/r8/internal/yb0;

    goto :goto_1

    .line 5
    :cond_0
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->c()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    if-ne p0, v1, :cond_1

    .line 6
    iput-object p1, v0, Lcom/android/tools/r8/internal/rZ;->b:Lcom/android/tools/r8/internal/yb0;

    goto :goto_1

    .line 7
    :cond_1
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->b()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    if-ne p0, v1, :cond_2

    .line 8
    iput-object p1, v0, Lcom/android/tools/r8/internal/rZ;->c:Lcom/android/tools/r8/internal/yb0;

    goto :goto_1

    .line 10
    :cond_2
    sget-boolean v1, Lcom/android/tools/r8/internal/rZ;->e:Z

    if-nez v1, :cond_4

    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->a()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    if-ne p0, v1, :cond_3

    goto :goto_0

    :cond_3
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 11
    :cond_4
    :goto_0
    iput-object p1, v0, Lcom/android/tools/r8/internal/rZ;->d:Lcom/android/tools/r8/internal/yb0;

    :goto_1
    return-object p1
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/qZ;Ljava/util/function/BiFunction;)Lcom/android/tools/r8/internal/yb0;
    .locals 2

    .line 12
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    if-ne p1, v0, :cond_0

    .line 13
    iget-object v0, p0, Lcom/android/tools/r8/internal/rZ;->a:Lcom/android/tools/r8/internal/yb0;

    goto :goto_1

    .line 14
    :cond_0
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->c()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    if-ne p1, v0, :cond_1

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/internal/rZ;->b:Lcom/android/tools/r8/internal/yb0;

    goto :goto_1

    .line 16
    :cond_1
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->b()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    if-ne p1, v0, :cond_2

    .line 17
    iget-object v0, p0, Lcom/android/tools/r8/internal/rZ;->c:Lcom/android/tools/r8/internal/yb0;

    goto :goto_1

    .line 19
    :cond_2
    sget-boolean v0, Lcom/android/tools/r8/internal/rZ;->e:Z

    if-nez v0, :cond_4

    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->a()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    if-ne p1, v0, :cond_3

    goto :goto_0

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 20
    :cond_4
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/rZ;->d:Lcom/android/tools/r8/internal/yb0;

    :goto_1
    if-eqz v0, :cond_5

    return-object v0

    .line 21
    :cond_5
    monitor-enter p0

    .line 22
    :try_start_0
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    if-ne p1, v0, :cond_6

    .line 23
    iget-object v0, p0, Lcom/android/tools/r8/internal/rZ;->a:Lcom/android/tools/r8/internal/yb0;

    goto :goto_3

    .line 24
    :cond_6
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->c()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    if-ne p1, v0, :cond_7

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/internal/rZ;->b:Lcom/android/tools/r8/internal/yb0;

    goto :goto_3

    .line 26
    :cond_7
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->b()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    if-ne p1, v0, :cond_8

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/internal/rZ;->c:Lcom/android/tools/r8/internal/yb0;

    goto :goto_3

    .line 29
    :cond_8
    sget-boolean v0, Lcom/android/tools/r8/internal/rZ;->e:Z

    if-nez v0, :cond_a

    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->a()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    if-ne p1, v0, :cond_9

    goto :goto_2

    :cond_9
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 30
    :cond_a
    :goto_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/rZ;->d:Lcom/android/tools/r8/internal/yb0;

    :goto_3
    if-eqz v0, :cond_b

    .line 31
    monitor-exit p0

    return-object v0

    .line 33
    :cond_b
    invoke-interface {p2, p1, p0}, Ljava/util/function/BiFunction;->apply(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/internal/yb0;

    .line 34
    sget-boolean v0, Lcom/android/tools/r8/internal/rZ;->e:Z

    if-nez v0, :cond_d

    if-eqz p2, :cond_c

    goto :goto_4

    :cond_c
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 35
    :cond_d
    :goto_4
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    if-ne p1, v1, :cond_e

    .line 36
    iput-object p2, p0, Lcom/android/tools/r8/internal/rZ;->a:Lcom/android/tools/r8/internal/yb0;

    goto :goto_6

    .line 37
    :cond_e
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->c()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    if-ne p1, v1, :cond_f

    .line 38
    iput-object p2, p0, Lcom/android/tools/r8/internal/rZ;->b:Lcom/android/tools/r8/internal/yb0;

    goto :goto_6

    .line 39
    :cond_f
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->b()Lcom/android/tools/r8/internal/qZ;

    move-result-object v1

    if-ne p1, v1, :cond_10

    .line 40
    iput-object p2, p0, Lcom/android/tools/r8/internal/rZ;->c:Lcom/android/tools/r8/internal/yb0;

    goto :goto_6

    :cond_10
    if-nez v0, :cond_12

    .line 42
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->a()Lcom/android/tools/r8/internal/qZ;

    move-result-object v0

    if-ne p1, v0, :cond_11

    goto :goto_5

    :cond_11
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 43
    :cond_12
    :goto_5
    iput-object p2, p0, Lcom/android/tools/r8/internal/rZ;->d:Lcom/android/tools/r8/internal/yb0;

    .line 44
    :goto_6
    monitor-exit p0

    return-object p2

    :catchall_0
    move-exception p1

    .line 45
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method
