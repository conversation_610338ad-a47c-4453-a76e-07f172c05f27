.class public abstract Lcom/android/tools/r8/internal/RB;
.super Lcom/android/tools/r8/internal/SB;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/util/NavigableSet;
.implements Lcom/android/tools/r8/internal/Ik0;


# static fields
.field public static final synthetic g:I


# instance fields
.field public final transient e:Ljava/util/Comparator;

.field public transient f:Lcom/android/tools/r8/internal/RB;


# direct methods
.method public constructor <init>(Ljava/util/Comparator;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/SB;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/RB;->e:Ljava/util/Comparator;

    return-void
.end method

.method public static a(Ljava/util/Comparator;)Lcom/android/tools/r8/internal/Cc0;
    .locals 2

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/rX;->b:Lcom/android/tools/r8/internal/rX;

    .line 2
    invoke-virtual {v0, p0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 3
    sget-object p0, Lcom/android/tools/r8/internal/Cc0;->i:Lcom/android/tools/r8/internal/Cc0;

    return-object p0

    .line 5
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/Cc0;

    .line 6
    sget-object v1, Lcom/android/tools/r8/internal/uc0;->e:Lcom/android/tools/r8/internal/uc0;

    .line 7
    invoke-direct {v0, v1, p0}, Lcom/android/tools/r8/internal/Cc0;-><init>(Lcom/android/tools/r8/internal/cB;Ljava/util/Comparator;)V

    return-object v0
.end method


# virtual methods
.method public final comparator()Ljava/util/Comparator;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/RB;->e:Ljava/util/Comparator;

    return-object v0
.end method

.method public final bridge synthetic descendingSet()Ljava/util/NavigableSet;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/RB;->m()Lcom/android/tools/r8/internal/RB;

    move-result-object v0

    return-object v0
.end method

.method public final headSet(Ljava/lang/Object;Z)Ljava/util/NavigableSet;
    .locals 1

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    move-object v0, p0

    check-cast v0, Lcom/android/tools/r8/internal/Cc0;

    .line 3
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Cc0;->a(Ljava/lang/Object;Z)I

    move-result p1

    const/4 p2, 0x0

    invoke-virtual {v0, p2, p1}, Lcom/android/tools/r8/internal/Cc0;->e(II)Lcom/android/tools/r8/internal/Cc0;

    move-result-object p1

    return-object p1
.end method

.method public final headSet(Ljava/lang/Object;)Ljava/util/SortedSet;
    .locals 2

    .line 4
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    move-object v0, p0

    check-cast v0, Lcom/android/tools/r8/internal/Cc0;

    const/4 v1, 0x0

    .line 6
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Cc0;->a(Ljava/lang/Object;Z)I

    move-result p1

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Cc0;->e(II)Lcom/android/tools/r8/internal/Cc0;

    move-result-object p1

    return-object p1
.end method

.method public abstract indexOf(Ljava/lang/Object;)I
.end method

.method public final m()Lcom/android/tools/r8/internal/RB;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/RB;->f:Lcom/android/tools/r8/internal/RB;

    if-nez v0, :cond_1

    .line 3
    move-object v0, p0

    check-cast v0, Lcom/android/tools/r8/internal/Cc0;

    .line 4
    iget-object v1, v0, Lcom/android/tools/r8/internal/RB;->e:Ljava/util/Comparator;

    invoke-static {v1}, Ljava/util/Collections;->reverseOrder(Ljava/util/Comparator;)Ljava/util/Comparator;

    move-result-object v1

    .line 5
    invoke-virtual {v0}, Ljava/util/AbstractCollection;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_0

    .line 6
    invoke-static {v1}, Lcom/android/tools/r8/internal/RB;->a(Ljava/util/Comparator;)Lcom/android/tools/r8/internal/Cc0;

    move-result-object v0

    goto :goto_0

    .line 7
    :cond_0
    new-instance v2, Lcom/android/tools/r8/internal/Cc0;

    iget-object v0, v0, Lcom/android/tools/r8/internal/Cc0;->h:Lcom/android/tools/r8/internal/cB;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/cB;->j()Lcom/android/tools/r8/internal/cB;

    move-result-object v0

    invoke-direct {v2, v0, v1}, Lcom/android/tools/r8/internal/Cc0;-><init>(Lcom/android/tools/r8/internal/cB;Ljava/util/Comparator;)V

    move-object v0, v2

    .line 8
    :goto_0
    iput-object v0, p0, Lcom/android/tools/r8/internal/RB;->f:Lcom/android/tools/r8/internal/RB;

    .line 9
    iput-object p0, v0, Lcom/android/tools/r8/internal/RB;->f:Lcom/android/tools/r8/internal/RB;

    :cond_1
    return-object v0
.end method

.method public final pollFirst()Ljava/lang/Object;
    .locals 1

    .line 1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method

.method public final pollLast()Ljava/lang/Object;
    .locals 1

    .line 1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method

.method public final subSet(Ljava/lang/Object;ZLjava/lang/Object;Z)Ljava/util/NavigableSet;
    .locals 1

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    invoke-virtual {p3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/RB;->e:Ljava/util/Comparator;

    invoke-interface {v0, p1, p3}, Ljava/util/Comparator;->compare(Ljava/lang/Object;Ljava/lang/Object;)I

    move-result v0

    if-gtz v0, :cond_0

    .line 4
    move-object v0, p0

    check-cast v0, Lcom/android/tools/r8/internal/Cc0;

    .line 5
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Cc0;->b(Ljava/lang/Object;Z)I

    move-result p1

    .line 6
    iget-object p2, v0, Lcom/android/tools/r8/internal/Cc0;->h:Lcom/android/tools/r8/internal/cB;

    invoke-virtual {p2}, Ljava/util/AbstractCollection;->size()I

    move-result p2

    .line 7
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Cc0;->e(II)Lcom/android/tools/r8/internal/Cc0;

    move-result-object p1

    .line 8
    invoke-virtual {p1, p3, p4}, Lcom/android/tools/r8/internal/Cc0;->a(Ljava/lang/Object;Z)I

    move-result p2

    const/4 p3, 0x0

    .line 9
    invoke-virtual {p1, p3, p2}, Lcom/android/tools/r8/internal/Cc0;->e(II)Lcom/android/tools/r8/internal/Cc0;

    move-result-object p1

    return-object p1

    .line 10
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1
.end method

.method public final subSet(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/SortedSet;
    .locals 2

    .line 11
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 12
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 13
    iget-object v0, p0, Lcom/android/tools/r8/internal/RB;->e:Ljava/util/Comparator;

    invoke-interface {v0, p1, p2}, Ljava/util/Comparator;->compare(Ljava/lang/Object;Ljava/lang/Object;)I

    move-result v0

    if-gtz v0, :cond_0

    .line 14
    move-object v0, p0

    check-cast v0, Lcom/android/tools/r8/internal/Cc0;

    const/4 v1, 0x1

    .line 15
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Cc0;->b(Ljava/lang/Object;Z)I

    move-result p1

    .line 16
    iget-object v1, v0, Lcom/android/tools/r8/internal/Cc0;->h:Lcom/android/tools/r8/internal/cB;

    invoke-virtual {v1}, Ljava/util/AbstractCollection;->size()I

    move-result v1

    .line 17
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Cc0;->e(II)Lcom/android/tools/r8/internal/Cc0;

    move-result-object p1

    const/4 v0, 0x0

    .line 18
    invoke-virtual {p1, p2, v0}, Lcom/android/tools/r8/internal/Cc0;->a(Ljava/lang/Object;Z)I

    move-result p2

    .line 19
    invoke-virtual {p1, v0, p2}, Lcom/android/tools/r8/internal/Cc0;->e(II)Lcom/android/tools/r8/internal/Cc0;

    move-result-object p1

    return-object p1

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1
.end method

.method public final tailSet(Ljava/lang/Object;Z)Ljava/util/NavigableSet;
    .locals 1

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    move-object v0, p0

    check-cast v0, Lcom/android/tools/r8/internal/Cc0;

    .line 3
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Cc0;->b(Ljava/lang/Object;Z)I

    move-result p1

    .line 4
    iget-object p2, v0, Lcom/android/tools/r8/internal/Cc0;->h:Lcom/android/tools/r8/internal/cB;

    invoke-virtual {p2}, Ljava/util/AbstractCollection;->size()I

    move-result p2

    .line 5
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Cc0;->e(II)Lcom/android/tools/r8/internal/Cc0;

    move-result-object p1

    return-object p1
.end method

.method public final tailSet(Ljava/lang/Object;)Ljava/util/SortedSet;
    .locals 2

    .line 6
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 7
    move-object v0, p0

    check-cast v0, Lcom/android/tools/r8/internal/Cc0;

    const/4 v1, 0x1

    .line 8
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Cc0;->b(Ljava/lang/Object;Z)I

    move-result p1

    .line 9
    iget-object v1, v0, Lcom/android/tools/r8/internal/Cc0;->h:Lcom/android/tools/r8/internal/cB;

    invoke-virtual {v1}, Ljava/util/AbstractCollection;->size()I

    move-result v1

    .line 10
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Cc0;->e(II)Lcom/android/tools/r8/internal/Cc0;

    move-result-object p1

    return-object p1
.end method
