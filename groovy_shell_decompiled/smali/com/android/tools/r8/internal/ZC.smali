.class public abstract Lcom/android/tools/r8/internal/ZC;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Lcom/android/tools/r8/internal/aZ;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract b()Lcom/android/tools/r8/internal/OC;
.end method

.method public abstract c()Lcom/android/tools/r8/graph/x2;
.end method

.method public abstract d()Z
.end method

.method public abstract e()Z
.end method

.method public abstract f()Lcom/android/tools/r8/internal/q;
.end method

.method public abstract g()Z
.end method
