.class public final synthetic Lcom/android/tools/r8/internal/CQ$$ExternalSyntheticLambda11;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiPredicate;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/CQ;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/CQ;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/CQ$$ExternalSyntheticLambda11;->f$0:Lcom/android/tools/r8/internal/CQ;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/CQ$$ExternalSyntheticLambda11;->f$0:Lcom/android/tools/r8/internal/CQ;

    check-cast p1, Lcom/android/tools/r8/internal/YR;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p2

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/CQ;->b(Lcom/android/tools/r8/internal/YR;I)Z

    move-result p1

    return p1
.end method
