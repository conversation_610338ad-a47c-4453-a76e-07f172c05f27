.class public final Lcom/android/tools/r8/internal/tf0;
.super Lcom/android/tools/r8/internal/uy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# static fields
.field public static final d:Lcom/android/tools/r8/internal/tf0;

.field public static final e:Lcom/android/tools/r8/internal/rf0;


# instance fields
.field public volatile b:Ljava/lang/String;

.field public c:B


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/tf0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/tf0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/tf0;->d:Lcom/android/tools/r8/internal/tf0;

    .line 9
    new-instance v0, Lcom/android/tools/r8/internal/rf0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/rf0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/tf0;->e:Lcom/android/tools/r8/internal/rf0;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 114
    invoke-direct {p0}, Lcom/android/tools/r8/internal/uy;-><init>()V

    const/4 v0, -0x1

    .line 224
    iput-byte v0, p0, Lcom/android/tools/r8/internal/tf0;->c:B

    const-string v0, ""

    .line 225
    iput-object v0, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    .locals 4

    .line 226
    invoke-direct {p0}, Lcom/android/tools/r8/internal/tf0;-><init>()V

    .line 227
    invoke-static {p2}, Lcom/android/tools/r8/internal/qg;->a(Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/qs0;

    move-result-object v0

    const/4 v1, 0x0

    :cond_0
    :goto_0
    if-nez v1, :cond_3

    .line 27652
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->s()I

    move-result v2

    if-eqz v2, :cond_2

    const/16 v3, 0xa

    if-eq v2, v3, :cond_1

    .line 27664
    invoke-virtual {p0, p1, v0, p2, v2}, Lcom/android/tools/r8/internal/uy;->parseUnknownField(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/qs0;Lcom/android/tools/r8/internal/Lu;I)Z

    move-result v2

    if-nez v2, :cond_0

    goto :goto_1

    .line 27665
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->r()Ljava/lang/String;

    move-result-object v2

    .line 27667
    iput-object v2, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :cond_2
    :goto_1
    const/4 v1, 0x1

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :catch_0
    move-exception p1

    .line 27682
    :try_start_1
    new-instance p2, Lcom/android/tools/r8/internal/lI;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/lI;-><init>(Ljava/io/IOException;)V

    .line 27683
    iput-object p0, p2, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 27684
    throw p2

    :catch_1
    move-exception p1

    .line 27685
    iput-object p0, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 27686
    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 27691
    :goto_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 27692
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    .line 27693
    throw p1

    .line 27694
    :cond_3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 27695
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/sf0;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/uy;-><init>(Lcom/android/tools/r8/internal/dy;)V

    const/4 p1, -0x1

    .line 113
    iput-byte p1, p0, Lcom/android/tools/r8/internal/tf0;->c:B

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/sf0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/tf0;->d:Lcom/android/tools/r8/internal/tf0;

    if-ne p0, v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/sf0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/sf0;-><init>()V

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/sf0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/sf0;-><init>()V

    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/sf0;->a(Lcom/android/tools/r8/internal/tf0;)Lcom/android/tools/r8/internal/sf0;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/tf0;

    if-nez v1, :cond_1

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/J0;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/tf0;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    .line 6
    instance-of v2, v1, Ljava/lang/String;

    if-eqz v2, :cond_2

    goto :goto_0

    .line 9
    :cond_2
    check-cast v1, Lcom/android/tools/r8/internal/Z7;

    .line 11
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v1

    .line 12
    iput-object v1, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    .line 13
    :goto_0
    iget-object v2, p1, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    .line 14
    instance-of v3, v2, Ljava/lang/String;

    if-eqz v3, :cond_3

    goto :goto_1

    .line 17
    :cond_3
    check-cast v2, Lcom/android/tools/r8/internal/Z7;

    .line 19
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v2

    .line 20
    iput-object v2, p1, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    .line 21
    :goto_1
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x0

    if-nez v1, :cond_4

    return v2

    .line 22
    :cond_4
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/vs0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_5

    return v2

    :cond_5
    return v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/tf0;->d:Lcom/android/tools/r8/internal/tf0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/tf0;->d:Lcom/android/tools/r8/internal/tf0;

    return-object v0
.end method

.method public final getSerializedSize()I
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    :cond_0
    const/4 v0, 0x0

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    invoke-static {v1}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    const/4 v1, 0x1

    invoke-static {v1, v0}, Lcom/android/tools/r8/internal/uy;->computeStringSize(ILjava/lang/Object;)I

    move-result v0

    .line 8
    :cond_1
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vs0;->getSerializedSize()I

    move-result v1

    add-int/2addr v1, v0

    .line 9
    iput v1, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    return v1
.end method

.method public final getUnknownFields()Lcom/android/tools/r8/internal/vs0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    return-object v0
.end method

.method public final hashCode()I
    .locals 5

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    if-eqz v0, :cond_0

    return v0

    :cond_0
    const/16 v0, 0x30b

    .line 2
    sget-object v1, Lcom/android/tools/r8/internal/Tg0;->Y:Lcom/android/tools/r8/internal/Ok;

    const/16 v2, 0x25

    const/4 v3, 0x1

    const/16 v4, 0x35

    .line 3
    invoke-static {v1, v0, v2, v3, v4}, Lcom/android/tools/r8/internal/ng;->a(Lcom/android/tools/r8/internal/Ok;IIII)I

    move-result v0

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    .line 5
    instance-of v2, v1, Ljava/lang/String;

    if-eqz v2, :cond_1

    goto :goto_0

    .line 8
    :cond_1
    check-cast v1, Lcom/android/tools/r8/internal/Z7;

    .line 10
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v1

    .line 11
    iput-object v1, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    .line 12
    :goto_0
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    mul-int/lit8 v1, v1, 0x1d

    .line 13
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vs0;->hashCode()I

    move-result v0

    add-int/2addr v0, v1

    .line 14
    iput v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    return v0
.end method

.method public final internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->Z:Lcom/android/tools/r8/internal/sy;

    .line 2
    const-class v1, Lcom/android/tools/r8/internal/tf0;

    const-class v2, Lcom/android/tools/r8/internal/sf0;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 2

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/internal/tf0;->c:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    if-nez v0, :cond_1

    const/4 v0, 0x0

    return v0

    .line 5
    :cond_1
    iput-byte v1, p0, Lcom/android/tools/r8/internal/tf0;->c:B

    return v1
.end method

.method public final newBuilderForType()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/tf0;->d:Lcom/android/tools/r8/internal/tf0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/tf0;->a()Lcom/android/tools/r8/internal/sf0;

    move-result-object v0

    return-object v0
.end method

.method public final newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/sf0;

    check-cast p1, Lcom/android/tools/r8/internal/ay;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/sf0;-><init>(Lcom/android/tools/r8/internal/ay;)V

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/tf0;->a()Lcom/android/tools/r8/internal/sf0;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/tf0;->a()Lcom/android/tools/r8/internal/sf0;

    move-result-object v0

    return-object v0
.end method

.method public final writeTo(Lcom/android/tools/r8/internal/je;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    invoke-static {v0}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/tf0;->b:Ljava/lang/String;

    const/4 v1, 0x1

    invoke-static {p1, v1, v0}, Lcom/android/tools/r8/internal/uy;->writeString(Lcom/android/tools/r8/internal/je;ILjava/lang/Object;)V

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/vs0;->writeTo(Lcom/android/tools/r8/internal/je;)V

    return-void
.end method
