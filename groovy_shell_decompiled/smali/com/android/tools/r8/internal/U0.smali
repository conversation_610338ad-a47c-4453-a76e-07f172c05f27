.class public abstract Lcom/android/tools/r8/internal/U0;
.super Ljava/util/AbstractCollection;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/YW;


# instance fields
.field public transient b:Ljava/util/Set;

.field public transient c:Ljava/util/Set;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/util/AbstractCollection;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Ljava/lang/Object;)I
    .locals 2

    const/4 v0, 0x0

    const-string v1, "count"

    .line 3
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/me;->a(ILjava/lang/String;)V

    .line 5
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/YW;->b(Ljava/lang/Object;)I

    move-result v0

    rsub-int/lit8 v1, v0, 0x0

    if-lez v1, :cond_0

    .line 9
    invoke-interface {p0, p1, v1}, Lcom/android/tools/r8/internal/YW;->a(Ljava/lang/Object;I)I

    goto :goto_0

    :cond_0
    if-gez v1, :cond_1

    neg-int v1, v1

    .line 11
    invoke-interface {p0, v1, p1}, Lcom/android/tools/r8/internal/YW;->b(ILjava/lang/Object;)I

    :cond_1
    :goto_0
    return v0
.end method

.method public a(Ljava/lang/Object;I)I
    .locals 0

    .line 1
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public a()Ljava/util/Set;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/S0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/S0;-><init>(Lcom/android/tools/r8/internal/U0;)V

    return-object v0
.end method

.method public a(ILjava/lang/Object;)Z
    .locals 2

    const-string v0, "oldCount"

    .line 12
    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/me;->a(ILjava/lang/String;)V

    const/4 v0, 0x0

    const-string v1, "newCount"

    .line 13
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/me;->a(ILjava/lang/String;)V

    .line 15
    invoke-interface {p0, p2}, Lcom/android/tools/r8/internal/YW;->b(Ljava/lang/Object;)I

    move-result v1

    if-ne v1, p1, :cond_0

    .line 16
    invoke-interface {p0, p2}, Lcom/android/tools/r8/internal/YW;->a(Ljava/lang/Object;)I

    const/4 v0, 0x1

    :cond_0
    return v0
.end method

.method public final add(Ljava/lang/Object;)Z
    .locals 1

    const/4 v0, 0x1

    .line 1
    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/internal/U0;->a(Ljava/lang/Object;I)I

    return v0
.end method

.method public final addAll(Ljava/util/Collection;)Z
    .locals 1

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    instance-of v0, p1, Lcom/android/tools/r8/internal/YW;

    if-eqz v0, :cond_0

    .line 3
    check-cast p1, Lcom/android/tools/r8/internal/YW;

    .line 4
    invoke-static {p0, p1}, Lcom/android/tools/r8/internal/dX;->a(Lcom/android/tools/r8/internal/U0;Lcom/android/tools/r8/internal/YW;)Z

    move-result p1

    goto :goto_0

    .line 5
    :cond_0
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 p1, 0x0

    goto :goto_0

    .line 8
    :cond_1
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    invoke-static {p0, p1}, Lcom/android/tools/r8/internal/hJ;->a(Ljava/util/Collection;Ljava/util/Iterator;)Z

    move-result p1

    :goto_0
    return p1
.end method

.method public b()Ljava/util/Set;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/T0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/T0;-><init>(Lcom/android/tools/r8/internal/U0;)V

    return-object v0
.end method

.method public abstract c()I
.end method

.method public contains(Ljava/lang/Object;)Z
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/YW;->b(Ljava/lang/Object;)I

    move-result p1

    if-lez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public abstract d()Ljava/util/Iterator;
.end method

.method public entrySet()Ljava/util/Set;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/U0;->c:Ljava/util/Set;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/U0;->b()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/U0;->c:Ljava/util/Set;

    :cond_0
    return-object v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lcom/android/tools/r8/internal/dX;->a(Lcom/android/tools/r8/internal/YW;Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public abstract g()Ljava/util/Iterator;
.end method

.method public final hashCode()I
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/U0;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->hashCode()I

    move-result v0

    return v0
.end method

.method public isEmpty()Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/U0;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    return v0
.end method

.method public final remove(Ljava/lang/Object;)Z
    .locals 1

    const/4 v0, 0x1

    .line 1
    invoke-interface {p0, v0, p1}, Lcom/android/tools/r8/internal/YW;->b(ILjava/lang/Object;)I

    move-result p1

    if-lez p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final removeAll(Ljava/util/Collection;)Z
    .locals 1

    .line 1
    instance-of v0, p1, Lcom/android/tools/r8/internal/YW;

    if-eqz v0, :cond_0

    .line 2
    check-cast p1, Lcom/android/tools/r8/internal/YW;

    invoke-interface {p1}, Lcom/android/tools/r8/internal/YW;->w()Ljava/util/Set;

    move-result-object p1

    .line 5
    :cond_0
    invoke-interface {p0}, Lcom/android/tools/r8/internal/YW;->w()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Set;->removeAll(Ljava/util/Collection;)Z

    move-result p1

    return p1
.end method

.method public final retainAll(Ljava/util/Collection;)Z
    .locals 1

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    instance-of v0, p1, Lcom/android/tools/r8/internal/YW;

    if-eqz v0, :cond_0

    .line 3
    check-cast p1, Lcom/android/tools/r8/internal/YW;

    invoke-interface {p1}, Lcom/android/tools/r8/internal/YW;->w()Ljava/util/Set;

    move-result-object p1

    .line 6
    :cond_0
    invoke-interface {p0}, Lcom/android/tools/r8/internal/YW;->w()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Set;->retainAll(Ljava/util/Collection;)Z

    move-result p1

    return p1
.end method

.method public final toString()Ljava/lang/String;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/U0;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public w()Ljava/util/Set;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/U0;->b:Ljava/util/Set;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/U0;->a()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/U0;->b:Ljava/util/Set;

    :cond_0
    return-object v0
.end method
