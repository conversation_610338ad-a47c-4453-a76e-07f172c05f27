.class public final Lcom/android/tools/r8/internal/w;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final b:Lcom/android/tools/r8/internal/w;


# instance fields
.field public a:Lcom/android/tools/r8/internal/w;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/w;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/w;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/w;->b:Lcom/android/tools/r8/internal/w;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
