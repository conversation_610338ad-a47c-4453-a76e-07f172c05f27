.class public final Lcom/android/tools/r8/internal/Rh;
.super Lcom/android/tools/r8/internal/ex;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/qP;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/ex;-><init>(Lcom/android/tools/r8/internal/qP;)V

    return-void
.end method


# virtual methods
.method public final a(IILcom/android/tools/r8/internal/Co0;Lcom/android/tools/r8/internal/Eo0;)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2, p3, p4}, Lcom/android/tools/r8/internal/ex;->a(IILcom/android/tools/r8/internal/Co0;Lcom/android/tools/r8/internal/Eo0;)V

    .line 2
    new-instance p1, Lcom/android/tools/r8/internal/ex;

    const/4 p2, 0x0

    invoke-direct {p1, p2}, Lcom/android/tools/r8/internal/ex;-><init>(Lcom/android/tools/r8/internal/qP;)V

    const/4 p2, 0x0

    .line 3
    invoke-virtual {p0, p4, p1, p2}, Lcom/android/tools/r8/internal/ex;->a(Lcom/android/tools/r8/internal/Eo0;Lcom/android/tools/r8/internal/ex;I)Z

    .line 4
    iget-object p3, p1, Lcom/android/tools/r8/internal/ex;->b:[I

    iput-object p3, p0, Lcom/android/tools/r8/internal/ex;->b:[I

    .line 5
    iget-object p3, p1, Lcom/android/tools/r8/internal/ex;->c:[I

    iput-object p3, p0, Lcom/android/tools/r8/internal/ex;->c:[I

    .line 6
    iput-short p2, p0, Lcom/android/tools/r8/internal/ex;->f:S

    .line 7
    iget-object p2, p1, Lcom/android/tools/r8/internal/ex;->d:[I

    iput-object p2, p0, Lcom/android/tools/r8/internal/ex;->d:[I

    .line 8
    iget-object p2, p1, Lcom/android/tools/r8/internal/ex;->e:[I

    iput-object p2, p0, Lcom/android/tools/r8/internal/ex;->e:[I

    .line 9
    iget-short p2, p1, Lcom/android/tools/r8/internal/ex;->g:S

    iput-short p2, p0, Lcom/android/tools/r8/internal/ex;->g:S

    .line 10
    iget p2, p1, Lcom/android/tools/r8/internal/ex;->h:I

    iput p2, p0, Lcom/android/tools/r8/internal/ex;->h:I

    .line 11
    iget-object p1, p1, Lcom/android/tools/r8/internal/ex;->i:[I

    iput-object p1, p0, Lcom/android/tools/r8/internal/ex;->i:[I

    return-void
.end method
