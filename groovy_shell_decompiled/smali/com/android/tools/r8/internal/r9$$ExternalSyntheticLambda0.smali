.class public final synthetic Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiFunction;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/N8;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/B1;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/A8;

.field public final synthetic f$3:Lcom/android/tools/r8/graph/J2;

.field public final synthetic f$4:Ljava/util/function/BiFunction;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/N8;Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/internal/A8;Lcom/android/tools/r8/graph/J2;Ljava/util/function/BiFunction;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/N8;

    iput-object p2, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/B1;

    iput-object p3, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/internal/A8;

    iput-object p4, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$3:Lcom/android/tools/r8/graph/J2;

    iput-object p5, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$4:Ljava/util/function/BiFunction;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    iget-object v0, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/N8;

    iget-object v1, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/B1;

    iget-object v2, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$2:Lcom/android/tools/r8/internal/A8;

    iget-object v3, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$3:Lcom/android/tools/r8/graph/J2;

    iget-object v4, p0, Lcom/android/tools/r8/internal/r9$$ExternalSyntheticLambda0;->f$4:Ljava/util/function/BiFunction;

    move-object v5, p1

    check-cast v5, Lcom/android/tools/r8/internal/r9;

    move-object v6, p2

    check-cast v6, Lcom/android/tools/r8/internal/R40;

    invoke-static/range {v0 .. v6}, Lcom/android/tools/r8/internal/r9;->a(Lcom/android/tools/r8/internal/N8;Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/internal/A8;Lcom/android/tools/r8/graph/J2;Ljava/util/function/BiFunction;Lcom/android/tools/r8/internal/r9;Lcom/android/tools/r8/internal/R40;)Lcom/android/tools/r8/internal/r9;

    move-result-object p1

    return-object p1
.end method
