.class public final enum Lcom/android/tools/r8/internal/Te0;
.super Ljava/lang/Enum;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/zH;


# static fields
.field public static final enum c:Lcom/android/tools/r8/internal/Te0;

.field public static final enum d:Lcom/android/tools/r8/internal/Te0;

.field public static final enum e:Lcom/android/tools/r8/internal/Te0;

.field public static final enum f:Lcom/android/tools/r8/internal/Te0;

.field public static final enum g:Lcom/android/tools/r8/internal/Te0;

.field public static final enum h:Lcom/android/tools/r8/internal/Te0;

.field public static final enum i:Lcom/android/tools/r8/internal/Te0;

.field public static final enum j:Lcom/android/tools/r8/internal/Te0;

.field public static final enum k:Lcom/android/tools/r8/internal/Te0;

.field public static final enum l:Lcom/android/tools/r8/internal/Te0;

.field public static final enum m:Lcom/android/tools/r8/internal/Te0;


# instance fields
.field public final b:I


# direct methods
.method static constructor <clinit>()V
    .locals 4

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "NONE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->c:Lcom/android/tools/r8/internal/Te0;

    .line 5
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "PUBLIC"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->d:Lcom/android/tools/r8/internal/Te0;

    .line 9
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "SYSTEM"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->e:Lcom/android/tools/r8/internal/Te0;

    .line 13
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "VENDOR"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->f:Lcom/android/tools/r8/internal/Te0;

    .line 17
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "PRODUCT"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->g:Lcom/android/tools/r8/internal/Te0;

    .line 21
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "SIGNATURE"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->h:Lcom/android/tools/r8/internal/Te0;

    .line 25
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "ODM"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->i:Lcom/android/tools/r8/internal/Te0;

    .line 29
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "OEM"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->j:Lcom/android/tools/r8/internal/Te0;

    .line 33
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "ACTOR"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->k:Lcom/android/tools/r8/internal/Te0;

    .line 37
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "CONFIG_SIGNATURE"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->l:Lcom/android/tools/r8/internal/Te0;

    .line 38
    new-instance v0, Lcom/android/tools/r8/internal/Te0;

    const-string v1, "UNRECOGNIZED"

    const/16 v2, 0xa

    const/4 v3, -0x1

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/Te0;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Te0;->m:Lcom/android/tools/r8/internal/Te0;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    iput p3, p0, Lcom/android/tools/r8/internal/Te0;->b:I

    return-void
.end method


# virtual methods
.method public final getNumber()I
    .locals 2

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Te0;->m:Lcom/android/tools/r8/internal/Te0;

    if-eq p0, v0, :cond_0

    .line 5
    iget v0, p0, Lcom/android/tools/r8/internal/Te0;->b:I

    return v0

    .line 6
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "Can\'t get the number of an unknown enum value."

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
