.class public Lcom/android/tools/r8/internal/qr;
.super Lcom/android/tools/r8/internal/ro;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(III)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/ro;-><init>(III)V

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Zo;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/ro;-><init>(ILcom/android/tools/r8/internal/j8;)V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Vz;)V
    .locals 4

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/UZ;->e:Lcom/android/tools/r8/internal/UZ;

    iget-short v1, p0, Lcom/android/tools/r8/internal/ro;->f:S

    iget-short v2, p0, Lcom/android/tools/r8/internal/ro;->g:S

    iget-short v3, p0, Lcom/android/tools/r8/internal/ro;->h:S

    invoke-virtual {p1, v0, v1, v2, v3}, Lcom/android/tools/r8/internal/Vz;->o(Lcom/android/tools/r8/internal/UZ;III)V

    return-void
.end method

.method public final l()Ljava/lang/String;
    .locals 1

    const-string v0, "ShrInt"

    return-object v0
.end method

.method public final s()I
    .locals 1

    const/16 v0, 0x99

    return v0
.end method

.method public final v()Ljava/lang/String;
    .locals 1

    const-string v0, "shr-int"

    return-object v0
.end method
