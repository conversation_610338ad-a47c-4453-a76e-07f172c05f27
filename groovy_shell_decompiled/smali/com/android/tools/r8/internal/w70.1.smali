.class public final Lcom/android/tools/r8/internal/w70;
.super Lcom/android/tools/r8/internal/Qx;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final v:Lcom/android/tools/r8/internal/w70;

.field public static final w:Lcom/android/tools/r8/internal/u70;


# instance fields
.field public final c:Lcom/android/tools/r8/internal/Y7;

.field public d:I

.field public e:I

.field public f:I

.field public g:I

.field public h:Lcom/android/tools/r8/internal/N70;

.field public i:I

.field public j:Ljava/util/List;

.field public k:Lcom/android/tools/r8/internal/N70;

.field public l:I

.field public m:Ljava/util/List;

.field public n:Ljava/util/List;

.field public o:I

.field public p:Lcom/android/tools/r8/internal/a80;

.field public q:I

.field public r:I

.field public s:Ljava/util/List;

.field public t:B

.field public u:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/u70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/u70;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/w70;->w:Lcom/android/tools/r8/internal/u70;

    .line 1825
    new-instance v0, Lcom/android/tools/r8/internal/w70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/w70;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/w70;->v:Lcom/android/tools/r8/internal/w70;

    .line 1826
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/w70;->f()V

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 676
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Qx;-><init>()V

    const/4 v0, -0x1

    .line 1120
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->o:I

    .line 1241
    iput-byte v0, p0, Lcom/android/tools/r8/internal/w70;->t:B

    .line 1345
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->u:I

    .line 1346
    sget-object v0, Lcom/android/tools/r8/internal/Y7;->b:Lcom/android/tools/r8/internal/XR;

    iput-object v0, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)V
    .locals 12

    .line 1347
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Qx;-><init>()V

    const/4 v0, -0x1

    .line 1776
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->o:I

    .line 1897
    iput-byte v0, p0, Lcom/android/tools/r8/internal/w70;->t:B

    .line 2001
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->u:I

    .line 2002
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w70;->f()V

    .line 2003
    new-instance v0, Lcom/android/tools/r8/internal/W7;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/W7;-><init>()V

    .line 2004
    new-instance v1, Lcom/android/tools/r8/internal/ie;

    const/4 v2, 0x1

    new-array v3, v2, [B

    invoke-direct {v1, v0, v3}, Lcom/android/tools/r8/internal/ie;-><init>(Ljava/io/OutputStream;[B)V

    const/4 v3, 0x0

    move v4, v3

    :cond_0
    :goto_0
    const/16 v5, 0x100

    const/16 v6, 0x2000

    const/16 v7, 0x20

    const/16 v8, 0x200

    if-nez v3, :cond_13

    .line 2005
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->i()I

    move-result v9

    const/4 v10, 0x0

    sparse-switch v9, :sswitch_data_0

    .line 2011
    invoke-virtual {p0, p1, v1, p2, v9}, Lcom/android/tools/r8/internal/Qx;->a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/ie;Lcom/android/tools/r8/internal/Ku;I)Z

    move-result v5

    goto/16 :goto_3

    .line 2137
    :sswitch_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 2138
    invoke-virtual {p1, v9}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v9

    and-int/lit16 v10, v4, 0x2000

    if-eq v10, v6, :cond_1

    .line 2139
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v10

    if-lez v10, :cond_1

    .line 2140
    new-instance v10, Ljava/util/ArrayList;

    invoke-direct {v10}, Ljava/util/ArrayList;-><init>()V

    iput-object v10, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    or-int/lit16 v4, v4, 0x2000

    .line 2143
    :cond_1
    :goto_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v10

    if-lez v10, :cond_2

    .line 2144
    iget-object v10, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 2145
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v11

    .line 2146
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v11

    invoke-interface {v10, v11}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 2147
    :cond_2
    iput v9, p1, Lcom/android/tools/r8/internal/be;->h:I

    .line 2148
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->j()V

    goto :goto_0

    :sswitch_1
    and-int/lit16 v9, v4, 0x2000

    if-eq v9, v6, :cond_3

    .line 2149
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    or-int/lit16 v4, v4, 0x2000

    .line 2152
    :cond_3
    iget-object v9, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 2153
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v10

    .line 2154
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v10

    invoke-interface {v9, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 2155
    :sswitch_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 2156
    invoke-virtual {p1, v9}, Lcom/android/tools/r8/internal/be;->b(I)I

    move-result v9

    and-int/lit16 v10, v4, 0x200

    if-eq v10, v8, :cond_4

    .line 2157
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v10

    if-lez v10, :cond_4

    .line 2158
    new-instance v10, Ljava/util/ArrayList;

    invoke-direct {v10}, Ljava/util/ArrayList;-><init>()V

    iput-object v10, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    or-int/lit16 v4, v4, 0x200

    .line 2161
    :cond_4
    :goto_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->a()I

    move-result v10

    if-lez v10, :cond_5

    .line 2162
    iget-object v10, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    .line 2163
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v11

    .line 2164
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v11

    invoke-interface {v10, v11}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 2165
    :cond_5
    iput v9, p1, Lcom/android/tools/r8/internal/be;->h:I

    .line 2166
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->j()V

    goto/16 :goto_0

    :sswitch_3
    and-int/lit16 v9, v4, 0x200

    if-eq v9, v8, :cond_6

    .line 2167
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    or-int/lit16 v4, v4, 0x200

    .line 2170
    :cond_6
    iget-object v9, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    .line 2171
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v10

    .line 2172
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v10

    invoke-interface {v9, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :sswitch_4
    and-int/lit16 v9, v4, 0x100

    if-eq v9, v5, :cond_7

    .line 2173
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    or-int/lit16 v4, v4, 0x100

    .line 2176
    :cond_7
    iget-object v9, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    sget-object v10, Lcom/android/tools/r8/internal/N70;->v:Lcom/android/tools/r8/internal/H70;

    invoke-virtual {p1, v10, p2}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v10

    invoke-interface {v9, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 2177
    :sswitch_5
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/2addr v9, v2

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    .line 2178
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 2179
    iput v9, p0, Lcom/android/tools/r8/internal/w70;->e:I

    goto/16 :goto_0

    .line 2180
    :sswitch_6
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/lit8 v9, v9, 0x40

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    .line 2181
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 2182
    iput v9, p0, Lcom/android/tools/r8/internal/w70;->l:I

    goto/16 :goto_0

    .line 2183
    :sswitch_7
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/lit8 v9, v9, 0x10

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    .line 2184
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 2185
    iput v9, p0, Lcom/android/tools/r8/internal/w70;->i:I

    goto/16 :goto_0

    .line 2186
    :sswitch_8
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/2addr v9, v8

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    .line 2187
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 2188
    iput v9, p0, Lcom/android/tools/r8/internal/w70;->r:I

    goto/16 :goto_0

    .line 2189
    :sswitch_9
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/2addr v9, v5

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    .line 2190
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 2191
    iput v9, p0, Lcom/android/tools/r8/internal/w70;->q:I

    goto/16 :goto_0

    .line 2192
    :sswitch_a
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v11, 0x80

    and-int/2addr v9, v11

    if-ne v9, v11, :cond_8

    .line 2193
    iget-object v9, p0, Lcom/android/tools/r8/internal/w70;->p:Lcom/android/tools/r8/internal/a80;

    invoke-virtual {v9}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2194
    new-instance v10, Lcom/android/tools/r8/internal/Z70;

    invoke-direct {v10}, Lcom/android/tools/r8/internal/Z70;-><init>()V

    .line 2195
    invoke-virtual {v10, v9}, Lcom/android/tools/r8/internal/Z70;->a(Lcom/android/tools/r8/internal/a80;)Lcom/android/tools/r8/internal/Z70;

    move-result-object v10

    .line 2196
    :cond_8
    sget-object v9, Lcom/android/tools/r8/internal/a80;->n:Lcom/android/tools/r8/internal/Y70;

    invoke-virtual {p1, v9, p2}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v9

    check-cast v9, Lcom/android/tools/r8/internal/a80;

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->p:Lcom/android/tools/r8/internal/a80;

    if-eqz v10, :cond_9

    .line 2198
    invoke-virtual {v10, v9}, Lcom/android/tools/r8/internal/Z70;->a(Lcom/android/tools/r8/internal/a80;)Lcom/android/tools/r8/internal/Z70;

    .line 2199
    invoke-virtual {v10}, Lcom/android/tools/r8/internal/Z70;->c()Lcom/android/tools/r8/internal/a80;

    move-result-object v9

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->p:Lcom/android/tools/r8/internal/a80;

    .line 2201
    :cond_9
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/2addr v9, v11

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    goto/16 :goto_0

    .line 2202
    :sswitch_b
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    and-int/2addr v9, v7

    if-ne v9, v7, :cond_a

    .line 2203
    iget-object v9, p0, Lcom/android/tools/r8/internal/w70;->k:Lcom/android/tools/r8/internal/N70;

    invoke-virtual {v9}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2204
    invoke-static {v9}, Lcom/android/tools/r8/internal/N70;->a(Lcom/android/tools/r8/internal/N70;)Lcom/android/tools/r8/internal/M70;

    move-result-object v10

    .line 2205
    :cond_a
    sget-object v9, Lcom/android/tools/r8/internal/N70;->v:Lcom/android/tools/r8/internal/H70;

    invoke-virtual {p1, v9, p2}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v9

    check-cast v9, Lcom/android/tools/r8/internal/N70;

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->k:Lcom/android/tools/r8/internal/N70;

    if-eqz v10, :cond_b

    .line 2207
    invoke-virtual {v10, v9}, Lcom/android/tools/r8/internal/M70;->a(Lcom/android/tools/r8/internal/N70;)Lcom/android/tools/r8/internal/M70;

    .line 2208
    invoke-virtual {v10}, Lcom/android/tools/r8/internal/M70;->d()Lcom/android/tools/r8/internal/N70;

    move-result-object v9

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->k:Lcom/android/tools/r8/internal/N70;

    .line 2210
    :cond_b
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/2addr v9, v7

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    goto/16 :goto_0

    :sswitch_c
    and-int/lit8 v9, v4, 0x20

    if-eq v9, v7, :cond_c

    .line 2211
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    or-int/lit8 v4, v4, 0x20

    .line 2214
    :cond_c
    iget-object v9, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    sget-object v10, Lcom/android/tools/r8/internal/U70;->o:Lcom/android/tools/r8/internal/R70;

    invoke-virtual {p1, v10, p2}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v10

    invoke-interface {v9, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 2215
    :sswitch_d
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v11, 0x8

    and-int/2addr v9, v11

    if-ne v9, v11, :cond_d

    .line 2216
    iget-object v9, p0, Lcom/android/tools/r8/internal/w70;->h:Lcom/android/tools/r8/internal/N70;

    invoke-virtual {v9}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2217
    invoke-static {v9}, Lcom/android/tools/r8/internal/N70;->a(Lcom/android/tools/r8/internal/N70;)Lcom/android/tools/r8/internal/M70;

    move-result-object v10

    .line 2218
    :cond_d
    sget-object v9, Lcom/android/tools/r8/internal/N70;->v:Lcom/android/tools/r8/internal/H70;

    invoke-virtual {p1, v9, p2}, Lcom/android/tools/r8/internal/be;->a(Lcom/android/tools/r8/internal/x30;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/N0;

    move-result-object v9

    check-cast v9, Lcom/android/tools/r8/internal/N70;

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->h:Lcom/android/tools/r8/internal/N70;

    if-eqz v10, :cond_e

    .line 2220
    invoke-virtual {v10, v9}, Lcom/android/tools/r8/internal/M70;->a(Lcom/android/tools/r8/internal/N70;)Lcom/android/tools/r8/internal/M70;

    .line 2221
    invoke-virtual {v10}, Lcom/android/tools/r8/internal/M70;->d()Lcom/android/tools/r8/internal/N70;

    move-result-object v9

    iput-object v9, p0, Lcom/android/tools/r8/internal/w70;->h:Lcom/android/tools/r8/internal/N70;

    .line 2223
    :cond_e
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/2addr v9, v11

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    goto/16 :goto_0

    .line 2224
    :sswitch_e
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/lit8 v9, v9, 0x4

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    .line 2225
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 2226
    iput v9, p0, Lcom/android/tools/r8/internal/w70;->g:I

    goto/16 :goto_0

    .line 2227
    :sswitch_f
    iget v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    or-int/lit8 v9, v9, 0x2

    iput v9, p0, Lcom/android/tools/r8/internal/w70;->d:I

    .line 2228
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/be;->f()I

    move-result v9

    .line 2229
    iput v9, p0, Lcom/android/tools/r8/internal/w70;->f:I
    :try_end_0
    .catch Lcom/android/tools/r8/internal/kI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto/16 :goto_0

    :goto_3
    if-nez v5, :cond_0

    :sswitch_10
    move v3, v2

    goto/16 :goto_0

    :catchall_0
    move-exception p1

    goto :goto_4

    :catch_0
    move-exception p1

    .line 2364
    :try_start_1
    new-instance p2, Lcom/android/tools/r8/internal/kI;

    .line 2365
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/kI;-><init>(Ljava/lang/String;)V

    .line 2366
    iput-object p0, p2, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 2367
    throw p2

    :catch_1
    move-exception p1

    .line 2368
    iput-object p0, p1, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 2369
    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :goto_4
    and-int/lit8 p2, v4, 0x20

    if-ne p2, v7, :cond_f

    .line 2375
    iget-object p2, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    :cond_f
    and-int/lit16 p2, v4, 0x100

    if-ne p2, v5, :cond_10

    .line 2378
    iget-object p2, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    :cond_10
    and-int/lit16 p2, v4, 0x200

    if-ne p2, v8, :cond_11

    .line 2381
    iget-object p2, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    :cond_11
    and-int/lit16 p2, v4, 0x2000

    if-ne p2, v6, :cond_12

    .line 2384
    iget-object p2, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 2387
    :cond_12
    :try_start_2
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/ie;->a()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 2391
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    goto :goto_5

    :catchall_1
    move-exception p1

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    .line 2392
    throw p1

    .line 2393
    :catch_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    .line 2394
    :goto_5
    iget-object p2, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Mv;->a()V

    .line 2395
    throw p1

    :cond_13
    and-int/lit8 p1, v4, 0x20

    if-ne p1, v7, :cond_14

    .line 2396
    iget-object p1, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    :cond_14
    and-int/lit16 p1, v4, 0x100

    if-ne p1, v5, :cond_15

    .line 2399
    iget-object p1, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    :cond_15
    and-int/lit16 p1, v4, 0x200

    if-ne p1, v8, :cond_16

    .line 2402
    iget-object p1, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    :cond_16
    and-int/lit16 p1, v4, 0x2000

    if-ne p1, v6, :cond_17

    .line 2405
    iget-object p1, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 2408
    :cond_17
    :try_start_3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/ie;->a()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    .line 2412
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    goto :goto_6

    :catchall_2
    move-exception p1

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    .line 2413
    throw p1

    .line 2414
    :catch_3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/W7;->c()Lcom/android/tools/r8/internal/Y7;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    .line 2415
    :goto_6
    iget-object p1, p0, Lcom/android/tools/r8/internal/Qx;->b:Lcom/android/tools/r8/internal/Mv;

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Mv;->a()V

    return-void

    :sswitch_data_0
    .sparse-switch
        0x0 -> :sswitch_10
        0x8 -> :sswitch_f
        0x10 -> :sswitch_e
        0x1a -> :sswitch_d
        0x22 -> :sswitch_c
        0x2a -> :sswitch_b
        0x32 -> :sswitch_a
        0x38 -> :sswitch_9
        0x40 -> :sswitch_8
        0x48 -> :sswitch_7
        0x50 -> :sswitch_6
        0x58 -> :sswitch_5
        0x62 -> :sswitch_4
        0x68 -> :sswitch_3
        0x6a -> :sswitch_2
        0xf8 -> :sswitch_1
        0xfa -> :sswitch_0
    .end sparse-switch
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/v70;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Qx;-><init>(Lcom/android/tools/r8/internal/Ox;)V

    const/4 v0, -0x1

    .line 448
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->o:I

    .line 569
    iput-byte v0, p0, Lcom/android/tools/r8/internal/w70;->t:B

    .line 673
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->u:I

    .line 674
    iget-object p1, p1, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    .line 675
    iput-object p1, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 8

    .line 59
    iget v0, p0, Lcom/android/tools/r8/internal/w70;->u:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    .line 63
    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/4 v1, 0x2

    and-int/2addr v0, v1

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-ne v0, v1, :cond_1

    .line 64
    iget v0, p0, Lcom/android/tools/r8/internal/w70;->f:I

    .line 65
    invoke-static {v3, v0}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v0

    goto :goto_0

    :cond_1
    move v0, v2

    .line 67
    :goto_0
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/4 v5, 0x4

    and-int/2addr v4, v5

    if-ne v4, v5, :cond_2

    .line 68
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->g:I

    .line 69
    invoke-static {v1, v4}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v4

    add-int/2addr v0, v4

    .line 71
    :cond_2
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v6, 0x8

    and-int/2addr v4, v6

    if-ne v4, v6, :cond_3

    .line 72
    iget-object v4, p0, Lcom/android/tools/r8/internal/w70;->h:Lcom/android/tools/r8/internal/N70;

    const/4 v7, 0x3

    .line 73
    invoke-static {v7, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    :cond_3
    move v4, v2

    .line 75
    :goto_1
    iget-object v7, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    invoke-interface {v7}, Ljava/util/List;->size()I

    move-result v7

    if-ge v4, v7, :cond_4

    .line 76
    iget-object v7, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    .line 77
    invoke-interface {v7, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/android/tools/r8/internal/N0;

    invoke-static {v5, v7}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v7

    add-int/2addr v0, v7

    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    .line 79
    :cond_4
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x20

    and-int/2addr v4, v5

    if-ne v4, v5, :cond_5

    .line 80
    iget-object v4, p0, Lcom/android/tools/r8/internal/w70;->k:Lcom/android/tools/r8/internal/N70;

    const/4 v5, 0x5

    .line 81
    invoke-static {v5, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    .line 83
    :cond_5
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x80

    and-int/2addr v4, v5

    if-ne v4, v5, :cond_6

    .line 84
    iget-object v4, p0, Lcom/android/tools/r8/internal/w70;->p:Lcom/android/tools/r8/internal/a80;

    const/4 v5, 0x6

    .line 85
    invoke-static {v5, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    .line 87
    :cond_6
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x100

    and-int/2addr v4, v5

    if-ne v4, v5, :cond_7

    .line 88
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->q:I

    const/4 v5, 0x7

    .line 89
    invoke-static {v5, v4}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v4

    add-int/2addr v0, v4

    .line 91
    :cond_7
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x200

    and-int/2addr v4, v5

    if-ne v4, v5, :cond_8

    .line 92
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->r:I

    .line 93
    invoke-static {v6, v4}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v4

    add-int/2addr v0, v4

    .line 95
    :cond_8
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x10

    and-int/2addr v4, v5

    if-ne v4, v5, :cond_9

    .line 96
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->i:I

    const/16 v5, 0x9

    .line 97
    invoke-static {v5, v4}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v4

    add-int/2addr v0, v4

    .line 99
    :cond_9
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x40

    and-int/2addr v4, v5

    const/16 v6, 0xa

    if-ne v4, v5, :cond_a

    .line 100
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->l:I

    .line 101
    invoke-static {v6, v4}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v4

    add-int/2addr v0, v4

    .line 103
    :cond_a
    iget v4, p0, Lcom/android/tools/r8/internal/w70;->d:I

    and-int/2addr v4, v3

    if-ne v4, v3, :cond_b

    .line 104
    iget v3, p0, Lcom/android/tools/r8/internal/w70;->e:I

    const/16 v4, 0xb

    .line 105
    invoke-static {v4, v3}, Lcom/android/tools/r8/internal/ie;->a(II)I

    move-result v3

    add-int/2addr v0, v3

    :cond_b
    move v3, v2

    .line 107
    :goto_2
    iget-object v4, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v3, v4, :cond_c

    .line 108
    iget-object v4, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    .line 109
    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/N0;

    const/16 v5, 0xc

    invoke-static {v5, v4}, Lcom/android/tools/r8/internal/ie;->a(ILcom/android/tools/r8/internal/N0;)I

    move-result v4

    add-int/2addr v0, v4

    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    :cond_c
    move v3, v2

    move v4, v3

    .line 113
    :goto_3
    iget-object v5, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v5

    if-ge v3, v5, :cond_e

    .line 114
    iget-object v5, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    .line 115
    invoke-interface {v5, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/Integer;

    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    move-result v5

    if-ltz v5, :cond_d

    .line 116
    invoke-static {v5}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v5

    goto :goto_4

    :cond_d
    move v5, v6

    :goto_4
    add-int/2addr v4, v5

    add-int/lit8 v3, v3, 0x1

    goto :goto_3

    :cond_e
    add-int/2addr v0, v4

    .line 117
    iget-object v3, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    .line 118
    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v3

    if-nez v3, :cond_10

    add-int/lit8 v0, v0, 0x1

    if-ltz v4, :cond_f

    .line 119
    invoke-static {v4}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v3

    goto :goto_5

    :cond_f
    move v3, v6

    :goto_5
    add-int/2addr v0, v3

    .line 120
    :cond_10
    iput v4, p0, Lcom/android/tools/r8/internal/w70;->o:I

    move v3, v2

    .line 124
    :goto_6
    iget-object v4, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v2, v4, :cond_12

    .line 125
    iget-object v4, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 126
    invoke-interface {v4, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    if-ltz v4, :cond_11

    .line 127
    invoke-static {v4}, Lcom/android/tools/r8/internal/ie;->b(I)I

    move-result v4

    goto :goto_7

    :cond_11
    move v4, v6

    :goto_7
    add-int/2addr v3, v4

    add-int/lit8 v2, v2, 0x1

    goto :goto_6

    :cond_12
    add-int/2addr v0, v3

    .line 128
    iget-object v2, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    .line 129
    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    mul-int/2addr v2, v1

    add-int/2addr v2, v0

    .line 131
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Qx;->e()I

    move-result v0

    add-int/2addr v0, v2

    .line 132
    iget-object v1, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Y7;->size()I

    move-result v1

    add-int/2addr v1, v0

    .line 133
    iput v1, p0, Lcom/android/tools/r8/internal/w70;->u:I

    return v1
.end method

.method public final a(Lcom/android/tools/r8/internal/ie;)V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/w70;->a()I

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Px;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/Px;-><init>(Lcom/android/tools/r8/internal/Qx;)V

    .line 3
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/4 v2, 0x2

    and-int/2addr v1, v2

    const/4 v3, 0x1

    const/4 v4, 0x0

    if-ne v1, v2, :cond_0

    .line 4
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->f:I

    .line 5
    invoke-virtual {p1, v3, v4}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 6
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 7
    :cond_0
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/4 v5, 0x4

    and-int/2addr v1, v5

    if-ne v1, v5, :cond_1

    .line 8
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->g:I

    .line 9
    invoke-virtual {p1, v2, v4}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 10
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 11
    :cond_1
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v2, 0x8

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_2

    .line 12
    iget-object v1, p0, Lcom/android/tools/r8/internal/w70;->h:Lcom/android/tools/r8/internal/N70;

    const/4 v6, 0x3

    invoke-virtual {p1, v6, v1}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    :cond_2
    move v1, v4

    .line 14
    :goto_0
    iget-object v6, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    if-ge v1, v6, :cond_3

    .line 15
    iget-object v6, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    invoke-interface {v6, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lcom/android/tools/r8/internal/N0;

    invoke-virtual {p1, v5, v6}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 17
    :cond_3
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x20

    and-int/2addr v1, v5

    if-ne v1, v5, :cond_4

    .line 18
    iget-object v1, p0, Lcom/android/tools/r8/internal/w70;->k:Lcom/android/tools/r8/internal/N70;

    const/4 v5, 0x5

    invoke-virtual {p1, v5, v1}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    .line 20
    :cond_4
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x80

    and-int/2addr v1, v5

    if-ne v1, v5, :cond_5

    .line 21
    iget-object v1, p0, Lcom/android/tools/r8/internal/w70;->p:Lcom/android/tools/r8/internal/a80;

    const/4 v5, 0x6

    invoke-virtual {p1, v5, v1}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    .line 23
    :cond_5
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x100

    and-int/2addr v1, v5

    if-ne v1, v5, :cond_6

    const/4 v1, 0x7

    .line 24
    iget v5, p0, Lcom/android/tools/r8/internal/w70;->q:I

    .line 25
    invoke-virtual {p1, v1, v4}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 26
    invoke-virtual {p1, v5}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 27
    :cond_6
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v5, 0x200

    and-int/2addr v1, v5

    if-ne v1, v5, :cond_7

    .line 28
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->r:I

    .line 29
    invoke-virtual {p1, v2, v4}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 30
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 31
    :cond_7
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v2, 0x10

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_8

    const/16 v1, 0x9

    .line 32
    iget v2, p0, Lcom/android/tools/r8/internal/w70;->i:I

    .line 33
    invoke-virtual {p1, v1, v4}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 34
    invoke-virtual {p1, v2}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 35
    :cond_8
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v2, 0x40

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_9

    const/16 v1, 0xa

    .line 36
    iget v2, p0, Lcom/android/tools/r8/internal/w70;->l:I

    .line 37
    invoke-virtual {p1, v1, v4}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 38
    invoke-virtual {p1, v2}, Lcom/android/tools/r8/internal/ie;->d(I)V

    .line 39
    :cond_9
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->d:I

    and-int/2addr v1, v3

    if-ne v1, v3, :cond_a

    const/16 v1, 0xb

    .line 40
    iget v2, p0, Lcom/android/tools/r8/internal/w70;->e:I

    .line 41
    invoke-virtual {p1, v1, v4}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 42
    invoke-virtual {p1, v2}, Lcom/android/tools/r8/internal/ie;->d(I)V

    :cond_a
    move v1, v4

    .line 43
    :goto_1
    iget-object v2, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_b

    .line 44
    iget-object v2, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/internal/N0;

    const/16 v3, 0xc

    invoke-virtual {p1, v3, v2}, Lcom/android/tools/r8/internal/ie;->b(ILcom/android/tools/r8/internal/N0;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 45
    :cond_b
    iget-object v1, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    .line 46
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_c

    const/16 v1, 0x6a

    .line 47
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    .line 48
    iget v1, p0, Lcom/android/tools/r8/internal/w70;->o:I

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/ie;->g(I)V

    :cond_c
    move v1, v4

    .line 50
    :goto_2
    iget-object v2, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_d

    .line 51
    iget-object v2, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    invoke-virtual {p1, v2}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_2

    :cond_d
    move v1, v4

    .line 53
    :goto_3
    iget-object v2, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_e

    const/16 v2, 0x1f

    .line 54
    iget-object v3, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    .line 55
    invoke-virtual {p1, v2, v4}, Lcom/android/tools/r8/internal/ie;->c(II)V

    .line 56
    invoke-virtual {p1, v3}, Lcom/android/tools/r8/internal/ie;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    :cond_e
    const/16 v1, 0x4a38

    .line 57
    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Px;->a(ILcom/android/tools/r8/internal/ie;)V

    .line 58
    iget-object v0, p0, Lcom/android/tools/r8/internal/w70;->c:Lcom/android/tools/r8/internal/Y7;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/ie;->a(Lcom/android/tools/r8/internal/Y7;)V

    return-void
.end method

.method public final b()Lcom/android/tools/r8/internal/Nx;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/v70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/v70;-><init>()V

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/Nx;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/v70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/v70;-><init>()V

    .line 2
    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/v70;->a(Lcom/android/tools/r8/internal/w70;)Lcom/android/tools/r8/internal/v70;

    move-result-object v0

    return-object v0
.end method

.method public final f()V
    .locals 3

    const/16 v0, 0x206

    .line 1
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->e:I

    const/16 v0, 0x806

    .line 2
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->f:I

    const/4 v0, 0x0

    .line 3
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->g:I

    .line 4
    sget-object v1, Lcom/android/tools/r8/internal/N70;->u:Lcom/android/tools/r8/internal/N70;

    .line 5
    iput-object v1, p0, Lcom/android/tools/r8/internal/w70;->h:Lcom/android/tools/r8/internal/N70;

    .line 6
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->i:I

    .line 7
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v2

    iput-object v2, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    .line 8
    iput-object v1, p0, Lcom/android/tools/r8/internal/w70;->k:Lcom/android/tools/r8/internal/N70;

    .line 9
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->l:I

    .line 10
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    .line 11
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/internal/w70;->n:Ljava/util/List;

    .line 12
    sget-object v1, Lcom/android/tools/r8/internal/a80;->m:Lcom/android/tools/r8/internal/a80;

    .line 13
    iput-object v1, p0, Lcom/android/tools/r8/internal/w70;->p:Lcom/android/tools/r8/internal/a80;

    .line 14
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->q:I

    .line 15
    iput v0, p0, Lcom/android/tools/r8/internal/w70;->r:I

    .line 16
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/w70;->s:Ljava/util/List;

    return-void
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/N0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/w70;->v:Lcom/android/tools/r8/internal/w70;

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 5

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/internal/w70;->t:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v2, 0x0

    if-nez v0, :cond_1

    return v2

    .line 2
    :cond_1
    iget v0, p0, Lcom/android/tools/r8/internal/w70;->d:I

    and-int/lit8 v3, v0, 0x4

    const/4 v4, 0x4

    if-ne v3, v4, :cond_a

    const/16 v3, 0x8

    and-int/2addr v0, v3

    if-ne v0, v3, :cond_2

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/w70;->h:Lcom/android/tools/r8/internal/N70;

    .line 4
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/N70;->isInitialized()Z

    move-result v0

    if-nez v0, :cond_2

    .line 5
    iput-byte v2, p0, Lcom/android/tools/r8/internal/w70;->t:B

    return v2

    :cond_2
    move v0, v2

    .line 6
    :goto_0
    iget-object v3, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_4

    .line 7
    iget-object v3, p0, Lcom/android/tools/r8/internal/w70;->j:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/U70;

    .line 8
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/U70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_3

    .line 9
    iput-byte v2, p0, Lcom/android/tools/r8/internal/w70;->t:B

    return v2

    :cond_3
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 10
    :cond_4
    iget v0, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v3, 0x20

    and-int/2addr v0, v3

    if-ne v0, v3, :cond_5

    .line 11
    iget-object v0, p0, Lcom/android/tools/r8/internal/w70;->k:Lcom/android/tools/r8/internal/N70;

    .line 12
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/N70;->isInitialized()Z

    move-result v0

    if-nez v0, :cond_5

    .line 13
    iput-byte v2, p0, Lcom/android/tools/r8/internal/w70;->t:B

    return v2

    :cond_5
    move v0, v2

    .line 14
    :goto_1
    iget-object v3, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_7

    .line 15
    iget-object v3, p0, Lcom/android/tools/r8/internal/w70;->m:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/N70;

    .line 16
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/N70;->isInitialized()Z

    move-result v3

    if-nez v3, :cond_6

    .line 17
    iput-byte v2, p0, Lcom/android/tools/r8/internal/w70;->t:B

    return v2

    :cond_6
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    .line 18
    :cond_7
    iget v0, p0, Lcom/android/tools/r8/internal/w70;->d:I

    const/16 v3, 0x80

    and-int/2addr v0, v3

    if-ne v0, v3, :cond_8

    .line 19
    iget-object v0, p0, Lcom/android/tools/r8/internal/w70;->p:Lcom/android/tools/r8/internal/a80;

    .line 20
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/a80;->isInitialized()Z

    move-result v0

    if-nez v0, :cond_8

    .line 21
    iput-byte v2, p0, Lcom/android/tools/r8/internal/w70;->t:B

    return v2

    .line 25
    :cond_8
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Qx;->d()Z

    move-result v0

    if-nez v0, :cond_9

    .line 26
    iput-byte v2, p0, Lcom/android/tools/r8/internal/w70;->t:B

    return v2

    .line 29
    :cond_9
    iput-byte v1, p0, Lcom/android/tools/r8/internal/w70;->t:B

    return v1

    .line 30
    :cond_a
    iput-byte v2, p0, Lcom/android/tools/r8/internal/w70;->t:B

    return v2
.end method
