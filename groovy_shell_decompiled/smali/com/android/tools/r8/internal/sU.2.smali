.class public interface abstract Lcom/android/tools/r8/internal/sU;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# virtual methods
.method public abstract a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;
.end method

.method public abstract a(Lcom/android/tools/r8/graph/y;)V
.end method

.method public abstract a(Lcom/android/tools/r8/graph/J2;)Z
.end method

.method public b(Lcom/android/tools/r8/graph/J2;)Z
    .locals 1

    .line 1
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/sU;->c(Lcom/android/tools/r8/graph/J2;)Z

    move-result v0

    if-nez v0, :cond_1

    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/sU;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public abstract c(Lcom/android/tools/r8/graph/J2;)Z
.end method
