.class public final Lcom/android/tools/r8/internal/RD;
.super Lcom/android/tools/r8/internal/b0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/WD;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/WD;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/RD;->b:Lcom/android/tools/r8/internal/WD;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/b0;-><init>()V

    return-void
.end method


# virtual methods
.method public final clear()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/RD;->b:Lcom/android/tools/r8/internal/WD;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/WD;->clear()V

    return-void
.end method

.method public final h(I)Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/RD;->b:Lcom/android/tools/r8/internal/WD;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/WD;->a(I)Z

    move-result p1

    return p1
.end method

.method public final iterator()Lcom/android/tools/r8/internal/EG;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/QD;

    iget-object v1, p0, Lcom/android/tools/r8/internal/RD;->b:Lcom/android/tools/r8/internal/WD;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/QD;-><init>(Lcom/android/tools/r8/internal/WD;)V

    return-object v0
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 2

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/QD;

    iget-object v1, p0, Lcom/android/tools/r8/internal/RD;->b:Lcom/android/tools/r8/internal/WD;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/QD;-><init>(Lcom/android/tools/r8/internal/WD;)V

    return-object v0
.end method

.method public final k(I)Z
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/RD;->b:Lcom/android/tools/r8/internal/WD;

    iget v1, v0, Lcom/android/tools/r8/internal/WD;->i:I

    .line 2
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/WD;->remove(I)I

    .line 3
    iget-object p1, p0, Lcom/android/tools/r8/internal/RD;->b:Lcom/android/tools/r8/internal/WD;

    iget p1, p1, Lcom/android/tools/r8/internal/WD;->i:I

    if-eq p1, v1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/RD;->b:Lcom/android/tools/r8/internal/WD;

    iget v0, v0, Lcom/android/tools/r8/internal/WD;->i:I

    return v0
.end method
