.class public final Lcom/android/tools/r8/internal/Ug;
.super Lcom/android/tools/r8/internal/Ud;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Ud;-><init>(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/aA;ZLjava/lang/String;)V
    .locals 0

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;)Z
    .locals 0

    .line 1
    iget-object p2, p0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/utils/w;->d0()Z

    move-result p2

    if-eqz p2, :cond_0

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/j1;->m1()Z

    move-result p2

    if-eqz p2, :cond_0

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/J2;->Y0()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/android/tools/r8/internal/Nk;->B(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final b(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/Xd;
    .locals 5

    .line 2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/aA;->r()Lcom/android/tools/r8/internal/cA;

    move-result-object p1

    const/4 v0, 0x0

    .line 3
    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/ListIterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 4
    invoke-interface {p1}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/rD;

    .line 5
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->A1()Z

    move-result v2

    if-eqz v2, :cond_0

    .line 6
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->H()Lcom/android/tools/r8/internal/Sg;

    move-result-object v2

    .line 8
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v1

    .line 9
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->D()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->C()Z

    move-result v3

    if-nez v3, :cond_0

    .line 10
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vt0;->e0()Lcom/android/tools/r8/internal/rD;

    move-result-object v3

    .line 11
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/rD;->e()Z

    move-result v4

    if-nez v4, :cond_1

    .line 12
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/rD;->m2()Z

    move-result v4

    if-nez v4, :cond_1

    .line 13
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/rD;->q1()Z

    move-result v4

    if-eqz v4, :cond_0

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/rD;->y()Lcom/android/tools/r8/internal/Q3;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Q3;->value()Lcom/android/tools/r8/internal/vt0;

    move-result-object v3

    if-ne v3, v1, :cond_0

    .line 14
    :cond_1
    new-instance v0, Lcom/android/tools/r8/internal/md0;

    .line 15
    iget-object v1, v2, Lcom/android/tools/r8/internal/rD;->b:Lcom/android/tools/r8/internal/vt0;

    .line 16
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Sg;->N2()I

    move-result v2

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/md0;-><init>(Lcom/android/tools/r8/internal/vt0;I)V

    const/4 v1, 0x0

    .line 17
    invoke-interface {p1, v0, v1}, Lcom/android/tools/r8/internal/uD;->a(Lcom/android/tools/r8/internal/rD;Ljava/util/Set;)V

    const/4 v0, 0x1

    goto :goto_0

    .line 18
    :cond_2
    invoke-static {v0}, Lcom/android/tools/r8/internal/Xd;->a(Z)Lcom/android/tools/r8/internal/Wd;

    move-result-object p1

    return-object p1
.end method

.method public final b()Ljava/lang/String;
    .locals 1

    const-string v0, "ConstResourceNumberRewriter"

    return-object v0
.end method
