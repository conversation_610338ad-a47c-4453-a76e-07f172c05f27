.class public final Lcom/android/tools/r8/internal/Sp0;
.super Lcom/android/tools/r8/internal/Tp0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/tracereferences/TraceReferencesConsumer$TracedMethod;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/j1;Lcom/android/tools/r8/diagnostic/DefinitionContext;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/x2;->y0()Lcom/android/tools/r8/references/MethodReference;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/internal/RU;

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/j1;->R0()Lcom/android/tools/r8/graph/H4;

    move-result-object p1

    invoke-direct {v1, p1}, Lcom/android/tools/r8/internal/RU;-><init>(Lcom/android/tools/r8/graph/H4;)V

    const/4 p1, 0x0

    .line 4
    invoke-direct {p0, v0, p2, v1, p1}, Lcom/android/tools/r8/internal/Tp0;-><init>(Ljava/lang/Object;Lcom/android/tools/r8/diagnostic/DefinitionContext;Lcom/android/tools/r8/tracereferences/TraceReferencesConsumer$AccessFlags;Z)V

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/references/MethodReference;Lcom/android/tools/r8/diagnostic/DefinitionContext;Lcom/android/tools/r8/tracereferences/TraceReferencesConsumer$MethodAccessFlags;)V
    .locals 1

    if-nez p3, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 5
    :goto_0
    invoke-direct {p0, p1, p2, p3, v0}, Lcom/android/tools/r8/internal/Tp0;-><init>(Ljava/lang/Object;Lcom/android/tools/r8/diagnostic/DefinitionContext;Lcom/android/tools/r8/tracereferences/TraceReferencesConsumer$AccessFlags;Z)V

    return-void
.end method


# virtual methods
.method public final toString()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Tp0;->a:Ljava/lang/Object;

    .line 2
    check-cast v0, Lcom/android/tools/r8/references/MethodReference;

    invoke-virtual {v0}, Lcom/android/tools/r8/references/MethodReference;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
