.class public final Lcom/android/tools/r8/internal/vY;
.super Lcom/android/tools/r8/internal/bu0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic c:Z = true


# instance fields
.field public final b:Lcom/android/tools/r8/graph/y;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/bu0;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/vY;->b:Lcom/android/tools/r8/graph/y;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/j1;Lcom/android/tools/r8/internal/q7;Lcom/android/tools/r8/graph/z4;)V
    .locals 1

    .line 68
    invoke-interface {p2}, Lcom/android/tools/r8/graph/z4;->d()Lcom/android/tools/r8/graph/j1;

    move-result-object v0

    invoke-static {v0, p0}, Lcom/android/tools/r8/internal/V10;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_0

    return-void

    .line 71
    :cond_0
    invoke-interface {p2}, Lcom/android/tools/r8/graph/z4;->getHolder()Lcom/android/tools/r8/graph/E0;

    move-result-object p0

    if-eqz p0, :cond_1

    .line 72
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result p0

    if-eqz p0, :cond_1

    .line 74
    sget-object p0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {p1, p0}, Lcom/android/tools/r8/internal/q7;->a(Ljava/lang/Object;)V

    :cond_1
    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/y4;)V
    .locals 0

    .line 75
    sget-boolean p0, Lcom/android/tools/r8/internal/vY;->c:Z

    if-eqz p0, :cond_0

    return-void

    :cond_0
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/E2;)Z
    .locals 5

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->G1()Ljava/lang/Iterable;

    move-result-object v0

    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    const/4 v2, 0x1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    .line 6
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p2, v1}, Lcom/android/tools/r8/graph/E0;->b(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/j1;

    move-result-object v1

    if-eqz v1, :cond_0

    return v2

    .line 19
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result v0

    if-eqz v0, :cond_8

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result v0

    if-nez v0, :cond_8

    .line 20
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 21
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->G1()Ljava/lang/Iterable;

    move-result-object p1

    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_2
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    .line 22
    iget-object v3, v1, Lcom/android/tools/r8/graph/j1;->g:Lcom/android/tools/r8/graph/H4;

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/H4;->K()Z

    move-result v3

    if-nez v3, :cond_2

    .line 23
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 38
    :cond_3
    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_4
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_8

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    .line 40
    iget-object v1, p0, Lcom/android/tools/r8/internal/vY;->b:Lcom/android/tools/r8/graph/y;

    .line 42
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    .line 43
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/h1;->E0()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v1, v3, v4}, Lcom/android/tools/r8/graph/j;->c(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/V4;

    move-result-object v1

    iget-object v3, p0, Lcom/android/tools/r8/internal/vY;->b:Lcom/android/tools/r8/graph/y;

    .line 44
    invoke-virtual {v1, p2, v3}, Lcom/android/tools/r8/graph/V4;->b(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/graph/D4;

    move-result-object v1

    .line 45
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/D4;->a()Lcom/android/tools/r8/graph/D4$a;

    move-result-object v1

    .line 46
    sget-boolean v3, Lcom/android/tools/r8/internal/vY;->c:Z

    if-nez v3, :cond_6

    if-eqz v1, :cond_5

    goto :goto_1

    :cond_5
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_6
    :goto_1
    if-nez v1, :cond_7

    return v2

    .line 50
    :cond_7
    invoke-virtual {v1, v0}, Lcom/android/tools/r8/graph/D4$a;->a(Lcom/android/tools/r8/graph/j1;)Z

    move-result v3

    if-eqz v3, :cond_4

    .line 51
    new-instance v3, Lcom/android/tools/r8/internal/q7;

    sget-object v4, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-direct {v3, v4}, Lcom/android/tools/r8/internal/q7;-><init>(Ljava/lang/Object;)V

    .line 52
    new-instance v4, Lcom/android/tools/r8/internal/vY$$ExternalSyntheticLambda0;

    invoke-direct {v4, v0, v3}, Lcom/android/tools/r8/internal/vY$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/j1;Lcom/android/tools/r8/internal/q7;)V

    sget-object v0, Lcom/android/tools/r8/internal/vY$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/vY$$ExternalSyntheticLambda1;

    invoke-virtual {v1, v4, v0}, Lcom/android/tools/r8/graph/D4$a;->a(Ljava/util/function/Consumer;Ljava/util/function/Consumer;)V

    .line 67
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/q7;->a()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    if-eqz v0, :cond_4

    return v2

    :cond_8
    const/4 p1, 0x0

    return p1
.end method

.method public final a(Lcom/android/tools/r8/internal/iu0;)Z
    .locals 1

    .line 1
    iget-object v0, p1, Lcom/android/tools/r8/internal/iu0;->b:Lcom/android/tools/r8/graph/E2;

    .line 2
    iget-object p1, p1, Lcom/android/tools/r8/internal/iu0;->c:Lcom/android/tools/r8/graph/E2;

    .line 3
    invoke-virtual {p0, v0, p1}, Lcom/android/tools/r8/internal/vY;->a(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/E2;)Z

    move-result p1

    xor-int/lit8 p1, p1, 0x1

    return p1
.end method

.method public final f()Ljava/lang/String;
    .locals 1

    const-string v0, "NoMethodResolutionChangesPolicy"

    return-object v0
.end method
