.class public final Lcom/android/tools/r8/internal/su;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/io/Serializable;


# instance fields
.field public final b:Lcom/android/tools/r8/internal/tu;

.field public final c:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/tu;Ljava/lang/Object;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    iput-object p1, p0, Lcom/android/tools/r8/internal/su;->b:Lcom/android/tools/r8/internal/tu;

    .line 4
    iput-object p2, p0, Lcom/android/tools/r8/internal/su;->c:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/su;->c:Ljava/lang/Object;

    return-object v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 2

    if-ne p1, p0, :cond_0

    const/4 p1, 0x1

    return p1

    .line 1
    :cond_0
    instance-of v0, p1, Lcom/android/tools/r8/internal/su;

    if-eqz v0, :cond_1

    .line 2
    check-cast p1, Lcom/android/tools/r8/internal/su;

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/su;->b:Lcom/android/tools/r8/internal/tu;

    iget-object v1, p1, Lcom/android/tools/r8/internal/su;->b:Lcom/android/tools/r8/internal/tu;

    invoke-virtual {v0, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/internal/su;->b:Lcom/android/tools/r8/internal/tu;

    .line 11
    iget-object v1, p0, Lcom/android/tools/r8/internal/su;->c:Ljava/lang/Object;

    iget-object p1, p1, Lcom/android/tools/r8/internal/su;->c:Ljava/lang/Object;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/tu;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    return p1

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public final hashCode()I
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/su;->b:Lcom/android/tools/r8/internal/tu;

    iget-object v1, p0, Lcom/android/tools/r8/internal/su;->c:Ljava/lang/Object;

    if-nez v1, :cond_0

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 v0, 0x0

    goto :goto_0

    .line 2
    :cond_0
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/tu;->a(Ljava/lang/Object;)I

    move-result v0

    :goto_0
    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 2

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lcom/android/tools/r8/internal/su;->b:Lcom/android/tools/r8/internal/tu;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".wrap("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/su;->c:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
