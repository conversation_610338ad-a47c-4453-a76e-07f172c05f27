.class public final Lcom/android/tools/r8/internal/v;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Ljava/lang/Throwable;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/v;

    new-instance v1, Lcom/android/tools/r8/internal/u;

    invoke-direct {v1}, Lcom/android/tools/r8/internal/u;-><init>()V

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/v;-><init>(Ljava/lang/Throwable;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    iput-object p1, p0, Lcom/android/tools/r8/internal/v;->a:Ljava/lang/Throwable;

    return-void
.end method
