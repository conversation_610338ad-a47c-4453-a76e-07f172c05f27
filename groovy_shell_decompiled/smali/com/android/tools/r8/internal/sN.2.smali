.class public final Lcom/android/tools/r8/internal/sN;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Z

.field public final b:Ljava/util/HashSet;


# direct methods
.method public constructor <init>(Z)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    iput-object v0, p0, Lcom/android/tools/r8/internal/sN;->b:Ljava/util/HashSet;

    .line 5
    iput-boolean p1, p0, Lcom/android/tools/r8/internal/sN;->a:Z

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/uN;
    .locals 6

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sN;->b:Ljava/util/HashSet;

    invoke-virtual {v0}, Ljava/util/HashSet;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 2
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/sN;->a:Z

    if-eqz v0, :cond_0

    .line 3
    sget-object v0, Lcom/android/tools/r8/internal/uN;->b:Lcom/android/tools/r8/internal/uN;

    return-object v0

    .line 4
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/YL;

    const-string v1, "Invalid keep options that disallow nothing."

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/YL;-><init>(Ljava/lang/String;)V

    throw v0

    .line 6
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sN;->b:Ljava/util/HashSet;

    invoke-virtual {v0}, Ljava/util/HashSet;->size()I

    move-result v0

    .line 7
    sget-object v1, Lcom/android/tools/r8/internal/tN;->h:[Lcom/android/tools/r8/internal/tN;

    invoke-virtual {v1}, [Lcom/android/tools/r8/internal/tN;->clone()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Lcom/android/tools/r8/internal/tN;

    .line 8
    array-length v2, v2

    if-ne v0, v2, :cond_3

    .line 9
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/sN;->a:Z

    if-nez v0, :cond_2

    .line 10
    sget-object v0, Lcom/android/tools/r8/internal/uN;->b:Lcom/android/tools/r8/internal/uN;

    return-object v0

    .line 11
    :cond_2
    new-instance v0, Lcom/android/tools/r8/internal/YL;

    const-string v1, "Invalid keep options that allow everything."

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/YL;-><init>(Ljava/lang/String;)V

    throw v0

    .line 14
    :cond_3
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/sN;->a:Z

    if-eqz v0, :cond_4

    .line 15
    new-instance v0, Lcom/android/tools/r8/internal/uN;

    iget-object v1, p0, Lcom/android/tools/r8/internal/sN;->b:Ljava/util/HashSet;

    invoke-static {v1}, Lcom/android/tools/r8/internal/LB;->a(Ljava/util/Collection;)Lcom/android/tools/r8/internal/LB;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/android/tools/r8/internal/uN;-><init>(Lcom/android/tools/r8/internal/LB;)V

    return-object v0

    .line 17
    :cond_4
    sget v0, Lcom/android/tools/r8/internal/LB;->c:I

    .line 18
    new-instance v0, Lcom/android/tools/r8/internal/DB;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/DB;-><init>()V

    .line 19
    invoke-virtual {v1}, [Lcom/android/tools/r8/internal/tN;->clone()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Lcom/android/tools/r8/internal/tN;

    .line 20
    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_6

    aget-object v4, v1, v3

    .line 21
    iget-object v5, p0, Lcom/android/tools/r8/internal/sN;->b:Ljava/util/HashSet;

    invoke-virtual {v5, v4}, Ljava/util/HashSet;->contains(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_5

    .line 22
    invoke-virtual {v0, v4}, Lcom/android/tools/r8/internal/DB;->b(Ljava/lang/Object;)Lcom/android/tools/r8/internal/DB;

    :cond_5
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 25
    :cond_6
    new-instance v1, Lcom/android/tools/r8/internal/uN;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/DB;->a()Lcom/android/tools/r8/internal/LB;

    move-result-object v0

    invoke-direct {v1, v0}, Lcom/android/tools/r8/internal/uN;-><init>(Lcom/android/tools/r8/internal/LB;)V

    return-object v1
.end method
