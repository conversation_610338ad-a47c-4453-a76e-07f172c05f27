.class public final Lcom/android/tools/r8/internal/qu0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Lcom/android/tools/r8/graph/B1;

.field public final c:Lcom/android/tools/r8/internal/vz;

.field public final d:Ljava/util/ArrayList;

.field public final e:Lcom/android/tools/r8/internal/pu0;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/vz;Ljava/util/ArrayList;Lcom/android/tools/r8/internal/pu0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/qu0;->a:Lcom/android/tools/r8/graph/y;

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/qu0;->b:Lcom/android/tools/r8/graph/B1;

    .line 4
    iput-object p2, p0, Lcom/android/tools/r8/internal/qu0;->c:Lcom/android/tools/r8/internal/vz;

    .line 5
    iput-object p3, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    .line 6
    iput-object p4, p0, Lcom/android/tools/r8/internal/qu0;->e:Lcom/android/tools/r8/internal/pu0;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/j1;Lcom/android/tools/r8/internal/J50;)V
    .locals 0

    .line 200
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p0

    invoke-interface {p1, p0}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/internal/J50;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/rz;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/D5;)V
    .locals 0

    .line 92
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/x2;

    .line 93
    iget-object p0, p0, Lcom/android/tools/r8/internal/rz;->b:Lcom/android/tools/r8/internal/a6;

    .line 94
    invoke-virtual {p0, p2, p1}, Lcom/android/tools/r8/internal/a6;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/u20;Lcom/android/tools/r8/graph/j1$a;)V
    .locals 1

    .line 95
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 96
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/T6;->e()Z

    move-result v0

    if-nez v0, :cond_0

    .line 97
    invoke-virtual {p1, p0}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/internal/u20;)Lcom/android/tools/r8/graph/j1$a;

    :cond_0
    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/D5;)Z
    .locals 0

    .line 91
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/j1;->k1()Z

    move-result p0

    return p0
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/H4;)Z
    .locals 0

    .line 48
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/H4;->N()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static synthetic b(Lcom/android/tools/r8/graph/D5;)Z
    .locals 0

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/j1;->k1()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static synthetic b(Lcom/android/tools/r8/graph/H4;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/H4;->K()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static synthetic c(Lcom/android/tools/r8/graph/D5;)Z
    .locals 0

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/j1;->k1()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static synthetic c(Lcom/android/tools/r8/graph/H4;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/H4;->L()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static synthetic d(Lcom/android/tools/r8/graph/H4;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/g;->f()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static synthetic e(Lcom/android/tools/r8/graph/H4;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/g;->p()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method

.method public static synthetic f(Lcom/android/tools/r8/graph/H4;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/H4;->Q()Z

    move-result p0

    xor-int/lit8 p0, p0, 0x1

    return p0
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/graph/H4;
    .locals 4

    .line 22
    iget-object v0, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    sget-object v1, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda0;

    .line 23
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/TI;->a(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/Hx;)Lcom/android/tools/r8/internal/QI;

    move-result-object v0

    .line 24
    iget-object v1, v0, Lcom/android/tools/r8/internal/QI;->b:Ljava/lang/Iterable;

    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v1

    iget-object v2, v0, Lcom/android/tools/r8/internal/QI;->c:Lcom/android/tools/r8/internal/Hx;

    invoke-static {v1, v2}, Lcom/android/tools/r8/internal/hJ;->a(Ljava/util/Iterator;Lcom/android/tools/r8/internal/Hx;)Lcom/android/tools/r8/internal/aJ;

    move-result-object v1

    .line 25
    iget-object v2, v1, Lcom/android/tools/r8/internal/Wp0;->b:Ljava/util/Iterator;

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/Wp0;->a(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    .line 26
    check-cast v1, Lcom/android/tools/r8/graph/H4;

    .line 27
    new-instance v2, Lcom/android/tools/r8/graph/H4;

    .line 28
    iget v3, v1, Lcom/android/tools/r8/graph/g;->b:I

    .line 29
    iget v1, v1, Lcom/android/tools/r8/graph/g;->c:I

    invoke-direct {v2, v3, v1}, Lcom/android/tools/r8/graph/H4;-><init>(II)V

    .line 30
    sget-boolean v1, Lcom/android/tools/r8/internal/qu0;->f:Z

    if-nez v1, :cond_1

    sget-object v3, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda11;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda11;

    invoke-static {v0, v3}, Lcom/android/tools/r8/internal/TI;->a(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_1
    :goto_0
    if-nez v1, :cond_3

    .line 31
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/H4;->O()Z

    move-result v3

    if-eqz v3, :cond_3

    sget-object v3, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda9;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda9;

    invoke-static {v0, v3}, Lcom/android/tools/r8/internal/TI;->a(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v3

    if-eqz v3, :cond_2

    goto :goto_1

    :cond_2
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_3
    :goto_1
    if-nez v1, :cond_5

    .line 32
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/H4;->P()Z

    move-result v1

    if-eqz v1, :cond_5

    sget-object v1, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda10;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda10;

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/TI;->a(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v1

    if-eqz v1, :cond_4

    goto :goto_2

    :cond_4
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    .line 33
    :cond_5
    :goto_2
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/H4;->K()Z

    move-result v1

    if-eqz v1, :cond_6

    sget-object v1, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda12;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda12;

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/TI;->b(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v1

    if-eqz v1, :cond_6

    const/16 v1, 0x400

    .line 34
    invoke-virtual {v2, v1}, Lcom/android/tools/r8/graph/g;->b(I)V

    .line 35
    :cond_6
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/H4;->L()Z

    move-result v1

    if-eqz v1, :cond_7

    sget-object v1, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda13;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda13;

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/TI;->b(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v1

    if-eqz v1, :cond_7

    const/16 v1, 0x40

    .line 36
    invoke-virtual {v2, v1}, Lcom/android/tools/r8/graph/g;->b(I)V

    .line 37
    :cond_7
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/g;->f()Z

    move-result v1

    if-eqz v1, :cond_9

    .line 38
    iget-object v1, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v1

    iget-object v3, p0, Lcom/android/tools/r8/internal/qu0;->c:Lcom/android/tools/r8/internal/vz;

    .line 39
    iget-object v3, v3, Lcom/android/tools/r8/internal/vz;->b:Ljava/util/LinkedList;

    .line 40
    invoke-virtual {v3}, Ljava/util/LinkedList;->size()I

    move-result v3

    if-lt v1, v3, :cond_8

    .line 41
    sget-object v1, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda14;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda14;

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/TI;->b(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v1

    if-eqz v1, :cond_9

    :cond_8
    const/16 v1, 0x10

    .line 42
    invoke-virtual {v2, v1}, Lcom/android/tools/r8/graph/g;->b(I)V

    .line 43
    :cond_9
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/g;->p()Z

    move-result v1

    if-eqz v1, :cond_a

    sget-object v1, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda1;

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/TI;->b(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v1

    if-eqz v1, :cond_a

    const/16 v1, 0x1000

    .line 44
    invoke-virtual {v2, v1}, Lcom/android/tools/r8/graph/g;->b(I)V

    .line 45
    :cond_a
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/H4;->Q()Z

    move-result v1

    if-eqz v1, :cond_b

    sget-object v1, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda2;

    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/TI;->b(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v0

    if-eqz v0, :cond_b

    const/16 v0, 0x80

    .line 46
    invoke-virtual {v2, v0}, Lcom/android/tools/r8/graph/g;->b(I)V

    :cond_b
    const/high16 v0, 0x20000

    .line 47
    invoke-virtual {v2, v0}, Lcom/android/tools/r8/graph/g;->b(I)V

    return-object v2
.end method

.method public final a(Lcom/android/tools/r8/internal/Xc;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/graph/x2;
    .locals 8

    .line 1
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    .line 2
    iget-object v1, p0, Lcom/android/tools/r8/internal/qu0;->b:Lcom/android/tools/r8/graph/B1;

    iget-object v2, v0, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    .line 4
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/n1;->l0()Ljava/lang/String;

    move-result-object v2

    .line 5
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object v3

    iget-object v4, v0, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v0, p0, Lcom/android/tools/r8/internal/qu0;->c:Lcom/android/tools/r8/internal/vz;

    .line 6
    iget-object v0, v0, Lcom/android/tools/r8/internal/vz;->d:Lcom/android/tools/r8/graph/E2;

    .line 7
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v5

    .line 8
    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v6, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda10;

    invoke-direct {v6, p1}, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda10;-><init>(Lcom/android/tools/r8/internal/Xc;)V

    .line 9
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 10
    sget-boolean v0, Lcom/android/tools/r8/graph/B1;->v6:Z

    if-nez v0, :cond_1

    if-eqz v3, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    const/4 v7, 0x0

    .line 11
    invoke-virtual/range {v1 .. v7}, Lcom/android/tools/r8/graph/B1;->a(Ljava/lang/String;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/J2;Ljava/util/function/Predicate;I)Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    .line 12
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j1;

    iget-object v1, p0, Lcom/android/tools/r8/internal/qu0;->b:Lcom/android/tools/r8/graph/B1;

    .line 13
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/j1;->O0()V

    const/4 v2, 0x0

    .line 14
    invoke-virtual {p2, v0, v1, v2}, Lcom/android/tools/r8/graph/j1;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/B1;Ljava/util/function/Consumer;)Lcom/android/tools/r8/graph/j1;

    move-result-object p2

    .line 15
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/j1;->R0()Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    .line 16
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/g;->C()V

    .line 17
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/g;->D()V

    .line 18
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/g;->v()V

    .line 19
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/Xc;->a(Lcom/android/tools/r8/graph/j1;)V

    .line 21
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/L50;Lcom/android/tools/r8/internal/Xc;Lcom/android/tools/r8/internal/rz;Lcom/android/tools/r8/internal/Oa0;Ljava/util/function/Consumer;)V
    .locals 8

    .line 98
    sget-boolean v0, Lcom/android/tools/r8/internal/qu0;->f:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 101
    :cond_1
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/qu0;->c()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 102
    invoke-virtual {p0, p2, p3}, Lcom/android/tools/r8/internal/qu0;->a(Lcom/android/tools/r8/internal/Xc;Lcom/android/tools/r8/internal/rz;)V

    return-void

    .line 106
    :cond_2
    new-instance v0, Lcom/android/tools/r8/internal/tF;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/tF;-><init>()V

    .line 110
    iget-object v1, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v1

    const/4 v2, 0x0

    move-object v3, v2

    :cond_3
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_6

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/D5;

    .line 111
    invoke-virtual {v4}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/j1;->k1()Z

    move-result v5

    if-eqz v5, :cond_4

    goto :goto_1

    .line 114
    :cond_4
    invoke-virtual {v4}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/j1;->h1()Z

    move-result v5

    if-eqz v5, :cond_5

    .line 115
    invoke-virtual {v4}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/j1;->T0()Lcom/android/tools/r8/internal/pb;

    move-result-object v5

    .line 116
    invoke-static {v3, v5}, Lcom/android/tools/r8/internal/E20;->b(Lcom/android/tools/r8/internal/E20;Lcom/android/tools/r8/internal/E20;)Lcom/android/tools/r8/internal/E20;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/pb;

    .line 118
    :cond_5
    invoke-virtual {p0, p2, v4}, Lcom/android/tools/r8/internal/qu0;->a(Lcom/android/tools/r8/internal/Xc;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/graph/x2;

    move-result-object v5

    .line 119
    invoke-virtual {v4}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v6

    check-cast v6, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p3, v6, v5}, Lcom/android/tools/r8/internal/rz;->c(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)V

    .line 121
    invoke-virtual {v4}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object v5

    invoke-virtual {p4, v5}, Lcom/android/tools/r8/internal/Oa0;->b(Ljava/lang/Object;)I

    move-result v5

    invoke-virtual {v4}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v6

    check-cast v6, Lcom/android/tools/r8/graph/x2;

    .line 122
    invoke-virtual {v0, v5, v6}, Lcom/android/tools/r8/internal/tF;->a(ILjava/lang/Object;)Ljava/lang/Object;

    if-nez v2, :cond_3

    move-object v2, v4

    goto :goto_1

    .line 129
    :cond_6
    sget-boolean p4, Lcom/android/tools/r8/internal/qu0;->f:Z

    if-nez p4, :cond_8

    if-eqz v2, :cond_7

    goto :goto_2

    :cond_7
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 132
    :cond_8
    :goto_2
    iget-object p4, p0, Lcom/android/tools/r8/internal/qu0;->a:Lcom/android/tools/r8/graph/y;

    .line 133
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object p4

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p4, v1}, Lcom/android/tools/r8/internal/Fy;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/x2;

    move-result-object p4

    .line 134
    iget-object v1, p0, Lcom/android/tools/r8/internal/qu0;->b:Lcom/android/tools/r8/graph/B1;

    .line 136
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/s2;->w0()Lcom/android/tools/r8/graph/I2;

    move-result-object v4

    invoke-virtual {v4}, Lcom/android/tools/r8/graph/n1;->l0()Ljava/lang/String;

    move-result-object v4

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, "$bridge"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    iget-object v5, p4, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    .line 138
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object p4

    .line 139
    invoke-static {p2}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v6, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda10;

    invoke-direct {v6, p2}, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda10;-><init>(Lcom/android/tools/r8/internal/Xc;)V

    .line 140
    invoke-virtual {v1, v4, v5, p4, v6}, Lcom/android/tools/r8/graph/B1;->a(Ljava/lang/String;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/J2;Ljava/util/function/Predicate;)Lcom/android/tools/r8/graph/x2;

    move-result-object p4

    .line 145
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    .line 146
    iget-object v4, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-static {v4}, Lcom/android/tools/r8/internal/QR;->a(Ljava/util/List;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v4}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/x2;

    iget-object v5, p0, Lcom/android/tools/r8/internal/qu0;->c:Lcom/android/tools/r8/internal/vz;

    .line 147
    iget-object v5, v5, Lcom/android/tools/r8/internal/vz;->d:Lcom/android/tools/r8/graph/E2;

    .line 148
    iget-object v6, p0, Lcom/android/tools/r8/internal/qu0;->b:Lcom/android/tools/r8/graph/B1;

    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 149
    invoke-virtual {v5}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v5

    .line 150
    invoke-virtual {v4, v5, v6}, Lcom/android/tools/r8/graph/x2;->b(Lcom/android/tools/r8/graph/G2;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/x2;

    move-result-object v4

    .line 151
    new-instance v5, Lcom/android/tools/r8/internal/fC;

    iget-object v6, p0, Lcom/android/tools/r8/internal/qu0;->c:Lcom/android/tools/r8/internal/vz;

    .line 153
    invoke-virtual {v6}, Lcom/android/tools/r8/internal/vz;->a()Lcom/android/tools/r8/graph/l1;

    move-result-object v6

    iget-object v7, p0, Lcom/android/tools/r8/internal/qu0;->e:Lcom/android/tools/r8/internal/pu0;

    invoke-direct {v5, v6, v0, v7}, Lcom/android/tools/r8/internal/fC;-><init>(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/internal/tF;Lcom/android/tools/r8/internal/pu0;)V

    .line 155
    invoke-static {}, Lcom/android/tools/r8/graph/j1;->F1()Lcom/android/tools/r8/graph/j1$a;

    move-result-object v0

    .line 156
    invoke-virtual {v0, v4}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v0

    .line 157
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/qu0;->a()Lcom/android/tools/r8/graph/H4;

    move-result-object v6

    invoke-virtual {v0, v6}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/H4;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v0

    .line 158
    invoke-virtual {v0, v5}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/graph/i0;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v0

    .line 159
    invoke-virtual {v0, v3}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/internal/pb;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v0

    .line 160
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h1;->C0()Lcom/android/tools/r8/androidapi/f;

    move-result-object v3

    invoke-virtual {v0, v3}, Lcom/android/tools/r8/graph/j1$a;->b(Lcom/android/tools/r8/androidapi/f;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v0

    .line 161
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/j1;->S0()Lcom/android/tools/r8/androidapi/f;

    move-result-object v3

    invoke-virtual {v0, v3}, Lcom/android/tools/r8/graph/j1$a;->a(Lcom/android/tools/r8/androidapi/f;)Lcom/android/tools/r8/graph/j1$a;

    move-result-object v0

    .line 162
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1$a;->a()Lcom/android/tools/r8/graph/j1;

    move-result-object v0

    .line 163
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/j1;->r1()Lcom/android/tools/r8/internal/u20;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/T6;->e()Z

    move-result v3

    if-nez v3, :cond_9

    .line 164
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/j1;->r1()Lcom/android/tools/r8/internal/u20;

    move-result-object v3

    invoke-virtual {v0, v3}, Lcom/android/tools/r8/graph/j1;->a(Lcom/android/tools/r8/internal/u20;)V

    .line 168
    :cond_9
    new-instance v3, Lcom/android/tools/r8/internal/uu0;

    .line 169
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/x2;

    invoke-direct {v3, v2}, Lcom/android/tools/r8/internal/uu0;-><init>(Lcom/android/tools/r8/graph/x2;)V

    .line 170
    iget-object v2, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-virtual {v2}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_3
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_a

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/graph/D5;

    .line 171
    invoke-virtual {v5}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v6

    check-cast v6, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p3, v6, v4}, Lcom/android/tools/r8/internal/rz;->b(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)V

    .line 172
    iget-object v6, p0, Lcom/android/tools/r8/internal/qu0;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v6, v5}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object v5

    invoke-virtual {v3, v5}, Lcom/android/tools/r8/internal/uu0;->a(Lcom/android/tools/r8/shaking/v1;)V

    goto :goto_3

    .line 178
    :cond_a
    iget-object v2, p0, Lcom/android/tools/r8/internal/qu0;->e:Lcom/android/tools/r8/internal/pu0;

    if-eqz v2, :cond_b

    .line 179
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/uu0;->a()Lcom/android/tools/r8/shaking/u1;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/shaking/u1;->z()Lcom/android/tools/r8/shaking/u1;

    .line 183
    :cond_b
    invoke-virtual {p3, p4, v4}, Lcom/android/tools/r8/internal/rz;->c(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;)V

    .line 186
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/L50;->b()Z

    move-result p3

    if-nez p3, :cond_c

    .line 187
    iget-object p3, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-virtual {p3}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :goto_4
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result p4

    if-eqz p4, :cond_c

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p4

    check-cast p4, Lcom/android/tools/r8/graph/D5;

    .line 189
    invoke-virtual {p4}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p4

    check-cast p4, Lcom/android/tools/r8/graph/x2;

    new-instance v2, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda3;

    invoke-direct {v2, v1}, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda3;-><init>(Lcom/android/tools/r8/graph/j1;)V

    .line 190
    invoke-virtual {p1, p4, v2}, Lcom/android/tools/r8/internal/L50;->a(Lcom/android/tools/r8/graph/x2;Ljava/util/function/Consumer;)V

    goto :goto_4

    .line 196
    :cond_c
    invoke-virtual {p2, v0}, Lcom/android/tools/r8/internal/Xc;->b(Lcom/android/tools/r8/graph/j1;)V

    .line 198
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/uu0;->a()Lcom/android/tools/r8/shaking/u1;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/shaking/m1;->k()Z

    move-result p1

    if-nez p1, :cond_d

    .line 199
    invoke-interface {p5, v3}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    :cond_d
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/Xc;Lcom/android/tools/r8/internal/rz;)V
    .locals 6

    .line 49
    iget-object v0, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-static {v0}, Lcom/android/tools/r8/internal/QR;->a(Ljava/util/List;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    iget-object v1, p0, Lcom/android/tools/r8/internal/qu0;->c:Lcom/android/tools/r8/internal/vz;

    .line 50
    iget-object v1, v1, Lcom/android/tools/r8/internal/vz;->d:Lcom/android/tools/r8/graph/E2;

    .line 51
    iget-object v2, p0, Lcom/android/tools/r8/internal/qu0;->b:Lcom/android/tools/r8/graph/B1;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 52
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/E0;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    .line 53
    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/graph/x2;->b(Lcom/android/tools/r8/graph/G2;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/graph/x2;

    move-result-object v0

    .line 54
    iget-object v1, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    sget-object v2, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda8;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda8;

    .line 55
    invoke-static {v1, v2}, Lcom/android/tools/r8/internal/TI;->a(Ljava/util/List;Lcom/android/tools/r8/internal/U40;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/D5;

    if-nez v1, :cond_0

    .line 57
    iget-object v1, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-static {v1}, Lcom/android/tools/r8/internal/QR;->a(Ljava/util/List;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/D5;

    .line 60
    :cond_0
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/H4;->K()Z

    move-result v2

    if-eqz v2, :cond_1

    iget-object v2, p0, Lcom/android/tools/r8/internal/qu0;->e:Lcom/android/tools/r8/internal/pu0;

    if-eqz v2, :cond_1

    .line 61
    iget-object p1, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    new-instance v1, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda4;

    invoke-direct {v1, p2, v0}, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda4;-><init>(Lcom/android/tools/r8/internal/rz;Lcom/android/tools/r8/graph/x2;)V

    invoke-virtual {p1, v1}, Ljava/util/ArrayList;->forEach(Ljava/util/function/Consumer;)V

    return-void

    .line 65
    :cond_1
    iget-object v2, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-virtual {v2}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/D5;

    .line 66
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v4

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v5

    if-ne v4, v5, :cond_2

    .line 67
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/x2;

    const/4 v4, 0x0

    .line 68
    invoke-virtual {p2, v3, v0, v4}, Lcom/android/tools/r8/internal/rz;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Z)V

    goto :goto_0

    .line 69
    :cond_2
    invoke-virtual {v3}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/x2;

    .line 70
    iget-object v4, p2, Lcom/android/tools/r8/internal/rz;->b:Lcom/android/tools/r8/internal/a6;

    .line 71
    invoke-virtual {v4, v3, v0}, Lcom/android/tools/r8/internal/a6;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 72
    :cond_3
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object p2

    iget-object v2, p0, Lcom/android/tools/r8/internal/qu0;->c:Lcom/android/tools/r8/internal/vz;

    .line 73
    iget-object v2, v2, Lcom/android/tools/r8/internal/vz;->d:Lcom/android/tools/r8/graph/E2;

    if-ne p2, v2, :cond_4

    .line 74
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j1;

    goto :goto_1

    .line 78
    :cond_4
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/j1;->r1()Lcom/android/tools/r8/internal/u20;

    move-result-object p2

    .line 81
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    iget-object v2, p0, Lcom/android/tools/r8/internal/qu0;->b:Lcom/android/tools/r8/graph/B1;

    new-instance v3, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda5;

    invoke-direct {v3, p2}, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda5;-><init>(Lcom/android/tools/r8/internal/u20;)V

    .line 82
    invoke-virtual {v1, v0, v2, v3}, Lcom/android/tools/r8/graph/j1;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/B1;Ljava/util/function/Consumer;)Lcom/android/tools/r8/graph/j1;

    move-result-object p2

    .line 88
    :goto_1
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/j1;->R0()Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    const/16 v1, 0x10

    .line 89
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/g;->b(I)V

    .line 90
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/Xc;->b(Lcom/android/tools/r8/graph/j1;)V

    return-void
.end method

.method public final b()Z
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/qu0;->e:Lcom/android/tools/r8/internal/pu0;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    sget-object v1, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda6;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda6;

    .line 3
    invoke-static {v0, v1}, Lcom/android/tools/r8/internal/TI;->a(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final c()Z
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/qu0;->b()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/qu0;->d()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public final d()Z
    .locals 4

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/qu0;->e:Lcom/android/tools/r8/internal/pu0;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    const/4 v2, 0x1

    if-ne v0, v2, :cond_1

    return v2

    .line 8
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/qu0;->d:Ljava/util/ArrayList;

    sget-object v3, Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda7;->INSTANCE:Lcom/android/tools/r8/internal/qu0$$ExternalSyntheticLambda7;

    .line 9
    invoke-static {v0, v3}, Lcom/android/tools/r8/internal/TI;->c(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/U40;)Lcom/android/tools/r8/internal/PI;

    move-result-object v0

    invoke-static {v0}, Lcom/android/tools/r8/internal/TI;->b(Ljava/lang/Iterable;)I

    move-result v0

    if-gt v0, v2, :cond_2

    move v1, v2

    :cond_2
    return v1
.end method
