.class public final synthetic Lcom/android/tools/r8/internal/V2$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiConsumer;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/y;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/V2$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/graph/y;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/V2$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/graph/y;

    check-cast p1, Lcom/android/tools/r8/internal/Y30;

    check-cast p2, Lcom/android/tools/r8/shaking/M;

    invoke-static {v0, p1, p2}, Lcom/android/tools/r8/internal/V2;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Y30;Lcom/android/tools/r8/shaking/M;)V

    return-void
.end method
