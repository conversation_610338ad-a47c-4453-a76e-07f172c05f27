.class public final synthetic Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/w30;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/vt0;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/aA;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/w30;Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/aA;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/internal/w30;

    iput-object p2, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/internal/vt0;

    iput-object p3, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda5;->f$2:Lcom/android/tools/r8/internal/aA;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda5;->f$0:Lcom/android/tools/r8/internal/w30;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda5;->f$1:Lcom/android/tools/r8/internal/vt0;

    iget-object v2, p0, Lcom/android/tools/r8/internal/w30$$ExternalSyntheticLambda5;->f$2:Lcom/android/tools/r8/internal/aA;

    check-cast p1, Lcom/android/tools/r8/internal/rD;

    invoke-virtual {v0, v1, v2, p1}, Lcom/android/tools/r8/internal/w30;->a(Lcom/android/tools/r8/internal/vt0;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/rD;)Z

    move-result p1

    return p1
.end method
