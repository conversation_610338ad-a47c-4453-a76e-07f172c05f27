.class public final Lcom/android/tools/r8/internal/Ue0;
.super Lcom/android/tools/r8/internal/uy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# static fields
.field public static final h:Lcom/android/tools/r8/internal/Qe0;

.field public static final i:Lcom/android/tools/r8/internal/Ue0;

.field public static final j:Lcom/android/tools/r8/internal/Re0;


# instance fields
.field public b:Lcom/android/tools/r8/internal/Df0;

.field public volatile c:Ljava/lang/String;

.field public d:Ljava/util/List;

.field public e:I

.field public f:I

.field public g:B


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Qe0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Qe0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Ue0;->h:Lcom/android/tools/r8/internal/Qe0;

    .line 992
    new-instance v0, Lcom/android/tools/r8/internal/Ue0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Ue0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Ue0;->i:Lcom/android/tools/r8/internal/Ue0;

    .line 1000
    new-instance v0, Lcom/android/tools/r8/internal/Re0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Re0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Ue0;->j:Lcom/android/tools/r8/internal/Re0;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 480
    invoke-direct {p0}, Lcom/android/tools/r8/internal/uy;-><init>()V

    const/4 v0, -0x1

    .line 956
    iput-byte v0, p0, Lcom/android/tools/r8/internal/Ue0;->g:B

    const-string v0, ""

    .line 957
    iput-object v0, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    .line 958
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/Se0;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/uy;-><init>(Lcom/android/tools/r8/internal/dy;)V

    const/4 p1, -0x1

    .line 479
    iput-byte p1, p0, Lcom/android/tools/r8/internal/Ue0;->g:B

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    .locals 7

    .line 959
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Ue0;-><init>()V

    .line 960
    invoke-static {p2}, Lcom/android/tools/r8/internal/qg;->a(Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/qs0;

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    move v3, v1

    :cond_0
    :goto_0
    if-nez v1, :cond_c

    .line 13953
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->s()I

    move-result v4

    if-eqz v4, :cond_a

    const/16 v5, 0xa

    if-eq v4, v5, :cond_8

    const/16 v5, 0x12

    if-eq v4, v5, :cond_7

    const/16 v5, 0x18

    if-eq v4, v5, :cond_5

    const/16 v5, 0x1a

    if-eq v4, v5, :cond_2

    const/16 v5, 0x20

    if-eq v4, v5, :cond_1

    .line 14006
    invoke-virtual {p0, p1, v0, p2, v4}, Lcom/android/tools/r8/internal/uy;->parseUnknownField(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/qs0;Lcom/android/tools/r8/internal/Lu;I)Z

    move-result v4

    if-nez v4, :cond_0

    goto/16 :goto_2

    .line 14007
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->t()I

    move-result v4

    iput v4, p0, Lcom/android/tools/r8/internal/Ue0;->f:I

    goto :goto_0

    .line 14008
    :cond_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->l()I

    move-result v4

    .line 14009
    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ce;->d(I)I

    move-result v4

    .line 14010
    :goto_1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->b()I

    move-result v5

    if-lez v5, :cond_4

    .line 14011
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->f()I

    move-result v5

    if-nez v3, :cond_3

    .line 14013
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    iput-object v6, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    move v3, v2

    .line 14016
    :cond_3
    iget-object v6, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v6, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 14018
    :cond_4
    invoke-virtual {p1, v4}, Lcom/android/tools/r8/internal/ce;->c(I)V

    goto :goto_0

    .line 14019
    :cond_5
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->f()I

    move-result v4

    if-nez v3, :cond_6

    .line 14021
    new-instance v5, Ljava/util/ArrayList;

    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    iput-object v5, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    move v3, v2

    .line 14024
    :cond_6
    iget-object v5, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v5, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 14025
    :cond_7
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->r()Ljava/lang/String;

    move-result-object v4

    .line 14027
    iput-object v4, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    goto :goto_0

    :cond_8
    const/4 v4, 0x0

    .line 14028
    iget-object v5, p0, Lcom/android/tools/r8/internal/Ue0;->b:Lcom/android/tools/r8/internal/Df0;

    if-eqz v5, :cond_9

    .line 14029
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/Df0;->b()Lcom/android/tools/r8/internal/Cf0;

    move-result-object v4

    .line 14030
    :cond_9
    sget-object v5, Lcom/android/tools/r8/internal/Df0;->f:Lcom/android/tools/r8/internal/Bf0;

    .line 14031
    invoke-virtual {p1, v5, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/internal/Df0;

    iput-object v5, p0, Lcom/android/tools/r8/internal/Ue0;->b:Lcom/android/tools/r8/internal/Df0;

    if-eqz v4, :cond_0

    .line 14033
    invoke-virtual {v4, v5}, Lcom/android/tools/r8/internal/Cf0;->a(Lcom/android/tools/r8/internal/Df0;)Lcom/android/tools/r8/internal/Cf0;

    .line 14034
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/Cf0;->b()Lcom/android/tools/r8/internal/Df0;

    move-result-object v4

    iput-object v4, p0, Lcom/android/tools/r8/internal/Ue0;->b:Lcom/android/tools/r8/internal/Df0;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto/16 :goto_0

    :cond_a
    :goto_2
    move v1, v2

    goto/16 :goto_0

    :catchall_0
    move-exception p1

    goto :goto_3

    :catch_0
    move-exception p1

    .line 14085
    :try_start_1
    new-instance p2, Lcom/android/tools/r8/internal/lI;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/lI;-><init>(Ljava/io/IOException;)V

    .line 14086
    iput-object p0, p2, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 14087
    throw p2

    :catch_1
    move-exception p1

    .line 14088
    iput-object p0, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 14089
    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :goto_3
    if-eqz v3, :cond_b

    .line 14095
    iget-object p2, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    .line 14097
    :cond_b
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 14098
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    .line 14099
    throw p1

    :cond_c
    if-eqz v3, :cond_d

    .line 14100
    iget-object p1, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    .line 14102
    :cond_d
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 14103
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/Df0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ue0;->b:Lcom/android/tools/r8/internal/Df0;

    if-nez v0, :cond_0

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/Df0;->e:Lcom/android/tools/r8/internal/Df0;

    :cond_0
    return-object v0
.end method

.method public final b()Lcom/android/tools/r8/internal/Se0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Ue0;->i:Lcom/android/tools/r8/internal/Ue0;

    if-ne p0, v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Se0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Se0;-><init>()V

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/Se0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Se0;-><init>()V

    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/Se0;->a(Lcom/android/tools/r8/internal/Ue0;)Lcom/android/tools/r8/internal/Se0;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 5

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/Ue0;

    if-nez v1, :cond_1

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/J0;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/Ue0;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->b:Lcom/android/tools/r8/internal/Df0;

    const/4 v2, 0x0

    if-eqz v1, :cond_2

    move v3, v0

    goto :goto_0

    :cond_2
    move v3, v2

    .line 6
    :goto_0
    iget-object v4, p1, Lcom/android/tools/r8/internal/Ue0;->b:Lcom/android/tools/r8/internal/Df0;

    if-eqz v4, :cond_3

    move v4, v0

    goto :goto_1

    :cond_3
    move v4, v2

    :goto_1
    if-eq v3, v4, :cond_4

    return v2

    :cond_4
    if-eqz v1, :cond_5

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ue0;->a()Lcom/android/tools/r8/internal/Df0;

    move-result-object v1

    .line 8
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Ue0;->a()Lcom/android/tools/r8/internal/Df0;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/Df0;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    return v2

    .line 9
    :cond_5
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    .line 10
    instance-of v3, v1, Ljava/lang/String;

    if-eqz v3, :cond_6

    goto :goto_2

    .line 13
    :cond_6
    check-cast v1, Lcom/android/tools/r8/internal/Z7;

    .line 15
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v1

    .line 16
    iput-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    .line 17
    :goto_2
    iget-object v3, p1, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    .line 18
    instance-of v4, v3, Ljava/lang/String;

    if-eqz v4, :cond_7

    goto :goto_3

    .line 21
    :cond_7
    check-cast v3, Lcom/android/tools/r8/internal/Z7;

    .line 23
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v3

    .line 24
    iput-object v3, p1, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    .line 25
    :goto_3
    invoke-virtual {v1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_8

    return v2

    .line 26
    :cond_8
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    iget-object v3, p1, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-interface {v1, v3}, Ljava/util/List;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_9

    return v2

    .line 27
    :cond_9
    iget v1, p0, Lcom/android/tools/r8/internal/Ue0;->f:I

    .line 28
    iget v3, p1, Lcom/android/tools/r8/internal/Ue0;->f:I

    if-eq v1, v3, :cond_a

    return v2

    .line 29
    :cond_a
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/vs0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_b

    return v2

    :cond_b
    return v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/Ue0;->i:Lcom/android/tools/r8/internal/Ue0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Ue0;->i:Lcom/android/tools/r8/internal/Ue0;

    return-object v0
.end method

.method public final getSerializedSize()I
    .locals 5

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ue0;->b:Lcom/android/tools/r8/internal/Df0;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ue0;->a()Lcom/android/tools/r8/internal/Df0;

    move-result-object v0

    const/4 v2, 0x1

    .line 8
    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v2

    invoke-static {v0}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/AU;)I

    move-result v0

    add-int/2addr v0, v2

    goto :goto_0

    :cond_1
    move v0, v1

    .line 9
    :goto_0
    iget-object v2, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    invoke-static {v2}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_2

    .line 10
    iget-object v2, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    const/4 v3, 0x2

    invoke-static {v3, v2}, Lcom/android/tools/r8/internal/uy;->computeStringSize(ILjava/lang/Object;)I

    move-result v2

    add-int/2addr v0, v2

    :cond_2
    move v2, v1

    .line 14
    :goto_1
    iget-object v3, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v1, v3, :cond_3

    .line 15
    iget-object v3, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    .line 16
    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    invoke-static {v3}, Lcom/android/tools/r8/internal/je;->a(I)I

    move-result v3

    add-int/2addr v2, v3

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_3
    add-int/2addr v0, v2

    .line 17
    new-instance v1, Lcom/android/tools/r8/internal/EH;

    iget-object v3, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    sget-object v4, Lcom/android/tools/r8/internal/Ue0;->h:Lcom/android/tools/r8/internal/Qe0;

    invoke-direct {v1, v3, v4}, Lcom/android/tools/r8/internal/EH;-><init>(Ljava/util/List;Lcom/android/tools/r8/internal/DH;)V

    .line 18
    invoke-virtual {v1}, Ljava/util/AbstractCollection;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_4

    add-int/lit8 v0, v0, 0x1

    .line 20
    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->c(I)I

    move-result v1

    add-int/2addr v0, v1

    .line 21
    :cond_4
    iput v2, p0, Lcom/android/tools/r8/internal/Ue0;->e:I

    .line 23
    iget v1, p0, Lcom/android/tools/r8/internal/Ue0;->f:I

    if-eqz v1, :cond_5

    const/4 v2, 0x4

    .line 24
    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v2

    .line 25
    invoke-static {v1, v2, v0}, Lcom/android/tools/r8/internal/pg;->a(III)I

    move-result v0

    .line 13526
    :cond_5
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vs0;->getSerializedSize()I

    move-result v1

    add-int/2addr v1, v0

    .line 13527
    iput v1, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    return v1
.end method

.method public final getUnknownFields()Lcom/android/tools/r8/internal/vs0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    return-object v0
.end method

.method public final hashCode()I
    .locals 5

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    if-eqz v0, :cond_0

    return v0

    .line 2
    :cond_0
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->A:Lcom/android/tools/r8/internal/Ok;

    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    add-int/lit16 v0, v0, 0x30b

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->b:Lcom/android/tools/r8/internal/Df0;

    const/16 v2, 0x35

    const/16 v3, 0x25

    if-eqz v1, :cond_1

    const/4 v1, 0x1

    .line 5
    invoke-static {v0, v3, v1, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ue0;->a()Lcom/android/tools/r8/internal/Df0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Df0;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    :cond_1
    const/4 v1, 0x2

    .line 8
    invoke-static {v0, v3, v1, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    .line 9
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    .line 10
    instance-of v4, v1, Ljava/lang/String;

    if-eqz v4, :cond_2

    goto :goto_0

    .line 13
    :cond_2
    check-cast v1, Lcom/android/tools/r8/internal/Z7;

    .line 15
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v1

    .line 16
    iput-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    .line 17
    :goto_0
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    .line 18
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_3

    const/4 v0, 0x3

    .line 19
    invoke-static {v1, v3, v0, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    :cond_3
    const/4 v0, 0x4

    .line 22
    invoke-static {v1, v3, v0, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    .line 23
    iget v1, p0, Lcom/android/tools/r8/internal/Ue0;->f:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1d

    .line 24
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vs0;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    .line 25
    iput v1, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    return v1
.end method

.method public final internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->B:Lcom/android/tools/r8/internal/sy;

    .line 2
    const-class v1, Lcom/android/tools/r8/internal/Ue0;

    const-class v2, Lcom/android/tools/r8/internal/Se0;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 2

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/internal/Ue0;->g:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    if-nez v0, :cond_1

    const/4 v0, 0x0

    return v0

    .line 5
    :cond_1
    iput-byte v1, p0, Lcom/android/tools/r8/internal/Ue0;->g:B

    return v1
.end method

.method public final newBuilderForType()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Ue0;->i:Lcom/android/tools/r8/internal/Ue0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Ue0;->b()Lcom/android/tools/r8/internal/Se0;

    move-result-object v0

    return-object v0
.end method

.method public final newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Se0;

    check-cast p1, Lcom/android/tools/r8/internal/ay;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/Se0;-><init>(Lcom/android/tools/r8/internal/ay;)V

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ue0;->b()Lcom/android/tools/r8/internal/Se0;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ue0;->b()Lcom/android/tools/r8/internal/Se0;

    move-result-object v0

    return-object v0
.end method

.method public final writeTo(Lcom/android/tools/r8/internal/je;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ue0;->getSerializedSize()I

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ue0;->b:Lcom/android/tools/r8/internal/Df0;

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ue0;->a()Lcom/android/tools/r8/internal/Df0;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    invoke-static {v0}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ue0;->c:Ljava/lang/String;

    const/4 v1, 0x2

    invoke-static {p1, v1, v0}, Lcom/android/tools/r8/internal/uy;->writeString(Lcom/android/tools/r8/internal/je;ILjava/lang/Object;)V

    .line 7
    :cond_1
    new-instance v0, Lcom/android/tools/r8/internal/EH;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    sget-object v2, Lcom/android/tools/r8/internal/Ue0;->h:Lcom/android/tools/r8/internal/Qe0;

    invoke-direct {v0, v1, v2}, Lcom/android/tools/r8/internal/EH;-><init>(Ljava/util/List;Lcom/android/tools/r8/internal/DH;)V

    .line 8
    iget-object v0, v0, Lcom/android/tools/r8/internal/EH;->b:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_2

    const/16 v0, 0x1a

    .line 9
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/je;->g(I)V

    .line 10
    iget v0, p0, Lcom/android/tools/r8/internal/Ue0;->e:I

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/je;->g(I)V

    :cond_2
    const/4 v0, 0x0

    .line 12
    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_3

    .line 13
    iget-object v1, p0, Lcom/android/tools/r8/internal/Ue0;->d:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Integer;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    .line 14
    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/je;->f(I)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 15
    :cond_3
    iget v0, p0, Lcom/android/tools/r8/internal/Ue0;->f:I

    if-eqz v0, :cond_4

    const/4 v1, 0x4

    .line 16
    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->e(II)V

    .line 18
    :cond_4
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/vs0;->writeTo(Lcom/android/tools/r8/internal/je;)V

    return-void
.end method
