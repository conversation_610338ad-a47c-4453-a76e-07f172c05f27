.class public final synthetic Lcom/android/tools/r8/internal/V30$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/uD;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/uD;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/V30$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/uD;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/V30$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/uD;

    invoke-interface {v0}, Ljava/util/ListIterator;->hasNext()Z

    move-result v0

    invoke-static {v0}, <PERSON>java/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    return-object v0
.end method
