.class public abstract Lcom/android/tools/r8/internal/u60;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/naming/P;


# static fields
.field public static final h:[B


# instance fields
.field public a:I

.field public b:I

.field public final c:Ljava/util/function/Predicate;

.field public final d:Z

.field public e:Z

.field public f:Z

.field public g:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, "sourceFile"

    .line 1
    invoke-virtual {v0}, Ljava/lang/String;->getBytes()[B

    move-result-object v0

    sput-object v0, Lcom/android/tools/r8/internal/u60;->h:[B

    return-void
.end method

.method public constructor <init>(Ljava/util/function/Predicate;Z)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 2
    iput v0, p0, Lcom/android/tools/r8/internal/u60;->a:I

    .line 3
    iput v0, p0, Lcom/android/tools/r8/internal/u60;->b:I

    .line 22
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/u60;->e:Z

    .line 23
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/u60;->f:Z

    const/16 v0, 0x9

    .line 24
    iput v0, p0, Lcom/android/tools/r8/internal/u60;->g:I

    .line 25
    iput-object p1, p0, Lcom/android/tools/r8/internal/u60;->c:Ljava/util/function/Predicate;

    .line 26
    iput-boolean p2, p0, Lcom/android/tools/r8/internal/u60;->d:Z

    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/String;
    .locals 16

    move-object/from16 v0, p0

    :cond_0
    const/4 v1, 0x0

    .line 1
    iput v1, v0, Lcom/android/tools/r8/internal/u60;->a:I

    .line 2
    iput v1, v0, Lcom/android/tools/r8/internal/u60;->b:I

    const/4 v2, 0x0

    move-object v3, v2

    .line 5
    :cond_1
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/u60;->e()[B

    move-result-object v4

    if-nez v4, :cond_2

    goto :goto_3

    .line 9
    :cond_2
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/u60;->b()Z

    move-result v5

    if-nez v5, :cond_4

    if-eqz v3, :cond_3

    goto :goto_0

    .line 24
    :cond_3
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/u60;->d()I

    move-result v3

    iput v3, v0, Lcom/android/tools/r8/internal/u60;->a:I

    .line 25
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/u60;->c()I

    move-result v3

    iput v3, v0, Lcom/android/tools/r8/internal/u60;->b:I

    move-object v3, v4

    goto :goto_2

    .line 26
    :cond_4
    :goto_0
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/u60;->c()I

    move-result v5

    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/u60;->d()I

    move-result v6

    sub-int/2addr v5, v6

    if-nez v3, :cond_5

    move v6, v1

    goto :goto_1

    .line 27
    :cond_5
    array-length v6, v3

    :goto_1
    add-int v7, v5, v6

    .line 28
    new-array v8, v7, [B

    if-eqz v3, :cond_6

    .line 30
    array-length v9, v3

    invoke-static {v3, v1, v8, v1, v9}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 33
    :cond_6
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/u60;->d()I

    move-result v3

    .line 34
    invoke-static {v4, v3, v8, v6, v5}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 37
    iput v7, v0, Lcom/android/tools/r8/internal/u60;->b:I

    move-object v3, v8

    .line 43
    :goto_2
    invoke-virtual/range {p0 .. p0}, Lcom/android/tools/r8/internal/u60;->b()Z

    move-result v4

    if-nez v4, :cond_1

    :goto_3
    if-nez v3, :cond_7

    return-object v2

    .line 44
    :cond_7
    iget-object v2, v0, Lcom/android/tools/r8/internal/u60;->c:Ljava/util/function/Predicate;

    if-nez v2, :cond_8

    .line 45
    new-instance v1, Ljava/lang/String;

    iget v2, v0, Lcom/android/tools/r8/internal/u60;->a:I

    iget v4, v0, Lcom/android/tools/r8/internal/u60;->b:I

    sub-int/2addr v4, v2

    sget-object v5, Ljava/nio/charset/StandardCharsets;->UTF_8:Ljava/nio/charset/Charset;

    invoke-direct {v1, v3, v2, v4, v5}, Ljava/lang/String;-><init>([BIILjava/nio/charset/Charset;)V

    return-object v1

    .line 47
    :cond_8
    iget v2, v0, Lcom/android/tools/r8/internal/u60;->a:I

    iget v4, v0, Lcom/android/tools/r8/internal/u60;->b:I

    const/4 v5, 0x4

    const/4 v6, 0x2

    const/16 v7, 0x8

    const/16 v8, 0x9

    const/4 v9, 0x1

    move v10, v9

    :goto_4
    const/4 v11, 0x6

    if-eq v10, v8, :cond_35

    if-eq v10, v11, :cond_35

    if-ne v10, v7, :cond_9

    goto/16 :goto_1f

    .line 48
    :cond_9
    sget-boolean v12, Lcom/android/tools/r8/internal/r60;->a:Z

    if-nez v12, :cond_b

    if-eq v10, v8, :cond_a

    goto :goto_5

    :cond_a
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 49
    :cond_b
    :goto_5
    invoke-static {v10}, Lcom/android/tools/r8/c;->b(I)I

    move-result v13

    if-eqz v13, :cond_31

    const/16 v15, 0x23

    const/4 v1, 0x3

    const/16 v14, 0x20

    if-eq v13, v9, :cond_29

    if-eq v13, v6, :cond_21

    const/16 v14, 0x3a

    if-eq v13, v1, :cond_1d

    if-eq v13, v5, :cond_18

    if-eq v13, v11, :cond_e

    if-nez v12, :cond_d

    if-eq v10, v8, :cond_d

    if-eq v10, v11, :cond_d

    if-ne v10, v7, :cond_c

    goto :goto_6

    .line 91
    :cond_c
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 92
    :cond_d
    :goto_6
    new-instance v1, Lcom/android/tools/r8/internal/Os0;

    const-string v2, "Should not compute next state on terminal state"

    invoke-direct {v1, v2}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_e
    const/16 v1, 0x7b

    :goto_7
    if-ge v2, v4, :cond_34

    add-int/lit8 v10, v2, 0x1

    .line 93
    aget-byte v2, v3, v2

    if-ne v2, v1, :cond_17

    :goto_8
    if-ge v10, v4, :cond_16

    add-int/lit8 v1, v10, 0x1

    .line 94
    aget-byte v2, v3, v10

    if-ne v2, v14, :cond_15

    add-int/lit8 v10, v10, 0x2

    .line 95
    aget-byte v1, v3, v1

    const/16 v2, 0x22

    const/16 v11, 0x27

    if-eq v1, v11, :cond_f

    if-ne v1, v2, :cond_16

    :cond_f
    sub-int v1, v4, v10

    .line 96
    sget-object v12, Lcom/android/tools/r8/internal/u60;->h:[B

    array-length v13, v12

    if-ge v1, v13, :cond_10

    goto :goto_b

    .line 99
    :cond_10
    array-length v1, v12

    add-int/2addr v1, v10

    const/4 v12, 0x0

    :goto_9
    if-ge v10, v1, :cond_12

    .line 102
    sget-object v13, Lcom/android/tools/r8/internal/u60;->h:[B

    add-int/lit8 v14, v12, 0x1

    aget-byte v12, v13, v12

    aget-byte v13, v3, v10

    if-eq v12, v13, :cond_11

    goto :goto_b

    :cond_11
    add-int/lit8 v10, v10, 0x1

    move v12, v14

    goto :goto_9

    :cond_12
    add-int/lit8 v1, v10, 0x1

    .line 103
    aget-byte v10, v3, v10

    if-eq v10, v11, :cond_14

    if-ne v10, v2, :cond_13

    goto :goto_a

    :cond_13
    move v10, v1

    goto :goto_b

    :cond_14
    :goto_a
    move v2, v1

    move v10, v7

    goto/16 :goto_1e

    :cond_15
    move v10, v1

    goto :goto_8

    :cond_16
    :goto_b
    move v2, v10

    goto/16 :goto_1d

    :cond_17
    move v2, v10

    goto :goto_7

    :cond_18
    add-int/lit8 v1, v2, 0x1

    .line 104
    aget-byte v2, v3, v2

    if-ne v2, v14, :cond_19

    move v2, v9

    goto :goto_c

    :cond_19
    const/4 v2, 0x0

    :goto_c
    if-nez v2, :cond_1a

    move v2, v1

    goto/16 :goto_1d

    :cond_1a
    move v2, v1

    :goto_d
    if-ge v2, v4, :cond_1c

    .line 105
    aget-byte v1, v3, v2

    invoke-static {v1}, Ljava/lang/Character;->isWhitespace(I)Z

    move-result v1

    if-nez v1, :cond_1b

    .line 106
    aget-byte v1, v3, v2

    if-ne v1, v15, :cond_34

    goto :goto_e

    :cond_1b
    add-int/lit8 v2, v2, 0x1

    goto :goto_d

    :cond_1c
    :goto_e
    move v10, v11

    goto/16 :goto_1e

    :cond_1d
    move v1, v2

    :goto_f
    if-ge v1, v4, :cond_20

    .line 107
    aget-byte v10, v3, v1

    if-ne v10, v14, :cond_1e

    sub-int v14, v1, v2

    goto :goto_11

    .line 111
    :cond_1e
    invoke-static {v10}, Ljava/lang/Character;->isWhitespace(I)Z

    move-result v10

    if-eqz v10, :cond_1f

    goto :goto_10

    :cond_1f
    add-int/lit8 v1, v1, 0x1

    goto :goto_f

    :cond_20
    :goto_10
    const/4 v14, -0x1

    :goto_11
    if-lez v14, :cond_30

    const/4 v2, 0x5

    move v10, v2

    move v2, v1

    goto/16 :goto_1e

    :cond_21
    add-int/lit8 v1, v2, 0x1

    .line 112
    aget-byte v2, v3, v2

    if-ne v2, v14, :cond_22

    move v2, v9

    goto :goto_12

    :cond_22
    const/4 v2, 0x0

    :goto_12
    if-eqz v2, :cond_27

    const/16 v2, 0x2d

    add-int/lit8 v10, v1, 0x1

    .line 113
    aget-byte v1, v3, v1

    if-ne v1, v2, :cond_23

    move v1, v9

    goto :goto_13

    :cond_23
    const/4 v1, 0x0

    :goto_13
    if-eqz v1, :cond_28

    const/16 v1, 0x3e

    add-int/lit8 v2, v10, 0x1

    aget-byte v10, v3, v10

    if-ne v10, v1, :cond_24

    move v1, v9

    goto :goto_14

    :cond_24
    const/4 v1, 0x0

    :goto_14
    if-eqz v1, :cond_26

    add-int/lit8 v10, v2, 0x1

    .line 114
    aget-byte v1, v3, v2

    if-ne v1, v14, :cond_25

    move v1, v9

    goto :goto_15

    :cond_25
    const/4 v1, 0x0

    :goto_15
    if-eqz v1, :cond_28

    move v1, v9

    move v2, v10

    goto :goto_16

    :cond_26
    move v1, v2

    :cond_27
    move v10, v1

    :cond_28
    move v2, v10

    const/4 v1, 0x0

    :goto_16
    if-eqz v1, :cond_34

    move v10, v5

    goto :goto_1e

    .line 115
    :cond_29
    aget-byte v10, v3, v2

    if-ne v10, v15, :cond_2a

    move v10, v9

    goto :goto_17

    :cond_2a
    const/4 v10, 0x0

    :goto_17
    if-eqz v10, :cond_2b

    const/4 v10, 0x7

    goto :goto_1e

    :cond_2b
    move v10, v2

    :goto_18
    if-ge v10, v4, :cond_2e

    .line 116
    aget-byte v11, v3, v10

    if-ne v11, v14, :cond_2c

    sub-int v14, v10, v2

    goto :goto_1a

    .line 120
    :cond_2c
    invoke-static {v11}, Ljava/lang/Character;->isWhitespace(I)Z

    move-result v11

    if-eqz v11, :cond_2d

    goto :goto_19

    :cond_2d
    add-int/lit8 v10, v10, 0x1

    goto :goto_18

    :cond_2e
    :goto_19
    const/4 v14, -0x1

    :goto_1a
    if-lez v14, :cond_2f

    move v2, v10

    move v10, v1

    goto :goto_1e

    :cond_2f
    move v1, v10

    :cond_30
    move v2, v1

    goto :goto_1d

    :cond_31
    :goto_1b
    if-ge v2, v4, :cond_33

    .line 121
    aget-byte v1, v3, v2

    invoke-static {v1}, Ljava/lang/Character;->isWhitespace(I)Z

    move-result v1

    if-nez v1, :cond_32

    move v1, v9

    goto :goto_1c

    :cond_32
    add-int/lit8 v2, v2, 0x1

    goto :goto_1b

    :cond_33
    const/4 v1, 0x0

    :goto_1c
    if-eqz v1, :cond_34

    move v10, v6

    goto :goto_1e

    :cond_34
    :goto_1d
    move v10, v8

    :goto_1e
    const/4 v1, 0x0

    goto/16 :goto_4

    .line 122
    :cond_35
    :goto_1f
    iput v10, v0, Lcom/android/tools/r8/internal/u60;->g:I

    if-ne v10, v11, :cond_37

    .line 124
    iput-boolean v9, v0, Lcom/android/tools/r8/internal/u60;->f:Z

    .line 125
    new-instance v1, Ljava/lang/String;

    iget v2, v0, Lcom/android/tools/r8/internal/u60;->a:I

    iget v4, v0, Lcom/android/tools/r8/internal/u60;->b:I

    sub-int/2addr v4, v2

    sget-object v5, Ljava/nio/charset/StandardCharsets;->UTF_8:Ljava/nio/charset/Charset;

    invoke-direct {v1, v3, v2, v4, v5}, Ljava/lang/String;-><init>([BIILjava/nio/charset/Charset;)V

    const-string v2, ">"

    .line 126
    invoke-virtual {v1, v2}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v2

    add-int/2addr v2, v6

    .line 127
    invoke-static {v1, v9, v2}, Lcom/android/tools/r8/a;->a(Ljava/lang/String;II)Ljava/lang/String;

    move-result-object v2

    .line 367
    iget-object v3, v0, Lcom/android/tools/r8/internal/u60;->c:Ljava/util/function/Predicate;

    invoke-interface {v3, v2}, Ljava/util/function/Predicate;->test(Ljava/lang/Object;)Z

    move-result v2

    iput-boolean v2, v0, Lcom/android/tools/r8/internal/u60;->e:Z

    if-nez v2, :cond_36

    .line 368
    iget-boolean v2, v0, Lcom/android/tools/r8/internal/u60;->d:Z

    if-eqz v2, :cond_0

    :cond_36
    return-object v1

    :cond_37
    if-ne v10, v7, :cond_38

    .line 371
    iget-boolean v1, v0, Lcom/android/tools/r8/internal/u60;->d:Z

    if-eqz v1, :cond_38

    .line 372
    new-instance v1, Ljava/lang/String;

    iget v2, v0, Lcom/android/tools/r8/internal/u60;->a:I

    iget v4, v0, Lcom/android/tools/r8/internal/u60;->b:I

    sub-int/2addr v4, v2

    sget-object v5, Ljava/nio/charset/StandardCharsets;->UTF_8:Ljava/nio/charset/Charset;

    invoke-direct {v1, v3, v2, v4, v5}, Ljava/lang/String;-><init>([BIILjava/nio/charset/Charset;)V

    return-object v1

    .line 373
    :cond_38
    iget-boolean v1, v0, Lcom/android/tools/r8/internal/u60;->e:Z

    if-nez v1, :cond_39

    iget-boolean v1, v0, Lcom/android/tools/r8/internal/u60;->f:Z

    if-nez v1, :cond_0

    iget-boolean v1, v0, Lcom/android/tools/r8/internal/u60;->d:Z

    if-eqz v1, :cond_0

    .line 374
    :cond_39
    new-instance v1, Ljava/lang/String;

    iget v2, v0, Lcom/android/tools/r8/internal/u60;->a:I

    iget v4, v0, Lcom/android/tools/r8/internal/u60;->b:I

    sub-int/2addr v4, v2

    sget-object v5, Ljava/nio/charset/StandardCharsets;->UTF_8:Ljava/nio/charset/Charset;

    invoke-direct {v1, v3, v2, v4, v5}, Ljava/lang/String;-><init>([BIILjava/nio/charset/Charset;)V

    return-object v1
.end method

.method public abstract b()Z
.end method

.method public abstract c()I
.end method

.method public abstract d()I
.end method

.method public abstract e()[B
.end method
