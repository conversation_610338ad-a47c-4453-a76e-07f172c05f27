.class public final Lcom/android/tools/r8/internal/pj;
.super Lcom/android/tools/r8/internal/uy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# static fields
.field public static final g:Lcom/android/tools/r8/internal/pj;

.field public static final h:Lcom/android/tools/r8/internal/nj;


# instance fields
.field public b:I

.field public c:I

.field public d:I

.field public e:Lcom/android/tools/r8/internal/Lj;

.field public f:B


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/pj;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/pj;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/pj;->g:Lcom/android/tools/r8/internal/pj;

    .line 9
    new-instance v0, Lcom/android/tools/r8/internal/nj;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/nj;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/pj;->h:Lcom/android/tools/r8/internal/nj;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 174
    invoke-direct {p0}, Lcom/android/tools/r8/internal/uy;-><init>()V

    const/4 v0, -0x1

    .line 344
    iput-byte v0, p0, Lcom/android/tools/r8/internal/pj;->f:B

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    .locals 5

    .line 345
    invoke-direct {p0}, Lcom/android/tools/r8/internal/uy;-><init>()V

    const/4 v0, -0x1

    .line 515
    iput-byte v0, p0, Lcom/android/tools/r8/internal/pj;->f:B

    .line 516
    invoke-static {p2}, Lcom/android/tools/r8/internal/qg;->a(Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/qs0;

    move-result-object v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    :cond_0
    :goto_0
    if-nez v2, :cond_7

    .line 517
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->s()I

    move-result v3

    if-eqz v3, :cond_6

    const/16 v4, 0x8

    if-eq v3, v4, :cond_5

    const/16 v4, 0x10

    if-eq v3, v4, :cond_4

    const/16 v4, 0x1a

    if-eq v3, v4, :cond_1

    .line 546
    invoke-virtual {p0, p1, v0, p2, v3}, Lcom/android/tools/r8/internal/uy;->parseUnknownField(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/qs0;Lcom/android/tools/r8/internal/Lu;I)Z

    move-result v3

    if-nez v3, :cond_0

    goto :goto_1

    :cond_1
    const/4 v3, 0x0

    .line 547
    iget v4, p0, Lcom/android/tools/r8/internal/pj;->b:I

    and-int/lit8 v4, v4, 0x4

    if-eqz v4, :cond_2

    .line 548
    iget-object v3, p0, Lcom/android/tools/r8/internal/pj;->e:Lcom/android/tools/r8/internal/Lj;

    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Lj;->a()Lcom/android/tools/r8/internal/Kj;

    move-result-object v3

    .line 550
    :cond_2
    sget-object v4, Lcom/android/tools/r8/internal/Lj;->f:Lcom/android/tools/r8/internal/Jj;

    invoke-virtual {p1, v4, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/Lj;

    iput-object v4, p0, Lcom/android/tools/r8/internal/pj;->e:Lcom/android/tools/r8/internal/Lj;

    if-eqz v3, :cond_3

    .line 552
    invoke-virtual {v3, v4}, Lcom/android/tools/r8/internal/Kj;->a(Lcom/android/tools/r8/internal/Lj;)Lcom/android/tools/r8/internal/Kj;

    .line 553
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Kj;->d()Lcom/android/tools/r8/internal/Lj;

    move-result-object v3

    iput-object v3, p0, Lcom/android/tools/r8/internal/pj;->e:Lcom/android/tools/r8/internal/Lj;

    .line 555
    :cond_3
    iget v3, p0, Lcom/android/tools/r8/internal/pj;->b:I

    or-int/lit8 v3, v3, 0x4

    iput v3, p0, Lcom/android/tools/r8/internal/pj;->b:I

    goto :goto_0

    .line 556
    :cond_4
    iget v3, p0, Lcom/android/tools/r8/internal/pj;->b:I

    or-int/lit8 v3, v3, 0x2

    iput v3, p0, Lcom/android/tools/r8/internal/pj;->b:I

    .line 557
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->j()I

    move-result v3

    iput v3, p0, Lcom/android/tools/r8/internal/pj;->d:I

    goto :goto_0

    .line 558
    :cond_5
    iget v3, p0, Lcom/android/tools/r8/internal/pj;->b:I

    or-int/2addr v3, v1

    iput v3, p0, Lcom/android/tools/r8/internal/pj;->b:I

    .line 559
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->j()I

    move-result v3

    iput v3, p0, Lcom/android/tools/r8/internal/pj;->c:I
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :cond_6
    :goto_1
    move v2, v1

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :catch_0
    move-exception p1

    .line 592
    :try_start_1
    new-instance p2, Lcom/android/tools/r8/internal/lI;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/lI;-><init>(Ljava/io/IOException;)V

    .line 593
    iput-object p0, p2, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 594
    throw p2

    :catch_1
    move-exception p1

    .line 595
    iput-object p0, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 596
    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 601
    :goto_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 602
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    .line 603
    throw p1

    .line 604
    :cond_7
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 605
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/oj;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/uy;-><init>(Lcom/android/tools/r8/internal/dy;)V

    const/4 p1, -0x1

    .line 173
    iput-byte p1, p0, Lcom/android/tools/r8/internal/pj;->f:B

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/Lj;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/pj;->e:Lcom/android/tools/r8/internal/Lj;

    if-nez v0, :cond_0

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/Lj;->e:Lcom/android/tools/r8/internal/Lj;

    :cond_0
    return-object v0
.end method

.method public final b()Z
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/pj;->b:I

    and-int/lit8 v0, v0, 0x4

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final c()Lcom/android/tools/r8/internal/oj;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/pj;->g:Lcom/android/tools/r8/internal/pj;

    if-ne p0, v0, :cond_0

    new-instance v0, Lcom/android/tools/r8/internal/oj;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/oj;-><init>()V

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/oj;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/oj;-><init>()V

    .line 2
    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/oj;->a(Lcom/android/tools/r8/internal/pj;)Lcom/android/tools/r8/internal/oj;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 7

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/pj;

    if-nez v1, :cond_1

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/J0;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/pj;

    .line 5
    iget v1, p0, Lcom/android/tools/r8/internal/pj;->b:I

    and-int/lit8 v2, v1, 0x1

    const/4 v3, 0x0

    if-eqz v2, :cond_2

    move v4, v0

    goto :goto_0

    :cond_2
    move v4, v3

    .line 6
    :goto_0
    iget v5, p1, Lcom/android/tools/r8/internal/pj;->b:I

    and-int/lit8 v6, v5, 0x1

    if-eqz v6, :cond_3

    move v6, v0

    goto :goto_1

    :cond_3
    move v6, v3

    :goto_1
    if-eq v4, v6, :cond_4

    return v3

    :cond_4
    if-eqz v2, :cond_5

    .line 7
    iget v2, p0, Lcom/android/tools/r8/internal/pj;->c:I

    .line 8
    iget v4, p1, Lcom/android/tools/r8/internal/pj;->c:I

    if-eq v2, v4, :cond_5

    return v3

    :cond_5
    and-int/lit8 v1, v1, 0x2

    if-eqz v1, :cond_6

    move v2, v0

    goto :goto_2

    :cond_6
    move v2, v3

    :goto_2
    and-int/lit8 v4, v5, 0x2

    if-eqz v4, :cond_7

    move v4, v0

    goto :goto_3

    :cond_7
    move v4, v3

    :goto_3
    if-eq v2, v4, :cond_8

    return v3

    :cond_8
    if-eqz v1, :cond_9

    .line 9
    iget v1, p0, Lcom/android/tools/r8/internal/pj;->d:I

    .line 10
    iget v2, p1, Lcom/android/tools/r8/internal/pj;->d:I

    if-eq v1, v2, :cond_9

    return v3

    .line 11
    :cond_9
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->b()Z

    move-result v1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/pj;->b()Z

    move-result v2

    if-eq v1, v2, :cond_a

    return v3

    .line 12
    :cond_a
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->b()Z

    move-result v1

    if-eqz v1, :cond_b

    .line 13
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->a()Lcom/android/tools/r8/internal/Lj;

    move-result-object v1

    .line 14
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/pj;->a()Lcom/android/tools/r8/internal/Lj;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/Lj;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_b

    return v3

    .line 16
    :cond_b
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/vs0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_c

    return v3

    :cond_c
    return v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/pj;->g:Lcom/android/tools/r8/internal/pj;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/pj;->g:Lcom/android/tools/r8/internal/pj;

    return-object v0
.end method

.method public final getSerializedSize()I
    .locals 3

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    :cond_0
    const/4 v0, 0x0

    .line 5
    iget v1, p0, Lcom/android/tools/r8/internal/pj;->b:I

    const/4 v2, 0x1

    and-int/2addr v1, v2

    if-eqz v1, :cond_1

    .line 6
    iget v0, p0, Lcom/android/tools/r8/internal/pj;->c:I

    .line 7
    invoke-static {v2, v0}, Lcom/android/tools/r8/internal/je;->a(II)I

    move-result v0

    .line 9
    :cond_1
    iget v1, p0, Lcom/android/tools/r8/internal/pj;->b:I

    const/4 v2, 0x2

    and-int/2addr v1, v2

    if-eqz v1, :cond_2

    .line 10
    iget v1, p0, Lcom/android/tools/r8/internal/pj;->d:I

    .line 11
    invoke-static {v2, v1}, Lcom/android/tools/r8/internal/je;->a(II)I

    move-result v1

    add-int/2addr v0, v1

    .line 13
    :cond_2
    iget v1, p0, Lcom/android/tools/r8/internal/pj;->b:I

    and-int/lit8 v1, v1, 0x4

    if-eqz v1, :cond_3

    const/4 v1, 0x3

    .line 15
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->a()Lcom/android/tools/r8/internal/Lj;

    move-result-object v2

    .line 16
    invoke-static {v1}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v1

    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/AU;)I

    move-result v2

    add-int/2addr v2, v1

    add-int/2addr v0, v2

    .line 17
    :cond_3
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vs0;->getSerializedSize()I

    move-result v1

    add-int/2addr v1, v0

    .line 18
    iput v1, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    return v1
.end method

.method public final getUnknownFields()Lcom/android/tools/r8/internal/vs0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    return-object v0
.end method

.method public final hashCode()I
    .locals 5

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    if-eqz v0, :cond_0

    return v0

    .line 2
    :cond_0
    sget-object v0, Lcom/android/tools/r8/internal/Lk;->e:Lcom/android/tools/r8/internal/Ok;

    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    add-int/lit16 v0, v0, 0x30b

    .line 4
    iget v1, p0, Lcom/android/tools/r8/internal/pj;->b:I

    and-int/lit8 v2, v1, 0x1

    const/16 v3, 0x35

    const/16 v4, 0x25

    if-eqz v2, :cond_1

    const/4 v2, 0x1

    .line 5
    invoke-static {v0, v4, v2, v3}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    .line 6
    iget v2, p0, Lcom/android/tools/r8/internal/pj;->c:I

    add-int/2addr v0, v2

    :cond_1
    const/4 v2, 0x2

    and-int/2addr v1, v2

    if-eqz v1, :cond_2

    .line 7
    invoke-static {v0, v4, v2, v3}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    .line 8
    iget v1, p0, Lcom/android/tools/r8/internal/pj;->d:I

    add-int/2addr v0, v1

    .line 9
    :cond_2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->b()Z

    move-result v1

    if-eqz v1, :cond_3

    const/4 v1, 0x3

    .line 11
    invoke-static {v0, v4, v1, v3}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->a()Lcom/android/tools/r8/internal/Lj;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Lj;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    :cond_3
    mul-int/lit8 v0, v0, 0x1d

    .line 13
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vs0;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    .line 14
    iput v1, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    return v1
.end method

.method public final internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Lk;->f:Lcom/android/tools/r8/internal/sy;

    .line 2
    const-class v1, Lcom/android/tools/r8/internal/pj;

    const-class v2, Lcom/android/tools/r8/internal/oj;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 3

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/internal/pj;->f:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    const/4 v2, 0x0

    if-nez v0, :cond_1

    return v2

    .line 5
    :cond_1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->b()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->a()Lcom/android/tools/r8/internal/Lj;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Lj;->isInitialized()Z

    move-result v0

    if-nez v0, :cond_2

    .line 7
    iput-byte v2, p0, Lcom/android/tools/r8/internal/pj;->f:B

    return v2

    .line 11
    :cond_2
    iput-byte v1, p0, Lcom/android/tools/r8/internal/pj;->f:B

    return v1
.end method

.method public final newBuilderForType()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/pj;->g:Lcom/android/tools/r8/internal/pj;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/pj;->c()Lcom/android/tools/r8/internal/oj;

    move-result-object v0

    return-object v0
.end method

.method public final newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/oj;

    check-cast p1, Lcom/android/tools/r8/internal/ay;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/oj;-><init>(Lcom/android/tools/r8/internal/ay;)V

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->c()Lcom/android/tools/r8/internal/oj;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->c()Lcom/android/tools/r8/internal/oj;

    move-result-object v0

    return-object v0
.end method

.method public final writeTo(Lcom/android/tools/r8/internal/je;)V
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/pj;->b:I

    const/4 v1, 0x1

    and-int/2addr v0, v1

    if-eqz v0, :cond_0

    .line 2
    iget v0, p0, Lcom/android/tools/r8/internal/pj;->c:I

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->c(II)V

    .line 4
    :cond_0
    iget v0, p0, Lcom/android/tools/r8/internal/pj;->b:I

    const/4 v1, 0x2

    and-int/2addr v0, v1

    if-eqz v0, :cond_1

    .line 5
    iget v0, p0, Lcom/android/tools/r8/internal/pj;->d:I

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->c(II)V

    .line 7
    :cond_1
    iget v0, p0, Lcom/android/tools/r8/internal/pj;->b:I

    and-int/lit8 v0, v0, 0x4

    if-eqz v0, :cond_2

    .line 8
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/pj;->a()Lcom/android/tools/r8/internal/Lj;

    move-result-object v0

    const/4 v1, 0x3

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    .line 10
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/vs0;->writeTo(Lcom/android/tools/r8/internal/je;)V

    return-void
.end method
