.class public final Lcom/android/tools/r8/internal/BI;
.super Lcom/android/tools/r8/graph/n0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)V
    .locals 1

    .line 1
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    invoke-direct {p0, p1, p2, v0}, Lcom/android/tools/r8/graph/n0;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Ljava/lang/<PERSON>an;)V

    .line 2
    sget-boolean p1, Lcom/android/tools/r8/internal/BI;->f:Z

    if-nez p1, :cond_1

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->isInterface()Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    return-void
.end method


# virtual methods
.method public final d(Lcom/android/tools/r8/graph/x2;)V
    .locals 0

    .line 1
    sget-boolean p1, Lcom/android/tools/r8/internal/BI;->f:Z

    if-eqz p1, :cond_0

    return-void

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1
.end method

.method public final g(Lcom/android/tools/r8/graph/x2;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    .line 2
    check-cast v0, Lcom/android/tools/r8/graph/D5;

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->q()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    .line 7
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/E0;->c(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/j1;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 8
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/j1;->M0()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 9
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 10
    iput-object p1, p0, Lcom/android/tools/r8/graph/c6;->e:Ljava/lang/Boolean;

    .line 11
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b6;->a()V

    :cond_1
    :goto_0
    return-void
.end method
