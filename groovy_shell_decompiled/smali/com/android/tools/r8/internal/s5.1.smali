.class public final Lcom/android/tools/r8/internal/s5;
.super Lcom/android/tools/r8/internal/r5;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/t5;Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/r5;-><init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/t5;Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/synthesis/Q;)Lcom/android/tools/r8/synthesis/Q$b;
    .locals 0

    .line 1
    iget-object p1, p1, Lcom/android/tools/r8/synthesis/Q;->B:Lcom/android/tools/r8/synthesis/Q$b;

    return-object p1
.end method
