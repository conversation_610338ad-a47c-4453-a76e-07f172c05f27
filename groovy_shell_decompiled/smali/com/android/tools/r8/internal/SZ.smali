.class public abstract Lcom/android/tools/r8/internal/SZ;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static synthetic a(Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/H9;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/O9;->U()Lcom/android/tools/r8/graph/x2;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/x2;->C0()Lcom/android/tools/r8/graph/J2;

    move-result-object p0

    invoke-static {p0}, Lcom/android/tools/r8/internal/UZ;->a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/UZ;

    move-result-object p0

    .line 3
    new-instance p1, Lcom/android/tools/r8/internal/F8;

    sget-object v0, Lcom/android/tools/r8/internal/F8$a;->b:Lcom/android/tools/r8/internal/F8$a;

    invoke-direct {p1, v0, p0}, Lcom/android/tools/r8/internal/F8;-><init>(Lcom/android/tools/r8/internal/F8$a;Lcom/android/tools/r8/internal/UZ;)V

    return-object p1
.end method

.method public static a()Lcom/android/tools/r8/internal/o5;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/SZ$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/SZ$$ExternalSyntheticLambda0;

    return-object v0
.end method

.method public static synthetic b(Lcom/android/tools/r8/internal/O9;Lcom/android/tools/r8/graph/B1;)Lcom/android/tools/r8/internal/H9;
    .locals 3

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/O9;->U()Lcom/android/tools/r8/graph/x2;

    move-result-object p0

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/O9;

    iget-object v1, p1, Lcom/android/tools/r8/graph/B1;->m2:Lcom/android/tools/r8/graph/J2;

    iget-object v2, p0, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object p0, p0, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    .line 5
    invoke-virtual {p1, v1, v2, p0}, Lcom/android/tools/r8/graph/B1;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/F2;Lcom/android/tools/r8/graph/I2;)Lcom/android/tools/r8/graph/x2;

    move-result-object p0

    const/16 p1, 0xb8

    const/4 v1, 0x0

    invoke-direct {v0, p1, p0, v1}, Lcom/android/tools/r8/internal/O9;-><init>(ILcom/android/tools/r8/graph/x2;Z)V

    return-object v0
.end method

.method public static b()Lcom/android/tools/r8/internal/o5;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/SZ$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/SZ$$ExternalSyntheticLambda1;

    return-object v0
.end method
