.class public final Lcom/android/tools/r8/internal/ry;
.super Lcom/android/tools/r8/internal/py;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final f:Ljava/lang/reflect/Method;

.field public final g:Ljava/lang/reflect/Method;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/al;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V
    .locals 3

    .line 1
    invoke-direct/range {p0 .. p5}, Lcom/android/tools/r8/internal/py;-><init>(Lcom/android/tools/r8/internal/al;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V

    const-string p1, "get"

    const-string p5, "Bytes"

    .line 2
    invoke-static {p1, p2, p5}, Lcom/android/tools/r8/internal/ac0;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const/4 v1, 0x0

    new-array v2, v1, [Ljava/lang/Class;

    .line 2920
    invoke-static {p3, v0, v2}, Lcom/android/tools/r8/internal/uy;->access$1000(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object p3

    iput-object p3, p0, Lcom/android/tools/r8/internal/ry;->f:Ljava/lang/reflect/Method;

    .line 2921
    invoke-static {p1, p2, p5}, Lcom/android/tools/r8/internal/ac0;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    new-array p3, v1, [Ljava/lang/Class;

    .line 5841
    invoke-static {p4, p1, p3}, Lcom/android/tools/r8/internal/uy;->access$1000(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    const-string p1, "set"

    .line 5842
    invoke-static {p1, p2, p5}, Lcom/android/tools/r8/internal/ac0;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const/4 p2, 0x1

    new-array p2, p2, [Ljava/lang/Class;

    .line 8764
    const-class p3, Lcom/android/tools/r8/internal/Z7;

    aput-object p3, p2, v1

    invoke-static {p4, p1, p2}, Lcom/android/tools/r8/internal/uy;->access$1000(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/ry;->g:Ljava/lang/reflect/Method;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/uy;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/ry;->f:Ljava/lang/reflect/Method;

    const/4 v1, 0x0

    new-array v1, v1, [Ljava/lang/Object;

    invoke-static {v0, p1, v1}, Lcom/android/tools/r8/internal/uy;->access$1100(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/dy;Ljava/lang/Object;)V
    .locals 3

    .line 2
    instance-of v0, p2, Lcom/android/tools/r8/internal/Z7;

    if-eqz v0, :cond_0

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/ry;->g:Ljava/lang/reflect/Method;

    const/4 v1, 0x1

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p2, v1, v2

    invoke-static {v0, p1, v1}, Lcom/android/tools/r8/internal/uy;->access$1100(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 5
    :cond_0
    invoke-super {p0, p1, p2}, Lcom/android/tools/r8/internal/py;->a(Lcom/android/tools/r8/internal/dy;Ljava/lang/Object;)V

    :goto_0
    return-void
.end method
