.class public final synthetic Lcom/android/tools/r8/internal/w50$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/pp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/w50;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/t50;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/w50;Lcom/android/tools/r8/internal/t50;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/w50$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/w50;

    iput-object p2, p0, Lcom/android/tools/r8/internal/w50$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/t50;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/w50$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/w50;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w50$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/t50;

    check-cast p1, Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/w50;->a(Lcom/android/tools/r8/internal/t50;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/internal/Gp0;

    move-result-object p1

    return-object p1
.end method
