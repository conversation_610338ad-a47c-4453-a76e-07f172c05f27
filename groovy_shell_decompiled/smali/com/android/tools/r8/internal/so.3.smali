.class abstract Lcom/android/tools/r8/internal/so;
.super Lcom/android/tools/r8/internal/Hm;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public f:I


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 3
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Hm;-><init>()V

    .line 4
    iput p1, p0, Lcom/android/tools/r8/internal/so;->f:I

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/Zo;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Hm;-><init>(Lcom/android/tools/r8/internal/Zo;)V

    .line 2
    invoke-static {p1}, Lcom/android/tools/r8/internal/Yo;->a(Lcom/android/tools/r8/internal/Zo;)I

    move-result p1

    iput p1, p0, Lcom/android/tools/r8/internal/so;->f:I

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Yo;Lcom/android/tools/r8/internal/Qe;)I
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/so;->f:I

    check-cast p1, Lcom/android/tools/r8/internal/so;

    iget p1, p1, Lcom/android/tools/r8/internal/so;->f:I

    invoke-virtual {p2, v0, p1}, Lcom/android/tools/r8/internal/Qe;->a(II)I

    move-result p1

    return p1
.end method

.method public a(Lcom/android/tools/r8/internal/Th0;)Ljava/lang/String;
    .locals 2

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Yo;->o()I

    move-result p1

    iget v0, p0, Lcom/android/tools/r8/internal/so;->f:I

    add-int/2addr p1, v0

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, ":label_"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Yo;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public a(Lcom/android/tools/r8/graph/s5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/jQ;Ljava/nio/ShortBuffer;)V
    .locals 0

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result p1

    const/4 p2, 0x0

    invoke-static {p2, p1, p6}, Lcom/android/tools/r8/internal/Yo;->a(IILjava/nio/ShortBuffer;)V

    .line 5
    iget p1, p0, Lcom/android/tools/r8/internal/so;->f:I

    int-to-long p1, p1

    invoke-static {p1, p2, p6}, Lcom/android/tools/r8/internal/Yo;->a(JLjava/nio/ShortBuffer;)V

    return-void
.end method

.method public a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/dex/M;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/jQ;)V
    .locals 0

    return-void
.end method

.method public b(Lcom/android/tools/r8/internal/Th0;)Ljava/lang/String;
    .locals 1

    .line 4
    iget p1, p0, Lcom/android/tools/r8/internal/so;->f:I

    const/4 v0, 0x2

    .line 5
    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/Sn0;->a(II)Ljava/lang/String;

    move-result-object p1

    .line 6
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Yo;->b(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public final b(Lcom/android/tools/r8/internal/mz;)V
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/so;->f:I

    check-cast p1, Lcom/android/tools/r8/internal/oz;

    .line 2
    iget-object p1, p1, Lcom/android/tools/r8/internal/oz;->a:Lcom/android/tools/r8/internal/iz;

    .line 3
    invoke-interface {p1, v0}, Lcom/android/tools/r8/internal/iz;->a(I)V

    return-void
.end method

.method public final hashCode()I
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/so;->f:I

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    xor-int/2addr v0, v1

    return v0
.end method
