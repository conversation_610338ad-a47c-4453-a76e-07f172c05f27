.class public final Lcom/android/tools/r8/internal/sW;
.super Lcom/android/tools/r8/internal/Ud;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic e:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/Ud;-><init>(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/I6;Lcom/android/tools/r8/internal/K5;Lcom/android/tools/r8/internal/A4;)V
    .locals 0

    .line 4
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/rD;->b()Lcom/android/tools/r8/internal/K5;

    move-result-object p2

    if-ne p2, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/I6;->a(Z)V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;)Z
    .locals 0

    .line 1
    iget-object p2, p0, Lcom/android/tools/r8/internal/Ud;->c:Lcom/android/tools/r8/utils/w;

    invoke-virtual {p2}, Lcom/android/tools/r8/utils/w;->a0()Z

    move-result p2

    if-eqz p2, :cond_0

    .line 2
    iget-object p1, p1, Lcom/android/tools/r8/internal/aA;->i:Lcom/android/tools/r8/internal/hA;

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/hA;->c()Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final b(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/Xd;
    .locals 16

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    .line 2
    invoke-static {}, Lcom/android/tools/r8/internal/kj0;->c()Ljava/util/Set;

    move-result-object v2

    .line 3
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->v()Lcom/android/tools/r8/internal/O5;

    move-result-object v3

    .line 4
    new-instance v4, Lcom/android/tools/r8/internal/or0;

    iget-object v5, v0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    const/4 v6, 0x0

    .line 5
    invoke-direct {v4, v5, v1, v6}, Lcom/android/tools/r8/internal/or0;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/aA;Z)V

    const/4 v5, 0x1

    .line 6
    iput-boolean v5, v4, Lcom/android/tools/r8/internal/or0;->b:Z

    move v7, v6

    move v8, v7

    .line 7
    :cond_0
    :goto_0
    invoke-interface {v3}, Ljava/util/ListIterator;->hasNext()Z

    move-result v9

    const/4 v10, 0x0

    if-eqz v9, :cond_10

    .line 8
    invoke-interface {v3}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lcom/android/tools/r8/internal/K5;

    .line 9
    invoke-interface {v2, v9}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_1

    goto :goto_0

    .line 13
    :cond_1
    invoke-virtual {v9, v1}, Lcom/android/tools/r8/internal/K5;->a(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/N5;

    move-result-object v11

    .line 14
    :goto_1
    invoke-interface {v11}, Ljava/util/ListIterator;->hasNext()Z

    move-result v12

    if-eqz v12, :cond_0

    .line 15
    invoke-interface {v11}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v12

    check-cast v12, Lcom/android/tools/r8/internal/rD;

    invoke-virtual {v12}, Lcom/android/tools/r8/internal/rD;->c0()Lcom/android/tools/r8/internal/uI;

    move-result-object v12

    if-eqz v12, :cond_f

    .line 16
    invoke-virtual {v12}, Lcom/android/tools/r8/internal/rD;->d1()Z

    move-result v13

    if-eqz v13, :cond_f

    invoke-virtual {v12}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v13

    invoke-virtual {v13}, Lcom/android/tools/r8/internal/vt0;->A()Z

    move-result v13

    if-eqz v13, :cond_2

    goto/16 :goto_8

    .line 21
    :cond_2
    iget-object v13, v0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->i()Lcom/android/tools/r8/graph/D5;

    move-result-object v14

    invoke-virtual {v12, v13, v14}, Lcom/android/tools/r8/internal/uI;->f(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/graph/H0;

    move-result-object v13

    if-nez v13, :cond_3

    goto/16 :goto_8

    .line 26
    :cond_3
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v13

    check-cast v13, Lcom/android/tools/r8/graph/j1;

    .line 27
    invoke-virtual {v13}, Lcom/android/tools/r8/graph/j1;->O0()V

    .line 28
    iget-object v13, v13, Lcom/android/tools/r8/graph/j1;->m:Lcom/android/tools/r8/internal/iV;

    .line 29
    invoke-virtual {v13}, Lcom/android/tools/r8/internal/iV;->G()Z

    move-result v14

    if-nez v14, :cond_4

    goto/16 :goto_8

    .line 33
    :cond_4
    invoke-virtual {v13}, Lcom/android/tools/r8/internal/iV;->t()I

    move-result v13

    if-ltz v13, :cond_f

    .line 34
    invoke-virtual {v12}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v14

    iget-object v14, v14, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v14, v14, Lcom/android/tools/r8/graph/F2;->e:Lcom/android/tools/r8/graph/J2;

    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v15

    iget-object v6, v0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-static {v14, v15, v6}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v6

    .line 35
    invoke-virtual {v12}, Lcom/android/tools/r8/internal/rD;->a2()Z

    move-result v14

    if-eqz v14, :cond_5

    .line 36
    invoke-virtual {v12}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v14

    iget-object v14, v14, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v14, v14, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    iget-object v14, v14, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    aget-object v14, v14, v13

    goto :goto_2

    :cond_5
    if-nez v13, :cond_6

    .line 39
    invoke-virtual {v12}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v14

    iget-object v14, v14, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    goto :goto_2

    .line 41
    :cond_6
    invoke-virtual {v12}, Lcom/android/tools/r8/internal/uI;->U2()Lcom/android/tools/r8/graph/x2;

    move-result-object v14

    iget-object v14, v14, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    iget-object v14, v14, Lcom/android/tools/r8/graph/F2;->f:Lcom/android/tools/r8/graph/L2;

    iget-object v14, v14, Lcom/android/tools/r8/graph/L2;->b:[Lcom/android/tools/r8/graph/J2;

    add-int/lit8 v15, v13, -0x1

    aget-object v14, v14, v15

    .line 42
    :goto_2
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v15

    iget-object v5, v0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-static {v14, v15, v5}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v5

    .line 43
    iget-object v14, v0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v14}, Lcom/android/tools/r8/graph/y;->p()Z

    move-result v14

    if-eqz v14, :cond_7

    .line 44
    iget-object v14, v0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v5, v6, v14}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/y;)Z

    move-result v5

    goto :goto_3

    .line 45
    :cond_7
    invoke-virtual {v5, v6}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result v5

    :goto_3
    if-nez v5, :cond_8

    :goto_4
    const/4 v6, 0x1

    goto/16 :goto_9

    .line 46
    :cond_8
    iget-object v5, v12, Lcom/android/tools/r8/internal/rD;->c:Ljava/util/ArrayList;

    .line 47
    invoke-virtual {v5, v13}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/internal/vt0;

    .line 48
    invoke-virtual {v12}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object v6

    .line 49
    sget-boolean v13, Lcom/android/tools/r8/internal/sW;->e:Z

    if-nez v13, :cond_9

    invoke-virtual {v5}, Lcom/android/tools/r8/internal/vt0;->d0()Lcom/android/tools/r8/internal/It0;

    move-result-object v13

    invoke-virtual {v6, v13}, Lcom/android/tools/r8/internal/vt0;->a(Lcom/android/tools/r8/internal/It0;)V

    .line 54
    :cond_9
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v13

    invoke-virtual {v6}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v14

    iget-object v15, v0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v13, v14, v15}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/y;)Z

    move-result v13

    if-nez v13, :cond_a

    goto :goto_4

    .line 59
    :cond_a
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v8

    invoke-virtual {v6}, Lcom/android/tools/r8/internal/vt0;->v()Lcom/android/tools/r8/internal/sr0;

    move-result-object v13

    invoke-virtual {v8, v13}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_b

    .line 60
    sget-object v8, Lcom/android/tools/r8/ir/optimize/a;->c:Lcom/android/tools/r8/ir/optimize/a;

    goto :goto_5

    .line 61
    :cond_b
    invoke-virtual {v6}, Lcom/android/tools/r8/internal/vt0;->a()Lcom/android/tools/r8/ir/optimize/a;

    move-result-object v8

    .line 63
    :goto_5
    invoke-virtual {v6}, Lcom/android/tools/r8/internal/vt0;->a0()I

    move-result v13

    if-lez v13, :cond_c

    const/4 v13, 0x1

    goto :goto_6

    :cond_c
    const/4 v13, 0x0

    :goto_6
    or-int/2addr v7, v13

    .line 64
    invoke-virtual {v6, v5}, Lcom/android/tools/r8/internal/vt0;->f(Lcom/android/tools/r8/internal/vt0;)V

    .line 65
    invoke-virtual {v12, v10}, Lcom/android/tools/r8/internal/rD;->d(Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/vt0;

    .line 66
    iget-object v5, v8, Lcom/android/tools/r8/ir/optimize/a;->b:Ljava/util/Set;

    .line 67
    invoke-interface {v5}, Ljava/util/Set;->isEmpty()Z

    move-result v5

    if-nez v5, :cond_d

    .line 68
    new-instance v5, Lcom/android/tools/r8/internal/I6;

    invoke-direct {v5}, Lcom/android/tools/r8/internal/I6;-><init>()V

    const/4 v6, 0x1

    .line 69
    iput-boolean v6, v4, Lcom/android/tools/r8/internal/or0;->b:Z

    .line 70
    new-instance v12, Lcom/android/tools/r8/internal/sW$$ExternalSyntheticLambda0;

    invoke-direct {v12, v5, v9}, Lcom/android/tools/r8/internal/sW$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/I6;Lcom/android/tools/r8/internal/K5;)V

    .line 71
    invoke-virtual {v4, v8, v12}, Lcom/android/tools/r8/internal/or0;->a(Ljava/lang/Iterable;Ljava/util/function/Consumer;)V

    .line 74
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/I6;->e()Z

    move-result v5

    if-eqz v5, :cond_e

    .line 76
    invoke-virtual {v9, v1}, Lcom/android/tools/r8/internal/K5;->a(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/N5;

    move-result-object v5

    move-object v11, v5

    goto :goto_7

    :cond_d
    const/4 v6, 0x1

    :cond_e
    :goto_7
    move v5, v6

    move v8, v5

    goto :goto_a

    :cond_f
    :goto_8
    move v6, v5

    :goto_9
    move v5, v6

    :goto_a
    const/4 v6, 0x0

    goto/16 :goto_1

    .line 81
    :cond_10
    new-instance v3, Lcom/android/tools/r8/ir/optimize/a;

    invoke-direct {v3}, Lcom/android/tools/r8/ir/optimize/a;-><init>()V

    .line 82
    invoke-interface {v2}, Ljava/util/Set;->isEmpty()Z

    move-result v5

    if-nez v5, :cond_12

    .line 83
    invoke-virtual {v1, v2}, Lcom/android/tools/r8/internal/aA;->b(Ljava/util/Collection;)V

    .line 84
    invoke-virtual {v1, v10, v3}, Lcom/android/tools/r8/internal/aA;->a(Lcom/android/tools/r8/internal/Vz;Lcom/android/tools/r8/ir/optimize/a;)Z

    .line 85
    sget-boolean v2, Lcom/android/tools/r8/internal/sW;->e:Z

    if-nez v2, :cond_13

    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->p()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_11

    goto :goto_b

    :cond_11
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    :cond_12
    if-eqz v7, :cond_13

    .line 86
    invoke-virtual {v1, v10, v3}, Lcom/android/tools/r8/internal/aA;->a(Lcom/android/tools/r8/internal/Vz;Lcom/android/tools/r8/ir/optimize/a;)Z

    .line 87
    :cond_13
    :goto_b
    invoke-static {}, Lcom/android/tools/r8/internal/Ch;->b()Ljava/util/function/Consumer;

    move-result-object v2

    invoke-virtual {v4, v3, v2}, Lcom/android/tools/r8/internal/or0;->a(Ljava/lang/Iterable;Ljava/util/function/Consumer;)V

    if-eqz v8, :cond_14

    .line 88
    invoke-virtual/range {p1 .. p1}, Lcom/android/tools/r8/internal/aA;->A()V

    .line 90
    :cond_14
    invoke-static {v8}, Lcom/android/tools/r8/internal/Xd;->a(Z)Lcom/android/tools/r8/internal/Wd;

    move-result-object v1

    return-object v1
.end method

.method public final b()Ljava/lang/String;
    .locals 1

    const-string v0, "MoveResultRewriter"

    return-object v0
.end method
