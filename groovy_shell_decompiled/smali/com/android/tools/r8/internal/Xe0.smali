.class public final Lcom/android/tools/r8/internal/Xe0;
.super Lcom/android/tools/r8/internal/uy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# static fields
.field public static final f:Lcom/android/tools/r8/internal/Xe0;

.field public static final g:Lcom/android/tools/r8/internal/Ve0;


# instance fields
.field public b:Lcom/android/tools/r8/internal/af0;

.field public volatile c:Ljava/lang/String;

.field public d:Ljava/util/List;

.field public e:B


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Xe0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Xe0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Xe0;->f:Lcom/android/tools/r8/internal/Xe0;

    .line 9
    new-instance v0, Lcom/android/tools/r8/internal/Ve0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Ve0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Xe0;->g:Lcom/android/tools/r8/internal/Ve0;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 265
    invoke-direct {p0}, Lcom/android/tools/r8/internal/uy;-><init>()V

    const/4 v0, -0x1

    .line 526
    iput-byte v0, p0, Lcom/android/tools/r8/internal/Xe0;->e:B

    const-string v0, ""

    .line 527
    iput-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->c:Ljava/lang/String;

    .line 528
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/We0;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/uy;-><init>(Lcom/android/tools/r8/internal/dy;)V

    const/4 p1, -0x1

    .line 264
    iput-byte p1, p0, Lcom/android/tools/r8/internal/Xe0;->e:B

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    .locals 6

    .line 529
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Xe0;-><init>()V

    .line 530
    invoke-static {p2}, Lcom/android/tools/r8/internal/qg;->a(Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/qs0;

    move-result-object v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    move v3, v2

    :cond_0
    :goto_0
    if-nez v2, :cond_8

    .line 7024
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->s()I

    move-result v4

    if-eqz v4, :cond_6

    const/16 v5, 0xa

    if-eq v4, v5, :cond_4

    const/16 v5, 0x12

    if-eq v4, v5, :cond_3

    const/16 v5, 0x1a

    if-eq v4, v5, :cond_1

    .line 7058
    invoke-virtual {p0, p1, v0, p2, v4}, Lcom/android/tools/r8/internal/uy;->parseUnknownField(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/qs0;Lcom/android/tools/r8/internal/Lu;I)Z

    move-result v4

    if-nez v4, :cond_0

    goto :goto_1

    :cond_1
    if-nez v3, :cond_2

    .line 7059
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    iput-object v4, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    move v3, v1

    .line 7062
    :cond_2
    iget-object v4, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    .line 7063
    sget-object v5, Lcom/android/tools/r8/internal/tg0;->g:Lcom/android/tools/r8/internal/rg0;

    .line 7064
    invoke-virtual {p1, v5, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/internal/tg0;

    .line 7065
    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 7066
    :cond_3
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->r()Ljava/lang/String;

    move-result-object v4

    .line 7068
    iput-object v4, p0, Lcom/android/tools/r8/internal/Xe0;->c:Ljava/lang/String;

    goto :goto_0

    :cond_4
    const/4 v4, 0x0

    .line 7069
    iget-object v5, p0, Lcom/android/tools/r8/internal/Xe0;->b:Lcom/android/tools/r8/internal/af0;

    if-eqz v5, :cond_5

    .line 7070
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/af0;->a()Lcom/android/tools/r8/internal/Ze0;

    move-result-object v4

    .line 7071
    :cond_5
    sget-object v5, Lcom/android/tools/r8/internal/af0;->e:Lcom/android/tools/r8/internal/Ye0;

    .line 7072
    invoke-virtual {p1, v5, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/internal/af0;

    iput-object v5, p0, Lcom/android/tools/r8/internal/Xe0;->b:Lcom/android/tools/r8/internal/af0;

    if-eqz v4, :cond_0

    .line 7074
    invoke-virtual {v4, v5}, Lcom/android/tools/r8/internal/Ze0;->a(Lcom/android/tools/r8/internal/af0;)Lcom/android/tools/r8/internal/Ze0;

    .line 7075
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/Ze0;->b()Lcom/android/tools/r8/internal/af0;

    move-result-object v4

    iput-object v4, p0, Lcom/android/tools/r8/internal/Xe0;->b:Lcom/android/tools/r8/internal/af0;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :cond_6
    :goto_1
    move v2, v1

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :catch_0
    move-exception p1

    .line 7107
    :try_start_1
    new-instance p2, Lcom/android/tools/r8/internal/lI;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/lI;-><init>(Ljava/io/IOException;)V

    .line 7108
    iput-object p0, p2, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 7109
    throw p2

    :catch_1
    move-exception p1

    .line 7110
    iput-object p0, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 7111
    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :goto_2
    if-eqz v3, :cond_7

    .line 7117
    iget-object p2, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    .line 7119
    :cond_7
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 7120
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    .line 7121
    throw p1

    :cond_8
    if-eqz v3, :cond_9

    .line 7122
    iget-object p1, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    .line 7124
    :cond_9
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 7125
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    return-void
.end method

.method public static synthetic a()Z
    .locals 1

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/uy;->alwaysUseFieldBuilders:Z

    return v0
.end method


# virtual methods
.method public final b()Lcom/android/tools/r8/internal/af0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->b:Lcom/android/tools/r8/internal/af0;

    if-nez v0, :cond_0

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/af0;->d:Lcom/android/tools/r8/internal/af0;

    :cond_0
    return-object v0
.end method

.method public final c()Ljava/lang/String;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->c:Ljava/lang/String;

    .line 2
    instance-of v1, v0, Ljava/lang/String;

    if-eqz v1, :cond_0

    return-object v0

    .line 5
    :cond_0
    check-cast v0, Lcom/android/tools/r8/internal/Z7;

    .line 7
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v0

    .line 8
    iput-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->c:Ljava/lang/String;

    return-object v0
.end method

.method public final d()Lcom/android/tools/r8/internal/We0;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Xe0;->f:Lcom/android/tools/r8/internal/Xe0;

    if-ne p0, v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/We0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/We0;-><init>()V

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/We0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/We0;-><init>()V

    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/We0;->a(Lcom/android/tools/r8/internal/Xe0;)Lcom/android/tools/r8/internal/We0;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 5

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/internal/Xe0;

    if-nez v1, :cond_1

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/J0;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/internal/Xe0;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xe0;->b:Lcom/android/tools/r8/internal/af0;

    const/4 v2, 0x0

    if-eqz v1, :cond_2

    move v3, v0

    goto :goto_0

    :cond_2
    move v3, v2

    .line 6
    :goto_0
    iget-object v4, p1, Lcom/android/tools/r8/internal/Xe0;->b:Lcom/android/tools/r8/internal/af0;

    if-eqz v4, :cond_3

    move v4, v0

    goto :goto_1

    :cond_3
    move v4, v2

    :goto_1
    if-eq v3, v4, :cond_4

    return v2

    :cond_4
    if-eqz v1, :cond_5

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xe0;->b()Lcom/android/tools/r8/internal/af0;

    move-result-object v1

    .line 8
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Xe0;->b()Lcom/android/tools/r8/internal/af0;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/af0;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    return v2

    .line 10
    :cond_5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xe0;->c()Ljava/lang/String;

    move-result-object v1

    .line 11
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Xe0;->c()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_6

    return v2

    .line 12
    :cond_6
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    .line 13
    iget-object v3, p1, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    .line 14
    invoke-interface {v1, v3}, Ljava/util/List;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_7

    return v2

    .line 15
    :cond_7
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/vs0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_8

    return v2

    :cond_8
    return v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/Xe0;->f:Lcom/android/tools/r8/internal/Xe0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Xe0;->f:Lcom/android/tools/r8/internal/Xe0;

    return-object v0
.end method

.method public final getSerializedSize()I
    .locals 4

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->b:Lcom/android/tools/r8/internal/af0;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xe0;->b()Lcom/android/tools/r8/internal/af0;

    move-result-object v0

    const/4 v2, 0x1

    .line 8
    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v2

    invoke-static {v0}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/AU;)I

    move-result v0

    add-int/2addr v0, v2

    goto :goto_0

    :cond_1
    move v0, v1

    .line 9
    :goto_0
    iget-object v2, p0, Lcom/android/tools/r8/internal/Xe0;->c:Ljava/lang/String;

    invoke-static {v2}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_2

    .line 10
    iget-object v2, p0, Lcom/android/tools/r8/internal/Xe0;->c:Ljava/lang/String;

    const/4 v3, 0x2

    invoke-static {v3, v2}, Lcom/android/tools/r8/internal/uy;->computeStringSize(ILjava/lang/Object;)I

    move-result v2

    add-int/2addr v0, v2

    .line 12
    :cond_2
    :goto_1
    iget-object v2, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_3

    const/4 v2, 0x3

    .line 13
    iget-object v3, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    .line 14
    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/AU;

    .line 15
    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v2

    .line 16
    invoke-static {v3, v2, v0}, Lcom/android/tools/r8/internal/Rd0;->a(Lcom/android/tools/r8/internal/AU;II)I

    move-result v0

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 6783
    :cond_3
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vs0;->getSerializedSize()I

    move-result v1

    add-int/2addr v1, v0

    .line 6784
    iput v1, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    return v1
.end method

.method public final getUnknownFields()Lcom/android/tools/r8/internal/vs0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    return-object v0
.end method

.method public final hashCode()I
    .locals 4

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    if-eqz v0, :cond_0

    return v0

    .line 2
    :cond_0
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->o:Lcom/android/tools/r8/internal/Ok;

    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    add-int/lit16 v0, v0, 0x30b

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xe0;->b:Lcom/android/tools/r8/internal/af0;

    const/16 v2, 0x35

    const/16 v3, 0x25

    if-eqz v1, :cond_1

    const/4 v1, 0x1

    .line 5
    invoke-static {v0, v3, v1, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xe0;->b()Lcom/android/tools/r8/internal/af0;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/af0;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    :cond_1
    const/4 v1, 0x2

    .line 8
    invoke-static {v0, v3, v1, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xe0;->c()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_2

    const/4 v0, 0x3

    .line 10
    invoke-static {v1, v3, v0, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    .line 11
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    .line 12
    invoke-interface {v1}, Ljava/util/List;->hashCode()I

    move-result v1

    add-int/2addr v1, v0

    :cond_2
    mul-int/lit8 v1, v1, 0x1d

    .line 14
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vs0;->hashCode()I

    move-result v0

    add-int/2addr v0, v1

    .line 15
    iput v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    return v0
.end method

.method public final internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->p:Lcom/android/tools/r8/internal/sy;

    .line 2
    const-class v1, Lcom/android/tools/r8/internal/Xe0;

    const-class v2, Lcom/android/tools/r8/internal/We0;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 2

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/internal/Xe0;->e:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    if-nez v0, :cond_1

    const/4 v0, 0x0

    return v0

    .line 5
    :cond_1
    iput-byte v1, p0, Lcom/android/tools/r8/internal/Xe0;->e:B

    return v1
.end method

.method public final newBuilderForType()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Xe0;->f:Lcom/android/tools/r8/internal/Xe0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xe0;->d()Lcom/android/tools/r8/internal/We0;

    move-result-object v0

    return-object v0
.end method

.method public final newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/We0;

    check-cast p1, Lcom/android/tools/r8/internal/ay;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/We0;-><init>(Lcom/android/tools/r8/internal/ay;)V

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xe0;->d()Lcom/android/tools/r8/internal/We0;

    move-result-object v0

    return-object v0
.end method

.method public final bridge synthetic toBuilder()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xe0;->d()Lcom/android/tools/r8/internal/We0;

    move-result-object v0

    return-object v0
.end method

.method public final writeTo(Lcom/android/tools/r8/internal/je;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->b:Lcom/android/tools/r8/internal/af0;

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Xe0;->b()Lcom/android/tools/r8/internal/af0;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->c:Ljava/lang/String;

    invoke-static {v0}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/internal/Xe0;->c:Ljava/lang/String;

    const/4 v1, 0x2

    invoke-static {p1, v1, v0}, Lcom/android/tools/r8/internal/uy;->writeString(Lcom/android/tools/r8/internal/je;ILjava/lang/Object;)V

    :cond_1
    const/4 v0, 0x0

    .line 7
    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_2

    .line 8
    iget-object v1, p0, Lcom/android/tools/r8/internal/Xe0;->d:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/AU;

    const/4 v2, 0x3

    invoke-virtual {p1, v2, v1}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 10
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/vs0;->writeTo(Lcom/android/tools/r8/internal/je;)V

    return-void
.end method
