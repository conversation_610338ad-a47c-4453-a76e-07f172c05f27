.class public final Lcom/android/tools/r8/internal/T10;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/Aq0;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Ly;Lcom/android/tools/r8/internal/Hr0;)Lcom/android/tools/r8/internal/zq0;
    .locals 1

    .line 1
    iget-object p2, p2, Lcom/android/tools/r8/internal/Hr0;->a:Ljava/lang/Class;

    .line 2
    const-class v0, Ljava/lang/Object;

    if-ne p2, v0, :cond_0

    .line 3
    new-instance p2, Lcom/android/tools/r8/internal/U10;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/U10;-><init>(Lcom/android/tools/r8/internal/Ly;)V

    return-object p2

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method
