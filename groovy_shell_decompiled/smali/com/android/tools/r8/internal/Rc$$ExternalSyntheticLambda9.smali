.class public final synthetic Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda9;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Rc;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Rc;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda9;->f$0:Lcom/android/tools/r8/internal/Rc;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/Rc$$ExternalSyntheticLambda9;->f$0:Lcom/android/tools/r8/internal/Rc;

    check-cast p1, Lcom/android/tools/r8/graph/l5;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Rc;->a(Lcom/android/tools/r8/graph/l5;)Z

    move-result p1

    return p1
.end method
