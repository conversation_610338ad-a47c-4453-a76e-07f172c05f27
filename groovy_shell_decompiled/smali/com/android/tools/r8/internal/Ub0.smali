.class public abstract Lcom/android/tools/r8/internal/Ub0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final a:Lcom/android/tools/r8/internal/Ub0;


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    sget v0, Lcom/android/tools/r8/internal/oJ;->a:I

    const/16 v1, 0x9

    if-lt v0, v1, :cond_0

    .line 2
    :try_start_0
    const-class v0, Ljava/lang/reflect/AccessibleObject;

    const-string v1, "canAccess"

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Class;

    const-class v3, Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object v3, v2, v4

    invoke-virtual {v0, v1, v2}, Ljava/lang/Class;->getDeclaredMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v0

    .line 3
    new-instance v1, Lcom/android/tools/r8/internal/Sb0;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/internal/Sb0;-><init>(Ljava/lang/reflect/Method;)V
    :try_end_0
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    :cond_0
    const/4 v1, 0x0

    :goto_0
    if-nez v1, :cond_1

    .line 17
    new-instance v1, Lcom/android/tools/r8/internal/Tb0;

    invoke-direct {v1}, Lcom/android/tools/r8/internal/Tb0;-><init>()V

    .line 24
    :cond_1
    sput-object v1, Lcom/android/tools/r8/internal/Ub0;->a:Lcom/android/tools/r8/internal/Ub0;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a(Ljava/lang/Object;Ljava/lang/reflect/AccessibleObject;)Z
.end method
