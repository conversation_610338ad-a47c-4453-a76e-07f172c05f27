.class public final Lcom/android/tools/r8/internal/Sx;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/lang/Comparable;


# instance fields
.field public final b:I

.field public final c:Lcom/android/tools/r8/internal/Pu0;

.field public final d:Z


# direct methods
.method public constructor <init>(ILcom/android/tools/r8/internal/Pu0;Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput p1, p0, Lcom/android/tools/r8/internal/Sx;->b:I

    .line 4
    iput-object p2, p0, Lcom/android/tools/r8/internal/Sx;->c:Lcom/android/tools/r8/internal/Pu0;

    .line 5
    iput-boolean p3, p0, Lcom/android/tools/r8/internal/Sx;->d:Z

    return-void
.end method


# virtual methods
.method public final compareTo(Ljava/lang/Object;)I
    .locals 1

    .line 1
    check-cast p1, Lcom/android/tools/r8/internal/Sx;

    .line 2
    iget v0, p0, Lcom/android/tools/r8/internal/Sx;->b:I

    iget p1, p1, Lcom/android/tools/r8/internal/Sx;->b:I

    sub-int/2addr v0, p1

    return v0
.end method
