.class public final Lcom/android/tools/r8/internal/SX;
.super Lcom/android/tools/r8/graph/m0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final e:Lcom/android/tools/r8/internal/Fy;

.field public final synthetic f:Lcom/android/tools/r8/internal/TX;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/TX;Lcom/android/tools/r8/graph/D5;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    .line 2
    iget-object p1, p1, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    .line 3
    iget-object p1, p1, Lcom/android/tools/r8/internal/UX;->b:Lcom/android/tools/r8/graph/y;

    .line 4
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/graph/m0;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/H0;)V

    .line 5
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object p2

    invoke-virtual {p2, p1}, Lcom/android/tools/r8/graph/i0;->a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/Fy;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/D0;)V
    .locals 1

    .line 56
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    .line 57
    iget-object v0, v0, Lcom/android/tools/r8/internal/UX;->b:Lcom/android/tools/r8/graph/y;

    .line 58
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/DP;->a(Lcom/android/tools/r8/graph/D0;Lcom/android/tools/r8/graph/h;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    .line 61
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/SX;->d()V

    :goto_0
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/E2;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/UX;->b:Lcom/android/tools/r8/graph/y;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    .line 5
    check-cast v1, Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v1

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/graph/j;->b(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/E0;)Z

    move-result v0

    if-nez v0, :cond_5

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->c:Ljava/util/Set;

    .line 7
    new-instance v1, Lcom/android/tools/r8/internal/Vu0;

    invoke-direct {v1, v0}, Lcom/android/tools/r8/internal/Vu0;-><init>(Ljava/util/Set;)V

    .line 8
    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Object;)Z

    .line 9
    :cond_0
    :goto_0
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Vu0;->b()Z

    move-result p1

    if-eqz p1, :cond_5

    .line 10
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Vu0;->d()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/E2;

    .line 11
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->b:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 12
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    .line 13
    iget-object v2, v0, Lcom/android/tools/r8/internal/TX;->g:Ljava/util/Collection;

    .line 14
    invoke-interface {v2}, Ljava/util/Collection;->size()I

    move-result v2

    const/4 v3, 0x1

    if-ne v2, v3, :cond_1

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->g:Ljava/util/Collection;

    invoke-interface {v0, p1}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 15
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/SX;->d()V

    goto :goto_1

    .line 18
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/TX;->b(Lcom/android/tools/r8/graph/E2;)V

    .line 19
    :cond_2
    :goto_1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->N0()Lcom/android/tools/r8/graph/j1;

    move-result-object v0

    .line 20
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/E2;->i(Lcom/android/tools/r8/graph/j1;)Lcom/android/tools/r8/graph/D5;

    move-result-object v0

    if-eqz v0, :cond_3

    .line 21
    iget-object v2, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    invoke-virtual {v2, v0}, Lcom/android/tools/r8/internal/TX;->a(Lcom/android/tools/r8/graph/D5;)Z

    move-result v0

    if-nez v0, :cond_3

    goto :goto_2

    .line 27
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    .line 28
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->d1()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/android/tools/r8/graph/y;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object v0

    invoke-static {v0}, Lcom/android/tools/r8/graph/E2;->b(Lcom/android/tools/r8/graph/E0;)Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    if-eqz v0, :cond_4

    .line 30
    invoke-virtual {v1, v0}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Object;)Z

    .line 33
    :cond_4
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/UX;->c:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/vz;

    if-eqz p1, :cond_0

    .line 34
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->a:Lcom/android/tools/r8/internal/vz;

    if-eq p1, v0, :cond_0

    .line 35
    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/Vu0;->b(Ljava/lang/Iterable;)V

    goto :goto_0

    :cond_5
    :goto_2
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;)V
    .locals 4

    .line 36
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    .line 37
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    .line 38
    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    .line 39
    check-cast v1, Lcom/android/tools/r8/graph/D5;

    iget-object v2, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 40
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    sget-object v3, Lcom/android/tools/r8/internal/JI;->d:Lcom/android/tools/r8/internal/JI;

    invoke-virtual {v0, p1, v1, v3, v2}, Lcom/android/tools/r8/internal/Fy;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    .line 41
    iget-object p1, p1, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 42
    check-cast p1, Lcom/android/tools/r8/graph/x2;

    .line 43
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    .line 44
    iget-object v0, v0, Lcom/android/tools/r8/internal/UX;->b:Lcom/android/tools/r8/graph/y;

    .line 45
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 46
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/j;->b(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/V4;

    move-result-object p1

    .line 47
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/V4;->r()Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    .line 51
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/H4;->N()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 52
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/SX;->d()V

    return-void

    .line 55
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/TX;->a(Lcom/android/tools/r8/graph/D5;)Z

    return-void
.end method

.method public final b(Lcom/android/tools/r8/graph/J2;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    invoke-interface {v0, p1}, Lcom/android/tools/r8/graph/d1;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/E2;->b(Lcom/android/tools/r8/graph/E0;)Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 4
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/SX;->a(Lcom/android/tools/r8/graph/E2;)V

    :cond_0
    return-void
.end method

.method public final b(Lcom/android/tools/r8/graph/x2;)V
    .locals 4

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    .line 7
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    .line 8
    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    .line 9
    check-cast v1, Lcom/android/tools/r8/graph/D5;

    iget-object v2, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 10
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    sget-object v3, Lcom/android/tools/r8/internal/JI;->e:Lcom/android/tools/r8/internal/JI;

    invoke-virtual {v0, p1, v1, v3, v2}, Lcom/android/tools/r8/internal/Fy;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    .line 11
    iget-object p1, p1, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 12
    check-cast p1, Lcom/android/tools/r8/graph/x2;

    .line 13
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    .line 14
    iget-object v0, v0, Lcom/android/tools/r8/internal/UX;->b:Lcom/android/tools/r8/graph/y;

    .line 15
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 16
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/j;->c(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/V4;

    move-result-object p1

    .line 17
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/V4;->p()Lcom/android/tools/r8/graph/H0;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 19
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/SX;->d()V

    :cond_0
    return-void
.end method

.method public final d()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/TX;->b()V

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b6;->a()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/TX;->a()V

    return-void
.end method

.method public final d(Lcom/android/tools/r8/graph/J2;)V
    .locals 2

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    invoke-interface {v0, p1}, Lcom/android/tools/r8/graph/d1;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/E2;->b(Lcom/android/tools/r8/graph/E0;)Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 7
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/SX;->a(Lcom/android/tools/r8/graph/E2;)V

    :cond_0
    return-void
.end method

.method public final e(Lcom/android/tools/r8/graph/l1;)V
    .locals 2

    .line 22
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    .line 23
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 24
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 25
    invoke-interface {v0, p1}, Lcom/android/tools/r8/graph/d1;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/E2;->b(Lcom/android/tools/r8/graph/E0;)Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 26
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/SX;->a(Lcom/android/tools/r8/graph/E2;)V

    :cond_0
    return-void
.end method

.method public final e(Lcom/android/tools/r8/graph/x2;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    .line 4
    check-cast v1, Lcom/android/tools/r8/graph/D5;

    iget-object v2, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    sget-object v3, Lcom/android/tools/r8/internal/JI;->f:Lcom/android/tools/r8/internal/JI;

    invoke-virtual {v0, p1, v1, v3, v2}, Lcom/android/tools/r8/internal/Fy;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    .line 6
    iget-object p1, p1, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 7
    check-cast p1, Lcom/android/tools/r8/graph/x2;

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    .line 9
    iget-object v0, v0, Lcom/android/tools/r8/internal/UX;->b:Lcom/android/tools/r8/graph/y;

    .line 10
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 11
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/j;->e(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/V4;

    move-result-object p1

    .line 12
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/V4;->r()Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    .line 16
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/H4;->N()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 17
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/SX;->d()V

    return-void

    .line 20
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/SX;->a(Lcom/android/tools/r8/graph/E2;)V

    .line 21
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/TX;->a(Lcom/android/tools/r8/graph/D5;)Z

    return-void
.end method

.method public final g(Lcom/android/tools/r8/graph/l1;)V
    .locals 2

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0, v1, p1}, Lcom/android/tools/r8/internal/Fy;->e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/graph/l1;

    move-result-object p1

    .line 28
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/s2;->v0()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 29
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 30
    invoke-interface {v0, p1}, Lcom/android/tools/r8/graph/d1;->g(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/E0;

    move-result-object p1

    invoke-static {p1}, Lcom/android/tools/r8/graph/E2;->b(Lcom/android/tools/r8/graph/E0;)Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 31
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/SX;->a(Lcom/android/tools/r8/graph/E2;)V

    :cond_0
    return-void
.end method

.method public final g(Lcom/android/tools/r8/graph/x2;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    .line 4
    check-cast v1, Lcom/android/tools/r8/graph/D5;

    iget-object v2, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    sget-object v3, Lcom/android/tools/r8/internal/JI;->g:Lcom/android/tools/r8/internal/JI;

    invoke-virtual {v0, p1, v1, v3, v2}, Lcom/android/tools/r8/internal/Fy;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    .line 6
    iget-object p1, p1, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 7
    check-cast p1, Lcom/android/tools/r8/graph/x2;

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    .line 9
    iget-object v0, v0, Lcom/android/tools/r8/internal/UX;->b:Lcom/android/tools/r8/graph/y;

    .line 10
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 11
    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    .line 12
    check-cast v1, Lcom/android/tools/r8/graph/D5;

    iget-object v2, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v2, v2, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    .line 13
    iget-object v2, v2, Lcom/android/tools/r8/internal/UX;->b:Lcom/android/tools/r8/graph/y;

    .line 14
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 15
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/j;

    .line 16
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v1

    invoke-virtual {v0, p1, v1, v2, v3}, Lcom/android/tools/r8/graph/j;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/j;)Lcom/android/tools/r8/graph/H0;

    move-result-object p1

    .line 17
    invoke-static {p1}, Lcom/android/tools/r8/graph/H0;->a(Lcom/android/tools/r8/graph/H0;)Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    .line 22
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/H4;->N()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 23
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/SX;->d()V

    return-void

    .line 26
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/TX;->a(Lcom/android/tools/r8/graph/D5;)Z

    return-void
.end method

.method public final h(Lcom/android/tools/r8/graph/x2;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v0

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->b:Lcom/android/tools/r8/graph/F5;

    .line 4
    check-cast v1, Lcom/android/tools/r8/graph/D5;

    iget-object v2, p0, Lcom/android/tools/r8/internal/SX;->e:Lcom/android/tools/r8/internal/Fy;

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/x2;

    sget-object v3, Lcom/android/tools/r8/internal/JI;->h:Lcom/android/tools/r8/internal/JI;

    invoke-virtual {v0, p1, v1, v3, v2}, Lcom/android/tools/r8/internal/Fy;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    .line 6
    iget-object p1, p1, Lcom/android/tools/r8/internal/YT;->a:Lcom/android/tools/r8/graph/s2;

    .line 7
    check-cast p1, Lcom/android/tools/r8/graph/x2;

    .line 8
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    iget-object v0, v0, Lcom/android/tools/r8/internal/TX;->h:Lcom/android/tools/r8/internal/UX;

    .line 9
    iget-object v0, v0, Lcom/android/tools/r8/internal/UX;->b:Lcom/android/tools/r8/graph/y;

    .line 10
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    .line 11
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/graph/j;->b(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/V4;

    move-result-object p1

    .line 12
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/V4;->p()Lcom/android/tools/r8/graph/H0;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    .line 16
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->getHolder()Lcom/android/tools/r8/graph/E0;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/graph/b6;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/graph/E0;->c(Lcom/android/tools/r8/graph/y;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 17
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/H4;->N()Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    .line 18
    :cond_1
    instance-of v0, p1, Lcom/android/tools/r8/graph/D5;

    if-eqz v0, :cond_2

    .line 19
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->w()Lcom/android/tools/r8/graph/H4;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/H4;->K()Z

    move-result v0

    if-nez v0, :cond_2

    .line 20
    iget-object v0, p0, Lcom/android/tools/r8/internal/SX;->f:Lcom/android/tools/r8/internal/TX;

    invoke-interface {p1}, Lcom/android/tools/r8/graph/o0;->H()Lcom/android/tools/r8/graph/D5;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/TX;->a(Lcom/android/tools/r8/graph/D5;)Z

    :cond_2
    return-void

    .line 21
    :cond_3
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/SX;->d()V

    return-void
.end method
