.class public final Lcom/android/tools/r8/internal/St0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic b:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/St0;->a:Lcom/android/tools/r8/graph/y;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/internal/sr0;)Z
    .locals 5

    .line 1
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v0

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v1

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    .line 4
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 5
    sget-boolean v0, Lcom/android/tools/r8/internal/St0;->b:Z

    if-nez v0, :cond_2

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->H()Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 6
    :cond_2
    :goto_0
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/sr0;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 8
    :cond_3
    sget-boolean v0, Lcom/android/tools/r8/internal/St0;->b:Z

    if-nez v0, :cond_5

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v1

    if-eqz v1, :cond_4

    goto :goto_1

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 9
    :cond_5
    :goto_1
    instance-of v1, p1, Lcom/android/tools/r8/internal/xb0;

    const/4 v3, 0x1

    if-eqz v1, :cond_6

    .line 10
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->I()Z

    move-result v1

    if-eqz v1, :cond_6

    return v3

    .line 13
    :cond_6
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->r()Z

    move-result v1

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->r()Z

    move-result v4

    if-eq v1, v4, :cond_8

    .line 14
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->r()Z

    move-result p1

    if-eqz p1, :cond_7

    .line 15
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Dd;->Q()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    iget-object p2, p0, Lcom/android/tools/r8/internal/St0;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object p2

    iget-object p2, p2, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    if-ne p1, p2, :cond_7

    move v2, v3

    :cond_7
    return v2

    .line 17
    :cond_8
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->r()Z

    move-result v1

    if-eqz v1, :cond_b

    if-nez v0, :cond_a

    .line 18
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->r()Z

    move-result v0

    if-eqz v0, :cond_9

    goto :goto_2

    :cond_9
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 19
    :cond_a
    :goto_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->a()Lcom/android/tools/r8/internal/T3;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/T3;->R()Lcom/android/tools/r8/internal/sr0;

    move-result-object p1

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->a()Lcom/android/tools/r8/internal/T3;

    move-result-object p2

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/T3;->R()Lcom/android/tools/r8/internal/sr0;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/St0;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/internal/sr0;)Z

    move-result p1

    return p1

    :cond_b
    if-nez v0, :cond_d

    .line 21
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->w()Z

    move-result v0

    if-eqz v0, :cond_c

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->w()Z

    move-result v0

    if-eqz v0, :cond_c

    goto :goto_3

    :cond_c
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 22
    :cond_d
    :goto_3
    iget-object v0, p0, Lcom/android/tools/r8/internal/St0;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->p()Z

    move-result v0

    if-eqz v0, :cond_10

    .line 23
    iget-object v0, p0, Lcom/android/tools/r8/internal/St0;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1, p2, v0}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/y;)Z

    move-result p2

    if-nez p2, :cond_e

    iget-object p2, p0, Lcom/android/tools/r8/internal/St0;->a:Lcom/android/tools/r8/graph/y;

    .line 24
    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->V()Lcom/android/tools/r8/graph/y;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/y;)Z

    move-result p1

    if-eqz p1, :cond_f

    :cond_e
    move v2, v3

    :cond_f
    return v2

    .line 27
    :cond_10
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Dd;->Q()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    iget-object v0, p0, Lcom/android/tools/r8/internal/St0;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    if-ne p1, v0, :cond_12

    .line 28
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/sr0;->b()Lcom/android/tools/r8/internal/Dd;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Dd;->Q()Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    iget-object p2, p0, Lcom/android/tools/r8/internal/St0;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object p2

    iget-object p2, p2, Lcom/android/tools/r8/graph/B1;->b2:Lcom/android/tools/r8/graph/J2;

    if-ne p1, p2, :cond_11

    move v2, v3

    :cond_11
    return v2

    :cond_12
    return v3
.end method
