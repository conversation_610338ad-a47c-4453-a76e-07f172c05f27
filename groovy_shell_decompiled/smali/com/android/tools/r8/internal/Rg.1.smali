.class public final Lcom/android/tools/r8/internal/Rg;
.super Lcom/android/tools/r8/internal/kD;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public d:J


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/kD;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/kD;
    .locals 0

    return-object p0
.end method

.method public final b()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final c()Lcom/android/tools/r8/internal/Sg;
    .locals 4

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Sg;

    iget-object v1, p0, Lcom/android/tools/r8/internal/kD;->a:Lcom/android/tools/r8/internal/vt0;

    iget-wide v2, p0, Lcom/android/tools/r8/internal/Rg;->d:J

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/Sg;-><init>(Lcom/android/tools/r8/internal/vt0;J)V

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/kD;->a(Lcom/android/tools/r8/internal/rD;)Lcom/android/tools/r8/internal/rD;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/Sg;

    return-object v0
.end method
