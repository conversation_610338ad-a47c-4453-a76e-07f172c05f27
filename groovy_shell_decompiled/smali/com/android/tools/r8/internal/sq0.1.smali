.class public final Lcom/android/tools/r8/internal/sq0;
.super Lcom/android/tools/r8/internal/Rw;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/util/concurrent/RunnableFuture;


# instance fields
.field public volatile i:Lcom/android/tools/r8/internal/rq0;


# direct methods
.method public constructor <init>(Ljava/util/concurrent/Callable;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Rw;-><init>()V

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/rq0;

    invoke-direct {v0, p0, p1}, Lcom/android/tools/r8/internal/rq0;-><init>(Lcom/android/tools/r8/internal/sq0;Ljava/util/concurrent/Callable;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/sq0;->i:Lcom/android/tools/r8/internal/rq0;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/C;->b:Ljava/lang/Object;

    .line 2
    instance-of v1, v0, Lcom/android/tools/r8/internal/t;

    if-eqz v1, :cond_1

    check-cast v0, Lcom/android/tools/r8/internal/t;

    iget-boolean v0, v0, Lcom/android/tools/r8/internal/t;->a:Z

    if-eqz v0, :cond_1

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/sq0;->i:Lcom/android/tools/r8/internal/rq0;

    if-eqz v0, :cond_1

    .line 4
    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Runnable;

    .line 5
    instance-of v2, v1, Ljava/lang/Thread;

    if-eqz v2, :cond_1

    .line 6
    new-instance v2, Lcom/android/tools/r8/internal/ZH;

    invoke-direct {v2, v0}, Lcom/android/tools/r8/internal/ZH;-><init>(Lcom/android/tools/r8/internal/rq0;)V

    .line 7
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v3

    invoke-static {v2, v3}, Lcom/android/tools/r8/internal/ZH;->a(Lcom/android/tools/r8/internal/ZH;Ljava/lang/Thread;)V

    .line 8
    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicReference;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 14
    :try_start_0
    move-object v2, v1

    check-cast v2, Ljava/lang/Thread;

    invoke-virtual {v2}, Ljava/lang/Thread;->interrupt()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 16
    sget-object v2, Lcom/android/tools/r8/internal/bI;->b:Lcom/android/tools/r8/internal/aI;

    invoke-virtual {v0, v2}, Ljava/util/concurrent/atomic/AtomicReference;->getAndSet(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Runnable;

    .line 17
    sget-object v2, Lcom/android/tools/r8/internal/bI;->c:Lcom/android/tools/r8/internal/aI;

    if-ne v0, v2, :cond_1

    .line 18
    check-cast v1, Ljava/lang/Thread;

    invoke-static {v1}, Ljava/util/concurrent/locks/LockSupport;->unpark(Ljava/lang/Thread;)V

    goto :goto_0

    :catchall_0
    move-exception v2

    .line 19
    sget-object v3, Lcom/android/tools/r8/internal/bI;->b:Lcom/android/tools/r8/internal/aI;

    invoke-virtual {v0, v3}, Ljava/util/concurrent/atomic/AtomicReference;->getAndSet(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Runnable;

    .line 20
    sget-object v3, Lcom/android/tools/r8/internal/bI;->c:Lcom/android/tools/r8/internal/aI;

    if-ne v0, v3, :cond_0

    .line 21
    check-cast v1, Ljava/lang/Thread;

    invoke-static {v1}, Ljava/util/concurrent/locks/LockSupport;->unpark(Ljava/lang/Thread;)V

    .line 23
    :cond_0
    throw v2

    :cond_1
    :goto_0
    const/4 v0, 0x0

    .line 24
    iput-object v0, p0, Lcom/android/tools/r8/internal/sq0;->i:Lcom/android/tools/r8/internal/rq0;

    return-void
.end method

.method public final b()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sq0;->i:Lcom/android/tools/r8/internal/rq0;

    if-eqz v0, :cond_0

    .line 3
    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "task=["

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 5
    :cond_0
    invoke-super {p0}, Lcom/android/tools/r8/internal/C;->b()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/sq0;->i:Lcom/android/tools/r8/internal/rq0;

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/bI;->run()V

    :cond_0
    const/4 v0, 0x0

    .line 9
    iput-object v0, p0, Lcom/android/tools/r8/internal/sq0;->i:Lcom/android/tools/r8/internal/rq0;

    return-void
.end method
