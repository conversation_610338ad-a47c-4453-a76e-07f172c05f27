.class public final Lcom/android/tools/r8/internal/Sp;
.super Lcom/android/tools/r8/internal/fo;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 2
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/fo;-><init>(I)V

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Zo;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/fo;-><init>(ILcom/android/tools/r8/internal/Zo;)V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Vz;)V
    .locals 1

    .line 1
    iget-short v0, p0, Lcom/android/tools/r8/internal/fo;->f:S

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/Vz;->b(I)V

    return-void
.end method

.method public final l()Ljava/lang/String;
    .locals 1

    const-string v0, "MoveException"

    return-object v0
.end method

.method public final s()I
    .locals 1

    const/16 v0, 0xd

    return v0
.end method

.method public final v()Ljava/lang/String;
    .locals 1

    const-string v0, "move-exception"

    return-object v0
.end method
