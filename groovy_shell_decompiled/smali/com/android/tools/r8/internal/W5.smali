.class public Lcom/android/tools/r8/internal/W5;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static a()Ljava/util/function/BiPredicate;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<S:",
            "Ljava/lang/Object;",
            "T:",
            "Ljava/lang/Object;",
            ">()",
            "Ljava/util/function/BiPredicate<",
            "TS;TT;>;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda0;

    return-object v0
.end method

.method public static synthetic a(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    const/4 p0, 0x0

    return p0
.end method

.method public static b()Ljava/util/function/BiPredicate;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<S:",
            "Ljava/lang/Object;",
            "T:",
            "Ljava/lang/Object;",
            ">()",
            "Ljava/util/function/BiPredicate<",
            "TS;TT;>;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/W5$$ExternalSyntheticLambda1;

    return-object v0
.end method

.method public static synthetic b(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    const/4 p0, 0x1

    return p0
.end method
