.class public abstract Lcom/android/tools/r8/internal/t30;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Lcom/android/tools/r8/internal/TH;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract a(Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/internal/t30;
.end method

.method public abstract a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/internal/t30;
.end method

.method public abstract a(Lcom/android/tools/r8/internal/vI;)Lcom/android/tools/r8/internal/t30;
.end method

.method public b()Lcom/android/tools/r8/internal/OY;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public abstract c()Lcom/android/tools/r8/internal/t30;
.end method

.method public abstract d()Z
.end method

.method public abstract e()Lcom/android/tools/r8/internal/t30;
.end method

.method public abstract f()Lcom/android/tools/r8/internal/t30;
.end method

.method public abstract g()Lcom/android/tools/r8/internal/t30;
.end method
