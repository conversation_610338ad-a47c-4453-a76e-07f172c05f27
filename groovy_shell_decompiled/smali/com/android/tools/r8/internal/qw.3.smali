.class public final synthetic Lcom/android/tools/r8/internal/qw;
.super Lcom/android/tools/r8/internal/kX;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final h:Lcom/android/tools/r8/internal/qw;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/qw;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/qw;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/qw;->h:Lcom/android/tools/r8/internal/qw;

    return-void
.end method

.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/kX;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Integer;Ljava/lang/Object;)V
    .locals 0

    .line 1
    check-cast p2, Lcom/android/tools/r8/internal/NO;

    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    move-result p1

    .line 2
    iput p1, p2, Lcom/android/tools/r8/internal/NO;->a:I

    return-void
.end method

.method public final b(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lcom/android/tools/r8/internal/NO;

    .line 2
    iget p1, p1, Lcom/android/tools/r8/internal/NO;->a:I

    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1
.end method

.method public final b()Ljava/lang/String;
    .locals 1

    const-string v0, "flags"

    return-object v0
.end method

.method public final c()Lcom/android/tools/r8/internal/TK;
    .locals 1

    const-class v0, Lcom/android/tools/r8/internal/NO;

    invoke-static {v0}, Lcom/android/tools/r8/internal/Rb0;->a(Ljava/lang/Class;)Lcom/android/tools/r8/internal/wd;

    move-result-object v0

    return-object v0
.end method

.method public final d()Ljava/lang/String;
    .locals 1

    const-string v0, "getFlags$kotlin_metadata()I"

    return-object v0
.end method
