.class public abstract Lcom/android/tools/r8/internal/al0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/util/Iterator;


# instance fields
.field public b:I

.field public c:Ljava/lang/String;

.field public final d:Ljava/lang/String;

.field public final e:Lcom/android/tools/r8/internal/zb;

.field public final f:Z

.field public g:I

.field public h:I


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/cl0;Ljava/lang/String;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x2

    .line 2
    iput v0, p0, Lcom/android/tools/r8/internal/al0;->b:I

    const/4 v0, 0x0

    .line 3
    iput v0, p0, Lcom/android/tools/r8/internal/al0;->g:I

    .line 4
    iget-object v0, p1, Lcom/android/tools/r8/internal/cl0;->a:Lcom/android/tools/r8/internal/zb;

    .line 5
    iput-object v0, p0, Lcom/android/tools/r8/internal/al0;->e:Lcom/android/tools/r8/internal/zb;

    .line 6
    iget-boolean v0, p1, Lcom/android/tools/r8/internal/cl0;->b:Z

    .line 7
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/al0;->f:Z

    .line 8
    iget p1, p1, Lcom/android/tools/r8/internal/cl0;->d:I

    .line 9
    iput p1, p0, Lcom/android/tools/r8/internal/al0;->h:I

    .line 10
    iput-object p2, p0, Lcom/android/tools/r8/internal/al0;->d:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public abstract a(I)I
.end method

.method public abstract b(I)I
.end method

.method public final hasNext()Z
    .locals 9

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/al0;->b:I

    const/4 v1, 0x4

    if-eq v0, v1, :cond_c

    .line 2
    invoke-static {v0}, Lcom/android/tools/r8/c;->b(I)I

    move-result v0

    const/4 v2, 0x1

    if-eqz v0, :cond_b

    const/4 v3, 0x2

    const/4 v4, 0x0

    if-eq v0, v3, :cond_a

    .line 3
    iput v1, p0, Lcom/android/tools/r8/internal/al0;->b:I

    .line 4
    iget v0, p0, Lcom/android/tools/r8/internal/al0;->g:I

    .line 5
    :cond_0
    :goto_0
    iget v1, p0, Lcom/android/tools/r8/internal/al0;->g:I

    const/4 v3, 0x3

    const/4 v5, -0x1

    if-eq v1, v5, :cond_8

    .line 9
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/al0;->b(I)I

    move-result v1

    if-ne v1, v5, :cond_1

    .line 11
    iget-object v1, p0, Lcom/android/tools/r8/internal/al0;->d:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    .line 12
    iput v5, p0, Lcom/android/tools/r8/internal/al0;->g:I

    goto :goto_1

    .line 15
    :cond_1
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/al0;->a(I)I

    move-result v6

    iput v6, p0, Lcom/android/tools/r8/internal/al0;->g:I

    .line 17
    :goto_1
    iget v6, p0, Lcom/android/tools/r8/internal/al0;->g:I

    if-ne v6, v0, :cond_2

    add-int/lit8 v6, v6, 0x1

    .line 24
    iput v6, p0, Lcom/android/tools/r8/internal/al0;->g:I

    .line 25
    iget-object v1, p0, Lcom/android/tools/r8/internal/al0;->d:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    if-le v6, v1, :cond_0

    .line 26
    iput v5, p0, Lcom/android/tools/r8/internal/al0;->g:I

    goto :goto_0

    :cond_2
    :goto_2
    if-ge v0, v1, :cond_3

    .line 31
    iget-object v6, p0, Lcom/android/tools/r8/internal/al0;->e:Lcom/android/tools/r8/internal/zb;

    iget-object v7, p0, Lcom/android/tools/r8/internal/al0;->d:Ljava/lang/String;

    invoke-virtual {v7, v0}, Ljava/lang/String;->charAt(I)C

    move-result v7

    invoke-virtual {v6, v7}, Lcom/android/tools/r8/internal/Db;->b(C)Z

    move-result v6

    if-eqz v6, :cond_3

    add-int/lit8 v0, v0, 0x1

    goto :goto_2

    :cond_3
    :goto_3
    if-le v1, v0, :cond_4

    .line 34
    iget-object v6, p0, Lcom/android/tools/r8/internal/al0;->e:Lcom/android/tools/r8/internal/zb;

    iget-object v7, p0, Lcom/android/tools/r8/internal/al0;->d:Ljava/lang/String;

    add-int/lit8 v8, v1, -0x1

    invoke-virtual {v7, v8}, Ljava/lang/String;->charAt(I)C

    move-result v7

    invoke-virtual {v6, v7}, Lcom/android/tools/r8/internal/Db;->b(C)Z

    move-result v6

    if-eqz v6, :cond_4

    add-int/lit8 v1, v1, -0x1

    goto :goto_3

    .line 38
    :cond_4
    iget-boolean v6, p0, Lcom/android/tools/r8/internal/al0;->f:Z

    if-eqz v6, :cond_5

    if-ne v0, v1, :cond_5

    .line 40
    iget v0, p0, Lcom/android/tools/r8/internal/al0;->g:I

    goto :goto_0

    .line 44
    :cond_5
    iget v6, p0, Lcom/android/tools/r8/internal/al0;->h:I

    if-ne v6, v2, :cond_6

    .line 48
    iget-object v1, p0, Lcom/android/tools/r8/internal/al0;->d:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    .line 49
    iput v5, p0, Lcom/android/tools/r8/internal/al0;->g:I

    :goto_4
    if-le v1, v0, :cond_7

    .line 51
    iget-object v5, p0, Lcom/android/tools/r8/internal/al0;->e:Lcom/android/tools/r8/internal/zb;

    iget-object v6, p0, Lcom/android/tools/r8/internal/al0;->d:Ljava/lang/String;

    add-int/lit8 v7, v1, -0x1

    invoke-virtual {v6, v7}, Ljava/lang/String;->charAt(I)C

    move-result v6

    invoke-virtual {v5, v6}, Lcom/android/tools/r8/internal/Db;->b(C)Z

    move-result v5

    if-eqz v5, :cond_7

    add-int/lit8 v1, v1, -0x1

    goto :goto_4

    :cond_6
    sub-int/2addr v6, v2

    .line 55
    iput v6, p0, Lcom/android/tools/r8/internal/al0;->h:I

    .line 58
    :cond_7
    iget-object v5, p0, Lcom/android/tools/r8/internal/al0;->d:Ljava/lang/String;

    invoke-virtual {v5, v0, v1}, Ljava/lang/String;->subSequence(II)Ljava/lang/CharSequence;

    move-result-object v0

    invoke-interface {v0}, Ljava/lang/CharSequence;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_5

    .line 59
    :cond_8
    iput v3, p0, Lcom/android/tools/r8/internal/al0;->b:I

    const/4 v0, 0x0

    .line 60
    :goto_5
    iput-object v0, p0, Lcom/android/tools/r8/internal/al0;->c:Ljava/lang/String;

    .line 61
    iget v0, p0, Lcom/android/tools/r8/internal/al0;->b:I

    if-eq v0, v3, :cond_9

    .line 62
    iput v2, p0, Lcom/android/tools/r8/internal/al0;->b:I

    goto :goto_6

    :cond_9
    move v2, v4

    :goto_6
    return v2

    :cond_a
    return v4

    :cond_b
    return v2

    .line 63
    :cond_c
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method

.method public final next()Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/al0;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x2

    .line 4
    iput v0, p0, Lcom/android/tools/r8/internal/al0;->b:I

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/internal/al0;->c:Ljava/lang/String;

    const/4 v1, 0x0

    .line 7
    iput-object v1, p0, Lcom/android/tools/r8/internal/al0;->c:Ljava/lang/String;

    return-object v0

    .line 8
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public final remove()V
    .locals 1

    .line 1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method
