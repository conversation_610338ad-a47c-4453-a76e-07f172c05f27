.class public final Lcom/android/tools/r8/internal/so0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/po0;


# static fields
.field public static final d:Lcom/android/tools/r8/internal/po0;


# instance fields
.field public volatile b:Lcom/android/tools/r8/internal/po0;

.field public c:Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/so0$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/so0$$ExternalSyntheticLambda0;

    sput-object v0, Lcom/android/tools/r8/internal/so0;->d:Lcom/android/tools/r8/internal/po0;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/po0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    iput-object p1, p0, Lcom/android/tools/r8/internal/so0;->b:Lcom/android/tools/r8/internal/po0;

    return-void
.end method

.method public static synthetic a()Ljava/lang/Void;
    .locals 1

    .line 1
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/so0;->b:Lcom/android/tools/r8/internal/po0;

    sget-object v1, Lcom/android/tools/r8/internal/so0;->d:Lcom/android/tools/r8/internal/po0;

    if-eq v0, v1, :cond_1

    .line 2
    monitor-enter p0

    .line 3
    :try_start_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/so0;->b:Lcom/android/tools/r8/internal/po0;

    if-eq v0, v1, :cond_0

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/so0;->b:Lcom/android/tools/r8/internal/po0;

    invoke-interface {v0}, Ljava/util/function/Supplier;->get()Ljava/lang/Object;

    move-result-object v0

    .line 5
    iput-object v0, p0, Lcom/android/tools/r8/internal/so0;->c:Ljava/lang/Object;

    .line 6
    iput-object v1, p0, Lcom/android/tools/r8/internal/so0;->b:Lcom/android/tools/r8/internal/po0;

    .line 7
    monitor-exit p0

    return-object v0

    .line 9
    :cond_0
    monitor-exit p0

    goto :goto_0

    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0

    .line 12
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/so0;->c:Ljava/lang/Object;

    return-object v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/so0;->b:Lcom/android/tools/r8/internal/po0;

    .line 2
    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Suppliers.memoize("

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 3
    sget-object v2, Lcom/android/tools/r8/internal/so0;->d:Lcom/android/tools/r8/internal/po0;

    if-ne v0, v2, :cond_0

    .line 4
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v2, "<supplier that returned "

    invoke-direct {v0, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    iget-object v2, p0, Lcom/android/tools/r8/internal/so0;->c:Ljava/lang/Object;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ">"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 5
    :cond_0
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
