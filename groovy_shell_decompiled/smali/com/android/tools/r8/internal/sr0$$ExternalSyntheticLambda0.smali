.class public final synthetic Lcom/android/tools/r8/internal/sr0$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Fy;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/Fy;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/sr0$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Fy;

    iput-object p2, p0, Lcom/android/tools/r8/internal/sr0$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/Fy;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/sr0$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/Fy;

    iget-object v1, p0, Lcom/android/tools/r8/internal/sr0$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/internal/Fy;

    check-cast p1, Lcom/android/tools/r8/graph/J2;

    invoke-static {v0, v1, p1}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    return-object p1
.end method
