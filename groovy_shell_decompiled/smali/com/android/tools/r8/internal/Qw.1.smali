.class public interface abstract Lcom/android/tools/r8/internal/Qw;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# virtual methods
.method public abstract a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/internal/Gt0;
.end method

.method public a(Lcom/android/tools/r8/internal/E5;Ljava/util/function/Supplier;)Lcom/android/tools/r8/internal/Gt0;
    .locals 1

    .line 1
    invoke-interface {p1}, Lcom/android/tools/r8/internal/UB;->m()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    invoke-interface {p1}, Lcom/android/tools/r8/internal/UB;->s()Lcom/android/tools/r8/internal/Sv;

    move-result-object p1

    .line 3
    iget-object p1, p1, Lcom/android/tools/r8/internal/Sv;->a:Lcom/android/tools/r8/graph/l1;

    .line 4
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/Qw;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/internal/Gt0;

    move-result-object p1

    return-object p1

    .line 6
    :cond_0
    sget-boolean v0, Lcom/android/tools/r8/internal/Pw;->a:Z

    if-nez v0, :cond_2

    invoke-interface {p1}, Lcom/android/tools/r8/internal/UB;->g()Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 7
    :cond_2
    :goto_0
    invoke-interface {p1}, Lcom/android/tools/r8/internal/UB;->r()Lcom/android/tools/r8/internal/pV;

    move-result-object p1

    invoke-interface {p0, p1, p2}, Lcom/android/tools/r8/internal/Qw;->a(Lcom/android/tools/r8/internal/pV;Ljava/util/function/Supplier;)Lcom/android/tools/r8/internal/Gt0;

    move-result-object p1

    return-object p1
.end method

.method public abstract a(Lcom/android/tools/r8/internal/pV;Ljava/util/function/Supplier;)Lcom/android/tools/r8/internal/Gt0;
.end method
