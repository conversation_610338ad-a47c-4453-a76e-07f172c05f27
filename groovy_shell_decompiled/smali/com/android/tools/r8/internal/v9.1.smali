.class public final Lcom/android/tools/r8/internal/v9;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/A8;


# instance fields
.field public final a:Lcom/android/tools/r8/internal/N8;

.field public final b:Lcom/android/tools/r8/graph/G;

.field public final c:Lcom/android/tools/r8/graph/D5;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/G;Lcom/android/tools/r8/graph/D5;)V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->R()Lcom/android/tools/r8/utils/w$q;

    move-result-object v0

    iget-boolean v0, v0, Lcom/android/tools/r8/utils/w$q;->c0:Z

    if-eqz v0, :cond_0

    .line 4
    new-instance v0, Lcom/android/tools/r8/internal/gb;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->V()Lcom/android/tools/r8/graph/y;

    move-result-object p1

    const/4 v1, 0x0

    invoke-direct {v0, p1, v1}, Lcom/android/tools/r8/internal/gb;-><init>(Lcom/android/tools/r8/graph/y;I)V

    goto :goto_0

    .line 5
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/N8;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/N8;-><init>(Lcom/android/tools/r8/graph/y;)V

    :goto_0
    iput-object v0, p0, Lcom/android/tools/r8/internal/v9;->a:Lcom/android/tools/r8/internal/N8;

    .line 6
    iput-object p2, p0, Lcom/android/tools/r8/internal/v9;->b:Lcom/android/tools/r8/graph/G;

    .line 7
    iput-object p3, p0, Lcom/android/tools/r8/internal/v9;->c:Lcom/android/tools/r8/graph/D5;

    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/v9;->b:Lcom/android/tools/r8/graph/G;

    .line 2
    iget v0, v0, Lcom/android/tools/r8/graph/G;->g:I

    return v0
.end method

.method public final a(Lcom/android/tools/r8/graph/J2;)Z
    .locals 1

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/internal/v9;->c:Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E0;->d1()Lcom/android/tools/r8/graph/J2;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/J2;)Z

    move-result p1

    return p1
.end method

.method public final b()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final c()Lcom/android/tools/r8/graph/x2;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/v9;->c:Lcom/android/tools/r8/graph/D5;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    return-object v0
.end method

.method public final d()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/v9;->b:Lcom/android/tools/r8/graph/G;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G;->I0()I

    move-result v0

    return v0
.end method

.method public final e()Lcom/android/tools/r8/internal/N8;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/v9;->a:Lcom/android/tools/r8/internal/N8;

    return-object v0
.end method
