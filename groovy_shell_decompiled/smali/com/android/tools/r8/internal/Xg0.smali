.class public final Lcom/android/tools/r8/internal/Xg0;
.super Lcom/android/tools/r8/internal/sP;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/lx;


# static fields
.field public static final b:Lcom/android/tools/r8/internal/Xg0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Xg0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Xg0;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/Xg0;->b:Lcom/android/tools/r8/internal/Xg0;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x1

    .line 1
    invoke-direct {p0, v0}, Lcom/android/tools/r8/internal/sP;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    check-cast p1, Lcom/android/tools/r8/internal/Kd0;

    const-string v0, "it"

    .line 2
    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/fI;->c(Ljava/lang/Object;Ljava/lang/String;)V

    .line 140
    iget-object p1, p1, Lcom/android/tools/r8/internal/Kd0;->c:Lcom/android/tools/r8/internal/Id0;

    sget-object v0, Lcom/android/tools/r8/internal/Id0;->C:Lcom/android/tools/r8/internal/Id0;

    if-eq p1, v0, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
