.class public abstract Lcom/android/tools/r8/internal/vZ;
.super Lcom/android/tools/r8/internal/Ho0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/internal/Ho0;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/J2;)V

    return-void
.end method

.method public static b(Ljava/util/ArrayList;)V
    .locals 4

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/S9;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/S9;-><init>()V

    .line 2
    new-instance v1, Lcom/android/tools/r8/internal/X9;

    sget-object v2, Lcom/android/tools/r8/internal/It0;->b:Lcom/android/tools/r8/internal/It0;

    const/4 v3, 0x0

    invoke-direct {v1, v2, v3}, Lcom/android/tools/r8/internal/X9;-><init>(Lcom/android/tools/r8/internal/It0;I)V

    invoke-virtual {p0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 3
    new-instance v1, Lcom/android/tools/r8/internal/z9;

    sget-object v3, Lcom/android/tools/r8/internal/IA;->g:Lcom/android/tools/r8/internal/IA;

    invoke-direct {v1, v3, v2, v0}, Lcom/android/tools/r8/internal/z9;-><init>(Lcom/android/tools/r8/internal/IA;Lcom/android/tools/r8/internal/It0;Lcom/android/tools/r8/internal/S9;)V

    invoke-virtual {p0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 4
    new-instance v1, Lcom/android/tools/r8/internal/h9;

    invoke-direct {v1}, Lcom/android/tools/r8/internal/h9;-><init>()V

    invoke-virtual {p0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 5
    new-instance v1, Lcom/android/tools/r8/internal/Ha;

    invoke-direct {v1, v2}, Lcom/android/tools/r8/internal/Ha;-><init>(Lcom/android/tools/r8/internal/It0;)V

    invoke-virtual {p0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 6
    invoke-virtual {p0, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    return-void
.end method
