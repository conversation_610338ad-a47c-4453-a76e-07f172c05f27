.class public abstract Lcom/android/tools/r8/internal/uy;
.super Lcom/android/tools/r8/internal/J0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/io/Serializable;


# static fields
.field protected static alwaysUseFieldBuilders:Z = false

.field private static final serialVersionUID:J = 0x1L


# instance fields
.field protected unknownFields:Lcom/android/tools/r8/internal/vs0;


# direct methods
.method protected constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/J0;-><init>()V

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/vs0;->c:Lcom/android/tools/r8/internal/vs0;

    .line 3
    iput-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    return-void
.end method

.method protected constructor <init>(Lcom/android/tools/r8/internal/dy;)V
    .locals 0

    .line 4
    invoke-direct {p0}, Lcom/android/tools/r8/internal/J0;-><init>()V

    .line 5
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/dy;->getUnknownFields()Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    return-void
.end method

.method private a(Z)Ljava/util/TreeMap;
    .locals 6

    .line 1
    new-instance v0, Ljava/util/TreeMap;

    invoke-direct {v0}, Ljava/util/TreeMap;-><init>()V

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v1

    .line 4
    iget-object v1, v1, Lcom/android/tools/r8/internal/sy;->a:Lcom/android/tools/r8/internal/Ok;

    .line 5
    iget-object v1, v1, Lcom/android/tools/r8/internal/Ok;->g:[Lcom/android/tools/r8/internal/al;

    .line 6
    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    const/4 v2, 0x0

    .line 7
    :goto_0
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v3

    if-ge v2, v3, :cond_6

    .line 8
    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/al;

    .line 9
    iget-object v4, v3, Lcom/android/tools/r8/internal/al;->k:Lcom/android/tools/r8/internal/el;

    if-eqz v4, :cond_1

    .line 10
    iget v3, v4, Lcom/android/tools/r8/internal/el;->g:I

    add-int/lit8 v3, v3, -0x1

    add-int/2addr v2, v3

    .line 11
    invoke-virtual {p0, v4}, Lcom/android/tools/r8/internal/uy;->hasOneof(Lcom/android/tools/r8/internal/el;)Z

    move-result v3

    if-nez v3, :cond_0

    goto :goto_2

    .line 16
    :cond_0
    invoke-virtual {p0, v4}, Lcom/android/tools/r8/internal/uy;->getOneofFieldDescriptor(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/al;

    move-result-object v3

    goto :goto_1

    .line 19
    :cond_1
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/al;->l()Z

    move-result v4

    if-eqz v4, :cond_2

    .line 20
    invoke-virtual {p0, v3}, Lcom/android/tools/r8/internal/uy;->getField(Lcom/android/tools/r8/internal/al;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/List;

    .line 21
    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    move-result v5

    if-nez v5, :cond_5

    .line 22
    invoke-virtual {v0, v3, v4}, Ljava/util/TreeMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    .line 26
    :cond_2
    invoke-virtual {p0, v3}, Lcom/android/tools/r8/internal/uy;->hasField(Lcom/android/tools/r8/internal/al;)Z

    move-result v4

    if-nez v4, :cond_3

    goto :goto_2

    :cond_3
    :goto_1
    if-eqz p1, :cond_4

    .line 27
    iget-object v4, v3, Lcom/android/tools/r8/internal/al;->h:Lcom/android/tools/r8/internal/Zk;

    .line 28
    iget-object v4, v4, Lcom/android/tools/r8/internal/Zk;->b:Lcom/android/tools/r8/internal/Yk;

    .line 29
    sget-object v5, Lcom/android/tools/r8/internal/Yk;->h:Lcom/android/tools/r8/internal/Yk;

    if-ne v4, v5, :cond_4

    .line 30
    invoke-virtual {p0, v3}, Lcom/android/tools/r8/internal/uy;->getFieldRaw(Lcom/android/tools/r8/internal/al;)Ljava/lang/Object;

    move-result-object v4

    invoke-virtual {v0, v3, v4}, Ljava/util/TreeMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    .line 32
    :cond_4
    invoke-virtual {p0, v3}, Lcom/android/tools/r8/internal/uy;->getField(Lcom/android/tools/r8/internal/al;)Ljava/lang/Object;

    move-result-object v4

    invoke-virtual {v0, v3, v4}, Ljava/util/TreeMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_5
    :goto_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_6
    return-object v0
.end method

.method static access$1000(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;
    .locals 3

    .line 1
    :try_start_0
    invoke-virtual {p0, p1, p2}, Ljava/lang/Class;->getMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p2

    .line 3
    new-instance v0, Ljava/lang/RuntimeException;

    new-instance v1, Ljava/lang/StringBuilder;

    const-string v2, "Generated message class \""

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 4
    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v1, "\" missing method \""

    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p1, "\"."

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0, p2}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0
.end method

.method static access$1100(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    :try_start_0
    invoke-virtual {p0, p1, p2}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    .line 7
    invoke-virtual {p0}, Ljava/lang/reflect/InvocationTargetException;->getCause()Ljava/lang/Throwable;

    move-result-object p0

    .line 8
    instance-of p1, p0, Ljava/lang/RuntimeException;

    if-nez p1, :cond_1

    .line 10
    instance-of p1, p0, Ljava/lang/Error;

    if-eqz p1, :cond_0

    .line 11
    check-cast p0, Ljava/lang/Error;

    throw p0

    .line 13
    :cond_0
    new-instance p1, Ljava/lang/RuntimeException;

    const-string p2, "Unexpected exception thrown by generated accessor method."

    invoke-direct {p1, p2, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw p1

    .line 14
    :cond_1
    check-cast p0, Ljava/lang/RuntimeException;

    throw p0

    :catch_1
    move-exception p0

    .line 15
    new-instance p1, Ljava/lang/RuntimeException;

    const-string p2, "Couldn\'t use Java reflection to implement protocol message reflection."

    invoke-direct {p1, p2, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw p1
.end method

.method static access$500(Lcom/android/tools/r8/internal/Eu;)Lcom/android/tools/r8/internal/Du;
    .locals 0

    const/4 p0, 0x0

    .line 1
    throw p0
.end method

.method static synthetic access$800(Lcom/android/tools/r8/internal/uy;Z)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/uy;->a(Z)Ljava/util/TreeMap;

    move-result-object p0

    return-object p0
.end method

.method protected static canUseUnsafe()Z
    .locals 1

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/gt0;->e:Z

    if-eqz v0, :cond_0

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/gt0;->d:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method protected static computeStringSize(ILjava/lang/Object;)I
    .locals 1

    .line 1
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 2
    check-cast p1, Ljava/lang/String;

    .line 3
    invoke-static {p0}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result p0

    invoke-static {p1}, Lcom/android/tools/r8/internal/je;->a(Ljava/lang/String;)I

    move-result p1

    :goto_0
    add-int/2addr p1, p0

    return p1

    .line 4
    :cond_0
    check-cast p1, Lcom/android/tools/r8/internal/Z7;

    .line 5
    invoke-static {p0}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result p0

    invoke-static {p1}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/Z7;)I

    move-result p1

    goto :goto_0
.end method

.method protected static computeStringSizeNoTag(Ljava/lang/Object;)I
    .locals 1

    .line 1
    instance-of v0, p0, Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 2
    check-cast p0, Ljava/lang/String;

    invoke-static {p0}, Lcom/android/tools/r8/internal/je;->a(Ljava/lang/String;)I

    move-result p0

    return p0

    .line 4
    :cond_0
    check-cast p0, Lcom/android/tools/r8/internal/Z7;

    invoke-static {p0}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/Z7;)I

    move-result p0

    return p0
.end method

.method protected static emptyBooleanList()Lcom/android/tools/r8/internal/wH;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/E6;->e:Lcom/android/tools/r8/internal/E6;

    return-object v0
.end method

.method protected static emptyDoubleList()Lcom/android/tools/r8/internal/xH;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/ps;->e:Lcom/android/tools/r8/internal/ps;

    return-object v0
.end method

.method protected static emptyFloatList()Lcom/android/tools/r8/internal/BH;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Dw;->e:Lcom/android/tools/r8/internal/Dw;

    return-object v0
.end method

.method protected static emptyIntList()Lcom/android/tools/r8/internal/CH;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/tG;->e:Lcom/android/tools/r8/internal/tG;

    return-object v0
.end method

.method protected static emptyLongList()Lcom/android/tools/r8/internal/FH;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/AS;->e:Lcom/android/tools/r8/internal/AS;

    return-object v0
.end method

.method static enableAlwaysUseFieldBuildersForTesting()V
    .locals 1

    const/4 v0, 0x1

    .line 1
    invoke-static {v0}, Lcom/android/tools/r8/internal/uy;->setAlwaysUseFieldBuildersForTesting(Z)V

    return-void
.end method

.method protected static isStringEmpty(Ljava/lang/Object;)Z
    .locals 1

    .line 1
    instance-of v0, p0, Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 2
    check-cast p0, Ljava/lang/String;

    invoke-virtual {p0}, Ljava/lang/String;->isEmpty()Z

    move-result p0

    return p0

    .line 4
    :cond_0
    check-cast p0, Lcom/android/tools/r8/internal/Z7;

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Z7;->size()I

    move-result p0

    if-nez p0, :cond_1

    const/4 p0, 0x1

    goto :goto_0

    :cond_1
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method protected static mutableCopy(Lcom/android/tools/r8/internal/BH;)Lcom/android/tools/r8/internal/BH;
    .locals 1

    .line 7
    check-cast p0, Lcom/android/tools/r8/internal/Dw;

    .line 8
    iget v0, p0, Lcom/android/tools/r8/internal/Dw;->d:I

    if-nez v0, :cond_0

    const/16 v0, 0xa

    goto :goto_0

    :cond_0
    mul-int/lit8 v0, v0, 0x2

    .line 9
    :goto_0
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/Dw;->j(I)Lcom/android/tools/r8/internal/Dw;

    move-result-object p0

    return-object p0
.end method

.method protected static mutableCopy(Lcom/android/tools/r8/internal/CH;)Lcom/android/tools/r8/internal/CH;
    .locals 1

    .line 1
    check-cast p0, Lcom/android/tools/r8/internal/tG;

    .line 2
    iget v0, p0, Lcom/android/tools/r8/internal/tG;->d:I

    if-nez v0, :cond_0

    const/16 v0, 0xa

    goto :goto_0

    :cond_0
    mul-int/lit8 v0, v0, 0x2

    .line 3
    :goto_0
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/tG;->l(I)Lcom/android/tools/r8/internal/tG;

    move-result-object p0

    return-object p0
.end method

.method protected static mutableCopy(Lcom/android/tools/r8/internal/FH;)Lcom/android/tools/r8/internal/FH;
    .locals 1

    .line 4
    check-cast p0, Lcom/android/tools/r8/internal/AS;

    .line 5
    iget v0, p0, Lcom/android/tools/r8/internal/AS;->d:I

    if-nez v0, :cond_0

    const/16 v0, 0xa

    goto :goto_0

    :cond_0
    mul-int/lit8 v0, v0, 0x2

    .line 6
    :goto_0
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/AS;->k(I)Lcom/android/tools/r8/internal/AS;

    move-result-object p0

    return-object p0
.end method

.method protected static mutableCopy(Lcom/android/tools/r8/internal/wH;)Lcom/android/tools/r8/internal/wH;
    .locals 1

    .line 13
    check-cast p0, Lcom/android/tools/r8/internal/E6;

    .line 14
    iget v0, p0, Lcom/android/tools/r8/internal/E6;->d:I

    if-nez v0, :cond_0

    const/16 v0, 0xa

    goto :goto_0

    :cond_0
    mul-int/lit8 v0, v0, 0x2

    .line 15
    :goto_0
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/E6;->j(I)Lcom/android/tools/r8/internal/E6;

    move-result-object p0

    return-object p0
.end method

.method protected static mutableCopy(Lcom/android/tools/r8/internal/xH;)Lcom/android/tools/r8/internal/xH;
    .locals 1

    .line 10
    check-cast p0, Lcom/android/tools/r8/internal/ps;

    .line 11
    iget v0, p0, Lcom/android/tools/r8/internal/ps;->d:I

    if-nez v0, :cond_0

    const/16 v0, 0xa

    goto :goto_0

    :cond_0
    mul-int/lit8 v0, v0, 0x2

    .line 12
    :goto_0
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ps;->j(I)Lcom/android/tools/r8/internal/ps;

    move-result-object p0

    return-object p0
.end method

.method protected static newBooleanList()Lcom/android/tools/r8/internal/wH;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/E6;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/E6;-><init>()V

    return-object v0
.end method

.method protected static newDoubleList()Lcom/android/tools/r8/internal/xH;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/ps;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/ps;-><init>()V

    return-object v0
.end method

.method protected static newFloatList()Lcom/android/tools/r8/internal/BH;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Dw;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/Dw;-><init>()V

    return-object v0
.end method

.method protected static newIntList()Lcom/android/tools/r8/internal/CH;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/tG;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/tG;-><init>()V

    return-object v0
.end method

.method protected static newLongList()Lcom/android/tools/r8/internal/FH;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/AS;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/AS;-><init>()V

    return-object v0
.end method

.method protected static parseDelimitedWithIOException(Lcom/android/tools/r8/internal/z30;Ljava/io/InputStream;)Lcom/android/tools/r8/internal/vU;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<M::",
            "Lcom/android/tools/r8/internal/vU;",
            ">(",
            "Lcom/android/tools/r8/internal/z30;",
            "Ljava/io/InputStream;",
            ")TM;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    :try_start_0
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/z30;->parseDelimitedFrom(Ljava/io/InputStream;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/internal/vU;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p0

    throw p0
.end method

.method protected static parseDelimitedWithIOException(Lcom/android/tools/r8/internal/z30;Ljava/io/InputStream;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vU;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<M::",
            "Lcom/android/tools/r8/internal/vU;",
            ">(",
            "Lcom/android/tools/r8/internal/z30;",
            "Ljava/io/InputStream;",
            "Lcom/android/tools/r8/internal/Lu;",
            ")TM;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 4
    :try_start_0
    invoke-interface {p0, p1, p2}, Lcom/android/tools/r8/internal/z30;->parseDelimitedFrom(Ljava/io/InputStream;Lcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/internal/vU;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p0

    throw p0
.end method

.method protected static parseWithIOException(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/ce;)Lcom/android/tools/r8/internal/vU;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<M::",
            "Lcom/android/tools/r8/internal/vU;",
            ">(",
            "Lcom/android/tools/r8/internal/z30;",
            "Lcom/android/tools/r8/internal/ce;",
            ")TM;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 7
    :try_start_0
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/z30;->parseFrom(Lcom/android/tools/r8/internal/ce;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/internal/vU;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    .line 9
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p0

    throw p0
.end method

.method protected static parseWithIOException(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vU;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<M::",
            "Lcom/android/tools/r8/internal/vU;",
            ">(",
            "Lcom/android/tools/r8/internal/z30;",
            "Lcom/android/tools/r8/internal/ce;",
            "Lcom/android/tools/r8/internal/Lu;",
            ")TM;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 10
    :try_start_0
    invoke-interface {p0, p1, p2}, Lcom/android/tools/r8/internal/z30;->parseFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/internal/vU;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    .line 12
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p0

    throw p0
.end method

.method protected static parseWithIOException(Lcom/android/tools/r8/internal/z30;Ljava/io/InputStream;)Lcom/android/tools/r8/internal/vU;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<M::",
            "Lcom/android/tools/r8/internal/vU;",
            ">(",
            "Lcom/android/tools/r8/internal/z30;",
            "Ljava/io/InputStream;",
            ")TM;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    :try_start_0
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/z30;->parseFrom(Ljava/io/InputStream;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/internal/vU;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p0

    throw p0
.end method

.method protected static parseWithIOException(Lcom/android/tools/r8/internal/z30;Ljava/io/InputStream;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vU;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<M::",
            "Lcom/android/tools/r8/internal/vU;",
            ">(",
            "Lcom/android/tools/r8/internal/z30;",
            "Ljava/io/InputStream;",
            "Lcom/android/tools/r8/internal/Lu;",
            ")TM;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 4
    :try_start_0
    invoke-interface {p0, p1, p2}, Lcom/android/tools/r8/internal/z30;->parseFrom(Ljava/io/InputStream;Lcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/internal/vU;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p0

    throw p0
.end method

.method protected static serializeBooleanMapTo(Lcom/android/tools/r8/internal/je;Lcom/android/tools/r8/internal/gT;Lcom/android/tools/r8/internal/fT;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<V:",
            "Ljava/lang/Object;",
            ">(",
            "Lcom/android/tools/r8/internal/je;",
            "Lcom/android/tools/r8/internal/gT;",
            "Lcom/android/tools/r8/internal/fT;",
            "I)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 p0, 0x0

    .line 1
    throw p0
.end method

.method protected static serializeIntegerMapTo(Lcom/android/tools/r8/internal/je;Lcom/android/tools/r8/internal/gT;Lcom/android/tools/r8/internal/fT;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<V:",
            "Ljava/lang/Object;",
            ">(",
            "Lcom/android/tools/r8/internal/je;",
            "Lcom/android/tools/r8/internal/gT;",
            "Lcom/android/tools/r8/internal/fT;",
            "I)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 p0, 0x0

    .line 1
    throw p0
.end method

.method protected static serializeLongMapTo(Lcom/android/tools/r8/internal/je;Lcom/android/tools/r8/internal/gT;Lcom/android/tools/r8/internal/fT;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<V:",
            "Ljava/lang/Object;",
            ">(",
            "Lcom/android/tools/r8/internal/je;",
            "Lcom/android/tools/r8/internal/gT;",
            "Lcom/android/tools/r8/internal/fT;",
            "I)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 p0, 0x0

    .line 1
    throw p0
.end method

.method protected static serializeStringMapTo(Lcom/android/tools/r8/internal/je;Lcom/android/tools/r8/internal/gT;Lcom/android/tools/r8/internal/fT;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<V:",
            "Ljava/lang/Object;",
            ">(",
            "Lcom/android/tools/r8/internal/je;",
            "Lcom/android/tools/r8/internal/gT;",
            "Lcom/android/tools/r8/internal/fT;",
            "I)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 p0, 0x0

    .line 1
    throw p0
.end method

.method static setAlwaysUseFieldBuildersForTesting(Z)V
    .locals 0

    .line 1
    sput-boolean p0, Lcom/android/tools/r8/internal/uy;->alwaysUseFieldBuilders:Z

    return-void
.end method

.method protected static writeString(Lcom/android/tools/r8/internal/je;ILjava/lang/Object;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    instance-of v0, p2, Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 2
    check-cast p2, Ljava/lang/String;

    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/je;->a(ILjava/lang/String;)V

    goto :goto_0

    .line 4
    :cond_0
    check-cast p2, Lcom/android/tools/r8/internal/Z7;

    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/Z7;)V

    :goto_0
    return-void
.end method

.method protected static writeStringNoTag(Lcom/android/tools/r8/internal/je;Ljava/lang/Object;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 2
    check-cast p1, Ljava/lang/String;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/je;->b(Ljava/lang/String;)V

    goto :goto_0

    .line 4
    :cond_0
    check-cast p1, Lcom/android/tools/r8/internal/Z7;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/je;->b(Lcom/android/tools/r8/internal/Z7;)V

    :goto_0
    return-void
.end method


# virtual methods
.method public getAllFields()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Lcom/android/tools/r8/internal/al;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 1
    invoke-direct {p0, v0}, Lcom/android/tools/r8/internal/uy;->a(Z)Ljava/util/TreeMap;

    move-result-object v0

    .line 2
    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method getAllFieldsRaw()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Lcom/android/tools/r8/internal/al;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x1

    .line 1
    invoke-direct {p0, v0}, Lcom/android/tools/r8/internal/uy;->a(Z)Ljava/util/TreeMap;

    move-result-object v0

    .line 2
    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method public getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/sy;->a:Lcom/android/tools/r8/internal/Ok;

    return-object v0
.end method

.method public getField(Lcom/android/tools/r8/internal/al;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/sy;->a(Lcom/android/tools/r8/internal/sy;Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/hy;

    move-result-object p1

    invoke-interface {p1, p0}, Lcom/android/tools/r8/internal/hy;->d(Lcom/android/tools/r8/internal/uy;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method getFieldRaw(Lcom/android/tools/r8/internal/al;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/sy;->a(Lcom/android/tools/r8/internal/sy;Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/hy;

    move-result-object p1

    invoke-interface {p1, p0}, Lcom/android/tools/r8/internal/hy;->a(Lcom/android/tools/r8/internal/uy;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getOneofFieldDescriptor(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/al;
    .locals 6

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/sy;->a(Lcom/android/tools/r8/internal/sy;Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/iy;

    move-result-object p1

    .line 2
    iget-object v0, p1, Lcom/android/tools/r8/internal/iy;->e:Lcom/android/tools/r8/internal/al;

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/uy;->hasField(Lcom/android/tools/r8/internal/al;)Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object p1, p1, Lcom/android/tools/r8/internal/iy;->e:Lcom/android/tools/r8/internal/al;

    goto :goto_1

    .line 5
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/internal/iy;->b:Ljava/lang/reflect/Method;

    const/4 v1, 0x0

    new-array v2, v1, [Ljava/lang/Object;

    invoke-static {v0, p0, v2}, Lcom/android/tools/r8/internal/uy;->access$1100(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/zH;

    invoke-interface {v0}, Lcom/android/tools/r8/internal/zH;->getNumber()I

    move-result v0

    if-lez v0, :cond_3

    .line 7
    iget-object p1, p1, Lcom/android/tools/r8/internal/iy;->a:Lcom/android/tools/r8/internal/Ok;

    .line 8
    iget-object p1, p1, Lcom/android/tools/r8/internal/Ok;->h:[Lcom/android/tools/r8/internal/al;

    .line 9
    array-length v2, p1

    .line 10
    sget-object v3, Lcom/android/tools/r8/internal/al;->n:[Lcom/android/tools/r8/internal/Qu0;

    .line 11
    sget-object v3, Lcom/android/tools/r8/internal/gl;->a:Ljava/util/logging/Logger;

    add-int/lit8 v2, v2, -0x1

    :goto_0
    if-gt v1, v2, :cond_3

    add-int v3, v1, v2

    .line 12
    div-int/lit8 v3, v3, 0x2

    .line 13
    aget-object v4, p1, v3

    .line 14
    iget-object v5, v4, Lcom/android/tools/r8/internal/al;->c:Lcom/android/tools/r8/internal/Qj;

    .line 15
    iget v5, v5, Lcom/android/tools/r8/internal/Qj;->d:I

    if-ge v0, v5, :cond_1

    add-int/lit8 v2, v3, -0x1

    goto :goto_0

    :cond_1
    if-le v0, v5, :cond_2

    add-int/lit8 v1, v3, 0x1

    goto :goto_0

    :cond_2
    move-object p1, v4

    goto :goto_1

    :cond_3
    const/4 p1, 0x0

    :goto_1
    return-object p1
.end method

.method public getRepeatedField(Lcom/android/tools/r8/internal/al;I)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/sy;->a(Lcom/android/tools/r8/internal/sy;Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/hy;

    move-result-object p1

    .line 2
    invoke-interface {p1, p2, p0}, Lcom/android/tools/r8/internal/hy;->a(ILcom/android/tools/r8/internal/uy;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getRepeatedFieldCount(Lcom/android/tools/r8/internal/al;)I
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/sy;->a(Lcom/android/tools/r8/internal/sy;Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/hy;

    move-result-object p1

    .line 2
    invoke-interface {p1, p0}, Lcom/android/tools/r8/internal/hy;->c(Lcom/android/tools/r8/internal/uy;)I

    move-result p1

    return p1
.end method

.method public hasField(Lcom/android/tools/r8/internal/al;)Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/sy;->a(Lcom/android/tools/r8/internal/sy;Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/hy;

    move-result-object p1

    invoke-interface {p1, p0}, Lcom/android/tools/r8/internal/hy;->b(Lcom/android/tools/r8/internal/uy;)Z

    move-result p1

    return p1
.end method

.method public hasOneof(Lcom/android/tools/r8/internal/el;)Z
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/android/tools/r8/internal/sy;->a(Lcom/android/tools/r8/internal/sy;Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/iy;

    move-result-object p1

    .line 2
    iget-object v0, p1, Lcom/android/tools/r8/internal/iy;->e:Lcom/android/tools/r8/internal/al;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/uy;->hasField(Lcom/android/tools/r8/internal/al;)Z

    move-result v1

    goto :goto_0

    .line 5
    :cond_0
    iget-object p1, p1, Lcom/android/tools/r8/internal/iy;->b:Ljava/lang/reflect/Method;

    new-array v0, v1, [Ljava/lang/Object;

    invoke-static {p1, p0, v0}, Lcom/android/tools/r8/internal/uy;->access$1100(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/zH;

    invoke-interface {p1}, Lcom/android/tools/r8/internal/zH;->getNumber()I

    move-result p1

    if-nez p1, :cond_1

    goto :goto_0

    :cond_1
    const/4 v1, 0x1

    :goto_0
    return v1
.end method

.method protected abstract internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
.end method

.method protected internalGetMapField(I)Lcom/android/tools/r8/internal/gT;
    .locals 2

    .line 1
    new-instance p1, Ljava/lang/RuntimeException;

    .line 2
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "No map fields found in "

    invoke-virtual {v1, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method protected makeExtensionsImmutable()V
    .locals 0

    return-void
.end method

.method protected mergeFromAndMakeImmutableInternal(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 1
    sget-object p2, Lcom/android/tools/r8/internal/T80;->c:Lcom/android/tools/r8/internal/T80;

    .line 2
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    .line 4
    sget-object v1, Lcom/android/tools/r8/internal/HH;->a:Ljava/nio/charset/Charset;

    .line 6
    iget-object v1, p2, Lcom/android/tools/r8/internal/T80;->b:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v1, v0}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/Di0;

    const/4 v2, 0x0

    if-nez v1, :cond_2

    .line 8
    iget-object p1, p2, Lcom/android/tools/r8/internal/T80;->a:Lcom/android/tools/r8/internal/cT;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 9
    sget-object p2, Lcom/android/tools/r8/internal/Ei0;->a:Ljava/lang/Class;

    .line 10
    const-class p2, Lcom/android/tools/r8/internal/Rx;

    invoke-virtual {p2, v0}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p2

    if-nez p2, :cond_1

    sget-object p2, Lcom/android/tools/r8/internal/Ei0;->a:Ljava/lang/Class;

    if-eqz p2, :cond_1

    .line 12
    invoke-virtual {p2, v0}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result p2

    if-eqz p2, :cond_0

    goto :goto_0

    .line 13
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Message classes must extend GeneratedMessageV3 or GeneratedMessageLite"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 14
    :cond_1
    :goto_0
    iget-object p1, p1, Lcom/android/tools/r8/internal/cT;->a:Lcom/android/tools/r8/internal/bT;

    invoke-virtual {p1, v0}, Lcom/android/tools/r8/internal/bT;->a(Ljava/lang/Class;)V

    .line 17
    throw v2

    .line 18
    :cond_2
    :try_start_0
    iget-object p2, p1, Lcom/android/tools/r8/internal/ce;->b:Lcom/android/tools/r8/internal/de;

    if-eqz p2, :cond_3

    goto :goto_1

    .line 21
    :cond_3
    new-instance p2, Lcom/android/tools/r8/internal/de;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/de;-><init>(Lcom/android/tools/r8/internal/ce;)V

    .line 22
    :goto_1
    throw v2
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    move-exception p1

    .line 23
    new-instance p2, Lcom/android/tools/r8/internal/lI;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/lI;-><init>(Ljava/io/IOException;)V

    .line 24
    iput-object p0, p2, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 25
    throw p2

    :catch_1
    move-exception p1

    .line 26
    iput-object p0, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 27
    throw p1
.end method

.method protected newBuilderForType(Lcom/android/tools/r8/internal/I0;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/ay;

    invoke-direct {v0, p1}, Lcom/android/tools/r8/internal/ay;-><init>(Lcom/android/tools/r8/internal/I0;)V

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/uy;->newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/internal/uU;

    move-result-object p1

    return-object p1
.end method

.method protected abstract newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/internal/uU;
.end method

.method protected parseUnknownField(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/qs0;Lcom/android/tools/r8/internal/Lu;I)Z
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 4
    invoke-virtual {p2, p4, p1}, Lcom/android/tools/r8/internal/qs0;->a(ILcom/android/tools/r8/internal/ce;)Z

    move-result p1

    return p1
.end method

.method protected parseUnknownFieldProto3(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/qs0;Lcom/android/tools/r8/internal/Lu;I)Z
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lcom/android/tools/r8/internal/uy;->parseUnknownField(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/qs0;Lcom/android/tools/r8/internal/Lu;I)Z

    move-result p1

    return p1
.end method

.method protected writeReplace()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/ObjectStreamException;
        }
    .end annotation

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Ux;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/Ux;-><init>(Lcom/android/tools/r8/internal/uy;)V

    return-object v0
.end method
