.class public final Lcom/android/tools/r8/internal/W2;
.super Lcom/android/tools/r8/internal/X2;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final a:Lcom/android/tools/r8/internal/W2;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/W2;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/W2;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/W2;->a:Lcom/android/tools/r8/internal/W2;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/X2;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/ST;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/ST;->c:Lcom/android/tools/r8/internal/ST;

    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/shaking/M;)V
    .locals 0

    return-void
.end method

.method public final b(Lcom/android/tools/r8/shaking/M;)V
    .locals 0

    return-void
.end method
