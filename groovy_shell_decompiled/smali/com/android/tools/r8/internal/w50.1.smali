.class public final Lcom/android/tools/r8/internal/w50;
.super Lcom/android/tools/r8/internal/vV;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic h:Z = true


# instance fields
.field public final c:Lcom/android/tools/r8/graph/y;

.field public final d:Lcom/android/tools/r8/internal/q8;

.field public final e:Lcom/android/tools/r8/internal/uV;

.field public final f:Ljava/util/ArrayDeque;

.field public g:Lcom/android/tools/r8/internal/ff;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/k8;Lcom/android/tools/r8/internal/uV;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/vV;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/w50;->c:Lcom/android/tools/r8/graph/y;

    .line 3
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/utils/w;->g0()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 5
    new-instance v0, Lcom/android/tools/r8/internal/o8;

    invoke-direct {v0, p1, p2, p0}, Lcom/android/tools/r8/internal/o8;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/k8;Lcom/android/tools/r8/internal/vV;)V

    goto :goto_0

    .line 6
    :cond_0
    sget-object v0, Lcom/android/tools/r8/internal/p8;->a:Lcom/android/tools/r8/internal/p8;

    .line 7
    :goto_0
    iput-object v0, p0, Lcom/android/tools/r8/internal/w50;->d:Lcom/android/tools/r8/internal/q8;

    .line 8
    iput-object p3, p0, Lcom/android/tools/r8/internal/w50;->e:Lcom/android/tools/r8/internal/uV;

    .line 9
    new-instance p3, Ljava/util/ArrayDeque;

    invoke-direct {p3}, Ljava/util/ArrayDeque;-><init>()V

    .line 10
    :goto_1
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/l8;->b()Z

    move-result v0

    if-nez v0, :cond_1

    .line 11
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/k8;->c()Lcom/android/tools/r8/internal/l60;

    move-result-object v0

    invoke-virtual {p3, v0}, Ljava/util/ArrayDeque;->addLast(Ljava/lang/Object;)V

    goto :goto_1

    .line 13
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->R()Lcom/android/tools/r8/utils/w$q;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/utils/w$q;->H:Ljava/util/function/Consumer;

    invoke-interface {p1, p3}, Ljava/util/function/Consumer;->accept(Ljava/lang/Object;)V

    .line 14
    iput-object p3, p0, Lcom/android/tools/r8/internal/w50;->f:Ljava/util/ArrayDeque;

    return-void
.end method


# virtual methods
.method public final synthetic a(Lcom/android/tools/r8/internal/t50;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/internal/Gp0;
    .locals 1

    .line 45
    iget-object v0, p0, Lcom/android/tools/r8/internal/w50;->g:Lcom/android/tools/r8/internal/ff;

    .line 47
    invoke-virtual {v0, p2}, Lcom/android/tools/r8/internal/ff;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/internal/ef;

    move-result-object v0

    .line 48
    invoke-interface {p1, p2, v0}, Lcom/android/tools/r8/internal/t50;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/internal/Gp0;

    move-result-object p1

    .line 50
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/Gp0;->b()V

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/t50;Lcom/android/tools/r8/internal/v50;Lcom/android/tools/r8/internal/u50;Lcom/android/tools/r8/internal/Gp0;Ljava/util/concurrent/ExecutorService;)V
    .locals 3

    .line 1
    invoke-virtual {p4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    invoke-static {p5}, Lcom/android/tools/r8/internal/ep0;->a(Ljava/util/concurrent/ExecutorService;)I

    move-result v0

    const-string v1, "primary-processor"

    invoke-virtual {p4, v0, v1}, Lcom/android/tools/r8/internal/Gp0;->a(ILjava/lang/String;)Lcom/android/tools/r8/internal/Ep0;

    move-result-object p4

    .line 3
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/internal/w50;->f:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_4

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/w50;->f:Ljava/util/ArrayDeque;

    invoke-virtual {v0}, Ljava/util/ArrayDeque;->removeFirst()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/l60;

    iput-object v0, p0, Lcom/android/tools/r8/internal/vV;->a:Lcom/android/tools/r8/internal/l60;

    .line 5
    sget-boolean v1, Lcom/android/tools/r8/internal/w50;->h:Z

    if-nez v1, :cond_1

    .line 6
    iget-object v0, v0, Lcom/android/tools/r8/internal/hn;->b:Ljava/util/Map;

    .line 7
    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_1

    .line 8
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_1
    if-nez v1, :cond_3

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/internal/vV;->b:Lcom/android/tools/r8/internal/i60;

    .line 10
    iget-object v0, v0, Lcom/android/tools/r8/internal/hn;->b:Ljava/util/Map;

    .line 11
    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_2

    .line 12
    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 14
    :cond_3
    :goto_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/w50;->c:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/y;->n()Lcom/android/tools/r8/internal/ff;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/w50;->g:Lcom/android/tools/r8/internal/ff;

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/internal/vV;->a:Lcom/android/tools/r8/internal/l60;

    invoke-interface {p2, v0}, Lcom/android/tools/r8/internal/v50;->a(Lcom/android/tools/r8/internal/l60;)V

    .line 16
    iget-object v0, p0, Lcom/android/tools/r8/internal/vV;->a:Lcom/android/tools/r8/internal/l60;

    new-instance v1, Lcom/android/tools/r8/internal/w50$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0, p1}, Lcom/android/tools/r8/internal/w50$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/w50;Lcom/android/tools/r8/internal/t50;)V

    iget-object v2, p0, Lcom/android/tools/r8/internal/w50;->c:Lcom/android/tools/r8/graph/y;

    .line 26
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/utils/w;->O()Lcom/android/tools/r8/threading/ThreadingModule;

    move-result-object v2

    .line 27
    invoke-static {v0, v1, v2, p5}, Lcom/android/tools/r8/internal/ep0;->a(Ljava/lang/Iterable;Lcom/android/tools/r8/internal/pp0;Lcom/android/tools/r8/threading/ThreadingModule;Ljava/util/concurrent/ExecutorService;)Ljava/util/ArrayList;

    move-result-object v0

    .line 38
    invoke-virtual {p4, v0}, Lcom/android/tools/r8/internal/Ep0;->a(Ljava/util/Collection;)V

    .line 39
    iget-object v0, p0, Lcom/android/tools/r8/internal/vV;->a:Lcom/android/tools/r8/internal/l60;

    invoke-interface {p3, v0, p5}, Lcom/android/tools/r8/internal/u50;->a(Lcom/android/tools/r8/internal/l60;Ljava/util/concurrent/ExecutorService;)V

    .line 40
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vV;->h()V

    .line 41
    iget-object v0, p0, Lcom/android/tools/r8/internal/vV;->a:Lcom/android/tools/r8/internal/l60;

    .line 42
    iget-object v0, v0, Lcom/android/tools/r8/internal/hn;->b:Ljava/util/Map;

    .line 43
    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_0

    .line 44
    :cond_4
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/Ep0;->a()V

    return-void
.end method

.method public final c()Lcom/android/tools/r8/internal/w50;
    .locals 0

    return-object p0
.end method

.method public final c(Lcom/android/tools/r8/graph/D5;)Z
    .locals 2

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/w50;->h:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/internal/vV;->a:Lcom/android/tools/r8/internal/l60;

    .line 3
    iget-object v0, v0, Lcom/android/tools/r8/internal/hn;->b:Ljava/util/Map;

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 6
    :cond_1
    :goto_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/j1;->w1()Z

    move-result p1

    xor-int/lit8 p1, p1, 0x1

    return p1
.end method

.method public final d()Lcom/android/tools/r8/internal/q8;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w50;->d:Lcom/android/tools/r8/internal/q8;

    return-object v0
.end method

.method public final e()Lcom/android/tools/r8/internal/uV;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/w50;->e:Lcom/android/tools/r8/internal/uV;

    return-object v0
.end method

.method public final g()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
