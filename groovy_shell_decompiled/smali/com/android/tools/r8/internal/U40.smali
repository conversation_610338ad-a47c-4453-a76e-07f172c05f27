.class public interface abstract Lcom/android/tools/r8/internal/U40;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/util/function/Predicate;


# virtual methods
.method public abstract apply(Ljava/lang/Object;)Z
.end method

.method public abstract equals(Ljava/lang/Object;)Z
.end method

.method public test(Ljava/lang/Object;)Z
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lcom/android/tools/r8/internal/U40;->apply(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
