.class public final synthetic Lcom/android/tools/r8/internal/qz$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/sz;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/sz;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/qz$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/sz;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/internal/qz$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/sz;

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/sz;->g(Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/x2;

    move-result-object p1

    return-object p1
.end method
