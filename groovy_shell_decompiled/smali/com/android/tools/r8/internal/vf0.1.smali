.class public final Lcom/android/tools/r8/internal/vf0;
.super Lcom/android/tools/r8/internal/dy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# instance fields
.field public b:I

.field public c:I

.field public d:Ljava/lang/String;

.field public e:Z

.field public f:Lcom/android/tools/r8/internal/ge0;

.field public g:I

.field public h:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/dy;-><init>()V

    const/4 v0, 0x0

    .line 172
    iput v0, p0, Lcom/android/tools/r8/internal/vf0;->b:I

    const-string v0, ""

    .line 269
    iput-object v0, p0, Lcom/android/tools/r8/internal/vf0;->d:Ljava/lang/String;

    .line 270
    sget-object v0, Lcom/android/tools/r8/internal/xf0;->j:Lcom/android/tools/r8/internal/xf0;

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/internal/ay;)V
    .locals 0

    .line 271
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/dy;-><init>(Lcom/android/tools/r8/internal/ey;)V

    const/4 p1, 0x0

    .line 436
    iput p1, p0, Lcom/android/tools/r8/internal/vf0;->b:I

    const-string p1, ""

    .line 533
    iput-object p1, p0, Lcom/android/tools/r8/internal/vf0;->d:Ljava/lang/String;

    .line 534
    sget-object p1, Lcom/android/tools/r8/internal/xf0;->j:Lcom/android/tools/r8/internal/xf0;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vf0;
    .locals 2

    const/4 v0, 0x0

    .line 1
    :try_start_0
    sget-object v1, Lcom/android/tools/r8/internal/xf0;->k:Lcom/android/tools/r8/internal/uf0;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    new-instance v1, Lcom/android/tools/r8/internal/xf0;

    invoke-direct {v1, p1, p2}, Lcom/android/tools/r8/internal/xf0;-><init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/vf0;->a(Lcom/android/tools/r8/internal/xf0;)Lcom/android/tools/r8/internal/vf0;

    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 4
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 5
    check-cast p2, Lcom/android/tools/r8/internal/xf0;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 6
    :try_start_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p1

    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_0

    .line 9
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/vf0;->a(Lcom/android/tools/r8/internal/xf0;)Lcom/android/tools/r8/internal/vf0;

    .line 11
    :cond_0
    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/xf0;)Lcom/android/tools/r8/internal/vf0;
    .locals 3

    .line 12
    sget-object v0, Lcom/android/tools/r8/internal/xf0;->j:Lcom/android/tools/r8/internal/xf0;

    if-ne p1, v0, :cond_0

    return-object p0

    .line 13
    :cond_0
    iget v0, p1, Lcom/android/tools/r8/internal/xf0;->b:I

    if-eqz v0, :cond_1

    .line 14
    iput v0, p0, Lcom/android/tools/r8/internal/vf0;->b:I

    .line 15
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 16
    :cond_1
    iget v0, p1, Lcom/android/tools/r8/internal/xf0;->c:I

    if-eqz v0, :cond_2

    .line 17
    iput v0, p0, Lcom/android/tools/r8/internal/vf0;->c:I

    .line 18
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 19
    :cond_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/xf0;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_3

    .line 20
    iget-object v0, p1, Lcom/android/tools/r8/internal/xf0;->d:Ljava/lang/String;

    iput-object v0, p0, Lcom/android/tools/r8/internal/vf0;->d:Ljava/lang/String;

    .line 21
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 22
    :cond_3
    iget-boolean v0, p1, Lcom/android/tools/r8/internal/xf0;->e:Z

    if-eqz v0, :cond_4

    .line 23
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/vf0;->e:Z

    .line 24
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 25
    :cond_4
    iget-object v0, p1, Lcom/android/tools/r8/internal/xf0;->f:Lcom/android/tools/r8/internal/ge0;

    if-eqz v0, :cond_6

    .line 26
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/xf0;->b()Lcom/android/tools/r8/internal/ge0;

    move-result-object v0

    .line 27
    iget-object v1, p0, Lcom/android/tools/r8/internal/vf0;->f:Lcom/android/tools/r8/internal/ge0;

    if-eqz v1, :cond_5

    .line 28
    sget-object v2, Lcom/android/tools/r8/internal/ge0;->d:Lcom/android/tools/r8/internal/ge0;

    invoke-virtual {v2}, Lcom/android/tools/r8/internal/ge0;->a()Lcom/android/tools/r8/internal/fe0;

    move-result-object v2

    invoke-virtual {v2, v1}, Lcom/android/tools/r8/internal/fe0;->a(Lcom/android/tools/r8/internal/ge0;)Lcom/android/tools/r8/internal/fe0;

    move-result-object v1

    .line 29
    invoke-virtual {v1, v0}, Lcom/android/tools/r8/internal/fe0;->a(Lcom/android/tools/r8/internal/ge0;)Lcom/android/tools/r8/internal/fe0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/fe0;->b()Lcom/android/tools/r8/internal/ge0;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/vf0;->f:Lcom/android/tools/r8/internal/ge0;

    goto :goto_0

    .line 31
    :cond_5
    iput-object v0, p0, Lcom/android/tools/r8/internal/vf0;->f:Lcom/android/tools/r8/internal/ge0;

    .line 33
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 34
    :cond_6
    iget v0, p1, Lcom/android/tools/r8/internal/xf0;->g:I

    if-eqz v0, :cond_7

    .line 35
    iput v0, p0, Lcom/android/tools/r8/internal/vf0;->g:I

    .line 36
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 37
    :cond_7
    iget-boolean v0, p1, Lcom/android/tools/r8/internal/xf0;->h:Z

    if-eqz v0, :cond_8

    .line 38
    iput-boolean v0, p0, Lcom/android/tools/r8/internal/vf0;->h:Z

    .line 39
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 40
    :cond_8
    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 41
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/vf0;

    .line 42
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    return-object p0
.end method

.method public final addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/vf0;

    return-object p1
.end method

.method public final b()Lcom/android/tools/r8/internal/xf0;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/xf0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/xf0;-><init>(Lcom/android/tools/r8/internal/vf0;)V

    .line 2
    iget v1, p0, Lcom/android/tools/r8/internal/vf0;->b:I

    iput v1, v0, Lcom/android/tools/r8/internal/xf0;->b:I

    .line 3
    iget v1, p0, Lcom/android/tools/r8/internal/vf0;->c:I

    iput v1, v0, Lcom/android/tools/r8/internal/xf0;->c:I

    .line 4
    iget-object v1, p0, Lcom/android/tools/r8/internal/vf0;->d:Ljava/lang/String;

    iput-object v1, v0, Lcom/android/tools/r8/internal/xf0;->d:Ljava/lang/String;

    .line 5
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/vf0;->e:Z

    iput-boolean v1, v0, Lcom/android/tools/r8/internal/xf0;->e:Z

    .line 7
    iget-object v1, p0, Lcom/android/tools/r8/internal/vf0;->f:Lcom/android/tools/r8/internal/ge0;

    iput-object v1, v0, Lcom/android/tools/r8/internal/xf0;->f:Lcom/android/tools/r8/internal/ge0;

    .line 11
    iget v1, p0, Lcom/android/tools/r8/internal/vf0;->g:I

    iput v1, v0, Lcom/android/tools/r8/internal/xf0;->g:I

    .line 12
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/vf0;->h:Z

    iput-boolean v1, v0, Lcom/android/tools/r8/internal/xf0;->h:Z

    .line 13
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onBuilt()V

    return-object v0
.end method

.method public final build()Lcom/android/tools/r8/internal/AU;
    .locals 2

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vf0;->b()Lcom/android/tools/r8/internal/xf0;

    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/xf0;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 6
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public final build()Lcom/android/tools/r8/internal/vU;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vf0;->b()Lcom/android/tools/r8/internal/xf0;

    move-result-object v0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/xf0;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 3
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public final bridge synthetic buildPartial()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/vf0;->b()Lcom/android/tools/r8/internal/xf0;

    move-result-object v0

    return-object v0
.end method

.method public final clone()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->clone()Lcom/android/tools/r8/internal/dy;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/vf0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/xf0;->j:Lcom/android/tools/r8/internal/xf0;

    return-object v0
.end method

.method public final getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/xf0;->j:Lcom/android/tools/r8/internal/xf0;

    return-object v0
.end method

.method public final getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->S:Lcom/android/tools/r8/internal/Ok;

    return-object v0
.end method

.method public final internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/Tg0;->T:Lcom/android/tools/r8/internal/sy;

    .line 2
    const-class v1, Lcom/android/tools/r8/internal/xf0;

    const-class v2, Lcom/android/tools/r8/internal/vf0;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/vf0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vf0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 4
    instance-of v0, p1, Lcom/android/tools/r8/internal/xf0;

    if-eqz v0, :cond_0

    .line 5
    check-cast p1, Lcom/android/tools/r8/internal/xf0;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/vf0;->a(Lcom/android/tools/r8/internal/xf0;)Lcom/android/tools/r8/internal/vf0;

    move-result-object p1

    goto :goto_0

    .line 7
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    move-object p1, p0

    :goto_0
    return-object p1
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/M0;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/vf0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vf0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 8
    instance-of v0, p1, Lcom/android/tools/r8/internal/xf0;

    if-eqz v0, :cond_0

    .line 9
    check-cast p1, Lcom/android/tools/r8/internal/xf0;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/vf0;->a(Lcom/android/tools/r8/internal/xf0;)Lcom/android/tools/r8/internal/vf0;

    move-result-object p1

    goto :goto_0

    .line 11
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    move-object p1, p0

    :goto_0
    return-object p1
.end method

.method public final bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/zU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/vf0;->a(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vf0;

    move-result-object p1

    return-object p1
.end method

.method public final mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/vf0;

    return-object p1
.end method

.method public final setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/vf0;

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/vf0;

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/vf0;

    return-object p1
.end method
