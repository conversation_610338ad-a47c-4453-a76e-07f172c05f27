.class public abstract Lcom/android/tools/r8/internal/Ud;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Lcom/android/tools/r8/graph/h;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# static fields
.field public static final synthetic d:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Lcom/android/tools/r8/graph/B1;

.field public final c:Lcom/android/tools/r8/utils/w;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/Ud;->b:Lcom/android/tools/r8/graph/B1;

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/Ud;->c:Lcom/android/tools/r8/utils/w;

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/graph/y;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    return-object v0
.end method

.method public final synthetic a(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/Xd;
    .locals 1

    const/4 v0, 0x0

    .line 4
    invoke-virtual {p0, p1, v0, v0}, Lcom/android/tools/r8/internal/Ud;->c(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/internal/Xd;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/Gp0;)Lcom/android/tools/r8/internal/Xd;
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ud;->b()Ljava/lang/String;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0, p1}, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/Ud;Lcom/android/tools/r8/internal/aA;)V

    invoke-virtual {p2, v0, v1}, Lcom/android/tools/r8/internal/Gp0;->a(Ljava/lang/String;Lcom/android/tools/r8/internal/xp0;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Xd;

    return-object p1
.end method

.method public final synthetic a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/internal/Xd;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/Ud;->c(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/internal/Xd;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;Lcom/android/tools/r8/internal/Gp0;)Lcom/android/tools/r8/internal/Xd;
    .locals 2

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ud;->b()Ljava/lang/String;

    move-result-object v0

    new-instance v1, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;

    invoke-direct {v1, p0, p1, p2, p3}, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/Ud;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)V

    invoke-virtual {p4, v0, v1}, Lcom/android/tools/r8/internal/Gp0;->a(Ljava/lang/String;Lcom/android/tools/r8/internal/xp0;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/internal/Xd;

    return-object p1
.end method

.method public a(Lcom/android/tools/r8/internal/aA;ZLjava/lang/String;)V
    .locals 3

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ud;->b()Ljava/lang/String;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Invalid code "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string v1, " "

    invoke-virtual {p3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    if-eqz p2, :cond_0

    .line 8
    :try_start_0
    iget-object p2, p0, Lcom/android/tools/r8/internal/Ud;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/aA;->b(Lcom/android/tools/r8/graph/y;)Z

    move-result p1

    goto :goto_0

    :cond_0
    const/4 p2, 0x0

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/internal/aA;->b(Z)V
    :try_end_0
    .catch Ljava/lang/AssertionError; {:try_start_0 .. :try_end_0} :catch_0

    const/4 p1, 0x1

    .line 12
    :goto_0
    sget-boolean p2, Lcom/android/tools/r8/internal/Ud;->d:Z

    if-nez p2, :cond_2

    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1, p3}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p1

    :cond_2
    :goto_1
    return-void

    :catch_0
    move-exception p1

    .line 13
    new-instance p2, Ljava/lang/AssertionError;

    invoke-direct {p2, p3, p1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw p2
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;)Z
    .locals 1

    .line 14
    iget-object v0, p0, Lcom/android/tools/r8/internal/Ud;->c:Lcom/android/tools/r8/utils/w;

    iget-boolean v0, v0, Lcom/android/tools/r8/utils/w;->f1:Z

    if-nez v0, :cond_1

    .line 15
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object p1

    .line 16
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E2;->R1()Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public abstract a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;)Z
.end method

.method public b(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/Xd;
    .locals 1

    .line 1
    new-instance p1, Lcom/android/tools/r8/internal/Os0;

    const-string v0, "Should Override or use overload"

    invoke-direct {p1, v0}, Lcom/android/tools/r8/internal/Os0;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public b(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/internal/Xd;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Ud;->b(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/Xd;

    move-result-object p1

    return-object p1
.end method

.method public abstract b()Ljava/lang/String;
.end method

.method public c()Lcom/android/tools/r8/internal/Xd;
    .locals 1

    .line 12
    sget-object v0, Lcom/android/tools/r8/internal/Xd;->a:Lcom/android/tools/r8/internal/Wd;

    return-object v0
.end method

.method public final c(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/internal/Xd;
    .locals 3

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/internal/Ud;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 2
    sget-boolean v0, Lcom/android/tools/r8/internal/Ud;->d:Z

    if-nez v0, :cond_0

    .line 3
    instance-of v1, p0, Lcom/android/tools/r8/internal/pq0;

    xor-int/lit8 v1, v1, 0x1

    const-string v2, "before"

    .line 4
    invoke-virtual {p0, p1, v1, v2}, Lcom/android/tools/r8/internal/Ud;->a(Lcom/android/tools/r8/internal/aA;ZLjava/lang/String;)V

    .line 5
    :cond_0
    invoke-virtual {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/Ud;->b(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/internal/Xd;

    move-result-object p2

    if-nez v0, :cond_1

    .line 6
    invoke-interface {p2}, Lcom/android/tools/r8/internal/Xd;->a()Lcom/android/tools/r8/internal/u20;

    move-result-object p3

    invoke-virtual {p3}, Lcom/android/tools/r8/internal/T6;->a()Z

    move-result p3

    if-nez p3, :cond_1

    .line 7
    instance-of p3, p0, Lcom/android/tools/r8/internal/pq0;

    xor-int/lit8 p3, p3, 0x1

    const-string v0, "after"

    .line 8
    invoke-virtual {p0, p1, p3, v0}, Lcom/android/tools/r8/internal/Ud;->a(Lcom/android/tools/r8/internal/aA;ZLjava/lang/String;)V

    :cond_1
    return-object p2

    .line 11
    :cond_2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Ud;->c()Lcom/android/tools/r8/internal/Xd;

    move-result-object p1

    return-object p1
.end method
