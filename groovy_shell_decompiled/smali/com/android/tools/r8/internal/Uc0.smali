.class public final Lcom/android/tools/r8/internal/Uc0;
.super Ljava/util/AbstractList;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/util/List;


# instance fields
.field public final b:Lcom/android/tools/r8/internal/Xc0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Xc0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/util/AbstractList;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/Uc0;->b:Lcom/android/tools/r8/internal/Xc0;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 1

    .line 1
    iget v0, p0, Ljava/util/AbstractList;->modCount:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Ljava/util/AbstractList;->modCount:I

    return-void
.end method

.method public final get(I)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uc0;->b:Lcom/android/tools/r8/internal/Xc0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(I)Lcom/android/tools/r8/internal/H0;

    move-result-object p1

    return-object p1
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Uc0;->b:Lcom/android/tools/r8/internal/Xc0;

    .line 2
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xc0;->b:Ljava/util/List;

    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method
