.class public final Lcom/android/tools/r8/internal/Wa;
.super Lcom/android/tools/r8/internal/Ya;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/internal/It0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/It0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Ya;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/Wa;->a:Lcom/android/tools/r8/internal/It0;

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/It0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Wa;->a:Lcom/android/tools/r8/internal/It0;

    return-object v0
.end method

.method public final b()Lcom/android/tools/r8/graph/J2;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/Wa;->a:Lcom/android/tools/r8/internal/It0;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Imprecise("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
