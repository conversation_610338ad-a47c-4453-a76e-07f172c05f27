.class public final Lcom/android/tools/r8/internal/SQ;
.super Lcom/android/tools/r8/internal/UQ;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/TQ;)V
    .locals 0

    .line 1
    iget-object p1, p1, Lcom/android/tools/r8/internal/TQ;->b:Lcom/android/tools/r8/internal/WQ;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/UQ;-><init>(Lcom/android/tools/r8/internal/WQ;)V

    return-void
.end method


# virtual methods
.method public final next()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/UQ;->b:Lcom/android/tools/r8/internal/VQ;

    .line 2
    iget-object v1, p0, Lcom/android/tools/r8/internal/UQ;->e:Lcom/android/tools/r8/internal/WQ;

    iget-object v2, v1, Lcom/android/tools/r8/internal/WQ;->g:Lcom/android/tools/r8/internal/VQ;

    if-eq v0, v2, :cond_1

    .line 5
    iget v1, v1, Lcom/android/tools/r8/internal/WQ;->f:I

    iget v2, p0, Lcom/android/tools/r8/internal/UQ;->d:I

    if-ne v1, v2, :cond_0

    .line 8
    iget-object v1, v0, Lcom/android/tools/r8/internal/VQ;->e:Lcom/android/tools/r8/internal/VQ;

    iput-object v1, p0, Lcom/android/tools/r8/internal/UQ;->b:Lcom/android/tools/r8/internal/VQ;

    .line 9
    iput-object v0, p0, Lcom/android/tools/r8/internal/UQ;->c:Lcom/android/tools/r8/internal/VQ;

    .line 10
    iget-object v0, v0, Lcom/android/tools/r8/internal/VQ;->g:Ljava/lang/Object;

    return-object v0

    .line 11
    :cond_0
    new-instance v0, Ljava/util/ConcurrentModificationException;

    invoke-direct {v0}, Ljava/util/ConcurrentModificationException;-><init>()V

    throw v0

    .line 12
    :cond_1
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method
