.class public final enum Lcom/android/tools/r8/internal/Tj;
.super Ljava/lang/Enum;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/zH;


# static fields
.field public static final enum c:Lcom/android/tools/r8/internal/Tj;

.field public static final enum d:Lcom/android/tools/r8/internal/Tj;

.field public static final enum e:Lcom/android/tools/r8/internal/Tj;


# instance fields
.field public final b:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/Tj;

    const-string v1, "STRING"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Tj;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Tj;->c:Lcom/android/tools/r8/internal/Tj;

    .line 5
    new-instance v0, Lcom/android/tools/r8/internal/Tj;

    const-string v1, "CORD"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Tj;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Tj;->d:Lcom/android/tools/r8/internal/Tj;

    .line 9
    new-instance v0, Lcom/android/tools/r8/internal/Tj;

    const-string v1, "STRING_PIECE"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2, v2}, Lcom/android/tools/r8/internal/Tj;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lcom/android/tools/r8/internal/Tj;->e:Lcom/android/tools/r8/internal/Tj;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    iput p3, p0, Lcom/android/tools/r8/internal/Tj;->b:I

    return-void
.end method

.method public static a(I)Lcom/android/tools/r8/internal/Tj;
    .locals 1

    if-eqz p0, :cond_2

    const/4 v0, 0x1

    if-eq p0, v0, :cond_1

    const/4 v0, 0x2

    if-eq p0, v0, :cond_0

    const/4 p0, 0x0

    return-object p0

    .line 1
    :cond_0
    sget-object p0, Lcom/android/tools/r8/internal/Tj;->e:Lcom/android/tools/r8/internal/Tj;

    return-object p0

    .line 2
    :cond_1
    sget-object p0, Lcom/android/tools/r8/internal/Tj;->d:Lcom/android/tools/r8/internal/Tj;

    return-object p0

    .line 3
    :cond_2
    sget-object p0, Lcom/android/tools/r8/internal/Tj;->c:Lcom/android/tools/r8/internal/Tj;

    return-object p0
.end method


# virtual methods
.method public final getNumber()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/Tj;->b:I

    return v0
.end method
