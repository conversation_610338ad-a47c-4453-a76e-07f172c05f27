.class public final Lcom/android/tools/r8/internal/Vb;
.super Lcom/android/tools/r8/internal/fV;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic y:Lcom/android/tools/r8/internal/SV;


# direct methods
.method public constructor <init>(IILjava/lang/String;Ljava/lang/String;Lcom/android/tools/r8/internal/SV;)V
    .locals 6

    .line 1
    iput-object p5, p0, Lcom/android/tools/r8/internal/Vb;->y:Lcom/android/tools/r8/internal/SV;

    const/4 v5, 0x0

    move-object v0, p0

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    invoke-direct/range {v0 .. v5}, Lcom/android/tools/r8/internal/fV;-><init>(IILjava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public final c()V
    .locals 16

    move-object/from16 v1, p0

    .line 1
    iget v0, v1, Lcom/android/tools/r8/internal/fV;->t:I

    .line 2
    iget v2, v1, Lcom/android/tools/r8/internal/fV;->s:I

    .line 5
    iget-object v3, v1, Lcom/android/tools/r8/internal/Vb;->y:Lcom/android/tools/r8/internal/SV;

    instance-of v4, v3, Lcom/android/tools/r8/internal/Xb;

    const/4 v6, 0x1

    if-eqz v4, :cond_1

    .line 6
    check-cast v3, Lcom/android/tools/r8/internal/Xb;

    .line 7
    iget-object v4, v3, Lcom/android/tools/r8/internal/Xb;->d:Lcom/android/tools/r8/internal/Hd;

    .line 8
    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    iget-object v4, v3, Lcom/android/tools/r8/internal/Xb;->d:Lcom/android/tools/r8/internal/Hd;

    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 9
    iget v4, v3, Lcom/android/tools/r8/internal/Xb;->c:I

    const v7, 0xffff

    and-int/2addr v4, v7

    const/16 v7, 0x33

    if-lt v4, v7, :cond_0

    .line 10
    iget-object v3, v3, Lcom/android/tools/r8/internal/Xb;->d:Lcom/android/tools/r8/internal/Hd;

    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move v3, v6

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    :goto_0
    move v4, v6

    goto :goto_1

    :cond_1
    const/4 v3, 0x0

    const/4 v4, 0x0

    :goto_1
    if-eqz v3, :cond_2

    .line 11
    new-instance v3, Lcom/android/tools/r8/internal/Ub;

    new-instance v7, Lcom/android/tools/r8/internal/S5;

    invoke-direct {v7}, Lcom/android/tools/r8/internal/S5;-><init>()V

    invoke-direct {v3, v7}, Lcom/android/tools/r8/internal/Ub;-><init>(Lcom/android/tools/r8/internal/S5;)V

    goto :goto_2

    .line 12
    :cond_2
    new-instance v3, Lcom/android/tools/r8/internal/p2;

    new-instance v7, Lcom/android/tools/r8/internal/S5;

    invoke-direct {v7}, Lcom/android/tools/r8/internal/S5;-><init>()V

    invoke-direct {v3, v7}, Lcom/android/tools/r8/internal/p2;-><init>(Lcom/android/tools/r8/internal/S5;)V

    :goto_2
    if-eqz v4, :cond_3

    .line 15
    :try_start_0
    invoke-virtual {v3, v1}, Lcom/android/tools/r8/internal/p2;->a(Lcom/android/tools/r8/internal/Vb;)V

    goto :goto_3

    .line 17
    :cond_3
    invoke-virtual {v3, v1}, Lcom/android/tools/r8/internal/p2;->b(Lcom/android/tools/r8/internal/Vb;)V
    :try_end_0
    .catch Ljava/lang/IndexOutOfBoundsException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Lcom/android/tools/r8/internal/q2; {:try_start_0 .. :try_end_0} :catch_0

    .line 22
    :goto_3
    iget-object v3, v1, Lcom/android/tools/r8/internal/Vb;->y:Lcom/android/tools/r8/internal/SV;

    if-eqz v3, :cond_4

    .line 23
    iput v0, v1, Lcom/android/tools/r8/internal/fV;->t:I

    .line 24
    iput v2, v1, Lcom/android/tools/r8/internal/fV;->s:I

    .line 25
    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/fV;->a(Lcom/android/tools/r8/internal/SV;)V

    :cond_4
    return-void

    :catch_0
    move-exception v0

    goto :goto_4

    :catch_1
    move-exception v0

    .line 26
    :goto_4
    new-instance v2, Ljava/io/StringWriter;

    invoke-direct {v2}, Ljava/io/StringWriter;-><init>()V

    .line 27
    new-instance v4, Ljava/io/PrintWriter;

    invoke-direct {v4, v2, v6}, Ljava/io/PrintWriter;-><init>(Ljava/io/Writer;Z)V

    .line 28
    new-instance v7, Lcom/android/tools/r8/internal/bp0;

    .line 29
    invoke-direct {v7}, Lcom/android/tools/r8/internal/bp0;-><init>()V

    .line 30
    new-instance v8, Lcom/android/tools/r8/internal/Np0;

    invoke-direct {v8, v7}, Lcom/android/tools/r8/internal/Np0;-><init>(Lcom/android/tools/r8/internal/bp0;)V

    .line 32
    iget-object v9, v1, Lcom/android/tools/r8/internal/fV;->d:Ljava/lang/String;

    iget-object v10, v1, Lcom/android/tools/r8/internal/fV;->e:Ljava/lang/String;

    .line 33
    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    .line 34
    invoke-virtual {v4, v9}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    const/4 v9, 0x0

    .line 35
    :goto_5
    iget-object v10, v1, Lcom/android/tools/r8/internal/fV;->q:Lcom/android/tools/r8/internal/IC;

    .line 36
    iget v11, v10, Lcom/android/tools/r8/internal/IC;->b:I

    const-string v12, " "

    if-ge v9, v11, :cond_9

    .line 37
    invoke-virtual {v10, v9}, Lcom/android/tools/r8/internal/IC;->j(I)Lcom/android/tools/r8/internal/G;

    move-result-object v10

    invoke-virtual {v10, v8}, Lcom/android/tools/r8/internal/G;->a(Lcom/android/tools/r8/internal/SV;)V

    .line 39
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    .line 40
    iget-object v11, v3, Lcom/android/tools/r8/internal/p2;->e:[Lcom/android/tools/r8/internal/dx;

    .line 41
    aget-object v11, v11, v9

    const-string v13, " : "

    const/16 v14, 0x20

    if-nez v11, :cond_5

    const/16 v11, 0x3f

    .line 43
    invoke-virtual {v10, v11}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_8

    :cond_5
    const/4 v15, 0x0

    .line 44
    :goto_6
    iget v5, v11, Lcom/android/tools/r8/internal/dx;->c:I

    if-ge v15, v5, :cond_6

    .line 45
    invoke-virtual {v11, v15}, Lcom/android/tools/r8/internal/dx;->a(I)Lcom/android/tools/r8/internal/ut0;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/internal/R5;

    invoke-virtual {v5}, Lcom/android/tools/r8/internal/R5;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lcom/android/tools/r8/internal/Sb;->e(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v10, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v14}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v15, v15, 0x1

    goto :goto_6

    .line 47
    :cond_6
    invoke-virtual {v10, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v5, 0x0

    .line 48
    :goto_7
    iget v15, v11, Lcom/android/tools/r8/internal/dx;->d:I

    if-ge v5, v15, :cond_7

    .line 49
    iget-object v15, v11, Lcom/android/tools/r8/internal/dx;->b:[Lcom/android/tools/r8/internal/ut0;

    iget v6, v11, Lcom/android/tools/r8/internal/dx;->c:I

    add-int/2addr v6, v5

    aget-object v6, v15, v6

    .line 50
    check-cast v6, Lcom/android/tools/r8/internal/R5;

    invoke-virtual {v6}, Lcom/android/tools/r8/internal/R5;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/android/tools/r8/internal/Sb;->e(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v10, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v14}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    const/4 v6, 0x1

    goto :goto_7

    .line 53
    :cond_7
    :goto_8
    invoke-virtual {v10}, Ljava/lang/StringBuilder;->length()I

    move-result v5

    iget v6, v1, Lcom/android/tools/r8/internal/fV;->s:I

    iget v11, v1, Lcom/android/tools/r8/internal/fV;->t:I

    add-int/2addr v6, v11

    const/4 v11, 0x1

    add-int/2addr v6, v11

    if-ge v5, v6, :cond_8

    .line 54
    invoke-virtual {v10, v14}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_8

    :cond_8
    const v5, 0x186a0

    add-int/2addr v5, v9

    .line 56
    invoke-static {v5}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v5, v11}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 57
    iget-object v5, v7, Lcom/android/tools/r8/internal/D50;->b:Ljava/util/ArrayList;

    .line 58
    invoke-virtual {v5}, Ljava/util/ArrayList;->size()I

    move-result v6

    sub-int/2addr v6, v11

    invoke-virtual {v5, v6}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v5

    .line 59
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6, v12}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v6, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 60
    invoke-virtual {v4, v5}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    add-int/lit8 v9, v9, 0x1

    const/4 v6, 0x1

    goto/16 :goto_5

    .line 63
    :cond_9
    iget-object v3, v1, Lcom/android/tools/r8/internal/fV;->r:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_9
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_a

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/internal/tq0;

    .line 64
    invoke-virtual {v5, v8}, Lcom/android/tools/r8/internal/tq0;->a(Lcom/android/tools/r8/internal/SV;)V

    .line 65
    iget-object v5, v7, Lcom/android/tools/r8/internal/D50;->b:Ljava/util/ArrayList;

    invoke-virtual {v5}, Ljava/util/ArrayList;->size()I

    move-result v6

    const/4 v9, 0x1

    sub-int/2addr v6, v9

    invoke-virtual {v5, v6}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v5

    .line 66
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6, v12}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 67
    invoke-virtual {v4, v5}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    goto :goto_9

    .line 69
    :cond_a
    invoke-virtual {v4}, Ljava/io/PrintWriter;->println()V

    .line 70
    invoke-virtual {v4}, Ljava/io/PrintWriter;->close()V

    .line 71
    new-instance v3, Ljava/lang/IllegalArgumentException;

    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2}, Ljava/io/StringWriter;->toString()Ljava/lang/String;

    move-result-object v2

    .line 72
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 73
    invoke-direct {v3, v2, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v3
.end method
