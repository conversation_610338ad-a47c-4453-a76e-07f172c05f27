.class public final Lcom/android/tools/r8/internal/ay;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/ey;


# instance fields
.field public final synthetic a:Lcom/android/tools/r8/internal/I0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/I0;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/ay;->a:Lcom/android/tools/r8/internal/I0;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/ay;->a:Lcom/android/tools/r8/internal/I0;

    invoke-interface {v0}, Lcom/android/tools/r8/internal/I0;->a()V

    return-void
.end method
