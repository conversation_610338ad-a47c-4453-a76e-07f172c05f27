.class public final synthetic Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/xp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/Ud;

.field public final synthetic f$1:Lcom/android/tools/r8/internal/aA;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/sV;

.field public final synthetic f$3:Lcom/android/tools/r8/internal/ef;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/Ud;Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/Ud;

    iput-object p2, p0, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;->f$1:Lcom/android/tools/r8/internal/aA;

    iput-object p3, p0, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;->f$2:Lcom/android/tools/r8/internal/sV;

    iput-object p4, p0, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;->f$3:Lcom/android/tools/r8/internal/ef;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 4

    iget-object v0, p0, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;->f$0:Lcom/android/tools/r8/internal/Ud;

    iget-object v1, p0, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;->f$1:Lcom/android/tools/r8/internal/aA;

    iget-object v2, p0, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;->f$2:Lcom/android/tools/r8/internal/sV;

    iget-object v3, p0, Lcom/android/tools/r8/internal/Ud$$ExternalSyntheticLambda1;->f$3:Lcom/android/tools/r8/internal/ef;

    invoke-virtual {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/Ud;->a(Lcom/android/tools/r8/internal/aA;Lcom/android/tools/r8/internal/sV;Lcom/android/tools/r8/internal/ef;)Lcom/android/tools/r8/internal/Xd;

    move-result-object v0

    return-object v0
.end method
