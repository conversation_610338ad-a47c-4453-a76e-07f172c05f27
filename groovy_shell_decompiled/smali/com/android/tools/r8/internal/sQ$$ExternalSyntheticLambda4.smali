.class public final synthetic Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda4;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BiPredicate;


# static fields
.field public static final synthetic INSTANCE:Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda4;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda4;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda4;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/internal/sQ$$ExternalSyntheticLambda4;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    check-cast p2, Ljava/util/List;

    invoke-static {p1, p2}, Lcom/android/tools/r8/internal/sQ;->c(Lcom/android/tools/r8/graph/x2;Ljava/util/List;)Z

    move-result p1

    return p1
.end method
