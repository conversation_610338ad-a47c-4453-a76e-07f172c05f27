.class public final Lcom/android/tools/r8/internal/v3;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic g:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/graph/y;

.field public final b:Lcom/android/tools/r8/internal/x50;

.field public final c:Lcom/android/tools/r8/internal/Pv;

.field public final d:Lcom/android/tools/r8/internal/OV;

.field public final e:Lcom/android/tools/r8/utils/w;

.field public final f:Lcom/android/tools/r8/internal/O40;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/x50;Lcom/android/tools/r8/internal/Pv;Lcom/android/tools/r8/internal/OV;Lcom/android/tools/r8/internal/O40;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/v3;->b:Lcom/android/tools/r8/internal/x50;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/internal/v3;->c:Lcom/android/tools/r8/internal/Pv;

    .line 5
    iput-object p4, p0, Lcom/android/tools/r8/internal/v3;->d:Lcom/android/tools/r8/internal/OV;

    .line 6
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/v3;->e:Lcom/android/tools/r8/utils/w;

    .line 7
    iput-object p5, p0, Lcom/android/tools/r8/internal/v3;->f:Lcom/android/tools/r8/internal/O40;

    return-void
.end method


# virtual methods
.method public final a(Ljava/util/concurrent/ExecutorService;)Lcom/android/tools/r8/graph/K5;
    .locals 5

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/l60;->d:Lcom/android/tools/r8/internal/j60;

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/i60;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/i60;-><init>()V

    .line 3
    iget-object v1, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 4
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h;->d()Ljava/util/Collection;

    move-result-object v1

    new-instance v2, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda3;

    invoke-direct {v2, p0, v0}, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda3;-><init>(Lcom/android/tools/r8/internal/v3;Lcom/android/tools/r8/internal/l60;)V

    iget-object v3, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 5
    invoke-static {v3, v1, v2, p1}, Lcom/android/tools/r8/K;->a(Lcom/android/tools/r8/graph/y;Ljava/util/Collection;Ljava/util/function/Consumer;Ljava/util/concurrent/ExecutorService;)V

    .line 6
    iget-object v1, v0, Lcom/android/tools/r8/internal/hn;->b:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    .line 7
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/D5;

    .line 8
    iget-object v3, p0, Lcom/android/tools/r8/internal/v3;->b:Lcom/android/tools/r8/internal/x50;

    .line 9
    invoke-virtual {v3, v2}, Lcom/android/tools/r8/internal/fA;->a(Lcom/android/tools/r8/graph/D5;)V

    .line 10
    iget-object v3, v3, Lcom/android/tools/r8/internal/fA;->D:Lcom/android/tools/r8/graph/J5;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/graph/x2;

    .line 11
    iget-object v3, v3, Lcom/android/tools/r8/graph/I5;->g:Ljava/util/Set;

    .line 12
    invoke-interface {v3, v4}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 13
    iget-object v3, p0, Lcom/android/tools/r8/internal/v3;->f:Lcom/android/tools/r8/internal/O40;

    iget-object v4, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v4}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object v4

    .line 14
    iget-object v3, v3, Lcom/android/tools/r8/internal/O40;->a:Lcom/android/tools/r8/internal/JS;

    .line 15
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v3, v4, v2}, Lcom/android/tools/r8/internal/JS;->a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/x2;)V

    goto :goto_0

    .line 16
    :cond_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/v3;->b:Lcom/android/tools/r8/internal/x50;

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/x50;->b(Ljava/util/concurrent/ExecutorService;)V

    .line 17
    iget-object v1, p0, Lcom/android/tools/r8/internal/v3;->b:Lcom/android/tools/r8/internal/x50;

    .line 18
    sget-object v2, Lcom/android/tools/r8/internal/l60;->d:Lcom/android/tools/r8/internal/j60;

    .line 19
    invoke-virtual {v1, v2, p1}, Lcom/android/tools/r8/internal/x50;->a(Lcom/android/tools/r8/internal/l60;Ljava/util/concurrent/ExecutorService;)V

    .line 20
    new-instance p1, Lcom/android/tools/r8/graph/I5;

    invoke-direct {p1}, Lcom/android/tools/r8/graph/I5;-><init>()V

    .line 21
    iget-object v1, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 22
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/y;->f()Lcom/android/tools/r8/graph/x0;

    move-result-object v1

    .line 23
    iput-object v1, p1, Lcom/android/tools/r8/graph/I5;->a:Lcom/android/tools/r8/graph/x0;

    .line 24
    sget-object v1, Lcom/android/tools/r8/internal/GS$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/GS$$ExternalSyntheticLambda1;

    .line 25
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/hn;->b(Ljava/util/function/IntFunction;)Ljava/util/Set;

    move-result-object v0

    .line 26
    iput-object v0, p1, Lcom/android/tools/r8/graph/I5;->g:Ljava/util/Set;

    .line 27
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/I5;->a()Lcom/android/tools/r8/graph/K5;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;ILcom/android/tools/r8/internal/Gt0;)Lcom/android/tools/r8/internal/Gt0;
    .locals 1

    .line 300
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->y0()Z

    move-result v0

    if-nez v0, :cond_0

    if-nez p2, :cond_0

    return-object p3

    .line 303
    :cond_0
    invoke-virtual {p1, p2}, Lcom/android/tools/r8/graph/H0;->a(I)Lcom/android/tools/r8/graph/J2;

    move-result-object p1

    .line 304
    iget-object p2, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/graph/J2;->a(Lcom/android/tools/r8/graph/y;)Z

    move-result p2

    if-nez p2, :cond_1

    return-object p3

    .line 307
    :cond_1
    new-instance p2, Lcom/android/tools/r8/internal/Of;

    iget-object p3, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 308
    iget-object p3, p3, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 309
    invoke-virtual {p3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-static {p1}, Lcom/android/tools/r8/internal/F1;->b(Lcom/android/tools/r8/graph/J2;)V

    sget-object p1, Lcom/android/tools/r8/internal/gk0;->b:Lcom/android/tools/r8/internal/gk0;

    .line 310
    sget-boolean p3, Lcom/android/tools/r8/internal/Gs;->a:Z

    .line 311
    sget-object p3, Lcom/android/tools/r8/internal/Is;->d:Lcom/android/tools/r8/internal/Is;

    .line 312
    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object v0

    invoke-direct {p2, p1, p3, v0}, Lcom/android/tools/r8/internal/Of;-><init>(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/Gs;Ljava/util/Set;)V

    return-object p2
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/LV;)Lcom/android/tools/r8/internal/LV;
    .locals 4

    .line 265
    sget-boolean v0, Lcom/android/tools/r8/internal/v3;->g:Z

    if-nez v0, :cond_1

    invoke-interface {p2}, Lcom/android/tools/r8/internal/LV;->f()Z

    move-result v1

    if-nez v1, :cond_1

    invoke-interface {p2}, Lcom/android/tools/r8/internal/LV;->isUnknown()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 266
    :cond_1
    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object v1

    iget-object v2, p0, Lcom/android/tools/r8/internal/v3;->e:Lcom/android/tools/r8/utils/w;

    .line 267
    invoke-virtual {v1, v2}, Lcom/android/tools/r8/shaking/n1;->c(Lcom/android/tools/r8/shaking/L0;)Z

    move-result v2

    if-eqz v2, :cond_6

    .line 268
    iget-boolean v1, v1, Lcom/android/tools/r8/shaking/v1;->o:Z

    if-eqz v1, :cond_6

    .line 269
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/j1;

    .line 270
    invoke-virtual {v1}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    .line 271
    check-cast v2, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/j1;->y0()Z

    move-result v1

    invoke-virtual {v2, v1}, Lcom/android/tools/r8/graph/x2;->a(Z)I

    move-result v1

    .line 272
    invoke-interface {p2}, Lcom/android/tools/r8/internal/LV;->f()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 273
    invoke-interface {p2}, Lcom/android/tools/r8/internal/LV;->b()Lcom/android/tools/r8/internal/Qf;

    move-result-object v0

    .line 274
    iget-boolean v1, v0, Lcom/android/tools/r8/internal/Qf;->b:Z

    .line 275
    iget-object v0, v0, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    goto :goto_3

    :cond_2
    if-nez v0, :cond_4

    .line 276
    invoke-interface {p2}, Lcom/android/tools/r8/internal/LV;->isUnknown()Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 278
    :cond_4
    :goto_1
    sget-object v0, Lcom/android/tools/r8/internal/Fs0;->b:Lcom/android/tools/r8/internal/Fs0;

    sget-boolean v2, Lcom/android/tools/r8/internal/QR;->a:Z

    .line 279
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2, v1}, Ljava/util/ArrayList;-><init>(I)V

    const/4 v3, 0x0

    :goto_2
    if-ge v3, v1, :cond_5

    .line 281
    invoke-virtual {v2, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    :cond_5
    const/4 v1, 0x1

    move-object v0, v2

    .line 282
    :goto_3
    new-instance v2, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda0;

    invoke-direct {v2, p0, p1}, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/internal/v3;Lcom/android/tools/r8/graph/D5;)V

    const/4 p1, 0x0

    .line 283
    invoke-static {v0, v2, p1}, Lcom/android/tools/r8/internal/QR;->a(Ljava/util/List;Lcom/android/tools/r8/internal/QG;Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_6

    .line 299
    new-instance p2, Lcom/android/tools/r8/internal/Qf;

    invoke-direct {p2, p1, v1}, Lcom/android/tools/r8/internal/Qf;-><init>(Ljava/util/List;Z)V

    :cond_6
    return-object p2
.end method

.method public final a(Lcom/android/tools/r8/graph/E2;)Lcom/android/tools/r8/internal/l60;
    .locals 3

    .line 34
    invoke-static {}, Lcom/android/tools/r8/internal/l60;->c()Lcom/android/tools/r8/internal/l60;

    move-result-object v0

    .line 35
    new-instance v1, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda1;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda1;-><init>(Lcom/android/tools/r8/internal/v3;)V

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 36
    sget-object v2, Lcom/android/tools/r8/internal/c50;->b:Lcom/android/tools/r8/internal/Y40;

    invoke-virtual {p1, v1, v2}, Lcom/android/tools/r8/graph/E2;->f(Ljava/util/function/Consumer;Ljava/util/function/Predicate;)V

    .line 37
    new-instance v1, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda2;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda2;-><init>(Lcom/android/tools/r8/internal/v3;Lcom/android/tools/r8/internal/l60;)V

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/graph/E2;->n(Ljava/util/function/Consumer;)V

    .line 38
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->d0()Lcom/android/tools/r8/graph/J4;

    move-result-object p1

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/hn;->b()Ljava/util/Set;

    move-result-object v1

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 39
    invoke-interface {v1}, Ljava/util/Set;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    .line 42
    :cond_0
    iget-object v2, p1, Lcom/android/tools/r8/graph/J4;->b:Lcom/android/tools/r8/graph/K4;

    invoke-virtual {v2, v1}, Lcom/android/tools/r8/graph/K4;->a(Ljava/util/Set;)V

    .line 43
    sget-object v1, Lcom/android/tools/r8/graph/j1;->v:Lcom/android/tools/r8/graph/j1;

    iput-object v1, p1, Lcom/android/tools/r8/graph/J4;->c:Lcom/android/tools/r8/graph/j1;

    :goto_0
    return-object v0
.end method

.method public final a(Lcom/android/tools/r8/graph/B5;)V
    .locals 5

    .line 44
    iget-object v0, p0, Lcom/android/tools/r8/internal/v3;->c:Lcom/android/tools/r8/internal/Pv;

    .line 45
    iget-object v0, v0, Lcom/android/tools/r8/internal/Pv;->a:Lcom/android/tools/r8/internal/d60;

    .line 46
    iget-object v1, v0, Lcom/android/tools/r8/internal/bn;->b:Ljava/util/Map;

    .line 47
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/an;->b(Lcom/android/tools/r8/graph/G0;)Lcom/android/tools/r8/internal/su;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    .line 48
    check-cast v0, Lcom/android/tools/r8/internal/Gt0;

    if-eqz v0, :cond_0

    goto :goto_0

    .line 49
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/l1;

    invoke-static {v0}, Lcom/android/tools/r8/internal/Gt0;->a(Lcom/android/tools/r8/graph/l1;)Lcom/android/tools/r8/internal/p7;

    move-result-object v0

    .line 50
    :goto_0
    instance-of v1, v0, Lcom/android/tools/r8/internal/Fs0;

    if-eqz v1, :cond_1

    return-void

    .line 51
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Gt0;->h()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 52
    invoke-static {}, Lcom/android/tools/r8/internal/l20;->a()Lcom/android/tools/r8/internal/p20;

    move-result-object v0

    .line 53
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/g1;

    iget-object v1, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    sget-object v2, Lcom/android/tools/r8/internal/o7;->a:Lcom/android/tools/r8/internal/o7;

    invoke-virtual {v0, p1, v1, v2}, Lcom/android/tools/r8/internal/p20;->a(Lcom/android/tools/r8/graph/g1;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/E1;)V

    return-void

    .line 57
    :cond_2
    sget-boolean v1, Lcom/android/tools/r8/internal/v3;->g:Z

    if-nez v1, :cond_4

    iget-object v2, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v2, p1}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/B5;)Lcom/android/tools/r8/shaking/k1;

    move-result-object v2

    iget-object v3, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v2, v3, p1}, Lcom/android/tools/r8/shaking/s1;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/C5;)Z

    move-result v2

    if-eqz v2, :cond_3

    goto :goto_1

    :cond_3
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 58
    :cond_4
    :goto_1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    .line 59
    check-cast v2, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v2

    .line 60
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->H0()Z

    move-result v3

    if-eqz v3, :cond_6

    .line 61
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Gt0;->a()Lcom/android/tools/r8/internal/Lf;

    move-result-object v0

    .line 62
    iget-object v0, v0, Lcom/android/tools/r8/internal/Lf;->e:Lcom/android/tools/r8/internal/qZ;

    .line 63
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qZ;->e()Z

    move-result v1

    if-eqz v1, :cond_5

    .line 64
    iget-object v0, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 65
    iget-object v0, v0, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 66
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-static {v2}, Lcom/android/tools/r8/internal/F1;->b(Lcom/android/tools/r8/graph/J2;)V

    sget-object v0, Lcom/android/tools/r8/internal/gk0;->b:Lcom/android/tools/r8/internal/gk0;

    .line 67
    invoke-static {}, Lcom/android/tools/r8/internal/l20;->a()Lcom/android/tools/r8/internal/p20;

    move-result-object v1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/g1;

    iget-object v3, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, v2, v3, v0}, Lcom/android/tools/r8/internal/p20;->a(Lcom/android/tools/r8/graph/g1;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/E1;)V

    .line 68
    sget-boolean v0, Lcom/android/tools/r8/internal/Gs;->a:Z

    .line 69
    sget-object v0, Lcom/android/tools/r8/internal/Is;->d:Lcom/android/tools/r8/internal/Is;

    .line 70
    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/internal/v3;->a(Lcom/android/tools/r8/graph/B5;Lcom/android/tools/r8/internal/Gs;)V

    goto :goto_3

    .line 71
    :cond_5
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qZ;->d()Z

    move-result v0

    if-eqz v0, :cond_a

    .line 72
    sget-boolean v0, Lcom/android/tools/r8/internal/Gs;->a:Z

    sget-object v0, Lcom/android/tools/r8/internal/iZ;->b:Lcom/android/tools/r8/internal/iZ;

    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/internal/v3;->a(Lcom/android/tools/r8/graph/B5;Lcom/android/tools/r8/internal/Gs;)V

    goto :goto_3

    .line 74
    :cond_6
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->L0()Z

    move-result v3

    if-eqz v3, :cond_7

    .line 75
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Gt0;->b()Lcom/android/tools/r8/internal/Of;

    move-result-object v0

    .line 76
    iget-object v1, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Of;->a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/E1;

    move-result-object v1

    .line 77
    invoke-static {}, Lcom/android/tools/r8/internal/l20;->a()Lcom/android/tools/r8/internal/p20;

    move-result-object v2

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/graph/g1;

    iget-object v4, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v2, v3, v4, v1}, Lcom/android/tools/r8/internal/p20;->a(Lcom/android/tools/r8/graph/g1;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/E1;)V

    .line 78
    iget-object v0, v0, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    .line 79
    invoke-virtual {p0, p1, v0}, Lcom/android/tools/r8/internal/v3;->a(Lcom/android/tools/r8/graph/B5;Lcom/android/tools/r8/internal/Gs;)V

    goto :goto_3

    :cond_7
    if-nez v1, :cond_9

    .line 81
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->S0()Z

    move-result v1

    if-eqz v1, :cond_8

    goto :goto_2

    :cond_8
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 82
    :cond_9
    :goto_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Gt0;->e()Lcom/android/tools/r8/internal/Xf;

    move-result-object v0

    .line 83
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xf;->d:Lcom/android/tools/r8/internal/E1;

    .line 84
    invoke-static {}, Lcom/android/tools/r8/internal/l20;->a()Lcom/android/tools/r8/internal/p20;

    move-result-object v1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/g1;

    iget-object v2, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, p1, v2, v0}, Lcom/android/tools/r8/internal/p20;->a(Lcom/android/tools/r8/graph/g1;Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/E1;)V

    :cond_a
    :goto_3
    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/B5;Lcom/android/tools/r8/internal/Gs;)V
    .locals 3

    .line 85
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Gs;->f()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 87
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Gs;->a()Lcom/android/tools/r8/internal/Is;

    move-result-object v0

    .line 88
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    .line 89
    check-cast v1, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/l1;->getType()Lcom/android/tools/r8/graph/J2;

    move-result-object v1

    .line 90
    iget-object v2, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, v2}, Lcom/android/tools/r8/graph/J2;->b(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v1

    iget-object v2, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 91
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 92
    invoke-static {v2, v1}, Lcom/android/tools/r8/internal/Is;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/Is;

    move-result-object v1

    .line 93
    invoke-virtual {v0, v2, v1}, Lcom/android/tools/r8/internal/Is;->b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Is;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 94
    :cond_0
    invoke-static {}, Lcom/android/tools/r8/internal/l20;->a()Lcom/android/tools/r8/internal/p20;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 95
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/g1;

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/p20;->a(Lcom/android/tools/r8/graph/g1;Lcom/android/tools/r8/internal/Gs;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/l60;Lcom/android/tools/r8/internal/LV;)V
    .locals 9

    .line 101
    invoke-interface {p3}, Lcom/android/tools/r8/internal/LV;->g()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 102
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/j1;->m1()Z

    move-result p3

    if-eqz p3, :cond_0

    return-void

    .line 110
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/graph/j1;

    .line 111
    iget-object p3, p3, Lcom/android/tools/r8/graph/j1;->g:Lcom/android/tools/r8/graph/H4;

    .line 112
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/H4;->H()Z

    move-result p3

    if-eqz p3, :cond_1

    .line 113
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->D()Lcom/android/tools/r8/internal/iV;

    move-result-object p3

    invoke-virtual {p3}, Lcom/android/tools/r8/internal/iV;->E()Z

    move-result p3

    if-nez p3, :cond_1

    .line 114
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/j1;->W0()Lcom/android/tools/r8/graph/D3$g;

    move-result-object p3

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/D3$g;->b()Z

    move-result p3

    if-nez p3, :cond_1

    iget-object p3, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 115
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    .line 116
    iget-object p3, p3, Lcom/android/tools/r8/shaking/i;->n:Ljava/util/Set;

    .line 117
    invoke-interface {p3, v0}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p3

    if-nez p3, :cond_1

    .line 118
    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/hn;->a(Lcom/android/tools/r8/graph/H0;)Z

    goto :goto_0

    .line 119
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p2}, Lcom/android/tools/r8/graph/j1;->i1()Z

    move-result p2

    if-eqz p2, :cond_2

    .line 120
    iget-object p2, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p1, p2}, Lcom/android/tools/r8/graph/D5;->c(Lcom/android/tools/r8/graph/y;)V

    .line 121
    iget-object p2, p0, Lcom/android/tools/r8/internal/v3;->b:Lcom/android/tools/r8/internal/x50;

    invoke-virtual {p2, p1}, Lcom/android/tools/r8/internal/fA;->c(Lcom/android/tools/r8/graph/D5;)V

    .line 122
    iget-object p2, p0, Lcom/android/tools/r8/internal/v3;->f:Lcom/android/tools/r8/internal/O40;

    iget-object p3, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/y;->B()Lcom/android/tools/r8/internal/Fy;

    move-result-object p3

    .line 123
    iget-object p2, p2, Lcom/android/tools/r8/internal/O40;->a:Lcom/android/tools/r8/internal/JS;

    .line 124
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {p2, p3, p1}, Lcom/android/tools/r8/internal/JS;->a(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/x2;)V

    :cond_2
    :goto_0
    return-void

    .line 125
    :cond_3
    iget-object p2, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p2, p1}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object p2

    iget-object v0, p0, Lcom/android/tools/r8/internal/v3;->e:Lcom/android/tools/r8/utils/w;

    .line 126
    invoke-virtual {p2, v0}, Lcom/android/tools/r8/shaking/n1;->c(Lcom/android/tools/r8/shaking/L0;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 127
    iget-boolean p2, p2, Lcom/android/tools/r8/shaking/v1;->o:Z

    if-eqz p2, :cond_4

    goto :goto_1

    .line 128
    :cond_4
    sget-object p3, Lcom/android/tools/r8/internal/zs0;->a:Lcom/android/tools/r8/internal/zs0;

    .line 131
    :goto_1
    iget-object p2, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p2, p1}, Lcom/android/tools/r8/graph/y;->a(Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/shaking/v1;

    move-result-object p2

    iget-object v0, p0, Lcom/android/tools/r8/internal/v3;->e:Lcom/android/tools/r8/utils/w;

    .line 132
    invoke-virtual {p2, v0}, Lcom/android/tools/r8/shaking/v1;->j(Lcom/android/tools/r8/shaking/L0;)Z

    move-result p2

    if-eqz p2, :cond_5

    .line 133
    invoke-virtual {p0, p1, p3}, Lcom/android/tools/r8/internal/v3;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/LV;)Lcom/android/tools/r8/internal/LV;

    move-result-object p3

    .line 136
    :cond_5
    invoke-interface {p3}, Lcom/android/tools/r8/internal/LV;->isUnknown()Z

    move-result p2

    if-eqz p2, :cond_6

    return-void

    .line 141
    :cond_6
    invoke-interface {p3}, Lcom/android/tools/r8/internal/LV;->d()Lcom/android/tools/r8/internal/Pf;

    move-result-object p2

    .line 142
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 143
    instance-of p3, p2, Lcom/android/tools/r8/internal/Vf;

    if-eqz p3, :cond_8

    .line 144
    sget-boolean p1, Lcom/android/tools/r8/internal/v3;->g:Z

    if-eqz p1, :cond_7

    return-void

    :cond_7
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 148
    :cond_8
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/MV;->b()Lcom/android/tools/r8/internal/Qf;

    move-result-object p2

    const/4 p3, 0x0

    move v0, p3

    .line 149
    :goto_2
    iget-object v1, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    .line 150
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_d

    .line 151
    iget-object v1, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/Gt0;

    .line 152
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Gt0;->c()Lcom/android/tools/r8/internal/bg;

    move-result-object v2

    if-eqz v2, :cond_c

    .line 153
    instance-of v1, v2, Lcom/android/tools/r8/internal/Of;

    if-nez v1, :cond_9

    goto :goto_4

    .line 154
    :cond_9
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/Gt0;->b()Lcom/android/tools/r8/internal/Of;

    move-result-object v1

    .line 155
    iget-object v1, v1, Lcom/android/tools/r8/internal/Of;->f:Lcom/android/tools/r8/internal/Gs;

    .line 156
    invoke-virtual {p1, v0}, Lcom/android/tools/r8/graph/H0;->a(I)Lcom/android/tools/r8/graph/J2;

    move-result-object v6

    .line 157
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Gs;->l()Z

    move-result v3

    if-eqz v3, :cond_a

    goto :goto_4

    .line 160
    :cond_a
    iget-object v3, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 161
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v4

    .line 162
    invoke-static {v3, v1, v6, v4}, Lcom/android/tools/r8/internal/Fu0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Gs;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;)Lcom/android/tools/r8/internal/Gs;

    move-result-object v3

    .line 163
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Gs;->l()Z

    move-result v3

    if-eqz v3, :cond_b

    goto :goto_3

    .line 166
    :cond_b
    iget-object v3, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v6, v3}, Lcom/android/tools/r8/graph/J2;->b(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v3

    .line 167
    invoke-virtual {v1, v3}, Lcom/android/tools/r8/internal/Gs;->a(Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v1

    .line 168
    iget-object v4, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {v1, v3, v4}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/internal/sr0;Lcom/android/tools/r8/graph/y;)Z

    move-result v1

    if-nez v1, :cond_c

    :goto_3
    const/4 v5, 0x0

    .line 169
    iget-object v3, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    new-instance v4, Lcom/android/tools/r8/internal/Of;

    .line 173
    sget-object v1, Lcom/android/tools/r8/internal/o7;->a:Lcom/android/tools/r8/internal/o7;

    invoke-static {}, Lcom/android/tools/r8/internal/Gs;->m()Lcom/android/tools/r8/internal/Is;

    move-result-object v7

    .line 174
    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object v8

    invoke-direct {v4, v1, v7, v8}, Lcom/android/tools/r8/internal/Of;-><init>(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/internal/Gs;Ljava/util/Set;)V

    .line 175
    sget-object v7, Lcom/android/tools/r8/internal/a2;->a:Lcom/android/tools/r8/internal/a2;

    .line 176
    invoke-virtual/range {v2 .. v7}, Lcom/android/tools/r8/internal/bg;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Gt0;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/a2;)Lcom/android/tools/r8/internal/SY;

    move-result-object v1

    .line 177
    invoke-virtual {p2, v0, v1}, Lcom/android/tools/r8/internal/Qf;->a(ILcom/android/tools/r8/internal/Gt0;)V

    :cond_c
    :goto_4
    add-int/lit8 v0, v0, 0x1

    goto :goto_2

    .line 178
    :cond_d
    iget-boolean v0, p2, Lcom/android/tools/r8/internal/Qf;->b:Z

    iget-object v1, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    invoke-static {v1, v0}, Lcom/android/tools/r8/internal/Qf;->a(Ljava/util/List;Z)Z

    move-result v0

    if-eqz v0, :cond_e

    return-void

    .line 179
    :cond_e
    sget-boolean v0, Lcom/android/tools/r8/internal/v3;->g:Z

    if-nez v0, :cond_10

    .line 180
    iget-object v1, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    .line 181
    invoke-interface {v1}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object v1

    sget-object v2, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda5;->INSTANCE:Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda5;

    invoke-interface {v1, v2}, Ljava/util/stream/Stream;->noneMatch(Ljava/util/function/Predicate;)Z

    move-result v1

    if-eqz v1, :cond_f

    goto :goto_5

    :cond_f
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_10
    :goto_5
    if-nez v0, :cond_12

    .line 182
    iget-object v0, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    .line 183
    invoke-interface {v0}, Ljava/util/List;->stream()Ljava/util/stream/Stream;

    move-result-object v0

    sget-object v1, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda6;->INSTANCE:Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda6;

    .line 184
    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object v0

    sget-object v1, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda4;

    .line 185
    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->map(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object v0

    sget-object v1, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda7;->INSTANCE:Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda7;

    .line 186
    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->noneMatch(Ljava/util/function/Predicate;)Z

    move-result v0

    if-eqz v0, :cond_11

    goto :goto_6

    .line 187
    :cond_11
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 188
    :cond_12
    :goto_6
    iget-object v0, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_1c

    .line 189
    invoke-static {}, Lcom/android/tools/r8/internal/l20;->a()Lcom/android/tools/r8/internal/p20;

    move-result-object v0

    iget-object v1, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    .line 190
    new-instance v2, Lcom/android/tools/r8/internal/Mf;

    .line 191
    iget-object v3, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    .line 192
    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    .line 193
    new-instance v4, Lcom/android/tools/r8/internal/wF;

    invoke-direct {v4, v3}, Lcom/android/tools/r8/internal/wF;-><init>(I)V

    new-instance v5, Lcom/android/tools/r8/internal/wF;

    invoke-direct {v5, v3}, Lcom/android/tools/r8/internal/wF;-><init>(I)V

    invoke-direct {v2, v3, v4, v5}, Lcom/android/tools/r8/internal/Mf;-><init>(ILcom/android/tools/r8/internal/IF;Lcom/android/tools/r8/internal/IF;)V

    const/4 v3, 0x1

    move v4, p3

    .line 194
    :goto_7
    iget-object v5, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v5

    if-ge v4, v5, :cond_1a

    .line 195
    iget-object v5, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    invoke-interface {v5, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/internal/Gt0;

    .line 196
    invoke-virtual {v5}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 197
    instance-of v6, v5, Lcom/android/tools/r8/internal/Fs0;

    if-eqz v6, :cond_13

    goto/16 :goto_9

    .line 198
    :cond_13
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/Gt0;->c()Lcom/android/tools/r8/internal/bg;

    move-result-object v5

    .line 201
    invoke-virtual {v5, v1}, Lcom/android/tools/r8/internal/Gt0;->a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/E1;

    move-result-object v6

    .line 202
    invoke-virtual {v6}, Lcom/android/tools/r8/internal/E1;->G()Z

    move-result v7

    if-eqz v7, :cond_14

    .line 203
    iget-object v3, v2, Lcom/android/tools/r8/internal/Mf;->c:Lcom/android/tools/r8/internal/IF;

    invoke-interface {v3, v4, v6}, Lcom/android/tools/r8/internal/xF;->a(ILjava/lang/Object;)Ljava/lang/Object;

    move v3, p3

    .line 208
    :cond_14
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v6

    check-cast v6, Lcom/android/tools/r8/graph/j1;

    .line 209
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v7

    .line 210
    check-cast v7, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v6}, Lcom/android/tools/r8/graph/j1;->y0()Z

    move-result v6

    invoke-virtual {v7, v4, v6}, Lcom/android/tools/r8/graph/x2;->a(IZ)Lcom/android/tools/r8/graph/J2;

    move-result-object v6

    .line 211
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/J2;->T0()Z

    move-result v7

    if-eqz v7, :cond_19

    .line 212
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->h()Lcom/android/tools/r8/internal/qZ;

    move-result-object v7

    .line 213
    invoke-static {v6, v7, v1}, Lcom/android/tools/r8/internal/sr0;->a(Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/internal/qZ;Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/sr0;

    move-result-object v7

    .line 214
    invoke-static {v1, v7}, Lcom/android/tools/r8/internal/Gs;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/sr0;)Lcom/android/tools/r8/internal/Is;

    move-result-object v7

    .line 215
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/J2;->H0()Z

    move-result v8

    if-eqz v8, :cond_18

    .line 216
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/Gt0;->a()Lcom/android/tools/r8/internal/Lf;

    move-result-object v5

    .line 217
    iget-object v5, v5, Lcom/android/tools/r8/internal/Lf;->e:Lcom/android/tools/r8/internal/qZ;

    .line 218
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/qZ;->e()Z

    move-result v8

    if-eqz v8, :cond_15

    .line 219
    iget-object v3, v2, Lcom/android/tools/r8/internal/Mf;->c:Lcom/android/tools/r8/internal/IF;

    .line 220
    iget-object v5, v1, Lcom/android/tools/r8/graph/y;->t:Lcom/android/tools/r8/internal/F1;

    .line 221
    invoke-virtual {v5}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-static {v6}, Lcom/android/tools/r8/internal/F1;->b(Lcom/android/tools/r8/graph/J2;)V

    sget-object v5, Lcom/android/tools/r8/internal/gk0;->b:Lcom/android/tools/r8/internal/gk0;

    .line 222
    invoke-interface {v3, v4, v5}, Lcom/android/tools/r8/internal/xF;->a(ILjava/lang/Object;)Ljava/lang/Object;

    :goto_8
    move v3, p3

    goto :goto_9

    .line 225
    :cond_15
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/qZ;->d()Z

    move-result v5

    if-eqz v5, :cond_16

    .line 226
    iget-object v3, v2, Lcom/android/tools/r8/internal/Mf;->b:Lcom/android/tools/r8/internal/IF;

    .line 227
    invoke-static {}, Lcom/android/tools/r8/internal/qZ;->b()Lcom/android/tools/r8/internal/qZ;

    move-result-object v5

    invoke-virtual {v7, v5}, Lcom/android/tools/r8/internal/Is;->b(Lcom/android/tools/r8/internal/qZ;)Lcom/android/tools/r8/internal/Is;

    move-result-object v5

    .line 228
    invoke-interface {v3, v4, v5}, Lcom/android/tools/r8/internal/xF;->a(ILjava/lang/Object;)Ljava/lang/Object;

    goto :goto_8

    .line 235
    :cond_16
    sget-boolean v5, Lcom/android/tools/r8/internal/Mf;->d:Z

    if-eqz v5, :cond_17

    goto :goto_9

    :cond_17
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 237
    :cond_18
    invoke-virtual {v6}, Lcom/android/tools/r8/graph/J2;->L0()Z

    move-result v6

    if-eqz v6, :cond_19

    .line 238
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/Gt0;->g()Lcom/android/tools/r8/internal/ag;

    move-result-object v5

    invoke-virtual {v5}, Lcom/android/tools/r8/internal/ag;->x()Lcom/android/tools/r8/internal/Gs;

    move-result-object v5

    .line 239
    invoke-virtual {v5}, Lcom/android/tools/r8/internal/Gs;->l()Z

    move-result v6

    if-nez v6, :cond_19

    .line 240
    iget-object v3, v2, Lcom/android/tools/r8/internal/Mf;->b:Lcom/android/tools/r8/internal/IF;

    invoke-interface {v3, v4, v5}, Lcom/android/tools/r8/internal/xF;->a(ILjava/lang/Object;)Ljava/lang/Object;

    goto :goto_8

    :cond_19
    :goto_9
    add-int/lit8 v4, v4, 0x1

    goto/16 :goto_7

    :cond_1a
    if-eqz v3, :cond_1b

    .line 246
    sget-object v2, Lcom/android/tools/r8/internal/Jp0;->a:Lcom/android/tools/r8/internal/Jp0;

    .line 247
    :cond_1b
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 248
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/graph/j1;

    .line 249
    invoke-virtual {p3}, Lcom/android/tools/r8/graph/j1;->Y0()Lcom/android/tools/r8/internal/jX;

    move-result-object p3

    .line 250
    iput-object v2, p3, Lcom/android/tools/r8/internal/jX;->b:Lcom/android/tools/r8/internal/r8;

    .line 251
    :cond_1c
    iget-boolean p3, p2, Lcom/android/tools/r8/internal/Qf;->b:Z

    if-nez p3, :cond_1d

    .line 252
    invoke-static {}, Lcom/android/tools/r8/internal/l20;->a()Lcom/android/tools/r8/internal/p20;

    move-result-object p3

    sget-object v0, Lcom/android/tools/r8/internal/u20;->b:Lcom/android/tools/r8/internal/u20;

    invoke-virtual {p3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 253
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p3

    check-cast p3, Lcom/android/tools/r8/graph/j1;

    invoke-virtual {p3}, Lcom/android/tools/r8/graph/j1;->Y0()Lcom/android/tools/r8/internal/jX;

    move-result-object p3

    .line 254
    iput-object v0, p3, Lcom/android/tools/r8/internal/jX;->k:Lcom/android/tools/r8/internal/u20;

    .line 255
    :cond_1d
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/H0;->D()Lcom/android/tools/r8/internal/iV;

    move-result-object p3

    .line 256
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/iV;->G()Z

    move-result v0

    if-eqz v0, :cond_1e

    .line 258
    invoke-virtual {p3}, Lcom/android/tools/r8/internal/iV;->t()I

    move-result p3

    .line 259
    iget-object p2, p2, Lcom/android/tools/r8/internal/Qf;->c:Ljava/util/List;

    invoke-interface {p2, p3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/internal/Gt0;

    .line 260
    invoke-static {}, Lcom/android/tools/r8/internal/l20;->a()Lcom/android/tools/r8/internal/p20;

    move-result-object p3

    .line 262
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/j1;

    iget-object v0, p0, Lcom/android/tools/r8/internal/v3;->a:Lcom/android/tools/r8/graph/y;

    invoke-virtual {p2, v0}, Lcom/android/tools/r8/internal/Gt0;->a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/E1;

    move-result-object p2

    .line 263
    invoke-virtual {p3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 264
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/j1;->Y0()Lcom/android/tools/r8/internal/jX;

    move-result-object p3

    invoke-virtual {p3, p2, p1}, Lcom/android/tools/r8/internal/jX;->a(Lcom/android/tools/r8/internal/E1;Lcom/android/tools/r8/graph/j1;)V

    :cond_1e
    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/l60;Lcom/android/tools/r8/graph/D5;)V
    .locals 3

    .line 96
    iget-object v0, p0, Lcom/android/tools/r8/internal/v3;->d:Lcom/android/tools/r8/internal/OV;

    .line 97
    sget-object v1, Lcom/android/tools/r8/internal/g7;->a:Lcom/android/tools/r8/internal/g7;

    .line 98
    iget-object v2, v0, Lcom/android/tools/r8/internal/NV;->a:Ljava/util/AbstractMap;

    .line 99
    invoke-virtual {v0, p2}, Lcom/android/tools/r8/internal/OV;->a(Lcom/android/tools/r8/graph/D5;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v2, v0}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/internal/LV;

    if-eqz v0, :cond_0

    move-object v1, v0

    .line 100
    :cond_0
    invoke-virtual {p0, p2, p1, v1}, Lcom/android/tools/r8/internal/v3;->a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/l60;Lcom/android/tools/r8/internal/LV;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/internal/l60;Lcom/android/tools/r8/graph/E2;)V
    .locals 0

    .line 31
    invoke-virtual {p0, p2}, Lcom/android/tools/r8/internal/v3;->a(Lcom/android/tools/r8/graph/E2;)Lcom/android/tools/r8/internal/l60;

    move-result-object p2

    .line 32
    iget-object p1, p1, Lcom/android/tools/r8/internal/hn;->b:Ljava/util/Map;

    .line 33
    iget-object p2, p2, Lcom/android/tools/r8/internal/hn;->b:Ljava/util/Map;

    invoke-interface {p1, p2}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    return-void
.end method
