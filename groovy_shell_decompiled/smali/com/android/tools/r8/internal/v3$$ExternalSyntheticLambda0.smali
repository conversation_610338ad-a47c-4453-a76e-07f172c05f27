.class public final synthetic Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/QG;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/v3;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/D5;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/v3;Lcom/android/tools/r8/graph/D5;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/v3;

    iput-object p2, p0, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/D5;

    return-void
.end method


# virtual methods
.method public final a(ILjava/lang/Object;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda0;->f$0:Lcom/android/tools/r8/internal/v3;

    iget-object v1, p0, Lcom/android/tools/r8/internal/v3$$ExternalSyntheticLambda0;->f$1:Lcom/android/tools/r8/graph/D5;

    check-cast p2, Lcom/android/tools/r8/internal/Gt0;

    invoke-virtual {v0, v1, p1, p2}, Lcom/android/tools/r8/internal/v3;->a(Lcom/android/tools/r8/graph/D5;ILcom/android/tools/r8/internal/Gt0;)Lcom/android/tools/r8/internal/Gt0;

    move-result-object p1

    return-object p1
.end method
