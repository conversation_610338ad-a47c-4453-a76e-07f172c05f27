.class public final Lcom/android/tools/r8/internal/t;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final c:Lcom/android/tools/r8/internal/t;

.field public static final d:Lcom/android/tools/r8/internal/t;


# instance fields
.field public final a:Z

.field public final b:Ljava/lang/RuntimeException;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/C;->e:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 2
    sput-object v1, Lcom/android/tools/r8/internal/t;->d:Lcom/android/tools/r8/internal/t;

    .line 3
    sput-object v1, Lcom/android/tools/r8/internal/t;->c:Lcom/android/tools/r8/internal/t;

    goto :goto_0

    .line 5
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/t;

    const/4 v2, 0x0

    invoke-direct {v0, v2, v1}, Lcom/android/tools/r8/internal/t;-><init>(ZLjava/lang/RuntimeException;)V

    sput-object v0, Lcom/android/tools/r8/internal/t;->d:Lcom/android/tools/r8/internal/t;

    .line 6
    new-instance v0, Lcom/android/tools/r8/internal/t;

    const/4 v2, 0x1

    invoke-direct {v0, v2, v1}, Lcom/android/tools/r8/internal/t;-><init>(ZLjava/lang/RuntimeException;)V

    sput-object v0, Lcom/android/tools/r8/internal/t;->c:Lcom/android/tools/r8/internal/t;

    :goto_0
    return-void
.end method

.method public constructor <init>(ZLjava/lang/RuntimeException;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-boolean p1, p0, Lcom/android/tools/r8/internal/t;->a:Z

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/internal/t;->b:Ljava/lang/RuntimeException;

    return-void
.end method
