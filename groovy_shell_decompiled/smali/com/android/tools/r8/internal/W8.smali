.class public abstract Lcom/android/tools/r8/internal/W8;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/y;)Lcom/android/tools/r8/internal/W8;
    .locals 4

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    .line 3
    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 4
    iget-boolean v1, v1, Lcom/android/tools/r8/internal/QS;->a:Z

    const/4 v2, 0x0

    if-eqz v1, :cond_7

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    .line 6
    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 7
    iget-boolean v1, v1, Lcom/android/tools/r8/internal/QS;->a:Z

    if-eqz v1, :cond_1

    .line 8
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 9
    iget-object v1, v1, Lcom/android/tools/r8/internal/QS;->c:Lcom/android/tools/r8/internal/VS;

    .line 10
    iget-object v1, v1, Lcom/android/tools/r8/internal/VS;->k:Lcom/android/tools/r8/internal/iB;

    .line 11
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/iB;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    .line 12
    :cond_0
    new-instance v1, Lcom/android/tools/r8/internal/c60;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/c60;-><init>(Lcom/android/tools/r8/graph/y;)V

    goto :goto_1

    :cond_1
    :goto_0
    move-object v1, v2

    :goto_1
    if-eqz v1, :cond_2

    .line 13
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 14
    :cond_2
    sget-boolean v1, Lcom/android/tools/r8/internal/El;->c:Z

    if-nez v1, :cond_4

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    .line 15
    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 16
    iget-boolean v1, v1, Lcom/android/tools/r8/internal/QS;->a:Z

    if-eqz v1, :cond_3

    goto :goto_2

    .line 17
    :cond_3
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 19
    :cond_4
    :goto_2
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    iget-object v1, v1, Lcom/android/tools/r8/utils/w;->Q1:Lcom/android/tools/r8/internal/QS;

    .line 20
    iget-object v1, v1, Lcom/android/tools/r8/internal/QS;->c:Lcom/android/tools/r8/internal/VS;

    .line 21
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/VS;->h()Ljava/util/Map;

    move-result-object v1

    .line 22
    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_5

    move-object v1, v2

    goto :goto_3

    .line 25
    :cond_5
    new-instance v1, Lcom/android/tools/r8/internal/El;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/El;-><init>(Lcom/android/tools/r8/graph/y;)V

    :goto_3
    if-eqz v1, :cond_6

    .line 26
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 28
    :cond_6
    new-instance v1, Lcom/android/tools/r8/internal/Ol;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/Ol;-><init>(Lcom/android/tools/r8/graph/y;)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 29
    :cond_7
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/utils/w;->r()I

    move-result v1

    const/4 v3, 0x3

    if-ne v1, v3, :cond_8

    .line 30
    new-instance v1, Lcom/android/tools/r8/internal/E90;

    invoke-direct {v1, p0}, Lcom/android/tools/r8/internal/E90;-><init>(Lcom/android/tools/r8/graph/y;)V

    goto :goto_4

    :cond_8
    move-object v1, v2

    :goto_4
    if-eqz v1, :cond_9

    .line 31
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 32
    :cond_9
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->N()Lcom/android/tools/r8/utils/w;

    move-result-object v1

    invoke-virtual {v1}, Lcom/android/tools/r8/utils/w;->n0()Z

    move-result v1

    if-eqz v1, :cond_a

    new-instance v2, Lcom/android/tools/r8/internal/Nt0;

    invoke-direct {v2, p0}, Lcom/android/tools/r8/internal/Nt0;-><init>(Lcom/android/tools/r8/graph/y;)V

    :cond_a
    if-eqz v2, :cond_b

    .line 33
    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 35
    :cond_b
    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_c

    .line 36
    new-instance p0, Lcom/android/tools/r8/internal/U8;

    invoke-direct {p0}, Lcom/android/tools/r8/internal/U8;-><init>()V

    return-object p0

    .line 38
    :cond_c
    new-instance v1, Lcom/android/tools/r8/internal/V8;

    invoke-direct {v1, p0, v0}, Lcom/android/tools/r8/internal/V8;-><init>(Lcom/android/tools/r8/graph/y;Ljava/util/ArrayList;)V

    return-object v1
.end method


# virtual methods
.method public abstract a(Ljava/util/concurrent/ExecutorService;Lcom/android/tools/r8/internal/Y8;)V
.end method
