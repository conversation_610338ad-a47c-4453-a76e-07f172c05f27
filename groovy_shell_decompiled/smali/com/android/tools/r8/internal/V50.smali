.class public final Lcom/android/tools/r8/internal/V50;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/S20;


# instance fields
.field public final a:Lcom/android/tools/r8/internal/Yf;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/Yf;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/internal/V50;->a:Lcom/android/tools/r8/internal/Yf;

    return-void
.end method

.method public static a(Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/J50;)V
    .locals 1

    .line 4
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    .line 6
    invoke-interface {p1, v0}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/internal/J50;

    move-result-object p1

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/D5;->getHolder()Lcom/android/tools/r8/graph/E2;

    move-result-object p0

    .line 8
    invoke-interface {p0}, Lcom/android/tools/r8/graph/o0;->getReference()Lcom/android/tools/r8/graph/G2;

    move-result-object p0

    invoke-interface {p1, p0}, Lcom/android/tools/r8/internal/J50;->a(Lcom/android/tools/r8/graph/G2;)Lcom/android/tools/r8/internal/J50;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/y;)V
    .locals 1

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/internal/V50;->a:Lcom/android/tools/r8/internal/Yf;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/y;)V

    return-void
.end method

.method public final a(Ljava/util/List;Lcom/android/tools/r8/graph/D5;)V
    .locals 3

    .line 1
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/D5;

    .line 2
    iget-object v1, p0, Lcom/android/tools/r8/internal/V50;->a:Lcom/android/tools/r8/internal/Yf;

    new-instance v2, Lcom/android/tools/r8/internal/V50$$ExternalSyntheticLambda0;

    invoke-direct {v2, p2}, Lcom/android/tools/r8/internal/V50$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/D5;)V

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v1, v0, v2}, Lcom/android/tools/r8/internal/Yf;->a(Lcom/android/tools/r8/graph/x2;Ljava/util/function/Consumer;)V

    goto :goto_0

    :cond_0
    return-void
.end method
