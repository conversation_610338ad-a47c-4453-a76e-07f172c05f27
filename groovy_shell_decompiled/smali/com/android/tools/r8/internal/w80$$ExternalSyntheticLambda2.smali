.class public final synthetic Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Function;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/internal/w80;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/E0;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/y80;

.field public final synthetic f$3:Ljava/util/Map;

.field public final synthetic f$4:Lcom/android/tools/r8/internal/x80;

.field public final synthetic f$5:Lcom/android/tools/r8/internal/A80;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/internal/w80;Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/internal/y80;Ljava/util/Map;Lcom/android/tools/r8/internal/x80;Lcom/android/tools/r8/internal/A80;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/w80;

    iput-object p2, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$1:Lcom/android/tools/r8/graph/E0;

    iput-object p3, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$2:Lcom/android/tools/r8/internal/y80;

    iput-object p4, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$3:Ljava/util/Map;

    iput-object p5, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$4:Lcom/android/tools/r8/internal/x80;

    iput-object p6, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$5:Lcom/android/tools/r8/internal/A80;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    iget-object v0, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$0:Lcom/android/tools/r8/internal/w80;

    iget-object v1, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$1:Lcom/android/tools/r8/graph/E0;

    iget-object v2, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$2:Lcom/android/tools/r8/internal/y80;

    iget-object v3, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$3:Ljava/util/Map;

    iget-object v4, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$4:Lcom/android/tools/r8/internal/x80;

    iget-object v5, p0, Lcom/android/tools/r8/internal/w80$$ExternalSyntheticLambda2;->f$5:Lcom/android/tools/r8/internal/A80;

    move-object v6, p1

    check-cast v6, Lcom/android/tools/r8/graph/j1;

    invoke-virtual/range {v0 .. v6}, Lcom/android/tools/r8/internal/w80;->a(Lcom/android/tools/r8/graph/E0;Lcom/android/tools/r8/internal/y80;Ljava/util/Map;Lcom/android/tools/r8/internal/x80;Lcom/android/tools/r8/internal/A80;Lcom/android/tools/r8/graph/j1;)Lcom/android/tools/r8/graph/j1;

    move-result-object p1

    return-object p1
.end method
