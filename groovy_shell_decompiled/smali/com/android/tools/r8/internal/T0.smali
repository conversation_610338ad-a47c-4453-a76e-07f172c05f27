.class public Lcom/android/tools/r8/internal/T0;
.super Lcom/android/tools/r8/internal/ij0;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final synthetic b:Lcom/android/tools/r8/internal/U0;


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/internal/U0;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/android/tools/r8/internal/T0;->b:Lcom/android/tools/r8/internal/U0;

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/internal/ij0;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Lcom/android/tools/r8/internal/YW;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T0;->b:Lcom/android/tools/r8/internal/U0;

    return-object v0
.end method

.method public final clear()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/T0;->a()Lcom/android/tools/r8/internal/YW;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->clear()V

    return-void
.end method

.method public final contains(Ljava/lang/Object;)Z
    .locals 3

    .line 1
    instance-of v0, p1, Lcom/android/tools/r8/internal/ZW;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    .line 6
    check-cast p1, Lcom/android/tools/r8/internal/ZW;

    .line 7
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ZW;->a()I

    move-result v0

    if-gtz v0, :cond_0

    return v1

    .line 10
    :cond_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/T0;->a()Lcom/android/tools/r8/internal/YW;

    move-result-object v0

    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ZW;->b()Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v2}, Lcom/android/tools/r8/internal/YW;->b(Ljava/lang/Object;)I

    move-result v0

    .line 11
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ZW;->a()I

    move-result p1

    if-ne v0, p1, :cond_1

    const/4 v1, 0x1

    :cond_1
    return v1
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T0;->b:Lcom/android/tools/r8/internal/U0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/U0;->g()Ljava/util/Iterator;

    move-result-object v0

    return-object v0
.end method

.method public final remove(Ljava/lang/Object;)Z
    .locals 2

    .line 1
    instance-of v0, p1, Lcom/android/tools/r8/internal/ZW;

    if-eqz v0, :cond_0

    .line 2
    check-cast p1, Lcom/android/tools/r8/internal/ZW;

    .line 3
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ZW;->b()Ljava/lang/Object;

    move-result-object v0

    .line 4
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ZW;->a()I

    move-result p1

    if-eqz p1, :cond_0

    .line 9
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/T0;->a()Lcom/android/tools/r8/internal/YW;

    move-result-object v1

    .line 10
    invoke-interface {v1, p1, v0}, Lcom/android/tools/r8/internal/YW;->a(ILjava/lang/Object;)Z

    move-result p1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public final size()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/T0;->b:Lcom/android/tools/r8/internal/U0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/U0;->c()I

    move-result v0

    return v0
.end method
