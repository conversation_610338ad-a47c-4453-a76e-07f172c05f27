.class public final Lcom/android/tools/r8/internal/W70;
.super Lcom/android/tools/r8/internal/Nx;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/BU;


# instance fields
.field public c:I

.field public d:Ljava/util/List;

.field public e:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Nx;-><init>()V

    .line 103
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/internal/W70;->d:Ljava/util/List;

    const/4 v0, -0x1

    .line 227
    iput v0, p0, Lcom/android/tools/r8/internal/W70;->e:I

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/internal/N0;
    .locals 2

    .line 23
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/W70;->c()Lcom/android/tools/r8/internal/X70;

    move-result-object v0

    .line 24
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/X70;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 25
    :cond_0
    new-instance v0, Lcom/android/tools/r8/internal/hs0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/hs0;-><init>()V

    .line 26
    throw v0
.end method

.method public final bridge synthetic a(Lcom/android/tools/r8/internal/Vx;)Lcom/android/tools/r8/internal/Nx;
    .locals 0

    .line 22
    check-cast p1, Lcom/android/tools/r8/internal/X70;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/W70;->a(Lcom/android/tools/r8/internal/X70;)Lcom/android/tools/r8/internal/W70;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)Lcom/android/tools/r8/internal/Nx;
    .locals 2

    const/4 v0, 0x0

    .line 27
    :try_start_0
    sget-object v1, Lcom/android/tools/r8/internal/X70;->i:Lcom/android/tools/r8/internal/V70;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 28
    new-instance v1, Lcom/android/tools/r8/internal/X70;

    invoke-direct {v1, p1, p2}, Lcom/android/tools/r8/internal/X70;-><init>(Lcom/android/tools/r8/internal/be;Lcom/android/tools/r8/internal/Ku;)V
    :try_end_0
    .catch Lcom/android/tools/r8/internal/kI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 29
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/internal/W70;->a(Lcom/android/tools/r8/internal/X70;)Lcom/android/tools/r8/internal/W70;

    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 30
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/kI;->b:Lcom/android/tools/r8/internal/N0;

    .line 31
    check-cast p2, Lcom/android/tools/r8/internal/X70;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 32
    :try_start_2
    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_0

    .line 35
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/W70;->a(Lcom/android/tools/r8/internal/X70;)Lcom/android/tools/r8/internal/W70;

    .line 37
    :cond_0
    throw p1
.end method

.method public final a(Lcom/android/tools/r8/internal/X70;)Lcom/android/tools/r8/internal/W70;
    .locals 3

    .line 1
    sget-object v0, Lcom/android/tools/r8/internal/X70;->h:Lcom/android/tools/r8/internal/X70;

    if-ne p1, v0, :cond_0

    return-object p0

    .line 2
    :cond_0
    iget-object v0, p1, Lcom/android/tools/r8/internal/X70;->d:Ljava/util/List;

    .line 3
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_3

    .line 4
    iget-object v0, p0, Lcom/android/tools/r8/internal/W70;->d:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 5
    iget-object v0, p1, Lcom/android/tools/r8/internal/X70;->d:Ljava/util/List;

    .line 6
    iput-object v0, p0, Lcom/android/tools/r8/internal/W70;->d:Ljava/util/List;

    .line 7
    iget v0, p0, Lcom/android/tools/r8/internal/W70;->c:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/internal/W70;->c:I

    goto :goto_0

    .line 8
    :cond_1
    iget v0, p0, Lcom/android/tools/r8/internal/W70;->c:I

    and-int/2addr v0, v1

    if-eq v0, v1, :cond_2

    .line 9
    new-instance v0, Ljava/util/ArrayList;

    iget-object v2, p0, Lcom/android/tools/r8/internal/W70;->d:Ljava/util/List;

    invoke-direct {v0, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/internal/W70;->d:Ljava/util/List;

    .line 10
    iget v0, p0, Lcom/android/tools/r8/internal/W70;->c:I

    or-int/2addr v0, v1

    iput v0, p0, Lcom/android/tools/r8/internal/W70;->c:I

    .line 11
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/internal/W70;->d:Ljava/util/List;

    .line 12
    iget-object v2, p1, Lcom/android/tools/r8/internal/X70;->d:Ljava/util/List;

    .line 13
    invoke-interface {v0, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 14
    :cond_3
    :goto_0
    iget v0, p1, Lcom/android/tools/r8/internal/X70;->c:I

    and-int/2addr v0, v1

    if-ne v0, v1, :cond_4

    .line 15
    iget v0, p1, Lcom/android/tools/r8/internal/X70;->e:I

    .line 16
    iget v1, p0, Lcom/android/tools/r8/internal/W70;->c:I

    or-int/lit8 v1, v1, 0x2

    iput v1, p0, Lcom/android/tools/r8/internal/W70;->c:I

    .line 17
    iput v0, p0, Lcom/android/tools/r8/internal/W70;->e:I

    .line 18
    :cond_4
    iget-object v0, p0, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    .line 19
    iget-object p1, p1, Lcom/android/tools/r8/internal/X70;->b:Lcom/android/tools/r8/internal/Y7;

    .line 20
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Y7;->a(Lcom/android/tools/r8/internal/Y7;)Lcom/android/tools/r8/internal/Y7;

    move-result-object p1

    .line 21
    iput-object p1, p0, Lcom/android/tools/r8/internal/Nx;->b:Lcom/android/tools/r8/internal/Y7;

    return-object p0
.end method

.method public final c()Lcom/android/tools/r8/internal/X70;
    .locals 4

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/X70;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/internal/X70;-><init>(Lcom/android/tools/r8/internal/W70;)V

    .line 2
    iget v1, p0, Lcom/android/tools/r8/internal/W70;->c:I

    and-int/lit8 v2, v1, 0x1

    const/4 v3, 0x1

    if-ne v2, v3, :cond_0

    .line 5
    iget-object v2, p0, Lcom/android/tools/r8/internal/W70;->d:Ljava/util/List;

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v2

    iput-object v2, p0, Lcom/android/tools/r8/internal/W70;->d:Ljava/util/List;

    .line 6
    iget v2, p0, Lcom/android/tools/r8/internal/W70;->c:I

    and-int/lit8 v2, v2, -0x2

    iput v2, p0, Lcom/android/tools/r8/internal/W70;->c:I

    .line 8
    :cond_0
    iget-object v2, p0, Lcom/android/tools/r8/internal/W70;->d:Ljava/util/List;

    .line 9
    iput-object v2, v0, Lcom/android/tools/r8/internal/X70;->d:Ljava/util/List;

    const/4 v2, 0x2

    and-int/2addr v1, v2

    if-ne v1, v2, :cond_1

    goto :goto_0

    :cond_1
    const/4 v3, 0x0

    .line 10
    :goto_0
    iget v1, p0, Lcom/android/tools/r8/internal/W70;->e:I

    .line 11
    iput v1, v0, Lcom/android/tools/r8/internal/X70;->e:I

    .line 12
    iput v3, v0, Lcom/android/tools/r8/internal/X70;->c:I

    return-object v0
.end method

.method public final clone()Ljava/lang/Object;
    .locals 2

    .line 1
    new-instance v0, Lcom/android/tools/r8/internal/W70;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/W70;-><init>()V

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/W70;->c()Lcom/android/tools/r8/internal/X70;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/W70;->a(Lcom/android/tools/r8/internal/X70;)Lcom/android/tools/r8/internal/W70;

    move-result-object v0

    return-object v0
.end method
