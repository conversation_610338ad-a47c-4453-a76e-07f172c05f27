.class public abstract Lcom/android/tools/r8/internal/S10;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/R10;
    .locals 3

    .line 4
    iget-object p2, p2, Lcom/android/tools/r8/internal/vt0;->c:Lcom/android/tools/r8/internal/rD;

    invoke-virtual {p2}, Lcom/android/tools/r8/internal/rD;->u0()Lcom/android/tools/r8/internal/HX;

    move-result-object p2

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    .line 7
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/rD;->d()Lcom/android/tools/r8/internal/vt0;

    move-result-object p2

    .line 8
    invoke-static {v0, p2}, Lcom/android/tools/r8/internal/dA;->a(Lcom/android/tools/r8/graph/B1;Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/pI;

    move-result-object p2

    if-nez p2, :cond_0

    .line 9
    sget-object p0, Lcom/android/tools/r8/internal/dt;->a:Lcom/android/tools/r8/internal/dt;

    return-object p0

    .line 12
    :cond_0
    invoke-virtual {p2, p0, p1}, Lcom/android/tools/r8/internal/uI;->f(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;)Lcom/android/tools/r8/graph/H0;

    move-result-object v0

    if-nez v0, :cond_1

    .line 14
    sget-object p0, Lcom/android/tools/r8/internal/dt;->a:Lcom/android/tools/r8/internal/dt;

    return-object p0

    .line 19
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/G0;->d()Lcom/android/tools/r8/graph/h1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/graph/j1;

    .line 20
    invoke-virtual {v0}, Lcom/android/tools/r8/graph/j1;->O0()V

    .line 21
    iget-object v0, v0, Lcom/android/tools/r8/graph/j1;->m:Lcom/android/tools/r8/internal/iV;

    .line 22
    invoke-virtual {v0, p2}, Lcom/android/tools/r8/internal/iV;->a(Lcom/android/tools/r8/internal/pI;)Lcom/android/tools/r8/internal/ZC;

    move-result-object v0

    .line 23
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/ZC;->b()Lcom/android/tools/r8/internal/OC;

    move-result-object v0

    .line 24
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/OC;->a()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 25
    sget-object p0, Lcom/android/tools/r8/internal/dt;->a:Lcom/android/tools/r8/internal/dt;

    return-object p0

    .line 26
    :cond_2
    new-instance v1, Lcom/android/tools/r8/internal/Q10;

    invoke-direct {v1}, Lcom/android/tools/r8/internal/Q10;-><init>()V

    .line 27
    new-instance v2, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;

    invoke-direct {v2, p0, p2, v1, p1}, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/pI;Lcom/android/tools/r8/internal/Q10;Lcom/android/tools/r8/graph/D5;)V

    invoke-virtual {v0, p0, v2}, Lcom/android/tools/r8/internal/OC;->a(Lcom/android/tools/r8/graph/d1;Ljava/util/function/BiConsumer;)V

    .line 28
    iget-object p0, v1, Lcom/android/tools/r8/internal/Q10;->a:Ljava/util/IdentityHashMap;

    invoke-virtual {p0}, Ljava/util/IdentityHashMap;->isEmpty()Z

    move-result p0

    if-eqz p0, :cond_3

    sget-object p0, Lcom/android/tools/r8/internal/dt;->a:Lcom/android/tools/r8/internal/dt;

    goto :goto_0

    :cond_3
    new-instance p0, Lcom/android/tools/r8/internal/MY;

    iget-object p1, v1, Lcom/android/tools/r8/internal/Q10;->a:Ljava/util/IdentityHashMap;

    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/MY;-><init>(Ljava/util/IdentityHashMap;)V

    :goto_0
    return-object p0
.end method

.method public static a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/pI;Lcom/android/tools/r8/internal/Q10;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/graph/F0;Lcom/android/tools/r8/internal/MC;)V
    .locals 3

    .line 29
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->g()Lcom/android/tools/r8/graph/h;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/i;

    invoke-virtual {v0, p4}, Lcom/android/tools/r8/shaking/i;->c(Lcom/android/tools/r8/graph/F0;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 30
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/y;->b()Lcom/android/tools/r8/graph/B1;

    move-result-object v0

    iget-object v0, v0, Lcom/android/tools/r8/graph/B1;->G4:Lcom/android/tools/r8/graph/K1;

    invoke-virtual {p4}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/graph/l1;

    .line 31
    iget-object v2, v0, Lcom/android/tools/r8/graph/K1;->a:Lcom/android/tools/r8/graph/l1;

    if-eq v1, v2, :cond_1

    .line 32
    iget-object v0, v0, Lcom/android/tools/r8/graph/K1;->b:Lcom/android/tools/r8/graph/l1;

    if-ne v1, v0, :cond_0

    goto :goto_0

    :cond_0
    return-void

    .line 33
    :cond_1
    :goto_0
    invoke-interface {p5}, Lcom/android/tools/r8/internal/MC;->g()Z

    move-result v0

    if-eqz v0, :cond_4

    .line 35
    invoke-interface {p5}, Lcom/android/tools/r8/internal/MC;->e()Lcom/android/tools/r8/internal/LC;

    move-result-object p5

    .line 36
    iget p5, p5, Lcom/android/tools/r8/internal/LC;->a:I

    .line 37
    invoke-virtual {p1, p5}, Lcom/android/tools/r8/internal/mI;->b(I)Lcom/android/tools/r8/internal/vt0;

    move-result-object p1

    .line 38
    sget-object p5, Lcom/android/tools/r8/internal/L1;->a:Lcom/android/tools/r8/internal/K1;

    .line 39
    invoke-virtual {p1, p0, p3, p5}, Lcom/android/tools/r8/internal/vt0;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/L1;)Lcom/android/tools/r8/internal/E1;

    move-result-object p0

    .line 40
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 41
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result p1

    if-nez p1, :cond_7

    .line 42
    sget-boolean p1, Lcom/android/tools/r8/internal/Q10;->b:Z

    if-nez p1, :cond_3

    iget-object p1, p2, Lcom/android/tools/r8/internal/Q10;->a:Ljava/util/IdentityHashMap;

    invoke-virtual {p4}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p3

    invoke-virtual {p1, p3}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_2

    goto :goto_1

    :cond_2
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 43
    :cond_3
    :goto_1
    iget-object p1, p2, Lcom/android/tools/r8/internal/Q10;->a:Ljava/util/IdentityHashMap;

    invoke-virtual {p4}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {p1, p2, p0}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_3

    .line 44
    :cond_4
    invoke-interface {p5}, Lcom/android/tools/r8/internal/MC;->f()Z

    move-result p0

    if-eqz p0, :cond_7

    .line 45
    invoke-interface {p5}, Lcom/android/tools/r8/internal/MC;->i()Lcom/android/tools/r8/internal/ok0;

    move-result-object p0

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 46
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/E1;->isUnknown()Z

    move-result p1

    if-nez p1, :cond_7

    .line 47
    sget-boolean p1, Lcom/android/tools/r8/internal/Q10;->b:Z

    if-nez p1, :cond_6

    iget-object p1, p2, Lcom/android/tools/r8/internal/Q10;->a:Ljava/util/IdentityHashMap;

    invoke-virtual {p4}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p3

    invoke-virtual {p1, p3}, Ljava/util/IdentityHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_5

    goto :goto_2

    :cond_5
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0

    .line 48
    :cond_6
    :goto_2
    iget-object p1, p2, Lcom/android/tools/r8/internal/Q10;->a:Ljava/util/IdentityHashMap;

    invoke-virtual {p4}, Lcom/android/tools/r8/graph/G0;->u()Lcom/android/tools/r8/graph/s2;

    move-result-object p2

    check-cast p2, Lcom/android/tools/r8/graph/l1;

    invoke-virtual {p1, p2, p0}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_7
    :goto_3
    return-void
.end method

.method public static a(Lcom/android/tools/r8/internal/rD;)Z
    .locals 1

    .line 1
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    instance-of v0, p0, Lcom/android/tools/r8/internal/CX;

    if-nez v0, :cond_1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->n2()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/android/tools/r8/internal/rD;->m2()Z

    move-result p0

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0
.end method

.method public static b(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/R10;
    .locals 1

    .line 1
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/vt0;->l()Lcom/android/tools/r8/internal/vt0;

    move-result-object p2

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/S10$$ExternalSyntheticLambda1;

    invoke-virtual {p2, v0}, Lcom/android/tools/r8/internal/vt0;->c(Ljava/util/function/Predicate;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 3
    iget-object p2, p2, Lcom/android/tools/r8/internal/vt0;->c:Lcom/android/tools/r8/internal/rD;

    .line 4
    sget-object v0, Lcom/android/tools/r8/internal/L1;->a:Lcom/android/tools/r8/internal/K1;

    .line 5
    invoke-virtual {p2, p0, p1, v0}, Lcom/android/tools/r8/internal/rD;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/L1;)Lcom/android/tools/r8/internal/E1;

    move-result-object p0

    .line 6
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 7
    instance-of p1, p0, Lcom/android/tools/r8/internal/Yl0;

    if-eqz p1, :cond_0

    .line 8
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/E1;->v()Lcom/android/tools/r8/internal/Yl0;

    move-result-object p0

    .line 9
    iget-object p0, p0, Lcom/android/tools/r8/internal/Yl0;->a:Lcom/android/tools/r8/internal/R10;

    goto :goto_0

    .line 10
    :cond_0
    sget-object p0, Lcom/android/tools/r8/internal/dt;->a:Lcom/android/tools/r8/internal/dt;

    :goto_0
    return-object p0

    .line 11
    :cond_1
    sget-object v0, Lcom/android/tools/r8/internal/Ft0$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/internal/Ft0$$ExternalSyntheticLambda4;

    invoke-virtual {p2, v0}, Lcom/android/tools/r8/internal/vt0;->c(Ljava/util/function/Predicate;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 12
    invoke-static {p0, p1, p2}, Lcom/android/tools/r8/internal/S10;->a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/vt0;)Lcom/android/tools/r8/internal/R10;

    move-result-object p0

    return-object p0

    .line 14
    :cond_2
    sget-object p0, Lcom/android/tools/r8/internal/dt;->a:Lcom/android/tools/r8/internal/dt;

    return-object p0
.end method
