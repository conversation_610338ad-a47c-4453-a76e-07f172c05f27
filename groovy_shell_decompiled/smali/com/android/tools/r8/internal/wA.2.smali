.class public final Lcom/android/tools/r8/internal/wA;
.super Lcom/android/tools/r8/internal/Fy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final b:Lcom/android/tools/r8/internal/wA;

.field public static final synthetic c:Z = true


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 3
    new-instance v0, Lcom/android/tools/r8/internal/wA;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/wA;-><init>()V

    sput-object v0, Lcom/android/tools/r8/internal/wA;->b:Lcom/android/tools/r8/internal/wA;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Fy;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;
    .locals 1

    .line 2
    sget-boolean p2, Lcom/android/tools/r8/internal/wA;->c:Z

    if-nez p2, :cond_1

    if-eqz p4, :cond_1

    invoke-virtual {p4}, Lcom/android/tools/r8/internal/Fy;->l()Z

    move-result p2

    if-eqz p2, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    const/4 p2, 0x0

    .line 3
    sget-object p4, Lcom/android/tools/r8/graph/proto/j;->d:Lcom/android/tools/r8/graph/proto/j;

    .line 4
    new-instance v0, Lcom/android/tools/r8/internal/cV;

    invoke-direct {v0, p1, p2, p3, p4}, Lcom/android/tools/r8/internal/cV;-><init>(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/graph/proto/j;)V

    .line 5
    invoke-virtual {v0, p0}, Lcom/android/tools/r8/internal/cV;->a(Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Ey;)Lcom/android/tools/r8/internal/cV;
    .locals 0

    .line 8
    invoke-virtual {p0, p1, p2, p3, p4}, Lcom/android/tools/r8/internal/wA;->a(Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/graph/x2;Lcom/android/tools/r8/internal/JI;Lcom/android/tools/r8/internal/Fy;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    invoke-interface {p5, p1}, Lcom/android/tools/r8/internal/Ey;->a(Lcom/android/tools/r8/internal/cV;)Lcom/android/tools/r8/internal/cV;

    move-result-object p1

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Dy;)Lcom/android/tools/r8/internal/tv;
    .locals 1

    .line 6
    new-instance p2, Lcom/android/tools/r8/internal/tv;

    const/4 v0, 0x0

    invoke-direct {p2, p1, v0, v0, v0}, Lcom/android/tools/r8/internal/tv;-><init>(Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/l1;Lcom/android/tools/r8/graph/J2;Lcom/android/tools/r8/graph/J2;)V

    .line 7
    invoke-interface {p3, p2}, Lcom/android/tools/r8/internal/Dy;->a(Lcom/android/tools/r8/internal/tv;)Lcom/android/tools/r8/internal/tv;

    move-result-object p1

    return-object p1
.end method

.method public final a(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    return-object p1
.end method

.method public final a(Lcom/android/tools/r8/internal/Fy;)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method public final b(Lcom/android/tools/r8/graph/J2;)Ljava/lang/Iterable;
    .locals 0

    .line 2
    invoke-static {p1}, Lcom/android/tools/r8/internal/OI;->b(Ljava/lang/Object;)Ljava/lang/Iterable;

    move-result-object p1

    return-object p1
.end method

.method public final b(Lcom/android/tools/r8/internal/Fy;)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method public final e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/J2;
    .locals 0

    return-object p2
.end method

.method public final e(Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/graph/x2;)Lcom/android/tools/r8/graph/proto/j;
    .locals 0

    .line 2
    sget-object p1, Lcom/android/tools/r8/graph/proto/j;->d:Lcom/android/tools/r8/graph/proto/j;

    return-object p1
.end method

.method public final l()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final n()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method
