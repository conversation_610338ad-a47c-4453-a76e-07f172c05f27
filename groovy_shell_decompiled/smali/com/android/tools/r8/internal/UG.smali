.class public final Lcom/android/tools/r8/internal/UG;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Ljava/util/Iterator;


# instance fields
.field public final b:I

.field public final c:I

.field public d:Z

.field public e:I


# direct methods
.method public constructor <init>(III)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p3, p0, Lcom/android/tools/r8/internal/UG;->b:I

    .line 3
    iput p2, p0, Lcom/android/tools/r8/internal/UG;->c:I

    if-lez p3, :cond_0

    if-gt p1, p2, :cond_1

    goto :goto_0

    :cond_0
    if-lt p1, p2, :cond_1

    :goto_0
    const/4 p3, 0x1

    goto :goto_1

    :cond_1
    const/4 p3, 0x0

    .line 4
    :goto_1
    iput-boolean p3, p0, Lcom/android/tools/r8/internal/UG;->d:Z

    if-eqz p3, :cond_2

    goto :goto_2

    :cond_2
    move p1, p2

    .line 5
    :goto_2
    iput p1, p0, Lcom/android/tools/r8/internal/UG;->e:I

    return-void
.end method


# virtual methods
.method public final hasNext()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/android/tools/r8/internal/UG;->d:Z

    return v0
.end method

.method public final next()Ljava/lang/Object;
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/UG;->e:I

    .line 2
    iget v1, p0, Lcom/android/tools/r8/internal/UG;->c:I

    if-ne v0, v1, :cond_1

    .line 3
    iget-boolean v1, p0, Lcom/android/tools/r8/internal/UG;->d:Z

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    .line 4
    iput-boolean v1, p0, Lcom/android/tools/r8/internal/UG;->d:Z

    goto :goto_0

    .line 5
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0

    .line 9
    :cond_1
    iget v1, p0, Lcom/android/tools/r8/internal/UG;->b:I

    add-int/2addr v1, v0

    iput v1, p0, Lcom/android/tools/r8/internal/UG;->e:I

    .line 10
    :goto_0
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    return-object v0
.end method

.method public final remove()V
    .locals 2

    .line 1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Operation is not supported for read-only collection"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
