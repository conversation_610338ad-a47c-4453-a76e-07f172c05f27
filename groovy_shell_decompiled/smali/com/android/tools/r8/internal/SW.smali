.class public interface abstract Lcom/android/tools/r8/internal/SW;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# virtual methods
.method public abstract a()Ljava/util/Collection;
.end method

.method public abstract b()Ljava/util/Map;
.end method

.method public abstract clear()V
.end method

.method public abstract containsKey(Ljava/lang/Object;)Z
.end method

.method public abstract keySet()Ljava/util/Set;
.end method

.method public abstract size()I
.end method
