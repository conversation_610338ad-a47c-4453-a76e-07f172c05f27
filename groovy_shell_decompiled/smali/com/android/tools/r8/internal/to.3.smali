.class abstract Lcom/android/tools/r8/internal/to;
.super Lcom/android/tools/r8/internal/Hm;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic h:Z = true


# instance fields
.field public final f:S

.field public g:Lcom/android/tools/r8/graph/I2;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/graph/I2;)V
    .locals 1

    .line 6
    invoke-direct {p0}, Lcom/android/tools/r8/internal/Hm;-><init>()V

    .line 7
    sget-boolean v0, Lcom/android/tools/r8/internal/to;->h:Z

    if-nez v0, :cond_1

    if-ltz p1, :cond_0

    const/16 v0, 0xff

    if-gt p1, v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    int-to-short p1, p1

    .line 8
    iput-short p1, p0, Lcom/android/tools/r8/internal/to;->f:S

    .line 9
    iput-object p2, p0, Lcom/android/tools/r8/internal/to;->g:Lcom/android/tools/r8/graph/I2;

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/internal/Zo;[Lcom/android/tools/r8/graph/I2;)V
    .locals 5

    .line 1
    invoke-direct {p0, p2}, Lcom/android/tools/r8/internal/Hm;-><init>(Lcom/android/tools/r8/internal/Zo;)V

    int-to-short p1, p1

    .line 2
    iput-short p1, p0, Lcom/android/tools/r8/internal/to;->f:S

    .line 3
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Zo;->b()I

    move-result p1

    const v0, 0xffff

    and-int/2addr p1, v0

    int-to-char p1, p1

    int-to-long v1, p1

    .line 4
    invoke-virtual {p2}, Lcom/android/tools/r8/internal/Zo;->b()I

    move-result p1

    and-int/2addr p1, v0

    int-to-char p1, p1

    int-to-long p1, p1

    const-wide/32 v3, 0xffff

    and-long/2addr p1, v3

    const/16 v0, 0x10

    shl-long/2addr p1, v0

    and-long v0, v1, v3

    or-long/2addr p1, v0

    long-to-int p1, p1

    .line 5
    aget-object p1, p3, p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/to;->g:Lcom/android/tools/r8/graph/I2;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/internal/to;)I
    .locals 0

    .line 1
    iget-short p0, p0, Lcom/android/tools/r8/internal/to;->f:S

    return p0
.end method

.method public static a(Lcom/android/tools/r8/internal/ko0;)V
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/to$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/internal/to$$ExternalSyntheticLambda2;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ko0;->a(Ljava/util/function/ToIntFunction;)Lcom/android/tools/r8/internal/ko0;

    move-result-object p0

    sget-object v0, Lcom/android/tools/r8/internal/to$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/internal/to$$ExternalSyntheticLambda1;

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/internal/ko0;->e(Ljava/util/function/Function;)Lcom/android/tools/r8/internal/ko0;

    return-void
.end method

.method public static synthetic b(Lcom/android/tools/r8/internal/to;)Lcom/android/tools/r8/graph/I2;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/android/tools/r8/internal/to;->g:Lcom/android/tools/r8/graph/I2;

    return-object p0
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/Yo;Lcom/android/tools/r8/internal/Qe;)I
    .locals 1

    .line 10
    check-cast p1, Lcom/android/tools/r8/internal/to;

    sget-object v0, Lcom/android/tools/r8/internal/to$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/to$$ExternalSyntheticLambda0;

    invoke-virtual {p2, p0, p1, v0}, Lcom/android/tools/r8/internal/Qe;->a(Ljava/lang/Object;Ljava/lang/Object;Lcom/android/tools/r8/internal/io0;)I

    move-result p1

    return p1
.end method

.method public a(Lcom/android/tools/r8/graph/s5;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/internal/jQ;Ljava/nio/ShortBuffer;)V
    .locals 0

    .line 3
    iget-short p2, p0, Lcom/android/tools/r8/internal/to;->f:S

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result p3

    invoke-static {p2, p3, p6}, Lcom/android/tools/r8/internal/Yo;->a(IILjava/nio/ShortBuffer;)V

    .line 5
    iget-object p2, p0, Lcom/android/tools/r8/internal/to;->g:Lcom/android/tools/r8/graph/I2;

    .line 6
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 7
    iget-object p1, p1, Lcom/android/tools/r8/graph/s5;->j:Lcom/android/tools/r8/internal/Ea0;

    .line 8
    invoke-static {p2, p1}, Lcom/android/tools/r8/graph/s5;->a(Lcom/android/tools/r8/graph/Z3;Lcom/android/tools/r8/internal/w1;)I

    move-result p1

    int-to-long p1, p1

    .line 9
    invoke-static {p1, p2, p6}, Lcom/android/tools/r8/internal/Yo;->a(JLjava/nio/ShortBuffer;)V

    return-void
.end method

.method public a(Lcom/android/tools/r8/graph/y;Lcom/android/tools/r8/internal/Fy;Lcom/android/tools/r8/dex/M;Lcom/android/tools/r8/graph/D5;Lcom/android/tools/r8/internal/jQ;)V
    .locals 0

    .line 11
    iget-object p1, p0, Lcom/android/tools/r8/internal/to;->g:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 12
    invoke-interface {p3, p1}, Lcom/android/tools/r8/dex/M;->a(Lcom/android/tools/r8/graph/I2;)Z

    return-void
.end method

.method public b(Lcom/android/tools/r8/internal/Th0;)Ljava/lang/String;
    .locals 3

    .line 4
    iget-short v0, p0, Lcom/android/tools/r8/internal/to;->f:S

    iget-object v1, p0, Lcom/android/tools/r8/internal/to;->g:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {p1, v1}, Lcom/android/tools/r8/internal/Th0;->a(Lcom/android/tools/r8/graph/Z3;)Ljava/lang/String;

    move-result-object p1

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "v"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/internal/Yo;->b(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public final b(Lcom/android/tools/r8/internal/mz;)V
    .locals 2

    .line 2
    sget-object v0, Lcom/android/tools/r8/internal/to$$ExternalSyntheticLambda0;->INSTANCE:Lcom/android/tools/r8/internal/to$$ExternalSyntheticLambda0;

    check-cast p1, Lcom/android/tools/r8/internal/oz;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 3
    new-instance v1, Lcom/android/tools/r8/internal/nz;

    invoke-direct {v1, p0, p1}, Lcom/android/tools/r8/internal/nz;-><init>(Ljava/lang/Object;Lcom/android/tools/r8/internal/oz;)V

    invoke-interface {v0, v1}, Lcom/android/tools/r8/internal/io0;->a(Lcom/android/tools/r8/internal/ko0;)V

    return-void
.end method

.method public final hashCode()I
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/to;->g:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v0}, Lcom/android/tools/r8/graph/E;->hashCode()I

    move-result v0

    shl-int/lit8 v0, v0, 0x8

    iget-short v1, p0, Lcom/android/tools/r8/internal/to;->f:S

    or-int/2addr v0, v1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    xor-int/2addr v0, v1

    return v0
.end method
