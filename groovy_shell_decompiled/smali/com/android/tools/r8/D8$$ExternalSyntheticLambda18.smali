.class public final synthetic Lcom/android/tools/r8/D8$$ExternalSyntheticLambda18;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/lp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/utils/w;

.field public final synthetic f$1:Lcom/android/tools/r8/graph/y;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/graph/y;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda18;->f$0:Lcom/android/tools/r8/utils/w;

    iput-object p2, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda18;->f$1:Lcom/android/tools/r8/graph/y;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda18;->f$0:Lcom/android/tools/r8/utils/w;

    iget-object v1, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda18;->f$1:Lcom/android/tools/r8/graph/y;

    invoke-static {v0, v1}, Lcom/android/tools/r8/D8;->$r8$lambda$AvIfyN56FLvX-tw9RpgZiTF5MYE(Lcom/android/tools/r8/utils/w;Lcom/android/tools/r8/graph/y;)V

    return-void
.end method
