.class public final Lcom/android/tools/r8/androidapi/g;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/androidapi/f;


# static fields
.field public static final b:Lcom/android/tools/r8/androidapi/g;

.field public static final synthetic c:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/androidapi/f;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/androidapi/g;->c:Z

    .line 3
    new-instance v0, Lcom/android/tools/r8/androidapi/g;

    invoke-direct {v0}, Lcom/android/tools/r8/androidapi/g;-><init>()V

    sput-object v0, Lcom/android/tools/r8/androidapi/g;->b:Lcom/android/tools/r8/androidapi/g;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/internal/A2;)Lcom/android/tools/r8/internal/u20;
    .locals 1

    .line 1
    sget-boolean p1, Lcom/android/tools/r8/androidapi/g;->c:Z

    if-eqz p1, :cond_0

    .line 2
    sget-object p1, Lcom/android/tools/r8/internal/u20;->c:Lcom/android/tools/r8/internal/u20;

    return-object p1

    .line 3
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    const-string v0, "Cannot compute relationship for not set"

    invoke-direct {p1, v0}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p1
.end method

.method public final b(Lcom/android/tools/r8/internal/A2;)Lcom/android/tools/r8/internal/u20;
    .locals 1

    .line 1
    sget-boolean p1, Lcom/android/tools/r8/androidapi/g;->c:Z

    if-eqz p1, :cond_0

    .line 2
    sget-object p1, Lcom/android/tools/r8/internal/u20;->c:Lcom/android/tools/r8/internal/u20;

    return-object p1

    .line 3
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    const-string v0, "Cannot compute relationship for not set"

    invoke-direct {p1, v0}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p1
.end method

.method public final e(Lcom/android/tools/r8/androidapi/f;)Lcom/android/tools/r8/internal/u20;
    .locals 1

    .line 1
    sget-boolean p1, Lcom/android/tools/r8/androidapi/g;->c:Z

    if-eqz p1, :cond_0

    .line 2
    sget-object p1, Lcom/android/tools/r8/internal/u20;->c:Lcom/android/tools/r8/internal/u20;

    return-object p1

    .line 3
    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    const-string v0, "Cannot compute relationship for not set"

    invoke-direct {p1, v0}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw p1
.end method

.method public final equals(Ljava/lang/Object;)Z
    .locals 0

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final hashCode()I
    .locals 1

    .line 1
    invoke-static {p0}, Ljava/lang/System;->identityHashCode(Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final x()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
