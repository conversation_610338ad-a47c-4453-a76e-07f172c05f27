.class public final Lcom/android/tools/r8/r0;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic c:Z


# instance fields
.field public final a:Lcom/android/tools/r8/graph/E2;

.field public final b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-class v0, Lcom/android/tools/r8/ResourceShrinker;

    const/4 v0, 0x1

    sput-boolean v0, Lcom/android/tools/r8/r0;->c:Z

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/graph/E2;Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/r0;->a:Lcom/android/tools/r8/graph/E2;

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    return-void
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/g1;)Ljava/util/stream/Stream;
    .locals 0

    .line 177
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->m0()Lcom/android/tools/r8/graph/u0;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/u0;->stream()Ljava/util/stream/Stream;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic a(Lcom/android/tools/r8/graph/j1;)Ljava/util/stream/Stream;
    .locals 0

    .line 178
    invoke-virtual {p0}, Lcom/android/tools/r8/graph/b1;->m0()Lcom/android/tools/r8/graph/u0;

    move-result-object p0

    invoke-virtual {p0}, Lcom/android/tools/r8/graph/u0;->stream()Ljava/util/stream/Stream;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final a()V
    .locals 16

    move-object/from16 v0, p0

    .line 1
    iget-object v1, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    iget-object v2, v0, Lcom/android/tools/r8/r0;->a:Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/E0;->O0()Lcom/android/tools/r8/references/ClassReference;

    move-result-object v2

    invoke-interface {v1, v2}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->startClassVisit(Lcom/android/tools/r8/references/ClassReference;)V

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    iget-object v2, v0, Lcom/android/tools/r8/r0;->a:Lcom/android/tools/r8/graph/E2;

    iget-object v2, v2, Lcom/android/tools/r8/graph/E0;->e:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/J2;->z0()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->shouldProcess(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_0

    return-void

    .line 6
    :cond_0
    iget-object v1, v0, Lcom/android/tools/r8/r0;->a:Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/E0;->D1()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_6

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/g1;

    .line 7
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/g1;->R0()Lcom/android/tools/r8/graph/O2;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 8
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->I0()Lcom/android/tools/r8/graph/S2;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    const/4 v5, 0x3

    if-eq v4, v5, :cond_5

    const/16 v5, 0x9

    if-eq v4, v5, :cond_4

    const/16 v5, 0xe

    if-eq v4, v5, :cond_2

    goto :goto_0

    .line 10
    :cond_2
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->p0()Lcom/android/tools/r8/graph/O2$b;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2$b;->h1()[Lcom/android/tools/r8/graph/O2;

    move-result-object v2

    array-length v4, v2

    const/4 v3, 0x0

    :goto_1
    if-ge v3, v4, :cond_1

    aget-object v5, v2, v3

    .line 11
    invoke-virtual {v5}, Lcom/android/tools/r8/graph/O2;->U0()Z

    move-result v6

    if-eqz v6, :cond_3

    .line 12
    iget-object v6, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/O2;->y0()Lcom/android/tools/r8/graph/O2$h;

    move-result-object v5

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/O2$h;->h1()I

    move-result v5

    invoke-interface {v6, v5}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedInt(I)V

    :cond_3
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 22
    :cond_4
    iget-object v3, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->F0()Lcom/android/tools/r8/graph/O2$k;

    move-result-object v2

    iget-object v2, v2, Lcom/android/tools/r8/graph/O2$k;->d:Lcom/android/tools/r8/graph/Z3;

    check-cast v2, Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v3, v2}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedString(Ljava/lang/String;)V

    goto :goto_0

    .line 23
    :cond_5
    iget-object v3, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2;->y0()Lcom/android/tools/r8/graph/O2$h;

    move-result-object v2

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/O2$h;->h1()I

    move-result v2

    invoke-interface {v3, v2}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedInt(I)V

    goto :goto_0

    .line 24
    :cond_6
    iget-object v1, v0, Lcom/android/tools/r8/r0;->a:Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/E0;->C0()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2e

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/j1;

    .line 26
    iget-object v4, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v5}, Lcom/android/tools/r8/graph/x2;->y0()Lcom/android/tools/r8/references/MethodReference;

    move-result-object v5

    invoke-interface {v4, v5}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->startMethodVisit(Lcom/android/tools/r8/references/MethodReference;)V

    .line 27
    invoke-virtual {v2}, Lcom/android/tools/r8/graph/j1;->U0()Lcom/android/tools/r8/graph/i0;

    move-result-object v4

    if-eqz v4, :cond_2d

    .line 28
    new-instance v5, Ljava/util/HashSet;

    invoke-direct {v5}, Ljava/util/HashSet;-><init>()V

    .line 29
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 30
    invoke-virtual {v4}, Lcom/android/tools/r8/graph/i0;->C()Lcom/android/tools/r8/graph/J0;

    move-result-object v4

    iget-object v4, v4, Lcom/android/tools/r8/graph/J0;->j:[Lcom/android/tools/r8/internal/Yo;

    const/4 v7, 0x0

    .line 32
    :goto_3
    array-length v8, v4

    if-ge v7, v8, :cond_2a

    .line 33
    aget-object v8, v4, v7

    .line 34
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v9

    const/16 v10, 0x16

    const/16 v11, 0x15

    const/16 v12, 0x17

    const/16 v13, 0x14

    const/16 v14, 0x13

    const/16 v15, 0x12

    if-eq v9, v15, :cond_23

    if-eq v9, v14, :cond_23

    if-eq v9, v13, :cond_23

    if-eq v9, v12, :cond_23

    if-eq v9, v11, :cond_23

    if-ne v9, v10, :cond_7

    goto/16 :goto_e

    .line 35
    :cond_7
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v9

    const/16 v10, 0x1b

    const/16 v11, 0x1a

    if-eq v9, v11, :cond_1e

    if-ne v9, v10, :cond_8

    goto/16 :goto_b

    .line 36
    :cond_8
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v9

    const/16 v10, 0x61

    const/16 v11, 0x66

    const/16 v12, 0x62

    const/16 v13, 0x65

    const/16 v14, 0x64

    const/16 v15, 0x63

    const/16 v3, 0x60

    if-eq v9, v3, :cond_14

    if-eq v9, v15, :cond_14

    if-eq v9, v14, :cond_14

    if-eq v9, v13, :cond_14

    if-eq v9, v12, :cond_14

    if-eq v9, v11, :cond_14

    if-ne v9, v10, :cond_9

    goto/16 :goto_8

    .line 37
    :cond_9
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v3

    const/16 v9, 0x71

    const/16 v10, 0x70

    const/16 v11, 0x6f

    const/16 v12, 0x6e

    if-eq v3, v12, :cond_11

    if-eq v3, v11, :cond_11

    if-eq v3, v10, :cond_11

    if-eq v3, v9, :cond_11

    const/16 v13, 0x72

    if-ne v3, v13, :cond_a

    goto/16 :goto_6

    .line 38
    :cond_a
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v3

    const/16 v9, 0x74

    if-eq v3, v9, :cond_e

    const/16 v9, 0x75

    if-eq v3, v9, :cond_e

    const/16 v9, 0x76

    if-eq v3, v9, :cond_e

    const/16 v9, 0x77

    if-eq v3, v9, :cond_e

    const/16 v9, 0x78

    if-ne v3, v9, :cond_b

    goto :goto_4

    .line 39
    :cond_b
    instance-of v3, v8, Lcom/android/tools/r8/internal/Un;

    if-eqz v3, :cond_d

    .line 40
    aget-object v3, v4, v7

    check-cast v3, Lcom/android/tools/r8/internal/Un;

    if-lez v7, :cond_c

    add-int/lit8 v8, v7, -0x1

    .line 41
    aget-object v8, v4, v8

    instance-of v9, v8, Lcom/android/tools/r8/internal/rq;

    if-eqz v9, :cond_c

    .line 42
    check-cast v8, Lcom/android/tools/r8/internal/rq;

    .line 43
    iget-object v8, v8, Lcom/android/tools/r8/internal/no;->h:Lcom/android/tools/r8/graph/G2;

    .line 44
    check-cast v8, Lcom/android/tools/r8/graph/J2;

    .line 45
    iget-object v8, v8, Lcom/android/tools/r8/graph/J2;->f:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v8}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v8

    const-string v9, "[I"

    invoke-static {v8, v9}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v8

    if-nez v8, :cond_c

    goto/16 :goto_11

    .line 46
    :cond_c
    iget v8, v3, Lcom/android/tools/r8/internal/vo;->g:I

    .line 47
    invoke-virtual {v3}, Lcom/android/tools/r8/internal/Yo;->o()I

    move-result v3

    add-int/2addr v3, v8

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    .line 48
    invoke-virtual {v5, v3}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    goto/16 :goto_11

    .line 49
    :cond_d
    instance-of v3, v8, Lcom/android/tools/r8/internal/Vn;

    if-eqz v3, :cond_28

    .line 50
    check-cast v8, Lcom/android/tools/r8/internal/Vn;

    invoke-virtual {v6, v8}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto/16 :goto_11

    .line 51
    :cond_e
    :goto_4
    sget-boolean v3, Lcom/android/tools/r8/r0;->c:Z

    if-nez v3, :cond_10

    .line 52
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v3

    const/16 v9, 0x74

    if-eq v3, v9, :cond_10

    const/16 v9, 0x75

    if-eq v3, v9, :cond_10

    const/16 v9, 0x76

    if-eq v3, v9, :cond_10

    const/16 v9, 0x77

    if-eq v3, v9, :cond_10

    const/16 v9, 0x78

    if-ne v3, v9, :cond_f

    goto :goto_5

    .line 53
    :cond_f
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 55
    :cond_10
    :goto_5
    check-cast v8, Lcom/android/tools/r8/internal/yo;

    .line 56
    iget-object v3, v8, Lcom/android/tools/r8/internal/yo;->h:Lcom/android/tools/r8/graph/Z3;

    check-cast v3, Lcom/android/tools/r8/graph/x2;

    .line 58
    iget-object v8, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    iget-object v9, v3, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    .line 59
    invoke-virtual {v9}, Lcom/android/tools/r8/graph/J2;->z0()Ljava/lang/String;

    move-result-object v9

    iget-object v10, v3, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    .line 60
    invoke-virtual {v10}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v10

    iget-object v3, v3, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    .line 61
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 62
    invoke-static {}, Lcom/android/tools/r8/naming/r0;->a()Lcom/android/tools/r8/naming/r0;

    move-result-object v11

    invoke-virtual {v3, v11}, Lcom/android/tools/r8/graph/F2;->a(Lcom/android/tools/r8/naming/r0;)Ljava/lang/String;

    move-result-object v3

    .line 63
    invoke-interface {v8, v9, v10, v3}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedMethod(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_11

    .line 64
    :cond_11
    :goto_6
    sget-boolean v3, Lcom/android/tools/r8/r0;->c:Z

    if-nez v3, :cond_13

    .line 65
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v3

    if-eq v3, v12, :cond_13

    if-eq v3, v11, :cond_13

    if-eq v3, v10, :cond_13

    if-eq v3, v9, :cond_13

    const/16 v9, 0x72

    if-ne v3, v9, :cond_12

    goto :goto_7

    .line 66
    :cond_12
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 68
    :cond_13
    :goto_7
    check-cast v8, Lcom/android/tools/r8/internal/xo;

    .line 69
    iget-object v3, v8, Lcom/android/tools/r8/internal/xo;->l:Lcom/android/tools/r8/graph/Z3;

    check-cast v3, Lcom/android/tools/r8/graph/x2;

    .line 71
    iget-object v8, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    iget-object v9, v3, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    .line 72
    invoke-virtual {v9}, Lcom/android/tools/r8/graph/J2;->z0()Ljava/lang/String;

    move-result-object v9

    iget-object v10, v3, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    .line 73
    invoke-virtual {v10}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v10

    iget-object v3, v3, Lcom/android/tools/r8/graph/x2;->i:Lcom/android/tools/r8/graph/F2;

    .line 74
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 75
    invoke-static {}, Lcom/android/tools/r8/naming/r0;->a()Lcom/android/tools/r8/naming/r0;

    move-result-object v11

    invoke-virtual {v3, v11}, Lcom/android/tools/r8/graph/F2;->a(Lcom/android/tools/r8/naming/r0;)Ljava/lang/String;

    move-result-object v3

    .line 76
    invoke-interface {v8, v9, v10, v3}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedMethod(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_11

    .line 77
    :cond_14
    :goto_8
    sget-boolean v9, Lcom/android/tools/r8/r0;->c:Z

    if-nez v9, :cond_16

    .line 78
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v9

    if-eq v9, v3, :cond_16

    if-eq v9, v15, :cond_16

    if-eq v9, v14, :cond_16

    if-eq v9, v13, :cond_16

    if-eq v9, v12, :cond_16

    if-eq v9, v11, :cond_16

    if-ne v9, v10, :cond_15

    goto :goto_9

    .line 79
    :cond_15
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 82
    :cond_16
    :goto_9
    instance-of v3, v8, Lcom/android/tools/r8/internal/cr;

    if-eqz v3, :cond_17

    .line 83
    check-cast v8, Lcom/android/tools/r8/internal/cr;

    .line 84
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/cr;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v3

    goto :goto_a

    .line 85
    :cond_17
    instance-of v3, v8, Lcom/android/tools/r8/internal/dr;

    if-eqz v3, :cond_18

    .line 86
    check-cast v8, Lcom/android/tools/r8/internal/dr;

    .line 87
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/dr;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v3

    goto :goto_a

    .line 88
    :cond_18
    instance-of v3, v8, Lcom/android/tools/r8/internal/er;

    if-eqz v3, :cond_19

    .line 89
    check-cast v8, Lcom/android/tools/r8/internal/er;

    .line 90
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/er;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v3

    goto :goto_a

    .line 91
    :cond_19
    instance-of v3, v8, Lcom/android/tools/r8/internal/fr;

    if-eqz v3, :cond_1a

    .line 92
    check-cast v8, Lcom/android/tools/r8/internal/fr;

    .line 93
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/fr;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v3

    goto :goto_a

    .line 94
    :cond_1a
    instance-of v3, v8, Lcom/android/tools/r8/internal/gr;

    if-eqz v3, :cond_1b

    .line 95
    check-cast v8, Lcom/android/tools/r8/internal/gr;

    .line 96
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/gr;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v3

    goto :goto_a

    .line 97
    :cond_1b
    instance-of v3, v8, Lcom/android/tools/r8/internal/ir;

    if-eqz v3, :cond_1c

    .line 98
    check-cast v8, Lcom/android/tools/r8/internal/ir;

    .line 99
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/ir;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v3

    goto :goto_a

    .line 100
    :cond_1c
    instance-of v3, v8, Lcom/android/tools/r8/internal/jr;

    if-eqz v3, :cond_1d

    .line 101
    check-cast v8, Lcom/android/tools/r8/internal/jr;

    .line 102
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/jr;->getField()Lcom/android/tools/r8/graph/l1;

    move-result-object v3

    .line 107
    :goto_a
    iget-object v8, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    iget-object v9, v3, Lcom/android/tools/r8/graph/s2;->f:Lcom/android/tools/r8/graph/J2;

    invoke-virtual {v9}, Lcom/android/tools/r8/graph/J2;->z0()Ljava/lang/String;

    move-result-object v9

    iget-object v3, v3, Lcom/android/tools/r8/graph/s2;->g:Lcom/android/tools/r8/graph/I2;

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v8, v9, v3}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedStaticField(Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_11

    .line 108
    :cond_1d
    new-instance v1, Ljava/lang/AssertionError;

    const-string v2, "Not a get static instruction"

    invoke-direct {v1, v2}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw v1

    .line 109
    :cond_1e
    :goto_b
    sget-boolean v3, Lcom/android/tools/r8/r0;->c:Z

    if-nez v3, :cond_20

    .line 110
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v3

    if-eq v3, v11, :cond_20

    if-ne v3, v10, :cond_1f

    goto :goto_c

    .line 111
    :cond_1f
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 114
    :cond_20
    :goto_c
    instance-of v3, v8, Lcom/android/tools/r8/internal/vn;

    if-eqz v3, :cond_21

    .line 115
    check-cast v8, Lcom/android/tools/r8/internal/vn;

    .line 116
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/vn;->J()Lcom/android/tools/r8/graph/I2;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v3

    goto :goto_d

    .line 117
    :cond_21
    instance-of v3, v8, Lcom/android/tools/r8/internal/wn;

    if-eqz v3, :cond_22

    .line 118
    check-cast v8, Lcom/android/tools/r8/internal/wn;

    .line 119
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/wn;->J()Lcom/android/tools/r8/graph/I2;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object v3

    .line 124
    :goto_d
    iget-object v8, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-interface {v8, v3}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedString(Ljava/lang/String;)V

    goto :goto_11

    .line 125
    :cond_22
    new-instance v1, Ljava/lang/AssertionError;

    const-string v2, "Not a string constant instruction."

    invoke-direct {v1, v2}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw v1

    .line 126
    :cond_23
    :goto_e
    sget-boolean v3, Lcom/android/tools/r8/r0;->c:Z

    if-nez v3, :cond_25

    .line 127
    invoke-virtual {v8}, Lcom/android/tools/r8/internal/Yo;->s()I

    move-result v3

    if-eq v3, v15, :cond_25

    if-eq v3, v14, :cond_25

    if-eq v3, v13, :cond_25

    if-eq v3, v12, :cond_25

    if-eq v3, v11, :cond_25

    if-ne v3, v10, :cond_24

    goto :goto_f

    .line 128
    :cond_24
    new-instance v1, Ljava/lang/AssertionError;

    invoke-direct {v1}, Ljava/lang/AssertionError;-><init>()V

    throw v1

    .line 131
    :cond_25
    :goto_f
    instance-of v3, v8, Lcom/android/tools/r8/internal/bk0;

    if-eqz v3, :cond_26

    .line 132
    check-cast v8, Lcom/android/tools/r8/internal/bk0;

    .line 133
    invoke-interface {v8}, Lcom/android/tools/r8/internal/bk0;->a()I

    move-result v3

    goto :goto_10

    .line 134
    :cond_26
    instance-of v3, v8, Lcom/android/tools/r8/internal/Bu0;

    if-eqz v3, :cond_29

    .line 135
    check-cast v8, Lcom/android/tools/r8/internal/Bu0;

    .line 136
    invoke-interface {v8}, Lcom/android/tools/r8/internal/Bu0;->a()J

    move-result-wide v9

    long-to-int v3, v9

    int-to-long v9, v3

    invoke-interface {v8}, Lcom/android/tools/r8/internal/Bu0;->a()J

    move-result-wide v11

    cmp-long v3, v9, v11

    if-eqz v3, :cond_27

    goto :goto_11

    .line 140
    :cond_27
    invoke-interface {v8}, Lcom/android/tools/r8/internal/Bu0;->a()J

    move-result-wide v8

    long-to-int v3, v8

    .line 145
    :goto_10
    iget-object v8, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-interface {v8, v3}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedInt(I)V

    :cond_28
    :goto_11
    add-int/lit8 v7, v7, 0x1

    goto/16 :goto_3

    .line 146
    :cond_29
    new-instance v1, Ljava/lang/AssertionError;

    const-string v2, "Not an int const instruction."

    invoke-direct {v1, v2}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw v1

    .line 147
    :cond_2a
    invoke-virtual {v6}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_2b
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_2d

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/internal/Vn;

    .line 148
    instance-of v6, v4, Lcom/android/tools/r8/internal/Vn;

    if-nez v6, :cond_2c

    const/4 v6, 0x0

    goto :goto_12

    .line 153
    :cond_2c
    invoke-virtual {v4}, Lcom/android/tools/r8/internal/Yo;->o()I

    move-result v6

    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/util/HashSet;->contains(Ljava/lang/Object;)Z

    move-result v6

    :goto_12
    if-eqz v6, :cond_2b

    const/4 v6, 0x0

    .line 154
    :goto_13
    iget-object v7, v4, Lcom/android/tools/r8/internal/Vn;->h:[S

    array-length v8, v7

    div-int/lit8 v8, v8, 0x2

    if-ge v6, v8, :cond_2b

    mul-int/lit8 v8, v6, 0x2

    add-int/lit8 v9, v8, 0x1

    .line 155
    aget-short v9, v7, v9

    shl-int/lit8 v9, v9, 0x10

    aget-short v7, v7, v8

    or-int/2addr v7, v9

    .line 156
    iget-object v8, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-interface {v8, v7}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedInt(I)V

    add-int/lit8 v6, v6, 0x1

    goto :goto_13

    .line 157
    :cond_2d
    iget-object v3, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/h1;->H0()Lcom/android/tools/r8/graph/s2;

    move-result-object v2

    check-cast v2, Lcom/android/tools/r8/graph/x2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/x2;->y0()Lcom/android/tools/r8/references/MethodReference;

    move-result-object v2

    invoke-interface {v3, v2}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->endMethodVisit(Lcom/android/tools/r8/references/MethodReference;)V

    goto/16 :goto_2

    .line 160
    :cond_2e
    iget-object v1, v0, Lcom/android/tools/r8/r0;->a:Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v1}, Lcom/android/tools/r8/graph/E2;->N1()Z

    move-result v1

    if-eqz v1, :cond_2f

    .line 161
    iget-object v1, v0, Lcom/android/tools/r8/r0;->a:Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/r0;->a(Lcom/android/tools/r8/graph/E2;)V

    .line 163
    :cond_2f
    iget-object v1, v0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    iget-object v2, v0, Lcom/android/tools/r8/r0;->a:Lcom/android/tools/r8/graph/E2;

    invoke-virtual {v2}, Lcom/android/tools/r8/graph/E0;->O0()Lcom/android/tools/r8/references/ClassReference;

    move-result-object v2

    invoke-interface {v1, v2}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->endClassVisit(Lcom/android/tools/r8/references/ClassReference;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/E2;)V
    .locals 4

    .line 164
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/b1;->m0()Lcom/android/tools/r8/graph/u0;

    move-result-object v0

    .line 165
    iget-object v0, v0, Lcom/android/tools/r8/graph/u0;->d:[Lcom/android/tools/r8/graph/r0;

    .line 166
    invoke-static {v0}, Ljava/util/Arrays;->stream([Ljava/lang/Object;)Ljava/util/stream/Stream;

    move-result-object v0

    .line 167
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->L0()Ljava/lang/Iterable;

    move-result-object v1

    invoke-static {v1}, Lcom/android/tools/r8/internal/um0;->a(Ljava/lang/Iterable;)Ljava/util/stream/Stream;

    move-result-object v1

    sget-object v2, Lcom/android/tools/r8/r0$$ExternalSyntheticLambda3;->INSTANCE:Lcom/android/tools/r8/r0$$ExternalSyntheticLambda3;

    .line 168
    invoke-interface {v1, v2}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object v1

    sget-object v2, Lcom/android/tools/r8/r0$$ExternalSyntheticLambda1;->INSTANCE:Lcom/android/tools/r8/r0$$ExternalSyntheticLambda1;

    .line 169
    invoke-interface {v1, v2}, Ljava/util/stream/Stream;->flatMap(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object v1

    .line 171
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/E0;->C1()Ljava/lang/Iterable;

    move-result-object p1

    invoke-static {p1}, Lcom/android/tools/r8/internal/um0;->a(Ljava/lang/Iterable;)Ljava/util/stream/Stream;

    move-result-object p1

    sget-object v2, Lcom/android/tools/r8/r0$$ExternalSyntheticLambda4;->INSTANCE:Lcom/android/tools/r8/r0$$ExternalSyntheticLambda4;

    .line 172
    invoke-interface {p1, v2}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object p1

    sget-object v2, Lcom/android/tools/r8/r0$$ExternalSyntheticLambda2;->INSTANCE:Lcom/android/tools/r8/r0$$ExternalSyntheticLambda2;

    .line 173
    invoke-interface {p1, v2}, Ljava/util/stream/Stream;->flatMap(Ljava/util/function/Function;)Ljava/util/stream/Stream;

    move-result-object p1

    const/4 v2, 0x3

    new-array v2, v2, [Ljava/util/stream/Stream;

    const/4 v3, 0x0

    aput-object v0, v2, v3

    const/4 v0, 0x1

    aput-object v1, v2, v0

    const/4 v0, 0x2

    aput-object p1, v2, v0

    .line 175
    invoke-static {v2}, Lcom/android/tools/r8/internal/um0;->a([Ljava/util/stream/Stream;)Ljava/util/stream/Stream;

    move-result-object p1

    new-instance v0, Lcom/android/tools/r8/r0$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/r0$$ExternalSyntheticLambda0;-><init>(Lcom/android/tools/r8/r0;)V

    .line 176
    invoke-interface {p1, v0}, Ljava/util/stream/Stream;->forEach(Ljava/util/function/Consumer;)V

    return-void
.end method

.method public final a(Lcom/android/tools/r8/graph/O2;)V
    .locals 3

    .line 182
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/O2;->I0()Lcom/android/tools/r8/graph/S2;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    move-result v0

    const/4 v1, 0x3

    if-eq v0, v1, :cond_3

    const/16 v1, 0x9

    if-eq v0, v1, :cond_2

    const/16 v1, 0xe

    const/4 v2, 0x0

    if-eq v0, v1, :cond_1

    const/16 v1, 0xf

    if-eq v0, v1, :cond_0

    goto :goto_2

    .line 184
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/O2;->o0()Lcom/android/tools/r8/graph/O2$a;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/graph/O2$a;->d:Lcom/android/tools/r8/graph/e1;

    iget-object p1, p1, Lcom/android/tools/r8/graph/e1;->c:[Lcom/android/tools/r8/graph/t0;

    array-length v0, p1

    :goto_0
    if-ge v2, v0, :cond_4

    aget-object v1, p1, v2

    .line 185
    iget-object v1, v1, Lcom/android/tools/r8/graph/t0;->c:Lcom/android/tools/r8/graph/O2;

    invoke-virtual {p0, v1}, Lcom/android/tools/r8/r0;->a(Lcom/android/tools/r8/graph/O2;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 190
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/graph/O2;->p0()Lcom/android/tools/r8/graph/O2$b;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/O2$b;->h1()[Lcom/android/tools/r8/graph/O2;

    move-result-object p1

    array-length v0, p1

    :goto_1
    if-ge v2, v0, :cond_4

    aget-object v1, p1, v2

    .line 191
    invoke-virtual {p0, v1}, Lcom/android/tools/r8/r0;->a(Lcom/android/tools/r8/graph/O2;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 200
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/O2;->F0()Lcom/android/tools/r8/graph/O2$k;

    move-result-object p1

    iget-object p1, p1, Lcom/android/tools/r8/graph/O2$k;->d:Lcom/android/tools/r8/graph/Z3;

    check-cast p1, Lcom/android/tools/r8/graph/I2;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/I2;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedString(Ljava/lang/String;)V

    goto :goto_2

    .line 201
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/r0;->b:Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;

    invoke-virtual {p1}, Lcom/android/tools/r8/graph/O2;->y0()Lcom/android/tools/r8/graph/O2$h;

    move-result-object p1

    iget p1, p1, Lcom/android/tools/r8/graph/O2$h;->d:I

    invoke-interface {v0, p1}, Lcom/android/tools/r8/ResourceShrinker$ReferenceChecker;->referencedInt(I)V

    :cond_4
    :goto_2
    return-void
.end method

.method public final synthetic a(Lcom/android/tools/r8/graph/r0;)V
    .locals 3

    .line 179
    iget-object p1, p1, Lcom/android/tools/r8/graph/r0;->c:Lcom/android/tools/r8/graph/e1;

    iget-object p1, p1, Lcom/android/tools/r8/graph/e1;->c:[Lcom/android/tools/r8/graph/t0;

    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v2, p1, v1

    .line 180
    iget-object v2, v2, Lcom/android/tools/r8/graph/t0;->c:Lcom/android/tools/r8/graph/O2;

    .line 181
    invoke-virtual {p0, v2}, Lcom/android/tools/r8/r0;->a(Lcom/android/tools/r8/graph/O2;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method
