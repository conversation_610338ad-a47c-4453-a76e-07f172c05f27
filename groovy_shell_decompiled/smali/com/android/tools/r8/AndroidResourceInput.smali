.class public interface abstract Lcom/android/tools/r8/AndroidResourceInput;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/Resource;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/android/tools/r8/AndroidResourceInput$Kind;
    }
.end annotation


# virtual methods
.method public abstract getByteStream()Ljava/io/InputStream;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/ResourceException;
        }
    .end annotation
.end method

.method public abstract getKind()Lcom/android/tools/r8/AndroidResourceInput$Kind;
.end method

.method public abstract getPath()Lcom/android/tools/r8/ResourcePath;
.end method
