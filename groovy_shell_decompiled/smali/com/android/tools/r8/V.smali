.class public final Lcom/android/tools/r8/V;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/DiagnosticsHandler;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final error(Lcom/android/tools/r8/Diagnostic;)V
    .locals 2

    .line 1
    instance-of v0, p1, Lcom/android/tools/r8/errors/DexFileOverflowDiagnostic;

    if-eqz v0, :cond_0

    .line 2
    check-cast p1, Lcom/android/tools/r8/errors/DexFileOverflowDiagnostic;

    .line 3
    new-instance v0, Lcom/android/tools/r8/utils/StringDiagnostic;

    .line 5
    invoke-virtual {p1}, Lcom/android/tools/r8/errors/DexFileOverflowDiagnostic;->getDiagnosticMessage()Ljava/lang/String;

    move-result-object p1

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ". Library too large. L8 can only produce a single .dex file"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Lcom/android/tools/r8/utils/StringDiagnostic;-><init>(Ljava/lang/String;)V

    .line 6
    invoke-super {p0, v0}, Lcom/android/tools/r8/DiagnosticsHandler;->error(Lcom/android/tools/r8/Diagnostic;)V

    return-void

    .line 12
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/DiagnosticsHandler;->error(Lcom/android/tools/r8/Diagnostic;)V

    return-void
.end method
