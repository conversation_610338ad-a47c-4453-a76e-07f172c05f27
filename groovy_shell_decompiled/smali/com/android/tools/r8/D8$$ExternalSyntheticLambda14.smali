.class public final synthetic Lcom/android/tools/r8/D8$$ExternalSyntheticLambda14;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/internal/lp0;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/graph/y;

.field public final synthetic f$1:Ljava/util/concurrent/ExecutorService;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/graph/y;Ljava/util/concurrent/ExecutorService;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda14;->f$0:Lcom/android/tools/r8/graph/y;

    iput-object p2, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda14;->f$1:Ljava/util/concurrent/ExecutorService;

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 2

    iget-object v0, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda14;->f$0:Lcom/android/tools/r8/graph/y;

    iget-object v1, p0, Lcom/android/tools/r8/D8$$ExternalSyntheticLambda14;->f$1:Ljava/util/concurrent/ExecutorService;

    invoke-static {v0, v1}, Lcom/android/tools/r8/D8;->$r8$lambda$CBQYZmoVezIjXn3QIl9wGnAMEZU(Lcom/android/tools/r8/graph/y;Ljava/util/concurrent/ExecutorService;)V

    return-void
.end method
