.class public final Lcom/android/tools/r8/synthesis/a;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic f:Z = true


# instance fields
.field public final a:Lcom/android/tools/r8/graph/x0;

.field public final b:I

.field public final c:Lcom/android/tools/r8/synthesis/c;

.field public final d:Lcom/android/tools/r8/internal/cB;

.field public final e:Lcom/android/tools/r8/synthesis/D;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(ILcom/android/tools/r8/graph/x0;Lcom/android/tools/r8/synthesis/c;Lcom/android/tools/r8/internal/cB;Lcom/android/tools/r8/synthesis/D;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Lcom/android/tools/r8/synthesis/a;->b:I

    .line 3
    iput-object p2, p0, Lcom/android/tools/r8/synthesis/a;->a:Lcom/android/tools/r8/graph/x0;

    .line 4
    iput-object p3, p0, Lcom/android/tools/r8/synthesis/a;->c:Lcom/android/tools/r8/synthesis/c;

    .line 5
    iput-object p4, p0, Lcom/android/tools/r8/synthesis/a;->d:Lcom/android/tools/r8/internal/cB;

    .line 6
    iput-object p5, p0, Lcom/android/tools/r8/synthesis/a;->e:Lcom/android/tools/r8/synthesis/D;

    .line 7
    sget-boolean p1, Lcom/android/tools/r8/synthesis/a;->f:Z

    if-nez p1, :cond_3

    .line 8
    sget-boolean p1, Lcom/android/tools/r8/synthesis/c;->f:Z

    if-nez p1, :cond_0

    .line 9
    iget-object p4, p3, Lcom/android/tools/r8/synthesis/c;->b:Lcom/android/tools/r8/internal/iB;

    .line 10
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/iB;->o()Lcom/android/tools/r8/internal/LB;

    move-result-object p4

    invoke-static {p2, p4}, Lcom/android/tools/r8/synthesis/c;->a(Lcom/android/tools/r8/graph/x0;Ljava/util/Collection;)V

    :cond_0
    if-nez p1, :cond_1

    .line 11
    iget-object p4, p3, Lcom/android/tools/r8/synthesis/c;->c:Lcom/android/tools/r8/internal/iB;

    .line 12
    invoke-virtual {p4}, Lcom/android/tools/r8/internal/iB;->o()Lcom/android/tools/r8/internal/LB;

    move-result-object p4

    invoke-static {p2, p4}, Lcom/android/tools/r8/synthesis/c;->a(Lcom/android/tools/r8/graph/x0;Ljava/util/Collection;)V

    :cond_1
    if-nez p1, :cond_2

    .line 13
    iget-object p1, p3, Lcom/android/tools/r8/synthesis/c;->e:Lcom/android/tools/r8/internal/LB;

    .line 14
    invoke-static {p2, p1}, Lcom/android/tools/r8/synthesis/c;->a(Lcom/android/tools/r8/graph/x0;Ljava/util/Collection;)V

    goto :goto_0

    .line 15
    :cond_2
    invoke-virtual {p3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    :cond_3
    :goto_0
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/function/Function;Lcom/android/tools/r8/graph/J2;)Lcom/android/tools/r8/graph/b0;
    .locals 0

    .line 1
    invoke-interface {p1, p2}, Ljava/util/function/Function;->apply(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/graph/b0;

    return-object p1
.end method
