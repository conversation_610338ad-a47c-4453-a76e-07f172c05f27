.class public final Lcom/android/tools/r8/shaking/N;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# instance fields
.field public final a:Lcom/android/tools/r8/graph/x2;

.field public final b:Z


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/graph/x2;Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/android/tools/r8/shaking/N;->a:Lcom/android/tools/r8/graph/x2;

    .line 3
    iput-boolean p2, p0, Lcom/android/tools/r8/shaking/N;->b:Z

    return-void
.end method


# virtual methods
.method public final equals(Ljava/lang/Object;)Z
    .locals 3

    const/4 v0, 0x0

    if-eqz p1, :cond_1

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    const-class v2, Lcom/android/tools/r8/shaking/N;

    if-eq v2, v1, :cond_0

    goto :goto_0

    .line 4
    :cond_0
    check-cast p1, Lcom/android/tools/r8/shaking/N;

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/shaking/N;->a:Lcom/android/tools/r8/graph/x2;

    iget-object v2, p1, Lcom/android/tools/r8/shaking/N;->a:Lcom/android/tools/r8/graph/x2;

    if-ne v1, v2, :cond_1

    iget-boolean v1, p0, Lcom/android/tools/r8/shaking/N;->b:Z

    iget-boolean p1, p1, Lcom/android/tools/r8/shaking/N;->b:Z

    if-ne v1, p1, :cond_1

    const/4 v0, 0x1

    :cond_1
    :goto_0
    return v0
.end method

.method public final hashCode()I
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/shaking/N;->a:Lcom/android/tools/r8/graph/x2;

    iget-boolean v1, p0, Lcom/android/tools/r8/shaking/N;->b:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object v0, v2, v3

    const/4 v0, 0x1

    aput-object v1, v2, v0

    invoke-static {v2}, Ljava/util/Objects;->hash([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
