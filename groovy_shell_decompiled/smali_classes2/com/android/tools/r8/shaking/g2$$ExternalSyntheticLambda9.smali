.class public final synthetic Lcom/android/tools/r8/shaking/g2$$ExternalSyntheticLambda9;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Ljava/util/Set;

.field public final synthetic f$1:Ljava/util/function/Predicate;

.field public final synthetic f$2:Lcom/android/tools/r8/internal/os;


# direct methods
.method public synthetic constructor <init>(Ljava/util/Set;Ljava/util/function/Predicate;Lcom/android/tools/r8/internal/os;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/shaking/g2$$ExternalSyntheticLambda9;->f$0:Ljava/util/Set;

    iput-object p2, p0, Lcom/android/tools/r8/shaking/g2$$ExternalSyntheticLambda9;->f$1:Ljava/util/function/Predicate;

    iput-object p3, p0, Lcom/android/tools/r8/shaking/g2$$ExternalSyntheticLambda9;->f$2:Lcom/android/tools/r8/internal/os;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 3

    iget-object v0, p0, Lcom/android/tools/r8/shaking/g2$$ExternalSyntheticLambda9;->f$0:Ljava/util/Set;

    iget-object v1, p0, Lcom/android/tools/r8/shaking/g2$$ExternalSyntheticLambda9;->f$1:Ljava/util/function/Predicate;

    iget-object v2, p0, Lcom/android/tools/r8/shaking/g2$$ExternalSyntheticLambda9;->f$2:Lcom/android/tools/r8/internal/os;

    check-cast p1, Lcom/android/tools/r8/graph/J2;

    invoke-static {v0, v1, v2, p1}, Lcom/android/tools/r8/shaking/g2;->a(Ljava/util/Set;Ljava/util/function/Predicate;Lcom/android/tools/r8/internal/os;Lcom/android/tools/r8/graph/J2;)Z

    move-result p1

    return p1
.end method
