.class public final synthetic Lcom/android/tools/r8/shaking/M$$ExternalSyntheticLambda92;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/android/tools/r8/synthesis/F;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/shaking/M;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/shaking/M;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/shaking/M$$ExternalSyntheticLambda92;->f$0:Lcom/android/tools/r8/shaking/M;

    return-void
.end method


# virtual methods
.method public final a(Lcom/android/tools/r8/graph/E2;)Ljava/util/Set;
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/shaking/M$$ExternalSyntheticLambda92;->f$0:Lcom/android/tools/r8/shaking/M;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/shaking/M;->g(Lcom/android/tools/r8/graph/E2;)Ljava/util/Set;

    move-result-object p1

    return-object p1
.end method
