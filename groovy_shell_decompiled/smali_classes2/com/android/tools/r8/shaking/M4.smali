.class public final Lcom/android/tools/r8/shaking/M4;
.super Lcom/android/tools/r8/shaking/i3;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# static fields
.field public static final synthetic r:I


# direct methods
.method public constructor <init>(Lcom/android/tools/r8/origin/Origin;Lcom/android/tools/r8/position/Position;Ljava/lang/String;Lcom/android/tools/r8/internal/cB;Lcom/android/tools/r8/shaking/F2;Lcom/android/tools/r8/shaking/F2;ZLcom/android/tools/r8/shaking/Y2;Lcom/android/tools/r8/shaking/P2;Lcom/android/tools/r8/internal/cB;Lcom/android/tools/r8/shaking/U3;ZLjava/util/List;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p13}, Lcom/android/tools/r8/shaking/i3;-><init>(Lcom/android/tools/r8/origin/Origin;Lcom/android/tools/r8/position/Position;Ljava/lang/String;Ljava/util/List;Lcom/android/tools/r8/shaking/F2;Lcom/android/tools/r8/shaking/F2;ZLcom/android/tools/r8/shaking/Y2;Lcom/android/tools/r8/shaking/P2;Ljava/util/List;Lcom/android/tools/r8/shaking/U3;ZLjava/util/List;)V

    return-void
.end method


# virtual methods
.method public final D()Ljava/lang/String;
    .locals 1

    const-string v0, "whyareyounotinlining"

    return-object v0
.end method
