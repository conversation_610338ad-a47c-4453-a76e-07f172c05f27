.class public final Lcom/android/tools/r8/shaking/x4;
.super Lcom/android/tools/r8/shaking/d1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/shaking/d1;-><init>()V

    return-void
.end method

.method public constructor <init>(Lcom/android/tools/r8/shaking/z4;)V
    .locals 0

    .line 2
    invoke-direct {p0, p1}, Lcom/android/tools/r8/shaking/d1;-><init>(Lcom/android/tools/r8/shaking/f1;)V

    return-void
.end method


# virtual methods
.method public final a()Lcom/android/tools/r8/shaking/n1;
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/android/tools/r8/shaking/l1;->a()Lcom/android/tools/r8/shaking/n1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/z4;

    return-object v0
.end method

.method public final b()Lcom/android/tools/r8/shaking/n1;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/shaking/z4;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/shaking/z4;-><init>(Lcom/android/tools/r8/shaking/x4;)V

    return-object v0
.end method

.method public final e()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final f()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final g()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final j()Lcom/android/tools/r8/shaking/l1;
    .locals 0

    return-object p0
.end method

.method public final m()Lcom/android/tools/r8/shaking/f1;
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/shaking/z4;

    invoke-direct {v0, p0}, Lcom/android/tools/r8/shaking/z4;-><init>(Lcom/android/tools/r8/shaking/x4;)V

    return-object v0
.end method

.method public final o()Lcom/android/tools/r8/shaking/d1;
    .locals 0

    return-object p0
.end method

.method public final p()Lcom/android/tools/r8/shaking/z4;
    .locals 1

    .line 1
    invoke-super {p0}, Lcom/android/tools/r8/shaking/l1;->a()Lcom/android/tools/r8/shaking/n1;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/shaking/z4;

    return-object v0
.end method
