.class public final synthetic Lcom/android/tools/r8/shaking/F2$$ExternalSyntheticLambda11;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/BooleanSupplier;


# instance fields
.field public final synthetic f$0:Lcom/android/tools/r8/shaking/F2;


# direct methods
.method public synthetic constructor <init>(Lcom/android/tools/r8/shaking/F2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/android/tools/r8/shaking/F2$$ExternalSyntheticLambda11;->f$0:Lcom/android/tools/r8/shaking/F2;

    return-void
.end method


# virtual methods
.method public final getAsBoolean()Z
    .locals 1

    iget-object v0, p0, Lcom/android/tools/r8/shaking/F2$$ExternalSyntheticLambda11;->f$0:Lcom/android/tools/r8/shaking/F2;

    invoke-virtual {v0}, Lcom/android/tools/r8/shaking/F2;->i()Z

    move-result v0

    return v0
.end method
