.class public final Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
.super Lcom/android/tools/r8/internal/dy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TargetOrBuilder;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Builder"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/android/tools/r8/internal/dy;",
        "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TargetOrBuilder;"
    }
.end annotation


# instance fields
.field private bitField0_:I

.field private constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/Xc0;"
        }
    .end annotation
.end field

.field private constraintAdditions_:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;",
            ">;"
        }
    .end annotation
.end field

.field private constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation
.end field

.field private constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

.field private itemBuilder_:Lcom/android/tools/r8/internal/dk0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation
.end field

.field private item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;


# direct methods
.method private constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/dy;-><init>()V

    .line 439
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    .line 440
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->maybeForceBuilderInitialization()V

    return-void
.end method

.method private constructor <init>(Lcom/android/tools/r8/internal/ey;)V
    .locals 0

    .line 441
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/dy;-><init>(Lcom/android/tools/r8/internal/ey;)V

    .line 873
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    .line 874
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->maybeForceBuilderInitialization()V

    return-void
.end method

.method synthetic constructor <init>(Lcom/android/tools/r8/internal/ey;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder-IA;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;-><init>(Lcom/android/tools/r8/internal/ey;)V

    return-void
.end method

.method synthetic constructor <init>(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder-IA;)V
    .locals 0

    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;-><init>()V

    return-void
.end method

.method private ensureConstraintAdditionsIsMutable()V
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v0, v0, 0x2

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    .line 3
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    or-int/lit8 v0, v0, 0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    :cond_0
    return-void
.end method

.method private getConstraintAdditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/Xc0;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Xc0;

    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    iget v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v2, v2, 0x2

    if-eqz v2, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    .line 6
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v3

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v4

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/android/tools/r8/internal/Xc0;-><init>(Ljava/util/List;ZLcom/android/tools/r8/internal/ey;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    const/4 v0, 0x0

    .line 8
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    .line 10
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    return-object v0
.end method

.method private getConstraintsFieldBuilder()Lcom/android/tools/r8/internal/dk0;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/dk0;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getConstraints()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    move-result-object v1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v2

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v3

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/dk0;-><init>(Lcom/android/tools/r8/internal/J0;Lcom/android/tools/r8/internal/I0;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v0, 0x0

    .line 7
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    return-object v0
.end method

.method public static final getDescriptor()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_Target_descriptor()Lcom/android/tools/r8/internal/Ok;

    move-result-object v0

    return-object v0
.end method

.method private getItemFieldBuilder()Lcom/android/tools/r8/internal/dk0;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/dk0;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getItem()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object v1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v2

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v3

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/dk0;-><init>(Lcom/android/tools/r8/internal/J0;Lcom/android/tools/r8/internal/I0;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v0, 0x0

    .line 7
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    return-object v0
.end method

.method private maybeForceBuilderInitialization()V
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->access$3900()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getConstraintsFieldBuilder()Lcom/android/tools/r8/internal/dk0;

    .line 3
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getConstraintAdditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    :cond_0
    return-void
.end method


# virtual methods
.method public addAllConstraintAdditions(Ljava/lang/Iterable;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Iterable<",
            "+",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;",
            ">;)",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->ensureConstraintAdditionsIsMutable()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/M0;->addAll(Ljava/lang/Iterable;Ljava/util/List;)V

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 7
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(Ljava/lang/Iterable;)V

    :goto_0
    return-object p0
.end method

.method public addConstraintAdditions(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 26
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->ensureConstraintAdditionsIsMutable()V

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    move-result-object p2

    invoke-interface {v0, p1, p2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 28
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 30
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->b(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public addConstraintAdditions(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 12
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 14
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->ensureConstraintAdditionsIsMutable()V

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 16
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 18
    :cond_0
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->b(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public addConstraintAdditions(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 19
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 20
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->ensureConstraintAdditionsIsMutable()V

    .line 21
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 22
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 24
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public addConstraintAdditions(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->ensureConstraintAdditionsIsMutable()V

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 9
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public addConstraintAdditionsBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getConstraintAdditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    move-result-object v1

    .line 3
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Xc0;->a(Lcom/android/tools/r8/internal/uy;)Lcom/android/tools/r8/internal/H0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;

    return-object v0
.end method

.method public addConstraintAdditionsBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;
    .locals 2

    .line 4
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getConstraintAdditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    .line 5
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    move-result-object v1

    .line 6
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Xc0;->a(ILcom/android/tools/r8/internal/uy;)Lcom/android/tools/r8/internal/H0;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;

    return-object p1
.end method

.method public bridge synthetic addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object p1
.end method

.method public bridge synthetic build()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic build()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v0

    return-object v0
.end method

.method public build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;
    .locals 2

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v0

    .line 4
    invoke-virtual {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 5
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public bridge synthetic buildPartial()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic buildPartial()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v0

    return-object v0
.end method

.method public buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;
    .locals 4

    .line 3
    new-instance v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;-><init>(Lcom/android/tools/r8/internal/dy;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target-IA;)V

    .line 4
    iget v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    .line 6
    iget-object v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v3, 0x1

    if-nez v2, :cond_0

    .line 7
    iget-object v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    invoke-static {v0, v2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fputitem_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)V

    goto :goto_0

    .line 8
    :cond_0
    iput-boolean v3, v2, Lcom/android/tools/r8/internal/dk0;->d:Z

    .line 9
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v2

    .line 10
    check-cast v2, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    invoke-static {v0, v2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fputitem_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)V

    :goto_0
    and-int/2addr v1, v3

    if-eqz v1, :cond_2

    .line 13
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v1, :cond_1

    .line 14
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fputconstraints_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;)V

    goto :goto_1

    .line 15
    :cond_1
    iput-boolean v3, v1, Lcom/android/tools/r8/internal/dk0;->d:Z

    .line 16
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v1

    .line 17
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fputconstraints_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;)V

    goto :goto_1

    :cond_2
    const/4 v3, 0x0

    .line 21
    :goto_1
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v1, :cond_4

    .line 22
    iget v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v1, v1, 0x2

    if-eqz v1, :cond_3

    .line 23
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-static {v1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    .line 24
    iget v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v1, v1, -0x3

    iput v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    .line 26
    :cond_3
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fputconstraintAdditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;Ljava/util/List;)V

    goto :goto_2

    .line 28
    :cond_4
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Xc0;->b()Ljava/util/List;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fputconstraintAdditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;Ljava/util/List;)V

    .line 30
    :goto_2
    invoke-static {v0, v3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fputbitField0_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;I)V

    .line 31
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onBuilt()V

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/dy;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 2

    .line 5
    invoke-super {p0}, Lcom/android/tools/r8/internal/dy;->clear()Lcom/android/tools/r8/internal/dy;

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 7
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    goto :goto_0

    .line 9
    :cond_0
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 10
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    .line 12
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 13
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    goto :goto_1

    .line 15
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->b()V

    .line 17
    :goto_1
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    .line 18
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_2

    .line 19
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    .line 20
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x3

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    goto :goto_2

    .line 22
    :cond_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->c()V

    :goto_2
    return-object p0
.end method

.method public clearConstraintAdditions()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    .line 3
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x3

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 6
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->c()V

    :goto_0
    return-object p0
.end method

.method public clearConstraints()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 5
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->b()V

    .line 7
    :goto_0
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    return-object p0
.end method

.method public bridge synthetic clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object p1
.end method

.method public clearItem()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 2
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 5
    :cond_0
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 6
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    :goto_0
    return-object p0
.end method

.method public bridge synthetic clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 0

    .line 4
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object p1
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/M0;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/dy;
    .locals 1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 7
    invoke-super {p0}, Lcom/android/tools/r8/internal/dy;->clone()Lcom/android/tools/r8/internal/dy;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object v0
.end method

.method public bridge synthetic clone()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/CloneNotSupportedException;
        }
    .end annotation

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object v0

    return-object v0
.end method

.method public getConstraintAdditions(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    return-object p1

    :cond_0
    const/4 v1, 0x0

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Xc0;->a(IZ)Lcom/android/tools/r8/internal/J0;

    move-result-object p1

    .line 4
    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    return-object p1
.end method

.method public getConstraintAdditionsBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getConstraintAdditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(I)Lcom/android/tools/r8/internal/H0;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;

    return-object p1
.end method

.method public getConstraintAdditionsBuilderList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getConstraintAdditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->f()Lcom/android/tools/r8/internal/Uc0;

    move-result-object v0

    return-object v0
.end method

.method public getConstraintAdditionsCount()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0

    .line 3
    :cond_0
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xc0;->b:Ljava/util/List;

    .line 4
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method

.method public getConstraintAdditionsList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 4
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->g()Lcom/android/tools/r8/internal/Vc0;

    move-result-object v0

    return-object v0
.end method

.method public getConstraintAdditionsOrBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConstraintOrBuilder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConstraintOrBuilder;

    return-object p1

    .line 3
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->b(I)Lcom/android/tools/r8/internal/DU;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConstraintOrBuilder;

    return-object p1
.end method

.method public getConstraintAdditionsOrBuilderList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "+",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConstraintOrBuilder;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->h()Lcom/android/tools/r8/internal/Wc0;

    move-result-object v0

    return-object v0

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getConstraints()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    move-result-object v0

    :cond_0
    return-object v0

    .line 4
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    return-object v0
.end method

.method public getConstraintsBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints$Builder;
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    or-int/lit8 v0, v0, 0x1

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 3
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getConstraintsFieldBuilder()Lcom/android/tools/r8/internal/dk0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->c()Lcom/android/tools/r8/internal/H0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints$Builder;

    return-object v0
.end method

.method public getConstraintsOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConstraintsOrBuilder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-eqz v0, :cond_1

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->b:Lcom/android/tools/r8/internal/H0;

    if-eqz v1, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->c:Lcom/android/tools/r8/internal/J0;

    .line 6
    :goto_0
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConstraintsOrBuilder;

    return-object v1

    .line 8
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    if-nez v0, :cond_2

    .line 9
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    move-result-object v0

    :cond_2
    return-object v0
.end method

.method public bridge synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v0

    return-object v0
.end method

.method public getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;
    .locals 1

    .line 3
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v0

    return-object v0
.end method

.method public getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_Target_descriptor()Lcom/android/tools/r8/internal/Ok;

    move-result-object v0

    return-object v0
.end method

.method public getItem()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object v0

    :cond_0
    return-object v0

    .line 4
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    return-object v0
.end method

.method public getItemBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getItemFieldBuilder()Lcom/android/tools/r8/internal/dk0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->c()Lcom/android/tools/r8/internal/H0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;

    return-object v0
.end method

.method public getItemOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReferenceOrBuilder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-eqz v0, :cond_1

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->b:Lcom/android/tools/r8/internal/H0;

    if-eqz v1, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->c:Lcom/android/tools/r8/internal/J0;

    .line 6
    :goto_0
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReferenceOrBuilder;

    return-object v1

    .line 8
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    if-nez v0, :cond_2

    .line 9
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object v0

    :cond_2
    return-object v0
.end method

.method public hasConstraints()Z
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    const/4 v1, 0x1

    and-int/2addr v0, v1

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    return v1
.end method

.method public hasItem()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method protected internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_Target_fieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    .line 2
    const-class v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    const-class v2, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public mergeConstraints(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v0, v0, 0x1

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    if-eqz v0, :cond_0

    .line 4
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    move-result-object v1

    if-eq v0, v1, :cond_0

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    .line 6
    invoke-static {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;->newBuilder(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints$Builder;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints$Builder;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    goto :goto_0

    .line 8
    :cond_0
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    .line 10
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_1

    .line 12
    :cond_1
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->a(Lcom/android/tools/r8/internal/J0;)V

    .line 14
    :goto_1
    iget p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    or-int/lit8 p1, p1, 0x1

    iput p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    return-object p0
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/H0;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/M0;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 3
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/uU;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 4
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/zU;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 6
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 49
    :try_start_0
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$sfgetPARSER()Lcom/android/tools/r8/internal/z30;

    move-result-object v1

    invoke-interface {v1, p1, p2}, Lcom/android/tools/r8/internal/z30;->parsePartialFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz p1, :cond_0

    .line 55
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    :cond_0
    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 56
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 57
    check-cast p2, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 58
    :try_start_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p1

    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_1

    .line 61
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    .line 63
    :cond_1
    throw p1
.end method

.method public mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 7
    instance-of v0, p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    if-eqz v0, :cond_0

    .line 8
    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1

    .line 10
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    return-object p0
.end method

.method public mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 2

    .line 11
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v0

    if-ne p1, v0, :cond_0

    return-object p0

    .line 12
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->hasItem()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 13
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->getItem()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeItem(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    .line 15
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->hasConstraints()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 16
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->getConstraints()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeConstraints(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    .line 18
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_4

    .line 19
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fgetconstraintAdditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_7

    .line 20
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 21
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fgetconstraintAdditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    .line 22
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x3

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    goto :goto_0

    .line 24
    :cond_3
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->ensureConstraintAdditionsIsMutable()V

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fgetconstraintAdditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 27
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_1

    .line 30
    :cond_4
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fgetconstraintAdditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_7

    .line 31
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    .line 32
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xc0;->b:Ljava/util/List;

    .line 33
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_6

    .line 34
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    const/4 v1, 0x0

    .line 35
    iput-object v1, v0, Lcom/android/tools/r8/internal/Xc0;->a:Lcom/android/tools/r8/internal/ey;

    .line 36
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    .line 37
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fgetconstraintAdditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    .line 38
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x3

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    .line 40
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->access$4000()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 41
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->getConstraintAdditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v1

    :cond_5
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    goto :goto_1

    .line 43
    :cond_6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->-$$Nest$fgetconstraintAdditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Ljava/util/List;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Xc0;->a(Ljava/lang/Iterable;)V

    .line 47
    :cond_7
    :goto_1
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->access$4100(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    .line 48
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    return-object p0
.end method

.method public mergeItem(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    if-eqz v0, :cond_0

    .line 4
    invoke-static {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;->newBuilder(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    goto :goto_0

    .line 6
    :cond_0
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 8
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_1

    .line 10
    :cond_1
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_1
    return-object p0
.end method

.method public bridge synthetic mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public final mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 0

    .line 4
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object p1
.end method

.method public removeConstraintAdditions(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->ensureConstraintAdditionsIsMutable()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 6
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->c(I)V

    :goto_0
    return-object p0
.end method

.method public setConstraintAdditions(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 11
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->ensureConstraintAdditionsIsMutable()V

    .line 12
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    move-result-object p2

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 13
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 15
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->c(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public setConstraintAdditions(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraint;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->ensureConstraintAdditionsIsMutable()V

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintAdditions_:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 9
    :cond_0
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->c(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public setConstraints(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 11
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 12
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    .line 13
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 15
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    .line 17
    :goto_0
    iget p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    or-int/lit8 p1, p1, 0x1

    iput p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    return-object p0
.end method

.method public setConstraints(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraintsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->constraints_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Constraints;

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    .line 10
    :goto_0
    iget p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    or-int/lit8 p1, p1, 0x1

    iput p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->bitField0_:I

    return-object p0
.end method

.method public bridge synthetic setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object p1
.end method

.method public setItem(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 10
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 11
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 13
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public setItem(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->itemBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->item_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public bridge synthetic setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2, p3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/dy;->setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object p1
.end method

.method public bridge synthetic setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    move-result-object p1

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object p1
.end method
