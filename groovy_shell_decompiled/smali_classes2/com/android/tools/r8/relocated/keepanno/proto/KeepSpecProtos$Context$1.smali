.class Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Context$1;
.super Lcom/android/tools/r8/internal/j1;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Context;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/android/tools/r8/internal/j1;"
    }
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/j1;-><init>()V

    return-void
.end method


# virtual methods
.method public parsePartialFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Context;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 2
    new-instance v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Context;

    const/4 v1, 0x0

    invoke-direct {v0, p1, p2, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Context;-><init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Context-IA;)V

    return-object v0
.end method

.method public bridge synthetic parsePartialFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Context$1;->parsePartialFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Context;

    move-result-object p1

    return-object p1
.end method
