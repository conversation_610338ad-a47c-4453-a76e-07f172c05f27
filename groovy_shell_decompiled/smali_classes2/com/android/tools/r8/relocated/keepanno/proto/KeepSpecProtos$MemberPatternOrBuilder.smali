.class public interface abstract Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPatternOrBuilder;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "MemberPatternOrBuilder"
.end annotation


# virtual methods
.method public abstract synthetic findInitializationErrors()Ljava/util/List;
.end method

.method public abstract synthetic getAllFields()Ljava/util/Map;
.end method

.method public abstract synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
.end method

.method public abstract synthetic getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
.end method

.method public abstract synthetic getField(Lcom/android/tools/r8/internal/al;)Ljava/lang/Object;
.end method

.method public abstract getFieldMember()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPatternField;
.end method

.method public abstract getFieldMemberOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPatternFieldOrBuilder;
.end method

.method public abstract getGeneralMember()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPatternGeneral;
.end method

.method public abstract getGeneralMemberOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPatternGeneralOrBuilder;
.end method

.method public abstract synthetic getInitializationErrorString()Ljava/lang/String;
.end method

.method public abstract getMemberOneofCase()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$MemberOneofCase;
.end method

.method public abstract getMethodMember()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPatternMethod;
.end method

.method public abstract getMethodMemberOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPatternMethodOrBuilder;
.end method

.method public abstract synthetic getOneofFieldDescriptor(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/al;
.end method

.method public abstract synthetic getRepeatedField(Lcom/android/tools/r8/internal/al;I)Ljava/lang/Object;
.end method

.method public abstract synthetic getRepeatedFieldCount(Lcom/android/tools/r8/internal/al;)I
.end method

.method public abstract synthetic getUnknownFields()Lcom/android/tools/r8/internal/vs0;
.end method

.method public abstract synthetic hasField(Lcom/android/tools/r8/internal/al;)Z
.end method

.method public abstract hasFieldMember()Z
.end method

.method public abstract hasGeneralMember()Z
.end method

.method public abstract hasMethodMember()Z
.end method

.method public abstract synthetic hasOneof(Lcom/android/tools/r8/internal/el;)Z
.end method
