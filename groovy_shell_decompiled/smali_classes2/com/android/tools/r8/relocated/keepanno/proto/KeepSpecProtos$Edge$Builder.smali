.class public final Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
.super Lcom/android/tools/r8/internal/dy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$EdgeOrBuilder;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Builder"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/android/tools/r8/internal/dy;",
        "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$EdgeOrBuilder;"
    }
.end annotation


# instance fields
.field private bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation
.end field

.field private bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

.field private bitField0_:I

.field private consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/Xc0;"
        }
    .end annotation
.end field

.field private consequences_:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;",
            ">;"
        }
    .end annotation
.end field

.field private metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation
.end field

.field private metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

.field private preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/Xc0;"
        }
    .end annotation
.end field

.field private preconditions_:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method private constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/dy;-><init>()V

    .line 474
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    .line 714
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    .line 715
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->maybeForceBuilderInitialization()V

    return-void
.end method

.method private constructor <init>(Lcom/android/tools/r8/internal/ey;)V
    .locals 0

    .line 716
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/dy;-><init>(Lcom/android/tools/r8/internal/ey;)V

    .line 1183
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    .line 1423
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    .line 1424
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->maybeForceBuilderInitialization()V

    return-void
.end method

.method synthetic constructor <init>(Lcom/android/tools/r8/internal/ey;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder-IA;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;-><init>(Lcom/android/tools/r8/internal/ey;)V

    return-void
.end method

.method synthetic constructor <init>(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder-IA;)V
    .locals 0

    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;-><init>()V

    return-void
.end method

.method private ensureConsequencesIsMutable()V
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, 0x2

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    .line 3
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    or-int/lit8 v0, v0, 0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    :cond_0
    return-void
.end method

.method private ensurePreconditionsIsMutable()V
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, 0x1

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    .line 3
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    or-int/lit8 v0, v0, 0x1

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    :cond_0
    return-void
.end method

.method private getBindingsFieldBuilder()Lcom/android/tools/r8/internal/dk0;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/dk0;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getBindings()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    move-result-object v1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v2

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v3

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/dk0;-><init>(Lcom/android/tools/r8/internal/J0;Lcom/android/tools/r8/internal/I0;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v0, 0x0

    .line 7
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    return-object v0
.end method

.method private getConsequencesFieldBuilder()Lcom/android/tools/r8/internal/Xc0;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/Xc0;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Xc0;

    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    iget v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v2, v2, 0x2

    if-eqz v2, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    .line 6
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v3

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v4

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/android/tools/r8/internal/Xc0;-><init>(Ljava/util/List;ZLcom/android/tools/r8/internal/ey;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    const/4 v0, 0x0

    .line 8
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    .line 10
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    return-object v0
.end method

.method public static final getDescriptor()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_Edge_descriptor()Lcom/android/tools/r8/internal/Ok;

    move-result-object v0

    return-object v0
.end method

.method private getMetaInfoFieldBuilder()Lcom/android/tools/r8/internal/dk0;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/dk0;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getMetaInfo()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    move-result-object v1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v2

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v3

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/dk0;-><init>(Lcom/android/tools/r8/internal/J0;Lcom/android/tools/r8/internal/I0;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v0, 0x0

    .line 7
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    return-object v0
.end method

.method private getPreconditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/Xc0;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_1

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/Xc0;

    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    iget v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    const/4 v3, 0x1

    and-int/2addr v2, v3

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    .line 6
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v2

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v4

    invoke-direct {v0, v1, v3, v2, v4}, Lcom/android/tools/r8/internal/Xc0;-><init>(Ljava/util/List;ZLcom/android/tools/r8/internal/ey;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    const/4 v0, 0x0

    .line 8
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    .line 10
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    return-object v0
.end method

.method private maybeForceBuilderInitialization()V
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->access$2400()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getPreconditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    .line 3
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getConsequencesFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    :cond_0
    return-void
.end method


# virtual methods
.method public addAllConsequences(Ljava/lang/Iterable;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Iterable<",
            "+",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;",
            ">;)",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensureConsequencesIsMutable()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/M0;->addAll(Ljava/lang/Iterable;Ljava/util/List;)V

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 7
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(Ljava/lang/Iterable;)V

    :goto_0
    return-object p0
.end method

.method public addAllPreconditions(Ljava/lang/Iterable;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Iterable<",
            "+",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;",
            ">;)",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensurePreconditionsIsMutable()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-static {p1, v0}, Lcom/android/tools/r8/internal/M0;->addAll(Ljava/lang/Iterable;Ljava/util/List;)V

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 7
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(Ljava/lang/Iterable;)V

    :goto_0
    return-object p0
.end method

.method public addConsequences(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 26
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensureConsequencesIsMutable()V

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object p2

    invoke-interface {v0, p1, p2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 28
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 30
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->b(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public addConsequences(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 12
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 14
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensureConsequencesIsMutable()V

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 16
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 18
    :cond_0
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->b(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public addConsequences(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 19
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 20
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensureConsequencesIsMutable()V

    .line 21
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 22
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 24
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public addConsequences(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensureConsequencesIsMutable()V

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 9
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public addConsequencesBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getConsequencesFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v1

    .line 3
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Xc0;->a(Lcom/android/tools/r8/internal/uy;)Lcom/android/tools/r8/internal/H0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object v0
.end method

.method public addConsequencesBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 2

    .line 4
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getConsequencesFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    .line 5
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object v1

    .line 6
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Xc0;->a(ILcom/android/tools/r8/internal/uy;)Lcom/android/tools/r8/internal/H0;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object p1
.end method

.method public addPreconditions(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 26
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensurePreconditionsIsMutable()V

    .line 27
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    move-result-object p2

    invoke-interface {v0, p1, p2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 28
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 30
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->b(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public addPreconditions(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 12
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 14
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensurePreconditionsIsMutable()V

    .line 15
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 16
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 18
    :cond_0
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->b(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public addPreconditions(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 19
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 20
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensurePreconditionsIsMutable()V

    .line 21
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 22
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 24
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public addPreconditions(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensurePreconditionsIsMutable()V

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 9
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public addPreconditionsBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;
    .locals 2

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getPreconditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    move-result-object v1

    .line 3
    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Xc0;->a(Lcom/android/tools/r8/internal/uy;)Lcom/android/tools/r8/internal/H0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;

    return-object v0
.end method

.method public addPreconditionsBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;
    .locals 2

    .line 4
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getPreconditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    .line 5
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    move-result-object v1

    .line 6
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Xc0;->a(ILcom/android/tools/r8/internal/uy;)Lcom/android/tools/r8/internal/H0;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;

    return-object p1
.end method

.method public bridge synthetic addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    return-object p1
.end method

.method public bridge synthetic build()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic build()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    move-result-object v0

    return-object v0
.end method

.method public build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;
    .locals 2

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    move-result-object v0

    .line 4
    invoke-virtual {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 5
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public bridge synthetic buildPartial()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic buildPartial()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    move-result-object v0

    return-object v0
.end method

.method public buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;
    .locals 3

    .line 3
    new-instance v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;-><init>(Lcom/android/tools/r8/internal/dy;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge-IA;)V

    .line 5
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v2, 0x1

    if-nez v1, :cond_0

    .line 6
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fputmetaInfo_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;)V

    goto :goto_0

    .line 7
    :cond_0
    iput-boolean v2, v1, Lcom/android/tools/r8/internal/dk0;->d:Z

    .line 8
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v1

    .line 9
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fputmetaInfo_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;)V

    .line 11
    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v1, :cond_1

    .line 12
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fputbindings_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;)V

    goto :goto_1

    .line 13
    :cond_1
    iput-boolean v2, v1, Lcom/android/tools/r8/internal/dk0;->d:Z

    .line 14
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v1

    .line 15
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fputbindings_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;)V

    .line 17
    :goto_1
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v1, :cond_3

    .line 18
    iget v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/2addr v1, v2

    if-eqz v1, :cond_2

    .line 19
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-static {v1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    .line 20
    iget v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v1, v1, -0x2

    iput v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    .line 22
    :cond_2
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fputpreconditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;Ljava/util/List;)V

    goto :goto_2

    .line 24
    :cond_3
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Xc0;->b()Ljava/util/List;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fputpreconditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;Ljava/util/List;)V

    .line 26
    :goto_2
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v1, :cond_5

    .line 27
    iget v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v1, v1, 0x2

    if-eqz v1, :cond_4

    .line 28
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-static {v1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    .line 29
    iget v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v1, v1, -0x3

    iput v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    .line 31
    :cond_4
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fputconsequences_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;Ljava/util/List;)V

    goto :goto_3

    .line 33
    :cond_5
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/Xc0;->b()Ljava/util/List;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fputconsequences_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;Ljava/util/List;)V

    .line 35
    :goto_3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onBuilt()V

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/dy;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 2

    .line 5
    invoke-super {p0}, Lcom/android/tools/r8/internal/dy;->clear()Lcom/android/tools/r8/internal/dy;

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 7
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    goto :goto_0

    .line 9
    :cond_0
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    .line 10
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    .line 12
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 13
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    goto :goto_1

    .line 15
    :cond_1
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    .line 16
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    .line 18
    :goto_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_2

    .line 19
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    .line 20
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    goto :goto_2

    .line 22
    :cond_2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->c()V

    .line 24
    :goto_2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_3

    .line 25
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    .line 26
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x3

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    goto :goto_3

    .line 28
    :cond_3
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->c()V

    :goto_3
    return-object p0
.end method

.method public clearBindings()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 2
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 5
    :cond_0
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    .line 6
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    :goto_0
    return-object p0
.end method

.method public clearConsequences()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    .line 3
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x3

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 6
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->c()V

    :goto_0
    return-object p0
.end method

.method public bridge synthetic clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    return-object p1
.end method

.method public clearMetaInfo()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 2
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 5
    :cond_0
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    .line 6
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    :goto_0
    return-object p0
.end method

.method public bridge synthetic clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 0

    .line 4
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    return-object p1
.end method

.method public clearPreconditions()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    .line 3
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 6
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->c()V

    :goto_0
    return-object p0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/M0;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/dy;
    .locals 1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 7
    invoke-super {p0}, Lcom/android/tools/r8/internal/dy;->clone()Lcom/android/tools/r8/internal/dy;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    return-object v0
.end method

.method public bridge synthetic clone()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/CloneNotSupportedException;
        }
    .end annotation

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object v0

    return-object v0
.end method

.method public getBindings()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    move-result-object v0

    :cond_0
    return-object v0

    .line 4
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    return-object v0
.end method

.method public getBindingsBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings$Builder;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getBindingsFieldBuilder()Lcom/android/tools/r8/internal/dk0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->c()Lcom/android/tools/r8/internal/H0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings$Builder;

    return-object v0
.end method

.method public getBindingsOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingsOrBuilder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-eqz v0, :cond_1

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->b:Lcom/android/tools/r8/internal/H0;

    if-eqz v1, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->c:Lcom/android/tools/r8/internal/J0;

    .line 6
    :goto_0
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingsOrBuilder;

    return-object v1

    .line 8
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    if-nez v0, :cond_2

    .line 9
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    move-result-object v0

    :cond_2
    return-object v0
.end method

.method public getConsequences(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    return-object p1

    :cond_0
    const/4 v1, 0x0

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Xc0;->a(IZ)Lcom/android/tools/r8/internal/J0;

    move-result-object p1

    .line 4
    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    return-object p1
.end method

.method public getConsequencesBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getConsequencesFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(I)Lcom/android/tools/r8/internal/H0;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;

    return-object p1
.end method

.method public getConsequencesBuilderList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getConsequencesFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->f()Lcom/android/tools/r8/internal/Uc0;

    move-result-object v0

    return-object v0
.end method

.method public getConsequencesCount()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0

    .line 3
    :cond_0
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xc0;->b:Ljava/util/List;

    .line 4
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method

.method public getConsequencesList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 4
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->g()Lcom/android/tools/r8/internal/Vc0;

    move-result-object v0

    return-object v0
.end method

.method public getConsequencesOrBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TargetOrBuilder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TargetOrBuilder;

    return-object p1

    .line 3
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->b(I)Lcom/android/tools/r8/internal/DU;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TargetOrBuilder;

    return-object p1
.end method

.method public getConsequencesOrBuilderList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "+",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TargetOrBuilder;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->h()Lcom/android/tools/r8/internal/Wc0;

    move-result-object v0

    return-object v0

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    move-result-object v0

    return-object v0
.end method

.method public getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;
    .locals 1

    .line 3
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    move-result-object v0

    return-object v0
.end method

.method public getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_Edge_descriptor()Lcom/android/tools/r8/internal/Ok;

    move-result-object v0

    return-object v0
.end method

.method public getMetaInfo()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    move-result-object v0

    :cond_0
    return-object v0

    .line 4
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    return-object v0
.end method

.method public getMetaInfoBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo$Builder;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getMetaInfoFieldBuilder()Lcom/android/tools/r8/internal/dk0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->c()Lcom/android/tools/r8/internal/H0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo$Builder;

    return-object v0
.end method

.method public getMetaInfoOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfoOrBuilder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-eqz v0, :cond_1

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->b:Lcom/android/tools/r8/internal/H0;

    if-eqz v1, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->c:Lcom/android/tools/r8/internal/J0;

    .line 6
    :goto_0
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfoOrBuilder;

    return-object v1

    .line 8
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    if-nez v0, :cond_2

    .line 9
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    move-result-object v0

    :cond_2
    return-object v0
.end method

.method public getPreconditions(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    return-object p1

    :cond_0
    const/4 v1, 0x0

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/android/tools/r8/internal/Xc0;->a(IZ)Lcom/android/tools/r8/internal/J0;

    move-result-object p1

    .line 4
    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    return-object p1
.end method

.method public getPreconditionsBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;
    .locals 1

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getPreconditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->a(I)Lcom/android/tools/r8/internal/H0;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;

    return-object p1
.end method

.method public getPreconditionsBuilderList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getPreconditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->f()Lcom/android/tools/r8/internal/Uc0;

    move-result-object v0

    return-object v0
.end method

.method public getPreconditionsCount()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0

    .line 3
    :cond_0
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xc0;->b:Ljava/util/List;

    .line 4
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method

.method public getPreconditionsList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 4
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->g()Lcom/android/tools/r8/internal/Vc0;

    move-result-object v0

    return-object v0
.end method

.method public getPreconditionsOrBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConditionOrBuilder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConditionOrBuilder;

    return-object p1

    .line 3
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->b(I)Lcom/android/tools/r8/internal/DU;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConditionOrBuilder;

    return-object p1
.end method

.method public getPreconditionsOrBuilderList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "+",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConditionOrBuilder;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Xc0;->h()Lcom/android/tools/r8/internal/Wc0;

    move-result-object v0

    return-object v0

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public hasBindings()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public hasMetaInfo()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method protected internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_Edge_fieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    .line 2
    const-class v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    const-class v2, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public mergeBindings(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    if-eqz v0, :cond_0

    .line 4
    invoke-static {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;->newBuilder(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings$Builder;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings$Builder;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    goto :goto_0

    .line 6
    :cond_0
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    .line 8
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_1

    .line 10
    :cond_1
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_1
    return-object p0
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/H0;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/M0;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 3
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/uU;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 4
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/zU;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 6
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 78
    :try_start_0
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$sfgetPARSER()Lcom/android/tools/r8/internal/z30;

    move-result-object v1

    invoke-interface {v1, p1, p2}, Lcom/android/tools/r8/internal/z30;->parsePartialFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz p1, :cond_0

    .line 84
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    :cond_0
    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 85
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 86
    check-cast p2, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 87
    :try_start_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p1

    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_1

    .line 90
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    .line 92
    :cond_1
    throw p1
.end method

.method public mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 7
    instance-of v0, p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    if-eqz v0, :cond_0

    .line 8
    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1

    .line 10
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    return-object p0
.end method

.method public mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 3

    .line 11
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;

    move-result-object v0

    if-ne p1, v0, :cond_0

    return-object p0

    .line 12
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->hasMetaInfo()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 13
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->getMetaInfo()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeMetaInfo(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    .line 15
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->hasBindings()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 16
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->getBindings()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeBindings(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    .line 18
    :cond_2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    const/4 v1, 0x0

    if-nez v0, :cond_4

    .line 19
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetpreconditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_7

    .line 20
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 21
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetpreconditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    .line 22
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    goto :goto_0

    .line 24
    :cond_3
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensurePreconditionsIsMutable()V

    .line 25
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetpreconditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 27
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_2

    .line 30
    :cond_4
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetpreconditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_7

    .line 31
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    .line 32
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xc0;->b:Ljava/util/List;

    .line 33
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_6

    .line 34
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    .line 35
    iput-object v1, v0, Lcom/android/tools/r8/internal/Xc0;->a:Lcom/android/tools/r8/internal/ey;

    .line 36
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    .line 37
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetpreconditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    .line 38
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    .line 40
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->access$2500()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 41
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getPreconditionsFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v0

    goto :goto_1

    :cond_5
    move-object v0, v1

    :goto_1
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    goto :goto_2

    .line 43
    :cond_6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetpreconditions_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v2

    invoke-virtual {v0, v2}, Lcom/android/tools/r8/internal/Xc0;->a(Ljava/lang/Iterable;)V

    .line 47
    :cond_7
    :goto_2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_9

    .line 48
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetconsequences_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_c

    .line 49
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_8

    .line 50
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetconsequences_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    .line 51
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x3

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    goto :goto_3

    .line 53
    :cond_8
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensureConsequencesIsMutable()V

    .line 54
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetconsequences_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 56
    :goto_3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_4

    .line 59
    :cond_9
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetconsequences_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_c

    .line 60
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    .line 61
    iget-object v0, v0, Lcom/android/tools/r8/internal/Xc0;->b:Ljava/util/List;

    .line 62
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_b

    .line 63
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    .line 64
    iput-object v1, v0, Lcom/android/tools/r8/internal/Xc0;->a:Lcom/android/tools/r8/internal/ey;

    .line 65
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    .line 66
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetconsequences_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    .line 67
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x3

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bitField0_:I

    .line 69
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->access$2600()Z

    move-result v0

    if-eqz v0, :cond_a

    .line 70
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->getConsequencesFieldBuilder()Lcom/android/tools/r8/internal/Xc0;

    move-result-object v1

    :cond_a
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    goto :goto_4

    .line 72
    :cond_b
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->-$$Nest$fgetconsequences_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Ljava/util/List;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/android/tools/r8/internal/Xc0;->a(Ljava/lang/Iterable;)V

    .line 76
    :cond_c
    :goto_4
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;->access$2700(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge;)Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    .line 77
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    return-object p0
.end method

.method public mergeMetaInfo(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    if-eqz v0, :cond_0

    .line 4
    invoke-static {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;->newBuilder(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo$Builder;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo$Builder;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    goto :goto_0

    .line 6
    :cond_0
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    .line 8
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_1

    .line 10
    :cond_1
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_1
    return-object p0
.end method

.method public bridge synthetic mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public final mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 0

    .line 4
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    return-object p1
.end method

.method public removeConsequences(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensureConsequencesIsMutable()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 6
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->c(I)V

    :goto_0
    return-object p0
.end method

.method public removePreconditions(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensurePreconditionsIsMutable()V

    .line 3
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 6
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/Xc0;->c(I)V

    :goto_0
    return-object p0
.end method

.method public setBindings(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 10
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    .line 11
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 13
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public setBindings(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindingsBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->bindings_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Bindings;

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public setConsequences(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 11
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensureConsequencesIsMutable()V

    .line 12
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object p2

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 13
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 15
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->c(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public setConsequences(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Target;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequencesBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensureConsequencesIsMutable()V

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->consequences_:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 9
    :cond_0
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->c(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public bridge synthetic setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    return-object p1
.end method

.method public setMetaInfo(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 10
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    .line 11
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 13
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public setMetaInfo(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfoBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->metaInfo_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MetaInfo;

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public setPreconditions(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 10
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 11
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensurePreconditionsIsMutable()V

    .line 12
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    move-result-object p2

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 13
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 15
    :cond_0
    invoke-virtual {p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->c(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public setPreconditions(ILcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Condition;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditionsBuilder_:Lcom/android/tools/r8/internal/Xc0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->ensurePreconditionsIsMutable()V

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->preconditions_:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 9
    :cond_0
    invoke-virtual {v0, p1, p2}, Lcom/android/tools/r8/internal/Xc0;->c(ILcom/android/tools/r8/internal/uy;)V

    :goto_0
    return-object p0
.end method

.method public bridge synthetic setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2, p3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/dy;->setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    return-object p1
.end method

.method public bridge synthetic setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    move-result-object p1

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$Edge$Builder;

    return-object p1
.end method
