.class public final Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
.super Lcom/android/tools/r8/internal/dy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPatternOrBuilder;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Builder"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/android/tools/r8/internal/dy;",
        "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPatternOrBuilder;"
    }
.end annotation


# instance fields
.field private bitField0_:I

.field private classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation
.end field

.field private classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

.field private memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation
.end field

.field private memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/android/tools/r8/internal/dy;-><init>()V

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->maybeForceBuilderInitialization()V

    return-void
.end method

.method private constructor <init>(Lcom/android/tools/r8/internal/ey;)V
    .locals 0

    .line 3
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/dy;-><init>(Lcom/android/tools/r8/internal/ey;)V

    .line 4
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->maybeForceBuilderInitialization()V

    return-void
.end method

.method synthetic constructor <init>(Lcom/android/tools/r8/internal/ey;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder-IA;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;-><init>(Lcom/android/tools/r8/internal/ey;)V

    return-void
.end method

.method synthetic constructor <init>(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder-IA;)V
    .locals 0

    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;-><init>()V

    return-void
.end method

.method private getClassReferenceFieldBuilder()Lcom/android/tools/r8/internal/dk0;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/dk0;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->getClassReference()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object v1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v2

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v3

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/dk0;-><init>(Lcom/android/tools/r8/internal/J0;Lcom/android/tools/r8/internal/I0;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v0, 0x0

    .line 7
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    return-object v0
.end method

.method public static final getDescriptor()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_MemberItemPattern_descriptor()Lcom/android/tools/r8/internal/Ok;

    move-result-object v0

    return-object v0
.end method

.method private getMemberPatternFieldBuilder()Lcom/android/tools/r8/internal/dk0;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/dk0;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Lcom/android/tools/r8/internal/dk0;

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->getMemberPattern()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    move-result-object v1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->getParentForChildren()Lcom/android/tools/r8/internal/ey;

    move-result-object v2

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->isClean()Z

    move-result v3

    invoke-direct {v0, v1, v2, v3}, Lcom/android/tools/r8/internal/dk0;-><init>(Lcom/android/tools/r8/internal/J0;Lcom/android/tools/r8/internal/I0;Z)V

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v0, 0x0

    .line 7
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    return-object v0
.end method

.method private maybeForceBuilderInitialization()V
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->access$7500()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->getMemberPatternFieldBuilder()Lcom/android/tools/r8/internal/dk0;

    :cond_0
    return-void
.end method


# virtual methods
.method public bridge synthetic addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->addRepeatedField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    return-object p1
.end method

.method public bridge synthetic build()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic build()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    move-result-object v0

    return-object v0
.end method

.method public build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;
    .locals 2

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    move-result-object v0

    .line 4
    invoke-virtual {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->isInitialized()Z

    move-result v1

    if-eqz v1, :cond_0

    return-object v0

    .line 5
    :cond_0
    invoke-static {v0}, Lcom/android/tools/r8/internal/H0;->newUninitializedMessageException(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/is0;

    move-result-object v0

    throw v0
.end method

.method public bridge synthetic buildPartial()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic buildPartial()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    move-result-object v0

    return-object v0
.end method

.method public buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;
    .locals 4

    .line 3
    new-instance v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;-><init>(Lcom/android/tools/r8/internal/dy;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern-IA;)V

    .line 4
    iget v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    .line 6
    iget-object v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v3, 0x1

    if-nez v2, :cond_0

    .line 7
    iget-object v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    invoke-static {v0, v2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->-$$Nest$fputclassReference_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)V

    goto :goto_0

    .line 8
    :cond_0
    iput-boolean v3, v2, Lcom/android/tools/r8/internal/dk0;->d:Z

    .line 9
    invoke-virtual {v2}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v2

    .line 10
    check-cast v2, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    invoke-static {v0, v2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->-$$Nest$fputclassReference_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)V

    :goto_0
    and-int/2addr v1, v3

    if-eqz v1, :cond_2

    .line 13
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v1, :cond_1

    .line 14
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->-$$Nest$fputmemberPattern_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;)V

    goto :goto_1

    .line 15
    :cond_1
    iput-boolean v3, v1, Lcom/android/tools/r8/internal/dk0;->d:Z

    .line 16
    invoke-virtual {v1}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v1

    .line 17
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    invoke-static {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->-$$Nest$fputmemberPattern_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;)V

    goto :goto_1

    :cond_2
    const/4 v3, 0x0

    .line 21
    :goto_1
    invoke-static {v0, v3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->-$$Nest$fputbitField0_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;I)V

    .line 22
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onBuilt()V

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/dy;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public clear()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 2

    .line 5
    invoke-super {p0}, Lcom/android/tools/r8/internal/dy;->clear()Lcom/android/tools/r8/internal/dy;

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 7
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    goto :goto_0

    .line 9
    :cond_0
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 10
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    .line 12
    :goto_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 13
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    goto :goto_1

    .line 15
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->b()V

    .line 17
    :goto_1
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    return-object p0
.end method

.method public clearClassReference()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 2
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 5
    :cond_0
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 6
    iput-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    :goto_0
    return-object p0
.end method

.method public bridge synthetic clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->clearField(Lcom/android/tools/r8/internal/al;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    return-object p1
.end method

.method public clearMemberPattern()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 5
    :cond_0
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->b()V

    .line 7
    :goto_0
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    and-int/lit8 v0, v0, -0x2

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    return-object p0
.end method

.method public bridge synthetic clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 0

    .line 4
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->clearOneof(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    return-object p1
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/H0;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/M0;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/dy;
    .locals 1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 1

    .line 7
    invoke-super {p0}, Lcom/android/tools/r8/internal/dy;->clone()Lcom/android/tools/r8/internal/dy;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    return-object v0
.end method

.method public bridge synthetic clone()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/CloneNotSupportedException;
        }
    .end annotation

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->clone()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object v0

    return-object v0
.end method

.method public getClassReference()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object v0

    :cond_0
    return-object v0

    .line 4
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    return-object v0
.end method

.method public getClassReferenceBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 2
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->getClassReferenceFieldBuilder()Lcom/android/tools/r8/internal/dk0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->c()Lcom/android/tools/r8/internal/H0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;

    return-object v0
.end method

.method public getClassReferenceOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReferenceOrBuilder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-eqz v0, :cond_1

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->b:Lcom/android/tools/r8/internal/H0;

    if-eqz v1, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->c:Lcom/android/tools/r8/internal/J0;

    .line 6
    :goto_0
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReferenceOrBuilder;

    return-object v1

    .line 8
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    if-nez v0, :cond_2

    .line 9
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object v0

    :cond_2
    return-object v0
.end method

.method public bridge synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    move-result-object v0

    return-object v0
.end method

.method public getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;
    .locals 1

    .line 3
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    move-result-object v0

    return-object v0
.end method

.method public getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_MemberItemPattern_descriptor()Lcom/android/tools/r8/internal/Ok;

    move-result-object v0

    return-object v0
.end method

.method public getMemberPattern()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    move-result-object v0

    :cond_0
    return-object v0

    .line 4
    :cond_1
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->d()Lcom/android/tools/r8/internal/J0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    return-object v0
.end method

.method public getMemberPatternBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$Builder;
    .locals 1

    .line 1
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    or-int/lit8 v0, v0, 0x1

    iput v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    .line 3
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->getMemberPatternFieldBuilder()Lcom/android/tools/r8/internal/dk0;

    move-result-object v0

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/dk0;->c()Lcom/android/tools/r8/internal/H0;

    move-result-object v0

    check-cast v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$Builder;

    return-object v0
.end method

.method public getMemberPatternOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPatternOrBuilder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-eqz v0, :cond_1

    .line 2
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->b:Lcom/android/tools/r8/internal/H0;

    if-eqz v1, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    iget-object v1, v0, Lcom/android/tools/r8/internal/dk0;->c:Lcom/android/tools/r8/internal/J0;

    .line 6
    :goto_0
    check-cast v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPatternOrBuilder;

    return-object v1

    .line 8
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    if-nez v0, :cond_2

    .line 9
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    move-result-object v0

    :cond_2
    return-object v0
.end method

.method public hasClassReference()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public hasMemberPattern()Z
    .locals 2

    .line 1
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    const/4 v1, 0x1

    and-int/2addr v0, v1

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    return v1
.end method

.method protected internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_MemberItemPattern_fieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    .line 2
    const-class v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    const-class v2, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public mergeClassReference(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    if-eqz v0, :cond_0

    .line 4
    invoke-static {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;->newBuilder(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    goto :goto_0

    .line 6
    :cond_0
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 8
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_1

    .line 10
    :cond_1
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->a(Lcom/android/tools/r8/internal/J0;)V

    :goto_1
    return-object p0
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/H0;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/M0;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 3
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/uU;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 4
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 5
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/zU;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 6
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public mergeFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 20
    :try_start_0
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->-$$Nest$sfgetPARSER()Lcom/android/tools/r8/internal/z30;

    move-result-object v1

    invoke-interface {v1, p1, p2}, Lcom/android/tools/r8/internal/z30;->parsePartialFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz p1, :cond_0

    .line 26
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    :cond_0
    return-object p0

    :catchall_0
    move-exception p1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 27
    :try_start_1
    iget-object p2, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 28
    check-cast p2, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 29
    :try_start_2
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/lI;->a()Ljava/io/IOException;

    move-result-object p1

    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    move-object v0, p2

    :goto_0
    if-eqz v0, :cond_1

    .line 32
    invoke-virtual {p0, v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    .line 34
    :cond_1
    throw p1
.end method

.method public mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 1

    .line 7
    instance-of v0, p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    if-eqz v0, :cond_0

    .line 8
    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1

    .line 10
    :cond_0
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/H0;->mergeFrom(Lcom/android/tools/r8/internal/vU;)Lcom/android/tools/r8/internal/H0;

    return-object p0
.end method

.method public mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 1

    .line 11
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;

    move-result-object v0

    if-ne p1, v0, :cond_0

    return-object p0

    .line 12
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->hasClassReference()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 13
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->getClassReference()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeClassReference(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    .line 15
    :cond_1
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->hasMemberPattern()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 16
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->getMemberPattern()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeMemberPattern(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    .line 18
    :cond_2
    invoke-static {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;->access$7600(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern;)Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    .line 19
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    return-object p0
.end method

.method public mergeMemberPattern(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_1

    .line 2
    iget v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    and-int/lit8 v0, v0, 0x1

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    if-eqz v0, :cond_0

    .line 4
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    move-result-object v1

    if-eq v0, v1, :cond_0

    .line 5
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    .line 6
    invoke-static {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;->newBuilder(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$Builder;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$Builder;

    move-result-object p1

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    goto :goto_0

    .line 8
    :cond_0
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    .line 10
    :goto_0
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_1

    .line 12
    :cond_1
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->a(Lcom/android/tools/r8/internal/J0;)V

    .line 14
    :goto_1
    iget p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    or-int/lit8 p1, p1, 0x1

    iput p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    return-object p0
.end method

.method public bridge synthetic mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/H0;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 3
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public final mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 0

    .line 4
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->mergeUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    return-object p1
.end method

.method public setClassReference(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 1

    .line 9
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 10
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 11
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 13
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public setClassReference(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReferenceBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->classReference_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    :goto_0
    return-object p0
.end method

.method public bridge synthetic setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1, p2}, Lcom/android/tools/r8/internal/dy;->setField(Lcom/android/tools/r8/internal/al;Ljava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    return-object p1
.end method

.method public setMemberPattern(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$Builder;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 1

    .line 11
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 12
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    .line 13
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 15
    :cond_0
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern$Builder;->build()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    .line 17
    :goto_0
    iget p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    or-int/lit8 p1, p1, 0x1

    iput p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    return-object p0
.end method

.method public setMemberPattern(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPatternBuilder_:Lcom/android/tools/r8/internal/dk0;

    if-nez v0, :cond_0

    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->memberPattern_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberPattern;

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/dy;->onChanged()V

    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/dk0;->b(Lcom/android/tools/r8/internal/J0;)V

    .line 10
    :goto_0
    iget p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    or-int/lit8 p1, p1, 0x1

    iput p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->bitField0_:I

    return-object p0
.end method

.method public bridge synthetic setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1, p2, p3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1, p2, p3}, Lcom/android/tools/r8/internal/dy;->setRepeatedField(Lcom/android/tools/r8/internal/al;ILjava/lang/Object;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    return-object p1
.end method

.method public bridge synthetic setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    move-result-object p1

    return-object p1
.end method

.method public final setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;
    .locals 0

    .line 3
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/dy;->setUnknownFields(Lcom/android/tools/r8/internal/vs0;)Lcom/android/tools/r8/internal/dy;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MemberItemPattern$Builder;

    return-object p1
.end method
