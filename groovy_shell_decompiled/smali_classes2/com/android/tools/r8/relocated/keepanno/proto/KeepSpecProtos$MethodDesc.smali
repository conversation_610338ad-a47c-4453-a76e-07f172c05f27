.class public final Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
.super Lcom/android/tools/r8/internal/uy;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDescOrBuilder;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "MethodDesc"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;
    }
.end annotation


# static fields
.field private static final DEFAULT_INSTANCE:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

.field public static final HOLDER_FIELD_NUMBER:I = 0x2

.field public static final NAME_FIELD_NUMBER:I = 0x1

.field public static final PARAMETER_TYPES_FIELD_NUMBER:I = 0x4

.field private static final PARSER:Lcom/android/tools/r8/internal/z30;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/android/tools/r8/internal/z30;"
        }
    .end annotation
.end field

.field public static final RETURN_TYPE_FIELD_NUMBER:I = 0x3

.field private static final serialVersionUID:J


# instance fields
.field private holder_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

.field private memoizedIsInitialized:B

.field private volatile name_:Ljava/lang/Object;

.field private parameterTypes_:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;",
            ">;"
        }
    .end annotation
.end field

.field private returnType_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;


# direct methods
.method static bridge synthetic -$$Nest$fgetname_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;)Ljava/lang/Object;
    .locals 0

    iget-object p0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    return-object p0
.end method

.method static bridge synthetic -$$Nest$fgetparameterTypes_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;)Ljava/util/List;
    .locals 0

    iget-object p0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    return-object p0
.end method

.method static bridge synthetic -$$Nest$fputholder_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;)V
    .locals 0

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->holder_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    return-void
.end method

.method static bridge synthetic -$$Nest$fputname_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;Ljava/lang/Object;)V
    .locals 0

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    return-void
.end method

.method static bridge synthetic -$$Nest$fputparameterTypes_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;Ljava/util/List;)V
    .locals 0

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    return-void
.end method

.method static bridge synthetic -$$Nest$fputreturnType_(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;)V
    .locals 0

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->returnType_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    return-void
.end method

.method static bridge synthetic -$$Nest$sfgetPARSER()Lcom/android/tools/r8/internal/z30;
    .locals 1

    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    invoke-direct {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;-><init>()V

    sput-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->DEFAULT_INSTANCE:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    .line 9
    new-instance v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$1;

    invoke-direct {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$1;-><init>()V

    sput-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    return-void
.end method

.method private constructor <init>()V
    .locals 1

    .line 246
    invoke-direct {p0}, Lcom/android/tools/r8/internal/uy;-><init>()V

    const/4 v0, -0x1

    .line 488
    iput-byte v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->memoizedIsInitialized:B

    const-string v0, ""

    .line 489
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    .line 490
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    return-void
.end method

.method private constructor <init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 491
    invoke-direct {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;-><init>()V

    .line 493
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 497
    sget-object v0, Lcom/android/tools/r8/internal/vs0;->c:Lcom/android/tools/r8/internal/vs0;

    .line 498
    new-instance v0, Lcom/android/tools/r8/internal/qs0;

    invoke-direct {v0}, Lcom/android/tools/r8/internal/qs0;-><init>()V

    const/4 v1, 0x1

    const/4 v2, 0x0

    move v3, v2

    :cond_0
    :goto_0
    if-nez v2, :cond_a

    .line 499
    :try_start_0
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->s()I

    move-result v4

    if-eqz v4, :cond_8

    const/16 v5, 0xa

    if-eq v4, v5, :cond_7

    const/16 v5, 0x12

    const/4 v6, 0x0

    if-eq v4, v5, :cond_5

    const/16 v5, 0x1a

    if-eq v4, v5, :cond_3

    const/16 v5, 0x22

    if-eq v4, v5, :cond_1

    .line 546
    invoke-virtual {p0, p1, v0, p2, v4}, Lcom/android/tools/r8/internal/uy;->parseUnknownField(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/qs0;Lcom/android/tools/r8/internal/Lu;I)Z

    move-result v4

    if-nez v4, :cond_0

    goto :goto_1

    :cond_1
    if-nez v3, :cond_2

    .line 547
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    iput-object v4, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    move v3, v1

    .line 550
    :cond_2
    iget-object v4, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    .line 551
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->parser()Lcom/android/tools/r8/internal/z30;

    move-result-object v5

    invoke-virtual {p1, v5, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v5

    check-cast v5, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    .line 552
    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 553
    :cond_3
    iget-object v4, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->returnType_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v4, :cond_4

    .line 554
    invoke-virtual {v4}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->toBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc$Builder;

    move-result-object v6

    .line 556
    :cond_4
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->parser()Lcom/android/tools/r8/internal/z30;

    move-result-object v4

    invoke-virtual {p1, v4, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    iput-object v4, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->returnType_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v6, :cond_0

    .line 558
    invoke-virtual {v6, v4}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc$Builder;

    .line 559
    invoke-virtual {v6}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v4

    iput-object v4, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->returnType_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    goto :goto_0

    .line 560
    :cond_5
    iget-object v4, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->holder_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v4, :cond_6

    .line 561
    invoke-virtual {v4}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->toBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc$Builder;

    move-result-object v6

    .line 563
    :cond_6
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->parser()Lcom/android/tools/r8/internal/z30;

    move-result-object v4

    invoke-virtual {p1, v4, p2}, Lcom/android/tools/r8/internal/ce;->a(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/AU;

    move-result-object v4

    check-cast v4, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    iput-object v4, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->holder_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v6, :cond_0

    .line 565
    invoke-virtual {v6, v4}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc$Builder;

    .line 566
    invoke-virtual {v6}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc$Builder;->buildPartial()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v4

    iput-object v4, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->holder_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    goto :goto_0

    .line 567
    :cond_7
    invoke-virtual {p1}, Lcom/android/tools/r8/internal/ce;->r()Ljava/lang/String;

    move-result-object v4

    .line 569
    iput-object v4, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;
    :try_end_0
    .catch Lcom/android/tools/r8/internal/lI; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto/16 :goto_0

    :cond_8
    :goto_1
    move v2, v1

    goto/16 :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :catch_0
    move-exception p1

    .line 619
    :try_start_1
    new-instance p2, Lcom/android/tools/r8/internal/lI;

    invoke-direct {p2, p1}, Lcom/android/tools/r8/internal/lI;-><init>(Ljava/io/IOException;)V

    .line 620
    iput-object p0, p2, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 621
    throw p2

    :catch_1
    move-exception p1

    .line 622
    iput-object p0, p1, Lcom/android/tools/r8/internal/lI;->b:Lcom/android/tools/r8/internal/AU;

    .line 623
    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :goto_2
    if-eqz v3, :cond_9

    .line 629
    iget-object p2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    invoke-static {p2}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    .line 631
    :cond_9
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p2

    iput-object p2, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 632
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    .line 633
    throw p1

    :cond_a
    if-eqz v3, :cond_b

    .line 634
    iget-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    .line 636
    :cond_b
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/qs0;->a()Lcom/android/tools/r8/internal/vs0;

    move-result-object p1

    iput-object p1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    .line 637
    invoke-virtual {p0}, Lcom/android/tools/r8/internal/uy;->makeExtensionsImmutable()V

    return-void
.end method

.method synthetic constructor <init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc-IA;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;-><init>(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)V

    return-void
.end method

.method private constructor <init>(Lcom/android/tools/r8/internal/dy;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/tools/r8/internal/dy;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1}, Lcom/android/tools/r8/internal/uy;-><init>(Lcom/android/tools/r8/internal/dy;)V

    const/4 p1, -0x1

    .line 245
    iput-byte p1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->memoizedIsInitialized:B

    return-void
.end method

.method synthetic constructor <init>(Lcom/android/tools/r8/internal/dy;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc-IA;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;-><init>(Lcom/android/tools/r8/internal/dy;)V

    return-void
.end method

.method static synthetic access$1200()Z
    .locals 1

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/uy;->alwaysUseFieldBuilders:Z

    return v0
.end method

.method static synthetic access$1300()Z
    .locals 1

    .line 1
    sget-boolean v0, Lcom/android/tools/r8/internal/uy;->alwaysUseFieldBuilders:Z

    return v0
.end method

.method static synthetic access$1400(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;)Lcom/android/tools/r8/internal/vs0;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    return-object p0
.end method

.method static synthetic access$1500(Lcom/android/tools/r8/internal/Z7;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalArgumentException;
        }
    .end annotation

    .line 1
    invoke-static {p0}, Lcom/android/tools/r8/internal/O0;->checkByteStringIsUtf8(Lcom/android/tools/r8/internal/Z7;)V

    return-void
.end method

.method public static getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->DEFAULT_INSTANCE:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object v0
.end method

.method public static final getDescriptor()Lcom/android/tools/r8/internal/Ok;
    .locals 1

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_MethodDesc_descriptor()Lcom/android/tools/r8/internal/Ok;

    move-result-object v0

    return-object v0
.end method

.method public static newBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;
    .locals 1

    .line 1
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->DEFAULT_INSTANCE:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    invoke-virtual {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->toBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object v0

    return-object v0
.end method

.method public static newBuilder(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;
    .locals 1

    .line 2
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->DEFAULT_INSTANCE:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    invoke-virtual {v0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->toBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object v0

    invoke-virtual {v0, p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object p0

    return-object p0
.end method

.method public static parseDelimitedFrom(Ljava/io/InputStream;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    .line 2
    invoke-static {v0, p0}, Lcom/android/tools/r8/internal/uy;->parseDelimitedWithIOException(Lcom/android/tools/r8/internal/z30;Ljava/io/InputStream;)Lcom/android/tools/r8/internal/vU;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseDelimitedFrom(Ljava/io/InputStream;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 3
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    .line 4
    invoke-static {v0, p0, p1}, Lcom/android/tools/r8/internal/uy;->parseDelimitedWithIOException(Lcom/android/tools/r8/internal/z30;Ljava/io/InputStream;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vU;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom(Lcom/android/tools/r8/internal/Z7;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 3
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    invoke-interface {v0, p0}, Lcom/android/tools/r8/internal/z30;->parseFrom(Lcom/android/tools/r8/internal/Z7;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom(Lcom/android/tools/r8/internal/Z7;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 4
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    invoke-interface {v0, p0, p1}, Lcom/android/tools/r8/internal/z30;->parseFrom(Lcom/android/tools/r8/internal/Z7;Lcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom(Lcom/android/tools/r8/internal/ce;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 11
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    .line 12
    invoke-static {v0, p0}, Lcom/android/tools/r8/internal/uy;->parseWithIOException(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/ce;)Lcom/android/tools/r8/internal/vU;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom(Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 13
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    .line 14
    invoke-static {v0, p0, p1}, Lcom/android/tools/r8/internal/uy;->parseWithIOException(Lcom/android/tools/r8/internal/z30;Lcom/android/tools/r8/internal/ce;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vU;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom(Ljava/io/InputStream;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 7
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    .line 8
    invoke-static {v0, p0}, Lcom/android/tools/r8/internal/uy;->parseWithIOException(Lcom/android/tools/r8/internal/z30;Ljava/io/InputStream;)Lcom/android/tools/r8/internal/vU;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom(Ljava/io/InputStream;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 9
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    .line 10
    invoke-static {v0, p0, p1}, Lcom/android/tools/r8/internal/uy;->parseWithIOException(Lcom/android/tools/r8/internal/z30;Ljava/io/InputStream;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/internal/vU;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom(Ljava/nio/ByteBuffer;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    invoke-interface {v0, p0}, Lcom/android/tools/r8/internal/z30;->parseFrom(Ljava/nio/ByteBuffer;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom(Ljava/nio/ByteBuffer;Lcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 2
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    invoke-interface {v0, p0, p1}, Lcom/android/tools/r8/internal/z30;->parseFrom(Ljava/nio/ByteBuffer;Lcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom([B)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 5
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    invoke-interface {v0, p0}, Lcom/android/tools/r8/internal/z30;->parseFrom([B)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parseFrom([BLcom/android/tools/r8/internal/Lu;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/android/tools/r8/internal/lI;
        }
    .end annotation

    .line 6
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    invoke-interface {v0, p0, p1}, Lcom/android/tools/r8/internal/z30;->parseFrom([BLcom/android/tools/r8/internal/Lu;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object p0
.end method

.method public static parser()Lcom/android/tools/r8/internal/z30;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/z30;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    return-object v0
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    if-nez v1, :cond_1

    .line 2
    invoke-super {p0, p1}, Lcom/android/tools/r8/internal/J0;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 4
    :cond_1
    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    .line 6
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getName()Ljava/lang/String;

    move-result-object v1

    .line 7
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x0

    if-nez v1, :cond_2

    return v2

    .line 8
    :cond_2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->hasHolder()Z

    move-result v1

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->hasHolder()Z

    move-result v3

    if-eq v1, v3, :cond_3

    return v2

    .line 9
    :cond_3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->hasHolder()Z

    move-result v1

    if-eqz v1, :cond_4

    .line 10
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getHolder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v1

    .line 11
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getHolder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    return v2

    .line 13
    :cond_4
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->hasReturnType()Z

    move-result v1

    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->hasReturnType()Z

    move-result v3

    if-eq v1, v3, :cond_5

    return v2

    .line 14
    :cond_5
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->hasReturnType()Z

    move-result v1

    if-eqz v1, :cond_6

    .line 15
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getReturnType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v1

    .line 16
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getReturnType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_6

    return v2

    .line 18
    :cond_6
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getParameterTypesList()Ljava/util/List;

    move-result-object v1

    .line 19
    invoke-virtual {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getParameterTypesList()Ljava/util/List;

    move-result-object v3

    invoke-interface {v1, v3}, Ljava/util/List;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_7

    return v2

    .line 20
    :cond_7
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    iget-object p1, p1, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1, p1}, Lcom/android/tools/r8/internal/vs0;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_8

    return v2

    :cond_8
    return v0
.end method

.method public bridge synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/AU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    move-result-object v0

    return-object v0
.end method

.method public getDefaultInstanceForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;
    .locals 1

    .line 3
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->DEFAULT_INSTANCE:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    return-object v0
.end method

.method public getHolder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->holder_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method public getHolderOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDescOrBuilder;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getHolder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v0

    return-object v0
.end method

.method public getName()Ljava/lang/String;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    .line 2
    instance-of v1, v0, Ljava/lang/String;

    if-eqz v1, :cond_0

    .line 3
    check-cast v0, Ljava/lang/String;

    return-object v0

    .line 5
    :cond_0
    check-cast v0, Lcom/android/tools/r8/internal/Z7;

    .line 7
    invoke-virtual {v0}, Lcom/android/tools/r8/internal/Z7;->c()Ljava/lang/String;

    move-result-object v0

    .line 8
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    return-object v0
.end method

.method public getNameBytes()Lcom/android/tools/r8/internal/Z7;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    .line 2
    instance-of v1, v0, Ljava/lang/String;

    if-eqz v1, :cond_0

    .line 3
    check-cast v0, Ljava/lang/String;

    .line 4
    invoke-static {v0}, Lcom/android/tools/r8/internal/Z7;->a(Ljava/lang/String;)Lcom/android/tools/r8/internal/V7;

    move-result-object v0

    .line 6
    iput-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    return-object v0

    .line 9
    :cond_0
    check-cast v0, Lcom/android/tools/r8/internal/Z7;

    return-object v0
.end method

.method public getParameterTypes(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    return-object p1
.end method

.method public getParameterTypesCount()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method

.method public getParameterTypesList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    return-object v0
.end method

.method public getParameterTypesOrBuilder(I)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDescOrBuilder;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDescOrBuilder;

    return-object p1
.end method

.method public getParameterTypesOrBuilderList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "+",
            "Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDescOrBuilder;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    return-object v0
.end method

.method public getParserForType()Lcom/android/tools/r8/internal/z30;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/android/tools/r8/internal/z30;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->PARSER:Lcom/android/tools/r8/internal/z30;

    return-object v0
.end method

.method public getReturnType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->returnType_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-nez v0, :cond_0

    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->getDefaultInstance()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method public getReturnTypeOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDescOrBuilder;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getReturnType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v0

    return-object v0
.end method

.method public getSerializedSize()I
    .locals 4

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    invoke-static {v0}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_1

    .line 6
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    const/4 v2, 0x1

    invoke-static {v2, v0}, Lcom/android/tools/r8/internal/uy;->computeStringSize(ILjava/lang/Object;)I

    move-result v0

    goto :goto_0

    :cond_1
    move v0, v1

    .line 8
    :goto_0
    iget-object v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->holder_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v2, :cond_2

    const/4 v2, 0x2

    .line 10
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getHolder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v3

    .line 11
    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v2

    invoke-static {v3}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/AU;)I

    move-result v3

    add-int/2addr v3, v2

    add-int/2addr v0, v3

    .line 12
    :cond_2
    iget-object v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->returnType_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v2, :cond_3

    const/4 v2, 0x3

    .line 14
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getReturnType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v3

    .line 15
    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v2

    invoke-static {v3}, Lcom/android/tools/r8/internal/je;->a(Lcom/android/tools/r8/internal/AU;)I

    move-result v3

    add-int/2addr v3, v2

    add-int/2addr v0, v3

    .line 16
    :cond_3
    :goto_1
    iget-object v2, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_4

    const/4 v2, 0x4

    .line 17
    iget-object v3, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    .line 18
    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/android/tools/r8/internal/AU;

    .line 19
    invoke-static {v2}, Lcom/android/tools/r8/internal/je;->b(I)I

    move-result v2

    .line 20
    invoke-static {v3, v2, v0}, Lcom/android/tools/r8/internal/Rd0;->a(Lcom/android/tools/r8/internal/AU;II)I

    move-result v0

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 5498
    :cond_4
    iget-object v1, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v1}, Lcom/android/tools/r8/internal/vs0;->getSerializedSize()I

    move-result v1

    add-int/2addr v1, v0

    .line 5499
    iput v1, p0, Lcom/android/tools/r8/internal/J0;->memoizedSize:I

    return v1
.end method

.method public final getUnknownFields()Lcom/android/tools/r8/internal/vs0;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    return-object v0
.end method

.method public hasHolder()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->holder_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public hasReturnType()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->returnType_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public hashCode()I
    .locals 4

    .line 1
    iget v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    if-eqz v0, :cond_0

    return v0

    .line 5
    :cond_0
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getDescriptor()Lcom/android/tools/r8/internal/Ok;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    add-int/lit16 v0, v0, 0x30b

    const/16 v1, 0x25

    mul-int/2addr v0, v1

    add-int/lit8 v0, v0, 0x1

    const/16 v2, 0x35

    mul-int/2addr v0, v2

    .line 7
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/String;->hashCode()I

    move-result v3

    add-int/2addr v3, v0

    .line 8
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->hasHolder()Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v0, 0x2

    .line 10
    invoke-static {v3, v1, v0, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getHolder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->hashCode()I

    move-result v3

    add-int/2addr v3, v0

    .line 12
    :cond_1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->hasReturnType()Z

    move-result v0

    if-eqz v0, :cond_2

    const/4 v0, 0x3

    .line 14
    invoke-static {v3, v1, v0, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getReturnType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v3

    invoke-virtual {v3}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;->hashCode()I

    move-result v3

    add-int/2addr v3, v0

    .line 16
    :cond_2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getParameterTypesCount()I

    move-result v0

    if-lez v0, :cond_3

    const/4 v0, 0x4

    .line 18
    invoke-static {v3, v1, v0, v2}, Lcom/android/tools/r8/internal/Nd0;->a(IIII)I

    move-result v0

    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getParameterTypesList()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->hashCode()I

    move-result v1

    add-int v3, v1, v0

    :cond_3
    mul-int/lit8 v3, v3, 0x1d

    .line 20
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0}, Lcom/android/tools/r8/internal/vs0;->hashCode()I

    move-result v0

    add-int/2addr v0, v3

    .line 21
    iput v0, p0, Lcom/android/tools/r8/internal/O0;->memoizedHashCode:I

    return v0
.end method

.method protected internalGetFieldAccessorTable()Lcom/android/tools/r8/internal/sy;
    .locals 3

    .line 1
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;->-$$Nest$sfgetinternal_static_com_android_tools_r8_keepanno_proto_MethodDesc_fieldAccessorTable()Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    .line 2
    const-class v1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    const-class v2, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    invoke-virtual {v0, v1, v2}, Lcom/android/tools/r8/internal/sy;->a(Ljava/lang/Class;Ljava/lang/Class;)Lcom/android/tools/r8/internal/sy;

    move-result-object v0

    return-object v0
.end method

.method public final isInitialized()Z
    .locals 2

    .line 1
    iget-byte v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->memoizedIsInitialized:B

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    return v1

    :cond_0
    if-nez v0, :cond_1

    const/4 v0, 0x0

    return v0

    .line 5
    :cond_1
    iput-byte v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->memoizedIsInitialized:B

    return v1
.end method

.method public bridge synthetic newBuilderForType()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->newBuilderForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object v0

    return-object v0
.end method

.method protected bridge synthetic newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/internal/uU;
    .locals 0

    .line 2
    invoke-virtual {p0, p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic newBuilderForType()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 3
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->newBuilderForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object v0

    return-object v0
.end method

.method public newBuilderForType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;
    .locals 1

    .line 4
    invoke-static {}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->newBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object v0

    return-object v0
.end method

.method protected newBuilderForType(Lcom/android/tools/r8/internal/ey;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;
    .locals 2

    .line 5
    new-instance v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    const/4 v1, 0x0

    invoke-direct {v0, p1, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;-><init>(Lcom/android/tools/r8/internal/ey;Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder-IA;)V

    return-object v0
.end method

.method protected newInstance(Lcom/android/tools/r8/internal/ty;)Ljava/lang/Object;
    .locals 0

    .line 1
    new-instance p1, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    invoke-direct {p1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;-><init>()V

    return-object p1
.end method

.method public bridge synthetic toBuilder()Lcom/android/tools/r8/internal/uU;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->toBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic toBuilder()Lcom/android/tools/r8/internal/zU;
    .locals 1

    .line 2
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->toBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object v0

    return-object v0
.end method

.method public toBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;
    .locals 2

    .line 3
    sget-object v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->DEFAULT_INSTANCE:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;

    const/4 v1, 0x0

    if-ne p0, v0, :cond_0

    .line 4
    new-instance v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;-><init>(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder-IA;)V

    goto :goto_0

    :cond_0
    new-instance v0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    invoke-direct {v0, v1}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;-><init>(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder-IA;)V

    invoke-virtual {v0, p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;->mergeFrom(Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;)Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc$Builder;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method public writeTo(Lcom/android/tools/r8/internal/je;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    invoke-static {v0}, Lcom/android/tools/r8/internal/uy;->isStringEmpty(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->name_:Ljava/lang/Object;

    const/4 v1, 0x1

    invoke-static {p1, v1, v0}, Lcom/android/tools/r8/internal/uy;->writeString(Lcom/android/tools/r8/internal/je;ILjava/lang/Object;)V

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->holder_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v0, :cond_1

    .line 5
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getHolder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v0

    const/4 v1, 0x2

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    .line 7
    :cond_1
    iget-object v0, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->returnType_:Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    if-eqz v0, :cond_2

    .line 8
    invoke-virtual {p0}, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->getReturnType()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$TypeDesc;

    move-result-object v0

    const/4 v1, 0x3

    invoke-virtual {p1, v1, v0}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    :cond_2
    const/4 v0, 0x0

    .line 10
    :goto_0
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_3

    .line 11
    iget-object v1, p0, Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$MethodDesc;->parameterTypes_:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/android/tools/r8/internal/AU;

    const/4 v2, 0x4

    invoke-virtual {p1, v2, v1}, Lcom/android/tools/r8/internal/je;->a(ILcom/android/tools/r8/internal/AU;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 13
    :cond_3
    iget-object v0, p0, Lcom/android/tools/r8/internal/uy;->unknownFields:Lcom/android/tools/r8/internal/vs0;

    invoke-virtual {v0, p1}, Lcom/android/tools/r8/internal/vs0;->writeTo(Lcom/android/tools/r8/internal/je;)V

    return-void
.end method
