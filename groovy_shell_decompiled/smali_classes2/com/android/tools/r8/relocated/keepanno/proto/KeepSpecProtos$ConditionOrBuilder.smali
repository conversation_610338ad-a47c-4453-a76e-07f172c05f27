.class public interface abstract Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$ConditionOrBuilder;
.super Ljava/lang/Object;
.source "R8_8.7.18_f8bee6d6fb926b7ebb3b15bf98f726f9d57471456ea20fce6d17d9a020197688"

# interfaces
.implements Lcom/android/tools/r8/internal/DU;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ConditionOrBuilder"
.end annotation


# virtual methods
.method public abstract synthetic findInitializationErrors()Ljava/util/List;
.end method

.method public abstract synthetic getAllFields()Ljava/util/Map;
.end method

.method public abstract synthetic getDefaultInstanceForType()Lcom/android/tools/r8/internal/vU;
.end method

.method public abstract synthetic getDescriptorForType()Lcom/android/tools/r8/internal/Ok;
.end method

.method public abstract synthetic getField(Lcom/android/tools/r8/internal/al;)Ljava/lang/Object;
.end method

.method public abstract synthetic getInitializationErrorString()Ljava/lang/String;
.end method

.method public abstract getItem()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReference;
.end method

.method public abstract getItemOrBuilder()Lcom/android/tools/r8/relocated/keepanno/proto/KeepSpecProtos$BindingReferenceOrBuilder;
.end method

.method public abstract synthetic getOneofFieldDescriptor(Lcom/android/tools/r8/internal/el;)Lcom/android/tools/r8/internal/al;
.end method

.method public abstract synthetic getRepeatedField(Lcom/android/tools/r8/internal/al;I)Ljava/lang/Object;
.end method

.method public abstract synthetic getRepeatedFieldCount(Lcom/android/tools/r8/internal/al;)I
.end method

.method public abstract synthetic getUnknownFields()Lcom/android/tools/r8/internal/vs0;
.end method

.method public abstract synthetic hasField(Lcom/android/tools/r8/internal/al;)Z
.end method

.method public abstract hasItem()Z
.end method

.method public abstract synthetic hasOneof(Lcom/android/tools/r8/internal/el;)Z
.end method
