.class public Lcom/example/groovy_shell/MainActivity;
.super Landroid/app/Activity;
.source "MainActivity.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 13
    invoke-direct {p0}, Landroid/app/Activity;-><init>()V

    return-void
.end method

.method private executeScript(Landroid/app/Activity;Ljava/lang/String;)V
    .locals 4

    .line 43
    new-instance v0, Lgroovy/lang/GrooidShell;

    const-class v1, Lcom/example/groovy_shell/MainActivity;

    invoke-virtual {v1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovy/lang/GrooidShell;-><init>(Ljava/lang/ClassLoader;)V

    if-eqz p1, :cond_0

    const-string v1, "activity"

    .line 45
    invoke-virtual {v0, v1, p1}, Lgroovy/lang/GrooidShell;->setVariable(Ljava/lang/String;Ljava/lang/Object;)V

    .line 48
    :cond_0
    :try_start_0
    invoke-virtual {v0, p2}, Lgroovy/lang/GrooidShell;->evaluate(Ljava/lang/String;)Lgroovy/lang/GrooidShell$Result;

    move-result-object p1

    .line 49
    invoke-virtual {p1}, Lgroovy/lang/GrooidShell$Result;->isSuccessful()Z

    move-result p2

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-eqz p2, :cond_2

    .line 50
    sget-object p2, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v2, "\u8fd4\u56de\u7ed3\u679c:[%s]%n"

    new-array v1, v1, [Ljava/lang/Object;

    invoke-virtual {p1}, Lgroovy/lang/GrooidShell$Result;->getResult()Ljava/lang/Object;

    move-result-object v3

    if-nez v3, :cond_1

    const-string p1, "\u65e0\u8fd4\u56de\u7ed3\u679c"

    goto :goto_0

    :cond_1
    invoke-virtual {p1}, Lgroovy/lang/GrooidShell$Result;->getResult()Ljava/lang/Object;

    move-result-object p1

    :goto_0
    aput-object p1, v1, v0

    invoke-virtual {p2, v2, v1}, Ljava/io/PrintStream;->printf(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/PrintStream;

    goto :goto_1

    .line 52
    :cond_2
    sget-object p2, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v2, "\u9519\u8bef\u4fe1\u606f:%s%n"

    new-array v1, v1, [Ljava/lang/Object;

    invoke-virtual {p1}, Lgroovy/lang/GrooidShell$Result;->getFailure()Ljava/lang/Throwable;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    aput-object p1, v1, v0

    invoke-virtual {p2, v2, v1}, Ljava/io/PrintStream;->printf(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/PrintStream;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception p1

    .line 55
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_1
    return-void
.end method


# virtual methods
.method protected onCreate(Landroid/os/Bundle;)V
    .locals 0

    .line 16
    invoke-super {p0, p1}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    const-string p1, "println \'Hello World\'"

    .line 17
    invoke-direct {p0, p0, p1}, Lcom/example/groovy_shell/MainActivity;->executeScript(Landroid/app/Activity;Ljava/lang/String;)V

    const-string p1, "import android.app.AlertDialog\nimport android.content.Context\n\n// \u521b\u5efa AlertDialog.Builder \u5bf9\u8c61\ndef builder = new AlertDialog.Builder(activity)\n\n// \u8bbe\u7f6e\u5bf9\u8bdd\u6846\u5185\u5bb9\nbuilder.setTitle(\"\u63d0\u793a\")\n       .setMessage(\"\u8fd9\u662f\u4e00\u4e2a\u7531 Groovy \u8c03\u7528\u7684 AlertDialog \u793a\u4f8b\uff01\")\n       .setPositiveButton(\"\u786e\u5b9a\") { dialog, which ->\n           // \u70b9\u51fb\u201c\u786e\u5b9a\u201d\u6309\u94ae\u540e\u7684\u64cd\u4f5c\n           println(\"\u7528\u6237\u70b9\u51fb\u4e86\u786e\u5b9a\")\n       }\n       .setNegativeButton(\"\u53d6\u6d88\") { dialog, which ->\n           // \u70b9\u51fb\u201c\u53d6\u6d88\u201d\u6309\u94ae\u540e\u7684\u64cd\u4f5c\n           println(\"\u7528\u6237\u70b9\u51fb\u4e86\u53d6\u6d88\")\n       }\n\n// \u521b\u5efa\u5e76\u663e\u793a\u5bf9\u8bdd\u6846\ndef dialog = builder.create()\ndialog.show()\n"

    .line 18
    invoke-direct {p0, p0, p1}, Lcom/example/groovy_shell/MainActivity;->executeScript(Landroid/app/Activity;Ljava/lang/String;)V

    return-void
.end method
