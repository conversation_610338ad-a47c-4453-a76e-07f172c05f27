.class public Lgroovyjarjaropenbeans/PropertyChangeSupport;
.super Ljava/lang/Object;
.source "PropertyChangeSupport.java"

# interfaces
.implements Ljava/io/Serializable;


# static fields
.field private static final serialVersionUID:J = 0x58d5d264574860bbL


# instance fields
.field private children:Ljava/util/Hashtable;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Hashtable<",
            "Ljava/lang/String;",
            "Lgroovyjarjaropenbeans/PropertyChangeSupport;",
            ">;"
        }
    .end annotation
.end field

.field private transient globalListeners:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjaropenbeans/PropertyChangeListener;",
            ">;"
        }
    .end annotation
.end field

.field private propertyChangeSupportSerializedDataVersion:I

.field private source:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 1

    .line 48
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 38
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->globalListeners:Ljava/util/List;

    .line 40
    new-instance v0, Ljava/util/Hashtable;

    invoke-direct {v0}, Ljava/util/Hashtable;-><init>()V

    iput-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    const/4 v0, 0x1

    .line 44
    iput v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->propertyChangeSupportSerializedDataVersion:I

    .line 50
    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    .line 52
    iput-object p1, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->source:Ljava/lang/Object;

    return-void
.end method

.method private createPropertyChangeEvent(Ljava/lang/String;II)Lgroovyjarjaropenbeans/PropertyChangeEvent;
    .locals 2

    .line 253
    new-instance v0, Lgroovyjarjaropenbeans/PropertyChangeEvent;

    iget-object v1, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->source:Ljava/lang/Object;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-direct {v0, v1, p1, p2, p3}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v0
.end method

.method private createPropertyChangeEvent(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjaropenbeans/PropertyChangeEvent;
    .locals 2

    .line 241
    new-instance v0, Lgroovyjarjaropenbeans/PropertyChangeEvent;

    iget-object v1, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->source:Ljava/lang/Object;

    invoke-direct {v0, v1, p1, p2, p3}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v0
.end method

.method private createPropertyChangeEvent(Ljava/lang/String;ZZ)Lgroovyjarjaropenbeans/PropertyChangeEvent;
    .locals 2

    .line 247
    new-instance v0, Lgroovyjarjaropenbeans/PropertyChangeEvent;

    iget-object v1, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->source:Ljava/lang/Object;

    invoke-static {p2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p2

    invoke-static {p3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p3

    invoke-direct {v0, v1, p1, p2, p3}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v0
.end method

.method private doFirePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V
    .locals 3

    .line 257
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;->getOldValue()Ljava/lang/Object;

    move-result-object v0

    .line 258
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;->getNewValue()Ljava/lang/Object;

    move-result-object v1

    if-eqz v0, :cond_0

    if-eqz v1, :cond_0

    .line 259
    invoke-virtual {v0, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 265
    :cond_0
    monitor-enter p0

    .line 266
    :try_start_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->globalListeners:Ljava/util/List;

    const/4 v1, 0x0

    new-array v2, v1, [Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-interface {v0, v2}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 267
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 270
    :goto_0
    array-length v2, v0

    if-ge v1, v2, :cond_1

    .line 271
    aget-object v2, v0, v1

    invoke-interface {v2, p1}, Lgroovyjarjaropenbeans/PropertyChangeListener;->propertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 275
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;->getPropertyName()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 276
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    .line 277
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;->getPropertyName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    if-eqz v0, :cond_2

    .line 279
    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->firePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    :cond_2
    return-void

    :catchall_0
    move-exception p1

    .line 267
    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method private readObject(Ljava/io/ObjectInputStream;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    .line 220
    invoke-virtual {p1}, Ljava/io/ObjectInputStream;->defaultReadObject()V

    .line 221
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->globalListeners:Ljava/util/List;

    .line 222
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    if-nez v0, :cond_0

    .line 223
    new-instance v0, Ljava/util/Hashtable;

    invoke-direct {v0}, Ljava/util/Hashtable;-><init>()V

    iput-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    .line 228
    :cond_0
    invoke-virtual {p1}, Ljava/io/ObjectInputStream;->readObject()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 230
    move-object v1, v0

    check-cast v1, Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-virtual {p0, v1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->addPropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    :cond_1
    if-nez v0, :cond_0

    return-void
.end method

.method private writeObject(Ljava/io/ObjectOutputStream;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 205
    invoke-virtual {p1}, Ljava/io/ObjectOutputStream;->defaultWriteObject()V

    .line 206
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->globalListeners:Ljava/util/List;

    const/4 v1, 0x0

    new-array v2, v1, [Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 207
    invoke-interface {v0, v2}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 208
    :goto_0
    array-length v2, v0

    if-ge v1, v2, :cond_1

    .line 209
    aget-object v2, v0, v1

    instance-of v2, v2, Ljava/io/Serializable;

    if-eqz v2, :cond_0

    .line 210
    aget-object v2, v0, v1

    invoke-virtual {p1, v2}, Ljava/io/ObjectOutputStream;->writeObject(Ljava/lang/Object;)V

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    .line 214
    invoke-virtual {p1, v0}, Ljava/io/ObjectOutputStream;->writeObject(Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public declared-synchronized addPropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    monitor-enter p0

    .line 177
    :try_start_0
    instance-of v0, p1, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    if-eqz v0, :cond_0

    .line 178
    move-object v0, p1

    check-cast v0, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    .line 179
    invoke-virtual {v0}, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;->getPropertyName()Ljava/lang/String;

    move-result-object v0

    .line 180
    check-cast p1, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    .line 181
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;->getListener()Ljava/util/EventListener;

    move-result-object p1

    check-cast p1, Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 182
    invoke-virtual {p0, v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    goto :goto_0

    :cond_0
    if-eqz p1, :cond_1

    .line 184
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->globalListeners:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 186
    :cond_1
    :goto_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 2

    monitor-enter p0

    if-eqz p2, :cond_2

    if-eqz p1, :cond_2

    .line 84
    :try_start_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    invoke-virtual {v0, p1}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    if-nez v0, :cond_0

    .line 87
    new-instance v0, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    iget-object v1, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->source:Ljava/lang/Object;

    invoke-direct {v0, v1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;-><init>(Ljava/lang/Object;)V

    .line 88
    iget-object v1, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    invoke-virtual {v1, p1, v0}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 92
    :cond_0
    instance-of p1, p2, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    if-eqz p1, :cond_1

    .line 93
    check-cast p2, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    .line 95
    new-instance p1, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    .line 97
    invoke-virtual {p2}, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;->getPropertyName()Ljava/lang/String;

    move-result-object v1

    .line 98
    invoke-virtual {p2}, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;->getListener()Ljava/util/EventListener;

    move-result-object p2

    check-cast p2, Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-direct {p1, v1, p2}, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;-><init>(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    .line 96
    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->addPropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    goto :goto_0

    .line 100
    :cond_1
    invoke-virtual {v0, p2}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->addPropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1

    .line 103
    :cond_2
    :goto_0
    monitor-exit p0

    return-void
.end method

.method public fireIndexedPropertyChange(Ljava/lang/String;III)V
    .locals 0

    if-eq p3, p4, :cond_0

    .line 145
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-static {p4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p4

    .line 144
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->fireIndexedPropertyChange(Ljava/lang/String;ILjava/lang/Object;Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public fireIndexedPropertyChange(Ljava/lang/String;ILjava/lang/Object;Ljava/lang/Object;)V
    .locals 7

    .line 66
    new-instance v6, Lgroovyjarjaropenbeans/IndexedPropertyChangeEvent;

    iget-object v1, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->source:Ljava/lang/Object;

    move-object v0, v6

    move-object v2, p1

    move-object v3, p3

    move-object v4, p4

    move v5, p2

    invoke-direct/range {v0 .. v5}, Lgroovyjarjaropenbeans/IndexedPropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;I)V

    invoke-direct {p0, v6}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->doFirePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void
.end method

.method public fireIndexedPropertyChange(Ljava/lang/String;IZZ)V
    .locals 0

    if-eq p3, p4, :cond_0

    .line 129
    invoke-static {p3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p3

    invoke-static {p4}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p4

    .line 128
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->fireIndexedPropertyChange(Ljava/lang/String;ILjava/lang/Object;Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public firePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V
    .locals 0

    .line 236
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->doFirePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void
.end method

.method public firePropertyChange(Ljava/lang/String;II)V
    .locals 0

    .line 135
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->createPropertyChangeEvent(Ljava/lang/String;II)Lgroovyjarjaropenbeans/PropertyChangeEvent;

    move-result-object p1

    .line 137
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->doFirePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void
.end method

.method public firePropertyChange(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0

    .line 57
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->createPropertyChangeEvent(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjaropenbeans/PropertyChangeEvent;

    move-result-object p1

    .line 59
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->doFirePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void
.end method

.method public firePropertyChange(Ljava/lang/String;ZZ)V
    .locals 0

    .line 119
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->createPropertyChangeEvent(Ljava/lang/String;ZZ)Lgroovyjarjaropenbeans/PropertyChangeEvent;

    move-result-object p1

    .line 121
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->doFirePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void
.end method

.method public declared-synchronized getPropertyChangeListeners()[Lgroovyjarjaropenbeans/PropertyChangeListener;
    .locals 7

    monitor-enter p0

    .line 189
    :try_start_0
    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->globalListeners:Ljava/util/List;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 191
    iget-object v1, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    invoke-virtual {v1}, Ljava/util/Hashtable;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    const/4 v3, 0x0

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    .line 192
    iget-object v4, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    .line 193
    invoke-virtual {v4, v2}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    .line 195
    invoke-virtual {v4}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->getPropertyChangeListeners()[Lgroovyjarjaropenbeans/PropertyChangeListener;

    move-result-object v4

    .line 196
    :goto_0
    array-length v5, v4

    if-ge v3, v5, :cond_0

    .line 197
    new-instance v5, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    aget-object v6, v4, v3

    invoke-direct {v5, v2, v6}, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;-><init>(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    invoke-virtual {v0, v5}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    new-array v1, v3, [Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 201
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjaropenbeans/PropertyChangeListener;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized getPropertyChangeListeners(Ljava/lang/String;)[Lgroovyjarjaropenbeans/PropertyChangeListener;
    .locals 1

    monitor-enter p0

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    .line 110
    :try_start_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    invoke-virtual {v0, p1}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    move-object v0, p1

    check-cast v0, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_2

    :cond_0
    :goto_0
    if-nez v0, :cond_1

    const/4 p1, 0x0

    new-array p1, p1, [Lgroovyjarjaropenbeans/PropertyChangeListener;

    goto :goto_1

    .line 114
    :cond_1
    invoke-virtual {v0}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->getPropertyChangeListeners()[Lgroovyjarjaropenbeans/PropertyChangeListener;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 113
    :goto_1
    monitor-exit p0

    return-object p1

    :goto_2
    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized hasListeners(Ljava/lang/String;)Z
    .locals 3

    monitor-enter p0

    .line 150
    :try_start_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->globalListeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v1, 0x1

    if-lez v0, :cond_0

    .line 151
    monitor-exit p0

    return v1

    :cond_0
    const/4 v0, 0x0

    if-eqz p1, :cond_2

    .line 155
    :try_start_1
    iget-object v2, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    invoke-virtual {v2, p1}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    if-eqz v2, :cond_1

    .line 156
    invoke-virtual {v2, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->hasListeners(Ljava/lang/String;)Z

    move-result p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-eqz p1, :cond_1

    goto :goto_0

    :cond_1
    move v1, v0

    :goto_0
    move v0, v1

    .line 158
    :cond_2
    monitor-exit p0

    return v0

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized removePropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    monitor-enter p0

    .line 163
    :try_start_0
    instance-of v0, p1, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    if-eqz v0, :cond_0

    .line 164
    move-object v0, p1

    check-cast v0, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    .line 165
    invoke-virtual {v0}, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;->getPropertyName()Ljava/lang/String;

    move-result-object v0

    .line 166
    check-cast p1, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;

    .line 167
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeListenerProxy;->getListener()Ljava/util/EventListener;

    move-result-object p1

    check-cast p1, Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 169
    invoke-virtual {p0, v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    goto :goto_0

    .line 171
    :cond_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->globalListeners:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 173
    :goto_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    monitor-enter p0

    if-eqz p1, :cond_0

    if-eqz p2, :cond_0

    .line 73
    :try_start_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyChangeSupport;->children:Ljava/util/Hashtable;

    invoke-virtual {v0, p1}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    if-eqz p1, :cond_0

    .line 76
    invoke-virtual {p1, p2}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->removePropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1

    .line 79
    :cond_0
    :goto_0
    monitor-exit p0

    return-void
.end method
