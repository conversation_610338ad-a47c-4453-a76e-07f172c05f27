.class Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;
.super Lgroovyjarjaropenbeans/PersistenceDelegate;
.source "UtilCollectionsPersistenceDelegate.java"


# static fields
.field static CLASS_PREFIX:Ljava/lang/String; = "java.util.Collections$"

.field private static final COLLECTIONS_TYPE:Ljava/lang/String; = "type"

.field private static final MAP_KEY_TYPE:Ljava/lang/String; = "keyType"

.field private static final MAP_VALUE_TYPE:Ljava/lang/String; = "valueType"


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method constructor <init>()V
    .locals 0

    .line 40
    invoke-direct {p0}, Lgroovyjarjaropenbeans/PersistenceDelegate;-><init>()V

    return-void
.end method

.method private static valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 8

    .line 188
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const/4 v1, 0x0

    move-object v2, v1

    :goto_0
    if-eqz v0, :cond_2

    .line 191
    invoke-virtual {v0}, Ljava/lang/Class;->getDeclaredFields()[Ljava/lang/reflect/Field;

    move-result-object v3

    array-length v4, v3

    const/4 v5, 0x0

    :goto_1
    if-ge v5, v4, :cond_1

    aget-object v6, v3, v5

    .line 192
    invoke-virtual {v6}, Ljava/lang/reflect/Field;->getName()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {p1, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_0

    move-object v2, v6

    goto :goto_2

    :cond_0
    add-int/lit8 v5, v5, 0x1

    goto :goto_1

    .line 197
    :cond_1
    :goto_2
    invoke-virtual {v0}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object v0

    goto :goto_0

    :cond_2
    if-eqz v2, :cond_4

    .line 200
    invoke-virtual {v2}, Ljava/lang/reflect/Field;->isAccessible()Z

    move-result p1

    if-nez p1, :cond_3

    const/4 p1, 0x1

    .line 201
    invoke-virtual {v2, p1}, Ljava/lang/reflect/Field;->setAccessible(Z)V

    .line 204
    :cond_3
    :try_start_0
    invoke-virtual {v2, p0}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    :cond_4
    return-object v1
.end method


# virtual methods
.method protected instantiate(Ljava/lang/Object;Lgroovyjarjaropenbeans/Encoder;)Lgroovyjarjaropenbeans/Expression;
    .locals 9

    .line 71
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p2

    const-string v0, "UnmodifiableCollection"

    .line 72
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_0

    .line 74
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/ArrayList;

    move-object v4, p1

    check-cast v4, Ljava/util/Collection;

    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    aput-object v3, v2, v1

    const-string v1, "unmodifiableCollection"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_0
    const-string v0, "UnmodifiableList"

    .line 77
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    const-string v3, "unmodifiableList"

    if-eqz v0, :cond_1

    .line 79
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v4, Ljava/util/LinkedList;

    move-object v5, p1

    check-cast v5, Ljava/util/Collection;

    invoke-direct {v4, v5}, Ljava/util/LinkedList;-><init>(Ljava/util/Collection;)V

    aput-object v4, v2, v1

    invoke-direct {p2, p1, v0, v3, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_1
    const-string v0, "UnmodifiableRandomAccessList"

    .line 82
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 84
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v4, Ljava/util/ArrayList;

    move-object v5, p1

    check-cast v5, Ljava/util/Collection;

    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    aput-object v4, v2, v1

    invoke-direct {p2, p1, v0, v3, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_2
    const-string v0, "UnmodifiableSet"

    .line 87
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 89
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/HashSet;

    move-object v4, p1

    check-cast v4, Ljava/util/Set;

    invoke-direct {v3, v4}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    aput-object v3, v2, v1

    const-string v1, "unmodifiableSet"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_3
    const-string v0, "UnmodifiableSortedSet"

    .line 92
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 94
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/TreeSet;

    move-object v4, p1

    check-cast v4, Ljava/util/SortedSet;

    invoke-direct {v3, v4}, Ljava/util/TreeSet;-><init>(Ljava/util/SortedSet;)V

    aput-object v3, v2, v1

    const-string v1, "unmodifiableSortedSet"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_4
    const-string v0, "UnmodifiableMap"

    .line 97
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 98
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/HashMap;

    move-object v4, p1

    check-cast v4, Ljava/util/Map;

    invoke-direct {v3, v4}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    aput-object v3, v2, v1

    const-string v1, "unmodifiableMap"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_5
    const-string v0, "UnmodifiableSortedMap"

    .line 101
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 103
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/TreeMap;

    move-object v4, p1

    check-cast v4, Ljava/util/Map;

    invoke-direct {v3, v4}, Ljava/util/TreeMap;-><init>(Ljava/util/Map;)V

    aput-object v3, v2, v1

    const-string v1, "unmodifiableSortedMap"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_6
    const-string v0, "SynchronizedCollection"

    .line 106
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_7

    .line 108
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/ArrayList;

    move-object v4, p1

    check-cast v4, Ljava/util/Collection;

    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    aput-object v3, v2, v1

    const-string v1, "synchronizedCollection"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_7
    const-string v0, "SynchronizedList"

    .line 111
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    const-string v3, "synchronizedList"

    if-eqz v0, :cond_8

    .line 113
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v4, Ljava/util/LinkedList;

    move-object v5, p1

    check-cast v5, Ljava/util/List;

    invoke-direct {v4, v5}, Ljava/util/LinkedList;-><init>(Ljava/util/Collection;)V

    aput-object v4, v2, v1

    invoke-direct {p2, p1, v0, v3, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_8
    const-string v0, "SynchronizedRandomAccessList"

    .line 116
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_9

    .line 118
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v4, Ljava/util/ArrayList;

    move-object v5, p1

    check-cast v5, Ljava/util/List;

    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    aput-object v4, v2, v1

    invoke-direct {p2, p1, v0, v3, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_9
    const-string v0, "SynchronizedSet"

    .line 121
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_a

    .line 123
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/HashSet;

    move-object v4, p1

    check-cast v4, Ljava/util/Set;

    invoke-direct {v3, v4}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    aput-object v3, v2, v1

    const-string v1, "synchronizedSet"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_a
    const-string v0, "SynchronizedSortedSet"

    .line 126
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_b

    .line 128
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/TreeSet;

    move-object v4, p1

    check-cast v4, Ljava/util/SortedSet;

    invoke-direct {v3, v4}, Ljava/util/TreeSet;-><init>(Ljava/util/SortedSet;)V

    aput-object v3, v2, v1

    const-string v1, "synchronizedSortedSet"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_b
    const-string v0, "SynchronizedMap"

    .line 131
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_c

    .line 133
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/HashMap;

    move-object v4, p1

    check-cast v4, Ljava/util/Map;

    invoke-direct {v3, v4}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    aput-object v3, v2, v1

    const-string v1, "synchronizedMap"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_c
    const-string v0, "SynchronizedSortedMap"

    .line 136
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_d

    .line 138
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v2, v2, [Ljava/lang/Object;

    new-instance v3, Ljava/util/TreeMap;

    move-object v4, p1

    check-cast v4, Ljava/util/SortedMap;

    invoke-direct {v3, v4}, Ljava/util/TreeMap;-><init>(Ljava/util/SortedMap;)V

    aput-object v3, v2, v1

    const-string v1, "synchronizedSortedMap"

    invoke-direct {p2, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_d
    const-string v0, "CheckedCollection"

    .line 141
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    const-string v3, "type"

    const/4 v4, 0x2

    if-eqz v0, :cond_e

    .line 143
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v4, v4, [Ljava/lang/Object;

    new-instance v5, Ljava/util/ArrayList;

    move-object v6, p1

    check-cast v6, Ljava/util/Collection;

    invoke-direct {v5, v6}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    aput-object v5, v4, v1

    .line 146
    invoke-static {p1, v3}, Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;->valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v4, v2

    const-string v1, "checkedCollection"

    invoke-direct {p2, p1, v0, v1, v4}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_e
    const-string v0, "CheckedList"

    .line 147
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    const-string v5, "checkedList"

    if-eqz v0, :cond_f

    .line 149
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v4, v4, [Ljava/lang/Object;

    new-instance v6, Ljava/util/LinkedList;

    move-object v7, p1

    check-cast v7, Ljava/util/Collection;

    invoke-direct {v6, v7}, Ljava/util/LinkedList;-><init>(Ljava/util/Collection;)V

    aput-object v6, v4, v1

    .line 152
    invoke-static {p1, v3}, Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;->valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v4, v2

    invoke-direct {p2, p1, v0, v5, v4}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_f
    const-string v0, "CheckedRandomAccessList"

    .line 153
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_10

    .line 155
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v4, v4, [Ljava/lang/Object;

    new-instance v6, Ljava/util/ArrayList;

    move-object v7, p1

    check-cast v7, Ljava/util/Collection;

    invoke-direct {v6, v7}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    aput-object v6, v4, v1

    .line 158
    invoke-static {p1, v3}, Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;->valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v4, v2

    invoke-direct {p2, p1, v0, v5, v4}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_10
    const-string v0, "CheckedSet"

    .line 159
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_11

    .line 161
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v4, v4, [Ljava/lang/Object;

    new-instance v5, Ljava/util/HashSet;

    move-object v6, p1

    check-cast v6, Ljava/util/Set;

    invoke-direct {v5, v6}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    aput-object v5, v4, v1

    .line 163
    invoke-static {p1, v3}, Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;->valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v4, v2

    const-string v1, "checkedSet"

    invoke-direct {p2, p1, v0, v1, v4}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_11
    const-string v0, "CheckedSortedSet"

    .line 164
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_12

    .line 166
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v4, v4, [Ljava/lang/Object;

    new-instance v5, Ljava/util/TreeSet;

    move-object v6, p1

    check-cast v6, Ljava/util/Set;

    invoke-direct {v5, v6}, Ljava/util/TreeSet;-><init>(Ljava/util/Collection;)V

    aput-object v5, v4, v1

    .line 169
    invoke-static {p1, v3}, Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;->valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v4, v2

    const-string v1, "checkedSortedSet"

    invoke-direct {p2, p1, v0, v1, v4}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_12
    const-string v0, "CheckedMap"

    .line 170
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    const-string v3, "valueType"

    const-string v5, "keyType"

    const/4 v6, 0x3

    if-eqz v0, :cond_13

    .line 172
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v6, v6, [Ljava/lang/Object;

    new-instance v7, Ljava/util/HashMap;

    move-object v8, p1

    check-cast v8, Ljava/util/Map;

    invoke-direct {v7, v8}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    aput-object v7, v6, v1

    .line 174
    invoke-static {p1, v5}, Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;->valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v6, v2

    .line 175
    invoke-static {p1, v3}, Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;->valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v6, v4

    const-string v1, "checkedMap"

    invoke-direct {p2, p1, v0, v1, v6}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_13
    const-string v0, "CheckedSortedMap"

    .line 176
    invoke-virtual {p2, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result p2

    if-eqz p2, :cond_14

    .line 178
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    const-class v0, Ljava/util/Collections;

    new-array v6, v6, [Ljava/lang/Object;

    new-instance v7, Ljava/util/TreeMap;

    move-object v8, p1

    check-cast v8, Ljava/util/Map;

    invoke-direct {v7, v8}, Ljava/util/TreeMap;-><init>(Ljava/util/Map;)V

    aput-object v7, v6, v1

    .line 181
    invoke-static {p1, v5}, Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;->valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v6, v2

    .line 182
    invoke-static {p1, v3}, Lgroovyjarjaropenbeans/UtilCollectionsPersistenceDelegate;->valueOfField(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v6, v4

    const-string v1, "checkedSortedMap"

    invoke-direct {p2, p1, v0, v1, v6}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2

    :cond_14
    const/4 p1, 0x0

    return-object p1
.end method

.method protected mutatesTo(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 4

    .line 51
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjarharmonybeans/BeansUtils;->declaredEquals(Ljava/lang/Class;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 52
    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1

    .line 54
    :cond_0
    instance-of v0, p1, Ljava/util/Collection;

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_2

    .line 55
    check-cast p1, Ljava/util/Collection;

    .line 56
    check-cast p2, Ljava/util/Collection;

    .line 57
    invoke-interface {p1}, Ljava/util/Collection;->size()I

    move-result v0

    invoke-interface {p2}, Ljava/util/Collection;->size()I

    move-result v3

    if-ne v0, v3, :cond_1

    invoke-interface {p1, p2}, Ljava/util/Collection;->containsAll(Ljava/util/Collection;)Z

    move-result p1

    if-eqz p1, :cond_1

    goto :goto_0

    :cond_1
    move v1, v2

    :goto_0
    return v1

    .line 59
    :cond_2
    instance-of v0, p1, Ljava/util/Map;

    if-eqz v0, :cond_4

    .line 60
    check-cast p1, Ljava/util/Map;

    .line 61
    check-cast p2, Ljava/util/Map;

    .line 62
    invoke-interface {p1}, Ljava/util/Map;->size()I

    move-result v0

    invoke-interface {p2}, Ljava/util/Map;->size()I

    move-result v3

    if-ne v0, v3, :cond_3

    .line 63
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/Set;->containsAll(Ljava/util/Collection;)Z

    move-result p1

    if-eqz p1, :cond_3

    goto :goto_1

    :cond_3
    move v1, v2

    :goto_1
    return v1

    .line 65
    :cond_4
    invoke-super {p0, p1, p2}, Lgroovyjarjaropenbeans/PersistenceDelegate;->mutatesTo(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
