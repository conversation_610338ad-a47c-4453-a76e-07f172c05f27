.class public Lgroovyjarjaropenbeans/XMLEncoder;
.super Lgroovyjarjaropenbeans/Encoder;
.source "XMLEncoder.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjaropenbeans/XMLEncoder$Record;
    }
.end annotation


# static fields
.field private static DEADLOCK_THRESHOLD:I = 0x7

.field private static final DEFAULT_ENCODING:Ljava/lang/String; = "UTF-8"

.field private static final INDENT_UNIT:I = 0x1

.field private static final isStaticConstantsSupported:Z = true


# instance fields
.field private clazzCounterMap:Ljava/util/IdentityHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/IdentityHashMap<",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private flushPending:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private flushPendingStat:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private flushPrePending:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private hasXmlHeader:Z

.field private needOwner:Z

.field private objPrePendingCache:Ljava/util/IdentityHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/IdentityHashMap<",
            "Ljava/lang/Object;",
            "Ljava/util/ArrayList<",
            "Ljava/lang/Object;",
            ">;>;"
        }
    .end annotation
.end field

.field private objRecordMap:Ljava/util/IdentityHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/IdentityHashMap<",
            "Ljava/lang/Object;",
            "Lgroovyjarjaropenbeans/XMLEncoder$Record;",
            ">;"
        }
    .end annotation
.end field

.field private out:Ljava/io/PrintWriter;

.field private owner:Ljava/lang/Object;

.field private writingObject:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Ljava/io/OutputStream;)V
    .locals 3

    .line 115
    invoke-direct {p0}, Lgroovyjarjaropenbeans/Encoder;-><init>()V

    .line 77
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPending:Ljava/util/ArrayList;

    .line 80
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPendingStat:Ljava/util/ArrayList;

    .line 83
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPrePending:Ljava/util/ArrayList;

    const/4 v0, 0x0

    .line 85
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->hasXmlHeader:Z

    .line 95
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->needOwner:Z

    const/4 v1, 0x0

    .line 99
    iput-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->owner:Ljava/lang/Object;

    .line 101
    new-instance v1, Ljava/util/IdentityHashMap;

    invoke-direct {v1}, Ljava/util/IdentityHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    .line 103
    new-instance v1, Ljava/util/IdentityHashMap;

    invoke-direct {v1}, Ljava/util/IdentityHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->clazzCounterMap:Ljava/util/IdentityHashMap;

    .line 105
    new-instance v1, Ljava/util/IdentityHashMap;

    invoke-direct {v1}, Ljava/util/IdentityHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objPrePendingCache:Ljava/util/IdentityHashMap;

    .line 107
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z

    if-eqz p1, :cond_0

    .line 118
    :try_start_0
    new-instance v0, Ljava/io/PrintWriter;

    new-instance v1, Ljava/io/OutputStreamWriter;

    const-string v2, "UTF-8"

    invoke-direct {v1, p1, v2}, Ljava/io/OutputStreamWriter;-><init>(Ljava/io/OutputStream;Ljava/lang/String;)V

    const/4 p1, 0x1

    invoke-direct {v0, v1, p1}, Ljava/io/PrintWriter;-><init>(Ljava/io/Writer;Z)V

    iput-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;
    :try_end_0
    .catch Ljava/io/UnsupportedEncodingException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_0
    return-void
.end method

.method private checkDeadLoop(Ljava/lang/Object;)Z
    .locals 5

    const/4 v0, 0x0

    move-object v1, p1

    move v2, v0

    :cond_0
    if-eqz v1, :cond_1

    .line 863
    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v3, v1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-eqz v1, :cond_1

    .line 865
    iget-object v3, v1, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    if-eqz v3, :cond_1

    .line 866
    iget-object v1, v1, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    invoke-virtual {v1}, Lgroovyjarjaropenbeans/Expression;->getTarget()Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 872
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 873
    invoke-virtual {v1, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    add-int/lit8 v2, v2, 0x1

    .line 876
    sget v3, Lgroovyjarjaropenbeans/XMLEncoder;->DEADLOCK_THRESHOLD:I

    if-lt v2, v3, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_1
    return v0
.end method

.method private decapitalize(Ljava/lang/String;)Ljava/lang/StringBuffer;
    .locals 2

    .line 137
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0, p1}, Ljava/lang/StringBuffer;-><init>(Ljava/lang/String;)V

    const/4 p1, 0x0

    .line 138
    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->charAt(I)C

    move-result v1

    invoke-static {v1}, Ljava/lang/Character;->toLowerCase(C)C

    move-result v1

    invoke-virtual {v0, p1, v1}, Ljava/lang/StringBuffer;->setCharAt(IC)V

    return-object v0
.end method

.method private flushBasicObject(Ljava/lang/Object;I)V
    .locals 2

    .line 193
    instance-of v0, p1, Ljava/lang/reflect/Proxy;

    if-eqz v0, :cond_0

    return-void

    .line 196
    :cond_0
    invoke-direct {p0, p2}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    if-nez p1, :cond_1

    .line 198
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "<null /> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 199
    :cond_1
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_3

    .line 200
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-eqz v0, :cond_2

    add-int/lit8 p2, p2, -0x3

    .line 202
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPendingStat:Ljava/util/ArrayList;

    .line 203
    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->contains(Ljava/lang/Object;)Z

    move-result v1

    .line 202
    invoke-direct {p0, p1, v0, p2, v1}, Lgroovyjarjaropenbeans/XMLEncoder;->flushExpression(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;IZ)V

    return-void

    .line 206
    :cond_2
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v0, "<string>"

    invoke-virtual {p2, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 207
    check-cast p1, Ljava/lang/String;

    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->flushString(Ljava/lang/String;)V

    .line 208
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "</string> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 209
    :cond_3
    instance-of p2, p1, Ljava/lang/Class;

    if-eqz p2, :cond_4

    .line 210
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<class>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    check-cast p1, Ljava/lang/Class;

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "</class> "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 211
    :cond_4
    instance-of p2, p1, Ljava/lang/Boolean;

    if-eqz p2, :cond_5

    .line 212
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<boolean>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "</boolean> "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 213
    :cond_5
    instance-of p2, p1, Ljava/lang/Byte;

    if-eqz p2, :cond_6

    .line 214
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<byte>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "</byte> "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 215
    :cond_6
    instance-of p2, p1, Ljava/lang/Character;

    if-eqz p2, :cond_8

    .line 216
    check-cast p1, Ljava/lang/Character;

    invoke-virtual {p1}, Ljava/lang/Character;->charValue()C

    move-result p1

    .line 217
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->invalidCharacter(C)Z

    move-result p2

    if-eqz p2, :cond_7

    .line 218
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<char code=\"#"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0x10

    invoke-static {p1, v1}, Ljava/lang/Integer;->toString(II)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "\"/>"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 221
    :cond_7
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<char>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "</char> "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 223
    :cond_8
    instance-of p2, p1, Ljava/lang/Double;

    if-eqz p2, :cond_9

    .line 224
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<double>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "</double> "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 225
    :cond_9
    instance-of p2, p1, Ljava/lang/Float;

    if-eqz p2, :cond_a

    .line 226
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<float>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "</float> "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 227
    :cond_a
    instance-of p2, p1, Ljava/lang/Integer;

    if-eqz p2, :cond_b

    .line 228
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<int>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "</int> "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 229
    :cond_b
    instance-of p2, p1, Ljava/lang/Long;

    if-eqz p2, :cond_c

    .line 230
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<long>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "</long> "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 231
    :cond_c
    instance-of p2, p1, Ljava/lang/Short;

    if-eqz p2, :cond_d

    .line 232
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<short>"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "</short> "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 234
    :cond_d
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/XMLEncoder;->getExceptionListener()Lgroovyjarjaropenbeans/ExceptionListener;

    move-result-object p2

    new-instance v0, Ljava/lang/Exception;

    const-string v1, "beans.73"

    .line 235
    invoke-static {v1, p1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    .line 234
    invoke-interface {p2, v0}, Lgroovyjarjaropenbeans/ExceptionListener;->exceptionThrown(Ljava/lang/Exception;)V

    :goto_0
    return-void
.end method

.method private flushExpression(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;IZ)V
    .locals 3

    if-eqz p4, :cond_0

    .line 248
    new-instance p4, Lgroovyjarjaropenbeans/Statement;

    iget-object v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    invoke-virtual {v0}, Lgroovyjarjaropenbeans/Expression;->getTarget()Ljava/lang/Object;

    move-result-object v0

    iget-object v1, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    .line 249
    invoke-virtual {v1}, Lgroovyjarjaropenbeans/Expression;->getMethodName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/Expression;->getArguments()[Ljava/lang/Object;

    move-result-object v2

    invoke-direct {p4, v0, v1, v2}, Lgroovyjarjaropenbeans/Statement;-><init>(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    iget-object p4, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    .line 251
    :goto_0
    invoke-virtual {p4}, Lgroovyjarjaropenbeans/Statement;->getMethodName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "getField"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 252
    invoke-direct {p0, p4, p3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushStatField(Lgroovyjarjaropenbeans/Statement;I)V

    return-void

    .line 257
    :cond_1
    iget-object v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->id:Ljava/lang/String;

    if-eqz v0, :cond_2

    .line 258
    invoke-direct {p0, p3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 259
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p3, "<object idref=\""

    invoke-virtual {p1, p3}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 260
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    iget-object p2, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->id:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 261
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "\"/> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void

    .line 266
    :cond_2
    iget v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->refCount:I

    const/4 v1, 0x1

    if-le v0, v1, :cond_3

    iget-object v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->id:Ljava/lang/String;

    if-nez v0, :cond_3

    .line 267
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->idSerialNoOfObject(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->id:Ljava/lang/String;

    .line 271
    :cond_3
    iget-object p1, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->id:Ljava/lang/String;

    iget-object p2, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->stats:Ljava/util/ArrayList;

    invoke-direct {p0, p4, p1, p2, p3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushStatement(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V

    return-void
.end method

.method private flushIndent(I)V
    .locals 3

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p1, :cond_0

    .line 276
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const/16 v2, 0x20

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(C)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method private flushObject(Ljava/lang/Object;I)V
    .locals 2

    .line 281
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-nez v0, :cond_0

    .line 282
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->isBasicType(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    return-void

    .line 286
    :cond_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->owner:Ljava/lang/Object;

    if-ne p1, v1, :cond_1

    iget-boolean v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->needOwner:Z

    if-eqz v1, :cond_1

    .line 287
    invoke-direct {p0, p1, v0, p2}, Lgroovyjarjaropenbeans/XMLEncoder;->flushOwner(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;I)V

    const/4 p1, 0x0

    .line 288
    iput-boolean p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->needOwner:Z

    return-void

    .line 292
    :cond_1
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->isBasicType(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    .line 293
    invoke-direct {p0, p1, p2}, Lgroovyjarjaropenbeans/XMLEncoder;->flushBasicObject(Ljava/lang/Object;I)V

    goto :goto_0

    .line 295
    :cond_2
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPendingStat:Ljava/util/ArrayList;

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->contains(Ljava/lang/Object;)Z

    move-result v1

    invoke-direct {p0, p1, v0, p2, v1}, Lgroovyjarjaropenbeans/XMLEncoder;->flushExpression(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;IZ)V

    :goto_0
    return-void
.end method

.method private flushOwner(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;I)V
    .locals 4

    .line 301
    iget v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->refCount:I

    const/4 v1, 0x1

    if-le v0, v1, :cond_0

    iget-object v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->id:Ljava/lang/String;

    if-nez v0, :cond_0

    .line 302
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->idSerialNoOfObject(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->id:Ljava/lang/String;

    .line 305
    :cond_0
    invoke-direct {p0, p3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    const-string p1, "void"

    .line 307
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "<"

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 308
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v0, p1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 311
    iget-object v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->id:Ljava/lang/String;

    if-eqz v0, :cond_1

    .line 312
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, " id=\""

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 313
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    iget-object v1, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->id:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 314
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "\""

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 317
    :cond_1
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, " property=\"owner\""

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 320
    iget-object v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    invoke-virtual {v0}, Lgroovyjarjaropenbeans/Expression;->getArguments()[Ljava/lang/Object;

    move-result-object v0

    array-length v0, v0

    if-nez v0, :cond_2

    iget-object v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->stats:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 321
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "/> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void

    .line 324
    :cond_2
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "> "

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    const/4 v0, 0x0

    .line 327
    :goto_0
    iget-object v2, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/Expression;->getArguments()[Ljava/lang/Object;

    move-result-object v2

    array-length v2, v2

    if-ge v0, v2, :cond_3

    .line 328
    iget-object v2, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/Expression;->getArguments()[Ljava/lang/Object;

    move-result-object v2

    aget-object v2, v2, v0

    add-int/lit8 v3, p3, 0x1

    invoke-direct {p0, v2, v3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushObject(Ljava/lang/Object;I)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 332
    :cond_3
    iget-object p2, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->stats:Ljava/util/ArrayList;

    invoke-direct {p0, p2, p3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushSubStatements(Ljava/util/List;I)V

    .line 335
    invoke-direct {p0, p3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 336
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p3, "</"

    invoke-virtual {p2, p3}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 337
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 338
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1, v1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void
.end method

.method private flushStatArray(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjaropenbeans/Statement;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "*>;I)V"
        }
    .end annotation

    .line 345
    invoke-direct {p0, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 346
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "<array"

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    const-string v0, "\""

    if-eqz p2, :cond_0

    .line 350
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, " id=\""

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 351
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v1, p2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 352
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 356
    :cond_0
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, " class=\""

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 357
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v1

    const/4 v2, 0x0

    aget-object v1, v1, v2

    check-cast v1, Ljava/lang/Class;

    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 358
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "\" length=\""

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 359
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object p1

    const/4 v1, 0x1

    aget-object p1, p1, v1

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->print(Ljava/lang/Object;)V

    .line 360
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 363
    invoke-interface {p3}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 364
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "/> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void

    .line 367
    :cond_1
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    .line 370
    invoke-direct {p0, p3, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushSubStatements(Ljava/util/List;I)V

    .line 373
    invoke-direct {p0, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 374
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "</array> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void
.end method

.method private flushStatCommon(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjaropenbeans/Statement;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "*>;I)V"
        }
    .end annotation

    .line 381
    invoke-direct {p0, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 382
    instance-of v0, p1, Lgroovyjarjaropenbeans/Expression;

    if-eqz v0, :cond_0

    const-string v0, "object"

    goto :goto_0

    :cond_0
    const-string v0, "void"

    .line 383
    :goto_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, "<"

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 384
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v1, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    const-string v1, "\""

    if-eqz p2, :cond_1

    .line 388
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v3, " id=\""

    invoke-virtual {v2, v3}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 389
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v2, p2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 390
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 394
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object p2

    instance-of p2, p2, Ljava/lang/Class;

    if-eqz p2, :cond_2

    .line 395
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, " class=\""

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 396
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Class;

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 397
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 401
    :cond_2
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getMethodName()Ljava/lang/String;

    move-result-object p2

    const-string v2, "new"

    invoke-virtual {v2, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_3

    .line 402
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, " method=\""

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 403
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getMethodName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 404
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 408
    :cond_3
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object p2

    array-length p2, p2

    if-nez p2, :cond_4

    invoke-interface {p3}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-eqz p2, :cond_4

    .line 409
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "/> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void

    .line 412
    :cond_4
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "> "

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    const/4 p2, 0x0

    .line 415
    :goto_1
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v2

    array-length v2, v2

    if-ge p2, v2, :cond_5

    .line 416
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v2

    aget-object v2, v2, p2

    add-int/lit8 v3, p4, 0x1

    invoke-direct {p0, v2, v3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushObject(Ljava/lang/Object;I)V

    add-int/lit8 p2, p2, 0x1

    goto :goto_1

    .line 420
    :cond_5
    invoke-direct {p0, p3, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushSubStatements(Ljava/util/List;I)V

    .line 423
    invoke-direct {p0, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 424
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "</"

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 425
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 426
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1, v1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void
.end method

.method private flushStatField(Lgroovyjarjaropenbeans/Statement;I)V
    .locals 6

    .line 466
    invoke-direct {p0, p2}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 467
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "<object"

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 470
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object v0

    .line 471
    instance-of v1, v0, Ljava/lang/Class;

    const-string v2, "\""

    if-eqz v1, :cond_0

    .line 472
    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v4, " class=\""

    invoke-virtual {v3, v4}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 473
    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    move-object v4, v0

    check-cast v4, Ljava/lang/Class;

    invoke-virtual {v4}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 474
    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v3, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    :cond_0
    const/4 v3, 0x0

    const/4 v4, 0x0

    if-eqz v1, :cond_1

    .line 478
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v1

    array-length v1, v1

    const/4 v5, 0x1

    if-ne v1, v5, :cond_1

    .line 479
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v1

    aget-object v1, v1, v4

    instance-of v1, v1, Ljava/lang/String;

    if-eqz v1, :cond_1

    .line 481
    :try_start_0
    check-cast v0, Ljava/lang/Class;

    .line 482
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v1

    aget-object v1, v1, v4

    check-cast v1, Ljava/lang/String;

    .line 481
    invoke-virtual {v0, v1}, Ljava/lang/Class;->getField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v3
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_1
    if-eqz v3, :cond_2

    .line 488
    invoke-virtual {v3}, Ljava/lang/reflect/Field;->getModifiers()I

    move-result v0

    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 489
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v0, " field=\""

    invoke-virtual {p2, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 490
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object p1

    aget-object p1, p1, v4

    invoke-virtual {p2, p1}, Ljava/io/PrintWriter;->print(Ljava/lang/Object;)V

    .line 491
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 492
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "/> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 494
    :cond_2
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, " method=\""

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 495
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getMethodName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 496
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v0, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 497
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "> "

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    .line 498
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object p1

    aget-object p1, p1, v4

    add-int/lit8 v0, p2, 0x1

    invoke-direct {p0, p1, v0}, Lgroovyjarjaropenbeans/XMLEncoder;->flushObject(Ljava/lang/Object;I)V

    .line 499
    invoke-direct {p0, p2}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 500
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "</object> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method private flushStatGetterSetter(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjaropenbeans/Statement;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "*>;I)V"
        }
    .end annotation

    .line 508
    invoke-direct {p0, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 510
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "<"

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 511
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "void"

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    const-string v0, "\""

    if-eqz p2, :cond_0

    .line 515
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v3, " id=\""

    invoke-virtual {v2, v3}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 516
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v2, p2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 517
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 521
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object p2

    instance-of p2, p2, Ljava/lang/Class;

    if-eqz p2, :cond_1

    .line 522
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, " class=\""

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 523
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Class;

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 524
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 528
    :cond_1
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, " property=\""

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 529
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getMethodName()Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x3

    invoke-virtual {v2, v3}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v2}, Lgroovyjarjaropenbeans/XMLEncoder;->decapitalize(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/Object;)V

    .line 530
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 533
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object p2

    array-length p2, p2

    if-nez p2, :cond_2

    invoke-interface {p3}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-eqz p2, :cond_2

    .line 534
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "/> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void

    .line 537
    :cond_2
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v0, "> "

    invoke-virtual {p2, v0}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    const/4 p2, 0x0

    .line 540
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v2

    array-length v2, v2

    if-ge p2, v2, :cond_3

    .line 541
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v2

    aget-object v2, v2, p2

    add-int/lit8 v3, p4, 0x1

    invoke-direct {p0, v2, v3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushObject(Ljava/lang/Object;I)V

    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    .line 545
    :cond_3
    invoke-direct {p0, p3, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushSubStatements(Ljava/util/List;I)V

    .line 548
    invoke-direct {p0, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 549
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "</"

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 550
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 551
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1, v0}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void
.end method

.method private flushStatIndexed(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjaropenbeans/Statement;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "*>;I)V"
        }
    .end annotation

    .line 558
    invoke-direct {p0, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 559
    instance-of v0, p1, Lgroovyjarjaropenbeans/Expression;

    if-eqz v0, :cond_0

    const-string v0, "object"

    goto :goto_0

    :cond_0
    const-string v0, "void"

    .line 560
    :goto_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, "<"

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 561
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v1, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    const-string v1, "\""

    if-eqz p2, :cond_1

    .line 565
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v3, " id=\""

    invoke-virtual {v2, v3}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 566
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v2, p2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 567
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 571
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object p2

    instance-of p2, p2, Ljava/lang/Class;

    if-eqz p2, :cond_2

    .line 572
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, " class=\""

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 573
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Class;

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 574
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 578
    :cond_2
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, " index=\""

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 579
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v2

    const/4 v3, 0x0

    aget-object v2, v2, v3

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/Object;)V

    .line 580
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 583
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object p2

    array-length p2, p2

    const/4 v1, 0x1

    if-ne p2, v1, :cond_3

    invoke-interface {p3}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-eqz p2, :cond_3

    .line 584
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "/> "

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void

    .line 587
    :cond_3
    iget-object p2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, "> "

    invoke-virtual {p2, v2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    .line 590
    :goto_1
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object p2

    array-length p2, p2

    if-ge v1, p2, :cond_4

    .line 591
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object p2

    aget-object p2, p2, v1

    add-int/lit8 v3, p4, 0x1

    invoke-direct {p0, p2, v3}, Lgroovyjarjaropenbeans/XMLEncoder;->flushObject(Ljava/lang/Object;I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 595
    :cond_4
    invoke-direct {p0, p3, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushSubStatements(Ljava/util/List;I)V

    .line 598
    invoke-direct {p0, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushIndent(I)V

    .line 599
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string p2, "</"

    invoke-virtual {p1, p2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 600
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1, v0}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 601
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {p1, v2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    return-void
.end method

.method private flushStatement(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjaropenbeans/Statement;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "*>;I)V"
        }
    .end annotation

    .line 432
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object v0

    .line 433
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getMethodName()Ljava/lang/String;

    move-result-object v1

    .line 434
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v2

    .line 437
    const-class v3, Ljava/lang/reflect/Array;

    if-ne v3, v0, :cond_0

    const-string v3, "newInstance"

    invoke-virtual {v3, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 438
    invoke-direct {p0, p1, p2, p3, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushStatArray(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V

    return-void

    .line 442
    :cond_0
    invoke-direct {p0, v0, v1, v2}, Lgroovyjarjaropenbeans/XMLEncoder;->isGetArrayStat(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_5

    .line 443
    invoke-direct {p0, v0, v1, v2}, Lgroovyjarjaropenbeans/XMLEncoder;->isSetArrayStat(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_1

    .line 448
    :cond_1
    invoke-direct {p0, v1, v2}, Lgroovyjarjaropenbeans/XMLEncoder;->isGetPropertyStat(Ljava/lang/String;[Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_4

    invoke-direct {p0, v1, v2}, Lgroovyjarjaropenbeans/XMLEncoder;->isSetPropertyStat(Ljava/lang/String;[Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_0

    .line 454
    :cond_2
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getMethodName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "getField"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 455
    invoke-direct {p0, p1, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushStatField(Lgroovyjarjaropenbeans/Statement;I)V

    return-void

    .line 460
    :cond_3
    invoke-direct {p0, p1, p2, p3, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushStatCommon(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V

    return-void

    .line 449
    :cond_4
    :goto_0
    invoke-direct {p0, p1, p2, p3, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushStatGetterSetter(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V

    return-void

    .line 444
    :cond_5
    :goto_1
    invoke-direct {p0, p1, p2, p3, p4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushStatIndexed(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V

    return-void
.end method

.method private flushString(Ljava/lang/String;)V
    .locals 5

    const/4 v0, 0x0

    .line 607
    :goto_0
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v1

    if-ge v0, v1, :cond_6

    .line 608
    invoke-virtual {p1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v2, 0x3c

    if-ne v1, v2, :cond_0

    .line 610
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, "&lt;"

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    goto :goto_1

    :cond_0
    const/16 v2, 0x3e

    if-ne v1, v2, :cond_1

    .line 612
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, "&gt;"

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    const/16 v2, 0x26

    if-ne v1, v2, :cond_2

    .line 614
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, "&amp;"

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    goto :goto_1

    :cond_2
    const/16 v2, 0x27

    if-ne v1, v2, :cond_3

    .line 616
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, "&apos;"

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    goto :goto_1

    :cond_3
    const/16 v2, 0x22

    if-ne v1, v2, :cond_4

    .line 618
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, "&quot;"

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    goto :goto_1

    .line 620
    :cond_4
    invoke-direct {p0, v1}, Lgroovyjarjaropenbeans/XMLEncoder;->invalidCharacter(C)Z

    move-result v2

    if-eqz v2, :cond_5

    .line 621
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "<char code=\"#"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v4, 0x10

    invoke-static {v1, v4}, Ljava/lang/Integer;->toString(II)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, "\"/>"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    goto :goto_1

    .line 624
    :cond_5
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v2, v1}, Ljava/io/PrintWriter;->print(C)V

    :goto_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_6
    return-void
.end method

.method private flushSubStatements(Ljava/util/List;I)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "*>;I)V"
        }
    .end annotation

    const/4 v0, 0x0

    .line 631
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_1

    .line 632
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjaropenbeans/Statement;

    .line 634
    :try_start_0
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Lgroovyjarjaropenbeans/Expression;

    if-ne v2, v3, :cond_0

    .line 635
    check-cast v1, Lgroovyjarjaropenbeans/Expression;

    .line 636
    invoke-virtual {v1}, Lgroovyjarjaropenbeans/Expression;->getValue()Ljava/lang/Object;

    move-result-object v1

    .line 637
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v2, v1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    add-int/lit8 v3, p2, 0x1

    const/4 v4, 0x1

    .line 638
    invoke-direct {p0, v1, v2, v3, v4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushExpression(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;IZ)V

    goto :goto_1

    :cond_0
    const/4 v2, 0x0

    .line 640
    sget-object v3, Ljava/util/Collections;->EMPTY_LIST:Ljava/util/List;

    add-int/lit8 v4, p2, 0x1

    invoke-direct {p0, v1, v2, v3, v4}, Lgroovyjarjaropenbeans/XMLEncoder;->flushStatement(Lgroovyjarjaropenbeans/Statement;Ljava/lang/String;Ljava/util/List;I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v1

    .line 645
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/XMLEncoder;->getExceptionListener()Lgroovyjarjaropenbeans/ExceptionListener;

    move-result-object v2

    invoke-interface {v2, v1}, Lgroovyjarjaropenbeans/ExceptionListener;->exceptionThrown(Ljava/lang/Exception;)V

    :goto_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method private idSerialNoOfObject(Ljava/lang/Object;)Ljava/lang/String;
    .locals 3

    .line 687
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    .line 688
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->clazzCounterMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v1, v0}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Integer;

    if-nez v1, :cond_0

    const/4 v1, 0x0

    goto :goto_0

    .line 689
    :cond_0
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    :goto_0
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    .line 690
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lgroovyjarjarharmonybeans/BeansUtils;->idOfClass(Ljava/lang/Class;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 691
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->clazzCounterMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    add-int/lit8 v1, v1, 0x1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {v2, v0, v1}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p1
.end method

.method private invalidCharacter(C)Z
    .locals 2

    if-ltz p1, :cond_0

    const/16 v0, 0x9

    if-lt p1, v0, :cond_4

    :cond_0
    const/16 v0, 0xa

    const/16 v1, 0xd

    if-ge v0, p1, :cond_1

    if-lt p1, v1, :cond_4

    :cond_1
    if-ge v1, p1, :cond_2

    const/16 v0, 0x20

    if-lt p1, v0, :cond_4

    :cond_2
    const v0, 0xd7ff

    if-ge v0, p1, :cond_3

    const v0, 0xe000

    if-lt p1, v0, :cond_4

    :cond_3
    const v0, 0xfffe

    if-ne p1, v0, :cond_5

    :cond_4
    const/4 p1, 0x1

    goto :goto_0

    :cond_5
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method private isBasicType(Ljava/lang/Object;)Z
    .locals 1

    if-eqz p1, :cond_1

    .line 660
    instance-of v0, p1, Ljava/lang/Boolean;

    if-nez v0, :cond_1

    instance-of v0, p1, Ljava/lang/Byte;

    if-nez v0, :cond_1

    instance-of v0, p1, Ljava/lang/Character;

    if-nez v0, :cond_1

    instance-of v0, p1, Ljava/lang/Class;

    if-nez v0, :cond_1

    instance-of v0, p1, Ljava/lang/Double;

    if-nez v0, :cond_1

    instance-of v0, p1, Ljava/lang/Float;

    if-nez v0, :cond_1

    instance-of v0, p1, Ljava/lang/Integer;

    if-nez v0, :cond_1

    instance-of v0, p1, Ljava/lang/Long;

    if-nez v0, :cond_1

    instance-of v0, p1, Ljava/lang/Short;

    if-nez v0, :cond_1

    instance-of v0, p1, Ljava/lang/String;

    if-nez v0, :cond_1

    instance-of p1, p1, Ljava/lang/reflect/Proxy;

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method private isGetArrayStat(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Z
    .locals 2

    const-string v0, "get"

    .line 669
    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p2, :cond_0

    array-length p2, p3

    if-ne p2, v0, :cond_0

    aget-object p2, p3, v1

    instance-of p2, p2, Ljava/lang/Integer;

    if-eqz p2, :cond_0

    .line 670
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->isArray()Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    move v0, v1

    :goto_0
    return v0
.end method

.method private isGetPropertyStat(Ljava/lang/String;[Ljava/lang/Object;)Z
    .locals 1

    const-string v0, "get"

    .line 674
    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p1

    const/4 v0, 0x3

    if-le p1, v0, :cond_0

    array-length p1, p2

    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method private isSetArrayStat(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Z
    .locals 2

    const-string v0, "set"

    .line 678
    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    const/4 v0, 0x0

    if-eqz p2, :cond_0

    array-length p2, p3

    const/4 v1, 0x2

    if-ne p2, v1, :cond_0

    aget-object p2, p3, v0

    instance-of p2, p2, Ljava/lang/Integer;

    if-eqz p2, :cond_0

    .line 679
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->isArray()Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 v0, 0x1

    :cond_0
    return v0
.end method

.method private isSetPropertyStat(Ljava/lang/String;[Ljava/lang/Object;)Z
    .locals 2

    const-string v0, "set"

    .line 683
    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p1

    const/4 v0, 0x3

    if-le p1, v0, :cond_0

    array-length p1, p2

    if-ne p1, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    return v1
.end method

.method private preprocess(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;)V
    .locals 4

    .line 700
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z

    if-eqz v0, :cond_0

    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->isBasicType(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 704
    :cond_0
    instance-of v0, p1, Ljava/lang/Class;

    if-eqz v0, :cond_1

    return-void

    .line 709
    :cond_1
    iget v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->refCount:I

    const/4 v1, 0x1

    add-int/2addr v0, v1

    iput v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->refCount:I

    .line 712
    iget v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->refCount:I

    if-le v0, v1, :cond_2

    return-void

    .line 717
    :cond_2
    iget-object v0, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    const/4 v1, 0x0

    if-eqz v0, :cond_5

    .line 719
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    iget-object v2, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/Expression;->getTarget()Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-eqz v0, :cond_3

    .line 720
    iget-object v2, v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    if-eqz v2, :cond_3

    iget-object v0, v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    .line 721
    invoke-virtual {v0}, Lgroovyjarjaropenbeans/Expression;->getMethodName()Ljava/lang/String;

    move-result-object v0

    const-string v2, "getField"

    invoke-virtual {v2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 722
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 725
    :cond_3
    iget-object p1, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Expression;->getArguments()[Ljava/lang/Object;

    move-result-object p1

    move v0, v1

    .line 726
    :goto_0
    array-length v2, p1

    if-ge v0, v2, :cond_5

    .line 727
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    aget-object v3, p1, v0

    invoke-virtual {v2, v3}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-eqz v2, :cond_4

    .line 729
    aget-object v3, p1, v0

    invoke-direct {p0, v3, v2}, Lgroovyjarjaropenbeans/XMLEncoder;->preprocess(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;)V

    :cond_4
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 734
    :cond_5
    iget-object p1, p2, Lgroovyjarjaropenbeans/XMLEncoder$Record;->stats:Ljava/util/ArrayList;

    invoke-virtual {p1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_6
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_c

    .line 735
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjaropenbeans/Statement;

    .line 736
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v2, Lgroovyjarjaropenbeans/Expression;

    if-ne v0, v2, :cond_a

    .line 738
    :try_start_0
    check-cast p2, Lgroovyjarjaropenbeans/Expression;

    .line 739
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {p2}, Lgroovyjarjaropenbeans/Expression;->getValue()Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-eqz v0, :cond_9

    .line 740
    iget-object v2, v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    if-eqz v2, :cond_9

    iget-object v2, v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    if-eq v2, p2, :cond_7

    goto :goto_2

    .line 745
    :cond_7
    invoke-virtual {p2}, Lgroovyjarjaropenbeans/Expression;->getValue()Ljava/lang/Object;

    move-result-object v2

    invoke-direct {p0, v2, v0}, Lgroovyjarjaropenbeans/XMLEncoder;->preprocess(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;)V

    .line 746
    iget-object v0, v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;->stats:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_6

    .line 747
    invoke-virtual {p2}, Lgroovyjarjaropenbeans/Expression;->getTarget()Ljava/lang/Object;

    move-result-object v0

    .line 748
    invoke-virtual {p2}, Lgroovyjarjaropenbeans/Expression;->getMethodName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p2}, Lgroovyjarjaropenbeans/Expression;->getArguments()[Ljava/lang/Object;

    move-result-object v3

    .line 747
    invoke-direct {p0, v0, v2, v3}, Lgroovyjarjaropenbeans/XMLEncoder;->isGetArrayStat(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_8

    .line 749
    invoke-virtual {p2}, Lgroovyjarjaropenbeans/Expression;->getMethodName()Ljava/lang/String;

    move-result-object v0

    .line 750
    invoke-virtual {p2}, Lgroovyjarjaropenbeans/Expression;->getArguments()[Ljava/lang/Object;

    move-result-object p2

    .line 749
    invoke-direct {p0, v0, p2}, Lgroovyjarjaropenbeans/XMLEncoder;->isGetPropertyStat(Ljava/lang/String;[Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_6

    .line 751
    :cond_8
    invoke-interface {p1}, Ljava/util/Iterator;->remove()V

    goto :goto_1

    .line 742
    :cond_9
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->remove()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p2

    .line 756
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/XMLEncoder;->getExceptionListener()Lgroovyjarjaropenbeans/ExceptionListener;

    move-result-object v0

    invoke-interface {v0, p2}, Lgroovyjarjaropenbeans/ExceptionListener;->exceptionThrown(Ljava/lang/Exception;)V

    .line 757
    invoke-interface {p1}, Ljava/util/Iterator;->remove()V

    goto :goto_1

    .line 762
    :cond_a
    invoke-virtual {p2}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object p2

    move v0, v1

    .line 763
    :goto_3
    array-length v2, p2

    if-ge v0, v2, :cond_6

    .line 764
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    aget-object v3, p2, v0

    invoke-virtual {v2, v3}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-eqz v2, :cond_b

    .line 766
    aget-object v3, p2, v0

    invoke-direct {p0, v3, v2}, Lgroovyjarjaropenbeans/XMLEncoder;->preprocess(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;)V

    :cond_b
    add-int/lit8 v0, v0, 0x1

    goto :goto_3

    :cond_c
    return-void
.end method

.method private recordExpression(Ljava/lang/Object;Lgroovyjarjaropenbeans/Expression;)V
    .locals 4

    .line 774
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-nez v0, :cond_0

    .line 776
    new-instance v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovyjarjaropenbeans/XMLEncoder$Record;-><init>(Lgroovyjarjaropenbeans/XMLEncoder$1;)V

    .line 777
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v1, p1, v0}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 780
    :cond_0
    iget-object v1, v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    if-nez v1, :cond_2

    .line 782
    iget-object v1, v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;->stats:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjaropenbeans/Statement;

    .line 783
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Lgroovyjarjaropenbeans/Expression;

    if-ne v2, v3, :cond_1

    .line 784
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPrePending:Ljava/util/ArrayList;

    invoke-virtual {v2, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 789
    :cond_2
    iput-object p2, v0, Lgroovyjarjaropenbeans/XMLEncoder$Record;->exp:Lgroovyjarjaropenbeans/Expression;

    .line 792
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->owner:Ljava/lang/Object;

    if-ne p1, v0, :cond_3

    if-eqz v0, :cond_3

    const/4 p1, 0x1

    .line 793
    iput-boolean p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->needOwner:Z

    .line 797
    :cond_3
    invoke-direct {p0, p2}, Lgroovyjarjaropenbeans/XMLEncoder;->recordStatement(Lgroovyjarjaropenbeans/Statement;)V

    return-void
.end method

.method private recordStatement(Lgroovyjarjaropenbeans/Statement;)V
    .locals 12

    if-nez p1, :cond_0

    return-void

    .line 805
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object v0

    .line 806
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->owner:Ljava/lang/Object;

    const/4 v2, 0x1

    if-ne v0, v1, :cond_1

    if-eqz v1, :cond_1

    .line 807
    iput-boolean v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->needOwner:Z

    .line 811
    :cond_1
    iget-object v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v1, v0}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-nez v1, :cond_2

    .line 813
    new-instance v1, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    const/4 v3, 0x0

    invoke-direct {v1, v3}, Lgroovyjarjaropenbeans/XMLEncoder$Record;-><init>(Lgroovyjarjaropenbeans/XMLEncoder$1;)V

    .line 814
    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v3, v0, v1}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 818
    :cond_2
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getMethodName()Ljava/lang/String;

    move-result-object v3

    .line 819
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v4

    .line 820
    invoke-direct {p0, v3, v4}, Lgroovyjarjaropenbeans/XMLEncoder;->isSetPropertyStat(Ljava/lang/String;[Ljava/lang/Object;)Z

    move-result v5

    const/4 v6, 0x0

    if-nez v5, :cond_3

    .line 821
    invoke-direct {p0, v0, v3, v4}, Lgroovyjarjaropenbeans/XMLEncoder;->isSetArrayStat(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_7

    .line 822
    :cond_3
    iget-object v5, v1, Lgroovyjarjaropenbeans/XMLEncoder$Record;->stats:Ljava/util/ArrayList;

    invoke-virtual {v5}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :cond_4
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_7

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjaropenbeans/Statement;

    .line 823
    invoke-virtual {v7}, Lgroovyjarjaropenbeans/Statement;->getTarget()Ljava/lang/Object;

    move-result-object v8

    if-ne v0, v8, :cond_4

    .line 824
    invoke-virtual {v7}, Lgroovyjarjaropenbeans/Statement;->getMethodName()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v3, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_4

    .line 825
    invoke-virtual {v7}, Lgroovyjarjaropenbeans/Statement;->getArguments()[Ljava/lang/Object;

    move-result-object v7

    .line 826
    array-length v8, v4

    array-length v9, v7

    if-ne v8, v9, :cond_4

    move v8, v6

    .line 828
    :goto_0
    array-length v9, v4

    if-ge v8, v9, :cond_6

    .line 829
    aget-object v9, v4, v8

    invoke-virtual {v9}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v9

    invoke-virtual {p0, v9}, Lgroovyjarjaropenbeans/XMLEncoder;->getPersistenceDelegate(Ljava/lang/Class;)Lgroovyjarjaropenbeans/PersistenceDelegate;

    move-result-object v9

    aget-object v10, v4, v8

    aget-object v11, v7, v8

    .line 830
    invoke-virtual {v9, v10, v11}, Lgroovyjarjaropenbeans/PersistenceDelegate;->mutatesTo(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_5

    add-int/lit8 v8, v8, 0x1

    goto :goto_0

    :cond_5
    move v7, v6

    goto :goto_1

    :cond_6
    move v7, v2

    :goto_1
    if-eqz v7, :cond_4

    goto :goto_2

    :cond_7
    move v2, v6

    :goto_2
    if-nez v2, :cond_8

    .line 845
    iget-object v0, v1, Lgroovyjarjaropenbeans/XMLEncoder$Record;->stats:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_8
    return-void
.end method


# virtual methods
.method public close()V
    .locals 2

    .line 131
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/XMLEncoder;->flush()V

    .line 132
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v1, "</java> "

    invoke-virtual {v0, v1}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    .line 133
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    invoke-virtual {v0}, Ljava/io/PrintWriter;->close()V

    return-void
.end method

.method public flush()V
    .locals 4

    .line 151
    monitor-enter p0

    .line 153
    :try_start_0
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->hasXmlHeader:Z

    const/4 v1, 0x1

    if-nez v0, :cond_0

    .line 154
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    const-string v2, "<?xml version=\"1.0\" encoding=\"UTF-8\"?> "

    invoke-virtual {v0, v2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    .line 155
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->out:Ljava/io/PrintWriter;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "<java version=\""

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "java.version"

    .line 156
    invoke-static {v3}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "\" class=\"com.googlecode.openbeans.XMLDecoder\"> "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 155
    invoke-virtual {v0, v2}, Ljava/io/PrintWriter;->println(Ljava/lang/String;)V

    .line 158
    iput-boolean v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->hasXmlHeader:Z

    .line 162
    :cond_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPending:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    .line 163
    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 164
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 165
    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v3, v2}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjaropenbeans/XMLEncoder$Record;

    if-eqz v3, :cond_1

    .line 167
    invoke-direct {p0, v2, v3}, Lgroovyjarjaropenbeans/XMLEncoder;->preprocess(Ljava/lang/Object;Lgroovyjarjaropenbeans/XMLEncoder$Record;)V

    goto :goto_0

    .line 172
    :cond_2
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPending:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    .line 173
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    .line 174
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 175
    invoke-direct {p0, v2, v1}, Lgroovyjarjaropenbeans/XMLEncoder;->flushObject(Ljava/lang/Object;I)V

    .line 177
    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    goto :goto_1

    .line 181
    :cond_3
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objRecordMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v0}, Ljava/util/IdentityHashMap;->clear()V

    .line 182
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPendingStat:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 183
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objPrePendingCache:Ljava/util/IdentityHashMap;

    invoke-virtual {v0}, Ljava/util/IdentityHashMap;->clear()V

    .line 184
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->clazzCounterMap:Ljava/util/IdentityHashMap;

    invoke-virtual {v0}, Ljava/util/IdentityHashMap;->clear()V

    .line 187
    invoke-super {p0}, Lgroovyjarjaropenbeans/Encoder;->clear()V

    .line 188
    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0
.end method

.method public getOwner()Ljava/lang/Object;
    .locals 1

    .line 656
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->owner:Ljava/lang/Object;

    return-object v0
.end method

.method public setOwner(Ljava/lang/Object;)V
    .locals 0

    .line 892
    iput-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->owner:Ljava/lang/Object;

    return-void
.end method

.method public writeExpression(Lgroovyjarjaropenbeans/Expression;)V
    .locals 4

    .line 902
    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    .line 904
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z

    const/4 v1, 0x1

    .line 905
    iput-boolean v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z

    .line 907
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->expressionValue(Lgroovyjarjaropenbeans/Expression;)Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_4

    .line 909
    invoke-virtual {p0, v1}, Lgroovyjarjaropenbeans/XMLEncoder;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    if-eqz v2, :cond_0

    if-nez v0, :cond_4

    .line 910
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Ljava/lang/String;

    if-eq v2, v3, :cond_0

    goto :goto_0

    .line 915
    :cond_0
    invoke-direct {p0, v1}, Lgroovyjarjaropenbeans/XMLEncoder;->isBasicType(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    if-nez v0, :cond_2

    .line 916
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Ljava/lang/String;

    if-ne v2, v3, :cond_2

    .line 917
    :cond_1
    invoke-direct {p0, v1, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->recordExpression(Ljava/lang/Object;Lgroovyjarjaropenbeans/Expression;)V

    .line 921
    :cond_2
    invoke-direct {p0, v1}, Lgroovyjarjaropenbeans/XMLEncoder;->checkDeadLoop(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_3

    return-void

    .line 924
    :cond_3
    invoke-super {p0, p1}, Lgroovyjarjaropenbeans/Encoder;->writeExpression(Lgroovyjarjaropenbeans/Expression;)V

    .line 925
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z

    :cond_4
    :goto_0
    return-void
.end method

.method public writeObject(Ljava/lang/Object;)V
    .locals 4

    .line 934
    monitor-enter p0

    .line 935
    :try_start_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objPrePendingCache:Ljava/util/IdentityHashMap;

    invoke-virtual {v0, p1}, Ljava/util/IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/ArrayList;

    const/4 v1, 0x1

    if-nez v0, :cond_0

    .line 937
    iget-boolean v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z

    .line 938
    iput-boolean v1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 940
    :try_start_1
    invoke-super {p0, p1}, Lgroovyjarjaropenbeans/Encoder;->writeObject(Ljava/lang/Object;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 942
    :try_start_2
    iput-boolean v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z

    goto :goto_0

    :catchall_0
    move-exception p1

    iput-boolean v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z

    .line 943
    throw p1

    .line 945
    :cond_0
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPrePending:Ljava/util/ArrayList;

    invoke-virtual {v2}, Ljava/util/ArrayList;->clear()V

    .line 946
    iget-object v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPrePending:Ljava/util/ArrayList;

    invoke-virtual {v2, v0}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 950
    :goto_0
    iget-boolean v2, p0, Lgroovyjarjaropenbeans/XMLEncoder;->writingObject:Z

    if-nez v2, :cond_4

    const/4 v2, 0x0

    if-nez v0, :cond_1

    goto :goto_1

    :cond_1
    move v1, v2

    :goto_1
    if-eqz v1, :cond_2

    if-eqz p1, :cond_2

    .line 954
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 955
    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPrePending:Ljava/util/ArrayList;

    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 956
    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->objPrePendingCache:Ljava/util/IdentityHashMap;

    invoke-virtual {v3, p1, v0}, Ljava/util/IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 960
    :cond_2
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPending:Ljava/util/ArrayList;

    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPrePending:Ljava/util/ArrayList;

    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 961
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPendingStat:Ljava/util/ArrayList;

    iget-object v3, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPrePending:Ljava/util/ArrayList;

    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 962
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPrePending:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    if-eqz v1, :cond_3

    .line 964
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPending:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 965
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPendingStat:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    goto :goto_2

    .line 967
    :cond_3
    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPending:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 969
    :goto_2
    iget-boolean p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->needOwner:Z

    if-eqz p1, :cond_4

    .line 970
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPending:Ljava/util/ArrayList;

    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->owner:Ljava/lang/Object;

    invoke-virtual {p1, v0}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 971
    iget-object p1, p0, Lgroovyjarjaropenbeans/XMLEncoder;->flushPending:Ljava/util/ArrayList;

    iget-object v0, p0, Lgroovyjarjaropenbeans/XMLEncoder;->owner:Ljava/lang/Object;

    invoke-virtual {p1, v2, v0}, Ljava/util/ArrayList;->add(ILjava/lang/Object;)V

    .line 974
    :cond_4
    monitor-exit p0

    return-void

    :catchall_1
    move-exception p1

    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    throw p1
.end method

.method public writeStatement(Lgroovyjarjaropenbeans/Statement;)V
    .locals 1

    if-nez p1, :cond_0

    .line 984
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v0, "java.lang.Exception: XMLEncoder: discarding statement null"

    .line 985
    invoke-virtual {p1, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 986
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v0, "Continuing..."

    invoke-virtual {p1, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void

    .line 991
    :cond_0
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/XMLEncoder;->recordStatement(Lgroovyjarjaropenbeans/Statement;)V

    .line 992
    invoke-super {p0, p1}, Lgroovyjarjaropenbeans/Encoder;->writeStatement(Lgroovyjarjaropenbeans/Statement;)V

    return-void
.end method
