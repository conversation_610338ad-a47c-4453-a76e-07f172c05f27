.class public Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;
.super Ljava/lang/Object;
.source "BeanContextChildSupport.java"

# interfaces
.implements Lgroovyjarjaropenbeans/beancontext/BeanContextChild;
.implements Lgroovyjarjaropenbeans/beancontext/BeanContextServicesListener;
.implements Ljava/io/Serializable;


# static fields
.field static final BEAN_CONTEXT:Ljava/lang/String; = "beanContext"

.field private static final serialVersionUID:J = 0x57d4efc704dc7225L


# instance fields
.field protected transient beanContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

.field public beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

.field private transient lastVetoedContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

.field protected pcSupport:Lgroovyjarjaropenbeans/PropertyChangeSupport;

.field protected transient rejectedSetBCOnce:Z

.field protected vcSupport:Lgroovyjarjaropenbeans/VetoableChangeSupport;


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 59
    invoke-direct {p0, v0}, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContextChild;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContextChild;)V
    .locals 1

    .line 62
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    if-nez p1, :cond_0

    move-object p1, p0

    .line 65
    :cond_0
    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    .line 68
    new-instance p1, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    invoke-direct {p1, v0}, Lgroovyjarjaropenbeans/PropertyChangeSupport;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->pcSupport:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    .line 69
    new-instance p1, Lgroovyjarjaropenbeans/VetoableChangeSupport;

    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    invoke-direct {p1, v0}, Lgroovyjarjaropenbeans/VetoableChangeSupport;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->vcSupport:Lgroovyjarjaropenbeans/VetoableChangeSupport;

    const/4 p1, 0x0

    .line 70
    iput-boolean p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->rejectedSetBCOnce:Z

    return-void
.end method

.method private readObject(Ljava/io/ObjectInputStream;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    .line 122
    invoke-virtual {p1}, Ljava/io/ObjectInputStream;->defaultReadObject()V

    return-void
.end method

.method private writeObject(Ljava/io/ObjectOutputStream;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 204
    invoke-virtual {p1}, Ljava/io/ObjectOutputStream;->defaultWriteObject()V

    return-void
.end method


# virtual methods
.method public addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    if-eqz p1, :cond_1

    if-nez p2, :cond_0

    goto :goto_0

    .line 80
    :cond_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->pcSupport:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public addVetoableChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/VetoableChangeListener;)V
    .locals 1

    if-eqz p1, :cond_1

    if-nez p2, :cond_0

    goto :goto_0

    .line 90
    :cond_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->vcSupport:Lgroovyjarjaropenbeans/VetoableChangeSupport;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjaropenbeans/VetoableChangeSupport;->addVetoableChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/VetoableChangeListener;)V

    const/4 p1, 0x0

    .line 91
    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->lastVetoedContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

    :cond_1
    :goto_0
    return-void
.end method

.method public firePropertyChange(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 95
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->pcSupport:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1, p2, p3}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->firePropertyChange(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method

.method public fireVetoableChange(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/PropertyVetoException;
        }
    .end annotation

    .line 101
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->vcSupport:Lgroovyjarjaropenbeans/VetoableChangeSupport;

    invoke-virtual {v0, p1, p2, p3}, Lgroovyjarjaropenbeans/VetoableChangeSupport;->fireVetoableChange(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method

.method public declared-synchronized getBeanContext()Lgroovyjarjaropenbeans/beancontext/BeanContext;
    .locals 1

    monitor-enter p0

    .line 105
    :try_start_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public getBeanContextChildPeer()Lgroovyjarjaropenbeans/beancontext/BeanContextChild;
    .locals 1

    .line 109
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    return-object v0
.end method

.method protected initializeBeanContextResources()V
    .locals 0

    return-void
.end method

.method public isDelegated()Z
    .locals 1

    .line 116
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    invoke-virtual {v0, p0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    return v0
.end method

.method protected releaseBeanContextResources()V
    .locals 0

    return-void
.end method

.method public removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    .line 131
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->pcSupport:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    return-void
.end method

.method public removeVetoableChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/VetoableChangeListener;)V
    .locals 1

    .line 137
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->vcSupport:Lgroovyjarjaropenbeans/VetoableChangeSupport;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjaropenbeans/VetoableChangeSupport;->removeVetoableChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/VetoableChangeListener;)V

    const/4 p1, 0x0

    .line 138
    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->lastVetoedContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

    return-void
.end method

.method public serviceAvailable(Lgroovyjarjaropenbeans/beancontext/BeanContextServiceAvailableEvent;)V
    .locals 1

    .line 142
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->isDelegated()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 143
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    check-cast v0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesListener;

    .line 144
    invoke-interface {v0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesListener;->serviceAvailable(Lgroovyjarjaropenbeans/beancontext/BeanContextServiceAvailableEvent;)V

    :cond_0
    return-void
.end method

.method public serviceRevoked(Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedEvent;)V
    .locals 1

    .line 149
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->isDelegated()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 150
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    check-cast v0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesListener;

    .line 151
    invoke-interface {v0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesListener;->serviceRevoked(Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedEvent;)V

    :cond_0
    return-void
.end method

.method public declared-synchronized setBeanContext(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/PropertyVetoException;
        }
    .end annotation

    monitor-enter p0

    .line 159
    :try_start_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v0, :cond_0

    if-nez p1, :cond_0

    .line 160
    monitor-exit p0

    return-void

    :cond_0
    if-eqz v0, :cond_1

    .line 163
    :try_start_1
    invoke-virtual {v0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-eqz v0, :cond_1

    .line 164
    monitor-exit p0

    return-void

    .line 174
    :cond_1
    :try_start_2
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->rejectedSetBCOnce:Z

    if-eqz v0, :cond_2

    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->lastVetoedContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

    if-eq v0, p1, :cond_3

    .line 175
    :cond_2
    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->lastVetoedContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

    const/4 v0, 0x1

    .line 176
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->rejectedSetBCOnce:Z

    .line 179
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->validatePendingSetBeanContext(Lgroovyjarjaropenbeans/beancontext/BeanContext;)Z

    move-result v0

    if-eqz v0, :cond_4

    const-string v0, "beanContext"

    .line 184
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

    invoke-virtual {p0, v0, v1, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->fireVetoableChange(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_3
    const/4 v0, 0x0

    .line 187
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->rejectedSetBCOnce:Z

    .line 189
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->releaseBeanContextResources()V

    const-string v0, "beanContext"

    .line 193
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

    invoke-virtual {p0, v0, v1, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->firePropertyChange(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 194
    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

    .line 195
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->initializeBeanContextResources()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 197
    monitor-exit p0

    return-void

    .line 180
    :cond_4
    :try_start_3
    new-instance v0, Lgroovyjarjaropenbeans/PropertyVetoException;

    const-string v1, "beans.0F"

    invoke-static {v1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    new-instance v2, Lgroovyjarjaropenbeans/PropertyChangeEvent;

    iget-object v3, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    const-string v4, "beanContext"

    iget-object v5, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;->beanContext:Lgroovyjarjaropenbeans/beancontext/BeanContext;

    invoke-direct {v2, v3, v4, v5, p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-direct {v0, v1, v2}, Lgroovyjarjaropenbeans/PropertyVetoException;-><init>(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    throw v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public validatePendingSetBeanContext(Lgroovyjarjaropenbeans/beancontext/BeanContext;)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method
