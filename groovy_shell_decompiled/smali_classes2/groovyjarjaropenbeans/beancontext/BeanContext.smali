.class public interface abstract Lgroovyjarjaropenbeans/beancontext/BeanContext;
.super Ljava/lang/Object;
.source "BeanContext.java"

# interfaces
.implements Lgroovyjarjaropenbeans/beancontext/BeanContextChild;
.implements Ljava/util/Collection;
.implements Lgroovyjarjaropenbeans/DesignMode;
.implements Lgroovyjarjaropenbeans/Visibility;


# static fields
.field public static final globalHierarchyLock:Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 36
    new-instance v0, Ljava/lang/Object;

    invoke-direct {v0}, Ljava/lang/Object;-><init>()V

    sput-object v0, Lgroovyjarjaropenbeans/beancontext/BeanContext;->globalHierarchyLock:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public abstract addBeanContextMembershipListener(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;)V
.end method

.method public abstract getResource(Ljava/lang/String;Lgroovyjarjaropenbeans/beancontext/BeanContextChild;)Ljava/net/URL;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalArgumentException;
        }
    .end annotation
.end method

.method public abstract getResourceAsStream(Ljava/lang/String;Lgroovyjarjaropenbeans/beancontext/BeanContextChild;)Ljava/io/InputStream;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalArgumentException;
        }
    .end annotation
.end method

.method public abstract instantiateChild(Ljava/lang/String;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation
.end method

.method public abstract removeBeanContextMembershipListener(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;)V
.end method
