.class public Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSProxyServiceProvider;
.super Ljava/lang/Object;
.source "BeanContextServicesSupport.java"

# interfaces
.implements Lgroovyjarjaropenbeans/beancontext/BeanContextServiceProvider;
.implements Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "BCSSProxyServiceProvider"
.end annotation


# instance fields
.field private backBCS:Lgroovyjarjaropenbeans/beancontext/BeanContextServices;

.field final synthetic this$0:Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;


# direct methods
.method constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;Lgroovyjarjaropenbeans/beancontext/BeanContextServices;)V
    .locals 0

    .line 121
    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSProxyServiceProvider;->this$0:Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 122
    iput-object p2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSProxyServiceProvider;->backBCS:Lgroovyjarjaropenbeans/beancontext/BeanContextServices;

    return-void
.end method


# virtual methods
.method public getCurrentServiceSelectors(Lgroovyjarjaropenbeans/beancontext/BeanContextServices;Ljava/lang/Class;)Ljava/util/Iterator;
    .locals 0

    .line 130
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public getService(Lgroovyjarjaropenbeans/beancontext/BeanContextServices;Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 138
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method getService(Lgroovyjarjaropenbeans/beancontext/BeanContextServices;Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedListener;)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/util/TooManyListenersException;
        }
    .end annotation

    .line 148
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSProxyServiceProvider;->backBCS:Lgroovyjarjaropenbeans/beancontext/BeanContextServices;

    iget-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSProxyServiceProvider;->this$0:Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;

    .line 149
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;->getBeanContextServicesPeer()Lgroovyjarjaropenbeans/beancontext/BeanContextServices;

    move-result-object v1

    new-instance v5, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$ServiceRevokedListenerDelegator;

    iget-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSProxyServiceProvider;->this$0:Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;

    invoke-direct {v5, p1, p5}, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$ServiceRevokedListenerDelegator;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedListener;)V

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    .line 148
    invoke-interface/range {v0 .. v5}, Lgroovyjarjaropenbeans/beancontext/BeanContextServices;->getService(Lgroovyjarjaropenbeans/beancontext/BeanContextChild;Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedListener;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public releaseService(Lgroovyjarjaropenbeans/beancontext/BeanContextServices;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 159
    iget-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSProxyServiceProvider;->backBCS:Lgroovyjarjaropenbeans/beancontext/BeanContextServices;

    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSProxyServiceProvider;->this$0:Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;

    .line 160
    invoke-virtual {v0}, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;->getBeanContextServicesPeer()Lgroovyjarjaropenbeans/beancontext/BeanContextServices;

    move-result-object v0

    .line 159
    invoke-interface {p1, v0, p2, p3}, Lgroovyjarjaropenbeans/beancontext/BeanContextServices;->releaseService(Lgroovyjarjaropenbeans/beancontext/BeanContextChild;Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method

.method public serviceRevoked(Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedEvent;)V
    .locals 0

    .line 167
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method
