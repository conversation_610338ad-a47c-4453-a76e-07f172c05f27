.class public Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedEvent;
.super Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;
.source "BeanContextServiceRevokedEvent.java"


# static fields
.field private static final serialVersionUID:J = -0x11fab1a29891fddaL


# instance fields
.field private invalidateRefs:Z

.field protected serviceClass:Ljava/lang/Class;


# direct methods
.method public constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContextServices;Ljava/lang/Class;Z)V
    .locals 0

    .line 41
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V

    .line 42
    iput-object p2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedEvent;->serviceClass:Ljava/lang/Class;

    .line 43
    iput-boolean p3, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedEvent;->invalidateRefs:Z

    return-void
.end method


# virtual methods
.method public getServiceClass()Ljava/lang/Class;
    .locals 1

    .line 47
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedEvent;->serviceClass:Ljava/lang/Class;

    return-object v0
.end method

.method public getSourceAsBeanContextServices()Lgroovyjarjaropenbeans/beancontext/BeanContextServices;
    .locals 1

    .line 51
    invoke-super {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;->getBeanContext()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v0

    check-cast v0, Lgroovyjarjaropenbeans/beancontext/BeanContextServices;

    return-object v0
.end method

.method public isCurrentServiceInvalidNow()Z
    .locals 1

    .line 55
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedEvent;->invalidateRefs:Z

    return v0
.end method

.method public isServiceClass(Ljava/lang/Class;)Z
    .locals 1

    .line 59
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedEvent;->serviceClass:Ljava/lang/Class;

    invoke-virtual {v0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
