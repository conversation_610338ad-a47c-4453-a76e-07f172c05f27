.class public Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;
.super Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;
.source "BeanContextSupport.java"

# interfaces
.implements Lgroovyjarjaropenbeans/beancontext/BeanContext;
.implements Lgroovyjarjaropenbeans/PropertyChangeListener;
.implements Lgroovyjarjaropenbeans/VetoableChangeListener;
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSIterator;,
        Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = -0x43b7df0f6e7046f4L


# instance fields
.field protected transient bcmListeners:Ljava/util/ArrayList;

.field protected transient children:Ljava/util/HashMap;

.field protected designTime:Z

.field private transient inNeedsGui:Z

.field protected locale:Ljava/util/Locale;

.field private transient nonSerPCL:Lgroovyjarjaropenbeans/PropertyChangeListener;

.field protected okToUseGui:Z

.field private serializable:I

.field private transient serializing:Z


# direct methods
.method public constructor <init>()V
    .locals 4

    .line 151
    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x1

    invoke-direct {p0, v1, v0, v2, v3}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;Ljava/util/Locale;ZZ)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V
    .locals 3

    .line 161
    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-direct {p0, p1, v0, v1, v2}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;Ljava/util/Locale;ZZ)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;Ljava/util/Locale;)V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 172
    invoke-direct {p0, p1, p2, v0, v1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;Ljava/util/Locale;ZZ)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;Ljava/util/Locale;Z)V
    .locals 1

    const/4 v0, 0x1

    .line 185
    invoke-direct {p0, p1, p2, p3, v0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;Ljava/util/Locale;ZZ)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;Ljava/util/Locale;ZZ)V
    .locals 0

    .line 199
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextChildSupport;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContextChild;)V

    if-nez p2, :cond_0

    .line 201
    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    move-result-object p2

    .line 203
    :cond_0
    iput-object p2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->locale:Ljava/util/Locale;

    .line 204
    iput-boolean p3, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->designTime:Z

    .line 205
    iput-boolean p4, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->okToUseGui:Z

    .line 207
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->initialize()V

    return-void
.end method

.method private addSerializable(Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V
    .locals 1

    .line 1314
    iget-object v0, p1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->child:Ljava/lang/Object;

    instance-of v0, v0, Ljava/io/Serializable;

    if-eqz v0, :cond_1

    iget-object v0, p1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->proxyPeer:Ljava/lang/Object;

    if-eqz v0, :cond_0

    iget-object p1, p1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->proxyPeer:Ljava/lang/Object;

    instance-of p1, p1, Ljava/io/Serializable;

    if-eqz p1, :cond_1

    .line 1316
    :cond_0
    iget p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializable:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializable:I

    :cond_1
    return-void
.end method

.method protected static final classEquals(Ljava/lang/Class;Ljava/lang/Class;)Z
    .locals 0

    if-eqz p0, :cond_2

    if-eqz p1, :cond_2

    if-eq p0, p1, :cond_1

    .line 444
    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0

    :cond_2
    const/4 p0, 0x0

    .line 442
    throw p0
.end method

.method protected static final getChildBeanContextChild(Ljava/lang/Object;)Lgroovyjarjaropenbeans/beancontext/BeanContextChild;
    .locals 3

    .line 600
    instance-of v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    const-string v1, "beans.6C"

    if-eqz v0, :cond_1

    .line 601
    instance-of v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextProxy;

    if-nez v0, :cond_0

    .line 605
    check-cast p0, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    return-object p0

    .line 602
    :cond_0
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 603
    invoke-static {v1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0

    .line 607
    :cond_1
    instance-of v2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextProxy;

    if-eqz v2, :cond_3

    if-nez v0, :cond_2

    .line 612
    check-cast p0, Lgroovyjarjaropenbeans/beancontext/BeanContextProxy;

    invoke-interface {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextProxy;->getBeanContextProxy()Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    move-result-object p0

    return-object p0

    .line 609
    :cond_2
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 610
    invoke-static {v1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0

    :cond_3
    const/4 p0, 0x0

    return-object p0
.end method

.method protected static final getChildBeanContextMembershipListener(Ljava/lang/Object;)Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;
    .locals 1

    .line 627
    instance-of v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;

    if-eqz v0, :cond_0

    .line 628
    check-cast p0, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;

    return-object p0

    :cond_0
    const/4 p0, 0x0

    return-object p0
.end method

.method protected static final getChildPropertyChangeListener(Ljava/lang/Object;)Lgroovyjarjaropenbeans/PropertyChangeListener;
    .locals 1

    .line 644
    instance-of v0, p0, Lgroovyjarjaropenbeans/PropertyChangeListener;

    if-eqz v0, :cond_0

    .line 645
    check-cast p0, Lgroovyjarjaropenbeans/PropertyChangeListener;

    return-object p0

    :cond_0
    const/4 p0, 0x0

    return-object p0
.end method

.method protected static final getChildSerializable(Ljava/lang/Object;)Ljava/io/Serializable;
    .locals 1

    .line 660
    instance-of v0, p0, Ljava/io/Serializable;

    if-eqz v0, :cond_0

    .line 661
    check-cast p0, Ljava/io/Serializable;

    return-object p0

    :cond_0
    const/4 p0, 0x0

    return-object p0
.end method

.method protected static final getChildVetoableChangeListener(Ljava/lang/Object;)Lgroovyjarjaropenbeans/VetoableChangeListener;
    .locals 1

    .line 677
    instance-of v0, p0, Lgroovyjarjaropenbeans/VetoableChangeListener;

    if-eqz v0, :cond_0

    .line 678
    check-cast p0, Lgroovyjarjaropenbeans/VetoableChangeListener;

    return-object p0

    :cond_0
    const/4 p0, 0x0

    return-object p0
.end method

.method protected static final getChildVisibility(Ljava/lang/Object;)Lgroovyjarjaropenbeans/Visibility;
    .locals 1

    .line 693
    instance-of v0, p0, Lgroovyjarjaropenbeans/Visibility;

    if-eqz v0, :cond_0

    .line 694
    check-cast p0, Lgroovyjarjaropenbeans/Visibility;

    return-object p0

    :cond_0
    const/4 p0, 0x0

    return-object p0
.end method

.method private readObject(Ljava/io/ObjectInputStream;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    .line 1296
    invoke-virtual {p1}, Ljava/io/ObjectInputStream;->defaultReadObject()V

    .line 1298
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->initialize()V

    .line 1300
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcsPreDeserializationHook(Ljava/io/ObjectInputStream;)V

    .line 1302
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v0

    if-ne p0, v0, :cond_0

    .line 1303
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->readChildren(Ljava/io/ObjectInputStream;)V

    .line 1306
    :cond_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->deserialize(Ljava/io/ObjectInputStream;Ljava/util/Collection;)V

    return-void
.end method

.method private removeSerializable(Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V
    .locals 1

    .line 1325
    iget v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializable:I

    if-lez v0, :cond_1

    iget-object v0, p1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->child:Ljava/lang/Object;

    instance-of v0, v0, Ljava/io/Serializable;

    if-eqz v0, :cond_1

    iget-object v0, p1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->proxyPeer:Ljava/lang/Object;

    if-eqz v0, :cond_0

    iget-object p1, p1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->proxyPeer:Ljava/lang/Object;

    instance-of p1, p1, Ljava/io/Serializable;

    if-eqz p1, :cond_1

    .line 1328
    :cond_0
    iget p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializable:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializable:I

    :cond_1
    return-void
.end method

.method private writeObject(Ljava/io/ObjectOutputStream;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1254
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    const/4 v1, 0x1

    .line 1255
    iput-boolean v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    .line 1258
    :try_start_0
    invoke-virtual {p1}, Ljava/io/ObjectOutputStream;->defaultWriteObject()V

    .line 1260
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcsPreSerializationHook(Ljava/io/ObjectOutputStream;)V

    .line 1262
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v1

    if-ne p0, v1, :cond_0

    .line 1263
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->writeChildren(Ljava/io/ObjectOutputStream;)V

    .line 1266
    :cond_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    monitor-enter v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 1267
    :try_start_1
    iget-object v2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    invoke-virtual {p0, p1, v2}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serialize(Ljava/io/ObjectOutputStream;Ljava/util/Collection;)V

    .line 1268
    monitor-exit v1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 1270
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    return-void

    :catchall_0
    move-exception p1

    .line 1268
    :try_start_2
    monitor-exit v1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :try_start_3
    throw p1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :catchall_1
    move-exception p1

    .line 1270
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    .line 1271
    throw p1
.end method


# virtual methods
.method public add(Ljava/lang/Object;)Z
    .locals 8

    if-eqz p1, :cond_a

    .line 235
    sget-object v0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->globalHierarchyLock:Ljava/lang/Object;

    monitor-enter v0

    .line 237
    :try_start_0
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->contains(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    .line 238
    monitor-exit v0

    return v2

    .line 242
    :cond_0
    iget-boolean v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    if-nez v1, :cond_9

    .line 248
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->validatePendingAdd(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_8

    .line 255
    instance-of v1, p1, Lgroovyjarjaropenbeans/beancontext/BeanContextProxy;

    const/4 v3, 0x0

    if-eqz v1, :cond_2

    .line 256
    move-object v1, p1

    check-cast v1, Lgroovyjarjaropenbeans/beancontext/BeanContextProxy;

    invoke-interface {v1}, Lgroovyjarjaropenbeans/beancontext/BeanContextProxy;->getBeanContextProxy()Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    move-result-object v1

    if-eqz v1, :cond_1

    goto :goto_0

    .line 258
    :cond_1
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v1, "beans.6A"

    .line 259
    invoke-static {v1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_2
    move-object v1, v3

    .line 262
    :goto_0
    invoke-static {p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getChildBeanContextChild(Ljava/lang/Object;)Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    move-result-object v4

    .line 266
    iget-object v5, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v5
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_4

    .line 267
    :try_start_1
    invoke-virtual {p0, p1, v1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->createBCSChild(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;

    move-result-object v6

    .line 268
    iget-object v7, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v7, p1, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz v1, :cond_3

    .line 270
    invoke-virtual {p0, v1, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->createBCSChild(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;

    move-result-object v3

    .line 271
    iget-object v7, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v7, v1, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 273
    :cond_3
    monitor-exit v5
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_3

    if-eqz v4, :cond_5

    .line 278
    :try_start_2
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v5

    invoke-interface {v4, v5}, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;->setBeanContext(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V
    :try_end_2
    .catch Lgroovyjarjaropenbeans/PropertyVetoException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_4

    :try_start_3
    const-string v5, "beanContext"

    .line 290
    iget-object v7, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->nonSerPCL:Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-interface {v4, v5, v7}, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;->removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    const-string v5, "beanContext"

    .line 293
    iget-object v7, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->nonSerPCL:Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-interface {v4, v5, v7}, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;->addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    goto :goto_1

    .line 280
    :catch_0
    iget-object v2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v2
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_4

    .line 281
    :try_start_4
    iget-object v3, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v3, p1}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz v1, :cond_4

    .line 283
    iget-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 285
    :cond_4
    monitor-exit v2
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    .line 286
    :try_start_5
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v1, "beans.6B"

    .line 287
    invoke-static {v1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p1, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_4

    :catchall_0
    move-exception p1

    .line 285
    :try_start_6
    monitor-exit v2
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_0

    :try_start_7
    throw p1

    .line 298
    :cond_5
    :goto_1
    monitor-enter p1
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_4

    .line 299
    :try_start_8
    invoke-direct {p0, v6}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->addSerializable(Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V

    .line 300
    invoke-virtual {p0, p1, v6}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->childJustAddedHook(Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V

    .line 301
    monitor-exit p1
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_2

    if-eqz v1, :cond_6

    .line 303
    :try_start_9
    monitor-enter v1
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_4

    .line 304
    :try_start_a
    invoke-direct {p0, v3}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->addSerializable(Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V

    .line 305
    invoke-virtual {p0, v1, v3}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->childJustAddedHook(Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V

    .line 306
    monitor-exit v1

    goto :goto_2

    :catchall_1
    move-exception p1

    monitor-exit v1
    :try_end_a
    .catchall {:try_start_a .. :try_end_a} :catchall_1

    :try_start_b
    throw p1

    .line 308
    :cond_6
    :goto_2
    monitor-exit v0
    :try_end_b
    .catchall {:try_start_b .. :try_end_b} :catchall_4

    .line 311
    new-instance v0, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;

    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v3

    const/4 v4, 0x1

    if-nez v1, :cond_7

    new-array v1, v4, [Ljava/lang/Object;

    aput-object p1, v1, v2

    goto :goto_3

    :cond_7
    const/4 v5, 0x2

    new-array v5, v5, [Ljava/lang/Object;

    aput-object p1, v5, v2

    aput-object v1, v5, v4

    move-object v1, v5

    :goto_3
    invoke-direct {v0, v3, v1}, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;[Ljava/lang/Object;)V

    invoke-virtual {p0, v0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->fireChildrenAdded(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;)V

    return v4

    :catchall_2
    move-exception v1

    .line 301
    :try_start_c
    monitor-exit p1
    :try_end_c
    .catchall {:try_start_c .. :try_end_c} :catchall_2

    :try_start_d
    throw v1
    :try_end_d
    .catchall {:try_start_d .. :try_end_d} :catchall_4

    :catchall_3
    move-exception p1

    .line 273
    :try_start_e
    monitor-exit v5
    :try_end_e
    .catchall {:try_start_e .. :try_end_e} :catchall_3

    :try_start_f
    throw p1

    .line 250
    :cond_8
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v1, "beans.69"

    .line 251
    invoke-static {v1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p1, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 243
    :cond_9
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v1, "beans.68"

    .line 244
    invoke-static {v1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p1, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :catchall_4
    move-exception p1

    .line 308
    monitor-exit v0
    :try_end_f
    .catchall {:try_start_f .. :try_end_f} :catchall_4

    throw p1

    .line 230
    :cond_a
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "beans.67"

    invoke-static {v0}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public addAll(Ljava/util/Collection;)Z
    .locals 0

    .line 323
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public addBeanContextMembershipListener(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;)V
    .locals 2

    .line 332
    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    .line 334
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    monitor-enter v0

    .line 335
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    .line 336
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 338
    :cond_0
    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public avoidingGui()Z
    .locals 1

    .line 347
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->needsGui()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->okToUseGui:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method protected bcsChildren()Ljava/util/Iterator;
    .locals 3

    .line 357
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 358
    :try_start_0
    new-instance v1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSIterator;

    iget-object v2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v2}, Ljava/util/HashMap;->values()Ljava/util/Collection;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    invoke-direct {v1, v2}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSIterator;-><init>(Ljava/util/Iterator;)V

    monitor-exit v0

    return-object v1

    :catchall_0
    move-exception v1

    .line 359
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method protected bcsPreDeserializationHook(Ljava/io/ObjectInputStream;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    return-void
.end method

.method protected bcsPreSerializationHook(Ljava/io/ObjectOutputStream;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    return-void
.end method

.method protected childDeserializedHook(Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V
    .locals 0

    return-void
.end method

.method protected childJustAddedHook(Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V
    .locals 0

    return-void
.end method

.method protected childJustRemovedHook(Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V
    .locals 0

    return-void
.end method

.method public clear()V
    .locals 1

    .line 453
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method

.method public contains(Ljava/lang/Object;)Z
    .locals 2

    .line 464
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 465
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v1, p1}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    monitor-exit v0

    return p1

    :catchall_0
    move-exception p1

    .line 466
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public containsAll(Ljava/util/Collection;)Z
    .locals 2

    .line 478
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 479
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v1}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1, p1}, Ljava/util/Set;->containsAll(Ljava/util/Collection;)Z

    move-result p1

    monitor-exit v0

    return p1

    :catchall_0
    move-exception p1

    .line 480
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public containsKey(Ljava/lang/Object;)Z
    .locals 2

    .line 490
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 491
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v1, p1}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    monitor-exit v0

    return p1

    :catchall_0
    move-exception p1

    .line 492
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method protected final copyChildren()[Ljava/lang/Object;
    .locals 2

    .line 501
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 502
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v1}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->toArray()[Ljava/lang/Object;

    move-result-object v1

    monitor-exit v0

    return-object v1

    :catchall_0
    move-exception v1

    .line 503
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method protected createBCSChild(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;
    .locals 1

    .line 514
    new-instance v0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;

    invoke-direct {v0, p0, p1, p2}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v0
.end method

.method protected final deserialize(Ljava/io/ObjectInputStream;Ljava/util/Collection;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    .line 531
    invoke-virtual {p1}, Ljava/io/ObjectInputStream;->readInt()I

    move-result v0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    .line 533
    invoke-virtual {p1}, Ljava/io/ObjectInputStream;->readObject()Ljava/lang/Object;

    move-result-object v2

    invoke-interface {p2, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public dontUseGui()V
    .locals 1

    const/4 v0, 0x0

    .line 541
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->okToUseGui:Z

    return-void
.end method

.method protected final fireChildrenAdded(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;)V
    .locals 3

    .line 552
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    monitor-enter v0

    .line 553
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->toArray()[Ljava/lang/Object;

    move-result-object v1

    .line 554
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v0, 0x0

    .line 555
    :goto_0
    array-length v2, v1

    if-ge v0, v2, :cond_0

    .line 556
    aget-object v2, v1, v0

    check-cast v2, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;

    .line 557
    invoke-interface {v2, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;->childrenAdded(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void

    :catchall_0
    move-exception p1

    .line 554
    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method protected final fireChildrenRemoved(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;)V
    .locals 3

    .line 569
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    monitor-enter v0

    .line 570
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->toArray()[Ljava/lang/Object;

    move-result-object v1

    .line 571
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v0, 0x0

    .line 572
    :goto_0
    array-length v2, v1

    if-ge v0, v2, :cond_0

    .line 573
    aget-object v2, v1, v0

    check-cast v2, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;

    .line 574
    invoke-interface {v2, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;->childrenRemoved(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void

    :catchall_0
    move-exception p1

    .line 571
    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;
    .locals 1

    .line 584
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    check-cast v0, Lgroovyjarjaropenbeans/beancontext/BeanContext;

    return-object v0
.end method

.method public getLocale()Ljava/util/Locale;
    .locals 1

    .line 706
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->locale:Ljava/util/Locale;

    return-object v0
.end method

.method public getResource(Ljava/lang/String;Lgroovyjarjaropenbeans/beancontext/BeanContextChild;)Ljava/net/URL;
    .locals 0

    if-eqz p1, :cond_1

    if-eqz p2, :cond_1

    .line 716
    invoke-virtual {p0, p2}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->contains(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_0

    .line 720
    invoke-static {p1}, Ljava/lang/ClassLoader;->getSystemResource(Ljava/lang/String;)Ljava/net/URL;

    move-result-object p1

    return-object p1

    .line 717
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "beans.6D"

    invoke-static {p2}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    const/4 p1, 0x0

    .line 714
    throw p1
.end method

.method public getResourceAsStream(Ljava/lang/String;Lgroovyjarjaropenbeans/beancontext/BeanContextChild;)Ljava/io/InputStream;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalArgumentException;
        }
    .end annotation

    if-eqz p1, :cond_1

    if-eqz p2, :cond_1

    .line 731
    invoke-virtual {p0, p2}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->contains(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_0

    .line 735
    invoke-static {p1}, Ljava/lang/ClassLoader;->getSystemResourceAsStream(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object p1

    return-object p1

    .line 732
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "beans.6D"

    invoke-static {p2}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    const/4 p1, 0x0

    .line 729
    throw p1
.end method

.method protected initialize()V
    .locals 1

    .line 744
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    .line 745
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    const/4 v0, 0x0

    .line 746
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    .line 747
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->inNeedsGui:Z

    .line 748
    new-instance v0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$1;

    invoke-direct {v0, p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$1;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;)V

    iput-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->nonSerPCL:Lgroovyjarjaropenbeans/PropertyChangeListener;

    return-void
.end method

.method public instantiateChild(Ljava/lang/String;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    .line 760
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    .line 761
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v1

    .line 760
    invoke-static {v0, p1, v1}, Lgroovyjarjaropenbeans/Beans;->instantiate(Ljava/lang/ClassLoader;Ljava/lang/String;Lgroovyjarjaropenbeans/beancontext/BeanContext;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public isDesignTime()Z
    .locals 1

    .line 768
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->designTime:Z

    return v0
.end method

.method public isEmpty()Z
    .locals 2

    .line 775
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 776
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v1}, Ljava/util/HashMap;->isEmpty()Z

    move-result v1

    monitor-exit v0

    return v1

    :catchall_0
    move-exception v1

    .line 777
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public isSerializing()Z
    .locals 1

    .line 788
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    return v0
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 3

    .line 798
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 799
    :try_start_0
    new-instance v1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSIterator;

    iget-object v2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v2}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    invoke-direct {v1, v2}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSIterator;-><init>(Ljava/util/Iterator;)V

    monitor-exit v0

    return-object v1

    :catchall_0
    move-exception v1

    .line 800
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public needsGui()Z
    .locals 5

    .line 814
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->inNeedsGui:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    :cond_0
    const/4 v0, 0x1

    .line 817
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->inNeedsGui:Z

    .line 820
    :try_start_0
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v2

    if-eq v2, p0, :cond_1

    .line 821
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjaropenbeans/beancontext/BeanContext;->needsGui()Z

    move-result v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v2, :cond_1

    .line 837
    iput-boolean v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->inNeedsGui:Z

    return v0

    .line 825
    :cond_1
    :try_start_1
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->copyChildren()[Ljava/lang/Object;

    move-result-object v2

    move v3, v1

    .line 826
    :goto_0
    array-length v4, v2

    if-ge v3, v4, :cond_4

    .line 827
    aget-object v4, v2, v3

    instance-of v4, v4, Ljava/awt/Component;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-eqz v4, :cond_2

    .line 837
    iput-boolean v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->inNeedsGui:Z

    return v0

    .line 830
    :cond_2
    :try_start_2
    aget-object v4, v2, v3

    invoke-static {v4}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getChildVisibility(Ljava/lang/Object;)Lgroovyjarjaropenbeans/Visibility;

    move-result-object v4

    if-eqz v4, :cond_3

    .line 831
    invoke-interface {v4}, Lgroovyjarjaropenbeans/Visibility;->needsGui()Z

    move-result v4
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    if-eqz v4, :cond_3

    .line 837
    iput-boolean v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->inNeedsGui:Z

    return v0

    :cond_3
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_4
    iput-boolean v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->inNeedsGui:Z

    return v1

    :catchall_0
    move-exception v0

    iput-boolean v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->inNeedsGui:Z

    .line 838
    throw v0
.end method

.method public okToUseGui()V
    .locals 1

    const/4 v0, 0x1

    .line 845
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->okToUseGui:Z

    return-void
.end method

.method public propertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V
    .locals 2

    .line 852
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;->getSource()Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 853
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;->getPropertyName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "beanContext"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 854
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;->getOldValue()Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v1

    if-ne v0, v1, :cond_0

    .line 855
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;->getSource()Ljava/lang/Object;

    move-result-object p1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->remove(Ljava/lang/Object;Z)Z

    :cond_0
    return-void
.end method

.method public final readChildren(Ljava/io/ObjectInputStream;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    .line 886
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    const/4 v1, 0x0

    .line 887
    :goto_0
    :try_start_0
    iget v2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializable:I

    if-ge v1, v2, :cond_1

    .line 888
    invoke-virtual {p1}, Ljava/io/ObjectInputStream;->readObject()Ljava/lang/Object;

    move-result-object v2

    .line 889
    invoke-virtual {p1}, Ljava/io/ObjectInputStream;->readObject()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;

    .line 890
    iget-object v4, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v4, v2, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 892
    invoke-virtual {p0, v2, v3}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->childDeserializedHook(Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V

    .line 895
    invoke-static {v2}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getChildBeanContextChild(Ljava/lang/Object;)Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    move-result-object v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v2, :cond_0

    .line 898
    :try_start_1
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v3

    invoke-interface {v2, v3}, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;->setBeanContext(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V
    :try_end_1
    .catch Lgroovyjarjaropenbeans/PropertyVetoException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :try_start_2
    const-string v3, "beanContext"

    .line 904
    iget-object v4, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->nonSerPCL:Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-interface {v2, v3, v4}, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;->removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    const-string v3, "beanContext"

    .line 907
    iget-object v4, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->nonSerPCL:Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-interface {v2, v3, v4}, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;->addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    goto :goto_1

    .line 900
    :catch_0
    new-instance p1, Ljava/io/IOException;

    const-string v1, "beans.6B"

    .line 901
    invoke-static {v1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p1, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_0
    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 911
    :cond_1
    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    throw p1
.end method

.method public remove(Ljava/lang/Object;)Z
    .locals 1

    const/4 v0, 0x1

    .line 926
    invoke-virtual {p0, p1, v0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->remove(Ljava/lang/Object;Z)Z

    move-result p1

    return p1
.end method

.method protected remove(Ljava/lang/Object;Z)Z
    .locals 5

    if-eqz p1, :cond_7

    .line 952
    sget-object v0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->globalHierarchyLock:Ljava/lang/Object;

    monitor-enter v0

    .line 954
    :try_start_0
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->contains(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x0

    if-nez v1, :cond_0

    .line 955
    monitor-exit v0

    return v2

    .line 959
    :cond_0
    iget-boolean v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    if-nez v1, :cond_6

    .line 965
    invoke-virtual {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->validatePendingRemove(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_5

    .line 972
    invoke-static {p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getChildBeanContextChild(Ljava/lang/Object;)Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    move-result-object v1

    const/4 v3, 0x0

    if-eqz v1, :cond_1

    if-eqz p2, :cond_1

    const-string p2, "beanContext"

    .line 975
    iget-object v4, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->nonSerPCL:Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-interface {v1, p2, v4}, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;->removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_3

    .line 978
    :try_start_1
    invoke-interface {v1, v3}, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;->setBeanContext(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V
    :try_end_1
    .catch Lgroovyjarjaropenbeans/PropertyVetoException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_3

    goto :goto_0

    :catch_0
    :try_start_2
    const-string p1, "beanContext"

    .line 981
    iget-object p2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->nonSerPCL:Lgroovyjarjaropenbeans/PropertyChangeListener;

    invoke-interface {v1, p1, p2}, Lgroovyjarjaropenbeans/beancontext/BeanContextChild;->addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    .line 983
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "beans.6B"

    .line 984
    invoke-static {p2}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 990
    :cond_1
    :goto_0
    iget-object p2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter p2
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_3

    .line 991
    :try_start_3
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v1, p1}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;

    .line 992
    iget-object v4, v1, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->proxyPeer:Ljava/lang/Object;

    if-eqz v4, :cond_2

    .line 994
    iget-object v3, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v3, v4}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;

    .line 996
    :cond_2
    monitor-exit p2
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    .line 999
    :try_start_4
    monitor-enter p1
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_3

    .line 1000
    :try_start_5
    invoke-direct {p0, v1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->removeSerializable(Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V

    .line 1001
    invoke-virtual {p0, p1, v1}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->childJustRemovedHook(Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V

    .line 1002
    monitor-exit p1
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    if-eqz v4, :cond_3

    .line 1004
    :try_start_6
    monitor-enter v4
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_3

    .line 1005
    :try_start_7
    invoke-direct {p0, v3}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->removeSerializable(Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V

    .line 1006
    invoke-virtual {p0, v4, v3}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->childJustRemovedHook(Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;)V

    .line 1007
    monitor-exit v4

    goto :goto_1

    :catchall_0
    move-exception p1

    monitor-exit v4
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_0

    :try_start_8
    throw p1

    .line 1009
    :cond_3
    :goto_1
    monitor-exit v0
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_3

    .line 1012
    new-instance p2, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;

    .line 1013
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->getBeanContextPeer()Lgroovyjarjaropenbeans/beancontext/BeanContext;

    move-result-object v0

    const/4 v1, 0x1

    if-nez v4, :cond_4

    new-array v3, v1, [Ljava/lang/Object;

    aput-object p1, v3, v2

    goto :goto_2

    :cond_4
    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p1, v3, v2

    aput-object v4, v3, v1

    :goto_2
    invoke-direct {p2, v0, v3}, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;[Ljava/lang/Object;)V

    .line 1012
    invoke-virtual {p0, p2}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->fireChildrenRemoved(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;)V

    return v1

    :catchall_1
    move-exception p2

    .line 1002
    :try_start_9
    monitor-exit p1
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_1

    :try_start_a
    throw p2
    :try_end_a
    .catchall {:try_start_a .. :try_end_a} :catchall_3

    :catchall_2
    move-exception p1

    .line 996
    :try_start_b
    monitor-exit p2
    :try_end_b
    .catchall {:try_start_b .. :try_end_b} :catchall_2

    :try_start_c
    throw p1

    .line 967
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "beans.6E"

    .line 968
    invoke-static {p2}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 960
    :cond_6
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "beans.68"

    .line 961
    invoke-static {p2}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :catchall_3
    move-exception p1

    .line 1009
    monitor-exit v0
    :try_end_c
    .catchall {:try_start_c .. :try_end_c} :catchall_3

    throw p1

    .line 947
    :cond_7
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "beans.67"

    invoke-static {p2}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public removeAll(Ljava/util/Collection;)Z
    .locals 0

    .line 1024
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public removeBeanContextMembershipListener(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;)V
    .locals 2

    .line 1033
    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    .line 1035
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    monitor-enter v0

    .line 1036
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->bcmListeners:Ljava/util/ArrayList;

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 1037
    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public retainAll(Ljava/util/Collection;)Z
    .locals 0

    .line 1046
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method protected final serialize(Ljava/io/ObjectOutputStream;Ljava/util/Collection;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1062
    invoke-interface {p2}, Ljava/util/Collection;->toArray()[Ljava/lang/Object;

    move-result-object p2

    const/4 v0, 0x0

    move v1, v0

    move v2, v1

    .line 1064
    :goto_0
    array-length v3, p2

    if-ge v1, v3, :cond_1

    .line 1065
    aget-object v3, p2, v1

    instance-of v3, v3, Ljava/io/Serializable;

    if-eqz v3, :cond_0

    add-int/lit8 v2, v2, 0x1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 1070
    :cond_1
    invoke-virtual {p1, v2}, Ljava/io/ObjectOutputStream;->writeInt(I)V

    .line 1071
    :goto_1
    array-length v1, p2

    if-ge v0, v1, :cond_3

    .line 1072
    aget-object v1, p2, v0

    instance-of v1, v1, Ljava/io/Serializable;

    if-eqz v1, :cond_2

    .line 1073
    aget-object v1, p2, v0

    invoke-virtual {p1, v1}, Ljava/io/ObjectOutputStream;->writeObject(Ljava/lang/Object;)V

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_3
    return-void
.end method

.method public setDesignTime(Z)V
    .locals 0

    .line 1082
    iput-boolean p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->designTime:Z

    return-void
.end method

.method public setLocale(Ljava/util/Locale;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/PropertyVetoException;
        }
    .end annotation

    if-eqz p1, :cond_1

    .line 1093
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->locale:Ljava/util/Locale;

    if-ne p1, v0, :cond_0

    goto :goto_0

    .line 1097
    :cond_0
    new-instance v0, Lgroovyjarjaropenbeans/PropertyChangeEvent;

    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->beanContextChildPeer:Lgroovyjarjaropenbeans/beancontext/BeanContextChild;

    iget-object v2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->locale:Ljava/util/Locale;

    const-string v3, "locale"

    invoke-direct {v0, v1, v3, v2, p1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 1101
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->locale:Ljava/util/Locale;

    .line 1102
    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->locale:Ljava/util/Locale;

    .line 1106
    :try_start_0
    iget-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->vcSupport:Lgroovyjarjaropenbeans/VetoableChangeSupport;

    invoke-virtual {p1, v0}, Lgroovyjarjaropenbeans/VetoableChangeSupport;->fireVetoableChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V
    :try_end_0
    .catch Lgroovyjarjaropenbeans/PropertyVetoException; {:try_start_0 .. :try_end_0} :catch_0

    .line 1113
    iget-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->pcSupport:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {p1, v0}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->firePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void

    :catch_0
    move-exception p1

    .line 1109
    iput-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->locale:Ljava/util/Locale;

    .line 1110
    throw p1

    :cond_1
    :goto_0
    return-void
.end method

.method public size()I
    .locals 2

    .line 1123
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 1124
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v1}, Ljava/util/HashMap;->size()I

    move-result v1

    monitor-exit v0

    return v1

    :catchall_0
    move-exception v1

    .line 1125
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public toArray()[Ljava/lang/Object;
    .locals 2

    .line 1135
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 1136
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v1}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->toArray()[Ljava/lang/Object;

    move-result-object v1

    monitor-exit v0

    return-object v1

    :catchall_0
    move-exception v1

    .line 1137
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public toArray([Ljava/lang/Object;)[Ljava/lang/Object;
    .locals 2

    .line 1148
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v0

    .line 1149
    :try_start_0
    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v1}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1, p1}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    monitor-exit v0

    return-object p1

    :catchall_0
    move-exception p1

    .line 1150
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method protected validatePendingAdd(Ljava/lang/Object;)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method protected validatePendingRemove(Ljava/lang/Object;)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method public vetoableChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/PropertyVetoException;
        }
    .end annotation

    if-eqz p1, :cond_0

    return-void

    .line 1185
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v0, "beans.1C"

    invoke-static {v0}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final writeChildren(Ljava/io/ObjectOutputStream;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1206
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    const/4 v1, 0x1

    .line 1207
    iput-boolean v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    const/4 v1, 0x0

    .line 1211
    :try_start_0
    iget-object v2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    monitor-enter v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 1212
    :try_start_1
    iget-object v3, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->children:Ljava/util/HashMap;

    invoke-virtual {v3}, Ljava/util/HashMap;->values()Ljava/util/Collection;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v3

    .line 1213
    :cond_0
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_2

    .line 1214
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;

    .line 1215
    iget-object v5, v4, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->child:Ljava/lang/Object;

    instance-of v5, v5, Ljava/io/Serializable;

    if-eqz v5, :cond_0

    iget-object v5, v4, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->proxyPeer:Ljava/lang/Object;

    if-eqz v5, :cond_1

    iget-object v5, v4, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->proxyPeer:Ljava/lang/Object;

    instance-of v5, v5, Ljava/io/Serializable;

    if-eqz v5, :cond_0

    .line 1217
    :cond_1
    iget-object v5, v4, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;->child:Ljava/lang/Object;

    invoke-virtual {p1, v5}, Ljava/io/ObjectOutputStream;->writeObject(Ljava/lang/Object;)V

    .line 1218
    invoke-virtual {p1, v4}, Ljava/io/ObjectOutputStream;->writeObject(Ljava/lang/Object;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 1222
    :cond_2
    monitor-exit v2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 1225
    :try_start_2
    iget p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializable:I
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    if-ne v1, p1, :cond_3

    .line 1229
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    return-void

    .line 1226
    :cond_3
    :try_start_3
    new-instance p1, Ljava/io/IOException;

    const-string v1, "beans.6F"

    invoke-static {v1}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p1, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :catchall_0
    move-exception p1

    .line 1222
    :try_start_4
    monitor-exit v2
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    :try_start_5
    throw p1
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    :catchall_1
    move-exception p1

    .line 1229
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;->serializing:Z

    .line 1230
    throw p1
.end method
