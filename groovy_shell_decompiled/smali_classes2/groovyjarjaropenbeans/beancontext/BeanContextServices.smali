.class public interface abstract Lgroovyjarjaropenbeans/beancontext/BeanContextServices;
.super Ljava/lang/Object;
.source "BeanContextServices.java"

# interfaces
.implements Lgroovyjarjaropenbeans/beancontext/BeanContext;
.implements Lgroovyjarjaropenbeans/beancontext/BeanContextServicesListener;


# virtual methods
.method public abstract addBeanContextServicesListener(Lgroovyjarjaropenbeans/beancontext/BeanContextServicesListener;)V
.end method

.method public abstract addService(Ljava/lang/Class;Lgroovyjarjaropenbeans/beancontext/BeanContextServiceProvider;)Z
.end method

.method public abstract getCurrentServiceClasses()Ljava/util/Iterator;
.end method

.method public abstract getCurrentServiceSelectors(Ljava/lang/Class;)Ljava/util/Iterator;
.end method

.method public abstract getService(Lgroovyjarjaropenbeans/beancontext/BeanContextChild;Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Lgroovyjarjaropenbeans/beancontext/BeanContextServiceRevokedListener;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/util/TooManyListenersException;
        }
    .end annotation
.end method

.method public abstract hasService(Ljava/lang/Class;)Z
.end method

.method public abstract releaseService(Lgroovyjarjaropenbeans/beancontext/BeanContextChild;Ljava/lang/Object;Ljava/lang/Object;)V
.end method

.method public abstract removeBeanContextServicesListener(Lgroovyjarjaropenbeans/beancontext/BeanContextServicesListener;)V
.end method

.method public abstract revokeService(Ljava/lang/Class;Lgroovyjarjaropenbeans/beancontext/BeanContextServiceProvider;Z)V
.end method
