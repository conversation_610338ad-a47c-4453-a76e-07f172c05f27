.class public Lgroovyjarjaropenbeans/beancontext/BeanContextServiceAvailableEvent;
.super Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;
.source "BeanContextServiceAvailableEvent.java"


# static fields
.field private static final serialVersionUID:J = -0x4a061fcd62fc778aL


# instance fields
.field protected serviceClass:Ljava/lang/Class;


# direct methods
.method public constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContextServices;Ljava/lang/Class;)V
    .locals 0

    .line 34
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V

    .line 35
    iput-object p2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServiceAvailableEvent;->serviceClass:Ljava/lang/Class;

    return-void
.end method


# virtual methods
.method public getCurrentServiceSelectors()Ljava/util/Iterator;
    .locals 2

    .line 39
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;->source:Ljava/lang/Object;

    check-cast v0, Lgroovyjarjaropenbeans/beancontext/BeanContextServices;

    iget-object v1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServiceAvailableEvent;->serviceClass:Ljava/lang/Class;

    .line 40
    invoke-interface {v0, v1}, Lgroovyjarjaropenbeans/beancontext/BeanContextServices;->getCurrentServiceSelectors(Ljava/lang/Class;)Ljava/util/Iterator;

    move-result-object v0

    return-object v0
.end method

.method public getServiceClass()Ljava/lang/Class;
    .locals 1

    .line 44
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServiceAvailableEvent;->serviceClass:Ljava/lang/Class;

    return-object v0
.end method

.method public getSourceAsBeanContextServices()Lgroovyjarjaropenbeans/beancontext/BeanContextServices;
    .locals 1

    .line 48
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;->source:Ljava/lang/Object;

    check-cast v0, Lgroovyjarjaropenbeans/beancontext/BeanContextServices;

    return-object v0
.end method
