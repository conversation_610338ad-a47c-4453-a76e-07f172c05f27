.class public Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSChild;
.super Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;
.source "BeanContextServicesSupport.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "BCSSChild"
.end annotation


# static fields
.field private static final serialVersionUID:J = -0x2d4b877892d32d79L


# instance fields
.field transient serviceRecords:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$ServiceRecord;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic this$0:Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;


# direct methods
.method constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0

    .line 99
    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport$BCSSChild;->this$0:Lgroovyjarjaropenbeans/beancontext/BeanContextServicesSupport;

    .line 100
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjaropenbeans/beancontext/BeanContextSupport$BCSChild;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContextSupport;Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method
