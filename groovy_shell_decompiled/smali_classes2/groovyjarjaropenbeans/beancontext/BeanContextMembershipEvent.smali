.class public Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;
.super Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;
.source "BeanContextMembershipEvent.java"


# static fields
.field private static final serialVersionUID:J = 0x30902d1c5e7effefL


# instance fields
.field protected children:Ljava/util/Collection;


# direct methods
.method public constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;Ljava/util/Collection;)V
    .locals 0

    .line 35
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V

    if-eqz p2, :cond_0

    .line 41
    iput-object p2, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;->children:Ljava/util/Collection;

    return-void

    .line 38
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "beans.0E"

    invoke-static {p2}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public constructor <init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;[Ljava/lang/Object;)V
    .locals 0

    .line 45
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/beancontext/BeanContextEvent;-><init>(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V

    if-eqz p2, :cond_0

    .line 52
    invoke-static {p2}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;->children:Ljava/util/Collection;

    return-void

    .line 48
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "beans.0E"

    invoke-static {p2}, Lgroovyjarjarharmonybeans/internal/nls/Messages;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method public contains(Ljava/lang/Object;)Z
    .locals 1

    .line 56
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;->children:Ljava/util/Collection;

    invoke-interface {v0, p1}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 1

    .line 60
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;->children:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    return-object v0
.end method

.method public size()I
    .locals 1

    .line 64
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;->children:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->size()I

    move-result v0

    return v0
.end method

.method public toArray()[Ljava/lang/Object;
    .locals 1

    .line 68
    iget-object v0, p0, Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;->children:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->toArray()[Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
