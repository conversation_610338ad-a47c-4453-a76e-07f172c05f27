.class public interface abstract Lgroovyjarjaropenbeans/beancontext/BeanContextChild;
.super Ljava/lang/Object;
.source "BeanContextChild.java"


# virtual methods
.method public abstract addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
.end method

.method public abstract addVetoableChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/VetoableChangeListener;)V
.end method

.method public abstract getBeanContext()Lgroovyjarjaropenbeans/beancontext/BeanContext;
.end method

.method public abstract removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
.end method

.method public abstract removeVetoableChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/VetoableChangeListener;)V
.end method

.method public abstract setBeanContext(Lgroovyjarjaropenbeans/beancontext/BeanContext;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/PropertyVetoException;
        }
    .end annotation
.end method
