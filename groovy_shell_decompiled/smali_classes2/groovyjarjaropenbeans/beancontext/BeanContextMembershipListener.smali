.class public interface abstract Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipListener;
.super Ljava/lang/Object;
.source "BeanContextMembershipListener.java"

# interfaces
.implements Ljava/util/EventListener;


# virtual methods
.method public abstract childrenAdded(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;)V
.end method

.method public abstract childrenRemoved(Lgroovyjarjaropenbeans/beancontext/BeanContextMembershipEvent;)V
.end method
