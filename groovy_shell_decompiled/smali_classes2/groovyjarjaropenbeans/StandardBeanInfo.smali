.class Lgroovyjarjaropenbeans/StandardBeanInfo;
.super Lgroovyjarjaropenbeans/SimpleBeanInfo;
.source "StandardBeanInfo.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjaropenbeans/StandardBeanInfo$PropertyComparator;
    }
.end annotation


# static fields
.field private static final PREFIX_ADD:Ljava/lang/String; = "add"

.field private static final PREFIX_GET:Ljava/lang/String; = "get"

.field private static final PREFIX_IS:Ljava/lang/String; = "is"

.field private static final PREFIX_REMOVE:Ljava/lang/String; = "remove"

.field private static final PREFIX_SET:Ljava/lang/String; = "set"

.field private static final STR_GETTERS:Ljava/lang/String; = "getters"

.field private static final STR_INDEXED:Ljava/lang/String; = "indexed"

.field private static final STR_INVALID:Ljava/lang/String; = "invalid"

.field private static final STR_IS_CONSTRAINED:Ljava/lang/String; = "isConstrained"

.field private static final STR_NORMAL:Ljava/lang/String; = "normal"

.field private static final STR_PROPERTY_TYPE:Ljava/lang/String; = "PropertyType"

.field private static final STR_SETTERS:Ljava/lang/String; = "setters"

.field private static final STR_VALID:Ljava/lang/String; = "valid"

.field private static final SUFFIX_LISTEN:Ljava/lang/String; = "Listener"

.field private static comparator:Lgroovyjarjaropenbeans/StandardBeanInfo$PropertyComparator;


# instance fields
.field additionalBeanInfo:[Lgroovyjarjaropenbeans/BeanInfo;

.field private beanClass:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end field

.field private beanDescriptor:Lgroovyjarjaropenbeans/BeanDescriptor;

.field private canAddPropertyChangeListener:Z

.field private canRemovePropertyChangeListener:Z

.field private defaultEventIndex:I

.field private defaultPropertyIndex:I

.field private events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

.field private explicitBeanInfo:Lgroovyjarjaropenbeans/BeanInfo;

.field private explicitEvents:Z

.field private explicitMethods:Z

.field private explicitProperties:Z

.field private icon:[Ljava/lang/Object;

.field private methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

.field private properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 100
    new-instance v0, Lgroovyjarjaropenbeans/StandardBeanInfo$PropertyComparator;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovyjarjaropenbeans/StandardBeanInfo$PropertyComparator;-><init>(Lgroovyjarjaropenbeans/StandardBeanInfo$1;)V

    sput-object v0, Lgroovyjarjaropenbeans/StandardBeanInfo;->comparator:Lgroovyjarjaropenbeans/StandardBeanInfo$PropertyComparator;

    return-void
.end method

.method constructor <init>(Ljava/lang/Class;Lgroovyjarjaropenbeans/BeanInfo;Ljava/lang/Class;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;",
            "Lgroovyjarjaropenbeans/BeanInfo;",
            "Ljava/lang/Class<",
            "*>;)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/IntrospectionException;
        }
    .end annotation

    .line 109
    invoke-direct {p0}, Lgroovyjarjaropenbeans/SimpleBeanInfo;-><init>()V

    const/4 v0, 0x0

    .line 76
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitMethods:Z

    .line 78
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitProperties:Z

    .line 80
    iput-boolean v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitEvents:Z

    const/4 v1, 0x0

    .line 82
    iput-object v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitBeanInfo:Lgroovyjarjaropenbeans/BeanInfo;

    .line 84
    iput-object v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    .line 86
    iput-object v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    .line 88
    iput-object v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    .line 90
    iput-object v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanDescriptor:Lgroovyjarjaropenbeans/BeanDescriptor;

    .line 92
    iput-object v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->additionalBeanInfo:[Lgroovyjarjaropenbeans/BeanInfo;

    const/4 v1, -0x1

    .line 96
    iput v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultEventIndex:I

    .line 98
    iput v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultPropertyIndex:I

    const/4 v2, 0x4

    new-array v3, v2, [Ljava/lang/Object;

    .line 102
    iput-object v3, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->icon:[Ljava/lang/Object;

    .line 110
    iput-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanClass:Ljava/lang/Class;

    if-eqz p2, :cond_7

    .line 129
    iput-object p2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitBeanInfo:Lgroovyjarjaropenbeans/BeanInfo;

    .line 130
    invoke-interface {p2}, Lgroovyjarjaropenbeans/BeanInfo;->getEventSetDescriptors()[Lgroovyjarjaropenbeans/EventSetDescriptor;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    .line 131
    invoke-interface {p2}, Lgroovyjarjaropenbeans/BeanInfo;->getMethodDescriptors()[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    .line 132
    invoke-interface {p2}, Lgroovyjarjaropenbeans/BeanInfo;->getPropertyDescriptors()[Lgroovyjarjaropenbeans/PropertyDescriptor;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    .line 133
    invoke-interface {p2}, Lgroovyjarjaropenbeans/BeanInfo;->getDefaultEventIndex()I

    move-result p1

    iput p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultEventIndex:I

    if-ltz p1, :cond_0

    .line 134
    iget-object v3, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    array-length v3, v3

    if-lt p1, v3, :cond_1

    .line 135
    :cond_0
    iput v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultEventIndex:I

    .line 137
    :cond_1
    invoke-interface {p2}, Lgroovyjarjaropenbeans/BeanInfo;->getDefaultPropertyIndex()I

    move-result p1

    iput p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultPropertyIndex:I

    if-ltz p1, :cond_2

    .line 138
    iget-object v3, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    array-length v3, v3

    if-lt p1, v3, :cond_3

    .line 140
    :cond_2
    iput v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultPropertyIndex:I

    .line 142
    :cond_3
    invoke-interface {p2}, Lgroovyjarjaropenbeans/BeanInfo;->getAdditionalBeanInfo()[Lgroovyjarjaropenbeans/BeanInfo;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->additionalBeanInfo:[Lgroovyjarjaropenbeans/BeanInfo;

    :goto_0
    if-ge v0, v2, :cond_4

    .line 144
    iget-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->icon:[Ljava/lang/Object;

    add-int/lit8 v1, v0, 0x1

    invoke-interface {p2, v1}, Lgroovyjarjaropenbeans/BeanInfo;->getIcon(I)Ljava/awt/Image;

    move-result-object v3

    aput-object v3, p1, v0

    move v0, v1

    goto :goto_0

    .line 147
    :cond_4
    iget-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    const/4 p2, 0x1

    if-eqz p1, :cond_5

    .line 148
    iput-boolean p2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitEvents:Z

    .line 149
    :cond_5
    iget-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    if-eqz p1, :cond_6

    .line 150
    iput-boolean p2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitMethods:Z

    .line 151
    :cond_6
    iget-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    if-eqz p1, :cond_7

    .line 152
    iput-boolean p2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitProperties:Z

    .line 155
    :cond_7
    iget-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    if-nez p1, :cond_8

    .line 156
    invoke-direct {p0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectMethods()[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    .line 159
    :cond_8
    iget-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    if-nez p1, :cond_9

    .line 160
    invoke-direct {p0, p3}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectProperties(Ljava/lang/Class;)[Lgroovyjarjaropenbeans/PropertyDescriptor;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    .line 163
    :cond_9
    iget-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    if-nez p1, :cond_a

    .line 164
    invoke-direct {p0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectEvents()[Lgroovyjarjaropenbeans/EventSetDescriptor;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    :cond_a
    return-void
.end method

.method private capitalize(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 502
    :cond_0
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    if-eqz v0, :cond_2

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x1

    if-le v0, v1, :cond_1

    invoke-virtual {p1, v1}, Ljava/lang/String;->charAt(I)C

    move-result v0

    invoke-static {v0}, Ljava/lang/Character;->isUpperCase(C)Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    .line 506
    :cond_1
    invoke-virtual {p1}, Ljava/lang/String;->toCharArray()[C

    move-result-object p1

    const/4 v0, 0x0

    .line 507
    aget-char v1, p1, v0

    invoke-static {v1}, Ljava/lang/Character;->toUpperCase(C)C

    move-result v1

    aput-char v1, p1, v0

    .line 508
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0, p1}, Ljava/lang/String;-><init>([C)V

    return-object v0

    :cond_2
    :goto_0
    return-object p1
.end method

.method private fixGetSet(Ljava/util/HashMap;)V
    .locals 20
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/util/HashMap;",
            ">;)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/IntrospectionException;
        }
    .end annotation

    if-nez p1, :cond_0

    return-void

    .line 974
    :cond_0
    invoke-virtual/range {p1 .. p1}, Ljava/util/HashMap;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_32

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 975
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/HashMap;

    const-string v2, "getters"

    .line 977
    invoke-virtual {v1, v2}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/ArrayList;

    const-string v3, "setters"

    .line 979
    invoke-virtual {v1, v3}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/ArrayList;

    if-nez v2, :cond_1

    .line 990
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    :cond_1
    if-nez v3, :cond_2

    .line 994
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 1000
    :cond_2
    invoke-virtual {v2}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v2

    const/4 v4, 0x0

    move-object v5, v4

    move-object v6, v5

    :cond_3
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    const-string v8, "is"

    const/4 v9, 0x0

    const/4 v10, 0x1

    const-string v11, "get"

    if-eqz v7, :cond_8

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/reflect/Method;

    .line 1001
    invoke-virtual {v7}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v12

    .line 1002
    invoke-virtual {v7}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v13

    if-eqz v12, :cond_4

    .line 1004
    array-length v14, v12

    if-nez v14, :cond_6

    :cond_4
    if-eqz v5, :cond_5

    .line 1007
    invoke-virtual {v13, v8}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v14

    if-eqz v14, :cond_6

    :cond_5
    move-object v5, v7

    :cond_6
    if-eqz v12, :cond_3

    .line 1013
    array-length v14, v12

    if-ne v14, v10, :cond_3

    aget-object v9, v12, v9

    sget-object v10, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    if-ne v9, v10, :cond_3

    if-eqz v6, :cond_7

    .line 1017
    invoke-virtual {v13, v11}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v9

    if-nez v9, :cond_7

    .line 1018
    invoke-virtual {v13, v8}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v8

    if-eqz v8, :cond_3

    .line 1019
    invoke-virtual {v6}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v8, v11}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v8

    if-nez v8, :cond_3

    :cond_7
    move-object v6, v7

    goto :goto_1

    :cond_8
    if-eqz v5, :cond_b

    .line 1028
    invoke-virtual {v5}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v2

    .line 1030
    invoke-virtual {v3}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v7

    :cond_9
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    move-result v12

    if-eqz v12, :cond_a

    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v12

    check-cast v12, Ljava/lang/reflect/Method;

    .line 1031
    invoke-virtual {v12}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v13

    array-length v13, v13

    if-ne v13, v10, :cond_9

    .line 1033
    invoke-virtual {v12}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v13

    aget-object v13, v13, v9

    invoke-virtual {v2, v13}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v13

    if-eqz v13, :cond_9

    goto :goto_3

    :cond_a
    move-object v12, v4

    goto :goto_3

    .line 1042
    :cond_b
    invoke-virtual {v3}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v2

    move-object v12, v4

    :cond_c
    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_d

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/reflect/Method;

    .line 1043
    invoke-virtual {v7}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v13

    array-length v13, v13

    if-ne v13, v10, :cond_c

    move-object v12, v7

    goto :goto_2

    :cond_d
    :goto_3
    const/4 v2, 0x2

    if-eqz v6, :cond_10

    .line 1052
    invoke-virtual {v6}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v7

    .line 1054
    invoke-virtual {v3}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_e
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v13

    if-eqz v13, :cond_f

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Ljava/lang/reflect/Method;

    .line 1055
    invoke-virtual {v13}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v14

    array-length v14, v14

    if-ne v14, v2, :cond_e

    .line 1056
    invoke-virtual {v13}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v14

    aget-object v14, v14, v9

    sget-object v15, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    if-ne v14, v15, :cond_e

    .line 1058
    invoke-virtual {v13}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v14

    aget-object v14, v14, v10

    invoke-virtual {v7, v14}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v14

    if-eqz v14, :cond_e

    goto :goto_5

    :cond_f
    move-object v13, v4

    goto :goto_5

    .line 1067
    :cond_10
    invoke-virtual {v3}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v3

    move-object v13, v4

    :cond_11
    :goto_4
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_12

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/reflect/Method;

    .line 1068
    invoke-virtual {v7}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v14

    array-length v14, v14

    if-ne v14, v2, :cond_11

    .line 1069
    invoke-virtual {v7}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v14

    aget-object v14, v14, v9

    sget-object v15, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    if-ne v14, v15, :cond_11

    move-object v13, v7

    goto :goto_4

    :cond_12
    :goto_5
    if-eqz v5, :cond_13

    .line 1077
    invoke-virtual {v5}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v2

    goto :goto_6

    :cond_13
    if-eqz v12, :cond_14

    .line 1079
    invoke-virtual {v12}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v2

    aget-object v2, v2, v9

    goto :goto_6

    :cond_14
    move-object v2, v4

    :goto_6
    if-eqz v6, :cond_15

    .line 1084
    invoke-virtual {v6}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v4

    goto :goto_7

    :cond_15
    if-eqz v13, :cond_16

    .line 1086
    invoke-virtual {v13}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v3

    aget-object v4, v3, v10

    :cond_16
    :goto_7
    if-eqz v5, :cond_17

    .line 1090
    invoke-virtual {v5}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Class;->isArray()Z

    :cond_17
    const-string v3, "normalPropertyType"

    const-string v7, "normalset"

    const-string v9, "normalget"

    const-string v10, "normal"

    const-string v14, "valid"

    if-eqz v5, :cond_19

    if-eqz v12, :cond_19

    if-eqz v6, :cond_18

    if-nez v13, :cond_19

    .line 1103
    :cond_18
    invoke-virtual {v1, v10, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1104
    invoke-virtual {v1, v9, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1105
    invoke-virtual {v1, v7, v12}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1106
    invoke-virtual {v1, v3, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_0

    :cond_19
    if-nez v5, :cond_1a

    if-eqz v12, :cond_1b

    :cond_1a
    if-nez v6, :cond_1b

    if-nez v13, :cond_1b

    .line 1115
    invoke-virtual {v1, v10, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1116
    invoke-virtual {v1, v9, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1117
    invoke-virtual {v1, v7, v12}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1118
    invoke-virtual {v1, v3, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_0

    :cond_1b
    const-string v15, "indexedget"

    move-object/from16 p1, v0

    const-string v0, "indexedPropertyType"

    move-object/from16 v16, v8

    const-string v8, "indexedset"

    move-object/from16 v17, v0

    const-string v0, "indexed"

    if-nez v5, :cond_1d

    if-eqz v12, :cond_1c

    goto :goto_8

    :cond_1c
    move-object/from16 v19, v16

    move-object/from16 v2, v17

    goto/16 :goto_9

    :cond_1d
    :goto_8
    if-nez v6, :cond_1e

    if-eqz v13, :cond_1c

    :cond_1e
    if-eqz v5, :cond_21

    if-eqz v12, :cond_21

    if-eqz v6, :cond_21

    if-eqz v13, :cond_21

    move-object/from16 v18, v4

    .line 1130
    invoke-virtual {v6}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v4, v11}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_1f

    .line 1131
    invoke-virtual {v1, v10, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1132
    invoke-virtual {v1, v9, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1133
    invoke-virtual {v1, v7, v12}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1134
    invoke-virtual {v1, v3, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1137
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1138
    invoke-virtual {v1, v15, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1139
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-object/from16 v6, v17

    move-object/from16 v4, v18

    .line 1140
    invoke-virtual {v1, v6, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_a

    :cond_1f
    move-object/from16 v6, v17

    move-object/from16 v4, v18

    .line 1143
    sget-object v11, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    if-eq v2, v11, :cond_20

    .line 1144
    invoke-virtual {v5}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v11

    move-object/from16 v15, v16

    invoke-virtual {v11, v15}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v11

    if-eqz v11, :cond_20

    .line 1145
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1146
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1147
    invoke-virtual {v1, v6, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_a

    .line 1150
    :cond_20
    invoke-virtual {v1, v10, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1151
    invoke-virtual {v1, v9, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1152
    invoke-virtual {v1, v7, v12}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1153
    invoke-virtual {v1, v3, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_a

    :cond_21
    move-object/from16 v18, v4

    move-object/from16 v19, v16

    move-object/from16 v4, v17

    if-eqz v5, :cond_23

    if-nez v12, :cond_23

    if-eqz v6, :cond_23

    if-eqz v13, :cond_23

    .line 1163
    invoke-virtual {v1, v10, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1164
    invoke-virtual {v1, v9, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1165
    invoke-virtual {v1, v7, v12}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1166
    invoke-virtual {v1, v3, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1168
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1169
    invoke-virtual {v6}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v11}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_22

    .line 1170
    invoke-virtual {v1, v15, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1172
    :cond_22
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-object/from16 v0, v18

    .line 1173
    invoke-virtual {v1, v4, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_a

    :cond_23
    move-object/from16 v16, v2

    move-object/from16 v2, v18

    if-nez v5, :cond_25

    if-eqz v12, :cond_25

    if-eqz v6, :cond_25

    if-eqz v13, :cond_25

    .line 1180
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1181
    invoke-virtual {v6}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, v11}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_24

    .line 1182
    invoke-virtual {v1, v15, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1184
    :cond_24
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1185
    invoke-virtual {v1, v4, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_a

    :cond_25
    if-eqz v5, :cond_27

    if-nez v12, :cond_27

    if-eqz v6, :cond_27

    if-nez v13, :cond_27

    move-object/from16 v18, v2

    .line 1192
    invoke-virtual {v6}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v2, v11}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_26

    .line 1193
    invoke-virtual {v1, v10, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1194
    invoke-virtual {v1, v9, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1195
    invoke-virtual {v1, v7, v12}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-object/from16 v2, v16

    .line 1196
    invoke-virtual {v1, v3, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1199
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1200
    invoke-virtual {v1, v15, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1201
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-object/from16 v0, v18

    .line 1202
    invoke-virtual {v1, v4, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_a

    :cond_26
    move-object/from16 v2, v16

    .line 1205
    invoke-virtual {v1, v10, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1206
    invoke-virtual {v1, v9, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1207
    invoke-virtual {v1, v7, v12}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1208
    invoke-virtual {v1, v3, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_a

    :cond_27
    move-object/from16 v17, v4

    move-object v4, v2

    move-object/from16 v2, v16

    if-nez v5, :cond_29

    if-eqz v12, :cond_29

    if-eqz v6, :cond_29

    if-nez v13, :cond_29

    move-object/from16 v18, v4

    .line 1217
    invoke-virtual {v6}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v4, v11}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_28

    .line 1218
    invoke-virtual {v1, v10, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1219
    invoke-virtual {v1, v9, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1220
    invoke-virtual {v1, v7, v12}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1221
    invoke-virtual {v1, v3, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1224
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1225
    invoke-virtual {v1, v15, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1226
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-object/from16 v2, v17

    move-object/from16 v4, v18

    .line 1227
    invoke-virtual {v1, v2, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_a

    .line 1230
    :cond_28
    invoke-virtual {v1, v10, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1231
    invoke-virtual {v1, v9, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1232
    invoke-virtual {v1, v7, v12}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1233
    invoke-virtual {v1, v3, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_a

    :cond_29
    move-object/from16 v2, v17

    if-eqz v5, :cond_2a

    if-nez v12, :cond_2a

    if-nez v6, :cond_2a

    if-eqz v13, :cond_2a

    .line 1242
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1243
    invoke-virtual {v1, v15, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1244
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1245
    invoke-virtual {v1, v2, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_a

    :cond_2a
    if-nez v5, :cond_2b

    if-eqz v12, :cond_2b

    if-nez v6, :cond_2b

    if-eqz v13, :cond_2b

    .line 1252
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1253
    invoke-virtual {v1, v15, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1254
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1255
    invoke-virtual {v1, v2, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_a

    :cond_2b
    :goto_9
    if-nez v12, :cond_2e

    if-nez v5, :cond_2e

    if-nez v6, :cond_2c

    if-eqz v13, :cond_2e

    :cond_2c
    if-eqz v6, :cond_2d

    .line 1267
    invoke-virtual {v6}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v3

    move-object/from16 v5, v19

    invoke-virtual {v3, v5}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_2d

    if-eqz v13, :cond_31

    .line 1269
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1270
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1271
    invoke-virtual {v1, v2, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_a

    .line 1276
    :cond_2d
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1277
    invoke-virtual {v1, v15, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1278
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1279
    invoke-virtual {v1, v2, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_a

    :cond_2e
    if-nez v12, :cond_2f

    if-eqz v5, :cond_30

    :cond_2f
    if-eqz v6, :cond_30

    if-eqz v13, :cond_30

    .line 1288
    invoke-virtual {v1, v0, v14}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1289
    invoke-virtual {v1, v15, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1290
    invoke-virtual {v1, v8, v13}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1291
    invoke-virtual {v1, v2, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_a

    :cond_30
    const-string v2, "invalid"

    .line 1296
    invoke-virtual {v1, v10, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1297
    invoke-virtual {v1, v0, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_31
    :goto_a
    move-object/from16 v0, p1

    goto/16 :goto_0

    :cond_32
    return-void
.end method

.method private static getQualifiedName(Ljava/lang/reflect/Method;)Ljava/lang/String;
    .locals 3

    .line 614
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v0

    .line 615
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object p0

    if-eqz p0, :cond_0

    const/4 v1, 0x0

    .line 617
    :goto_0
    array-length v2, p0

    if-ge v1, v2, :cond_0

    .line 618
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "_"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget-object v2, p0, v1

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static internalAsMap([Lgroovyjarjaropenbeans/EventSetDescriptor;)Ljava/util/HashMap;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Lgroovyjarjaropenbeans/EventSetDescriptor;",
            ")",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjaropenbeans/EventSetDescriptor;",
            ">;"
        }
    .end annotation

    .line 606
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    const/4 v1, 0x0

    .line 607
    :goto_0
    array-length v2, p0

    if-ge v1, v2, :cond_0

    .line 608
    aget-object v2, p0, v1

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/EventSetDescriptor;->getName()Ljava/lang/String;

    move-result-object v2

    aget-object v3, p0, v1

    invoke-virtual {v0, v2, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static internalAsMap([Lgroovyjarjaropenbeans/MethodDescriptor;)Ljava/util/HashMap;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Lgroovyjarjaropenbeans/MethodDescriptor;",
            ")",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjaropenbeans/MethodDescriptor;",
            ">;"
        }
    .end annotation

    .line 596
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    const/4 v1, 0x0

    .line 597
    :goto_0
    array-length v2, p0

    if-ge v1, v2, :cond_0

    .line 598
    aget-object v2, p0, v1

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v2

    invoke-static {v2}, Lgroovyjarjaropenbeans/StandardBeanInfo;->getQualifiedName(Ljava/lang/reflect/Method;)Ljava/lang/String;

    move-result-object v2

    .line 599
    aget-object v3, p0, v1

    invoke-virtual {v0, v2, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static internalAsMap([Lgroovyjarjaropenbeans/PropertyDescriptor;)Ljava/util/HashMap;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Lgroovyjarjaropenbeans/PropertyDescriptor;",
            ")",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjaropenbeans/PropertyDescriptor;",
            ">;"
        }
    .end annotation

    .line 587
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    const/4 v1, 0x0

    .line 588
    :goto_0
    array-length v2, p0

    if-ge v1, v2, :cond_0

    .line 589
    aget-object v2, p0, v1

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getName()Ljava/lang/String;

    move-result-object v2

    aget-object v3, p0, v1

    invoke-virtual {v0, v2, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private introspectEvents()[Lgroovyjarjaropenbeans/EventSetDescriptor;
    .locals 15
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/IntrospectionException;
        }
    .end annotation

    .line 1313
    invoke-direct {p0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectMethods()[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 1318
    :cond_0
    new-instance v1, Ljava/util/HashMap;

    array-length v2, v0

    invoke-direct {v1, v2}, Ljava/util/HashMap;-><init>(I)V

    const/4 v2, 0x0

    move v3, v2

    .line 1322
    :goto_0
    array-length v4, v0

    const-string v5, "remove"

    const-string v6, "add"

    if-ge v3, v4, :cond_1

    .line 1323
    aget-object v4, v0, v3

    invoke-virtual {v4}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v4

    invoke-static {v6, v4, v1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectListenerMethods(Ljava/lang/String;Ljava/lang/reflect/Method;Ljava/util/HashMap;)V

    .line 1325
    aget-object v4, v0, v3

    invoke-virtual {v4}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v4

    invoke-static {v5, v4, v1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectListenerMethods(Ljava/lang/String;Ljava/lang/reflect/Method;Ljava/util/HashMap;)V

    .line 1327
    aget-object v4, v0, v3

    invoke-virtual {v4}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v4

    invoke-static {v4, v1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectGetListenerMethods(Ljava/lang/reflect/Method;Ljava/util/HashMap;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 1330
    :cond_1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 1331
    invoke-virtual {v1}, Ljava/util/HashMap;->entrySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_2
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_5

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 1332
    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/HashMap;

    .line 1333
    invoke-virtual {v4, v6}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    move-object v12, v7

    check-cast v12, Ljava/lang/reflect/Method;

    .line 1334
    invoke-virtual {v4, v5}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    move-object v13, v7

    check-cast v13, Ljava/lang/reflect/Method;

    if-eqz v12, :cond_2

    if-nez v13, :cond_3

    goto :goto_1

    :cond_3
    const-string v7, "get"

    .line 1340
    invoke-virtual {v4, v7}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    move-object v14, v7

    check-cast v14, Ljava/lang/reflect/Method;

    const-string v7, "listenerType"

    .line 1341
    invoke-virtual {v4, v7}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    move-object v10, v7

    check-cast v10, Ljava/lang/Class;

    const-string v7, "listenerMethods"

    .line 1342
    invoke-virtual {v4, v7}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, [Ljava/lang/reflect/Method;

    move-object v11, v7

    check-cast v11, [Ljava/lang/reflect/Method;

    .line 1343
    new-instance v7, Lgroovyjarjaropenbeans/EventSetDescriptor;

    .line 1344
    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-static {v3}, Lgroovyjarjaropenbeans/Introspector;->decapitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    move-object v8, v7

    invoke-direct/range {v8 .. v14}, Lgroovyjarjaropenbeans/EventSetDescriptor;-><init>(Ljava/lang/String;Ljava/lang/Class;[Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;)V

    const-string v3, "isUnicast"

    .line 1347
    invoke-virtual {v4, v3}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    if-eqz v3, :cond_4

    const/4 v3, 0x1

    goto :goto_2

    :cond_4
    move v3, v2

    :goto_2
    invoke-virtual {v7, v3}, Lgroovyjarjaropenbeans/EventSetDescriptor;->setUnicast(Z)V

    .line 1348
    invoke-virtual {v0, v7}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 1352
    :cond_5
    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v1

    new-array v1, v1, [Lgroovyjarjaropenbeans/EventSetDescriptor;

    .line 1353
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    return-object v1
.end method

.method private static introspectGet(Ljava/lang/reflect/Method;Ljava/util/HashMap;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/reflect/Method;",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/util/HashMap;",
            ">;)V"
        }
    .end annotation

    .line 829
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    const-string v1, "get"

    .line 841
    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    const/4 v1, 0x3

    goto :goto_0

    :cond_1
    move v1, v2

    :goto_0
    const-string v3, "is"

    .line 845
    invoke-virtual {v0, v3}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v3

    const/4 v4, 0x2

    if-eqz v3, :cond_2

    move v1, v4

    :cond_2
    if-nez v1, :cond_3

    return-void

    .line 853
    :cond_3
    invoke-virtual {v0, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjaropenbeans/Introspector;->decapitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 856
    invoke-static {v0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->isValidProperty(Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_4

    return-void

    .line 861
    :cond_4
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v3

    if-eqz v3, :cond_a

    .line 863
    sget-object v5, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    if-ne v3, v5, :cond_5

    goto :goto_1

    :cond_5
    if-ne v1, v4, :cond_6

    .line 869
    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    if-eq v3, v1, :cond_6

    return-void

    .line 875
    :cond_6
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v1

    .line 876
    array-length v3, v1

    const/4 v4, 0x1

    if-gt v3, v4, :cond_a

    array-length v3, v1

    if-ne v3, v4, :cond_7

    aget-object v1, v1, v2

    sget-object v2, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    if-eq v1, v2, :cond_7

    goto :goto_1

    .line 881
    :cond_7
    invoke-virtual {p1, v0}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/HashMap;

    if-nez v1, :cond_8

    .line 883
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    .line 884
    invoke-virtual {p1, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_8
    const-string p1, "getters"

    .line 887
    invoke-virtual {v1, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/ArrayList;

    if-nez v0, :cond_9

    .line 889
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 890
    invoke-virtual {v1, p1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 894
    :cond_9
    invoke-virtual {v0, p0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_a
    :goto_1
    return-void
.end method

.method private static introspectGetListenerMethods(Ljava/lang/reflect/Method;Ljava/util/HashMap;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/reflect/Method;",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/util/HashMap;",
            ">;)V"
        }
    .end annotation

    .line 1449
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    const-string v1, "get"

    .line 1454
    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_6

    const-string v2, "Listeners"

    .line 1455
    invoke-virtual {v0, v2}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_1

    goto :goto_0

    :cond_1
    const/4 v2, 0x3

    .line 1460
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v3

    add-int/lit8 v3, v3, -0x1

    .line 1459
    invoke-virtual {v0, v2, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    const/4 v2, 0x0

    const-string v3, "Listener"

    .line 1462
    invoke-virtual {v0, v3}, Ljava/lang/String;->lastIndexOf(Ljava/lang/String;)I

    move-result v3

    .line 1461
    invoke-virtual {v0, v2, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_6

    .line 1463
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v3

    if-nez v3, :cond_2

    goto :goto_0

    .line 1467
    :cond_2
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v3

    if-eqz v3, :cond_6

    .line 1468
    array-length v3, v3

    if-eqz v3, :cond_3

    goto :goto_0

    .line 1472
    :cond_3
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v3

    .line 1473
    invoke-virtual {v3}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object v4

    if-eqz v4, :cond_6

    .line 1474
    invoke-virtual {v3}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_4

    goto :goto_0

    .line 1479
    :cond_4
    invoke-virtual {p1, v2}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/HashMap;

    if-nez v0, :cond_5

    .line 1481
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 1484
    :cond_5
    invoke-virtual {v0, v1, p0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1485
    invoke-virtual {p1, v2, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_6
    :goto_0
    return-void
.end method

.method private static introspectListenerMethods(Ljava/lang/String;Ljava/lang/reflect/Method;Ljava/util/HashMap;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/reflect/Method;",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/util/HashMap;",
            ">;)V"
        }
    .end annotation

    .line 1364
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 1369
    :cond_0
    invoke-virtual {v0, p0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_a

    const-string v1, "Listener"

    .line 1370
    invoke-virtual {v0, v1}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_1

    goto/16 :goto_2

    .line 1374
    :cond_1
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v2

    invoke-virtual {v0, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v0

    .line 1376
    invoke-virtual {v0, v1}, Ljava/lang/String;->lastIndexOf(Ljava/lang/String;)I

    move-result v1

    const/4 v2, 0x0

    .line 1375
    invoke-virtual {v0, v2, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_a

    .line 1377
    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v3

    if-nez v3, :cond_2

    goto/16 :goto_2

    .line 1381
    :cond_2
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v3

    if-eqz v3, :cond_a

    .line 1382
    array-length v4, v3

    const/4 v5, 0x1

    if-eq v4, v5, :cond_3

    goto :goto_2

    .line 1386
    :cond_3
    aget-object v3, v3, v2

    .line 1388
    const-class v4, Ljava/util/EventListener;

    invoke-virtual {v4, v3}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v4

    if-nez v4, :cond_4

    return-void

    .line 1392
    :cond_4
    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v4, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_5

    return-void

    .line 1396
    :cond_5
    invoke-virtual {p2, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/HashMap;

    if-nez v0, :cond_6

    .line 1398
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    :cond_6
    const-string v4, "listenerType"

    .line 1401
    invoke-virtual {v0, v4}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    if-nez v5, :cond_7

    .line 1402
    invoke-virtual {v0, v4, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1404
    invoke-static {v3}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectListenerMethods(Ljava/lang/Class;)[Ljava/lang/reflect/Method;

    move-result-object v3

    const-string v4, "listenerMethods"

    .line 1403
    invoke-virtual {v0, v4, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1407
    :cond_7
    invoke-virtual {v0, p0, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v3, "add"

    .line 1410
    invoke-virtual {p0, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_9

    .line 1411
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getExceptionTypes()[Ljava/lang/Class;

    move-result-object p0

    if-eqz p0, :cond_9

    .line 1413
    :goto_0
    array-length p1, p0

    if-ge v2, p1, :cond_9

    .line 1414
    aget-object p1, p0, v2

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    const-class v3, Ljava/util/TooManyListenersException;

    .line 1415
    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    .line 1414
    invoke-virtual {p1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_8

    const-string p0, "isUnicast"

    const-string p1, "true"

    .line 1416
    invoke-virtual {v0, p0, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    :cond_8
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 1423
    :cond_9
    :goto_1
    invoke-virtual {p2, v1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_a
    :goto_2
    return-void
.end method

.method private static introspectListenerMethods(Ljava/lang/Class;)[Ljava/lang/reflect/Method;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)[",
            "Ljava/lang/reflect/Method;"
        }
    .end annotation

    .line 1427
    invoke-virtual {p0}, Ljava/lang/Class;->getDeclaredMethods()[Ljava/lang/reflect/Method;

    move-result-object p0

    .line 1428
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    move v2, v1

    .line 1429
    :goto_0
    array-length v3, p0

    if-ge v2, v3, :cond_2

    .line 1430
    aget-object v3, p0, v2

    invoke-virtual {v3}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v3

    .line 1431
    array-length v4, v3

    const/4 v5, 0x1

    if-eq v4, v5, :cond_0

    goto :goto_1

    .line 1435
    :cond_0
    const-class v4, Ljava/util/EventObject;

    aget-object v3, v3, v1

    invoke-virtual {v4, v3}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 1436
    aget-object v3, p0, v2

    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_1
    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 1439
    :cond_2
    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result p0

    new-array p0, p0, [Ljava/lang/reflect/Method;

    .line 1440
    invoke-virtual {v0, p0}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    return-object p0
.end method

.method private introspectMethods()[Lgroovyjarjaropenbeans/MethodDescriptor;
    .locals 2

    .line 632
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanClass:Ljava/lang/Class;

    const/4 v1, 0x0

    invoke-direct {p0, v1, v0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectMethods(ZLjava/lang/Class;)[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object v0

    return-object v0
.end method

.method private introspectMethods(Z)[Lgroovyjarjaropenbeans/MethodDescriptor;
    .locals 1

    .line 636
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanClass:Ljava/lang/Class;

    invoke-direct {p0, p1, v0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectMethods(ZLjava/lang/Class;)[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object p1

    return-object p1
.end method

.method private introspectMethods(ZLjava/lang/Class;)[Lgroovyjarjaropenbeans/MethodDescriptor;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/Class<",
            "*>;)[",
            "Lgroovyjarjaropenbeans/MethodDescriptor;"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 643
    invoke-virtual {p2}, Ljava/lang/Class;->getMethods()[Ljava/lang/reflect/Method;

    move-result-object p1

    goto :goto_0

    .line 644
    :cond_0
    invoke-virtual {p2}, Ljava/lang/Class;->getDeclaredMethods()[Ljava/lang/reflect/Method;

    move-result-object p1

    :goto_0
    const/4 p2, 0x0

    if-eqz p1, :cond_4

    .line 646
    array-length v0, p1

    if-nez v0, :cond_1

    goto :goto_2

    .line 649
    :cond_1
    new-instance v0, Ljava/util/ArrayList;

    array-length v1, p1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    const/4 v1, 0x0

    .line 653
    :goto_1
    array-length v2, p1

    if-ge v1, v2, :cond_3

    .line 654
    aget-object v2, p1, v1

    invoke-virtual {v2}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result v2

    .line 655
    invoke-static {v2}, Ljava/lang/reflect/Modifier;->isPublic(I)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 657
    new-instance v2, Lgroovyjarjaropenbeans/MethodDescriptor;

    aget-object v3, p1, v1

    invoke-direct {v2, v3}, Lgroovyjarjaropenbeans/MethodDescriptor;-><init>(Ljava/lang/reflect/Method;)V

    .line 659
    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 664
    :cond_3
    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result p1

    if-lez p1, :cond_4

    .line 667
    new-array p1, p1, [Lgroovyjarjaropenbeans/MethodDescriptor;

    .line 668
    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    move-object p2, p1

    check-cast p2, [Lgroovyjarjaropenbeans/MethodDescriptor;

    :cond_4
    :goto_2
    return-object p2
.end method

.method private introspectProperties(Ljava/lang/Class;)[Lgroovyjarjaropenbeans/PropertyDescriptor;
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)[",
            "Lgroovyjarjaropenbeans/PropertyDescriptor;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/IntrospectionException;
        }
    .end annotation

    .line 688
    invoke-direct {p0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectMethods()[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    .line 694
    :cond_0
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    const/4 v3, 0x0

    move v4, v3

    .line 696
    :goto_0
    array-length v5, v0

    if-ge v4, v5, :cond_2

    .line 697
    aget-object v5, v0, v4

    invoke-virtual {v5}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result v5

    .line 698
    invoke-static {v5}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v5

    if-nez v5, :cond_1

    .line 699
    aget-object v5, v0, v4

    invoke-virtual {v2, v5}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 704
    :cond_2
    invoke-virtual {v2}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-lez v0, :cond_3

    .line 707
    new-array v0, v0, [Lgroovyjarjaropenbeans/MethodDescriptor;

    .line 708
    invoke-virtual {v2, v0}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjaropenbeans/MethodDescriptor;

    goto :goto_1

    :cond_3
    move-object v0, v1

    :goto_1
    if-nez v0, :cond_4

    return-object v1

    .line 715
    :cond_4
    new-instance v1, Ljava/util/HashMap;

    array-length v2, v0

    invoke-direct {v1, v2}, Ljava/util/HashMap;-><init>(I)V

    move v2, v3

    .line 719
    :goto_2
    array-length v4, v0

    if-ge v2, v4, :cond_5

    .line 720
    aget-object v4, v0, v2

    invoke-virtual {v4}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v4

    invoke-static {v4, v1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectGet(Ljava/lang/reflect/Method;Ljava/util/HashMap;)V

    .line 721
    aget-object v4, v0, v2

    invoke-virtual {v4}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v4

    invoke-static {v4, v1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectSet(Ljava/lang/reflect/Method;Ljava/util/HashMap;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    .line 725
    :cond_5
    invoke-direct {p0, v1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->fixGetSet(Ljava/util/HashMap;)V

    const/4 v0, 0x1

    .line 728
    invoke-direct {p0, v0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectMethods(Z)[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object v2

    if-eqz p1, :cond_8

    .line 730
    invoke-direct {p0, v0, p1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectMethods(ZLjava/lang/Class;)[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object p1

    if-eqz p1, :cond_8

    .line 733
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 734
    array-length v5, v2

    move v6, v3

    :goto_3
    if-ge v6, v5, :cond_7

    aget-object v7, v2, v6

    .line 735
    invoke-direct {p0, v7, p1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->isInSuper(Lgroovyjarjaropenbeans/MethodDescriptor;[Lgroovyjarjaropenbeans/MethodDescriptor;)Z

    move-result v8

    if-nez v8, :cond_6

    .line 736
    invoke-virtual {v4, v7}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_6
    add-int/lit8 v6, v6, 0x1

    goto :goto_3

    :cond_7
    new-array p1, v3, [Lgroovyjarjaropenbeans/MethodDescriptor;

    .line 740
    invoke-virtual {v4, p1}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    move-object v2, p1

    check-cast v2, [Lgroovyjarjaropenbeans/MethodDescriptor;

    :cond_8
    move p1, v3

    .line 743
    :goto_4
    array-length v4, v2

    if-ge p1, v4, :cond_9

    .line 744
    aget-object v4, v2, p1

    invoke-virtual {v4}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v4

    invoke-direct {p0, v4}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectPropertyListener(Ljava/lang/reflect/Method;)V

    add-int/lit8 p1, p1, 0x1

    goto :goto_4

    .line 747
    :cond_9
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    .line 749
    invoke-virtual {v1}, Ljava/util/HashMap;->entrySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_5
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_f

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map$Entry;

    .line 750
    invoke-interface {v2}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    .line 751
    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/HashMap;

    if-nez v2, :cond_a

    goto :goto_5

    :cond_a
    const-string v5, "normal"

    .line 755
    invoke-virtual {v2, v5}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    const-string v6, "indexed"

    .line 756
    invoke-virtual {v2, v6}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    if-nez v5, :cond_b

    if-nez v6, :cond_b

    goto :goto_5

    :cond_b
    const-string v5, "normalget"

    .line 762
    invoke-virtual {v2, v5}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    move-object v7, v5

    check-cast v7, Ljava/lang/reflect/Method;

    const-string v5, "normalset"

    .line 763
    invoke-virtual {v2, v5}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    move-object v8, v5

    check-cast v8, Ljava/lang/reflect/Method;

    const-string v5, "indexedget"

    .line 764
    invoke-virtual {v2, v5}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    move-object v11, v5

    check-cast v11, Ljava/lang/reflect/Method;

    const-string v5, "indexedset"

    .line 765
    invoke-virtual {v2, v5}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    move-object v12, v5

    check-cast v12, Ljava/lang/reflect/Method;

    if-nez v6, :cond_c

    .line 769
    new-instance v5, Lgroovyjarjaropenbeans/PropertyDescriptor;

    invoke-direct {v5, v4, v7, v8}, Lgroovyjarjaropenbeans/PropertyDescriptor;-><init>(Ljava/lang/String;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;)V

    goto :goto_6

    .line 772
    :cond_c
    :try_start_0
    new-instance v13, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;

    move-object v5, v13

    move-object v6, v4

    move-object v9, v11

    move-object v10, v12

    invoke-direct/range {v5 .. v10}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;-><init>(Ljava/lang/String;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;)V
    :try_end_0
    .catch Lgroovyjarjaropenbeans/IntrospectionException; {:try_start_0 .. :try_end_0} :catch_0

    move-object v5, v13

    goto :goto_6

    .line 777
    :catch_0
    new-instance v13, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;

    const/4 v7, 0x0

    const/4 v8, 0x0

    move-object v5, v13

    move-object v6, v4

    move-object v9, v11

    move-object v10, v12

    invoke-direct/range {v5 .. v10}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;-><init>(Ljava/lang/String;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;)V

    .line 783
    :goto_6
    iget-boolean v4, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->canAddPropertyChangeListener:Z

    if-eqz v4, :cond_d

    iget-boolean v4, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->canRemovePropertyChangeListener:Z

    if-eqz v4, :cond_d

    .line 784
    invoke-virtual {v5, v0}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setBound(Z)V

    goto :goto_7

    .line 786
    :cond_d
    invoke-virtual {v5, v3}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setBound(Z)V

    :goto_7
    const-string v4, "isConstrained"

    .line 788
    invoke-virtual {v2, v4}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    sget-object v4, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    if-ne v2, v4, :cond_e

    .line 789
    invoke-virtual {v5, v0}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setConstrained(Z)V

    .line 791
    :cond_e
    invoke-virtual {p1, v5}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto/16 :goto_5

    .line 795
    :cond_f
    invoke-virtual {p1}, Ljava/util/ArrayList;->size()I

    move-result v0

    new-array v0, v0, [Lgroovyjarjaropenbeans/PropertyDescriptor;

    .line 796
    invoke-virtual {p1, v0}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    return-object v0
.end method

.method private introspectPropertyListener(Ljava/lang/reflect/Method;)V
    .locals 5

    .line 812
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v0

    .line 813
    invoke-virtual {p1}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object p1

    .line 814
    array-length v1, p1

    const/4 v2, 0x1

    if-eq v1, v2, :cond_0

    return-void

    :cond_0
    const-string v1, "addPropertyChangeListener"

    .line 817
    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    const/4 v3, 0x0

    if-eqz v1, :cond_1

    aget-object v1, p1, v3

    const-class v4, Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 818
    invoke-virtual {v1, v4}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 819
    iput-boolean v2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->canAddPropertyChangeListener:Z

    :cond_1
    const-string v1, "removePropertyChangeListener"

    .line 820
    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    aget-object p1, p1, v3

    const-class v0, Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 821
    invoke-virtual {p1, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    .line 822
    iput-boolean v2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->canRemovePropertyChangeListener:Z

    :cond_2
    return-void
.end method

.method private static introspectSet(Ljava/lang/reflect/Method;Ljava/util/HashMap;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/reflect/Method;",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/util/HashMap;",
            ">;)V"
        }
    .end annotation

    .line 901
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 910
    :cond_0
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v1

    .line 911
    sget-object v2, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    if-eq v1, v2, :cond_1

    return-void

    :cond_1
    if-eqz v0, :cond_9

    const-string v1, "set"

    .line 915
    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_2

    goto :goto_1

    :cond_2
    const/4 v1, 0x3

    .line 919
    invoke-virtual {v0, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjaropenbeans/Introspector;->decapitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 922
    invoke-static {v0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->isValidProperty(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_3

    return-void

    .line 929
    :cond_3
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v1

    .line 931
    array-length v2, v1

    if-eqz v2, :cond_9

    array-length v2, v1

    const/4 v3, 0x2

    if-gt v2, v3, :cond_9

    array-length v2, v1

    const/4 v4, 0x0

    if-ne v2, v3, :cond_4

    aget-object v1, v1, v4

    sget-object v2, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    if-eq v1, v2, :cond_4

    goto :goto_1

    .line 936
    :cond_4
    invoke-virtual {p1, v0}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/HashMap;

    if-nez v1, :cond_5

    .line 938
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    .line 939
    invoke-virtual {p1, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_5
    const-string p1, "setters"

    .line 942
    invoke-virtual {v1, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/ArrayList;

    if-nez v0, :cond_6

    .line 944
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 945
    invoke-virtual {v1, p1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 949
    :cond_6
    invoke-virtual {p0}, Ljava/lang/reflect/Method;->getExceptionTypes()[Ljava/lang/Class;

    move-result-object p1

    .line 950
    array-length v2, p1

    :goto_0
    if-ge v4, v2, :cond_8

    aget-object v3, p1, v4

    .line 951
    const-class v5, Lgroovyjarjaropenbeans/PropertyVetoException;

    invoke-virtual {v3, v5}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_7

    .line 952
    sget-object v3, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    const-string v5, "isConstrained"

    invoke-virtual {v1, v5, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_7
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 957
    :cond_8
    invoke-virtual {v0, p0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_9
    :goto_1
    return-void
.end method

.method private isInSuper(Lgroovyjarjaropenbeans/MethodDescriptor;[Lgroovyjarjaropenbeans/MethodDescriptor;)Z
    .locals 5

    .line 802
    array-length v0, p2

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_1

    aget-object v3, p2, v2

    .line 803
    invoke-virtual {p1}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v4

    invoke-virtual {v3}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v3

    invoke-virtual {v4, v3}, Ljava/lang/reflect/Method;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return v1
.end method

.method private static isValidProperty(Ljava/lang/String;)Z
    .locals 0

    if-eqz p0, :cond_0

    .line 1489
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result p0

    if-eqz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method private static mergeAttributes(Lgroovyjarjaropenbeans/PropertyDescriptor;Lgroovyjarjaropenbeans/PropertyDescriptor;)V
    .locals 2

    .line 515
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->hidden:Z

    iget-boolean v1, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->hidden:Z

    or-int/2addr v0, v1

    iput-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->hidden:Z

    .line 516
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->expert:Z

    iget-boolean v1, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->expert:Z

    or-int/2addr v0, v1

    iput-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->expert:Z

    .line 517
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->preferred:Z

    iget-boolean v1, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->preferred:Z

    or-int/2addr v0, v1

    iput-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->preferred:Z

    .line 518
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->bound:Z

    iget-boolean v1, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->bound:Z

    or-int/2addr v0, v1

    iput-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->bound:Z

    .line 519
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->constrained:Z

    iget-boolean v1, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->constrained:Z

    or-int/2addr v0, v1

    iput-boolean v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->constrained:Z

    .line 520
    iget-object v0, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->name:Ljava/lang/String;

    iput-object v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->name:Ljava/lang/String;

    .line 521
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->shortDescription:Ljava/lang/String;

    if-nez v0, :cond_0

    iget-object v0, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->shortDescription:Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 523
    iget-object v0, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->shortDescription:Ljava/lang/String;

    iput-object v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->shortDescription:Ljava/lang/String;

    .line 525
    :cond_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->displayName:Ljava/lang/String;

    if-nez v0, :cond_1

    iget-object v0, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->displayName:Ljava/lang/String;

    if-eqz v0, :cond_1

    .line 526
    iget-object p1, p1, Lgroovyjarjaropenbeans/PropertyDescriptor;->displayName:Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjaropenbeans/PropertyDescriptor;->displayName:Ljava/lang/String;

    :cond_1
    return-void
.end method

.method private mergeEvents([Lgroovyjarjaropenbeans/EventSetDescriptor;I)[Lgroovyjarjaropenbeans/EventSetDescriptor;
    .locals 7

    .line 552
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    invoke-static {v0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->internalAsMap([Lgroovyjarjaropenbeans/EventSetDescriptor;)Ljava/util/HashMap;

    move-result-object v0

    .line 554
    iget v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultEventIndex:I

    if-ltz v1, :cond_0

    iget-object v2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    array-length v3, v2

    if-ge v1, v3, :cond_0

    .line 555
    aget-object p2, v2, v1

    invoke-virtual {p2}, Lgroovyjarjaropenbeans/EventSetDescriptor;->getName()Ljava/lang/String;

    move-result-object p2

    goto :goto_0

    :cond_0
    if-ltz p2, :cond_1

    .line 556
    array-length v1, p1

    if-ge p2, v1, :cond_1

    .line 558
    aget-object p2, p1, p2

    invoke-virtual {p2}, Lgroovyjarjaropenbeans/EventSetDescriptor;->getName()Ljava/lang/String;

    move-result-object p2

    goto :goto_0

    :cond_1
    const/4 p2, 0x0

    .line 561
    :goto_0
    array-length v1, p1

    const/4 v2, 0x0

    move v3, v2

    :goto_1
    if-ge v3, v1, :cond_3

    aget-object v4, p1, v3

    .line 562
    invoke-virtual {v4}, Lgroovyjarjaropenbeans/EventSetDescriptor;->getName()Ljava/lang/String;

    move-result-object v5

    .line 563
    invoke-virtual {v0, v5}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjaropenbeans/EventSetDescriptor;

    if-nez v6, :cond_2

    .line 565
    invoke-virtual {v0, v5, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    .line 567
    :cond_2
    invoke-virtual {v6, v4}, Lgroovyjarjaropenbeans/EventSetDescriptor;->merge(Lgroovyjarjaropenbeans/EventSetDescriptor;)V

    :goto_2
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 571
    :cond_3
    invoke-virtual {v0}, Ljava/util/HashMap;->size()I

    move-result p1

    new-array v1, p1, [Lgroovyjarjaropenbeans/EventSetDescriptor;

    .line 572
    invoke-virtual {v0}, Ljava/util/HashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0, v1}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    if-eqz p2, :cond_5

    .line 574
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitEvents:Z

    if-nez v0, :cond_5

    :goto_3
    if-ge v2, p1, :cond_5

    .line 576
    aget-object v0, v1, v2

    invoke-virtual {v0}, Lgroovyjarjaropenbeans/EventSetDescriptor;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 577
    iput v2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultEventIndex:I

    goto :goto_4

    :cond_4
    add-int/lit8 v2, v2, 0x1

    goto :goto_3

    :cond_5
    :goto_4
    return-object v1
.end method

.method private mergeMethods([Lgroovyjarjaropenbeans/MethodDescriptor;)[Lgroovyjarjaropenbeans/MethodDescriptor;
    .locals 6

    .line 534
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    invoke-static {v0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->internalAsMap([Lgroovyjarjaropenbeans/MethodDescriptor;)Ljava/util/HashMap;

    move-result-object v0

    .line 536
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, p1, v2

    .line 537
    invoke-virtual {v3}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v4

    invoke-static {v4}, Lgroovyjarjaropenbeans/StandardBeanInfo;->getQualifiedName(Ljava/lang/reflect/Method;)Ljava/lang/String;

    move-result-object v4

    .line 538
    invoke-virtual {v0, v4}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjaropenbeans/MethodDescriptor;

    if-nez v5, :cond_0

    .line 540
    invoke-virtual {v0, v4, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 542
    :cond_0
    invoke-virtual {v5, v3}, Lgroovyjarjaropenbeans/MethodDescriptor;->merge(Lgroovyjarjaropenbeans/MethodDescriptor;)V

    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 545
    :cond_1
    invoke-virtual {v0}, Ljava/util/HashMap;->size()I

    move-result p1

    new-array p1, p1, [Lgroovyjarjaropenbeans/MethodDescriptor;

    .line 546
    invoke-virtual {v0}, Ljava/util/HashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    return-object p1
.end method

.method private mergeProps([Lgroovyjarjaropenbeans/PropertyDescriptor;I)[Lgroovyjarjaropenbeans/PropertyDescriptor;
    .locals 18
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/IntrospectionException;
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    .line 263
    iget-object v3, v0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    invoke-static {v3}, Lgroovyjarjaropenbeans/StandardBeanInfo;->internalAsMap([Lgroovyjarjaropenbeans/PropertyDescriptor;)Ljava/util/HashMap;

    move-result-object v3

    .line 265
    iget v4, v0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultPropertyIndex:I

    if-ltz v4, :cond_0

    iget-object v6, v0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    array-length v7, v6

    if-ge v4, v7, :cond_0

    .line 267
    aget-object v2, v6, v4

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getName()Ljava/lang/String;

    move-result-object v2

    goto :goto_0

    :cond_0
    if-ltz v2, :cond_1

    .line 268
    array-length v4, v1

    if-ge v2, v4, :cond_1

    .line 270
    aget-object v2, v1, v2

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getName()Ljava/lang/String;

    move-result-object v2

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :goto_0
    const/4 v6, 0x0

    .line 273
    :goto_1
    array-length v7, v1

    if-ge v6, v7, :cond_20

    .line 274
    aget-object v7, v1, v6

    .line 275
    invoke-virtual {v7}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getName()Ljava/lang/String;

    move-result-object v8

    .line 276
    invoke-virtual {v3, v8}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v9

    if-nez v9, :cond_2

    .line 277
    invoke-virtual {v3, v8, v7}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-object/from16 v16, v2

    move/from16 v17, v6

    const/4 v6, 0x0

    goto/16 :goto_e

    .line 281
    :cond_2
    invoke-virtual {v3, v8}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    .line 283
    move-object v10, v9

    check-cast v10, Lgroovyjarjaropenbeans/PropertyDescriptor;

    invoke-virtual {v10}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getReadMethod()Ljava/lang/reflect/Method;

    move-result-object v11

    .line 284
    invoke-virtual {v10}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getWriteMethod()Ljava/lang/reflect/Method;

    move-result-object v12

    .line 285
    invoke-virtual {v7}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getReadMethod()Ljava/lang/reflect/Method;

    move-result-object v13

    .line 286
    invoke-virtual {v7}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getWriteMethod()Ljava/lang/reflect/Method;

    move-result-object v14

    .line 288
    invoke-virtual {v7}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getPropertyType()Ljava/lang/Class;

    move-result-object v15

    .line 290
    invoke-virtual {v10}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getPropertyType()Ljava/lang/Class;

    move-result-object v5

    .line 293
    instance-of v4, v9, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;

    if-eqz v4, :cond_3

    .line 294
    move-object v4, v9

    check-cast v4, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;

    .line 295
    invoke-virtual {v4}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->getIndexedPropertyType()Ljava/lang/Class;

    move-result-object v4

    goto :goto_2

    :cond_3
    const/4 v4, 0x0

    .line 297
    :goto_2
    instance-of v1, v7, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;

    if-eqz v1, :cond_4

    .line 298
    move-object v1, v7

    check-cast v1, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;

    .line 299
    invoke-virtual {v1}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->getIndexedPropertyType()Ljava/lang/Class;

    move-result-object v1

    move-object/from16 v16, v2

    goto :goto_3

    :cond_4
    move-object/from16 v16, v2

    const/4 v1, 0x0

    :goto_3
    const-string v2, "is"

    move/from16 v17, v6

    const/4 v6, 0x1

    if-nez v1, :cond_11

    if-nez v4, :cond_c

    if-eqz v5, :cond_9

    if-eqz v15, :cond_9

    .line 309
    invoke-virtual {v5}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_9

    .line 310
    invoke-virtual {v5}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v15}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v1, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_9

    if-eqz v13, :cond_6

    if-eqz v11, :cond_5

    .line 312
    invoke-virtual {v13, v11}, Ljava/lang/reflect/Method;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    .line 313
    :cond_5
    invoke-virtual {v10, v13}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setReadMethod(Ljava/lang/reflect/Method;)V

    :cond_6
    if-eqz v14, :cond_8

    if-eqz v12, :cond_7

    .line 316
    invoke-virtual {v14, v12}, Ljava/lang/reflect/Method;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_8

    .line 317
    :cond_7
    invoke-virtual {v10, v14}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setWriteMethod(Ljava/lang/reflect/Method;)V

    .line 319
    :cond_8
    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    if-ne v5, v1, :cond_f

    if-eqz v11, :cond_f

    if-eqz v13, :cond_f

    .line 321
    invoke-virtual {v13}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1, v2}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_f

    .line 322
    invoke-virtual {v10, v13}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setReadMethod(Ljava/lang/reflect/Method;)V

    goto/16 :goto_5

    :cond_9
    if-eqz v11, :cond_a

    if-nez v12, :cond_f

    :cond_a
    if-eqz v13, :cond_f

    .line 328
    new-instance v1, Lgroovyjarjaropenbeans/PropertyDescriptor;

    invoke-direct {v1, v8, v13, v14}, Lgroovyjarjaropenbeans/PropertyDescriptor;-><init>(Ljava/lang/String;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;)V

    if-eqz v11, :cond_10

    .line 331
    invoke-virtual {v11}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v2

    .line 333
    invoke-direct/range {p0 .. p0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->introspectMethods()[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object v4

    .line 334
    array-length v5, v4

    const/4 v6, 0x0

    :goto_4
    if-ge v6, v5, :cond_10

    aget-object v9, v4, v6

    .line 335
    invoke-virtual {v9}, Lgroovyjarjaropenbeans/MethodDescriptor;->getMethod()Ljava/lang/reflect/Method;

    move-result-object v9

    if-eq v9, v11, :cond_b

    .line 338
    invoke-virtual {v9}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v12

    .line 337
    invoke-virtual {v2, v12}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v12

    if-eqz v12, :cond_b

    .line 339
    invoke-virtual {v9}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v12

    array-length v12, v12

    if-nez v12, :cond_b

    .line 340
    invoke-virtual {v9}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v12

    if-ne v12, v15, :cond_b

    .line 341
    invoke-virtual {v1, v9}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setReadMethod(Ljava/lang/reflect/Method;)V

    goto :goto_6

    :cond_b
    add-int/lit8 v6, v6, 0x1

    goto :goto_4

    :cond_c
    if-eqz v15, :cond_e

    .line 350
    invoke-virtual {v15}, Ljava/lang/Class;->isArray()Z

    move-result v1

    if-eqz v1, :cond_e

    .line 351
    invoke-virtual {v15}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    .line 352
    invoke-virtual {v4}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_e

    if-nez v11, :cond_d

    if-eqz v13, :cond_d

    .line 354
    invoke-virtual {v10, v13}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setReadMethod(Ljava/lang/reflect/Method;)V

    :cond_d
    if-nez v12, :cond_e

    if-eqz v14, :cond_e

    .line 357
    invoke-virtual {v10, v14}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setWriteMethod(Ljava/lang/reflect/Method;)V

    .line 361
    :cond_e
    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    if-ne v4, v1, :cond_f

    sget-object v1, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    if-ne v15, v1, :cond_f

    .line 363
    move-object v1, v10

    check-cast v1, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;

    .line 364
    invoke-virtual {v1}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->getIndexedWriteMethod()Ljava/lang/reflect/Method;

    move-result-object v1

    if-nez v11, :cond_f

    if-nez v12, :cond_f

    if-eqz v1, :cond_f

    if-eqz v13, :cond_f

    .line 368
    :try_start_0
    iget-object v2, v0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanClass:Ljava/lang/Class;

    .line 369
    invoke-virtual {v1}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v1

    new-array v4, v6, [Ljava/lang/Class;

    sget-object v5, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    const/4 v6, 0x0

    aput-object v5, v4, v6

    .line 368
    invoke-virtual {v2, v1, v4}, Ljava/lang/Class;->getDeclaredMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v12
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    if-eqz v12, :cond_f

    .line 375
    new-instance v1, Lgroovyjarjaropenbeans/PropertyDescriptor;

    invoke-direct {v1, v8, v13, v12}, Lgroovyjarjaropenbeans/PropertyDescriptor;-><init>(Ljava/lang/String;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;)V

    goto :goto_6

    :cond_f
    :goto_5
    move-object v1, v10

    .line 381
    :cond_10
    :goto_6
    invoke-virtual {v3, v8, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_7
    const/4 v6, 0x0

    goto/16 :goto_d

    :cond_11
    if-nez v4, :cond_1a

    if-eqz v5, :cond_14

    .line 385
    invoke-virtual {v5}, Ljava/lang/Class;->isArray()Z

    move-result v4

    if-eqz v4, :cond_14

    .line 386
    invoke-virtual {v5}, Ljava/lang/Class;->getComponentType()Ljava/lang/Class;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v4

    .line 387
    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_14

    if-eqz v11, :cond_12

    .line 390
    invoke-virtual {v7, v11}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setReadMethod(Ljava/lang/reflect/Method;)V

    :cond_12
    if-eqz v12, :cond_13

    .line 393
    invoke-virtual {v7, v12}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setWriteMethod(Ljava/lang/reflect/Method;)V

    .line 395
    :cond_13
    invoke-virtual {v3, v8, v7}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_7

    :cond_14
    if-eqz v11, :cond_16

    if-nez v12, :cond_15

    goto :goto_9

    :cond_15
    :goto_8
    const/4 v6, 0x0

    goto/16 :goto_c

    .line 402
    :cond_16
    :goto_9
    iget-object v1, v0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanClass:Ljava/lang/Class;

    invoke-virtual {v1}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object v1

    .line 403
    invoke-direct {v0, v8}, Lgroovyjarjaropenbeans/StandardBeanInfo;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    if-nez v11, :cond_18

    .line 407
    sget-object v6, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    if-ne v5, v6, :cond_17

    .line 409
    :try_start_1
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v4, 0x0

    new-array v6, v4, [Ljava/lang/Class;

    .line 410
    invoke-virtual {v1, v2, v6}, Ljava/lang/Class;->getDeclaredMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v1

    goto :goto_a

    .line 417
    :cond_17
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "get"

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const/4 v4, 0x0

    new-array v6, v4, [Ljava/lang/Class;

    .line 418
    invoke-virtual {v1, v2, v6}, Ljava/lang/Class;->getDeclaredMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v1
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_a

    :catch_1
    const/4 v1, 0x0

    :goto_a
    if-eqz v1, :cond_15

    .line 426
    invoke-virtual {v1}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result v2

    .line 425
    invoke-static {v2}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v2

    if-nez v2, :cond_15

    .line 427
    invoke-virtual {v1}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v2

    if-ne v2, v5, :cond_15

    .line 429
    invoke-virtual {v10, v1}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setReadMethod(Ljava/lang/reflect/Method;)V

    goto :goto_8

    .line 434
    :cond_18
    :try_start_2
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "set"

    invoke-virtual {v2, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    new-array v4, v6, [Ljava/lang/Class;
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_2

    const/4 v6, 0x0

    :try_start_3
    aput-object v5, v4, v6

    invoke-virtual {v1, v2, v4}, Ljava/lang/Class;->getDeclaredMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object v1
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_3 .. :try_end_3} :catch_3

    goto :goto_b

    :catch_2
    const/4 v6, 0x0

    :catch_3
    const/4 v1, 0x0

    :goto_b
    if-eqz v1, :cond_19

    .line 441
    invoke-virtual {v1}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result v2

    .line 440
    invoke-static {v2}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v2

    if-nez v2, :cond_19

    .line 442
    invoke-virtual {v1}, Ljava/lang/reflect/Method;->getReturnType()Ljava/lang/Class;

    move-result-object v2

    sget-object v4, Ljava/lang/Void;->TYPE:Ljava/lang/Class;

    if-ne v2, v4, :cond_19

    .line 444
    invoke-virtual {v10, v1}, Lgroovyjarjaropenbeans/PropertyDescriptor;->setWriteMethod(Ljava/lang/reflect/Method;)V

    .line 448
    :cond_19
    :goto_c
    invoke-virtual {v3, v8, v10}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_d

    :cond_1a
    const/4 v6, 0x0

    .line 450
    invoke-virtual {v4}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    .line 451
    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    .line 450
    invoke-virtual {v2, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1f

    .line 453
    check-cast v9, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;

    if-nez v11, :cond_1b

    if-eqz v13, :cond_1b

    .line 455
    invoke-virtual {v9, v13}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->setReadMethod(Ljava/lang/reflect/Method;)V

    :cond_1b
    if-nez v12, :cond_1c

    if-eqz v14, :cond_1c

    .line 458
    invoke-virtual {v9, v14}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->setWriteMethod(Ljava/lang/reflect/Method;)V

    .line 460
    :cond_1c
    move-object v1, v7

    check-cast v1, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;

    .line 462
    invoke-virtual {v9}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->getIndexedReadMethod()Ljava/lang/reflect/Method;

    move-result-object v2

    if-nez v2, :cond_1d

    .line 463
    invoke-virtual {v1}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->getIndexedReadMethod()Ljava/lang/reflect/Method;

    move-result-object v2

    if-eqz v2, :cond_1d

    .line 465
    invoke-virtual {v1}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->getIndexedReadMethod()Ljava/lang/reflect/Method;

    move-result-object v2

    .line 464
    invoke-virtual {v9, v2}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->setIndexedReadMethod(Ljava/lang/reflect/Method;)V

    .line 468
    :cond_1d
    invoke-virtual {v9}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->getIndexedWriteMethod()Ljava/lang/reflect/Method;

    move-result-object v2

    if-nez v2, :cond_1e

    .line 469
    invoke-virtual {v1}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->getIndexedWriteMethod()Ljava/lang/reflect/Method;

    move-result-object v2

    if-eqz v2, :cond_1e

    .line 471
    invoke-virtual {v1}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->getIndexedWriteMethod()Ljava/lang/reflect/Method;

    move-result-object v1

    .line 470
    invoke-virtual {v9, v1}, Lgroovyjarjaropenbeans/IndexedPropertyDescriptor;->setIndexedWriteMethod(Ljava/lang/reflect/Method;)V

    .line 474
    :cond_1e
    invoke-virtual {v3, v8, v9}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 477
    :cond_1f
    :goto_d
    invoke-static {v10, v7}, Lgroovyjarjaropenbeans/StandardBeanInfo;->mergeAttributes(Lgroovyjarjaropenbeans/PropertyDescriptor;Lgroovyjarjaropenbeans/PropertyDescriptor;)V

    :goto_e
    add-int/lit8 v1, v17, 0x1

    move v6, v1

    move-object/from16 v2, v16

    move-object/from16 v1, p1

    goto/16 :goto_1

    :cond_20
    move-object/from16 v16, v2

    const/4 v6, 0x0

    .line 480
    invoke-virtual {v3}, Ljava/util/HashMap;->size()I

    move-result v1

    new-array v2, v1, [Lgroovyjarjaropenbeans/PropertyDescriptor;

    .line 481
    invoke-virtual {v3}, Ljava/util/HashMap;->values()Ljava/util/Collection;

    move-result-object v3

    invoke-interface {v3, v2}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    if-eqz v16, :cond_22

    .line 483
    iget-boolean v3, v0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitProperties:Z

    if-nez v3, :cond_22

    move v4, v6

    :goto_f
    if-ge v4, v1, :cond_22

    .line 485
    aget-object v3, v2, v4

    invoke-virtual {v3}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getName()Ljava/lang/String;

    move-result-object v3

    move-object/from16 v5, v16

    invoke-virtual {v5, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_21

    .line 486
    iput v4, v0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultPropertyIndex:I

    goto :goto_10

    :cond_21
    add-int/lit8 v4, v4, 0x1

    move-object/from16 v16, v5

    goto :goto_f

    :cond_22
    :goto_10
    return-object v2
.end method


# virtual methods
.method public getAdditionalBeanInfo()[Lgroovyjarjaropenbeans/BeanInfo;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getBeanDescriptor()Lgroovyjarjaropenbeans/BeanDescriptor;
    .locals 2

    .line 190
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanDescriptor:Lgroovyjarjaropenbeans/BeanDescriptor;

    if-nez v0, :cond_1

    .line 191
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitBeanInfo:Lgroovyjarjaropenbeans/BeanInfo;

    if-eqz v0, :cond_0

    .line 192
    invoke-interface {v0}, Lgroovyjarjaropenbeans/BeanInfo;->getBeanDescriptor()Lgroovyjarjaropenbeans/BeanDescriptor;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanDescriptor:Lgroovyjarjaropenbeans/BeanDescriptor;

    .line 194
    :cond_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanDescriptor:Lgroovyjarjaropenbeans/BeanDescriptor;

    if-nez v0, :cond_1

    .line 195
    new-instance v0, Lgroovyjarjaropenbeans/BeanDescriptor;

    iget-object v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanClass:Ljava/lang/Class;

    invoke-direct {v0, v1}, Lgroovyjarjaropenbeans/BeanDescriptor;-><init>(Ljava/lang/Class;)V

    iput-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanDescriptor:Lgroovyjarjaropenbeans/BeanDescriptor;

    .line 198
    :cond_1
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->beanDescriptor:Lgroovyjarjaropenbeans/BeanDescriptor;

    return-object v0
.end method

.method public getDefaultEventIndex()I
    .locals 1

    .line 203
    iget v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultEventIndex:I

    return v0
.end method

.method public getDefaultPropertyIndex()I
    .locals 1

    .line 208
    iget v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultPropertyIndex:I

    return v0
.end method

.method public getEventSetDescriptors()[Lgroovyjarjaropenbeans/EventSetDescriptor;
    .locals 1

    .line 175
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    return-object v0
.end method

.method public getIcon(I)Ljava/awt/Image;
    .locals 1

    .line 213
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->icon:[Ljava/lang/Object;

    add-int/lit8 p1, p1, -0x1

    aget-object p1, v0, p1

    check-cast p1, Ljava/awt/Image;

    return-object p1
.end method

.method public getMethodDescriptors()[Lgroovyjarjaropenbeans/MethodDescriptor;
    .locals 1

    .line 180
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    return-object v0
.end method

.method public getPropertyDescriptors()[Lgroovyjarjaropenbeans/PropertyDescriptor;
    .locals 1

    .line 185
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    return-object v0
.end method

.method init()V
    .locals 4

    .line 1503
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    new-array v0, v1, [Lgroovyjarjaropenbeans/EventSetDescriptor;

    .line 1504
    iput-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    .line 1506
    :cond_0
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    if-nez v0, :cond_1

    new-array v0, v1, [Lgroovyjarjaropenbeans/PropertyDescriptor;

    .line 1507
    iput-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    .line 1510
    :cond_1
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    if-eqz v0, :cond_4

    .line 1511
    iget v2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultPropertyIndex:I

    const/4 v3, -0x1

    if-eq v2, v3, :cond_2

    aget-object v0, v0, v2

    .line 1512
    invoke-virtual {v0}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getName()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    .line 1514
    :goto_0
    iget-object v2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    sget-object v3, Lgroovyjarjaropenbeans/StandardBeanInfo;->comparator:Lgroovyjarjaropenbeans/StandardBeanInfo$PropertyComparator;

    invoke-static {v2, v3}, Ljava/util/Arrays;->sort([Ljava/lang/Object;Ljava/util/Comparator;)V

    if-eqz v0, :cond_4

    .line 1516
    :goto_1
    iget-object v2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    array-length v3, v2

    if-ge v1, v3, :cond_4

    .line 1517
    aget-object v2, v2, v1

    invoke-virtual {v2}, Lgroovyjarjaropenbeans/PropertyDescriptor;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_3

    .line 1518
    iput v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultPropertyIndex:I

    goto :goto_2

    :cond_3
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_4
    :goto_2
    return-void
.end method

.method mergeBeanInfo(Lgroovyjarjaropenbeans/BeanInfo;Z)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjaropenbeans/IntrospectionException;
        }
    .end annotation

    if-nez p2, :cond_0

    .line 218
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitProperties:Z

    if-nez v0, :cond_2

    .line 219
    :cond_0
    invoke-interface {p1}, Lgroovyjarjaropenbeans/BeanInfo;->getPropertyDescriptors()[Lgroovyjarjaropenbeans/PropertyDescriptor;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 221
    invoke-virtual {p0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->getPropertyDescriptors()[Lgroovyjarjaropenbeans/PropertyDescriptor;

    move-result-object v1

    if-eqz v1, :cond_1

    .line 223
    invoke-interface {p1}, Lgroovyjarjaropenbeans/BeanInfo;->getDefaultPropertyIndex()I

    move-result v1

    .line 222
    invoke-direct {p0, v0, v1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->mergeProps([Lgroovyjarjaropenbeans/PropertyDescriptor;I)[Lgroovyjarjaropenbeans/PropertyDescriptor;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    goto :goto_0

    .line 225
    :cond_1
    iput-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->properties:[Lgroovyjarjaropenbeans/PropertyDescriptor;

    .line 226
    invoke-interface {p1}, Lgroovyjarjaropenbeans/BeanInfo;->getDefaultPropertyIndex()I

    move-result v0

    iput v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultPropertyIndex:I

    :cond_2
    :goto_0
    if-nez p2, :cond_3

    .line 231
    iget-boolean v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitMethods:Z

    if-nez v0, :cond_5

    .line 232
    :cond_3
    invoke-interface {p1}, Lgroovyjarjaropenbeans/BeanInfo;->getMethodDescriptors()[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object v0

    if-eqz v0, :cond_5

    .line 234
    iget-object v1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    if-eqz v1, :cond_4

    .line 235
    invoke-direct {p0, v0}, Lgroovyjarjaropenbeans/StandardBeanInfo;->mergeMethods([Lgroovyjarjaropenbeans/MethodDescriptor;)[Lgroovyjarjaropenbeans/MethodDescriptor;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    goto :goto_1

    .line 237
    :cond_4
    iput-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->methods:[Lgroovyjarjaropenbeans/MethodDescriptor;

    :cond_5
    :goto_1
    if-nez p2, :cond_6

    .line 242
    iget-boolean p2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->explicitEvents:Z

    if-nez p2, :cond_8

    .line 244
    :cond_6
    invoke-interface {p1}, Lgroovyjarjaropenbeans/BeanInfo;->getEventSetDescriptors()[Lgroovyjarjaropenbeans/EventSetDescriptor;

    move-result-object p2

    if-eqz p2, :cond_8

    .line 246
    iget-object v0, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    if-eqz v0, :cond_7

    .line 248
    invoke-interface {p1}, Lgroovyjarjaropenbeans/BeanInfo;->getDefaultEventIndex()I

    move-result p1

    .line 247
    invoke-direct {p0, p2, p1}, Lgroovyjarjaropenbeans/StandardBeanInfo;->mergeEvents([Lgroovyjarjaropenbeans/EventSetDescriptor;I)[Lgroovyjarjaropenbeans/EventSetDescriptor;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    goto :goto_2

    .line 250
    :cond_7
    iput-object p2, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->events:[Lgroovyjarjaropenbeans/EventSetDescriptor;

    .line 251
    invoke-interface {p1}, Lgroovyjarjaropenbeans/BeanInfo;->getDefaultEventIndex()I

    move-result p1

    iput p1, p0, Lgroovyjarjaropenbeans/StandardBeanInfo;->defaultEventIndex:I

    :cond_8
    :goto_2
    return-void
.end method
