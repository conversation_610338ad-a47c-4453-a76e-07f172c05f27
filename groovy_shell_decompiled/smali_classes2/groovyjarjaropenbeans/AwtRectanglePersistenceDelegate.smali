.class Lgroovyjarjaropenbeans/AwtRectanglePersistenceDelegate;
.super Lgroovyjarjaropenbeans/DefaultPersistenceDelegate;
.source "AwtRectanglePersistenceDelegate.java"


# direct methods
.method constructor <init>()V
    .locals 0

    .line 27
    invoke-direct {p0}, Lgroovyjarjaropenbeans/DefaultPersistenceDelegate;-><init>()V

    return-void
.end method


# virtual methods
.method protected initialize(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;Lgroovyjarjaropenbeans/Encoder;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "Lgroovyjarjaropenbeans/Encoder;",
            ")V"
        }
    .end annotation

    return-void
.end method

.method protected instantiate(Ljava/lang/Object;Lgroovyjarjaropenbeans/Encoder;)Lgroovyjarjaropenbeans/Expression;
    .locals 4

    .line 42
    check-cast p1, Ljava/awt/Rectangle;

    .line 43
    new-instance p2, Lgroovyjarjaropenbeans/Expression;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const/4 v1, 0x4

    new-array v1, v1, [Ljava/lang/Object;

    iget v2, p1, Ljava/awt/Rectangle;->x:I

    .line 44
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const/4 v3, 0x0

    aput-object v2, v1, v3

    iget v2, p1, Ljava/awt/Rectangle;->y:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const/4 v3, 0x1

    aput-object v2, v1, v3

    iget v2, p1, Ljava/awt/Rectangle;->width:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const/4 v3, 0x2

    aput-object v2, v1, v3

    iget v2, p1, Ljava/awt/Rectangle;->height:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const/4 v3, 0x3

    aput-object v2, v1, v3

    const-string v2, "new"

    invoke-direct {p2, p1, v0, v2, v1}, Lgroovyjarjaropenbeans/Expression;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object p2
.end method

.method protected mutatesTo(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 31
    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
