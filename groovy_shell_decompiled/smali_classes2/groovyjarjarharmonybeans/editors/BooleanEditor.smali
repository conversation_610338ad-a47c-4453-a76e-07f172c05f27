.class public final Lgroovyjarjarharmonybeans/editors/BooleanEditor;
.super Lgroovyjarjaropenbeans/PropertyEditorSupport;
.source "BooleanEditor.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 29
    invoke-direct {p0}, Lgroovyjarjaropenbeans/PropertyEditorSupport;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;)V
    .locals 0

    .line 25
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/PropertyEditorSupport;-><init>(Ljava/lang/Object;)V

    return-void
.end method

.method private getValueAsString()Ljava/lang/String;
    .locals 1

    .line 71
    invoke-virtual {p0}, Lgroovyjarjarharmonybeans/editors/BooleanEditor;->getValue()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 73
    check-cast v0, Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method


# virtual methods
.method public getAsText()Ljava/lang/String;
    .locals 2

    .line 46
    invoke-virtual {p0}, Lgroovyjarjarharmonybeans/editors/BooleanEditor;->getValue()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 50
    :cond_0
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {v1, v0}, Ljava/lang/Boolean;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    const-string v0, "True"

    goto :goto_0

    :cond_1
    const-string v0, "False"

    :goto_0
    return-object v0
.end method

.method public getJavaInitializationString()Ljava/lang/String;
    .locals 1

    .line 55
    invoke-direct {p0}, Lgroovyjarjarharmonybeans/editors/BooleanEditor;->getValueAsString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getTags()[Ljava/lang/String;
    .locals 2

    const-string v0, "True"

    const-string v1, "False"

    .line 60
    filled-new-array {v0, v1}, [Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public setAsText(Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalArgumentException;
        }
    .end annotation

    .line 35
    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "true"

    .line 37
    invoke-virtual {v0, p1}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_1

    const-string v0, "false"

    invoke-virtual {v0, p1}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 40
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 38
    :cond_1
    :goto_0
    new-instance v0, Ljava/lang/Boolean;

    invoke-direct {v0, p1}, Ljava/lang/Boolean;-><init>(Ljava/lang/String;)V

    invoke-virtual {p0, v0}, Lgroovyjarjarharmonybeans/editors/BooleanEditor;->setValue(Ljava/lang/Object;)V

    return-void
.end method

.method public setValue(Ljava/lang/Object;)V
    .locals 1

    .line 65
    instance-of v0, p1, Ljava/lang/Boolean;

    if-eqz v0, :cond_0

    .line 66
    invoke-super {p0, p1}, Lgroovyjarjaropenbeans/PropertyEditorSupport;->setValue(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method
