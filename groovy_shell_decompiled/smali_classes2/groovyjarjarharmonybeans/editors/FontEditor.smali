.class public Lgroovyjarjarharmonybeans/editors/FontEditor;
.super Ljava/awt/Panel;
.source "FontEditor.java"

# interfaces
.implements Lgroovyjarjaropenbeans/PropertyEditor;


# instance fields
.field listeners:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjaropenbeans/PropertyChangeListener;",
            ">;"
        }
    .end annotation
.end field

.field private source:Ljava/lang/Object;

.field private value:Ljava/awt/Font;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 49
    invoke-direct {p0}, Ljava/awt/Panel;-><init>()V

    .line 35
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->listeners:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;)V
    .locals 1

    .line 41
    invoke-direct {p0}, Ljava/awt/Panel;-><init>()V

    .line 35
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->listeners:Ljava/util/List;

    .line 43
    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    .line 45
    iput-object p1, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->source:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public declared-synchronized addPropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    monitor-enter p0

    .line 125
    :try_start_0
    iget-object v0, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->listeners:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 126
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public firePropertyChange()V
    .locals 4

    .line 129
    iget-object v0, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->listeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 133
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->listeners:Ljava/util/List;

    .line 134
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 135
    iget-object v1, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->listeners:Ljava/util/List;

    monitor-enter v1

    .line 136
    :try_start_0
    iget-object v2, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->listeners:Ljava/util/List;

    invoke-interface {v0, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 137
    monitor-exit v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 139
    new-instance v1, Lgroovyjarjaropenbeans/PropertyChangeEvent;

    iget-object v2, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->source:Ljava/lang/Object;

    const/4 v3, 0x0

    invoke-direct {v1, v2, v3, v3, v3}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 141
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    .line 142
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    .line 143
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 144
    invoke-interface {v2, v1}, Lgroovyjarjaropenbeans/PropertyChangeListener;->propertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    goto :goto_0

    :cond_1
    return-void

    :catchall_0
    move-exception v0

    .line 137
    :try_start_1
    monitor-exit v1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v0
.end method

.method public getAsText()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getCustomEditor()Ljava/awt/Component;
    .locals 0

    return-object p0
.end method

.method public getJavaInitializationString()Ljava/lang/String;
    .locals 3

    .line 61
    iget-object v0, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->value:Ljava/awt/Font;

    if-eqz v0, :cond_0

    .line 62
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "new Font("

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 63
    iget-object v1, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->value:Ljava/awt/Font;

    invoke-virtual {v1}, Ljava/awt/Font;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x2c

    .line 64
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 65
    iget-object v2, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->value:Ljava/awt/Font;

    invoke-virtual {v2}, Ljava/awt/Font;->getStyle()I

    move-result v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 66
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 67
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->value:Ljava/awt/Font;

    invoke-virtual {v2}, Ljava/awt/Font;->getSize()I

    move-result v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ")"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 68
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public getTags()[Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getValue()Ljava/lang/Object;
    .locals 1

    .line 107
    iget-object v0, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->value:Ljava/awt/Font;

    return-object v0
.end method

.method public isPaintable()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public paintValue(Ljava/awt/Graphics;Ljava/awt/Rectangle;)V
    .locals 7

    .line 94
    invoke-virtual {p0}, Lgroovyjarjarharmonybeans/editors/FontEditor;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/awt/Font;

    if-eqz v0, :cond_0

    .line 96
    invoke-virtual {p1, v0}, Ljava/awt/Graphics;->setFont(Ljava/awt/Font;)V

    const-string v0, "Hello"

    .line 97
    invoke-virtual {v0}, Ljava/lang/String;->getBytes()[B

    move-result-object v2

    iget v3, p2, Ljava/awt/Rectangle;->x:I

    iget v4, p2, Ljava/awt/Rectangle;->y:I

    iget v0, p2, Ljava/awt/Rectangle;->x:I

    iget v1, p2, Ljava/awt/Rectangle;->width:I

    add-int v5, v0, v1

    iget v0, p2, Ljava/awt/Rectangle;->y:I

    iget p2, p2, Ljava/awt/Rectangle;->height:I

    add-int v6, v0, p2

    move-object v1, p1

    invoke-virtual/range {v1 .. v6}, Ljava/awt/Graphics;->drawBytes([BIIII)V

    :cond_0
    return-void
.end method

.method public declared-synchronized removePropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    monitor-enter p0

    .line 117
    :try_start_0
    iget-object v0, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->listeners:Ljava/util/List;

    if-eqz v0, :cond_0

    .line 118
    invoke-interface {v0, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 120
    :cond_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public setAsText(Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalArgumentException;
        }
    .end annotation

    .line 111
    new-instance v0, Ljava/lang/IllegalArgumentException;

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->value:Ljava/awt/Font;

    invoke-virtual {p1}, Ljava/awt/Font;->toString()Ljava/lang/String;

    move-result-object p1

    :goto_0
    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public setValue(Ljava/lang/Object;)V
    .locals 4

    .line 78
    iget-object v0, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->value:Ljava/awt/Font;

    .line 79
    check-cast p1, Ljava/awt/Font;

    iput-object p1, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->value:Ljava/awt/Font;

    .line 80
    new-instance p1, Lgroovyjarjaropenbeans/PropertyChangeEvent;

    iget-object v1, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->value:Ljava/awt/Font;

    const-string v2, "value"

    invoke-direct {p1, p0, v2, v0, v1}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 82
    iget-object v0, p0, Lgroovyjarjarharmonybeans/editors/FontEditor;->listeners:Ljava/util/List;

    const/4 v1, 0x0

    new-array v2, v1, [Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 83
    invoke-interface {v0, v2}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjaropenbeans/PropertyChangeListener;

    .line 84
    array-length v2, v0

    :goto_0
    if-ge v1, v2, :cond_0

    aget-object v3, v0, v1

    .line 85
    invoke-interface {v3, p1}, Lgroovyjarjaropenbeans/PropertyChangeListener;->propertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public supportsCustomEditor()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
