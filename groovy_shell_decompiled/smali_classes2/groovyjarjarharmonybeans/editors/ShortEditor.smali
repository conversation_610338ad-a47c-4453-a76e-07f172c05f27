.class public final Lgroovyjarjarharmonybeans/editors/ShortEditor;
.super Lgroovyjarjaropenbeans/PropertyEditorSupport;
.source "ShortEditor.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 29
    invoke-direct {p0}, Lgroovyjarjaropenbeans/PropertyEditorSupport;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;)V
    .locals 0

    .line 25
    invoke-direct {p0, p1}, Lgroovyjarjaropenbeans/PropertyEditorSupport;-><init>(Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public getJavaInitializationString()Ljava/lang/String;
    .locals 2

    .line 39
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "((short)"

    .line 40
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarharmonybeans/editors/ShortEditor;->getValue()Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getTags()[Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public setAsText(Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalArgumentException;
        }
    .end annotation

    .line 34
    new-instance v0, Ljava/lang/Short;

    invoke-direct {v0, p1}, Ljava/lang/Short;-><init>(Ljava/lang/String;)V

    invoke-virtual {p0, v0}, Lgroovyjarjarharmonybeans/editors/ShortEditor;->setValue(Ljava/lang/Object;)V

    return-void
.end method

.method public setValue(Ljava/lang/Object;)V
    .locals 1

    .line 50
    instance-of v0, p1, Ljava/lang/Short;

    if-eqz v0, :cond_0

    .line 51
    invoke-super {p0, p1}, Lgroovyjarjaropenbeans/PropertyEditorSupport;->setValue(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method
