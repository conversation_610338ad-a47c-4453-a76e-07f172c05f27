.class public interface abstract Lgroovyjarjarpicocli/CommandLine$IExitCodeExceptionMapper;
.super Ljava/lang/Object;
.source "CommandLine.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "IExitCodeExceptionMapper"
.end annotation


# virtual methods
.method public abstract getExitCode(Ljava/lang/Throwable;)I
.end method
