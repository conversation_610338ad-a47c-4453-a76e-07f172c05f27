.class Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;
.super Ljava/lang/Object;
.source "CommandLine.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "MatchResult"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<V:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field private final fullName:Ljava/lang/String;

.field private final value:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TV;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "TV;)V"
        }
    .end annotation

    .line 18443
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 18444
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->fullName:Ljava/lang/String;

    .line 18445
    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->value:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 3

    .line 18462
    instance-of v0, p1, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 18465
    :cond_0
    check-cast p1, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;

    .line 18466
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->fullName:Ljava/lang/String;

    iget-object v2, p1, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->fullName:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->hasValue()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->value:Ljava/lang/Object;

    iget-object p1, p1, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->value:Ljava/lang/Object;

    invoke-virtual {v0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->value:Ljava/lang/Object;

    iget-object p1, p1, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->value:Ljava/lang/Object;

    if-ne v0, p1, :cond_2

    :goto_0
    const/4 v1, 0x1

    :cond_2
    return v1
.end method

.method getFullName()Ljava/lang/String;
    .locals 1

    .line 18449
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->fullName:Ljava/lang/String;

    return-object v0
.end method

.method getValue()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TV;"
        }
    .end annotation

    .line 18457
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->value:Ljava/lang/Object;

    return-object v0
.end method

.method hasValue()Z
    .locals 1

    .line 18453
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->value:Ljava/lang/Object;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public hashCode()I
    .locals 2

    .line 18471
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->fullName:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$AbbreviationMatcher$MatchResult;->value:Ljava/lang/Object;

    if-nez v1, :cond_0

    const/4 v1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    :goto_0
    xor-int/2addr v0, v1

    return v0
.end method
