.class Lgroovyjarjarpicocli/CommandLine$PositionalParametersSorter;
.super Ljava/lang/Object;
.source "CommandLine.java"

# interfaces
.implements Ljava/util/Comparator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "PositionalParametersSorter"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Comparator<",
        "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;",
        ">;"
    }
.end annotation


# static fields
.field private static final OPTION_INDEX:Lgroovyjarjarpicocli/CommandLine$Range;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 14615
    new-instance v6, Lgroovyjarjarpicocli/CommandLine$Range;

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x1

    const-string v5, "0"

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarpicocli/CommandLine$Range;-><init>(IIZZLjava/lang/String;)V

    sput-object v6, Lgroovyjarjarpicocli/CommandLine$PositionalParametersSorter;->OPTION_INDEX:Lgroovyjarjarpicocli/CommandLine$Range;

    return-void
.end method

.method constructor <init>()V
    .locals 0

    .line 14614
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private index(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Range;
    .locals 1

    .line 14620
    invoke-virtual {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->isOption()Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object p1, Lgroovyjarjarpicocli/CommandLine$PositionalParametersSorter;->OPTION_INDEX:Lgroovyjarjarpicocli/CommandLine$Range;

    goto :goto_0

    :cond_0
    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    invoke-virtual {p1}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->index()Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object p1

    :goto_0
    return-object p1
.end method


# virtual methods
.method public compare(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)I
    .locals 2

    .line 14617
    invoke-direct {p0, p1}, Lgroovyjarjarpicocli/CommandLine$PositionalParametersSorter;->index(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object v0

    invoke-direct {p0, p2}, Lgroovyjarjarpicocli/CommandLine$PositionalParametersSorter;->index(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Range;->compareTo(Lgroovyjarjarpicocli/CommandLine$Range;)I

    move-result v0

    if-nez v0, :cond_0

    .line 14618
    invoke-virtual {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->arity()Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object p1

    invoke-virtual {p2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->arity()Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object p2

    invoke-virtual {p1, p2}, Lgroovyjarjarpicocli/CommandLine$Range;->compareTo(Lgroovyjarjarpicocli/CommandLine$Range;)I

    move-result v0

    :cond_0
    return v0
.end method

.method public bridge synthetic compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    .line 14614
    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    check-cast p2, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarpicocli/CommandLine$PositionalParametersSorter;->compare(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)I

    move-result p1

    return p1
.end method
