.class public interface abstract Lgroovyjarjarpicocli/CommandLine$IParseResultHandler;
.super Ljava/lang/Object;
.source "CommandLine.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "IParseResultHandler"
.end annotation

.annotation runtime Ljava/lang/Deprecated;
.end annotation


# virtual methods
.method public abstract handleParseResult(Ljava/util/List;Ljava/io/PrintStream;Lgroovyjarjarpicocli/CommandLine$Help$Ansi;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine;",
            ">;",
            "Ljava/io/PrintStream;",
            "Lgroovyjarjarpicocli/CommandLine$Help$Ansi;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarpicocli/CommandLine$ExecutionException;
        }
    .end annotation
.end method
