.class Lgroovyjarjarpicocli/CommandLine$Model$PicocliInvocationHandler;
.super Ljava/lang/Object;
.source "CommandLine.java"

# interfaces
.implements Ljava/lang/reflect/InvocationHandler;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine$Model;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "PicocliInvocationHandler"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarpicocli/CommandLine$Model$PicocliInvocationHandler$ProxyBinding;
    }
.end annotation


# instance fields
.field final map:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method private constructor <init>()V
    .locals 1

    .line 11815
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 11816
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$PicocliInvocationHandler;->map:Ljava/util/Map;

    return-void
.end method

.method synthetic constructor <init>(Lgroovyjarjarpicocli/CommandLine$1;)V
    .locals 0

    .line 11815
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Model$PicocliInvocationHandler;-><init>()V

    return-void
.end method


# virtual methods
.method public invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    .line 11818
    iget-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$PicocliInvocationHandler;->map:Ljava/util/Map;

    invoke-virtual {p2}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
