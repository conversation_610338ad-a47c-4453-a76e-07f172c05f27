.class public Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;
.super Ljava/lang/Object;
.source "CommandLine.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine$RegexTransformer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Builder"
.end annotation


# instance fields
.field replacements:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/util/regex/Pattern;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field synopsis:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/util/regex/Pattern;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 5324
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5321
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->replacements:Ljava/util/Map;

    .line 5322
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->synopsis:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarpicocli/CommandLine$RegexTransformer;)V
    .locals 2

    .line 5326
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5321
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->replacements:Ljava/util/Map;

    .line 5322
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->synopsis:Ljava/util/Map;

    .line 5327
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->replacements:Ljava/util/Map;

    iget-object v1, p1, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;->replacements:Ljava/util/Map;

    invoke-interface {v0, v1}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 5328
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->synopsis:Ljava/util/Map;

    iget-object p1, p1, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;->synopsis:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    return-void
.end method


# virtual methods
.method public addPattern(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;
    .locals 1

    .line 5361
    invoke-static {p1}, Ljava/util/regex/Pattern;->compile(Ljava/lang/String;)Ljava/util/regex/Pattern;

    move-result-object p1

    .line 5362
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->replacements:Ljava/util/Map;

    invoke-interface {v0, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 5363
    iget-object p2, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->synopsis:Ljava/util/Map;

    invoke-interface {p2, p1, p3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p0
.end method

.method public build()Lgroovyjarjarpicocli/CommandLine$RegexTransformer;
    .locals 1

    .line 5382
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;

    invoke-direct {v0, p0}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;-><init>(Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;)V

    return-object v0
.end method

.method public removePattern(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;
    .locals 3

    .line 5372
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->replacements:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 5373
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/regex/Pattern;

    .line 5374
    invoke-virtual {v1}, Ljava/util/regex/Pattern;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 5375
    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    .line 5376
    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->synopsis:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_1
    return-object p0
.end method
