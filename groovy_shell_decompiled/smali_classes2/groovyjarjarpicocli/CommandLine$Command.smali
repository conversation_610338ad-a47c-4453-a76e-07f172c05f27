.class public interface abstract annotation Lgroovyjarjarpicocli/CommandLine$Command;
.super Ljava/lang/Object;
.source "CommandLine.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovyjarjarpicocli/CommandLine$Command;
        abbreviateSynopsis = false
        addMethodSubcommands = true
        aliases = {}
        commandListHeading = "Commands:%n"
        customSynopsis = {}
        defaultValueProvider = Lgroovyjarjarpicocli/CommandLine$NoDefaultProvider;
        description = {}
        descriptionHeading = ""
        exitCodeList = {}
        exitCodeListHeading = ""
        exitCodeOnExecutionException = 0x1
        exitCodeOnInvalidInput = 0x2
        exitCodeOnSuccess = 0x0
        exitCodeOnUsageHelp = 0x0
        exitCodeOnVersionHelp = 0x0
        footer = {}
        footerHeading = ""
        header = {}
        headerHeading = ""
        helpCommand = false
        hidden = false
        mixinStandardHelpOptions = false
        modelTransformer = Lgroovyjarjarpicocli/CommandLine$NoOpModelTransformer;
        name = "<main class>"
        optionListHeading = ""
        parameterListHeading = ""
        preprocessor = Lgroovyjarjarpicocli/CommandLine$NoOpParameterPreprocessor;
        requiredOptionMarker = ' '
        resourceBundle = ""
        scope = .enum Lgroovyjarjarpicocli/CommandLine$ScopeType;->LOCAL:Lgroovyjarjarpicocli/CommandLine$ScopeType;
        separator = "="
        showAtFileInUsageHelp = false
        showDefaultValues = false
        showEndOfOptionsDelimiterInUsageHelp = false
        sortOptions = true
        subcommands = {}
        subcommandsRepeatable = false
        synopsisHeading = "Usage: "
        synopsisSubcommandLabel = "[COMMAND]"
        usageHelpAutoWidth = false
        usageHelpWidth = 0x50
        version = {}
        versionProvider = Lgroovyjarjarpicocli/CommandLine$NoVersionProvider;
    .end subannotation
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2609
    name = "Command"
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->LOCAL_VARIABLE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->FIELD:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->PACKAGE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->METHOD:Ljava/lang/annotation/ElementType;
    }
.end annotation


# virtual methods
.method public abstract abbreviateSynopsis()Z
.end method

.method public abstract addMethodSubcommands()Z
.end method

.method public abstract aliases()[Ljava/lang/String;
.end method

.method public abstract commandListHeading()Ljava/lang/String;
.end method

.method public abstract customSynopsis()[Ljava/lang/String;
.end method

.method public abstract defaultValueProvider()Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "+",
            "Lgroovyjarjarpicocli/CommandLine$IDefaultValueProvider;",
            ">;"
        }
    .end annotation
.end method

.method public abstract description()[Ljava/lang/String;
.end method

.method public abstract descriptionHeading()Ljava/lang/String;
.end method

.method public abstract exitCodeList()[Ljava/lang/String;
.end method

.method public abstract exitCodeListHeading()Ljava/lang/String;
.end method

.method public abstract exitCodeOnExecutionException()I
.end method

.method public abstract exitCodeOnInvalidInput()I
.end method

.method public abstract exitCodeOnSuccess()I
.end method

.method public abstract exitCodeOnUsageHelp()I
.end method

.method public abstract exitCodeOnVersionHelp()I
.end method

.method public abstract footer()[Ljava/lang/String;
.end method

.method public abstract footerHeading()Ljava/lang/String;
.end method

.method public abstract header()[Ljava/lang/String;
.end method

.method public abstract headerHeading()Ljava/lang/String;
.end method

.method public abstract helpCommand()Z
.end method

.method public abstract hidden()Z
.end method

.method public abstract mixinStandardHelpOptions()Z
.end method

.method public abstract modelTransformer()Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "+",
            "Lgroovyjarjarpicocli/CommandLine$IModelTransformer;",
            ">;"
        }
    .end annotation
.end method

.method public abstract name()Ljava/lang/String;
.end method

.method public abstract optionListHeading()Ljava/lang/String;
.end method

.method public abstract parameterListHeading()Ljava/lang/String;
.end method

.method public abstract preprocessor()Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "+",
            "Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;",
            ">;"
        }
    .end annotation
.end method

.method public abstract requiredOptionMarker()C
.end method

.method public abstract resourceBundle()Ljava/lang/String;
.end method

.method public abstract scope()Lgroovyjarjarpicocli/CommandLine$ScopeType;
.end method

.method public abstract separator()Ljava/lang/String;
.end method

.method public abstract showAtFileInUsageHelp()Z
.end method

.method public abstract showDefaultValues()Z
.end method

.method public abstract showEndOfOptionsDelimiterInUsageHelp()Z
.end method

.method public abstract sortOptions()Z
.end method

.method public abstract subcommands()[Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()[",
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end method

.method public abstract subcommandsRepeatable()Z
.end method

.method public abstract synopsisHeading()Ljava/lang/String;
.end method

.method public abstract synopsisSubcommandLabel()Ljava/lang/String;
.end method

.method public abstract usageHelpAutoWidth()Z
.end method

.method public abstract usageHelpWidth()I
.end method

.method public abstract version()[Ljava/lang/String;
.end method

.method public abstract versionProvider()Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "+",
            "Lgroovyjarjarpicocli/CommandLine$IVersionProvider;",
            ">;"
        }
    .end annotation
.end method
