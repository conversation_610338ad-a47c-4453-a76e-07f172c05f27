.class public Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
.super Ljava/lang/Object;
.source "CommandLine.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Builder"
.end annotation


# instance fields
.field private final args:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;",
            ">;"
        }
    .end annotation
.end field

.field private final compositesReferencingMe:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;",
            ">;"
        }
    .end annotation
.end field

.field private exclusive:Z

.field private getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

.field private heading:Ljava/lang/String;

.field private headingKey:Ljava/lang/String;

.field private multiplicity:Lgroovyjarjarpicocli/CommandLine$Range;

.field private order:I

.field private scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

.field private setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

.field private final specElements:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;",
            ">;"
        }
    .end annotation
.end field

.field private final subgroups:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;",
            ">;"
        }
    .end annotation
.end field

.field private topologicalSortDone:Ljava/lang/Boolean;

.field private typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

.field private validate:Z


# direct methods
.method constructor <init>()V
    .locals 2

    .line 10593
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 10581
    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->exclusive:Z

    const-string v1, "0..1"

    .line 10582
    invoke-static {v1}, Lgroovyjarjarpicocli/CommandLine$Range;->valueOf(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object v1

    iput-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->multiplicity:Lgroovyjarjarpicocli/CommandLine$Range;

    .line 10583
    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->validate:Z

    const/4 v0, -0x1

    .line 10584
    iput v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->order:I

    .line 10585
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->args:Ljava/util/List;

    .line 10586
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->subgroups:Ljava/util/List;

    .line 10587
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->specElements:Ljava/util/List;

    .line 10591
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->compositesReferencingMe:Ljava/util/List;

    return-void
.end method

.method constructor <init>(Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;)V
    .locals 2

    .line 10594
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 10581
    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->exclusive:Z

    const-string v1, "0..1"

    .line 10582
    invoke-static {v1}, Lgroovyjarjarpicocli/CommandLine$Range;->valueOf(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object v1

    iput-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->multiplicity:Lgroovyjarjarpicocli/CommandLine$Range;

    .line 10583
    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->validate:Z

    const/4 v0, -0x1

    .line 10584
    iput v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->order:I

    .line 10585
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->args:Ljava/util/List;

    .line 10586
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->subgroups:Ljava/util/List;

    .line 10587
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->specElements:Ljava/util/List;

    .line 10591
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->compositesReferencingMe:Ljava/util/List;

    .line 10595
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getTypeInfo()Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    .line 10596
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getter()Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    .line 10597
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->setter()Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    .line 10598
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->scope()Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    return-void
.end method

.method static synthetic access$13900(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 10574
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->heading:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$14000(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 10574
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->headingKey:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$14100(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)Z
    .locals 0

    .line 10574
    iget-boolean p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->exclusive:Z

    return p0
.end method

.method static synthetic access$14200(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)Z
    .locals 0

    .line 10574
    iget-boolean p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->validate:Z

    return p0
.end method

.method static synthetic access$14300(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Range;
    .locals 0

    .line 10574
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->multiplicity:Lgroovyjarjarpicocli/CommandLine$Range;

    return-object p0
.end method

.method static synthetic access$14400(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)I
    .locals 0

    .line 10574
    iget p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->order:I

    return p0
.end method

.method static synthetic access$14500(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;
    .locals 0

    .line 10574
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    return-object p0
.end method

.method static synthetic access$14600(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$IGetter;
    .locals 0

    .line 10574
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    return-object p0
.end method

.method static synthetic access$14700(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$ISetter;
    .locals 0

    .line 10574
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    return-object p0
.end method

.method static synthetic access$14800(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$IScope;
    .locals 0

    .line 10574
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    return-object p0
.end method


# virtual methods
.method public addArg(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 1

    .line 10709
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->args:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p0
.end method

.method public addSpecElement(Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 1

    .line 10722
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->specElements:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p0
.end method

.method public addSubgroup(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 1

    .line 10715
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->subgroups:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p0
.end method

.method public args()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;",
            ">;"
        }
    .end annotation

    .line 10712
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->args:Ljava/util/List;

    return-object v0
.end method

.method public build()Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;
    .locals 1

    .line 10615
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;

    invoke-direct {v0, p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;-><init>(Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;)V

    return-object v0
.end method

.method public exclusive(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10624
    iput-boolean p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->exclusive:Z

    return-object p0
.end method

.method public exclusive()Z
    .locals 1

    .line 10620
    iget-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->exclusive:Z

    return v0
.end method

.method public getter(Lgroovyjarjarpicocli/CommandLine$Model$IGetter;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10696
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    return-object p0
.end method

.method public getter()Lgroovyjarjarpicocli/CommandLine$Model$IGetter;
    .locals 1

    .line 10694
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    return-object v0
.end method

.method public heading(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10675
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->heading:Ljava/lang/String;

    return-object p0
.end method

.method public heading()Ljava/lang/String;
    .locals 1

    .line 10671
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->heading:Ljava/lang/String;

    return-object v0
.end method

.method public headingKey(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10682
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->headingKey:Ljava/lang/String;

    return-object p0
.end method

.method public headingKey()Ljava/lang/String;
    .locals 1

    .line 10679
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->headingKey:Ljava/lang/String;

    return-object v0
.end method

.method public multiplicity(Lgroovyjarjarpicocli/CommandLine$Range;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10646
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->multiplicity:Lgroovyjarjarpicocli/CommandLine$Range;

    return-object p0
.end method

.method public multiplicity(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10639
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Range;->valueOf(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->multiplicity(Lgroovyjarjarpicocli/CommandLine$Range;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public multiplicity()Lgroovyjarjarpicocli/CommandLine$Range;
    .locals 1

    .line 10632
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->multiplicity:Lgroovyjarjarpicocli/CommandLine$Range;

    return-object v0
.end method

.method public order()I
    .locals 1

    .line 10664
    iget v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->order:I

    return v0
.end method

.method public order(I)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10667
    iput p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->order:I

    return-object p0
.end method

.method public scope(Lgroovyjarjarpicocli/CommandLine$Model$IScope;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10706
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    return-object p0
.end method

.method public scope()Lgroovyjarjarpicocli/CommandLine$Model$IScope;
    .locals 1

    .line 10704
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    return-object v0
.end method

.method public setter(Lgroovyjarjarpicocli/CommandLine$Model$ISetter;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10701
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    return-object p0
.end method

.method public setter()Lgroovyjarjarpicocli/CommandLine$Model$ISetter;
    .locals 1

    .line 10699
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    return-object v0
.end method

.method public specElements()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;",
            ">;"
        }
    .end annotation

    .line 10726
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->specElements:Ljava/util/List;

    return-object v0
.end method

.method public subgroups()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;",
            ">;"
        }
    .end annotation

    .line 10718
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->subgroups:Ljava/util/List;

    return-object v0
.end method

.method public typeInfo(Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10691
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    return-object p0
.end method

.method public typeInfo()Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;
    .locals 1

    .line 10687
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    return-object v0
.end method

.method public updateArgGroupAttributes(Lgroovyjarjarpicocli/CommandLine$ArgGroup;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 2

    .line 10606
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$ArgGroup;->heading()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->heading(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;

    move-result-object v0

    .line 10607
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$ArgGroup;->headingKey()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->headingKey(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;

    move-result-object v0

    .line 10608
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$ArgGroup;->exclusive()Z

    move-result v1

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->exclusive(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;

    move-result-object v0

    .line 10609
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$ArgGroup;->multiplicity()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->multiplicity(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;

    move-result-object v0

    .line 10610
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$ArgGroup;->validate()Z

    move-result v1

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->validate(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;

    move-result-object v0

    .line 10611
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$ArgGroup;->order()I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->order(I)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public validate(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;
    .locals 0

    .line 10659
    iput-boolean p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->validate:Z

    return-object p0
.end method

.method public validate()Z
    .locals 1

    .line 10653
    iget-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec$Builder;->validate:Z

    return v0
.end method
