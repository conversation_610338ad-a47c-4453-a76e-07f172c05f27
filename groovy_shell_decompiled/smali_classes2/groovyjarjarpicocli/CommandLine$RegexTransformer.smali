.class public Lgroovyjarjarpicocli/CommandLine$RegexTransformer;
.super Ljava/lang/Object;
.source "CommandLine.java"

# interfaces
.implements Lgroovyjarjarpicocli/CommandLine$INegatableOptionTransformer;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "RegexTransformer"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;
    }
.end annotation


# instance fields
.field final replacements:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/util/regex/Pattern;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field final synopsis:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/util/regex/Pattern;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>(Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;)V
    .locals 2

    .line 5198
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5199
    new-instance v0, Ljava/util/LinkedHashMap;

    iget-object v1, p1, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->replacements:Ljava/util/Map;

    invoke-direct {v0, v1}, Ljava/util/LinkedHashMap;-><init>(Ljava/util/Map;)V

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;->replacements:Ljava/util/Map;

    .line 5200
    new-instance v0, Ljava/util/LinkedHashMap;

    iget-object p1, p1, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->synopsis:Ljava/util/Map;

    invoke-direct {v0, p1}, Ljava/util/LinkedHashMap;-><init>(Ljava/util/Map;)V

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;->synopsis:Ljava/util/Map;

    return-void
.end method

.method public static createCaseInsensitive()Lgroovyjarjarpicocli/CommandLine$RegexTransformer;
    .locals 4

    .line 5287
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    invoke-direct {v0}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;-><init>()V

    const-string v1, "^--((?i)no)-(\\w(-|\\w)*)$"

    const-string v2, "--$2"

    const-string v3, "--[$1-]$2"

    .line 5288
    invoke-virtual {v0, v1, v2, v3}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->addPattern(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    move-result-object v0

    const-string v1, "^--(\\w(-|\\w)*)$"

    const-string v2, "--no-$1"

    const-string v3, "--[no-]$1"

    .line 5289
    invoke-virtual {v0, v1, v2, v3}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->addPattern(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    move-result-object v0

    const-string v1, "^(-|--)(\\w*:)\\+(\\w(-|\\w)*)$"

    const-string v2, "$1$2-$3"

    const-string v3, "$1$2(+|-)$3"

    .line 5290
    invoke-virtual {v0, v1, v2, v3}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->addPattern(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    move-result-object v0

    const-string v1, "^(-|--)(\\w*:)\\-(\\w(-|\\w)*)$"

    const-string v2, "$1$2+$3"

    .line 5291
    invoke-virtual {v0, v1, v2, v3}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->addPattern(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    move-result-object v0

    .line 5292
    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->build()Lgroovyjarjarpicocli/CommandLine$RegexTransformer;

    move-result-object v0

    return-object v0
.end method

.method public static createDefault()Lgroovyjarjarpicocli/CommandLine$RegexTransformer;
    .locals 4

    .line 5240
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    invoke-direct {v0}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;-><init>()V

    const-string v1, "^--no-(\\w(-|\\w)*)$"

    const-string v2, "--$1"

    const-string v3, "--[no-]$1"

    .line 5241
    invoke-virtual {v0, v1, v2, v3}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->addPattern(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    move-result-object v0

    const-string v1, "^--(\\w(-|\\w)*)$"

    const-string v2, "--no-$1"

    .line 5242
    invoke-virtual {v0, v1, v2, v3}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->addPattern(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    move-result-object v0

    const-string v1, "^(-|--)(\\w*:)\\+(\\w(-|\\w)*)$"

    const-string v2, "$1$2-$3"

    const-string v3, "$1$2(+|-)$3"

    .line 5243
    invoke-virtual {v0, v1, v2, v3}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->addPattern(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    move-result-object v0

    const-string v1, "^(-|--)(\\w*:)\\-(\\w(-|\\w)*)$"

    const-string v2, "$1$2+$3"

    .line 5244
    invoke-virtual {v0, v1, v2, v3}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->addPattern(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;

    move-result-object v0

    .line 5245
    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer$Builder;->build()Lgroovyjarjarpicocli/CommandLine$RegexTransformer;

    move-result-object v0

    return-object v0
.end method


# virtual methods
.method public makeNegative(Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Ljava/lang/String;
    .locals 3

    .line 5298
    iget-object p2, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;->replacements:Ljava/util/Map;

    invoke-interface {p2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map$Entry;

    .line 5299
    invoke-interface {v0}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/regex/Pattern;

    invoke-virtual {v1, p1}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    move-result-object v1

    .line 5300
    invoke-virtual {v1}, Ljava/util/regex/Matcher;->find()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    invoke-virtual {v1, p1}, Ljava/util/regex/Matcher;->replaceAll(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    :cond_1
    return-object p1
.end method

.method public makeSynopsis(Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Ljava/lang/String;
    .locals 3

    .line 5307
    iget-object p2, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;->synopsis:Ljava/util/Map;

    invoke-interface {p2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map$Entry;

    .line 5308
    invoke-interface {v0}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/regex/Pattern;

    invoke-virtual {v1, p1}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    move-result-object v1

    .line 5309
    invoke-virtual {v1}, Ljava/util/regex/Matcher;->find()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    invoke-virtual {v1, p1}, Ljava/util/regex/Matcher;->replaceAll(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    :cond_1
    return-object p1
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 5315
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "[replacements="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;->replacements:Ljava/util/Map;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", synopsis="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;->synopsis:Ljava/util/Map;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]@"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p0}, Ljava/lang/System;->identityHashCode(Ljava/lang/Object;)I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
