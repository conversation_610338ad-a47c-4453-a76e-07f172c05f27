.class public Lgroovyjarjarpicocli/CommandLine$Help;
.super Ljava/lang/Object;
.source "CommandLine.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Help"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarpicocli/CommandLine$Help$Ansi;,
        Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;,
        Lgroovyjarjarpicocli/CommandLine$Help$Column;,
        Lgroovyjarjarpicocli/CommandLine$Help$TextTable;,
        Lgroovyjarjarpicocli/CommandLine$Help$SortByOrder;,
        Lgroovyjarjarpicocli/CommandLine$Help$SortByOptionArityAndNameAlphabetically;,
        Lgroovyjarjarpicocli/CommandLine$Help$SortByShortestOptionNameAlphabetically;,
        Lgroovyjarjarpicocli/CommandLine$Help$ShortestFirst;,
        Lgroovyjarjarpicocli/CommandLine$Help$Layout;,
        Lgroovyjarjarpicocli/CommandLine$Help$DefaultParamLabelRenderer;,
        Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;,
        Lgroovyjarjarpicocli/CommandLine$Help$DefaultParameterRenderer;,
        Lgroovyjarjarpicocli/CommandLine$Help$IParameterRenderer;,
        Lgroovyjarjarpicocli/CommandLine$Help$MinimalParameterRenderer;,
        Lgroovyjarjarpicocli/CommandLine$Help$MinimalOptionRenderer;,
        Lgroovyjarjarpicocli/CommandLine$Help$DefaultOptionRenderer;,
        Lgroovyjarjarpicocli/CommandLine$Help$IOptionRenderer;,
        Lgroovyjarjarpicocli/CommandLine$Help$Visibility;
    }
.end annotation


# static fields
.field protected static final DEFAULT_COMMAND_NAME:Ljava/lang/String; = "<main class>"

.field protected static final DEFAULT_SEPARATOR:Ljava/lang/String; = "="


# instance fields
.field public final AT_FILE_POSITIONAL_PARAM:Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

.field public final END_OF_OPTIONS_OPTION:Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

.field private aliases:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final allCommands:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarpicocli/CommandLine$Help;",
            ">;"
        }
    .end annotation
.end field

.field private final colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

.field private final commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

.field private final parameterLabelRenderer:Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

.field private final visibleCommands:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarpicocli/CommandLine$Help;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V
    .locals 4

    .line 15078
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 15031
    invoke-static {}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->builder()Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;

    move-result-object v0

    const-string v1, "${picocli.atfile.label:-@<filename>}"

    .line 15032
    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;->paramLabel(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object v0

    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;

    const-string v1, "${picocli.atfile.description:-One or more argument files containing options.}"

    filled-new-array {v1}, [Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;->description([Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object v0

    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;

    const-string v1, "0..*"

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;->arity(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object v0

    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;

    const-string v1, "groovyjarjarpicocli.atfile"

    .line 15033
    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;->descriptionKey(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object v0

    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec$Builder;->build()Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->AT_FILE_POSITIONAL_PARAM:Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    const-string v1, "--"

    .line 15035
    invoke-direct {p0, v1}, Lgroovyjarjarpicocli/CommandLine$Help;->createEndOfOptionsOption(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    move-result-object v1

    iput-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->END_OF_OPTIONS_OPTION:Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    .line 15046
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->allCommands:Ljava/util/Map;

    .line 15047
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->visibleCommands:Ljava/util/Map;

    const-string v1, "commandSpec"

    .line 15079
    invoke-static {p1, v1}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    iput-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    .line 15080
    new-instance v1, Ljava/util/ArrayList;

    invoke-virtual {p1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->aliases()[Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->aliases:Ljava/util/List;

    .line 15081
    invoke-virtual {p1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->name()Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x0

    invoke-interface {v1, v3, v2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 15082
    new-instance v1, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;

    invoke-direct {v1, p2}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;->applySystemProperties()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;

    move-result-object p2

    invoke-virtual {p2}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;->build()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    .line 15083
    new-instance p2, Lgroovyjarjarpicocli/CommandLine$Help$DefaultParamLabelRenderer;

    invoke-direct {p2, p1}, Lgroovyjarjarpicocli/CommandLine$Help$DefaultParamLabelRenderer;-><init>(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)V

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer:Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    .line 15085
    invoke-virtual {p1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->subcommands()Ljava/util/Map;

    move-result-object p2

    invoke-direct {p0, p2}, Lgroovyjarjarpicocli/CommandLine$Help;->registerSubcommands(Ljava/util/Map;)V

    .line 15086
    iput-object p1, v0, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;)V
    .locals 1

    .line 15056
    sget-object v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi;->AUTO:Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    invoke-direct {p0, p1, v0}, Lgroovyjarjarpicocli/CommandLine$Help;-><init>(Ljava/lang/Object;Lgroovyjarjarpicocli/CommandLine$Help$Ansi;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;Lgroovyjarjarpicocli/CommandLine$Help$Ansi;)V
    .locals 0

    .line 15064
    invoke-static {p2}, Lgroovyjarjarpicocli/CommandLine$Help;->defaultColorScheme(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object p2

    invoke-direct {p0, p1, p2}, Lgroovyjarjarpicocli/CommandLine$Help;-><init>(Ljava/lang/Object;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 15072
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;-><init>(Lgroovyjarjarpicocli/CommandLine$1;)V

    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->forAnnotatedObject(Ljava/lang/Object;Lgroovyjarjarpicocli/CommandLine$IFactory;)Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    move-result-object p1

    invoke-direct {p0, p1, p2}, Lgroovyjarjarpicocli/CommandLine$Help;-><init>(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V

    return-void
.end method

.method static synthetic access$23100([Ljava/lang/String;IILjava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 15023
    invoke-static {p0, p1, p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help;->join([Ljava/lang/String;IILjava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$23200(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;[Ljava/lang/String;[Z)[Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 0

    .line 15023
    invoke-static {p0, p1, p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help;->createDescriptionFirstLines(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;[Ljava/lang/String;[Z)[Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$23300(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V
    .locals 0

    .line 15023
    invoke-static {p0, p1, p2}, Lgroovyjarjarpicocli/CommandLine$Help;->addTrailingDefaultLine(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V

    return-void
.end method

.method static synthetic access$23700(I)[C
    .locals 0

    .line 15023
    invoke-static {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->spaces(I)[C

    move-result-object p0

    return-object p0
.end method

.method private static addTrailingDefaultLine(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "[",
            "Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;",
            ">;",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;",
            "Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;",
            ")V"
        }
    .end annotation

    .line 16320
    sget-object v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi;->EMPTY_TEXT:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    const/4 v1, 0x5

    new-array v1, v1, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    const/4 v2, 0x0

    aput-object v0, v1, v2

    const/4 v2, 0x1

    aput-object v0, v1, v2

    const/4 v3, 0x2

    aput-object v0, v1, v3

    const/4 v3, 0x3

    aput-object v0, v1, v3

    .line 16321
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    invoke-virtual {p2}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "  Default: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {p1, v2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->defaultValueString(Z)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, v3, p1, p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V

    const/4 p1, 0x4

    aput-object v0, v1, p1

    invoke-interface {p0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method private adjustCJK()Z
    .locals 1

    .line 15769
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->adjustLineBreaksForWideCJKCharacters()Z

    move-result v0

    return v0
.end method

.method static concatOptionText(Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 3

    .line 15371
    invoke-virtual {p3}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->hidden()Z

    move-result v0

    if-nez v0, :cond_3

    .line 15372
    invoke-virtual {p3}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->shortestName()Ljava/lang/String;

    move-result-object v0

    .line 15373
    invoke-static {p3}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->access$12500(Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 15374
    iget-object v0, p3, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    if-nez v0, :cond_0

    invoke-static {}, Lgroovyjarjarpicocli/CommandLine$RegexTransformer;->createDefault()Lgroovyjarjarpicocli/CommandLine$RegexTransformer;

    move-result-object v0

    goto :goto_0

    :cond_0
    iget-object v0, p3, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->negatableOptionTransformer()Lgroovyjarjarpicocli/CommandLine$INegatableOptionTransformer;

    move-result-object v0

    .line 15375
    :goto_0
    invoke-virtual {p3}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->shortestName()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p3, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$INegatableOptionTransformer;->makeSynopsis(Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Ljava/lang/String;

    move-result-object v0

    .line 15377
    :cond_1
    invoke-virtual {p2, v0}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->optionText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    .line 15378
    invoke-virtual {p2}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v1

    invoke-static {p2}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->access$22800(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Ljava/util/List;

    move-result-object p2

    invoke-interface {p4, p3, v1, p2}, Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;->renderParameterLabel(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;Lgroovyjarjarpicocli/CommandLine$Help$Ansi;Ljava/util/List;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    .line 15379
    invoke-virtual {p1, p0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    .line 15380
    invoke-virtual {p3}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->required()Z

    move-result p1

    if-eqz p1, :cond_2

    .line 15381
    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    invoke-virtual {p0, p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    const-string p1, ""

    invoke-virtual {p0, p1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p1

    .line 15382
    invoke-virtual {p3}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->isMultiValue()Z

    move-result p0

    if-eqz p0, :cond_3

    const-string p0, " ["

    .line 15383
    invoke-virtual {p1, p0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    invoke-virtual {p0, p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    const-string p1, "]..."

    invoke-virtual {p0, p1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p1

    goto :goto_1

    :cond_2
    const-string p1, "["

    .line 15386
    invoke-virtual {p0, p1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    invoke-virtual {p0, p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    const-string p1, "]"

    invoke-virtual {p0, p1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p1

    .line 15387
    invoke-virtual {p3}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->isMultiValue()Z

    move-result p0

    if-eqz p0, :cond_3

    const-string p0, "..."

    .line 15388
    invoke-virtual {p1, p0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p1

    :cond_3
    :goto_1
    return-object p1
.end method

.method static concatPositionalText(Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 1

    .line 15425
    invoke-virtual {p3}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->hidden()Z

    move-result v0

    if-nez v0, :cond_0

    .line 15426
    invoke-virtual {p2}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v0

    invoke-static {p2}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->access$22700(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Ljava/util/List;

    move-result-object p2

    invoke-interface {p4, p3, v0, p2}, Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;->renderParameterLabel(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;Lgroovyjarjarpicocli/CommandLine$Help$Ansi;Ljava/util/List;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    .line 15427
    invoke-virtual {p1, p0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p0

    invoke-virtual {p0, p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p1

    :cond_0
    return-object p1
.end method

.method private static countTrailingSpaces(Ljava/lang/String;)I
    .locals 4

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return v0

    .line 15736
    :cond_0
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    :goto_0
    if-ltz v1, :cond_1

    invoke-virtual {p0, v1}, Ljava/lang/String;->charAt(I)C

    move-result v2

    const/16 v3, 0x20

    if-ne v2, v3, :cond_1

    add-int/lit8 v0, v0, 0x1

    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    :cond_1
    return v0
.end method

.method private static createDescriptionFirstLines(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;[Ljava/lang/String;[Z)[Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 6

    .line 16325
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 v2, 0x0

    invoke-static {p2, v2}, Lgroovyjarjarpicocli/CommandLine;->access$22300([Ljava/lang/String;I)Ljava/lang/String;

    move-result-object p2

    invoke-direct {v0, v1, p2, p0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->splitLines()[Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    .line 16326
    array-length v0, p2

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    array-length v0, p2

    if-ne v0, v1, :cond_2

    aget-object v0, p2, v2

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->access$23400(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->length()I

    move-result v0

    if-nez v0, :cond_2

    .line 16327
    :cond_0
    aget-boolean p2, p3, v2

    if-eqz p2, :cond_1

    new-array p2, v1, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    .line 16328
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "  Default: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {p1, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->defaultValueString(Z)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, v3, p1, p0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V

    aput-object v0, p2, v2

    .line 16329
    aput-boolean v2, p3, v2

    goto :goto_0

    :cond_1
    new-array p2, v1, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    .line 16331
    sget-object p0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi;->EMPTY_TEXT:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    aput-object p0, p2, v2

    :cond_2
    :goto_0
    return-object p2
.end method

.method private createEndOfOptionsOption(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;
    .locals 1

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/String;

    .line 15038
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->builder(Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    move-result-object p1

    const-string v0, "${picocli.endofoptions.description:-This option can be used to separate command-line options from the list of positional parameters.}"

    filled-new-array {v0}, [Ljava/lang/String;

    move-result-object v0

    .line 15039
    invoke-virtual {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;->description([Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    const-string v0, "0"

    invoke-virtual {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;->arity(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    const-string v0, "groovyjarjarpicocli.endofoptions"

    .line 15040
    invoke-virtual {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;->descriptionKey(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;

    .line 15041
    invoke-virtual {p1}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec$Builder;->build()Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    move-result-object p1

    return-object p1
.end method

.method private createLayout(ILgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Lgroovyjarjarpicocli/CommandLine$Help$Layout;
    .locals 3

    .line 16004
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v0

    invoke-static {p2, p1, v0}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->forDefaultColumns(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;II)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    move-result-object p1

    .line 16005
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->adjustLineBreaksForWideCJKCharacters()Z

    move-result v0

    invoke-virtual {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->setAdjustLineBreaksForWideCJKCharacters(Z)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    .line 16006
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Layout;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultOptionRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IOptionRenderer;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultParameterRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParameterRenderer;

    move-result-object v2

    invoke-direct {v0, p2, p1, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;Lgroovyjarjarpicocli/CommandLine$Help$TextTable;Lgroovyjarjarpicocli/CommandLine$Help$IOptionRenderer;Lgroovyjarjarpicocli/CommandLine$Help$IParameterRenderer;)V

    return-object v0
.end method

.method public static createMinimalOptionRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IOptionRenderer;
    .locals 1

    .line 16064
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$MinimalOptionRenderer;

    invoke-direct {v0}, Lgroovyjarjarpicocli/CommandLine$Help$MinimalOptionRenderer;-><init>()V

    return-object v0
.end method

.method public static createMinimalParamLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;
    .locals 1

    .line 16093
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$1;

    invoke-direct {v0}, Lgroovyjarjarpicocli/CommandLine$Help$1;-><init>()V

    return-object v0
.end method

.method public static createMinimalParameterRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParameterRenderer;
    .locals 1

    .line 16087
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$MinimalParameterRenderer;

    invoke-direct {v0}, Lgroovyjarjarpicocli/CommandLine$Help$MinimalParameterRenderer;-><init>()V

    return-object v0
.end method

.method static createOrderComparator()Ljava/util/Comparator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;"
        }
    .end annotation

    .line 16130
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$SortByOrder;

    invoke-direct {v0}, Lgroovyjarjarpicocli/CommandLine$Help$SortByOrder;-><init>()V

    return-object v0
.end method

.method private static createOrderComparatorIfNecessary(Ljava/util/List;)Ljava/util/Comparator;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;)",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;"
        }
    .end annotation

    .line 15504
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->order()I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    invoke-static {}, Lgroovyjarjarpicocli/CommandLine$Help;->createOrderComparator()Ljava/util/Comparator;

    move-result-object p0

    return-object p0

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method public static createShortOptionArityAndNameComparator()Ljava/util/Comparator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;"
        }
    .end annotation

    .line 16121
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$SortByOptionArityAndNameAlphabetically;

    invoke-direct {v0}, Lgroovyjarjarpicocli/CommandLine$Help$SortByOptionArityAndNameAlphabetically;-><init>()V

    return-object v0
.end method

.method public static createShortOptionNameComparator()Ljava/util/Comparator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;"
        }
    .end annotation

    .line 16115
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$SortByShortestOptionNameAlphabetically;

    invoke-direct {v0}, Lgroovyjarjarpicocli/CommandLine$Help$SortByShortestOptionNameAlphabetically;-><init>()V

    return-object v0
.end method

.method public static defaultColorScheme(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;
    .locals 4

    .line 17331
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;

    invoke-direct {v0, p0}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;)V

    const/4 p0, 0x1

    new-array v1, p0, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;

    sget-object v2, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;->bold:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;

    const/4 v3, 0x0

    aput-object v2, v1, v3

    .line 17332
    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;->commands([Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;

    move-result-object v0

    new-array v1, p0, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;

    sget-object v2, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;->fg_yellow:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;

    aput-object v2, v1, v3

    .line 17333
    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;->options([Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;

    move-result-object v0

    new-array v1, p0, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;

    sget-object v2, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;->fg_yellow:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;

    aput-object v2, v1, v3

    .line 17334
    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;->parameters([Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;

    move-result-object v0

    new-array v1, p0, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;

    sget-object v2, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;->italic:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;

    aput-object v2, v1, v3

    .line 17335
    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;->optionParams([Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;

    move-result-object v0

    const/4 v1, 0x2

    new-array v1, v1, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;

    sget-object v2, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;->fg_red:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;

    aput-object v2, v1, v3

    sget-object v2, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;->bold:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;

    aput-object v2, v1, p0

    .line 17336
    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;->errors([Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;

    move-result-object v0

    new-array p0, p0, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;

    sget-object v1, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;->italic:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Style;

    aput-object v1, p0, v3

    .line 17337
    invoke-virtual {v0, p0}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;->stackTraces([Lgroovyjarjarpicocli/CommandLine$Help$Ansi$IStyle;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;

    move-result-object p0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme$Builder;->build()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object p0

    return-object p0
.end method

.method private excludeHiddenAndGroupOptions(Ljava/util/List;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;"
        }
    .end annotation

    .line 15483
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0, p1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 15484
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->optionSectionGroups()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->allOptionsNested()Ljava/util/List;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->removeAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 15485
    :cond_0
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 15486
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->hidden()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 15487
    invoke-interface {p1}, Ljava/util/Iterator;->remove()V

    goto :goto_1

    :cond_2
    return-object v0
.end method

.method private excludeHiddenAndGroupParams(Ljava/util/List;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;",
            ">;"
        }
    .end annotation

    .line 15493
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0, p1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 15494
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->optionSectionGroups()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->allPositionalParametersNested()Ljava/util/List;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->removeAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 15495
    :cond_0
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 15496
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->hidden()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 15497
    invoke-interface {p1}, Ljava/util/Iterator;->remove()V

    goto :goto_1

    :cond_2
    return-object v0
.end method

.method private getHelpFactory()Lgroovyjarjarpicocli/CommandLine$IHelpFactory;
    .locals 1

    .line 15101
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->helpFactory()Lgroovyjarjarpicocli/CommandLine$IHelpFactory;

    move-result-object v0

    return-object v0
.end method

.method private static varargs heading(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;IZLjava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;
    .locals 7

    const/4 v0, 0x1

    new-array v4, v0, [Ljava/lang/String;

    const/4 v0, 0x0

    aput-object p3, v4, v0

    .line 15724
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    move-object v1, p0

    move v2, p1

    move v3, p2

    move-object v6, p4

    invoke-static/range {v1 .. v6}, Lgroovyjarjarpicocli/CommandLine$Help;->join(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;IZ[Ljava/lang/String;Ljava/lang/StringBuilder;[Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    .line 15725
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->trimLineSeparator(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    new-instance p1, Ljava/lang/String;

    invoke-static {p3}, Lgroovyjarjarpicocli/CommandLine$Help;->countTrailingSpaces(Ljava/lang/String;)I

    move-result p2

    invoke-static {p2}, Lgroovyjarjarpicocli/CommandLine$Help;->spaces(I)[C

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/String;-><init>([C)V

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static join([Ljava/lang/String;IILjava/lang/String;)Ljava/lang/String;
    .locals 5

    const-string v0, ""

    if-nez p0, :cond_0

    return-object v0

    .line 15976
    :cond_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    move v2, p1

    :goto_0
    add-int v3, p1, p2

    if-ge v2, v3, :cond_2

    if-le v2, p1, :cond_1

    move-object v3, p3

    goto :goto_1

    :cond_1
    move-object v3, v0

    .line 15978
    :goto_1
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    aget-object v4, p0, v2

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 15980
    :cond_2
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static varargs join(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;IZ[Ljava/lang/String;Ljava/lang/StringBuilder;[Ljava/lang/Object;)Ljava/lang/StringBuilder;
    .locals 4

    if-eqz p3, :cond_1

    const/4 v0, 0x1

    new-array v1, v0, [I

    const/4 v2, 0x0

    aput p1, v1, v2

    .line 15758
    invoke-static {p0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->forColumnWidths(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;[I)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    move-result-object p0

    .line 15759
    invoke-virtual {p0, p2}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->setAdjustLineBreaksForWideCJKCharacters(Z)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    .line 15760
    iput v2, p0, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->indentWrappedLines:I

    .line 15761
    array-length p1, p3

    move p2, v2

    :goto_0
    if-ge p2, p1, :cond_0

    aget-object v1, p3, p2

    new-array v3, v0, [Ljava/lang/String;

    .line 15762
    invoke-static {v1, p5}, Lgroovyjarjarpicocli/CommandLine;->access$7400(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    aput-object v1, v3, v2

    invoke-virtual {p0, v3}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->addRowValues([Ljava/lang/String;)V

    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    .line 15764
    :cond_0
    invoke-virtual {p0, p4}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->toString(Ljava/lang/StringBuilder;)Ljava/lang/StringBuilder;

    :cond_1
    return-object p4
.end method

.method public static varargs join(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;I[Ljava/lang/String;Ljava/lang/StringBuilder;[Ljava/lang/Object;)Ljava/lang/StringBuilder;
    .locals 7
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 15744
    sget-object v0, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->DEFAULT_ADJUST_CJK:Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v3

    move-object v1, p0

    move v2, p1

    move-object v4, p2

    move-object v5, p3

    move-object v6, p4

    invoke-static/range {v1 .. v6}, Lgroovyjarjarpicocli/CommandLine$Help;->join(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;IZ[Ljava/lang/String;Ljava/lang/StringBuilder;[Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    return-object p0
.end method

.method private static maxLength(Ljava/util/Collection;)I
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "*>;)I"
        }
    .end annotation

    .line 15960
    invoke-interface {p0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p0

    const/4 v0, 0x0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v0

    goto :goto_0

    :cond_0
    return v0
.end method

.method private optionListGroupSections(Ljava/util/List;Ljava/util/Comparator;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;",
            ">;",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;",
            "Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;",
            ")",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 15599
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 15600
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1, p1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 15601
    new-instance p1, Lgroovyjarjarpicocli/CommandLine$Help$SortByOrder;

    invoke-direct {p1}, Lgroovyjarjarpicocli/CommandLine$Help$SortByOrder;-><init>()V

    invoke-static {v1, p1}, Ljava/util/Collections;->sort(Ljava/util/List;Ljava/util/Comparator;)V

    .line 15603
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    .line 15604
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;

    .line 15605
    new-instance v3, Ljava/util/ArrayList;

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->allOptionsNested()Ljava/util/List;

    move-result-object v4

    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    if-eqz p2, :cond_0

    .line 15606
    invoke-static {v3, p2}, Ljava/util/Collections;->sort(Ljava/util/List;Ljava/util/Comparator;)V

    .line 15607
    :cond_0
    invoke-interface {v3, v0}, Ljava/util/List;->removeAll(Ljava/util/Collection;)Z

    .line 15608
    invoke-interface {v0, v3}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 15610
    new-instance v4, Ljava/util/ArrayList;

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->allPositionalParametersNested()Ljava/util/List;

    move-result-object v5

    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 15611
    invoke-interface {v4, v0}, Ljava/util/List;->removeAll(Ljava/util/Collection;)Z

    .line 15612
    invoke-interface {v0, v4}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 15614
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultLayout()Lgroovyjarjarpicocli/CommandLine$Help$Layout;

    move-result-object v5

    .line 15615
    invoke-virtual {v5, v4, p3}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->addPositionalParameters(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)V

    .line 15616
    invoke-virtual {v5, v3, p3}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->addOptions(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)V

    .line 15618
    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->heading()Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x0

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {p0, v2, v3}, Lgroovyjarjarpicocli/CommandLine$Help;->createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 15619
    invoke-virtual {p1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 15621
    :cond_1
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private static optionSectionGroups(Ljava/util/List;Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;",
            ">;",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;",
            ">;)V"
        }
    .end annotation

    .line 15633
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;

    .line 15634
    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->subgroups()Ljava/util/List;

    move-result-object v1

    invoke-static {v1, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->optionSectionGroups(Ljava/util/List;Ljava/util/List;)V

    .line 15635
    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->heading()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    return-void
.end method

.method private registerSubcommands(Ljava/util/Map;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarpicocli/CommandLine;",
            ">;)V"
        }
    .end annotation

    .line 15138
    new-instance v0, Ljava/util/IdentityHashMap;

    invoke-direct {v0}, Ljava/util/IdentityHashMap;-><init>()V

    .line 15139
    invoke-interface {p1}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarpicocli/CommandLine;

    .line 15140
    invoke-interface {v0, v2}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_0

    .line 15141
    new-instance v3, Ljava/util/ArrayList;

    invoke-static {v2}, Lgroovyjarjarpicocli/CommandLine;->access$19900(Lgroovyjarjarpicocli/CommandLine;)Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    move-result-object v4

    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->aliases()[Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v4

    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    invoke-interface {v0, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 15145
    :cond_1
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_2
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map$Entry;

    .line 15146
    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    .line 15147
    invoke-interface {v2}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_2

    const/4 v4, 0x0

    invoke-interface {v2}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v3, v4, v2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    goto :goto_1

    .line 15151
    :cond_3
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_4
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_6

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 15154
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine;

    .line 15155
    invoke-interface {v0, v1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/List;

    if-nez v2, :cond_5

    goto :goto_2

    .line 15157
    :cond_5
    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/String;->length()I

    move-result v4

    const/4 v5, 0x1

    sub-int/2addr v4, v5

    invoke-virtual {v3, v5, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v3

    .line 15158
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->getHelpFactory()Lgroovyjarjarpicocli/CommandLine$IHelpFactory;

    move-result-object v4

    invoke-static {v1}, Lgroovyjarjarpicocli/CommandLine;->access$19900(Lgroovyjarjarpicocli/CommandLine;)Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    move-result-object v1

    iget-object v5, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    invoke-interface {v4, v1, v5}, Lgroovyjarjarpicocli/CommandLine$IHelpFactory;->create(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Lgroovyjarjarpicocli/CommandLine$Help;

    move-result-object v1

    invoke-virtual {v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help;->withCommandNames(Ljava/util/List;)Lgroovyjarjarpicocli/CommandLine$Help;

    move-result-object v1

    .line 15159
    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->allCommands:Ljava/util/Map;

    invoke-interface {v2, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 15160
    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec()Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->hidden()Z

    move-result v2

    if-nez v2, :cond_4

    .line 15161
    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->visibleCommands:Ljava/util/Map;

    invoke-interface {v2, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :cond_6
    return-void
.end method

.method public static shortestFirst()Ljava/util/Comparator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Comparator<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 16125
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$ShortestFirst;

    invoke-direct {v0}, Lgroovyjarjarpicocli/CommandLine$Help$ShortestFirst;-><init>()V

    return-object v0
.end method

.method private static spaces(I)[C
    .locals 1

    .line 15732
    new-array p0, p0, [C

    const/16 v0, 0x20

    invoke-static {p0, v0}, Ljava/util/Arrays;->fill([CC)V

    return-object p0
.end method

.method private static stringOf(CI)Ljava/lang/String;
    .locals 0

    .line 15983
    new-array p1, p1, [C

    .line 15984
    invoke-static {p1, p0}, Ljava/util/Arrays;->fill([CC)V

    .line 15985
    new-instance p0, Ljava/lang/String;

    invoke-direct {p0, p1}, Ljava/lang/String;-><init>([C)V

    return-object p0
.end method

.method static trimLineSeparator(Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    const-string v0, "line.separator"

    .line 15728
    invoke-static {v0}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    .line 15729
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v2

    invoke-static {v0}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    sub-int/2addr v2, v0

    invoke-virtual {p0, v1, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p0

    :cond_0
    return-object p0
.end method

.method private width()I
    .locals 1

    .line 15768
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->width()I

    move-result v0

    return v0
.end method


# virtual methods
.method public abbreviatedSynopsis()Ljava/lang/String;
    .locals 7

    .line 15219
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 15220
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->optionsMap()Ljava/util/Map;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_0

    const-string v1, " [OPTIONS]"

    .line 15221
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 15224
    :cond_0
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->positionalParameters()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    .line 15225
    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->hidden()Z

    move-result v3

    if-nez v3, :cond_1

    const/16 v3, 0x20

    .line 15226
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v4

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v5

    iget-object v6, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    invoke-static {v6}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->access$22700(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Ljava/util/List;

    move-result-object v6

    invoke-interface {v4, v2, v5, v6}, Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;->renderParameterLabel(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;Lgroovyjarjarpicocli/CommandLine$Help$Ansi;Ljava/util/List;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 15231
    :cond_2
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->subcommands()Ljava/util/Map;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_3

    const-string v1, " "

    .line 15232
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->synopsisSubcommandLabel()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 15235
    :cond_3
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->qualifiedName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->commandText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 15236
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "line.separator"

    invoke-static {v1}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public addAllSubcommands(Ljava/util/Map;)Lgroovyjarjarpicocli/CommandLine$Help;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarpicocli/CommandLine;",
            ">;)",
            "Lgroovyjarjarpicocli/CommandLine$Help;"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 15131
    invoke-direct {p0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->registerSubcommands(Ljava/util/Map;)V

    :cond_0
    return-object p0
.end method

.method public addSubcommand(Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarpicocli/CommandLine$Help;
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 15175
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->getHelpFactory()Lgroovyjarjarpicocli/CommandLine$IHelpFactory;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->commandLine()Lgroovyjarjarpicocli/CommandLine;

    move-result-object v1

    invoke-static {v1}, Lgroovyjarjarpicocli/CommandLine;->access$15400(Lgroovyjarjarpicocli/CommandLine;)Lgroovyjarjarpicocli/CommandLine$IFactory;

    move-result-object v1

    invoke-static {p2, v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->forAnnotatedObject(Ljava/lang/Object;Lgroovyjarjarpicocli/CommandLine$IFactory;)Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    move-result-object p2

    sget-object v1, Lgroovyjarjarpicocli/CommandLine$Help$Ansi;->AUTO:Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    invoke-static {v1}, Lgroovyjarjarpicocli/CommandLine$Help;->defaultColorScheme(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object v1

    invoke-interface {v0, p2, v1}, Lgroovyjarjarpicocli/CommandLine$IHelpFactory;->create(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Lgroovyjarjarpicocli/CommandLine$Help;

    move-result-object p2

    .line 15176
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->visibleCommands:Ljava/util/Map;

    invoke-interface {v0, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 15177
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->allCommands:Ljava/util/Map;

    invoke-interface {v0, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p0
.end method

.method protected aliases()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 15115
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->aliases:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public allSubcommands()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarpicocli/CommandLine$Help;",
            ">;"
        }
    .end annotation

    .line 15111
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->allCommands:Ljava/util/Map;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method public ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;
    .locals 1

    .line 16136
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->access$1200(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v0

    return-object v0
.end method

.method public atFileParameterList()Ljava/lang/String;
    .locals 3

    .line 15691
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->hasAtFileParameter()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 15692
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->AT_FILE_POSITIONAL_PARAM:Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->messages()Lgroovyjarjarpicocli/CommandLine$Model$Messages;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->messages(Lgroovyjarjarpicocli/CommandLine$Model$Messages;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    .line 15693
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultLayout()Lgroovyjarjarpicocli/CommandLine$Help$Layout;

    move-result-object v0

    .line 15694
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->AT_FILE_POSITIONAL_PARAM:Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->addPositionalParameter(Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)V

    .line 15695
    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    const-string v0, ""

    return-object v0
.end method

.method public calcLongOptionColumnWidth(Ljava/util/List;Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)I
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;",
            ">;",
            "Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;",
            ")I"
        }
    .end annotation

    .line 16017
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultOptionRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IOptionRenderer;

    move-result-object v0

    .line 16018
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->adjustLineBreaksForWideCJKCharacters()Z

    move-result v1

    .line 16019
    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->longOptionsMaxWidth()I

    move-result v2

    add-int/lit8 v2, v2, 0x1

    .line 16020
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    const/4 v3, 0x0

    move v4, v3

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    const/4 v6, 0x3

    if-eqz v5, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    .line 16021
    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->hidden()Z

    move-result v7

    if-eqz v7, :cond_1

    goto :goto_0

    .line 16022
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v7

    invoke-interface {v0, v5, v7, p3}, Lgroovyjarjarpicocli/CommandLine$Help$IOptionRenderer;->render(Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)[[Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v5

    if-eqz v1, :cond_2

    .line 16023
    aget-object v5, v5, v3

    aget-object v5, v5, v6

    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->getCJKAdjustedLength()I

    move-result v5

    goto :goto_1

    :cond_2
    aget-object v5, v5, v3

    aget-object v5, v5, v6

    invoke-static {v5}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->access$15100(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)I

    move-result v5

    :goto_1
    if-ge v5, v2, :cond_0

    .line 16024
    invoke-static {v4, v5}, Ljava/lang/Math;->max(II)I

    move-result v4

    goto :goto_0

    .line 16026
    :cond_3
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1, p2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 16027
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->hasAtFileParameter()Z

    move-result p2

    if-eqz p2, :cond_4

    .line 16028
    iget-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->AT_FILE_POSITIONAL_PARAM:Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    invoke-interface {p1, v3, p2}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 16029
    iget-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->AT_FILE_POSITIONAL_PARAM:Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->messages()Lgroovyjarjarpicocli/CommandLine$Model$Messages;

    move-result-object v0

    invoke-virtual {p2, v0}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->messages(Lgroovyjarjarpicocli/CommandLine$Model$Messages;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    .line 16032
    :cond_4
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_5
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_8

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    .line 16033
    invoke-virtual {p2}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->hidden()Z

    move-result v0

    if-eqz v0, :cond_6

    goto :goto_2

    .line 16035
    :cond_6
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v0

    invoke-virtual {p3}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v3

    invoke-static {p3}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->access$22700(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Ljava/util/List;

    move-result-object v5

    invoke-interface {v0, p2, v3, v5}, Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;->renderParameterLabel(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;Lgroovyjarjarpicocli/CommandLine$Help$Ansi;Ljava/util/List;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    if-eqz v1, :cond_7

    .line 16036
    invoke-virtual {p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->getCJKAdjustedLength()I

    move-result p2

    goto :goto_3

    :cond_7
    invoke-static {p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->access$15100(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)I

    move-result p2

    :goto_3
    if-ge p2, v2, :cond_5

    .line 16037
    invoke-static {v4, p2}, Ljava/lang/Math;->max(II)I

    move-result p2

    move v4, p2

    goto :goto_2

    :cond_8
    add-int/2addr v4, v6

    return v4
.end method

.method public colorScheme()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;
    .locals 1

    .line 15097
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    return-object v0
.end method

.method public commandList()Ljava/lang/String;
    .locals 1

    .line 15930
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->subcommands()Ljava/util/Map;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->commandList(Ljava/util/Map;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public commandList(Ljava/util/Map;)Ljava/lang/String;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarpicocli/CommandLine$Help;",
            ">;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 15938
    invoke-interface {p1}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    const-string v1, ""

    if-eqz v0, :cond_0

    return-object v1

    .line 15939
    :cond_0
    invoke-interface {p1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine$Help;->maxLength(Ljava/util/Collection;)I

    move-result v0

    .line 15940
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v2

    const/4 v3, 0x2

    new-array v4, v3, [Lgroovyjarjarpicocli/CommandLine$Help$Column;

    new-instance v5, Lgroovyjarjarpicocli/CommandLine$Help$Column;

    add-int/2addr v0, v3

    sget-object v6, Lgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;->SPAN:Lgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;

    invoke-direct {v5, v0, v3, v6}, Lgroovyjarjarpicocli/CommandLine$Help$Column;-><init>(IILgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;)V

    const/4 v6, 0x0

    aput-object v5, v4, v6

    new-instance v5, Lgroovyjarjarpicocli/CommandLine$Help$Column;

    .line 15942
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v7

    sub-int/2addr v7, v0

    sget-object v0, Lgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;->WRAP:Lgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;

    invoke-direct {v5, v7, v3, v0}, Lgroovyjarjarpicocli/CommandLine$Help$Column;-><init>(IILgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;)V

    const/4 v0, 0x1

    aput-object v5, v4, v0

    .line 15940
    invoke-static {v2, v4}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->forColumns(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;[Lgroovyjarjarpicocli/CommandLine$Help$Column;)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    move-result-object v2

    .line 15943
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->adjustCJK()Z

    move-result v4

    invoke-virtual {v2, v4}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->setAdjustLineBreaksForWideCJKCharacters(Z)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    .line 15945
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_5

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Map$Entry;

    .line 15946
    invoke-interface {v4}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarpicocli/CommandLine$Help;

    .line 15947
    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec()Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    move-result-object v5

    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v5

    .line 15948
    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->header()[Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Lgroovyjarjarpicocli/CommandLine;->access$22600([Ljava/lang/Object;)Z

    move-result v7

    if-nez v7, :cond_2

    .line 15949
    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->header()[Ljava/lang/String;

    move-result-object v5

    aget-object v5, v5, v6

    goto :goto_0

    .line 15950
    :cond_2
    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->description()[Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Lgroovyjarjarpicocli/CommandLine;->access$22600([Ljava/lang/Object;)Z

    move-result v7

    if-nez v7, :cond_3

    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->description()[Ljava/lang/String;

    move-result-object v5

    aget-object v5, v5, v6

    goto :goto_0

    :cond_3
    move-object v5, v1

    .line 15951
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object v7

    new-array v8, v6, [Ljava/lang/Object;

    invoke-static {v5, v8}, Lgroovyjarjarpicocli/CommandLine;->access$7400(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v7, v5}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->text(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v5

    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->splitLines()[Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v5

    move v7, v6

    .line 15952
    :goto_1
    array-length v8, v5

    if-ge v7, v8, :cond_1

    new-array v8, v3, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    if-nez v7, :cond_4

    const-string v9, ", "

    .line 15953
    invoke-virtual {v4, v9}, Lgroovyjarjarpicocli/CommandLine$Help;->commandNamesText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v9

    goto :goto_2

    :cond_4
    sget-object v9, Lgroovyjarjarpicocli/CommandLine$Help$Ansi;->EMPTY_TEXT:Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    :goto_2
    aput-object v9, v8, v6

    aget-object v9, v5, v7

    aput-object v9, v8, v0

    invoke-virtual {v2, v8}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->addRowValues([Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)V

    add-int/lit8 v7, v7, 0x1

    goto :goto_1

    .line 15956
    :cond_5
    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public varargs commandListHeading([Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 15858
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->visibleCommands:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p1, ""

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->commandListHeading()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    :goto_0
    return-object p1
.end method

.method commandName()Ljava/lang/String;
    .locals 1

    .line 15183
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->name()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public commandNamesText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 4

    .line 15968
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->aliases()Ljava/util/List;

    move-result-object v1

    const/4 v2, 0x0

    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->commandText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    const/4 v1, 0x1

    .line 15969
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->aliases()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_0

    .line 15970
    invoke-virtual {v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object v2

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->aliases()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {v2, v3}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->commandText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public commandSpec()Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;
    .locals 1

    .line 15093
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    return-object v0
.end method

.method public createDefaultLayout()Lgroovyjarjarpicocli/CommandLine$Help$Layout;
    .locals 3

    .line 15991
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->options()Ljava/util/List;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->positionalParameters()Ljava/util/List;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object v2

    invoke-virtual {p0, v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultLayout(Ljava/util/List;Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Lgroovyjarjarpicocli/CommandLine$Help$Layout;

    move-result-object v0

    return-object v0
.end method

.method public createDefaultLayout(Ljava/util/List;Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Lgroovyjarjarpicocli/CommandLine$Help$Layout;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;",
            ">;",
            "Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;",
            ")",
            "Lgroovyjarjarpicocli/CommandLine$Help$Layout;"
        }
    .end annotation

    .line 16000
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help;->calcLongOptionColumnWidth(Ljava/util/List;Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)I

    move-result p1

    invoke-direct {p0, p1, p3}, Lgroovyjarjarpicocli/CommandLine$Help;->createLayout(ILgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)Lgroovyjarjarpicocli/CommandLine$Help$Layout;

    move-result-object p1

    return-object p1
.end method

.method public createDefaultOptionRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IOptionRenderer;
    .locals 4

    .line 16058
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$DefaultOptionRenderer;

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-static {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->access$22900(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->showDefaultValues()Z

    move-result v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, ""

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v3

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->requiredOptionMarker()C

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$DefaultOptionRenderer;-><init>(ZLjava/lang/String;)V

    return-object v0
.end method

.method public createDefaultOptionSort()Ljava/util/Comparator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;"
        }
    .end annotation

    .line 15518
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->sortOptions()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 15519
    invoke-static {}, Lgroovyjarjarpicocli/CommandLine$Help;->createShortOptionNameComparator()Ljava/util/Comparator;

    move-result-object v0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    .line 15520
    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->options()Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine$Help;->createOrderComparatorIfNecessary(Ljava/util/List;)Ljava/util/Comparator;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method public createDefaultParamLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;
    .locals 2

    .line 16109
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$DefaultParamLabelRenderer;

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-direct {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$DefaultParamLabelRenderer;-><init>(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)V

    return-object v0
.end method

.method public createDefaultParameterRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParameterRenderer;
    .locals 4

    .line 16081
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$DefaultParameterRenderer;

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-static {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->access$22900(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->showDefaultValues()Z

    move-result v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, ""

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v3

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->requiredOptionMarker()C

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$DefaultParameterRenderer;-><init>(ZLjava/lang/String;)V

    return-object v0
.end method

.method protected createDetailedSynopsisCommandText()Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 3

    .line 15437
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;I)V

    .line 15438
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->subcommands()Ljava/util/Map;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_0

    const-string v1, " "

    .line 15439
    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->synopsisSubcommandLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method protected createDetailedSynopsisEndOfOptionsText()Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 3

    .line 15400
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->access$22900(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->showEndOfOptionsDelimiterInUsageHelp()Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 15401
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-direct {v0, v2, v1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;I)V

    return-object v0

    .line 15403
    :cond_0
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    invoke-direct {v0, v2, v1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;I)V

    const-string v1, " ["

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->parser()Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;->endOfOptionsDelimiter()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->optionText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    return-object v0
.end method

.method protected createDetailedSynopsisGroupsText(Ljava/util/Set;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;",
            ">;)",
            "Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;"
        }
    .end annotation

    .line 15301
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;I)V

    .line 15302
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec()Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->argGroups()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;

    .line 15303
    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->validate()Z

    move-result v3

    if-eqz v3, :cond_0

    const-string v3, " "

    .line 15304
    invoke-virtual {v0, v3}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme()Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object v3

    invoke-virtual {v2, v3, p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->synopsisText(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;Ljava/util/Set;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method protected createDetailedSynopsisOptionsText(Ljava/util/Collection;Ljava/util/Comparator;Z)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;",
            ">;",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;Z)",
            "Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;"
        }
    .end annotation

    .line 15317
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->options()Ljava/util/List;

    move-result-object v0

    invoke-virtual {p0, p1, v0, p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help;->createDetailedSynopsisOptionsText(Ljava/util/Collection;Ljava/util/List;Ljava/util/Comparator;Z)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p1

    return-object p1
.end method

.method protected createDetailedSynopsisOptionsText(Ljava/util/Collection;Ljava/util/List;Ljava/util/Comparator;Z)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;",
            ">;",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;Z)",
            "Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;"
        }
    .end annotation

    .line 15328
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;I)V

    .line 15329
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1, p2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    if-eqz p3, :cond_0

    .line 15331
    invoke-static {v1, p3}, Ljava/util/Collections;->sort(Ljava/util/List;Ljava/util/Comparator;)V

    .line 15333
    :cond_0
    invoke-interface {v1, p1}, Ljava/util/List;->removeAll(Ljava/util/Collection;)Z

    const-string p1, " "

    if-eqz p4, :cond_7

    .line 15335
    new-instance p2, Ljava/util/ArrayList;

    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    .line 15336
    new-instance p3, Ljava/lang/StringBuilder;

    const-string p4, "-"

    invoke-direct {p3, p4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 15337
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2, p4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 15338
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_1
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    const/4 v5, 0x1

    if-eqz v4, :cond_5

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    .line 15339
    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->hidden()Z

    move-result v6

    if-eqz v6, :cond_2

    goto :goto_0

    .line 15340
    :cond_2
    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->typeInfo()Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    move-result-object v6

    invoke-interface {v6}, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;->isBoolean()Z

    move-result v6

    if-eqz v6, :cond_1

    .line 15341
    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->arity()Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object v6

    iget v6, v6, Lgroovyjarjarpicocli/CommandLine$Range;->max:I

    if-gtz v6, :cond_1

    .line 15342
    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->shortestName()Ljava/lang/String;

    move-result-object v6

    .line 15343
    invoke-virtual {v6}, Ljava/lang/String;->length()I

    move-result v7

    const/4 v8, 0x2

    if-ne v7, v8, :cond_1

    invoke-virtual {v6, p4}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_1

    .line 15345
    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->negatable()Z

    move-result v7

    if-eqz v7, :cond_3

    iget-object v7, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v7}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->negatableOptionTransformer()Lgroovyjarjarpicocli/CommandLine$INegatableOptionTransformer;

    move-result-object v7

    iget-object v8, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-interface {v7, v6, v8}, Lgroovyjarjarpicocli/CommandLine$INegatableOptionTransformer;->makeSynopsis(Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_1

    .line 15346
    :cond_3
    invoke-interface {p2, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 15347
    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->required()Z

    move-result v4

    if-eqz v4, :cond_4

    .line 15348
    invoke-virtual {v6, v5}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 15350
    :cond_4
    invoke-virtual {v6, v5}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 15356
    :cond_5
    invoke-interface {v1, p2}, Ljava/util/List;->removeAll(Ljava/util/Collection;)Z

    .line 15357
    invoke-virtual {p3}, Ljava/lang/StringBuilder;->length()I

    move-result p2

    if-le p2, v5, :cond_6

    .line 15358
    invoke-virtual {v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    iget-object p4, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p4, p3}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->optionText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p3

    invoke-virtual {p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    move-object v0, p2

    .line 15360
    :cond_6
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->length()I

    move-result p2

    if-le p2, v5, :cond_7

    const-string p2, " ["

    .line 15361
    invoke-virtual {v0, p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    iget-object p3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p4

    invoke-virtual {p3, p4}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->optionText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p3

    invoke-virtual {p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    const-string p3, "]"

    invoke-virtual {p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    .line 15364
    :cond_7
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_8

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    .line 15365
    iget-object p4, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v1

    invoke-static {p1, v0, p4, p3, v1}, Lgroovyjarjarpicocli/CommandLine$Help;->concatOptionText(Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    goto :goto_1

    :cond_8
    return-object v0
.end method

.method protected createDetailedSynopsisPositionalsText(Ljava/util/Collection;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;",
            ">;)",
            "Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;"
        }
    .end annotation

    .line 15412
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;I)V

    .line 15413
    new-instance v1, Ljava/util/ArrayList;

    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->positionalParameters()Ljava/util/List;

    move-result-object v3

    invoke-direct {v1, v3}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 15414
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->hasAtFileParameter()Z

    move-result v3

    if-eqz v3, :cond_0

    .line 15415
    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->AT_FILE_POSITIONAL_PARAM:Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    invoke-interface {v1, v2, v3}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 15416
    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->AT_FILE_POSITIONAL_PARAM:Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v3

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->messages()Lgroovyjarjarpicocli/CommandLine$Model$Messages;

    move-result-object v3

    invoke-virtual {v2, v3}, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;->messages(Lgroovyjarjarpicocli/CommandLine$Model$Messages;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    .line 15418
    :cond_0
    invoke-interface {v1, p1}, Ljava/util/List;->removeAll(Ljava/util/Collection;)Z

    .line 15419
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;

    .line 15420
    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v3

    const-string v4, " "

    invoke-static {v4, v0, v2, v1, v3}, Lgroovyjarjarpicocli/CommandLine$Help;->concatPositionalText(Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public varargs createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;
    .locals 3

    .line 15894
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v0

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v1

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->adjustCJK()Z

    move-result v2

    invoke-static {v0, v1, v2, p1, p2}, Lgroovyjarjarpicocli/CommandLine$Help;->heading(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;IZLjava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public createTextTable(Ljava/util/Map;)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "**>;)",
            "Lgroovyjarjarpicocli/CommandLine$Help$TextTable;"
        }
    .end annotation

    const/4 v0, 0x1

    const/4 v1, 0x2

    const/4 v2, 0x0

    if-eqz p1, :cond_2

    .line 15912
    invoke-interface {p1}, Ljava/util/Map;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_1

    .line 15915
    :cond_0
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v3

    add-int/lit8 v3, v3, -0x3

    sub-int/2addr v3, v0

    invoke-interface {p1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v4

    invoke-static {v4}, Lgroovyjarjarpicocli/CommandLine$Help;->maxLength(Ljava/util/Collection;)I

    move-result v4

    invoke-static {v3, v4}, Ljava/lang/Math;->min(II)I

    move-result v3

    .line 15916
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v4

    new-array v5, v1, [Lgroovyjarjarpicocli/CommandLine$Help$Column;

    new-instance v6, Lgroovyjarjarpicocli/CommandLine$Help$Column;

    add-int/lit8 v3, v3, 0x3

    sget-object v7, Lgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;->SPAN:Lgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;

    invoke-direct {v6, v3, v1, v7}, Lgroovyjarjarpicocli/CommandLine$Help$Column;-><init>(IILgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;)V

    aput-object v6, v5, v2

    new-instance v6, Lgroovyjarjarpicocli/CommandLine$Help$Column;

    .line 15918
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v7

    sub-int/2addr v7, v3

    sget-object v3, Lgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;->WRAP:Lgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;

    invoke-direct {v6, v7, v1, v3}, Lgroovyjarjarpicocli/CommandLine$Help$Column;-><init>(IILgroovyjarjarpicocli/CommandLine$Help$Column$Overflow;)V

    aput-object v6, v5, v0

    .line 15916
    invoke-static {v4, v5}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->forColumns(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;[Lgroovyjarjarpicocli/CommandLine$Help$Column;)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    move-result-object v3

    .line 15919
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->adjustCJK()Z

    move-result v4

    invoke-virtual {v3, v4}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->setAdjustLineBreaksForWideCJKCharacters(Z)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    .line 15921
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Map$Entry;

    new-array v5, v1, [Ljava/lang/String;

    .line 15922
    invoke-interface {v4}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v6

    invoke-static {v6}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v6

    new-array v7, v2, [Ljava/lang/Object;

    invoke-static {v6, v7}, Lgroovyjarjarpicocli/CommandLine;->access$7400(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v6

    aput-object v6, v5, v2

    invoke-interface {v4}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    invoke-static {v4}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    new-array v6, v2, [Ljava/lang/Object;

    invoke-static {v4, v6}, Lgroovyjarjarpicocli/CommandLine;->access$7400(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    aput-object v4, v5, v0

    invoke-virtual {v3, v5}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->addRowValues([Ljava/lang/String;)V

    goto :goto_0

    :cond_1
    return-object v3

    .line 15912
    :cond_2
    :goto_1
    iget-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    new-array v1, v1, [I

    const/16 v3, 0xa

    aput v3, v1, v2

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v2

    sub-int/2addr v2, v3

    aput v2, v1, v0

    invoke-static {p1, v1}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->forColumnWidths(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;[I)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    move-result-object p1

    return-object p1
.end method

.method public varargs customSynopsis([Ljava/lang/Object;)Ljava/lang/String;
    .locals 6

    .line 15777
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v0

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v1

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->adjustCJK()Z

    move-result v2

    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v3

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->customSynopsis()[Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    move-object v5, p1

    invoke-static/range {v0 .. v5}, Lgroovyjarjarpicocli/CommandLine$Help;->join(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;IZ[Ljava/lang/String;Ljava/lang/StringBuilder;[Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public varargs description([Ljava/lang/Object;)Ljava/lang/String;
    .locals 6

    .line 15786
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v0

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v1

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->adjustCJK()Z

    move-result v2

    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v3

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->description()[Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    move-object v5, p1

    invoke-static/range {v0 .. v5}, Lgroovyjarjarpicocli/CommandLine$Help;->join(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;IZ[Ljava/lang/String;Ljava/lang/StringBuilder;[Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public varargs descriptionHeading([Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 15826
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->descriptionHeading()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine;->access$2200(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p1, ""

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->descriptionHeading()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    :goto_0
    return-object p1
.end method

.method public detailedSynopsis(ILjava/util/Comparator;Z)Ljava/lang/String;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;Z)",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 15256
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 15257
    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDetailedSynopsisGroupsText(Ljava/util/Set;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v4

    .line 15258
    invoke-virtual {p0, v0, p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help;->createDetailedSynopsisOptionsText(Ljava/util/Collection;Ljava/util/Comparator;Z)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v3

    .line 15259
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDetailedSynopsisEndOfOptionsText()Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v5

    .line 15260
    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDetailedSynopsisPositionalsText(Ljava/util/Collection;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v6

    .line 15261
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDetailedSynopsisCommandText()Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v7

    move-object v1, p0

    move v2, p1

    .line 15263
    invoke-virtual/range {v1 .. v7}, Lgroovyjarjarpicocli/CommandLine$Help;->makeSynopsisFromParts(ILgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public detailedSynopsis(Ljava/util/Comparator;Z)Ljava/lang/String;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;Z)",
            "Ljava/lang/String;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    const/4 v0, 0x0

    .line 15245
    invoke-virtual {p0, v0, p1, p2}, Lgroovyjarjarpicocli/CommandLine$Help;->detailedSynopsis(ILjava/util/Comparator;Z)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public endOfOptionsList()Ljava/lang/String;
    .locals 4

    .line 15706
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->access$22900(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->showEndOfOptionsDelimiterInUsageHelp()Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, ""

    return-object v0

    .line 15709
    :cond_0
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->parser()Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;->endOfOptionsDelimiter()Ljava/lang/String;

    move-result-object v0

    const-string v1, "--"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->END_OF_OPTIONS_OPTION:Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    .line 15711
    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->parser()Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;->endOfOptionsDelimiter()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->createEndOfOptionsOption(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    move-result-object v0

    .line 15712
    :goto_0
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    iput-object v1, v0, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    const/4 v1, 0x0

    .line 15714
    :try_start_0
    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->messages()Lgroovyjarjarpicocli/CommandLine$Model$Messages;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->messages(Lgroovyjarjarpicocli/CommandLine$Model$Messages;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    .line 15715
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultLayout()Lgroovyjarjarpicocli/CommandLine$Help$Layout;

    move-result-object v2

    .line 15716
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v3

    invoke-virtual {v2, v0, v3}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->addOption(Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)V

    .line 15717
    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->toString()Ljava/lang/String;

    move-result-object v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 15719
    iput-object v1, v0, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    return-object v2

    :catchall_0
    move-exception v2

    iput-object v1, v0, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    .line 15720
    throw v2
.end method

.method public exitCodeList()Ljava/lang/String;
    .locals 1

    .line 15879
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->exitCodeList()Ljava/util/Map;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->createTextTable(Ljava/util/Map;)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public varargs exitCodeListHeading([Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 15873
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->exitCodeListHeading()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public varargs footer([Ljava/lang/Object;)Ljava/lang/String;
    .locals 6

    .line 15804
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v0

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v1

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->adjustCJK()Z

    move-result v2

    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v3

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->footer()[Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    move-object v5, p1

    invoke-static/range {v0 .. v5}, Lgroovyjarjarpicocli/CommandLine$Help;->join(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;IZ[Ljava/lang/String;Ljava/lang/StringBuilder;[Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public varargs footerHeading([Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 15865
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->footerHeading()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public fullSynopsis()Ljava/lang/String;
    .locals 2

    .line 15191
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v1, 0x0

    new-array v1, v1, [Ljava/lang/Object;

    invoke-virtual {p0, v1}, Lgroovyjarjarpicocli/CommandLine$Help;->synopsisHeading([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->synopsisHeadingLength()I

    move-result v1

    invoke-virtual {p0, v1}, Lgroovyjarjarpicocli/CommandLine$Help;->synopsis(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public hasAtFileParameter()Z
    .locals 1

    .line 15682
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->access$21200(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$ParserSpec;->expandAtFiles()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->access$22900(Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;)Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->showAtFileInUsageHelp()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public varargs header([Ljava/lang/Object;)Ljava/lang/String;
    .locals 6

    .line 15795
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->ansi()Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    move-result-object v0

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v1

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->adjustCJK()Z

    move-result v2

    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v3

    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->header()[Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    move-object v5, p1

    invoke-static/range {v0 .. v5}, Lgroovyjarjarpicocli/CommandLine$Help;->join(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;IZ[Ljava/lang/String;Ljava/lang/StringBuilder;[Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public varargs headerHeading([Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 15811
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->headerHeading()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected insertSynopsisCommandName(ILgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Ljava/lang/String;
    .locals 9

    if-ltz p1, :cond_2

    .line 15456
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->qualifiedName()Ljava/lang/String;

    move-result-object v0

    .line 15459
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v1

    add-int/2addr v1, p1

    const/4 v2, 0x1

    add-int/2addr v1, v2

    int-to-double v3, v1

    .line 15460
    iget-object v5, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v5

    invoke-virtual {v5}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->synopsisAutoIndentThreshold()D

    move-result-wide v5

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v7

    int-to-double v7, v7

    mul-double/2addr v5, v7

    cmpl-double v3, v3, v5

    if-lez v3, :cond_1

    .line 15461
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->synopsisIndent()I

    move-result v1

    if-gez v1, :cond_0

    move v1, p1

    goto :goto_0

    :cond_0
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->synopsisIndent()I

    move-result v1

    :goto_0
    const-wide v3, 0x3feccccccccccccdL    # 0.9

    .line 15462
    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v5

    int-to-double v5, v5

    mul-double/2addr v5, v3

    double-to-int v3, v5

    invoke-static {v1, v3}, Ljava/lang/Math;->min(II)I

    move-result v1

    .line 15464
    :cond_1
    iget-object v3, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    new-array v4, v2, [I

    invoke-direct {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->width()I

    move-result v5

    const/4 v6, 0x0

    aput v5, v4, v6

    invoke-static {v3, v4}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->forColumnWidths(Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;[I)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    move-result-object v3

    .line 15465
    iget-object v4, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v4

    invoke-virtual {v4}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->adjustLineBreaksForWideCJKCharacters()Z

    move-result v4

    invoke-virtual {v3, v4}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->setAdjustLineBreaksForWideCJKCharacters(Z)Lgroovyjarjarpicocli/CommandLine$Help$TextTable;

    .line 15466
    iput v1, v3, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->indentWrappedLines:I

    .line 15469
    new-instance v1, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    sget-object v4, Lgroovyjarjarpicocli/CommandLine$Help$Ansi;->OFF:Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    invoke-virtual {v4}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    const/16 v5, 0x58

    invoke-static {v5, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->stringOf(CI)Ljava/lang/String;

    move-result-object v5

    invoke-static {p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->access$23000(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    move-result-object v7

    invoke-direct {v1, v4, v5, v7}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;)V

    new-array v2, v2, [Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    .line 15470
    iget-object v4, p0, Lgroovyjarjarpicocli/CommandLine$Help;->colorScheme:Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;

    invoke-virtual {v4, v0}, Lgroovyjarjarpicocli/CommandLine$Help$ColorScheme;->commandText(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    invoke-virtual {v1, v0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object v0

    invoke-virtual {v0, p2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    aput-object p2, v2, v6

    invoke-virtual {v3, v2}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->addRowValues([Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)V

    .line 15471
    invoke-virtual {v3}, Lgroovyjarjarpicocli/CommandLine$Help$TextTable;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 15453
    :cond_2
    new-instance p2, Ljava/lang/IllegalArgumentException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "synopsisHeadingLength must be a positive number but was "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p2
.end method

.method protected makeSynopsisFromParts(ILgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Ljava/lang/String;
    .locals 4

    .line 15280
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec()Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->argGroups()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x1

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;

    .line 15281
    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->validate()Z

    move-result v3

    if-eqz v3, :cond_0

    .line 15282
    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;->allOptionsNested()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    and-int/2addr v1, v2

    goto :goto_0

    :cond_1
    if-eqz v1, :cond_2

    .line 15287
    invoke-virtual {p2, p4}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    invoke-virtual {p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    invoke-virtual {p2, p5}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    invoke-virtual {p2, p6}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    goto :goto_1

    .line 15289
    :cond_2
    invoke-virtual {p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    invoke-virtual {p2, p4}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    invoke-virtual {p2, p5}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    invoke-virtual {p2, p6}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->concat(Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    move-result-object p2

    .line 15291
    :goto_1
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarpicocli/CommandLine$Help;->insertSynopsisCommandName(ILgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public optionList()Ljava/lang/String;
    .locals 3

    .line 15533
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultLayout()Lgroovyjarjarpicocli/CommandLine$Help$Layout;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultOptionSort()Ljava/util/Comparator;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v2

    invoke-virtual {p0, v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help;->optionList(Lgroovyjarjarpicocli/CommandLine$Help$Layout;Ljava/util/Comparator;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public optionList(Lgroovyjarjarpicocli/CommandLine$Help$Layout;Ljava/util/Comparator;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$Help$Layout;",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;",
            "Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;",
            ")",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 15562
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->options()Ljava/util/List;

    move-result-object v0

    invoke-direct {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->excludeHiddenAndGroupOptions(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    .line 15563
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0, v0, p1, p2, p3}, Lgroovyjarjarpicocli/CommandLine$Help;->optionListExcludingGroups(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$Layout;Ljava/util/Comparator;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->optionListGroupSections()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public optionListExcludingGroups(Ljava/util/List;)Ljava/lang/String;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 15550
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultLayout()Lgroovyjarjarpicocli/CommandLine$Help$Layout;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultOptionSort()Ljava/util/Comparator;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v2

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help;->optionListExcludingGroups(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$Layout;Ljava/util/Comparator;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public optionListExcludingGroups(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$Layout;Ljava/util/Comparator;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;",
            "Lgroovyjarjarpicocli/CommandLine$Help$Layout;",
            "Ljava/util/Comparator<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;",
            "Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;",
            ")",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 15576
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0, p1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    if-eqz p3, :cond_0

    .line 15578
    invoke-static {v0, p3}, Ljava/util/Collections;->sort(Ljava/util/List;Ljava/util/Comparator;)V

    .line 15581
    :cond_0
    invoke-virtual {p2, v0, p4}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->addAllOptions(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)V

    .line 15582
    invoke-virtual {p2}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public optionListGroupSections()Ljava/lang/String;
    .locals 3

    .line 15595
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->optionSectionGroups()Ljava/util/List;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultOptionSort()Ljava/util/Comparator;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v2

    invoke-direct {p0, v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help;->optionListGroupSections(Ljava/util/List;Ljava/util/Comparator;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public varargs optionListHeading([Ljava/lang/Object;)Ljava/lang/String;
    .locals 3

    .line 15846
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->options()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;->hidden()Z

    move-result v2

    xor-int/lit8 v2, v2, 0x1

    or-int/2addr v1, v2

    goto :goto_0

    .line 15847
    :cond_0
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->showEndOfOptionsDelimiterInUsageHelp()Z

    move-result v0

    if-nez v0, :cond_2

    if-eqz v1, :cond_1

    goto :goto_1

    :cond_1
    const-string p1, ""

    return-object p1

    .line 15848
    :cond_2
    :goto_1
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->optionListHeading()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public optionSectionGroups()Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgGroupSpec;",
            ">;"
        }
    .end annotation

    .line 15628
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 15629
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v1}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->argGroups()Ljava/util/List;

    move-result-object v1

    invoke-static {v1, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->optionSectionGroups(Ljava/util/List;Ljava/util/List;)V

    return-object v0
.end method

.method options()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$OptionSpec;",
            ">;"
        }
    .end annotation

    .line 15181
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->options()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;
    .locals 1

    .line 15121
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer:Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    return-object v0
.end method

.method public parameterList()Ljava/lang/String;
    .locals 1

    .line 15645
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->positionalParameters()Ljava/util/List;

    move-result-object v0

    invoke-direct {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->excludeHiddenAndGroupParams(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterList(Ljava/util/List;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public parameterList(Lgroovyjarjarpicocli/CommandLine$Help$Layout;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;
    .locals 1

    .line 15664
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->positionalParameters()Ljava/util/List;

    move-result-object v0

    invoke-direct {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->excludeHiddenAndGroupParams(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    invoke-virtual {p0, v0, p1, p2}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterList(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$Layout;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public parameterList(Ljava/util/List;)Ljava/lang/String;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;",
            ">;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 15655
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->createDefaultLayout()Lgroovyjarjarpicocli/CommandLine$Help$Layout;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterLabelRenderer()Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;

    move-result-object v1

    invoke-virtual {p0, p1, v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help;->parameterList(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$Layout;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public parameterList(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$Layout;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)Ljava/lang/String;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;",
            ">;",
            "Lgroovyjarjarpicocli/CommandLine$Help$Layout;",
            "Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;",
            ")",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 15675
    invoke-virtual {p2, p1, p3}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->addAllPositionalParameters(Ljava/util/List;Lgroovyjarjarpicocli/CommandLine$Help$IParamLabelRenderer;)V

    .line 15676
    invoke-virtual {p2}, Lgroovyjarjarpicocli/CommandLine$Help$Layout;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public varargs parameterListHeading([Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 15834
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->hasAtFileParameter()Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->positionalParameters()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    const-string p1, ""

    return-object p1

    .line 15835
    :cond_1
    :goto_0
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->parameterListHeading()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method positionalParameters()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarpicocli/CommandLine$Model$PositionalParamSpec;",
            ">;"
        }
    .end annotation

    .line 15182
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->positionalParameters()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public subcommands()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarpicocli/CommandLine$Help;",
            ">;"
        }
    .end annotation

    .line 15106
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->visibleCommands:Ljava/util/Map;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method public synopsis()Ljava/lang/String;
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    const/4 v0, 0x0

    .line 15199
    invoke-virtual {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Help;->synopsis(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public synopsis(I)Ljava/lang/String;
    .locals 2

    .line 15210
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->customSynopsis()[Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjarpicocli/CommandLine;->access$22600([Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    new-array p1, p1, [Ljava/lang/Object;

    invoke-virtual {p0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->customSynopsis([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 15211
    :cond_0
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->abbreviateSynopsis()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Help;->abbreviatedSynopsis()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    .line 15212
    :cond_1
    invoke-static {}, Lgroovyjarjarpicocli/CommandLine$Help;->createShortOptionArityAndNameComparator()Ljava/util/Comparator;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {p0, p1, v0, v1}, Lgroovyjarjarpicocli/CommandLine$Help;->detailedSynopsis(ILjava/util/Comparator;Z)Ljava/lang/String;

    move-result-object p1

    :goto_0
    return-object p1
.end method

.method public varargs synopsisHeading([Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 15818
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->synopsisHeading()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarpicocli/CommandLine$Help;->createHeading(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public synopsisHeadingLength()I
    .locals 3

    .line 15479
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;

    sget-object v1, Lgroovyjarjarpicocli/CommandLine$Help$Ansi;->OFF:Lgroovyjarjarpicocli/CommandLine$Help$Ansi;

    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    iget-object v2, p0, Lgroovyjarjarpicocli/CommandLine$Help;->commandSpec:Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$CommandSpec;->usageMessage()Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarpicocli/CommandLine$Model$UsageMessageSpec;->synopsisHeading()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;-><init>(Lgroovyjarjarpicocli/CommandLine$Help$Ansi;Ljava/lang/String;)V

    invoke-virtual {v0}, Lgroovyjarjarpicocli/CommandLine$Help$Ansi$Text;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "\\r?\\n|\\r|%n"

    const/4 v2, -0x1

    invoke-virtual {v0, v1, v2}, Ljava/lang/String;->split(Ljava/lang/String;I)[Ljava/lang/String;

    move-result-object v0

    .line 15480
    array-length v1, v0

    add-int/lit8 v1, v1, -0x1

    aget-object v0, v0, v1

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    return v0
.end method

.method withCommandNames(Ljava/util/List;)Lgroovyjarjarpicocli/CommandLine$Help;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Lgroovyjarjarpicocli/CommandLine$Help;"
        }
    .end annotation

    .line 15089
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Help;->aliases:Ljava/util/List;

    return-object p0
.end method
