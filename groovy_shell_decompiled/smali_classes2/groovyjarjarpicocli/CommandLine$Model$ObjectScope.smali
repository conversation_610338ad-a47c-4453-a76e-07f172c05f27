.class Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;
.super Ljava/lang/Object;
.source "CommandLine.java"

# interfaces
.implements Lgroovyjarjarpicocli/CommandLine$Model$IScope;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine$Model;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "ObjectScope"
.end annotation


# instance fields
.field private value:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 0

    .line 11845
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;->value:Ljava/lang/Object;

    return-void
.end method

.method static asScope(Ljava/lang/Object;)Lgroovyjarjarpicocli/CommandLine$Model$IScope;
    .locals 1

    .line 11871
    instance-of v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    if-eqz v0, :cond_0

    check-cast p0, Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    goto :goto_0

    :cond_0
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;

    invoke-direct {v0, p0}, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;-><init>(Ljava/lang/Object;)V

    move-object p0, v0

    :goto_0
    return-object p0
.end method

.method public static hasInstance(Lgroovyjarjarpicocli/CommandLine$Model$IScope;)Z
    .locals 3

    .line 11856
    instance-of v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$CommandUserObject;

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_1

    .line 11857
    check-cast p0, Lgroovyjarjarpicocli/CommandLine$Model$CommandUserObject;

    invoke-static {p0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandUserObject;->access$1900(Lgroovyjarjarpicocli/CommandLine$Model$CommandUserObject;)Ljava/lang/Object;

    move-result-object p0

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    move v1, v2

    :goto_0
    return v1

    .line 11859
    :cond_1
    invoke-static {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;->tryGet(Lgroovyjarjarpicocli/CommandLine$Model$IScope;)Ljava/lang/Object;

    move-result-object p0

    if-eqz p0, :cond_2

    goto :goto_1

    :cond_2
    move v1, v2

    :goto_1
    return v1
.end method

.method public static isProxyClass(Lgroovyjarjarpicocli/CommandLine$Model$IScope;)Z
    .locals 1

    .line 11848
    instance-of v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$CommandUserObject;

    if-eqz v0, :cond_0

    .line 11849
    check-cast p0, Lgroovyjarjarpicocli/CommandLine$Model$CommandUserObject;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$CommandUserObject;->isProxyClass()Z

    move-result p0

    return p0

    .line 11851
    :cond_0
    invoke-static {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;->tryGet(Lgroovyjarjarpicocli/CommandLine$Model$IScope;)Ljava/lang/Object;

    move-result-object p0

    if-eqz p0, :cond_1

    .line 11852
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    invoke-static {p0}, Ljava/lang/reflect/Proxy;->isProxyClass(Ljava/lang/Class;)Z

    move-result p0

    if-eqz p0, :cond_1

    const/4 p0, 0x1

    goto :goto_0

    :cond_1
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static tryGet(Lgroovyjarjarpicocli/CommandLine$Model$IScope;)Ljava/lang/Object;
    .locals 2

    .line 11866
    :try_start_0
    invoke-interface {p0}, Lgroovyjarjarpicocli/CommandLine$Model$IScope;->get()Ljava/lang/Object;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    .line 11868
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$InitializationException;

    const-string v1, "Could not get scope value"

    invoke-direct {v0, v1, p0}, Lgroovyjarjarpicocli/CommandLine$InitializationException;-><init>(Ljava/lang/String;Ljava/lang/Exception;)V

    throw v0
.end method


# virtual methods
.method public get()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">()TT;"
        }
    .end annotation

    .line 11862
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;->value:Ljava/lang/Object;

    return-object v0
.end method

.method public set(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(TT;)TT;"
        }
    .end annotation

    .line 11863
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;->value:Ljava/lang/Object;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;->value:Ljava/lang/Object;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    .line 11872
    iget-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;->value:Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    const-string v1, "Scope(value=%s)"

    invoke-static {v1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
