.class abstract Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
.super Ljava/lang/Object;
.source "CommandLine.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x408
    name = "Builder"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder<",
        "TT;>;>",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field private annotatedElement:Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;

.field private arity:Lgroovyjarjarpicocli/CommandLine$Range;

.field private auxiliaryTypes:[Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end field

.field private completionCandidates:Ljava/lang/Iterable;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Iterable<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private converters:[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lgroovyjarjarpicocli/CommandLine$ITypeConverter<",
            "*>;"
        }
    .end annotation
.end field

.field private defaultValue:Ljava/lang/String;

.field private description:[Ljava/lang/String;

.field private descriptionKey:Ljava/lang/String;

.field private echo:Z

.field private getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

.field private hasInitialValue:Z

.field private hidden:Z

.field private hideParamSyntax:Z

.field private inherited:Z

.field private initialValue:Ljava/lang/Object;

.field private initialValueState:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

.field private interactive:Z

.field private mapFallbackValue:Ljava/lang/String;

.field private originalDefaultValue:Ljava/lang/String;

.field private originalMapFallbackValue:Ljava/lang/String;

.field private paramLabel:Ljava/lang/String;

.field private parameterConsumer:Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

.field private preprocessor:Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

.field private prompt:Ljava/lang/String;

.field private required:Z

.field private root:Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

.field private scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

.field private scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

.field private setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

.field private showDefaultValue:Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

.field private splitRegex:Ljava/lang/String;

.field private splitRegexSynopsisLabel:Ljava/lang/String;

.field private toString:Ljava/lang/String;

.field private type:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end field

.field private typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

.field private userObject:Ljava/lang/Object;


# direct methods
.method constructor <init>()V
    .locals 2

    .line 9262
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 9246
    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hasInitialValue:Z

    .line 9247
    sget-object v0, Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;->UNAVAILABLE:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValueState:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    .line 9253
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectBinding;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ObjectBinding;-><init>(Lgroovyjarjarpicocli/CommandLine$1;)V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    .line 9254
    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    .line 9255
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;

    invoke-direct {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    .line 9256
    sget-object v0, Lgroovyjarjarpicocli/CommandLine$ScopeType;->LOCAL:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    const-string v0, "__unspecified__"

    .line 9258
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->mapFallbackValue:Ljava/lang/String;

    .line 9259
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalDefaultValue:Ljava/lang/String;

    .line 9260
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalMapFallbackValue:Ljava/lang/String;

    return-void
.end method

.method constructor <init>(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)V
    .locals 2

    .line 9263
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 9246
    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hasInitialValue:Z

    .line 9247
    sget-object v0, Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;->UNAVAILABLE:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValueState:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    .line 9253
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectBinding;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ObjectBinding;-><init>(Lgroovyjarjarpicocli/CommandLine$1;)V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    .line 9254
    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    .line 9255
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;

    invoke-direct {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    .line 9256
    sget-object v0, Lgroovyjarjarpicocli/CommandLine$ScopeType;->LOCAL:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    const-string v0, "__unspecified__"

    .line 9258
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->mapFallbackValue:Ljava/lang/String;

    .line 9259
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalDefaultValue:Ljava/lang/String;

    .line 9260
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalMapFallbackValue:Ljava/lang/String;

    .line 9264
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$7700(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/Object;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->userObject:Ljava/lang/Object;

    .line 9265
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$3000(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->arity:Lgroovyjarjarpicocli/CommandLine$Range;

    .line 9266
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$7800(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)[Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->description:[Ljava/lang/String;

    .line 9267
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$7900(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->descriptionKey:Ljava/lang/String;

    .line 9268
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8000(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Z

    move-result v0

    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->required:Z

    .line 9269
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8100(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Z

    move-result v0

    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->interactive:Z

    .line 9270
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8200(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Z

    move-result v0

    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->echo:Z

    .line 9271
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8300(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->prompt:Ljava/lang/String;

    .line 9272
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8400(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->paramLabel:Ljava/lang/String;

    .line 9273
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8500(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Z

    move-result v0

    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hideParamSyntax:Z

    .line 9274
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8600(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegex:Ljava/lang/String;

    .line 9275
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8700(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegexSynopsisLabel:Ljava/lang/String;

    .line 9276
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8800(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Z

    move-result v0

    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hidden:Z

    .line 9277
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$8900(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Z

    move-result v0

    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->inherited:Z

    .line 9278
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9000(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->root:Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    .line 9279
    iget-object v0, p1, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    invoke-direct {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setTypeInfo(Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)V

    .line 9280
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9100(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->converters:[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    .line 9281
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9200(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->defaultValue:Ljava/lang/String;

    .line 9282
    iget-object v0, p1, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->annotatedElement:Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->annotatedElement:Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;

    .line 9283
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9300(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/Object;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValue:Ljava/lang/Object;

    .line 9284
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9400(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValueState:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    .line 9285
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9500(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Z

    move-result v0

    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hasInitialValue:Z

    .line 9286
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9600(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->showDefaultValue:Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    .line 9287
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9700(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/Iterable;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->completionCandidates:Ljava/lang/Iterable;

    .line 9288
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9800(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->parameterConsumer:Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

    .line 9289
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$9900(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->preprocessor:Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

    .line 9290
    iget-object v0, p1, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->toString:Ljava/lang/String;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->toString:Ljava/lang/String;

    .line 9291
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$10000(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    .line 9292
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$10100(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    .line 9293
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$10200(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    .line 9294
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$10300(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$ScopeType;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    .line 9295
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$10400(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalDefaultValue:Ljava/lang/String;

    .line 9296
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;->access$10500(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalMapFallbackValue:Ljava/lang/String;

    return-void
.end method

.method constructor <init>(Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;)V
    .locals 2

    .line 9298
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 9246
    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hasInitialValue:Z

    .line 9247
    sget-object v0, Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;->UNAVAILABLE:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValueState:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    .line 9253
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectBinding;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ObjectBinding;-><init>(Lgroovyjarjarpicocli/CommandLine$1;)V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    .line 9254
    check-cast v0, Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    .line 9255
    new-instance v0, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;

    invoke-direct {v0, v1}, Lgroovyjarjarpicocli/CommandLine$Model$ObjectScope;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    .line 9256
    sget-object v0, Lgroovyjarjarpicocli/CommandLine$ScopeType;->LOCAL:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    const-string v0, "__unspecified__"

    .line 9258
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->mapFallbackValue:Ljava/lang/String;

    .line 9259
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalDefaultValue:Ljava/lang/String;

    .line 9260
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalMapFallbackValue:Ljava/lang/String;

    .line 9299
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->annotatedElement:Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;

    .line 9300
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->userObject()Ljava/lang/Object;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->userObject:Ljava/lang/Object;

    .line 9301
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getTypeInfo()Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    move-result-object v0

    invoke-direct {p0, v0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setTypeInfo(Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)V

    .line 9302
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getToString()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->toString:Ljava/lang/String;

    .line 9303
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getter()Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    .line 9304
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->setter()Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    .line 9305
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->scope()Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    .line 9306
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->hasInitialValue()Z

    move-result v0

    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hasInitialValue:Z

    .line 9307
    instance-of v0, p1, Lgroovyjarjarpicocli/CommandLine$Model$IExtensible;

    if-eqz v0, :cond_0

    .line 9308
    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Model$IExtensible;

    const-class v0, Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    invoke-interface {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Model$IExtensible;->getExtension(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValueState:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    :cond_0
    return-void
.end method

.method constructor <init>(Lgroovyjarjarpicocli/CommandLine$Option;Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;Lgroovyjarjarpicocli/CommandLine$IFactory;)V
    .locals 2

    .line 9312
    invoke-direct {p0, p2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;-><init>(Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;)V

    .line 9313
    invoke-static {p2}, Lgroovyjarjarpicocli/CommandLine$Range;->access$10600(Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;)Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->arity:Lgroovyjarjarpicocli/CommandLine$Range;

    .line 9314
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->required()Z

    move-result v0

    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->required:Z

    .line 9316
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->paramLabel()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p2}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-interface {p2}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getTypeInfo()Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    move-result-object p2

    invoke-static {v0, v1, p2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->inferLabel(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->paramLabel:Ljava/lang/String;

    .line 9318
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->hideParamSyntax()Z

    move-result p2

    iput-boolean p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hideParamSyntax:Z

    .line 9319
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->interactive()Z

    move-result p2

    iput-boolean p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->interactive:Z

    .line 9320
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->echo()Z

    move-result p2

    iput-boolean p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->echo:Z

    .line 9321
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->prompt()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->prompt:Ljava/lang/String;

    .line 9322
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->description()[Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->description:[Ljava/lang/String;

    .line 9323
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->descriptionKey()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->descriptionKey:Ljava/lang/String;

    .line 9324
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->split()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegex:Ljava/lang/String;

    .line 9325
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->splitSynopsisLabel()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegexSynopsisLabel:Ljava/lang/String;

    .line 9326
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->hidden()Z

    move-result p2

    iput-boolean p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hidden:Z

    .line 9327
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->defaultValue()Ljava/lang/String;

    move-result-object p2

    const-string v0, "_NULL_"

    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    const/4 v1, 0x0

    if-eqz p2, :cond_0

    move-object p2, v1

    goto :goto_0

    :cond_0
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->defaultValue()Ljava/lang/String;

    move-result-object p2

    :goto_0
    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->defaultValue:Ljava/lang/String;

    .line 9328
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->mapFallbackValue()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_1

    goto :goto_1

    :cond_1
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->mapFallbackValue()Ljava/lang/String;

    move-result-object v1

    :goto_1
    iput-object v1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->mapFallbackValue:Ljava/lang/String;

    .line 9329
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->defaultValue()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalDefaultValue:Ljava/lang/String;

    .line 9330
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->mapFallbackValue()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalMapFallbackValue:Ljava/lang/String;

    .line 9331
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->showDefaultValue()Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->showDefaultValue:Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    .line 9332
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->scope()Lgroovyjarjarpicocli/CommandLine$ScopeType;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    const/4 p2, 0x0

    .line 9333
    iput-boolean p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->inherited:Z

    if-eqz p3, :cond_4

    .line 9335
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->converter()[Ljava/lang/Class;

    move-result-object p2

    invoke-static {p3, p2}, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;->access$10700(Lgroovyjarjarpicocli/CommandLine$IFactory;[Ljava/lang/Class;)[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->converters:[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    .line 9336
    const-class p2, Lgroovyjarjarpicocli/CommandLine$NoCompletionCandidates;

    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->completionCandidates()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_2

    .line 9337
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->completionCandidates()Ljava/lang/Class;

    move-result-object p2

    invoke-static {p3, p2}, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;->createCompletionCandidates(Lgroovyjarjarpicocli/CommandLine$IFactory;Ljava/lang/Class;)Ljava/lang/Iterable;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->completionCandidates:Ljava/lang/Iterable;

    .line 9339
    :cond_2
    const-class p2, Lgroovyjarjarpicocli/CommandLine$NullParameterConsumer;

    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->parameterConsumer()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_3

    .line 9340
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->parameterConsumer()Ljava/lang/Class;

    move-result-object p2

    invoke-static {p3, p2}, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;->createParameterConsumer(Lgroovyjarjarpicocli/CommandLine$IFactory;Ljava/lang/Class;)Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->parameterConsumer:Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

    .line 9342
    :cond_3
    const-class p2, Lgroovyjarjarpicocli/CommandLine$NoOpParameterPreprocessor;

    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->preprocessor()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_4

    .line 9343
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Option;->preprocessor()Ljava/lang/Class;

    move-result-object p1

    invoke-static {p3, p1}, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;->create(Lgroovyjarjarpicocli/CommandLine$IFactory;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->preprocessor:Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

    :cond_4
    return-void
.end method

.method constructor <init>(Lgroovyjarjarpicocli/CommandLine$Parameters;Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;Lgroovyjarjarpicocli/CommandLine$IFactory;)V
    .locals 4

    .line 9348
    invoke-direct {p0, p2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;-><init>(Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;)V

    .line 9349
    invoke-static {p2}, Lgroovyjarjarpicocli/CommandLine$Range;->access$10800(Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;)Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->arity:Lgroovyjarjarpicocli/CommandLine$Range;

    .line 9350
    iget v0, v0, Lgroovyjarjarpicocli/CommandLine$Range;->min:I

    const/4 v1, 0x0

    if-lez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    move v0, v1

    :goto_0
    iput-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->required:Z

    const/4 v0, 0x0

    if-nez p1, :cond_1

    .line 9354
    invoke-interface {p2}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-interface {p2}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getTypeInfo()Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    move-result-object p2

    invoke-static {v0, p1, p2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->inferLabel(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->paramLabel:Ljava/lang/String;

    goto/16 :goto_3

    .line 9356
    :cond_1
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->paramLabel()Ljava/lang/String;

    move-result-object v2

    invoke-interface {p2}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p2}, Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;->getTypeInfo()Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    move-result-object p2

    invoke-static {v2, v3, p2}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->inferLabel(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->paramLabel:Ljava/lang/String;

    .line 9358
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->hideParamSyntax()Z

    move-result p2

    iput-boolean p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hideParamSyntax:Z

    .line 9359
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->interactive()Z

    move-result p2

    iput-boolean p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->interactive:Z

    .line 9360
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->echo()Z

    move-result p2

    iput-boolean p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->echo:Z

    .line 9361
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->prompt()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->prompt:Ljava/lang/String;

    .line 9362
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->description()[Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->description:[Ljava/lang/String;

    .line 9363
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->descriptionKey()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->descriptionKey:Ljava/lang/String;

    .line 9364
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->split()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegex:Ljava/lang/String;

    .line 9365
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->splitSynopsisLabel()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegexSynopsisLabel:Ljava/lang/String;

    .line 9366
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->hidden()Z

    move-result p2

    iput-boolean p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hidden:Z

    .line 9367
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->defaultValue()Ljava/lang/String;

    move-result-object p2

    const-string v2, "_NULL_"

    invoke-virtual {v2, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_2

    move-object p2, v0

    goto :goto_1

    :cond_2
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->defaultValue()Ljava/lang/String;

    move-result-object p2

    :goto_1
    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->defaultValue:Ljava/lang/String;

    .line 9368
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->mapFallbackValue()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v2, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_3

    goto :goto_2

    :cond_3
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->mapFallbackValue()Ljava/lang/String;

    move-result-object v0

    :goto_2
    iput-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->mapFallbackValue:Ljava/lang/String;

    .line 9369
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->defaultValue()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalDefaultValue:Ljava/lang/String;

    .line 9370
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->mapFallbackValue()Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalMapFallbackValue:Ljava/lang/String;

    .line 9371
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->showDefaultValue()Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->showDefaultValue:Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    .line 9372
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->scope()Lgroovyjarjarpicocli/CommandLine$ScopeType;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    .line 9373
    iput-boolean v1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->inherited:Z

    if-eqz p3, :cond_6

    .line 9375
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->converter()[Ljava/lang/Class;

    move-result-object p2

    invoke-static {p3, p2}, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;->access$10700(Lgroovyjarjarpicocli/CommandLine$IFactory;[Ljava/lang/Class;)[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->converters:[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    .line 9376
    const-class p2, Lgroovyjarjarpicocli/CommandLine$NoCompletionCandidates;

    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->completionCandidates()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_4

    .line 9377
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->completionCandidates()Ljava/lang/Class;

    move-result-object p2

    invoke-static {p3, p2}, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;->createCompletionCandidates(Lgroovyjarjarpicocli/CommandLine$IFactory;Ljava/lang/Class;)Ljava/lang/Iterable;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->completionCandidates:Ljava/lang/Iterable;

    .line 9379
    :cond_4
    const-class p2, Lgroovyjarjarpicocli/CommandLine$NullParameterConsumer;

    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->parameterConsumer()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_5

    .line 9380
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->parameterConsumer()Ljava/lang/Class;

    move-result-object p2

    invoke-static {p3, p2}, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;->createParameterConsumer(Lgroovyjarjarpicocli/CommandLine$IFactory;Ljava/lang/Class;)Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->parameterConsumer:Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

    .line 9382
    :cond_5
    const-class p2, Lgroovyjarjarpicocli/CommandLine$NoOpParameterPreprocessor;

    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->preprocessor()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_6

    .line 9383
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Parameters;->preprocessor()Ljava/lang/Class;

    move-result-object p1

    invoke-static {p3, p1}, Lgroovyjarjarpicocli/CommandLine$DefaultFactory;->create(Lgroovyjarjarpicocli/CommandLine$IFactory;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->preprocessor:Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

    :cond_6
    :goto_3
    return-void
.end method

.method static synthetic access$3700(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/Object;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->userObject:Ljava/lang/Object;

    return-object p0
.end method

.method static synthetic access$3800(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)[Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->description:[Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$3900(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->descriptionKey:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$4000(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegex:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$4100(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegexSynopsisLabel:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$4200(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->paramLabel:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$4300(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Z
    .locals 0

    .line 9224
    iget-boolean p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hideParamSyntax:Z

    return p0
.end method

.method static synthetic access$4400(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->converters:[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    return-object p0
.end method

.method static synthetic access$4500(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->parameterConsumer:Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

    return-object p0
.end method

.method static synthetic access$4600(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->preprocessor:Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

    return-object p0
.end method

.method static synthetic access$4700(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Help$Visibility;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->showDefaultValue:Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    return-object p0
.end method

.method static synthetic access$4800(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Z
    .locals 0

    .line 9224
    iget-boolean p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hidden:Z

    return p0
.end method

.method static synthetic access$4900(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Z
    .locals 0

    .line 9224
    iget-boolean p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->inherited:Z

    return p0
.end method

.method static synthetic access$5000(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->root:Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    return-object p0
.end method

.method static synthetic access$5100(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$ScopeType;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    return-object p0
.end method

.method static synthetic access$5200(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Z
    .locals 0

    .line 9224
    iget-boolean p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->interactive:Z

    return p0
.end method

.method static synthetic access$5300(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Z
    .locals 0

    .line 9224
    iget-boolean p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->echo:Z

    return p0
.end method

.method static synthetic access$5400(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->prompt:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$5500(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/Object;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValue:Ljava/lang/Object;

    return-object p0
.end method

.method static synthetic access$5600(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Z
    .locals 0

    .line 9224
    iget-boolean p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hasInitialValue:Z

    return p0
.end method

.method static synthetic access$5700(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValueState:Lgroovyjarjarpicocli/CommandLine$Model$InitialValueState;

    return-object p0
.end method

.method static synthetic access$5800(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->annotatedElement:Lgroovyjarjarpicocli/CommandLine$Model$IAnnotatedElement;

    return-object p0
.end method

.method static synthetic access$5900(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->defaultValue:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$6000(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Z
    .locals 0

    .line 9224
    iget-boolean p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->required:Z

    return p0
.end method

.method static synthetic access$6100(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->toString:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$6200(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$IGetter;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    return-object p0
.end method

.method static synthetic access$6300(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$ISetter;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    return-object p0
.end method

.method static synthetic access$6400(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$IScope;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    return-object p0
.end method

.method static synthetic access$6500(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->mapFallbackValue:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$6600(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalDefaultValue:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$6700(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/String;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->originalMapFallbackValue:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$6800(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Range;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->arity:Lgroovyjarjarpicocli/CommandLine$Range;

    return-object p0
.end method

.method static synthetic access$6900(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/Class;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->type:Ljava/lang/Class;

    return-object p0
.end method

.method static synthetic access$7000(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    return-object p0
.end method

.method static synthetic access$7100(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)[Ljava/lang/Class;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->auxiliaryTypes:[Ljava/lang/Class;

    return-object p0
.end method

.method static synthetic access$7200(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;)Ljava/lang/Iterable;
    .locals 0

    .line 9224
    iget-object p0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->completionCandidates:Ljava/lang/Iterable;

    return-object p0
.end method

.method private static inferLabel(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)Ljava/lang/String;
    .locals 1

    .line 9389
    invoke-static {p0}, Lgroovyjarjarpicocli/CommandLine;->access$2200(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 9391
    :cond_0
    invoke-interface {p2}, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;->isMap()Z

    move-result p0

    if-eqz p0, :cond_3

    .line 9392
    invoke-interface {p2}, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;->getAuxiliaryTypeInfos()Ljava/util/List;

    move-result-object p0

    .line 9393
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result p1

    const/4 p2, 0x2

    if-lt p1, p2, :cond_2

    const/4 p1, 0x0

    invoke-interface {p0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    if-eqz p2, :cond_2

    const/4 p2, 0x1

    invoke-interface {p0, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_0

    .line 9395
    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-interface {p0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;->getClassSimpleName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "="

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-interface {p0, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    invoke-interface {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;->getClassSimpleName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto :goto_1

    :cond_2
    :goto_0
    const-string p1, "String=String"

    .line 9397
    :cond_3
    :goto_1
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "<"

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p1, ">"

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private setTypeInfo(Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)V
    .locals 0

    .line 9634
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    if-eqz p1, :cond_0

    .line 9636
    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;->getType()Ljava/lang/Class;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->type:Ljava/lang/Class;

    .line 9637
    iget-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    invoke-interface {p1}, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;->getAuxiliaryTypes()[Ljava/lang/Class;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->auxiliaryTypes:[Ljava/lang/Class;

    :cond_0
    return-void
.end method


# virtual methods
.method public arity(Lgroovyjarjarpicocli/CommandLine$Range;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$Range;",
            ")TT;"
        }
    .end annotation

    const-string v0, "arity"

    .line 9571
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Range;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->arity:Lgroovyjarjarpicocli/CommandLine$Range;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public arity(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    .line 9568
    invoke-static {p1}, Lgroovyjarjarpicocli/CommandLine$Range;->valueOf(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Range;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->arity(Lgroovyjarjarpicocli/CommandLine$Range;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public arity()Lgroovyjarjarpicocli/CommandLine$Range;
    .locals 1

    .line 9431
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->arity:Lgroovyjarjarpicocli/CommandLine$Range;

    return-object v0
.end method

.method public varargs auxiliaryTypes([Ljava/lang/Class;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/Class<",
            "*>;)TT;"
        }
    .end annotation

    const-string v0, "types"

    .line 9585
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Class;

    invoke-virtual {p1}, [Ljava/lang/Class;->clone()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Class;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->auxiliaryTypes:[Ljava/lang/Class;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public auxiliaryTypes()[Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()[",
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation

    .line 9447
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->auxiliaryTypes:[Ljava/lang/Class;

    return-object v0
.end method

.method public abstract build()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;
.end method

.method public completionCandidates(Ljava/lang/Iterable;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Iterable<",
            "Ljava/lang/String;",
            ">;)TT;"
        }
    .end annotation

    .line 9600
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->completionCandidates:Ljava/lang/Iterable;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public completionCandidates()Ljava/lang/Iterable;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Iterable<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 9521
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->completionCandidates:Ljava/lang/Iterable;

    return-object v0
.end method

.method public varargs converters([Lgroovyjarjarpicocli/CommandLine$ITypeConverter;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Lgroovyjarjarpicocli/CommandLine$ITypeConverter<",
            "*>;)TT;"
        }
    .end annotation

    const-string v0, "type converters"

    .line 9588
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    invoke-virtual {p1}, [Lgroovyjarjarpicocli/CommandLine$ITypeConverter;->clone()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->converters:[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public converters()[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()[",
            "Lgroovyjarjarpicocli/CommandLine$ITypeConverter<",
            "*>;"
        }
    .end annotation

    .line 9453
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->converters:[Lgroovyjarjarpicocli/CommandLine$ITypeConverter;

    return-object v0
.end method

.method public defaultValue(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    .line 9658
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->defaultValue:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public defaultValue()Ljava/lang/String;
    .locals 1

    .line 9507
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->defaultValue:Ljava/lang/String;

    return-object v0
.end method

.method public varargs description([Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    const-string v0, "description"

    .line 9559
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/String;

    invoke-virtual {p1}, [Ljava/lang/String;->clone()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->description:[Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public description()[Ljava/lang/String;
    .locals 1

    .line 9421
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->description:[Ljava/lang/String;

    return-object v0
.end method

.method public descriptionKey(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    .line 9565
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->descriptionKey:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public descriptionKey()Ljava/lang/String;
    .locals 1

    .line 9427
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->descriptionKey:Ljava/lang/String;

    return-object v0
.end method

.method public echo(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)TT;"
        }
    .end annotation

    .line 9552
    iput-boolean p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->echo:Z

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public echo()Z
    .locals 1

    .line 9412
    iget-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->echo:Z

    return v0
.end method

.method public getter(Lgroovyjarjarpicocli/CommandLine$Model$IGetter;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$Model$IGetter;",
            ")TT;"
        }
    .end annotation

    .line 9672
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public getter()Lgroovyjarjarpicocli/CommandLine$Model$IGetter;
    .locals 1

    .line 9533
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->getter:Lgroovyjarjarpicocli/CommandLine$Model$IGetter;

    return-object v0
.end method

.method public hasInitialValue(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)TT;"
        }
    .end annotation

    .line 9669
    iput-boolean p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hasInitialValue:Z

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public hasInitialValue()Z
    .locals 1

    .line 9514
    iget-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hasInitialValue:Z

    return v0
.end method

.method public hidden(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)TT;"
        }
    .end annotation

    .line 9611
    iput-boolean p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hidden:Z

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public hidden()Z
    .locals 1

    .line 9466
    iget-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hidden:Z

    return v0
.end method

.method public hideParamSyntax(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)TT;"
        }
    .end annotation

    .line 9580
    iput-boolean p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hideParamSyntax:Z

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public hideParamSyntax()Z
    .locals 1

    .line 9442
    iget-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->hideParamSyntax:Z

    return v0
.end method

.method public inherited(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)TT;"
        }
    .end annotation

    .line 9615
    iput-boolean p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->inherited:Z

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public inherited()Z
    .locals 1

    .line 9471
    iget-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->inherited:Z

    return v0
.end method

.method public initialValue(Ljava/lang/Object;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TT;"
        }
    .end annotation

    .line 9665
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValue:Ljava/lang/Object;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public initialValue()Ljava/lang/Object;
    .locals 1

    .line 9511
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->initialValue:Ljava/lang/Object;

    return-object v0
.end method

.method public interactive(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)TT;"
        }
    .end annotation

    .line 9549
    iput-boolean p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->interactive:Z

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public interactive()Z
    .locals 1

    .line 9407
    iget-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->interactive:Z

    return v0
.end method

.method public mapFallbackValue(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0

    .line 9653
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->mapFallbackValue:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public mapFallbackValue()Ljava/lang/String;
    .locals 1

    .line 9503
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->mapFallbackValue:Ljava/lang/String;

    return-object v0
.end method

.method public paramLabel(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    const-string v0, "paramLabel"

    .line 9574
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->paramLabel:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public paramLabel()Ljava/lang/String;
    .locals 1

    .line 9436
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->paramLabel:Ljava/lang/String;

    return-object v0
.end method

.method public parameterConsumer()Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;
    .locals 1

    .line 9525
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->parameterConsumer:Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

    return-object v0
.end method

.method public parameterConsumer(Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;",
            ")TT;"
        }
    .end annotation

    .line 9604
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->parameterConsumer:Lgroovyjarjarpicocli/CommandLine$IParameterConsumer;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public preprocessor()Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;
    .locals 1

    .line 9530
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->preprocessor:Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

    return-object v0
.end method

.method public preprocessor(Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;",
            ")TT;"
        }
    .end annotation

    .line 9608
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->preprocessor:Lgroovyjarjarpicocli/CommandLine$IParameterPreprocessor;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public prompt(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    .line 9555
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->prompt:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public prompt()Ljava/lang/String;
    .locals 1

    .line 9417
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->prompt:Ljava/lang/String;

    return-object v0
.end method

.method public required(Z)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)TT;"
        }
    .end annotation

    .line 9546
    iput-boolean p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->required:Z

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public required()Z
    .locals 1

    .line 9404
    iget-boolean v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->required:Z

    return v0
.end method

.method public root(Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;",
            ")TT;"
        }
    .end annotation

    .line 9620
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->root:Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public root()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;
    .locals 1

    .line 9477
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->root:Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec;

    return-object v0
.end method

.method public scope(Lgroovyjarjarpicocli/CommandLine$Model$IScope;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$Model$IScope;",
            ")TT;"
        }
    .end annotation

    .line 9676
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public scope()Lgroovyjarjarpicocli/CommandLine$Model$IScope;
    .locals 1

    .line 9537
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scope:Lgroovyjarjarpicocli/CommandLine$Model$IScope;

    return-object v0
.end method

.method public scopeType(Lgroovyjarjarpicocli/CommandLine$ScopeType;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$ScopeType;",
            ")TT;"
        }
    .end annotation

    .line 9679
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public scopeType()Lgroovyjarjarpicocli/CommandLine$ScopeType;
    .locals 1

    .line 9541
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->scopeType:Lgroovyjarjarpicocli/CommandLine$ScopeType;

    return-object v0
.end method

.method protected abstract self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation
.end method

.method public setter(Lgroovyjarjarpicocli/CommandLine$Model$ISetter;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$Model$ISetter;",
            ")TT;"
        }
    .end annotation

    .line 9674
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public setter()Lgroovyjarjarpicocli/CommandLine$Model$ISetter;
    .locals 1

    .line 9535
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setter:Lgroovyjarjarpicocli/CommandLine$Model$ISetter;

    return-object v0
.end method

.method public showDefaultValue()Lgroovyjarjarpicocli/CommandLine$Help$Visibility;
    .locals 1

    .line 9517
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->showDefaultValue:Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    return-object v0
.end method

.method public showDefaultValue(Lgroovyjarjarpicocli/CommandLine$Help$Visibility;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$Help$Visibility;",
            ")TT;"
        }
    .end annotation

    const-string v0, "visibility"

    .line 9596
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->showDefaultValue:Lgroovyjarjarpicocli/CommandLine$Help$Visibility;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public splitRegex(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    const-string v0, "splitRegex"

    .line 9591
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegex:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public splitRegex()Ljava/lang/String;
    .locals 1

    .line 9457
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegex:Ljava/lang/String;

    return-object v0
.end method

.method public splitRegexSynopsisLabel(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    const-string v0, "splitRegexSynopsisLabel"

    .line 9594
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegexSynopsisLabel:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public splitRegexSynopsisLabel()Ljava/lang/String;
    .locals 1

    .line 9463
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->splitRegexSynopsisLabel:Ljava/lang/String;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 9543
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->toString:Ljava/lang/String;

    return-object v0
.end method

.method public type(Ljava/lang/Class;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)TT;"
        }
    .end annotation

    const-string v0, "type"

    .line 9624
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Class;

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->type:Ljava/lang/Class;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public type()Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation

    .line 9482
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->type:Ljava/lang/Class;

    return-object v0
.end method

.method public typeInfo(Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;",
            ")TT;"
        }
    .end annotation

    const-string v0, "typeInfo"

    .line 9630
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    invoke-direct {p0, p1}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->setTypeInfo(Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;)V

    .line 9631
    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public typeInfo()Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;
    .locals 1

    .line 9488
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->typeInfo:Lgroovyjarjarpicocli/CommandLine$Model$ITypeInfo;

    return-object v0
.end method

.method public userObject(Ljava/lang/Object;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TT;"
        }
    .end annotation

    const-string v0, "userObject"

    .line 9644
    invoke-static {p1, v0}, Lgroovyjarjarpicocli/CommandLine$Assert;->notNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->userObject:Ljava/lang/Object;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method

.method public userObject()Ljava/lang/Object;
    .locals 1

    .line 9493
    iget-object v0, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->userObject:Ljava/lang/Object;

    return-object v0
.end method

.method public withToString(Ljava/lang/String;)Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")TT;"
        }
    .end annotation

    .line 9682
    iput-object p1, p0, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->toString:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;->self()Lgroovyjarjarpicocli/CommandLine$Model$ArgSpec$Builder;

    move-result-object p1

    return-object p1
.end method
