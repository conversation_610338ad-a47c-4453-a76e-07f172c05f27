.class Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;
.super Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;
.source "Tool.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/Tool;->checkForRuleIssues(Lgroovyjarjarantlr4/v4/tool/Grammar;)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = "1UndefChecker"
.end annotation


# instance fields
.field public badref:Z

.field final synthetic this$0:Lgroovyjarjarantlr4/v4/Tool;

.field final synthetic val$g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field final synthetic val$ruleToAST:Ljava/util/Map;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 451
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->this$0:Lgroovyjarjarantlr4/v4/Tool;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->val$g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iput-object p3, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->val$ruleToAST:Ljava/util/Map;

    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;-><init>()V

    const/4 p1, 0x0

    .line 452
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->badref:Z

    return-void
.end method


# virtual methods
.method public getErrorManager()Lgroovyjarjarantlr4/v4/tool/ErrorManager;
    .locals 1

    .line 481
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->this$0:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    return-object v0
.end method

.method public ruleRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 6

    .line 465
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->val$ruleToAST:Ljava/util/Map;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p2, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 466
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/CharStream;->getSourceName()Ljava/lang/String;

    move-result-object v0

    .line 467
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->currentRuleName:Ljava/lang/String;

    const/4 v2, 0x0

    invoke-virtual {v1, v2}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-static {v1}, Ljava/lang/Character;->isUpperCase(C)Z

    move-result v1

    const/4 v3, 0x1

    if-eqz v1, :cond_0

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1, v2}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-static {v1}, Ljava/lang/Character;->isLowerCase(C)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 470
    iput-boolean v3, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->badref:Z

    .line 471
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->this$0:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->PARSER_RULE_REF_IN_LEXER_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v4

    const/4 v5, 0x2

    new-array v5, v5, [Ljava/lang/Object;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p1

    aput-object p1, v5, v2

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->currentRuleName:Ljava/lang/String;

    aput-object p1, v5, v3

    invoke-virtual {p2, v1, v0, v4, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    if-nez p2, :cond_1

    .line 475
    iput-boolean v3, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->badref:Z

    .line 476
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->this$0:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNDEFINED_RULE_REF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v4, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p1

    aput-object p1, v3, v2

    invoke-virtual {p2, v1, v0, v4, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public tokenRef(Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;)V
    .locals 2

    .line 455
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->getText()Ljava/lang/String;

    move-result-object v0

    const-string v1, "EOF"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 460
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->val$g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->ruleRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    :cond_1
    return-void
.end method
