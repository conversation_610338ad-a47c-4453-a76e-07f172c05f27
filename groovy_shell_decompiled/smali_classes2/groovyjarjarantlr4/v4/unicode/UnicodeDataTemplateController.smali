.class public abstract Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;
.super Ljava/lang/Object;
.source "UnicodeDataTemplateController.java"


# static fields
.field static final synthetic $assertionsDisabled:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 29
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static addEmojiPresentationPropertyCodesToCodePointRanges(Ljava/util/Map;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ">;)V"
        }
    .end annotation

    .line 353
    new-instance v0, Lcom/ibm/icu/text/UnicodeSet;

    const-string v1, "[[\\p{Emoji=Yes}]&[\\p{Emoji_Presentation=Yes}]]"

    invoke-direct {v0, v1}, Lcom/ibm/icu/text/UnicodeSet;-><init>(Ljava/lang/String;)V

    .line 354
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v2, 0x0

    new-array v3, v2, [I

    invoke-direct {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 355
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeSetToIntervalSet(Lcom/ibm/icu/text/UnicodeSet;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    const-string v0, "EmojiPresentation=EmojiDefault"

    .line 356
    invoke-interface {p0, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 358
    new-instance v0, Lcom/ibm/icu/text/UnicodeSet;

    const-string v1, "[[\\p{Emoji=Yes}]&[\\p{Emoji_Presentation=No}]]"

    invoke-direct {v0, v1}, Lcom/ibm/icu/text/UnicodeSet;-><init>(Ljava/lang/String;)V

    .line 359
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v3, v2, [I

    invoke-direct {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 360
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeSetToIntervalSet(Lcom/ibm/icu/text/UnicodeSet;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    const-string v0, "EmojiPresentation=TextDefault"

    .line 361
    invoke-interface {p0, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 363
    new-instance v0, Lcom/ibm/icu/text/UnicodeSet;

    const-string v1, "[\\p{Emoji=No}]"

    invoke-direct {v0, v1}, Lcom/ibm/icu/text/UnicodeSet;-><init>(Ljava/lang/String;)V

    .line 364
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v2, v2, [I

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 365
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeSetToIntervalSet(Lcom/ibm/icu/text/UnicodeSet;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    const-string v0, "EmojiPresentation=Text"

    .line 366
    invoke-interface {p0, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private static addIntPropertyAliases(ILjava/lang/String;Ljava/util/Map;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 370
    invoke-static {p0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->getShortPropertyName(I)Ljava/lang/String;

    move-result-object v0

    .line 371
    invoke-static {p0}, Lcom/ibm/icu/lang/UCharacter;->getIntPropertyMinValue(I)I

    move-result v1

    .line 372
    :goto_0
    invoke-static {p0}, Lcom/ibm/icu/lang/UCharacter;->getIntPropertyMaxValue(I)I

    move-result v2

    if-gt v1, v2, :cond_0

    .line 374
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const/4 v3, 0x0

    invoke-static {p0, v1, v3}, Lcom/ibm/icu/lang/UCharacter;->getPropertyValueName(III)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 379
    :goto_1
    :try_start_0
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-static {p0, v1, v3}, Lcom/ibm/icu/lang/UCharacter;->getPropertyValueName(III)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_0

    .line 385
    invoke-static {p2, v4, v2}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :catch_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method private static addIntPropertyRanges(ILjava/lang/String;Ljava/util/Map;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ">;)V"
        }
    .end annotation

    .line 178
    invoke-static {p0}, Lcom/ibm/icu/lang/UCharacter;->getIntPropertyMinValue(I)I

    move-result v0

    .line 179
    :goto_0
    invoke-static {p0}, Lcom/ibm/icu/lang/UCharacter;->getIntPropertyMaxValue(I)I

    move-result v1

    if-gt v0, v1, :cond_1

    .line 181
    new-instance v1, Lcom/ibm/icu/text/UnicodeSet;

    invoke-direct {v1}, Lcom/ibm/icu/text/UnicodeSet;-><init>()V

    .line 182
    invoke-virtual {v1, p0, v0}, Lcom/ibm/icu/text/UnicodeSet;->applyIntPropertyValue(II)Lcom/ibm/icu/text/UnicodeSet;

    .line 183
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const/4 v3, 0x0

    invoke-static {p0, v0, v3}, Lcom/ibm/icu/lang/UCharacter;->getPropertyValueName(III)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 184
    invoke-interface {p2, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    if-nez v4, :cond_0

    .line 186
    new-instance v4, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v3, v3, [I

    invoke-direct {v4, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 187
    invoke-interface {p2, v2, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 189
    :cond_0
    invoke-static {v1, v4}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeSetToIntervalSet(Lcom/ibm/icu/text/UnicodeSet;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method private static addIntervalForCategory(Ljava/util/Map;Ljava/lang/String;II)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ">;",
            "Ljava/lang/String;",
            "II)V"
        }
    .end annotation

    .line 35
    invoke-interface {p0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    if-nez v0, :cond_0

    .line 37
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v1, 0x0

    new-array v1, v1, [I

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 38
    invoke-interface {p0, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 40
    :cond_0
    invoke-virtual {v0, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    return-void
.end method

.method private static addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 66
    invoke-interface {p0, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private static addPropertyAliases(Ljava/util/Map;Ljava/lang/String;I)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            "I)V"
        }
    .end annotation

    const/4 v0, 0x1

    .line 51
    :goto_0
    :try_start_0
    invoke-static {p2, v0}, Lcom/ibm/icu/lang/UCharacter;->getPropertyName(II)Ljava/lang/String;

    move-result-object v1
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_0

    .line 57
    invoke-static {p0, v1, p1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :catch_0
    return-void
.end method

.method private static addTR35ExtendedPictographicPropertyCodesToCodePointRanges(Ljava/util/Map;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ">;)V"
        }
    .end annotation

    .line 210
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v1, 0x0

    new-array v2, v1, [I

    invoke-direct {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    const v2, 0x1f774

    const v3, 0x1f77f

    .line 212
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2700

    const/16 v3, 0x2701

    .line 213
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2703

    const/16 v3, 0x2704

    .line 214
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x270e

    .line 215
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x2710

    const/16 v3, 0x2711

    .line 216
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2765

    const/16 v3, 0x2767

    .line 217
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f030

    const v3, 0x1f093

    .line 218
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f094

    const v3, 0x1f09f

    .line 219
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f10d

    const v3, 0x1f10f

    .line 220
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f12f

    .line 221
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f16c

    const v3, 0x1f16f

    .line 222
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f1ad

    const v3, 0x1f1e5

    .line 223
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f260

    const v3, 0x1f265

    .line 224
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f203

    const v3, 0x1f20f

    .line 225
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f23c

    const v3, 0x1f23f

    .line 226
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f249

    const v3, 0x1f24f

    .line 227
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f252

    const v3, 0x1f25f

    .line 228
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f266

    const v3, 0x1f2ff

    .line 229
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f7d5

    const v3, 0x1f7ff

    .line 230
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f000

    const v3, 0x1f003

    .line 231
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f005

    const v3, 0x1f02b

    .line 232
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f02c

    const v3, 0x1f02f

    .line 233
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f322

    const v3, 0x1f323

    .line 234
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f394

    const v3, 0x1f395

    .line 235
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f398

    .line 236
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f39c

    const v3, 0x1f39d

    .line 237
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f3f1

    const v3, 0x1f3f2

    .line 238
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f3f6

    .line 239
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f4fe

    .line 240
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f53e

    const v3, 0x1f548

    .line 241
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f54f

    .line 242
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f568

    const v3, 0x1f56e

    .line 243
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f571

    const v3, 0x1f572

    .line 244
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f57b

    const v3, 0x1f586

    .line 245
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f588

    const v3, 0x1f589

    .line 246
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f58e

    const v3, 0x1f58f

    .line 247
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f591

    const v3, 0x1f594

    .line 248
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f597

    const v3, 0x1f5a3

    .line 249
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5a6

    const v3, 0x1f5a7

    .line 250
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5a9

    const v3, 0x1f5b0

    .line 251
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5b3

    const v3, 0x1f5bb

    .line 252
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5bd

    const v3, 0x1f5c1    # 1.79995E-40f

    .line 253
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5c5    # 1.80001E-40f

    const v3, 0x1f5d0

    .line 254
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5d4

    const v3, 0x1f5db

    .line 255
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5df

    const v3, 0x1f5e0

    .line 256
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5e2

    .line 257
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f5e4

    const v3, 0x1f5e7

    .line 258
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5e9

    const v3, 0x1f5ee

    .line 259
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5f0

    const v3, 0x1f5f2

    .line 260
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f5f4

    const v3, 0x1f5f9

    .line 261
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2605

    .line 262
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x2607

    const/16 v3, 0x260d

    .line 263
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x260f

    const/16 v3, 0x2610

    .line 264
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2612

    .line 265
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x2616

    const/16 v3, 0x2617

    .line 266
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2619

    const/16 v3, 0x261c

    .line 267
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x261e

    const/16 v3, 0x261f

    .line 268
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2621

    .line 269
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x2624

    const/16 v3, 0x2625

    .line 270
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2627

    const/16 v3, 0x2629

    .line 271
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x262b

    const/16 v3, 0x262d

    .line 272
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2630

    const/16 v3, 0x2637

    .line 273
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x263b

    const/16 v3, 0x2647

    .line 274
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2654

    const/16 v3, 0x265f

    .line 275
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2661

    const/16 v3, 0x2662

    .line 276
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2664

    .line 277
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x2667

    .line 278
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x2669

    const/16 v3, 0x267a

    .line 279
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x267c

    const/16 v3, 0x267e

    .line 280
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2680

    const/16 v3, 0x2691

    .line 281
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2695

    .line 282
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x2698

    .line 283
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x269a

    .line 284
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x269d

    const/16 v3, 0x269f

    .line 285
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26a2

    const/16 v3, 0x26a9

    .line 286
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26ac

    const/16 v3, 0x26af

    .line 287
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26b2

    const/16 v3, 0x26bc

    .line 288
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26bf

    const/16 v3, 0x26c3

    .line 289
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26c6

    const/16 v3, 0x26c7

    .line 290
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26c9

    const/16 v3, 0x26cd

    .line 291
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26d0

    .line 292
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x26d2

    .line 293
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x26d5

    const/16 v3, 0x26e8

    .line 294
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26eb

    const/16 v3, 0x26ef

    .line 295
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26f6

    .line 296
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v2, 0x26fb

    const/16 v3, 0x26fc

    .line 297
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x26fe

    const/16 v3, 0x26ff

    .line 298
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const/16 v2, 0x2388

    .line 299
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1fa00

    const v3, 0x1fffd

    .line 300
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f0a0

    const v3, 0x1f0ae

    .line 301
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f0b1

    const v3, 0x1f0bf

    .line 302
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f0c1

    const v3, 0x1f0cf

    .line 303
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f0d1

    const v3, 0x1f0f5

    .line 304
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f0af

    const v3, 0x1f0b0

    .line 305
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f0c0

    .line 306
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f0d0

    .line 307
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f0f6

    const v3, 0x1f0ff

    .line 308
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f80c

    const v3, 0x1f80f

    .line 309
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f848

    const v3, 0x1f84f

    .line 310
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f85a

    const v3, 0x1f85f

    .line 311
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f888

    const v3, 0x1f88f

    .line 312
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f8ae

    const v3, 0x1f8ff

    .line 313
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f900

    const v3, 0x1f90b

    .line 314
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f91f

    .line 315
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f928

    const v3, 0x1f92f

    .line 316
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f931

    const v3, 0x1f932

    .line 317
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f94c

    .line 318
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f95f

    const v3, 0x1f96b

    .line 319
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f992

    const v3, 0x1f997

    .line 320
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f9d0

    const v3, 0x1f9e6

    .line 321
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f90c

    const v3, 0x1f90f

    .line 322
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f93f

    .line 323
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f94d

    const v3, 0x1f94f

    .line 324
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f96c

    const v3, 0x1f97f

    .line 325
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f998

    const v3, 0x1f9bf

    .line 326
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f9c1

    const v3, 0x1f9cf

    .line 327
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f9e7

    const v3, 0x1f9ff

    .line 328
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f6c6

    const v3, 0x1f6ca

    .line 329
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f6d3

    const v3, 0x1f6d4

    .line 330
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f6e6

    const v3, 0x1f6e8

    .line 331
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f6ea

    .line 332
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const v2, 0x1f6f1

    const v3, 0x1f6f2

    .line 333
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f6f7

    const v3, 0x1f6f8

    .line 334
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f6d5

    const v3, 0x1f6df

    .line 335
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f6ed

    const v3, 0x1f6ef

    .line 336
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const v2, 0x1f6f9

    const v3, 0x1f6ff

    .line 337
    invoke-virtual {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    const-string v2, "Extended_Pictographic"

    .line 338
    invoke-interface {p0, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 340
    new-instance v0, Lcom/ibm/icu/text/UnicodeSet;

    const-string v2, "[\\p{GCB=Regional_Indicator}\\*#0-9\\u00a9\\u00ae\\u2122\\u3030\\u303d]"

    invoke-direct {v0, v2}, Lcom/ibm/icu/text/UnicodeSet;-><init>(Ljava/lang/String;)V

    .line 341
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v3, v1, [I

    invoke-direct {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 342
    invoke-static {v0, v2}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeSetToIntervalSet(Lcom/ibm/icu/text/UnicodeSet;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    const-string v3, "EmojiRK"

    .line 343
    invoke-interface {p0, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 345
    new-instance v2, Lcom/ibm/icu/text/UnicodeSet;

    const-string v3, "[\\p{Emoji=Yes}]"

    invoke-direct {v2, v3}, Lcom/ibm/icu/text/UnicodeSet;-><init>(Ljava/lang/String;)V

    .line 346
    invoke-virtual {v2, v0}, Lcom/ibm/icu/text/UnicodeSet;->removeAll(Lcom/ibm/icu/text/UnicodeSet;)Lcom/ibm/icu/text/UnicodeSet;

    .line 347
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v1, v1, [I

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 348
    invoke-static {v2, v0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeSetToIntervalSet(Lcom/ibm/icu/text/UnicodeSet;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    const-string v1, "EmojiNRK"

    .line 349
    invoke-interface {p0, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private static addUnicodeBinaryPropertyCodesToCodePointRanges(Ljava/util/Map;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x0

    move v1, v0

    :goto_0
    const/16 v2, 0x3d

    if-ge v1, v2, :cond_1

    .line 156
    invoke-static {v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->getShortPropertyName(I)Ljava/lang/String;

    move-result-object v2

    .line 157
    new-instance v3, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v4, v0, [I

    invoke-direct {v3, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 158
    new-instance v4, Lcom/ibm/icu/text/UnicodeSet;

    invoke-direct {v4}, Lcom/ibm/icu/text/UnicodeSet;-><init>()V

    const/4 v5, 0x1

    .line 159
    invoke-virtual {v4, v1, v5}, Lcom/ibm/icu/text/UnicodeSet;->applyIntPropertyValue(II)Lcom/ibm/icu/text/UnicodeSet;

    .line 160
    invoke-virtual {v4}, Lcom/ibm/icu/text/UnicodeSet;->ranges()Ljava/lang/Iterable;

    move-result-object v4

    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_1
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_0

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/ibm/icu/text/UnicodeSet$EntryRange;

    .line 161
    iget v6, v5, Lcom/ibm/icu/text/UnicodeSet$EntryRange;->codepoint:I

    iget v5, v5, Lcom/ibm/icu/text/UnicodeSet$EntryRange;->codepointEnd:I

    invoke-virtual {v3, v6, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    goto :goto_1

    .line 163
    :cond_0
    invoke-interface {p0, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method private static addUnicodeBinaryPropertyCodesToNames(Ljava/util/Map;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x0

    :goto_0
    const/16 v1, 0x3d

    if-ge v0, v1, :cond_0

    .line 172
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->getShortPropertyName(I)Ljava/lang/String;

    move-result-object v1

    .line 173
    invoke-static {p0, v1, v0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAliases(Ljava/util/Map;Ljava/lang/String;I)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method private static addUnicodeBlocksToNames(Ljava/util/Map;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const/16 v0, 0x1001

    const-string v1, "In"

    .line 396
    invoke-static {v0, v1, p0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addIntPropertyAliases(ILjava/lang/String;Ljava/util/Map;)V

    return-void
.end method

.method private static addUnicodeCategoryCodesToCodePointRanges(Ljava/util/Map;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ">;)V"
        }
    .end annotation

    .line 101
    invoke-static {}, Lcom/ibm/icu/lang/UCharacter;->getTypeIterator()Lcom/ibm/icu/util/RangeValueIterator;

    move-result-object v0

    .line 102
    new-instance v1, Lcom/ibm/icu/util/RangeValueIterator$Element;

    invoke-direct {v1}, Lcom/ibm/icu/util/RangeValueIterator$Element;-><init>()V

    .line 103
    :goto_0
    invoke-interface {v0, v1}, Lcom/ibm/icu/util/RangeValueIterator;->next(Lcom/ibm/icu/util/RangeValueIterator$Element;)Z

    move-result v2

    if-eqz v2, :cond_0

    const/16 v2, 0x2000

    .line 104
    iget v3, v1, Lcom/ibm/icu/util/RangeValueIterator$Element;->value:I

    const/4 v4, 0x1

    shl-int v3, v4, v3

    const/4 v5, 0x0

    invoke-static {v2, v3, v5}, Lcom/ibm/icu/lang/UCharacter;->getPropertyValueName(III)Ljava/lang/String;

    move-result-object v2

    .line 108
    iget v3, v1, Lcom/ibm/icu/util/RangeValueIterator$Element;->start:I

    iget v6, v1, Lcom/ibm/icu/util/RangeValueIterator$Element;->limit:I

    sub-int/2addr v6, v4

    invoke-static {p0, v2, v3, v6}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addIntervalForCategory(Ljava/util/Map;Ljava/lang/String;II)V

    .line 110
    invoke-virtual {v2, v5, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v2

    .line 111
    iget v3, v1, Lcom/ibm/icu/util/RangeValueIterator$Element;->start:I

    iget v5, v1, Lcom/ibm/icu/util/RangeValueIterator$Element;->limit:I

    sub-int/2addr v5, v4

    invoke-static {p0, v2, v3, v5}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addIntervalForCategory(Ljava/util/Map;Ljava/lang/String;II)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method private static addUnicodeCategoryCodesToNames(Ljava/util/Map;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 116
    invoke-static {}, Lcom/ibm/icu/lang/UCharacter;->getTypeIterator()Lcom/ibm/icu/util/RangeValueIterator;

    move-result-object v0

    .line 117
    new-instance v1, Lcom/ibm/icu/util/RangeValueIterator$Element;

    invoke-direct {v1}, Lcom/ibm/icu/util/RangeValueIterator$Element;-><init>()V

    .line 118
    :catch_0
    invoke-interface {v0, v1}, Lcom/ibm/icu/util/RangeValueIterator;->next(Lcom/ibm/icu/util/RangeValueIterator$Element;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 119
    iget v2, v1, Lcom/ibm/icu/util/RangeValueIterator$Element;->value:I

    const/4 v3, 0x1

    shl-int v2, v3, v2

    const/4 v4, 0x0

    const/16 v5, 0x2000

    .line 120
    invoke-static {v5, v2, v4}, Lcom/ibm/icu/lang/UCharacter;->getPropertyValueName(III)Ljava/lang/String;

    move-result-object v4

    .line 128
    :goto_0
    :try_start_0
    invoke-static {v5, v2, v3}, Lcom/ibm/icu/lang/UCharacter;->getPropertyValueName(III)Ljava/lang/String;

    move-result-object v6
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_0

    .line 137
    invoke-static {p0, v6, v4}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    const-string v0, "Control"

    const-string v1, "C"

    .line 142
    invoke-static {p0, v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    const-string v0, "Letter"

    const-string v1, "L"

    .line 143
    invoke-static {p0, v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    const-string v0, "Number"

    const-string v1, "N"

    .line 144
    invoke-static {p0, v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    const-string v0, "Mark"

    const-string v1, "M"

    .line 145
    invoke-static {p0, v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    const-string v0, "Punctuation"

    const-string v1, "P"

    .line 146
    invoke-static {p0, v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    const-string v0, "Symbol"

    const-string v1, "S"

    .line 147
    invoke-static {p0, v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    const-string v0, "Space"

    const-string v1, "Z"

    .line 148
    invoke-static {p0, v0, v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addPropertyAlias(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method private static addUnicodeIntPropertyCodesToCodePointRanges(Ljava/util/Map;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ">;)V"
        }
    .end annotation

    const/16 v0, 0x1000

    :goto_0
    const/16 v1, 0x1016

    if-ge v0, v1, :cond_0

    .line 204
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->getShortPropertyName(I)Ljava/lang/String;

    move-result-object v1

    .line 205
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1, p0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addIntPropertyRanges(ILjava/lang/String;Ljava/util/Map;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method private static addUnicodeIntPropertyCodesToNames(Ljava/util/Map;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const/16 v0, 0x1000

    :goto_0
    const/16 v1, 0x1016

    if-ge v0, v1, :cond_0

    const/4 v1, 0x1

    .line 408
    :goto_1
    :try_start_0
    invoke-static {v0, v1}, Lcom/ibm/icu/lang/UCharacter;->getPropertyName(II)Ljava/lang/String;

    move-result-object v2
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_0

    .line 413
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v0, v2, p0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addIntPropertyAliases(ILjava/lang/String;Ljava/util/Map;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :catch_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method private static addUnicodeScriptCodesToNames(Ljava/util/Map;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const/16 v0, 0x100a

    const-string v1, ""

    .line 392
    invoke-static {v0, v1, p0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addIntPropertyAliases(ILjava/lang/String;Ljava/util/Map;)V

    return-void
.end method

.method private static addUnicodeSetToIntervalSet(Lcom/ibm/icu/text/UnicodeSet;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V
    .locals 2

    .line 194
    invoke-virtual {p0}, Lcom/ibm/icu/text/UnicodeSet;->ranges()Ljava/lang/Iterable;

    move-result-object p0

    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/ibm/icu/text/UnicodeSet$EntryRange;

    .line 195
    iget v1, v0, Lcom/ibm/icu/text/UnicodeSet$EntryRange;->codepoint:I

    iget v0, v0, Lcom/ibm/icu/text/UnicodeSet$EntryRange;->codepointEnd:I

    invoke-virtual {p1, v1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public static getProperties()Ljava/util/Map;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    .line 70
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    .line 71
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeCategoryCodesToCodePointRanges(Ljava/util/Map;)V

    .line 72
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeBinaryPropertyCodesToCodePointRanges(Ljava/util/Map;)V

    .line 73
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeIntPropertyCodesToCodePointRanges(Ljava/util/Map;)V

    .line 74
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addTR35ExtendedPictographicPropertyCodesToCodePointRanges(Ljava/util/Map;)V

    .line 75
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addEmojiPresentationPropertyCodesToCodePointRanges(Ljava/util/Map;)V

    .line 77
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 78
    invoke-static {v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeCategoryCodesToNames(Ljava/util/Map;)V

    .line 79
    invoke-static {v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeBinaryPropertyCodesToNames(Ljava/util/Map;)V

    .line 80
    invoke-static {v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeScriptCodesToNames(Ljava/util/Map;)V

    .line 81
    invoke-static {v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeBlocksToNames(Ljava/util/Map;)V

    .line 82
    invoke-static {v1}, Lgroovyjarjarantlr4/v4/unicode/UnicodeDataTemplateController;->addUnicodeIntPropertyCodesToNames(Ljava/util/Map;)V

    const-string v2, "EP"

    const-string v3, "Extended_Pictographic"

    .line 83
    invoke-interface {v1, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 85
    new-instance v2, Ljava/util/LinkedHashMap;

    invoke-direct {v2}, Ljava/util/LinkedHashMap;-><init>()V

    const-string v3, "propertyCodePointRanges"

    .line 86
    invoke-interface {v2, v3, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "propertyAliases"

    .line 87
    invoke-interface {v2, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object v2
.end method

.method private static getShortPropertyName(I)Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 92
    invoke-static {p0, v0}, Lcom/ibm/icu/lang/UCharacter;->getPropertyName(II)Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    .line 95
    invoke-static {p0, v0}, Lcom/ibm/icu/lang/UCharacter;->getPropertyName(II)Ljava/lang/String;

    move-result-object v0

    :cond_0
    return-object v0
.end method
