.class public Lgroovyjarjarantlr4/v4/tool/DOTGenerator;
.super Ljava/lang/Object;
.source "DOTGenerator.java"


# static fields
.field public static final STRIP_NONREDUCED_STATES:Z = false

.field public static stlib:Lorg/stringtemplate/v4/STGroup;


# instance fields
.field protected arrowhead:Ljava/lang/String;

.field protected grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field protected rankdir:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 52
    new-instance v0, Lorg/stringtemplate/v4/STGroupFile;

    const-string v1, "groovyjarjarantlr4/v4/tool/templates/dot/graphs.stg"

    invoke-direct {v0, v1}, Lorg/stringtemplate/v4/STGroupFile;-><init>(Ljava/lang/String;)V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 1

    .line 57
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "normal"

    .line 48
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->arrowhead:Ljava/lang/String;

    const-string v0, "LR"

    .line 49
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->rankdir:Ljava/lang/String;

    .line 58
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    return-void
.end method


# virtual methods
.method public getDOT(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 156
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getDOT(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Z)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getDOT(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Z)Ljava/lang/String;
    .locals 5

    .line 160
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    .line 161
    invoke-interface {v0}, Ljava/util/Set;->size()I

    move-result v1

    add-int/lit8 v1, v1, 0x1

    new-array v1, v1, [Ljava/lang/String;

    .line 163
    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v2, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    add-int/lit8 v4, v2, 0x1

    aput-object v3, v1, v2

    move v2, v4

    goto :goto_0

    .line 164
    :cond_0
    invoke-virtual {p0, p1, v1, p2}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getDOT(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;[Ljava/lang/String;Z)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getDOT(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;[Ljava/lang/String;Z)Ljava/lang/String;
    .locals 18

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    if-nez v1, :cond_0

    const/4 v1, 0x0

    return-object v1

    .line 175
    :cond_0
    new-instance v2, Ljava/util/HashSet;

    invoke-direct {v2}, Ljava/util/HashSet;-><init>()V

    .line 176
    sget-object v3, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    const-string v4, "atn"

    invoke-virtual {v3, v4}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v3

    .line 177
    iget v4, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v5, "startState"

    invoke-virtual {v3, v5, v4}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 178
    iget-object v4, v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->rankdir:Ljava/lang/String;

    const-string v5, "rankdir"

    invoke-virtual {v3, v5, v4}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 180
    new-instance v4, Ljava/util/LinkedList;

    invoke-direct {v4}, Ljava/util/LinkedList;-><init>()V

    .line 182
    invoke-interface {v4, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 183
    :cond_1
    :goto_0
    invoke-interface {v4}, Ljava/util/List;->isEmpty()Z

    move-result v1

    const-string v5, "s"

    const-string v6, "label"

    if-nez v1, :cond_16

    const/4 v1, 0x0

    .line 184
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 185
    invoke-interface {v2, v7}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_2

    invoke-interface {v4, v1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    goto :goto_0

    .line 186
    :cond_2
    invoke-interface {v2, v7}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 189
    instance-of v8, v7, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    if-eqz v8, :cond_3

    goto :goto_0

    :cond_3
    move v8, v1

    .line 207
    :goto_1
    invoke-virtual {v7}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v9

    if-ge v8, v9, :cond_1

    .line 208
    invoke-virtual {v7, v8}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v9

    .line 209
    instance-of v10, v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    const-string v11, "edges"

    const-string v12, "arrowhead"

    const-string v13, "target"

    const-string v14, "src"

    const-string v15, "edge"

    if-eqz v10, :cond_5

    .line 210
    check-cast v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    .line 212
    sget-object v10, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    invoke-virtual {v10, v15}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v10

    .line 214
    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<"

    invoke-virtual {v15, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v15, v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->ruleIndex:I

    aget-object v15, p2, v15

    invoke-virtual {v1, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 215
    iget-object v15, v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    check-cast v15, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    iget-boolean v15, v15, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->isPrecedenceRule:Z

    if-eqz v15, :cond_4

    .line 216
    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v15, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v15, "["

    invoke-virtual {v1, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v15, v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->precedence:I

    invoke-virtual {v1, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v15, "]"

    invoke-virtual {v1, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 218
    :cond_4
    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v15, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v15, ">"

    invoke-virtual {v1, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 220
    invoke-virtual {v10, v6, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 221
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v15, v7, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v1, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v10, v14, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 222
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v14, v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v14, v14, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v1, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v10, v13, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 223
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->arrowhead:Ljava/lang/String;

    invoke-virtual {v10, v12, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 224
    invoke-virtual {v3, v11, v10}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 225
    iget-object v1, v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-interface {v4, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-object/from16 v16, v2

    const/4 v2, 0x0

    goto/16 :goto_9

    .line 228
    :cond_5
    instance-of v1, v9, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;

    if-eqz v1, :cond_6

    .line 229
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    const-string v15, "action-edge"

    invoke-virtual {v1, v15}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 230
    invoke-virtual {v9}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v0, v15}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v1, v6, v15}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    :goto_2
    move-object/from16 v16, v2

    move-object/from16 v17, v4

    goto/16 :goto_7

    .line 232
    :cond_6
    instance-of v1, v9, Lgroovyjarjarantlr4/v4/runtime/atn/AbstractPredicateTransition;

    if-eqz v1, :cond_7

    .line 233
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    invoke-virtual {v1, v15}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 234
    invoke-virtual {v9}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v0, v15}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v1, v6, v15}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_2

    .line 236
    :cond_7
    invoke-virtual {v9}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->isEpsilon()Z

    move-result v1

    if-eqz v1, :cond_a

    .line 237
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    const-string v15, "epsilon-edge"

    invoke-virtual {v1, v15}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 238
    invoke-virtual {v9}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v0, v15}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v1, v6, v15}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 240
    iget-object v15, v9, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    instance-of v15, v15, Lgroovyjarjarantlr4/v4/runtime/atn/PlusBlockStartState;

    if-eqz v15, :cond_8

    .line 241
    iget-object v15, v9, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    check-cast v15, Lgroovyjarjarantlr4/v4/runtime/atn/PlusBlockStartState;

    iget-object v15, v15, Lgroovyjarjarantlr4/v4/runtime/atn/PlusBlockStartState;->loopBackState:Lgroovyjarjarantlr4/v4/runtime/atn/PlusLoopbackState;

    invoke-virtual {v7, v15}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->equals(Ljava/lang/Object;)Z

    move-result v15

    goto :goto_3

    .line 243
    :cond_8
    iget-object v15, v9, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    instance-of v15, v15, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    if-eqz v15, :cond_9

    .line 244
    iget-object v15, v9, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    check-cast v15, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    iget-object v15, v15, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;->loopBackState:Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopbackState;

    invoke-virtual {v7, v15}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->equals(Ljava/lang/Object;)Z

    move-result v15

    goto :goto_3

    :cond_9
    const/4 v15, 0x0

    .line 246
    :goto_3
    invoke-static {v15}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v15

    const-string v10, "loopback"

    invoke-virtual {v1, v10, v15}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_2

    .line 248
    :cond_a
    instance-of v1, v9, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;

    if-eqz v1, :cond_d

    .line 249
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    invoke-virtual {v1, v15}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 250
    move-object v10, v9

    check-cast v10, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;

    .line 251
    iget v15, v10, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    invoke-static {v15}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v15

    if-eqz p3, :cond_b

    .line 252
    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v16, v2

    const-string v2, "\'"

    invoke-virtual {v15, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v15

    move-object/from16 v17, v4

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    iget v10, v10, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    invoke-virtual {v4, v10}, Ljava/lang/StringBuilder;->appendCodePoint(I)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v15, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v15

    goto :goto_4

    :cond_b
    move-object/from16 v16, v2

    move-object/from16 v17, v4

    .line 253
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    if-eqz v2, :cond_c

    iget v4, v10, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenDisplayName(I)Ljava/lang/String;

    move-result-object v15

    .line 254
    :cond_c
    :goto_4
    invoke-virtual {v0, v15}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v6, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto/16 :goto_7

    :cond_d
    move-object/from16 v16, v2

    move-object/from16 v17, v4

    .line 256
    instance-of v1, v9, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;

    if-eqz v1, :cond_11

    .line 257
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    invoke-virtual {v1, v15}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 258
    move-object v2, v9

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;

    .line 259
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;->label()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v4

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->toString()Ljava/lang/String;

    move-result-object v4

    if-eqz p3, :cond_e

    .line 260
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;->label()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    const/4 v4, 0x1

    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->toString(Z)Ljava/lang/String;

    move-result-object v2

    move-object v4, v2

    goto :goto_5

    .line 261
    :cond_e
    iget-object v10, v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    if-eqz v10, :cond_f

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;->label()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    iget-object v4, v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v4

    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;)Ljava/lang/String;

    move-result-object v4

    .line 262
    :cond_f
    :goto_5
    instance-of v2, v9, Lgroovyjarjarantlr4/v4/runtime/atn/NotSetTransition;

    if-eqz v2, :cond_10

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v10, "~"

    invoke-virtual {v2, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .line 263
    :cond_10
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v6, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_7

    .line 265
    :cond_11
    instance-of v1, v9, Lgroovyjarjarantlr4/v4/runtime/atn/RangeTransition;

    if-eqz v1, :cond_14

    .line 266
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    invoke-virtual {v1, v15}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 267
    move-object v2, v9

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/RangeTransition;

    .line 268
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/RangeTransition;->label()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v4

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->toString()Ljava/lang/String;

    move-result-object v4

    if-eqz p3, :cond_12

    .line 269
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/RangeTransition;->toString()Ljava/lang/String;

    move-result-object v4

    goto :goto_6

    .line 270
    :cond_12
    iget-object v10, v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    if-eqz v10, :cond_13

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/RangeTransition;->label()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    iget-object v4, v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v4

    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;)Ljava/lang/String;

    move-result-object v4

    .line 271
    :cond_13
    :goto_6
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v6, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_7

    .line 274
    :cond_14
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    invoke-virtual {v1, v15}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 275
    invoke-virtual {v9}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v6, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 277
    :goto_7
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget v4, v7, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v14, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 278
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v4, v9, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v13, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 279
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->arrowhead:Ljava/lang/String;

    invoke-virtual {v1, v12, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 280
    invoke-virtual {v7}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v2

    const-string v4, "transitionIndex"

    const/4 v10, 0x1

    if-le v2, v10, :cond_15

    .line 281
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-virtual {v1, v4, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    const/4 v2, 0x0

    goto :goto_8

    :cond_15
    const/4 v2, 0x0

    .line 284
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v10

    invoke-virtual {v1, v4, v10}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 286
    :goto_8
    invoke-virtual {v3, v11, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 287
    iget-object v1, v9, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-object/from16 v4, v17

    invoke-interface {v4, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_9
    add-int/lit8 v8, v8, 0x1

    move v1, v2

    move-object/from16 v2, v16

    goto/16 :goto_1

    :cond_16
    move-object/from16 v16, v2

    .line 302
    invoke-interface/range {v16 .. v16}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_a
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    const-string v4, "states"

    const-string v7, "name"

    if-eqz v2, :cond_18

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 303
    instance-of v8, v2, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    if-nez v8, :cond_17

    goto :goto_a

    .line 304
    :cond_17
    sget-object v8, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    const-string v9, "stopstate"

    invoke-virtual {v8, v9}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v8

    .line 305
    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    iget v10, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v7, v9}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 306
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getStateLabel(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v8, v6, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 307
    invoke-virtual {v3, v4, v8}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_a

    .line 310
    :cond_18
    invoke-interface/range {v16 .. v16}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_b
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1a

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 311
    instance-of v8, v2, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    if-eqz v8, :cond_19

    goto :goto_b

    .line 312
    :cond_19
    sget-object v8, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    const-string v9, "state"

    invoke-virtual {v8, v9}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v8

    .line 313
    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v9

    iget v10, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v9

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v7, v9}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 314
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getStateLabel(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v6, v9}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 315
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getTransitions()[Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v2

    const-string v9, "transitions"

    invoke-virtual {v8, v9, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 316
    invoke-virtual {v3, v4, v8}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_b

    .line 319
    :cond_1a
    invoke-virtual {v3}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v1

    return-object v1
.end method

.method public getDOT(Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;Z)Ljava/lang/String;
    .locals 11

    .line 62
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 64
    :cond_0
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    const-string v1, "dfa"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    .line 65
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "DFA"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v2, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->decision:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "name"

    invoke-virtual {v0, v2, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 66
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->stateNumber:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v3, "startState"

    invoke-virtual {v0, v3, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 68
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->rankdir:Ljava/lang/String;

    const-string v3, "rankdir"

    invoke-virtual {v0, v3, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 71
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->states:Ljava/util/concurrent/ConcurrentMap;

    invoke-interface {v1}, Ljava/util/concurrent/ConcurrentMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    const-string v4, "states"

    const-string v5, "label"

    const-string v6, "s"

    if-eqz v3, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    .line 72
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->isAcceptState()Z

    move-result v7

    if-nez v7, :cond_1

    goto :goto_0

    .line 73
    :cond_1
    sget-object v7, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    const-string v8, "stopstate"

    invoke-virtual {v7, v8}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v7

    .line 74
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    iget v8, v3, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->stateNumber:I

    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v7, v2, v6}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 75
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getStateLabel(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v7, v5, v3}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 76
    invoke-virtual {v0, v4, v7}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_0

    .line 79
    :cond_2
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->states:Ljava/util/concurrent/ConcurrentMap;

    invoke-interface {v1}, Ljava/util/concurrent/ConcurrentMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    const v7, 0x7fffffff

    if-eqz v3, :cond_5

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    .line 80
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->isAcceptState()Z

    move-result v8

    if-eqz v8, :cond_3

    goto :goto_1

    .line 81
    :cond_3
    iget v8, v3, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->stateNumber:I

    if-ne v8, v7, :cond_4

    goto :goto_1

    .line 82
    :cond_4
    sget-object v7, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    const-string v8, "state"

    invoke-virtual {v7, v8}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v7

    .line 83
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    iget v9, v3, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->stateNumber:I

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v2, v8}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 84
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getStateLabel(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v7, v5, v3}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 85
    invoke-virtual {v0, v4, v7}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_1

    .line 88
    :cond_5
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->states:Ljava/util/concurrent/ConcurrentMap;

    invoke-interface {p1}, Ljava/util/concurrent/ConcurrentMap;->keySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_6
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_b

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    .line 89
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getEdgeMap()Ljava/util/Map;

    move-result-object v2

    .line 90
    invoke-interface {v2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_6

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 91
    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    if-nez v4, :cond_7

    goto :goto_2

    .line 93
    :cond_7
    iget v8, v4, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->stateNumber:I

    if-ne v8, v7, :cond_8

    goto :goto_2

    .line 94
    :cond_8
    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Ljava/lang/Integer;

    invoke-virtual {v8}, Ljava/lang/Integer;->intValue()I

    move-result v8

    .line 95
    invoke-static {v8}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v9

    if-eqz p2, :cond_9

    .line 96
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "\'"

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    invoke-virtual {v10, v3}, Ljava/lang/StringBuilder;->appendCodePoint(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v8, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    goto :goto_3

    .line 97
    :cond_9
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    if-eqz v3, :cond_a

    invoke-virtual {v3, v8}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenDisplayName(I)Ljava/lang/String;

    move-result-object v9

    .line 98
    :cond_a
    :goto_3
    sget-object v3, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->stlib:Lorg/stringtemplate/v4/STGroup;

    const-string v8, "edge"

    invoke-virtual {v3, v8}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v3

    .line 99
    invoke-virtual {v3, v5, v9}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 100
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    iget v9, v1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->stateNumber:I

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    const-string v9, "src"

    invoke-virtual {v3, v9, v8}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 101
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    iget v4, v4, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->stateNumber:I

    invoke-virtual {v8, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const-string v8, "target"

    invoke-virtual {v3, v8, v4}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 102
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->arrowhead:Ljava/lang/String;

    const-string v8, "arrowhead"

    invoke-virtual {v3, v8, v4}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    const-string v4, "edges"

    .line 103
    invoke-virtual {v0, v4, v3}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto/16 :goto_2

    .line 107
    :cond_b
    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    .line 108
    invoke-static {p1}, Lgroovyjarjarantlr4/v4/misc/Utils;->sortLinesInString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected getEdgeLabel(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    const-string v0, "\\"

    const-string v1, "\\\\"

    .line 419
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "\""

    const-string v1, "\\\""

    .line 420
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "\n"

    const-string v1, "\\\\n"

    .line 421
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "\r"

    const-string v1, ""

    .line 422
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected getStateLabel(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;
    .locals 2

    if-nez p1, :cond_0

    const-string p1, "null"

    return-object p1

    :cond_0
    const-string v0, ""

    .line 430
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;

    if-eqz v1, :cond_1

    .line 431
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "&rarr;\\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    .line 433
    :cond_1
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/BlockEndState;

    if-eqz v1, :cond_2

    .line 434
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "&larr;\\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 437
    :cond_2
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 439
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/PlusBlockStartState;

    if-nez v1, :cond_5

    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/PlusLoopbackState;

    if-eqz v1, :cond_3

    goto :goto_1

    .line 442
    :cond_3
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarBlockStartState;

    if-nez v1, :cond_4

    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    if-nez v1, :cond_4

    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopbackState;

    if-eqz v1, :cond_6

    .line 443
    :cond_4
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "*"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_2

    .line 440
    :cond_5
    :goto_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "+"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 446
    :cond_6
    :goto_2
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    if-eqz v1, :cond_7

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    if-ltz v1, :cond_7

    .line 447
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\\nd="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    :cond_7
    return-object v0
.end method

.method protected getStateLabel(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)Ljava/lang/String;
    .locals 10

    if-nez p1, :cond_0

    const-string p1, "null"

    return-object p1

    .line 113
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    const/16 v1, 0xfa

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(I)V

    const/16 v1, 0x73

    .line 114
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 115
    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->stateNumber:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 116
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->isAcceptState()Z

    move-result v1

    if-eqz v1, :cond_1

    const-string v1, "=>"

    .line 117
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getPrediction()I

    move-result v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 119
    :cond_1
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    if-eqz v1, :cond_8

    .line 120
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->configs:Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->getRepresentedAlternatives()Ljava/util/BitSet;

    move-result-object v1

    const-string v2, "\\n"

    .line 121
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 122
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->configs:Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    const/4 v3, 0x0

    .line 123
    invoke-virtual {v1, v3}, Ljava/util/BitSet;->nextSetBit(I)I

    move-result v4

    :goto_0
    if-ltz v4, :cond_8

    .line 124
    invoke-virtual {v1, v3}, Ljava/util/BitSet;->nextSetBit(I)I

    move-result v5

    if-le v4, v5, :cond_2

    .line 125
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_2
    const-string v5, "alt"

    .line 127
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 128
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const/16 v5, 0x3a

    .line 129
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 132
    new-instance v5, Ljava/util/ArrayList;

    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    .line 133
    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :goto_1
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_4

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 134
    invoke-virtual {v7}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result v8

    if-eq v8, v4, :cond_3

    goto :goto_1

    .line 135
    :cond_3
    invoke-interface {v5, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_4
    move v6, v3

    move v7, v6

    .line 138
    :goto_2
    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v8

    if-ge v6, v8, :cond_7

    .line 139
    invoke-interface {v5, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    add-int/lit8 v7, v7, 0x1

    const/4 v9, 0x0

    .line 141
    invoke-virtual {v8, v9, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->toString(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Z)Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v8, v6, 0x1

    .line 142
    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v9

    if-ge v8, v9, :cond_5

    const-string v9, ", "

    .line 143
    invoke-virtual {v0, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 145
    :cond_5
    rem-int/lit8 v9, v7, 0x5

    if-nez v9, :cond_6

    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v9

    sub-int/2addr v9, v6

    const/4 v6, 0x3

    if-le v9, v6, :cond_6

    .line 146
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_6
    move v6, v8

    goto :goto_2

    :cond_7
    add-int/lit8 v4, v4, 0x1

    .line 123
    invoke-virtual {v1, v4}, Ljava/util/BitSet;->nextSetBit(I)I

    move-result v4

    goto :goto_0

    .line 151
    :cond_8
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
