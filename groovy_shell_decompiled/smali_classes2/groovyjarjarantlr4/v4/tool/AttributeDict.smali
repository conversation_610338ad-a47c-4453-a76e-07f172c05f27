.class public Lgroovyjarjarantlr4/v4/tool/AttributeDict;
.super Ljava/lang/Object;
.source "AttributeDict.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;
    }
.end annotation


# static fields
.field public static final predefinedTokenDict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;


# instance fields
.field public ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

.field public final attributes:Ljava/util/LinkedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/Attribute;",
            ">;"
        }
    .end annotation
.end field

.field public name:Ljava/lang/String;

.field public type:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 35
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->TOKEN:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;-><init>(Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;)V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->predefinedTokenDict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    .line 37
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "text"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 38
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "type"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 39
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "line"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 40
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "index"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 41
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "pos"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 42
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "channel"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 43
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "int"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 56
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 52
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;)V
    .locals 1

    .line 57
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 52
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    .line 57
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->type:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    return-void
.end method


# virtual methods
.method public add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 2

    .line 59
    iput-object p0, p1, Lgroovyjarjarantlr4/v4/tool/Attribute;->dict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Attribute;->name:Ljava/lang/String;

    invoke-virtual {v0, v1, p1}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    return-object p1
.end method

.method public get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 1

    .line 60
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    invoke-virtual {v0, p1}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    return-object p1
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    .line 63
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->name:Ljava/lang/String;

    return-object v0
.end method

.method public intersection(Lgroovyjarjarantlr4/v4/tool/AttributeDict;)Ljava/util/Set;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/AttributeDict;",
            ")",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    if-eqz p1, :cond_1

    .line 73
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->size()I

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->size()I

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 77
    :cond_0
    new-instance v0, Ljava/util/HashSet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    invoke-virtual {v1}, Ljava/util/LinkedHashMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    .line 78
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    invoke-virtual {p1}, Ljava/util/LinkedHashMap;->keySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Set;->retainAll(Ljava/util/Collection;)Z

    return-object v0

    .line 74
    :cond_1
    :goto_0
    invoke-static {}, Ljava/util/Collections;->emptySet()Ljava/util/Set;

    move-result-object p1

    return-object p1
.end method

.method public size()I
    .locals 1

    .line 66
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->size()I

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 84
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
