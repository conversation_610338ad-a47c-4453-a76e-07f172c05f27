.class public final enum Lgroovyjarjarantlr4/v4/tool/LabelType;
.super Ljava/lang/Enum;
.source "LabelType.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovyjarjarantlr4/v4/tool/LabelType;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovyjarjarantlr4/v4/tool/LabelType;

.field public static final enum LEXER_STRING_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

.field public static final enum RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

.field public static final enum RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

.field public static final enum TOKEN_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

.field public static final enum TOKEN_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;


# direct methods
.method static constructor <clinit>()V
    .locals 11

    .line 11
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/LabelType;

    const-string v1, "RULE_LABEL"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/tool/LabelType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    .line 12
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/LabelType;

    const-string v3, "TOKEN_LABEL"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lgroovyjarjarantlr4/v4/tool/LabelType;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    .line 13
    new-instance v3, Lgroovyjarjarantlr4/v4/tool/LabelType;

    const-string v5, "RULE_LIST_LABEL"

    const/4 v6, 0x2

    invoke-direct {v3, v5, v6}, Lgroovyjarjarantlr4/v4/tool/LabelType;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    .line 14
    new-instance v5, Lgroovyjarjarantlr4/v4/tool/LabelType;

    const-string v7, "TOKEN_LIST_LABEL"

    const/4 v8, 0x3

    invoke-direct {v5, v7, v8}, Lgroovyjarjarantlr4/v4/tool/LabelType;-><init>(Ljava/lang/String;I)V

    sput-object v5, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    .line 15
    new-instance v7, Lgroovyjarjarantlr4/v4/tool/LabelType;

    const-string v9, "LEXER_STRING_LABEL"

    const/4 v10, 0x4

    invoke-direct {v7, v9, v10}, Lgroovyjarjarantlr4/v4/tool/LabelType;-><init>(Ljava/lang/String;I)V

    sput-object v7, Lgroovyjarjarantlr4/v4/tool/LabelType;->LEXER_STRING_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    const/4 v9, 0x5

    new-array v9, v9, [Lgroovyjarjarantlr4/v4/tool/LabelType;

    aput-object v0, v9, v2

    aput-object v1, v9, v4

    aput-object v3, v9, v6

    aput-object v5, v9, v8

    aput-object v7, v9, v10

    .line 10
    sput-object v9, Lgroovyjarjarantlr4/v4/tool/LabelType;->$VALUES:[Lgroovyjarjarantlr4/v4/tool/LabelType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 10
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelType;
    .locals 1

    .line 10
    const-class v0, Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/tool/LabelType;

    return-object p0
.end method

.method public static values()[Lgroovyjarjarantlr4/v4/tool/LabelType;
    .locals 1

    .line 10
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/LabelType;->$VALUES:[Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v0}, [Lgroovyjarjarantlr4/v4/tool/LabelType;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/tool/LabelType;

    return-object v0
.end method
