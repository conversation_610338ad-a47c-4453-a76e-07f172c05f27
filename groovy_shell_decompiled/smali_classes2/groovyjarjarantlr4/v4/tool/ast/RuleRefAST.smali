.class public Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;
.super Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
.source "RuleRefAST.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/tool/ast/RuleElementAST;


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 18
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(I)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 19
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(ILgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 17
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;)V
    .locals 0

    .line 14
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 12
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 12
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
    .locals 1

    .line 12
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;
    .locals 3

    .line 24
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;)V

    .line 29
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 30
    new-instance v1, Lgroovyjarjarantlr4/runtime/CommonToken;

    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    return-object v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 35
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
