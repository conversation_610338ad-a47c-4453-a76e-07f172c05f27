.class public Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;
.super Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
.source "RuleAST.java"


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 20
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(I)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 19
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)V
    .locals 0

    .line 16
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;
    .locals 1

    .line 34
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)V

    return-object v0
.end method

.method public getLexerAction()Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;
    .locals 3

    const/16 v0, 0x4e

    .line 37
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    .line 38
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChildCount()I

    move-result v1

    const/4 v2, 0x1

    if-ne v1, v2, :cond_0

    const/4 v1, 0x0

    .line 39
    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    .line 40
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChildCount()I

    move-result v1

    sub-int/2addr v1, v2

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    .line 41
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v1

    const/4 v2, 0x4

    if-ne v1, v2, :cond_0

    .line 42
    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public getRuleName()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 28
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v0, :cond_0

    .line 29
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public isLexerRule()Z
    .locals 1

    .line 23
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getRuleName()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 24
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isTokenName(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 49
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
