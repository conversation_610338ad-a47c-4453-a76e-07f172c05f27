.class public Lgroovyjarjarantlr4/v4/tool/ast/SetAST;
.super Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
.source "SetAST.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/tool/ast/RuleElementAST;


# direct methods
.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)V
    .locals 0

    .line 17
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/SetAST;)V
    .locals 0

    .line 14
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 11
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/SetAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/SetAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 11
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/SetAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/SetAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/SetAST;
    .locals 1

    .line 21
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/SetAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/SetAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/SetAST;)V

    return-object v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 25
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/SetAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
