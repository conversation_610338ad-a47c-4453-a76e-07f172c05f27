.class public Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;
.super Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
.source "GrammarRootAST.java"


# static fields
.field public static final defaultOptions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public cmdLineOptions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public fileName:Ljava/lang/String;

.field public grammarType:I

.field public hasErrors:Z

.field public final tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 18
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->defaultOptions:Ljava/util/Map;

    const-string v1, "language"

    const-string v2, "Java"

    .line 20
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "abstract"

    const-string v2, "false"

    .line 21
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/TokenStream;)V
    .locals 0

    .line 49
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(ILgroovyjarjarantlr4/runtime/Token;)V

    const-string p1, "tokenStream"

    .line 51
    invoke-static {p3, p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 54
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/TokenStream;)V
    .locals 0

    .line 58
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)V

    const-string p1, "tokenStream"

    .line 60
    invoke-static {p4, p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 63
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/TokenStream;)V
    .locals 0

    .line 40
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    const-string p1, "tokenStream"

    .line 42
    invoke-static {p2, p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 45
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)V
    .locals 1

    .line 33
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;)V

    .line 34
    iget v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->grammarType:I

    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->grammarType:I

    .line 35
    iget-boolean v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->hasErrors:Z

    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->hasErrors:Z

    .line 36
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-void
.end method


# virtual methods
.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 17
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 17
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
    .locals 1

    .line 17
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;
    .locals 1

    .line 88
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)V

    return-object v0
.end method

.method public getGrammarName()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 67
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 68
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public getOptionString(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    .line 74
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->cmdLineOptions:Ljava/util/Map;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 75
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->cmdLineOptions:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    return-object p1

    .line 77
    :cond_0
    invoke-super {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_1

    .line 79
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->defaultOptions:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    move-object v0, p1

    check-cast v0, Ljava/lang/String;

    :cond_1
    return-object v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 85
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
