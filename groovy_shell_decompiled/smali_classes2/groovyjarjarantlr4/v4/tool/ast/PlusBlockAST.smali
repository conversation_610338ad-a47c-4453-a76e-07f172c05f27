.class public Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;
.super Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
.source "PlusBlockAST.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/tool/ast/RuleElementAST;
.implements Lgroovyjarjarantlr4/v4/tool/ast/QuantifierAST;


# instance fields
.field private final _greedy:Z


# direct methods
.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 20
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>(ILgroovyjarjarantlr4/runtime/Token;)V

    if-nez p3, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    .line 21
    :goto_0
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;->_greedy:Z

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;)V
    .locals 0

    .line 15
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 16
    iget-boolean p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;->_greedy:Z

    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;->_greedy:Z

    return-void
.end method


# virtual methods
.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 11
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 11
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;
    .locals 1

    .line 30
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;)V

    return-object v0
.end method

.method public isGreedy()Z
    .locals 1

    .line 26
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;->_greedy:Z

    return v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 33
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
