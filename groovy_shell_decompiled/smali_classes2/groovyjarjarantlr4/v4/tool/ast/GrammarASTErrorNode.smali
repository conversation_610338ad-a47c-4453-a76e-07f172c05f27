.class public Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;
.super Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
.source "GrammarASTErrorNode.java"


# instance fields
.field delegate:Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 1

    .line 18
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>()V

    .line 19
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;

    invoke-direct {v0, p1, p2, p3, p4}, Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;->delegate:Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;

    return-void
.end method


# virtual methods
.method public getText()Ljava/lang/String;
    .locals 1

    .line 29
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;->delegate:Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;->getText()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getType()I
    .locals 1

    .line 26
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;->delegate:Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;->getType()I

    move-result v0

    return v0
.end method

.method public isNil()Z
    .locals 1

    .line 23
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;->delegate:Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;->isNil()Z

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 31
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;->delegate:Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/tree/CommonErrorNode;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
