.class public Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
.super Lgroovyjarjarantlr4/runtime/tree/CommonTree;
.source "GrammarAST.java"


# instance fields
.field public atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

.field public g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public textOverride:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 36
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;-><init>()V

    return-void
.end method

.method public constructor <init>(I)V
    .locals 2

    .line 44
    new-instance v0, Lgroovyjarjarantlr4/runtime/CommonToken;

    sget-object v1, Lgroovyjarjarantlr4/v4/parse/ANTLRParser;->tokenNames:[Ljava/lang/String;

    aget-object v1, v1, p1

    invoke-direct {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;)V
    .locals 1

    .line 46
    new-instance v0, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-direct {v0, p2}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    .line 47
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {p2, p1}, Lgroovyjarjarantlr4/runtime/Token;->setType(I)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)V
    .locals 1

    .line 50
    new-instance v0, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-direct {v0, p2}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    .line 51
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {p2, p1}, Lgroovyjarjarantlr4/runtime/Token;->setType(I)V

    .line 52
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {p1, p3}, Lgroovyjarjarantlr4/runtime/Token;->setText(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 37
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 1

    .line 39
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;-><init>(Lgroovyjarjarantlr4/runtime/tree/CommonTree;)V

    .line 40
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 41
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 42
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->textOverride:Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->textOverride:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public deleteChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)Z
    .locals 3

    const/4 v0, 0x0

    move v1, v0

    .line 148
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_1

    .line 149
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    if-ne v2, p1, :cond_0

    .line 151
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChildIndex()I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->deleteChild(I)Ljava/lang/Object;

    const/4 p1, 0x1

    return p1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 26
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 211
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-object v0
.end method

.method public dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 2

    .line 216
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v0

    .line 217
    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v1, v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    .line 218
    invoke-virtual {v1, p0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->dupTree(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    return-object v0
.end method

.method public getAllChildrenWithType(I)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation

    .line 64
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    .line 65
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    if-eqz v2, :cond_1

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_1

    .line 66
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/runtime/tree/Tree;

    .line 67
    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v3

    if-ne v3, p1, :cond_0

    .line 68
    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getAltLabel()Ljava/lang/String;
    .locals 6

    .line 132
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAncestors()Ljava/util/List;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    .line 134
    :cond_0
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v2

    add-int/lit8 v2, v2, -0x1

    :goto_0
    if-ltz v2, :cond_3

    .line 135
    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 136
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v4

    const/16 v5, 0x4a

    if-ne v4, v5, :cond_2

    .line 137
    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 138
    iget-object v4, v3, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v4, :cond_1

    iget-object v0, v3, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 139
    :cond_1
    iget-object v4, v3, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    if-eqz v4, :cond_2

    .line 140
    iget-object v0, v3, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    return-object v0

    :cond_2
    add-int/lit8 v2, v2, -0x1

    goto :goto_0

    :cond_3
    return-object v1
.end method

.method public getChildrenAsArray()[Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 2

    .line 56
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    new-array v1, v1, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-interface {v0, v1}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    return-object v0
.end method

.method public getFirstDescendantWithType(I)Lgroovyjarjarantlr4/runtime/tree/CommonTree;
    .locals 4

    .line 162
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v0

    if-ne v0, p1, :cond_0

    return-object p0

    .line 163
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    const/4 v1, 0x0

    if-nez v0, :cond_1

    return-object v1

    .line 164
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_4

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 165
    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 166
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v3

    if-ne v3, p1, :cond_3

    return-object v2

    .line 167
    :cond_3
    invoke-virtual {v2, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstDescendantWithType(I)Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    move-result-object v2

    if-eqz v2, :cond_2

    return-object v2

    :cond_4
    return-object v1
.end method

.method public getFirstDescendantWithType(Lgroovyjarjarantlr4/runtime/BitSet;)Lgroovyjarjarantlr4/runtime/tree/CommonTree;
    .locals 4

    .line 175
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/runtime/BitSet;->member(I)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p0

    .line 176
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    const/4 v1, 0x0

    if-nez v0, :cond_1

    return-object v1

    .line 177
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_4

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 178
    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 179
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v3

    invoke-virtual {p1, v3}, Lgroovyjarjarantlr4/runtime/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_3

    return-object v2

    .line 180
    :cond_3
    invoke-virtual {v2, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstDescendantWithType(Lgroovyjarjarantlr4/runtime/BitSet;)Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    move-result-object v2

    if-eqz v2, :cond_2

    return-object v2

    :cond_4
    return-object v1
.end method

.method public getNodeWithTokenIndex(I)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 2

    .line 105
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getTokenIndex()I

    move-result v0

    if-ne v0, p1, :cond_0

    return-object p0

    :cond_0
    const/4 v0, 0x0

    .line 109
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v1

    if-ge v0, v1, :cond_2

    .line 110
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 111
    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodeWithTokenIndex(I)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v1

    if-eqz v1, :cond_1

    return-object v1

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    return-object p1
.end method

.method public getNodesWithType(I)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation

    .line 60
    invoke-static {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->of(I)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodesWithType(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public getNodesWithType(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation

    .line 75
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 76
    new-instance v1, Ljava/util/LinkedList;

    invoke-direct {v1}, Ljava/util/LinkedList;-><init>()V

    .line 77
    invoke-interface {v1, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 79
    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_3

    const/4 v2, 0x0

    .line 80
    invoke-interface {v1, v2}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz p1, :cond_1

    .line 81
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v3

    invoke-virtual {p1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v3

    if-eqz v3, :cond_2

    :cond_1
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 82
    :cond_2
    iget-object v3, v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->children:Ljava/util/List;

    if-eqz v3, :cond_0

    .line 83
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildrenAsArray()[Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    invoke-static {v2}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    :cond_3
    return-object v0
.end method

.method public getNodesWithTypePreorderDFS(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation

    .line 90
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 91
    invoke-virtual {p0, v0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodesWithTypePreorderDFS_(Ljava/util/List;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    return-object v0
.end method

.method public getNodesWithTypePreorderDFS_(Ljava/util/List;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ")V"
        }
    .end annotation

    .line 96
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v0

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    const/4 v0, 0x0

    .line 98
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v1

    if-ge v0, v1, :cond_1

    .line 99
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 100
    invoke-virtual {v1, p1, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodesWithTypePreorderDFS_(Ljava/util/List;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public getOutermostAltNode()Lgroovyjarjarantlr4/v4/tool/ast/AltAST;
    .locals 1

    .line 120
    instance-of v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    if-eqz v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    instance-of v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    if-eqz v0, :cond_0

    .line 121
    move-object v0, p0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    return-object v0

    .line 123
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getOutermostAltNode()Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    move-result-object v0

    return-object v0

    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public setText(Ljava/lang/String;)V
    .locals 1

    .line 201
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/runtime/Token;->setText(Ljava/lang/String;)V

    return-void
.end method

.method public setType(I)V
    .locals 1

    .line 187
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/runtime/Token;->setType(I)V

    return-void
.end method

.method public toTokenString()Ljava/lang/String;
    .locals 7

    .line 222
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v0

    .line 223
    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v1, v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    .line 224
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    invoke-direct {v0, v1, p0}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/Object;)V

    .line 226
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v3, 0x1

    .line 227
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 228
    invoke-virtual {v1, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->getType(Ljava/lang/Object;)I

    move-result v5

    :goto_0
    const/4 v6, -0x1

    if-eq v5, v6, :cond_0

    const-string v5, " "

    .line 230
    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 231
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 232
    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;->consume()V

    .line 233
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 234
    invoke-virtual {v1, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->getType(Ljava/lang/Object;)I

    move-result v5

    goto :goto_0

    .line 236
    :cond_0
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 239
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
