.class public Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;
.super Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
.source "BlockAST.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/tool/ast/RuleElementAST;


# static fields
.field public static final defaultBlockOptions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final defaultLexerBlockOptions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 18
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->defaultBlockOptions:Ljava/util/Map;

    .line 22
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->defaultLexerBlockOptions:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>(I)V
    .locals 0

    .line 30
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(I)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 31
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(ILgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)V
    .locals 0

    .line 32
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 29
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;)V
    .locals 0

    .line 26
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;
    .locals 1

    .line 35
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;)V

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    move-result-object v0

    return-object v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 38
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
