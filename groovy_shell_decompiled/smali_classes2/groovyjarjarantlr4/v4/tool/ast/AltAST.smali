.class public Lgroovyjarjarantlr4/v4/tool/ast/AltAST;
.super Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
.source "AltAST.java"


# instance fields
.field public alt:Lgroovyjarjarantlr4/v4/tool/Alternative;

.field public altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

.field public leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 33
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(I)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 34
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(ILgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)V
    .locals 0

    .line 35
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 32
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;)V
    .locals 1

    .line 26
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;)V

    .line 27
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->alt:Lgroovyjarjarantlr4/v4/tool/Alternative;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->alt:Lgroovyjarjarantlr4/v4/tool/Alternative;

    .line 28
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 29
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    return-void
.end method


# virtual methods
.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/AltAST;
    .locals 1

    .line 38
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;)V

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    move-result-object v0

    return-object v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 41
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
