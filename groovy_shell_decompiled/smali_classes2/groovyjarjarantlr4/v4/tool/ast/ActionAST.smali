.class public Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;
.super Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
.source "ActionAST.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/tool/ast/RuleElementAST;


# instance fields
.field public chunks:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation
.end field

.field public resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 26
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(I)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 27
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(ILgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 25
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 1

    .line 20
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;)V

    .line 21
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    .line 22
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->chunks:Ljava/util/List;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->chunks:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;
    .locals 1

    .line 30
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
    .locals 1

    .line 14
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    move-result-object v0

    return-object v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 33
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
