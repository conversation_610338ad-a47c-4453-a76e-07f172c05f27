.class public Lgroovyjarjarantlr4/v4/tool/ast/PredAST;
.super Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;
.source "PredAST.java"


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 17
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;-><init>(I)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 18
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;-><init>(ILgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 16
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/PredAST;)V
    .locals 0

    .line 13
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    .line 11
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;
    .locals 1

    .line 11
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 11
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;
    .locals 1

    .line 11
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    move-result-object v0

    return-object v0
.end method

.method public dupNode()Lgroovyjarjarantlr4/v4/tool/ast/PredAST;
    .locals 1

    .line 21
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/PredAST;)V

    return-object v0
.end method

.method public visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;)Ljava/lang/Object;
    .locals 0

    .line 24
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTVisitor;->visit(Lgroovyjarjarantlr4/v4/tool/ast/PredAST;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
