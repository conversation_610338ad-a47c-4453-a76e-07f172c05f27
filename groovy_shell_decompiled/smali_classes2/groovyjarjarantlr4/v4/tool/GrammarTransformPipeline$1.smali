.class Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$1;
.super Ljava/lang/Object;
.source "GrammarTransformPipeline.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/tree/TreeVisitorAction;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->expandParameterizedLoops(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;)V
    .locals 0

    .line 78
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$1;->this$0:Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public post(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    return-object p1
.end method

.method public pre(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 81
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v1

    const/4 v2, 0x3

    if-ne v1, v2, :cond_0

    .line 82
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$1;->this$0:Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->expandParameterizedLoop(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p1

    :cond_0
    return-object p1
.end method
