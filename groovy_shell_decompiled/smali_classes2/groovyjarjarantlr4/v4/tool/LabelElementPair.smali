.class public Lgroovyjarjarantlr4/v4/tool/LabelElementPair;
.super Ljava/lang/Object;
.source "LabelElementPair.java"


# static fields
.field public static final tokenTypeForTokens:Lgroovyjarjarantlr4/runtime/BitSet;


# instance fields
.field public element:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

.field public label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

.field public type:Lgroovyjarjarantlr4/v4/tool/LabelType;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 14
    new-instance v0, Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/runtime/BitSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->tokenTypeForTokens:Lgroovyjarjarantlr4/runtime/BitSet;

    const/16 v1, 0x42

    .line 16
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/runtime/BitSet;->add(I)V

    const/16 v1, 0x3e

    .line 17
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/runtime/BitSet;->add(I)V

    const/16 v1, 0x64

    .line 18
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/runtime/BitSet;->add(I)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;I)V
    .locals 1

    .line 25
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 26
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 27
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->element:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 29
    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->tokenTypeForTokens:Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-virtual {p3, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstDescendantWithType(Lgroovyjarjarantlr4/runtime/BitSet;)Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    move-result-object p2

    const/16 v0, 0xa

    if-eqz p2, :cond_1

    if-ne p4, v0, :cond_0

    .line 30
    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    goto :goto_0

    .line 31
    :cond_0
    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    goto :goto_0

    :cond_1
    const/16 p2, 0x39

    .line 33
    invoke-virtual {p3, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstDescendantWithType(I)Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    move-result-object p2

    if-eqz p2, :cond_3

    if-ne p4, v0, :cond_2

    .line 34
    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    goto :goto_0

    .line 35
    :cond_2
    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    .line 39
    :cond_3
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result p1

    if-eqz p1, :cond_4

    const/16 p1, 0x3e

    .line 40
    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstDescendantWithType(I)Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    move-result-object p1

    if-eqz p1, :cond_4

    if-ne p4, v0, :cond_4

    .line 41
    sget-object p1, Lgroovyjarjarantlr4/v4/tool/LabelType;->LEXER_STRING_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    :cond_4
    return-void
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 3

    .line 48
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->element:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
