.class public Lgroovyjarjarantlr4/v4/tool/Grammar;
.super Ljava/lang/Object;
.source "Grammar.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/tool/AttributeResolver;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z = false

.field public static final AUTO_GENERATED_TOKEN_NAME_PREFIX:Ljava/lang/String; = "T__"

.field public static final GRAMMAR_FROM_STRING_NAME:Ljava/lang/String; = "<string>"

.field public static final INVALID_RULE_NAME:Ljava/lang/String; = "<invalid>"

.field public static final INVALID_TOKEN_NAME:Ljava/lang/String; = "<INVALID>"

.field public static final LexerBlockOptions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final ParserBlockOptions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final actionOptions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final doNotCopyOptionsToLexer:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final grammarAndLabelRefTypeToScope:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/AttributeDict;",
            ">;"
        }
    .end annotation
.end field

.field public static final lexerOptions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final parserOptions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final ruleOptions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final ruleRefOptions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final semPredOptions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final tokenOptions:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

.field public atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

.field public final channelNameToValueMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public final channelValueToNameList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final contextASTs:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;",
            ">;>;"
        }
    .end annotation
.end field

.field public decisionDFAs:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;",
            ">;"
        }
    .end annotation
.end field

.field public decisionLOOK:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "[",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ">;"
        }
    .end annotation
.end field

.field public fileName:Ljava/lang/String;

.field public implicitLexer:Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

.field public importedGrammars:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ">;"
        }
    .end annotation
.end field

.field public indexToPredMap:Ljava/util/LinkedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/tool/ast/PredAST;",
            ">;"
        }
    .end annotation
.end field

.field public indexToRule:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;"
        }
    .end annotation
.end field

.field public lexerActions:Ljava/util/LinkedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field maxChannelType:I

.field maxTokenType:I

.field public name:Ljava/lang/String;

.field public namedActions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ">;"
        }
    .end annotation
.end field

.field public originalGrammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public final originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

.field public parent:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field ruleNumber:I

.field public rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/misc/OrderedHashMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;"
        }
    .end annotation
.end field

.field public sempreds:Ljava/util/LinkedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Lgroovyjarjarantlr4/v4/tool/ast/PredAST;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public stateToGrammarRegionMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Interval;",
            ">;"
        }
    .end annotation
.end field

.field stringLiteralRuleNumber:I

.field public final stringLiteralToTypeMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public text:Ljava/lang/String;

.field public final tokenNameToTypeMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public final tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

.field public final tool:Lgroovyjarjarantlr4/v4/Tool;

.field public final typeToStringLiteralList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final typeToTokenList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 8

    .line 81
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->parserOptions:Ljava/util/Set;

    const-string v1, "superClass"

    .line 83
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v2, "contextSuperClass"

    .line 84
    invoke-interface {v0, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v2, "TokenLabelType"

    .line 85
    invoke-interface {v0, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v3, "abstract"

    .line 86
    invoke-interface {v0, v3}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v4, "tokenVocab"

    .line 87
    invoke-interface {v0, v4}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v5, "language"

    .line 88
    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v5, "accessLevel"

    .line 89
    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v5, "exportMacro"

    .line 90
    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 93
    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->lexerOptions:Ljava/util/Set;

    .line 95
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ruleOptions:Ljava/util/Set;

    const-string v5, "baseContext"

    .line 97
    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 100
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ParserBlockOptions:Ljava/util/Set;

    const-string v5, "sll"

    .line 102
    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 105
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->LexerBlockOptions:Ljava/util/Set;

    .line 108
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ruleRefOptions:Ljava/util/Set;

    const-string v5, "p"

    .line 110
    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v6, "tokenIndex"

    .line 111
    invoke-interface {v0, v6}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 115
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenOptions:Ljava/util/Set;

    const-string v7, "assoc"

    .line 117
    invoke-interface {v0, v7}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 118
    invoke-interface {v0, v6}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 121
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->actionOptions:Ljava/util/Set;

    .line 123
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->semPredOptions:Ljava/util/Set;

    .line 125
    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v5, "fail"

    .line 126
    invoke-interface {v0, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 129
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->doNotCopyOptionsToLexer:Ljava/util/Set;

    .line 131
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 132
    invoke-interface {v0, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 133
    invoke-interface {v0, v3}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 134
    invoke-interface {v0, v4}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 137
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->grammarAndLabelRefTypeToScope:Ljava/util/Map;

    .line 140
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/Rule;->predefinedRulePropertiesDict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    const-string v2, "parser:RULE_LABEL"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 141
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->predefinedTokenDict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    const-string v2, "parser:TOKEN_LABEL"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 142
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/Rule;->predefinedRulePropertiesDict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    const-string v2, "combined:RULE_LABEL"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 143
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->predefinedTokenDict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    const-string v2, "combined:TOKEN_LABEL"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)V
    .locals 2

    .line 294
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 181
    new-instance v0, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    .line 182
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToRule:Ljava/util/List;

    .line 199
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->contextASTs:Ljava/util/Map;

    const/4 v0, 0x0

    .line 201
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ruleNumber:I

    .line 202
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralRuleNumber:I

    .line 211
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->decisionDFAs:Ljava/util/Map;

    .line 226
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxTokenType:I

    .line 232
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    .line 239
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    .line 245
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    .line 251
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    const/4 v1, 0x1

    .line 257
    iput v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxChannelType:I

    .line 263
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelNameToValueMap:Ljava/util/Map;

    .line 271
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    .line 278
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->namedActions:Ljava/util/Map;

    .line 283
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->lexerActions:Ljava/util/LinkedHashMap;

    .line 288
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->sempreds:Ljava/util/LinkedHashMap;

    const-string v1, "ast"

    .line 296
    invoke-static {p2, v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 299
    iget-object v1, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    if-eqz v1, :cond_0

    .line 303
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    .line 304
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    .line 305
    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p1

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    .line 306
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    .line 307
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    .line 309
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->initTokenSymbolTables()V

    return-void

    .line 300
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "ast must have a token stream"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    const-string v0, "<string>"

    const/4 v1, 0x0

    .line 314
    invoke-direct {p0, v0, p1, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    const-string v0, "<string>"

    .line 325
    invoke-direct {p0, v0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/LexerGrammar;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    const-string v0, "<string>"

    const/4 v1, 0x0

    .line 318
    invoke-direct {p0, v0, p1, p2, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 332
    invoke-direct {p0, p1, p2, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 339
    invoke-direct {p0, p1, p2, v0, p3}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 345
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 181
    new-instance v0, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    .line 182
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToRule:Ljava/util/List;

    .line 199
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->contextASTs:Ljava/util/Map;

    const/4 v0, 0x0

    .line 201
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ruleNumber:I

    .line 202
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralRuleNumber:I

    .line 211
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->decisionDFAs:Ljava/util/Map;

    .line 226
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxTokenType:I

    .line 232
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    .line 239
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    .line 245
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    .line 251
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    const/4 v1, 0x1

    .line 257
    iput v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxChannelType:I

    .line 263
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelNameToValueMap:Ljava/util/Map;

    .line 271
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    .line 278
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->namedActions:Ljava/util/Map;

    .line 283
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->lexerActions:Ljava/util/LinkedHashMap;

    .line 288
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->sempreds:Ljava/util/LinkedHashMap;

    .line 346
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->text:Ljava/lang/String;

    .line 347
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    .line 348
    new-instance v1, Lgroovyjarjarantlr4/v4/Tool;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/Tool;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    .line 349
    invoke-virtual {v1, p4}, Lgroovyjarjarantlr4/v4/Tool;->addListener(Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V

    .line 350
    new-instance p4, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;

    invoke-direct {p4, p2}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;-><init>(Ljava/lang/String;)V

    .line 351
    iput-object p1, p4, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->name:Ljava/lang/String;

    .line 353
    invoke-virtual {v1, p1, p4}, Lgroovyjarjarantlr4/v4/Tool;->parse(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/CharStream;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-eqz p1, :cond_2

    .line 358
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    if-eqz p1, :cond_1

    .line 362
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    .line 363
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    .line 367
    new-instance p1, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;

    new-instance p2, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {p2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    invoke-direct {p1, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;)V

    .line 368
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    new-instance p4, Lgroovyjarjarantlr4/v4/tool/Grammar$1;

    invoke-direct {p4, p0, p0}, Lgroovyjarjarantlr4/v4/tool/Grammar$1;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    invoke-virtual {p1, p2, p4}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;->visit(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeVisitorAction;)Ljava/lang/Object;

    .line 374
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->initTokenSymbolTables()V

    if-eqz p3, :cond_0

    .line 377
    invoke-virtual {p0, p3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->importVocab(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 380
    :cond_0
    invoke-virtual {v1, p0, v0}, Lgroovyjarjarantlr4/v4/Tool;->process(Lgroovyjarjarantlr4/v4/tool/Grammar;Z)V

    return-void

    .line 359
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "expected ast to have a token stream"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 355
    :cond_2
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method protected static defAlias(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/tree/TreeWizard;Ljava/util/List;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/runtime/tree/TreeWizard;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;>;)Z"
        }
    .end annotation

    .line 1283
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 1284
    invoke-virtual {p2, p0, p1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeWizard;->parse(Ljava/lang/Object;Ljava/lang/String;Ljava/util/Map;)Z

    move-result p0

    if-eqz p0, :cond_0

    const-string p0, "lit"

    .line 1285
    invoke-virtual {v0, p0}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const-string p1, "name"

    .line 1286
    invoke-virtual {v0, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 1287
    invoke-static {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p0

    .line 1288
    invoke-interface {p3, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/4 p0, 0x1

    return p0

    :cond_0
    const/4 p0, 0x0

    return p0
.end method

.method public static getGrammarTypeToFileNameSuffix(I)Ljava/lang/String;
    .locals 2

    const/16 v0, 0x1f

    if-eq p0, v0, :cond_1

    const/16 v0, 0x2c

    const-string v1, "Parser"

    if-eq p0, v0, :cond_0

    const/16 v0, 0x51

    if-eq p0, v0, :cond_0

    const-string p0, "<invalid>"

    return-object p0

    :cond_0
    return-object v1

    :cond_1
    const-string p0, "Lexer"

    return-object p0
.end method

.method public static getStateToGrammarRegionMap(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/Map;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Interval;",
            ">;"
        }
    .end annotation

    .line 1313
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    if-nez p0, :cond_0

    return-object v0

    .line 1316
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getNodesWithType(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/List;

    move-result-object p1

    .line 1317
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 1318
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    if-eqz v2, :cond_1

    .line 1319
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStartIndex()I

    move-result v2

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStopIndex()I

    move-result v3

    invoke-static {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v2

    const/4 v3, 0x0

    .line 1322
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v4

    const/16 v5, 0x4e

    const/16 v6, 0x5e

    if-eq v4, v5, :cond_3

    const/16 v5, 0x50

    if-eq v4, v5, :cond_3

    if-eq v4, v6, :cond_2

    goto :goto_1

    :cond_2
    move-object v3, v1

    goto :goto_1

    .line 1328
    :cond_3
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAncestor(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    .line 1331
    :goto_1
    instance-of v4, v3, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    if-eqz v4, :cond_4

    .line 1332
    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getRuleName()Ljava/lang/String;

    move-result-object v3

    .line 1333
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v4, v3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v3

    .line 1334
    instance-of v4, v3, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;

    if-eqz v4, :cond_4

    .line 1335
    check-cast v3, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->getOriginalAST()Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    move-result-object v2

    .line 1336
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getTokenStartIndex()I

    move-result v3

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getTokenStopIndex()I

    move-result v2

    invoke-static {v3, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v2

    .line 1339
    :cond_4
    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_5
    return-object v0
.end method

.method public static getStringLiteralAliasesFromLexerRules(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)Ljava/util/List;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;>;"
        }
    .end annotation

    const-string v0, "(RULE %name:TOKEN_REF (BLOCK (ALT %lit:STRING_LITERAL)))"

    const-string v1, "(RULE %name:TOKEN_REF (BLOCK (ALT %lit:STRING_LITERAL ACTION)))"

    const-string v2, "(RULE %name:TOKEN_REF (BLOCK (ALT %lit:STRING_LITERAL SEMPRED)))"

    const-string v3, "(RULE %name:TOKEN_REF (BLOCK (LEXER_ALT_ACTION (ALT %lit:STRING_LITERAL) .)))"

    const-string v4, "(RULE %name:TOKEN_REF (BLOCK (LEXER_ALT_ACTION (ALT %lit:STRING_LITERAL) . .)))"

    const-string v5, "(RULE %name:TOKEN_REF (BLOCK (LEXER_ALT_ACTION (ALT %lit:STRING_LITERAL) (LEXER_ACTION_CALL . .))))"

    const-string v6, "(RULE %name:TOKEN_REF (BLOCK (LEXER_ALT_ACTION (ALT %lit:STRING_LITERAL) . (LEXER_ACTION_CALL . .))))"

    const-string v7, "(RULE %name:TOKEN_REF (BLOCK (LEXER_ALT_ACTION (ALT %lit:STRING_LITERAL) (LEXER_ACTION_CALL . .) .)))"

    .line 1242
    filled-new-array/range {v0 .. v7}, [Ljava/lang/String;

    move-result-object v0

    .line 1253
    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v2

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    .line 1254
    new-instance v2, Lgroovyjarjarantlr4/runtime/tree/TreeWizard;

    sget-object v3, Lgroovyjarjarantlr4/v4/parse/ANTLRParser;->tokenNames:[Ljava/lang/String;

    invoke-direct {v2, v1, v3}, Lgroovyjarjarantlr4/runtime/tree/TreeWizard;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;[Ljava/lang/String;)V

    .line 1255
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    const/16 v3, 0x5e

    .line 1258
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object p0

    if-eqz p0, :cond_4

    .line 1259
    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_2

    .line 1261
    :cond_0
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_1
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/4 v4, 0x0

    .line 1264
    invoke-virtual {v3, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    .line 1265
    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v5

    const/16 v6, 0x42

    if-ne v5, v6, :cond_1

    :goto_1
    const/16 v5, 0x8

    if-ge v4, v5, :cond_1

    .line 1268
    aget-object v5, v0, v4

    .line 1269
    invoke-static {v3, v5, v2, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defAlias(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/tree/TreeWizard;Ljava/util/List;)Z

    move-result v5

    if-eqz v5, :cond_2

    goto :goto_0

    :cond_2
    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_3
    return-object v1

    :cond_4
    :goto_2
    const/4 p0, 0x0

    return-object p0
.end method

.method public static isTokenName(Ljava/lang/String;)Z
    .locals 1

    const/4 v0, 0x0

    .line 1200
    invoke-virtual {p0, v0}, Ljava/lang/String;->charAt(I)C

    move-result p0

    invoke-static {p0}, Ljava/lang/Character;->isUpperCase(C)Z

    move-result p0

    return p0
.end method

.method public static load(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Grammar;
    .locals 1

    .line 597
    new-instance v0, Lgroovyjarjarantlr4/v4/Tool;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/Tool;-><init>()V

    .line 598
    invoke-virtual {v0, p0}, Lgroovyjarjarantlr4/v4/Tool;->loadGrammar(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p0

    return-object p0
.end method

.method public static setNodeOptions(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 3

    if-nez p1, :cond_0

    return-void

    .line 1227
    :cond_0
    check-cast p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    .line 1228
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->getChildCount()I

    move-result v0

    if-eqz v0, :cond_3

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v0

    if-nez v0, :cond_1

    goto :goto_1

    .line 1229
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    .line 1230
    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 1231
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v1

    const/16 v2, 0xa

    if-ne v1, v2, :cond_2

    const/4 v1, 0x0

    .line 1232
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p0, v1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->setOption(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    goto :goto_0

    .line 1235
    :cond_2
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->setOption(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    goto :goto_0

    :cond_3
    :goto_1
    return-void
.end method


# virtual methods
.method public createGrammarParserInterpreter(Lgroovyjarjarantlr4/v4/runtime/TokenStream;)Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;
    .locals 2

    .line 1375
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-nez v0, :cond_0

    .line 1379
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRuleNames()[Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getSerializedAsChars(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)[C

    move-result-object v0

    .line 1380
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;-><init>()V

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->deserialize([C)Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v0

    .line 1381
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;

    invoke-direct {v1, p0, v0, p1}, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    return-object v1

    .line 1376
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "A parser interpreter can only be created for a parser or combined grammar."

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public createLexerInterpreter(Lgroovyjarjarantlr4/v4/runtime/CharStream;)Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;
    .locals 10

    .line 1356
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isParser()Z

    move-result v0

    if-nez v0, :cond_1

    .line 1360
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isCombined()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1361
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->implicitLexer:Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->createLexerInterpreter(Lgroovyjarjarantlr4/v4/runtime/CharStream;)Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;

    move-result-object p1

    return-object p1

    .line 1364
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRuleNames()[Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getSerializedAsChars(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)[C

    move-result-object v0

    .line 1365
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;-><init>()V

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->deserialize([C)Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v8

    .line 1366
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    const-string v0, "DEFAULT_TOKEN_CHANNEL"

    .line 1367
    invoke-interface {v6, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string v0, "HIDDEN"

    .line 1368
    invoke-interface {v6, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 1369
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-interface {v6, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 1370
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v4

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRuleNames()[Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v5

    move-object v1, p0

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->keySet()Ljava/util/Set;

    move-result-object v7

    move-object v2, v0

    move-object v9, p1

    invoke-direct/range {v2 .. v9}, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;-><init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    return-object v0

    .line 1357
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "A lexer interpreter can only be created for a lexer or combined grammar."

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public createParserInterpreter(Lgroovyjarjarantlr4/v4/runtime/TokenStream;)Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;
    .locals 8

    .line 1385
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-nez v0, :cond_0

    .line 1389
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRuleNames()[Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getSerializedAsChars(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)[C

    move-result-object v0

    .line 1390
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;-><init>()V

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->deserialize([C)Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v6

    .line 1391
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v4

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRuleNames()[Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v5

    move-object v2, v0

    move-object v7, p1

    invoke-direct/range {v2 .. v7}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;-><init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    return-object v0

    .line 1386
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "A parser interpreter can only be created for a parser or combined grammar."

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public defineAction(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 5

    .line 431
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    const/4 v3, 0x2

    if-ne v0, v3, :cond_0

    .line 432
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v0

    .line 433
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->namedActions:Ljava/util/Map;

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {v2, v0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 436
    :cond_0
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v0

    .line 437
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTypeString()Ljava/lang/String;

    move-result-object v2

    .line 438
    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_1

    const-string v4, "parser"

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    const-string v0, "combined"

    invoke-virtual {v2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 439
    :cond_1
    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v0

    .line 440
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->namedActions:Ljava/util/Map;

    invoke-virtual {p1, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {v1, v0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    :goto_0
    return-void
.end method

.method public defineChannelName(Ljava/lang/String;)I
    .locals 1

    .line 1092
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelNameToValueMap:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    if-nez v0, :cond_0

    .line 1094
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getNewChannelNumber()I

    move-result v0

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineChannelName(Ljava/lang/String;I)I

    move-result p1

    return p1

    .line 1097
    :cond_0
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1
.end method

.method public defineChannelName(Ljava/lang/String;I)I
    .locals 2

    .line 1111
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelNameToValueMap:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    if-eqz v0, :cond_0

    .line 1113
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1

    .line 1116
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelNameToValueMap:Ljava/util/Map;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1117
    invoke-virtual {p0, p2, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->setChannelNameForValue(ILjava/lang/String;)V

    .line 1118
    iget p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxChannelType:I

    invoke-static {p1, p2}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxChannelType:I

    return p2
.end method

.method public defineRule(Lgroovyjarjarantlr4/v4/tool/Rule;)Z
    .locals 2

    .line 456
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    return p1

    .line 460
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 461
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ruleNumber:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ruleNumber:I

    iput v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    .line 462
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToRule:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/4 p1, 0x1

    return p1
.end method

.method public defineStringLiteral(Ljava/lang/String;)I
    .locals 1

    .line 1036
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1037
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1

    .line 1039
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getNewTokenType()I

    move-result v0

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineStringLiteral(Ljava/lang/String;I)I

    move-result p1

    return p1
.end method

.method public defineStringLiteral(Ljava/lang/String;I)I
    .locals 2

    .line 1044
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 1045
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1047
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lt p2, v0, :cond_0

    .line 1048
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    add-int/lit8 v1, p2, 0x1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/misc/Utils;->setSize(Ljava/util/List;I)V

    .line 1050
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-interface {v0, p2, p1}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 1052
    invoke-virtual {p0, p2, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->setTokenForType(ILjava/lang/String;)V

    return p2

    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method public defineTokenAlias(Ljava/lang/String;Ljava/lang/String;)I
    .locals 3

    .line 1059
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineTokenName(Ljava/lang/String;)I

    move-result v0

    .line 1060
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v1, p2, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1061
    invoke-virtual {p0, v0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->setTokenForType(ILjava/lang/String;)V

    return v0
.end method

.method public defineTokenName(Ljava/lang/String;)I
    .locals 1

    .line 1021
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    if-nez v0, :cond_0

    .line 1022
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getNewTokenType()I

    move-result v0

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineTokenName(Ljava/lang/String;I)I

    move-result p1

    return p1

    .line 1023
    :cond_0
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1
.end method

.method public defineTokenName(Ljava/lang/String;I)I
    .locals 2

    .line 1027
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    if-eqz v0, :cond_0

    .line 1028
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1

    .line 1029
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1030
    invoke-virtual {p0, p2, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->setTokenForType(ILjava/lang/String;)V

    .line 1031
    iget p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxTokenType:I

    invoke-static {p1, p2}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxTokenType:I

    return p2
.end method

.method public getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;
    .locals 1

    .line 525
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    if-nez v0, :cond_0

    .line 526
    new-instance v0, Lgroovyjarjarantlr4/v4/automata/ParserATNFactory;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/automata/ParserATNFactory;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 527
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/automata/ParserATNFactory;->createATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 529
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    return-object v0
.end method

.method public getAllCharValues()Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;
    .locals 2

    .line 960
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getMaxCharValue()I

    move-result v0

    const/4 v1, 0x0

    invoke-static {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    return-object v0
.end method

.method public getAllImportedGrammars()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ">;"
        }
    .end annotation

    .line 571
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->importedGrammars:Ljava/util/List;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 575
    :cond_0
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    .line 576
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->importedGrammars:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 577
    iget-object v3, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {v0, v3, v2}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 578
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getAllImportedGrammars()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 580
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 581
    iget-object v4, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {v0, v4, v3}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 586
    :cond_2
    new-instance v1, Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-direct {v1, v0}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    return-object v1
.end method

.method protected getBaseContextName(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    .line 546
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/Rule;

    if-eqz v0, :cond_0

    .line 548
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object p1

    :cond_0
    return-object p1
.end method

.method public getChannelValue(Ljava/lang/String;)I
    .locals 1

    .line 775
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelNameToValueMap:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    if-eqz p1, :cond_0

    .line 776
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    goto :goto_0

    :cond_0
    const/4 p1, -0x1

    :goto_0
    return p1
.end method

.method public getDefaultActionScope()Ljava/lang/String;
    .locals 2

    .line 1174
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getType()I

    move-result v0

    const/16 v1, 0x1f

    if-eq v0, v1, :cond_1

    const/16 v1, 0x2c

    if-eq v0, v1, :cond_0

    const/16 v1, 0x51

    if-eq v0, v1, :cond_0

    const/4 v0, 0x0

    return-object v0

    :cond_0
    const-string v0, "parser"

    return-object v0

    :cond_1
    const-string v0, "lexer"

    return-object v0
.end method

.method public getGrammarAncestors()Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ">;"
        }
    .end annotation

    .line 605
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOutermostGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    if-ne p0, v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 607
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 609
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->parent:Lgroovyjarjarantlr4/v4/tool/Grammar;

    :goto_0
    if-eqz v1, :cond_1

    const/4 v2, 0x0

    .line 611
    invoke-interface {v0, v2, v1}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 612
    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->parent:Lgroovyjarjarantlr4/v4/tool/Grammar;

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getImplicitLexer()Lgroovyjarjarantlr4/v4/tool/LexerGrammar;
    .locals 1

    .line 592
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->implicitLexer:Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    return-object v0
.end method

.method public getImportedGrammar(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Grammar;
    .locals 3

    .line 667
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->importedGrammars:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 668
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    return-object v1

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public getImportedGrammars()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ">;"
        }
    .end annotation

    .line 589
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->importedGrammars:Ljava/util/List;

    return-object v0
.end method

.method public getIndexToPredicateMap()Ljava/util/LinkedHashMap;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/tool/ast/PredAST;",
            ">;"
        }
    .end annotation

    .line 915
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    .line 916
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 917
    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->actions:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 918
    instance-of v4, v3, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    if-eqz v4, :cond_1

    .line 919
    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    .line 920
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->sempreds:Ljava/util/LinkedHashMap;

    invoke-virtual {v4, v3}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-virtual {v0, v4, v3}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_2
    return-object v0
.end method

.method public getLabeledAlternatives(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;>;>;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 561
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;

    new-instance v1, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    new-instance v2, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    invoke-direct {v1, v2, p1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/Object;)V

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V

    .line 562
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->rule()Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$rule_return;

    .line 563
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->getLabeledAlternatives()Ljava/util/Map;

    move-result-object p1

    return-object p1
.end method

.method public getMaxCharValue()I
    .locals 1

    const v0, 0x10ffff

    return v0
.end method

.method public getMaxTokenType()I
    .locals 1

    .line 965
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    return v0
.end method

.method public getNewChannelNumber()I
    .locals 1

    .line 976
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxChannelType:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxChannelType:I

    return v0
.end method

.method public getNewTokenType()I
    .locals 1

    .line 970
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxTokenType:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxTokenType:I

    return v0
.end method

.method public getOptionString(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    .line 1220
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getOutermostGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;
    .locals 1

    .line 621
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->parent:Lgroovyjarjarantlr4/v4/tool/Grammar;

    if-nez v0, :cond_0

    return-object p0

    .line 622
    :cond_0
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOutermostGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    return-object v0
.end method

.method public getPredicateDisplayString(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Predicate;)Ljava/lang/String;
    .locals 1

    .line 928
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToPredMap:Ljava/util/LinkedHashMap;

    if-nez v0, :cond_0

    .line 929
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getIndexToPredicateMap()Ljava/util/LinkedHashMap;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToPredMap:Ljava/util/LinkedHashMap;

    .line 931
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToPredMap:Ljava/util/LinkedHashMap;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Predicate;->predIndex:I

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 932
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getText()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getRecognizerName()Ljava/lang/String;
    .locals 4

    .line 636
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOutermostGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getGrammarAncestors()Ljava/util/List;

    move-result-object v0

    .line 637
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    const-string v2, "Abstract"

    if-eqz v0, :cond_2

    .line 639
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 640
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 641
    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v3, 0x5f

    .line 642
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 644
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isAbstract()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 645
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 647
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 648
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    .line 650
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isAbstract()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 651
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 654
    :cond_3
    :goto_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isCombined()Z

    move-result v0

    if-nez v0, :cond_5

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_4

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->implicitLexer:Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    if-eqz v0, :cond_4

    goto :goto_2

    :cond_4
    const-string v0, ""

    goto :goto_3

    .line 656
    :cond_5
    :goto_2
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getType()I

    move-result v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getGrammarTypeToFileNameSuffix(I)Ljava/lang/String;

    move-result-object v0

    .line 658
    :goto_3
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getRule(I)Lgroovyjarjarantlr4/v4/tool/Rule;
    .locals 1

    .line 532
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToRule:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/Rule;

    return-object p1
.end method

.method public getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;
    .locals 1

    .line 510
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/Rule;

    if-eqz p1, :cond_0

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public getRule(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;
    .locals 0

    if-eqz p1, :cond_1

    .line 536
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getImportedGrammar(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 540
    :cond_0
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/Rule;

    return-object p1

    .line 542
    :cond_1
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    return-object p1
.end method

.method public getRuleNames()[Ljava/lang/String;
    .locals 4

    .line 791
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->size()I

    move-result v0

    new-array v0, v0, [Ljava/lang/String;

    const-string v1, "<invalid>"

    .line 792
    invoke-static {v0, v1}, Ljava/util/Arrays;->fill([Ljava/lang/Object;Ljava/lang/Object;)V

    .line 793
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 794
    iget v3, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v2, v0, v3

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getSemanticContextDisplayString(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;)Ljava/lang/String;
    .locals 1

    .line 887
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Predicate;

    if-eqz v0, :cond_0

    .line 888
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Predicate;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getPredicateDisplayString(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Predicate;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 890
    :cond_0
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;

    if-eqz v0, :cond_1

    .line 891
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;

    const-string v0, " and "

    .line 892
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->joinPredicateOperands(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Operator;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 894
    :cond_1
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$OR;

    if-eqz v0, :cond_2

    .line 895
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$OR;

    const-string v0, " or "

    .line 896
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->joinPredicateOperands(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Operator;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 898
    :cond_2
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getStateToGrammarRegion(I)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;
    .locals 2

    .line 1347
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stateToGrammarRegionMap:Ljava/util/Map;

    if-nez v0, :cond_0

    .line 1348
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getStateToGrammarRegionMap(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/Map;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stateToGrammarRegionMap:Ljava/util/Map;

    .line 1350
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stateToGrammarRegionMap:Ljava/util/Map;

    if-nez v0, :cond_1

    sget-object p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->INVALID:Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    return-object p1

    .line 1352
    :cond_1
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    return-object p1
.end method

.method public getStringLiteralLexerRuleName(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 662
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "T__"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralRuleNumber:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralRuleNumber:I

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getStringLiterals()Ljava/util/Set;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 1295
    new-instance v0, Ljava/util/LinkedHashSet;

    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 1296
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Grammar$2;

    invoke-direct {v1, p0, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar$2;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/Set;)V

    .line 1304
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;->visitGrammar(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-object v0
.end method

.method public getTokenDisplayName(I)Ljava/lang/String;
    .locals 1

    .line 702
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_0

    if-ltz p1, :cond_0

    const v0, 0x10ffff

    if-gt p1, v0, :cond_0

    .line 705
    invoke-static {p1}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->getANTLRCharLiteralForChar(I)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 v0, -0x1

    if-ne p1, v0, :cond_1

    const-string p1, "EOF"

    return-object p1

    :cond_1
    if-nez p1, :cond_2

    const-string p1, "<INVALID>"

    return-object p1

    :cond_2
    if-ltz p1, :cond_3

    .line 716
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_3

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_3

    .line 717
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    return-object p1

    :cond_3
    if-ltz p1, :cond_4

    .line 720
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_4

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_4

    .line 721
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    return-object p1

    .line 724
    :cond_4
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getTokenDisplayNames()[Ljava/lang/String;
    .locals 4

    .line 827
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getMaxTokenType()I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    .line 828
    new-array v1, v0, [Ljava/lang/String;

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    .line 830
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenDisplayName(I)Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method public getTokenLiteralNames()[Ljava/lang/String;
    .locals 5

    .line 841
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getMaxTokenType()I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    .line 842
    new-array v1, v0, [Ljava/lang/String;

    const/4 v2, 0x0

    .line 843
    :goto_0
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    invoke-static {v0, v3}, Ljava/lang/Math;->min(II)I

    move-result v3

    if-ge v2, v3, :cond_0

    .line 844
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 847
    :cond_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 848
    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    if-ltz v4, :cond_1

    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    if-ge v4, v0, :cond_1

    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    aget-object v4, v1, v4

    if-nez v4, :cond_1

    .line 849
    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    aput-object v3, v1, v4

    goto :goto_1

    :cond_2
    return-object v1
.end method

.method public getTokenName(I)Ljava/lang/String;
    .locals 1

    .line 743
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_0

    if-ltz p1, :cond_0

    const v0, 0x10ffff

    if-gt p1, v0, :cond_0

    .line 746
    invoke-static {p1}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->getANTLRCharLiteralForChar(I)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 v0, -0x1

    if-ne p1, v0, :cond_1

    const-string p1, "EOF"

    return-object p1

    :cond_1
    if-ltz p1, :cond_2

    .line 753
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_2

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 754
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    return-object p1

    :cond_2
    const-string p1, "<INVALID>"

    return-object p1
.end method

.method public getTokenName(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    move-object v0, p0

    :goto_0
    if-eqz v0, :cond_1

    .line 689
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v1, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 690
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v1, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenName(I)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 691
    :cond_0
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->parent:Lgroovyjarjarantlr4/v4/tool/Grammar;

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public getTokenNames()[Ljava/lang/String;
    .locals 4

    .line 809
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getMaxTokenType()I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    .line 810
    new-array v1, v0, [Ljava/lang/String;

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    .line 812
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenName(I)Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method public getTokenStream()Lgroovyjarjarantlr4/runtime/TokenStream;
    .locals 1

    .line 1190
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-eqz v0, :cond_0

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public getTokenSymbolicNames()[Ljava/lang/String;
    .locals 5

    .line 861
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getMaxTokenType()I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    .line 862
    new-array v1, v0, [Ljava/lang/String;

    const/4 v2, 0x0

    .line 863
    :goto_0
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    invoke-static {v0, v3}, Ljava/lang/Math;->min(II)I

    move-result v3

    if-ge v2, v3, :cond_2

    .line 864
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    if-eqz v3, :cond_1

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    const-string v4, "T__"

    invoke-virtual {v3, v4}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_1

    .line 868
    :cond_0
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    aput-object v3, v1, v2

    :cond_1
    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    return-object v1
.end method

.method public getTokenType(Ljava/lang/String;)I
    .locals 3

    const/4 v0, 0x0

    .line 675
    invoke-virtual {p1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v2, 0x27

    if-ne v1, v2, :cond_0

    .line 676
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v1, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    goto :goto_0

    .line 679
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    invoke-interface {v1, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    :goto_0
    if-eqz p1, :cond_1

    .line 681
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result v0

    :cond_1
    return v0
.end method

.method public getTokenTypes()Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;
    .locals 2

    .line 950
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 951
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getAllCharValues()Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x1

    .line 953
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getMaxTokenType()I

    move-result v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    return-object v0
.end method

.method public getType()I
    .locals 1

    .line 1185
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-eqz v0, :cond_0

    iget v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->grammarType:I

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public getTypeString()Ljava/lang/String;
    .locals 2

    .line 1204
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 1205
    :cond_0
    sget-object v0, Lgroovyjarjarantlr4/v4/parse/ANTLRParser;->tokenNames:[Ljava/lang/String;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getType()I

    move-result v1

    aget-object v0, v0, v1

    invoke-virtual {v0}, Ljava/lang/String;->toLowerCase()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getUnlabeledAlternatives(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 555
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;

    new-instance v1, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    new-instance v2, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    invoke-direct {v1, v2, p1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/Object;)V

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V

    .line 556
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->rule()Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$rule_return;

    .line 557
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->getUnlabeledAlternatives()Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;
    .locals 3

    .line 880
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/VocabularyImpl;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenLiteralNames()[Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenSymbolicNames()[Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/VocabularyImpl;-><init>([Ljava/lang/String;[Ljava/lang/String;)V

    return-object v0
.end method

.method public importTokensFromTokensFile()V
    .locals 5

    const-string v0, "tokenVocab"

    .line 981
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 983
    new-instance v0, Lgroovyjarjarantlr4/v4/parse/TokenVocabParser;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/parse/TokenVocabParser;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 984
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/parse/TokenVocabParser;->load()Ljava/util/Map;

    move-result-object v0

    .line 985
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "tokens="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const-string v3, "grammar"

    invoke-virtual {v1, v3, v2}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 986
    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    const/4 v3, 0x0

    .line 987
    invoke-virtual {v2, v3}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v4, 0x27

    if-ne v3, v4, :cond_0

    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    invoke-virtual {p0, v2, v3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineStringLiteral(Ljava/lang/String;I)I

    goto :goto_0

    .line 988
    :cond_0
    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    invoke-virtual {p0, v2, v3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineTokenName(Ljava/lang/String;I)I

    goto :goto_0

    :cond_1
    return-void
.end method

.method public importVocab(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 4

    .line 994
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    .line 995
    iget-object v2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    invoke-virtual {p0, v1, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineTokenName(Ljava/lang/String;I)I

    goto :goto_0

    .line 997
    :cond_0
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    .line 998
    iget-object v2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    invoke-virtual {p0, v1, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineStringLiteral(Ljava/lang/String;I)I

    goto :goto_1

    .line 1000
    :cond_1
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelNameToValueMap:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 1001
    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Integer;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    invoke-virtual {p0, v2, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineChannelName(Ljava/lang/String;I)I

    goto :goto_2

    .line 1005
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v0

    .line 1006
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-static {v1, v0}, Lgroovyjarjarantlr4/v4/misc/Utils;->setSize(Ljava/util/List;I)V

    const/4 v0, 0x0

    move v1, v0

    .line 1007
    :goto_3
    iget-object v2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_3

    .line 1008
    iget v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxTokenType:I

    invoke-static {v2, v1}, Ljava/lang/Math;->max(II)I

    move-result v2

    iput v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxTokenType:I

    .line 1009
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v1, v3}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    .line 1012
    :cond_3
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    iget-object v2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    move-result v1

    .line 1013
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-static {v2, v1}, Lgroovyjarjarantlr4/v4/misc/Utils;->setSize(Ljava/util/List;I)V

    .line 1014
    :goto_4
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_4

    .line 1015
    iget v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxChannelType:I

    invoke-static {v1, v0}, Ljava/lang/Math;->max(II)I

    move-result v1

    iput v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->maxChannelType:I

    .line 1016
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    iget-object v2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v1, v0, v2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v0, v0, 0x1

    goto :goto_4

    :cond_4
    return-void
.end method

.method protected initTokenSymbolTables()V
    .locals 3

    .line 384
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    const/4 v1, -0x1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "EOF"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 387
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public isAbstract()Z
    .locals 1

    const-string v0, "abstract"

    .line 626
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v0

    return v0
.end method

.method public isCombined()Z
    .locals 2

    .line 1196
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getType()I

    move-result v0

    const/16 v1, 0x51

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public isLexer()Z
    .locals 2

    .line 1194
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getType()I

    move-result v0

    const/16 v1, 0x1f

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public isParser()Z
    .locals 2

    .line 1195
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getType()I

    move-result v0

    const/16 v1, 0x2c

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public joinPredicateOperands(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Operator;Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    .line 902
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 903
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Operator;->getOperands()Ljava/util/Collection;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    .line 904
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->length()I

    move-result v2

    if-lez v2, :cond_0

    .line 905
    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 908
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getSemanticContextDisplayString(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 911
    :cond_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public loadImportedGrammars()V
    .locals 9

    .line 391
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/16 v1, 0x1d

    .line 392
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-nez v0, :cond_1

    return-void

    .line 394
    :cond_1
    new-instance v1, Ljava/util/HashSet;

    invoke-direct {v1}, Ljava/util/HashSet;-><init>()V

    .line 395
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-interface {v1, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 396
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    iput-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->importedGrammars:Ljava/util/List;

    .line 397
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_6

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 398
    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/4 v3, 0x0

    .line 400
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v4

    const/16 v5, 0xa

    const/4 v6, 0x1

    if-ne v4, v5, :cond_2

    .line 401
    invoke-virtual {v2, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 402
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v3

    goto :goto_1

    .line 404
    :cond_2
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v4

    const/16 v5, 0x1c

    if-ne v4, v5, :cond_3

    .line 405
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v3

    .line 407
    :cond_3
    :goto_1
    invoke-interface {v1, v3}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_4

    goto :goto_0

    .line 412
    :cond_4
    :try_start_0
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v4, p0, v2}, Lgroovyjarjarantlr4/v4/Tool;->loadImportedGrammar(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v2
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    if-nez v2, :cond_5

    goto :goto_0

    .line 424
    :cond_5
    iput-object p0, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->parent:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 425
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->importedGrammars:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 426
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->loadImportedGrammars()V

    goto :goto_0

    .line 415
    :catch_0
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v5, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ERROR_READING_IMPORTED_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v2

    const/4 v7, 0x2

    new-array v7, v7, [Ljava/lang/Object;

    const/4 v8, 0x0

    aput-object v3, v7, v8

    iget-object v8, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    aput-object v8, v7, v6

    invoke-virtual {v4, v5, v3, v2, v7}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_6
    return-void
.end method

.method public resolveToAttribute(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public resolveToAttribute(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public resolvesToAttributeDict(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public resolvesToLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public resolvesToListLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public resolvesToToken(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public setChannelNameForValue(ILjava/lang/String;)V
    .locals 2

    .line 1133
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lt p1, v0, :cond_0

    .line 1134
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    add-int/lit8 v1, p1, 0x1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/misc/Utils;->setSize(Ljava/util/List;I)V

    .line 1137
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    if-nez v0, :cond_1

    .line 1139
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    :cond_1
    return-void
.end method

.method public setLookaheadDFA(ILgroovyjarjarantlr4/v4/runtime/dfa/DFA;)V
    .locals 1

    .line 1309
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->decisionDFAs:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setTokenForType(ILjava/lang/String;)V
    .locals 2

    const/4 v0, -0x1

    if-ne p1, v0, :cond_0

    return-void

    .line 1071
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lt p1, v0, :cond_1

    .line 1072
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    add-int/lit8 v1, p1, 0x1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/misc/Utils;->setSize(Ljava/util/List;I)V

    .line 1074
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    if-eqz v0, :cond_2

    const/4 v1, 0x0

    .line 1075
    invoke-virtual {v0, v1}, Ljava/lang/String;->charAt(I)C

    move-result v0

    const/16 v1, 0x27

    if-ne v0, v1, :cond_3

    .line 1077
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToTokenList:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    :cond_3
    return-void
.end method

.method public undefineRule(Lgroovyjarjarantlr4/v4/tool/Rule;)Z
    .locals 3

    .line 483
    iget v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    if-ltz v0, :cond_2

    iget v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToRule:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_2

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToRule:Ljava/util/List;

    iget v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    if-eq v0, p1, :cond_0

    goto :goto_1

    .line 489
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 490
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToRule:Ljava/util/List;

    iget v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    invoke-interface {v0, v1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    .line 491
    iget p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToRule:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    if-ge p1, v0, :cond_1

    .line 493
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->indexToRule:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/Rule;

    iget v2, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    sub-int/2addr v2, v1

    iput v2, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 496
    :cond_1
    iget p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ruleNumber:I

    sub-int/2addr p1, v1

    iput p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ruleNumber:I

    return v1

    :cond_2
    :goto_1
    const/4 p1, 0x0

    return p1
.end method
