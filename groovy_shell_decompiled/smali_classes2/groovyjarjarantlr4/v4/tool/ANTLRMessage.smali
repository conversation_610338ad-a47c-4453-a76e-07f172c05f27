.class public Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;
.super Ljava/lang/Object;
.source "ANTLRMessage.java"


# static fields
.field private static final EMPTY_ARGS:[Ljava/lang/Object;


# instance fields
.field private final args:[Ljava/lang/Object;

.field public charPosition:I

.field private final e:Ljava/lang/Throwable;

.field private final errorType:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public fileName:Ljava/lang/String;

.field public g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public line:I

.field public offendingToken:Lgroovyjarjarantlr4/runtime/Token;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/Object;

    .line 17
    sput-object v0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->EMPTY_ARGS:[Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;)V
    .locals 3

    const/4 v0, 0x0

    .line 38
    move-object v1, v0

    check-cast v1, Ljava/lang/Throwable;

    sget-object v1, Lgroovyjarjarantlr4/runtime/Token;->INVALID_TOKEN:Lgroovyjarjarantlr4/runtime/Token;

    const/4 v2, 0x0

    new-array v2, v2, [Ljava/lang/Object;

    invoke-direct {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    return-void
.end method

.method public varargs constructor <init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V
    .locals 1

    const/4 v0, 0x0

    .line 42
    invoke-direct {p0, p1, v0, p2, p3}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    return-void
.end method

.method public varargs constructor <init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V
    .locals 1

    .line 45
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 28
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->line:I

    .line 29
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->charPosition:I

    .line 46
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->errorType:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 47
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->e:Ljava/lang/Throwable;

    .line 48
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->args:[Ljava/lang/Object;

    .line 49
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->offendingToken:Lgroovyjarjarantlr4/runtime/Token;

    return-void
.end method


# virtual methods
.method public getArgs()[Ljava/lang/Object;
    .locals 1

    .line 59
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->args:[Ljava/lang/Object;

    if-nez v0, :cond_0

    .line 60
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->EMPTY_ARGS:[Ljava/lang/Object;

    :cond_0
    return-object v0
.end method

.method public getCause()Ljava/lang/Throwable;
    .locals 1

    .line 94
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->e:Ljava/lang/Throwable;

    return-object v0
.end method

.method public getErrorType()Lgroovyjarjarantlr4/v4/tool/ErrorType;
    .locals 1

    .line 54
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->errorType:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    return-object v0
.end method

.method public getMessageTemplate(Z)Lorg/stringtemplate/v4/ST;
    .locals 4

    .line 67
    new-instance v0, Lorg/stringtemplate/v4/ST;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getErrorType()Lgroovyjarjarantlr4/v4/tool/ErrorType;

    move-result-object v1

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->msg:Ljava/lang/String;

    invoke-direct {v0, v1}, Lorg/stringtemplate/v4/ST;-><init>(Ljava/lang/String;)V

    .line 68
    iget-object v1, v0, Lorg/stringtemplate/v4/ST;->impl:Lorg/stringtemplate/v4/compiler/CompiledST;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->errorType:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ErrorType;->name()Ljava/lang/String;

    move-result-object v2

    iput-object v2, v1, Lorg/stringtemplate/v4/compiler/CompiledST;->name:Ljava/lang/String;

    .line 70
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    const-string v1, "verbose"

    invoke-virtual {v0, v1, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 71
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getArgs()[Ljava/lang/Object;

    move-result-object p1

    const/4 v1, 0x0

    .line 72
    :goto_0
    array-length v2, p1

    if-ge v1, v2, :cond_1

    const-string v2, "arg"

    if-lez v1, :cond_0

    .line 74
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    add-int/lit8 v3, v1, 0x1

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 75
    :cond_0
    aget-object v3, p1, v1

    invoke-virtual {v0, v2, v3}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 77
    :cond_1
    array-length p1, p1

    const/4 v1, 0x2

    const/4 v2, 0x0

    if-ge p1, v1, :cond_2

    const-string p1, "arg2"

    invoke-virtual {v0, p1, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 79
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getCause()Ljava/lang/Throwable;

    move-result-object p1

    const-string v1, "stackTrace"

    const-string v3, "exception"

    if-eqz p1, :cond_3

    .line 81
    invoke-virtual {v0, v3, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 82
    invoke-virtual {p1}, Ljava/lang/Throwable;->getStackTrace()[Ljava/lang/StackTraceElement;

    move-result-object p1

    invoke-virtual {v0, v1, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_1

    .line 85
    :cond_3
    invoke-virtual {v0, v3, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 86
    invoke-virtual {v0, v1, v2}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    :goto_1
    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 99
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Message{errorType="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getErrorType()Lgroovyjarjarantlr4/v4/tool/ErrorType;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", args="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getArgs()[Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", e="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getCause()Ljava/lang/Throwable;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", fileName=\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->fileName:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0x27

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", line="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->line:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", charPosition="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->charPosition:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
