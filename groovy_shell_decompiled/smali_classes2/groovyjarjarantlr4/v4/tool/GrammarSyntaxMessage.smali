.class public Lgroovyjarjarantlr4/v4/tool/GrammarSyntaxMessage;
.super Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;
.source "GrammarSyntaxMessage.java"


# direct methods
.method public varargs constructor <init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/Object;)V
    .locals 0

    .line 22
    invoke-direct {p0, p1, p4, p3, p5}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 23
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarSyntaxMessage;->fileName:Ljava/lang/String;

    .line 24
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/tool/GrammarSyntaxMessage;->offendingToken:Lgroovyjarjarantlr4/runtime/Token;

    if-eqz p3, :cond_0

    .line 26
    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarSyntaxMessage;->line:I

    .line 27
    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarSyntaxMessage;->charPosition:I

    :cond_0
    return-void
.end method


# virtual methods
.method public getCause()Lgroovyjarjarantlr4/runtime/RecognitionException;
    .locals 1

    .line 33
    invoke-super {p0}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getCause()Ljava/lang/Throwable;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/RecognitionException;

    return-object v0
.end method

.method public bridge synthetic getCause()Ljava/lang/Throwable;
    .locals 1

    .line 15
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/GrammarSyntaxMessage;->getCause()Lgroovyjarjarantlr4/runtime/RecognitionException;

    move-result-object v0

    return-object v0
.end method
