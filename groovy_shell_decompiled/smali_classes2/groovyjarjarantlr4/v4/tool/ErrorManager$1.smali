.class synthetic Lgroovyjarjarantlr4/v4/tool/ErrorManager$1;
.super Ljava/lang/Object;
.source "ErrorManager.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/tool/ErrorManager;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1008
    name = null
.end annotation


# static fields
.field static final synthetic $SwitchMap$org$antlr$v4$tool$ErrorSeverity:[I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 194
    invoke-static {}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->values()[Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    move-result-object v0

    array-length v0, v0

    new-array v0, v0, [I

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorManager$1;->$SwitchMap$org$antlr$v4$tool$ErrorSeverity:[I

    :try_start_0
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING_ONE_OFF:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ordinal()I

    move-result v1

    const/4 v2, 0x1

    aput v2, v0, v1
    :try_end_0
    .catch Ljava/lang/NoSuchFieldError; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :try_start_1
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorManager$1;->$SwitchMap$org$antlr$v4$tool$ErrorSeverity:[I

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ordinal()I

    move-result v1

    const/4 v2, 0x2

    aput v2, v0, v1
    :try_end_1
    .catch Ljava/lang/NoSuchFieldError; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    :try_start_2
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorManager$1;->$SwitchMap$org$antlr$v4$tool$ErrorSeverity:[I

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR_ONE_OFF:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ordinal()I

    move-result v1

    const/4 v2, 0x3

    aput v2, v0, v1
    :try_end_2
    .catch Ljava/lang/NoSuchFieldError; {:try_start_2 .. :try_end_2} :catch_2

    :catch_2
    :try_start_3
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorManager$1;->$SwitchMap$org$antlr$v4$tool$ErrorSeverity:[I

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ordinal()I

    move-result v1

    const/4 v2, 0x4

    aput v2, v0, v1
    :try_end_3
    .catch Ljava/lang/NoSuchFieldError; {:try_start_3 .. :try_end_3} :catch_3

    :catch_3
    return-void
.end method
