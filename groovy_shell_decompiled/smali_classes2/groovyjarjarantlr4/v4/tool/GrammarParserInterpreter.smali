.class public Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;
.super Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;
.source "GrammarParserInterpreter.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;
    }
.end annotation


# instance fields
.field protected decisionStatesThatSetOuterAltNumInContext:Ljava/util/BitSet;

.field protected final g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field protected stateToAltsMap:[[I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V
    .locals 6

    .line 71
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRuleNames()[Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    move-object v0, p0

    move-object v4, p2

    move-object v5, p3

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;-><init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    .line 75
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 76
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->findOuterMostDecisionStates()Ljava/util/BitSet;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->decisionStatesThatSetOuterAltNumInContext:Ljava/util/BitSet;

    .line 77
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    new-array p1, p1, [[I

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->stateToAltsMap:[[I

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/Vocabulary;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Lgroovyjarjarantlr4/v4/runtime/TokenStream;",
            ")V"
        }
    .end annotation

    move-object v0, p0

    move-object v1, p2

    move-object v2, p3

    move-object v3, p4

    move-object v4, p5

    move-object v5, p6

    .line 66
    invoke-direct/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;-><init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    .line 67
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    return-void
.end method

.method public static deriveTempParserInterpreter(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;
    .locals 7

    .line 393
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;

    if-eqz v0, :cond_0

    .line 394
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;

    invoke-virtual {v0, v1}, Ljava/lang/Class;->asSubclass(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v0

    const/4 v1, 0x3

    :try_start_0
    new-array v2, v1, [Ljava/lang/Class;

    .line 396
    const-class v3, Lgroovyjarjarantlr4/v4/tool/Grammar;

    const/4 v4, 0x0

    aput-object v3, v2, v4

    const-class v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    const/4 v5, 0x1

    aput-object v3, v2, v5

    const-class v3, Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    const/4 v6, 0x2

    aput-object v3, v2, v6

    invoke-virtual {v0, v2}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v0

    new-array v1, v1, [Ljava/lang/Object;

    aput-object p0, v1, v4

    .line 397
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object p0

    aput-object p0, v1, v5

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object p0

    aput-object p0, v1, v6

    invoke-virtual {v0, v1}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p0

    .line 400
    new-instance p2, Ljava/lang/IllegalArgumentException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "can\'t create parser to match incoming "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1, p0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw p2

    .line 404
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object p0

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleNames()[Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getSerializedAsChars(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)[C

    move-result-object p0

    .line 405
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;-><init>()V

    invoke-virtual {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->deserialize([C)Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v5

    .line 406
    new-instance p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getGrammarFileName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v3

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleNames()[Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v4

    move-object v1, p0

    move-object v6, p2

    invoke-direct/range {v1 .. v6}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;-><init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    .line 413
    :goto_0
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->setInputStream(Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    .line 416
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/BailErrorStrategy;

    invoke-direct {p1}, Lgroovyjarjarantlr4/v4/runtime/BailErrorStrategy;-><init>()V

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->setErrorHandler(Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;)V

    .line 417
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->removeErrorListeners()V

    .line 418
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->removeParseListeners()V

    .line 419
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    sget-object p2, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;->LL_EXACT_AMBIG_DETECTION:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->setPredictionMode(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;)V

    return-object p0
.end method

.method public static getAllPossibleParseTrees(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/TokenStream;ILjava/util/BitSet;III)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Lgroovyjarjarantlr4/v4/runtime/Parser;",
            "Lgroovyjarjarantlr4/v4/runtime/TokenStream;",
            "I",
            "Ljava/util/BitSet;",
            "III)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
        }
    .end annotation

    .line 280
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 282
    invoke-static {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->deriveTempParserInterpreter(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;

    move-result-object p0

    .line 284
    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->size()I

    move-result p1

    add-int/lit8 p1, p1, -0x1

    if-lt p6, p1, :cond_0

    .line 286
    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->size()I

    move-result p1

    add-int/lit8 p6, p1, -0x2

    :cond_0
    const/4 p1, 0x0

    .line 290
    invoke-virtual {p4, p1}, Ljava/util/BitSet;->nextSetBit(I)I

    move-result p1

    :goto_0
    if-ltz p1, :cond_2

    .line 295
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->reset()V

    .line 296
    invoke-virtual {p0, p3, p5, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->addDecisionOverride(III)V

    .line 297
    invoke-virtual {p0, p7}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->parse(I)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object p2

    .line 298
    invoke-static {p2, p5, p6}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->getRootOfSubtreeEnclosingRegion(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;II)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;

    .line 301
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getOverrideDecisionRoot()Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    move-result-object v1

    invoke-static {v1, p2}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->isAncestorOf(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 302
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getOverrideDecisionRoot()Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;

    .line 304
    :cond_1
    invoke-interface {v0, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 p1, p1, 0x1

    .line 305
    invoke-virtual {p4, p1}, Ljava/util/BitSet;->nextSetBit(I)I

    move-result p1

    goto :goto_0

    :cond_2
    return-object v0
.end method

.method public static getLookaheadParseTrees(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;Lgroovyjarjarantlr4/v4/runtime/TokenStream;IIII)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;",
            "Lgroovyjarjarantlr4/v4/runtime/TokenStream;",
            "IIII)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;",
            ">;"
        }
    .end annotation

    .line 344
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 346
    invoke-static {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->deriveTempParserInterpreter(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;

    move-result-object p0

    .line 348
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object p1

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {p1, p4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    const/4 p2, 0x1

    .line 350
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->getTransitions()[Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v1

    array-length v1, v1

    if-gt p2, v1, :cond_3

    .line 354
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;-><init>()V

    .line 356
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->setErrorHandler(Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;)V

    .line 357
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->reset()V

    .line 358
    invoke-virtual {p0, p4, p5, p2}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->addDecisionOverride(III)V

    .line 359
    invoke-virtual {p0, p3}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->parse(I)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v2

    .line 361
    iget v3, v1, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;->firstErrorTokenIndex:I

    if-ltz v3, :cond_0

    .line 362
    iget v1, v1, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;->firstErrorTokenIndex:I

    goto :goto_1

    :cond_0
    move v1, p6

    .line 364
    :goto_1
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getSourceInterval()Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v3

    .line 365
    iget v4, v3, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-le v1, v4, :cond_1

    .line 369
    iget v1, v3, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    .line 371
    :cond_1
    invoke-static {v2, p5, v1}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->getRootOfSubtreeEnclosingRegion(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;II)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v2

    .line 376
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getOverrideDecisionRoot()Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    move-result-object v3

    invoke-static {v3, v2}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->isAncestorOf(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Z

    move-result v3

    if-eqz v3, :cond_2

    .line 377
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getOverrideDecisionRoot()Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    move-result-object v2

    .line 379
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getOverrideDecisionRoot()Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    move-result-object v3

    invoke-static {v2, v3, p5, v1}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->stripChildrenOutOfRange(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V

    .line 380
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    :cond_3
    return-object v0
.end method


# virtual methods
.method protected createInterpreterRuleContext(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;
    .locals 1

    .line 85
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;

    invoke-direct {v0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V

    return-object v0
.end method

.method public findOuterMostDecisionStates()Ljava/util/BitSet;
    .locals 7

    .line 101
    new-instance v0, Ljava/util/BitSet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    invoke-direct {v0, v1}, Ljava/util/BitSet;-><init>(I)V

    .line 102
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->getNumberOfDecisions()I

    move-result v1

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_2

    .line 104
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {v4, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->getDecisionState(I)Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    move-result-object v4

    .line 105
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    iget v6, v4, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->ruleIndex:I

    aget-object v5, v5, v6

    .line 107
    instance-of v6, v4, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    if-eqz v6, :cond_0

    .line 108
    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    .line 109
    iget-boolean v5, v4, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;->precedenceRuleDecision:Z

    if-eqz v5, :cond_1

    .line 113
    invoke-virtual {v4, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v4

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 115
    iget v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v0, v4}, Ljava/util/BitSet;->set(I)V

    goto :goto_1

    .line 118
    :cond_0
    invoke-virtual {v5, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v5

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    if-ne v5, v4, :cond_1

    .line 120
    iget v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->stateNumber:I

    invoke-virtual {v0, v4}, Ljava/util/BitSet;->set(I)V

    :cond_1
    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    return-object v0
.end method

.method public reset()V
    .locals 1

    .line 90
    invoke-super {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->reset()V

    const/4 v0, 0x0

    .line 91
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->overrideDecisionRoot:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    return-void
.end method

.method protected visitDecisionState(Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;)I
    .locals 6

    .line 163
    invoke-super {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->visitDecisionState(Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;)I

    move-result v0

    .line 164
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->getNumberOfTransitions()I

    move-result v1

    const/4 v2, 0x1

    if-le v1, v2, :cond_0

    .line 166
    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    iget v2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->overrideDecision:I

    if-ne v1, v2, :cond_0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->index()I

    move-result v1

    iget v2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->overrideDecisionInputIndex:I

    if-ne v1, v2, :cond_0

    .line 169
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->getContext()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->overrideDecisionRoot:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    .line 173
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;

    .line 174
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->decisionStatesThatSetOuterAltNumInContext:Ljava/util/BitSet;

    iget v3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->stateNumber:I

    invoke-virtual {v2, v3}, Ljava/util/BitSet;->get(I)Z

    move-result v2

    if-eqz v2, :cond_3

    .line 175
    iput v0, v1, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;->outerAltNum:I

    .line 176
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget v3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->ruleIndex:I

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(I)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v2

    .line 177
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    iget v2, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    aget-object v2, v3, v2

    iget-boolean v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->isPrecedenceRule:Z

    if-eqz v2, :cond_3

    .line 178
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->stateToAltsMap:[[I

    iget v3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->stateNumber:I

    aget-object v2, v2, v3

    .line 179
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget v4, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->ruleIndex:I

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(I)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;

    .line 180
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->getStateType()I

    move-result v4

    const/4 v5, 0x3

    if-ne v4, v5, :cond_1

    if-nez v2, :cond_2

    .line 182
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->getPrimaryAlts()[I

    move-result-object v2

    .line 183
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->stateToAltsMap:[[I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->stateNumber:I

    aput-object v2, v3, p1

    goto :goto_0

    .line 186
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->getStateType()I

    move-result v4

    const/4 v5, 0x5

    if-ne v4, v5, :cond_2

    if-nez v2, :cond_2

    .line 188
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->getRecursiveOpAlts()[I

    move-result-object v2

    .line 189
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;->stateToAltsMap:[[I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->stateNumber:I

    aput-object v2, v3, p1

    .line 192
    :cond_2
    :goto_0
    aget p1, v2, v0

    iput p1, v1, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;->outerAltNum:I

    :cond_3
    return v0
.end method
