.class public Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;
.super Lgroovyjarjarantlr4/v4/tool/Rule;
.source "LeftRecursiveRule.java"


# instance fields
.field public leftRecursiveRuleRefLabels:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end field

.field public originalAST:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

.field public recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/misc/OrderedHashMap<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;",
            ">;"
        }
    .end annotation
.end field

.field public recPrimaryAlts:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)V
    .locals 1

    const/4 v0, 0x1

    .line 32
    invoke-direct {p0, p1, p2, p3, v0}, Lgroovyjarjarantlr4/v4/tool/Rule;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;I)V

    .line 28
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->leftRecursiveRuleRefLabels:Ljava/util/List;

    .line 33
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->originalAST:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 34
    iget p1, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->numberOfAlts:I

    add-int/2addr p1, v0

    new-array p1, p1, [Lgroovyjarjarantlr4/v4/tool/Alternative;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    .line 35
    :goto_0
    iget p1, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->numberOfAlts:I

    if-gt v0, p1, :cond_0

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    new-instance p2, Lgroovyjarjarantlr4/v4/tool/Alternative;

    invoke-direct {p2, p0, v0}, Lgroovyjarjarantlr4/v4/tool/Alternative;-><init>(Lgroovyjarjarantlr4/v4/tool/Rule;I)V

    aput-object p2, p1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public getAltLabels()Ljava/util/Map;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;>;>;"
        }
    .end annotation

    .line 119
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 120
    invoke-super {p0}, Lgroovyjarjarantlr4/v4/tool/Rule;->getAltLabels()Ljava/util/Map;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 121
    invoke-interface {v0, v1}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 122
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    if-eqz v1, :cond_3

    .line 123
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 124
    iget-object v3, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    if-eqz v3, :cond_1

    .line 125
    iget-object v3, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    invoke-interface {v0, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    if-nez v3, :cond_2

    .line 127
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 128
    iget-object v4, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    invoke-interface {v0, v4, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 131
    :cond_2
    iget v4, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altNum:I

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->originalAltAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-static {v4, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v2

    invoke-interface {v3, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 135
    :cond_3
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    if-eqz v1, :cond_6

    const/4 v1, 0x0

    .line 136
    :goto_1
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->size()I

    move-result v2

    if-ge v1, v2, :cond_6

    .line 137
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->getElement(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 138
    iget-object v3, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    if-eqz v3, :cond_5

    .line 139
    iget-object v3, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    invoke-interface {v0, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    if-nez v3, :cond_4

    .line 141
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 142
    iget-object v4, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    invoke-interface {v0, v4, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 145
    :cond_4
    iget v4, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altNum:I

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->originalAltAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-static {v4, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v2

    invoke-interface {v3, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_5
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 149
    :cond_6
    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_7

    const/4 v0, 0x0

    :cond_7
    return-object v0
.end method

.method public getOriginalAST()Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;
    .locals 1

    .line 52
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->originalAST:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    return-object v0
.end method

.method public getOriginalNumberOfAlts()I
    .locals 2

    .line 46
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/2addr v1, v0

    .line 47
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->size()I

    move-result v0

    add-int/2addr v1, v0

    :cond_1
    return v1
.end method

.method public getPrimaryAlts()[I
    .locals 3

    .line 83
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 84
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    new-array v0, v0, [I

    const/4 v1, 0x0

    .line 85
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_1

    .line 86
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    add-int/lit8 v1, v1, 0x1

    .line 87
    iget v2, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altNum:I

    aput v2, v0, v1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getRecursiveOpAlts()[I
    .locals 5

    .line 106
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->size()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 107
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->size()I

    move-result v0

    const/4 v1, 0x1

    add-int/2addr v0, v1

    new-array v0, v0, [I

    .line 109
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    move v3, v1

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_1

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 110
    iget v4, v4, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altNum:I

    aput v4, v0, v3

    add-int/2addr v3, v1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getUnlabeledAltASTs()Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;"
        }
    .end annotation

    .line 57
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 58
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 59
    iget-object v3, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    if-nez v3, :cond_0

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->originalAltAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    .line 61
    :goto_1
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->size()I

    move-result v2

    if-ge v1, v2, :cond_3

    .line 62
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->getElement(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 63
    iget-object v3, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    if-nez v3, :cond_2

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->originalAltAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 65
    :cond_3
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_4

    const/4 v0, 0x0

    :cond_4
    return-object v0
.end method

.method public hasAltSpecificContexts()Z
    .locals 1

    .line 40
    invoke-super {p0}, Lgroovyjarjarantlr4/v4/tool/Rule;->hasAltSpecificContexts()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->getAltLabels()Ljava/util/Map;

    move-result-object v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method
