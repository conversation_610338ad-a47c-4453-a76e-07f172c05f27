.class public Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;
.super Ljava/lang/Object;
.source "GrammarTransformPipeline.java"


# instance fields
.field public g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public tool:Lgroovyjarjarantlr4/v4/Tool;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/Tool;)V
    .locals 0

    .line 43
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 44
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 45
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->tool:Lgroovyjarjarantlr4/v4/Tool;

    return-void
.end method

.method public static augmentTokensWithOriginalPosition(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 6

    if-nez p1, :cond_0

    return-void

    :cond_0
    const/16 v0, 0x52

    .line 112
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object p1

    const/4 v0, 0x0

    .line 113
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_3

    .line 114
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 115
    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    .line 116
    instance-of v2, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    if-eqz v2, :cond_2

    .line 117
    move-object v2, v1

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->getOptions()Ljava/util/Map;

    move-result-object v2

    const-string v3, "tokenIndex"

    .line 118
    invoke-interface {v2, v3}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_2

    .line 119
    new-instance v4, Lgroovyjarjarantlr4/v4/parse/GrammarToken;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v5

    invoke-direct {v4, p0, v5}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/runtime/Token;)V

    .line 120
    invoke-interface {v2, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(Ljava/lang/String;)Ljava/lang/Integer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    iput v2, v4, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->originalTokenIndex:I

    .line 121
    iput-object v4, v1, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 123
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getTokenIndex()I

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getNodeWithTokenIndex(I)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 127
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStartIndex()I

    move-result v3

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->setTokenStartIndex(I)V

    .line 128
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStopIndex()I

    move-result v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->setTokenStopIndex(I)V

    goto :goto_1

    .line 134
    :cond_1
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getTokenIndex()I

    move-result v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->setTokenStartIndex(I)V

    .line 135
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getTokenIndex()I

    move-result v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->setTokenStopIndex(I)V

    :cond_2
    :goto_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method public static setGrammarPtr(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    .line 100
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;

    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;)V

    .line 101
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$2;

    invoke-direct {v1, p0}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$2;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;->visit(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeVisitorAction;)Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public expandParameterizedLoop(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 0

    return-object p1
.end method

.method public expandParameterizedLoops(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 2

    .line 77
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;

    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;)V

    .line 78
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$1;

    invoke-direct {v1, p0}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$1;-><init>(Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;)V

    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;->visit(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeVisitorAction;)Ljava/lang/Object;

    return-void
.end method

.method public extractImplicitLexer(Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;
    .locals 14

    .line 386
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    .line 388
    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v2

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    .line 389
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getChildren()Ljava/util/List;

    move-result-object v2

    const/4 v3, 0x0

    new-array v4, v3, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-interface {v2, v4}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 392
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, "Lexer"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .line 393
    new-instance v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    new-instance v6, Lgroovyjarjarantlr4/runtime/CommonToken;

    const/16 v7, 0x19

    const-string v8, "LEXER_GRAMMAR"

    invoke-direct {v6, v7, v8}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    iget-object v7, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    iget-object v7, v7, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    invoke-direct {v5, v6, v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/TokenStream;)V

    const/16 v6, 0x1f

    .line 395
    iput v6, v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->grammarType:I

    .line 396
    iget-object v6, v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    iget-object v7, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v7}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v7

    invoke-interface {v6, v7}, Lgroovyjarjarantlr4/runtime/Token;->setInputStream(Lgroovyjarjarantlr4/runtime/CharStream;)V

    const/16 v6, 0x1c

    .line 397
    invoke-virtual {v1, v6, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v4

    invoke-virtual {v5, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    const/16 v4, 0x2a

    .line 400
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v4, :cond_1

    .line 402
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v6

    if-eqz v6, :cond_1

    .line 403
    invoke-virtual {v1, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->dupNode(Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v6

    .line 404
    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 405
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object v4

    new-array v7, v3, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-interface {v4, v7}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v4

    check-cast v4, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 406
    array-length v7, v4

    move v8, v3

    :goto_0
    if-ge v8, v7, :cond_1

    aget-object v9, v4, v8

    .line 407
    invoke-virtual {v9, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v10

    invoke-interface {v10}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v10

    .line 408
    sget-object v11, Lgroovyjarjarantlr4/v4/tool/Grammar;->lexerOptions:Ljava/util/Set;

    invoke-interface {v11, v10}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_0

    sget-object v11, Lgroovyjarjarantlr4/v4/tool/Grammar;->doNotCopyOptionsToLexer:Ljava/util/Set;

    invoke-interface {v11, v10}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v11

    if-nez v11, :cond_0

    .line 411
    invoke-virtual {v1, v9}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->dupTree(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 412
    invoke-virtual {v6, v9}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    const/4 v11, 0x1

    .line 413
    invoke-virtual {v9, v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v9

    check-cast v9, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v5, v10, v9}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->setOption(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    :cond_0
    add-int/lit8 v8, v8, 0x1

    goto :goto_0

    .line 419
    :cond_1
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 420
    array-length v6, v2

    move v7, v3

    :goto_1
    if-ge v7, v6, :cond_3

    aget-object v8, v2, v7

    .line 421
    invoke-virtual {v8}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v9

    const/16 v10, 0xb

    if-ne v9, v10, :cond_2

    .line 422
    invoke-virtual {v1, v8}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->dupTree(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lgroovyjarjarantlr4/runtime/tree/Tree;

    invoke-virtual {v5, v9}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 423
    invoke-virtual {v8, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v9

    invoke-interface {v9}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v9

    const-string v10, "lexer"

    invoke-virtual {v9, v10}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_2

    .line 424
    invoke-interface {v4, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_2
    add-int/lit8 v7, v7, 0x1

    goto :goto_1

    .line 429
    :cond_3
    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_4

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 430
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->deleteChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)Z

    goto :goto_2

    :cond_4
    const/16 v2, 0x61

    .line 433
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-nez v4, :cond_5

    return-object v5

    :cond_5
    const-string v6, "RULES"

    .line 439
    invoke-virtual {v1, v2, v6}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    .line 440
    invoke-virtual {v5, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 441
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 443
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v7

    if-lez v7, :cond_6

    .line 444
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object v7

    new-array v8, v3, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    invoke-interface {v7, v8}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v7

    check-cast v7, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    goto :goto_3

    :cond_6
    new-array v7, v3, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    .line 450
    :goto_3
    array-length v8, v7

    move v9, v3

    :goto_4
    if-ge v9, v8, :cond_8

    aget-object v10, v7, v9

    .line 451
    invoke-virtual {v10, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v11

    invoke-interface {v11}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v11

    .line 452
    invoke-static {v11}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isTokenName(Ljava/lang/String;)Z

    move-result v11

    if-eqz v11, :cond_7

    .line 453
    invoke-virtual {v1, v10}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->dupTree(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lgroovyjarjarantlr4/runtime/tree/Tree;

    invoke-virtual {v2, v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 454
    invoke-interface {v6, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_7
    add-int/lit8 v9, v9, 0x1

    goto :goto_4

    .line 458
    :cond_8
    invoke-interface {v6}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_5
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_9

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 459
    invoke-virtual {v4, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->deleteChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)Z

    goto :goto_5

    .line 463
    :cond_9
    invoke-static {v5}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getStringLiteralAliasesFromLexerRules(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)Ljava/util/List;

    move-result-object v1

    .line 466
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getStringLiterals()Ljava/util/Set;

    move-result-object v4

    .line 472
    invoke-interface {v4}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_6
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_c

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    if-eqz v1, :cond_b

    .line 475
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v7

    :cond_a
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    move-result v8

    if-eqz v8, :cond_b

    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    .line 476
    invoke-virtual {v8}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem2()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 477
    invoke-virtual {v8}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v6, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_a

    goto :goto_6

    .line 481
    :cond_b
    invoke-virtual {p1, v6}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getStringLiteralLexerRuleName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    .line 483
    new-instance v8, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/16 v9, 0x5e

    invoke-direct {v8, v9}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;-><init>(I)V

    .line 484
    new-instance v9, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    const/16 v10, 0x4e

    invoke-direct {v9, v10}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;-><init>(I)V

    .line 485
    new-instance v10, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    const/16 v11, 0x4a

    invoke-direct {v10, v11}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;-><init>(I)V

    .line 486
    new-instance v11, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;

    new-instance v12, Lgroovyjarjarantlr4/runtime/CommonToken;

    const/16 v13, 0x3e

    invoke-direct {v12, v13, v6}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    invoke-direct {v11, v12}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    .line 487
    invoke-virtual {v10, v11}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 488
    invoke-virtual {v9, v10}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 489
    new-instance v6, Lgroovyjarjarantlr4/runtime/CommonToken;

    const/16 v10, 0x42

    invoke-direct {v6, v10, v7}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    .line 490
    new-instance v7, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;

    invoke-direct {v7, v6}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    invoke-virtual {v8, v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 491
    invoke-virtual {v8, v9}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 492
    invoke-virtual {v2, v3, v8}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->insertChild(ILjava/lang/Object;)V

    .line 494
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->freshenParentAndChildIndexes()V

    add-int/lit8 v3, v3, 0x1

    goto :goto_6

    .line 501
    :cond_c
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->sanityCheckParentAndChildIndexes()V

    .line 502
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->sanityCheckParentAndChildIndexes()V

    .line 505
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "after extract implicit lexer ="

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->toStringTree()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v3, "grammar"

    invoke-virtual {v1, v3, v0}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 506
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "lexer ="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->toStringTree()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v3, v0}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 508
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result p1

    if-nez p1, :cond_d

    const/4 p1, 0x0

    return-object p1

    :cond_d
    return-object v5
.end method

.method public integrateImportedGrammars(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 29

    move-object/from16 v0, p1

    .line 157
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getAllImportedGrammars()Ljava/util/List;

    move-result-object v1

    if-nez v1, :cond_0

    return-void

    .line 160
    :cond_0
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/4 v3, 0x0

    .line 161
    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 162
    new-instance v5, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v4

    invoke-direct {v5, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    const/16 v4, 0xd

    .line 164
    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/16 v7, 0x41

    .line 165
    invoke-virtual {v2, v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v8

    check-cast v8, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/16 v9, 0xb

    .line 167
    invoke-virtual {v2, v9}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object v10

    const/16 v11, 0x61

    .line 170
    invoke-virtual {v2, v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v11

    check-cast v11, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 171
    new-instance v12, Ljava/util/HashSet;

    invoke-direct {v12}, Ljava/util/HashSet;-><init>()V

    const/16 v13, 0x5e

    .line 173
    invoke-virtual {v11, v13}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object v14

    .line 174
    invoke-interface {v14}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v14

    :goto_0
    invoke-interface {v14}, Ljava/util/Iterator;->hasNext()Z

    move-result v15

    if-eqz v15, :cond_1

    invoke-interface {v14}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v15

    check-cast v15, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v15, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v15

    invoke-interface {v15}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v15

    invoke-interface {v12, v15}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    const/16 v14, 0x24

    .line 177
    invoke-virtual {v2, v14}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object v15

    .line 178
    new-instance v13, Ljava/util/HashSet;

    invoke-direct {v13}, Ljava/util/HashSet;-><init>()V

    .line 179
    invoke-interface {v15}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v16

    :goto_1
    invoke-interface/range {v16 .. v16}, Ljava/util/Iterator;->hasNext()Z

    move-result v17

    if-eqz v17, :cond_2

    invoke-interface/range {v16 .. v16}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v17

    move-object/from16 v14, v17

    check-cast v14, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v14, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v14

    invoke-interface {v14}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v14

    invoke-interface {v13, v14}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const/16 v14, 0x24

    goto :goto_1

    .line 180
    :cond_2
    new-instance v14, Ljava/util/ArrayList;

    invoke-direct {v14}, Ljava/util/ArrayList;-><init>()V

    .line 182
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v14

    const-string v9, "grammar"

    if-eqz v14, :cond_21

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v14

    check-cast v14, Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 184
    iget-object v3, v14, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v3, :cond_8

    .line 186
    iget-object v7, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v20, v1

    const-string v1, "imported channels: "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object v4

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v7, v9, v1}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    if-nez v6, :cond_4

    .line 188
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v1

    .line 189
    iput-object v0, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    const/4 v3, 0x1

    .line 190
    invoke-virtual {v2, v3, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->insertChild(ILjava/lang/Object;)V

    move-object v6, v1

    :cond_3
    :goto_3
    move-object/from16 v21, v11

    goto :goto_7

    :cond_4
    const/4 v1, 0x0

    .line 192
    :goto_4
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v4

    if-ge v1, v4, :cond_3

    .line 193
    invoke-virtual {v3, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v4

    move-object/from16 v21, v11

    const/4 v7, 0x0

    .line 195
    :goto_5
    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v11

    if-ge v7, v11, :cond_6

    .line 196
    invoke-virtual {v6, v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v11

    invoke-interface {v11}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v11

    .line 197
    invoke-virtual {v11, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_5

    const/4 v4, 0x1

    goto :goto_6

    :cond_5
    add-int/lit8 v7, v7, 0x1

    goto :goto_5

    :cond_6
    const/4 v4, 0x0

    :goto_6
    if-nez v4, :cond_7

    .line 203
    invoke-virtual {v3, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    invoke-virtual {v6, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    :cond_7
    add-int/lit8 v1, v1, 0x1

    move-object/from16 v11, v21

    goto :goto_4

    :cond_8
    move-object/from16 v20, v1

    goto :goto_3

    .line 210
    :goto_7
    iget-object v1, v14, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/16 v3, 0x41

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v1, :cond_a

    .line 212
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "imported tokens: "

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object v7

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v9, v4}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    if-nez v8, :cond_9

    const-string v3, "TOKENS"

    const/16 v4, 0x41

    .line 214
    invoke-virtual {v5, v4, v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v8

    .line 215
    iput-object v0, v8, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    const/4 v3, 0x1

    .line 216
    invoke-virtual {v2, v3, v8}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->insertChild(ILjava/lang/Object;)V

    goto :goto_8

    :cond_9
    const/16 v4, 0x41

    .line 218
    :goto_8
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object v1

    const/4 v3, 0x0

    new-array v7, v3, [Lgroovyjarjarantlr4/runtime/tree/Tree;

    invoke-interface {v1, v7}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-virtual {v8, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChildren(Ljava/util/List;)V

    goto :goto_9

    :cond_a
    const/16 v4, 0x41

    .line 221
    :goto_9
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 222
    iget-object v3, v14, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/16 v7, 0xb

    invoke-virtual {v3, v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object v3

    if-eqz v10, :cond_b

    .line 223
    invoke-interface {v1, v10}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 224
    :cond_b
    invoke-interface {v1, v3}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    if-eqz v3, :cond_12

    .line 228
    new-instance v11, Lgroovyjarjarantlr4/v4/runtime/misc/DoubleKeyMap;

    invoke-direct {v11}, Lgroovyjarjarantlr4/v4/runtime/misc/DoubleKeyMap;-><init>()V

    .line 231
    iget-object v4, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v22, v5

    const-string v5, "imported actions: "

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v4, v9, v3}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 232
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_a
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_f

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 233
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getDefaultActionScope()Ljava/lang/String;

    move-result-object v4

    .line 235
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v5

    const/4 v7, 0x2

    if-le v5, v7, :cond_c

    const/4 v5, 0x0

    .line 236
    invoke-virtual {v3, v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 237
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x1

    .line 238
    invoke-virtual {v3, v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v19

    check-cast v19, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 239
    invoke-virtual {v3, v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v7

    check-cast v7, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-object/from16 v23, v1

    move-object/from16 v5, v19

    goto :goto_b

    :cond_c
    const/4 v5, 0x1

    const/4 v7, 0x0

    .line 242
    invoke-virtual {v3, v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v19

    move-object/from16 v7, v19

    check-cast v7, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 243
    invoke-virtual {v3, v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v23

    move-object/from16 v5, v23

    check-cast v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-object/from16 v23, v1

    move-object/from16 v28, v7

    move-object v7, v5

    move-object/from16 v5, v28

    .line 245
    :goto_b
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v11, v4, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/DoubleKeyMap;->get(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-nez v1, :cond_d

    .line 247
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v11, v4, v1, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/DoubleKeyMap;->put(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-object/from16 v24, v6

    move-object/from16 v25, v8

    goto :goto_c

    .line 250
    :cond_d
    iget-object v4, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-object/from16 v24, v6

    iget-object v6, v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    if-ne v4, v6, :cond_e

    .line 251
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ACTION_REDEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v6, v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    move-object/from16 v25, v8

    const/4 v7, 0x1

    new-array v8, v7, [Ljava/lang/Object;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v5

    const/16 v17, 0x0

    aput-object v5, v8, v17

    invoke-virtual {v1, v4, v3, v6, v8}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_c

    :cond_e
    move-object/from16 v25, v8

    const/4 v3, 0x1

    .line 255
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v4

    .line 256
    invoke-virtual {v4}, Ljava/lang/String;->length()I

    move-result v5

    sub-int/2addr v5, v3

    invoke-virtual {v4, v3, v5}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v4

    .line 257
    invoke-virtual {v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v5

    .line 258
    invoke-virtual {v5}, Ljava/lang/String;->length()I

    move-result v6

    sub-int/2addr v6, v3

    invoke-virtual {v5, v3, v6}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v5

    .line 259
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "{"

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const/16 v4, 0xa

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, "}"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 260
    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v1, v3}, Lgroovyjarjarantlr4/runtime/Token;->setText(Ljava/lang/String;)V

    :goto_c
    move-object/from16 v1, v23

    move-object/from16 v6, v24

    move-object/from16 v8, v25

    goto/16 :goto_a

    :cond_f
    move-object/from16 v24, v6

    move-object/from16 v25, v8

    .line 267
    invoke-virtual {v11}, Lgroovyjarjarantlr4/v4/runtime/misc/DoubleKeyMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_10
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_13

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    .line 268
    invoke-virtual {v11, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/DoubleKeyMap;->keySet(Ljava/lang/Object;)Ljava/util/Set;

    move-result-object v4

    invoke-interface {v4}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_d
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_10

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    .line 269
    invoke-virtual {v11, v3, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/DoubleKeyMap;->get(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 270
    iget-object v7, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v23, v1

    iget-object v1, v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v8, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v8, " "

    invoke-virtual {v1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v8, ":"

    invoke-virtual {v1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v5, "="

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v7, v9, v1}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 271
    iget-object v1, v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    if-eq v1, v0, :cond_11

    .line 272
    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    const/4 v5, 0x1

    invoke-virtual {v2, v5, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->insertChild(ILjava/lang/Object;)V

    :cond_11
    move-object/from16 v1, v23

    goto :goto_d

    :cond_12
    move-object/from16 v22, v5

    move-object/from16 v24, v6

    move-object/from16 v25, v8

    .line 285
    :cond_13
    iget-object v1, v14, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/16 v3, 0x24

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object v1

    const-string v4, "imported rule: "

    if-eqz v1, :cond_1a

    .line 287
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_e
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_1a

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 288
    iget-object v6, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "imported mode: "

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->toStringTree()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v9, v7}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v6, 0x0

    .line 289
    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v7

    invoke-interface {v7}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v7

    .line 290
    invoke-interface {v13, v7}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_16

    .line 293
    invoke-interface {v15}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v17

    :goto_f
    invoke-interface/range {v17 .. v17}, Ljava/util/Iterator;->hasNext()Z

    move-result v18

    if-eqz v18, :cond_15

    invoke-interface/range {v17 .. v17}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v18

    move-object/from16 v3, v18

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 294
    invoke-virtual {v3, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v18

    invoke-interface/range {v18 .. v18}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v11, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v11

    if-eqz v11, :cond_14

    move-object v11, v3

    goto :goto_10

    :cond_14
    const/16 v3, 0x24

    goto :goto_f

    :cond_15
    const/4 v11, 0x0

    goto :goto_10

    .line 300
    :cond_16
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v11

    .line 301
    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-virtual {v11, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    :goto_10
    const/16 v3, 0x5e

    .line 305
    invoke-virtual {v5, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object v5

    .line 306
    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    const/4 v5, 0x0

    :goto_11
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_18

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-object/from16 v18, v1

    .line 307
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    move-object/from16 v26, v2

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    move-object/from16 v27, v3

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->toStringTree()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v9, v2}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v1, 0x0

    .line 308
    invoke-virtual {v6, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v1

    .line 309
    invoke-interface {v12, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_17

    .line 311
    invoke-virtual {v11, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    add-int/lit8 v5, v5, 0x1

    .line 313
    invoke-interface {v12, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    :cond_17
    move-object/from16 v1, v18

    move-object/from16 v2, v26

    move-object/from16 v3, v27

    goto :goto_11

    :cond_18
    move-object/from16 v18, v1

    move-object/from16 v26, v2

    if-nez v8, :cond_19

    if-lez v5, :cond_19

    .line 317
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v1, v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 318
    invoke-interface {v13, v7}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 319
    invoke-interface {v15, v11}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_19
    move-object/from16 v1, v18

    move-object/from16 v2, v26

    const/16 v3, 0x24

    goto/16 :goto_e

    :cond_1a
    move-object/from16 v26, v2

    .line 326
    iget-object v1, v14, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/16 v2, 0x5e

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_1c

    .line 328
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_12
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1c

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 329
    iget-object v5, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->toStringTree()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v9, v6}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v5, 0x0

    .line 330
    invoke-virtual {v3, v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v6

    invoke-interface {v6}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v5

    .line 331
    invoke-interface {v12, v5}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v6

    move-object/from16 v11, v21

    if-nez v6, :cond_1b

    .line 333
    invoke-virtual {v11, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 334
    invoke-interface {v12, v5}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    :cond_1b
    move-object/from16 v21, v11

    goto :goto_12

    :cond_1c
    move-object/from16 v11, v21

    .line 339
    iget-object v1, v14, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/16 v3, 0x2a

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v1, :cond_20

    .line 346
    iget-object v3, v14, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getOptions()Ljava/util/Map;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_1d
    :goto_13
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_1f

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Map$Entry;

    .line 347
    iget-object v5, v14, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-interface {v4}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    if-nez v5, :cond_1e

    goto :goto_13

    .line 352
    :cond_1e
    iget-object v6, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-interface {v4}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-virtual {v6, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    .line 353
    invoke-virtual {v5, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_1d

    const/4 v3, 0x1

    goto :goto_14

    :cond_1f
    const/4 v3, 0x0

    :goto_14
    if-eqz v3, :cond_20

    .line 360
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;->OPTIONS_IN_DELEGATE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v5, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    const/4 v6, 0x1

    new-array v6, v6, [Ljava/lang/Object;

    iget-object v7, v14, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    const/4 v8, 0x0

    aput-object v7, v6, v8

    invoke-virtual {v3, v4, v5, v1, v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_15

    :cond_20
    const/4 v8, 0x0

    :goto_15
    move v3, v8

    move-object/from16 v1, v20

    move-object/from16 v5, v22

    move-object/from16 v6, v24

    move-object/from16 v8, v25

    move-object/from16 v2, v26

    const/16 v4, 0xd

    const/16 v7, 0x41

    const/16 v9, 0xb

    goto/16 :goto_2

    .line 365
    :cond_21
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Grammar: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->toStringTree()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v9, v0}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public process()V
    .locals 5

    .line 49
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-nez v0, :cond_0

    return-void

    .line 51
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "before: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->toStringTree()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const-string v3, "grammar"

    invoke-virtual {v1, v3, v2}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 53
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->integrateImportedGrammars(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 54
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->reduceBlocksToSets(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 55
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->expandParameterizedLoops(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 57
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "after: "

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->toStringTree()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v3, v0}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public reduceBlocksToSets(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 4

    .line 61
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    invoke-direct {v0, v1, p1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/Object;)V

    .line 62
    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    .line 63
    new-instance v2, Lgroovyjarjarantlr4/v4/parse/BlockSetTransformer;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v2, v0, v3}, Lgroovyjarjarantlr4/v4/parse/BlockSetTransformer;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 64
    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/parse/BlockSetTransformer;->setTreeAdaptor(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;)V

    .line 65
    invoke-virtual {v2, p1}, Lgroovyjarjarantlr4/v4/parse/BlockSetTransformer;->downup(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method
