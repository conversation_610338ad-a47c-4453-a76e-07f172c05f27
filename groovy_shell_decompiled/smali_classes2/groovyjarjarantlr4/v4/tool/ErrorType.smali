.class public final enum Lgroovyjarjarantlr4/v4/tool/ErrorType;
.super Ljava/lang/Enum;
.source "ErrorType.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovyjarjarantlr4/v4/tool/ErrorType;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ACTION_REDEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ALL_OPS_NEED_SAME_ASSOC:Lgroovyjarjarantlr4/v4/tool/ErrorType;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum ALT_LABEL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ALT_LABEL_REDEF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ARG_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ARG_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ASSIGNMENT_TO_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ATTRIBUTE_IN_LEXER_ACTION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum BAD_OPTION_SET_SYNTAX:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum BASE_CONTEXT_CANNOT_BE_TRANSITIVE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum BASE_CONTEXT_MUST_BE_RULE_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CANNOT_CREATE_TARGET_GENERATOR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CANNOT_FIND_ATTRIBUTE_NAME_IN_DECL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CANNOT_FIND_IMPORTED_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CANNOT_FIND_TOKENS_FILE_GIVEN_ON_CMDLINE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CANNOT_FIND_TOKENS_FILE_REFD_IN_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CANNOT_OPEN_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CANNOT_WRITE_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CHANNELS_BLOCK_IN_COMBINED_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CHANNELS_BLOCK_IN_PARSER_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CHANNEL_CONFLICTS_WITH_COMMON_CONSTANTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CHANNEL_CONFLICTS_WITH_MODE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CHANNEL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CHARACTERS_COLLISION_IN_SET:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CODE_GEN_TEMPLATES_INCOMPLETE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CODE_TEMPLATE_ARG_ISSUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CONSTANT_VALUE_IS_NOT_A_RECOGNIZED_CHANNEL_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CONSTANT_VALUE_IS_NOT_A_RECOGNIZED_MODE_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum CONSTANT_VALUE_IS_NOT_A_RECOGNIZED_TOKEN_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum DIR_NOT_FOUND:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum DUPLICATED_COMMAND:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum EMPTY_STRINGS_AND_SETS_NOT_ALLOWED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum EPSILON_CLOSURE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum EPSILON_LR_FOLLOW:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum EPSILON_OPTIONAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum EPSILON_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ERROR_READING_IMPORTED_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ERROR_READING_TOKENS_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum EXPECTED_NON_GREEDY_WILDCARD_BLOCK:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum FILE_AND_GRAMMAR_NAME_DIFFER:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum FRAGMENT_ACTION_IGNORED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ILLEGAL_OPTION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ILLEGAL_OPTION_VALUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum IMPLICIT_STRING_DEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum IMPLICIT_TOKEN_DEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum IMPORT_NAME_CLASH:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum INCOMPATIBLE_COMMANDS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum INVALID_CMDLINE_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum INVALID_ESCAPE_SEQUENCE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum INVALID_IMPORT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum INVALID_LEXER_COMMAND:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum INVALID_LITERAL_IN_LEXER_SET:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum INVALID_RULE_PARAMETER_REF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum ISOLATED_RULE_REF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LABEL_BLOCK_NOT_A_SET:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LABEL_CONFLICTS_WITH_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LABEL_CONFLICTS_WITH_LOCAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LABEL_CONFLICTS_WITH_RETVAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LABEL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LABEL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LABEL_TYPE_CONFLICT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LEFT_RECURSION_CYCLES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LEXER_ACTION_PLACEMENT_ISSUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum LEXER_COMMAND_PLACEMENT_ISSUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LEXER_RULES_NOT_ALLOWED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LEXER_RULE_CANNOT_HAVE_BASE_CONTEXT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LOCAL_CONFLICTS_WITH_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LOCAL_CONFLICTS_WITH_RETVAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LOCAL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum LOCAL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum MISSING_CODE_GEN_TEMPLATES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum MISSING_LEXER_COMMAND_ARGUMENT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum MISSING_RULE_ARGS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum MODE_CONFLICTS_WITH_COMMON_CONSTANTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum MODE_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum MODE_NOT_IN_LEXER:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum MODE_WITHOUT_RULES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum NONCONFORMING_LR_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum NO_MODEL_TO_TEMPLATE_MAPPING:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum NO_NON_LR_ALTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum NO_RULES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum NO_SUCH_GRAMMAR_SCOPE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum NO_SUCH_RULE_IN_SCOPE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum OPTIONS_IN_DELEGATE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum OUTPUT_DIR_IS_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum PARSER_RULES_NOT_ALLOWED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum PARSER_RULE_REF_IN_LEXER_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum REPEATED_PREQUEL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum RESERVED_RULE_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum RETVAL_CONFLICTS_WITH_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum RETVAL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum RETVAL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum RULE_HAS_NO_ARGS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum RULE_REDEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum RULE_WITH_TOO_FEW_ALT_LABELS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum RULE_WITH_TOO_FEW_ALT_LABELS_GROUP:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum STRING_TEMPLATE_WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum SYNTAX_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum TOKENS_FILE_SYNTAX_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum TOKEN_CONFLICTS_WITH_COMMON_CONSTANTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum TOKEN_NAMES_MUST_START_UPPER:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum TOKEN_NAME_REASSIGNMENT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum TOKEN_RANGE_IN_PARSER:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum TOKEN_UNREACHABLE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNDEFINED_RULE_IN_NONLOCAL_REF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNDEFINED_RULE_REF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNICODE_PROPERTY_NOT_ALLOWED_IN_RANGE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNKNOWN_ATTRIBUTE_IN_SCOPE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNKNOWN_LEXER_CONSTANT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNKNOWN_RULE_ATTRIBUTE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNKNOWN_SIMPLE_ATTRIBUTE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNRECOGNIZED_ASSOC_OPTION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNSUPPORTED_REFERENCE_IN_LEXER_SET:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNTERMINATED_STRING_LITERAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum UNWANTED_LEXER_COMMAND_ARGUMENT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum USE_OF_BAD_WORD:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum V3_ASSIGN_IN_TOKENS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum V3_GATED_SEMPRED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum V3_LEXER_LABEL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum V3_SYNPRED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum V3_TOKENS_SYNTAX:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum V3_TREE_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

.field public static final enum WARNING_TREATED_AS_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;


# instance fields
.field public final code:I

.field public final msg:Ljava/lang/String;

.field public final severity:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;


# direct methods
.method static constructor <clinit>()V
    .locals 138

    .line 31
    new-instance v6, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v5, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v1, "CANNOT_WRITE_FILE"

    const/4 v2, 0x0

    const/4 v3, 0x1

    const-string v4, "cannot write file \'<arg>\': <arg2>"

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v6, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_WRITE_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 37
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v12, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v8, "INVALID_CMDLINE_ARG"

    const/4 v9, 0x1

    const/4 v10, 0x2

    const-string v11, "unknown command-line option \'<arg>\'"

    move-object v7, v0

    invoke-direct/range {v7 .. v12}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INVALID_CMDLINE_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 43
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v18, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v14, "CANNOT_FIND_TOKENS_FILE_GIVEN_ON_CMDLINE"

    const/4 v15, 0x2

    const/16 v16, 0x3

    const-string v17, "cannot find tokens file \'<arg>\' given for \'<arg2>\'"

    move-object v13, v1

    invoke-direct/range {v13 .. v18}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_FIND_TOKENS_FILE_GIVEN_ON_CMDLINE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 49
    new-instance v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v12, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v8, "ERROR_READING_TOKENS_FILE"

    const/4 v9, 0x3

    const/4 v10, 0x4

    const-string v11, "error reading tokens file \'<arg>\': <arg2>"

    move-object v7, v2

    invoke-direct/range {v7 .. v12}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ERROR_READING_TOKENS_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 55
    new-instance v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v18, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v14, "DIR_NOT_FOUND"

    const/4 v15, 0x4

    const/16 v16, 0x5

    const-string v17, "directory not found: <arg>"

    move-object v13, v3

    invoke-direct/range {v13 .. v18}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->DIR_NOT_FOUND:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 61
    new-instance v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v12, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v8, "OUTPUT_DIR_IS_FILE"

    const/4 v9, 0x5

    const/4 v10, 0x6

    const-string v11, "output directory is a file: <arg>"

    move-object v7, v4

    invoke-direct/range {v7 .. v12}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;->OUTPUT_DIR_IS_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 67
    new-instance v5, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v18, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v14, "CANNOT_OPEN_FILE"

    const/4 v15, 0x6

    const/16 v16, 0x7

    const-string v17, "cannot find or open file: <arg><if(exception&&verbose)>; reason: <exception><endif>"

    move-object v13, v5

    invoke-direct/range {v13 .. v18}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v5, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_OPEN_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 74
    new-instance v13, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v12, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v8, "FILE_AND_GRAMMAR_NAME_DIFFER"

    const/4 v9, 0x7

    const/16 v10, 0x8

    const-string v11, "grammar name \'<arg>\' and file name \'<arg2>\' differ"

    move-object v7, v13

    invoke-direct/range {v7 .. v12}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v13, Lgroovyjarjarantlr4/v4/tool/ErrorType;->FILE_AND_GRAMMAR_NAME_DIFFER:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 80
    new-instance v7, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v19, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v15, "BAD_OPTION_SET_SYNTAX"

    const/16 v16, 0x8

    const/16 v17, 0x9

    const-string v18, "invalid -Dname=value syntax: \'<arg>\'"

    move-object v14, v7

    invoke-direct/range {v14 .. v19}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v7, Lgroovyjarjarantlr4/v4/tool/ErrorType;->BAD_OPTION_SET_SYNTAX:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 86
    new-instance v8, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v25, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR_ONE_OFF:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v21, "WARNING_TREATED_AS_ERROR"

    const/16 v22, 0x9

    const/16 v23, 0xa

    const-string v24, "warning treated as error"

    move-object/from16 v20, v8

    invoke-direct/range {v20 .. v25}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v8, Lgroovyjarjarantlr4/v4/tool/ErrorType;->WARNING_TREATED_AS_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 92
    new-instance v9, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v19, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v15, "ERROR_READING_IMPORTED_GRAMMAR"

    const/16 v16, 0xa

    const/16 v17, 0xb

    const-string v18, "error reading imported grammar \'<arg>\' referenced in \'<arg2>\'"

    move-object v14, v9

    invoke-direct/range {v14 .. v19}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v9, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ERROR_READING_IMPORTED_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 99
    new-instance v10, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v25, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v21, "INTERNAL_ERROR"

    const/16 v22, 0xb

    const/16 v23, 0x14

    const-string v24, "internal error: <arg> <arg2><if(exception&&verbose)>: <exception><stackTrace; separator=\"\\n\"><endif>"

    move-object/from16 v20, v10

    invoke-direct/range {v20 .. v25}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v10, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 106
    new-instance v11, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v19, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v15, "TOKENS_FILE_SYNTAX_ERROR"

    const/16 v16, 0xc

    const/16 v17, 0x15

    const-string v18, ".tokens file syntax error <arg>:<arg2>"

    move-object v14, v11

    invoke-direct/range {v14 .. v19}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v11, Lgroovyjarjarantlr4/v4/tool/ErrorType;->TOKENS_FILE_SYNTAX_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 112
    new-instance v12, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v25, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v21, "STRING_TEMPLATE_WARNING"

    const/16 v22, 0xd

    const/16 v23, 0x16

    const-string v24, "template error: <arg> <arg2><if(exception&&verbose)>: <exception><stackTrace; separator=\"\\n\"><endif>"

    move-object/from16 v20, v12

    invoke-direct/range {v20 .. v25}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v12, Lgroovyjarjarantlr4/v4/tool/ErrorType;->STRING_TEMPLATE_WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 124
    new-instance v20, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v19, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v15, "MISSING_CODE_GEN_TEMPLATES"

    const/16 v16, 0xe

    const/16 v17, 0x1e

    const-string v18, "can\'t find code generation templates: <arg>"

    move-object/from16 v14, v20

    invoke-direct/range {v14 .. v19}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v20, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MISSING_CODE_GEN_TEMPLATES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 132
    new-instance v14, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v16, v12

    const-string v12, "ANTLR cannot generate \'<arg>\' code as of version "

    invoke-virtual {v15, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    sget-object v15, Lgroovyjarjarantlr4/v4/Tool;->VERSION:Ljava/lang/String;

    invoke-virtual {v12, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v25

    sget-object v26, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v22, "CANNOT_CREATE_TARGET_GENERATOR"

    const/16 v23, 0xf

    const/16 v24, 0x1f

    move-object/from16 v21, v14

    invoke-direct/range {v21 .. v26}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v14, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_CREATE_TARGET_GENERATOR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 140
    new-instance v12, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v32, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v28, "CODE_TEMPLATE_ARG_ISSUE"

    const/16 v29, 0x10

    const/16 v30, 0x20

    const-string v31, "code generation template \'<arg>\' has missing, misnamed, or incomplete arg list; missing \'<arg2>\'"

    move-object/from16 v27, v12

    invoke-direct/range {v27 .. v32}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v12, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CODE_TEMPLATE_ARG_ISSUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 146
    new-instance v15, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v26, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v22, "CODE_GEN_TEMPLATES_INCOMPLETE"

    const/16 v23, 0x11

    const/16 v24, 0x21

    const-string v25, "missing code generation template \'<arg>\'"

    move-object/from16 v21, v15

    invoke-direct/range {v21 .. v26}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v15, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CODE_GEN_TEMPLATES_INCOMPLETE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 153
    new-instance v17, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v32, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v28, "NO_MODEL_TO_TEMPLATE_MAPPING"

    const/16 v29, 0x12

    const/16 v30, 0x22

    const-string v31, "no mapping to template name for output model class \'<arg>\'"

    move-object/from16 v27, v17

    invoke-direct/range {v27 .. v32}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v17, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NO_MODEL_TO_TEMPLATE_MAPPING:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 164
    new-instance v18, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v26, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v22, "SYNTAX_ERROR"

    const/16 v23, 0x13

    const/16 v24, 0x32

    const-string v25, "syntax error: <arg>"

    move-object/from16 v21, v18

    invoke-direct/range {v21 .. v26}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v18, Lgroovyjarjarantlr4/v4/tool/ErrorType;->SYNTAX_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 170
    new-instance v19, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v32, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v28, "RULE_REDEFINITION"

    const/16 v29, 0x14

    const/16 v30, 0x33

    const-string v31, "rule \'<arg>\' redefinition; previous at line <arg2>"

    move-object/from16 v27, v19

    invoke-direct/range {v27 .. v32}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v19, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RULE_REDEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 176
    new-instance v27, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v26, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v22, "LEXER_RULES_NOT_ALLOWED"

    const/16 v23, 0x15

    const/16 v24, 0x34

    const-string v25, "lexer rule \'<arg>\' not allowed in parser"

    move-object/from16 v21, v27

    invoke-direct/range {v21 .. v26}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v27, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LEXER_RULES_NOT_ALLOWED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 182
    new-instance v21, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v33, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v29, "PARSER_RULES_NOT_ALLOWED"

    const/16 v30, 0x16

    const/16 v31, 0x35

    const-string v32, "parser rule \'<arg>\' not allowed in lexer"

    move-object/from16 v28, v21

    invoke-direct/range {v28 .. v33}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v21, Lgroovyjarjarantlr4/v4/tool/ErrorType;->PARSER_RULES_NOT_ALLOWED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 190
    new-instance v22, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v39, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v35, "REPEATED_PREQUEL"

    const/16 v36, 0x17

    const/16 v37, 0x36

    const-string v38, "repeated grammar prequel spec (options, tokens, or import); please merge"

    move-object/from16 v34, v22

    invoke-direct/range {v34 .. v39}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v22, Lgroovyjarjarantlr4/v4/tool/ErrorType;->REPEATED_PREQUEL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 198
    new-instance v23, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v33, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v29, "UNDEFINED_RULE_REF"

    const/16 v30, 0x18

    const/16 v31, 0x38

    const-string v32, "reference to undefined rule: <arg>"

    move-object/from16 v28, v23

    invoke-direct/range {v28 .. v33}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v23, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNDEFINED_RULE_REF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 206
    new-instance v24, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v39, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v35, "UNDEFINED_RULE_IN_NONLOCAL_REF"

    const/16 v36, 0x19

    const/16 v37, 0x39

    const-string v38, "reference to undefined rule \'<arg>\' in non-local ref \'<arg3>\'"

    move-object/from16 v34, v24

    invoke-direct/range {v34 .. v39}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v24, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNDEFINED_RULE_IN_NONLOCAL_REF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 212
    new-instance v25, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v33, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v29, "TOKEN_NAMES_MUST_START_UPPER"

    const/16 v30, 0x1a

    const/16 v31, 0x3c

    const-string v32, "token names must start with an uppercase letter: <arg>"

    move-object/from16 v28, v25

    invoke-direct/range {v28 .. v33}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v25, Lgroovyjarjarantlr4/v4/tool/ErrorType;->TOKEN_NAMES_MUST_START_UPPER:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 220
    new-instance v26, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v39, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v35, "UNKNOWN_SIMPLE_ATTRIBUTE"

    const/16 v36, 0x1b

    const/16 v37, 0x3f

    const-string v38, "unknown attribute reference \'<arg>\' in \'<arg2>\'"

    move-object/from16 v34, v26

    invoke-direct/range {v34 .. v39}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v26, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNKNOWN_SIMPLE_ATTRIBUTE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 228
    new-instance v34, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v33, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v29, "INVALID_RULE_PARAMETER_REF"

    const/16 v30, 0x1c

    const/16 v31, 0x40

    const-string v32, "parameter \'<arg>\' of rule \'<arg2>\' is not accessible in this scope: <arg3>"

    move-object/from16 v28, v34

    invoke-direct/range {v28 .. v33}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v34, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INVALID_RULE_PARAMETER_REF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 236
    new-instance v28, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v40, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v36, "UNKNOWN_RULE_ATTRIBUTE"

    const/16 v37, 0x1d

    const/16 v38, 0x41

    const-string v39, "unknown attribute \'<arg>\' for rule \'<arg2>\' in \'<arg3>\'"

    move-object/from16 v35, v28

    invoke-direct/range {v35 .. v40}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v28, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNKNOWN_RULE_ATTRIBUTE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 244
    new-instance v29, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v46, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v42, "UNKNOWN_ATTRIBUTE_IN_SCOPE"

    const/16 v43, 0x1e

    const/16 v44, 0x42

    const-string v45, "attribute \'<arg>\' isn\'t a valid property in \'<arg2>\'"

    move-object/from16 v41, v29

    invoke-direct/range {v41 .. v46}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v29, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNKNOWN_ATTRIBUTE_IN_SCOPE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 252
    new-instance v30, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v40, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v36, "ISOLATED_RULE_REF"

    const/16 v37, 0x1f

    const/16 v38, 0x43

    const-string v39, "missing attribute access on rule reference \'<arg>\' in \'<arg2>\'"

    move-object/from16 v35, v30

    invoke-direct/range {v35 .. v40}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v30, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ISOLATED_RULE_REF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 258
    new-instance v31, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v46, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v42, "LABEL_CONFLICTS_WITH_RULE"

    const/16 v43, 0x20

    const/16 v44, 0x45

    const-string v45, "label \'<arg>\' conflicts with rule with same name"

    move-object/from16 v41, v31

    invoke-direct/range {v41 .. v46}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v31, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 264
    new-instance v32, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v40, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v36, "LABEL_CONFLICTS_WITH_TOKEN"

    const/16 v37, 0x21

    const/16 v38, 0x46

    const-string v39, "label \'<arg>\' conflicts with token with same name"

    move-object/from16 v35, v32

    invoke-direct/range {v35 .. v40}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v32, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 270
    new-instance v33, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v46, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v42, "LABEL_CONFLICTS_WITH_ARG"

    const/16 v43, 0x22

    const/16 v44, 0x48

    const-string v45, "label \'<arg>\' conflicts with parameter with same name"

    move-object/from16 v41, v33

    invoke-direct/range {v41 .. v46}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v33, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 276
    new-instance v41, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v40, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v36, "LABEL_CONFLICTS_WITH_RETVAL"

    const/16 v37, 0x23

    const/16 v38, 0x49

    const-string v39, "label \'<arg>\' conflicts with return value with same name"

    move-object/from16 v35, v41

    invoke-direct/range {v35 .. v40}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v41, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_RETVAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 282
    new-instance v35, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v47, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v43, "LABEL_CONFLICTS_WITH_LOCAL"

    const/16 v44, 0x24

    const/16 v45, 0x4a

    const-string v46, "label \'<arg>\' conflicts with local with same name"

    move-object/from16 v42, v35

    invoke-direct/range {v42 .. v47}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v35, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_LOCAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 290
    new-instance v36, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v53, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v49, "LABEL_TYPE_CONFLICT"

    const/16 v50, 0x25

    const/16 v51, 0x4b

    const-string v52, "label \'<arg>\' type mismatch with previous definition: <arg2>"

    move-object/from16 v48, v36

    invoke-direct/range {v48 .. v53}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v36, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_TYPE_CONFLICT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 297
    new-instance v37, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v47, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v43, "RETVAL_CONFLICTS_WITH_ARG"

    const/16 v44, 0x26

    const/16 v45, 0x4c

    const-string v46, "return value \'<arg>\' conflicts with parameter with same name"

    move-object/from16 v42, v37

    invoke-direct/range {v42 .. v47}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v37, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RETVAL_CONFLICTS_WITH_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 303
    new-instance v38, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v53, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v49, "MISSING_RULE_ARGS"

    const/16 v50, 0x27

    const/16 v51, 0x4f

    const-string v52, "missing argument(s) on rule reference: <arg>"

    move-object/from16 v48, v38

    invoke-direct/range {v48 .. v53}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v38, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MISSING_RULE_ARGS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 309
    new-instance v39, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v47, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v43, "RULE_HAS_NO_ARGS"

    const/16 v44, 0x28

    const/16 v45, 0x50

    const-string v46, "rule \'<arg>\' has no defined parameters"

    move-object/from16 v42, v39

    invoke-direct/range {v42 .. v47}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v39, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RULE_HAS_NO_ARGS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 315
    new-instance v40, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v53, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v49, "ILLEGAL_OPTION"

    const/16 v50, 0x29

    const/16 v51, 0x53

    const-string v52, "unsupported option \'<arg>\'"

    move-object/from16 v48, v40

    invoke-direct/range {v48 .. v53}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v40, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ILLEGAL_OPTION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 321
    new-instance v48, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v47, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v43, "ILLEGAL_OPTION_VALUE"

    const/16 v44, 0x2a

    const/16 v45, 0x54

    const-string v46, "unsupported option value \'<arg>=<arg2>\'"

    move-object/from16 v42, v48

    invoke-direct/range {v42 .. v47}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v48, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ILLEGAL_OPTION_VALUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 327
    new-instance v42, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v54, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v50, "ACTION_REDEFINITION"

    const/16 v51, 0x2b

    const/16 v52, 0x5e

    const-string v53, "redefinition of \'<arg>\' action"

    move-object/from16 v49, v42

    invoke-direct/range {v49 .. v54}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v42, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ACTION_REDEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 338
    new-instance v43, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v60, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v56, "NO_RULES"

    const/16 v57, 0x2c

    const/16 v58, 0x63

    const-string v59, "<if(arg2.implicitLexerOwner)>implicitly generated <endif>grammar \'<arg>\' has no rules"

    move-object/from16 v55, v43

    invoke-direct/range {v55 .. v60}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v43, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NO_RULES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 346
    new-instance v44, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v54, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v50, "NO_SUCH_GRAMMAR_SCOPE"

    const/16 v51, 0x2d

    const/16 v52, 0x69

    const-string v53, "reference to undefined grammar in rule reference: <arg>.<arg2>"

    move-object/from16 v49, v44

    invoke-direct/range {v49 .. v54}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v44, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NO_SUCH_GRAMMAR_SCOPE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 352
    new-instance v45, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v60, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v56, "NO_SUCH_RULE_IN_SCOPE"

    const/16 v57, 0x2e

    const/16 v58, 0x6a

    const-string v59, "rule \'<arg2>\' is not defined in grammar \'<arg>\'"

    move-object/from16 v55, v45

    invoke-direct/range {v55 .. v60}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v45, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NO_SUCH_RULE_IN_SCOPE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 358
    new-instance v46, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v54, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v50, "TOKEN_NAME_REASSIGNMENT"

    const/16 v51, 0x2f

    const/16 v52, 0x6c

    const-string v53, "token name \'<arg>\' is already defined"

    move-object/from16 v49, v46

    invoke-direct/range {v49 .. v54}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v46, Lgroovyjarjarantlr4/v4/tool/ErrorType;->TOKEN_NAME_REASSIGNMENT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 364
    new-instance v47, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v60, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v56, "OPTIONS_IN_DELEGATE"

    const/16 v57, 0x30

    const/16 v58, 0x6d

    const-string v59, "options ignored in imported grammar \'<arg>\'"

    move-object/from16 v55, v47

    invoke-direct/range {v55 .. v60}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v47, Lgroovyjarjarantlr4/v4/tool/ErrorType;->OPTIONS_IN_DELEGATE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 371
    new-instance v55, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v54, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v50, "CANNOT_FIND_IMPORTED_GRAMMAR"

    const/16 v51, 0x31

    const/16 v52, 0x6e

    const-string v53, "can\'t find or load grammar \'<arg>\'"

    move-object/from16 v49, v55

    invoke-direct/range {v49 .. v54}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v55, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_FIND_IMPORTED_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 379
    new-instance v49, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v61, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v57, "INVALID_IMPORT"

    const/16 v58, 0x32

    const/16 v59, 0x6f

    const-string v60, "<arg.typeString> grammar \'<arg.name>\' cannot import <arg2.typeString> grammar \'<arg2.name>\'"

    move-object/from16 v56, v49

    invoke-direct/range {v56 .. v61}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v49, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INVALID_IMPORT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 388
    new-instance v50, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v67, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v63, "IMPORT_NAME_CLASH"

    const/16 v64, 0x33

    const/16 v65, 0x71

    const-string v66, "<arg.typeString> grammar \'<arg.name>\' and imported <arg2.typeString> grammar \'<arg2.name>\' both generate \'<arg2.recognizerName>\'"

    move-object/from16 v62, v50

    invoke-direct/range {v62 .. v67}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v50, Lgroovyjarjarantlr4/v4/tool/ErrorType;->IMPORT_NAME_CLASH:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 394
    new-instance v51, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v61, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v57, "CANNOT_FIND_TOKENS_FILE_REFD_IN_GRAMMAR"

    const/16 v58, 0x34

    const/16 v59, 0x72

    const-string v60, "cannot find tokens file \'<arg>\'"

    move-object/from16 v56, v51

    invoke-direct/range {v56 .. v61}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v51, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_FIND_TOKENS_FILE_REFD_IN_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 404
    new-instance v52, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v67, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v63, "ALL_OPS_NEED_SAME_ASSOC"

    const/16 v64, 0x35

    const/16 v65, 0x76

    const-string v66, "all operators of alt \'<arg>\' of left-recursive rule must have same associativity"

    move-object/from16 v62, v52

    invoke-direct/range {v62 .. v67}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v52, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ALL_OPS_NEED_SAME_ASSOC:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 413
    new-instance v53, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v61, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v57, "LEFT_RECURSION_CYCLES"

    const/16 v58, 0x36

    const/16 v59, 0x77

    const-string v60, "The following sets of rules are mutually left-recursive <arg:{c| [<c:{r|<r.name>}; separator=\", \">]}; separator=\" and \">"

    move-object/from16 v56, v53

    invoke-direct/range {v56 .. v61}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v53, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LEFT_RECURSION_CYCLES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 419
    new-instance v54, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v67, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v63, "MODE_NOT_IN_LEXER"

    const/16 v64, 0x37

    const/16 v65, 0x78

    const-string v66, "lexical modes are only allowed in lexer grammars"

    move-object/from16 v62, v54

    invoke-direct/range {v62 .. v67}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v54, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MODE_NOT_IN_LEXER:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 425
    new-instance v62, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v61, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v57, "CANNOT_FIND_ATTRIBUTE_NAME_IN_DECL"

    const/16 v58, 0x38

    const/16 v59, 0x79

    const-string v60, "cannot find an attribute name in attribute declaration"

    move-object/from16 v56, v62

    invoke-direct/range {v56 .. v61}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v62, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_FIND_ATTRIBUTE_NAME_IN_DECL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 431
    new-instance v56, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v68, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v64, "RULE_WITH_TOO_FEW_ALT_LABELS"

    const/16 v65, 0x39

    const/16 v66, 0x7a

    const-string v67, "rule \'<arg>\': must label all alternatives or none"

    move-object/from16 v63, v56

    invoke-direct/range {v63 .. v68}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v56, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RULE_WITH_TOO_FEW_ALT_LABELS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 439
    new-instance v57, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v74, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v70, "ALT_LABEL_REDEF"

    const/16 v71, 0x3a

    const/16 v72, 0x7b

    const-string v73, "rule alt label \'<arg>\' redefined in rule \'<arg2>\', originally in rule \'<arg3>\'"

    move-object/from16 v69, v57

    invoke-direct/range {v69 .. v74}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v57, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ALT_LABEL_REDEF:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 446
    new-instance v58, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v68, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v64, "ALT_LABEL_CONFLICTS_WITH_RULE"

    const/16 v65, 0x3b

    const/16 v66, 0x7c

    const-string v67, "rule alt label \'<arg>\' conflicts with rule \'<arg2>\'"

    move-object/from16 v63, v58

    invoke-direct/range {v63 .. v68}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v58, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ALT_LABEL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 452
    new-instance v59, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v74, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v70, "IMPLICIT_TOKEN_DEFINITION"

    const/16 v71, 0x3c

    const/16 v72, 0x7d

    const-string v73, "implicit definition of token \'<arg>\' in parser"

    move-object/from16 v69, v59

    invoke-direct/range {v69 .. v74}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v59, Lgroovyjarjarantlr4/v4/tool/ErrorType;->IMPLICIT_TOKEN_DEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 460
    new-instance v60, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v68, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v64, "IMPLICIT_STRING_DEFINITION"

    const/16 v65, 0x3d

    const/16 v66, 0x7e

    const-string v67, "cannot create implicit token for string literal in non-combined grammar: <arg>"

    move-object/from16 v63, v60

    invoke-direct/range {v63 .. v68}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v60, Lgroovyjarjarantlr4/v4/tool/ErrorType;->IMPLICIT_STRING_DEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 468
    new-instance v61, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v74, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v70, "ATTRIBUTE_IN_LEXER_ACTION"

    const/16 v71, 0x3e

    const/16 v72, 0x80

    const-string v73, "attribute references not allowed in lexer actions: $<arg>"

    move-object/from16 v69, v61

    invoke-direct/range {v69 .. v74}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v61, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ATTRIBUTE_IN_LEXER_ACTION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 474
    new-instance v69, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v68, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v64, "LABEL_BLOCK_NOT_A_SET"

    const/16 v65, 0x3f

    const/16 v66, 0x82

    const-string v67, "label \'<arg>\' assigned to a block which is not a set"

    move-object/from16 v63, v69

    invoke-direct/range {v63 .. v68}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v69, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_BLOCK_NOT_A_SET:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 485
    new-instance v63, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v75, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v71, "EXPECTED_NON_GREEDY_WILDCARD_BLOCK"

    const/16 v72, 0x40

    const/16 v73, 0x83

    const-string v74, "greedy block ()<arg> contains wildcard; the non-greedy syntax ()<arg>? may be preferred"

    move-object/from16 v70, v63

    invoke-direct/range {v70 .. v75}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v63, Lgroovyjarjarantlr4/v4/tool/ErrorType;->EXPECTED_NON_GREEDY_WILDCARD_BLOCK:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 495
    new-instance v64, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v81, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v77, "LEXER_ACTION_PLACEMENT_ISSUE"

    const/16 v78, 0x41

    const/16 v79, 0x84

    const-string v80, "action in lexer rule \'<arg>\' must be last element of single outermost alt"

    move-object/from16 v76, v64

    invoke-direct/range {v76 .. v81}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v64, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LEXER_ACTION_PLACEMENT_ISSUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 504
    new-instance v65, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v75, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v71, "LEXER_COMMAND_PLACEMENT_ISSUE"

    const/16 v72, 0x42

    const/16 v73, 0x85

    const-string v74, "->command in lexer rule \'<arg>\' must be last element of single outermost alt"

    move-object/from16 v70, v65

    invoke-direct/range {v70 .. v75}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v65, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LEXER_COMMAND_PLACEMENT_ISSUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 516
    new-instance v66, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v81, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v77, "USE_OF_BAD_WORD"

    const/16 v78, 0x43

    const/16 v79, 0x86

    const-string v80, "symbol \'<arg>\' conflicts with generated code in target language or runtime"

    move-object/from16 v76, v66

    invoke-direct/range {v76 .. v81}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v66, Lgroovyjarjarantlr4/v4/tool/ErrorType;->USE_OF_BAD_WORD:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 526
    new-instance v67, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v75, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v71, "UNSUPPORTED_REFERENCE_IN_LEXER_SET"

    const/16 v72, 0x44

    const/16 v73, 0xb7

    const-string v74, "rule reference \'<arg>\' is not currently supported in a set"

    move-object/from16 v70, v67

    invoke-direct/range {v70 .. v75}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v67, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNSUPPORTED_REFERENCE_IN_LEXER_SET:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 532
    new-instance v68, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v81, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v77, "ASSIGNMENT_TO_LIST_LABEL"

    const/16 v78, 0x45

    const/16 v79, 0x87

    const-string v80, "cannot assign a value to list label \'<arg>\'"

    move-object/from16 v76, v68

    invoke-direct/range {v76 .. v81}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v68, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ASSIGNMENT_TO_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 538
    new-instance v76, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v75, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v71, "RETVAL_CONFLICTS_WITH_RULE"

    const/16 v72, 0x46

    const/16 v73, 0x88

    const-string v74, "return value \'<arg>\' conflicts with rule with same name"

    move-object/from16 v70, v76

    invoke-direct/range {v70 .. v75}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v76, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RETVAL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 544
    new-instance v70, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v82, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v78, "RETVAL_CONFLICTS_WITH_TOKEN"

    const/16 v79, 0x47

    const/16 v80, 0x89

    const-string v81, "return value \'<arg>\' conflicts with token with same name"

    move-object/from16 v77, v70

    invoke-direct/range {v77 .. v82}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v70, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RETVAL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 550
    new-instance v71, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v88, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v84, "ARG_CONFLICTS_WITH_RULE"

    const/16 v85, 0x48

    const/16 v86, 0x8a

    const-string v87, "parameter \'<arg>\' conflicts with rule with same name"

    move-object/from16 v83, v71

    invoke-direct/range {v83 .. v88}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v71, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ARG_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 556
    new-instance v72, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v82, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v78, "ARG_CONFLICTS_WITH_TOKEN"

    const/16 v79, 0x49

    const/16 v80, 0x8b

    const-string v81, "parameter \'<arg>\' conflicts with token with same name"

    move-object/from16 v77, v72

    invoke-direct/range {v77 .. v82}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v72, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ARG_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 562
    new-instance v73, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v88, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v84, "LOCAL_CONFLICTS_WITH_RULE"

    const/16 v85, 0x4a

    const/16 v86, 0x8c

    const-string v87, "local \'<arg>\' conflicts with rule with same name"

    move-object/from16 v83, v73

    invoke-direct/range {v83 .. v88}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v73, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LOCAL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 568
    new-instance v74, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v82, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v78, "LOCAL_CONFLICTS_WITH_TOKEN"

    const/16 v79, 0x4b

    const/16 v80, 0x8d

    const-string v81, "local \'<arg>\' conflicts with rule token same name"

    move-object/from16 v77, v74

    invoke-direct/range {v77 .. v82}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v74, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LOCAL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 574
    new-instance v75, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v88, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v84, "LOCAL_CONFLICTS_WITH_ARG"

    const/16 v85, 0x4c

    const/16 v86, 0x8e

    const-string v87, "local \'<arg>\' conflicts with parameter with same name"

    move-object/from16 v83, v75

    invoke-direct/range {v83 .. v88}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v75, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LOCAL_CONFLICTS_WITH_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 580
    new-instance v83, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v82, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v78, "LOCAL_CONFLICTS_WITH_RETVAL"

    const/16 v79, 0x4d

    const/16 v80, 0x8f

    const-string v81, "local \'<arg>\' conflicts with return value with same name"

    move-object/from16 v77, v83

    invoke-direct/range {v77 .. v82}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v83, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LOCAL_CONFLICTS_WITH_RETVAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 588
    new-instance v77, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v89, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v85, "INVALID_LITERAL_IN_LEXER_SET"

    const/16 v86, 0x4e

    const/16 v87, 0x90

    const-string v88, "multi-character literals are not allowed in lexer sets: <arg>"

    move-object/from16 v84, v77

    invoke-direct/range {v84 .. v89}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v77, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INVALID_LITERAL_IN_LEXER_SET:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 600
    new-instance v78, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v95, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v91, "MODE_WITHOUT_RULES"

    const/16 v92, 0x4f

    const/16 v93, 0x91

    const-string v94, "lexer mode \'<arg>\' must contain at least one non-fragment rule"

    move-object/from16 v90, v78

    invoke-direct/range {v90 .. v95}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v78, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MODE_WITHOUT_RULES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 619
    new-instance v79, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v89, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v85, "EPSILON_TOKEN"

    const/16 v86, 0x50

    const/16 v87, 0x92

    const-string v88, "non-fragment lexer rule \'<arg>\' can match the empty string"

    move-object/from16 v84, v79

    invoke-direct/range {v84 .. v89}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v79, Lgroovyjarjarantlr4/v4/tool/ErrorType;->EPSILON_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 639
    new-instance v80, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v95, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v91, "NO_NON_LR_ALTS"

    const/16 v92, 0x51

    const/16 v93, 0x93

    const-string v94, "left recursive rule \'<arg>\' must contain an alternative which is not left recursive"

    move-object/from16 v90, v80

    invoke-direct/range {v90 .. v95}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v80, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NO_NON_LR_ALTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 659
    new-instance v81, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v89, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v85, "EPSILON_LR_FOLLOW"

    const/16 v86, 0x52

    const/16 v87, 0x94

    const-string v88, "left recursive rule \'<arg>\' contains a left recursive alternative which can be followed by the empty string"

    move-object/from16 v84, v81

    invoke-direct/range {v84 .. v89}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v81, Lgroovyjarjarantlr4/v4/tool/ErrorType;->EPSILON_LR_FOLLOW:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 680
    new-instance v82, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v95, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v91, "INVALID_LEXER_COMMAND"

    const/16 v92, 0x53

    const/16 v93, 0x95

    const-string v94, "lexer command \'<arg>\' does not exist or is not supported by the current target"

    move-object/from16 v90, v82

    invoke-direct/range {v90 .. v95}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v82, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INVALID_LEXER_COMMAND:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 697
    new-instance v90, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v89, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v85, "MISSING_LEXER_COMMAND_ARGUMENT"

    const/16 v86, 0x54

    const/16 v87, 0x96

    const-string v88, "missing argument for lexer command \'<arg>\'"

    move-object/from16 v84, v90

    invoke-direct/range {v84 .. v89}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v90, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MISSING_LEXER_COMMAND_ARGUMENT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 715
    new-instance v84, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v96, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v92, "UNWANTED_LEXER_COMMAND_ARGUMENT"

    const/16 v93, 0x55

    const/16 v94, 0x97

    const-string v95, "lexer command \'<arg>\' does not take any arguments"

    move-object/from16 v91, v84

    invoke-direct/range {v91 .. v96}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v84, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNWANTED_LEXER_COMMAND_ARGUMENT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 732
    new-instance v85, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v102, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v98, "UNTERMINATED_STRING_LITERAL"

    const/16 v99, 0x56

    const/16 v100, 0x98

    const-string v101, "unterminated string literal"

    move-object/from16 v97, v85

    invoke-direct/range {v97 .. v102}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v85, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNTERMINATED_STRING_LITERAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 754
    new-instance v86, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v96, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v92, "EPSILON_CLOSURE"

    const/16 v93, 0x57

    const/16 v94, 0x99

    const-string v95, "rule \'<arg>\' contains a closure with at least one alternative that can match an empty string"

    move-object/from16 v91, v86

    invoke-direct/range {v91 .. v96}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v86, Lgroovyjarjarantlr4/v4/tool/ErrorType;->EPSILON_CLOSURE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 776
    new-instance v87, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v102, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v98, "EPSILON_OPTIONAL"

    const/16 v99, 0x58

    const/16 v100, 0x9a

    const-string v101, "rule \'<arg>\' contains an optional block with at least one alternative that can match an empty string"

    move-object/from16 v97, v87

    invoke-direct/range {v97 .. v102}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v87, Lgroovyjarjarantlr4/v4/tool/ErrorType;->EPSILON_OPTIONAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 803
    new-instance v88, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v96, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v92, "UNKNOWN_LEXER_CONSTANT"

    const/16 v93, 0x59

    const/16 v94, 0x9b

    const-string v95, "rule \'<arg>\' contains a lexer command with an unrecognized constant value; lexer interpreters may produce incorrect output"

    move-object/from16 v91, v88

    invoke-direct/range {v91 .. v96}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v88, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNKNOWN_LEXER_CONSTANT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 820
    new-instance v89, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v102, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v98, "INVALID_ESCAPE_SEQUENCE"

    const/16 v99, 0x5a

    const/16 v100, 0x9c

    const-string v101, "invalid escape sequence \'<arg>\'"

    move-object/from16 v97, v89

    invoke-direct/range {v97 .. v102}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v89, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INVALID_ESCAPE_SEQUENCE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 846
    new-instance v97, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v96, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v92, "UNRECOGNIZED_ASSOC_OPTION"

    const/16 v93, 0x5b

    const/16 v94, 0x9d

    const-string v95, "rule \'<arg>\' contains an \'assoc\' terminal option in an unrecognized location"

    move-object/from16 v91, v97

    invoke-direct/range {v91 .. v96}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v97, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNRECOGNIZED_ASSOC_OPTION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 877
    new-instance v91, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v103, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v99, "FRAGMENT_ACTION_IGNORED"

    const/16 v100, 0x5c

    const/16 v101, 0x9e

    const-string v102, "fragment rule \'<arg>\' contains an action or command which can never be executed"

    move-object/from16 v98, v91

    invoke-direct/range {v98 .. v103}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v91, Lgroovyjarjarantlr4/v4/tool/ErrorType;->FRAGMENT_ACTION_IGNORED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 894
    new-instance v92, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v109, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v105, "RESERVED_RULE_NAME"

    const/16 v106, 0x5d

    const/16 v107, 0x9f

    const-string v108, "cannot declare a rule with reserved name \'<arg>\'"

    move-object/from16 v104, v92

    invoke-direct/range {v104 .. v109}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v92, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RESERVED_RULE_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 902
    new-instance v93, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v103, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v99, "PARSER_RULE_REF_IN_LEXER_RULE"

    const/16 v100, 0x5e

    const/16 v101, 0xa0

    const-string v102, "reference to parser rule \'<arg>\' in lexer rule \'<arg2>\'"

    move-object/from16 v98, v93

    invoke-direct/range {v98 .. v103}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v93, Lgroovyjarjarantlr4/v4/tool/ErrorType;->PARSER_RULE_REF_IN_LEXER_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 908
    new-instance v94, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v109, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v105, "CHANNEL_CONFLICTS_WITH_TOKEN"

    const/16 v106, 0x5f

    const/16 v107, 0xa1

    const-string v108, "channel \'<arg>\' conflicts with token with same name"

    move-object/from16 v104, v94

    invoke-direct/range {v104 .. v109}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v94, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CHANNEL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 914
    new-instance v95, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v103, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v99, "CHANNEL_CONFLICTS_WITH_MODE"

    const/16 v100, 0x60

    const/16 v101, 0xa2

    const-string v102, "channel \'<arg>\' conflicts with mode with same name"

    move-object/from16 v98, v95

    invoke-direct/range {v98 .. v103}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v95, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CHANNEL_CONFLICTS_WITH_MODE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 920
    new-instance v96, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v109, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v105, "CHANNELS_BLOCK_IN_PARSER_GRAMMAR"

    const/16 v106, 0x61

    const/16 v107, 0xa3

    const-string v108, "custom channels are not supported in parser grammars"

    move-object/from16 v104, v96

    invoke-direct/range {v104 .. v109}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v96, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CHANNELS_BLOCK_IN_PARSER_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 926
    new-instance v104, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v103, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v99, "CHANNELS_BLOCK_IN_COMBINED_GRAMMAR"

    const/16 v100, 0x62

    const/16 v101, 0xa4

    const-string v102, "custom channels are not supported in combined grammars"

    move-object/from16 v98, v104

    invoke-direct/range {v98 .. v103}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v104, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CHANNELS_BLOCK_IN_COMBINED_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 932
    new-instance v98, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v110, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v106, "RULE_WITH_TOO_FEW_ALT_LABELS_GROUP"

    const/16 v107, 0x63

    const/16 v108, 0xa5

    const-string v109, "rule \'<arg>\': must label all alternatives in rules with the same base context, or none"

    move-object/from16 v105, v98

    invoke-direct/range {v105 .. v110}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v98, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RULE_WITH_TOO_FEW_ALT_LABELS_GROUP:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 938
    new-instance v99, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v116, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v112, "BASE_CONTEXT_MUST_BE_RULE_NAME"

    const/16 v113, 0x64

    const/16 v114, 0xa6

    const-string v115, "rule \'<arg>\': baseContext option value must reference a rule"

    move-object/from16 v111, v99

    invoke-direct/range {v111 .. v116}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v99, Lgroovyjarjarantlr4/v4/tool/ErrorType;->BASE_CONTEXT_MUST_BE_RULE_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 944
    new-instance v100, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v110, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v106, "BASE_CONTEXT_CANNOT_BE_TRANSITIVE"

    const/16 v107, 0x65

    const/16 v108, 0xa7

    const-string v109, "rule \'<arg>\': base context must reference a rule that does not specify a base context"

    move-object/from16 v105, v100

    invoke-direct/range {v105 .. v110}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v100, Lgroovyjarjarantlr4/v4/tool/ErrorType;->BASE_CONTEXT_CANNOT_BE_TRANSITIVE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 950
    new-instance v101, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v116, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v112, "LEXER_RULE_CANNOT_HAVE_BASE_CONTEXT"

    const/16 v113, 0x66

    const/16 v114, 0xa8

    const-string v115, "rule \'<arg>\': lexer rules cannot specify a base context"

    move-object/from16 v111, v101

    invoke-direct/range {v111 .. v116}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v101, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LEXER_RULE_CANNOT_HAVE_BASE_CONTEXT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 959
    new-instance v102, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v110, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v106, "NONCONFORMING_LR_RULE"

    const/16 v107, 0x67

    const/16 v108, 0xa9

    const-string v109, "rule \'<arg>\' is left recursive but doesn\'t conform to a pattern ANTLR can handle"

    move-object/from16 v105, v102

    invoke-direct/range {v105 .. v110}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v102, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NONCONFORMING_LR_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 973
    new-instance v103, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v116, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v112, "MODE_CONFLICTS_WITH_TOKEN"

    const/16 v113, 0x68

    const/16 v114, 0xaa

    const-string v115, "mode \'<arg>\' conflicts with token with same name"

    move-object/from16 v111, v103

    invoke-direct/range {v111 .. v116}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v103, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MODE_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 983
    new-instance v111, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v110, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v106, "TOKEN_CONFLICTS_WITH_COMMON_CONSTANTS"

    const/16 v107, 0x69

    const/16 v108, 0xab

    const-string v109, "cannot use or declare token with reserved name \'<arg>\'"

    move-object/from16 v105, v111

    invoke-direct/range {v105 .. v110}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v111, Lgroovyjarjarantlr4/v4/tool/ErrorType;->TOKEN_CONFLICTS_WITH_COMMON_CONSTANTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 993
    new-instance v105, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v117, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v113, "CHANNEL_CONFLICTS_WITH_COMMON_CONSTANTS"

    const/16 v114, 0x6a

    const/16 v115, 0xac

    const-string v116, "cannot use or declare channel with reserved name \'<arg>\'"

    move-object/from16 v112, v105

    invoke-direct/range {v112 .. v117}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v105, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CHANNEL_CONFLICTS_WITH_COMMON_CONSTANTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1003
    new-instance v106, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v123, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v119, "MODE_CONFLICTS_WITH_COMMON_CONSTANTS"

    const/16 v120, 0x6b

    const/16 v121, 0xad

    const-string v122, "cannot use or declare mode with reserved name \'<arg>\'"

    move-object/from16 v118, v106

    invoke-direct/range {v118 .. v123}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v106, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MODE_CONFLICTS_WITH_COMMON_CONSTANTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1017
    new-instance v107, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v117, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v113, "EMPTY_STRINGS_AND_SETS_NOT_ALLOWED"

    const/16 v114, 0x6c

    const/16 v115, 0xae

    const-string v116, "string literals and sets cannot be empty: <arg>"

    move-object/from16 v112, v107

    invoke-direct/range {v112 .. v117}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v107, Lgroovyjarjarantlr4/v4/tool/ErrorType;->EMPTY_STRINGS_AND_SETS_NOT_ALLOWED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1025
    new-instance v108, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v123, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v119, "CONSTANT_VALUE_IS_NOT_A_RECOGNIZED_TOKEN_NAME"

    const/16 v120, 0x6d

    const/16 v121, 0xaf

    const-string v122, "\'<arg>\' is not a recognized token name"

    move-object/from16 v118, v108

    invoke-direct/range {v118 .. v123}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v108, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CONSTANT_VALUE_IS_NOT_A_RECOGNIZED_TOKEN_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1033
    new-instance v109, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v117, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v113, "CONSTANT_VALUE_IS_NOT_A_RECOGNIZED_MODE_NAME"

    const/16 v114, 0x6e

    const/16 v115, 0xb0

    const-string v116, "\'<arg>\' is not a recognized mode name"

    move-object/from16 v112, v109

    invoke-direct/range {v112 .. v117}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v109, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CONSTANT_VALUE_IS_NOT_A_RECOGNIZED_MODE_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1041
    new-instance v110, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v123, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v119, "CONSTANT_VALUE_IS_NOT_A_RECOGNIZED_CHANNEL_NAME"

    const/16 v120, 0x6f

    const/16 v121, 0xb1

    const-string v122, "\'<arg>\' is not a recognized channel name"

    move-object/from16 v118, v110

    invoke-direct/range {v118 .. v123}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v110, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CONSTANT_VALUE_IS_NOT_A_RECOGNIZED_CHANNEL_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1049
    new-instance v118, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v117, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v113, "DUPLICATED_COMMAND"

    const/16 v114, 0x70

    const/16 v115, 0xb2

    const-string v116, "duplicated command \'<arg>\'"

    move-object/from16 v112, v118

    invoke-direct/range {v112 .. v117}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v118, Lgroovyjarjarantlr4/v4/tool/ErrorType;->DUPLICATED_COMMAND:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1057
    new-instance v112, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v124, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v120, "INCOMPATIBLE_COMMANDS"

    const/16 v121, 0x71

    const/16 v122, 0xb3

    const-string v123, "incompatible commands \'<arg>\' and \'<arg2>\'"

    move-object/from16 v119, v112

    invoke-direct/range {v119 .. v124}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v112, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INCOMPATIBLE_COMMANDS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1070
    new-instance v113, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v130, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v126, "CHARACTERS_COLLISION_IN_SET"

    const/16 v127, 0x72

    const/16 v128, 0xb4

    const-string v129, "chars \'<arg>\' used multiple times in set: <arg2>"

    move-object/from16 v125, v113

    invoke-direct/range {v125 .. v130}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v113, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CHARACTERS_COLLISION_IN_SET:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1085
    new-instance v114, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v124, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v120, "TOKEN_RANGE_IN_PARSER"

    const/16 v121, 0x73

    const/16 v122, 0xb5

    const-string v123, "token ranges not allowed in parser: <arg>..<arg2>"

    move-object/from16 v119, v114

    invoke-direct/range {v119 .. v124}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v114, Lgroovyjarjarantlr4/v4/tool/ErrorType;->TOKEN_RANGE_IN_PARSER:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1096
    new-instance v115, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v130, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v126, "UNICODE_PROPERTY_NOT_ALLOWED_IN_RANGE"

    const/16 v127, 0x74

    const/16 v128, 0xb6

    const-string v129, "unicode property escapes not allowed in lexer charset range: <arg>"

    move-object/from16 v125, v115

    invoke-direct/range {v125 .. v130}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v115, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNICODE_PROPERTY_NOT_ALLOWED_IN_RANGE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1111
    new-instance v116, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v124, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v120, "TOKEN_UNREACHABLE"

    const/16 v121, 0x75

    const/16 v122, 0xb8

    const-string v123, "One of the token \'<arg>\' values unreachable. \'<arg2>\' is always overlapped by token \'<arg3>\'"

    move-object/from16 v119, v116

    invoke-direct/range {v119 .. v124}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v116, Lgroovyjarjarantlr4/v4/tool/ErrorType;->TOKEN_UNREACHABLE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1131
    new-instance v117, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v130, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v126, "V3_TREE_GRAMMAR"

    const/16 v127, 0x76

    const/16 v128, 0xc8

    const-string v129, "tree grammars are not supported in ANTLR 4"

    move-object/from16 v125, v117

    invoke-direct/range {v125 .. v130}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v117, Lgroovyjarjarantlr4/v4/tool/ErrorType;->V3_TREE_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1146
    new-instance v125, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v124, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v120, "V3_LEXER_LABEL"

    const/16 v121, 0x77

    const/16 v122, 0xc9

    const-string v123, "labels in lexer rules are not supported in ANTLR 4; actions cannot reference elements of lexical rules but you can use getText() to get the entire text matched for the rule"

    move-object/from16 v119, v125

    invoke-direct/range {v119 .. v124}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v125, Lgroovyjarjarantlr4/v4/tool/ErrorType;->V3_LEXER_LABEL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1165
    new-instance v119, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v131, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v127, "V3_TOKENS_SYNTAX"

    const/16 v128, 0x78

    const/16 v129, 0xca

    const-string v130, "\'tokens {A; B;}\' syntax is now \'tokens {A, B}\' in ANTLR 4"

    move-object/from16 v126, v119

    invoke-direct/range {v126 .. v131}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v119, Lgroovyjarjarantlr4/v4/tool/ErrorType;->V3_TOKENS_SYNTAX:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1180
    new-instance v120, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v137, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v133, "V3_ASSIGN_IN_TOKENS"

    const/16 v134, 0x79

    const/16 v135, 0xcb

    const-string v136, "assignments in tokens{} are not supported in ANTLR 4; use lexical rule \'<arg> : <arg2>;\' instead"

    move-object/from16 v132, v120

    invoke-direct/range {v132 .. v137}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v120, Lgroovyjarjarantlr4/v4/tool/ErrorType;->V3_ASSIGN_IN_TOKENS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1195
    new-instance v121, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v131, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v127, "V3_GATED_SEMPRED"

    const/16 v128, 0x7a

    const/16 v129, 0xcc

    const-string v130, "{...}?=> explicitly gated semantic predicates are deprecated in ANTLR 4; use {...}? instead"

    move-object/from16 v126, v121

    invoke-direct/range {v126 .. v131}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v121, Lgroovyjarjarantlr4/v4/tool/ErrorType;->V3_GATED_SEMPRED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 1207
    new-instance v122, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    sget-object v137, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v133, "V3_SYNPRED"

    const/16 v134, 0x7b

    const/16 v135, 0xcd

    const-string v136, "(...)=> syntactic predicates are not supported in ANTLR 4"

    move-object/from16 v132, v122

    invoke-direct/range {v132 .. v137}, Lgroovyjarjarantlr4/v4/tool/ErrorType;-><init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V

    sput-object v122, Lgroovyjarjarantlr4/v4/tool/ErrorType;->V3_SYNPRED:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    move-object/from16 v123, v15

    const/16 v15, 0x7c

    new-array v15, v15, [Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/16 v124, 0x0

    aput-object v6, v15, v124

    const/4 v6, 0x1

    aput-object v0, v15, v6

    const/4 v0, 0x2

    aput-object v1, v15, v0

    const/4 v0, 0x3

    aput-object v2, v15, v0

    const/4 v0, 0x4

    aput-object v3, v15, v0

    const/4 v0, 0x5

    aput-object v4, v15, v0

    const/4 v0, 0x6

    aput-object v5, v15, v0

    const/4 v0, 0x7

    aput-object v13, v15, v0

    const/16 v0, 0x8

    aput-object v7, v15, v0

    const/16 v0, 0x9

    aput-object v8, v15, v0

    const/16 v0, 0xa

    aput-object v9, v15, v0

    const/16 v0, 0xb

    aput-object v10, v15, v0

    const/16 v0, 0xc

    aput-object v11, v15, v0

    const/16 v0, 0xd

    aput-object v16, v15, v0

    const/16 v0, 0xe

    aput-object v20, v15, v0

    const/16 v0, 0xf

    aput-object v14, v15, v0

    const/16 v0, 0x10

    aput-object v12, v15, v0

    const/16 v0, 0x11

    aput-object v123, v15, v0

    const/16 v0, 0x12

    aput-object v17, v15, v0

    const/16 v0, 0x13

    aput-object v18, v15, v0

    const/16 v0, 0x14

    aput-object v19, v15, v0

    const/16 v0, 0x15

    aput-object v27, v15, v0

    const/16 v0, 0x16

    aput-object v21, v15, v0

    const/16 v0, 0x17

    aput-object v22, v15, v0

    const/16 v0, 0x18

    aput-object v23, v15, v0

    const/16 v0, 0x19

    aput-object v24, v15, v0

    const/16 v0, 0x1a

    aput-object v25, v15, v0

    const/16 v0, 0x1b

    aput-object v26, v15, v0

    const/16 v0, 0x1c

    aput-object v34, v15, v0

    const/16 v0, 0x1d

    aput-object v28, v15, v0

    const/16 v0, 0x1e

    aput-object v29, v15, v0

    const/16 v0, 0x1f

    aput-object v30, v15, v0

    const/16 v0, 0x20

    aput-object v31, v15, v0

    const/16 v0, 0x21

    aput-object v32, v15, v0

    const/16 v0, 0x22

    aput-object v33, v15, v0

    const/16 v0, 0x23

    aput-object v41, v15, v0

    const/16 v0, 0x24

    aput-object v35, v15, v0

    const/16 v0, 0x25

    aput-object v36, v15, v0

    const/16 v0, 0x26

    aput-object v37, v15, v0

    const/16 v0, 0x27

    aput-object v38, v15, v0

    const/16 v0, 0x28

    aput-object v39, v15, v0

    const/16 v0, 0x29

    aput-object v40, v15, v0

    const/16 v0, 0x2a

    aput-object v48, v15, v0

    const/16 v0, 0x2b

    aput-object v42, v15, v0

    const/16 v0, 0x2c

    aput-object v43, v15, v0

    const/16 v0, 0x2d

    aput-object v44, v15, v0

    const/16 v0, 0x2e

    aput-object v45, v15, v0

    const/16 v0, 0x2f

    aput-object v46, v15, v0

    const/16 v0, 0x30

    aput-object v47, v15, v0

    const/16 v0, 0x31

    aput-object v55, v15, v0

    const/16 v0, 0x32

    aput-object v49, v15, v0

    const/16 v0, 0x33

    aput-object v50, v15, v0

    const/16 v0, 0x34

    aput-object v51, v15, v0

    const/16 v0, 0x35

    aput-object v52, v15, v0

    const/16 v0, 0x36

    aput-object v53, v15, v0

    const/16 v0, 0x37

    aput-object v54, v15, v0

    const/16 v0, 0x38

    aput-object v62, v15, v0

    const/16 v0, 0x39

    aput-object v56, v15, v0

    const/16 v0, 0x3a

    aput-object v57, v15, v0

    const/16 v0, 0x3b

    aput-object v58, v15, v0

    const/16 v0, 0x3c

    aput-object v59, v15, v0

    const/16 v0, 0x3d

    aput-object v60, v15, v0

    const/16 v0, 0x3e

    aput-object v61, v15, v0

    const/16 v0, 0x3f

    aput-object v69, v15, v0

    const/16 v0, 0x40

    aput-object v63, v15, v0

    const/16 v0, 0x41

    aput-object v64, v15, v0

    const/16 v0, 0x42

    aput-object v65, v15, v0

    const/16 v0, 0x43

    aput-object v66, v15, v0

    const/16 v0, 0x44

    aput-object v67, v15, v0

    const/16 v0, 0x45

    aput-object v68, v15, v0

    const/16 v0, 0x46

    aput-object v76, v15, v0

    const/16 v0, 0x47

    aput-object v70, v15, v0

    const/16 v0, 0x48

    aput-object v71, v15, v0

    const/16 v0, 0x49

    aput-object v72, v15, v0

    const/16 v0, 0x4a

    aput-object v73, v15, v0

    const/16 v0, 0x4b

    aput-object v74, v15, v0

    const/16 v0, 0x4c

    aput-object v75, v15, v0

    const/16 v0, 0x4d

    aput-object v83, v15, v0

    const/16 v0, 0x4e

    aput-object v77, v15, v0

    const/16 v0, 0x4f

    aput-object v78, v15, v0

    const/16 v0, 0x50

    aput-object v79, v15, v0

    const/16 v0, 0x51

    aput-object v80, v15, v0

    const/16 v0, 0x52

    aput-object v81, v15, v0

    const/16 v0, 0x53

    aput-object v82, v15, v0

    const/16 v0, 0x54

    aput-object v90, v15, v0

    const/16 v0, 0x55

    aput-object v84, v15, v0

    const/16 v0, 0x56

    aput-object v85, v15, v0

    const/16 v0, 0x57

    aput-object v86, v15, v0

    const/16 v0, 0x58

    aput-object v87, v15, v0

    const/16 v0, 0x59

    aput-object v88, v15, v0

    const/16 v0, 0x5a

    aput-object v89, v15, v0

    const/16 v0, 0x5b

    aput-object v97, v15, v0

    const/16 v0, 0x5c

    aput-object v91, v15, v0

    const/16 v0, 0x5d

    aput-object v92, v15, v0

    const/16 v0, 0x5e

    aput-object v93, v15, v0

    const/16 v0, 0x5f

    aput-object v94, v15, v0

    const/16 v0, 0x60

    aput-object v95, v15, v0

    const/16 v0, 0x61

    aput-object v96, v15, v0

    const/16 v0, 0x62

    aput-object v104, v15, v0

    const/16 v0, 0x63

    aput-object v98, v15, v0

    const/16 v0, 0x64

    aput-object v99, v15, v0

    const/16 v0, 0x65

    aput-object v100, v15, v0

    const/16 v0, 0x66

    aput-object v101, v15, v0

    const/16 v0, 0x67

    aput-object v102, v15, v0

    const/16 v0, 0x68

    aput-object v103, v15, v0

    const/16 v0, 0x69

    aput-object v111, v15, v0

    const/16 v0, 0x6a

    aput-object v105, v15, v0

    const/16 v0, 0x6b

    aput-object v106, v15, v0

    const/16 v0, 0x6c

    aput-object v107, v15, v0

    const/16 v0, 0x6d

    aput-object v108, v15, v0

    const/16 v0, 0x6e

    aput-object v109, v15, v0

    const/16 v0, 0x6f

    aput-object v110, v15, v0

    const/16 v0, 0x70

    aput-object v118, v15, v0

    const/16 v0, 0x71

    aput-object v112, v15, v0

    const/16 v0, 0x72

    aput-object v113, v15, v0

    const/16 v0, 0x73

    aput-object v114, v15, v0

    const/16 v0, 0x74

    aput-object v115, v15, v0

    const/16 v0, 0x75

    aput-object v116, v15, v0

    const/16 v0, 0x76

    aput-object v117, v15, v0

    const/16 v0, 0x77

    aput-object v125, v15, v0

    const/16 v0, 0x78

    aput-object v119, v15, v0

    const/16 v0, 0x79

    aput-object v120, v15, v0

    const/16 v0, 0x7a

    aput-object v121, v15, v0

    const/16 v0, 0x7b

    aput-object v122, v15, v0

    .line 21
    sput-object v15, Lgroovyjarjarantlr4/v4/tool/ErrorType;->$VALUES:[Lgroovyjarjarantlr4/v4/tool/ErrorType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;IILjava/lang/String;Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;",
            ")V"
        }
    .end annotation

    .line 1250
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 1251
    iput p3, p0, Lgroovyjarjarantlr4/v4/tool/ErrorType;->code:I

    .line 1252
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/tool/ErrorType;->msg:Ljava/lang/String;

    .line 1253
    iput-object p5, p0, Lgroovyjarjarantlr4/v4/tool/ErrorType;->severity:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ErrorType;
    .locals 1

    .line 21
    const-class v0, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    return-object p0
.end method

.method public static values()[Lgroovyjarjarantlr4/v4/tool/ErrorType;
    .locals 1

    .line 21
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorType;->$VALUES:[Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {v0}, [Lgroovyjarjarantlr4/v4/tool/ErrorType;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/tool/ErrorType;

    return-object v0
.end method
