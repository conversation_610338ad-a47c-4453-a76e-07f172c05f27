.class public Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;
.super Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;
.source "GrammarInterpreterRuleContext.java"


# instance fields
.field protected outerAltNum:I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V
    .locals 0

    .line 21
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V

    const/4 p1, 0x1

    .line 18
    iput p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;->outerAltNum:I

    return-void
.end method


# virtual methods
.method public getAltNumber()I
    .locals 1

    .line 37
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;->getOuterAltNum()I

    move-result v0

    return v0
.end method

.method public getOuterAltNum()I
    .locals 1

    .line 28
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;->outerAltNum:I

    return v0
.end method

.method public setAltNumber(I)V
    .locals 0

    .line 42
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;->setOuterAltNum(I)V

    return-void
.end method

.method public setOuterAltNum(I)V
    .locals 0

    .line 31
    iput p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarInterpreterRuleContext;->outerAltNum:I

    return-void
.end method
