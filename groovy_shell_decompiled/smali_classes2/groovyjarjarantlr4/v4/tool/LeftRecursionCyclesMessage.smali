.class public Lgroovyjarjarantlr4/v4/tool/LeftRecursionCyclesMessage;
.super Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;
.source "LeftRecursionCyclesMessage.java"


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/util/Collection;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Collection<",
            "+",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;>;)V"
        }
    .end annotation

    .line 15
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LEFT_RECURSION_CYCLES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-static {p2}, Lgroovyjarjarantlr4/v4/tool/LeftRecursionCyclesMessage;->getStartTokenOfFirstRule(Ljava/util/Collection;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v1

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p2, v2, v3

    invoke-direct {p0, v0, v1, v2}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 16
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/LeftRecursionCyclesMessage;->fileName:Ljava/lang/String;

    return-void
.end method

.method protected static getStartTokenOfFirstRule(Ljava/util/Collection;)Lgroovyjarjarantlr4/runtime/Token;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;>;)",
            "Lgroovyjarjarantlr4/runtime/Token;"
        }
    .end annotation

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return-object v0

    .line 24
    :cond_0
    invoke-interface {p0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Collection;

    if-nez v1, :cond_2

    return-object v0

    .line 29
    :cond_2
    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 30
    iget-object v3, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    if-eqz v3, :cond_3

    .line 31
    iget-object p0, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p0

    return-object p0

    :cond_4
    return-object v0
.end method
