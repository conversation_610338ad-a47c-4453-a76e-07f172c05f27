.class public interface abstract Lgroovyjarjarantlr4/v4/tool/AttributeResolver;
.super Ljava/lang/Object;
.source "AttributeResolver.java"


# virtual methods
.method public abstract resolveToAttribute(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;
.end method

.method public abstract resolveToAttribute(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;
.end method

.method public abstract resolvesToAttributeDict(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
.end method

.method public abstract resolvesToLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
.end method

.method public abstract resolvesToListLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
.end method

.method public abstract resolvesToToken(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
.end method
