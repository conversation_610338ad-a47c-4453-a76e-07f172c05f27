.class public Lgroovyjarjarantlr4/v4/tool/ToolMessage;
.super Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;
.source "ToolMessage.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;)V
    .locals 0

    .line 21
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    return-void
.end method

.method public varargs constructor <init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V
    .locals 1

    .line 27
    sget-object v0, Lgroovyjarjarantlr4/runtime/Token;->INVALID_TOKEN:Lgroovyjarjarantlr4/runtime/Token;

    invoke-direct {p0, p1, p2, v0, p3}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    return-void
.end method

.method public varargs constructor <init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V
    .locals 2

    .line 24
    sget-object v0, Lgroovyjarjarantlr4/runtime/Token;->INVALID_TOKEN:Lgroovyjarjarantlr4/runtime/Token;

    const/4 v1, 0x0

    invoke-direct {p0, p1, v1, v0, p2}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    return-void
.end method
