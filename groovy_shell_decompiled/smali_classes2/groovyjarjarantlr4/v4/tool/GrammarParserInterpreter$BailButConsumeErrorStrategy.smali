.class public Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;
.super Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;
.source "GrammarParserInterpreter.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "BailButConsumeErrorStrategy"
.end annotation


# instance fields
.field public firstErrorTokenIndex:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 430
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;-><init>()V

    const/4 v0, -0x1

    .line 431
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;->firstErrorTokenIndex:I

    return-void
.end method


# virtual methods
.method public recover(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 2

    .line 434
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object p2

    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->index()I

    move-result p2

    .line 435
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;->firstErrorTokenIndex:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    .line 436
    iput p2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;->firstErrorTokenIndex:I

    .line 439
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object p2

    .line 440
    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->index()I

    move-result v0

    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->size()I

    move-result p2

    add-int/lit8 p2, p2, -0x1

    if-ge v0, p2, :cond_1

    .line 441
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->consume()Lgroovyjarjarantlr4/v4/runtime/Token;

    :cond_1
    return-void
.end method

.method public recoverInline(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
        }
    .end annotation

    .line 447
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->index()I

    move-result v0

    .line 448
    iget v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;->firstErrorTokenIndex:I

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    .line 449
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/GrammarParserInterpreter$BailButConsumeErrorStrategy;->firstErrorTokenIndex:I

    .line 452
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 455
    throw v0
.end method

.method public sync(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 0

    return-void
.end method
