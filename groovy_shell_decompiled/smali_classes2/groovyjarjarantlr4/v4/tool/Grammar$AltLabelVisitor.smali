.class public Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;
.super Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;
.source "Grammar.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/tool/Grammar;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xc
    name = "AltLabelVisitor"
.end annotation


# instance fields
.field private final labeledAlternatives:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;>;>;"
        }
    .end annotation
.end field

.field private final unlabeledAlternatives:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V
    .locals 0

    .line 1401
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V

    .line 1395
    new-instance p1, Ljava/util/LinkedHashMap;

    invoke-direct {p1}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->labeledAlternatives:Ljava/util/Map;

    .line 1397
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->unlabeledAlternatives:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public discoverOuterAlt(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;)V
    .locals 3

    .line 1414
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v0, :cond_1

    .line 1415
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->labeledAlternatives:Ljava/util/Map;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    if-nez v0, :cond_0

    .line 1417
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 1418
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->labeledAlternatives:Ljava/util/Map;

    iget-object v2, p1, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1421
    :cond_0
    iget v1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->currentOuterAltNumber:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-static {v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 1424
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->unlabeledAlternatives:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public getLabeledAlternatives()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;>;>;"
        }
    .end annotation

    .line 1405
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->labeledAlternatives:Ljava/util/Map;

    return-object v0
.end method

.method public getUnlabeledAlternatives()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;"
        }
    .end annotation

    .line 1409
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$AltLabelVisitor;->unlabeledAlternatives:Ljava/util/List;

    return-object v0
.end method
