.class public final enum Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;
.super Ljava/lang/Enum;
.source "AttributeDict.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/tool/AttributeDict;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "DictType"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

.field public static final enum ARG:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

.field public static final enum LOCAL:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

.field public static final enum PREDEFINED_LEXER_RULE:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

.field public static final enum PREDEFINED_RULE:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

.field public static final enum RET:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

.field public static final enum TOKEN:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;


# direct methods
.method static constructor <clinit>()V
    .locals 13

    .line 47
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    const-string v1, "ARG"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->ARG:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    new-instance v1, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    const-string v3, "RET"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->RET:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    new-instance v3, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    const-string v5, "LOCAL"

    const/4 v6, 0x2

    invoke-direct {v3, v5, v6}, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->LOCAL:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    new-instance v5, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    const-string v7, "TOKEN"

    const/4 v8, 0x3

    invoke-direct {v5, v7, v8}, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;-><init>(Ljava/lang/String;I)V

    sput-object v5, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->TOKEN:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    .line 48
    new-instance v7, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    const-string v9, "PREDEFINED_RULE"

    const/4 v10, 0x4

    invoke-direct {v7, v9, v10}, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;-><init>(Ljava/lang/String;I)V

    sput-object v7, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->PREDEFINED_RULE:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    new-instance v9, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    const-string v11, "PREDEFINED_LEXER_RULE"

    const/4 v12, 0x5

    invoke-direct {v9, v11, v12}, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;-><init>(Ljava/lang/String;I)V

    sput-object v9, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->PREDEFINED_LEXER_RULE:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    const/4 v11, 0x6

    new-array v11, v11, [Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    aput-object v0, v11, v2

    aput-object v1, v11, v4

    aput-object v3, v11, v6

    aput-object v5, v11, v8

    aput-object v7, v11, v10

    aput-object v9, v11, v12

    .line 46
    sput-object v11, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->$VALUES:[Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 46
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;
    .locals 1

    .line 46
    const-class v0, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    return-object p0
.end method

.method public static values()[Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;
    .locals 1

    .line 46
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->$VALUES:[Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    invoke-virtual {v0}, [Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    return-object v0
.end method
