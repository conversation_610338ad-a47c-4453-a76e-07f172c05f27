.class public Lgroovyjarjarantlr4/v4/tool/Alternative;
.super Ljava/lang/Object;
.source "Alternative.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/tool/AttributeResolver;


# instance fields
.field public actions:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ">;"
        }
    .end annotation
.end field

.field public altNum:I

.field public ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

.field public labelDefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/LabelElementPair;",
            ">;"
        }
    .end annotation
.end field

.field public rule:Lgroovyjarjarantlr4/v4/tool/Rule;

.field public ruleRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public ruleRefsInActions:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public tokenRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;",
            ">;"
        }
    .end annotation
.end field

.field public tokenRefsInActions:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Rule;I)V
    .locals 1

    .line 54
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 28
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->tokenRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    .line 31
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->tokenRefsInActions:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    .line 34
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->ruleRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    .line 37
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->ruleRefsInActions:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    .line 40
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->labelDefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    .line 52
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->actions:Ljava/util/List;

    .line 54
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iput p2, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->altNum:I

    return-void
.end method


# virtual methods
.method public getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;
    .locals 1

    .line 124
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->labelDefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    if-eqz p1, :cond_0

    const/4 v0, 0x0

    .line 125
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public resolveToAttribute(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 1

    .line 77
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->resolveToAttribute(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    return-object p1
.end method

.method public resolveToAttribute(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 1

    .line 85
    iget-object p3, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->tokenRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p3

    if-eqz p3, :cond_0

    .line 86
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    sget-object p3, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {p1, p3}, Lgroovyjarjarantlr4/v4/tool/Rule;->getPredefinedScope(Lgroovyjarjarantlr4/v4/tool/LabelType;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    move-result-object p1

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    return-object p1

    .line 88
    :cond_0
    iget-object p3, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->ruleRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p3

    if-eqz p3, :cond_1

    .line 90
    iget-object p3, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object p3, p3, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->resolveRetvalOrProperty(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    return-object p1

    .line 92
    :cond_1
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Alternative;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    if-eqz p1, :cond_2

    .line 93
    iget-object p3, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v0, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne p3, v0, :cond_2

    .line 94
    iget-object p3, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object p3, p3, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->element:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->resolveRetvalOrProperty(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    return-object p1

    :cond_2
    const/4 p3, 0x0

    if-eqz p1, :cond_4

    .line 97
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getPredefinedScope(Lgroovyjarjarantlr4/v4/tool/LabelType;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    move-result-object p1

    if-nez p1, :cond_3

    return-object p3

    .line 102
    :cond_3
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    return-object p1

    :cond_4
    return-object p3
.end method

.method public resolveToRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;
    .locals 2

    .line 131
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->ruleRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    return-object p1

    .line 132
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Alternative;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 133
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne v0, v1, :cond_1

    .line 134
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->element:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    return-object p1

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public resolvesToAttributeDict(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 1

    .line 66
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/Alternative;->resolvesToToken(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z

    move-result p2

    const/4 v0, 0x1

    if-eqz p2, :cond_0

    return v0

    .line 67
    :cond_0
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->ruleRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    if-eqz p2, :cond_1

    return v0

    .line 68
    :cond_1
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Alternative;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    if-eqz p1, :cond_2

    .line 69
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne p1, p2, :cond_2

    return v0

    :cond_2
    const/4 p1, 0x0

    return p1
.end method

.method public resolvesToLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 1

    .line 109
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Alternative;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 110
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v0, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-eq p2, v0, :cond_0

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne p1, p2, :cond_1

    :cond_0
    const/4 p1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public resolvesToListLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 1

    .line 117
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Alternative;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 118
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v0, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-eq p2, v0, :cond_0

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne p1, p2, :cond_1

    :cond_0
    const/4 p1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public resolvesToToken(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 1

    .line 58
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Alternative;->tokenRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    const/4 v0, 0x1

    if-eqz p2, :cond_0

    return v0

    .line 59
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Alternative;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 60
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne p1, p2, :cond_1

    return v0

    :cond_1
    const/4 p1, 0x0

    return p1
.end method
