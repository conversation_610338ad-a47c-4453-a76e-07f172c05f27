.class public final enum Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;
.super Ljava/lang/Enum;
.source "ErrorSeverity.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

.field public static final enum ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

.field public static final enum ERROR_ONE_OFF:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

.field public static final enum FATAL:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

.field public static final enum INFO:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

.field public static final enum WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

.field public static final enum WARNING_ONE_OFF:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;


# instance fields
.field private final text:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 14

    .line 16
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v1, "INFO"

    const/4 v2, 0x0

    const-string v3, "info"

    invoke-direct {v0, v1, v2, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->INFO:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    .line 17
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v3, "WARNING"

    const/4 v4, 0x1

    const-string v5, "warning"

    invoke-direct {v1, v3, v4, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    .line 18
    new-instance v3, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v6, "WARNING_ONE_OFF"

    const/4 v7, 0x2

    invoke-direct {v3, v6, v7, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->WARNING_ONE_OFF:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    .line 19
    new-instance v5, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v6, "ERROR"

    const/4 v8, 0x3

    const-string v9, "error"

    invoke-direct {v5, v6, v8, v9}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v5, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    .line 20
    new-instance v6, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v10, "ERROR_ONE_OFF"

    const/4 v11, 0x4

    invoke-direct {v6, v10, v11, v9}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v6, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ERROR_ONE_OFF:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    .line 21
    new-instance v9, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const-string v10, "FATAL"

    const/4 v12, 0x5

    const-string v13, "fatal"

    invoke-direct {v9, v10, v12, v13}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v9, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->FATAL:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    const/4 v10, 0x6

    new-array v10, v10, [Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    aput-object v0, v10, v2

    aput-object v1, v10, v4

    aput-object v3, v10, v7

    aput-object v5, v10, v8

    aput-object v6, v10, v11

    aput-object v9, v10, v12

    .line 15
    sput-object v10, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->$VALUES:[Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;ILjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 42
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    iput-object p3, p0, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->text:Ljava/lang/String;

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;
    .locals 1

    .line 15
    const-class v0, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    return-object p0
.end method

.method public static values()[Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;
    .locals 1

    .line 15
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->$VALUES:[Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    invoke-virtual {v0}, [Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    return-object v0
.end method


# virtual methods
.method public getText()Ljava/lang/String;
    .locals 1

    .line 35
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->text:Ljava/lang/String;

    return-object v0
.end method
