.class public Lgroovyjarjarantlr4/v4/tool/ErrorManager;
.super Ljava/lang/Object;
.source "ErrorManager.java"


# static fields
.field public static final FORMATS_DIR:Ljava/lang/String; = "groovyjarjarantlr4/v4/tool/templates/messages/formats/"


# instance fields
.field public errorTypes:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lgroovyjarjarantlr4/v4/tool/ErrorType;",
            ">;"
        }
    .end annotation
.end field

.field public errors:I

.field format:Lorg/stringtemplate/v4/STGroup;

.field formatName:Ljava/lang/String;

.field initSTListener:Lorg/stringtemplate/v4/misc/ErrorBuffer;

.field public tool:Lgroovyjarjarantlr4/v4/Tool;

.field public warnings:I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/Tool;)V
    .locals 1

    .line 38
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 29
    const-class v0, Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-static {v0}, Ljava/util/EnumSet;->noneOf(Ljava/lang/Class;)Ljava/util/EnumSet;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errorTypes:Ljava/util/Set;

    .line 36
    new-instance v0, Lorg/stringtemplate/v4/misc/ErrorBuffer;

    invoke-direct {v0}, Lorg/stringtemplate/v4/misc/ErrorBuffer;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->initSTListener:Lorg/stringtemplate/v4/misc/ErrorBuffer;

    .line 39
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->tool:Lgroovyjarjarantlr4/v4/Tool;

    return-void
.end method

.method public static fatalInternalError(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 1

    .line 125
    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->internalError(Ljava/lang/String;Ljava/lang/Throwable;)V

    .line 126
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0, p0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0
.end method

.method private static getLastNonErrorManagerCodeLocation(Ljava/lang/Throwable;)Ljava/lang/StackTraceElement;
    .locals 3

    .line 178
    invoke-virtual {p0}, Ljava/lang/Throwable;->getStackTrace()[Ljava/lang/StackTraceElement;

    move-result-object p0

    const/4 v0, 0x0

    .line 180
    :goto_0
    array-length v1, p0

    if-ge v0, v1, :cond_1

    .line 181
    aget-object v1, p0, v0

    .line 182
    invoke-virtual {v1}, Ljava/lang/StackTraceElement;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "ErrorManager"

    invoke-virtual {v1, v2}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_1

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 186
    :cond_1
    :goto_1
    aget-object p0, p0, v0

    return-object p0
.end method

.method public static internalError(Ljava/lang/String;)V
    .locals 3

    .line 135
    new-instance v0, Ljava/lang/Exception;

    invoke-direct {v0}, Ljava/lang/Exception;-><init>()V

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getLastNonErrorManagerCodeLocation(Ljava/lang/Throwable;)Ljava/lang/StackTraceElement;

    move-result-object v0

    .line 137
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ": "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    .line 138
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "internal error: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method

.method public static internalError(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 3

    .line 130
    invoke-static {p1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getLastNonErrorManagerCodeLocation(Ljava/lang/Throwable;)Ljava/lang/StackTraceElement;

    move-result-object v0

    .line 131
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Exception "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, "@"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ": "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->internalError(Ljava/lang/String;)V

    return-void
.end method

.method public static panic()V
    .locals 2

    .line 301
    new-instance v0, Ljava/lang/Error;

    const-string v1, "ANTLR ErrorManager panic"

    invoke-direct {v0, v1}, Ljava/lang/Error;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public static panic(Ljava/lang/String;)V
    .locals 0

    .line 294
    invoke-static {p0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->rawError(Ljava/lang/String;)V

    .line 295
    invoke-static {}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->panic()V

    return-void
.end method

.method static rawError(Ljava/lang/String;)V
    .locals 1

    .line 275
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {v0, p0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method

.method static rawError(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 0

    .line 279
    invoke-static {p0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->rawError(Ljava/lang/String;)V

    .line 280
    sget-object p0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {p1, p0}, Ljava/lang/Throwable;->printStackTrace(Ljava/io/PrintStream;)V

    return-void
.end method


# virtual methods
.method public emit(Lgroovyjarjarantlr4/v4/tool/ErrorType;Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V
    .locals 3

    .line 194
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorManager$1;->$SwitchMap$org$antlr$v4$tool$ErrorSeverity:[I

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->severity:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->ordinal()I

    move-result v1

    aget v0, v0, v1

    const/4 v1, 0x1

    if-eq v0, v1, :cond_2

    const/4 v2, 0x2

    if-eq v0, v2, :cond_3

    const/4 v2, 0x3

    if-eq v0, v2, :cond_0

    const/4 v2, 0x4

    if-eq v0, v2, :cond_1

    goto :goto_0

    .line 203
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errorTypes:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    .line 206
    :cond_1
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errors:I

    add-int/2addr v0, v1

    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errors:I

    .line 207
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v0, p2}, Lgroovyjarjarantlr4/v4/Tool;->error(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    goto :goto_0

    .line 196
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errorTypes:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_0

    .line 199
    :cond_3
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->warnings:I

    add-int/2addr v0, v1

    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->warnings:I

    .line 200
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v0, p2}, Lgroovyjarjarantlr4/v4/Tool;->warning(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    .line 210
    :goto_0
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errorTypes:Ljava/util/Set;

    invoke-interface {p2, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public formatWantsSingleLineMessage()Z
    .locals 2

    .line 109
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->format:Lorg/stringtemplate/v4/STGroup;

    const-string v1, "wantsSingleLineMessage"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v0

    const-string v1, "true"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    return v0
.end method

.method public getLocationFormat()Lorg/stringtemplate/v4/ST;
    .locals 2

    .line 96
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->format:Lorg/stringtemplate/v4/STGroup;

    const-string v1, "location"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    return-object v0
.end method

.method public getMessageFormat()Lorg/stringtemplate/v4/ST;
    .locals 2

    .line 106
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->format:Lorg/stringtemplate/v4/STGroup;

    const-string v1, "message"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    return-object v0
.end method

.method public getMessageTemplate(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)Lorg/stringtemplate/v4/ST;
    .locals 8

    .line 48
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-boolean v0, v0, Lgroovyjarjarantlr4/v4/Tool;->longMessages:Z

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getMessageTemplate(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    .line 49
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getLocationFormat()Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 50
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getErrorType()Lgroovyjarjarantlr4/v4/tool/ErrorType;

    move-result-object v2

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->severity:Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getReportFormat(Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)Lorg/stringtemplate/v4/ST;

    move-result-object v2

    .line 51
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getMessageFormat()Lorg/stringtemplate/v4/ST;

    move-result-object v3

    .line 54
    iget v4, p1, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->line:I

    const/4 v5, -0x1

    const/4 v6, 0x1

    if-eq v4, v5, :cond_0

    .line 55
    iget v4, p1, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->line:I

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v7, "line"

    invoke-virtual {v1, v7, v4}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    move v4, v6

    goto :goto_0

    :cond_0
    const/4 v4, 0x0

    .line 58
    :goto_0
    iget v7, p1, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->charPosition:I

    if-eq v7, v5, :cond_1

    .line 59
    iget v4, p1, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->charPosition:I

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v5, "column"

    invoke-virtual {v1, v5, v4}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    move v4, v6

    .line 62
    :cond_1
    iget-object v5, p1, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->fileName:Ljava/lang/String;

    if-eqz v5, :cond_3

    .line 63
    iget-object v4, p1, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->fileName:Ljava/lang/String;

    .line 64
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->format:Lorg/stringtemplate/v4/STGroup;

    const-string v7, "antlr"

    invoke-virtual {v5, v7}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_2

    .line 67
    new-instance v5, Ljava/io/File;

    iget-object v7, p1, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->fileName:Ljava/lang/String;

    invoke-direct {v5, v7}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 68
    invoke-virtual {v5}, Ljava/io/File;->exists()Z

    move-result v7

    if-eqz v7, :cond_2

    .line 69
    invoke-virtual {v5}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v4

    :cond_2
    const-string v5, "file"

    .line 78
    invoke-virtual {v1, v5, v4}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_1

    :cond_3
    move v6, v4

    .line 82
    :goto_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;->getErrorType()Lgroovyjarjarantlr4/v4/tool/ErrorType;

    move-result-object p1

    iget p1, p1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->code:I

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    const-string v4, "id"

    invoke-virtual {v3, v4, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    const-string p1, "text"

    .line 83
    invoke-virtual {v3, p1, v0}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    if-eqz v6, :cond_4

    const-string p1, "location"

    .line 85
    invoke-virtual {v2, p1, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    :cond_4
    const-string p1, "message"

    .line 86
    invoke-virtual {v2, p1, v3}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    return-object v2
.end method

.method public getNumErrors()I
    .locals 1

    .line 173
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errors:I

    return v0
.end method

.method public getReportFormat(Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;)Lorg/stringtemplate/v4/ST;
    .locals 2

    .line 100
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->format:Lorg/stringtemplate/v4/STGroup;

    const-string v1, "report"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    .line 101
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ErrorSeverity;->getText()Ljava/lang/String;

    move-result-object p1

    const-string v1, "type"

    invoke-virtual {v0, v1, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    return-object v0
.end method

.method public varargs grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V
    .locals 1

    .line 161
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/GrammarSemanticsMessage;

    invoke-direct {v0, p1, p2, p3, p4}, Lgroovyjarjarantlr4/v4/tool/GrammarSemanticsMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 162
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->emit(Lgroovyjarjarantlr4/v4/tool/ErrorType;Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    return-void
.end method

.method public info(Ljava/lang/String;)V
    .locals 1

    .line 112
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/Tool;->info(Ljava/lang/String;)V

    return-void
.end method

.method public leftRecursionCycles(Ljava/lang/String;Ljava/util/Collection;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Collection<",
            "+",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;>;)V"
        }
    .end annotation

    .line 167
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errors:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errors:I

    .line 168
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/LeftRecursionCyclesMessage;

    invoke-direct {v0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/LeftRecursionCyclesMessage;-><init>(Ljava/lang/String;Ljava/util/Collection;)V

    .line 169
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/Tool;->error(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    return-void
.end method

.method public varargs panic(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V
    .locals 1

    .line 284
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ToolMessage;

    invoke-direct {v0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/ToolMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    .line 285
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getMessageTemplate(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    .line 286
    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    .line 287
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->formatWantsSingleLineMessage()Z

    move-result p2

    if-eqz p2, :cond_0

    const/16 p2, 0xa

    const/16 v0, 0x20

    .line 288
    invoke-virtual {p1, p2, v0}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p1

    .line 290
    :cond_0
    invoke-static {p1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->panic(Ljava/lang/String;)V

    return-void
.end method

.method public resetErrorState()V
    .locals 1

    const/4 v0, 0x0

    .line 43
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->errors:I

    .line 44
    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->warnings:I

    return-void
.end method

.method public setFormat(Ljava/lang/String;)V
    .locals 6

    .line 217
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->formatName:Ljava/lang/String;

    .line 218
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "groovyjarjarantlr4/v4/tool/templates/messages/formats/"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lorg/stringtemplate/v4/STGroup;->GROUP_FILE_EXTENSION:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 219
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Thread;->getContextClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    .line 220
    invoke-virtual {v1, v0}, Ljava/lang/ClassLoader;->getResource(Ljava/lang/String;)Ljava/net/URL;

    move-result-object v1

    if-nez v1, :cond_0

    .line 222
    const-class v1, Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    .line 223
    invoke-virtual {v1, v0}, Ljava/lang/ClassLoader;->getResource(Ljava/lang/String;)Ljava/net/URL;

    move-result-object v1

    :cond_0
    const-string v2, "antlr"

    if-nez v1, :cond_1

    .line 225
    invoke-virtual {p1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 226
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "ANTLR installation corrupted; cannot find ANTLR messages format file "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->rawError(Ljava/lang/String;)V

    .line 227
    invoke-static {}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->panic()V

    goto :goto_0

    :cond_1
    if-nez v1, :cond_2

    .line 230
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "no such message format file "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " retrying with default ANTLR format"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->rawError(Ljava/lang/String;)V

    .line 231
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->setFormat(Ljava/lang/String;)V

    return-void

    .line 234
    :cond_2
    :goto_0
    new-instance v0, Lorg/stringtemplate/v4/STGroupFile;

    const/16 v3, 0x3c

    const/16 v4, 0x3e

    const-string v5, "UTF-8"

    invoke-direct {v0, v1, v5, v3, v4}, Lorg/stringtemplate/v4/STGroupFile;-><init>(Ljava/net/URL;Ljava/lang/String;CC)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->format:Lorg/stringtemplate/v4/STGroup;

    .line 235
    invoke-virtual {v0}, Lorg/stringtemplate/v4/STGroup;->load()V

    .line 237
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->initSTListener:Lorg/stringtemplate/v4/misc/ErrorBuffer;

    iget-object v0, v0, Lorg/stringtemplate/v4/misc/ErrorBuffer;->errors:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_3

    .line 238
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ANTLR installation corrupted; can\'t load messages format file:\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->initSTListener:Lorg/stringtemplate/v4/misc/ErrorBuffer;

    invoke-virtual {v1}, Lorg/stringtemplate/v4/misc/ErrorBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->rawError(Ljava/lang/String;)V

    .line 240
    invoke-static {}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->panic()V

    .line 243
    :cond_3
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->verifyFormat()Z

    move-result v0

    if-nez v0, :cond_4

    .line 244
    invoke-virtual {p1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_4

    .line 245
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ANTLR installation corrupted; ANTLR messages format file "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ".stg incomplete"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->rawError(Ljava/lang/String;)V

    .line 246
    invoke-static {}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->panic()V

    goto :goto_1

    :cond_4
    if-nez v0, :cond_5

    .line 249
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->setFormat(Ljava/lang/String;)V

    :cond_5
    :goto_1
    return-void
.end method

.method public varargs syntaxError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/Object;)V
    .locals 7

    .line 120
    new-instance v6, Lgroovyjarjarantlr4/v4/tool/GrammarSyntaxMessage;

    move-object v0, v6

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/tool/GrammarSyntaxMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/Object;)V

    .line 121
    invoke-virtual {p0, p1, v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->emit(Lgroovyjarjarantlr4/v4/tool/ErrorType;Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    return-void
.end method

.method public varargs toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V
    .locals 1

    .line 152
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ToolMessage;

    invoke-direct {v0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/tool/ToolMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    .line 153
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->emit(Lgroovyjarjarantlr4/v4/tool/ErrorType;Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    return-void
.end method

.method public varargs toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V
    .locals 1

    const/4 v0, 0x0

    .line 148
    invoke-virtual {p0, p1, v0, p2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    return-void
.end method

.method protected verifyFormat()Z
    .locals 4

    .line 256
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->format:Lorg/stringtemplate/v4/STGroup;

    const-string v1, "location"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->isDefined(Ljava/lang/String;)Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 257
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Format template \'location\' not found in "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->formatName:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    move v0, v1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    .line 260
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->format:Lorg/stringtemplate/v4/STGroup;

    const-string v3, "message"

    invoke-virtual {v2, v3}, Lorg/stringtemplate/v4/STGroup;->isDefined(Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_1

    .line 261
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Format template \'message\' not found in "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->formatName:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    move v0, v1

    .line 264
    :cond_1
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->format:Lorg/stringtemplate/v4/STGroup;

    const-string v3, "report"

    invoke-virtual {v2, v3}, Lorg/stringtemplate/v4/STGroup;->isDefined(Ljava/lang/String;)Z

    move-result v2

    if-nez v2, :cond_2

    .line 265
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Format template \'report\' not found in "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->formatName:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    goto :goto_1

    :cond_2
    move v1, v0

    :goto_1
    return v1
.end method
