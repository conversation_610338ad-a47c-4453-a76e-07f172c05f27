.class final Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$2;
.super Ljava/lang/Object;
.source "GrammarTransformPipeline.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/tree/TreeVisitorAction;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->setGrammarPtr(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic val$g:Lgroovyjarjarantlr4/v4/tool/Grammar;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 0

    .line 101
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$2;->val$g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public post(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    return-object p1
.end method

.method public pre(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 103
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline$2;->val$g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    return-object p1
.end method
