.class public Lgroovyjarjarantlr4/v4/tool/GrammarSemanticsMessage;
.super Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;
.source "GrammarSemanticsMessage.java"


# direct methods
.method public varargs constructor <init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V
    .locals 0

    .line 20
    invoke-direct {p0, p1, p3, p4}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 21
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/GrammarSemanticsMessage;->fileName:Ljava/lang/String;

    if-eqz p3, :cond_0

    .line 23
    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarSemanticsMessage;->line:I

    .line 24
    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/tool/GrammarSemanticsMessage;->charPosition:I

    :cond_0
    return-void
.end method
