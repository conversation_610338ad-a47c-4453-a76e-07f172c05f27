.class Lgroovyjarjarantlr4/v4/tool/Grammar$2;
.super Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;
.source "Grammar.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/tool/Grammar;->getStringLiterals()Ljava/util/Set;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field final synthetic val$strings:Ljava/util/Set;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/Set;)V
    .locals 0

    .line 1296
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$2;->this$0:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$2;->val$strings:Ljava/util/Set;

    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;-><init>()V

    return-void
.end method


# virtual methods
.method public getErrorManager()Lgroovyjarjarantlr4/v4/tool/ErrorManager;
    .locals 1

    .line 1302
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$2;->this$0:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    return-object v0
.end method

.method public stringRef(Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;)V
    .locals 1

    .line 1299
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar$2;->val$strings:Ljava/util/Set;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void
.end method
