.class public Lgroovyjarjarantlr4/v4/tool/Rule;
.super Ljava/lang/Object;
.source "Rule.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/tool/AttributeResolver;


# static fields
.field public static final predefinedRulePropertiesDict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

.field public static final validLexerCommands:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public actionIndex:I

.field public actions:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ">;"
        }
    .end annotation
.end field

.field public alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

.field public args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

.field public ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

.field private baseContext:Ljava/lang/String;

.field public exceptions:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public finallyAction:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

.field public g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public index:I

.field public isStartRule:Z

.field public locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

.field public mode:Ljava/lang/String;

.field public modifiers:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public name:Ljava/lang/String;

.field public namedActions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ">;"
        }
    .end annotation
.end field

.field public numberOfAlts:I

.field public retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 33
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->PREDEFINED_RULE:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;-><init>(Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;)V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Rule;->predefinedRulePropertiesDict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    .line 36
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "parser"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 37
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "text"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 38
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "start"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 39
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "stop"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 40
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/Attribute;

    const-string v2, "ctx"

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 43
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/tool/Rule;->validLexerCommands:Ljava/util/Set;

    const-string v1, "mode"

    .line 46
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v1, "pushMode"

    .line 47
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v1, "type"

    .line 48
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v1, "channel"

    .line 49
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v1, "popMode"

    .line 52
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v1, "skip"

    .line 53
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    const-string v1, "more"

    .line 54
    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;I)V
    .locals 2

    .line 108
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 77
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->namedActions:Ljava/util/Map;

    .line 83
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->exceptions:Ljava/util/List;

    .line 92
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->actions:Ljava/util/List;

    const/4 v0, 0x1

    .line 98
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->isStartRule:Z

    const/4 v1, -0x1

    .line 106
    iput v1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->actionIndex:I

    .line 109
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 110
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    .line 111
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 112
    iput p4, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    add-int/lit8 p1, p4, 0x1

    .line 113
    new-array p1, p1, [Lgroovyjarjarantlr4/v4/tool/Alternative;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    :goto_0
    if-gt v0, p4, :cond_0

    .line 114
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    new-instance p2, Lgroovyjarjarantlr4/v4/tool/Alternative;

    invoke-direct {p2, p0, v0}, Lgroovyjarjarantlr4/v4/tool/Alternative;-><init>(Lgroovyjarjarantlr4/v4/tool/Rule;I)V

    aput-object p2, p1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public defineActionInAlt(ILgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 1

    .line 140
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->actions:Ljava/util/List;

    invoke-interface {v0, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 141
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object p1, v0, p1

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Alternative;->actions:Ljava/util/List;

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 142
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result p1

    if-eqz p1, :cond_0

    .line 143
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->defineLexerAction(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    :cond_0
    return-void
.end method

.method public defineLexerAction(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 2

    .line 149
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->lexerActions:Ljava/util/LinkedHashMap;

    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->size()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->actionIndex:I

    .line 150
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->lexerActions:Ljava/util/LinkedHashMap;

    invoke-virtual {v0, p1}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    .line 151
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->lexerActions:Ljava/util/LinkedHashMap;

    iget v1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->actionIndex:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {v0, p1, v1}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-void
.end method

.method public definePredicateInAlt(ILgroovyjarjarantlr4/v4/tool/ast/PredAST;)V
    .locals 1

    .line 156
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->actions:Ljava/util/List;

    invoke-interface {v0, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 157
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object p1, v0, p1

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Alternative;->actions:Ljava/util/List;

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 158
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->sempreds:Ljava/util/LinkedHashMap;

    invoke-virtual {p1, p2}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-nez p1, :cond_0

    .line 159
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->sempreds:Ljava/util/LinkedHashMap;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->sempreds:Ljava/util/LinkedHashMap;

    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->size()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {p1, p2, v0}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-void
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 1

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    return p1

    .line 349
    :cond_0
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;

    if-nez v0, :cond_1

    const/4 p1, 0x0

    return p1

    .line 353
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public getAltLabels()Ljava/util/Map;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;>;>;"
        }
    .end annotation

    .line 218
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    const/4 v1, 0x1

    .line 219
    :goto_0
    iget v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    if-gt v1, v2, :cond_2

    .line 220
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v2, v2, v1

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v2, :cond_1

    .line 222
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    if-nez v3, :cond_0

    .line 224
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 225
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v0, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 228
    :cond_0
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v4, v4, v1

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-static {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v2

    invoke-interface {v3, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 231
    :cond_2
    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_3

    const/4 v0, 0x0

    :cond_3
    return-object v0
.end method

.method public getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;
    .locals 1

    .line 322
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Rule;->getElementLabelDefs()Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    if-eqz p1, :cond_0

    const/4 v0, 0x0

    .line 323
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public getBaseContext()Ljava/lang/String;
    .locals 3

    .line 118
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->baseContext:Ljava/lang/String;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    .line 119
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->baseContext:Ljava/lang/String;

    return-object v0

    .line 122
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const-string v1, "baseContext"

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 123
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_1

    return-object v0

    .line 127
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    const/16 v1, 0x24

    invoke-virtual {v0, v1}, Ljava/lang/String;->indexOf(I)I

    move-result v0

    if-ltz v0, :cond_2

    .line 129
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    const/4 v2, 0x0

    invoke-virtual {v1, v2, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 132
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    return-object v0
.end method

.method public getElementLabelDefs()Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/LabelElementPair;",
            ">;"
        }
    .end annotation

    .line 190
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;-><init>()V

    const/4 v1, 0x1

    .line 192
    :goto_0
    iget v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    if-gt v1, v2, :cond_2

    .line 193
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v2, v2, v1

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Alternative;->labelDefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->values()Ljava/util/Collection;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    .line 194
    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_0

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    .line 195
    iget-object v5, v4, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v0, v5, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_1

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    return-object v0
.end method

.method public getElementLabelNames()Ljava/util/Set;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 181
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    const/4 v1, 0x1

    .line 182
    :goto_0
    iget v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    if-gt v1, v2, :cond_0

    .line 183
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v2, v2, v1

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Alternative;->labelDefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 185
    :cond_0
    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_1

    const/4 v0, 0x0

    :cond_1
    return-object v0
.end method

.method public getOriginalNumberOfAlts()I
    .locals 1

    .line 208
    iget v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    return v0
.end method

.method public getPredefinedScope(Lgroovyjarjarantlr4/v4/tool/LabelType;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;
    .locals 2

    .line 328
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTypeString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 329
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->grammarAndLabelRefTypeToScope:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    return-object p1
.end method

.method public getTokenRefs()Ljava/util/Set;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 173
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    const/4 v1, 0x1

    .line 174
    :goto_0
    iget v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    if-gt v1, v2, :cond_0

    .line 175
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v2, v2, v1

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Alternative;->tokenRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getUnlabeledAltASTs()Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;"
        }
    .end annotation

    .line 236
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x1

    .line 237
    :goto_0
    iget v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    if-gt v1, v2, :cond_1

    .line 238
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v2, v2, v1

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-nez v2, :cond_0

    .line 239
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v2, v2, v1

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 241
    :cond_1
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_2

    const/4 v0, 0x0

    :cond_2
    return-object v0
.end method

.method public hasAltSpecificContexts()Z
    .locals 1

    .line 203
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Rule;->getAltLabels()Ljava/util/Map;

    move-result-object v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public hashCode()I
    .locals 1

    .line 341
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    return v0
.end method

.method public isFragment()Z
    .locals 4

    .line 333
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->modifiers:Ljava/util/List;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 334
    :cond_0
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 335
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    const-string v3, "fragment"

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    const/4 v0, 0x1

    return v0

    :cond_2
    return v1
.end method

.method public resolveRetvalOrProperty(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 1

    .line 164
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v0, :cond_0

    .line 165
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    .line 168
    :cond_0
    sget-object v0, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/Rule;->getPredefinedScope(Lgroovyjarjarantlr4/v4/tool/LabelType;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    move-result-object v0

    .line 169
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    return-object p1
.end method

.method public resolveToAttribute(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 0

    .line 249
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz p2, :cond_0

    .line 250
    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p2

    if-eqz p2, :cond_0

    return-object p2

    .line 252
    :cond_0
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz p2, :cond_1

    .line 253
    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p2

    if-eqz p2, :cond_1

    return-object p2

    .line 255
    :cond_1
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz p2, :cond_2

    .line 256
    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p2

    if-eqz p2, :cond_2

    return-object p2

    .line 258
    :cond_2
    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->getPredefinedScope(Lgroovyjarjarantlr4/v4/tool/LabelType;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    move-result-object p2

    .line 259
    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    return-object p1
.end method

.method public resolveToAttribute(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 2

    .line 265
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    const/4 p3, 0x0

    if-eqz p1, :cond_2

    .line 267
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne v0, v1, :cond_0

    .line 268
    iget-object p3, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->element:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->resolveRetvalOrProperty(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    return-object p1

    .line 271
    :cond_0
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getPredefinedScope(Lgroovyjarjarantlr4/v4/tool/LabelType;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    move-result-object p1

    if-nez p1, :cond_1

    return-object p3

    .line 276
    :cond_1
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    return-object p1

    :cond_2
    return-object p3
.end method

.method public resolveToRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;
    .locals 3

    .line 313
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p0

    .line 314
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 315
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne v1, v2, :cond_1

    .line 316
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->element:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    return-object p1

    .line 318
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    return-object p1
.end method

.method public resolvesToAttributeDict(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 0

    .line 308
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->resolvesToToken(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public resolvesToLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 1

    .line 285
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 286
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v0, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-eq p2, v0, :cond_0

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne p1, p2, :cond_1

    :cond_0
    const/4 p1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public resolvesToListLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 1

    .line 293
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 294
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v0, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-eq p2, v0, :cond_0

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne p1, p2, :cond_1

    :cond_0
    const/4 p1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public resolvesToToken(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 0

    .line 301
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getAnyLabelDef(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 302
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object p2, Lgroovyjarjarantlr4/v4/tool/LabelType;->TOKEN_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    if-ne p1, p2, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public setBaseContext(Ljava/lang/String;)V
    .locals 0

    .line 136
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->baseContext:Ljava/lang/String;

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 358
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Rule{name="

    .line 359
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 360
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v1, :cond_0

    const-string v1, ", args="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 361
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v1, :cond_1

    const-string v1, ", retvals="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    :cond_1
    const-string v1, "}"

    .line 362
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 363
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
