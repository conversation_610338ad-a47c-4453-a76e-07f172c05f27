.class public Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;
.super Ljava/lang/Object;
.source "DefaultToolListener.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;


# instance fields
.field public tool:Lgroovyjarjarantlr4/v4/Tool;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/Tool;)V
    .locals 0

    .line 16
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;->tool:Lgroovyjarjarantlr4/v4/Tool;

    return-void
.end method


# virtual methods
.method public error(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V
    .locals 2

    .line 28
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getMessageTemplate(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    .line 29
    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    .line 30
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->formatWantsSingleLineMessage()Z

    move-result v0

    if-eqz v0, :cond_0

    const/16 v0, 0xa

    const/16 v1, 0x20

    .line 31
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p1

    .line 33
    :cond_0
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method

.method public info(Ljava/lang/String;)V
    .locals 2

    .line 20
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->formatWantsSingleLineMessage()Z

    move-result v0

    if-eqz v0, :cond_0

    const/16 v0, 0xa

    const/16 v1, 0x20

    .line 21
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p1

    .line 23
    :cond_0
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method

.method public warning(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V
    .locals 2

    .line 38
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getMessageTemplate(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    .line 39
    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    .line 40
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->formatWantsSingleLineMessage()Z

    move-result v0

    if-eqz v0, :cond_0

    const/16 v0, 0xa

    const/16 v1, 0x20

    .line 41
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p1

    .line 43
    :cond_0
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method
