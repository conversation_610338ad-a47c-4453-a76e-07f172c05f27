.class public Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;
.super Ljava/lang/Object;
.source "BuildDependencyGenerator.java"


# instance fields
.field protected g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field protected generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

.field protected templates:Lorg/stringtemplate/v4/STGroup;

.field protected tool:Lgroovyjarjarantlr4/v4/Tool;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 2

    .line 69
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 70
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    .line 71
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    const-string v0, "language"

    .line 72
    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 73
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-direct {v1, p1, p2, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    return-void
.end method


# virtual methods
.method public getDependencies()Lorg/stringtemplate/v4/ST;
    .locals 3

    .line 244
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->loadDependencyTemplates()V

    .line 245
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->templates:Lorg/stringtemplate/v4/STGroup;

    const-string v1, "dependencies"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    .line 246
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getDependenciesFileList()Ljava/util/List;

    move-result-object v1

    const-string v2, "in"

    invoke-virtual {v0, v2, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 247
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getGeneratedFileList()Ljava/util/List;

    move-result-object v1

    const-string v2, "out"

    invoke-virtual {v0, v2, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 248
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    const-string v2, "grammarFileName"

    invoke-virtual {v0, v2, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    return-object v0
.end method

.method public getDependenciesFileList()Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/io/File;",
            ">;"
        }
    .end annotation

    .line 197
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getNonImportDependenciesFileList()Ljava/util/List;

    move-result-object v0

    .line 200
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getAllImportedGrammars()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 202
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 203
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    .line 204
    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p0, v3, v2}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->groomQualifiedFileName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 205
    new-instance v3, Ljava/io/File;

    invoke-direct {v3, v2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 209
    :cond_0
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_1

    const/4 v0, 0x0

    :cond_1
    return-object v0
.end method

.method public getGeneratedFileList()Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/io/File;",
            ">;"
        }
    .end annotation

    .line 80
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-nez v0, :cond_0

    .line 83
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    return-object v0

    .line 86
    :cond_0
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 89
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v2

    const/4 v3, 0x1

    if-eqz v2, :cond_1

    .line 90
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getRecognizerFileName(Z)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 92
    :cond_1
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    const/4 v4, 0x0

    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getRecognizerFileName(Z)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 96
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getVocabFileName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 99
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v2

    const-string v5, "codeFileExtension"

    invoke-virtual {v2, v5}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v2

    .line 100
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v5

    const-string v6, "headerFile"

    invoke-virtual {v5, v6}, Lorg/stringtemplate/v4/STGroup;->isDefined(Ljava/lang/String;)Z

    move-result v5

    const/4 v6, 0x0

    if-eqz v5, :cond_2

    .line 101
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v5, "headerFileExtension"

    invoke-virtual {v0, v5}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    .line 102
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getType()I

    move-result v5

    invoke-static {v5}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getGrammarTypeToFileNameSuffix(I)Ljava/lang/String;

    move-result-object v5

    .line 103
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v8, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v8, v8, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 104
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v5

    invoke-interface {v1, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_2
    move-object v0, v6

    .line 106
    :goto_0
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isCombined()Z

    move-result v5

    if-eqz v5, :cond_3

    const/16 v5, 0x1f

    .line 109
    invoke-static {v5}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getGrammarTypeToFileNameSuffix(I)Ljava/lang/String;

    move-result-object v5

    .line 110
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v8, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v8, v8, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v2}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v7, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 111
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 112
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v7, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v7, v7, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v7, ".tokens"

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 113
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    if-eqz v0, :cond_3

    .line 117
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v7, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v7, v7, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 118
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 122
    :cond_3
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-boolean v0, v0, Lgroovyjarjarantlr4/v4/Tool;->gen_listener:Z

    if-eqz v0, :cond_6

    .line 124
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v0

    if-eqz v0, :cond_4

    .line 125
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getListenerFileName(Z)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 127
    :cond_4
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getListenerFileName(Z)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 130
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 131
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseListenerFileName(Z)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 133
    :cond_5
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseListenerFileName(Z)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 136
    :cond_6
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-boolean v0, v0, Lgroovyjarjarantlr4/v4/Tool;->gen_visitor:Z

    if-eqz v0, :cond_9

    .line 138
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v0

    if-eqz v0, :cond_7

    .line 139
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getVisitorFileName(Z)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 141
    :cond_7
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getVisitorFileName(Z)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 144
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v0

    if-eqz v0, :cond_8

    .line 145
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseVisitorFileName(Z)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 147
    :cond_8
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseVisitorFileName(Z)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 152
    :cond_9
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getAllImportedGrammars()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_a

    .line 154
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_a

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 158
    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getOutputFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 162
    :cond_a
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_b

    return-object v6

    :cond_b
    return-object v1
.end method

.method public getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;
    .locals 1

    .line 259
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->generator:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    return-object v0
.end method

.method public getNonImportDependenciesFileList()Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/io/File;",
            ">;"
        }
    .end annotation

    .line 223
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 226
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    const-string v2, "tokenVocab"

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_1

    .line 228
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ".tokens"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 231
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    const-string v3, "."

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 232
    new-instance v2, Ljava/io/File;

    invoke-direct {v2, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    goto :goto_0

    .line 235
    :cond_0
    new-instance v2, Ljava/io/File;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    invoke-direct {v2, v3, v1}, Ljava/io/File;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 237
    :goto_0
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    return-object v0
.end method

.method public getOutputFile(Ljava/lang/String;)Ljava/io/File;
    .locals 4

    .line 169
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/Tool;->getOutputDirectory(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    .line 170
    invoke-virtual {v0}, Ljava/io/File;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "."

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 172
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/Tool;->getOutputDirectory(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    .line 174
    :cond_0
    invoke-virtual {v0}, Ljava/io/File;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 175
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    return-object v0

    .line 177
    :cond_1
    invoke-virtual {v0}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    .line 178
    invoke-virtual {v0}, Ljava/io/File;->toString()Ljava/lang/String;

    move-result-object v1

    const/16 v2, 0x2e

    .line 179
    invoke-virtual {v1, v2}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v1

    .line 180
    new-instance v2, Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->toString()Ljava/lang/String;

    move-result-object v0

    const/4 v3, 0x0

    invoke-virtual {v0, v3, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    invoke-direct {v2, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v0, v2

    .line 183
    :cond_2
    invoke-virtual {v0}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v1

    const/16 v2, 0x20

    invoke-virtual {v1, v2}, Ljava/lang/String;->indexOf(I)I

    move-result v1

    if-ltz v1, :cond_3

    .line 184
    invoke-virtual {v0}, Ljava/io/File;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, " "

    const-string v2, "\\ "

    invoke-virtual {v0, v1, v2}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v0

    .line 185
    new-instance v1, Ljava/io/File;

    invoke-direct {v1, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v0, v1

    .line 187
    :cond_3
    new-instance v1, Ljava/io/File;

    invoke-direct {v1, v0, p1}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    return-object v1
.end method

.method public groomQualifiedFileName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    const-string v0, "."

    .line 263
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p2

    :cond_0
    const/16 v0, 0x20

    .line 266
    invoke-virtual {p1, v0}, Ljava/lang/String;->indexOf(I)I

    move-result v0

    if-ltz v0, :cond_1

    const-string v0, " "

    const-string v1, "\\ "

    .line 267
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    .line 268
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    sget-object v0, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 271
    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    sget-object v0, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public loadDependencyTemplates()V
    .locals 3

    .line 253
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->templates:Lorg/stringtemplate/v4/STGroup;

    if-eqz v0, :cond_0

    return-void

    .line 255
    :cond_0
    new-instance v0, Lorg/stringtemplate/v4/STGroupFile;

    const-string v1, "groovyjarjarantlr4/v4/tool/templates/depend.stg"

    const-string v2, "UTF-8"

    invoke-direct {v0, v1, v2}, Lorg/stringtemplate/v4/STGroupFile;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->templates:Lorg/stringtemplate/v4/STGroup;

    return-void
.end method
