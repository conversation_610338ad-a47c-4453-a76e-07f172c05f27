.class public Lgroovyjarjarantlr4/v4/tool/LexerGrammar;
.super Lgroovyjarjarantlr4/v4/tool/Grammar;
.source "LexerGrammar.java"


# static fields
.field static final synthetic $assertionsDisabled:Z = false

.field public static final DEFAULT_MODE_NAME:Ljava/lang/String; = "DEFAULT_MODE"


# instance fields
.field public implicitLexerOwner:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)V
    .locals 0

    .line 25
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 29
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 33
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 37
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V

    return-void
.end method


# virtual methods
.method public defineRule(Lgroovyjarjarantlr4/v4/tool/Rule;)Z
    .locals 2

    .line 42
    invoke-super {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineRule(Lgroovyjarjarantlr4/v4/tool/Rule;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return p1

    .line 46
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    if-nez v0, :cond_1

    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    .line 47
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->mode:Ljava/lang/String;

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    const/4 p1, 0x1

    return p1
.end method

.method public undefineRule(Lgroovyjarjarantlr4/v4/tool/Rule;)Z
    .locals 2

    .line 53
    invoke-super {p0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->undefineRule(Lgroovyjarjarantlr4/v4/tool/Rule;)Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return p1

    .line 57
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->mode:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    const/4 p1, 0x1

    return p1
.end method
