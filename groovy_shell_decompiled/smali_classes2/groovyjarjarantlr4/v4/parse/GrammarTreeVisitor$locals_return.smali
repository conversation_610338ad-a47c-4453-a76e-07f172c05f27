.class public Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$locals_return;
.super Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;
.source "GrammarTreeVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "locals_return"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1924
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;-><init>()V

    return-void
.end method
