.class public Lgroovyjarjarantlr4/v4/parse/GrammarToken;
.super Lgroovyjarjarantlr4/runtime/CommonToken;
.source "GrammarToken.java"


# instance fields
.field public g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public originalTokenIndex:I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 21
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    const/4 p2, -0x1

    .line 18
    iput p2, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->originalTokenIndex:I

    .line 22
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    return-void
.end method


# virtual methods
.method public getCharPositionInLine()I
    .locals 2

    .line 27
    iget v0, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->originalTokenIndex:I

    if-ltz v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    iget v1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->originalTokenIndex:I

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result v0

    return v0

    .line 28
    :cond_0
    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/CommonToken;->getCharPositionInLine()I

    move-result v0

    return v0
.end method

.method public getLine()I
    .locals 2

    .line 33
    iget v0, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->originalTokenIndex:I

    if-ltz v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    iget v1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->originalTokenIndex:I

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result v0

    return v0

    .line 34
    :cond_0
    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/CommonToken;->getLine()I

    move-result v0

    return v0
.end method

.method public getStartIndex()I
    .locals 2

    .line 44
    iget v0, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->originalTokenIndex:I

    if-ltz v0, :cond_0

    .line 45
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    iget v1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->originalTokenIndex:I

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/CommonToken;->getStartIndex()I

    move-result v0

    return v0

    .line 47
    :cond_0
    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/CommonToken;->getStartIndex()I

    move-result v0

    return v0
.end method

.method public getStopIndex()I
    .locals 2

    .line 52
    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/CommonToken;->getStopIndex()I

    move-result v0

    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/CommonToken;->getStartIndex()I

    move-result v1

    sub-int/2addr v0, v1

    add-int/lit8 v0, v0, 0x1

    .line 53
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getStartIndex()I

    move-result v1

    add-int/2addr v1, v0

    add-int/lit8 v1, v1, -0x1

    return v1
.end method

.method public getTokenIndex()I
    .locals 1

    .line 39
    iget v0, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->originalTokenIndex:I

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 6

    .line 59
    iget v0, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->channel:I

    if-lez v0, :cond_0

    .line 60
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, ",channel="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->channel:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const-string v0, ""

    .line 62
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getText()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_1

    const-string v2, "\n"

    const-string v3, "\\\\n"

    .line 64
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "\r"

    const-string v3, "\\\\r"

    .line 65
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "\t"

    const-string v3, "\\\\t"

    .line 66
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    :cond_1
    const-string v1, "<no text>"

    .line 71
    :goto_1
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "[@"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getTokenIndex()I

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, ","

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getStartIndex()I

    move-result v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v4, ":"

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getStopIndex()I

    move-result v5

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v5, "=\'"

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "\',<"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getType()I

    move-result v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ">"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getLine()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarToken;->getCharPositionInLine()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
