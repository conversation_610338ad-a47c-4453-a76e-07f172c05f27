.class public Lgroovyjarjarantlr4/v4/parse/ScopeParser;
.super Ljava/lang/Object;
.source "ScopeParser.java"


# static fields
.field static final synthetic $assertionsDisabled:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 33
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static _parsePostfixDecl(Lgroovyjarjarantlr4/v4/tool/Attribute;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Attribute;",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ")",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    const/16 v0, 0x3a

    .line 202
    invoke-virtual {p1, v0}, Ljava/lang/String;->indexOf(I)I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    .line 203
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v2

    goto :goto_0

    :cond_0
    move v2, v0

    :goto_0
    const/4 v3, 0x0

    move v4, v3

    :goto_1
    const/16 v5, 0x5f

    if-ge v4, v2, :cond_2

    .line 207
    invoke-virtual {p1, v4}, Ljava/lang/String;->charAt(I)C

    move-result v6

    .line 208
    invoke-static {v6}, Ljava/lang/Character;->isLetterOrDigit(C)Z

    move-result v7

    if-nez v7, :cond_3

    if-ne v6, v5, :cond_1

    goto :goto_2

    :cond_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_2
    move v4, v1

    :cond_3
    :goto_2
    const/4 v6, 0x1

    if-ne v4, v1, :cond_4

    .line 216
    iget-object v4, p3, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v7, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_FIND_ATTRIBUTE_NAME_IN_DECL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object p3, p3, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v8, v6, [Ljava/lang/Object;

    aput-object p1, v8, v3

    invoke-virtual {v4, v7, p3, p2, v8}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_3

    :cond_4
    move v3, v4

    :goto_3
    move p3, v1

    move p2, v3

    :goto_4
    if-ge p2, v2, :cond_7

    .line 221
    invoke-virtual {p1, p2}, Ljava/lang/String;->charAt(I)C

    move-result v4

    .line 222
    invoke-static {v4}, Ljava/lang/Character;->isLetterOrDigit(C)Z

    move-result v7

    if-nez v7, :cond_5

    if-eq v4, v5, :cond_5

    goto :goto_5

    :cond_5
    add-int/lit8 v4, v2, -0x1

    if-ne p2, v4, :cond_6

    move p3, v2

    :cond_6
    add-int/lit8 p2, p2, 0x1

    goto :goto_4

    :cond_7
    move p2, p3

    :goto_5
    if-ne p2, v1, :cond_8

    move p2, v3

    .line 236
    :cond_8
    invoke-virtual {p1, v3, p2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p3

    iput-object p3, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->name:Ljava/lang/String;

    if-ne v0, v1, :cond_9

    const-string p1, ""

    .line 240
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    goto :goto_6

    :cond_9
    add-int/2addr v0, v6

    .line 243
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p3

    invoke-virtual {p1, v0, p3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    .line 245
    :goto_6
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    invoke-virtual {p1}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    .line 247
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p1

    if-nez p1, :cond_a

    const/4 p1, 0x0

    .line 248
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    .line 250
    :cond_a
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p0

    return-object p0
.end method

.method public static _parsePrefixDecl(Lgroovyjarjarantlr4/v4/tool/Attribute;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Attribute;",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ")",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .line 151
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    const/16 v4, 0x5f

    const/4 v5, -0x1

    if-ltz v0, :cond_2

    .line 152
    invoke-virtual {p1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v6

    if-nez v3, :cond_0

    .line 154
    invoke-static {v6}, Ljava/lang/Character;->isLetterOrDigit(C)Z

    move-result v7

    if-eqz v7, :cond_0

    move v3, v1

    goto :goto_1

    :cond_0
    if-eqz v3, :cond_1

    .line 157
    invoke-static {v6}, Ljava/lang/Character;->isLetterOrDigit(C)Z

    move-result v7

    if-nez v7, :cond_1

    if-eq v6, v4, :cond_1

    add-int/2addr v0, v1

    goto :goto_2

    :cond_1
    :goto_1
    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_2
    move v0, v5

    :goto_2
    if-gez v0, :cond_3

    if-eqz v3, :cond_3

    move v0, v2

    :cond_3
    if-gez v0, :cond_4

    .line 166
    iget-object v3, p3, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_FIND_ATTRIBUTE_NAME_IN_DECL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object p3, p3, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v7, v1, [Ljava/lang/Object;

    aput-object p1, v7, v2

    invoke-virtual {v3, v6, p3, p2, v7}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    :cond_4
    move p2, v0

    .line 171
    :goto_3
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p3

    if-ge p2, p3, :cond_7

    .line 172
    invoke-virtual {p1, p2}, Ljava/lang/String;->charAt(I)C

    move-result p3

    .line 174
    invoke-static {p3}, Ljava/lang/Character;->isLetterOrDigit(C)Z

    move-result v3

    if-nez v3, :cond_5

    if-eq p3, v4, :cond_5

    move v5, p2

    goto :goto_4

    .line 178
    :cond_5
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p3

    sub-int/2addr p3, v1

    if-ne p2, p3, :cond_6

    add-int/lit8 p3, p2, 0x1

    move v5, p3

    :cond_6
    add-int/lit8 p2, p2, 0x1

    goto :goto_3

    .line 184
    :cond_7
    :goto_4
    invoke-virtual {p1, v0, v5}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->name:Ljava/lang/String;

    .line 187
    invoke-virtual {p1, v2, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    .line 188
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p2

    sub-int/2addr p2, v1

    if-gt v5, p2, :cond_8

    .line 189
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    iget-object p3, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p3

    invoke-virtual {p1, v5, p3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    .line 192
    :cond_8
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    invoke-virtual {p1}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    .line 193
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p1

    if-nez p1, :cond_9

    const/4 p1, 0x0

    .line 194
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/tool/Attribute;->type:Ljava/lang/String;

    .line 196
    :cond_9
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p0

    return-object p0
.end method

.method public static _splitArgumentList(Ljava/lang/String;IIILjava/util/List;)I
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "III",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;>;)I"
        }
    .end annotation

    const/4 v0, -0x1

    if-nez p0, :cond_0

    return v0

    :cond_0
    const-string v1, "//[^\\n]*"

    const-string v2, ""

    .line 277
    invoke-virtual {p0, v1, v2}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    .line 278
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v1

    move v2, p1

    :goto_0
    if-ge p1, v1, :cond_a

    .line 282
    invoke-virtual {p0, p1}, Ljava/lang/String;->charAt(I)C

    move-result v3

    if-eq v3, p2, :cond_a

    .line 283
    invoke-virtual {p0, p1}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v4, 0x5c

    const/16 v5, 0x22

    if-eq v3, v5, :cond_9

    const/16 v5, 0x3c

    if-eq v3, v5, :cond_7

    const/16 v5, 0x5b

    if-eq v3, v5, :cond_6

    const/16 v5, 0x7b

    if-eq v3, v5, :cond_5

    const/16 v5, 0x27

    if-eq v3, v5, :cond_4

    const/16 v4, 0x28

    if-eq v3, v4, :cond_3

    if-ne v3, p3, :cond_2

    if-ne p2, v0, :cond_2

    .line 328
    invoke-virtual {p0, v2, p1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v3

    :goto_1
    if-ge v2, p1, :cond_1

    .line 330
    invoke-virtual {p0, v2}, Ljava/lang/String;->charAt(I)C

    move-result v4

    invoke-static {v4}, Ljava/lang/Character;->isWhitespace(C)Z

    move-result v4

    if-eqz v4, :cond_1

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 334
    :cond_1
    invoke-virtual {v3}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v3, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v2

    invoke-interface {p4, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, p1, 0x1

    :cond_2
    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_3
    add-int/lit8 p1, p1, 0x1

    const/16 v3, 0x29

    .line 308
    invoke-static {p0, p1, v3, p3, p4}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->_splitArgumentList(Ljava/lang/String;IIILjava/util/List;)I

    move-result p1

    goto :goto_0

    :cond_4
    :goto_2
    add-int/lit8 p1, p1, 0x1

    if-ge p1, v1, :cond_2

    .line 287
    invoke-virtual {p0, p1}, Ljava/lang/String;->charAt(I)C

    move-result v3

    if-eq v3, v5, :cond_2

    .line 288
    invoke-virtual {p0, p1}, Ljava/lang/String;->charAt(I)C

    move-result v3

    if-ne v3, v4, :cond_4

    add-int/lit8 v3, p1, 0x1

    if-ge v3, v1, :cond_4

    invoke-virtual {p0, v3}, Ljava/lang/String;->charAt(I)C

    move-result v6

    if-ne v6, v5, :cond_4

    move p1, v3

    goto :goto_2

    :cond_5
    add-int/lit8 p1, p1, 0x1

    const/16 v3, 0x7d

    .line 311
    invoke-static {p0, p1, v3, p3, p4}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->_splitArgumentList(Ljava/lang/String;IIILjava/util/List;)I

    move-result p1

    goto :goto_0

    :cond_6
    add-int/lit8 p1, p1, 0x1

    const/16 v3, 0x5d

    .line 324
    invoke-static {p0, p1, v3, p3, p4}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->_splitArgumentList(Ljava/lang/String;IIILjava/util/List;)I

    move-result p1

    goto/16 :goto_0

    :cond_7
    add-int/lit8 v3, p1, 0x1

    const/16 v4, 0x3e

    .line 314
    invoke-virtual {p0, v4, v3}, Ljava/lang/String;->indexOf(II)I

    move-result v5

    if-lt v5, p1, :cond_8

    .line 317
    invoke-static {p0, v3, v4, p3, p4}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->_splitArgumentList(Ljava/lang/String;IIILjava/util/List;)I

    move-result p1

    goto/16 :goto_0

    :cond_8
    move p1, v3

    goto/16 :goto_0

    :cond_9
    :goto_3
    add-int/lit8 p1, p1, 0x1

    if-ge p1, v1, :cond_2

    .line 298
    invoke-virtual {p0, p1}, Ljava/lang/String;->charAt(I)C

    move-result v3

    if-eq v3, v5, :cond_2

    .line 299
    invoke-virtual {p0, p1}, Ljava/lang/String;->charAt(I)C

    move-result v3

    if-ne v3, v4, :cond_9

    add-int/lit8 v3, p1, 0x1

    if-ge v3, v1, :cond_9

    invoke-virtual {p0, v3}, Ljava/lang/String;->charAt(I)C

    move-result v6

    if-ne v6, v5, :cond_9

    move p1, v3

    goto :goto_3

    :cond_a
    if-ne p2, v0, :cond_c

    if-gt p1, v1, :cond_c

    .line 342
    invoke-virtual {p0, v2, p1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object p2

    :goto_4
    if-ge v2, p1, :cond_b

    .line 344
    invoke-virtual {p0, v2}, Ljava/lang/String;->charAt(I)C

    move-result p3

    invoke-static {p3}, Ljava/lang/Character;->isWhitespace(C)Z

    move-result p3

    if-eqz p3, :cond_b

    add-int/lit8 v2, v2, 0x1

    goto :goto_4

    .line 348
    :cond_b
    invoke-virtual {p2}, Ljava/lang/String;->length()I

    move-result p0

    if-lez p0, :cond_c

    .line 349
    invoke-virtual {p2}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object p0

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    invoke-static {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p0

    invoke-interface {p4, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_c
    add-int/lit8 p1, p1, 0x1

    return p1
.end method

.method public static parse(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Ljava/lang/String;CLgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;
    .locals 2

    .line 54
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;-><init>()V

    .line 55
    invoke-static {p1, p2}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->splitDecls(Ljava/lang/String;I)Ljava/util/List;

    move-result-object p1

    .line 56
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    .line 57
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    if-lez v1, :cond_0

    .line 58
    invoke-static {p0, p2, p3}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->parseAttributeDef(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p2

    .line 59
    invoke-virtual {v0, p2}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->add(Lgroovyjarjarantlr4/v4/tool/Attribute;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public static parseAttributeDef(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/tool/Attribute;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ")",
            "Lgroovyjarjarantlr4/v4/tool/Attribute;"
        }
    .end annotation

    .line 72
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 p0, 0x0

    return-object p0

    .line 74
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/Attribute;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/tool/Attribute;-><init>()V

    .line 75
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    .line 76
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    const/16 v3, 0x3d

    invoke-virtual {v2, v3}, Ljava/lang/String;->indexOf(I)I

    move-result v2

    if-lez v2, :cond_1

    .line 79
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    add-int/lit8 v3, v2, 0x1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-virtual {v4}, Ljava/lang/String;->length()I

    move-result v4

    invoke-virtual {v1, v3, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v1

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Attribute;->initValue:Ljava/lang/String;

    add-int/lit8 v1, v2, -0x1

    .line 83
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    add-int/lit8 v1, v1, 0x1

    const/4 v3, 0x0

    invoke-virtual {v2, v3, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    .line 85
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    const-string v4, "::"

    const-string v5, ""

    .line 86
    invoke-virtual {v2, v4, v5}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    const-string v4, ":"

    .line 87
    invoke-virtual {v2, v4}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 89
    invoke-static {v0, v1, p0, p2}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->_parsePostfixDecl(Lgroovyjarjarantlr4/v4/tool/Attribute;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p2

    goto :goto_0

    .line 93
    :cond_2
    invoke-static {v0, v1, p0, p2}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->_parsePrefixDecl(Lgroovyjarjarantlr4/v4/tool/Attribute;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p2

    .line 95
    :goto_0
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Integer;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    .line 96
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem2()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p2

    .line 98
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    iput-object v2, v0, Lgroovyjarjarantlr4/v4/tool/Attribute;->decl:Ljava/lang/String;

    if-eqz p0, :cond_8

    .line 101
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getText()Ljava/lang/String;

    move-result-object v2

    .line 102
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v4

    new-array v4, v4, [I

    .line 103
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v5

    new-array v5, v5, [I

    move v6, v3

    move v7, v6

    move v8, v7

    .line 104
    :goto_1
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v9

    const/16 v10, 0xa

    if-ge v6, v9, :cond_4

    .line 105
    aput v7, v4, v6

    .line 106
    aput v8, v5, v6

    .line 107
    invoke-virtual {v2, v6}, Ljava/lang/String;->charAt(I)C

    move-result v9

    if-ne v9, v10, :cond_3

    add-int/lit8 v7, v7, 0x1

    const/4 v8, -0x1

    :cond_3
    add-int/lit8 v6, v6, 0x1

    add-int/lit8 v8, v8, 0x1

    goto :goto_1

    .line 113
    :cond_4
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v6

    new-array v6, v6, [I

    move v7, v3

    .line 114
    :goto_2
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v8

    if-ge v3, v8, :cond_6

    .line 115
    aput v3, v6, v7

    .line 117
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v8

    add-int/lit8 v8, v8, -0x1

    if-ge v3, v8, :cond_5

    invoke-virtual {v2, v3}, Ljava/lang/String;->charAt(I)C

    move-result v8

    const/16 v9, 0x2f

    if-ne v8, v9, :cond_5

    add-int/lit8 v8, v3, 0x1

    invoke-virtual {v2, v8}, Ljava/lang/String;->charAt(I)C

    move-result v8

    if-ne v8, v9, :cond_5

    .line 118
    :goto_3
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v8

    if-ge v3, v8, :cond_5

    invoke-virtual {v2, v3}, Ljava/lang/String;->charAt(I)C

    move-result v8

    if-eq v8, v10, :cond_5

    add-int/lit8 v3, v3, 0x1

    goto :goto_3

    :cond_5
    add-int/lit8 v3, v3, 0x1

    add-int/lit8 v7, v7, 0x1

    goto :goto_2

    .line 124
    :cond_6
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem2()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    aget p1, v6, p1

    add-int v2, p1, v1

    .line 125
    aget v3, v4, v2

    .line 127
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v4

    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result v4

    add-int/2addr v4, v3

    .line 128
    aget v2, v5, v2

    if-nez v3, :cond_7

    .line 134
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v3

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result v3

    add-int/lit8 v3, v3, 0x1

    add-int/2addr v2, v3

    .line 137
    :cond_7
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/runtime/CommonToken;->getStartIndex()I

    move-result v3

    .line 138
    new-instance v11, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p0

    invoke-interface {p0}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v6

    const/16 v7, 0x1c

    const/4 v8, 0x0

    add-int/2addr v3, p1

    add-int/2addr v1, v3

    add-int/lit8 v9, v1, 0x1

    add-int v10, v3, p2

    move-object v5, v11

    invoke-direct/range {v5 .. v10}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;IIII)V

    iput-object v11, v0, Lgroovyjarjarantlr4/v4/tool/Attribute;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 139
    iget-object p0, v0, Lgroovyjarjarantlr4/v4/tool/Attribute;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {p0, v4}, Lgroovyjarjarantlr4/runtime/Token;->setLine(I)V

    .line 140
    iget-object p0, v0, Lgroovyjarjarantlr4/v4/tool/Attribute;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {p0, v2}, Lgroovyjarjarantlr4/runtime/Token;->setCharPositionInLine(I)V

    :cond_8
    return-object v0
.end method

.method public static parseTypedArgList(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;
    .locals 1

    const/16 v0, 0x2c

    .line 50
    invoke-static {p0, p1, v0, p2}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->parse(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Ljava/lang/String;CLgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    move-result-object p0

    return-object p0
.end method

.method public static splitDecls(Ljava/lang/String;I)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;>;"
        }
    .end annotation

    .line 263
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    const/4 v2, -0x1

    .line 264
    invoke-static {p0, v1, v2, p1, v0}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->_splitArgumentList(Ljava/lang/String;IIILjava/util/List;)I

    return-object v0
.end method
