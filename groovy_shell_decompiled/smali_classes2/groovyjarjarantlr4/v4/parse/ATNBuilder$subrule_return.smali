.class public Lgroovyjarjarantlr4/v4/parse/ATNBuilder$subrule_return;
.super Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;
.source "ATNBuilder.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/ATNBuilder;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "subrule_return"
.end annotation


# instance fields
.field public p:Lgroovyjarjarantlr4/v4/automata/ATNFactory$Handle;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1077
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;-><init>()V

    return-void
.end method
