.class public Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$outerAlternative_return;
.super Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;
.source "LeftRecursiveRuleWalker.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "outerAlternative_return"
.end annotation


# instance fields
.field public isLeftRec:Z


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 548
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;-><init>()V

    return-void
.end method
