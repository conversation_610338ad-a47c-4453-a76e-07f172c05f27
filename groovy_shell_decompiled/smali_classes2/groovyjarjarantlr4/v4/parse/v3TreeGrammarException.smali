.class public Lgroovyjarjarantlr4/v4/parse/v3TreeGrammarException;
.super Lgroovyjarjarantlr4/v4/runtime/misc/ParseCancellationException;
.source "v3TreeGrammarException.java"


# static fields
.field private static final serialVersionUID:J = -0x74589259fa3e5909L


# instance fields
.field public location:Lgroovyjarjarantlr4/runtime/Token;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 17
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/misc/ParseCancellationException;-><init>()V

    .line 18
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/v3TreeGrammarException;->location:Lgroovyjarjarantlr4/runtime/Token;

    return-void
.end method
