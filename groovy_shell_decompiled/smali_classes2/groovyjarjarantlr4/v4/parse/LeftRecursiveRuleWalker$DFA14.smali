.class public Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;
.super Lgroovyjarjarantlr4/runtime/DFA;
.source "LeftRecursiveRuleWalker.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "DFA14"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;Lgroovyjarjarantlr4/runtime/BaseRecognizer;)V
    .locals 0

    .line 2813
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->this$0:Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;

    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/DFA;-><init>()V

    .line 2814
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->recognizer:Lgroovyjarjarantlr4/runtime/BaseRecognizer;

    const/16 p1, 0xe

    .line 2815
    iput p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->decisionNumber:I

    .line 2816
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA14_eot:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->eot:[S

    .line 2817
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA14_eof:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->eof:[S

    .line 2818
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA14_min:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->min:[C

    .line 2819
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA14_max:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->max:[C

    .line 2820
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA14_accept:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->accept:[S

    .line 2821
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA14_special:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->special:[S

    .line 2822
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA14_transition:[[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA14;->transition:[[S

    return-void
.end method


# virtual methods
.method public getDescription()Ljava/lang/String;
    .locals 1

    const-string v0, "()+ loopback of 106:4: ( element )+"

    return-object v0
.end method
