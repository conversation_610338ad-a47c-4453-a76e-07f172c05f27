.class public Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;
.super Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;
.source "ToolANTLRLexer.java"


# instance fields
.field public tool:Lgroovyjarjarantlr4/v4/Tool;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/CharStream;Lgroovyjarjarantlr4/v4/Tool;)V
    .locals 0

    .line 19
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    .line 20
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    return-void
.end method


# virtual methods
.method public displayRecognitionError([Ljava/lang/String;Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 7

    .line 25
    invoke-virtual {p0, p2, p1}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;->getErrorMessage(Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 26
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->SYNTAX_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;->getSourceName()Ljava/lang/String;

    move-result-object v3

    iget-object v4, p2, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    const/4 v0, 0x1

    new-array v6, v0, [Ljava/lang/Object;

    const/4 v0, 0x0

    aput-object p1, v6, v0

    move-object v5, p2

    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->syntaxError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/Object;)V

    return-void
.end method

.method public varargs grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V
    .locals 2

    .line 31
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;->getSourceName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, p1, v1, p2, p3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    return-void
.end method
