.class public Lgroovyjarjarantlr4/v4/parse/ANTLRParser$id_return;
.super Lgroovyjarjarantlr4/runtime/ParserRuleReturnScope;
.source "ANTLRParser.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/ANTLRParser;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "id_return"
.end annotation


# instance fields
.field tree:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 8966
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/ParserRuleReturnScope;-><init>()V

    return-void
.end method


# virtual methods
.method public getTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 8969
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRParser$id_return;->tree:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    return-object v0
.end method

.method public bridge synthetic getTree()Ljava/lang/Object;
    .locals 1

    .line 8966
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/ANTLRParser$id_return;->getTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    return-object v0
.end method
