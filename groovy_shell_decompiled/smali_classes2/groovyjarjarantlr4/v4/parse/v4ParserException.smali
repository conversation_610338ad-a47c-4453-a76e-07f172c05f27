.class public Lgroovyjarjarantlr4/v4/parse/v4ParserException;
.super Lgroovyjarjarantlr4/runtime/RecognitionException;
.source "v4ParserException.java"


# static fields
.field private static final serialVersionUID:J = -0x6e65b41ac4a70382L


# instance fields
.field public msg:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 18
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/RecognitionException;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/IntStream;)V
    .locals 0

    .line 21
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/runtime/RecognitionException;-><init>(Lgroovyjarjarantlr4/runtime/IntStream;)V

    .line 22
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/v4ParserException;->msg:Ljava/lang/String;

    return-void
.end method
