.class public Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$prequelConstructs_return;
.super Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;
.source "GrammarTreeVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "prequelConstructs_return"
.end annotation


# instance fields
.field public firstOne:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 508
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;-><init>()V

    const/4 v0, 0x0

    .line 509
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$prequelConstructs_return;->firstOne:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    return-void
.end method
