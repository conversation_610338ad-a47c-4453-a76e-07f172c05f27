.class public Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;
.super Lgroovyjarjarantlr4/runtime/DFA;
.source "ANTLRLexer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "DFA35"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;Lgroovyjarjarantlr4/runtime/BaseRecognizer;)V
    .locals 0

    .line 4149
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->this$0:Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;

    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/DFA;-><init>()V

    .line 4150
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->recognizer:Lgroovyjarjarantlr4/runtime/BaseRecognizer;

    const/16 p1, 0x23

    .line 4151
    iput p1, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->decisionNumber:I

    .line 4152
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->DFA35_eot:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->eot:[S

    .line 4153
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->DFA35_eof:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->eof:[S

    .line 4154
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->DFA35_min:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->min:[C

    .line 4155
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->DFA35_max:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->max:[C

    .line 4156
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->DFA35_accept:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->accept:[S

    .line 4157
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->DFA35_special:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->special:[S

    .line 4158
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->DFA35_transition:[[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->transition:[[S

    return-void
.end method


# virtual methods
.method public getDescription()Ljava/lang/String;
    .locals 1

    const-string v0, "1:1: Tokens : ( COMMENT | ARG_OR_CHARSET | ACTION | OPTIONS | TOKENS_SPEC | CHANNELS | IMPORT | FRAGMENT | LEXER | PARSER | GRAMMAR | TREE_GRAMMAR | PROTECTED | PUBLIC | PRIVATE | RETURNS | LOCALS | THROWS | CATCH | FINALLY | MODE | COLON | COLONCOLON | COMMA | SEMI | LPAREN | RPAREN | RARROW | LT | GT | ASSIGN | QUESTION | SYNPRED | STAR | PLUS | PLUS_ASSIGN | OR | DOLLAR | DOT | RANGE | AT | POUND | NOT | RBRACE | ID | INT | STRING_LITERAL | WS | UnicodeBOM | ERRCHAR );"

    return-object v0
.end method

.method public specialStateTransition(ILgroovyjarjarantlr4/runtime/IntStream;)I
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/NoViableAltException;
        }
    .end annotation

    move-object/from16 v0, p0

    move/from16 v1, p1

    move-object/from16 v2, p2

    const/16 v3, 0x29

    const/16 v4, 0x28

    const/16 v8, 0xd

    const/16 v9, 0xa

    const/16 v10, 0xc

    const/16 v12, 0x9

    const/16 v13, 0xb

    const/16 v14, 0x23

    const/4 v15, 0x1

    if-eqz v1, :cond_49

    if-eq v1, v15, :cond_0

    goto/16 :goto_6

    .line 4184
    :cond_0
    invoke-interface {v2, v15}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result v6

    const/16 v15, 0x2f

    const/16 v7, 0x20

    const/16 v5, 0x24

    const/16 v11, 0x8

    if-ne v6, v15, :cond_1

    const/4 v7, 0x1

    goto/16 :goto_4

    :cond_1
    const/16 v15, 0x5b

    if-ne v6, v15, :cond_2

    const/4 v7, 0x2

    goto/16 :goto_4

    :cond_2
    const/16 v15, 0x7b

    if-ne v6, v15, :cond_3

    const/4 v7, 0x3

    goto/16 :goto_4

    :cond_3
    const/16 v15, 0x6f

    if-ne v6, v15, :cond_4

    const/4 v7, 0x4

    goto/16 :goto_4

    :cond_4
    const/16 v15, 0x74

    if-ne v6, v15, :cond_5

    const/4 v7, 0x5

    goto/16 :goto_4

    :cond_5
    const/16 v15, 0x63

    if-ne v6, v15, :cond_6

    const/4 v7, 0x6

    goto/16 :goto_4

    :cond_6
    const/16 v15, 0x69

    if-ne v6, v15, :cond_7

    const/4 v7, 0x7

    goto/16 :goto_4

    :cond_7
    const/16 v15, 0x66

    if-ne v6, v15, :cond_8

    move v7, v11

    goto/16 :goto_4

    :cond_8
    const/16 v15, 0x6c

    if-ne v6, v15, :cond_9

    move v7, v12

    goto/16 :goto_4

    :cond_9
    const/16 v15, 0x70

    if-ne v6, v15, :cond_a

    move v7, v9

    goto/16 :goto_4

    :cond_a
    const/16 v15, 0x67

    if-ne v6, v15, :cond_b

    move v7, v13

    goto/16 :goto_4

    :cond_b
    const/16 v15, 0x72

    if-ne v6, v15, :cond_c

    move v7, v10

    goto/16 :goto_4

    :cond_c
    const/16 v15, 0x6d

    if-ne v6, v15, :cond_d

    move v7, v8

    goto/16 :goto_4

    :cond_d
    const/16 v15, 0x3a

    if-ne v6, v15, :cond_e

    const/16 v7, 0xe

    goto/16 :goto_4

    :cond_e
    const/16 v15, 0x2c

    if-ne v6, v15, :cond_f

    const/16 v7, 0xf

    goto/16 :goto_4

    :cond_f
    const/16 v15, 0x3b

    if-ne v6, v15, :cond_10

    const/16 v7, 0x10

    goto/16 :goto_4

    :cond_10
    if-ne v6, v4, :cond_11

    const/16 v7, 0x11

    goto/16 :goto_4

    :cond_11
    if-ne v6, v3, :cond_12

    const/16 v7, 0x12

    goto/16 :goto_4

    :cond_12
    const/16 v3, 0x2d

    if-ne v6, v3, :cond_13

    const/16 v7, 0x13

    goto/16 :goto_4

    :cond_13
    const/16 v3, 0x3c

    if-ne v6, v3, :cond_14

    const/16 v7, 0x14

    goto/16 :goto_4

    :cond_14
    const/16 v3, 0x3e

    if-ne v6, v3, :cond_15

    const/16 v7, 0x15

    goto/16 :goto_4

    :cond_15
    const/16 v3, 0x3d

    if-ne v6, v3, :cond_16

    const/16 v7, 0x16

    goto/16 :goto_4

    :cond_16
    const/16 v3, 0x3f

    if-ne v6, v3, :cond_17

    const/16 v7, 0x17

    goto/16 :goto_4

    :cond_17
    const/16 v3, 0x2a

    if-ne v6, v3, :cond_18

    const/16 v7, 0x18

    goto/16 :goto_4

    :cond_18
    const/16 v3, 0x2b

    if-ne v6, v3, :cond_19

    const/16 v7, 0x19

    goto/16 :goto_4

    :cond_19
    const/16 v3, 0x7c

    if-ne v6, v3, :cond_1a

    const/16 v7, 0x1a

    goto/16 :goto_4

    :cond_1a
    if-ne v6, v5, :cond_1b

    const/16 v7, 0x1b

    goto/16 :goto_4

    :cond_1b
    const/16 v3, 0x2e

    if-ne v6, v3, :cond_1c

    const/16 v7, 0x1c

    goto/16 :goto_4

    :cond_1c
    const/16 v3, 0x40

    if-ne v6, v3, :cond_1d

    const/16 v7, 0x1d

    goto/16 :goto_4

    :cond_1d
    if-ne v6, v14, :cond_1e

    const/16 v7, 0x1e

    goto/16 :goto_4

    :cond_1e
    const/16 v3, 0x7e

    if-ne v6, v3, :cond_1f

    const/16 v7, 0x1f

    goto/16 :goto_4

    :cond_1f
    const/16 v3, 0x7d

    if-ne v6, v3, :cond_20

    goto/16 :goto_4

    :cond_20
    const/16 v3, 0x41

    if-lt v6, v3, :cond_22

    const/16 v3, 0x5a

    if-le v6, v3, :cond_21

    goto :goto_1

    :cond_21
    :goto_0
    const/16 v3, 0x21

    goto/16 :goto_3

    :cond_22
    :goto_1
    const/16 v3, 0x61

    if-lt v6, v3, :cond_23

    const/16 v3, 0x62

    if-le v6, v3, :cond_21

    :cond_23
    const/16 v3, 0x64

    if-lt v6, v3, :cond_24

    const/16 v3, 0x65

    if-le v6, v3, :cond_21

    :cond_24
    const/16 v3, 0x68

    if-eq v6, v3, :cond_21

    const/16 v3, 0x6a

    if-lt v6, v3, :cond_25

    const/16 v3, 0x6b

    if-le v6, v3, :cond_21

    :cond_25
    const/16 v3, 0x6e

    if-eq v6, v3, :cond_21

    const/16 v3, 0x71

    if-eq v6, v3, :cond_21

    const/16 v3, 0x73

    if-eq v6, v3, :cond_21

    const/16 v3, 0x75

    if-lt v6, v3, :cond_26

    const/16 v3, 0x7a

    if-le v6, v3, :cond_21

    :cond_26
    const/16 v3, 0xc0

    if-lt v6, v3, :cond_27

    const/16 v3, 0xd6

    if-le v6, v3, :cond_21

    :cond_27
    const/16 v3, 0xd8

    if-lt v6, v3, :cond_28

    const/16 v3, 0xf6

    if-le v6, v3, :cond_21

    :cond_28
    const/16 v3, 0xf8

    if-lt v6, v3, :cond_29

    const/16 v3, 0x2ff

    if-le v6, v3, :cond_21

    :cond_29
    const/16 v3, 0x370

    if-lt v6, v3, :cond_2a

    const/16 v3, 0x37d

    if-le v6, v3, :cond_21

    :cond_2a
    const/16 v3, 0x37f

    if-lt v6, v3, :cond_2b

    const/16 v3, 0x1fff

    if-le v6, v3, :cond_21

    :cond_2b
    const/16 v3, 0x200c

    if-lt v6, v3, :cond_2c

    const/16 v3, 0x200d

    if-le v6, v3, :cond_21

    :cond_2c
    const/16 v3, 0x2070

    if-lt v6, v3, :cond_2d

    const/16 v3, 0x218f

    if-le v6, v3, :cond_21

    :cond_2d
    const/16 v3, 0x2c00

    if-lt v6, v3, :cond_2e

    const/16 v3, 0x2fef

    if-le v6, v3, :cond_21

    :cond_2e
    const/16 v3, 0x3001

    if-lt v6, v3, :cond_2f

    const v3, 0xd7ff

    if-le v6, v3, :cond_21

    :cond_2f
    const v3, 0xf900

    if-lt v6, v3, :cond_30

    const v3, 0xfdcf

    if-le v6, v3, :cond_21

    :cond_30
    const v3, 0xfdf0

    if-lt v6, v3, :cond_31

    const v3, 0xfefe

    if-le v6, v3, :cond_21

    :cond_31
    const v3, 0xff00

    if-lt v6, v3, :cond_32

    const v3, 0xfffd

    if-gt v6, v3, :cond_32

    goto/16 :goto_0

    :cond_32
    const/16 v3, 0x30

    if-lt v6, v3, :cond_33

    const/16 v3, 0x39

    if-gt v6, v3, :cond_33

    const/16 v7, 0x22

    goto/16 :goto_4

    :cond_33
    const/16 v3, 0x27

    if-ne v6, v3, :cond_34

    move v7, v14

    goto/16 :goto_4

    :cond_34
    if-lt v6, v12, :cond_35

    if-le v6, v9, :cond_37

    :cond_35
    if-lt v6, v10, :cond_36

    if-le v6, v8, :cond_37

    :cond_36
    if-ne v6, v7, :cond_38

    :cond_37
    :goto_2
    move v7, v5

    goto/16 :goto_4

    :cond_38
    const v3, 0xfeff

    if-ne v6, v3, :cond_39

    const/16 v7, 0x25

    goto/16 :goto_4

    :cond_39
    if-ltz v6, :cond_3a

    if-le v6, v11, :cond_48

    :cond_3a
    if-eq v6, v13, :cond_48

    const/16 v3, 0xe

    if-lt v6, v3, :cond_3b

    const/16 v3, 0x1f

    if-le v6, v3, :cond_48

    :cond_3b
    const/16 v3, 0x21

    if-lt v6, v3, :cond_3c

    const/16 v3, 0x22

    if-le v6, v3, :cond_48

    :cond_3c
    const/16 v3, 0x25

    const/16 v5, 0x26

    if-lt v6, v3, :cond_3d

    if-le v6, v5, :cond_37

    :cond_3d
    const/16 v3, 0x5c

    if-lt v6, v3, :cond_3e

    const/16 v3, 0x60

    if-le v6, v3, :cond_37

    :cond_3e
    const/16 v3, 0x7f

    if-lt v6, v3, :cond_3f

    const/16 v3, 0xbf

    if-le v6, v3, :cond_37

    :cond_3f
    const/16 v3, 0xd7

    if-eq v6, v3, :cond_37

    const/16 v3, 0xf7

    if-eq v6, v3, :cond_37

    const/16 v3, 0x300

    if-lt v6, v3, :cond_40

    const/16 v3, 0x36f

    if-le v6, v3, :cond_37

    :cond_40
    const/16 v3, 0x37e

    if-eq v6, v3, :cond_37

    const/16 v3, 0x2000

    if-lt v6, v3, :cond_41

    const/16 v3, 0x200b

    if-le v6, v3, :cond_37

    :cond_41
    const/16 v3, 0x200e

    if-lt v6, v3, :cond_42

    const/16 v3, 0x206f

    if-le v6, v3, :cond_37

    :cond_42
    const/16 v3, 0x2190

    if-lt v6, v3, :cond_43

    const/16 v3, 0x2bff

    if-le v6, v3, :cond_37

    :cond_43
    const/16 v3, 0x2ff0

    if-lt v6, v3, :cond_44

    const/16 v3, 0x3000

    if-le v6, v3, :cond_37

    :cond_44
    const v3, 0xd800

    if-lt v6, v3, :cond_45

    const v3, 0xf8ff

    if-le v6, v3, :cond_37

    :cond_45
    const v3, 0xfdd0

    if-lt v6, v3, :cond_46

    const v3, 0xfdef

    if-le v6, v3, :cond_37

    :cond_46
    const v3, 0xfffe

    if-lt v6, v3, :cond_47

    const v3, 0xffff

    if-gt v6, v3, :cond_47

    goto/16 :goto_2

    :cond_47
    const/4 v7, -0x1

    goto :goto_4

    :cond_48
    const/16 v5, 0x26

    goto/16 :goto_2

    :goto_3
    move v7, v3

    :goto_4
    if-ltz v7, :cond_51

    return v7

    :cond_49
    move v6, v15

    const/16 v5, 0x26

    .line 4170
    invoke-interface {v2, v6}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result v7

    .line 4172
    invoke-interface/range {p2 .. p2}, Lgroovyjarjarantlr4/runtime/IntStream;->index()I

    move-result v6

    .line 4173
    invoke-interface/range {p2 .. p2}, Lgroovyjarjarantlr4/runtime/IntStream;->rewind()V

    if-ltz v7, :cond_4a

    if-le v7, v12, :cond_4c

    :cond_4a
    if-lt v7, v13, :cond_4b

    if-le v7, v10, :cond_4c

    :cond_4b
    const/16 v10, 0xe

    if-lt v7, v10, :cond_4e

    const v10, 0xffff

    if-gt v7, v10, :cond_4e

    .line 4175
    :cond_4c
    iget-object v10, v0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->this$0:Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;

    iget-boolean v10, v10, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->isLexerRule:Z

    if-eqz v10, :cond_4d

    iget-object v10, v0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->this$0:Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;

    iget-boolean v10, v10, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->isLexerRule:Z

    if-eqz v10, :cond_4e

    :cond_4d
    move v3, v4

    goto :goto_5

    :cond_4e
    if-eq v7, v9, :cond_4f

    if-ne v7, v8, :cond_50

    .line 4176
    :cond_4f
    iget-object v4, v0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->this$0:Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;

    iget-boolean v4, v4, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->isLexerRule:Z

    if-nez v4, :cond_50

    goto :goto_5

    :cond_50
    move v3, v5

    .line 4179
    :goto_5
    invoke-interface {v2, v6}, Lgroovyjarjarantlr4/runtime/IntStream;->seek(I)V

    if-ltz v3, :cond_51

    return v3

    .line 4227
    :cond_51
    :goto_6
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->this$0:Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;

    invoke-static {v3}, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->access$200(Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;)Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    move-result-object v3

    iget v3, v3, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    if-lez v3, :cond_52

    iget-object v1, v0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->this$0:Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;

    invoke-static {v1}, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->access$300(Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;)Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    move-result-object v1

    const/4 v2, 0x1

    iput-boolean v2, v1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    const/4 v1, -0x1

    return v1

    .line 4228
    :cond_52
    new-instance v3, Lgroovyjarjarantlr4/runtime/NoViableAltException;

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->getDescription()Ljava/lang/String;

    move-result-object v4

    invoke-direct {v3, v4, v14, v1, v2}, Lgroovyjarjarantlr4/runtime/NoViableAltException;-><init>(Ljava/lang/String;IILgroovyjarjarantlr4/runtime/IntStream;)V

    .line 4230
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer$DFA35;->error(Lgroovyjarjarantlr4/runtime/NoViableAltException;)V

    .line 4231
    throw v3
.end method
