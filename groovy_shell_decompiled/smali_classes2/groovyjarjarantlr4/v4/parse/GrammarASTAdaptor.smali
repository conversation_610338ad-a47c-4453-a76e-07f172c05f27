.class public Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;
.super Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;
.source "GrammarASTAdaptor.java"


# instance fields
.field input:Lgroovyjarjarantlr4/runtime/CharStream;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 19
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;-><init>()V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/CharStream;)V
    .locals 0

    .line 20
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    return-void
.end method


# virtual methods
.method public create(ILgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 0

    .line 59
    invoke-super {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;->create(ILgroovyjarjarantlr4/runtime/Token;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    return-object p1
.end method

.method public create(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 0

    .line 54
    invoke-super {p0, p1, p2, p3}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;->create(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    return-object p1
.end method

.method public create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 2

    const/16 v0, 0x5e

    if-ne p1, v0, :cond_0

    .line 38
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    new-instance v1, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-direct {v1, p1, p2}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    goto :goto_0

    :cond_0
    const/16 v0, 0x3e

    if-ne p1, v0, :cond_1

    .line 43
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;

    new-instance v1, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-direct {v1, p1, p2}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    goto :goto_0

    .line 46
    :cond_1
    invoke-super {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;->create(ILjava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 48
    :goto_0
    iget-object p1, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    iget-object p2, p0, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {p1, p2}, Lgroovyjarjarantlr4/runtime/Token;->setInputStream(Lgroovyjarjarantlr4/runtime/CharStream;)V

    return-object v0
.end method

.method public create(Lgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 29
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-object v0
.end method

.method public bridge synthetic create(ILgroovyjarjarantlr4/runtime/Token;)Ljava/lang/Object;
    .locals 0

    .line 17
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic create(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)Ljava/lang/Object;
    .locals 0

    .line 17
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILgroovyjarjarantlr4/runtime/Token;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic create(ILjava/lang/String;)Ljava/lang/Object;
    .locals 0

    .line 17
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic create(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/Object;
    .locals 0

    .line 17
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(Lgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p1

    return-object p1
.end method

.method public dupNode(Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 0

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 65
    :cond_0
    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->dupNode()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic dupNode(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 17
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->dupNode(Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p1

    return-object p1
.end method

.method public errorNode(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;
    .locals 1

    .line 72
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;

    invoke-direct {v0, p1, p2, p3, p4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    return-object v0
.end method

.method public bridge synthetic errorNode(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;)Ljava/lang/Object;
    .locals 0

    .line 17
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->errorNode(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/RecognitionException;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;

    move-result-object p1

    return-object p1
.end method

.method public nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 1

    .line 24
    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;->nil()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    return-object v0
.end method

.method public bridge synthetic nil()Ljava/lang/Object;
    .locals 1

    .line 17
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    return-object v0
.end method
