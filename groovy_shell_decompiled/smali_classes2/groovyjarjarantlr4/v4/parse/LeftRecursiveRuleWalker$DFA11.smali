.class public Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;
.super Lgroovyjarjarantlr4/runtime/DFA;
.source "LeftRecursiveRuleWalker.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "DFA11"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;Lgroovyjarjarantlr4/runtime/BaseRecognizer;)V
    .locals 0

    .line 2638
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->this$0:Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;

    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/DFA;-><init>()V

    .line 2639
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->recognizer:Lgroovyjarjarantlr4/runtime/BaseRecognizer;

    const/16 p1, 0xb

    .line 2640
    iput p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->decisionNumber:I

    .line 2641
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA11_eot:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->eot:[S

    .line 2642
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA11_eof:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->eof:[S

    .line 2643
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA11_min:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->min:[C

    .line 2644
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA11_max:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->max:[C

    .line 2645
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA11_accept:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->accept:[S

    .line 2646
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA11_special:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->special:[S

    .line 2647
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;->DFA11_transition:[[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker$DFA11;->transition:[[S

    return-void
.end method


# virtual methods
.method public getDescription()Ljava/lang/String;
    .locals 1

    const-string v0, "()* loopback of 100:35: ( element )*"

    return-object v0
.end method
