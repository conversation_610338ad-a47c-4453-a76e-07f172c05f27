.class public Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;
.super Lgroovyjarjarantlr4/runtime/DFA;
.source "GrammarTreeVisitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "DFA38"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;Lgroovyjarjarantlr4/runtime/BaseRecognizer;)V
    .locals 0

    .line 5278
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->this$0:Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;

    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/DFA;-><init>()V

    .line 5279
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->recognizer:Lgroovyjarjarantlr4/runtime/BaseRecognizer;

    const/16 p1, 0x26

    .line 5280
    iput p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->decisionNumber:I

    .line 5281
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;->DFA38_eot:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->eot:[S

    .line 5282
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;->DFA38_eof:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->eof:[S

    .line 5283
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;->DFA38_min:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->min:[C

    .line 5284
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;->DFA38_max:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->max:[C

    .line 5285
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;->DFA38_accept:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->accept:[S

    .line 5286
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;->DFA38_special:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->special:[S

    .line 5287
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;->DFA38_transition:[[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$DFA38;->transition:[[S

    return-void
.end method


# virtual methods
.method public getDescription()Ljava/lang/String;
    .locals 1

    const-string v0, "749:1: alternative : ( ^( ALT ( elementOptions )? ( element )+ ) | ^( ALT ( elementOptions )? EPSILON ) );"

    return-object v0
.end method
