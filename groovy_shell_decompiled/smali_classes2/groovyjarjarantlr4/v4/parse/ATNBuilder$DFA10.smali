.class public Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;
.super Lgroovyjarjarantlr4/runtime/DFA;
.source "ATNBuilder.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/parse/ATNBuilder;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "DFA10"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/parse/ATNBuilder;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/parse/ATNBuilder;Lgroovyjarjarantlr4/runtime/BaseRecognizer;)V
    .locals 0

    .line 2315
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->this$0:Lgroovyjarjarantlr4/v4/parse/ATNBuilder;

    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/DFA;-><init>()V

    .line 2316
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->recognizer:Lgroovyjarjarantlr4/runtime/BaseRecognizer;

    const/16 p1, 0xa

    .line 2317
    iput p1, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->decisionNumber:I

    .line 2318
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ATNBuilder;->DFA10_eot:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->eot:[S

    .line 2319
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ATNBuilder;->DFA10_eof:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->eof:[S

    .line 2320
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ATNBuilder;->DFA10_min:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->min:[C

    .line 2321
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ATNBuilder;->DFA10_max:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->max:[C

    .line 2322
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ATNBuilder;->DFA10_accept:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->accept:[S

    .line 2323
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ATNBuilder;->DFA10_special:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->special:[S

    .line 2324
    sget-object p1, Lgroovyjarjarantlr4/v4/parse/ATNBuilder;->DFA10_transition:[[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/parse/ATNBuilder$DFA10;->transition:[[S

    return-void
.end method


# virtual methods
.method public getDescription()Ljava/lang/String;
    .locals 1

    const-string v0, "59:1: alternative returns [ATNFactory.Handle p] : ( ^( LEXER_ALT_ACTION a= alternative lexerCommands ) | ^( ALT ( elementOptions )? EPSILON ) | ^( ALT ( elementOptions )? (e= element )+ ) );"

    return-object v0
.end method
