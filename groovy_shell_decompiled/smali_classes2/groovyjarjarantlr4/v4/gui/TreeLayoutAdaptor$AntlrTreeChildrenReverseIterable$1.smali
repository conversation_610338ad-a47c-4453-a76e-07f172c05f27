.class Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;
.super Ljava/lang/Object;
.source "TreeLayoutAdaptor.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;->iterator()Ljava/util/Iterator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
        ">;"
    }
.end annotation


# instance fields
.field private i:I

.field final synthetic this$0:Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;)V
    .locals 0

    .line 61
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;->this$0:Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 62
    invoke-static {p1}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;->access$100(Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p1

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChildCount()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;->i:I

    return-void
.end method


# virtual methods
.method public hasNext()Z
    .locals 1

    .line 66
    iget v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;->i:I

    if-lez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public next()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 2

    .line 71
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 74
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;->this$0:Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;->access$100(Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;->i:I

    add-int/lit8 v1, v1, -0x1

    iput v1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;->i:I

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v0

    return-object v0

    .line 72
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public bridge synthetic next()Ljava/lang/Object;
    .locals 1

    .line 61
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;->next()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v0

    return-object v0
.end method

.method public remove()V
    .locals 1

    .line 79
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method
