.class public Lgroovyjarjarantlr4/v4/gui/TestRig;
.super Ljava/lang/Object;
.source "TestRig.java"


# static fields
.field public static final LEXER_START_RULE_NAME:Ljava/lang/String; = "tokens"


# instance fields
.field protected SLL:Z

.field protected diagnostics:Z

.field protected encoding:Ljava/lang/String;

.field protected grammarName:Ljava/lang/String;

.field protected gui:Z

.field protected final inputFiles:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field protected printTree:Z

.field protected psFile:Ljava/lang/String;

.field protected showTokens:Z

.field protected startRuleName:Ljava/lang/String;

.field protected trace:Z


# direct methods
.method public constructor <init>([Ljava/lang/String;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 58
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 48
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->inputFiles:Ljava/util/List;

    const/4 v0, 0x0

    .line 49
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->printTree:Z

    .line 50
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->gui:Z

    const/4 v1, 0x0

    .line 51
    iput-object v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->psFile:Ljava/lang/String;

    .line 52
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->showTokens:Z

    .line 53
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->trace:Z

    .line 54
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->diagnostics:Z

    .line 55
    iput-object v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->encoding:Ljava/lang/String;

    .line 56
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->SLL:Z

    .line 59
    array-length v1, p1

    const/4 v2, 0x2

    if-ge v1, v2, :cond_0

    .line 60
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v0, "java org.antlr.v4.gui.TestRig GrammarName startRuleName\n  [-tokens] [-tree] [-gui] [-ps file.ps] [-encoding encodingname]\n  [-trace] [-diagnostics] [-SLL]\n  [input-filename(s)]"

    invoke-virtual {p1, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 64
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v0, "Use startRuleName=\'tokens\' if GrammarName is a lexer grammar."

    invoke-virtual {p1, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 65
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v0, "Omitting input-filename makes rig read from stdin."

    invoke-virtual {p1, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void

    .line 69
    :cond_0
    aget-object v1, p1, v0

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->grammarName:Ljava/lang/String;

    const/4 v1, 0x1

    .line 71
    aget-object v3, p1, v1

    iput-object v3, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->startRuleName:Ljava/lang/String;

    .line 73
    :cond_1
    :goto_0
    array-length v3, p1

    if-ge v2, v3, :cond_c

    .line 74
    aget-object v3, p1, v2

    add-int/lit8 v2, v2, 0x1

    .line 76
    invoke-virtual {v3, v0}, Ljava/lang/String;->charAt(I)C

    move-result v4

    const/16 v5, 0x2d

    if-eq v4, v5, :cond_2

    .line 77
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->inputFiles:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_2
    const-string v4, "-tree"

    .line 80
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_3

    .line 81
    iput-boolean v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->printTree:Z

    :cond_3
    const-string v4, "-gui"

    .line 83
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_4

    .line 84
    iput-boolean v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->gui:Z

    :cond_4
    const-string v4, "-tokens"

    .line 86
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_5

    .line 87
    iput-boolean v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->showTokens:Z

    goto :goto_0

    :cond_5
    const-string v4, "-trace"

    .line 89
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_6

    .line 90
    iput-boolean v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->trace:Z

    goto :goto_0

    :cond_6
    const-string v4, "-SLL"

    .line 92
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_7

    .line 93
    iput-boolean v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->SLL:Z

    goto :goto_0

    :cond_7
    const-string v4, "-diagnostics"

    .line 95
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_8

    .line 96
    iput-boolean v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->diagnostics:Z

    goto :goto_0

    :cond_8
    const-string v4, "-encoding"

    .line 98
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_a

    .line 99
    array-length v3, p1

    if-lt v2, v3, :cond_9

    .line 100
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v0, "missing encoding on -encoding"

    invoke-virtual {p1, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void

    .line 103
    :cond_9
    aget-object v3, p1, v2

    iput-object v3, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->encoding:Ljava/lang/String;

    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_a
    const-string v4, "-ps"

    .line 106
    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 107
    array-length v3, p1

    if-lt v2, v3, :cond_b

    .line 108
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v0, "missing filename on -ps"

    invoke-virtual {p1, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void

    .line 111
    :cond_b
    aget-object v3, p1, v2

    iput-object v3, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->psFile:Ljava/lang/String;

    goto :goto_1

    :cond_c
    return-void
.end method

.method public static main([Ljava/lang/String;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 118
    new-instance v0, Lgroovyjarjarantlr4/v4/gui/TestRig;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/gui/TestRig;-><init>([Ljava/lang/String;)V

    .line 119
    array-length p0, p0

    const/4 v1, 0x2

    if-lt p0, v1, :cond_0

    .line 120
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/gui/TestRig;->process()V

    :cond_0
    return-void
.end method


# virtual methods
.method public process()V
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 126
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->grammarName:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "Lexer"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 127
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Thread;->getContextClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    .line 130
    :try_start_0
    invoke-virtual {v1, v0}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    const-class v2, Lgroovyjarjarantlr4/v4/runtime/Lexer;

    invoke-virtual {v0, v2}, Ljava/lang/Class;->asSubclass(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 134
    :catch_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->grammarName:Ljava/lang/String;

    .line 136
    :try_start_1
    invoke-virtual {v1, v0}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v2

    const-class v3, Lgroovyjarjarantlr4/v4/runtime/Lexer;

    invoke-virtual {v2, v3}, Ljava/lang/Class;->asSubclass(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v0
    :try_end_1
    .catch Ljava/lang/ClassNotFoundException; {:try_start_1 .. :try_end_1} :catch_1

    :goto_0
    const/4 v2, 0x1

    new-array v3, v2, [Ljava/lang/Class;

    .line 144
    const-class v4, Lgroovyjarjarantlr4/v4/runtime/CharStream;

    const/4 v5, 0x0

    aput-object v4, v3, v5

    invoke-virtual {v0, v3}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v0

    new-array v3, v2, [Ljava/lang/Object;

    const/4 v4, 0x0

    .line 145
    move-object v6, v4

    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/CharStream;

    aput-object v4, v3, v5

    invoke-virtual {v0, v3}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Lexer;

    .line 149
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->startRuleName:Ljava/lang/String;

    const-string v6, "tokens"

    invoke-virtual {v3, v6}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_0

    .line 150
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->grammarName:Ljava/lang/String;

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v6, "Parser"

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 151
    invoke-virtual {v1, v3}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v1

    const-class v3, Lgroovyjarjarantlr4/v4/runtime/Parser;

    invoke-virtual {v1, v3}, Ljava/lang/Class;->asSubclass(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v1

    new-array v3, v2, [Ljava/lang/Class;

    .line 152
    const-class v6, Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    aput-object v6, v3, v5

    invoke-virtual {v1, v3}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v3

    new-array v6, v2, [Ljava/lang/Object;

    .line 153
    move-object v7, v4

    check-cast v7, Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    aput-object v4, v6, v5

    invoke-virtual {v3, v6}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    move-object v4, v3

    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/Parser;

    move-object v9, v4

    move-object v4, v1

    move-object v1, v9

    goto :goto_1

    :cond_0
    move-object v1, v4

    .line 156
    :goto_1
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->encoding:Ljava/lang/String;

    if-nez v3, :cond_1

    invoke-static {}, Ljava/nio/charset/Charset;->defaultCharset()Ljava/nio/charset/Charset;

    move-result-object v3

    goto :goto_2

    :cond_1
    invoke-static {v3}, Ljava/nio/charset/Charset;->forName(Ljava/lang/String;)Ljava/nio/charset/Charset;

    move-result-object v3

    .line 157
    :goto_2
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->inputFiles:Ljava/util/List;

    invoke-interface {v5}, Ljava/util/List;->isEmpty()Z

    move-result v5

    if-eqz v5, :cond_2

    .line 158
    sget-object v2, Ljava/lang/System;->in:Ljava/io/InputStream;

    invoke-static {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromStream(Ljava/io/InputStream;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v2

    .line 159
    invoke-virtual {p0, v0, v4, v1, v2}, Lgroovyjarjarantlr4/v4/gui/TestRig;->process(Lgroovyjarjarantlr4/v4/runtime/Lexer;Ljava/lang/Class;Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    return-void

    .line 162
    :cond_2
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->inputFiles:Ljava/util/List;

    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :goto_3
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_4

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    .line 163
    new-instance v7, Ljava/io/File;

    invoke-direct {v7, v6}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v3}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromFile(Ljava/io/File;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v7

    .line 164
    iget-object v8, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->inputFiles:Ljava/util/List;

    invoke-interface {v8}, Ljava/util/List;->size()I

    move-result v8

    if-le v8, v2, :cond_3

    .line 165
    sget-object v8, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {v8, v6}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 167
    :cond_3
    invoke-virtual {p0, v0, v4, v1, v7}, Lgroovyjarjarantlr4/v4/gui/TestRig;->process(Lgroovyjarjarantlr4/v4/runtime/Lexer;Ljava/lang/Class;Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    goto :goto_3

    :cond_4
    return-void

    .line 139
    :catch_1
    sget-object v1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Can\'t load "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " as lexer or parser"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method

.method protected process(Lgroovyjarjarantlr4/v4/runtime/Lexer;Ljava/lang/Class;Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/Lexer;",
            "Ljava/lang/Class<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/Parser;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/Parser;",
            "Lgroovyjarjarantlr4/v4/runtime/CharStream;",
            ")V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/IllegalAccessException;,
            Ljava/lang/reflect/InvocationTargetException;,
            Ljavax/print/PrintException;
        }
    .end annotation

    .line 172
    invoke-virtual {p1, p4}, Lgroovyjarjarantlr4/v4/runtime/Lexer;->setInputStream(Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    .line 173
    new-instance p4, Lgroovyjarjarantlr4/v4/runtime/CommonTokenStream;

    invoke-direct {p4, p1}, Lgroovyjarjarantlr4/v4/runtime/CommonTokenStream;-><init>(Lgroovyjarjarantlr4/v4/runtime/TokenSource;)V

    .line 175
    invoke-virtual {p4}, Lgroovyjarjarantlr4/v4/runtime/CommonTokenStream;->fill()V

    .line 177
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->showTokens:Z

    if-eqz v0, :cond_1

    .line 178
    invoke-virtual {p4}, Lgroovyjarjarantlr4/v4/runtime/CommonTokenStream;->getTokens()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 179
    instance-of v2, v1, Lgroovyjarjarantlr4/v4/runtime/CommonToken;

    if-eqz v2, :cond_0

    .line 180
    sget-object v2, Ljava/lang/System;->out:Ljava/io/PrintStream;

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/CommonToken;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/runtime/CommonToken;->toString(Lgroovyjarjarantlr4/v4/runtime/Recognizer;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 183
    :cond_0
    sget-object v2, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 188
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->startRuleName:Ljava/lang/String;

    const-string v0, "tokens"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    return-void

    .line 190
    :cond_2
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->diagnostics:Z

    if-eqz p1, :cond_3

    .line 191
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/DiagnosticErrorListener;

    invoke-direct {p1}, Lgroovyjarjarantlr4/v4/runtime/DiagnosticErrorListener;-><init>()V

    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->addErrorListener(Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;)V

    .line 192
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;->LL_EXACT_AMBIG_DETECTION:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->setPredictionMode(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;)V

    .line 195
    :cond_3
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->printTree:Z

    if-nez p1, :cond_4

    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->gui:Z

    if-nez p1, :cond_4

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->psFile:Ljava/lang/String;

    if-eqz p1, :cond_5

    :cond_4
    const/4 p1, 0x1

    .line 196
    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setBuildParseTree(Z)V

    .line 199
    :cond_5
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->SLL:Z

    if-eqz p1, :cond_6

    .line 200
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;->SLL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->setPredictionMode(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;)V

    .line 203
    :cond_6
    invoke-virtual {p3, p4}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setInputStream(Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    .line 204
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->trace:Z

    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setTrace(Z)V

    .line 207
    :try_start_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->startRuleName:Ljava/lang/String;

    const/4 p4, 0x0

    new-array p4, p4, [Ljava/lang/Class;

    invoke-virtual {p2, p1, p4}, Ljava/lang/Class;->getMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object p1

    const/4 p2, 0x0

    .line 208
    move-object p4, p2

    check-cast p4, [Ljava/lang/Object;

    invoke-virtual {p1, p3, p2}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 210
    iget-boolean p2, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->printTree:Z

    if-eqz p2, :cond_7

    .line 211
    sget-object p2, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {p1, p3}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->toStringTree(Lgroovyjarjarantlr4/v4/runtime/Parser;)Ljava/lang/String;

    move-result-object p4

    invoke-virtual {p2, p4}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 213
    :cond_7
    iget-boolean p2, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->gui:Z

    if-eqz p2, :cond_8

    .line 214
    invoke-static {p1, p3}, Lgroovyjarjarantlr4/v4/gui/Trees;->inspect(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/Parser;)Ljava/util/concurrent/Future;

    .line 216
    :cond_8
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->psFile:Ljava/lang/String;

    if-eqz p2, :cond_9

    .line 217
    invoke-static {p1, p3, p2}, Lgroovyjarjarantlr4/v4/gui/Trees;->save(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/Parser;Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    .line 221
    :catch_0
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "No method for rule "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p3, p0, Lgroovyjarjarantlr4/v4/gui/TestRig;->startRuleName:Ljava/lang/String;

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, " or it has arguments"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    :cond_9
    :goto_1
    return-void
.end method
