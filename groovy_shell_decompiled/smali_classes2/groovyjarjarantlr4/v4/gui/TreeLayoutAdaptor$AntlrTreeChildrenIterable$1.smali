.class Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;
.super Ljava/lang/Object;
.source "TreeLayoutAdaptor.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;->iterator()Ljava/util/Iterator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
        ">;"
    }
.end annotation


# instance fields
.field private i:I

.field final synthetic this$0:Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;)V
    .locals 0

    .line 26
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;->this$0:Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 p1, 0x0

    .line 27
    iput p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;->i:I

    return-void
.end method


# virtual methods
.method public hasNext()Z
    .locals 2

    .line 31
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;->this$0:Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;->access$000(Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChildCount()I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;->i:I

    if-le v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public next()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 3

    .line 36
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 39
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;->this$0:Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;->access$000(Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;->i:I

    add-int/lit8 v2, v1, 0x1

    iput v2, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;->i:I

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v0

    return-object v0

    .line 37
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public bridge synthetic next()Ljava/lang/Object;
    .locals 1

    .line 26
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable$1;->next()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v0

    return-object v0
.end method

.method public remove()V
    .locals 1

    .line 44
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    invoke-direct {v0}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw v0
.end method
