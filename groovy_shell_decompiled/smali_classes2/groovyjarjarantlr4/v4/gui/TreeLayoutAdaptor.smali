.class public Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;
.super Ljava/lang/Object;
.source "TreeLayoutAdaptor.java"

# interfaces
.implements Lorg/abego/treelayout/TreeForTreeLayout;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;,
        Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lorg/abego/treelayout/TreeForTreeLayout<",
        "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
        ">;"
    }
.end annotation


# instance fields
.field private root:Lgroovyjarjarantlr4/v4/runtime/tree/Tree;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)V
    .locals 0

    .line 87
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 88
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;->root:Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    return-void
.end method


# virtual methods
.method public getChildren(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/Iterable;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ")",
            "Ljava/lang/Iterable<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ">;"
        }
    .end annotation

    .line 123
    new-instance v0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenIterable;-><init>(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)V

    return-object v0
.end method

.method public bridge synthetic getChildren(Ljava/lang/Object;)Ljava/lang/Iterable;
    .locals 0

    .line 16
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;->getChildren(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/Iterable;

    move-result-object p1

    return-object p1
.end method

.method public getChildrenReverse(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/Iterable;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ")",
            "Ljava/lang/Iterable<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ">;"
        }
    .end annotation

    .line 118
    new-instance v0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;-><init>(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)V

    return-object v0
.end method

.method public bridge synthetic getChildrenReverse(Ljava/lang/Object;)Ljava/lang/Iterable;
    .locals 0

    .line 16
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;->getChildrenReverse(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/Iterable;

    move-result-object p1

    return-object p1
.end method

.method public getFirstChild(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 1

    const/4 v0, 0x0

    .line 113
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic getFirstChild(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 16
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;->getFirstChild(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p1

    return-object p1
.end method

.method public getLastChild(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 1

    .line 108
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChildCount()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic getLastChild(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 16
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;->getLastChild(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p1

    return-object p1
.end method

.method public getRoot()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 1

    .line 103
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;->root:Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    return-object v0
.end method

.method public bridge synthetic getRoot()Ljava/lang/Object;
    .locals 1

    .line 16
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;->getRoot()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v0

    return-object v0
.end method

.method public isChildOfParent(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Z
    .locals 0

    .line 98
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p1

    if-ne p1, p2, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public bridge synthetic isChildOfParent(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 16
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;->isChildOfParent(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Z

    move-result p1

    return p1
.end method

.method public isLeaf(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Z
    .locals 0

    .line 93
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChildCount()I

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public bridge synthetic isLeaf(Ljava/lang/Object;)Z
    .locals 0

    .line 16
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;->isLeaf(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Z

    move-result p1

    return p1
.end method
