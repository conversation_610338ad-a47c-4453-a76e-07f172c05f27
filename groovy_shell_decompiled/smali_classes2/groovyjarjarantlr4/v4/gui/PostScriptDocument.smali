.class public Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;
.super Ljava/lang/Object;
.source "PostScriptDocument.java"


# static fields
.field public static final DEFAULT_FONT:Ljava/lang/String; = "Courier New"

.field public static final POSTSCRIPT_FONT_NAMES:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field protected boundingBox:Ljava/lang/String;

.field protected boundingBoxHeight:I

.field protected boundingBoxWidth:I

.field protected closed:Z

.field protected fontMetrics:Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;

.field protected fontName:Ljava/lang/String;

.field protected fontSize:I

.field protected lineWidth:D

.field protected ps:Ljava/lang/StringBuilder;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 19
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->POSTSCRIPT_FONT_NAMES:Ljava/util/Map;

    const-string v1, "SansSerif.plain"

    const-string v2, "ArialMT"

    .line 20
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "SansSerif.bold"

    const-string v2, "Arial-BoldMT"

    .line 21
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "SansSerif.italic"

    const-string v2, "Arial-ItalicMT"

    .line 22
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "SansSerif.bolditalic"

    const-string v2, "Arial-BoldItalicMT"

    .line 23
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "Serif.plain"

    const-string v2, "TimesNewRomanPSMT"

    .line 24
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "Serif.bold"

    const-string v2, "TimesNewRomanPS-BoldMT"

    .line 25
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "Serif.italic"

    const-string v2, "TimesNewRomanPS-ItalicMT"

    .line 26
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "Serif.bolditalic"

    const-string v2, "TimesNewRomanPS-BoldItalicMT"

    .line 27
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "Monospaced.plain"

    const-string v2, "CourierNewPSMT"

    .line 28
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "Monospaced.bold"

    const-string v2, "CourierNewPS-BoldMT"

    .line 29
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "Monospaced.italic"

    const-string v2, "CourierNewPS-ItalicMT"

    .line 30
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "Monospaced.bolditalic"

    const-string v2, "CourierNewPS-BoldItalicMT"

    .line 31
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    const-string v0, "Courier New"

    const/16 v1, 0xc

    .line 47
    invoke-direct {p0, v0, v1}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 2

    .line 50
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0xc

    .line 39
    iput v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontSize:I

    const-wide v0, 0x3fd3333333333333L    # 0.3

    .line 40
    iput-wide v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->lineWidth:D

    .line 43
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    const/4 v0, 0x0

    .line 44
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->closed:Z

    .line 51
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->header()Ljava/lang/StringBuilder;

    .line 52
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->setFont(Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public boundingBox(II)V
    .locals 2

    .line 61
    iput p1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->boundingBoxWidth:I

    .line 62
    iput p2, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->boundingBoxHeight:I

    .line 63
    sget-object p1, Ljava/util/Locale;->US:Ljava/util/Locale;

    const/4 p2, 0x4

    new-array p2, p2, [Ljava/lang/Object;

    const/4 v0, 0x0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    aput-object v1, p2, v0

    const/4 v0, 0x1

    aput-object v1, p2, v0

    iget v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->boundingBoxWidth:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const/4 v1, 0x2

    aput-object v0, p2, v1

    iget v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->boundingBoxHeight:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const/4 v1, 0x3

    aput-object v0, p2, v1

    const-string v0, "%%%%BoundingBox: %d %d %d %d\n"

    invoke-static {p1, v0, p2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->boundingBox:Ljava/lang/String;

    return-void
.end method

.method public close()V
    .locals 2

    .line 68
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->closed:Z

    if-eqz v0, :cond_0

    return-void

    .line 70
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    const-string v1, "%%Trailer\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v0, 0x1

    .line 71
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->closed:Z

    return-void
.end method

.method public getFontSize()I
    .locals 1

    .line 187
    iget v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontSize:I

    return v0
.end method

.method public getLineHeight()D
    .locals 2

    .line 185
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontMetrics:Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;

    iget v1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontSize:I

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;->getLineHeight(I)D

    move-result-wide v0

    return-wide v0
.end method

.method public getPS()Ljava/lang/String;
    .locals 2

    .line 56
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->close()V

    .line 57
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->header()Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getWidth(C)D
    .locals 2

    .line 183
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontMetrics:Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;

    iget v1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontSize:I

    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;->getWidth(CI)D

    move-result-wide v0

    return-wide v0
.end method

.method public getWidth(Ljava/lang/String;)D
    .locals 2

    .line 184
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontMetrics:Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;

    iget v1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontSize:I

    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;->getWidth(Ljava/lang/String;I)D

    move-result-wide v0

    return-wide v0
.end method

.method protected header()Ljava/lang/StringBuilder;
    .locals 3

    .line 76
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "%!PS-Adobe-3.0 EPSF-3.0\n"

    .line 77
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 78
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->boundingBox:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "\n"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "0.3 setlinewidth\n"

    .line 79
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "%% x y w h highlight\n/highlight {\n        4 dict begin\n        /h exch def\n        /w exch def\n        /y exch def\n        /x exch def\n        gsave\n        newpath\n        x y moveto\n        0 h rlineto     % up to left corner\n        w 0 rlineto     % to upper right corner\n        0 h neg rlineto % to lower right corner\n        w neg 0 rlineto % back home to lower left corner\n        closepath\n        .95 .83 .82 setrgbcolor\n        fill\n        grestore\n        end\n} def\n"

    .line 80
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-object v0
.end method

.method public highlight(DDDD)V
    .locals 3

    .line 144
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    sget-object v1, Ljava/util/Locale;->US:Ljava/util/Locale;

    const/4 v2, 0x4

    new-array v2, v2, [Ljava/lang/Object;

    invoke-static {p1, p2}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    const/4 p2, 0x0

    aput-object p1, v2, p2

    invoke-static {p3, p4}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    const/4 p2, 0x1

    aput-object p1, v2, p2

    invoke-static {p5, p6}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    const/4 p2, 0x2

    aput-object p1, v2, p2

    invoke-static {p7, p8}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    const/4 p2, 0x3

    aput-object p1, v2, p2

    const-string p1, "%1.3f %1.3f %1.3f %1.3f highlight\n"

    invoke-static {v1, p1, v2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method public line(DDDD)V
    .locals 0

    .line 131
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->move(DD)V

    .line 132
    invoke-virtual {p0, p5, p6, p7, p8}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->lineto(DD)V

    return-void
.end method

.method public lineWidth(D)V
    .locals 1

    .line 118
    iput-wide p1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->lineWidth:D

    .line 119
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1, p2}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " setlinewidth\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method public lineto(DD)V
    .locals 3

    .line 127
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    sget-object v1, Ljava/util/Locale;->US:Ljava/util/Locale;

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    invoke-static {p1, p2}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    const/4 p2, 0x0

    aput-object p1, v2, p2

    invoke-static {p3, p4}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    const/4 p2, 0x1

    aput-object p1, v2, p2

    const-string p1, "%1.3f %1.3f lineto\n"

    invoke-static {v1, p1, v2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method public move(DD)V
    .locals 3

    .line 123
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    sget-object v1, Ljava/util/Locale;->US:Ljava/util/Locale;

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    invoke-static {p1, p2}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    const/4 p2, 0x0

    aput-object p1, v2, p2

    invoke-static {p3, p4}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    const/4 p2, 0x1

    aput-object p1, v2, p2

    const-string p1, "%1.3f %1.3f moveto\n"

    invoke-static {v1, p1, v2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method public rect(DDDD)V
    .locals 13

    add-double v9, p3, p7

    move-object v0, p0

    move-wide v1, p1

    move-wide/from16 v3, p3

    move-wide v5, p1

    move-wide v7, v9

    .line 136
    invoke-virtual/range {v0 .. v8}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->line(DDDD)V

    add-double v11, p1, p5

    move-wide v3, v9

    move-wide v5, v11

    .line 137
    invoke-virtual/range {v0 .. v8}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->line(DDDD)V

    move-wide v1, v11

    move-wide/from16 v7, p3

    .line 138
    invoke-virtual/range {v0 .. v8}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->line(DDDD)V

    move-wide/from16 v3, p3

    move-wide v5, p1

    .line 139
    invoke-virtual/range {v0 .. v8}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->line(DDDD)V

    return-void
.end method

.method public setFont(Ljava/lang/String;I)V
    .locals 4

    .line 105
    new-instance v0, Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;-><init>(Ljava/lang/String;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontMetrics:Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;

    .line 106
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/gui/SystemFontMetrics;->getFont()Ljava/awt/Font;

    move-result-object p1

    invoke-virtual {p1}, Ljava/awt/Font;->getPSName()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontName:Ljava/lang/String;

    .line 107
    iput p2, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontSize:I

    .line 109
    sget-object v0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->POSTSCRIPT_FONT_NAMES:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    if-nez p1, :cond_0

    .line 111
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->fontName:Ljava/lang/String;

    .line 114
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    sget-object v1, Ljava/util/Locale;->US:Ljava/util/Locale;

    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const/4 p1, 0x1

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, v2, p1

    const-string p1, "/%s findfont %d scalefont setfont\n"

    invoke-static {v1, p1, v2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method public stroke()V
    .locals 2

    .line 148
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    const-string v1, "stroke\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method public text(Ljava/lang/String;DD)V
    .locals 7

    .line 160
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 162
    invoke-virtual {p1}, Ljava/lang/String;->toCharArray()[C

    move-result-object p1

    array-length v1, p1

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-char v4, p1, v3

    const/16 v5, 0x28

    const/16 v6, 0x5c

    if-eq v4, v5, :cond_0

    const/16 v5, 0x29

    if-eq v4, v5, :cond_0

    if-eq v4, v6, :cond_0

    .line 171
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 167
    :cond_0
    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 168
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 175
    :cond_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 176
    invoke-virtual {p0, p2, p3, p4, p5}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->move(DD)V

    .line 177
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->ps:Ljava/lang/StringBuilder;

    sget-object p3, Ljava/util/Locale;->US:Ljava/util/Locale;

    const/4 p4, 0x1

    new-array p4, p4, [Ljava/lang/Object;

    aput-object p1, p4, v2

    const-string p1, "(%s) show\n"

    invoke-static {p3, p1, p4}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 178
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->stroke()V

    return-void
.end method
