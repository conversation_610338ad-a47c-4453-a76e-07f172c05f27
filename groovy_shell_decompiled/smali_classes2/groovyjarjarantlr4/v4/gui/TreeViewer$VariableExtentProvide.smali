.class public Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;
.super Ljava/lang/Object;
.source "TreeViewer.java"

# interfaces
.implements Lorg/abego/treelayout/NodeExtentProvider;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/gui/TreeViewer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "VariableExtentProvide"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lorg/abego/treelayout/NodeExtentProvider<",
        "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
        ">;"
    }
.end annotation


# instance fields
.field viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/gui/TreeViewer;)V
    .locals 0

    .line 101
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 102
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;->viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    return-void
.end method


# virtual methods
.method public getHeight(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)D
    .locals 2

    .line 114
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;->viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    iget-object v1, v0, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->font:Ljava/awt/Font;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->getFontMetrics(Ljava/awt/Font;)Ljava/awt/FontMetrics;

    move-result-object v0

    .line 115
    invoke-virtual {v0}, Ljava/awt/FontMetrics;->getHeight()I

    move-result v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;->viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    iget v1, v1, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->nodeHeightPadding:I

    mul-int/lit8 v1, v1, 0x2

    add-int/2addr v0, v1

    .line 116
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;->viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->getText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/String;

    move-result-object p1

    const-string v1, "\n"

    .line 117
    invoke-virtual {p1, v1}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    .line 118
    array-length p1, p1

    mul-int/2addr v0, p1

    int-to-double v0, v0

    return-wide v0
.end method

.method public bridge synthetic getHeight(Ljava/lang/Object;)D
    .locals 2

    .line 99
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;->getHeight(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)D

    move-result-wide v0

    return-wide v0
.end method

.method public getWidth(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)D
    .locals 2

    .line 106
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;->viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    iget-object v1, v0, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->font:Ljava/awt/Font;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->getFontMetrics(Ljava/awt/Font;)Ljava/awt/FontMetrics;

    move-result-object v0

    .line 107
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;->viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->getText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/String;

    move-result-object p1

    .line 108
    invoke-virtual {v0, p1}, Ljava/awt/FontMetrics;->stringWidth(Ljava/lang/String;)I

    move-result p1

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;->viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    iget v0, v0, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->nodeWidthPadding:I

    mul-int/lit8 v0, v0, 0x2

    add-int/2addr p1, v0

    int-to-double v0, p1

    return-wide v0
.end method

.method public bridge synthetic getWidth(Ljava/lang/Object;)D
    .locals 2

    .line 99
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer$VariableExtentProvide;->getWidth(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)D

    move-result-wide v0

    return-wide v0
.end method
