.class final Lgroovyjarjarantlr4/v4/gui/TreeViewer$1;
.super Ljava/lang/Object;
.source "TreeViewer.java"

# interfaces
.implements Ljava/awt/event/ActionListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/gui/TreeViewer;->showInDialog(Lgroovyjarjarantlr4/v4/gui/TreeViewer;)Ljavax/swing/JDialog;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic val$dialog:Ljavax/swing/JDialog;


# direct methods
.method constructor <init>(Ljavax/swing/JDialog;)V
    .locals 0

    .line 385
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$1;->val$dialog:Ljavax/swing/JDialog;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public actionPerformed(Ljava/awt/event/ActionEvent;)V
    .locals 3

    .line 388
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$1;->val$dialog:Ljavax/swing/JDialog;

    new-instance v0, Ljava/awt/event/WindowEvent;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$1;->val$dialog:Ljavax/swing/JDialog;

    const/16 v2, 0xc9

    invoke-direct {v0, v1, v2}, Ljava/awt/event/WindowEvent;-><init>(Ljava/awt/Window;I)V

    invoke-virtual {p1, v0}, Ljavax/swing/JDialog;->dispatchEvent(Ljava/awt/AWTEvent;)V

    return-void
.end method
