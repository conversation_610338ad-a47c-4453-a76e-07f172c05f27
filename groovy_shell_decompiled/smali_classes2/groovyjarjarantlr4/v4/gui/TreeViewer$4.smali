.class final Lgroovyjarjarantlr4/v4/gui/TreeViewer$4;
.super Ljava/lang/Object;
.source "TreeViewer.java"

# interfaces
.implements Ljavax/swing/event/ChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/gui/TreeViewer;->showInDialog(Lgroovyjarjarantlr4/v4/gui/TreeViewer;)Ljavax/swing/JDialog;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic val$scaleSlider:Ljavax/swing/JSlider;

.field final synthetic val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;


# direct methods
.method constructor <init>(Ljavax/swing/JSlider;Lgroovyjarjarantlr4/v4/gui/TreeViewer;)V
    .locals 0

    .line 428
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$4;->val$scaleSlider:Ljavax/swing/JSlider;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$4;->val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public stateChanged(Ljavax/swing/event/ChangeEvent;)V
    .locals 5

    .line 431
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$4;->val$scaleSlider:Ljavax/swing/JSlider;

    invoke-virtual {p1}, Ljavax/swing/JSlider;->getValue()I

    move-result p1

    .line 432
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$4;->val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    int-to-double v1, p1

    const-wide v3, 0x408f400000000000L    # 1000.0

    div-double/2addr v1, v3

    const-wide/high16 v3, 0x3ff0000000000000L    # 1.0

    add-double/2addr v1, v3

    invoke-virtual {v0, v1, v2}, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->setScale(D)V

    return-void
.end method
