.class public Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;
.super Ljava/lang/Object;
.source "TreePostScriptGenerator.java"

# interfaces
.implements Lorg/abego/treelayout/NodeExtentProvider;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "VariableExtentProvide"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lorg/abego/treelayout/NodeExtentProvider<",
        "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;)V
    .locals 0

    .line 24
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->this$0:Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getHeight(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)D
    .locals 4

    .line 33
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->this$0:Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;->getText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/String;

    move-result-object p1

    .line 34
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->this$0:Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;->doc:Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->getLineHeight()D

    move-result-wide v0

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->this$0:Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;

    iget v2, v2, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;->nodeHeightPaddingAbove:I

    int-to-double v2, v2

    add-double/2addr v0, v2

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->this$0:Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;

    iget v2, v2, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;->nodeHeightPaddingBelow:I

    int-to-double v2, v2

    add-double/2addr v0, v2

    const-string v2, "\n"

    .line 36
    invoke-virtual {p1, v2}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    .line 37
    array-length p1, p1

    int-to-double v2, p1

    mul-double/2addr v0, v2

    return-wide v0
.end method

.method public bridge synthetic getHeight(Ljava/lang/Object;)D
    .locals 2

    .line 24
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->getHeight(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)D

    move-result-wide v0

    return-wide v0
.end method

.method public getWidth(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)D
    .locals 4

    .line 27
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->this$0:Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;->getText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/String;

    move-result-object p1

    .line 28
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->this$0:Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;->doc:Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/gui/PostScriptDocument;->getWidth(Ljava/lang/String;)D

    move-result-wide v0

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->this$0:Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;

    iget p1, p1, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator;->nodeWidthPadding:I

    mul-int/lit8 p1, p1, 0x2

    int-to-double v2, p1

    add-double/2addr v0, v2

    return-wide v0
.end method

.method public bridge synthetic getWidth(Ljava/lang/Object;)D
    .locals 2

    .line 24
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/gui/TreePostScriptGenerator$VariableExtentProvide;->getWidth(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)D

    move-result-wide v0

    return-wide v0
.end method
