.class Lgroovyjarjarantlr4/v4/gui/TreeViewer$TreeNodeWrapper;
.super Ljavax/swing/tree/DefaultMutableTreeNode;
.source "TreeViewer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/gui/TreeViewer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "TreeNodeWrapper"
.end annotation


# instance fields
.field final viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/gui/TreeViewer;)V
    .locals 0

    .line 865
    invoke-direct {p0, p1}, Ljavax/swing/tree/DefaultMutableTreeNode;-><init>(Ljava/lang/Object;)V

    .line 866
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$TreeNodeWrapper;->viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    return-void
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 2

    .line 871
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$TreeNodeWrapper;->viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/TreeViewer$TreeNodeWrapper;->getUserObject()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->getText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
