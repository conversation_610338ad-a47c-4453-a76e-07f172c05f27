.class final Lgroovyjarjarantlr4/v4/gui/TreeViewer$5;
.super Ljava/lang/Object;
.source "TreeViewer.java"

# interfaces
.implements Ljavax/swing/event/TreeSelectionListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/gui/TreeViewer;->showInDialog(Lgroovyjarjarantlr4/v4/gui/TreeViewer;)Ljavax/swing/JDialog;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/gui/TreeViewer;)V
    .locals 0

    .line 454
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$5;->val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public valueChanged(Ljavax/swing/event/TreeSelectionEvent;)V
    .locals 1

    .line 458
    invoke-virtual {p1}, Ljavax/swing/event/TreeSelectionEvent;->getSource()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljavax/swing/JTree;

    .line 459
    invoke-virtual {p1}, Ljavax/swing/JTree;->getSelectionPath()Ljavax/swing/tree/TreePath;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 461
    invoke-virtual {p1}, Ljavax/swing/tree/TreePath;->getLastPathComponent()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/gui/TreeViewer$TreeNodeWrapper;

    .line 464
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$5;->val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer$TreeNodeWrapper;->getUserObject()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->setTree(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)V

    :cond_0
    return-void
.end method
