.class Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;
.super Ljava/lang/Object;
.source "TreeLayoutAdaptor.java"

# interfaces
.implements Ljava/lang/Iterable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "AntlrTreeChildrenReverseIterable"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/lang/Iterable<",
        "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
        ">;"
    }
.end annotation


# instance fields
.field private final tree:Lgroovyjarjarantlr4/v4/runtime/tree/Tree;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)V
    .locals 0

    .line 55
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 56
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;->tree:Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    return-void
.end method

.method static synthetic access$100(Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 0

    .line 50
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;->tree:Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    return-object p0
.end method


# virtual methods
.method public iterator()Ljava/util/Iterator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ">;"
        }
    .end annotation

    .line 61
    new-instance v0, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable$1;-><init>(Lgroovyjarjarantlr4/v4/gui/TreeLayoutAdaptor$AntlrTreeChildrenReverseIterable;)V

    return-object v0
.end method
