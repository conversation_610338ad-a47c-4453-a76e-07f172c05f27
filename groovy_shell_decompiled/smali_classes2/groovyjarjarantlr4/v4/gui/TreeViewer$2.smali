.class final Lgroovyjarjarantlr4/v4/gui/TreeViewer$2;
.super Ljava/lang/Object;
.source "TreeViewer.java"

# interfaces
.implements Ljava/awt/event/ActionListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/gui/TreeViewer;->showInDialog(Lgroovyjarjarantlr4/v4/gui/TreeViewer;)Ljavax/swing/JDialog;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic val$dialog:Ljavax/swing/JDialog;

.field final synthetic val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/gui/TreeViewer;Ljavax/swing/JDialog;)V
    .locals 0

    .line 397
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$2;->val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$2;->val$dialog:Ljavax/swing/JDialog;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public actionPerformed(Ljava/awt/event/ActionEvent;)V
    .locals 1

    .line 400
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$2;->val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$2;->val$dialog:Ljavax/swing/JDialog;

    invoke-static {p1, v0}, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->access$000(Lgroovyjarjarantlr4/v4/gui/TreeViewer;Ljavax/swing/JDialog;)V

    return-void
.end method
