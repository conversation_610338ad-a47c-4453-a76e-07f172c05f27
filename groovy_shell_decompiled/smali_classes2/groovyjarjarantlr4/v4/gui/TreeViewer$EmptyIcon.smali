.class Lgroovyjarjarantlr4/v4/gui/TreeViewer$EmptyIcon;
.super Ljava/lang/Object;
.source "TreeViewer.java"

# interfaces
.implements Ljavax/swing/Icon;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/gui/TreeViewer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "EmptyIcon"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 875
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Lgroovyjarjarantlr4/v4/gui/TreeViewer$1;)V
    .locals 0

    .line 875
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/gui/TreeViewer$EmptyIcon;-><init>()V

    return-void
.end method


# virtual methods
.method public getIconHeight()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public getIconWidth()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public paintIcon(Ljava/awt/Component;Ljava/awt/Graphics;II)V
    .locals 0

    return-void
.end method
