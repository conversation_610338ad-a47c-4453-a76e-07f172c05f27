.class Lgroovyjarjarantlr4/v4/gui/TreeViewer$8$1;
.super Ljava/lang/Object;
.source "TreeViewer.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;->call()Ljavax/swing/JDialog;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$1:Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;)V
    .locals 0

    .line 673
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8$1;->this$1:Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    .line 676
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8$1;->this$1:Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;

    iget-object v1, v0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;->val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    invoke-static {v1}, Lgroovyjarjarantlr4/v4/gui/TreeViewer;->showInDialog(Lgroovyjarjarantlr4/v4/gui/TreeViewer;)Ljavax/swing/JDialog;

    move-result-object v1

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;->result:Ljavax/swing/JDialog;

    return-void
.end method
