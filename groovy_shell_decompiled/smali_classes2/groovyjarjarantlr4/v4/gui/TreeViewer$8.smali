.class Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;
.super Ljava/lang/Object;
.source "TreeViewer.java"

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/gui/TreeViewer;->open()Ljava/util/concurrent/Future;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Ljavax/swing/JDialog;",
        ">;"
    }
.end annotation


# instance fields
.field result:Ljavax/swing/JDialog;

.field final synthetic this$0:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

.field final synthetic val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/gui/TreeViewer;Lgroovyjarjarantlr4/v4/gui/TreeViewer;)V
    .locals 0

    .line 668
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;->this$0:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;->val$viewer:Lgroovyjarjarantlr4/v4/gui/TreeViewer;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 668
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;->call()Ljavax/swing/JDialog;

    move-result-object v0

    return-object v0
.end method

.method public call()Ljavax/swing/JDialog;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 673
    new-instance v0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8$1;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8$1;-><init>(Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;)V

    invoke-static {v0}, Ljavax/swing/SwingUtilities;->invokeAndWait(Ljava/lang/Runnable;)V

    .line 680
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/gui/TreeViewer$8;->result:Ljavax/swing/JDialog;

    return-object v0
.end method
