.class public final enum Lgroovyjarjarantlr4/v4/Tool$OptionArgType;
.super Ljava/lang/Enum;
.source "Tool.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/Tool;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "OptionArgType"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovyjarjarantlr4/v4/Tool$OptionArgType;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

.field public static final enum NONE:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

.field public static final enum STRING:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 80
    new-instance v0, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    const-string v1, "NONE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->NONE:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    new-instance v1, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    const-string v3, "STRING"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->STRING:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    const/4 v3, 0x2

    new-array v3, v3, [Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    aput-object v0, v3, v2

    aput-object v1, v3, v4

    sput-object v3, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->$VALUES:[Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 80
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/Tool$OptionArgType;
    .locals 1

    .line 80
    const-class v0, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    return-object p0
.end method

.method public static values()[Lgroovyjarjarantlr4/v4/Tool$OptionArgType;
    .locals 1

    .line 80
    sget-object v0, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->$VALUES:[Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    invoke-virtual {v0}, [Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    return-object v0
.end method
