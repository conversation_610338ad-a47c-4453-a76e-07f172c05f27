.class public Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;
.super Ljava/lang/Object;
.source "LeftRecursiveRuleAltInfo.java"


# instance fields
.field public altAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

.field public altLabel:Ljava/lang/String;

.field public altNum:I

.field public altText:Ljava/lang/String;

.field public final isListLabel:Z

.field public leftRecursiveRuleRefLabel:Ljava/lang/String;

.field public nextPrec:I

.field public originalAltAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;


# direct methods
.method public constructor <init>(ILjava/lang/String;)V
    .locals 7

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    move-object v0, p0

    move v1, p1

    move-object v2, p2

    .line 22
    invoke-direct/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/tool/ast/AltAST;)V

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/tool/ast/AltAST;)V
    .locals 0

    .line 30
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 31
    iput p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altNum:I

    .line 32
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altText:Ljava/lang/String;

    .line 33
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->leftRecursiveRuleRefLabel:Ljava/lang/String;

    .line 34
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    .line 35
    iput-boolean p5, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->isListLabel:Z

    .line 36
    iput-object p6, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->originalAltAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    return-void
.end method
