.class public Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;
.super Ljava/lang/Object;
.source "LeftRecursiveRuleTransformer.java"


# static fields
.field public static final PRECEDENCE_OPTION_NAME:Ljava/lang/String; = "p"

.field public static final TOKENINDEX_OPTION_NAME:Ljava/lang/String; = "tokenIndex"


# instance fields
.field public ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

.field public g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public rules:Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;"
        }
    .end annotation
.end field

.field public tool:Lgroovyjarjarantlr4/v4/Tool;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ")V"
        }
    .end annotation

    .line 57
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 58
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    .line 59
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->rules:Ljava/util/Collection;

    .line 60
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 61
    iget-object p1, p3, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    return-void
.end method


# virtual methods
.method public parseArtificialRule(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;
    .locals 7

    .line 194
    new-instance v0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;

    new-instance v1, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;

    invoke-direct {v1, p2}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;-><init>(Ljava/lang/String;)V

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    .line 195
    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->getCharStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v2

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    .line 196
    new-instance v2, Lgroovyjarjarantlr4/runtime/CommonTokenStream;

    invoke-direct {v2, v0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;-><init>(Lgroovyjarjarantlr4/runtime/TokenSource;)V

    .line 197
    iput-object v2, v0, Lgroovyjarjarantlr4/v4/parse/ANTLRLexer;->tokens:Lgroovyjarjarantlr4/runtime/CommonTokenStream;

    .line 198
    new-instance v0, Lgroovyjarjarantlr4/v4/parse/ToolANTLRParser;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-direct {v0, v2, v3}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRParser;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/v4/Tool;)V

    .line 199
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRParser;->setTreeAdaptor(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;)V

    const/4 v1, 0x0

    .line 202
    :try_start_0
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRParser;->rule()Lgroovyjarjarantlr4/v4/parse/ANTLRParser$rule_return;

    move-result-object v0

    .line 203
    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/ParserRuleReturnScope;->getTree()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 204
    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/ParserRuleReturnScope;->getStart()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_1

    .line 205
    :try_start_1
    invoke-static {p1, v2}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->setGrammarPtr(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 206
    invoke-static {p1, v2}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->augmentTokensWithOriginalPosition(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    return-object v2

    :catch_0
    move-exception p1

    goto :goto_0

    :catch_1
    move-exception p1

    move-object v0, v1

    .line 210
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/4 v4, 0x2

    new-array v4, v4, [Ljava/lang/Object;

    const/4 v5, 0x0

    aput-object v0, v4, v5

    const/4 v0, 0x1

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "error parsing rule created during left-recursion detection: "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    aput-object p2, v4, v0

    invoke-virtual {v2, v3, p1, v4}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    return-object v1
.end method

.method public setAltASTPointers(Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)V
    .locals 5

    const/16 v0, 0x4e

    .line 236
    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    const/4 v0, 0x0

    .line 237
    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 238
    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    const/4 v2, 0x1

    .line 239
    invoke-virtual {p2, v2}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p2

    invoke-interface {p2, v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    move v2, v0

    .line 240
    :goto_0
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v2, v3, :cond_0

    .line 241
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 242
    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v4, v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 243
    iget-object v4, v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v3, v4, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 244
    iget-object v4, v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->originalAltAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v3, v4, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 248
    :cond_0
    :goto_1
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->size()I

    move-result v1

    if-ge v0, v1, :cond_1

    .line 249
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->getElement(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 250
    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v2, v1, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 251
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v1, v2, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 252
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->originalAltAST:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v1, v2, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_1
    return-void
.end method

.method public translateLeftRecursiveRule(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;Ljava/lang/String;)Z
    .locals 8

    .line 103
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/4 v1, 0x0

    .line 104
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v2

    .line 105
    new-instance v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-direct {v3, v0, v4, v2, p3}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/Tool;Ljava/lang/String;Ljava/lang/String;)V

    .line 111
    :try_start_0
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->rec_rule()Z

    move-result p3
    :try_end_0
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move p3, v1

    :goto_0
    if-nez p3, :cond_0

    return v1

    .line 119
    :cond_0
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->getBaseContext()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->setBaseContext(Ljava/lang/String;)V

    const/16 p3, 0x61

    .line 122
    invoke-virtual {p1, p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 123
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->getArtificialOpPrecRule()Ljava/lang/String;

    move-result-object p3

    .line 129
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p0, v2, p3}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->parseArtificialRule(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    move-result-object p3

    .line 132
    invoke-virtual {p3, v1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v4

    iput-object v4, v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 135
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildIndex()I

    move-result v0

    invoke-virtual {p1, v0, p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->setChild(ILgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 136
    iput-object p3, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 139
    new-instance p1, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-direct {p1, v0, v2}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/Tool;)V

    .line 140
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->reduceBlocksToSets(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 141
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->expandParameterizedLoops(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 144
    new-instance p1, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {p1, v0}, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    const-string v0, "rule"

    .line 145
    invoke-virtual {p1, p3, v0}, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;->visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;)V

    .line 146
    new-instance v2, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks;

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v2, v4, p1}, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/semantics/RuleCollector;)V

    .line 149
    iput-boolean v1, v2, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks;->checkAssocElementOption:Z

    .line 150
    invoke-virtual {v2, p3, v0}, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks;->visit(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;)V

    .line 153
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    .line 154
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    iget-object v0, v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->prefixAndOtherAlts:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 155
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result p1

    const/4 v0, 0x1

    if-eqz p1, :cond_1

    .line 156
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NO_NON_LR_ALTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v5, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v5, v1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v5

    new-array v6, v0, [Ljava/lang/Object;

    iget-object v7, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->name:Ljava/lang/String;

    aput-object v7, v6, v1

    invoke-virtual {p1, v2, v4, v5, v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 159
    :cond_1
    new-instance p1, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-direct {p1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;-><init>()V

    iput-object p1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    .line 160
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    iget-object v1, v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->binaryAlts:Ljava/util/LinkedHashMap;

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->putAll(Ljava/util/Map;)V

    .line 161
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    iget-object v1, v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ternaryAlts:Ljava/util/LinkedHashMap;

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->putAll(Ljava/util/Map;)V

    .line 162
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    iget-object v1, v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->suffixAlts:Ljava/util/LinkedHashMap;

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->putAll(Ljava/util/Map;)V

    .line 166
    invoke-virtual {p0, p2, p3}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->setAltASTPointers(Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)V

    .line 169
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/16 v1, 0x8

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    if-eqz p1, :cond_2

    .line 171
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getText()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-static {p1, v1, v2}, Lgroovyjarjarantlr4/v4/parse/ScopeParser;->parseTypedArgList(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    move-result-object v1

    iput-object v1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    .line 172
    iget-object v1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->ARG:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    iput-object v2, v1, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->type:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    .line 173
    iget-object v1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iput-object p1, v1, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 174
    iget-object v1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v1, v1, v0

    iput-object v1, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    .line 179
    :cond_2
    iget-object p1, v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->leftRecursiveRuleRefLabels:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    .line 180
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 181
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 182
    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 183
    new-instance v5, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v2

    invoke-direct {v5, v6, v1, v4, v2}, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;I)V

    .line 184
    iget-object v2, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v2, v2, v0

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Alternative;->labelDefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_1

    .line 187
    :cond_3
    iget-object p1, v3, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->leftRecursiveRuleRefLabels:Ljava/util/List;

    iput-object p1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->leftRecursiveRuleRefLabels:Ljava/util/List;

    .line 189
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "added: "

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->toStringTree()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string p3, "grammar"

    invoke-virtual {p1, p3, p2}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method

.method public translateLeftRecursiveRules()V
    .locals 7

    .line 65
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    const-string v1, "language"

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 67
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 68
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->rules:Ljava/util/Collection;

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_0
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 69
    iget-object v4, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-static {v4}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isTokenName(Ljava/lang/String;)Z

    move-result v4

    if-nez v4, :cond_0

    .line 70
    iget-object v4, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    iget-object v5, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-static {v4, v5}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->hasImmediateRecursiveRuleRefs(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_0

    .line 71
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-object v5, v3

    check-cast v5, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;

    invoke-virtual {p0, v4, v5, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->translateLeftRecursiveRule(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_0

    .line 73
    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 87
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/16 v2, 0x39

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_2
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 88
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v3

    const/16 v4, 0x5e

    if-ne v3, v4, :cond_3

    goto :goto_1

    .line 89
    :cond_3
    move-object v3, v2

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    const-string v4, "p"

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    if-eqz v5, :cond_4

    goto :goto_1

    .line 90
    :cond_4
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 92
    new-instance v2, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    const/16 v5, 0x1e

    const-string v6, "0"

    invoke-virtual {v2, v5, v6}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    invoke-virtual {v3, v4, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->setOption(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    goto :goto_1

    :cond_5
    return-void
.end method
