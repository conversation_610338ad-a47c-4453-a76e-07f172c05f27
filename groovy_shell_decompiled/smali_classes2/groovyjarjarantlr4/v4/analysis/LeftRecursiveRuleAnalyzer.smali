.class public Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;
.super Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;
.source "LeftRecursiveRuleAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field public altAssociativity:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;",
            ">;"
        }
    .end annotation
.end field

.field public binaryAlts:Ljava/util/LinkedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;",
            ">;"
        }
    .end annotation
.end field

.field public codegenTemplates:Lorg/stringtemplate/v4/STGroup;

.field public language:Ljava/lang/String;

.field public leftRecursiveRuleRefLabels:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end field

.field public prefixAndOtherAlts:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;",
            ">;"
        }
    .end annotation
.end field

.field public recRuleTemplates:Lorg/stringtemplate/v4/STGroup;

.field public retvals:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

.field public ruleName:Ljava/lang/String;

.field public suffixAlts:Ljava/util/LinkedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;",
            ">;"
        }
    .end annotation
.end field

.field public ternaryAlts:Ljava/util/LinkedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/Integer;",
            "Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;",
            ">;"
        }
    .end annotation
.end field

.field public final tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

.field public tool:Lgroovyjarjarantlr4/v4/Tool;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/Tool;Ljava/lang/String;Ljava/lang/String;)V
    .locals 3

    .line 71
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    iget-object v2, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v2

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    invoke-direct {v0, v1, p1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/Object;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/parse/LeftRecursiveRuleWalker;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V

    .line 46
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->binaryAlts:Ljava/util/LinkedHashMap;

    .line 47
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ternaryAlts:Ljava/util/LinkedHashMap;

    .line 48
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->suffixAlts:Ljava/util/LinkedHashMap;

    .line 49
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->prefixAndOtherAlts:Ljava/util/List;

    .line 52
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->leftRecursiveRuleRefLabels:Ljava/util/List;

    .line 66
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->altAssociativity:Ljava/util/Map;

    .line 72
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    .line 73
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ruleName:Ljava/lang/String;

    .line 74
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->language:Ljava/lang/String;

    .line 75
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    const-string p2, "grammar must have a token stream"

    .line 77
    invoke-static {p1, p2}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 80
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->loadPrecRuleTemplates()V

    return-void
.end method

.method public static hasImmediateRecursiveRuleRefs(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;)Z
    .locals 8

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return v0

    :cond_0
    const/16 v1, 0x4e

    .line 274
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-nez p0, :cond_1

    return v0

    .line 276
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    move v2, v0

    :goto_0
    if-ge v2, v1, :cond_6

    .line 278
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object v3

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 279
    invoke-virtual {v3, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    if-nez v4, :cond_2

    goto :goto_1

    .line 281
    :cond_2
    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v5

    const/16 v6, 0x52

    const/4 v7, 0x1

    if-ne v5, v6, :cond_3

    .line 282
    invoke-virtual {v3, v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    if-nez v4, :cond_3

    goto :goto_1

    .line 287
    :cond_3
    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v3

    const/16 v5, 0x39

    if-ne v3, v5, :cond_4

    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v3, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    return v7

    .line 288
    :cond_4
    invoke-interface {v4, v7}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    if-eqz v3, :cond_5

    .line 289
    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v4

    if-ne v4, v5, :cond_5

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v3, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_5

    return v7

    :cond_5
    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_6
    return v0
.end method


# virtual methods
.method public addPrecedenceArgToRules(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;I)Lgroovyjarjarantlr4/v4/tool/ast/AltAST;
    .locals 8

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    const/16 v0, 0x39

    .line 255
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->of(I)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getNodesWithTypePreorderDFS(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/List;

    move-result-object v0

    .line 256
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 257
    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;

    .line 258
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->getText()Ljava/lang/String;

    move-result-object v3

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ruleName:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    .line 259
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v4

    const/4 v5, 0x1

    sub-int/2addr v4, v5

    invoke-interface {v0, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    if-ne v2, v4, :cond_2

    goto :goto_1

    :cond_2
    const/4 v5, 0x0

    :goto_1
    if-eqz v3, :cond_1

    if-eqz v5, :cond_1

    .line 261
    new-instance v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    new-instance v4, Lgroovyjarjarantlr4/runtime/CommonToken;

    const/16 v5, 0x1e

    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, ""

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v4, v5, v6}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    invoke-direct {v3, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    const-string v4, "p"

    .line 262
    invoke-virtual {v2, v4, v3}, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->setOption(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    goto :goto_0

    :cond_3
    return-object p1
.end method

.method public binaryAlt(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;I)V
    .locals 10

    .line 135
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 136
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    move-object v7, v1

    goto :goto_0

    :cond_0
    move-object v7, v2

    .line 140
    :goto_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->stripLeftRecursion(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v1

    const/4 v3, 0x0

    if-eqz v1, :cond_2

    .line 142
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    .line 143
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v4

    const/16 v5, 0x2e

    if-ne v4, v5, :cond_1

    const/4 v3, 0x1

    .line 144
    :cond_1
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->leftRecursiveRuleRefLabels:Ljava/util/List;

    invoke-static {v1, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v1

    invoke-interface {v4, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_2
    move-object v6, v2

    move v8, v3

    .line 147
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->stripAltLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 150
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->nextPrecedence(I)I

    move-result v1

    .line 151
    invoke-virtual {p0, v0, v1}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->addPrecedenceArgToRules(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;I)Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    move-result-object v0

    .line 153
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->stripAltLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 154
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->text(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;

    move-result-object v0

    .line 155
    invoke-virtual {v0}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v5

    .line 156
    new-instance v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    move-object v3, v0

    move v4, p2

    move-object v9, p1

    invoke-direct/range {v3 .. v9}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/tool/ast/AltAST;)V

    .line 158
    iput v1, v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->nextPrec:I

    .line 159
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->binaryAlts:Ljava/util/LinkedHashMap;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    invoke-virtual {p1, p2, v0}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public getArtificialOpPrecRule()Ljava/lang/String;
    .locals 10

    .line 220
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->recRuleTemplates:Lorg/stringtemplate/v4/STGroup;

    const-string v1, "recRule"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    .line 221
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ruleName:Ljava/lang/String;

    const-string v2, "ruleName"

    invoke-virtual {v0, v2, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 222
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->codegenTemplates:Lorg/stringtemplate/v4/STGroup;

    const-string v3, "recRuleArg"

    invoke-virtual {v1, v3}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    const-string v3, "argName"

    .line 223
    invoke-virtual {v0, v3, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 224
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->codegenTemplates:Lorg/stringtemplate/v4/STGroup;

    const-string v3, "recRuleSetResultAction"

    invoke-virtual {v1, v3}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    const-string v3, "setResultAction"

    .line 225
    invoke-virtual {v0, v3, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 226
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->retvals:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const-string v3, "userRetvals"

    invoke-virtual {v0, v3, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 228
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 229
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->binaryAlts:Ljava/util/LinkedHashMap;

    invoke-virtual {v1, v3}, Ljava/util/LinkedHashMap;->putAll(Ljava/util/Map;)V

    .line 230
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ternaryAlts:Ljava/util/LinkedHashMap;

    invoke-virtual {v1, v3}, Ljava/util/LinkedHashMap;->putAll(Ljava/util/Map;)V

    .line 231
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->suffixAlts:Ljava/util/LinkedHashMap;

    invoke-virtual {v1, v3}, Ljava/util/LinkedHashMap;->putAll(Ljava/util/Map;)V

    .line 232
    invoke-virtual {v1}, Ljava/util/LinkedHashMap;->keySet()Ljava/util/Set;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_0

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    .line 233
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 234
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->recRuleTemplates:Lorg/stringtemplate/v4/STGroup;

    const-string v7, "recRuleAlt"

    invoke-virtual {v6, v7}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v6

    .line 235
    iget-object v7, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->codegenTemplates:Lorg/stringtemplate/v4/STGroup;

    const-string v8, "recRuleAltPredicate"

    invoke-virtual {v7, v8}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v7

    .line 236
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->precedence(I)I

    move-result v8

    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v8

    const-string v9, "opPrec"

    invoke-virtual {v7, v9, v8}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 237
    iget-object v8, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ruleName:Ljava/lang/String;

    invoke-virtual {v7, v2, v8}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    const-string v8, "pred"

    .line 238
    invoke-virtual {v6, v8, v7}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    const-string v7, "alt"

    .line 239
    invoke-virtual {v6, v7, v5}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    const-string v5, "precOption"

    const-string v7, "p"

    .line 240
    invoke-virtual {v6, v5, v7}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 241
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->precedence(I)I

    move-result v4

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-virtual {v6, v9, v4}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    const-string v4, "opAlts"

    .line 242
    invoke-virtual {v0, v4, v6}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_0

    .line 245
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->prefixAndOtherAlts:Ljava/util/List;

    const-string v2, "primaryAlts"

    invoke-virtual {v0, v2, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 247
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v2

    const-string v3, "left-recursion"

    invoke-virtual {v1, v3, v2}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 249
    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public loadPrecRuleTemplates()V
    .locals 5

    .line 85
    new-instance v0, Lorg/stringtemplate/v4/STGroupFile;

    const-string v1, "groovyjarjarantlr4/v4/tool/templates/LeftRecursiveRules.stg"

    invoke-direct {v0, v1}, Lorg/stringtemplate/v4/STGroupFile;-><init>(Ljava/lang/String;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->recRuleTemplates:Lorg/stringtemplate/v4/STGroup;

    const-string v1, "recRule"

    .line 86
    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->isDefined(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 87
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MISSING_CODE_GEN_TEMPLATES:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    const-string v4, "LeftRecursiveRules"

    aput-object v4, v2, v3

    invoke-virtual {v0, v1, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    .line 91
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->language:Ljava/lang/String;

    const/4 v3, 0x0

    invoke-direct {v0, v1, v3, v2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)V

    .line 92
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    if-nez v0, :cond_1

    .line 95
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    const-string v2, "Java"

    invoke-direct {v0, v1, v3, v2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)V

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    .line 99
    :cond_1
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->codegenTemplates:Lorg/stringtemplate/v4/STGroup;

    return-void
.end method

.method public nextPrecedence(I)I
    .locals 2

    .line 418
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->precedence(I)I

    move-result v0

    .line 419
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->altAssociativity:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v1, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    sget-object v1, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;->right:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;

    if-ne p1, v1, :cond_0

    return v0

    :cond_0
    add-int/lit8 v0, v0, 0x1

    return v0
.end method

.method public otherAlt(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;I)V
    .locals 8

    .line 205
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 206
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->stripAltLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 207
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->text(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;

    move-result-object v3

    .line 208
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v1, :cond_0

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    move-object v5, v0

    .line 209
    new-instance v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    const/4 v4, 0x0

    const/4 v6, 0x0

    move-object v1, v0

    move v2, p2

    move-object v7, p1

    invoke-direct/range {v1 .. v7}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/tool/ast/AltAST;)V

    .line 213
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->prefixAndOtherAlts:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public precedence(I)I
    .locals 1

    .line 413
    iget v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->numAlts:I

    sub-int/2addr v0, p1

    add-int/lit8 v0, v0, 0x1

    return v0
.end method

.method public prefixAlt(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;I)V
    .locals 10

    .line 165
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 166
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->stripAltLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 168
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->precedence(I)I

    move-result v1

    .line 170
    invoke-virtual {p0, v0, v1}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->addPrecedenceArgToRules(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;I)Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    move-result-object v0

    .line 171
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->text(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;

    move-result-object v2

    .line 172
    invoke-virtual {v2}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v5

    .line 173
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v2, :cond_0

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    move-object v7, v0

    .line 174
    new-instance v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    const/4 v6, 0x0

    const/4 v8, 0x0

    move-object v3, v0

    move v4, p2

    move-object v9, p1

    invoke-direct/range {v3 .. v9}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/tool/ast/AltAST;)V

    .line 176
    iput v1, v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->nextPrec:I

    .line 177
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->prefixAndOtherAlts:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public setAltAssoc(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;I)V
    .locals 8

    .line 109
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;->left:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;

    .line 110
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getOptions()Ljava/util/Map;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-eqz v1, :cond_2

    const-string v1, "assoc"

    .line 111
    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_2

    .line 113
    sget-object v5, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;->right:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_0

    .line 114
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;->right:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;

    goto :goto_0

    .line 116
    :cond_0
    sget-object v5, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;->left:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_1

    .line 117
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;->left:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer$ASSOC;

    goto :goto_0

    .line 120
    :cond_1
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v5, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ILLEGAL_OPTION_VALUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v6, p1, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getOptionAST(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p1

    const/4 v7, 0x2

    new-array v7, v7, [Ljava/lang/Object;

    aput-object v1, v7, v2

    aput-object v0, v7, v3

    invoke-virtual {v4, v5, v6, p1, v7}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 125
    :cond_2
    :goto_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->altAssociativity:Ljava/util/Map;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-eqz p1, :cond_3

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->altAssociativity:Ljava/util/Map;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-eq p1, v0, :cond_3

    .line 126
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v3, v3, [Ljava/lang/Object;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "all operators of alt "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v5, " of left-recursive rule must have same associativity"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    aput-object v4, v3, v2

    invoke-virtual {p1, v1, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    .line 128
    :cond_3
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->altAssociativity:Ljava/util/Map;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    invoke-interface {p1, p2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setReturnValues(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 0

    .line 104
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->retvals:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    return-void
.end method

.method public stripAltLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 4

    .line 322
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStartIndex()I

    move-result v0

    .line 323
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStopIndex()I

    move-result v1

    :goto_0
    if-lt v1, v0, :cond_1

    .line 326
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    invoke-interface {v2, v1}, Lgroovyjarjarantlr4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v2

    const/16 v3, 0x2f

    if-ne v2, v3, :cond_0

    add-int/lit8 v1, v1, -0x1

    .line 327
    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->setTokenStopIndex(I)V

    return-void

    :cond_0
    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public stripLeftRecursion(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 8

    const/4 v0, 0x0

    .line 299
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 301
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v2

    const/4 v3, 0x1

    const/16 v4, 0x52

    if-ne v2, v4, :cond_0

    .line 302
    invoke-virtual {p1, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move v2, v3

    goto :goto_0

    :cond_0
    move v2, v0

    .line 305
    :goto_0
    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    .line 306
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v4

    const/16 v5, 0x39

    const/4 v6, 0x0

    if-ne v4, v5, :cond_1

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v4

    iget-object v7, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ruleName:Ljava/lang/String;

    invoke-virtual {v4, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_2

    :cond_1
    if-eqz v3, :cond_5

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v4

    if-ne v4, v5, :cond_5

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v3

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ruleName:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_5

    .line 309
    :cond_2
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v3

    const/16 v4, 0xa

    if-eq v3, v4, :cond_3

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v3

    const/16 v4, 0x2e

    if-ne v3, v4, :cond_4

    :cond_3
    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-object v6, v0

    .line 311
    :cond_4
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->deleteChild(I)Ljava/lang/Object;

    .line 314
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 315
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStartIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->setTokenStartIndex(I)V

    :cond_5
    return-object v6
.end method

.method public suffixAlt(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;I)V
    .locals 10

    .line 183
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 184
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    move-object v7, v1

    goto :goto_0

    :cond_0
    move-object v7, v2

    .line 188
    :goto_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->stripLeftRecursion(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v1

    const/4 v3, 0x0

    if-eqz v1, :cond_2

    .line 190
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    .line 191
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v4

    const/16 v5, 0x2e

    if-ne v4, v5, :cond_1

    const/4 v3, 0x1

    .line 192
    :cond_1
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->leftRecursiveRuleRefLabels:Ljava/util/List;

    invoke-static {v1, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v1

    invoke-interface {v4, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_2
    move-object v6, v2

    move v8, v3

    .line 194
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->stripAltLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 195
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->text(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;

    move-result-object v0

    .line 196
    invoke-virtual {v0}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v5

    .line 197
    new-instance v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    move-object v3, v0

    move v4, p2

    move-object v9, p1

    invoke-direct/range {v3 .. v9}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/tool/ast/AltAST;)V

    .line 199
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->suffixAlts:Ljava/util/LinkedHashMap;

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    invoke-virtual {p1, p2, v0}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public text(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;
    .locals 12

    if-nez p1, :cond_0

    const-string p1, ""

    return-object p1

    .line 336
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStartIndex()I

    move-result v0

    .line 337
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStopIndex()I

    move-result v1

    .line 344
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v3, 0x0

    new-array v4, v3, [I

    invoke-direct {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    const/16 v4, 0x52

    .line 345
    invoke-virtual {p1, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object v4

    .line 346
    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_1

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 347
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStartIndex()I

    move-result v6

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getTokenStopIndex()I

    move-result v5

    invoke-virtual {v2, v6, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(II)V

    goto :goto_0

    .line 353
    :cond_1
    new-instance v4, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v5, v3, [I

    invoke-direct {v4, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 354
    new-instance v5, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v6, 0x2

    new-array v6, v6, [I

    fill-array-data v6, :array_0

    invoke-direct {v5, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    invoke-virtual {p1, v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodesWithType(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/List;

    move-result-object v5

    .line 355
    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :goto_1
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_2

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 356
    invoke-virtual {v6, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v6

    invoke-interface {v6}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getTokenStartIndex()I

    move-result v6

    invoke-virtual {v4, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    goto :goto_1

    .line 359
    :cond_2
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    :cond_3
    :goto_2
    if-gt v0, v1, :cond_a

    .line 362
    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v5

    if-eqz v5, :cond_4

    add-int/lit8 v0, v0, 0x1

    goto :goto_2

    .line 367
    :cond_4
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    invoke-interface {v5, v0}, Lgroovyjarjarantlr4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v5

    .line 370
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    .line 371
    invoke-virtual {v4, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v7

    const/16 v8, 0x3e

    const/16 v9, 0x39

    if-nez v7, :cond_8

    .line 372
    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/Token;->getTokenIndex()I

    move-result v7

    invoke-virtual {p1, v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getNodeWithTokenIndex(I)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v7

    if-eqz v7, :cond_6

    .line 373
    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v10

    const/16 v11, 0x42

    if-eq v10, v11, :cond_5

    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v10

    if-eq v10, v8, :cond_5

    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v10

    if-ne v10, v9, :cond_6

    :cond_5
    const-string v10, "tokenIndex="

    .line 378
    invoke-virtual {v6, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v10

    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/Token;->getTokenIndex()I

    move-result v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 381
    :cond_6
    instance-of v10, v7, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    if-eqz v10, :cond_8

    .line 382
    check-cast v7, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    .line 383
    invoke-virtual {v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->getOptions()Ljava/util/Map;

    move-result-object v7

    invoke-interface {v7}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v7

    invoke-interface {v7}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v7

    :goto_3
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    move-result v10

    if-eqz v10, :cond_8

    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Ljava/util/Map$Entry;

    .line 384
    invoke-virtual {v6}, Ljava/lang/StringBuilder;->length()I

    move-result v11

    if-lez v11, :cond_7

    const/16 v11, 0x2c

    .line 385
    invoke-virtual {v6, v11}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 388
    :cond_7
    invoke-interface {v10}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Ljava/lang/String;

    invoke-virtual {v6, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v11, 0x3d

    .line 389
    invoke-virtual {v6, v11}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 390
    invoke-interface {v10}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v10}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v6, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_3

    .line 395
    :cond_8
    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v3, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v0, v0, 0x1

    .line 399
    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v5

    if-ne v5, v9, :cond_9

    if-gt v0, v1, :cond_9

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    invoke-interface {v5, v0}, Lgroovyjarjarantlr4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v5

    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v5

    const/16 v7, 0x8

    if-ne v5, v7, :cond_9

    .line 400
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v7, 0x5b

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v7, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->tokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    invoke-interface {v7, v0}, Lgroovyjarjarantlr4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v7

    invoke-interface {v7}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const/16 v7, 0x5d

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v0, v0, 0x1

    .line 405
    :cond_9
    invoke-virtual {v6}, Ljava/lang/StringBuilder;->length()I

    move-result v5

    if-lez v5, :cond_3

    const/16 v5, 0x3c

    .line 406
    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/CharSequence;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_2

    .line 409
    :cond_a
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    nop

    :array_0
    .array-data 4
        0xa
        0x2e
    .end array-data
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 425
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "PrecRuleOperatorCollector{binaryAlts="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->binaryAlts:Ljava/util/LinkedHashMap;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", ternaryAlts="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->ternaryAlts:Ljava/util/LinkedHashMap;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", suffixAlts="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->suffixAlts:Ljava/util/LinkedHashMap;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", prefixAndOtherAlts="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAnalyzer;->prefixAndOtherAlts:Ljava/util/List;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
