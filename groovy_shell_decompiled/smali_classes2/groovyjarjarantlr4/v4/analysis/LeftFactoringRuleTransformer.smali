.class public Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;
.super Ljava/lang/Object;
.source "LeftFactoringRuleTransformer.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;,
        Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z = false

.field public static final LEFTFACTOR:Ljava/lang/String; = "leftfactor"

.field private static final LOGGER:Ljava/util/logging/Logger;

.field public static final SUPPRESS_ACCESSOR:Ljava/lang/String; = "suppressAccessor"


# instance fields
.field public _ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

.field public _g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public _rules:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;"
        }
    .end annotation
.end field

.field public _tool:Lgroovyjarjarantlr4/v4/Tool;

.field private final adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 46
    const-class v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/util/logging/Logger;->getLogger(Ljava/lang/String;)Ljava/util/logging/Logger;

    move-result-object v0

    sput-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->LOGGER:Ljava/util/logging/Logger;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Ljava/util/Map;Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ")V"
        }
    .end annotation

    .line 55
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 53
    new-instance v0, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    .line 56
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    .line 57
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_rules:Ljava/util/Map;

    .line 58
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 59
    iget-object p1, p3, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_tool:Lgroovyjarjarantlr4/v4/Tool;

    return-void
.end method


# virtual methods
.method protected createLeftFactoredRuleVariant(Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;
    .locals 11

    .line 635
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/16 v1, 0x4e

    .line 636
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    .line 641
    sget-object v7, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->FULL_FACTOR:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    const/4 v6, 0x1

    const/4 v8, 0x0

    move-object v3, p0

    move-object v4, v2

    move-object v5, p2

    invoke-virtual/range {v3 .. v8}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredDecision(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Z

    move-result v3

    const/4 v4, 0x0

    if-eqz v3, :cond_0

    move-object v1, v4

    goto :goto_0

    .line 644
    :cond_0
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 645
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    const/4 v6, 0x1

    .line 646
    sget-object v7, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->PARTIAL_FACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    const/4 v8, 0x0

    move-object v3, p0

    move-object v4, v2

    move-object v5, p2

    invoke-virtual/range {v3 .. v8}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredDecision(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Z

    move-result v3

    if-nez v3, :cond_1

    .line 648
    sget-object p1, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->NONE:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    return-object p1

    .line 651
    :cond_1
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v3

    move-object v4, v3

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 652
    invoke-virtual {v4, v1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    const/4 v8, 0x1

    .line 653
    sget-object v9, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->PARTIAL_UNFACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    const/4 v10, 0x0

    move-object v5, p0

    move-object v6, v1

    move-object v7, p2

    invoke-virtual/range {v5 .. v10}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredDecision(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Z

    move-result v3

    if-eqz v3, :cond_5

    .line 662
    :goto_0
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v5, 0x0

    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v6

    invoke-interface {v6}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v6, "$lf$"

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 663
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iget-object v7, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v8

    invoke-interface {v8}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v8

    invoke-virtual {v7, v8, v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->createToken(ILjava/lang/String;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v3

    iput-object v3, v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 664
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 665
    iget-object v6, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChildIndex()I

    move-result v6

    add-int/lit8 v6, v6, 0x1

    invoke-virtual {v3, v6, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->insertChild(ILjava/lang/Object;)V

    .line 666
    iget-object v6, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChildIndex()I

    move-result v6

    invoke-virtual {v3, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->freshenParentAndChildIndexes(I)V

    const/16 v3, 0x4a

    .line 668
    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object v2

    .line 669
    new-instance v6, Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v7, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v8

    invoke-interface {v8}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v8

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v9

    invoke-direct {v6, v7, v8, v0, v9}, Lgroovyjarjarantlr4/v4/tool/Rule;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;I)V

    .line 670
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineRule(Lgroovyjarjarantlr4/v4/tool/Rule;)Z

    move v0, v5

    .line 671
    :goto_1
    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v7

    if-ge v0, v7, :cond_2

    .line 672
    iget-object v7, v6, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    add-int/lit8 v8, v0, 0x1

    aget-object v7, v7, v8

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v0, v7, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    move v0, v8

    goto :goto_1

    :cond_2
    if-eqz v4, :cond_3

    .line 680
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "$nolf$"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    .line 681
    invoke-virtual {v4, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v4, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v6

    invoke-interface {v6}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v6

    invoke-virtual {v2, v6, p2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->createToken(ILjava/lang/String;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p2

    iput-object p2, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 682
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 683
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChildIndex()I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    invoke-virtual {p2, v0, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->insertChild(ILjava/lang/Object;)V

    .line 684
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChildIndex()I

    move-result p1

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->freshenParentAndChildIndexes(I)V

    .line 686
    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object p1

    .line 687
    new-instance p2, Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v4, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v2

    invoke-direct {p2, v0, v1, v4, v2}, Lgroovyjarjarantlr4/v4/tool/Rule;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;I)V

    .line 688
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0, p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineRule(Lgroovyjarjarantlr4/v4/tool/Rule;)Z

    .line 689
    :goto_2
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    if-ge v5, v0, :cond_3

    .line 690
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    add-int/lit8 v1, v5, 0x1

    aget-object v0, v0, v1

    invoke-interface {p1, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v2, v0, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    move v5, v1

    goto :goto_2

    :cond_3
    if-nez v4, :cond_4

    .line 697
    sget-object p1, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->FULLY_FACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    goto :goto_3

    :cond_4
    sget-object p1, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->PARTIALLY_FACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    :goto_3
    return-object p1

    .line 654
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "expected unfactored alts for partial factorization"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method protected expandOptionalQuantifiersForAlt(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 8

    .line 151
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    :cond_0
    const/4 v0, 0x0

    .line 155
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v2

    const/16 v3, 0x59

    const-string v4, "EPSILON"

    const/16 v5, 0x53

    if-ne v2, v3, :cond_4

    .line 156
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v1

    .line 158
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    .line 159
    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->deleteChild(I)Ljava/lang/Object;

    .line 160
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v3

    if-nez v3, :cond_1

    .line 161
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v3, v5, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v4

    invoke-virtual {v3, v2, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 164
    :cond_1
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-interface {v3, v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-virtual {p1, v0, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->setChild(ILgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 165
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v3

    const/16 v4, 0x4e

    if-ne v3, v4, :cond_3

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChildCount()I

    move-result v3

    const/4 v4, 0x1

    if-ne v3, v4, :cond_3

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-interface {v3, v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v3

    const/16 v4, 0x4a

    if-ne v3, v4, :cond_3

    .line 166
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v3

    .line 167
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    invoke-interface {v4, v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object v4

    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_2

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    .line 168
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v6, v3, v5}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_0

    .line 171
    :cond_2
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v4, p1, v0, v0, v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->replaceChildren(Ljava/lang/Object;IILjava/lang/Object;)V

    .line 174
    :cond_3
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 175
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {p1, v1, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v1

    .line 178
    :cond_4
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v2

    const/16 v3, 0x50

    if-ne v2, v3, :cond_7

    .line 179
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    .line 181
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v3

    .line 182
    invoke-virtual {v3, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->deleteChild(I)Ljava/lang/Object;

    .line 183
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v6

    if-nez v6, :cond_5

    .line 184
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v6, v5, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v4

    invoke-virtual {v6, v3, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 187
    :cond_5
    new-instance v4, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v6, 0x5a

    const-string v7, "+"

    invoke-virtual {v5, v6, v7}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->createToken(ILjava/lang/String;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v5

    invoke-direct {v4, v6, v5, v1}, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;-><init>(ILgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)V

    move v1, v0

    .line 188
    :goto_1
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    invoke-interface {v5}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChildCount()I

    move-result v5

    if-ge v1, v5, :cond_6

    .line 189
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    invoke-interface {v5, v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    invoke-virtual {v4, v5}, Lgroovyjarjarantlr4/v4/tool/ast/PlusBlockAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 192
    :cond_6
    invoke-virtual {p1, v0, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->setChild(ILgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 194
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v0, v2, p1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 195
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {p1, v2, v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v2

    :cond_7
    return-object p1
.end method

.method protected expandOptionalQuantifiersForBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Z)Z
    .locals 6

    .line 113
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    move v2, v1

    .line 114
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v3

    const/16 v4, 0x4a

    if-ge v2, v3, :cond_2

    .line 115
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 116
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v5

    if-eq v5, v4, :cond_0

    .line 117
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 121
    :cond_0
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->expandOptionalQuantifiersForAlt(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v3

    if-nez v3, :cond_1

    return v1

    .line 126
    :cond_1
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 129
    :cond_2
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    .line 130
    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChildren(Ljava/util/List;)V

    .line 131
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v0

    const/4 v3, 0x1

    sub-int/2addr v0, v3

    invoke-virtual {p1, v1, v0, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->replaceChildren(IILjava/lang/Object;)V

    .line 132
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->freshenParentAndChildIndexesDeeply()V

    if-nez p2, :cond_3

    .line 134
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p2

    instance-of p2, p2, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    if-eqz p2, :cond_3

    .line 135
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 136
    invoke-virtual {p2, v1}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p2

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object p2

    .line 137
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_rules:Ljava/util/Map;

    invoke-interface {v0, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 138
    invoke-virtual {p1, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object p1

    .line 139
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    iput v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    .line 140
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    add-int/2addr v0, v3

    new-array v0, v0, [Lgroovyjarjarantlr4/v4/tool/Alternative;

    iput-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    .line 141
    :goto_2
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    if-ge v1, v0, :cond_3

    .line 142
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    add-int/lit8 v2, v1, 0x1

    new-instance v4, Lgroovyjarjarantlr4/v4/tool/Alternative;

    invoke-direct {v4, p2, v2}, Lgroovyjarjarantlr4/v4/tool/Alternative;-><init>(Lgroovyjarjarantlr4/v4/tool/Rule;I)V

    aput-object v4, v0, v2

    .line 143
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v0, v0, v2

    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    move v1, v2

    goto :goto_2

    :cond_3
    return v3
.end method

.method protected translateLeftFactoredAlternative(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 10

    .line 376
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->PARTIAL_UNFACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    if-ne p4, v0, :cond_1

    if-nez p5, :cond_0

    goto :goto_0

    .line 377
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Cannot include the factored element in unfactored alternatives."

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 379
    :cond_1
    :goto_0
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->COMBINED_FACTOR:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    if-ne p4, v0, :cond_3

    if-eqz p5, :cond_2

    goto :goto_1

    .line 380
    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Cannot return a combined answer without the factored element."

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    :goto_1
    const/4 v0, 0x0

    .line 385
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v1

    const/4 v2, 0x0

    const/16 v3, 0x53

    if-ne v1, v3, :cond_5

    .line 386
    sget-object p2, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->PARTIAL_UNFACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    if-ne p4, p2, :cond_4

    return-object p1

    :cond_4
    return-object v2

    .line 393
    :cond_5
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    move-object v5, v1

    check-cast v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-object v4, p0

    move-object v6, p2

    move v7, p3

    move-object v8, p4

    move v9, p5

    invoke-virtual/range {v4 .. v9}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p2

    if-nez p2, :cond_6

    return-object v2

    .line 398
    :cond_6
    invoke-virtual {p1, v0, v0, p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->replaceChildren(IILjava/lang/Object;)V

    .line 399
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result p2

    if-nez p2, :cond_7

    .line 400
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const-string p3, "EPSILON"

    invoke-virtual {p2, v3, p3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object p3

    invoke-virtual {p2, p1, p3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_7
    return-object p1
.end method

.method protected translateLeftFactoredDecision(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Z
    .locals 20

    move-object/from16 v6, p0

    move-object/from16 v7, p1

    move/from16 v8, p3

    move-object/from16 v9, p4

    .line 203
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->PARTIAL_UNFACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    if-ne v9, v0, :cond_1

    if-nez p5, :cond_0

    goto :goto_0

    .line 204
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "Cannot include the factored element in unfactored alternatives."

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 206
    :cond_1
    :goto_0
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->COMBINED_FACTOR:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    if-ne v9, v0, :cond_3

    if-eqz p5, :cond_2

    goto :goto_1

    .line 207
    :cond_2
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "Cannot return a combined answer without the factored element."

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 210
    :cond_3
    :goto_1
    invoke-virtual {v6, v7, v8}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->expandOptionalQuantifiersForBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Z)Z

    move-result v0

    const/4 v10, 0x0

    if-nez v0, :cond_4

    return v10

    :cond_4
    const/16 v11, 0x4a

    .line 214
    invoke-virtual {v7, v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object v12

    .line 215
    invoke-interface {v12}, Ljava/util/List;->size()I

    move-result v13

    new-array v14, v13, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 216
    invoke-interface {v12}, Ljava/util/List;->size()I

    move-result v15

    new-array v5, v15, [Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 217
    new-instance v4, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v0, v10, [I

    invoke-direct {v4, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 218
    new-instance v3, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v0, v10, [I

    invoke-direct {v3, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    move v2, v10

    .line 219
    :goto_2
    invoke-interface {v12}, Ljava/util/List;->size()I

    move-result v0

    if-ge v2, v0, :cond_9

    .line 220
    invoke-interface {v12, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    move-object/from16 v16, v0

    check-cast v16, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 221
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 222
    invoke-virtual/range {v16 .. v16}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v1

    sget-object v17, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->PARTIAL_UNFACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    const/16 v18, 0x0

    move-object/from16 v0, p0

    move v11, v2

    move-object/from16 v2, p2

    move-object v10, v3

    move/from16 v3, p3

    move-object v8, v4

    move-object/from16 v4, v17

    move-object/from16 v17, v5

    move/from16 v5, v18

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredAlternative(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    .line 223
    aput-object v0, v17, v11

    if-eqz v0, :cond_6

    .line 225
    invoke-virtual {v10, v11}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    goto :goto_3

    :cond_5
    move v11, v2

    move-object v10, v3

    move-object v8, v4

    move-object/from16 v17, v5

    .line 229
    :cond_6
    :goto_3
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeFactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_8

    .line 230
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->COMBINED_FACTOR:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    if-ne v9, v0, :cond_7

    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->PARTIAL_FACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    goto :goto_4

    :cond_7
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->FULL_FACTOR:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    :goto_4
    move-object v4, v0

    move-object/from16 v0, p0

    move-object/from16 v1, v16

    move-object/from16 v2, p2

    move/from16 v3, p3

    move/from16 v5, p5

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredAlternative(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    .line 231
    aput-object v0, v14, v11

    if-eqz v0, :cond_8

    .line 233
    invoke-virtual/range {v16 .. v16}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildIndex()I

    move-result v0

    invoke-virtual {v8, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    :cond_8
    add-int/lit8 v2, v11, 0x1

    move-object v4, v8

    move-object v3, v10

    move-object/from16 v5, v17

    const/4 v10, 0x0

    const/16 v11, 0x4a

    move/from16 v8, p3

    goto :goto_2

    :cond_9
    move-object v10, v3

    move-object v8, v4

    move-object/from16 v17, v5

    .line 238
    invoke-virtual {v8}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->isNil()Z

    move-result v0

    if-eqz v0, :cond_a

    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v0

    if-nez v0, :cond_a

    const/4 v0, 0x0

    return v0

    :cond_a
    const/4 v0, 0x0

    .line 240
    invoke-virtual {v10}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->isNil()Z

    move-result v1

    if-eqz v1, :cond_b

    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeFactoredAlts()Z

    move-result v1

    if-nez v1, :cond_b

    return v0

    .line 244
    :cond_b
    invoke-virtual {v10}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->isNil()Z

    move-result v0

    const-string v1, "EPSILON"

    const/16 v2, 0x53

    const/4 v3, 0x1

    if-eqz v0, :cond_e

    invoke-virtual {v8}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->size()I

    move-result v0

    invoke-interface {v12}, Ljava/util/List;->size()I

    move-result v4

    if-ne v0, v4, :cond_e

    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeFactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_e

    if-nez p5, :cond_e

    const/4 v10, 0x0

    :goto_5
    if-ge v10, v13, :cond_d

    .line 246
    aget-object v0, v14, v10

    .line 247
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v4

    if-nez v4, :cond_c

    .line 248
    iget-object v4, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v4, v2, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v5

    invoke-virtual {v4, v0, v5}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 251
    :cond_c
    iget-object v4, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v4, v7, v10, v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->setChild(Ljava/lang/Object;ILjava/lang/Object;)V

    add-int/lit8 v10, v10, 0x1

    goto :goto_5

    :cond_d
    return v3

    .line 256
    :cond_e
    invoke-virtual {v8}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->isNil()Z

    move-result v0

    if-eqz v0, :cond_11

    invoke-virtual {v10}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->size()I

    move-result v0

    invoke-interface {v12}, Ljava/util/List;->size()I

    move-result v4

    if-ne v0, v4, :cond_11

    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_11

    const/4 v10, 0x0

    :goto_6
    if-ge v10, v15, :cond_10

    .line 258
    aget-object v0, v17, v10

    .line 259
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v4

    if-nez v4, :cond_f

    .line 260
    iget-object v4, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v4, v2, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v5

    invoke-virtual {v4, v0, v5}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 263
    :cond_f
    iget-object v4, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v4, v7, v10, v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->setChild(Ljava/lang/Object;ILjava/lang/Object;)V

    add-int/lit8 v10, v10, 0x1

    goto :goto_6

    :cond_10
    return v3

    .line 269
    :cond_11
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->FULL_FACTOR:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    if-ne v9, v0, :cond_12

    const/4 v0, 0x0

    return v0

    .line 285
    :cond_12
    iget-object v0, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    const/4 v4, 0x0

    .line 286
    :goto_7
    invoke-interface {v12}, Ljava/util/List;->size()I

    move-result v5

    if-ge v4, v5, :cond_25

    .line 287
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeFactoredAlts()Z

    move-result v5

    if-eqz v5, :cond_21

    invoke-virtual {v8, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v5

    if-eqz v5, :cond_21

    if-lez v4, :cond_14

    add-int/lit8 v5, v4, -0x1

    .line 288
    invoke-virtual {v8, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v11

    if-eqz v11, :cond_14

    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v11

    if-eqz v11, :cond_13

    invoke-virtual {v10, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v5

    if-nez v5, :cond_14

    :cond_13
    move v5, v3

    goto :goto_8

    :cond_14
    const/4 v5, 0x0

    :goto_8
    if-eqz v5, :cond_1f

    .line 290
    aget-object v5, v14, v4

    .line 291
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v11

    if-nez v11, :cond_15

    .line 292
    iget-object v11, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v11, v2, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v13

    invoke-virtual {v11, v5, v13}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 295
    :cond_15
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v11

    sub-int/2addr v11, v3

    invoke-virtual {v0, v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v11

    check-cast v11, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 296
    sget-object v13, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->LOGGER:Ljava/util/logging/Logger;

    sget-object v15, Ljava/util/logging/Level;->FINE:Ljava/util/logging/Level;

    invoke-virtual {v13, v15}, Ljava/util/logging/Logger;->isLoggable(Ljava/util/logging/Level;)Z

    move-result v15

    if-eqz v15, :cond_16

    .line 297
    sget-object v15, Ljava/util/logging/Level;->FINE:Ljava/util/logging/Level;

    invoke-virtual {v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->toStringTree()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v13, v15, v2}, Ljava/util/logging/Logger;->log(Ljava/util/logging/Level;Ljava/lang/String;)V

    .line 298
    sget-object v2, Ljava/util/logging/Level;->FINE:Ljava/util/logging/Level;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->toStringTree()Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v13, v2, v15}, Ljava/util/logging/Logger;->log(Ljava/util/logging/Level;Ljava/lang/String;)V

    .line 300
    :cond_16
    invoke-virtual {v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v2

    const-string v13, "ALT"

    const-string v15, "BLOCK"

    move-object/from16 v16, v8

    const/16 v8, 0x4e

    if-eq v2, v3, :cond_18

    invoke-virtual {v11, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v2

    if-eq v2, v8, :cond_17

    goto :goto_9

    :cond_17
    move-object/from16 v19, v12

    goto :goto_b

    .line 301
    :cond_18
    :goto_9
    new-instance v2, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    iget-object v3, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v3, v8, v15}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->createToken(ILjava/lang/String;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v3

    invoke-direct {v2, v3}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    .line 302
    new-instance v3, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v9, 0x4a

    invoke-virtual {v8, v9, v13}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->createToken(ILjava/lang/String;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v8

    invoke-direct {v3, v8}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    .line 303
    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v8, v2, v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 304
    :goto_a
    invoke-virtual {v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v8

    const/4 v9, 0x1

    if-le v8, v9, :cond_19

    .line 305
    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    move-object/from16 v19, v12

    invoke-virtual {v11, v9}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->deleteChild(I)Ljava/lang/Object;

    move-result-object v12

    invoke-virtual {v8, v3, v12}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    move-object/from16 v12, v19

    goto :goto_a

    :cond_19
    move-object/from16 v19, v12

    .line 308
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v8

    if-nez v8, :cond_1a

    .line 309
    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v9, 0x53

    invoke-virtual {v8, v9, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v12

    invoke-virtual {v8, v3, v12}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 312
    :cond_1a
    iget-object v3, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v3, v11, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 315
    :goto_b
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v2

    const/4 v3, 0x1

    if-eq v2, v3, :cond_1c

    invoke-virtual {v5, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v2

    const/16 v3, 0x4e

    if-eq v2, v3, :cond_1b

    goto :goto_d

    :cond_1b
    :goto_c
    const/4 v2, 0x1

    goto :goto_f

    :cond_1c
    const/16 v3, 0x4e

    .line 316
    :goto_d
    new-instance v2, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v8, v3, v15}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->createToken(ILjava/lang/String;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v3

    invoke-direct {v2, v3}, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    .line 317
    new-instance v3, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v9, 0x4a

    invoke-virtual {v8, v9, v13}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->createToken(ILjava/lang/String;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v8

    invoke-direct {v3, v8}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    .line 318
    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v8, v2, v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 319
    :goto_e
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v8

    const/4 v9, 0x1

    if-le v8, v9, :cond_1d

    .line 320
    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v5, v9}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->deleteChild(I)Ljava/lang/Object;

    move-result-object v12

    invoke-virtual {v8, v3, v12}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_e

    .line 323
    :cond_1d
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v8

    if-nez v8, :cond_1e

    .line 324
    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v9, 0x53

    invoke-virtual {v8, v9, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v12

    invoke-virtual {v8, v3, v12}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 327
    :cond_1e
    iget-object v3, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v3, v5, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_c

    .line 330
    :goto_f
    invoke-virtual {v11, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 331
    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v5, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    const/4 v2, 0x0

    invoke-interface {v5, v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    invoke-virtual {v8, v3, v5}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 333
    sget-object v2, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->LOGGER:Ljava/util/logging/Logger;

    sget-object v3, Ljava/util/logging/Level;->FINE:Ljava/util/logging/Level;

    invoke-virtual {v2, v3}, Ljava/util/logging/Logger;->isLoggable(Ljava/util/logging/Level;)Z

    move-result v3

    if-eqz v3, :cond_22

    .line 334
    sget-object v3, Ljava/util/logging/Level;->FINE:Ljava/util/logging/Level;

    invoke-virtual {v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->toStringTree()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v2, v3, v5}, Ljava/util/logging/Logger;->log(Ljava/util/logging/Level;Ljava/lang/String;)V

    goto :goto_10

    :cond_1f
    move-object/from16 v16, v8

    move-object/from16 v19, v12

    .line 338
    aget-object v2, v14, v4

    .line 339
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v3

    if-nez v3, :cond_20

    .line 340
    iget-object v3, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v5, 0x53

    invoke-virtual {v3, v5, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v8

    invoke-virtual {v3, v2, v8}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 343
    :cond_20
    iget-object v3, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v3, v0, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_10

    :cond_21
    move-object/from16 v16, v8

    move-object/from16 v19, v12

    .line 347
    :cond_22
    :goto_10
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v2

    if-eqz v2, :cond_24

    invoke-virtual {v10, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v2

    if-eqz v2, :cond_24

    .line 348
    aget-object v2, v17, v4

    .line 349
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v3

    if-nez v3, :cond_23

    .line 350
    iget-object v3, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v5, 0x53

    invoke-virtual {v3, v5, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v8

    invoke-virtual {v3, v2, v8}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_11

    :cond_23
    const/16 v5, 0x53

    .line 353
    :goto_11
    iget-object v3, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v3, v0, v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_12

    :cond_24
    const/16 v5, 0x53

    :goto_12
    add-int/lit8 v4, v4, 0x1

    move-object/from16 v9, p4

    move v2, v5

    move-object/from16 v8, v16

    move-object/from16 v12, v19

    const/4 v3, 0x1

    goto/16 :goto_7

    .line 357
    :cond_25
    iget-object v1, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v2

    const/4 v3, 0x1

    sub-int/2addr v2, v3

    const/4 v3, 0x0

    invoke-virtual {v1, v7, v3, v2, v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->replaceChildren(Ljava/lang/Object;IILjava/lang/Object;)V

    if-nez p3, :cond_26

    .line 359
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    instance-of v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    if-eqz v0, :cond_26

    .line 360
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 361
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v0

    .line 362
    iget-object v1, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_rules:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/Rule;

    const/16 v1, 0x4a

    .line 363
    invoke-virtual {v7, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object v1

    .line 364
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v2

    iput v2, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    .line 365
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v2

    const/4 v4, 0x1

    add-int/2addr v2, v4

    new-array v2, v2, [Lgroovyjarjarantlr4/v4/tool/Alternative;

    iput-object v2, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    :goto_13
    move v10, v3

    .line 366
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v2

    if-ge v10, v2, :cond_26

    .line 367
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    add-int/lit8 v3, v10, 0x1

    new-instance v4, Lgroovyjarjarantlr4/v4/tool/Alternative;

    invoke-direct {v4, v0, v3}, Lgroovyjarjarantlr4/v4/tool/Alternative;-><init>(Lgroovyjarjarantlr4/v4/tool/Rule;I)V

    aput-object v4, v2, v3

    .line 368
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v2, v2, v3

    invoke-interface {v1, v10}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iput-object v4, v2, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    goto :goto_13

    :cond_26
    const/4 v0, 0x1

    return v0
.end method

.method protected translateLeftFactoredElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 14

    move-object v6, p0

    move-object v7, p1

    move-object/from16 v8, p2

    move-object/from16 v9, p4

    .line 408
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->PARTIAL_UNFACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    if-ne v9, v0, :cond_1

    if-nez p5, :cond_0

    goto :goto_0

    .line 409
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "Cannot include the factored element in unfactored alternatives."

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 412
    :cond_1
    :goto_0
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->COMBINED_FACTOR:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    if-eq v9, v0, :cond_1b

    .line 418
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v0

    const/4 v10, 0x2

    const/4 v11, 0x1

    const/4 v12, 0x0

    const/4 v13, 0x0

    sparse-switch v0, :sswitch_data_0

    return-object v13

    .line 563
    :sswitch_0
    invoke-virtual {p1, v12}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    move-object v7, v0

    check-cast v7, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 564
    invoke-virtual {v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v1

    move-object v0, p0

    move-object/from16 v2, p2

    move/from16 v3, p3

    move-object/from16 v4, p4

    move/from16 v5, p5

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    if-nez v0, :cond_2

    return-object v13

    .line 569
    :cond_2
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/ast/StarBlockAST;

    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v3, 0x50

    const-string v4, "CLOSURE"

    invoke-virtual {v2, v3, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->createToken(ILjava/lang/String;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v2

    invoke-direct {v1, v3, v2, v13}, Lgroovyjarjarantlr4/v4/tool/ast/StarBlockAST;-><init>(ILgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)V

    .line 570
    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v2, v1, v7}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 572
    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    .line 573
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeFactoredAlts()Z

    move-result v3

    if-eqz v3, :cond_3

    if-eqz p5, :cond_3

    .line 575
    iget-object v3, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v3, v0, v12}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->deleteChild(Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v3

    .line 576
    iget-object v4, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v4, v2, v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 579
    :cond_3
    iget-object v3, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v3, v2, v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 580
    iget-object v0, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v0, v2, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v2

    .line 622
    :sswitch_1
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_4

    return-object v7

    :cond_4
    return-object v13

    .line 587
    :sswitch_2
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_5

    return-object v7

    :cond_5
    return-object v13

    .line 537
    :sswitch_3
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v7

    move-object v0, p0

    move-object v1, v7

    move-object/from16 v2, p2

    move/from16 v3, p3

    move-object/from16 v4, p4

    move/from16 v5, p5

    .line 538
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredDecision(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Z

    move-result v0

    if-nez v0, :cond_6

    return-object v13

    .line 542
    :cond_6
    invoke-virtual {v7}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v0

    if-eq v0, v11, :cond_7

    return-object v13

    .line 546
    :cond_7
    iget-object v0, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    move v1, v12

    .line 547
    :goto_1
    invoke-virtual {v7, v12}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChildCount()I

    move-result v2

    if-ge v1, v2, :cond_8

    .line 548
    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v7, v12}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-interface {v3, v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-virtual {v2, v0, v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_8
    return-object v0

    .line 464
    :sswitch_4
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v8, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_b

    .line 465
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeFactoredAlts()Z

    move-result v0

    if-nez v0, :cond_9

    return-object v13

    :cond_9
    if-eqz p5, :cond_a

    return-object v7

    .line 474
    :cond_a
    iget-object v0, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    .line 475
    iget-object v1, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/4 v2, -0x2

    const-string v3, "EPSILON"

    invoke-virtual {v1, v2, v3}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 476
    invoke-virtual {v0, v12}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->deleteChild(I)Ljava/lang/Object;

    return-object v0

    .line 480
    :cond_b
    iget-object v0, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_rules:Ljava/util/Map;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/Rule;

    if-nez v0, :cond_c

    return-object v13

    .line 485
    :cond_c
    invoke-virtual {p0, v0, v8}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->createLeftFactoredRuleVariant(Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    move-result-object v0

    .line 486
    sget-object v1, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$1;->$SwitchMap$org$antlr$v4$analysis$LeftFactoringRuleTransformer$RuleVariants:[I

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->ordinal()I

    move-result v0

    aget v0, v1, v0

    if-eq v0, v11, :cond_15

    if-eq v0, v10, :cond_e

    const/4 v1, 0x3

    if-ne v0, v1, :cond_d

    goto :goto_2

    .line 506
    :cond_d
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0

    .line 496
    :cond_e
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeFactoredAlts()Z

    move-result v0

    if-nez v0, :cond_f

    return-object v13

    .line 509
    :cond_f
    :goto_2
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeFactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_10

    const-string v0, "$lf$"

    goto :goto_3

    :cond_10
    const-string v0, "$nolf$"

    .line 510
    :goto_3
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->setText(Ljava/lang/String;)V

    .line 512
    iget-object v0, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->nil()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    if-eqz p5, :cond_14

    .line 516
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;

    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v3, 0x39

    invoke-virtual {v2, v3, v8}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->createToken(ILjava/lang/String;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v2

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    .line 517
    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v3, 0x1c

    const-string v4, "true"

    invoke-virtual {v2, v3, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    const-string v3, "suppressAccessor"

    invoke-virtual {v1, v3, v2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->setOption(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 518
    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_rules:Ljava/util/Map;

    invoke-interface {v2, v8}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 519
    instance-of v2, v2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;

    if-eqz v2, :cond_11

    .line 520
    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    const/16 v3, 0x1e

    const-string v4, "0"

    invoke-virtual {v2, v3, v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    const-string v3, "p"

    invoke-virtual {v1, v3, v2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleRefAST;->setOption(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 523
    :cond_11
    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_rules:Ljava/util/Map;

    invoke-interface {v2, v8}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v2, :cond_13

    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_rules:Ljava/util/Map;

    invoke-interface {v2, v8}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->size()I

    move-result v2

    if-gtz v2, :cond_12

    goto :goto_4

    .line 524
    :cond_12
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Cannot left-factor rules with arguments yet."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 527
    :cond_13
    :goto_4
    iget-object v2, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v2, v0, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 530
    :cond_14
    iget-object v1, v6, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->adaptor:Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-virtual {v1, v0, p1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;->addChild(Ljava/lang/Object;Ljava/lang/Object;)V

    return-object v0

    .line 488
    :cond_15
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v0

    if-nez v0, :cond_16

    return-object v13

    :cond_16
    return-object v7

    .line 614
    :sswitch_5
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_17

    return-object v7

    :cond_17
    return-object v13

    .line 595
    :sswitch_6
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_18

    return-object v7

    :cond_18
    return-object v13

    .line 429
    :sswitch_7
    invoke-virtual {p1, v11}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-object v0, p0

    move-object/from16 v2, p2

    move/from16 v3, p3

    move-object/from16 v4, p4

    move/from16 v5, p5

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v0

    if-nez v0, :cond_19

    return-object v13

    :cond_19
    const/16 v0, 0x5e

    .line 434
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAncestor(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 435
    sget-object v1, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->LOGGER:Ljava/util/logging/Logger;

    sget-object v2, Ljava/util/logging/Level;->WARNING:Ljava/util/logging/Level;

    new-array v3, v10, [Ljava/lang/Object;

    aput-object v8, v3, v12

    invoke-virtual {v0, v12}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v0

    aput-object v0, v3, v11

    const-string v0, "Could not left factor \'\'{0}\'\' out of decision in rule \'\'{1}\'\': labeled rule references are not yet supported."

    invoke-virtual {v1, v2, v0, v3}, Ljava/util/logging/Logger;->log(Ljava/util/logging/Level;Ljava/lang/String;[Ljava/lang/Object;)V

    return-object v13

    .line 603
    :sswitch_8
    invoke-virtual/range {p4 .. p4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->includeUnfactoredAlts()Z

    move-result v0

    if-eqz v0, :cond_1a

    return-object v7

    :cond_1a
    return-object v13

    .line 413
    :cond_1b
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Cannot return a combined answer."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0

    nop

    :sswitch_data_0
    .sparse-switch
        0x4 -> :sswitch_8
        0xa -> :sswitch_7
        0x14 -> :sswitch_6
        0x27 -> :sswitch_5
        0x2e -> :sswitch_7
        0x39 -> :sswitch_4
        0x3b -> :sswitch_8
        0x3e -> :sswitch_5
        0x42 -> :sswitch_5
        0x4e -> :sswitch_3
        0x50 -> :sswitch_2
        0x53 -> :sswitch_1
        0x59 -> :sswitch_2
        0x5a -> :sswitch_0
        0x64 -> :sswitch_5
    .end sparse-switch
.end method

.method public translateLeftFactoredRules()V
    .locals 15

    .line 64
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->_rules:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_9

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 65
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isTokenName(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_1

    goto :goto_0

    .line 69
    :cond_1
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->namedActions:Ljava/util/Map;

    const-string v3, "leftfactor"

    invoke-interface {v2, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    if-nez v2, :cond_2

    goto :goto_0

    .line 74
    :cond_2
    instance-of v3, v2, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    if-nez v3, :cond_3

    goto :goto_0

    .line 78
    :cond_3
    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    .line 79
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v3

    const/4 v4, 0x1

    sub-int/2addr v3, v4

    invoke-virtual {v2, v4, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v2

    const-string v3, ",\\s*"

    .line 80
    invoke-virtual {v2, v3}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v2

    .line 81
    array-length v3, v2

    if-nez v3, :cond_4

    goto :goto_0

    .line 85
    :cond_4
    sget-object v3, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->LOGGER:Ljava/util/logging/Logger;

    sget-object v5, Ljava/util/logging/Level;->FINE:Ljava/util/logging/Level;

    const/4 v6, 0x2

    new-array v6, v6, [Ljava/lang/Object;

    invoke-static {v2}, Ljava/util/Arrays;->toString([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v7

    const/4 v8, 0x0

    aput-object v7, v6, v8

    iget-object v7, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v7, v6, v4

    const-string v7, "Left factoring {0} out of alts in grammar rule {1}"

    invoke-virtual {v3, v5, v7, v6}, Ljava/util/logging/Logger;->log(Ljava/util/logging/Level;Ljava/lang/String;[Ljava/lang/Object;)V

    .line 87
    new-instance v3, Ljava/util/HashSet;

    invoke-direct {v3}, Ljava/util/HashSet;-><init>()V

    .line 88
    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/16 v5, 0x4e

    invoke-virtual {v1, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object v1

    .line 90
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 91
    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v7

    :goto_2
    check-cast v7, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v7, :cond_6

    .line 92
    invoke-interface {v3, v7}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_5

    goto :goto_1

    .line 91
    :cond_5
    invoke-virtual {v7, v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAncestor(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v7

    goto :goto_2

    .line 98
    :cond_6
    array-length v7, v2

    if-ne v7, v4, :cond_8

    .line 102
    aget-object v11, v2, v8

    const/4 v12, 0x0

    sget-object v13, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;->COMBINED_FACTOR:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;

    const/4 v14, 0x1

    move-object v9, p0

    move-object v10, v6

    invoke-virtual/range {v9 .. v14}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredDecision(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZLgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$DecisionFactorMode;Z)Z

    move-result v7

    if-nez v7, :cond_7

    goto :goto_1

    .line 107
    :cond_7
    invoke-interface {v3, v6}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 99
    :cond_8
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Chained left factoring is not yet implemented."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_9
    return-void
.end method
