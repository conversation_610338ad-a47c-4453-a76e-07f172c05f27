.class public Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;
.super Ljava/lang/Object;
.source "AnalysisPipeline.java"


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field public g:Lgroovyjarjarantlr4/v4/tool/Grammar;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 0

    .line 26
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 27
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    return-void
.end method

.method public static disjoint([Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Z
    .locals 7

    .line 84
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v1, 0x0

    new-array v2, v1, [I

    invoke-direct {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    if-nez p0, :cond_0

    return v1

    .line 86
    :cond_0
    array-length v2, p0

    move v3, v1

    :goto_0
    const/4 v4, 0x1

    if-ge v3, v2, :cond_3

    aget-object v5, p0, v3

    if-nez v5, :cond_1

    return v1

    .line 88
    :cond_1
    invoke-virtual {v5, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->and(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v6

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->isNil()Z

    move-result v6

    if-nez v6, :cond_2

    move v1, v4

    goto :goto_1

    .line 92
    :cond_2
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->addAll(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_3
    :goto_1
    xor-int/lit8 p0, v1, 0x1

    return p0
.end method


# virtual methods
.method public process()V
    .locals 3

    .line 32
    new-instance v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursionDetector;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursionDetector;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    .line 33
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursionDetector;->check()V

    .line 34
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/analysis/LeftRecursionDetector;->listOfRecursiveCycles:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 36
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 37
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->processLexer()V

    goto :goto_0

    .line 41
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->processParser()V

    :goto_0
    return-void
.end method

.method protected processLexer()V
    .locals 8

    .line 47
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 48
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Rule;->isFragment()Z

    move-result v2

    if-eqz v2, :cond_1

    goto :goto_0

    .line 52
    :cond_1
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-direct {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    .line 53
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    iget v4, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    aget-object v3, v3, v4

    sget-object v4, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_LOCAL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    invoke-virtual {v2, v3, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    const/4 v3, -0x2

    .line 54
    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 55
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->EPSILON_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v5, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/4 v6, 0x0

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v5

    const/4 v7, 0x1

    new-array v7, v7, [Ljava/lang/Object;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v1, v7, v6

    invoke-virtual {v2, v3, v4, v5, v7}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_2
    return-void
.end method

.method protected processParser()V
    .locals 7

    .line 61
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    new-instance v1, Ljava/util/ArrayList;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->getNumberOfDecisions()I

    move-result v2

    add-int/lit8 v2, v2, 0x1

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->decisionLOOK:Ljava/util/List;

    .line 62
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    .line 63
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "\nDECISION "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    iget v4, v1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, " in rule "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget v5, v1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->ruleIndex:I

    invoke-virtual {v4, v5}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(I)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v4

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const-string v4, "LL1"

    invoke-virtual {v2, v4, v3}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 65
    iget-boolean v2, v1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->nonGreedy:Z

    if-eqz v2, :cond_0

    .line 66
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->getNumberOfTransitions()I

    move-result v2

    add-int/lit8 v2, v2, 0x1

    new-array v2, v2, [Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    goto :goto_1

    .line 69
    :cond_0
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-direct {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    .line 70
    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->getDecisionLookahead(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)[Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    .line 71
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "look="

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-static {v2}, Ljava/util/Arrays;->toString([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 75
    :goto_1
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->decisionLOOK:Ljava/util/List;

    iget v5, v1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    add-int/lit8 v5, v5, 0x1

    invoke-static {v3, v5}, Lgroovyjarjarantlr4/v4/misc/Utils;->setSize(Ljava/util/List;I)V

    .line 76
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->decisionLOOK:Ljava/util/List;

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    invoke-interface {v3, v1, v2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 77
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "LL(1)? "

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->disjoint([Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Z

    move-result v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v4, v2}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_0

    :cond_1
    return-void
.end method
