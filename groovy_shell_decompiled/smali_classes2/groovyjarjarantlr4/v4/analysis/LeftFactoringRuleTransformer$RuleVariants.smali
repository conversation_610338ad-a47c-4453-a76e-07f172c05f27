.class public final enum Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;
.super Ljava/lang/Enum;
.source "LeftFactoringRuleTransformer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x401c
    name = "RuleVariants"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

.field public static final enum FULLY_FACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

.field public static final enum NONE:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

.field public static final enum PARTIALLY_FACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 743
    new-instance v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    const-string v1, "NONE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->NONE:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    .line 744
    new-instance v1, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    const-string v3, "PARTIALLY_FACTORED"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->PARTIALLY_FACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    .line 745
    new-instance v3, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    const-string v5, "FULLY_FACTORED"

    const/4 v6, 0x2

    invoke-direct {v3, v5, v6}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->FULLY_FACTORED:Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    const/4 v5, 0x3

    new-array v5, v5, [Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    aput-object v0, v5, v2

    aput-object v1, v5, v4

    aput-object v3, v5, v6

    .line 742
    sput-object v5, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->$VALUES:[Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 742
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;
    .locals 1

    .line 742
    const-class v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    return-object p0
.end method

.method public static values()[Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;
    .locals 1

    .line 742
    sget-object v0, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->$VALUES:[Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    invoke-virtual {v0}, [Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer$RuleVariants;

    return-object v0
.end method
