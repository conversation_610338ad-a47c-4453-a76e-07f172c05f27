.class public Lgroovyjarjarantlr4/v4/semantics/UseDefAnalyzer;
.super Ljava/lang/Object;
.source "UseDefAnalyzer.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 29
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static actionIsContextDependent(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z
    .locals 4

    .line 44
    new-instance v0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;-><init>(Ljava/lang/String;)V

    .line 45
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->setLine(I)V

    .line 46
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {p0}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result p0

    invoke-virtual {v0, p0}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->setCharPositionInLine(I)V

    const/4 p0, 0x1

    new-array p0, p0, [Z

    const/4 v1, 0x0

    aput-boolean v1, p0, v1

    .line 48
    new-instance v2, Lgroovyjarjarantlr4/v4/semantics/UseDefAnalyzer$1;

    invoke-direct {v2, p0}, Lgroovyjarjarantlr4/v4/semantics/UseDefAnalyzer$1;-><init>([Z)V

    .line 62
    new-instance v3, Lgroovyjarjarantlr4/v4/parse/ActionSplitter;

    invoke-direct {v3, v0, v2}, Lgroovyjarjarantlr4/v4/parse/ActionSplitter;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;Lgroovyjarjarantlr4/v4/parse/ActionSplitterListener;)V

    .line 64
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/parse/ActionSplitter;->getActionTokens()Ljava/util/List;

    .line 65
    aget-boolean p0, p0, v1

    return p0
.end method

.method public static getRuleDependencies(Lgroovyjarjarantlr4/v4/tool/Grammar;)Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ")",
            "Ljava/util/Map<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            "Ljava/util/Set<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;>;"
        }
    .end annotation

    .line 70
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/semantics/UseDefAnalyzer;->getRuleDependencies(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/Collection;)Ljava/util/Map;

    move-result-object p0

    return-object p0
.end method

.method public static getRuleDependencies(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/Collection;)Ljava/util/Map;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;)",
            "Ljava/util/Map<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            "Ljava/util/Set<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;>;"
        }
    .end annotation

    .line 78
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 80
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 81
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/16 v3, 0x42

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getNodesWithType(I)Ljava/util/List;

    move-result-object v2

    .line 82
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 83
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Set;

    if-nez v4, :cond_1

    .line 85
    new-instance v4, Ljava/util/HashSet;

    invoke-direct {v4}, Ljava/util/HashSet;-><init>()V

    .line 86
    invoke-interface {v0, v1, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 88
    :cond_1
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v3

    invoke-interface {v4, v3}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_2
    return-object v0
.end method

.method public static getRuleDependencies(Lgroovyjarjarantlr4/v4/tool/LexerGrammar;Ljava/lang/String;)Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/LexerGrammar;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/Map<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            "Ljava/util/Set<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;>;"
        }
    .end annotation

    .line 74
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Collection;

    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/semantics/UseDefAnalyzer;->getRuleDependencies(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/Collection;)Ljava/util/Map;

    move-result-object p0

    return-object p0
.end method

.method public static trackTokenRuleRefsInActions(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 12

    .line 32
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    const/4 v2, 0x1

    move v8, v2

    .line 33
    :goto_0
    iget v2, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    if-gt v8, v2, :cond_0

    .line 34
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v9, v2, v8

    .line 35
    iget-object v2, v9, Lgroovyjarjarantlr4/v4/tool/Alternative;->actions:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v10

    :goto_1
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    move-object v6, v2

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 36
    new-instance v11, Lgroovyjarjarantlr4/v4/semantics/ActionSniffer;

    iget-object v7, v6, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    move-object v2, v11

    move-object v3, p0

    move-object v4, v1

    move-object v5, v9

    invoke-direct/range {v2 .. v7}, Lgroovyjarjarantlr4/v4/semantics/ActionSniffer;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/Alternative;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/runtime/Token;)V

    .line 37
    invoke-virtual {v11}, Lgroovyjarjarantlr4/v4/semantics/ActionSniffer;->examineAction()V

    goto :goto_1

    :cond_1
    add-int/lit8 v8, v8, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method
