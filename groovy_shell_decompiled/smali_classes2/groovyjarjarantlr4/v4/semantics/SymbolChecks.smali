.class public Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;
.super Ljava/lang/Object;
.source "SymbolChecks.java"


# instance fields
.field actionScopeToActionNames:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end field

.field collector:Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;

.field public errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

.field g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field nameToRuleMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;"
        }
    .end annotation
.end field

.field protected final reservedNames:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field tokenIDs:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;)V
    .locals 2

    .line 61
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 49
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->nameToRuleMap:Ljava/util/Map;

    .line 50
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->tokenIDs:Ljava/util/Set;

    .line 51
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->actionScopeToActionNames:Ljava/util/Map;

    .line 55
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->reservedNames:Ljava/util/Set;

    .line 58
    invoke-static {}, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory;->getCommonConstants()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 62
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 63
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->collector:Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;

    .line 64
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    .line 66
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->tokenIDRefs:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 67
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->tokenIDs:Ljava/util/Set;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-interface {v0, p2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_0
    return-void
.end method

.method private checkForOverlap(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/util/List;Ljava/util/List;)V
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    move-object/from16 v0, p2

    move-object/from16 v1, p3

    const/4 v2, 0x0

    move v3, v2

    .line 446
    :goto_0
    invoke-interface/range {p4 .. p4}, Ljava/util/List;->size()I

    move-result v4

    if-ge v3, v4, :cond_3

    if-ne v0, v1, :cond_0

    add-int/lit8 v4, v3, 0x1

    move-object/from16 v5, p4

    goto :goto_1

    :cond_0
    move-object/from16 v5, p4

    move v4, v2

    .line 448
    :goto_1
    invoke-interface {v5, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    .line 449
    :goto_2
    invoke-interface/range {p5 .. p5}, Ljava/util/List;->size()I

    move-result v7

    if-ge v4, v7, :cond_2

    move-object/from16 v7, p5

    .line 450
    invoke-interface {v7, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Ljava/lang/String;

    .line 451
    invoke-virtual {v6, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_1

    move-object/from16 v9, p0

    .line 452
    iget-object v10, v9, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v11, Lgroovyjarjarantlr4/v4/tool/ErrorType;->TOKEN_UNREACHABLE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    move-object/from16 v12, p1

    iget-object v13, v12, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v14, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v14, v2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v14

    check-cast v14, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iget-object v14, v14, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    const/4 v15, 0x3

    new-array v15, v15, [Ljava/lang/Object;

    iget-object v5, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v5, v15, v2

    const/4 v5, 0x1

    aput-object v8, v15, v5

    const/4 v5, 0x2

    iget-object v8, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v8, v15, v5

    invoke-virtual {v10, v11, v13, v14, v15}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_3

    :cond_1
    move-object/from16 v9, p0

    move-object/from16 v12, p1

    :goto_3
    add-int/lit8 v4, v4, 0x1

    move-object/from16 v5, p4

    goto :goto_2

    :cond_2
    move-object/from16 v9, p0

    move-object/from16 v12, p1

    move-object/from16 v7, p5

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_3
    move-object/from16 v9, p0

    return-void
.end method

.method private checkForTypeMismatch(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/LabelElementPair;Lgroovyjarjarantlr4/v4/tool/LabelElementPair;)V
    .locals 11

    .line 195
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    iget-object v1, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    const/4 v2, 0x1

    const/4 v3, 0x2

    const/4 v4, 0x0

    if-eq v0, v1, :cond_1

    .line 201
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;

    if-eqz v0, :cond_0

    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    goto :goto_0

    :cond_0
    iget-object v0, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 204
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v5, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_TYPE_CONFLICT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    new-array v7, v3, [Ljava/lang/Object;

    iget-object v8, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v8}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v8

    aput-object v8, v7, v4

    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v9, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v8

    const-string v9, "!="

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    iget-object v9, p2, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    aput-object v8, v7, v2

    invoke-virtual {v1, v5, v6, v0, v7}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 212
    :cond_1
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->element:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    .line 213
    iget-object v1, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->element:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    .line 214
    iget-object v5, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/LabelType;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_2

    iget-object v5, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/LabelType;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_4

    .line 216
    :cond_2
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v5, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v5

    if-eqz v5, :cond_3

    .line 217
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v5, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object v0

    .line 220
    :cond_3
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v5, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v5

    if-eqz v5, :cond_4

    .line 221
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v5, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object v1

    .line 225
    :cond_4
    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_a

    iget-object v5, p2, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/LabelType;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_5

    iget-object v5, p2, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/LabelType;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_a

    :cond_5
    iget-object v5, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/LabelType;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_6

    iget-object v5, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/LabelType;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_a

    .line 229
    :cond_6
    instance-of v5, p1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;

    if-eqz v5, :cond_7

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {p1, v4}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p1

    goto :goto_1

    :cond_7
    iget-object p1, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 232
    :goto_1
    iget-object v5, p2, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr4/v4/tool/LabelType;->equals(Ljava/lang/Object;)Z

    move-result v5

    const-string v6, "+="

    const-string v7, "="

    if-eqz v5, :cond_8

    move-object v5, v6

    goto :goto_2

    :cond_8
    move-object v5, v7

    .line 233
    :goto_2
    iget-object v8, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->type:Lgroovyjarjarantlr4/v4/tool/LabelType;

    sget-object v9, Lgroovyjarjarantlr4/v4/tool/LabelType;->RULE_LIST_LABEL:Lgroovyjarjarantlr4/v4/tool/LabelType;

    invoke-virtual {v8, v9}, Lgroovyjarjarantlr4/v4/tool/LabelType;->equals(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_9

    goto :goto_3

    :cond_9
    move-object v6, v7

    .line 234
    :goto_3
    iget-object v7, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v8, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_TYPE_CONFLICT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v9, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v9, v9, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    new-array v3, v3, [Ljava/lang/Object;

    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    iget-object p3, p3, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {v10, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    aput-object p3, v3, v4

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    aput-object p2, v3, v2

    invoke-virtual {v7, v8, v9, p1, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    :cond_a
    return-void
.end method

.method private checkLabelPairs(Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/util/Map;Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/LabelElementPair;",
            ">;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/LabelElementPair;",
            ">;)V"
        }
    .end annotation

    .line 159
    invoke-interface {p3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :goto_0
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    .line 160
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p0, p1, v1}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkForLabelConflict(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 161
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    .line 162
    invoke-interface {p2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    if-nez v2, :cond_0

    .line 164
    invoke-interface {p2, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 167
    :cond_0
    invoke-direct {p0, p1, v2, v0}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkForTypeMismatch(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/LabelElementPair;Lgroovyjarjarantlr4/v4/tool/LabelElementPair;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method private findAltLabelName(Lgroovyjarjarantlr4/runtime/tree/CommonTree;)Ljava/lang/String;
    .locals 2

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 176
    :cond_0
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    if-eqz v0, :cond_3

    .line 177
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 178
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v1, :cond_1

    .line 179
    iget-object p1, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->altLabel:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 181
    :cond_1
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    if-eqz v1, :cond_2

    .line 182
    iget-object p1, v0, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->leftRecursiveAltInfo:Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    invoke-virtual {p1}, Ljava/lang/String;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 185
    :cond_2
    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->findAltLabelName(Lgroovyjarjarantlr4/runtime/tree/CommonTree;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 189
    :cond_3
    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->findAltLabelName(Lgroovyjarjarantlr4/runtime/tree/CommonTree;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private getSingleTokenValues(Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/util/List;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 396
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 397
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    array-length v1, p1

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_6

    aget-object v4, p1, v3

    if-eqz v4, :cond_5

    .line 400
    iget-object v5, v4, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getChildCount()I

    move-result v5

    const/4 v6, 0x2

    const/4 v7, 0x1

    if-ne v5, v6, :cond_0

    iget-object v5, v4, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-virtual {v5, v2}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    instance-of v5, v5, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    if-eqz v5, :cond_0

    iget-object v5, v4, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-virtual {v5, v7}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v5

    instance-of v5, v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz v5, :cond_0

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    invoke-virtual {v4, v2}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    goto :goto_1

    :cond_0
    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 405
    :goto_1
    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getTokenStartIndex()I

    move-result v5

    const/4 v6, -0x1

    if-ne v5, v6, :cond_1

    goto :goto_4

    .line 411
    :cond_1
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    move v6, v2

    .line 412
    :goto_2
    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChildCount()I

    move-result v8

    if-ge v6, v8, :cond_4

    .line 413
    invoke-interface {v4, v6}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v8

    .line 414
    instance-of v9, v8, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;

    if-nez v9, :cond_2

    goto :goto_3

    .line 419
    :cond_2
    check-cast v8, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;

    .line 420
    iget-object v9, v8, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v9}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v9

    const/16 v10, 0x3e

    if-eq v9, v10, :cond_3

    goto :goto_3

    .line 425
    :cond_3
    iget-object v8, v8, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v8}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v8

    .line 426
    invoke-virtual {v8}, Ljava/lang/String;->length()I

    move-result v9

    sub-int/2addr v9, v7

    invoke-virtual {v8, v7, v9}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v6, v6, 0x1

    goto :goto_2

    :cond_4
    move v7, v2

    :goto_3
    if-nez v7, :cond_5

    .line 431
    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-interface {v0, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_5
    :goto_4
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_6
    return-object v0
.end method


# virtual methods
.method public checkActionRedefinitions(Ljava/util/List;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;)V"
        }
    .end annotation

    if-nez p1, :cond_0

    return-void

    .line 85
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getDefaultActionScope()Ljava/lang/String;

    move-result-object v0

    .line 88
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/4 v2, 0x0

    .line 89
    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 90
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v4

    const/4 v5, 0x2

    const/4 v6, 0x1

    if-ne v4, v5, :cond_1

    .line 91
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    .line 94
    :cond_1
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    .line 95
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v1

    .line 97
    :goto_1
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->actionScopeToActionNames:Ljava/util/Map;

    invoke-interface {v4, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/Set;

    if-nez v4, :cond_2

    .line 99
    new-instance v4, Ljava/util/HashSet;

    invoke-direct {v4}, Ljava/util/HashSet;-><init>()V

    .line 100
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->actionScopeToActionNames:Ljava/util/Map;

    invoke-interface {v5, v0, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 102
    :cond_2
    invoke-interface {v4, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_3

    .line 103
    invoke-interface {v4, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 106
    :cond_3
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v5, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ACTION_REDEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v7, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v7, v7, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v6, v6, [Ljava/lang/Object;

    aput-object v1, v6, v2

    invoke-virtual {v4, v5, v7, v3, v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_4
    return-void
.end method

.method protected checkDeclarationRuleConflicts(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Ljava/util/Set;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            "Lgroovyjarjarantlr4/v4/tool/AttributeDict;",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/ErrorType;",
            ")V"
        }
    .end annotation

    if-nez p2, :cond_0

    return-void

    .line 291
    :cond_0
    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    invoke-virtual {p2}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_1
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 292
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Attribute;->name:Ljava/lang/String;

    invoke-interface {p3, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 293
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v3, v0, Lgroovyjarjarantlr4/v4/tool/Attribute;->token:Lgroovyjarjarantlr4/runtime/Token;

    const/4 v4, 0x0

    if-eqz v3, :cond_2

    iget-object v3, v0, Lgroovyjarjarantlr4/v4/tool/Attribute;->token:Lgroovyjarjarantlr4/runtime/Token;

    goto :goto_1

    :cond_2
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    :goto_1
    const/4 v5, 0x2

    new-array v5, v5, [Ljava/lang/Object;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Attribute;->name:Ljava/lang/String;

    aput-object v0, v5, v4

    const/4 v0, 0x1

    iget-object v4, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v4, v5, v0

    invoke-virtual {v1, p4, v2, v3, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_3
    return-void
.end method

.method public checkForAttributeConflicts(Lgroovyjarjarantlr4/v4/tool/Rule;)V
    .locals 3

    .line 272
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->nameToRuleMap:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v1

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ARG_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkDeclarationRuleConflicts(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Ljava/util/Set;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    .line 273
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->tokenIDs:Ljava/util/Set;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ARG_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkDeclarationRuleConflicts(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Ljava/util/Set;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    .line 275
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->nameToRuleMap:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v1

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RETVAL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkDeclarationRuleConflicts(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Ljava/util/Set;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    .line 276
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->tokenIDs:Ljava/util/Set;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RETVAL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkDeclarationRuleConflicts(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Ljava/util/Set;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    .line 278
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->nameToRuleMap:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v1

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LOCAL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkDeclarationRuleConflicts(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Ljava/util/Set;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    .line 279
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->tokenIDs:Ljava/util/Set;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LOCAL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkDeclarationRuleConflicts(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Ljava/util/Set;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    .line 281
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RETVAL_CONFLICTS_WITH_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkLocalConflictingDeclarations(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    .line 282
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LOCAL_CONFLICTS_WITH_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkLocalConflictingDeclarations(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    .line 283
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LOCAL_CONFLICTS_WITH_RETVAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkLocalConflictingDeclarations(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    return-void
.end method

.method public checkForLabelConflict(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 10

    .line 244
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    .line 245
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->nameToRuleMap:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x2

    if-eqz v1, :cond_0

    .line 246
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_RULE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 247
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v7, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v8, v4, [Ljava/lang/Object;

    aput-object v0, v8, v3

    iget-object v9, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v9, v8, v2

    invoke-virtual {v5, v1, v6, v7, v8}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 250
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->tokenIDs:Ljava/util/Set;

    invoke-interface {v1, v0}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 251
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 252
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v7, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v8, v4, [Ljava/lang/Object;

    aput-object v0, v8, v3

    iget-object v9, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v9, v8, v2

    invoke-virtual {v5, v1, v6, v7, v8}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 255
    :cond_1
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v1, :cond_2

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object v1

    if-eqz v1, :cond_2

    .line 256
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 257
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v7, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v8, v4, [Ljava/lang/Object;

    aput-object v0, v8, v3

    iget-object v9, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v9, v8, v2

    invoke-virtual {v5, v1, v6, v7, v8}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 260
    :cond_2
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v1, :cond_3

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object v1

    if-eqz v1, :cond_3

    .line 261
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_RETVAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 262
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v7, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v8, v4, [Ljava/lang/Object;

    aput-object v0, v8, v3

    iget-object v9, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v9, v8, v2

    invoke-virtual {v5, v1, v6, v7, v8}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 265
    :cond_3
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v1, :cond_4

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object v1

    if-eqz v1, :cond_4

    .line 266
    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->LABEL_CONFLICTS_WITH_LOCAL:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    .line 267
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v4, v4, [Ljava/lang/Object;

    aput-object v0, v4, v3

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object p1, v4, v2

    invoke-virtual {v5, v1, v6, p2, v4}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    :cond_4
    return-void
.end method

.method public checkForLabelConflicts(Ljava/util/Collection;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;)V"
        }
    .end annotation

    .line 120
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_7

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 121
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkForAttributeConflicts(Lgroovyjarjarantlr4/v4/tool/Rule;)V

    .line 123
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    const/4 v2, 0x1

    .line 124
    :goto_0
    iget v3, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    if-gt v2, v3, :cond_0

    .line 125
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v3, v3, v2

    .line 126
    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Alternative;->labelDefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->values()Ljava/util/Collection;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_1
    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_6

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/List;

    .line 127
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Rule;->hasAltSpecificContexts()Z

    move-result v5

    if-eqz v5, :cond_5

    .line 129
    new-instance v5, Ljava/util/HashMap;

    invoke-direct {v5}, Ljava/util/HashMap;-><init>()V

    .line 130
    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :cond_2
    :goto_2
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_4

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    .line 131
    iget-object v7, v6, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;->label:Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-direct {p0, v7}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->findAltLabelName(Lgroovyjarjarantlr4/runtime/tree/CommonTree;)Ljava/lang/String;

    move-result-object v7

    if-eqz v7, :cond_2

    .line 134
    invoke-interface {v5, v7}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_3

    .line 135
    invoke-interface {v5, v7}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/util/List;

    goto :goto_3

    .line 138
    :cond_3
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    .line 139
    invoke-interface {v5, v7, v8}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-object v7, v8

    .line 141
    :goto_3
    invoke-interface {v7, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 145
    :cond_4
    invoke-interface {v5}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v4

    invoke-interface {v4}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_4
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_1

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/util/List;

    .line 146
    invoke-interface {v1}, Ljava/util/Map;->clear()V

    .line 147
    invoke-direct {p0, v0, v1, v5}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkLabelPairs(Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/util/Map;Ljava/util/List;)V

    goto :goto_4

    .line 151
    :cond_5
    invoke-direct {p0, v0, v1, v4}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkLabelPairs(Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/util/Map;Ljava/util/List;)V

    goto :goto_1

    :cond_6
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_7
    return-void
.end method

.method public checkForModeConflicts(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 10

    .line 328
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 329
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    .line 330
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    const-string v3, "DEFAULT_MODE"

    .line 331
    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-nez v3, :cond_1

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->reservedNames:Ljava/util/Set;

    invoke-interface {v3, v2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 332
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 333
    iget-object v6, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v7, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MODE_CONFLICTS_WITH_COMMON_CONSTANTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v8, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v3

    new-array v9, v5, [Ljava/lang/Object;

    aput-object v2, v9, v4

    invoke-virtual {v6, v7, v8, v3, v9}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 336
    :cond_1
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenType(Ljava/lang/String;)I

    move-result v3

    if-eqz v3, :cond_0

    .line 337
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 338
    iget-object v6, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v7, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MODE_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v8, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v3

    new-array v5, v5, [Ljava/lang/Object;

    aput-object v2, v5, v4

    invoke-virtual {v6, v7, v8, v3, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_2
    return-void
.end method

.method public checkForQualifiedRuleIssues(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;)V"
        }
    .end annotation

    .line 479
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_0
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/4 v1, 0x0

    .line 480
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/4 v3, 0x1

    .line 481
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 482
    iget-object v4, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v6, "."

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    const-string v6, "semantics"

    invoke-virtual {v4, v6, v5}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 483
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p1, v4}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getImportedGrammar(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v4

    const/4 v5, 0x2

    if-nez v4, :cond_1

    .line 485
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NO_SUCH_GRAMMAR_SCOPE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v7, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v8, v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v5, v5, [Ljava/lang/Object;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v5, v1

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    aput-object v0, v5, v3

    invoke-virtual {v4, v6, v7, v8, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    .line 490
    :cond_1
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p1, v4, v6}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v4

    if-nez v4, :cond_0

    .line 491
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NO_SUCH_RULE_IN_SCOPE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v7, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v8, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v5, v5, [Ljava/lang/Object;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v5, v1

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    aput-object v0, v5, v3

    invoke-virtual {v4, v6, v7, v8, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto/16 :goto_0

    :cond_2
    return-void
.end method

.method public checkForUnreachableTokens(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 13

    .line 355
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 356
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    .line 357
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    .line 359
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 360
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    const/4 v4, 0x0

    move v5, v4

    .line 361
    :goto_0
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v6

    if-ge v5, v6, :cond_2

    .line 362
    invoke-interface {v1, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 364
    invoke-direct {p0, v6}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->getSingleTokenValues(Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/util/List;

    move-result-object v7

    if-eqz v7, :cond_1

    .line 365
    invoke-interface {v7}, Ljava/util/List;->size()I

    move-result v8

    if-lez v8, :cond_1

    .line 366
    invoke-interface {v2, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 367
    invoke-interface {v3, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 372
    :cond_2
    :goto_1
    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v1

    if-ge v4, v1, :cond_0

    .line 373
    invoke-interface {v3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    .line 374
    invoke-interface {v2, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    move-object v11, v5

    check-cast v11, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 375
    invoke-interface {v3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    move-object v10, v5

    check-cast v10, Ljava/util/List;

    move-object v5, p0

    move-object v6, p1

    move-object v7, v11

    move-object v8, v11

    move-object v9, v1

    invoke-direct/range {v5 .. v10}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkForOverlap(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/util/List;Ljava/util/List;)V

    .line 378
    invoke-virtual {v11}, Lgroovyjarjarantlr4/v4/tool/Rule;->isFragment()Z

    move-result v5

    if-nez v5, :cond_4

    add-int/lit8 v5, v4, 0x1

    move v12, v5

    .line 379
    :goto_2
    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v5

    if-ge v12, v5, :cond_4

    .line 380
    invoke-interface {v2, v12}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 381
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/tool/Rule;->isFragment()Z

    move-result v5

    if-nez v5, :cond_3

    .line 382
    invoke-interface {v2, v12}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    move-object v8, v5

    check-cast v8, Lgroovyjarjarantlr4/v4/tool/Rule;

    invoke-interface {v3, v12}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    move-object v10, v5

    check-cast v10, Ljava/util/List;

    move-object v5, p0

    move-object v6, p1

    move-object v7, v11

    move-object v9, v1

    invoke-direct/range {v5 .. v10}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkForOverlap(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/util/List;Ljava/util/List;)V

    :cond_3
    add-int/lit8 v12, v12, 0x1

    goto :goto_2

    :cond_4
    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_5
    return-void
.end method

.method protected checkLocalConflictingDeclarations(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Lgroovyjarjarantlr4/v4/tool/AttributeDict;Lgroovyjarjarantlr4/v4/tool/ErrorType;)V
    .locals 6

    if-eqz p2, :cond_2

    if-nez p3, :cond_0

    goto :goto_2

    .line 308
    :cond_0
    invoke-virtual {p2, p3}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->intersection(Lgroovyjarjarantlr4/v4/tool/AttributeDict;)Ljava/util/Set;

    move-result-object p3

    .line 309
    invoke-interface {p3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :goto_0
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    .line 310
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object v3

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Attribute;->token:Lgroovyjarjarantlr4/runtime/Token;

    const/4 v4, 0x0

    if-eqz v3, :cond_1

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->get(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object v3

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Attribute;->token:Lgroovyjarjarantlr4/runtime/Token;

    goto :goto_1

    :cond_1
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    :goto_1
    const/4 v5, 0x2

    new-array v5, v5, [Ljava/lang/Object;

    aput-object v0, v5, v4

    const/4 v0, 0x1

    iget-object v4, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v4, v5, v0

    invoke-virtual {v1, p4, v2, v3, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_2
    :goto_2
    return-void
.end method

.method protected checkReservedNames(Ljava/util/Collection;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ">;)V"
        }
    .end annotation

    .line 320
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 321
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->reservedNames:Ljava/util/Set;

    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-interface {v1, v2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 322
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RESERVED_RULE_NAME:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v4, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/4 v5, 0x0

    invoke-virtual {v4, v5}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v4

    const/4 v6, 0x1

    new-array v6, v6, [Ljava/lang/Object;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    aput-object v0, v6, v5

    invoke-virtual {v1, v2, v3, v4, v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public checkRuleArgs(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    return-void

    .line 462
    :cond_0
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_1
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 463
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    .line 464
    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v2

    const/16 v3, 0x8

    .line 465
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-eqz v3, :cond_3

    if-eqz v2, :cond_2

    .line 466
    iget-object v6, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-nez v6, :cond_3

    .line 467
    :cond_2
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RULE_HAS_NO_ARGS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v6, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v5, v5, [Ljava/lang/Object;

    aput-object v1, v5, v4

    invoke-virtual {v2, v3, v6, v0, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_3
    if-nez v3, :cond_1

    if-eqz v2, :cond_1

    .line 471
    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v2, :cond_1

    .line 472
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->MISSING_RULE_ARGS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v6, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v5, v5, [Ljava/lang/Object;

    aput-object v1, v5, v4

    invoke-virtual {v2, v3, v6, v0, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_4
    return-void
.end method

.method public process()V
    .locals 4

    .line 75
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    if-eqz v0, :cond_0

    .line 76
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->nameToRuleMap:Ljava/util/Map;

    iget-object v3, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-interface {v2, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 78
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkReservedNames(Ljava/util/Collection;)V

    .line 79
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->collector:Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->namedActions:Ljava/util/List;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkActionRedefinitions(Ljava/util/List;)V

    .line 80
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkForLabelConflicts(Ljava/util/Collection;)V

    return-void
.end method
