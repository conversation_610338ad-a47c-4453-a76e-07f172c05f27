.class final Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks$1;
.super Lorg/stringtemplate/v4/misc/MultiMap;
.source "BasicSemanticChecks.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/stringtemplate/v4/misc/MultiMap<",
        "Ljava/lang/Integer;",
        "Ljava/lang/Integer;",
        ">;"
    }
.end annotation


# direct methods
.method constructor <init>()V
    .locals 2

    .line 67
    invoke-direct {p0}, Lorg/stringtemplate/v4/misc/MultiMap;-><init>()V

    const/16 v0, 0x1f

    .line 69
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {p0, v0, v0}, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks$1;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    const/16 v1, 0x51

    .line 70
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks$1;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    const/16 v0, 0x2c

    .line 72
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {p0, v0, v0}, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks$1;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 73
    invoke-virtual {p0, v0, v1}, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks$1;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 75
    invoke-virtual {p0, v1, v1}, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks$1;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method
