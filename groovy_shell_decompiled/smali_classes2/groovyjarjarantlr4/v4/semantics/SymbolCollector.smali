.class public Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;
.super Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;
.source "SymbolCollector.java"


# instance fields
.field public channelDefs:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

.field public errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

.field public g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field namedActions:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public qualifiedRulerefs:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public rulerefs:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public strings:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public terminals:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public tokenIDRefs:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field

.field public tokensDefs:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 1

    .line 54
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;-><init>()V

    .line 38
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->rulerefs:Ljava/util/List;

    .line 39
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->qualifiedRulerefs:Ljava/util/List;

    .line 40
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->terminals:Ljava/util/List;

    .line 41
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->tokenIDRefs:Ljava/util/List;

    .line 42
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->strings:Ljava/util/Set;

    .line 43
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->tokensDefs:Ljava/util/List;

    .line 44
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->channelDefs:Ljava/util/List;

    .line 47
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->namedActions:Ljava/util/List;

    .line 55
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 56
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    return-void
.end method

.method private setActionResolver(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 2

    .line 185
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    if-eqz v0, :cond_0

    .line 186
    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    iget v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    aget-object v0, v0, v1

    iput-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    :cond_0
    return-void
.end method


# virtual methods
.method public actionInAlt(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 2

    .line 107
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->defineActionInAlt(ILgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    .line 108
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    iget v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    aget-object v0, v0, v1

    iput-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    return-void
.end method

.method public blockOption(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 0

    .line 175
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->setActionResolver(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-void
.end method

.method public defineChannel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 1

    .line 79
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->channelDefs:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public defineToken(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 1

    .line 72
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->terminals:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 73
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->tokenIDRefs:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 74
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->tokensDefs:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public discoverLexerRule(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")V"
        }
    .end annotation

    .line 97
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    return-void
.end method

.method public discoverOuterAlt(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;)V
    .locals 2

    .line 102
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    iget v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    aget-object v0, v0, v1

    iput-object p1, v0, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    return-void
.end method

.method public discoverRule(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Ljava/util/List;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")V"
        }
    .end annotation

    .line 90
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    return-void
.end method

.method public elementOption(Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 0

    .line 180
    invoke-direct {p0, p3}, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->setActionResolver(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-void
.end method

.method public finallyAction(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 1

    .line 126
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iput-object p1, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->finallyAction:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 127
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iput-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    return-void
.end method

.method public getErrorManager()Lgroovyjarjarantlr4/v4/tool/ErrorManager;
    .locals 1

    .line 60
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    return-object v0
.end method

.method public globalNamedAction(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 0

    .line 66
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->namedActions:Ljava/util/List;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 67
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iput-object p1, p3, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    return-void
.end method

.method public grammarOption(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 0

    .line 165
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->setActionResolver(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-void
.end method

.method public label(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 2

    .line 132
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result p1

    invoke-direct {v0, v1, p2, p3, p1}, Lgroovyjarjarantlr4/v4/tool/LabelElementPair;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;I)V

    .line 133
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    iget p3, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    aget-object p1, p1, p3

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Alternative;->labelDefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method

.method public process(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 0

    .line 62
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->visitGrammar(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-void
.end method

.method public ruleCatch(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 1

    .line 119
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 120
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->exceptions:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 121
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iput-object p1, p2, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    return-void
.end method

.method public ruleOption(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 0

    .line 170
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->setActionResolver(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-void
.end method

.method public ruleRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 1

    .line 157
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->rulerefs:Ljava/util/List;

    invoke-interface {p2, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 158
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    if-eqz p2, :cond_0

    .line 159
    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    iget v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    aget-object p2, p2, v0

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/Alternative;->ruleRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public sempredInAlt(Lgroovyjarjarantlr4/v4/tool/ast/PredAST;)V
    .locals 2

    .line 113
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->definePredicateInAlt(ILgroovyjarjarantlr4/v4/tool/ast/PredAST;)V

    .line 114
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    iget v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    aget-object v0, v0, v1

    iput-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    return-void
.end method

.method public stringRef(Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;)V
    .locals 2

    .line 138
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->terminals:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 139
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->strings:Ljava/util/Set;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 140
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    if-eqz v0, :cond_0

    .line 141
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    iget v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    aget-object v0, v0, v1

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Alternative;->tokenRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public tokenRef(Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;)V
    .locals 2

    .line 147
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->terminals:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 148
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->tokenIDRefs:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 149
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentRule:Lgroovyjarjarantlr4/v4/tool/Rule;

    if-eqz v0, :cond_0

    .line 150
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    iget v1, p0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->currentOuterAltNumber:I

    aget-object v0, v0, v1

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Alternative;->tokenRefs:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->map(Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_0
    return-void
.end method
