.class public Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;
.super Ljava/lang/Object;
.source "SemanticPipeline.java"


# instance fields
.field public g:Lgroovyjarjarantlr4/v4/tool/Grammar;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 0

    .line 54
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 55
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    return-void
.end method


# virtual methods
.method assignChannelTypes(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;)V"
        }
    .end annotation

    .line 277
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOutermostGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    .line 278
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 279
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    .line 288
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenType(Ljava/lang/String;)I

    move-result v3

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-eqz v3, :cond_0

    .line 289
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CHANNEL_CONFLICTS_WITH_TOKEN:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v7, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v8, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v9, v5, [Ljava/lang/Object;

    aput-object v2, v9, v4

    invoke-virtual {v3, v6, v7, v8, v9}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 292
    :cond_0
    sget-object v3, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory;->COMMON_CONSTANTS:Ljava/util/Map;

    invoke-interface {v3, v2}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 293
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CHANNEL_CONFLICTS_WITH_COMMON_CONSTANTS:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v7, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v8, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v9, v5, [Ljava/lang/Object;

    aput-object v2, v9, v4

    invoke-virtual {v3, v6, v7, v8, v9}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 296
    :cond_1
    instance-of v3, v0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    if-eqz v3, :cond_2

    .line 297
    move-object v3, v0

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    .line 298
    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    .line 299
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CHANNEL_CONFLICTS_WITH_MODE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v7, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v8, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v5, v5, [Ljava/lang/Object;

    aput-object v2, v5, v4

    invoke-virtual {v3, v6, v7, v8, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 303
    :cond_2
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineChannelName(Ljava/lang/String;)I

    goto :goto_0

    :cond_3
    return-void
.end method

.method assignLexerTokenTypes(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;)V"
        }
    .end annotation

    .line 159
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOutermostGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    .line 160
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_0
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 162
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isTokenName(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 163
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineTokenName(Ljava/lang/String;)I

    goto :goto_0

    .line 170
    :cond_1
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_2
    :goto_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 171
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Rule;->isFragment()Z

    move-result v2

    if-nez v2, :cond_2

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->hasTypeOrMoreCommand(Lgroovyjarjarantlr4/v4/tool/Rule;)Z

    move-result v2

    if-nez v2, :cond_2

    .line 172
    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineTokenName(Ljava/lang/String;)I

    goto :goto_1

    .line 177
    :cond_3
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getStringLiteralAliasesFromLexerRules(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)Ljava/util/List;

    move-result-object p1

    .line 178
    new-instance p2, Ljava/util/HashSet;

    invoke-direct {p2}, Ljava/util/HashSet;-><init>()V

    if-eqz p1, :cond_7

    .line 180
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    .line 181
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 182
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem2()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 183
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_4

    .line 184
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v2, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineTokenAlias(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_2

    .line 188
    :cond_4
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-interface {p2, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 191
    :cond_5
    invoke-interface {p2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_6
    :goto_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_7

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/String;

    .line 194
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v1, p2}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Integer;

    if-eqz v1, :cond_6

    .line 195
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v2

    if-lez v2, :cond_6

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v2

    iget-object v3, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v2, v3, :cond_6

    iget-object v2, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v3

    invoke-interface {v2, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_6

    .line 196
    iget-object p2, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->typeToStringLiteralList:Ljava/util/List;

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    const/4 v2, 0x0

    invoke-interface {p2, v1, v2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    goto :goto_3

    :cond_7
    return-void
.end method

.method assignTokenTypes(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;)V"
        }
    .end annotation

    .line 237
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_1

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 238
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1, v3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenType(Ljava/lang/String;)I

    move-result v3

    if-eqz v3, :cond_0

    .line 239
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;->TOKEN_NAME_REASSIGNMENT:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v5, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v6, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v2, v2, [Ljava/lang/Object;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v7

    aput-object v7, v2, v1

    invoke-virtual {v3, v4, v5, v6, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 242
    :cond_0
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineTokenName(Ljava/lang/String;)I

    goto :goto_0

    .line 246
    :cond_1
    invoke-interface {p3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_3

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 247
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenType(Ljava/lang/String;)I

    move-result v0

    if-nez v0, :cond_2

    .line 248
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->IMPLICIT_TOKEN_DEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v4, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v5, p3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v6, v2, [Ljava/lang/Object;

    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v7

    aput-object v7, v6, v1

    invoke-virtual {v0, v3, v4, v5, v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    .line 251
    :cond_2
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p1, p3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineTokenName(Ljava/lang/String;)I

    goto :goto_1

    .line 255
    :cond_3
    invoke-interface {p4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_4
    :goto_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_6

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 256
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result p4

    const/16 v0, 0x3e

    if-eq p4, v0, :cond_5

    goto :goto_2

    .line 260
    :cond_5
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p4

    invoke-virtual {p1, p4}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenType(Ljava/lang/String;)I

    move-result p4

    if-nez p4, :cond_4

    .line 261
    iget-object p4, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p4, p4, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorType;->IMPLICIT_STRING_DEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iget-object v4, p3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    new-array v5, v2, [Ljava/lang/Object;

    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p3

    aput-object p3, v5, v1

    invoke-virtual {p4, v0, v3, v4, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_2

    .line 265
    :cond_6
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "tokens="

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    iget-object p4, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    const-string p4, "semantics"

    invoke-virtual {p2, p4, p3}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 266
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "strings="

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p4, p1}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method hasTypeOrMoreCommand(Lgroovyjarjarantlr4/v4/tool/Rule;)Z
    .locals 6

    .line 204
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/4 v0, 0x0

    if-nez p1, :cond_0

    return v0

    :cond_0
    const/16 v1, 0x57

    .line 209
    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstDescendantWithType(I)Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-nez p1, :cond_1

    return v0

    :cond_1
    const/4 v1, 0x1

    move v2, v1

    .line 216
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v3

    if-ge v2, v3, :cond_4

    .line 217
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 218
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v4

    const/16 v5, 0x56

    if-ne v4, v5, :cond_2

    .line 219
    invoke-virtual {v3, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v3

    const-string v4, "type"

    invoke-virtual {v4, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_3

    return v1

    .line 223
    :cond_2
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v3

    const-string v4, "more"

    invoke-virtual {v4, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_3

    return v1

    :cond_3
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_4
    return v0
.end method

.method identifyStartRules(Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;)V
    .locals 2

    .line 151
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->rulerefs:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 152
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    .line 153
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v0

    if-eqz v0, :cond_0

    const/4 v1, 0x0

    .line 154
    iput-boolean v1, v0, Lgroovyjarjarantlr4/v4/tool/Rule;->isStartRule:Z

    goto :goto_0

    :cond_1
    return-void
.end method

.method public process()V
    .locals 7

    .line 59
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-nez v0, :cond_0

    return-void

    .line 62
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 63
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;->process(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 66
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 67
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->contextASTs:Ljava/util/Map;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    if-nez v3, :cond_1

    .line 69
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 70
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Grammar;->contextASTs:Ljava/util/Map;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object v5

    invoke-interface {v4, v5, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 73
    :cond_1
    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->dupTree()Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    invoke-interface {v3, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 77
    :cond_2
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v1

    .line 78
    new-instance v2, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v2, v3, v0}, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/semantics/RuleCollector;)V

    .line 79
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/semantics/BasicSemanticChecks;->process()V

    .line 80
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v2

    if-le v2, v1, :cond_3

    return-void

    .line 83
    :cond_3
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v1

    .line 84
    new-instance v2, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    iget-object v4, v0, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v4

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 86
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleTransformer;->translateLeftRecursiveRules()V

    .line 89
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v2

    if-le v2, v1, :cond_4

    return-void

    .line 94
    :cond_4
    new-instance v1, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    iget-object v3, v0, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v1, v2, v3, v4}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;-><init>(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Ljava/util/Map;Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 95
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/analysis/LeftFactoringRuleTransformer;->translateLeftFactoredRules()V

    .line 98
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/semantics/RuleCollector;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 99
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineRule(Lgroovyjarjarantlr4/v4/tool/Rule;)Z

    goto :goto_1

    .line 103
    :cond_5
    new-instance v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 104
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->process(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 107
    new-instance v1, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v1, v2, v0}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;)V

    .line 108
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->process()V

    .line 110
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->namedActions:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_6

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 111
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v4, v3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->defineAction(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    goto :goto_2

    .line 115
    :cond_6
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_7
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_8

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/Rule;

    const/4 v4, 0x1

    .line 116
    :goto_3
    iget v5, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->numberOfAlts:I

    if-gt v4, v5, :cond_7

    .line 117
    iget-object v5, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v5, v5, v4

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/tool/Alternative;->ast:Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    iget-object v6, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->alt:[Lgroovyjarjarantlr4/v4/tool/Alternative;

    aget-object v6, v6, v4

    iput-object v6, v5, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->alt:Lgroovyjarjarantlr4/v4/tool/Alternative;

    add-int/lit8 v4, v4, 0x1

    goto :goto_3

    .line 122
    :cond_8
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->importTokensFromTokensFile()V

    .line 123
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v2

    if-eqz v2, :cond_9

    .line 124
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->tokensDefs:Ljava/util/List;

    invoke-virtual {p0, v2, v3}, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->assignLexerTokenTypes(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;)V

    goto :goto_4

    .line 127
    :cond_9
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->tokensDefs:Ljava/util/List;

    iget-object v4, v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->tokenIDRefs:Ljava/util/List;

    iget-object v5, v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->terminals:Ljava/util/List;

    invoke-virtual {p0, v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->assignTokenTypes(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V

    .line 131
    :goto_4
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkForModeConflicts(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 132
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkForUnreachableTokens(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 134
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->channelDefs:Ljava/util/List;

    invoke-virtual {p0, v2, v3}, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->assignChannelTypes(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;)V

    .line 137
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v3, v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->rulerefs:Ljava/util/List;

    invoke-virtual {v1, v2, v3}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkRuleArgs(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;)V

    .line 138
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->identifyStartRules(Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;)V

    .line 139
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/semantics/SymbolCollector;->qualifiedRulerefs:Ljava/util/List;

    invoke-virtual {v1, v2, v0}, Lgroovyjarjarantlr4/v4/semantics/SymbolChecks;->checkForQualifiedRuleIssues(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/List;)V

    .line 142
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/Tool;->getNumErrors()I

    move-result v0

    if-lez v0, :cond_a

    return-void

    .line 145
    :cond_a
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/semantics/AttributeChecks;->checkAllAttributeExpressions(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 147
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/semantics/UseDefAnalyzer;->trackTokenRuleRefsInActions(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    return-void
.end method
