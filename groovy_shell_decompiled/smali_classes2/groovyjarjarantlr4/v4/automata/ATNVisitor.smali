.class public Lgroovyjarjarantlr4/v4/automata/ATNVisitor;
.super Ljava/lang/Object;
.source "ATNVisitor.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 20
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V
    .locals 1

    .line 22
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/automata/ATNVisitor;->visit_(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Ljava/util/Set;)V

    return-void
.end method

.method public visitState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V
    .locals 0

    return-void
.end method

.method public visit_(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Ljava/util/Set;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;",
            "Ljava/util/Set<",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    .line 26
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {p2, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 27
    :cond_0
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-interface {p2, v0}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 29
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/automata/ATNVisitor;->visitState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V

    .line 30
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    .line 32
    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v2

    .line 33
    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p0, v2, p2}, Lgroovyjarjarantlr4/v4/automata/ATNVisitor;->visit_(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Ljava/util/Set;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method
