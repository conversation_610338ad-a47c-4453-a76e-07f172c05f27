.class synthetic Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$1;
.super Ljava/lang/Object;
.source "LexerATNFactory.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/automata/LexerATNFactory;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1008
    name = null
.end annotation


# static fields
.field static final synthetic $SwitchMap$org$antlr$v4$automata$LexerATNFactory$CharSetParseState$Mode:[I

.field static final synthetic $SwitchMap$org$antlr$v4$misc$EscapeSequenceParsing$Result$Type:[I


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 570
    invoke-static {}, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->values()[Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    move-result-object v0

    array-length v0, v0

    new-array v0, v0, [I

    sput-object v0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$1;->$SwitchMap$org$antlr$v4$automata$LexerATNFactory$CharSetParseState$Mode:[I

    const/4 v1, 0x1

    :try_start_0
    sget-object v2, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->NONE:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->ordinal()I

    move-result v2

    aput v1, v0, v2
    :try_end_0
    .catch Ljava/lang/NoSuchFieldError; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    const/4 v0, 0x2

    :try_start_1
    sget-object v2, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$1;->$SwitchMap$org$antlr$v4$automata$LexerATNFactory$CharSetParseState$Mode:[I

    sget-object v3, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->ERROR:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->ordinal()I

    move-result v3

    aput v0, v2, v3
    :try_end_1
    .catch Ljava/lang/NoSuchFieldError; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    const/4 v2, 0x3

    :try_start_2
    sget-object v3, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$1;->$SwitchMap$org$antlr$v4$automata$LexerATNFactory$CharSetParseState$Mode:[I

    sget-object v4, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->PREV_CODE_POINT:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->ordinal()I

    move-result v4

    aput v2, v3, v4
    :try_end_2
    .catch Ljava/lang/NoSuchFieldError; {:try_start_2 .. :try_end_2} :catch_2

    :catch_2
    :try_start_3
    sget-object v3, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$1;->$SwitchMap$org$antlr$v4$automata$LexerATNFactory$CharSetParseState$Mode:[I

    sget-object v4, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->PREV_PROPERTY:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->ordinal()I

    move-result v4

    const/4 v5, 0x4

    aput v5, v3, v4
    :try_end_3
    .catch Ljava/lang/NoSuchFieldError; {:try_start_3 .. :try_end_3} :catch_3

    .line 480
    :catch_3
    invoke-static {}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->values()[Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    move-result-object v3

    array-length v3, v3

    new-array v3, v3, [I

    sput-object v3, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$1;->$SwitchMap$org$antlr$v4$misc$EscapeSequenceParsing$Result$Type:[I

    :try_start_4
    sget-object v4, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->INVALID:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->ordinal()I

    move-result v4

    aput v1, v3, v4
    :try_end_4
    .catch Ljava/lang/NoSuchFieldError; {:try_start_4 .. :try_end_4} :catch_4

    :catch_4
    :try_start_5
    sget-object v1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$1;->$SwitchMap$org$antlr$v4$misc$EscapeSequenceParsing$Result$Type:[I

    sget-object v3, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->CODE_POINT:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->ordinal()I

    move-result v3

    aput v0, v1, v3
    :try_end_5
    .catch Ljava/lang/NoSuchFieldError; {:try_start_5 .. :try_end_5} :catch_5

    :catch_5
    :try_start_6
    sget-object v0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$1;->$SwitchMap$org$antlr$v4$misc$EscapeSequenceParsing$Result$Type:[I

    sget-object v1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->PROPERTY:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->ordinal()I

    move-result v1

    aput v2, v0, v1
    :try_end_6
    .catch Ljava/lang/NoSuchFieldError; {:try_start_6 .. :try_end_6} :catch_6

    :catch_6
    return-void
.end method
