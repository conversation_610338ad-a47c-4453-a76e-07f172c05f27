.class Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;
.super Ljava/lang/Object;
.source "LexerATNFactory.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/automata/LexerATNFactory;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "CharSetParseState"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;
    }
.end annotation


# static fields
.field public static final ERROR:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;

.field public static final NONE:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;


# instance fields
.field public final inRange:Z

.field public final mode:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

.field public final prevCodePoint:I

.field public final prevProperty:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 401
    new-instance v0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;

    sget-object v1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->NONE:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    sget-object v2, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->EMPTY_SET:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v3, 0x0

    const/4 v4, -0x1

    invoke-direct {v0, v1, v3, v4, v2}, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;-><init>(Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;ZILgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    sput-object v0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->NONE:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;

    .line 402
    new-instance v0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;

    sget-object v1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;->ERROR:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    sget-object v2, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->EMPTY_SET:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-direct {v0, v1, v3, v4, v2}, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;-><init>(Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;ZILgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    sput-object v0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->ERROR:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;ZILgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V
    .locals 0

    .line 413
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 414
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->mode:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    .line 415
    iput-boolean p2, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->inRange:Z

    .line 416
    iput p3, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevCodePoint:I

    .line 417
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevProperty:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    .line 433
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 436
    :cond_0
    check-cast p1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;

    const/4 v0, 0x1

    if-ne p0, p1, :cond_1

    return v0

    .line 440
    :cond_1
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->mode:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    iget-object v3, p1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->mode:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    invoke-static {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-boolean v2, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->inRange:Z

    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    iget-boolean v3, p1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->inRange:Z

    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    invoke-static {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget v2, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevCodePoint:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    iget v3, p1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevCodePoint:I

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevProperty:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevProperty:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-static {v2, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    move v1, v0

    :cond_2
    return v1
.end method

.method public hashCode()I
    .locals 2

    .line 448
    invoke-static {}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->initialize()I

    move-result v0

    .line 449
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->mode:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(ILjava/lang/Object;)I

    move-result v0

    .line 450
    iget-boolean v1, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->inRange:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(ILjava/lang/Object;)I

    move-result v0

    .line 451
    iget v1, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevCodePoint:I

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(II)I

    move-result v0

    .line 452
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevProperty:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(ILjava/lang/Object;)I

    move-result v0

    const/4 v1, 0x4

    .line 453
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->finish(II)I

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    const/4 v0, 0x5

    new-array v0, v0, [Ljava/lang/Object;

    .line 422
    invoke-super {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->mode:Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState$Mode;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->inRange:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    iget v1, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevCodePoint:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x3

    aput-object v1, v0, v2

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory$CharSetParseState;->prevProperty:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    const-string v1, "%s mode=%s inRange=%s prevCodePoint=%d prevProperty=%s"

    invoke-static {v1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
