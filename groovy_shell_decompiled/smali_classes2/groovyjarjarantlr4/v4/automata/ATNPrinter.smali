.class public Lgroovyjarjarantlr4/v4/automata/ATNPrinter;
.super Ljava/lang/Object;
.source "ATNPrinter.java"


# instance fields
.field g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field marked:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;",
            ">;"
        }
    .end annotation
.end field

.field start:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

.field work:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V
    .locals 0

    .line 40
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 41
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 42
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->start:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    return-void
.end method


# virtual methods
.method public asString()Ljava/lang/String;
    .locals 13

    .line 46
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->start:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 47
    :cond_0
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->marked:Ljava/util/Set;

    .line 49
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->work:Ljava/util/List;

    .line 50
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->start:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 52
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 55
    :cond_1
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->work:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_d

    .line 56
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->work:Ljava/util/List;

    const/4 v2, 0x0

    invoke-interface {v1, v2}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 57
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->marked:Ljava/util/Set;

    invoke-interface {v3, v1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    goto :goto_0

    .line 58
    :cond_2
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v3

    .line 60
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->marked:Ljava/util/Set;

    invoke-interface {v4, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    :goto_1
    if-ge v2, v3, :cond_1

    .line 62
    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v4

    .line 63
    instance-of v5, v1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    if-nez v5, :cond_4

    .line 64
    instance-of v5, v4, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    if-eqz v5, :cond_3

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->work:Ljava/util/List;

    move-object v6, v4

    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-interface {v5, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 65
    :cond_3
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->work:Ljava/util/List;

    iget-object v6, v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-interface {v5, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 67
    :cond_4
    :goto_2
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->getStateString(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 68
    instance-of v5, v4, Lgroovyjarjarantlr4/v4/runtime/atn/EpsilonTransition;

    const/16 v6, 0xa

    const-string v7, "->"

    if-eqz v5, :cond_5

    .line 69
    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->getStateString(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_5

    .line 71
    :cond_5
    instance-of v5, v4, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    const-string v8, "-"

    if-eqz v5, :cond_6

    .line 72
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v8, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-object v9, v4

    check-cast v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    iget v9, v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->ruleIndex:I

    invoke-virtual {v8, v9}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(I)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v8

    iget-object v8, v8, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->getStateString(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_5

    .line 74
    :cond_6
    instance-of v5, v4, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;

    if-eqz v5, :cond_7

    .line 75
    move-object v5, v4

    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;

    .line 76
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v8, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->getStateString(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_5

    .line 78
    :cond_7
    instance-of v5, v4, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;

    if-eqz v5, :cond_b

    .line 79
    move-object v5, v4

    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;

    .line 80
    instance-of v9, v5, Lgroovyjarjarantlr4/v4/runtime/atn/NotSetTransition;

    .line 81
    iget-object v10, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v10}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v10

    const-string v11, "~"

    const-string v12, ""

    if-eqz v10, :cond_9

    .line 82
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    if-eqz v9, :cond_8

    goto :goto_3

    :cond_8
    move-object v11, v12

    :goto_3
    invoke-virtual {v8, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v8, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->getStateString(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_5

    .line 85
    :cond_9
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    if-eqz v9, :cond_a

    goto :goto_4

    :cond_a
    move-object v11, v12

    :goto_4
    invoke-virtual {v8, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;->label()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v5

    iget-object v9, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v9}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v9

    invoke-virtual {v5, v9}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v8, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->getStateString(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_5

    .line 88
    :cond_b
    instance-of v5, v4, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;

    if-eqz v5, :cond_c

    .line 89
    move-object v5, v4

    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;

    .line 90
    iget-object v9, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget v5, v5, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    invoke-virtual {v9, v5}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenDisplayName(I)Ljava/lang/String;

    move-result-object v5

    .line 91
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->getStateString(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_5

    .line 94
    :cond_c
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v4}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->getStateString(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :goto_5
    add-int/lit8 v2, v2, 0x1

    goto/16 :goto_1

    .line 98
    :cond_d
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method getStateString(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Ljava/lang/String;
    .locals 4

    .line 102
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    .line 103
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "s"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 104
    instance-of v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarBlockStartState;

    if-eqz v2, :cond_0

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "StarBlockStart_"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto/16 :goto_0

    .line 105
    :cond_0
    instance-of v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/PlusBlockStartState;

    if-eqz v2, :cond_1

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "PlusBlockStart_"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto/16 :goto_0

    .line 106
    :cond_1
    instance-of v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;

    if-eqz v2, :cond_2

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "BlockStart_"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto/16 :goto_0

    .line 107
    :cond_2
    instance-of v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/BlockEndState;

    if-eqz v2, :cond_3

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "BlockEnd_"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto/16 :goto_0

    .line 108
    :cond_3
    instance-of v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    const-string v3, "_"

    if-eqz v2, :cond_4

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "RuleStart_"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    invoke-virtual {v2, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(I)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    .line 109
    :cond_4
    instance-of v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    if-eqz v2, :cond_5

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "RuleStop_"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/automata/ATNPrinter;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    invoke-virtual {v2, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(I)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    .line 110
    :cond_5
    instance-of v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/PlusLoopbackState;

    if-eqz v2, :cond_6

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "PlusLoopBack_"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    .line 111
    :cond_6
    instance-of v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopbackState;

    if-eqz v2, :cond_7

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "StarLoopBack_"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    .line 112
    :cond_7
    instance-of p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    if-eqz p1, :cond_8

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "StarLoopEntry_"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    :cond_8
    :goto_0
    return-object v1
.end method
