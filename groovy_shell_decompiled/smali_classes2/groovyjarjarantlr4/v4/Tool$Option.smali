.class public Lgroovyjarjarantlr4/v4/Tool$Option;
.super Ljava/lang/Object;
.source "Tool.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/Tool;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Option"
.end annotation


# instance fields
.field argType:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

.field description:Ljava/lang/String;

.field fieldName:Ljava/lang/String;

.field name:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/Tool$OptionArgType;Ljava/lang/String;)V
    .locals 0

    .line 91
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 92
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/Tool$Option;->fieldName:Ljava/lang/String;

    .line 93
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/Tool$Option;->name:Ljava/lang/String;

    .line 94
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/Tool$Option;->argType:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    .line 95
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/Tool$Option;->description:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 88
    sget-object v0, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->NONE:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    invoke-direct {p0, p1, p2, v0, p3}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/Tool$OptionArgType;Ljava/lang/String;)V

    return-void
.end method
