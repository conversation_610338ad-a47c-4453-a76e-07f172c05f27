.class public Lgroovyjarjarantlr4/v4/misc/Graph$Node;
.super Ljava/lang/Object;
.source "Graph.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/misc/Graph;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Node"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field public edges:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/misc/Graph$Node<",
            "TT;>;>;"
        }
    .end annotation
.end field

.field public payload:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)V"
        }
    .end annotation

    .line 27
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 25
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/misc/Graph$Node;->edges:Ljava/util/List;

    .line 27
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/misc/Graph$Node;->payload:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public addEdge(Lgroovyjarjarantlr4/v4/misc/Graph$Node;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/misc/Graph$Node<",
            "TT;>;)V"
        }
    .end annotation

    .line 30
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/misc/Graph$Node;->edges:Ljava/util/List;

    sget-object v1, Ljava/util/Collections;->EMPTY_LIST:Ljava/util/List;

    if-ne v0, v1, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/misc/Graph$Node;->edges:Ljava/util/List;

    .line 31
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/misc/Graph$Node;->edges:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/misc/Graph$Node;->edges:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 35
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/misc/Graph$Node;->payload:Ljava/lang/Object;

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
