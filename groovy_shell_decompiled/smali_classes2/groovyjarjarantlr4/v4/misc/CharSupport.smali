.class public Lgroovyjarjarantlr4/v4/misc/CharSupport;
.super Ljava/lang/Object;
.source "CharSupport.java"


# static fields
.field public static ANTLRLiteralCharValueEscape:[Ljava/lang/String;

.field public static ANTLRLiteralEscapedCharValue:[I


# direct methods
.method static constructor <clinit>()V
    .locals 8

    const/16 v0, 0xff

    new-array v1, v0, [I

    .line 20
    sput-object v1, Lgroovyjarjarantlr4/v4/misc/CharSupport;->ANTLRLiteralEscapedCharValue:[I

    new-array v0, v0, [Ljava/lang/String;

    .line 24
    sput-object v0, Lgroovyjarjarantlr4/v4/misc/CharSupport;->ANTLRLiteralCharValueEscape:[Ljava/lang/String;

    const/16 v2, 0x6e

    const/16 v3, 0xa

    aput v3, v1, v2

    const/16 v2, 0x72

    const/16 v4, 0xd

    aput v4, v1, v2

    const/16 v2, 0x74

    const/16 v5, 0x9

    aput v5, v1, v2

    const/16 v2, 0x62

    const/16 v6, 0x8

    aput v6, v1, v2

    const/16 v2, 0x66

    const/16 v7, 0xc

    aput v7, v1, v2

    const/16 v2, 0x5c

    aput v2, v1, v2

    const-string v1, "\\n"

    aput-object v1, v0, v3

    const-string v1, "\\r"

    aput-object v1, v0, v4

    const-string v1, "\\t"

    aput-object v1, v0, v5

    const-string v1, "\\b"

    aput-object v1, v0, v6

    const-string v1, "\\f"

    aput-object v1, v0, v7

    const-string v1, "\\\\"

    aput-object v1, v0, v2

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 16
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static capitalize(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 184
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v1, 0x0

    invoke-virtual {p0, v1}, Ljava/lang/String;->charAt(I)C

    move-result v1

    invoke-static {v1}, Ljava/lang/Character;->toUpperCase(C)C

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {p0, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getANTLRCharLiteralForChar(I)Ljava/lang/String;
    .locals 4

    const/16 v0, 0x27

    if-gez p0, :cond_0

    const-string p0, "<INVALID>"

    goto :goto_1

    .line 52
    :cond_0
    sget-object v1, Lgroovyjarjarantlr4/v4/misc/CharSupport;->ANTLRLiteralCharValueEscape:[Ljava/lang/String;

    array-length v2, v1

    if-ge p0, v2, :cond_1

    aget-object v1, v1, p0

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    if-eqz v1, :cond_2

    move-object p0, v1

    goto :goto_1

    :cond_2
    int-to-char v1, p0

    .line 56
    invoke-static {v1}, Ljava/lang/Character$UnicodeBlock;->of(C)Ljava/lang/Character$UnicodeBlock;

    move-result-object v2

    sget-object v3, Ljava/lang/Character$UnicodeBlock;->BASIC_LATIN:Ljava/lang/Character$UnicodeBlock;

    if-ne v2, v3, :cond_5

    invoke-static {v1}, Ljava/lang/Character;->isISOControl(C)Z

    move-result v2

    if-nez v2, :cond_5

    const/16 v2, 0x5c

    if-ne p0, v2, :cond_3

    const-string p0, "\\\\"

    goto :goto_1

    :cond_3
    if-ne p0, v0, :cond_4

    const-string p0, "\\\'"

    goto :goto_1

    .line 65
    :cond_4
    invoke-static {v1}, Ljava/lang/Character;->toString(C)Ljava/lang/String;

    move-result-object p0

    goto :goto_1

    :cond_5
    const v1, 0xffff

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-gt p0, v1, :cond_6

    new-array v1, v3, [Ljava/lang/Object;

    .line 69
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    aput-object p0, v1, v2

    const-string p0, "\\u%04X"

    invoke-static {p0, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    goto :goto_1

    :cond_6
    new-array v1, v3, [Ljava/lang/Object;

    .line 71
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    aput-object p0, v1, v2

    const-string p0, "\\u{%06X}"

    invoke-static {p0, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    .line 74
    :goto_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getCharValueFromCharInGrammarLiteral(Ljava/lang/String;)I
    .locals 6

    .line 135
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eq v0, v2, :cond_8

    const/4 v3, 0x2

    const/4 v4, -0x1

    if-eq v0, v3, :cond_4

    const/4 v1, 0x6

    const/16 v2, 0x7d

    const/4 v5, 0x3

    if-eq v0, v1, :cond_1

    const-string v0, "\\u{"

    .line 162
    invoke-virtual {p0, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 163
    invoke-virtual {p0, v2}, Ljava/lang/String;->indexOf(I)I

    move-result v0

    invoke-static {p0, v5, v0}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->parseHexValue(Ljava/lang/String;II)I

    move-result p0

    return p0

    :cond_0
    return v4

    :cond_1
    const-string v0, "\\u"

    .line 149
    invoke-virtual {p0, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_2

    return v4

    .line 152
    :cond_2
    invoke-virtual {p0, v3}, Ljava/lang/String;->charAt(I)C

    move-result v0

    const/16 v1, 0x7b

    if-ne v0, v1, :cond_3

    .line 154
    invoke-virtual {p0, v2}, Ljava/lang/String;->indexOf(I)I

    move-result v0

    move v3, v5

    goto :goto_0

    .line 158
    :cond_3
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    .line 160
    :goto_0
    invoke-static {p0, v3, v0}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->parseHexValue(Ljava/lang/String;II)I

    move-result p0

    return p0

    .line 140
    :cond_4
    invoke-virtual {p0, v1}, Ljava/lang/String;->charAt(I)C

    move-result v0

    const/16 v1, 0x5c

    if-eq v0, v1, :cond_5

    return v4

    .line 142
    :cond_5
    invoke-virtual {p0, v2}, Ljava/lang/String;->charAt(I)C

    move-result p0

    const/16 v0, 0x27

    if-ne p0, v0, :cond_6

    return p0

    .line 144
    :cond_6
    sget-object v0, Lgroovyjarjarantlr4/v4/misc/CharSupport;->ANTLRLiteralEscapedCharValue:[I

    aget p0, v0, p0

    if-nez p0, :cond_7

    return v4

    :cond_7
    return p0

    .line 138
    :cond_8
    invoke-virtual {p0, v1}, Ljava/lang/String;->charAt(I)C

    move-result p0

    return p0
.end method

.method public static getCharValueFromGrammarCharLiteral(Ljava/lang/String;)I
    .locals 2

    if-eqz p0, :cond_1

    .line 82
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x3

    if-ge v0, v1, :cond_0

    goto :goto_0

    .line 83
    :cond_0
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    invoke-virtual {p0, v1, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->getCharValueFromCharInGrammarLiteral(Ljava/lang/String;)I

    move-result p0

    return p0

    :cond_1
    :goto_0
    const/4 p0, -0x1

    return p0
.end method

.method public static getIntervalSetEscapedString(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/lang/String;
    .locals 3

    .line 188
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 189
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->getIntervals()Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    .line 190
    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 191
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    .line 192
    iget v2, v1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    invoke-static {v2, v1}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->getRangeEscapedString(II)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 193
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    const-string v1, " | "

    .line 194
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 197
    :cond_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getRangeEscapedString(II)Ljava/lang/String;
    .locals 1

    if-eq p0, p1, :cond_0

    .line 201
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p0}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->getANTLRCharLiteralForChar(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, ".."

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->getANTLRCharLiteralForChar(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    goto :goto_0

    :cond_0
    invoke-static {p0}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->getANTLRCharLiteralForChar(I)Ljava/lang/String;

    move-result-object p0

    :goto_0
    return-object p0
.end method

.method public static getStringFromGrammarStringLiteral(Ljava/lang/String;)Ljava/lang/String;
    .locals 11

    .line 87
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 89
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v1

    const/4 v2, 0x1

    sub-int/2addr v1, v2

    :goto_0
    if-ge v2, v1, :cond_e

    add-int/lit8 v3, v2, 0x1

    .line 92
    invoke-virtual {p0, v2}, Ljava/lang/String;->charAt(I)C

    move-result v4

    const/16 v5, 0x5c

    const/4 v6, 0x0

    if-ne v4, v5, :cond_a

    add-int/lit8 v4, v2, 0x2

    if-ge v3, v1, :cond_b

    .line 94
    invoke-virtual {p0, v3}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v5, 0x75

    if-ne v3, v5, :cond_b

    const/16 v3, 0x46

    const/16 v5, 0x66

    const/16 v7, 0x41

    const/16 v8, 0x61

    if-ge v4, v1, :cond_5

    .line 95
    invoke-virtual {p0, v4}, Ljava/lang/String;->charAt(I)C

    move-result v9

    const/16 v10, 0x7b

    if-ne v9, v10, :cond_5

    add-int/lit8 v4, v2, 0x3

    :goto_1
    add-int/lit8 v9, v4, 0x1

    if-le v9, v1, :cond_0

    return-object v6

    .line 99
    :cond_0
    invoke-virtual {p0, v4}, Ljava/lang/String;->charAt(I)C

    move-result v4

    const/16 v10, 0x7d

    if-ne v4, v10, :cond_1

    move v4, v9

    goto :goto_3

    .line 103
    :cond_1
    invoke-static {v4}, Ljava/lang/Character;->isDigit(C)Z

    move-result v10

    if-nez v10, :cond_4

    if-lt v4, v8, :cond_2

    if-le v4, v5, :cond_4

    :cond_2
    if-lt v4, v7, :cond_3

    if-le v4, v3, :cond_4

    :cond_3
    return-object v6

    :cond_4
    move v4, v9

    goto :goto_1

    :cond_5
    :goto_2
    add-int/lit8 v9, v2, 0x6

    if-ge v4, v9, :cond_b

    if-le v4, v1, :cond_6

    return-object v6

    .line 111
    :cond_6
    invoke-virtual {p0, v4}, Ljava/lang/String;->charAt(I)C

    move-result v9

    .line 112
    invoke-static {v9}, Ljava/lang/Character;->isDigit(C)Z

    move-result v10

    if-nez v10, :cond_9

    if-lt v9, v8, :cond_7

    if-le v9, v5, :cond_9

    :cond_7
    if-lt v9, v7, :cond_8

    if-le v9, v3, :cond_9

    :cond_8
    return-object v6

    :cond_9
    add-int/lit8 v4, v4, 0x1

    goto :goto_2

    :cond_a
    move v4, v3

    :cond_b
    :goto_3
    if-le v4, v1, :cond_c

    return-object v6

    .line 120
    :cond_c
    invoke-virtual {p0, v2, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v2

    .line 121
    invoke-static {v2}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->getCharValueFromCharInGrammarLiteral(Ljava/lang/String;)I

    move-result v2

    const/4 v3, -0x1

    if-ne v2, v3, :cond_d

    return-object v6

    .line 125
    :cond_d
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->appendCodePoint(I)Ljava/lang/StringBuilder;

    move v2, v4

    goto :goto_0

    .line 128
    :cond_e
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static parseHexValue(Ljava/lang/String;II)I
    .locals 1

    const/4 v0, -0x1

    if-ltz p1, :cond_1

    if-gez p2, :cond_0

    goto :goto_0

    .line 173
    :cond_0
    invoke-virtual {p0, p1, p2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p0

    const/16 p1, 0x10

    .line 176
    :try_start_0
    invoke-static {p0, p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;I)I

    move-result v0
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_1
    :goto_0
    return v0
.end method
