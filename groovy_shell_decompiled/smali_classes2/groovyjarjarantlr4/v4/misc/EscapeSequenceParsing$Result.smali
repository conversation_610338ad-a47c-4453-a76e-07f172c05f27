.class public Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;
.super Ljava/lang/Object;
.source "EscapeSequenceParsing.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Result"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;
    }
.end annotation


# instance fields
.field public final codePoint:I

.field public final parseLength:I

.field public final propertyIntervalSet:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

.field public final startOffset:I

.field public final type:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;ILgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;II)V
    .locals 0

    .line 37
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 38
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->type:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    .line 39
    iput p2, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->codePoint:I

    .line 40
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->propertyIntervalSet:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    .line 41
    iput p4, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->startOffset:I

    .line 42
    iput p5, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->parseLength:I

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    .line 58
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 61
    :cond_0
    check-cast p1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    const/4 v0, 0x1

    if-ne p0, p1, :cond_1

    return v0

    .line 65
    :cond_1
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->type:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    iget-object v3, p1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->type:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    invoke-static {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget v2, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->codePoint:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    iget v3, p1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->codePoint:I

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->propertyIntervalSet:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    iget-object v3, p1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->propertyIntervalSet:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-static {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget v2, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->parseLength:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    iget p1, p1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->parseLength:I

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {v2, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    move v1, v0

    :cond_2
    return v1
.end method

.method public hashCode()I
    .locals 2

    .line 73
    invoke-static {}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->initialize()I

    move-result v0

    .line 74
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->type:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(ILjava/lang/Object;)I

    move-result v0

    .line 75
    iget v1, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->codePoint:I

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(II)I

    move-result v0

    .line 76
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->propertyIntervalSet:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(ILjava/lang/Object;)I

    move-result v0

    .line 77
    iget v1, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->parseLength:I

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(II)I

    move-result v0

    const/4 v1, 0x4

    .line 78
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->finish(II)I

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    const/4 v0, 0x5

    new-array v0, v0, [Ljava/lang/Object;

    .line 47
    invoke-super {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->type:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget v1, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->codePoint:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->propertyIntervalSet:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    iget v1, p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;->parseLength:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    const-string v1, "%s type=%s codePoint=%d propertyIntervalSet=%s parseLength=%d"

    invoke-static {v1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
