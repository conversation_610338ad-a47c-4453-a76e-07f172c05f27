.class public abstract Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;
.super Ljava/lang/Object;
.source "EscapeSequenceParsing.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;
    .locals 7

    .line 183
    new-instance v6, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    sget-object v1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->INVALID:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    sget-object v3, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->EMPTY_SET:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    sub-int/2addr p1, p0

    add-int/lit8 v5, p1, 0x1

    const/4 v2, 0x0

    move-object v0, v6

    move v4, p0

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;-><init>(Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;ILgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;II)V

    return-object v6
.end method

.method public static parseEscape(Ljava/lang/String;I)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;
    .locals 13

    add-int/lit8 v0, p1, 0x2

    .line 89
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v1

    if-gt v0, v1, :cond_12

    invoke-virtual {p0, p1}, Ljava/lang/String;->codePointAt(I)I

    move-result v0

    const/16 v1, 0x5c

    if-eq v0, v1, :cond_0

    goto/16 :goto_3

    :cond_0
    add-int/lit8 v0, p1, 0x1

    .line 94
    invoke-virtual {p0, v0}, Ljava/lang/String;->codePointAt(I)I

    move-result v1

    .line 96
    invoke-static {v1}, Ljava/lang/Character;->charCount(I)I

    move-result v2

    add-int/2addr v2, v0

    const/16 v3, 0x75

    const/16 v4, 0x7d

    const/16 v5, 0x7b

    const/4 v6, -0x1

    if-ne v1, v3, :cond_7

    add-int/lit8 v0, v2, 0x3

    .line 99
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v1

    if-le v0, v1, :cond_1

    .line 100
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result p0

    add-int/lit8 p0, p0, -0x1

    invoke-static {p1, p0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    .line 104
    :cond_1
    invoke-virtual {p0, v2}, Ljava/lang/String;->codePointAt(I)I

    move-result v0

    if-ne v0, v5, :cond_3

    add-int/lit8 v2, v2, 0x1

    .line 106
    invoke-virtual {p0, v4, v2}, Ljava/lang/String;->indexOf(II)I

    move-result v0

    if-ne v0, v6, :cond_2

    .line 108
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result p0

    add-int/lit8 p0, p0, -0x1

    invoke-static {p1, p0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    :cond_2
    add-int/lit8 v1, v0, 0x1

    goto :goto_0

    :cond_3
    add-int/lit8 v0, v2, 0x4

    .line 113
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v1

    if-le v0, v1, :cond_4

    .line 114
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result p0

    add-int/lit8 p0, p0, -0x1

    invoke-static {p1, p0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    :cond_4
    move v1, v0

    .line 120
    :goto_0
    invoke-static {p0, v2, v0}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->parseHexValue(Ljava/lang/String;II)I

    move-result v9

    if-eq v9, v6, :cond_6

    const p0, 0x10ffff

    if-le v9, p0, :cond_5

    goto :goto_1

    .line 124
    :cond_5
    new-instance p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    sget-object v8, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->CODE_POINT:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    sget-object v10, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->EMPTY_SET:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    sub-int v12, v1, p1

    move-object v7, p0

    move v11, p1

    invoke-direct/range {v7 .. v12}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;-><init>(Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;ILgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;II)V

    return-object p0

    :cond_6
    :goto_1
    add-int/lit8 p0, p1, 0x6

    add-int/lit8 p0, p0, -0x1

    .line 122
    invoke-static {p1, p0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    :cond_7
    const/16 v3, 0x70

    const/16 v7, 0x50

    if-eq v1, v3, :cond_c

    if-ne v1, v7, :cond_8

    goto :goto_2

    .line 160
    :cond_8
    sget-object v3, Lgroovyjarjarantlr4/v4/misc/CharSupport;->ANTLRLiteralEscapedCharValue:[I

    array-length v3, v3

    if-ge v1, v3, :cond_b

    .line 161
    sget-object p0, Lgroovyjarjarantlr4/v4/misc/CharSupport;->ANTLRLiteralEscapedCharValue:[I

    aget p0, p0, v1

    if-nez p0, :cond_a

    const/16 p0, 0x5d

    if-eq v1, p0, :cond_9

    const/16 p0, 0x2d

    if-eq v1, p0, :cond_9

    .line 164
    invoke-static {p1, v0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    :cond_9
    move p0, v1

    .line 170
    :cond_a
    new-instance v6, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    sget-object v1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->CODE_POINT:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    sget-object v3, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->EMPTY_SET:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    sub-int v5, v2, p1

    move-object v0, v6

    move v2, p0

    move v4, p1

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;-><init>(Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;ILgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;II)V

    return-object v6

    .line 178
    :cond_b
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result p0

    add-int/lit8 p0, p0, -0x1

    invoke-static {p1, p0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    :cond_c
    :goto_2
    add-int/lit8 v0, v2, 0x3

    .line 133
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v3

    if-le v0, v3, :cond_d

    .line 134
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result p0

    add-int/lit8 p0, p0, -0x1

    invoke-static {p1, p0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    .line 136
    :cond_d
    invoke-virtual {p0, v2}, Ljava/lang/String;->codePointAt(I)I

    move-result v0

    if-eq v0, v5, :cond_e

    .line 137
    invoke-static {p1, v2}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    .line 140
    :cond_e
    invoke-virtual {p0, v4, v2}, Ljava/lang/String;->indexOf(II)I

    move-result v0

    if-ne v0, v6, :cond_f

    .line 142
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result p0

    add-int/lit8 p0, p0, -0x1

    invoke-static {p1, p0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    :cond_f
    add-int/lit8 v2, v2, 0x1

    .line 144
    invoke-virtual {p0, v2, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p0

    .line 145
    invoke-static {p0}, Lgroovyjarjarantlr4/v4/unicode/UnicodeData;->getPropertyCodePoints(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p0

    if-nez p0, :cond_10

    .line 147
    invoke-static {p1, v0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0

    :cond_10
    add-int/lit8 v0, v0, 0x1

    if-ne v1, v7, :cond_11

    .line 151
    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->COMPLETE_CHAR_SET:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->complement(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p0

    :cond_11
    move-object v4, p0

    .line 153
    new-instance p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    sget-object v2, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->PROPERTY:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    const/4 v3, -0x1

    sub-int v6, v0, p1

    move-object v1, p0

    move v5, p1

    invoke-direct/range {v1 .. v6}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;-><init>(Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;ILgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;II)V

    return-object p0

    .line 90
    :cond_12
    :goto_3
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result p0

    add-int/lit8 p0, p0, -0x1

    invoke-static {p1, p0}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing;->invalid(II)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;

    move-result-object p0

    return-object p0
.end method
