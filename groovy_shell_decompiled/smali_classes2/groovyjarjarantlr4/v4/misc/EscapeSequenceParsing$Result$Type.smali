.class public final enum Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;
.super Ljava/lang/Enum;
.source "EscapeSequenceParsing.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "Type"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

.field public static final enum CODE_POINT:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

.field public static final enum INVALID:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

.field public static final enum PROPERTY:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 26
    new-instance v0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    const-string v1, "INVALID"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->INVALID:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    .line 27
    new-instance v1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    const-string v3, "CODE_POINT"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->CODE_POINT:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    .line 28
    new-instance v3, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    const-string v5, "PROPERTY"

    const/4 v6, 0x2

    invoke-direct {v3, v5, v6}, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;-><init>(Ljava/lang/String;I)V

    sput-object v3, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->PROPERTY:Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    const/4 v5, 0x3

    new-array v5, v5, [Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    aput-object v0, v5, v2

    aput-object v1, v5, v4

    aput-object v3, v5, v6

    .line 25
    sput-object v5, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->$VALUES:[Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 25
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;
    .locals 1

    .line 25
    const-class v0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    return-object p0
.end method

.method public static values()[Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;
    .locals 1

    .line 25
    sget-object v0, Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->$VALUES:[Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    invoke-virtual {v0}, [Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/misc/EscapeSequenceParsing$Result$Type;

    return-object v0
.end method
