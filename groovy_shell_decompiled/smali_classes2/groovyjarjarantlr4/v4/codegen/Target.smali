.class public abstract Lgroovyjarjarantlr4/v4/codegen/Target;
.super Ljava/lang/Object;
.source "Target.java"


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field protected final gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

.field private final language:Ljava/lang/String;

.field protected targetCharValueEscape:[Ljava/lang/String;

.field private templates:Lorg/stringtemplate/v4/STGroup;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method protected constructor <init>(Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;Ljava/lang/String;)V
    .locals 3

    .line 46
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0xff

    new-array v0, v0, [Ljava/lang/String;

    .line 40
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->targetCharValueEscape:[Ljava/lang/String;

    const/16 v1, 0xa

    const-string v2, "\\n"

    aput-object v2, v0, v1

    const/16 v1, 0xd

    const-string v2, "\\r"

    aput-object v2, v0, v1

    const/16 v1, 0x9

    const-string v2, "\\t"

    aput-object v2, v0, v1

    const/16 v1, 0x8

    const-string v2, "\\b"

    aput-object v2, v0, v1

    const/16 v1, 0xc

    const-string v2, "\\f"

    aput-object v2, v0, v1

    const/16 v1, 0x5c

    const-string v2, "\\\\"

    aput-object v2, v0, v1

    const/16 v1, 0x27

    const-string v2, "\\\'"

    aput-object v2, v0, v1

    const/16 v1, 0x22

    const-string v2, "\\\""

    aput-object v2, v0, v1

    .line 55
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    .line 56
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->language:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method protected abstract appendUnicodeEscapedCodePoint(ILjava/lang/StringBuilder;)V
.end method

.method public abstract encodeIntAsCharEscape(I)Ljava/lang/String;
.end method

.method protected genFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Lorg/stringtemplate/v4/ST;Ljava/lang/String;)V
    .locals 0

    .line 78
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getCodeGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object p1

    invoke-virtual {p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->write(Lorg/stringtemplate/v4/ST;Ljava/lang/String;)V

    return-void
.end method

.method public getAltLabelContextStructName(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 214
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/misc/Utils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v1, "RuleContextNameSuffix"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getBaseListenerFileName(Z)Ljava/lang/String;
    .locals 2

    .line 309
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object p1

    const-string v0, "codeFileExtension"

    invoke-virtual {p1, v0}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    .line 310
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "BaseListener"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 311
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getBaseVisitorFileName(Z)Ljava/lang/String;
    .locals 2

    .line 319
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object p1

    const-string v0, "codeFileExtension"

    invoke-virtual {p1, v0}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    .line 320
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "BaseVisitor"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 321
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getCodeGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;
    .locals 1

    .line 60
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    return-object v0
.end method

.method public getElementListName(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 259
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v1, "ElementListName"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    .line 260
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getElementName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-string v1, "elemName"

    invoke-virtual {v0, v1, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 261
    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getElementName(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    const-string v0, "."

    .line 265
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p1, "_wild"

    return-object p1

    .line 269
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getCodeGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v0

    if-eqz v0, :cond_1

    return-object p1

    .line 270
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getCodeGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenType(Ljava/lang/String;)I

    move-result v0

    if-nez v0, :cond_2

    return-object p1

    .line 272
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getCodeGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object p1

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTokenTypeAsTargetLabel(Lgroovyjarjarantlr4/v4/tool/Grammar;I)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getImplicitRuleLabel(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 253
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v1, "ImplicitRuleLabel"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    const-string v1, "ruleName"

    .line 254
    invoke-virtual {v0, v1, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 255
    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getImplicitSetLabel(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 247
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v1, "ImplicitSetLabel"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    const-string v1, "id"

    .line 248
    invoke-virtual {v0, v1, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 249
    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getImplicitTokenLabel(Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    .line 235
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v1, "ImplicitTokenLabel"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    .line 236
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getCodeGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v1

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenType(Ljava/lang/String;)I

    move-result v1

    const-string v2, "\'"

    .line 237
    invoke-virtual {p1, v2}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 238
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "s"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 240
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getCodeGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object p1

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p0, p1, v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTokenTypeAsTargetLabel(Lgroovyjarjarantlr4/v4/tool/Grammar;I)Ljava/lang/String;

    move-result-object p1

    const-string v1, "tokenName"

    .line 241
    invoke-virtual {v0, v1, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 242
    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getInlineTestSetWordSize()I
    .locals 1

    const/16 v0, 0x40

    return v0
.end method

.method public getLanguage()Ljava/lang/String;
    .locals 1

    .line 64
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->language:Ljava/lang/String;

    return-object v0
.end method

.method public getListLabel(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 199
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v1, "ListLabelName"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    const-string v1, "label"

    .line 200
    invoke-virtual {v0, v1, p1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 201
    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getListenerFileName(Z)Ljava/lang/String;
    .locals 2

    .line 289
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object p1

    const-string v0, "codeFileExtension"

    invoke-virtual {p1, v0}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    .line 290
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "Listener"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 291
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getLoopCounter(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;
    .locals 2

    .line 195
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "cnt"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getTokenIndex()I

    move-result p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getLoopLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;
    .locals 2

    .line 191
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "loop"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getTokenIndex()I

    move-result p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getRecognizerFileName(Z)Ljava/lang/String;
    .locals 2

    .line 279
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object p1

    const-string v0, "codeFileExtension"

    invoke-virtual {p1, v0}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    .line 280
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRecognizerName()Ljava/lang/String;

    move-result-object v0

    .line 281
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getRuleFunctionContextStructName(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)Ljava/lang/String;
    .locals 2

    .line 223
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 224
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 225
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object p1

    const-string v0, "LexerRuleContext"

    invoke-virtual {p1, v0}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 228
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object p1

    .line 229
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/misc/Utils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v1, "RuleContextNameSuffix"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getRuleFunctionContextStructName(Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/lang/String;
    .locals 2

    .line 205
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 206
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object p1

    const-string v0, "LexerRuleContext"

    invoke-virtual {p1, v0}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 209
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object p1

    .line 210
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/misc/Utils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v1, "RuleContextNameSuffix"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getSerializedATNSegmentLimit()I
    .locals 1

    const v0, 0x7fffffff

    return v0
.end method

.method public abstract getTargetStringLiteralFromANTLRStringLiteral(Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;Ljava/lang/String;Z)Ljava/lang/String;
.end method

.method public getTargetStringLiteralFromString(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    const/4 v0, 0x1

    .line 163
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTargetStringLiteralFromString(Ljava/lang/String;Z)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getTargetStringLiteralFromString(Ljava/lang/String;Z)Ljava/lang/String;
    .locals 6

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 129
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v1, 0x22

    if-eqz p2, :cond_1

    .line 131
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_1
    const/4 v2, 0x0

    .line 133
    :goto_0
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v3

    if-ge v2, v3, :cond_4

    .line 134
    invoke-virtual {p1, v2}, Ljava/lang/String;->codePointAt(I)I

    move-result v3

    const/16 v4, 0x27

    if-eq v3, v4, :cond_2

    .line 135
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->targetCharValueEscape:[Ljava/lang/String;

    array-length v5, v4

    if-ge v3, v5, :cond_2

    aget-object v5, v4, v3

    if-eqz v5, :cond_2

    .line 139
    aget-object v4, v4, v3

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 141
    :cond_2
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/codegen/Target;->shouldUseUnicodeEscapeForCodePointInDoubleQuotedString(I)Z

    move-result v4

    if-eqz v4, :cond_3

    .line 142
    invoke-virtual {p0, v2, v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->appendUnicodeEscapedCodePoint(ILjava/lang/StringBuilder;)V

    goto :goto_1

    .line 146
    :cond_3
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->appendCodePoint(I)Ljava/lang/StringBuilder;

    .line 148
    :goto_1
    invoke-static {v3}, Ljava/lang/Character;->charCount(I)I

    move-result v3

    add-int/2addr v2, v3

    goto :goto_0

    :cond_4
    if-eqz p2, :cond_5

    .line 151
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 153
    :cond_5
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getTemplates()Lorg/stringtemplate/v4/STGroup;
    .locals 1

    .line 69
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->templates:Lorg/stringtemplate/v4/STGroup;

    if-nez v0, :cond_0

    .line 70
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->loadTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->templates:Lorg/stringtemplate/v4/STGroup;

    .line 73
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->templates:Lorg/stringtemplate/v4/STGroup;

    return-object v0
.end method

.method public getTokenTypeAsTargetLabel(Lgroovyjarjarantlr4/v4/tool/Grammar;I)Ljava/lang/String;
    .locals 1

    .line 88
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenName(I)Ljava/lang/String;

    move-result-object p1

    const-string v0, "<INVALID>"

    .line 90
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 91
    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    :cond_0
    return-object p1
.end method

.method public getTokenTypesAsTargetLabels(Lgroovyjarjarantlr4/v4/tool/Grammar;[I)[Ljava/lang/String;
    .locals 3

    .line 98
    array-length v0, p2

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    .line 99
    :goto_0
    array-length v2, p2

    if-ge v1, v2, :cond_0

    .line 100
    aget v2, p2, v1

    invoke-virtual {p0, p1, v2}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTokenTypeAsTargetLabel(Lgroovyjarjarantlr4/v4/tool/Grammar;I)Ljava/lang/String;

    move-result-object v2

    aput-object v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getVisitorFileName(Z)Ljava/lang/String;
    .locals 2

    .line 299
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object p1

    const-string v0, "codeFileExtension"

    invoke-virtual {p1, v0}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    .line 300
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/Target;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "Visitor"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 301
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public grammarSymbolCausesIssueInGeneratedCode(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Z
    .locals 4

    .line 347
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v0

    const/16 v1, 0xa

    const/16 v2, 0x52

    const/4 v3, 0x0

    if-eq v0, v1, :cond_2

    const/16 v1, 0xb

    if-eq v0, v1, :cond_1

    if-eq v0, v2, :cond_1

    const/16 v1, 0x56

    if-eq v0, v1, :cond_0

    goto :goto_0

    .line 365
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildIndex()I

    move-result v0

    if-nez v0, :cond_3

    :cond_1
    return v3

    .line 349
    :cond_2
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v0

    const/16 v1, 0x2a

    if-eq v0, v1, :cond_4

    if-eq v0, v2, :cond_4

    .line 377
    :cond_3
    :goto_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->visibleGrammarSymbolCausesIssueInGeneratedCode(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Z

    move-result p1

    return p1

    :cond_4
    return v3
.end method

.method protected loadTemplates()Lorg/stringtemplate/v4/STGroup;
    .locals 3

    .line 384
    new-instance v0, Lorg/stringtemplate/v4/STGroupFile;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "groovyjarjarantlr4/v4/tool/templates/codegen/"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getLanguage()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "/"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getLanguage()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    sget-object v2, Lorg/stringtemplate/v4/STGroup;->GROUP_FILE_EXTENSION:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lorg/stringtemplate/v4/STGroupFile;-><init>(Ljava/lang/String;)V

    .line 385
    const-class v1, Ljava/lang/Integer;

    new-instance v2, Lorg/stringtemplate/v4/NumberRenderer;

    invoke-direct {v2}, Lorg/stringtemplate/v4/NumberRenderer;-><init>()V

    invoke-virtual {v0, v1, v2}, Lorg/stringtemplate/v4/STGroup;->registerRenderer(Ljava/lang/Class;Lorg/stringtemplate/v4/AttributeRenderer;)V

    .line 386
    const-class v1, Ljava/lang/String;

    new-instance v2, Lorg/stringtemplate/v4/StringRenderer;

    invoke-direct {v2}, Lorg/stringtemplate/v4/StringRenderer;-><init>()V

    invoke-virtual {v0, v1, v2}, Lorg/stringtemplate/v4/STGroup;->registerRenderer(Ljava/lang/Class;Lorg/stringtemplate/v4/AttributeRenderer;)V

    .line 387
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/Target$1;

    invoke-direct {v1, p0}, Lgroovyjarjarantlr4/v4/codegen/Target$1;-><init>(Lgroovyjarjarantlr4/v4/codegen/Target;)V

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->setListener(Lorg/stringtemplate/v4/STErrorListener;)V

    return-object v0
.end method

.method public needsHeader()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method protected shouldUseUnicodeEscapeForCodePointInDoubleQuotedString(I)Z
    .locals 1

    const/16 v0, 0x20

    if-lt p1, v0, :cond_1

    const/16 v0, 0x5c

    if-eq p1, v0, :cond_1

    const/16 v0, 0x7f

    if-lt p1, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public supportsOverloadedMethods()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method protected abstract visibleGrammarSymbolCausesIssueInGeneratedCode(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Z
.end method

.method public wantsBaseListener()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public wantsBaseVisitor()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
