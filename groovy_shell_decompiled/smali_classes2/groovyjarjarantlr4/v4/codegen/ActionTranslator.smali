.class public Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;
.super Ljava/lang/Object;
.source "ActionTranslator.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/parse/ActionSplitterListener;


# static fields
.field public static final rulePropToModelMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Class<",
            "+",
            "Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef;",
            ">;>;"
        }
    .end annotation
.end field

.field public static final thisRulePropToModelMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Class<",
            "+",
            "Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef;",
            ">;>;"
        }
    .end annotation
.end field

.field public static final tokenPropToModelMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Class<",
            "+",
            "Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef;",
            ">;>;"
        }
    .end annotation
.end field


# instance fields
.field chunks:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionChunk;",
            ">;"
        }
    .end annotation
.end field

.field factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

.field gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

.field node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

.field nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

.field rf:Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 60
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->thisRulePropToModelMap:Ljava/util/Map;

    .line 63
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ThisRulePropertyRef_start;

    const-string v2, "start"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 64
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ThisRulePropertyRef_stop;

    const-string v3, "stop"

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 65
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ThisRulePropertyRef_text;

    const-string v4, "text"

    invoke-interface {v0, v4, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 66
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ThisRulePropertyRef_ctx;

    const-string v5, "ctx"

    invoke-interface {v0, v5, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 67
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ThisRulePropertyRef_parser;

    const-string v6, "parser"

    invoke-interface {v0, v6, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 70
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->rulePropToModelMap:Ljava/util/Map;

    .line 73
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef_start;

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 74
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef_stop;

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 75
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef_text;

    invoke-interface {v0, v4, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 76
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef_ctx;

    invoke-interface {v0, v5, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 77
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef_parser;

    invoke-interface {v0, v6, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 80
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->tokenPropToModelMap:Ljava/util/Map;

    .line 83
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef_text;

    invoke-interface {v0, v4, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 84
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef_type;

    const-string v2, "type"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 85
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef_line;

    const-string v2, "line"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 86
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef_index;

    const-string v2, "index"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 87
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef_pos;

    const-string v2, "pos"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 88
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef_channel;

    const-string v2, "channel"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 89
    const-class v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef_int;

    const-string v2, "int"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 1

    .line 99
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 95
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    .line 100
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    .line 101
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 102
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    return-void
.end method

.method public static toString(Ljava/util/List;)Ljava/lang/String;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionChunk;",
            ">;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 106
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 107
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionChunk;

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 108
    :cond_0
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static translateAction(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;",
            "Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;",
            "Lgroovyjarjarantlr4/runtime/Token;",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionChunk;",
            ">;"
        }
    .end annotation

    .line 116
    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    if-eqz p2, :cond_0

    .line 117
    invoke-virtual {p2}, Ljava/lang/String;->length()I

    move-result v0

    if-lez v0, :cond_0

    const/4 v0, 0x0

    invoke-virtual {p2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    const/16 v1, 0x7b

    if-ne v0, v1, :cond_0

    .line 118
    invoke-virtual {p2, v1}, Ljava/lang/String;->indexOf(I)I

    move-result v0

    const/16 v1, 0x7d

    .line 119
    invoke-virtual {p2, v1}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v1

    if-ltz v0, :cond_0

    if-ltz v1, :cond_0

    add-int/lit8 v0, v0, 0x1

    .line 121
    invoke-virtual {p2, v0, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p2

    .line 124
    :cond_0
    invoke-static {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->translateActionChunk(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static translateActionChunk(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;",
            "Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionChunk;",
            ">;"
        }
    .end annotation

    .line 132
    iget-object v0, p3, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 133
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;

    invoke-direct {v1, p0, p3}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    .line 134
    iput-object p1, v1, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->rf:Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    .line 135
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v2

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "translate "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const-string v4, "action-translator"

    invoke-virtual {v2, v4, v3}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 136
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getAltLabel()Ljava/lang/String;

    move-result-object p3

    if-eqz p1, :cond_0

    .line 138
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v2

    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getEffectiveRuleContext(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    move-result-object v2

    iput-object v2, v1, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    if-eqz p3, :cond_0

    .line 139
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object p0

    invoke-virtual {p1, p0}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getEffectiveAltLabelContexts(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Ljava/util/Map;

    move-result-object p0

    invoke-interface {p0, p3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    iput-object p0, v1, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    .line 141
    :cond_0
    new-instance p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;

    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;-><init>(Ljava/lang/String;)V

    .line 142
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->setLine(I)V

    .line 143
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->setCharPositionInLine(I)V

    .line 144
    new-instance p1, Lgroovyjarjarantlr4/v4/parse/ActionSplitter;

    invoke-direct {p1, p0, v1}, Lgroovyjarjarantlr4/v4/parse/ActionSplitter;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;Lgroovyjarjarantlr4/v4/parse/ActionSplitterListener;)V

    .line 146
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/parse/ActionSplitter;->getActionTokens()Ljava/util/List;

    .line 147
    iget-object p0, v1, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    return-object p0
.end method


# virtual methods
.method public attr(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 3

    .line 152
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "attr "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "action-translator"

    invoke-virtual {p1, v1, v0}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 153
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {p1, v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeResolver;->resolveToAttribute(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    if-eqz p1, :cond_4

    .line 155
    sget-object v0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator$1;->$SwitchMap$org$antlr$v4$tool$AttributeDict$DictType:[I

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Attribute;->dict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->type:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->ordinal()I

    move-result p1

    aget p1, v0, p1

    const/4 v0, 0x1

    if-eq p1, v0, :cond_3

    const/4 v0, 0x2

    if-eq p1, v0, :cond_2

    const/4 v0, 0x3

    if-eq p1, v0, :cond_1

    const/4 v0, 0x4

    if-eq p1, v0, :cond_0

    goto :goto_0

    .line 159
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getRulePropertyRef(Lgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 158
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/LocalRef;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/LocalRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 157
    :cond_2
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/RetValueRef;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->rf:Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getEffectiveRuleContext(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    move-result-object v1

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/RetValueRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 156
    :cond_3
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ArgRef;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ArgRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 162
    :cond_4
    :goto_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {p1, v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeResolver;->resolvesToToken(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z

    move-result p1

    if-eqz p1, :cond_5

    .line 163
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenRef;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getTokenLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {v0, v1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void

    .line 166
    :cond_5
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {p1, v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeResolver;->resolvesToLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z

    move-result p1

    if-eqz p1, :cond_6

    .line 167
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/LabelRef;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getTokenLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {v0, v1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/LabelRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void

    .line 170
    :cond_6
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {p1, v0, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeResolver;->resolvesToListLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z

    move-result p1

    if-eqz p1, :cond_7

    .line 171
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ListLabelRef;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-direct {v0, v1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ListLabelRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void

    .line 174
    :cond_7
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p1

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    if-eqz p1, :cond_8

    .line 176
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/LabelRef;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getRuleLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {v0, v1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/LabelRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_8
    return-void
.end method

.method public getRuleLabel(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 291
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {v0, p1, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeResolver;->resolvesToLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p1

    .line 292
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getImplicitRuleLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method getRulePropertyRef(Lgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef;
    .locals 6

    const/4 v0, 0x0

    .line 258
    :try_start_0
    sget-object v1, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->thisRulePropToModelMap:Ljava/util/Map;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Class;

    const/4 v2, 0x2

    new-array v3, v2, [Ljava/lang/Class;

    .line 259
    const-class v4, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    aput-object v4, v3, v0

    const-class v4, Ljava/lang/String;

    const/4 v5, 0x1

    aput-object v4, v3, v5

    invoke-virtual {v1, v3}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v1

    new-array v2, v2, [Ljava/lang/Object;

    .line 260
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    aput-object v3, v2, v0

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getRuleLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    aput-object p1, v2, v5

    invoke-virtual {v1, v2}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 265
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v1

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {v1, v2, p1, v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method getRulePropertyRef(Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef;
    .locals 7

    .line 271
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    .line 273
    :try_start_0
    sget-object v3, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->rulePropToModelMap:Ljava/util/Map;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v4

    invoke-interface {v3, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Class;

    const/4 v4, 0x2

    new-array v5, v4, [Ljava/lang/Class;

    .line 274
    const-class v6, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    aput-object v6, v5, v1

    const-class v6, Ljava/lang/String;

    aput-object v6, v5, v2

    invoke-virtual {v3, v5}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v3

    new-array v4, v4, [Ljava/lang/Object;

    .line 275
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    aput-object v5, v4, v1

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getRuleLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    aput-object p1, v4, v2

    invoke-virtual {v3, v4}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 280
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v2, v2, [Ljava/lang/Object;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    aput-object p2, v2, v1

    invoke-virtual {v0, v3, p1, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method public getTokenLabel(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 286
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {v0, p1, v1}, Lgroovyjarjarantlr4/v4/tool/AttributeResolver;->resolvesToLabel(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p1

    .line 287
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getImplicitTokenLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method getTokenPropertyRef(Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef;
    .locals 5

    const/4 v0, 0x0

    .line 243
    :try_start_0
    sget-object v1, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->tokenPropToModelMap:Ljava/util/Map;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-interface {v1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/lang/Class;

    const/4 v1, 0x2

    new-array v2, v1, [Ljava/lang/Class;

    .line 244
    const-class v3, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    aput-object v3, v2, v0

    const-class v3, Ljava/lang/String;

    const/4 v4, 0x1

    aput-object v3, v2, v4

    invoke-virtual {p2, v2}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object p2

    new-array v1, v1, [Ljava/lang/Object;

    .line 245
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    aput-object v2, v1, v0

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getTokenLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    aput-object p1, v1, v4

    invoke-virtual {p2, v1}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 250
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p2

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p2, v1, p1, v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method public nonLocalAttr(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 3

    .line 222
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "nonLocalAttr "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "::"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "action-translator"

    invoke-virtual {p1, v1, v0}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 223
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p1

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    .line 224
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/NonLocalAttrRef;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p3

    iget p1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    invoke-direct {v1, v2, p2, p3, p1}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/NonLocalAttrRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;Ljava/lang/String;I)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public qualifiedAttr(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 5

    .line 182
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "qattr "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "."

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v3, "action-translator"

    invoke-virtual {v0, v3, v1}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 183
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v1

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {v0, v1, v3}, Lgroovyjarjarantlr4/v4/tool/AttributeResolver;->resolveToAttribute(Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 185
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->attr(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;)V

    .line 186
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance p2, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionText;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {v1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-direct {p2, v0, p3}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionText;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void

    .line 189
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->resolver:Lgroovyjarjarantlr4/v4/tool/AttributeResolver;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-interface {p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/tool/AttributeResolver;->resolveToAttribute(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Lgroovyjarjarantlr4/v4/tool/Attribute;

    move-result-object p1

    const/4 v0, 0x2

    const/4 v1, 0x1

    if-nez p1, :cond_1

    .line 192
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object p3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->UNKNOWN_SIMPLE_ATTRIBUTE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v3, 0x0

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v4

    aput-object v4, v0, v3

    const-string v3, "rule"

    aput-object v3, v0, v1

    invoke-virtual {p1, p3, v2, p2, v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    return-void

    .line 198
    :cond_1
    sget-object v2, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator$1;->$SwitchMap$org$antlr$v4$tool$AttributeDict$DictType:[I

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Attribute;->dict:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->type:Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/AttributeDict$DictType;->ordinal()I

    move-result p1

    aget p1, v2, p1

    if-eq p1, v1, :cond_5

    if-eq p1, v0, :cond_4

    const/4 v0, 0x4

    if-eq p1, v0, :cond_3

    const/4 v0, 0x5

    if-eq p1, v0, :cond_2

    goto :goto_0

    .line 207
    :cond_2
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    invoke-virtual {p0, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getTokenPropertyRef(Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 204
    :cond_3
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    invoke-virtual {p0, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getRulePropertyRef(Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)Lgroovyjarjarantlr4/v4/codegen/model/chunk/RulePropertyRef;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 201
    :cond_4
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/QRetValueRef;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->getRuleLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p3

    invoke-direct {v0, v1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/QRetValueRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;Ljava/lang/String;)V

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 199
    :cond_5
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance p2, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ArgRef;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p3

    invoke-direct {p2, v0, p3}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ArgRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_0
    return-void
.end method

.method public setAttr(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 2

    .line 214
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "setAttr "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "action-translator"

    invoke-virtual {p1, v1, v0}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 215
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->rf:Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p3

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-static {p1, v0, p3, v1}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->translateActionChunk(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;

    move-result-object p1

    .line 216
    new-instance p3, Lgroovyjarjarantlr4/v4/codegen/model/chunk/SetAttr;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p3, v0, p2, p1}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/SetAttr;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;Ljava/util/List;)V

    .line 217
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    invoke-interface {p1, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public setNonLocalAttr(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 9

    .line 229
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->gen:Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "setNonLocalAttr "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "::"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "action-translator"

    invoke-virtual {p1, v1, v0}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 230
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p1

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    .line 231
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->rf:Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    invoke-interface {p4}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p4

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->node:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-static {v0, v1, p4, v2}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->translateActionChunk(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Ljava/lang/String;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;

    move-result-object v8

    .line 232
    new-instance p4, Lgroovyjarjarantlr4/v4/codegen/model/chunk/SetNonLocalAttr;

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v5

    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v6

    iget v7, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    move-object v3, p4

    invoke-direct/range {v3 .. v8}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/SetNonLocalAttr;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;Ljava/lang/String;ILjava/util/List;)V

    .line 233
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    invoke-interface {p1, p4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public text(Ljava/lang/String;)V
    .locals 3

    .line 238
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->chunks:Ljava/util/List;

    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionText;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->nodeContext:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-direct {v1, v2, p1}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionText;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method
