.class public Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;
.super Ljava/lang/Object;
.source "CodeGenPipeline.java"


# instance fields
.field g:Lgroovyjarjarantlr4/v4/tool/Grammar;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 0

    .line 21
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 22
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    return-void
.end method


# virtual methods
.method public process()V
    .locals 10

    .line 26
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 27
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v1

    if-nez v1, :cond_0

    return-void

    .line 32
    :cond_0
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v3, 0x0

    new-array v4, v3, [I

    invoke-direct {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    const/16 v4, 0x1c

    .line 33
    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v4, 0x39

    .line 34
    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    const/16 v4, 0x42

    .line 35
    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    .line 36
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v4, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getNodesWithType(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/List;

    move-result-object v2

    .line 37
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    const/4 v5, 0x1

    if-eqz v4, :cond_2

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 38
    invoke-virtual {v1, v4}, Lgroovyjarjarantlr4/v4/codegen/Target;->grammarSymbolCausesIssueInGeneratedCode(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Z

    move-result v6

    if-eqz v6, :cond_1

    .line 39
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v7, Lgroovyjarjarantlr4/v4/tool/ErrorType;->USE_OF_BAD_WORD:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v8, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v8, v8, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v9

    new-array v5, v5, [Ljava/lang/Object;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v4

    aput-object v4, v5, v3

    invoke-virtual {v6, v7, v8, v9, v5}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_0

    .line 48
    :cond_2
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v2

    .line 50
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v4

    if-eqz v4, :cond_4

    .line 51
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v1

    if-eqz v1, :cond_3

    .line 52
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateLexer(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 53
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v4

    if-ne v4, v2, :cond_3

    .line 54
    invoke-virtual {p0, v1, v0, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->writeRecognizer(Lorg/stringtemplate/v4/ST;Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;Z)V

    .line 57
    :cond_3
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateLexer(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 58
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v4

    if-ne v4, v2, :cond_e

    .line 59
    invoke-virtual {p0, v1, v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->writeRecognizer(Lorg/stringtemplate/v4/ST;Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;Z)V

    goto/16 :goto_1

    .line 63
    :cond_4
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v4

    if-eqz v4, :cond_5

    .line 64
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateParser(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v4

    .line 65
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v6

    if-ne v6, v2, :cond_5

    .line 66
    invoke-virtual {p0, v4, v0, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->writeRecognizer(Lorg/stringtemplate/v4/ST;Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;Z)V

    .line 69
    :cond_5
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateParser(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v4

    .line 70
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v6

    if-ne v6, v2, :cond_6

    .line 71
    invoke-virtual {p0, v4, v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->writeRecognizer(Lorg/stringtemplate/v4/ST;Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;Z)V

    .line 74
    :cond_6
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-boolean v4, v4, Lgroovyjarjarantlr4/v4/Tool;->gen_listener:Z

    if-eqz v4, :cond_a

    .line 75
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v4

    if-eqz v4, :cond_7

    .line 76
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateListener(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v4

    .line 77
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v6

    if-ne v6, v2, :cond_7

    .line 78
    invoke-virtual {v0, v4, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeListener(Lorg/stringtemplate/v4/ST;Z)V

    .line 81
    :cond_7
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateListener(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v4

    .line 82
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v6

    if-ne v6, v2, :cond_8

    .line 83
    invoke-virtual {v0, v4, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeListener(Lorg/stringtemplate/v4/ST;Z)V

    .line 86
    :cond_8
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v4

    if-eqz v4, :cond_9

    .line 87
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateBaseListener(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v4

    .line 88
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v6

    if-ne v6, v2, :cond_9

    .line 89
    invoke-virtual {v0, v4, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeBaseListener(Lorg/stringtemplate/v4/ST;Z)V

    .line 92
    :cond_9
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->wantsBaseListener()Z

    move-result v4

    if-eqz v4, :cond_a

    .line 93
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateBaseListener(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v4

    .line 94
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v6

    if-ne v6, v2, :cond_a

    .line 95
    invoke-virtual {v0, v4, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeBaseListener(Lorg/stringtemplate/v4/ST;Z)V

    .line 99
    :cond_a
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-boolean v4, v4, Lgroovyjarjarantlr4/v4/Tool;->gen_visitor:Z

    if-eqz v4, :cond_e

    .line 100
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v4

    if-eqz v4, :cond_b

    .line 101
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateVisitor(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v4

    .line 102
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v6

    if-ne v6, v2, :cond_b

    .line 103
    invoke-virtual {v0, v4, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeVisitor(Lorg/stringtemplate/v4/ST;Z)V

    .line 106
    :cond_b
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateVisitor(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v4

    .line 107
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v6

    if-ne v6, v2, :cond_c

    .line 108
    invoke-virtual {v0, v4, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeVisitor(Lorg/stringtemplate/v4/ST;Z)V

    .line 111
    :cond_c
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->needsHeader()Z

    move-result v4

    if-eqz v4, :cond_d

    .line 112
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateBaseVisitor(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v4

    .line 113
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v6

    if-ne v6, v2, :cond_d

    .line 114
    invoke-virtual {v0, v4, v5}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeBaseVisitor(Lorg/stringtemplate/v4/ST;Z)V

    .line 117
    :cond_d
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->wantsBaseVisitor()Z

    move-result v1

    if-eqz v1, :cond_e

    .line 118
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateBaseVisitor(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 119
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v4

    if-ne v4, v2, :cond_e

    .line 120
    invoke-virtual {v0, v1, v3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeBaseVisitor(Lorg/stringtemplate/v4/ST;Z)V

    .line 125
    :cond_e
    :goto_1
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeVocabFile()V

    return-void
.end method

.method protected writeRecognizer(Lorg/stringtemplate/v4/ST;Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;Z)V
    .locals 4

    .line 129
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-boolean v0, v0, Lgroovyjarjarantlr4/v4/Tool;->launch_ST_inspector:Z

    if-eqz v0, :cond_0

    .line 130
    invoke-virtual {p1}, Lorg/stringtemplate/v4/ST;->inspect()Lorg/stringtemplate/v4/gui/STViz;

    move-result-object v0

    .line 131
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-boolean v1, v1, Lgroovyjarjarantlr4/v4/Tool;->ST_inspector_wait_for_close:Z

    if-eqz v1, :cond_0

    .line 133
    :try_start_0
    invoke-virtual {v0}, Lorg/stringtemplate/v4/gui/STViz;->waitForClose()V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 136
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/4 v3, 0x0

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {v1, v2, v0, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    .line 141
    :cond_0
    :goto_0
    invoke-virtual {p2, p1, p3}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->writeRecognizer(Lorg/stringtemplate/v4/ST;Z)V

    return-void
.end method
