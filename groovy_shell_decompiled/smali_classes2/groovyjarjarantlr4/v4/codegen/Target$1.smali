.class Lgroovyjarjarantlr4/v4/codegen/Target$1;
.super Ljava/lang/Object;
.source "Target.java"

# interfaces
.implements Lorg/stringtemplate/v4/STErrorListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/codegen/Target;->loadTemplates()Lorg/stringtemplate/v4/STGroup;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/codegen/Target;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/codegen/Target;)V
    .locals 0

    .line 387
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/Target$1;->this$0:Lgroovyjarjarantlr4/v4/codegen/Target;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private reportError(Lorg/stringtemplate/v4/misc/STMessage;)V
    .locals 5

    .line 409
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/Target$1;->this$0:Lgroovyjarjarantlr4/v4/codegen/Target;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getCodeGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->STRING_TEMPLATE_WARNING:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v2, p1, Lorg/stringtemplate/v4/misc/STMessage;->cause:Ljava/lang/Throwable;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {p1}, Lorg/stringtemplate/v4/misc/STMessage;->toString()Ljava/lang/String;

    move-result-object p1

    const/4 v4, 0x0

    aput-object p1, v3, v4

    invoke-virtual {v0, v1, v2, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public IOError(Lorg/stringtemplate/v4/misc/STMessage;)V
    .locals 0

    .line 400
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target$1;->reportError(Lorg/stringtemplate/v4/misc/STMessage;)V

    return-void
.end method

.method public compileTimeError(Lorg/stringtemplate/v4/misc/STMessage;)V
    .locals 0

    .line 390
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target$1;->reportError(Lorg/stringtemplate/v4/misc/STMessage;)V

    return-void
.end method

.method public internalError(Lorg/stringtemplate/v4/misc/STMessage;)V
    .locals 0

    .line 405
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target$1;->reportError(Lorg/stringtemplate/v4/misc/STMessage;)V

    return-void
.end method

.method public runTimeError(Lorg/stringtemplate/v4/misc/STMessage;)V
    .locals 0

    .line 395
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target$1;->reportError(Lorg/stringtemplate/v4/misc/STMessage;)V

    return-void
.end method
