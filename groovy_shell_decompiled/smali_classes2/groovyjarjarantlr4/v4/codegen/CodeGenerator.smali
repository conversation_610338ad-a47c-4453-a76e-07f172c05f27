.class public Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;
.super Ljava/lang/Object;
.source "CodeGenerator.java"


# static fields
.field public static final DEFAULT_LANGUAGE:Ljava/lang/String; = "Java"

.field public static final TEMPLATE_ROOT:Ljava/lang/String; = "groovyjarjarantlr4/v4/tool/templates/codegen"

.field public static final VOCAB_FILE_EXTENSION:Ljava/lang/String; = ".tokens"

.field public static final vocabFilePattern:Ljava/lang/String; = "<tokens.keys:{t | <t>=<tokens.(t)>\n}><literals.keys:{t | <t>=<literals.(t)>\n}>"


# instance fields
.field public final g:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field public final language:Ljava/lang/String;

.field public lineWidth:I

.field private target:Lgroovyjarjarantlr4/v4/codegen/Target;

.field public final tool:Lgroovyjarjarantlr4/v4/Tool;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)V
    .locals 1

    .line 53
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x48

    .line 47
    iput v0, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->lineWidth:I

    .line 54
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 55
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    if-eqz p3, :cond_0

    goto :goto_0

    :cond_0
    const-string p3, "Java"

    .line 56
    :goto_0
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->language:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 2

    .line 50
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    const-string v1, "language"

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v0, p1, v1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)V

    return-void
.end method

.method private createController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;
    .locals 2

    .line 115
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/ParserFactory;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/codegen/ParserFactory;-><init>(Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;)V

    .line 116
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    invoke-direct {v1, v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    .line 117
    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->setController(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)V

    return-object v1
.end method

.method private walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;
    .locals 3

    .line 122
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 127
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    invoke-direct {v1, v2, v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lorg/stringtemplate/v4/STGroup;)V

    .line 128
    invoke-virtual {v1, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    return-object p1

    .line 124
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "Cannot generate code without a target."

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method public generateBaseListener()Lorg/stringtemplate/v4/ST;
    .locals 1

    const/4 v0, 0x0

    .line 140
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateBaseListener(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    return-object v0
.end method

.method public generateBaseListener(Z)Lorg/stringtemplate/v4/ST;
    .locals 1

    .line 141
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->createController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildBaseListenerOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    return-object p1
.end method

.method public generateBaseVisitor()Lorg/stringtemplate/v4/ST;
    .locals 1

    const/4 v0, 0x0

    .line 146
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateBaseVisitor(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    return-object v0
.end method

.method public generateBaseVisitor(Z)Lorg/stringtemplate/v4/ST;
    .locals 1

    .line 147
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->createController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildBaseVisitorOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    return-object p1
.end method

.method public generateLexer()Lorg/stringtemplate/v4/ST;
    .locals 1

    const/4 v0, 0x0

    .line 131
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateLexer(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    return-object v0
.end method

.method public generateLexer(Z)Lorg/stringtemplate/v4/ST;
    .locals 1

    .line 132
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->createController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildLexerOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    return-object p1
.end method

.method public generateListener()Lorg/stringtemplate/v4/ST;
    .locals 1

    const/4 v0, 0x0

    .line 137
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateListener(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    return-object v0
.end method

.method public generateListener(Z)Lorg/stringtemplate/v4/ST;
    .locals 1

    .line 138
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->createController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildListenerOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    return-object p1
.end method

.method public generateParser()Lorg/stringtemplate/v4/ST;
    .locals 1

    const/4 v0, 0x0

    .line 134
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateParser(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    return-object v0
.end method

.method public generateParser(Z)Lorg/stringtemplate/v4/ST;
    .locals 1

    .line 135
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->createController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildParserOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    return-object p1
.end method

.method public generateVisitor()Lorg/stringtemplate/v4/ST;
    .locals 1

    const/4 v0, 0x0

    .line 143
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->generateVisitor(Z)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    return-object v0
.end method

.method public generateVisitor(Z)Lorg/stringtemplate/v4/ST;
    .locals 1

    .line 144
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->createController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v0

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildVisitorOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object p1

    return-object p1
.end method

.method public getBaseListenerFileName()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 263
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseListenerFileName(Z)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getBaseListenerFileName(Z)Ljava/lang/String;
    .locals 1

    .line 294
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 299
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getBaseListenerFileName(Z)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 296
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Cannot generate code without a target."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getBaseVisitorFileName()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 264
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseVisitorFileName(Z)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getBaseVisitorFileName(Z)Ljava/lang/String;
    .locals 1

    .line 303
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 308
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getBaseVisitorFileName(Z)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 305
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Cannot generate code without a target."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getHeaderFileName()Ljava/lang/String;
    .locals 3

    .line 319
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 324
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    const-string v1, "headerFileExtension"

    invoke-virtual {v0, v1}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 326
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRecognizerName()Ljava/lang/String;

    move-result-object v1

    .line 327
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v0}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 321
    :cond_1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Cannot generate code without a target."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public getListenerFileName()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 261
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getListenerFileName(Z)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getListenerFileName(Z)Ljava/lang/String;
    .locals 1

    .line 276
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 281
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getListenerFileName(Z)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 278
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Cannot generate code without a target."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getRecognizerFileName()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 260
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getRecognizerFileName(Z)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getRecognizerFileName(Z)Ljava/lang/String;
    .locals 1

    .line 267
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 272
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getRecognizerFileName(Z)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 269
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Cannot generate code without a target."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;
    .locals 1

    .line 61
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->target:Lgroovyjarjarantlr4/v4/codegen/Target;

    if-nez v0, :cond_0

    .line 62
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->language:Ljava/lang/String;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->loadLanguageTarget(Ljava/lang/String;)V

    .line 65
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->target:Lgroovyjarjarantlr4/v4/codegen/Target;

    return-object v0
.end method

.method public getTemplates()Lorg/stringtemplate/v4/STGroup;
    .locals 1

    .line 70
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 75
    :cond_0
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v0

    return-object v0
.end method

.method getTokenVocabOutput()Lorg/stringtemplate/v4/ST;
    .locals 6

    .line 157
    new-instance v0, Lorg/stringtemplate/v4/ST;

    const-string v1, "<tokens.keys:{t | <t>=<tokens.(t)>\n}><literals.keys:{t | <t>=<literals.(t)>\n}>"

    invoke-direct {v0, v1}, Lorg/stringtemplate/v4/ST;-><init>(Ljava/lang/String;)V

    .line 158
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 160
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    invoke-interface {v2}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_0
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    const/4 v4, 0x1

    if-eqz v3, :cond_1

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    .line 161
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/tool/Grammar;->tokenNameToTypeMap:Ljava/util/Map;

    invoke-interface {v5, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/Integer;

    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    move-result v5

    if-lt v5, v4, :cond_0

    .line 163
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v1, v3, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_1
    const-string v2, "tokens"

    .line 166
    invoke-virtual {v0, v2, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 169
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 170
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v2}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_2
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    .line 171
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/tool/Grammar;->stringLiteralToTypeMap:Ljava/util/Map;

    invoke-interface {v5, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/Integer;

    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    move-result v5

    if-lt v5, v4, :cond_2

    .line 173
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v1, v3, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    :cond_3
    const-string v2, "literals"

    .line 176
    invoke-virtual {v0, v2, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    return-object v0
.end method

.method public getVisitorFileName()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 262
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getVisitorFileName(Z)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getVisitorFileName(Z)Ljava/lang/String;
    .locals 1

    .line 285
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 290
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getVisitorFileName(Z)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 287
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Cannot generate code without a target."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getVocabFileName()Ljava/lang/String;
    .locals 2

    .line 315
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".tokens"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method protected loadLanguageTarget(Ljava/lang/String;)V
    .locals 5

    .line 79
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "groovyjarjarantlr4.v4.codegen.target."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "Target"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 81
    :try_start_0
    invoke-static {p1}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v2

    const-class v3, Lgroovyjarjarantlr4/v4/codegen/Target;

    invoke-virtual {v2, v3}, Ljava/lang/Class;->asSubclass(Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v2

    new-array v3, v1, [Ljava/lang/Class;

    .line 82
    const-class v4, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    aput-object v4, v3, v0

    invoke-virtual {v2, v3}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v2

    new-array v3, v1, [Ljava/lang/Object;

    aput-object p0, v3, v0

    .line 83
    invoke-virtual {v2, v3}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/codegen/Target;

    iput-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->target:Lgroovyjarjarantlr4/v4/codegen/Target;
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_4
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljava/lang/reflect/InvocationTargetException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/InstantiationException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v2

    .line 106
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_CREATE_TARGET_GENERATOR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v1, v1, [Ljava/lang/Object;

    aput-object p1, v1, v0

    invoke-virtual {v3, v4, v2, v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    goto :goto_0

    :catch_1
    move-exception v2

    .line 101
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_CREATE_TARGET_GENERATOR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v1, v1, [Ljava/lang/Object;

    aput-object p1, v1, v0

    invoke-virtual {v3, v4, v2, v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    goto :goto_0

    :catch_2
    move-exception v2

    .line 96
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_CREATE_TARGET_GENERATOR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v1, v1, [Ljava/lang/Object;

    aput-object p1, v1, v0

    invoke-virtual {v3, v4, v2, v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    goto :goto_0

    :catch_3
    move-exception v2

    .line 91
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_CREATE_TARGET_GENERATOR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v1, v1, [Ljava/lang/Object;

    aput-object p1, v1, v0

    invoke-virtual {v3, v4, v2, v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    goto :goto_0

    :catch_4
    move-exception v2

    .line 86
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v4, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_CREATE_TARGET_GENERATOR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v1, v1, [Ljava/lang/Object;

    aput-object p1, v1, v0

    invoke-virtual {v3, v4, v2, v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public write(Lorg/stringtemplate/v4/ST;Ljava/lang/String;)V
    .locals 4

    .line 244
    :try_start_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 245
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0, v1, p2}, Lgroovyjarjarantlr4/v4/Tool;->getOutputFileWriter(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)Ljava/io/Writer;

    move-result-object v0

    .line 246
    new-instance v1, Lorg/stringtemplate/v4/AutoIndentWriter;

    invoke-direct {v1, v0}, Lorg/stringtemplate/v4/AutoIndentWriter;-><init>(Ljava/io/Writer;)V

    .line 247
    iget v2, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->lineWidth:I

    invoke-interface {v1, v2}, Lorg/stringtemplate/v4/STWriter;->setLineWidth(I)V

    .line 248
    invoke-virtual {p1, v1}, Lorg/stringtemplate/v4/ST;->write(Lorg/stringtemplate/v4/STWriter;)I

    .line 249
    invoke-virtual {v0}, Ljava/io/Writer;->close()V

    .line 251
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 254
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_WRITE_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p2, v2, v3

    invoke-virtual {v0, v1, p1, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public writeBaseListener(Lorg/stringtemplate/v4/ST;Z)V
    .locals 2

    .line 200
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 205
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseListenerFileName(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, v1, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/Target;->genFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Lorg/stringtemplate/v4/ST;Ljava/lang/String;)V

    return-void

    .line 202
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "Cannot generate code without a target."

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public writeBaseVisitor(Lorg/stringtemplate/v4/ST;Z)V
    .locals 2

    .line 218
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 223
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseVisitorFileName(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, v1, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/Target;->genFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Lorg/stringtemplate/v4/ST;Ljava/lang/String;)V

    return-void

    .line 220
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "Cannot generate code without a target."

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public writeListener(Lorg/stringtemplate/v4/ST;Z)V
    .locals 2

    .line 191
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 196
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getListenerFileName(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, v1, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/Target;->genFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Lorg/stringtemplate/v4/ST;Ljava/lang/String;)V

    return-void

    .line 193
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "Cannot generate code without a target."

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public writeRecognizer(Lorg/stringtemplate/v4/ST;Z)V
    .locals 2

    .line 182
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 187
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getRecognizerFileName(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, v1, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/Target;->genFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Lorg/stringtemplate/v4/ST;Ljava/lang/String;)V

    return-void

    .line 184
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "Cannot generate code without a target."

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public writeVisitor(Lorg/stringtemplate/v4/ST;Z)V
    .locals 2

    .line 209
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 214
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getVisitorFileName(Z)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, v1, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/Target;->genFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Lorg/stringtemplate/v4/ST;Ljava/lang/String;)V

    return-void

    .line 211
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "Cannot generate code without a target."

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public writeVocabFile()V
    .locals 4

    .line 227
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 234
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTokenVocabOutput()Lorg/stringtemplate/v4/ST;

    move-result-object v1

    .line 235
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getVocabFileName()Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_0

    .line 237
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v0, v3, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/Target;->genFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Lorg/stringtemplate/v4/ST;Ljava/lang/String;)V

    :cond_0
    return-void

    .line 229
    :cond_1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Cannot generate code without a target."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
