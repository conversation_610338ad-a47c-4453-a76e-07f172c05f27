.class public Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;
.super Ljava/lang/Object;
.source "OutputModelWalker.java"


# instance fields
.field templates:Lorg/stringtemplate/v4/STGroup;

.field tool:Lgroovyjarjarantlr4/v4/Tool;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/Tool;Lorg/stringtemplate/v4/STGroup;)V
    .locals 0

    .line 49
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 50
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->tool:Lgroovyjarjarantlr4/v4/Tool;

    .line 51
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->templates:Lorg/stringtemplate/v4/STGroup;

    return-void
.end method


# virtual methods
.method public walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;
    .locals 17

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    .line 56
    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    .line 57
    invoke-virtual {v3}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v4

    const-string v5, " invalid]"

    const-string v6, "["

    const/4 v7, 0x0

    const/4 v8, 0x1

    if-nez v4, :cond_0

    .line 59
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->NO_MODEL_TO_TEMPLATE_MAPPING:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v8, v8, [Ljava/lang/Object;

    invoke-virtual {v3}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v8, v7

    invoke-virtual {v1, v2, v8}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    .line 60
    new-instance v1, Lorg/stringtemplate/v4/ST;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lorg/stringtemplate/v4/ST;-><init>(Ljava/lang/String;)V

    return-object v1

    :cond_0
    if-eqz v2, :cond_1

    .line 63
    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v9, "Header"

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .line 65
    :cond_1
    iget-object v9, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->templates:Lorg/stringtemplate/v4/STGroup;

    invoke-virtual {v9, v4}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v9

    if-nez v9, :cond_2

    .line 67
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CODE_GEN_TEMPLATES_INCOMPLETE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v3, v8, [Ljava/lang/Object;

    aput-object v4, v3, v7

    invoke-virtual {v1, v2, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    .line 68
    new-instance v1, Lorg/stringtemplate/v4/ST;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Lorg/stringtemplate/v4/ST;-><init>(Ljava/lang/String;)V

    return-object v1

    .line 70
    :cond_2
    iget-object v5, v9, Lorg/stringtemplate/v4/ST;->impl:Lorg/stringtemplate/v4/compiler/CompiledST;

    iget-object v5, v5, Lorg/stringtemplate/v4/compiler/CompiledST;->formalArguments:Ljava/util/Map;

    const/4 v6, 0x2

    if-nez v5, :cond_3

    .line 71
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CODE_TEMPLATE_ARG_ISSUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v3, v6, [Ljava/lang/Object;

    aput-object v4, v3, v7

    const-string v4, "<none>"

    aput-object v4, v3, v8

    invoke-virtual {v1, v2, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    return-object v9

    .line 75
    :cond_3
    iget-object v5, v9, Lorg/stringtemplate/v4/ST;->impl:Lorg/stringtemplate/v4/compiler/CompiledST;

    iget-object v5, v5, Lorg/stringtemplate/v4/compiler/CompiledST;->formalArguments:Ljava/util/Map;

    .line 78
    invoke-interface {v5}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v10

    .line 79
    invoke-interface {v10}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v10

    .line 80
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Ljava/lang/String;

    .line 81
    invoke-virtual {v9, v10, v1}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 84
    new-instance v10, Ljava/util/HashSet;

    invoke-direct {v10}, Ljava/util/HashSet;-><init>()V

    .line 85
    invoke-virtual {v3}, Ljava/lang/Class;->getFields()[Ljava/lang/reflect/Field;

    move-result-object v3

    .line 86
    array-length v11, v3

    move v12, v7

    :goto_0
    if-ge v12, v11, :cond_f

    aget-object v13, v3, v12

    .line 87
    const-class v14, Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;

    invoke-virtual {v13, v14}, Ljava/lang/reflect/Field;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    move-result-object v14

    check-cast v14, Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;

    if-nez v14, :cond_4

    move v15, v7

    move/from16 v16, v8

    move v8, v6

    goto/16 :goto_5

    .line 92
    :cond_4
    invoke-virtual {v13}, Ljava/lang/reflect/Field;->getName()Ljava/lang/String;

    move-result-object v14

    .line 94
    invoke-interface {v10, v14}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v15

    if-nez v15, :cond_6

    .line 95
    iget-object v13, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v13, v13, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v15, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v6, v8, [Ljava/lang/Object;

    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "Model object "

    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    const-string v8, " has multiple fields named \'"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    const-string v8, "\'"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    const/4 v8, 0x0

    aput-object v7, v6, v8

    invoke-virtual {v13, v15, v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    :cond_5
    :goto_1
    const/4 v8, 0x2

    const/4 v15, 0x0

    const/16 v16, 0x1

    goto/16 :goto_5

    .line 100
    :cond_6
    invoke-interface {v5, v14}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    if-nez v6, :cond_7

    goto :goto_1

    .line 103
    :cond_7
    :try_start_0
    invoke-virtual {v13, v1}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    .line 104
    instance-of v7, v6, Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    if-eqz v7, :cond_8

    .line 105
    check-cast v6, Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    .line 106
    invoke-virtual {v0, v6, v2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object v6

    .line 108
    invoke-virtual {v9, v14, v6}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_1

    .line 110
    :cond_8
    instance-of v7, v6, Ljava/util/Collection;

    if-nez v7, :cond_c

    instance-of v7, v6, [Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    if-eqz v7, :cond_9

    goto :goto_3

    .line 123
    :cond_9
    instance-of v7, v6, Ljava/util/Map;

    if-eqz v7, :cond_b

    .line 124
    check-cast v6, Ljava/util/Map;

    .line 125
    new-instance v7, Ljava/util/LinkedHashMap;

    invoke-direct {v7}, Ljava/util/LinkedHashMap;-><init>()V

    .line 126
    invoke-interface {v6}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :goto_2
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v8

    if-eqz v8, :cond_a

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Ljava/util/Map$Entry;

    .line 127
    invoke-interface {v8}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    invoke-virtual {v0, v13, v2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object v13

    .line 129
    invoke-interface {v8}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v8

    invoke-interface {v7, v8, v13}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    .line 131
    :cond_a
    invoke-virtual {v9, v14, v7}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_1

    :cond_b
    if-eqz v6, :cond_5

    .line 134
    iget-object v6, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v7, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/4 v8, 0x1

    new-array v13, v8, [Ljava/lang/Object;

    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v15, "not recognized nested model element: "

    invoke-virtual {v8, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    const/4 v15, 0x0

    aput-object v8, v13, v15

    invoke-virtual {v6, v7, v13}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    goto/16 :goto_1

    .line 112
    :cond_c
    :goto_3
    instance-of v7, v6, [Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    if-eqz v7, :cond_d

    .line 113
    check-cast v6, [Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    check-cast v6, [Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    invoke-static {v6}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v6

    .line 115
    :cond_d
    check-cast v6, Ljava/util/Collection;

    .line 116
    invoke-interface {v6}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :goto_4
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_5

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    if-nez v7, :cond_e

    goto :goto_4

    .line 118
    :cond_e
    check-cast v7, Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    invoke-virtual {v0, v7, v2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->walk(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;Z)Lorg/stringtemplate/v4/ST;

    move-result-object v7

    .line 120
    invoke-virtual {v9, v14, v7}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;
    :try_end_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_4

    .line 138
    :catch_0
    iget-object v6, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelWalker;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v7, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CODE_TEMPLATE_ARG_ISSUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/4 v8, 0x2

    new-array v13, v8, [Ljava/lang/Object;

    const/4 v15, 0x0

    aput-object v4, v13, v15

    const/16 v16, 0x1

    aput-object v14, v13, v16

    invoke-virtual {v6, v7, v13}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    :goto_5
    add-int/lit8 v12, v12, 0x1

    move v6, v8

    move v7, v15

    move/from16 v8, v16

    goto/16 :goto_0

    :cond_f
    return-object v9
.end method
