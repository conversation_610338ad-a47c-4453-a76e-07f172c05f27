.class public Lgroovyjarjarantlr4/v4/codegen/OutputModelController;
.super Ljava/lang/Object;
.source "OutputModelController.java"


# instance fields
.field public codeBlockLevel:I

.field public currentBlock:Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;

.field public currentOuterMostAlt:Lgroovyjarjarantlr4/v4/tool/Alternative;

.field public currentOuterMostAlternativeBlock:Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

.field public currentRule:Ljava/util/Stack;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Stack<",
            "Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;",
            ">;"
        }
    .end annotation
.end field

.field public delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

.field public extensions:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;",
            ">;"
        }
    .end annotation
.end field

.field public root:Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

.field public treeLevel:I

.field public walker:Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V
    .locals 1

    .line 79
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 63
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    const/4 v0, -0x1

    .line 71
    iput v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->codeBlockLevel:I

    .line 72
    iput v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->treeLevel:I

    .line 74
    new-instance v0, Ljava/util/Stack;

    invoke-direct {v0}, Ljava/util/Stack;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentRule:Ljava/util/Stack;

    .line 80
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    return-void
.end method


# virtual methods
.method public action(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .line 416
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->action(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;

    move-result-object p1

    .line 417
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->action(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public addExtension(Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;)V
    .locals 1

    .line 83
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public alternative(Lgroovyjarjarantlr4/v4/tool/Alternative;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .locals 2

    .line 351
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->alternative(Lgroovyjarjarantlr4/v4/tool/Alternative;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    move-result-object p1

    if-eqz p2, :cond_0

    .line 353
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentOuterMostAlternativeBlock:Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    .line 355
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v1, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->alternative(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    move-result-object p1

    goto :goto_0

    :cond_1
    return-object p1
.end method

.method public buildBaseListenerOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
    .locals 3

    .line 123
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    .line 124
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/BaseListenerFile;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseListenerFileName(Z)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lgroovyjarjarantlr4/v4/codegen/model/BaseListenerFile;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    return-object v1
.end method

.method public buildBaseVisitorOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
    .locals 3

    .line 133
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    .line 134
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/BaseVisitorFile;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getBaseVisitorFileName(Z)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lgroovyjarjarantlr4/v4/codegen/model/BaseVisitorFile;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    return-object v1
.end method

.method public buildLeftRecursiveRuleFunction(Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;Lgroovyjarjarantlr4/v4/codegen/model/LeftRecursiveRuleFunction;)V
    .locals 16

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    .line 191
    invoke-virtual/range {p0 .. p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildNormalRuleFunction(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)V

    .line 193
    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/codegen/model/LeftRecursiveRuleFunction;->getEffectiveRuleContext(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    move-result-object v3

    .line 194
    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/codegen/model/LeftRecursiveRuleFunction;->getEffectiveAltLabelContexts(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Ljava/util/Map;

    move-result-object v4

    .line 197
    iget-object v5, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v5}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v5

    .line 198
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/codegen/Target;->getTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v5

    .line 201
    iget-object v6, v2, Lgroovyjarjarantlr4/v4/codegen/model/LeftRecursiveRuleFunction;->code:Ljava/util/List;

    const/4 v7, 0x0

    invoke-interface {v6, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    .line 202
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    .line 203
    iget-object v9, v6, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;->ops:Ljava/util/List;

    invoke-interface {v9, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;

    .line 204
    instance-of v10, v9, Lgroovyjarjarantlr4/v4/codegen/model/Choice;

    if-eqz v10, :cond_0

    .line 205
    check-cast v9, Lgroovyjarjarantlr4/v4/codegen/model/Choice;

    .line 206
    iget-object v9, v9, Lgroovyjarjarantlr4/v4/codegen/model/Choice;->alts:Ljava/util/List;

    invoke-interface {v8, v9}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 209
    :cond_0
    check-cast v9, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    invoke-interface {v8, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 213
    :goto_0
    iget-object v9, v6, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;->ops:Ljava/util/List;

    const/4 v10, 0x1

    invoke-interface {v9, v10}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lgroovyjarjarantlr4/v4/codegen/model/StarBlock;

    .line 214
    iget-object v11, v9, Lgroovyjarjarantlr4/v4/codegen/model/StarBlock;->alts:Ljava/util/List;

    invoke-interface {v11, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    .line 215
    new-instance v12, Ljava/util/ArrayList;

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    .line 216
    iget-object v11, v11, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;->ops:Ljava/util/List;

    invoke-interface {v11, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;

    .line 217
    instance-of v13, v11, Lgroovyjarjarantlr4/v4/codegen/model/AltBlock;

    if-eqz v13, :cond_1

    .line 218
    check-cast v11, Lgroovyjarjarantlr4/v4/codegen/model/AltBlock;

    .line 219
    iget-object v11, v11, Lgroovyjarjarantlr4/v4/codegen/model/AltBlock;->alts:Ljava/util/List;

    invoke-interface {v12, v11}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_1

    .line 222
    :cond_1
    check-cast v11, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    invoke-interface {v12, v11}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_1
    move v11, v7

    .line 226
    :goto_2
    invoke-interface {v8}, Ljava/util/List;->size()I

    move-result v13

    const-string v14, "ctxName"

    if-ge v11, v13, :cond_3

    .line 227
    iget-object v13, v1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recPrimaryAlts:Ljava/util/List;

    invoke-interface {v13, v11}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 228
    iget-object v15, v13, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    if-nez v15, :cond_2

    goto :goto_3

    :cond_2
    const-string v15, "recRuleReplaceContext"

    .line 229
    invoke-virtual {v5, v15}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v15

    .line 230
    iget-object v10, v13, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    invoke-static {v10}, Lgroovyjarjarantlr4/v4/misc/Utils;->capitalize(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v15, v14, v10}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 231
    new-instance v10, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    iget-object v14, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    iget-object v13, v13, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    invoke-interface {v4, v13}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-direct {v10, v14, v13, v15}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Lorg/stringtemplate/v4/ST;)V

    .line 233
    invoke-interface {v8, v11}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v13

    check-cast v13, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    .line 234
    invoke-virtual {v13, v7, v10}, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;->insertOp(ILgroovyjarjarantlr4/v4/codegen/model/SrcOp;)V

    :goto_3
    add-int/lit8 v11, v11, 0x1

    const/4 v10, 0x1

    goto :goto_2

    :cond_3
    const-string v8, "recRuleSetStopToken"

    .line 238
    invoke-virtual {v5, v8}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v8

    .line 239
    new-instance v10, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    iget-object v11, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v10, v11, v3, v8}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Lorg/stringtemplate/v4/ST;)V

    const/4 v8, 0x1

    .line 240
    invoke-virtual {v6, v8, v10}, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;->insertOp(ILgroovyjarjarantlr4/v4/codegen/model/SrcOp;)V

    const-string v6, "recRuleSetPrevCtx"

    .line 243
    invoke-virtual {v5, v6}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v6

    .line 244
    new-instance v8, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    iget-object v10, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v8, v10, v3, v6}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Lorg/stringtemplate/v4/ST;)V

    .line 245
    invoke-virtual {v9, v8}, Lgroovyjarjarantlr4/v4/codegen/model/StarBlock;->addIterationOp(Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;)V

    move v3, v7

    .line 248
    :goto_4
    invoke-interface {v12}, Ljava/util/List;->size()I

    move-result v6

    if-ge v3, v6, :cond_7

    .line 250
    iget-object v6, v1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->recOpAlts:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v6, v3}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->getElement(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;

    .line 252
    iget-object v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    if-eqz v8, :cond_4

    const-string v8, "recRuleLabeledAltStartAction"

    .line 254
    invoke-virtual {v5, v8}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v9

    .line 255
    iget-object v10, v6, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    const-string v11, "currentAltLabel"

    invoke-virtual {v9, v11, v10}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 256
    iget-object v10, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v10}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v10

    invoke-virtual {v10, v2}, Lgroovyjarjarantlr4/v4/codegen/Target;->getRuleFunctionContextStructName(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v14, v10}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_5

    :cond_4
    const-string v8, "recRuleAltStartAction"

    .line 260
    invoke-virtual {v5, v8}, Lorg/stringtemplate/v4/STGroup;->getInstanceOf(Ljava/lang/String;)Lorg/stringtemplate/v4/ST;

    move-result-object v9

    .line 261
    iget-object v10, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v10}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v10

    invoke-virtual {v10, v2}, Lgroovyjarjarantlr4/v4/codegen/Target;->getRuleFunctionContextStructName(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v9, v14, v10}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 263
    :goto_5
    iget-object v10, v1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->name:Ljava/lang/String;

    const-string v11, "ruleName"

    invoke-virtual {v9, v11, v10}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 265
    iget-object v10, v6, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->leftRecursiveRuleRefLabel:Ljava/lang/String;

    const-string v11, "label"

    invoke-virtual {v9, v11, v10}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    .line 266
    iget-object v10, v9, Lorg/stringtemplate/v4/ST;->impl:Lorg/stringtemplate/v4/compiler/CompiledST;

    iget-object v10, v10, Lorg/stringtemplate/v4/compiler/CompiledST;->formalArguments:Ljava/util/Map;

    const-string v11, "isListLabel"

    invoke-interface {v10, v11}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v10

    if-eqz v10, :cond_5

    .line 267
    iget-boolean v8, v6, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->isListLabel:Z

    invoke-static {v8}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v8

    invoke-virtual {v9, v11, v8}, Lorg/stringtemplate/v4/ST;->add(Ljava/lang/String;Ljava/lang/Object;)Lorg/stringtemplate/v4/ST;

    goto :goto_6

    .line 269
    :cond_5
    iget-boolean v10, v6, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->isListLabel:Z

    if-eqz v10, :cond_6

    .line 270
    iget-object v10, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v10}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v10

    iget-object v10, v10, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v10, v10, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v13, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CODE_TEMPLATE_ARG_ISSUE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/4 v15, 0x2

    new-array v15, v15, [Ljava/lang/Object;

    aput-object v8, v15, v7

    const/4 v8, 0x1

    aput-object v11, v15, v8

    invoke-virtual {v10, v13, v15}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    goto :goto_7

    :cond_6
    :goto_6
    const/4 v8, 0x1

    .line 272
    :goto_7
    new-instance v10, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    iget-object v11, v0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/analysis/LeftRecursiveRuleAltInfo;->altLabel:Ljava/lang/String;

    invoke-interface {v4, v6}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-direct {v10, v11, v6, v9}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Lorg/stringtemplate/v4/ST;)V

    .line 274
    invoke-interface {v12, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    .line 275
    invoke-virtual {v6, v7, v10}, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;->insertOp(ILgroovyjarjarantlr4/v4/codegen/model/SrcOp;)V

    add-int/lit8 v3, v3, 0x1

    goto/16 :goto_4

    :cond_7
    return-void
.end method

.method public buildLexerOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
    .locals 3

    .line 104
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    .line 105
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getRecognizerFileName(Z)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->lexerFile(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;

    move-result-object p1

    .line 106
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->setRoot(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;)V

    .line 107
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->lexer(Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;)Lgroovyjarjarantlr4/v4/codegen/model/Lexer;

    move-result-object v0

    iput-object v0, p1, Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;->lexer:Lgroovyjarjarantlr4/v4/codegen/model/Lexer;

    .line 109
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    .line 110
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 111
    iget-object v2, p1, Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;->lexer:Lgroovyjarjarantlr4/v4/codegen/model/Lexer;

    invoke-virtual {p0, v2, v1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildLexerRuleActions(Lgroovyjarjarantlr4/v4/codegen/model/Lexer;Lgroovyjarjarantlr4/v4/tool/Rule;)V

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public buildLexerRuleActions(Lgroovyjarjarantlr4/v4/codegen/model/Lexer;Lgroovyjarjarantlr4/v4/tool/Rule;)V
    .locals 9

    .line 301
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->actions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 305
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    .line 306
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    .line 307
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v1

    invoke-virtual {v1, p2}, Lgroovyjarjarantlr4/v4/codegen/Target;->getRuleFunctionContextStructName(Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/lang/String;

    move-result-object v1

    .line 308
    iget-object v2, p1, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;->actionFuncs:Ljava/util/LinkedHashMap;

    invoke-virtual {v2, p2}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/codegen/model/RuleActionFunction;

    if-nez v2, :cond_1

    .line 310
    new-instance v2, Lgroovyjarjarantlr4/v4/codegen/model/RuleActionFunction;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v2, v3, p2, v1}, Lgroovyjarjarantlr4/v4/codegen/model/RuleActionFunction;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/lang/String;)V

    .line 313
    :cond_1
    iget-object v3, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->actions:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_2
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_5

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 314
    instance-of v5, v4, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    if-eqz v5, :cond_4

    .line 315
    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    .line 316
    iget-object v5, p1, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;->sempredFuncs:Ljava/util/LinkedHashMap;

    invoke-virtual {v5, p2}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/codegen/model/RuleSempredFunction;

    if-nez v5, :cond_3

    .line 318
    new-instance v5, Lgroovyjarjarantlr4/v4/codegen/model/RuleSempredFunction;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v5, v6, p2, v1}, Lgroovyjarjarantlr4/v4/codegen/model/RuleSempredFunction;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/lang/String;)V

    .line 319
    iget-object v6, p1, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;->sempredFuncs:Ljava/util/LinkedHashMap;

    invoke-virtual {v6, p2, v5}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 321
    :cond_3
    iget-object v5, v5, Lgroovyjarjarantlr4/v4/codegen/model/RuleSempredFunction;->actions:Ljava/util/LinkedHashMap;

    iget-object v6, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->sempreds:Ljava/util/LinkedHashMap;

    invoke-virtual {v6, v4}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    new-instance v7, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    iget-object v8, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v7, v8, v4}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    invoke-virtual {v5, v6, v7}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 323
    :cond_4
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->getType()I

    move-result v5

    const/4 v6, 0x4

    if-ne v5, v6, :cond_2

    .line 324
    iget-object v5, v2, Lgroovyjarjarantlr4/v4/codegen/model/RuleActionFunction;->actions:Ljava/util/LinkedHashMap;

    iget-object v6, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->lexerActions:Ljava/util/LinkedHashMap;

    invoke-virtual {v6, v4}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    new-instance v7, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    iget-object v8, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v7, v8, v4}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    invoke-virtual {v5, v6, v7}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 328
    :cond_5
    iget-object v0, v2, Lgroovyjarjarantlr4/v4/codegen/model/RuleActionFunction;->actions:Ljava/util/LinkedHashMap;

    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_6

    iget-object v0, p1, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;->actionFuncs:Ljava/util/LinkedHashMap;

    invoke-virtual {v0, p2}, Ljava/util/LinkedHashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_6

    .line 330
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;->actionFuncs:Ljava/util/LinkedHashMap;

    invoke-virtual {p1, p2, v2}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_6
    return-void
.end method

.method public buildListenerOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
    .locals 3

    .line 118
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    .line 119
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/ListenerFile;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getListenerFileName(Z)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lgroovyjarjarantlr4/v4/codegen/model/ListenerFile;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    return-object v1
.end method

.method public buildNormalRuleFunction(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)V
    .locals 3

    .line 280
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    .line 282
    new-instance v0, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    .line 283
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Rule;->ast:Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    const/16 v2, 0x4e

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 284
    new-instance v2, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    invoke-direct {v2, v0, v1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/Object;)V

    .line 285
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;

    invoke-direct {v0, v2, p0}, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->walker:Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;

    const/4 v1, 0x0

    .line 288
    :try_start_0
    invoke-virtual {v0, v1, v1}, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;->block(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/codegen/DefaultOutputModelFactory;->list(Ljava/util/Collection;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p2, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->code:Ljava/util/List;

    .line 289
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->walker:Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;

    iget-boolean v0, v0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;->hasLookaheadBlock:Z

    iput-boolean v0, p2, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->hasLookaheadBlock:Z
    :try_end_0
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 292
    sget-object v1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/runtime/RecognitionException;->printStackTrace(Ljava/io/PrintStream;)V

    .line 295
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    invoke-virtual {v0, p2}, Lgroovyjarjarantlr4/v4/codegen/Target;->getRuleFunctionContextStructName(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p2, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ctxType:Ljava/lang/String;

    .line 297
    invoke-virtual {p0, p2, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->rulePostamble(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p2, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->postamble:Ljava/util/List;

    return-void
.end method

.method public buildParserOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
    .locals 3

    .line 90
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    .line 91
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getRecognizerFileName(Z)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->parserFile(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;

    move-result-object p1

    .line 92
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->setRoot(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;)V

    .line 93
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->parser(Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;)Lgroovyjarjarantlr4/v4/codegen/model/Parser;

    move-result-object v0

    iput-object v0, p1, Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;->parser:Lgroovyjarjarantlr4/v4/codegen/model/Parser;

    .line 95
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    .line 96
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 97
    iget-object v2, p1, Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;->parser:Lgroovyjarjarantlr4/v4/codegen/model/Parser;

    invoke-virtual {p0, v2, v1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildRuleFunction(Lgroovyjarjarantlr4/v4/codegen/model/Parser;Lgroovyjarjarantlr4/v4/tool/Rule;)V

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public buildRuleFunction(Lgroovyjarjarantlr4/v4/codegen/model/Parser;Lgroovyjarjarantlr4/v4/tool/Rule;)V
    .locals 8

    .line 161
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->rule(Lgroovyjarjarantlr4/v4/tool/Rule;)Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    move-result-object v0

    .line 162
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/codegen/model/Parser;->funcs:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 163
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->pushCurrentRule(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)V

    .line 164
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-virtual {v0, v1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->fillNamedActions(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;)V

    .line 166
    instance-of v1, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;

    if-eqz v1, :cond_0

    .line 167
    move-object v1, p2

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;

    move-object v2, v0

    check-cast v2, Lgroovyjarjarantlr4/v4/codegen/model/LeftRecursiveRuleFunction;

    invoke-virtual {p0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildLeftRecursiveRuleFunction(Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;Lgroovyjarjarantlr4/v4/codegen/model/LeftRecursiveRuleFunction;)V

    goto :goto_0

    .line 171
    :cond_0
    invoke-virtual {p0, p2, v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->buildNormalRuleFunction(Lgroovyjarjarantlr4/v4/tool/Rule;Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)V

    .line 174
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v1

    .line 175
    iget-object v2, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->actions:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 176
    instance-of v4, v3, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    if-eqz v4, :cond_1

    .line 177
    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    .line 178
    iget-object v4, p1, Lgroovyjarjarantlr4/v4/codegen/model/Parser;->sempredFuncs:Ljava/util/LinkedHashMap;

    invoke-virtual {v4, p2}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/codegen/model/RuleSempredFunction;

    if-nez v4, :cond_2

    .line 180
    new-instance v4, Lgroovyjarjarantlr4/v4/codegen/model/RuleSempredFunction;

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    iget-object v6, v0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ctxType:Ljava/lang/String;

    invoke-direct {v4, v5, p2, v6}, Lgroovyjarjarantlr4/v4/codegen/model/RuleSempredFunction;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/lang/String;)V

    .line 181
    iget-object v5, p1, Lgroovyjarjarantlr4/v4/codegen/model/Parser;->sempredFuncs:Ljava/util/LinkedHashMap;

    invoke-virtual {v5, p2, v4}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 183
    :cond_2
    iget-object v4, v4, Lgroovyjarjarantlr4/v4/codegen/model/RuleSempredFunction;->actions:Ljava/util/LinkedHashMap;

    iget-object v5, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->sempreds:Ljava/util/LinkedHashMap;

    invoke-virtual {v5, v3}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    new-instance v6, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    iget-object v7, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v6, v7, v3}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    invoke-virtual {v4, v5, v6}, Ljava/util/LinkedHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 187
    :cond_3
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->popCurrentRule()Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    return-void
.end method

.method public buildVisitorOutputModel(Z)Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
    .locals 3

    .line 128
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    .line 129
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getVisitorFileName(Z)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    return-object v1
.end method

.method public epsilon(Lgroovyjarjarantlr4/v4/tool/Alternative;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .locals 1

    .line 402
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->epsilon(Lgroovyjarjarantlr4/v4/tool/Alternative;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    move-result-object p1

    .line 403
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->epsilon(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public finishAlternative(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;Ljava/util/List;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;Z)",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;"
        }
    .end annotation

    .line 362
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->finishAlternative(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    move-result-object p1

    .line 363
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v0, p1, p3}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->finishAlternative(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public getChoiceBlock(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;Ljava/util/List;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation

    .line 428
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getChoiceBlock(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;Ljava/util/List;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;

    move-result-object p1

    .line 429
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->getChoiceBlock(Lgroovyjarjarantlr4/v4/codegen/model/Choice;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public getCodeBlockLevel()I
    .locals 1

    .line 481
    iget v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->codeBlockLevel:I

    return v0
.end method

.method public getCurrentBlock()Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;
    .locals 1

    .line 470
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentBlock:Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;

    return-object v0
.end method

.method public getCurrentOuterMostAlt()Lgroovyjarjarantlr4/v4/tool/Alternative;
    .locals 1

    .line 461
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentOuterMostAlt:Lgroovyjarjarantlr4/v4/tool/Alternative;

    return-object v0
.end method

.method public getCurrentOuterMostAlternativeBlock()Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;
    .locals 1

    .line 478
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentOuterMostAlternativeBlock:Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    return-object v0
.end method

.method public getCurrentRuleFunction()Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
    .locals 1

    .line 450
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentRule:Ljava/util/Stack;

    invoke-virtual {v0}, Ljava/util/Stack;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentRule:Ljava/util/Stack;

    invoke-virtual {v0}, Ljava/util/Stack;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public getEBNFBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation

    .line 434
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getEBNFBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;

    move-result-object p1

    .line 435
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->getEBNFBlock(Lgroovyjarjarantlr4/v4/codegen/model/Choice;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;
    .locals 1

    .line 348
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    return-object v0
.end method

.method public getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;
    .locals 1

    .line 346
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    return-object v0
.end method

.method public getRoot()Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
    .locals 1

    .line 445
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->root:Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    return-object v0
.end method

.method public lexer(Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;)Lgroovyjarjarantlr4/v4/codegen/model/Lexer;
    .locals 2

    .line 154
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v0, v1, p1}, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;)V

    return-object v0
.end method

.method public lexerFile(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;
    .locals 2

    .line 150
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v0, v1, p1}, Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    return-object v0
.end method

.method public needsImplicitLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/codegen/model/LabeledOp;)Z
    .locals 3

    .line 440
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->needsImplicitLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/codegen/model/LabeledOp;)Z

    move-result v0

    .line 441
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v2, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->needsImplicitLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/codegen/model/LabeledOp;)Z

    move-result v2

    or-int/2addr v0, v2

    goto :goto_0

    :cond_0
    return v0
.end method

.method public parser(Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;)Lgroovyjarjarantlr4/v4/codegen/model/Parser;
    .locals 2

    .line 144
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->parser(Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;)Lgroovyjarjarantlr4/v4/codegen/model/Parser;

    move-result-object p1

    .line 145
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->parser(Lgroovyjarjarantlr4/v4/codegen/model/Parser;)Lgroovyjarjarantlr4/v4/codegen/model/Parser;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public parserFile(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;
    .locals 2

    .line 138
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->parserFile(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;

    move-result-object p1

    .line 139
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->parserFile(Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;)Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public popCurrentRule()Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
    .locals 1

    .line 457
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentRule:Ljava/util/Stack;

    invoke-virtual {v0}, Ljava/util/Stack;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentRule:Ljava/util/Stack;

    invoke-virtual {v0}, Ljava/util/Stack;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public pushCurrentRule(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)V
    .locals 1

    .line 454
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentRule:Ljava/util/Stack;

    invoke-virtual {v0, p1}, Ljava/util/Stack;->push(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public rule(Lgroovyjarjarantlr4/v4/tool/Rule;)Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
    .locals 2

    .line 335
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->rule(Lgroovyjarjarantlr4/v4/tool/Rule;)Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    move-result-object p1

    .line 336
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->rule(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public rulePostamble(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .line 341
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->rulePostamble(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/util/List;

    move-result-object p1

    .line 342
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->rulePostamble(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public ruleRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .line 368
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->ruleRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;

    move-result-object p1

    .line 369
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    .line 370
    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->ruleRef(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public sempred(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .line 422
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->sempred(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;

    move-result-object p1

    .line 423
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->sempred(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public set(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Z)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Z)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .line 394
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->set(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Z)Ljava/util/List;

    move-result-object p1

    .line 395
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    .line 396
    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->set(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public setCurrentBlock(Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;)V
    .locals 0

    .line 466
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentBlock:Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;

    return-void
.end method

.method public setCurrentOuterMostAlt(Lgroovyjarjarantlr4/v4/tool/Alternative;)V
    .locals 0

    .line 463
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentOuterMostAlt:Lgroovyjarjarantlr4/v4/tool/Alternative;

    return-void
.end method

.method public setCurrentOuterMostAlternativeBlock(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;)V
    .locals 0

    .line 474
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->currentOuterMostAlternativeBlock:Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    return-void
.end method

.method public setRoot(Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;)V
    .locals 0

    .line 447
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->root:Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;

    return-void
.end method

.method public stringRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .line 385
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->stringRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;

    move-result-object p1

    .line 386
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    .line 387
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->stringRef(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public tokenRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .line 377
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->tokenRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;

    move-result-object p1

    .line 378
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    .line 379
    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->tokenRef(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public wildcard(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .line 408
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->delegate:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->wildcard(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;

    move-result-object p1

    .line 409
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->extensions:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;

    .line 410
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->wildcard(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method
