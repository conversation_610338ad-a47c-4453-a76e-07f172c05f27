.class public Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;
.super Ljava/lang/Object;
.source "CodeGeneratorExtension.java"


# instance fields
.field public factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V
    .locals 0

    .line 27
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 28
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/CodeGeneratorExtension;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    return-void
.end method


# virtual methods
.method public action(Ljava/util/List;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    return-object p1
.end method

.method public alternative(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .locals 0

    return-object p1
.end method

.method public epsilon(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .locals 0

    return-object p1
.end method

.method public finishAlternative(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .locals 0

    return-object p1
.end method

.method public getChoiceBlock(Lgroovyjarjarantlr4/v4/codegen/model/Choice;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 0

    return-object p1
.end method

.method public getEBNFBlock(Lgroovyjarjarantlr4/v4/codegen/model/Choice;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 0

    return-object p1
.end method

.method public lexer(Lgroovyjarjarantlr4/v4/codegen/model/Lexer;)Lgroovyjarjarantlr4/v4/codegen/model/Lexer;
    .locals 0

    return-object p1
.end method

.method public lexerFile(Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;)Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;
    .locals 0

    return-object p1
.end method

.method public needsImplicitLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/codegen/model/LabeledOp;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public parser(Lgroovyjarjarantlr4/v4/codegen/model/Parser;)Lgroovyjarjarantlr4/v4/codegen/model/Parser;
    .locals 0

    return-object p1
.end method

.method public parserFile(Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;)Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;
    .locals 0

    return-object p1
.end method

.method public rule(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;)Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
    .locals 0

    return-object p1
.end method

.method public rulePostamble(Ljava/util/List;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    return-object p1
.end method

.method public ruleRef(Ljava/util/List;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    return-object p1
.end method

.method public sempred(Ljava/util/List;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    return-object p1
.end method

.method public set(Ljava/util/List;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    return-object p1
.end method

.method public stringRef(Ljava/util/List;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    return-object p1
.end method

.method public tokenRef(Ljava/util/List;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    return-object p1
.end method

.method public wildcard(Ljava/util/List;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    return-object p1
.end method
