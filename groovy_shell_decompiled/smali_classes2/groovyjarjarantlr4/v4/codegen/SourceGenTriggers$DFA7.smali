.class public Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;
.super Lgroovyjarjarantlr4/runtime/DFA;
.source "SourceGenTriggers.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "DFA7"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;Lgroovyjarjarantlr4/runtime/BaseRecognizer;)V
    .locals 0

    .line 1899
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->this$0:Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;

    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/DFA;-><init>()V

    .line 1900
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->recognizer:Lgroovyjarjarantlr4/runtime/BaseRecognizer;

    const/4 p1, 0x7

    .line 1901
    iput p1, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->decisionNumber:I

    .line 1902
    sget-object p1, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;->DFA7_eot:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->eot:[S

    .line 1903
    sget-object p1, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;->DFA7_eof:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->eof:[S

    .line 1904
    sget-object p1, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;->DFA7_min:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->min:[C

    .line 1905
    sget-object p1, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;->DFA7_max:[C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->max:[C

    .line 1906
    sget-object p1, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;->DFA7_accept:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->accept:[S

    .line 1907
    sget-object p1, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;->DFA7_special:[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->special:[S

    .line 1908
    sget-object p1, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;->DFA7_transition:[[S

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$DFA7;->transition:[[S

    return-void
.end method


# virtual methods
.method public getDescription()Ljava/lang/String;
    .locals 1

    const-string v0, "65:1: alt[boolean outerMost] returns [CodeBlockForAlt altCodeBlock, List<SrcOp> ops] : ( ^( ALT ( elementOptions )? ( element )+ ) | ^( ALT ( elementOptions )? EPSILON ) );"

    return-object v0
.end method
