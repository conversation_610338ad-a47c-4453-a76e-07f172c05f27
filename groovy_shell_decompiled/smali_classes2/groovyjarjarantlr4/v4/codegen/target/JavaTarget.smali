.class public Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;
.super Lgroovyjarjarantlr4/v4/codegen/Target;
.source "JavaTarget.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget$JavaStringRenderer;
    }
.end annotation


# static fields
.field protected static final javaKeywords:[Ljava/lang/String;

.field private static final targetTemplates:Ljava/lang/ThreadLocal;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ThreadLocal<",
            "Lorg/stringtemplate/v4/STGroup;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field protected final badWords:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 54

    .line 31
    new-instance v0, Ljava/lang/ThreadLocal;

    invoke-direct {v0}, Ljava/lang/ThreadLocal;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->targetTemplates:Ljava/lang/ThreadLocal;

    const-string v1, "abstract"

    const-string v2, "assert"

    const-string v3, "boolean"

    const-string v4, "break"

    const-string v5, "byte"

    const-string v6, "case"

    const-string v7, "catch"

    const-string v8, "char"

    const-string v9, "class"

    const-string v10, "const"

    const-string v11, "continue"

    const-string v12, "default"

    const-string v13, "do"

    const-string v14, "double"

    const-string v15, "else"

    const-string v16, "enum"

    const-string v17, "extends"

    const-string v18, "false"

    const-string v19, "final"

    const-string v20, "finally"

    const-string v21, "float"

    const-string v22, "for"

    const-string v23, "goto"

    const-string v24, "if"

    const-string v25, "implements"

    const-string v26, "import"

    const-string v27, "instanceof"

    const-string v28, "int"

    const-string v29, "interface"

    const-string v30, "long"

    const-string v31, "native"

    const-string v32, "new"

    const-string v33, "null"

    const-string v34, "package"

    const-string v35, "private"

    const-string v36, "protected"

    const-string v37, "public"

    const-string v38, "return"

    const-string v39, "short"

    const-string v40, "static"

    const-string v41, "strictfp"

    const-string v42, "super"

    const-string v43, "switch"

    const-string v44, "synchronized"

    const-string v45, "this"

    const-string v46, "throw"

    const-string v47, "throws"

    const-string v48, "transient"

    const-string v49, "true"

    const-string v50, "try"

    const-string v51, "void"

    const-string v52, "volatile"

    const-string v53, "while"

    .line 33
    filled-new-array/range {v1 .. v53}, [Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->javaKeywords:[Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;)V
    .locals 1

    const-string v0, "Java"

    .line 48
    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/Target;-><init>(Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;Ljava/lang/String;)V

    .line 45
    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->badWords:Ljava/util/Set;

    return-void
.end method


# virtual methods
.method protected addBadWords()V
    .locals 2

    .line 60
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->badWords:Ljava/util/Set;

    sget-object v1, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->javaKeywords:[Ljava/lang/String;

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    .line 61
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->badWords:Ljava/util/Set;

    const-string v1, "rule"

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 62
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->badWords:Ljava/util/Set;

    const-string v1, "parserRule"

    invoke-interface {v0, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method protected appendUnicodeEscapedCodePoint(ILjava/lang/StringBuilder;)V
    .locals 0

    .line 221
    invoke-static {p1, p2}, Lgroovyjarjarantlr4/v4/codegen/UnicodeEscapes;->appendJavaStyleEscapedCodePoint(ILjava/lang/StringBuilder;)V

    return-void
.end method

.method public encodeIntAsCharEscape(I)Ljava/lang/String;
    .locals 3

    const/4 v0, 0x1

    if-ltz p1, :cond_4

    const v1, 0xffff

    if-gt p1, v1, :cond_4

    if-ltz p1, :cond_0

    .line 164
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->targetCharValueEscape:[Ljava/lang/String;

    array-length v1, v1

    if-ge p1, v1, :cond_0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->targetCharValueEscape:[Ljava/lang/String;

    aget-object v1, v1, p1

    if-eqz v1, :cond_0

    .line 165
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->targetCharValueEscape:[Ljava/lang/String;

    aget-object p1, v0, p1

    return-object p1

    :cond_0
    const/16 v1, 0x20

    const/16 v2, 0x7f

    if-lt p1, v1, :cond_2

    if-ge p1, v2, :cond_2

    .line 168
    invoke-static {p1}, Ljava/lang/Character;->isDigit(I)Z

    move-result v1

    if-eqz v1, :cond_1

    const/16 v1, 0x38

    if-eq p1, v1, :cond_1

    const/16 v1, 0x39

    if-ne p1, v1, :cond_2

    :cond_1
    int-to-char p1, p1

    .line 169
    invoke-static {p1}, Ljava/lang/String;->valueOf(C)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_2
    if-ltz p1, :cond_3

    if-gt p1, v2, :cond_3

    .line 173
    invoke-static {p1}, Ljava/lang/Integer;->toOctalString(I)Ljava/lang/String;

    move-result-object p1

    .line 174
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "\\"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_3
    const/high16 v1, 0x10000

    or-int/2addr p1, v1

    .line 177
    invoke-static {p1}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x5

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p1

    .line 178
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "\\u"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 161
    :cond_4
    new-instance v1, Ljava/lang/IllegalArgumentException;

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v2, 0x0

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    aput-object p1, v0, v2

    const-string p1, "Cannot encode the specified value: %d"

    invoke-static {p1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v1
.end method

.method public getBadWords()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 52
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->badWords:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 53
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->addBadWords()V

    .line 56
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->badWords:Ljava/util/Set;

    return-object v0
.end method

.method public getSerializedATNSegmentLimit()I
    .locals 1

    const/16 v0, 0x5555

    return v0
.end method

.method public getTargetStringLiteralFromANTLRStringLiteral(Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;Ljava/lang/String;Z)Ljava/lang/String;
    .locals 7

    .line 84
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v0, 0x22

    if-eqz p3, :cond_0

    .line 87
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_0
    const/4 v1, 0x1

    move v2, v1

    .line 89
    :goto_0
    invoke-virtual {p2}, Ljava/lang/String;->length()I

    move-result v3

    sub-int/2addr v3, v1

    if-ge v2, v3, :cond_a

    .line 90
    invoke-virtual {p2, v2}, Ljava/lang/String;->codePointAt(I)I

    move-result v3

    .line 91
    invoke-static {v3}, Ljava/lang/Character;->charCount(I)I

    move-result v4

    const/16 v5, 0x5c

    if-ne v3, v5, :cond_6

    add-int v3, v2, v4

    .line 99
    invoke-virtual {p2, v3}, Ljava/lang/String;->codePointAt(I)I

    move-result v3

    add-int/lit8 v4, v4, 0x1

    if-eq v3, v5, :cond_5

    const/16 v6, 0x62

    if-eq v3, v6, :cond_5

    const/16 v6, 0x66

    if-eq v3, v6, :cond_5

    const/16 v6, 0x6e

    if-eq v3, v6, :cond_5

    const/16 v6, 0x72

    if-eq v3, v6, :cond_5

    const/16 v6, 0x74

    if-eq v3, v6, :cond_5

    const/16 v5, 0x75

    if-eq v3, v5, :cond_2

    .line 132
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->shouldUseUnicodeEscapeForCodePointInDoubleQuotedString(I)Z

    move-result v5

    if-eqz v5, :cond_1

    .line 133
    invoke-virtual {p0, v3, p1}, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->appendUnicodeEscapedCodePoint(ILjava/lang/StringBuilder;)V

    goto :goto_3

    .line 135
    :cond_1
    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->appendCodePoint(I)Ljava/lang/StringBuilder;

    goto :goto_3

    :cond_2
    add-int v3, v2, v4

    .line 116
    invoke-virtual {p2, v3}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v5, 0x7b

    if-ne v3, v5, :cond_4

    :goto_1
    add-int v3, v2, v4

    .line 117
    invoke-virtual {p2, v3}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v5, 0x7d

    if-eq v3, v5, :cond_3

    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_3
    add-int/lit8 v4, v4, 0x1

    goto :goto_2

    :cond_4
    add-int/lit8 v4, v4, 0x4

    :goto_2
    add-int v3, v2, v4

    .line 124
    invoke-virtual {p2}, Ljava/lang/String;->length()I

    move-result v5

    if-gt v3, v5, :cond_9

    .line 125
    invoke-virtual {p2, v2, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v3

    .line 126
    invoke-static {v3}, Lgroovyjarjarantlr4/v4/misc/CharSupport;->getCharValueFromCharInGrammarLiteral(Ljava/lang/String;)I

    move-result v3

    invoke-virtual {p0, v3, p1}, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->appendUnicodeEscapedCodePoint(ILjava/lang/StringBuilder;)V

    goto :goto_3

    .line 111
    :cond_5
    invoke-virtual {p1, v5}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 112
    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->appendCodePoint(I)Ljava/lang/StringBuilder;

    goto :goto_3

    :cond_6
    if-ne v3, v0, :cond_7

    const-string v3, "\\\""

    .line 143
    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_3

    .line 144
    :cond_7
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->shouldUseUnicodeEscapeForCodePointInDoubleQuotedString(I)Z

    move-result v5

    if-eqz v5, :cond_8

    .line 145
    invoke-virtual {p0, v3, p1}, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->appendUnicodeEscapedCodePoint(ILjava/lang/StringBuilder;)V

    goto :goto_3

    .line 147
    :cond_8
    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->appendCodePoint(I)Ljava/lang/StringBuilder;

    :cond_9
    :goto_3
    add-int/2addr v2, v4

    goto/16 :goto_0

    :cond_a
    if-eqz p3, :cond_b

    .line 153
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 155
    :cond_b
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected loadTemplates()Lorg/stringtemplate/v4/STGroup;
    .locals 5

    .line 195
    sget-object v0, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->targetTemplates:Ljava/lang/ThreadLocal;

    invoke-virtual {v0}, Ljava/lang/ThreadLocal;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/stringtemplate/v4/STGroup;

    if-nez v1, :cond_0

    .line 197
    invoke-super {p0}, Lgroovyjarjarantlr4/v4/codegen/Target;->loadTemplates()Lorg/stringtemplate/v4/STGroup;

    move-result-object v1

    .line 198
    const-class v2, Ljava/lang/String;

    new-instance v3, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget$JavaStringRenderer;

    invoke-direct {v3}, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget$JavaStringRenderer;-><init>()V

    const/4 v4, 0x1

    invoke-virtual {v1, v2, v3, v4}, Lorg/stringtemplate/v4/STGroup;->registerRenderer(Ljava/lang/Class;Lorg/stringtemplate/v4/AttributeRenderer;Z)V

    .line 199
    invoke-virtual {v0, v1}, Ljava/lang/ThreadLocal;->set(Ljava/lang/Object;)V

    :cond_0
    return-object v1
.end method

.method protected visibleGrammarSymbolCausesIssueInGeneratedCode(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Z
    .locals 1

    .line 190
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/target/JavaTarget;->getBadWords()Ljava/util/Set;

    move-result-object v0

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method
