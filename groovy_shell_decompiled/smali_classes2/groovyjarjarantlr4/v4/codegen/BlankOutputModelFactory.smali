.class public abstract Lgroovyjarjarantlr4/v4/codegen/BlankOutputModelFactory;
.super Ljava/lang/Object;
.source "BlankOutputModelFactory.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 27
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public action(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public alternative(Lgroovyjarjarantlr4/v4/tool/Alternative;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public epsilon(Lgroovyjarjarantlr4/v4/tool/Alternative;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public finishAlternative(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;"
        }
    .end annotation

    return-object p1
.end method

.method public getChoiceBlock(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;Ljava/util/List;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public getComplexChoiceBlock(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public getComplexEBNFBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public getEBNFBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public getLL1ChoiceBlock(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public getLL1EBNFBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public getLL1Test(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public lexer(Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;)Lgroovyjarjarantlr4/v4/codegen/model/Lexer;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public lexerFile(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public needsImplicitLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/codegen/model/LabeledOp;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public parser(Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;)Lgroovyjarjarantlr4/v4/codegen/model/Parser;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public parserFile(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public rule(Lgroovyjarjarantlr4/v4/tool/Rule;)Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public rulePostamble(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public ruleRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public sempred(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public set(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Z)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Z)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public stringRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 64
    invoke-virtual {p0, p1, p2, v0}, Lgroovyjarjarantlr4/v4/codegen/BlankOutputModelFactory;->tokenRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public tokenRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public wildcard(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method
