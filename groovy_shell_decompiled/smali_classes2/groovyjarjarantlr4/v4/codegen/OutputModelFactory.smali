.class public interface abstract Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;
.super Ljava/lang/Object;
.source "OutputModelFactory.java"


# virtual methods
.method public abstract action(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end method

.method public abstract alternative(Lgroovyjarjarantlr4/v4/tool/Alternative;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
.end method

.method public abstract epsilon(Lgroovyjarjarantlr4/v4/tool/Alternative;Z)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
.end method

.method public abstract finishAlternative(Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;"
        }
    .end annotation
.end method

.method public abstract getChoiceBlock(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;Ljava/util/List;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation
.end method

.method public abstract getCodeBlockLevel()I
.end method

.method public abstract getComplexChoiceBlock(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation
.end method

.method public abstract getComplexEBNFBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation
.end method

.method public abstract getController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;
.end method

.method public abstract getCurrentBlock()Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;
.end method

.method public abstract getCurrentOuterMostAlt()Lgroovyjarjarantlr4/v4/tool/Alternative;
.end method

.method public abstract getCurrentOuterMostAlternativeBlock()Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;
.end method

.method public abstract getCurrentRuleFunction()Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
.end method

.method public abstract getEBNFBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation
.end method

.method public abstract getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;
.end method

.method public abstract getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;
.end method

.method public abstract getLL1ChoiceBlock(Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation
.end method

.method public abstract getLL1EBNFBlock(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)Lgroovyjarjarantlr4/v4/codegen/model/Choice;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/codegen/model/Choice;"
        }
    .end annotation
.end method

.method public abstract getLL1Test(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end method

.method public abstract getRoot()Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
.end method

.method public abstract getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;
.end method

.method public abstract getTreeLevel()I
.end method

.method public abstract lexer(Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;)Lgroovyjarjarantlr4/v4/codegen/model/Lexer;
.end method

.method public abstract lexerFile(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;
.end method

.method public abstract needsImplicitLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/codegen/model/LabeledOp;)Z
.end method

.method public abstract parser(Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;)Lgroovyjarjarantlr4/v4/codegen/model/Parser;
.end method

.method public abstract parserFile(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/codegen/model/ParserFile;
.end method

.method public abstract rule(Lgroovyjarjarantlr4/v4/tool/Rule;)Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
.end method

.method public abstract rulePostamble(Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end method

.method public abstract ruleRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end method

.method public abstract sempred(Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end method

.method public abstract set(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Z)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Z)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end method

.method public abstract setController(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)V
.end method

.method public abstract stringRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end method

.method public abstract tokenRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end method

.method public abstract wildcard(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end method
