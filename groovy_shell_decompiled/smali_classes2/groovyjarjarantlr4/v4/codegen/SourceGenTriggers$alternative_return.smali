.class public Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers$alternative_return;
.super Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;
.source "SourceGenTriggers.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/codegen/SourceGenTriggers;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "alternative_return"
.end annotation


# instance fields
.field public altCodeBlock:Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;

.field public ops:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 315
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;-><init>()V

    return-void
.end method
