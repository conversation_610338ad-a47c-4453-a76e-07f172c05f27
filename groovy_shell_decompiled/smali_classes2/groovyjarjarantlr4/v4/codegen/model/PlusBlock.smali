.class public Lgroovyjarjarantlr4/v4/codegen/model/PlusBlock;
.super Lgroovyjarjarantlr4/v4/codegen/model/Loop;
.source "PlusBlock.java"


# instance fields
.field public error:Lgroovyjarjarantlr4/v4/codegen/model/ThrowNoViableAlt;
    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)V"
        }
    .end annotation

    .line 24
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/Loop;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V

    const/4 p3, 0x0

    .line 25
    invoke-virtual {p2, p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p3

    check-cast p3, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;

    .line 26
    iget-object p3, p3, Lgroovyjarjarantlr4/v4/tool/ast/BlockAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    check-cast p3, Lgroovyjarjarantlr4/v4/runtime/atn/PlusBlockStartState;

    .line 27
    iget-object v0, p3, Lgroovyjarjarantlr4/v4/runtime/atn/PlusBlockStartState;->loopBackState:Lgroovyjarjarantlr4/v4/runtime/atn/PlusLoopbackState;

    .line 28
    iget-object v1, p3, Lgroovyjarjarantlr4/v4/runtime/atn/PlusBlockStartState;->loopBackState:Lgroovyjarjarantlr4/v4/runtime/atn/PlusLoopbackState;

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/PlusLoopbackState;->stateNumber:I

    iput v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/PlusBlock;->stateNumber:I

    .line 29
    iget p3, p3, Lgroovyjarjarantlr4/v4/runtime/atn/PlusBlockStartState;->stateNumber:I

    iput p3, p0, Lgroovyjarjarantlr4/v4/codegen/model/PlusBlock;->blockStartStateNumber:I

    .line 30
    iget p3, v0, Lgroovyjarjarantlr4/v4/runtime/atn/PlusLoopbackState;->stateNumber:I

    iput p3, p0, Lgroovyjarjarantlr4/v4/codegen/model/PlusBlock;->loopBackStateNumber:I

    const/4 p3, 0x0

    .line 31
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/PlusBlock;->getThrowNoViableAlt(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Lgroovyjarjarantlr4/v4/codegen/model/ThrowNoViableAlt;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/PlusBlock;->error:Lgroovyjarjarantlr4/v4/codegen/model/ThrowNoViableAlt;

    .line 32
    iget p1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/PlusLoopbackState;->decision:I

    iput p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/PlusBlock;->decision:I

    return-void
.end method
