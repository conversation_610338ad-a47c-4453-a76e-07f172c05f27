.class public Lgroovyjarjarantlr4/v4/codegen/model/LL1AltBlock;
.super Lgroovyjarjarantlr4/v4/codegen/model/LL1Choice;
.source "LL1AltBlock.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)V"
        }
    .end annotation

    .line 19
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/LL1Choice;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V

    .line 20
    iget-object p3, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    check-cast p3, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    iget p3, p3, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    iput p3, p0, Lgroovyjarjarantlr4/v4/codegen/model/LL1AltBlock;->decision:I

    .line 23
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p3

    iget-object p3, p3, Lgroovyjarjarantlr4/v4/tool/Grammar;->decisionLOOK:Ljava/util/List;

    iget v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/LL1AltBlock;->decision:I

    invoke-interface {p3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, [Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    .line 24
    invoke-virtual {p0, p3}, Lgroovyjarjarantlr4/v4/codegen/model/LL1AltBlock;->getAltLookaheadAsStringLists([Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/LL1AltBlock;->altLook:Ljava/util/List;

    .line 26
    invoke-static {p3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->or([Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p3

    .line 27
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/LL1AltBlock;->getThrowNoViableAlt(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Lgroovyjarjarantlr4/v4/codegen/model/ThrowNoViableAlt;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/LL1AltBlock;->error:Lgroovyjarjarantlr4/v4/codegen/model/ThrowNoViableAlt;

    return-void
.end method
