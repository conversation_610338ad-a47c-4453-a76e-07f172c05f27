.class public Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextToken;
.super Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;
.source "CaptureNextToken.java"


# instance fields
.field public varName:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V
    .locals 0

    .line 14
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    .line 15
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextToken;->varName:Ljava/lang/String;

    return-void
.end method
