.class public Lgroovyjarjarantlr4/v4/codegen/model/MatchSet;
.super Lgroovyjarjarantlr4/v4/codegen/model/MatchToken;
.source "MatchSet.java"


# instance fields
.field public capture:Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextTokenType;
    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public expr:Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;
    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 3

    .line 20
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/MatchToken;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 21
    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    const/4 v0, 0x0

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;

    .line 22
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGenerator()Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getInlineTestSetWordSize()I

    move-result v0

    .line 23
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;->set:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v2, 0x0

    invoke-direct {v1, p1, v2, p2, v0}, Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;I)V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/MatchSet;->expr:Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;

    .line 24
    new-instance p2, Lgroovyjarjarantlr4/v4/codegen/model/decl/TokenTypeDecl;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/MatchSet;->expr:Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;->varName:Ljava/lang/String;

    invoke-direct {p2, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/model/decl/TokenTypeDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    .line 25
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getCurrentRuleFunction()Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    move-result-object v0

    invoke-virtual {v0, p2}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->addLocalDecl(Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V

    .line 26
    new-instance p2, Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextTokenType;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/MatchSet;->expr:Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;->varName:Ljava/lang/String;

    invoke-direct {p2, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextTokenType;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/MatchSet;->capture:Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextTokenType;

    return-void
.end method
