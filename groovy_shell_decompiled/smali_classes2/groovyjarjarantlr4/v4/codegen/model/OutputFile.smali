.class public abstract Lgroovyjarjarantlr4/v4/codegen/model/OutputFile;
.super Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
.source "OutputFile.java"


# instance fields
.field public final ANTLRVersion:Ljava/lang/String;

.field public final InputSymbolType:Ljava/lang/String;

.field public final TokenLabelType:Ljava/lang/String;

.field public final fileName:Ljava/lang/String;

.field public final grammarFileName:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V
    .locals 0

    .line 25
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    .line 26
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/OutputFile;->fileName:Ljava/lang/String;

    .line 27
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p1

    .line 28
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/OutputFile;->grammarFileName:Ljava/lang/String;

    .line 29
    sget-object p2, Lgroovyjarjarantlr4/v4/Tool;->VERSION:Ljava/lang/String;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/OutputFile;->ANTLRVersion:Ljava/lang/String;

    const-string p2, "TokenLabelType"

    .line 30
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/OutputFile;->TokenLabelType:Ljava/lang/String;

    .line 31
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/OutputFile;->InputSymbolType:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public buildNamedActions(Lgroovyjarjarantlr4/v4/tool/Grammar;)Ljava/util/Map;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/codegen/model/Action;",
            ">;"
        }
    .end annotation

    .line 35
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 36
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->namedActions:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    .line 37
    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->namedActions:Ljava/util/Map;

    invoke-interface {v3, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 38
    new-instance v4, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/codegen/model/OutputFile;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {v4, v5, v3}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    invoke-interface {v0, v2, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    return-object v0
.end method
