.class public Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;
.super Lgroovyjarjarantlr4/v4/codegen/model/OutputFile;
.source "VisitorFile.java"


# instance fields
.field public accessLevel:Ljava/lang/String;

.field public exportMacro:Ljava/lang/String;

.field public genPackage:Ljava/lang/String;

.field public grammarName:Ljava/lang/String;

.field public header:Lgroovyjarjarantlr4/v4/codegen/model/Action;
    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public namedActions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/codegen/model/Action;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public parserName:Ljava/lang/String;

.field public visitorLabelRuleNames:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public visitorNames:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V
    .locals 6

    .line 44
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/OutputFile;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    .line 32
    new-instance p2, Ljava/util/LinkedHashSet;

    invoke-direct {p2}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->visitorNames:Ljava/util/Set;

    .line 38
    new-instance p2, Ljava/util/LinkedHashMap;

    invoke-direct {p2}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->visitorLabelRuleNames:Ljava/util/Map;

    .line 45
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p2

    .line 46
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->buildNamedActions(Lgroovyjarjarantlr4/v4/tool/Grammar;)Ljava/util/Map;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->namedActions:Ljava/util/Map;

    .line 47
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRecognizerName()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->parserName:Ljava/lang/String;

    .line 48
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->grammarName:Ljava/lang/String;

    .line 50
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Grammar;->contextASTs:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 51
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :catch_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 53
    :try_start_0
    invoke-virtual {p2, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getLabeledAlternatives(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)Ljava/util/Map;

    move-result-object v2

    .line 54
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->visitorNames:Ljava/util/Set;

    invoke-interface {v2}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v3, v2}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z
    :try_end_0
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 60
    :cond_1
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 61
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->visitorNames:Ljava/util/Set;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v2, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 64
    :cond_2
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 65
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getAltLabels()Ljava/util/Map;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 67
    invoke-interface {v2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 68
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->visitorLabelRuleNames:Ljava/util/Map;

    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    iget-object v5, v1, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-interface {v4, v3, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    .line 73
    :cond_4
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Grammar;->namedActions:Ljava/util/Map;

    const-string v1, "header"

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    if-eqz v0, :cond_5

    .line 74
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    invoke-direct {v1, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->header:Lgroovyjarjarantlr4/v4/codegen/model/Action;

    .line 75
    :cond_5
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/Tool;->genPackage:Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->genPackage:Ljava/lang/String;

    const-string p1, "accessLevel"

    .line 76
    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->accessLevel:Ljava/lang/String;

    const-string p1, "exportMacro"

    .line 77
    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/VisitorFile;->exportMacro:Ljava/lang/String;

    return-void
.end method
