.class public Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;
.super Lgroovyjarjarantlr4/v4/codegen/model/RuleElement;
.source "InvokeRule.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/codegen/model/LabeledOp;


# instance fields
.field public argExprsChunks:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionChunk;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public ctxName:Ljava/lang/String;

.field public labels:Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet<",
            "Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;",
            ">;"
        }
    .end annotation
.end field

.field public name:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/ParserFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 3

    .line 33
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/RuleElement;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    .line 27
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->labels:Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    .line 34
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    if-eqz v0, :cond_0

    .line 36
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    .line 37
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    iput v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->stateNumber:I

    .line 40
    :cond_0
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->name:Ljava/lang/String;

    .line 41
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/codegen/ParserFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v0

    .line 42
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/codegen/ParserFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v1

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/codegen/Target;->getRuleFunctionContextStructName(Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->ctxName:Ljava/lang/String;

    .line 45
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/codegen/ParserFactory;->getCurrentRuleFunction()Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    move-result-object v0

    if-eqz p3, :cond_2

    .line 48
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    .line 49
    iget-object p3, p3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    invoke-virtual {p3}, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->getType()I

    move-result p3

    const/16 v2, 0x2e

    if-ne p3, v2, :cond_1

    .line 50
    invoke-virtual {p1, p2, p0}, Lgroovyjarjarantlr4/v4/codegen/ParserFactory;->defineImplicitLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/codegen/model/LabeledOp;)V

    .line 51
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/codegen/ParserFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object p3

    invoke-virtual {p3, v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getListLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p3

    .line 52
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextListDecl;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->ctxName:Ljava/lang/String;

    invoke-direct {v1, p1, p3, v2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextListDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Ljava/lang/String;)V

    .line 53
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAltLabel()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {v0, p3, v1}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->addContextDecl(Ljava/lang/String;Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V

    goto :goto_0

    .line 56
    :cond_1
    new-instance p3, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextDecl;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->ctxName:Ljava/lang/String;

    invoke-direct {p3, p1, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Ljava/lang/String;)V

    .line 57
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->labels:Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    invoke-virtual {v1, p3}, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->add(Ljava/lang/Object;)Z

    .line 58
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAltLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1, p3}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->addContextDecl(Ljava/lang/String;Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V

    :cond_2
    :goto_0
    const/16 p3, 0x8

    .line 62
    invoke-virtual {p2, p3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p3

    check-cast p3, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    if-eqz p3, :cond_3

    .line 64
    iget-object v1, p3, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-static {p1, v0, v1, p3}, Lgroovyjarjarantlr4/v4/codegen/ActionTranslator;->translateAction(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)Ljava/util/List;

    move-result-object p3

    iput-object p3, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->argExprsChunks:Ljava/util/List;

    .line 68
    :cond_3
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/codegen/ParserFactory;->getCurrentOuterMostAlt()Lgroovyjarjarantlr4/v4/tool/Alternative;

    move-result-object p3

    iget-object p3, p3, Lgroovyjarjarantlr4/v4/tool/Alternative;->ruleRefsInActions:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p3, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->containsKey(Ljava/lang/Object;)Z

    move-result p3

    if-eqz p3, :cond_4

    .line 69
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/codegen/ParserFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object p3

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p3, v1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getImplicitRuleLabel(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p3

    .line 70
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextDecl;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->ctxName:Ljava/lang/String;

    invoke-direct {v1, p1, p3, v2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Ljava/lang/String;)V

    .line 71
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->labels:Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->add(Ljava/lang/Object;)Z

    .line 72
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAltLabel()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->addContextDecl(Ljava/lang/String;Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V

    :cond_4
    return-void
.end method


# virtual methods
.method public getLabels()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;",
            ">;"
        }
    .end annotation

    .line 78
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/InvokeRule;->labels:Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
