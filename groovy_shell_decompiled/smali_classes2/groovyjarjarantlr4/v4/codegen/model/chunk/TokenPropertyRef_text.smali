.class public Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef_text;
.super Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef;
.source "TokenPropertyRef_text.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V
    .locals 0

    .line 14
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/TokenPropertyRef;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;)V

    return-void
.end method
