.class public Lgroovyjarjarantlr4/v4/codegen/model/chunk/SetNonLocalAttr;
.super Lgroovyjarjarantlr4/v4/codegen/model/chunk/SetAttr;
.source "SetNonLocalAttr.java"


# instance fields
.field public ruleIndex:I

.field public ruleName:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;Ljava/lang/String;ILjava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "I",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/chunk/ActionChunk;",
            ">;)V"
        }
    .end annotation

    .line 21
    invoke-direct {p0, p1, p3, p5}, Lgroovyjarjarantlr4/v4/codegen/model/chunk/SetAttr;-><init>(Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;Ljava/lang/String;Ljava/util/List;)V

    .line 22
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/SetNonLocalAttr;->ruleName:Ljava/lang/String;

    .line 23
    iput p4, p0, Lgroovyjarjarantlr4/v4/codegen/model/chunk/SetNonLocalAttr;->ruleIndex:I

    return-void
.end method
