.class public Lgroovyjarjarantlr4/v4/codegen/model/LeftRecursiveRuleFunction;
.super Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
.source "LeftRecursiveRuleFunction.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;)V
    .locals 6

    .line 21
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;)V

    .line 25
    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/LeftRecursiveRule;->leftRecursiveRuleRefLabels:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_0
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    .line 26
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 27
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem2()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    .line 28
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v2

    .line 29
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    const/4 v4, 0x1

    invoke-interface {v3, v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 30
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v4

    const/16 v5, 0x39

    if-ne v4, v5, :cond_0

    .line 31
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v4

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v4, v3}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v3

    .line 32
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v4

    invoke-virtual {v4, v3}, Lgroovyjarjarantlr4/v4/codegen/Target;->getRuleFunctionContextStructName(Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/lang/String;

    move-result-object v3

    .line 34
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getParent()Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getType()I

    move-result v1

    const/16 v4, 0xa

    if-ne v1, v4, :cond_1

    .line 35
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextDecl;

    invoke-direct {v1, p1, v2, v3}, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    .line 38
    :cond_1
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextListDecl;

    invoke-direct {v1, p1, v2, v3}, Lgroovyjarjarantlr4/v4/codegen/model/decl/RuleContextListDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Ljava/lang/String;)V

    .line 41
    :goto_1
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/codegen/model/LeftRecursiveRuleFunction;->getEffectiveRuleContext(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    move-result-object v2

    .line 42
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/codegen/model/LeftRecursiveRuleFunction;->getEffectiveAltLabelContexts(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Ljava/util/Map;

    move-result-object v3

    invoke-interface {v3, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    if-eqz v0, :cond_2

    move-object v2, v0

    .line 44
    :cond_2
    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;->addDecl(Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V

    goto :goto_0

    :cond_3
    return-void
.end method
