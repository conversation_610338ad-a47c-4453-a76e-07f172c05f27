.class public abstract Lgroovyjarjarantlr4/v4/codegen/model/LL1Choice;
.super Lgroovyjarjarantlr4/v4/codegen/model/Choice;
.source "LL1Choice.java"


# instance fields
.field public altLook:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "[",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public error:Lgroovyjarjarantlr4/v4/codegen/model/ThrowNoViableAlt;
    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)V"
        }
    .end annotation

    .line 22
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/Choice;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V

    return-void
.end method
