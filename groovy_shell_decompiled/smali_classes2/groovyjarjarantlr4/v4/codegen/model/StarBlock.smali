.class public Lgroovyjarjarantlr4/v4/codegen/model/StarBlock;
.super Lgroovyjarjarantlr4/v4/codegen/model/Loop;
.source "StarBlock.java"


# instance fields
.field public loopLabel:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)V"
        }
    .end annotation

    .line 22
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/Loop;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V

    .line 23
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object p1

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/codegen/Target;->getLoopLabel(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/StarBlock;->loopLabel:Ljava/lang/String;

    .line 24
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    .line 25
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;->loopBackState:Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopbackState;

    iget p2, p2, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopbackState;->stateNumber:I

    iput p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/StarBlock;->loopBackStateNumber:I

    .line 26
    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;->decision:I

    iput p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/StarBlock;->decision:I

    return-void
.end method
