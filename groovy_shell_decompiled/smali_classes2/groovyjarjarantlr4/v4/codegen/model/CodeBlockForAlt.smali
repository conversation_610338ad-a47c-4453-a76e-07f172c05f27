.class public Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;
.super Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;
.source "CodeBlockForAlt.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V
    .locals 0

    .line 16
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    return-void
.end method
