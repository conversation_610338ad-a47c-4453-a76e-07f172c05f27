.class public abstract Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;
.super Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
.source "SrcOp.java"


# instance fields
.field public enclosingBlock:Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;

.field public enclosingRuleRunction:Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

.field public uniqueID:I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V
    .locals 1

    const/4 v0, 0x0

    .line 29
    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 0

    .line 31
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    if-eqz p2, :cond_0

    .line 32
    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getTokenIndex()I

    move-result p2

    iput p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;->uniqueID:I

    .line 33
    :cond_0
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getCurrentBlock()Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;->enclosingBlock:Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;

    .line 34
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getCurrentRuleFunction()Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;->enclosingRuleRunction:Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    return-void
.end method


# virtual methods
.method public getContextName()Ljava/lang/String;
    .locals 2

    .line 54
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;->getOuterMostAltCodeBlock()Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 55
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;->altLabel:Ljava/lang/String;

    if-eqz v1, :cond_0

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;->altLabel:Ljava/lang/String;

    return-object v0

    .line 56
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;->enclosingRuleRunction:Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->name:Ljava/lang/String;

    return-object v0
.end method

.method public getOuterMostAltCodeBlock()Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;
    .locals 2

    .line 39
    instance-of v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    if-eqz v0, :cond_0

    .line 40
    move-object v0, p0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    return-object v0

    .line 42
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;->enclosingBlock:Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;

    :goto_0
    if-eqz v0, :cond_2

    .line 44
    instance-of v1, v0, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    if-eqz v1, :cond_1

    .line 45
    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    return-object v0

    .line 47
    :cond_1
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;->enclosingBlock:Lgroovyjarjarantlr4/v4/codegen/model/decl/CodeBlock;

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    return-object v0
.end method
