.class public Lgroovyjarjarantlr4/v4/codegen/model/ListenerDispatchMethod;
.super Lgroovyjarjarantlr4/v4/codegen/model/DispatchMethod;
.source "ListenerDispatchMethod.java"


# instance fields
.field public isEnter:Z


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Z)V
    .locals 0

    .line 14
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/DispatchMethod;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    .line 15
    iput-boolean p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/ListenerDispatchMethod;->isEnter:Z

    return-void
.end method
