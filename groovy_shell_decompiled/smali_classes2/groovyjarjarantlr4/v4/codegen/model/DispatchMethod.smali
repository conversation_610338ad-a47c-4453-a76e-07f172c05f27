.class public Lgroovyjarjarantlr4/v4/codegen/model/DispatchMethod;
.super Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
.source "DispatchMethod.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V
    .locals 0

    .line 12
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    return-void
.end method
