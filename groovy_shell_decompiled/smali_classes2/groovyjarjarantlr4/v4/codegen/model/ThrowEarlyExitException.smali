.class public Lgroovyjarjarantlr4/v4/codegen/model/ThrowEarlyExitException;
.super Lgroovyjarjarantlr4/v4/codegen/model/ThrowRecognitionException;
.source "ThrowEarlyExitException.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V
    .locals 0

    .line 16
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/ThrowRecognitionException;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    return-void
.end method
