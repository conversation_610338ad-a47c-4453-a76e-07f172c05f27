.class public Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;
.super Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;
.source "ElementFrequenciesVisitor.java"


# static fields
.field static final synthetic $assertionsDisabled:Z

.field private static final SENTINEL:Lgroovyjarjarantlr4/v4/misc/FrequencySet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field final frequencies:Ljava/util/Deque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Deque<",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end field

.field final grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

.field private final minFrequencies:Ljava/util/Deque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Deque<",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 31
    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->SENTINEL:Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V
    .locals 0

    .line 38
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V

    .line 39
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 40
    new-instance p1, Ljava/util/ArrayDeque;

    invoke-direct {p1}, Ljava/util/ArrayDeque;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    .line 41
    new-instance p2, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {p2}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, p2}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 42
    new-instance p1, Ljava/util/ArrayDeque;

    invoke-direct {p1}, Ljava/util/ArrayDeque;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    .line 43
    sget-object p2, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->SENTINEL:Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-interface {p1, p2}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected static combineAndClip(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;I)Lgroovyjarjarantlr4/v4/misc/FrequencySet;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;I)",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 120
    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    .line 121
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->entrySet()Ljava/util/Set;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 122
    :goto_0
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    iget v3, v3, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    if-ge v2, v3, :cond_0

    .line 123
    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->add(Ljava/lang/Object;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 127
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->entrySet()Ljava/util/Set;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_2
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result p1

    if-eqz p1, :cond_3

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Map$Entry;

    move v1, v2

    .line 128
    :goto_1
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    iget v3, v3, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    if-ge v1, v3, :cond_2

    .line 129
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->add(Ljava/lang/Object;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 133
    :cond_3
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->entrySet()Ljava/util/Set;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_2
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result p1

    if-eqz p1, :cond_4

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Map$Entry;

    .line 134
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    iget p1, p1, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    invoke-static {p1, p2}, Ljava/lang/Math;->min(II)I

    move-result p1

    iput p1, v1, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    goto :goto_2

    :cond_4
    return-object v0
.end method

.method protected static combineMax(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;)Lgroovyjarjarantlr4/v4/misc/FrequencySet;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x1

    .line 69
    invoke-static {p0, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineAndClip(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;I)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    .line 70
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->entrySet()Ljava/util/Set;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 71
    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    iget v1, v1, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    iput v1, v2, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    goto :goto_0

    .line 74
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->entrySet()Ljava/util/Set;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result p1

    if-eqz p1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Map$Entry;

    .line 75
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    .line 76
    iget v2, v1, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    iget p1, p1, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    invoke-static {v2, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, v1, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    goto :goto_1

    :cond_1
    return-object v0
.end method

.method protected static combineMin(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;)Lgroovyjarjarantlr4/v4/misc/FrequencySet;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 94
    sget-object v0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->SENTINEL:Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    if-ne p1, v0, :cond_0

    return-object p0

    :cond_0
    const v0, 0x7fffffff

    .line 99
    invoke-static {p0, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineAndClip(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;I)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    .line 100
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->entrySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map$Entry;

    .line 101
    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    invoke-interface {v2}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->count(Ljava/lang/Object;)I

    move-result v4

    invoke-interface {v2}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->count(Ljava/lang/Object;)I

    move-result v2

    invoke-static {v4, v2}, Ljava/lang/Math;->min(II)I

    move-result v2

    iput v2, v3, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    goto :goto_0

    :cond_1
    return-object v0
.end method


# virtual methods
.method protected enterAlternative(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;)V
    .locals 1

    .line 175
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 176
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected enterBlockSet(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 1

    .line 199
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 200
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected enterElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 1

    .line 187
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 188
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected enterLexerAlternative(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 1

    .line 243
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 244
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected enterLexerElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 1

    .line 255
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 256
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected exitAlternative(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;)V
    .locals 2

    .line 181
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineMax(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 182
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineMin(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected exitBlockSet(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 3

    .line 205
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map$Entry;

    .line 210
    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    iput v1, v0, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    goto :goto_0

    .line 213
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->size()I

    move-result p1

    if-le p1, v1, :cond_1

    .line 215
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->clear()V

    .line 218
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    const/4 v2, 0x2

    invoke-static {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineAndClip(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;I)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 219
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-static {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineAndClip(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;I)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected exitElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 3

    .line 193
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    const/4 v2, 0x2

    invoke-static {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineAndClip(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;I)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 194
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-static {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineAndClip(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;I)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected exitLexerAlternative(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 2

    .line 249
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineMax(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 250
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineMin(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected exitLexerElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 3

    .line 261
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    const/4 v2, 0x2

    invoke-static {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineAndClip(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;I)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 262
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-static {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->combineAndClip(Lgroovyjarjarantlr4/v4/misc/FrequencySet;Lgroovyjarjarantlr4/v4/misc/FrequencySet;I)Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    return-void
.end method

.method protected exitLexerSubrule(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 4

    .line 267
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v0

    const/16 v1, 0x50

    if-eq v0, v1, :cond_0

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v0

    const/16 v2, 0x5a

    if-ne v0, v2, :cond_1

    .line 268
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map$Entry;

    .line 269
    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    const/4 v3, 0x2

    iput v3, v2, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    goto :goto_0

    .line 273
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result p1

    if-ne p1, v1, :cond_2

    .line 276
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->clear()V

    :cond_2
    return-void
.end method

.method protected exitSubrule(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V
    .locals 4

    .line 224
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v0

    const/16 v1, 0x50

    if-eq v0, v1, :cond_0

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v0

    const/16 v2, 0x5a

    if-ne v0, v2, :cond_1

    .line 225
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map$Entry;

    .line 226
    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/misc/MutableInt;

    const/4 v3, 0x2

    iput v3, v2, Lgroovyjarjarantlr4/v4/misc/MutableInt;->v:I

    goto :goto_0

    .line 230
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v0

    if-eq v0, v1, :cond_2

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result p1

    const/16 v0, 0x59

    if-ne p1, v0, :cond_3

    .line 233
    :cond_2
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->clear()V

    :cond_3
    return-void
.end method

.method getMinFrequencies()Lgroovyjarjarantlr4/v4/misc/FrequencySet;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 51
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    return-object v0
.end method

.method public ruleRef(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V
    .locals 1

    .line 148
    instance-of p2, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    if-eqz p2, :cond_0

    .line 149
    move-object p2, p1

    check-cast p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;

    const-string v0, "suppressAccessor"

    .line 150
    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTWithOptions;->getOptionString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-static {p2}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result p2

    if-eqz p2, :cond_0

    return-void

    .line 155
    :cond_0
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {p2}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getLabelName(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->add(Ljava/lang/Object;)V

    .line 156
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {p2}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->grammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getLabelName(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->add(Ljava/lang/Object;)V

    return-void
.end method

.method public stringRef(Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;)V
    .locals 1

    .line 161
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_0

    const-string v0, "T__"

    .line 163
    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 164
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->add(Ljava/lang/Object;)V

    .line 165
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->add(Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public tokenRef(Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;)V
    .locals 2

    .line 142
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->add(Ljava/lang/Object;)V

    .line 143
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->minFrequencies:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/TerminalAST;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->add(Ljava/lang/Object;)V

    return-void
.end method
