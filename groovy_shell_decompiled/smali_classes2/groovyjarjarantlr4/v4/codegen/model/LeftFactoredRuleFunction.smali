.class public Lgroovyjarjarantlr4/v4/codegen/model/LeftFactoredRuleFunction;
.super Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
.source "LeftFactoredRuleFunction.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;)V
    .locals 0

    .line 17
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;)V

    return-void
.end method
