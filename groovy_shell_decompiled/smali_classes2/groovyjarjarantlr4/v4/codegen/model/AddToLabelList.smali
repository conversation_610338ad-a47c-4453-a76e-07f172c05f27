.class public Lgroovyjarjarantlr4/v4/codegen/model/AddToLabelList;
.super Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;
.source "AddToLabelList.java"


# instance fields
.field public label:Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;

.field public listName:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V
    .locals 0

    .line 18
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    .line 19
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/codegen/model/AddToLabelList;->label:Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;

    .line 20
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/AddToLabelList;->listName:Ljava/lang/String;

    return-void
.end method
