.class public Lgroovyjarjarantlr4/v4/codegen/model/AltBlock;
.super Lgroovyjarjarantlr4/v4/codegen/model/Choice;
.source "AltBlock.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)V"
        }
    .end annotation

    .line 22
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/Choice;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V

    .line 23
    iget-object p1, p2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->atnState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;->decision:I

    iput p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/AltBlock;->decision:I

    return-void
.end method
