.class public Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextTokenType;
.super Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;
.source "CaptureNextTokenType.java"


# instance fields
.field public varName:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V
    .locals 0

    .line 15
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    .line 16
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextTokenType;->varName:Ljava/lang/String;

    return-void
.end method
