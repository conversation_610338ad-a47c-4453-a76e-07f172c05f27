.class public Lgroovyjarjarantlr4/v4/codegen/model/Lexer;
.super Lgroovyjarjarantlr4/v4/codegen/model/Recognizer;
.source "Lexer.java"


# instance fields
.field public actionFuncs:Ljava/util/LinkedHashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Lgroovyjarjarantlr4/v4/tool/Rule;",
            "Lgroovyjarjarantlr4/v4/codegen/model/RuleActionFunction;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public channels:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public file:Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;

.field public modes:Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;)V
    .locals 1

    .line 27
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/Recognizer;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    .line 23
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;->actionFuncs:Ljava/util/LinkedHashMap;

    .line 28
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;->file:Lgroovyjarjarantlr4/v4/codegen/model/LexerFile;

    .line 30
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p1

    .line 31
    new-instance p2, Ljava/util/LinkedHashMap;

    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelNameToValueMap:Ljava/util/Map;

    invoke-direct {p2, v0}, Ljava/util/LinkedHashMap;-><init>(Ljava/util/Map;)V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;->channels:Ljava/util/Map;

    .line 32
    check-cast p1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->keySet()Ljava/util/Set;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/Lexer;->modes:Ljava/util/Collection;

    return-void
.end method
