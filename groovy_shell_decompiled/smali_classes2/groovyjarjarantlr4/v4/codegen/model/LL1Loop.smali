.class public abstract Lgroovyjarjarantlr4/v4/codegen/model/LL1Loop;
.super Lgroovyjarjarantlr4/v4/codegen/model/Choice;
.source "LL1Loop.java"


# instance fields
.field public blockStartStateNumber:I

.field public iteration:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public loopBackStateNumber:I

.field public loopExpr:Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForAlt;",
            ">;)V"
        }
    .end annotation

    .line 31
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/codegen/model/Choice;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method public addCodeForLoopLookaheadTempVar(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;
    .locals 3

    .line 40
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/LL1Loop;->addCodeForLookaheadTempVar(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 42
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextTokenType;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/LL1Loop;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    iget-object v2, p1, Lgroovyjarjarantlr4/v4/codegen/model/TestSetInline;->varName:Ljava/lang/String;

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/CaptureNextTokenType;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    .line 43
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/model/LL1Loop;->addIterationOp(Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;)V

    :cond_0
    return-object p1
.end method

.method public addIterationOp(Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;)V
    .locals 1

    .line 35
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/LL1Loop;->iteration:Ljava/util/List;

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/LL1Loop;->iteration:Ljava/util/List;

    .line 36
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/LL1Loop;->iteration:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method
