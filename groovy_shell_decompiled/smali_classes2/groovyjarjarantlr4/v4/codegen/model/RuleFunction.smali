.class public Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;
.super Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;
.source "RuleFunction.java"


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field public altLabelCtxs:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/codegen/model/decl/AltLabelStructDecl;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public args:Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/codegen/model/decl/AttributeDecl;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public code:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public ctxType:Ljava/lang/String;

.field public exceptions:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/ExceptionClause;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public finallyAction:Lgroovyjarjarantlr4/v4/codegen/model/Action;
    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public hasLookaheadBlock:Z

.field public index:I

.field public locals:Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet<",
            "Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public modifiers:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public name:Ljava/lang/String;

.field public namedActions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/codegen/model/Action;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public postamble:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/SrcOp;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public rule:Lgroovyjarjarantlr4/v4/tool/Rule;

.field public ruleCtx:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;
    .annotation runtime Lgroovyjarjarantlr4/v4/codegen/model/ModelElement;
    .end annotation
.end field

.field public ruleLabels:Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public startState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

.field public tokenLabels:Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public variantOf:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;)V
    .locals 6

    .line 81
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/OutputModelObject;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;)V

    const/4 v0, 0x0

    .line 72
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->args:Ljava/util/Collection;

    .line 82
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->name:Ljava/lang/String;

    .line 83
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 84
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->modifiers:Ljava/util/List;

    if-eqz v0, :cond_0

    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->modifiers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    .line 85
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->modifiers:Ljava/util/List;

    .line 86
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->modifiers:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->modifiers:Ljava/util/List;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v2, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 88
    :cond_0
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->modifiers:Ljava/util/List;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/misc/Utils;->nodesToStrings(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->modifiers:Ljava/util/List;

    .line 90
    iget v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    iput v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->index:I

    .line 91
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->name:Ljava/lang/String;

    const/16 v1, 0x24

    invoke-virtual {v0, v1}, Ljava/lang/String;->indexOf(I)I

    move-result v0

    const/4 v1, 0x0

    if-ltz v0, :cond_1

    .line 93
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->name:Ljava/lang/String;

    invoke-virtual {v2, v1, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->variantOf:Ljava/lang/String;

    .line 96
    :cond_1
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 97
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-direct {v0, p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ruleCtx:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    .line 98
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->contextASTs:Ljava/util/Map;

    iget-object v2, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Collection;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->addContextGetters(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/util/Collection;)V

    .line 100
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v0, :cond_3

    .line 101
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    .line 102
    invoke-interface {v0}, Ljava/util/Collection;->size()I

    move-result v2

    if-lez v2, :cond_3

    .line 103
    new-instance v2, Ljava/util/ArrayList;

    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    iput-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->args:Ljava/util/Collection;

    .line 104
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ruleCtx:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;->addDecls(Ljava/util/Collection;)V

    .line 105
    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Attribute;

    .line 106
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->args:Ljava/util/Collection;

    new-instance v4, Lgroovyjarjarantlr4/v4/codegen/model/decl/AttributeDecl;

    invoke-direct {v4, p1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/AttributeDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Attribute;)V

    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 108
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ruleCtx:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->args:Ljava/util/Collection;

    iput-object v2, v0, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;->ctorAttrs:Ljava/util/Collection;

    .line 111
    :cond_3
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v0, :cond_4

    .line 112
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ruleCtx:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    iget-object v2, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    invoke-virtual {v2}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;->addDecls(Ljava/util/Collection;)V

    .line 114
    :cond_4
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-eqz v0, :cond_6

    .line 115
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ruleCtx:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    iget-object v2, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/AttributeDict;->attributes:Ljava/util/LinkedHashMap;

    invoke-virtual {v2}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;->addDecls(Ljava/util/Collection;)V

    goto :goto_2

    .line 119
    :cond_5
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->args:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-nez v0, :cond_8

    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->retvals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-nez v0, :cond_8

    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->locals:Lgroovyjarjarantlr4/v4/tool/AttributeDict;

    if-nez v0, :cond_8

    .line 124
    :cond_6
    :goto_2
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->getElementLabelNames()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ruleLabels:Ljava/util/Collection;

    .line 125
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/Rule;->getTokenRefs()Ljava/util/Set;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->tokenLabels:Ljava/util/Collection;

    .line 126
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->exceptions:Ljava/util/List;

    if-eqz v0, :cond_7

    .line 127
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->exceptions:Ljava/util/List;

    .line 128
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->exceptions:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_7

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 129
    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    const/4 v4, 0x1

    .line 130
    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 131
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->exceptions:Ljava/util/List;

    new-instance v5, Lgroovyjarjarantlr4/v4/codegen/model/ExceptionClause;

    invoke-direct {v5, p1, v3, v2}, Lgroovyjarjarantlr4/v4/codegen/model/ExceptionClause;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_3

    .line 135
    :cond_7
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p1

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    iget p2, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    aget-object p1, p1, p2

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->startState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    return-void

    .line 120
    :cond_8
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "customized fields are not yet supported for customized context objects"

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public static getLabelName(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;
    .locals 2

    .line 307
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    .line 308
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result p1

    const/16 v1, 0x3e

    if-eq p1, v1, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    :goto_0
    if-eqz v0, :cond_3

    const-string p1, "T__"

    .line 309
    invoke-virtual {v0, p1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_1

    goto :goto_1

    .line 315
    :cond_1
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/tool/Rule;

    if-eqz p0, :cond_2

    .line 317
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object v0

    :cond_2
    return-object v0

    :cond_3
    :goto_1
    const/4 p0, 0x0

    return-object p0
.end method

.method private getRuleTokens(Ljava/util/List;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            ">;"
        }
    .end annotation

    .line 284
    new-instance v0, Ljava/util/ArrayList;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 285
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    const/4 v2, 0x0

    move-object v3, v1

    :goto_1
    if-eqz v3, :cond_2

    .line 291
    instance-of v4, v3, Lgroovyjarjarantlr4/v4/tool/ast/PredAST;

    if-eqz v4, :cond_1

    const/4 v2, 0x1

    goto :goto_2

    .line 295
    :cond_1
    iget-object v3, v3, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->parent:Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    goto :goto_1

    :cond_2
    :goto_2
    if-nez v2, :cond_0

    .line 299
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_3
    return-object v0
.end method


# virtual methods
.method public addContextDecl(Ljava/lang/String;Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V
    .locals 1

    .line 383
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;->getOuterMostAltCodeBlock()Lgroovyjarjarantlr4/v4/codegen/model/CodeBlockForOuterMostAlt;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 387
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getEffectiveAltLabelContexts(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/codegen/model/decl/AltLabelStructDecl;

    if-eqz p1, :cond_0

    .line 390
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/AltLabelStructDecl;->addDecl(Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V

    return-void

    .line 394
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getController()Lgroovyjarjarantlr4/v4/codegen/OutputModelController;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getEffectiveRuleContext(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    move-result-object p1

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;->addDecl(Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V

    return-void
.end method

.method public addContextGetters(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/util/Collection;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;",
            ">;)V"
        }
    .end annotation

    .line 167
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 168
    new-instance v1, Ljava/util/LinkedHashMap;

    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 170
    invoke-interface {p2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :catch_0
    :cond_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 172
    :try_start_0
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getUnlabeledAlternatives(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)Ljava/util/List;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 173
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getLabeledAlternatives(Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;)Ljava/util/Map;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    .line 174
    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v1, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/util/List;

    if-nez v4, :cond_2

    .line 176
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 177
    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v1, v5, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 180
    :cond_2
    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_1

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    .line 181
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem2()Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 190
    :cond_3
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_4

    .line 191
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getDeclsForAllElements(Ljava/util/List;)Ljava/util/Set;

    move-result-object p2

    .line 194
    invoke-interface {p2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;

    .line 195
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ruleCtx:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;->addDecl(Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V

    goto :goto_1

    .line 200
    :cond_4
    new-instance p2, Ljava/util/LinkedHashMap;

    invoke-direct {p2}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->altLabelCtxs:Ljava/util/Map;

    .line 201
    invoke-interface {v1}, Ljava/util/Map;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_6

    .line 202
    invoke-interface {v1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p2

    invoke-interface {p2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_5
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_6

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Map$Entry;

    .line 203
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/decl/AltLabelStructDecl;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    invoke-interface {v0}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-direct {v1, p1, v2, v3}, Lgroovyjarjarantlr4/v4/codegen/model/decl/AltLabelStructDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/lang/String;)V

    .line 204
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->altLabelCtxs:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 205
    invoke-interface {v0}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/List;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getDeclsForAllElements(Ljava/util/List;)Ljava/util/Set;

    move-result-object v0

    .line 206
    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;

    .line 207
    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/AltLabelStructDecl;->addDecl(Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V

    goto :goto_2

    :cond_6
    return-void
.end method

.method public addLocalDecl(Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;)V
    .locals 1

    .line 376
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->locals:Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    if-nez v0, :cond_0

    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->locals:Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    .line 377
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->locals:Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->add(Ljava/lang/Object;)Z

    const/4 v0, 0x1

    .line 378
    iput-boolean v0, p1, Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;->isLocal:Z

    return-void
.end method

.method public fillNamedActions(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/Rule;)V
    .locals 5

    .line 214
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->finallyAction:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    if-eqz v0, :cond_0

    .line 215
    new-instance v0, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    iget-object v1, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->finallyAction:Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    invoke-direct {v0, p1, v1}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->finallyAction:Lgroovyjarjarantlr4/v4/codegen/model/Action;

    .line 218
    :cond_0
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->namedActions:Ljava/util/Map;

    .line 219
    iget-object v0, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->namedActions:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    .line 220
    iget-object v2, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->namedActions:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;

    .line 221
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->namedActions:Ljava/util/Map;

    new-instance v4, Lgroovyjarjarantlr4/v4/codegen/model/Action;

    invoke-direct {v4, p1, v2}, Lgroovyjarjarantlr4/v4/codegen/model/Action;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Lgroovyjarjarantlr4/v4/tool/ast/ActionAST;)V

    invoke-interface {v3, v1, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_1
    return-void
.end method

.method public getDeclForAltElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZZ)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;",
            "Ljava/lang/String;",
            "ZZ)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;",
            ">;"
        }
    .end annotation

    const/16 v0, 0x24

    .line 342
    invoke-virtual {p2, v0}, Ljava/lang/String;->indexOf(I)I

    move-result v0

    if-ltz v0, :cond_0

    const/4 v1, 0x0

    .line 344
    invoke-virtual {p2, v1, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p2

    .line 347
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 348
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v1

    const/16 v2, 0x39

    if-ne v1, v2, :cond_3

    .line 349
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object p1

    .line 350
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object v1

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->getRuleFunctionContextStructName(Lgroovyjarjarantlr4/v4/tool/Rule;)Ljava/lang/String;

    move-result-object p1

    if-eqz p3, :cond_2

    .line 353
    iget-object p3, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {p3}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object p3

    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/codegen/Target;->supportsOverloadedMethods()Z

    move-result p3

    if-eqz p3, :cond_1

    .line 354
    new-instance p3, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextRuleListGetterDecl;

    iget-object p4, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {p3, p4, p2, p1}, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextRuleListGetterDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Ljava/lang/String;)V

    invoke-interface {v0, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 355
    :cond_1
    new-instance p3, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextRuleListIndexedGetterDecl;

    iget-object p4, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {p3, p4, p2, p1}, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextRuleListIndexedGetterDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Ljava/lang/String;)V

    invoke-interface {v0, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 358
    :cond_2
    new-instance p3, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextRuleGetterDecl;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {p3, v1, p2, p1, p4}, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextRuleGetterDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-interface {v0, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_3
    if-eqz p3, :cond_5

    .line 363
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getTarget()Lgroovyjarjarantlr4/v4/codegen/Target;

    move-result-object p1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/codegen/Target;->supportsOverloadedMethods()Z

    move-result p1

    if-eqz p1, :cond_4

    .line 364
    new-instance p1, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextTokenListGetterDecl;

    iget-object p3, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {p1, p3, p2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextTokenListGetterDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 365
    :cond_4
    new-instance p1, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextTokenListIndexedGetterDecl;

    iget-object p3, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {p1, p3, p2}, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextTokenListIndexedGetterDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;)V

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 368
    :cond_5
    new-instance p1, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextTokenGetterDecl;

    iget-object p3, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-direct {p1, p3, p2, p4}, Lgroovyjarjarantlr4/v4/codegen/model/decl/ContextTokenGetterDecl;-><init>(Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;Ljava/lang/String;Z)V

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_0
    return-object v0
.end method

.method public getDeclsForAllElements(Ljava/util/List;)Ljava/util/Set;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ">;)",
            "Ljava/util/Set<",
            "Lgroovyjarjarantlr4/v4/codegen/model/decl/Decl;",
            ">;"
        }
    .end annotation

    .line 230
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 231
    new-instance v1, Ljava/util/HashSet;

    invoke-direct {v1}, Ljava/util/HashSet;-><init>()V

    .line 232
    new-instance v2, Ljava/util/HashSet;

    invoke-direct {v2}, Ljava/util/HashSet;-><init>()V

    .line 233
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 235
    new-instance v4, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v5, 0x3

    new-array v5, v5, [I

    fill-array-data v5, :array_0

    invoke-direct {v4, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 236
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    const/4 v5, 0x0

    const/4 v6, 0x1

    move v7, v6

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v8

    if-eqz v8, :cond_6

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;

    .line 237
    invoke-virtual {v8, v4}, Lgroovyjarjarantlr4/v4/tool/ast/AltAST;->getNodesWithType(Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)Ljava/util/List;

    move-result-object v9

    invoke-direct {p0, v9}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getRuleTokens(Ljava/util/List;)Ljava/util/List;

    move-result-object v9

    .line 238
    invoke-interface {v3, v9}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 239
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getElementFrequenciesForAlt(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v8

    .line 240
    invoke-virtual {v8}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    .line 241
    invoke-virtual {v8}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem2()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    .line 242
    invoke-interface {v9}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v9

    :cond_0
    :goto_1
    invoke-interface {v9}, Ljava/util/Iterator;->hasNext()Z

    move-result v11

    if-eqz v11, :cond_3

    invoke-interface {v9}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 243
    iget-object v12, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v12, v12, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-static {v12, v11}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getLabelName(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;

    move-result-object v11

    if-eqz v11, :cond_0

    .line 245
    invoke-virtual {v8, v11}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->count(Ljava/lang/Object;)I

    move-result v12

    if-nez v12, :cond_1

    .line 246
    invoke-interface {v2, v11}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 249
    :cond_1
    invoke-virtual {v8, v11}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->count(Ljava/lang/Object;)I

    move-result v12

    if-le v12, v6, :cond_2

    .line 250
    invoke-interface {v0, v11}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    :cond_2
    if-eqz v7, :cond_0

    .line 253
    invoke-virtual {v10, v11}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->count(Ljava/lang/Object;)I

    move-result v12

    if-eqz v12, :cond_0

    .line 254
    invoke-interface {v1, v11}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 259
    :cond_3
    invoke-interface {v1}, Ljava/util/Set;->size()I

    move-result v7

    new-array v7, v7, [Ljava/lang/String;

    invoke-interface {v1, v7}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v7

    check-cast v7, [Ljava/lang/String;

    array-length v8, v7

    move v9, v5

    :goto_2
    if-ge v9, v8, :cond_5

    aget-object v11, v7, v9

    .line 260
    invoke-virtual {v10, v11}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;->count(Ljava/lang/Object;)I

    move-result v12

    if-nez v12, :cond_4

    .line 261
    invoke-interface {v1, v11}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    :cond_4
    add-int/lit8 v9, v9, 0x1

    goto :goto_2

    :cond_5
    move v7, v5

    goto :goto_0

    .line 267
    :cond_6
    new-instance p1, Ljava/util/LinkedHashSet;

    invoke-direct {p1}, Ljava/util/LinkedHashSet;-><init>()V

    .line 268
    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_7
    :goto_3
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_9

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 269
    iget-object v5, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-static {v5, v4}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getLabelName(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Ljava/lang/String;

    move-result-object v5

    if-eqz v5, :cond_7

    .line 270
    invoke-interface {v2, v5}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_8

    goto :goto_3

    .line 274
    :cond_8
    invoke-interface {v0, v5}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v7

    invoke-interface {v1, v5}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v8

    xor-int/2addr v8, v6

    invoke-virtual {p0, v4, v5, v7, v8}, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->getDeclForAltElement(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;Ljava/lang/String;ZZ)Ljava/util/List;

    move-result-object v4

    .line 278
    invoke-interface {p1, v4}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    goto :goto_3

    :cond_9
    return-object p1

    nop

    :array_0
    .array-data 4
        0x39
        0x42
        0x3e
    .end array-data
.end method

.method public getEffectiveAltLabelContexts(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/codegen/OutputModelController;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/codegen/model/decl/AltLabelStructDecl;",
            ">;"
        }
    .end annotation

    .line 153
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->altLabelCtxs:Ljava/util/Map;

    if-eqz v0, :cond_0

    return-object v0

    .line 157
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->rule(Lgroovyjarjarantlr4/v4/tool/Rule;)Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    move-result-object p1

    if-eq p1, p0, :cond_1

    .line 163
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->altLabelCtxs:Ljava/util/Map;

    return-object p1

    .line 159
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "Rule function does not have an effective context"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getEffectiveRuleContext(Lgroovyjarjarantlr4/v4/codegen/OutputModelController;)Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;
    .locals 2

    .line 139
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ruleCtx:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    if-eqz v0, :cond_0

    return-object v0

    .line 143
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/Rule;->getBaseContext()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRule(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Rule;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/codegen/OutputModelController;->rule(Lgroovyjarjarantlr4/v4/tool/Rule;)Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;

    move-result-object p1

    if-eq p1, p0, :cond_1

    .line 149
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->ruleCtx:Lgroovyjarjarantlr4/v4/codegen/model/decl/StructDecl;

    return-object p1

    .line 145
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "Rule function does not have an effective context"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method protected getElementFrequenciesForAlt(Lgroovyjarjarantlr4/v4/tool/ast/AltAST;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/tool/ast/AltAST;",
            ")",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/misc/FrequencySet<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 326
    :try_start_0
    new-instance v1, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->rule:Lgroovyjarjarantlr4/v4/tool/Rule;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    new-instance v3, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    new-instance v4, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v4}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>()V

    invoke-direct {v3, v4, p1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/Object;)V

    invoke-direct {v1, v2, v3}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V

    .line 327
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->outerAlternative()Lgroovyjarjarantlr4/v4/parse/GrammarTreeVisitor$outerAlternative_return;

    .line 328
    iget-object p1, v1, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->size()I

    move-result p1

    const/4 v2, 0x1

    if-eq p1, v2, :cond_0

    .line 329
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object p1

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v2, v0, [Ljava/lang/Object;

    invoke-virtual {p1, v1, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    .line 330
    new-instance p1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    new-instance v1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-static {p1, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p1

    return-object p1

    .line 333
    :cond_0
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->getMinFrequencies()Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    move-result-object p1

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/codegen/model/ElementFrequenciesVisitor;->frequencies:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v1

    invoke-static {p1, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p1
    :try_end_0
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 336
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/codegen/model/RuleFunction;->factory:Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/codegen/OutputModelFactory;->getGrammar()Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v1

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {v1, v2, p1, v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    .line 337
    new-instance p1, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {p1}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    new-instance v0, Lgroovyjarjarantlr4/v4/misc/FrequencySet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/FrequencySet;-><init>()V

    invoke-static {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object p1

    return-object p1
.end method
