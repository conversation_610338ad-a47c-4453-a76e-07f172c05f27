.class public Lgroovyjarjarantlr4/v4/codegen/LexerFactory;
.super Lgroovyjarjarantlr4/v4/codegen/DefaultOutputModelFactory;
.source "LexerFactory.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;)V
    .locals 0

    .line 11
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/codegen/DefaultOutputModelFactory;-><init>(Lgroovyjarjarantlr4/v4/codegen/CodeGenerator;)V

    return-void
.end method
