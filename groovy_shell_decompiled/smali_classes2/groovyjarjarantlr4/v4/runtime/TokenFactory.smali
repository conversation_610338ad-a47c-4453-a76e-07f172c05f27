.class public interface abstract Lgroovyjarjarantlr4/v4/runtime/TokenFactory;
.super Ljava/lang/Object;
.source "TokenFactory.java"


# virtual methods
.method public abstract create(ILjava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/Token;
.end method

.method public abstract create(Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;ILjava/lang/String;IIIII)Lgroovyjarjarantlr4/v4/runtime/Token;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/TokenSource;",
            "Lgroovyjarjarantlr4/v4/runtime/CharStream;",
            ">;I",
            "Ljava/lang/String;",
            "IIIII)",
            "Lgroovyjarjarantlr4/v4/runtime/Token;"
        }
    .end annotation
.end method
