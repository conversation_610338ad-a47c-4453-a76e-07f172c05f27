.class public interface abstract Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;
.super Ljava/lang/Object;
.source "ANTLRErrorListener.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<Symbol:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract syntaxError(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Ljava/lang/Object;IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:TSymbol;>(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "TT;*>;TT;II",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/RecognitionException;",
            ")V"
        }
    .end annotation
.end method
