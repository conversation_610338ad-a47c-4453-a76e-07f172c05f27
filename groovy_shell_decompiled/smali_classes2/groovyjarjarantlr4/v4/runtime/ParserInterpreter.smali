.class public Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;
.super Lgroovyjarjarantlr4/v4/runtime/Parser;
.source "ParserInterpreter.java"


# instance fields
.field protected final _parentContextStack:Ljava/util/Deque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Deque<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2<",
            "Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;",
            "Ljava/lang/Integer;",
            ">;>;"
        }
    .end annotation
.end field

.field protected final atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

.field protected final grammarFileName:Ljava/lang/String;

.field protected overrideDecision:I

.field protected overrideDecisionAlt:I

.field protected overrideDecisionInputIndex:I

.field protected overrideDecisionReached:Z

.field protected overrideDecisionRoot:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

.field protected final pushRecursionContextStates:Ljava/util/BitSet;

.field protected rootContext:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

.field protected final ruleNames:[Ljava/lang/String;

.field protected final tokenNames:[Ljava/lang/String;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field private final vocabulary:Lgroovyjarjarantlr4/v4/runtime/Vocabulary;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;)V
    .locals 2

    .line 99
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/Parser;-><init>(Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    .line 71
    new-instance v0, Ljava/util/ArrayDeque;

    invoke-direct {v0}, Ljava/util/ArrayDeque;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_parentContextStack:Ljava/util/Deque;

    const/4 v0, -0x1

    .line 77
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecision:I

    .line 78
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionInputIndex:I

    .line 79
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionAlt:I

    const/4 v0, 0x0

    .line 80
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionReached:Z

    const/4 v0, 0x0

    .line 86
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionRoot:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    .line 100
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->grammarFileName:Ljava/lang/String;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->grammarFileName:Ljava/lang/String;

    .line 101
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 102
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->pushRecursionContextStates:Ljava/util/BitSet;

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->pushRecursionContextStates:Ljava/util/BitSet;

    .line 103
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->tokenNames:[Ljava/lang/String;

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->tokenNames:[Ljava/lang/String;

    .line 104
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->ruleNames:[Ljava/lang/String;

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->ruleNames:[Ljava/lang/String;

    .line 105
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->vocabulary:Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->vocabulary:Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    .line 106
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    invoke-direct {p1, p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->setInterpreter(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/Vocabulary;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Lgroovyjarjarantlr4/v4/runtime/TokenStream;",
            ")V"
        }
    .end annotation

    .line 121
    invoke-direct {p0, p5}, Lgroovyjarjarantlr4/v4/runtime/Parser;-><init>(Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    .line 71
    new-instance p5, Ljava/util/ArrayDeque;

    invoke-direct {p5}, Ljava/util/ArrayDeque;-><init>()V

    iput-object p5, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_parentContextStack:Ljava/util/Deque;

    const/4 p5, -0x1

    .line 77
    iput p5, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecision:I

    .line 78
    iput p5, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionInputIndex:I

    .line 79
    iput p5, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionAlt:I

    const/4 p5, 0x0

    .line 80
    iput-boolean p5, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionReached:Z

    const/4 v0, 0x0

    .line 86
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionRoot:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    .line 122
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->grammarFileName:Ljava/lang/String;

    .line 123
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 124
    iget p1, p4, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->maxTokenType:I

    new-array p1, p1, [Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->tokenNames:[Ljava/lang/String;

    .line 125
    :goto_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->tokenNames:[Ljava/lang/String;

    array-length v0, p1

    if-ge p5, v0, :cond_0

    .line 126
    invoke-interface {p2, p5}, Lgroovyjarjarantlr4/v4/runtime/Vocabulary;->getDisplayName(I)Ljava/lang/String;

    move-result-object v0

    aput-object v0, p1, p5

    add-int/lit8 p5, p5, 0x1

    goto :goto_0

    .line 129
    :cond_0
    invoke-interface {p3}, Ljava/util/Collection;->size()I

    move-result p1

    new-array p1, p1, [Ljava/lang/String;

    invoke-interface {p3, p1}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->ruleNames:[Ljava/lang/String;

    .line 130
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->vocabulary:Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    .line 133
    new-instance p1, Ljava/util/BitSet;

    iget-object p2, p4, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    invoke-direct {p1, p2}, Ljava/util/BitSet;-><init>(I)V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->pushRecursionContextStates:Ljava/util/BitSet;

    .line 134
    iget-object p1, p4, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 135
    instance-of p3, p2, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    if-nez p3, :cond_2

    goto :goto_1

    .line 139
    :cond_2
    move-object p3, p2

    check-cast p3, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    iget-boolean p3, p3, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;->precedenceRuleDecision:Z

    if-eqz p3, :cond_1

    .line 140
    iget-object p3, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->pushRecursionContextStates:Ljava/util/BitSet;

    iget p2, p2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p3, p2}, Ljava/util/BitSet;->set(I)V

    goto :goto_1

    .line 145
    :cond_3
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    invoke-direct {p1, p0, p4}, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->setInterpreter(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/util/Collection;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Lgroovyjarjarantlr4/v4/runtime/TokenStream;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 115
    invoke-interface {p2}, Ljava/util/Collection;->size()I

    move-result v0

    new-array v0, v0, [Ljava/lang/String;

    invoke-interface {p2, v0}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/String;

    invoke-static {p2}, Lgroovyjarjarantlr4/v4/runtime/VocabularyImpl;->fromTokenNames([Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v2

    move-object v0, p0

    move-object v1, p1

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;-><init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    return-void
.end method


# virtual methods
.method public addDecisionOverride(III)V
    .locals 0

    .line 404
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecision:I

    .line 405
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionInputIndex:I

    .line 406
    iput p3, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionAlt:I

    return-void
.end method

.method protected createInterpreterRuleContext(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;
    .locals 1

    .line 345
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    invoke-direct {v0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V

    return-object v0
.end method

.method public enterRecursionRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;III)V
    .locals 3

    .line 232
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_parentContextStack:Ljava/util/Deque;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget v2, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->invokingState:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 233
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarantlr4/v4/runtime/Parser;->enterRecursionRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;III)V

    return-void
.end method

.method public getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;
    .locals 1

    .line 157
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    return-object v0
.end method

.method protected getATNState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;
    .locals 2

    .line 237
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getState()I

    move-result v1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    return-object v0
.end method

.method public getGrammarFileName()Ljava/lang/String;
    .locals 1

    .line 178
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->grammarFileName:Ljava/lang/String;

    return-object v0
.end method

.method public getOverrideDecisionRoot()Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;
    .locals 1

    .line 410
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionRoot:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    return-object v0
.end method

.method public getRootContext()Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;
    .locals 1

    .line 463
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->rootContext:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    return-object v0
.end method

.method public getRuleNames()[Ljava/lang/String;
    .locals 1

    .line 173
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->ruleNames:[Ljava/lang/String;

    return-object v0
.end method

.method public getTokenNames()[Ljava/lang/String;
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 163
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->tokenNames:[Ljava/lang/String;

    return-object v0
.end method

.method public getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;
    .locals 1

    .line 168
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->vocabulary:Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    return-object v0
.end method

.method public parse(I)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
    .locals 4

    .line 183
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    aget-object v0, v0, p1

    const/4 v1, 0x0

    const/4 v2, -0x1

    .line 185
    invoke-virtual {p0, v1, v2, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->createInterpreterRuleContext(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    move-result-object v1

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->rootContext:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    .line 186
    iget-boolean v1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->isPrecedenceRule:Z

    if-eqz v1, :cond_0

    .line 187
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->rootContext:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    iget v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->stateNumber:I

    const/4 v3, 0x0

    invoke-virtual {p0, v1, v2, p1, v3}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->enterRecursionRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;III)V

    goto :goto_0

    .line 190
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->rootContext:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    iget v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->stateNumber:I

    invoke-virtual {p0, v1, v2, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->enterRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V

    .line 194
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getATNState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object p1

    .line 195
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getStateType()I

    move-result v1

    const/4 v2, 0x7

    if-eq v1, v2, :cond_1

    .line 216
    :try_start_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->visitState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V
    :try_end_0
    .catch Lgroovyjarjarantlr4/v4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v1

    .line 219
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStopState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    aget-object p1, v2, p1

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;->stateNumber:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->setState(I)V

    .line 220
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getContext()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object p1

    iput-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->exception:Lgroovyjarjarantlr4/v4/runtime/RecognitionException;

    .line 221
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getErrorHandler()Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    move-result-object p1

    invoke-interface {p1, p0, v1}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->reportError(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    .line 222
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->recover(Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    goto :goto_0

    .line 198
    :cond_1
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_3

    .line 199
    iget-boolean p1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->isPrecedenceRule:Z

    if-eqz p1, :cond_2

    .line 200
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 201
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_parentContextStack:Ljava/util/Deque;

    invoke-interface {v0}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    .line 202
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->unrollRecursionContexts(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V

    return-object p1

    .line 206
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->exitRule()V

    .line 207
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->rootContext:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    return-object p1

    .line 211
    :cond_3
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->visitRuleStopState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V

    goto :goto_0
.end method

.method protected recover(Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 11

    .line 418
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->index()I

    move-result v0

    .line 419
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getErrorHandler()Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    move-result-object v1

    invoke-interface {v1, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->recover(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    .line 420
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->index()I

    move-result v1

    if-ne v1, v0, :cond_2

    .line 422
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;

    if-eqz v0, :cond_1

    .line 423
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;

    .line 424
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->getOffendingToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    const/4 v1, 0x0

    .line 426
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;->getExpectedTokens()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->isNil()Z

    move-result v2

    if-nez v2, :cond_0

    .line 427
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;->getExpectedTokens()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->getMinElement()I

    move-result v1

    :cond_0
    move v4, v1

    .line 429
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getTokenFactory()Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

    move-result-object v2

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    move-result-object v0

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/TokenSource;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v3

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v5

    const/4 v6, 0x0

    const/4 v7, -0x1

    const/4 v8, -0x1

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getLine()I

    move-result v9

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getCharPositionInLine()I

    move-result v10

    invoke-interface/range {v2 .. v10}, Lgroovyjarjarantlr4/v4/runtime/TokenFactory;->create(Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;ILjava/lang/String;IIIII)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    .line 435
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p0, v1, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->createErrorNode(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addErrorNode(Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    goto :goto_0

    .line 438
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->getOffendingToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    .line 439
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getTokenFactory()Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

    move-result-object v0

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    move-result-object v1

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/v4/runtime/TokenSource;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v2

    invoke-static {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v1

    const/4 v2, 0x0

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    const/4 v5, -0x1

    const/4 v6, -0x1

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getLine()I

    move-result v7

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getCharPositionInLine()I

    move-result v8

    invoke-interface/range {v0 .. v8}, Lgroovyjarjarantlr4/v4/runtime/TokenFactory;->create(Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;ILjava/lang/String;IIIII)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    .line 445
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p0, v1, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->createErrorNode(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addErrorNode(Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    :cond_2
    :goto_0
    return-void
.end method

.method protected recoverInline()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 1

    .line 451
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    invoke-interface {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->recoverInline(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    return-object v0
.end method

.method public reset()V
    .locals 1

    .line 150
    invoke-super {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->reset()V

    const/4 v0, 0x0

    .line 151
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionReached:Z

    const/4 v0, 0x0

    .line 152
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionRoot:Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    return-void
.end method

.method protected visitDecisionState(Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;)I
    .locals 3

    .line 325
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getErrorHandler()Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    move-result-object v0

    invoke-interface {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->sync(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 326
    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    .line 327
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecision:I

    if-ne p1, v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->index()I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionInputIndex:I

    if-ne v0, v1, :cond_0

    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionReached:Z

    if-nez v0, :cond_0

    .line 328
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionAlt:I

    const/4 v0, 0x1

    .line 329
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->overrideDecisionReached:Z

    goto :goto_0

    .line 332
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v0, v1, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->adaptivePredict(Lgroovyjarjarantlr4/v4/runtime/TokenStream;ILgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)I

    move-result p1

    :goto_0
    return p1
.end method

.method protected visitRuleStopState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V
    .locals 1

    .line 349
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    aget-object p1, v0, p1

    .line 350
    iget-boolean p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->isPrecedenceRule:Z

    if-eqz p1, :cond_0

    .line 351
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_parentContextStack:Ljava/util/Deque;

    invoke-interface {p1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    .line 352
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->unrollRecursionContexts(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V

    .line 353
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem2()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->setState(I)V

    goto :goto_0

    .line 356
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->exitRule()V

    .line 359
    :goto_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->getState()I

    move-result v0

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    .line 360
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->setState(I)V

    return-void
.end method

.method protected visitState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V
    .locals 4

    .line 242
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v0

    const/4 v1, 0x1

    if-le v0, v1, :cond_0

    .line 243
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->visitDecisionState(Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;)I

    move-result v0

    goto :goto_0

    :cond_0
    move v0, v1

    :goto_0
    sub-int/2addr v0, v1

    .line 246
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v0

    .line 247
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->getSerializationType()I

    move-result v2

    packed-switch v2, :pswitch_data_0

    .line 313
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Unrecognized ATN transition type."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 307
    :pswitch_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-object v2, v0

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/PrecedencePredicateTransition;

    iget v3, v2, Lgroovyjarjarantlr4/v4/runtime/atn/PrecedencePredicateTransition;->precedence:I

    invoke-virtual {p0, p1, v3}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->precpred(Lgroovyjarjarantlr4/v4/runtime/RuleContext;I)Z

    move-result p1

    if-eqz p1, :cond_1

    goto/16 :goto_1

    .line 308
    :cond_1
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/FailedPredicateException;

    new-array v0, v1, [Ljava/lang/Object;

    const/4 v1, 0x0

    iget v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/PrecedencePredicateTransition;->precedence:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v0, v1

    const-string v1, "precpred(_ctx, %d)"

    invoke-static {v1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, p0, v0}, Lgroovyjarjarantlr4/v4/runtime/FailedPredicateException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;Ljava/lang/String;)V

    throw p1

    .line 278
    :pswitch_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->matchWildcard()Lgroovyjarjarantlr4/v4/runtime/Token;

    goto/16 :goto_1

    .line 302
    :pswitch_2
    move-object p1, v0

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;

    .line 303
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->ruleIndex:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->actionIndex:I

    invoke-virtual {p0, v1, v2, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->action(Lgroovyjarjarantlr4/v4/runtime/RuleContext;II)V

    goto/16 :goto_1

    .line 265
    :pswitch_3
    move-object p1, v0

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->match(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    goto/16 :goto_1

    .line 294
    :pswitch_4
    move-object p1, v0

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;

    .line 295
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;->ruleIndex:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;->predIndex:I

    invoke-virtual {p0, v1, v2, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->sempred(Lgroovyjarjarantlr4/v4/runtime/RuleContext;II)Z

    move-result p1

    if-eqz p1, :cond_2

    goto/16 :goto_1

    .line 296
    :cond_2
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/FailedPredicateException;

    invoke-direct {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/FailedPredicateException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    throw p1

    .line 282
    :pswitch_5
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    .line 283
    iget v2, v1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->ruleIndex:I

    .line 284
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p0, v3, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->createInterpreterRuleContext(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    move-result-object p1

    .line 285
    iget-boolean v3, v1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->isPrecedenceRule:Z

    if-eqz v3, :cond_3

    .line 286
    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->stateNumber:I

    move-object v3, v0

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    iget v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->precedence:I

    invoke-virtual {p0, p1, v1, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->enterRecursionRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;III)V

    goto :goto_1

    .line 289
    :cond_3
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p0, p1, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->enterRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V

    goto :goto_1

    .line 271
    :pswitch_6
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    invoke-interface {p1, v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LA(I)I

    move-result p1

    const v2, 0xffff

    invoke-virtual {v0, p1, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->matches(III)Z

    move-result p1

    if-nez p1, :cond_4

    .line 272
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->recoverInline()Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 274
    :cond_4
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->matchWildcard()Lgroovyjarjarantlr4/v4/runtime/Token;

    goto :goto_1

    .line 249
    :pswitch_7
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->pushRecursionContextStates:Ljava/util/BitSet;

    iget v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v1, v2}, Ljava/util/BitSet;->get(I)Z

    move-result v1

    if-eqz v1, :cond_5

    iget-object v1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    instance-of v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/LoopEndState;

    if-nez v1, :cond_5

    .line 254
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_parentContextStack:Ljava/util/Deque;

    invoke-interface {v1}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem1()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_parentContextStack:Ljava/util/Deque;

    invoke-interface {v2}, Ljava/util/Deque;->peek()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->getItem2()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getRuleIndex()I

    move-result v3

    invoke-virtual {p0, v1, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->createInterpreterRuleContext(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;

    move-result-object v1

    .line 258
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    aget-object p1, v2, p1

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->stateNumber:I

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getRuleIndex()I

    move-result v2

    invoke-virtual {p0, v1, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->pushNewRecursionContext(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V

    .line 316
    :cond_5
    :goto_1
    iget-object p1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserInterpreter;->setState(I)V

    return-void

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_6
        :pswitch_6
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
