.class final Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;
.super Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
.source "CodePointCharStream.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "CodePoint32BitCharStream"
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field private final intArray:[I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 252
    const-class v0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    return-void
.end method

.method private constructor <init>(IILjava/lang/String;[II)V
    .locals 0

    const/4 p5, 0x0

    .line 256
    invoke-direct {p0, p1, p2, p3, p5}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;-><init>(IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;)V

    .line 257
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->intArray:[I

    return-void
.end method

.method synthetic constructor <init>(IILjava/lang/String;[IILgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;)V
    .locals 0

    .line 252
    invoke-direct/range {p0 .. p5}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;-><init>(IILjava/lang/String;[II)V

    return-void
.end method


# virtual methods
.method public LA(I)I
    .locals 3

    .line 276
    invoke-static {p1}, Ljava/lang/Integer;->signum(I)I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_3

    if-eqz v0, :cond_2

    const/4 v2, 0x1

    if-ne v0, v2, :cond_1

    .line 287
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->position:I

    add-int/2addr v0, p1

    sub-int/2addr v0, v2

    .line 288
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->size:I

    if-lt v0, p1, :cond_0

    return v1

    .line 291
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->intArray:[I

    aget p1, p1, v0

    return p1

    .line 293
    :cond_1
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Not reached"

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_2
    const/4 p1, 0x0

    return p1

    .line 278
    :cond_3
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->position:I

    add-int/2addr v0, p1

    if-gez v0, :cond_4

    return v1

    .line 282
    :cond_4
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->intArray:[I

    aget p1, p1, v0

    return p1
.end method

.method getInternalStorage()Ljava/lang/Object;
    .locals 1

    .line 298
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->intArray:[I

    return-object v0
.end method

.method public getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;
    .locals 3

    .line 265
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->size:I

    invoke-static {v0, v1}, Ljava/lang/Math;->min(II)I

    move-result v0

    .line 266
    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    sub-int/2addr v1, p1

    add-int/lit8 v1, v1, 0x1

    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->size:I

    sub-int/2addr p1, v0

    invoke-static {v1, p1}, Ljava/lang/Math;->min(II)I

    move-result p1

    .line 270
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;->intArray:[I

    invoke-direct {v1, v2, v0, p1}, Ljava/lang/String;-><init>([III)V

    return-object v1
.end method
