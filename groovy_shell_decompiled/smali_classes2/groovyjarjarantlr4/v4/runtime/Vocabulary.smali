.class public interface abstract Lgroovyjarjarantlr4/v4/runtime/Vocabulary;
.super Ljava/lang/Object;
.source "Vocabulary.java"


# virtual methods
.method public abstract getDisplayName(I)Ljava/lang/String;
.end method

.method public abstract getLiteralName(I)Ljava/lang/String;
.end method

.method public abstract getMaxTokenType()I
.end method

.method public abstract getSymbolicName(I)Ljava/lang/String;
.end method
