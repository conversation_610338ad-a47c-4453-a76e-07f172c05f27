.class public Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;
.super Ljava/lang/Object;
.source "DefaultErrorStrategy.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;


# instance fields
.field protected errorRecoveryMode:Z

.field protected lastErrorIndex:I

.field protected lastErrorStates:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

.field protected nextTokensContext:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

.field protected nextTokensState:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 22
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 30
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->errorRecoveryMode:Z

    const/4 v0, -0x1

    .line 38
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->lastErrorIndex:I

    return-void
.end method


# virtual methods
.method protected beginErrorCondition(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 0

    const/4 p1, 0x1

    .line 75
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->errorRecoveryMode:Z

    return-void
.end method

.method protected constructToken(Lgroovyjarjarantlr4/v4/runtime/TokenSource;ILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 9

    .line 612
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/TokenSource;->getTokenFactory()Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

    move-result-object v0

    .line 613
    invoke-interface {p4}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/TokenSource;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v1

    invoke-static {p1, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v1

    invoke-interface {p4}, Lgroovyjarjarantlr4/v4/runtime/Token;->getLine()I

    move-result v7

    invoke-interface {p4}, Lgroovyjarjarantlr4/v4/runtime/Token;->getCharPositionInLine()I

    move-result v8

    const/4 v4, 0x0

    const/4 v5, -0x1

    const/4 v6, -0x1

    move v2, p2

    move-object v3, p3

    invoke-interface/range {v0 .. v8}, Lgroovyjarjarantlr4/v4/runtime/TokenFactory;->create(Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;ILjava/lang/String;IIIII)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    return-object p1
.end method

.method protected consumeUntil(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V
    .locals 3

    .line 777
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LA(I)I

    move-result v0

    :goto_0
    const/4 v2, -0x1

    if-eq v0, v2, :cond_0

    .line 778
    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v0

    if-nez v0, :cond_0

    .line 781
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->consume()Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 782
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LA(I)I

    move-result v0

    goto :goto_0

    :cond_0
    return-void
.end method

.method protected endErrorCondition(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 0

    const/4 p1, 0x0

    .line 93
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->errorRecoveryMode:Z

    const/4 p1, 0x0

    .line 94
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->lastErrorStates:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 p1, -0x1

    .line 95
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->lastErrorIndex:I

    return-void
.end method

.method protected escapeWSAndQuote(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    const-string v0, "\n"

    const-string v1, "\\n"

    .line 658
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "\r"

    const-string v1, "\\r"

    .line 659
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "\t"

    const-string v1, "\\t"

    .line 660
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    .line 661
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected getErrorRecoverySet(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 5

    .line 758
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 759
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 760
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v2, 0x0

    new-array v3, v2, [I

    invoke-direct {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    :goto_0
    if-eqz p1, :cond_0

    .line 761
    iget v3, p1, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->invokingState:I

    if-ltz v3, :cond_0

    .line 763
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    iget v4, p1, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->invokingState:I

    invoke-interface {v3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 764
    invoke-virtual {v3, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    .line 765
    iget-object v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v3

    .line 766
    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->addAll(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    .line 767
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    goto :goto_0

    :cond_0
    const/4 p1, -0x2

    .line 769
    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->remove(I)V

    return-object v1
.end method

.method protected getExpectedTokens(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 0

    .line 622
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getExpectedTokens()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p1

    return-object p1
.end method

.method protected getMissingSymbol(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 6

    .line 593
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 594
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getExpectedTokens(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v1

    .line 596
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->isNil()Z

    move-result v2

    if-nez v2, :cond_0

    .line 597
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->getMinElement()I

    move-result v1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    const/4 v2, -0x1

    if-ne v1, v2, :cond_1

    const-string v3, "<missing EOF>"

    goto :goto_1

    .line 601
    :cond_1
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "<missing "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v4

    invoke-interface {v4, v1}, Lgroovyjarjarantlr4/v4/runtime/Vocabulary;->getDisplayName(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ">"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 603
    :goto_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v4

    invoke-interface {v4, v2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v4

    .line 604
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v5

    if-ne v5, v2, :cond_2

    if-eqz v4, :cond_2

    move-object v0, v4

    .line 608
    :cond_2
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object p1

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    move-result-object p1

    invoke-virtual {p0, p1, v1, v3, v0}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->constructToken(Lgroovyjarjarantlr4/v4/runtime/TokenSource;ILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    return-object p1
.end method

.method protected getSymbolText(Lgroovyjarjarantlr4/v4/runtime/Token;)Ljava/lang/String;
    .locals 0

    .line 648
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected getSymbolType(Lgroovyjarjarantlr4/v4/runtime/Token;)I
    .locals 0

    .line 652
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result p1

    return p1
.end method

.method protected getTokenErrorDisplay(Lgroovyjarjarantlr4/v4/runtime/Token;)Ljava/lang/String;
    .locals 2

    if-nez p1, :cond_0

    const-string p1, "<no token>"

    return-object p1

    .line 635
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getSymbolText(Lgroovyjarjarantlr4/v4/runtime/Token;)Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_2

    .line 637
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getSymbolType(Lgroovyjarjarantlr4/v4/runtime/Token;)I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_1

    const-string v0, "<EOF>"

    goto :goto_0

    .line 641
    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getSymbolType(Lgroovyjarjarantlr4/v4/runtime/Token;)I

    move-result p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ">"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 644
    :cond_2
    :goto_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->escapeWSAndQuote(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public inErrorRecoveryMode(Lgroovyjarjarantlr4/v4/runtime/Parser;)Z
    .locals 0

    .line 83
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->errorRecoveryMode:Z

    return p1
.end method

.method protected notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Parser;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 1

    .line 154
    invoke-virtual {p3, p1}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->getOffendingToken(Lgroovyjarjarantlr4/v4/runtime/Recognizer;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-virtual {p1, v0, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/Parser;->notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Token;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    return-void
.end method

.method public recover(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 1

    .line 171
    iget p2, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->lastErrorIndex:I

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->index()I

    move-result v0

    if-ne p2, v0, :cond_0

    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->lastErrorStates:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    if-eqz p2, :cond_0

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getState()I

    move-result v0

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result p2

    if-eqz p2, :cond_0

    .line 181
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->consume()Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 183
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object p2

    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->index()I

    move-result p2

    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->lastErrorIndex:I

    .line 184
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->lastErrorStates:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    if-nez p2, :cond_1

    new-instance p2, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v0, 0x0

    new-array v0, v0, [I

    invoke-direct {p2, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->lastErrorStates:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    .line 185
    :cond_1
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->lastErrorStates:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getState()I

    move-result v0

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    .line 186
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getErrorRecoverySet(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p2

    .line 187
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->consumeUntil(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    return-void
.end method

.method public recoverInline(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
        }
    .end annotation

    .line 474
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->singleTokenDeletion(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 478
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->consume()Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object v0

    .line 483
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->singleTokenInsertion(Lgroovyjarjarantlr4/v4/runtime/Parser;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 484
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getMissingSymbol(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    return-object p1

    .line 489
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->nextTokensContext:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    if-nez v0, :cond_2

    .line 490
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    goto :goto_0

    .line 492
    :cond_2
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->nextTokensState:I

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->nextTokensContext:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-direct {v0, p1, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;ILgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V

    .line 495
    :goto_0
    throw v0
.end method

.method public reportError(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 3

    .line 133
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->inErrorRecoveryMode(Lgroovyjarjarantlr4/v4/runtime/Parser;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 137
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->beginErrorCondition(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 138
    instance-of v0, p2, Lgroovyjarjarantlr4/v4/runtime/NoViableAltException;

    if-eqz v0, :cond_1

    .line 139
    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/NoViableAltException;

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->reportNoViableAlternative(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/NoViableAltException;)V

    goto :goto_0

    .line 141
    :cond_1
    instance-of v0, p2, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;

    if-eqz v0, :cond_2

    .line 142
    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->reportInputMismatch(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;)V

    goto :goto_0

    .line 144
    :cond_2
    instance-of v0, p2, Lgroovyjarjarantlr4/v4/runtime/FailedPredicateException;

    if-eqz v0, :cond_3

    .line 145
    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/FailedPredicateException;

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->reportFailedPredicate(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/FailedPredicateException;)V

    goto :goto_0

    .line 148
    :cond_3
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "unknown recognition error type: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 149
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, p1, v0, p2}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Parser;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    :goto_0
    return-void
.end method

.method protected reportFailedPredicate(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/FailedPredicateException;)V
    .locals 3

    .line 349
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleNames()[Ljava/lang/String;

    move-result-object v0

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getRuleIndex()I

    move-result v1

    aget-object v0, v0, v1

    .line 350
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "rule "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/FailedPredicateException;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 351
    invoke-virtual {p0, p1, v0, p2}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Parser;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    return-void
.end method

.method protected reportInputMismatch(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;)V
    .locals 3

    .line 332
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "mismatched input "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;->getOffendingToken(Lgroovyjarjarantlr4/v4/runtime/Recognizer;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getTokenErrorDisplay(Lgroovyjarjarantlr4/v4/runtime/Token;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " expecting "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;->getExpectedTokens()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 334
    invoke-virtual {p0, p1, v0, p2}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Parser;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    return-void
.end method

.method public reportMatch(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 0

    .line 105
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->endErrorCondition(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    return-void
.end method

.method protected reportMissingToken(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 4

    .line 405
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->inErrorRecoveryMode(Lgroovyjarjarantlr4/v4/runtime/Parser;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 409
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->beginErrorCondition(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 411
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 412
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getExpectedTokens(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v1

    .line 413
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "missing "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v3

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " at "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getTokenErrorDisplay(Lgroovyjarjarantlr4/v4/runtime/Token;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    .line 416
    invoke-virtual {p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/Parser;->notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Token;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    return-void
.end method

.method protected reportNoViableAlternative(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/NoViableAltException;)V
    .locals 3

    .line 307
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 310
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/NoViableAltException;->getStartToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    const-string v0, "<EOF>"

    goto :goto_0

    .line 311
    :cond_0
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/NoViableAltException;->getStartToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v1

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/NoViableAltException;->getOffendingToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v2

    invoke-interface {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->getText(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_1
    const-string v0, "<unknown input>"

    .line 316
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "no viable alternative at input "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->escapeWSAndQuote(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 317
    invoke-virtual {p0, p1, v0, p2}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Parser;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    return-void
.end method

.method protected reportUnwantedToken(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 5

    .line 373
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->inErrorRecoveryMode(Lgroovyjarjarantlr4/v4/runtime/Parser;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 377
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->beginErrorCondition(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 379
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 380
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getTokenErrorDisplay(Lgroovyjarjarantlr4/v4/runtime/Token;)Ljava/lang/String;

    move-result-object v1

    .line 381
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getExpectedTokens(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    .line 382
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "extraneous input "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " expecting "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    .line 384
    invoke-virtual {p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/Parser;->notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Token;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    return-void
.end method

.method public reset(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 0

    .line 65
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->endErrorCondition(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    return-void
.end method

.method protected singleTokenDeletion(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 2

    .line 553
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    const/4 v1, 0x2

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LA(I)I

    move-result v0

    .line 554
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getExpectedTokens(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v1

    .line 555
    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 556
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->reportUnwantedToken(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 563
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->consume()Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 565
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 566
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->reportMatch(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    return-object v0

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method protected singleTokenInsertion(Lgroovyjarjarantlr4/v4/runtime/Parser;)Z
    .locals 6

    .line 516
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LA(I)I

    move-result v0

    .line 520
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getState()I

    move-result v3

    invoke-interface {v2, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    const/4 v3, 0x0

    .line 521
    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v2

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 522
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 523
    iget-object v5, p1, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-static {v4, v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->fromRuleContext(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v5

    invoke-virtual {v4, v2, v5}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    .line 525
    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 526
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->reportMissingToken(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    return v1

    :cond_0
    return v3
.end method

.method public sync(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
        }
    .end annotation

    .line 238
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getState()I

    move-result v1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 241
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->inErrorRecoveryMode(Lgroovyjarjarantlr4/v4/runtime/Parser;)Z

    move-result v1

    if-eqz v1, :cond_0

    return-void

    .line 245
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v1

    const/4 v2, 0x1

    .line 246
    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LA(I)I

    move-result v1

    .line 249
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v2

    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    .line 250
    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v1

    if-eqz v1, :cond_1

    const/4 p1, 0x0

    .line 252
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->nextTokensContext:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    const/4 p1, -0x1

    .line 253
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->nextTokensState:I

    return-void

    :cond_1
    const/4 v1, -0x2

    .line 257
    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v1

    if-eqz v1, :cond_3

    .line 258
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->nextTokensContext:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    if-nez v0, :cond_2

    .line 261
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getContext()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->nextTokensContext:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 262
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getState()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->nextTokensState:I

    :cond_2
    return-void

    .line 267
    :cond_3
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getStateType()I

    move-result v0

    const/4 v1, 0x3

    if-eq v0, v1, :cond_4

    const/4 v1, 0x4

    if-eq v0, v1, :cond_4

    const/4 v1, 0x5

    if-eq v0, v1, :cond_4

    packed-switch v0, :pswitch_data_0

    goto :goto_0

    .line 282
    :pswitch_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->reportUnwantedToken(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 283
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getExpectedTokens()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    .line 284
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->getErrorRecoverySet(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->or(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    .line 286
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->consumeUntil(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;)V

    :goto_0
    return-void

    .line 273
    :cond_4
    :pswitch_1
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;->singleTokenDeletion(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    if-eqz v0, :cond_5

    return-void

    .line 277
    :cond_5
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    throw v0

    :pswitch_data_0
    .packed-switch 0x9
        :pswitch_0
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
