.class public Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;
.super Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
.source "LexerNoViableAltException.java"


# static fields
.field private static final serialVersionUID:J = -0xa2507e72853eafeL


# instance fields
.field private final deadEndConfigs:Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

.field private final startIndex:I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/Lexer;Lgroovyjarjarantlr4/v4/runtime/CharStream;ILgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V
    .locals 0

    .line 31
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Lexer;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    .line 32
    iput p3, p0, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;->startIndex:I

    .line 33
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;->deadEndConfigs:Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    return-void
.end method


# virtual methods
.method public getDeadEndConfigs()Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;
    .locals 1

    .line 42
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;->deadEndConfigs:Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    return-object v0
.end method

.method public getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 1

    .line 47
    invoke-super {p0}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/IntStream;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/CharStream;

    return-object v0
.end method

.method public bridge synthetic getInputStream()Lgroovyjarjarantlr4/v4/runtime/IntStream;
    .locals 1

    .line 17
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v0

    return-object v0
.end method

.method public getStartIndex()I
    .locals 1

    .line 37
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;->startIndex:I

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 5

    .line 53
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;->startIndex:I

    const/4 v1, 0x0

    if-ltz v0, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v2

    invoke-interface {v2}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->size()I

    move-result v2

    if-ge v0, v2, :cond_0

    .line 54
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v0

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;->startIndex:I

    invoke-static {v2, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v2

    invoke-interface {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;

    move-result-object v0

    .line 55
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->escapeWhitespace(Ljava/lang/String;Z)Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const-string v0, ""

    .line 58
    :goto_0
    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    move-result-object v2

    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    const-string v4, "LexerNoViableAltException"

    aput-object v4, v3, v1

    const/4 v1, 0x1

    aput-object v0, v3, v1

    const-string v0, "%s(\'%s\')"

    invoke-static {v2, v0, v3}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
