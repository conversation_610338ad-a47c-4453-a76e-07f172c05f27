.class public interface abstract Lgroovyjarjarantlr4/v4/runtime/TokenStream;
.super Ljava/lang/Object;
.source "TokenStream.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/IntStream;


# virtual methods
.method public abstract LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;
.end method

.method public abstract get(I)Lgroovyjarjarantlr4/v4/runtime/Token;
.end method

.method public abstract getText()Ljava/lang/String;
.end method

.method public abstract getText(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Ljava/lang/String;
.end method

.method public abstract getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;
.end method

.method public abstract getText(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/String;
.end method

.method public abstract getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;
.end method
