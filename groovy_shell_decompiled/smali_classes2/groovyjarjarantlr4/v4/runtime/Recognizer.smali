.class public abstract Lgroovyjarjarantlr4/v4/runtime/Recognizer;
.super Ljava/lang/Object;
.source "Recognizer.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<Symbol:",
        "Ljava/lang/Object;",
        "ATNInterpreter:",
        "Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# static fields
.field public static final EOF:I = -0x1

.field private static final ruleIndexMapCache:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "[",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;>;"
        }
    .end annotation
.end field

.field private static final tokenTypeMapCache:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lgroovyjarjarantlr4/v4/runtime/Vocabulary;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;>;"
        }
    .end annotation
.end field


# instance fields
.field protected _interp:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TATNInterpreter;"
        }
    .end annotation
.end field

.field private _listeners:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
            "-TSymbol;>;>;"
        }
    .end annotation
.end field

.field private _stateNumber:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 28
    new-instance v0, Ljava/util/WeakHashMap;

    invoke-direct {v0}, Ljava/util/WeakHashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->tokenTypeMapCache:Ljava/util/Map;

    .line 30
    new-instance v0, Ljava/util/WeakHashMap;

    invoke-direct {v0}, Ljava/util/WeakHashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->ruleIndexMapCache:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 25
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 33
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/Recognizer$1;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/Recognizer$1;-><init>(Lgroovyjarjarantlr4/v4/runtime/Recognizer;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_listeners:Ljava/util/List;

    const/4 v0, -0x1

    .line 40
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_stateNumber:I

    return-void
.end method


# virtual methods
.method public action(Lgroovyjarjarantlr4/v4/runtime/RuleContext;II)V
    .locals 0

    return-void
.end method

.method public addErrorListener(Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
            "-TSymbol;>;)V"
        }
    .end annotation

    const-string v0, "listener"

    .line 226
    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Args;->notNull(Ljava/lang/String;Ljava/lang/Object;)V

    .line 227
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_listeners:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;
    .locals 1

    .line 151
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_interp:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    return-object v0
.end method

.method public getErrorHeader(Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)Ljava/lang/String;
    .locals 3

    .line 186
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->getOffendingToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getLine()I

    move-result v0

    .line 187
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->getOffendingToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getCharPositionInLine()I

    move-result p1

    .line 188
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "line "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getErrorListenerDispatch()Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
            "-TSymbol;>;"
        }
    .end annotation

    .line 244
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/ProxyErrorListener;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getErrorListeners()Ljava/util/List;

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/ProxyErrorListener;-><init>(Ljava/util/Collection;)V

    return-object v0
.end method

.method public getErrorListeners()Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
            "-TSymbol;>;>;"
        }
    .end annotation

    .line 240
    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_listeners:Ljava/util/List;

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    return-object v0
.end method

.method public abstract getGrammarFileName()Ljava/lang/String;
.end method

.method public abstract getInputStream()Lgroovyjarjarantlr4/v4/runtime/IntStream;
.end method

.method public getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TATNInterpreter;"
        }
    .end annotation

    .line 161
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_interp:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    return-object v0
.end method

.method public getParseInfo()Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getRuleIndexMap()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .line 105
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getRuleNames()[Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 110
    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->ruleIndexMapCache:Ljava/util/Map;

    monitor-enter v1

    .line 111
    :try_start_0
    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map;

    if-nez v2, :cond_0

    .line 113
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->toMap([Ljava/lang/String;)Ljava/util/Map;

    move-result-object v2

    invoke-static {v2}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v2

    .line 114
    invoke-interface {v1, v0, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 117
    :cond_0
    monitor-exit v1

    return-object v2

    :catchall_0
    move-exception v0

    .line 118
    monitor-exit v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0

    .line 107
    :cond_1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "The current recognizer does not provide a list of rule names."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public abstract getRuleNames()[Ljava/lang/String;
.end method

.method public getSerializedATN()Ljava/lang/String;
    .locals 2

    .line 136
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "there is no serialized ATN"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final getState()I
    .locals 1

    .line 261
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_stateNumber:I

    return v0
.end method

.method public getTokenErrorDisplay(Lgroovyjarjarantlr4/v4/runtime/Token;)Ljava/lang/String;
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    if-nez p1, :cond_0

    const-string p1, "<no token>"

    return-object p1

    .line 207
    :cond_0
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_2

    .line 209
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_1

    const-string v0, "<EOF>"

    goto :goto_0

    .line 213
    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ">"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    :cond_2
    :goto_0
    const-string p1, "\n"

    const-string v1, "\\n"

    .line 216
    invoke-virtual {v0, p1, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "\r"

    const-string v1, "\\r"

    .line 217
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "\t"

    const-string v1, "\\t"

    .line 218
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    .line 219
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public abstract getTokenNames()[Ljava/lang/String;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public getTokenType(Ljava/lang/String;)I
    .locals 1

    .line 122
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getTokenTypeMap()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    if-eqz p1, :cond_0

    .line 123
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public getTokenTypeMap()Ljava/util/Map;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .line 72
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v0

    .line 73
    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->tokenTypeMapCache:Ljava/util/Map;

    monitor-enter v1

    .line 74
    :try_start_0
    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map;

    if-nez v2, :cond_3

    .line 76
    new-instance v2, Ljava/util/HashMap;

    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    const/4 v3, 0x0

    .line 77
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v4

    iget v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->maxTokenType:I

    if-gt v3, v4, :cond_2

    .line 78
    invoke-interface {v0, v3}, Lgroovyjarjarantlr4/v4/runtime/Vocabulary;->getLiteralName(I)Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_0

    .line 80
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v2, v4, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 83
    :cond_0
    invoke-interface {v0, v3}, Lgroovyjarjarantlr4/v4/runtime/Vocabulary;->getSymbolicName(I)Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_1

    .line 85
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v2, v4, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    const-string v3, "EOF"

    const/4 v4, -0x1

    .line 89
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-interface {v2, v3, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 90
    invoke-static {v2}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v2

    .line 91
    sget-object v3, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->tokenTypeMapCache:Ljava/util/Map;

    invoke-interface {v3, v0, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 94
    :cond_3
    monitor-exit v1

    return-object v2

    :catchall_0
    move-exception v0

    .line 95
    monitor-exit v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0
.end method

.method public getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;
    .locals 1

    .line 62
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getTokenNames()[Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/VocabularyImpl;->fromTokenNames([Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v0

    return-object v0
.end method

.method public precpred(Lgroovyjarjarantlr4/v4/runtime/RuleContext;I)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method public removeErrorListener(Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
            "-TSymbol;>;)V"
        }
    .end annotation

    .line 231
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_listeners:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    return-void
.end method

.method public removeErrorListeners()V
    .locals 1

    .line 235
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_listeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    return-void
.end method

.method public sempred(Lgroovyjarjarantlr4/v4/runtime/RuleContext;II)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method public setInterpreter(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TATNInterpreter;)V"
        }
    .end annotation

    .line 180
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_interp:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    return-void
.end method

.method public final setState(I)V
    .locals 0

    .line 273
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->_stateNumber:I

    return-void
.end method
