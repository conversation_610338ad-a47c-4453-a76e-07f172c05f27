.class public interface abstract Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;
.super Ljava/lang/Object;
.source "ParserErrorListener.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
        "Lgroovyjarjarantlr4/v4/runtime/Token;",
        ">;"
    }
.end annotation


# virtual methods
.method public abstract reportAmbiguity(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IIZLjava/util/BitSet;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V
.end method

.method public abstract reportAttemptingFullContext(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IILjava/util/BitSet;Lgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;)V
.end method

.method public abstract reportContextSensitivity(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IIILgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;)V
.end method
