.class public Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
.super Ljava/lang/RuntimeException;
.source "RecognitionException.java"


# static fields
.field private static final serialVersionUID:J = -0x3597f7278de752e6L


# instance fields
.field private final ctx:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

.field private final input:Lgroovyjarjarantlr4/v4/runtime/IntStream;

.field private offendingState:I

.field private offendingToken:Lgroovyjarjarantlr4/v4/runtime/Token;

.field private final recognizer:Lgroovyjarjarantlr4/v4/runtime/Recognizer;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "**>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/Lexer;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V
    .locals 1

    .line 42
    invoke-direct {p0}, Ljava/lang/RuntimeException;-><init>()V

    const/4 v0, -0x1

    .line 38
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingState:I

    .line 43
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->recognizer:Lgroovyjarjarantlr4/v4/runtime/Recognizer;

    .line 44
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->input:Lgroovyjarjarantlr4/v4/runtime/IntStream;

    const/4 p1, 0x0

    .line 45
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->ctx:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/IntStream;Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            "*>;",
            "Lgroovyjarjarantlr4/v4/runtime/IntStream;",
            "Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;",
            ")V"
        }
    .end annotation

    .line 51
    invoke-direct {p0}, Ljava/lang/RuntimeException;-><init>()V

    const/4 v0, -0x1

    .line 38
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingState:I

    .line 52
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->recognizer:Lgroovyjarjarantlr4/v4/runtime/Recognizer;

    .line 53
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->input:Lgroovyjarjarantlr4/v4/runtime/IntStream;

    .line 54
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->ctx:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    if-eqz p1, :cond_0

    .line 55
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getState()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingState:I

    :cond_0
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/IntStream;Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            "*>;",
            "Lgroovyjarjarantlr4/v4/runtime/IntStream;",
            "Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;",
            ")V"
        }
    .end annotation

    .line 63
    invoke-direct {p0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    const/4 p1, -0x1

    .line 38
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingState:I

    .line 64
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->recognizer:Lgroovyjarjarantlr4/v4/runtime/Recognizer;

    .line 65
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->input:Lgroovyjarjarantlr4/v4/runtime/IntStream;

    .line 66
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->ctx:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    if-eqz p2, :cond_0

    .line 67
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getState()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingState:I

    :cond_0
    return-void
.end method


# virtual methods
.method public getContext()Lgroovyjarjarantlr4/v4/runtime/RuleContext;
    .locals 1

    .line 116
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->ctx:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    return-object v0
.end method

.method public getExpectedTokens()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 3

    .line 99
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->recognizer:Lgroovyjarjarantlr4/v4/runtime/Recognizer;

    if-eqz v0, :cond_0

    .line 100
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingState:I

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->ctx:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    invoke-virtual {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->getExpectedTokens(ILgroovyjarjarantlr4/v4/runtime/RuleContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public getInputStream()Lgroovyjarjarantlr4/v4/runtime/IntStream;
    .locals 1

    .line 131
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->input:Lgroovyjarjarantlr4/v4/runtime/IntStream;

    return-object v0
.end method

.method public getOffendingState()I
    .locals 1

    .line 80
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingState:I

    return v0
.end method

.method public getOffendingToken()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 1

    .line 136
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object v0
.end method

.method public getOffendingToken(Lgroovyjarjarantlr4/v4/runtime/Recognizer;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "TT;*>;)TT;"
        }
    .end annotation

    .line 160
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->recognizer:Lgroovyjarjarantlr4/v4/runtime/Recognizer;

    if-ne v0, p1, :cond_0

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method public getRecognizer()Lgroovyjarjarantlr4/v4/runtime/Recognizer;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "**>;"
        }
    .end annotation

    .line 155
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->recognizer:Lgroovyjarjarantlr4/v4/runtime/Recognizer;

    return-object v0
.end method

.method protected final setOffendingState(I)V
    .locals 0

    .line 84
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingState:I

    return-void
.end method

.method protected final setOffendingToken(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/Token;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<Symbol::",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "TSymbol;*>;TSymbol;)V"
        }
    .end annotation

    .line 140
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->recognizer:Lgroovyjarjarantlr4/v4/runtime/Recognizer;

    if-ne p1, v0, :cond_0

    .line 141
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;->offendingToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    :cond_0
    return-void
.end method
