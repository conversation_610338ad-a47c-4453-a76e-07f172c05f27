.class public Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
.super Lgroovyjarjarantlr4/v4/runtime/RuleContext;
.source "ParserRuleContext.java"


# static fields
.field static final synthetic $assertionsDisabled:Z

.field private static final EMPTY:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;


# instance fields
.field public children:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">;"
        }
    .end annotation
.end field

.field public exception:Lgroovyjarjarantlr4/v4/runtime/RecognitionException;

.field public start:Lgroovyjarjarantlr4/v4/runtime/Token;

.field public stop:Lgroovyjarjarantlr4/v4/runtime/Token;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 44
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->EMPTY:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 82
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/RuleContext;-><init>()V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;I)V
    .locals 0

    .line 123
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/RuleContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/RuleContext;I)V

    return-void
.end method

.method public static emptyContext()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
    .locals 1

    .line 85
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->EMPTY:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    return-object v0
.end method


# virtual methods
.method public addAnyChild(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T::",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">(TT;)TT;"
        }
    .end annotation

    .line 145
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    .line 146
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public addChild(Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 174
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;-><init>(Lgroovyjarjarantlr4/v4/runtime/Token;)V

    .line 175
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addAnyChild(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    .line 176
    invoke-virtual {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->setParent(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    return-object v0
.end method

.method public addChild(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V
    .locals 0

    .line 151
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addAnyChild(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    return-void
.end method

.method public addChild(Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;)V
    .locals 0

    .line 156
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addAnyChild(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    return-void
.end method

.method public addErrorNode(Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 187
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNodeImpl;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNodeImpl;-><init>(Lgroovyjarjarantlr4/v4/runtime/Token;)V

    .line 188
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addAnyChild(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    .line 189
    invoke-virtual {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNodeImpl;->setParent(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    return-object v0
.end method

.method public addErrorNode(Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;
    .locals 0

    .line 164
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addAnyChild(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    return-object p1
.end method

.method public copyFrom(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V
    .locals 2

    .line 102
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    .line 103
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->invokingState:I

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->invokingState:I

    .line 105
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 106
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 109
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    if-eqz v0, :cond_1

    .line 110
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    .line 112
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    .line 113
    instance-of v1, v0, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNodeImpl;

    if-eqz v1, :cond_0

    .line 114
    move-object v1, v0

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNodeImpl;

    invoke-virtual {v1, p0}, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNodeImpl;->setParent(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    .line 117
    :cond_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addAnyChild(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    goto :goto_0

    :cond_1
    return-void
.end method

.method public enterRule(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V
    .locals 0

    return-void
.end method

.method public exitRule(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V
    .locals 0

    return-void
.end method

.method public getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;
    .locals 1

    .line 216
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    if-eqz v0, :cond_0

    if-ltz p1, :cond_0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method public getChild(Ljava/lang/Class;I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T::",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">(",
            "Ljava/lang/Class<",
            "+TT;>;I)TT;"
        }
    .end annotation

    .line 220
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_2

    if-ltz p2, :cond_2

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lt p2, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, -0x1

    .line 225
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    .line 226
    invoke-virtual {p1, v3}, Ljava/lang/Class;->isInstance(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_1

    add-int/lit8 v0, v0, 0x1

    if-ne v0, p2, :cond_1

    .line 229
    invoke-virtual {p1, v3}, Ljava/lang/Class;->cast(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    return-object p1

    :cond_2
    :goto_0
    return-object v1
.end method

.method public bridge synthetic getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 0

    .line 43
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    move-result-object p1

    return-object p1
.end method

.method public getChildCount()I
    .locals 1

    .line 312
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public getParent()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
    .locals 1

    .line 211
    invoke-super {p0}, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->getParent()Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    return-object v0
.end method

.method public bridge synthetic getParent()Lgroovyjarjarantlr4/v4/runtime/RuleContext;
    .locals 1

    .line 43
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getParent()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getParent()Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;
    .locals 1

    .line 43
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getParent()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getParent()Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;
    .locals 1

    .line 43
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getParent()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 1

    .line 43
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getParent()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v0

    return-object v0
.end method

.method public getRuleContext(Ljava/lang/Class;I)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;",
            ">(",
            "Ljava/lang/Class<",
            "+TT;>;I)TT;"
        }
    .end annotation

    .line 285
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getChild(Ljava/lang/Class;I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    return-object p1
.end method

.method public getRuleContexts(Ljava/lang/Class;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;",
            ">(",
            "Ljava/lang/Class<",
            "+TT;>;)",
            "Ljava/util/List<",
            "+TT;>;"
        }
    .end annotation

    .line 289
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    if-nez v0, :cond_0

    .line 290
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 v1, 0x0

    .line 294
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    .line 295
    invoke-virtual {p1, v2}, Ljava/lang/Class;->isInstance(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    if-nez v1, :cond_2

    .line 297
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 300
    :cond_2
    invoke-virtual {p1, v2}, Ljava/lang/Class;->cast(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_3
    if-nez v1, :cond_4

    .line 305
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    return-object p1

    :cond_4
    return-object v1
.end method

.method public getSourceInterval()Lgroovyjarjarantlr4/v4/runtime/misc/Interval;
    .locals 2

    .line 316
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    if-nez v0, :cond_0

    .line 317
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->INVALID:Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    return-object v0

    .line 319
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_2

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result v1

    if-ge v0, v1, :cond_1

    goto :goto_0

    .line 322
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v0

    return-object v0

    .line 320
    :cond_2
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v0

    return-object v0
.end method

.method public getStart()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 1

    .line 330
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object v0
.end method

.method public getStop()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 1

    .line 336
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object v0
.end method

.method public getToken(II)Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;
    .locals 5

    .line 237
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_2

    if-ltz p2, :cond_2

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lt p2, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, -0x1

    .line 242
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    .line 243
    instance-of v4, v3, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;

    if-eqz v4, :cond_1

    .line 244
    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;

    .line 245
    invoke-interface {v3}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;->getSymbol()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v4

    .line 246
    invoke-interface {v4}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v4

    if-ne v4, p1, :cond_1

    add-int/lit8 v0, v0, 0x1

    if-ne v0, p2, :cond_1

    return-object v3

    :cond_2
    :goto_0
    return-object v1
.end method

.method public getTokens(I)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;",
            ">;"
        }
    .end annotation

    .line 259
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    if-nez v0, :cond_0

    .line 260
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 v1, 0x0

    .line 264
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    .line 265
    instance-of v3, v2, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;

    if-eqz v3, :cond_1

    .line 266
    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;

    .line 267
    invoke-interface {v2}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;->getSymbol()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v3

    .line 268
    invoke-interface {v3}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v3

    if-ne v3, p1, :cond_1

    if-nez v1, :cond_2

    .line 270
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 272
    :cond_2
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_3
    if-nez v1, :cond_4

    .line 278
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    return-object p1

    :cond_4
    return-object v1
.end method

.method public removeLastChild()V
    .locals 2

    .line 203
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    if-eqz v0, :cond_0

    .line 204
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-interface {v0, v1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    :cond_0
    return-void
.end method

.method public toInfoString(Lgroovyjarjarantlr4/v4/runtime/Parser;)Ljava/lang/String;
    .locals 2

    .line 340
    invoke-virtual {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleInvocationStack(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Ljava/util/List;

    move-result-object p1

    .line 341
    invoke-static {p1}, Ljava/util/Collections;->reverse(Ljava/util/List;)V

    .line 342
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ParserRuleContext"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "{"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "start="

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", stop="

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x7d

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
