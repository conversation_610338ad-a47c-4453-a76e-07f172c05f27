.class public Lgroovyjarjarantlr4/v4/runtime/ProxyErrorListener;
.super Ljava/lang/Object;
.source "ProxyErrorListener.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<Symbol:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
        "TSymbol;>;"
    }
.end annotation


# instance fields
.field private final delegates:Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Collection<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
            "-TSymbol;>;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/util/Collection;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
            "-TSymbol;>;>;)V"
        }
    .end annotation

    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "delegates"

    .line 25
    invoke-static {p1, v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 28
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ProxyErrorListener;->delegates:Ljava/util/Collection;

    return-void
.end method


# virtual methods
.method protected getDelegates()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
            "-TSymbol;>;>;"
        }
    .end annotation

    .line 32
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ProxyErrorListener;->delegates:Ljava/util/Collection;

    return-object v0
.end method

.method public syntaxError(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Ljava/lang/Object;IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:TSymbol;>(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "TT;*>;TT;II",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/RecognitionException;",
            ")V"
        }
    .end annotation

    .line 43
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ProxyErrorListener;->delegates:Ljava/util/Collection;

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;

    move-object v3, p1

    move-object v4, p2

    move v5, p3

    move v6, p4

    move-object v7, p5

    move-object v8, p6

    .line 44
    invoke-interface/range {v2 .. v8}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;->syntaxError(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Ljava/lang/Object;IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    goto :goto_0

    :cond_0
    return-void
.end method
