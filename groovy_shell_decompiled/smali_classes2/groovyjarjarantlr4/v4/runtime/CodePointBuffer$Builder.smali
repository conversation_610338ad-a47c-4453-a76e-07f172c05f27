.class public Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;
.super Ljava/lang/Object;
.source "CodePointBuffer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Builder"
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field private byteBuffer:Ljava/nio/ByteBuffer;

.field private charBuffer:Ljava/nio/CharBuffer;

.field private intBuffer:Ljava/nio/IntBuffer;

.field private prevHighSurrogate:I

.field private type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 133
    const-class v0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;

    return-void
.end method

.method private constructor <init>(I)V
    .locals 1

    .line 140
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 141
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->BYTE:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    .line 142
    invoke-static {p1}, Ljava/nio/ByteBuffer;->allocate(I)Ljava/nio/ByteBuffer;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    const/4 p1, 0x0

    .line 143
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    .line 144
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    const/4 p1, -0x1

    .line 145
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->prevHighSurrogate:I

    return-void
.end method

.method synthetic constructor <init>(ILgroovyjarjarantlr4/v4/runtime/CodePointBuffer$1;)V
    .locals 0

    .line 133
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;-><init>(I)V

    return-void
.end method

.method private appendArray(Ljava/nio/CharBuffer;)V
    .locals 2

    .line 229
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$1;->$SwitchMap$org$antlr$v4$runtime$CodePointBuffer$Type:[I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->ordinal()I

    move-result v1

    aget v0, v0, v1

    const/4 v1, 0x1

    if-eq v0, v1, :cond_2

    const/4 v1, 0x2

    if-eq v0, v1, :cond_1

    const/4 v1, 0x3

    if-eq v0, v1, :cond_0

    goto :goto_0

    .line 237
    :cond_0
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->appendArrayInt(Ljava/nio/CharBuffer;)V

    goto :goto_0

    .line 234
    :cond_1
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->appendArrayChar(Ljava/nio/CharBuffer;)V

    goto :goto_0

    .line 231
    :cond_2
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->appendArrayByte(Ljava/nio/CharBuffer;)V

    :goto_0
    return-void
.end method

.method private appendArrayByte(Ljava/nio/CharBuffer;)V
    .locals 7

    .line 245
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->array()[C

    move-result-object v0

    .line 246
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v1

    invoke-virtual {p1}, Ljava/nio/CharBuffer;->position()I

    move-result v2

    add-int/2addr v1, v2

    .line 247
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v2

    invoke-virtual {p1}, Ljava/nio/CharBuffer;->limit()I

    move-result v3

    add-int/2addr v2, v3

    .line 249
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v3}, Ljava/nio/ByteBuffer;->array()[B

    move-result-object v3

    .line 250
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v4}, Ljava/nio/ByteBuffer;->arrayOffset()I

    move-result v4

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v5}, Ljava/nio/ByteBuffer;->position()I

    move-result v5

    add-int/2addr v4, v5

    :goto_0
    if-ge v1, v2, :cond_2

    .line 253
    aget-char v5, v0, v1

    const/16 v6, 0xff

    if-gt v5, v6, :cond_0

    and-int/lit16 v5, v5, 0xff

    int-to-byte v5, v5

    .line 255
    aput-byte v5, v3, v4

    add-int/lit8 v1, v1, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 257
    :cond_0
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-virtual {p1, v1}, Ljava/nio/CharBuffer;->position(I)Ljava/nio/Buffer;

    .line 258
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->arrayOffset()I

    move-result v1

    sub-int/2addr v4, v1

    invoke-virtual {v0, v4}, Ljava/nio/ByteBuffer;->position(I)Ljava/nio/Buffer;

    .line 259
    invoke-static {v5}, Ljava/lang/Character;->isHighSurrogate(C)Z

    move-result v0

    if-nez v0, :cond_1

    .line 260
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->remaining()I

    move-result v0

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteToCharBuffer(I)V

    .line 261
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->appendArrayChar(Ljava/nio/CharBuffer;)V

    return-void

    .line 264
    :cond_1
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->remaining()I

    move-result v0

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteToIntBuffer(I)V

    .line 265
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->appendArrayInt(Ljava/nio/CharBuffer;)V

    return-void

    .line 273
    :cond_2
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-virtual {p1, v1}, Ljava/nio/CharBuffer;->position(I)Ljava/nio/Buffer;

    .line 274
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {p1}, Ljava/nio/ByteBuffer;->arrayOffset()I

    move-result v0

    sub-int/2addr v4, v0

    invoke-virtual {p1, v4}, Ljava/nio/ByteBuffer;->position(I)Ljava/nio/Buffer;

    return-void
.end method

.method private appendArrayChar(Ljava/nio/CharBuffer;)V
    .locals 7

    .line 280
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->array()[C

    move-result-object v0

    .line 281
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v1

    invoke-virtual {p1}, Ljava/nio/CharBuffer;->position()I

    move-result v2

    add-int/2addr v1, v2

    .line 282
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v2

    invoke-virtual {p1}, Ljava/nio/CharBuffer;->limit()I

    move-result v3

    add-int/2addr v2, v3

    .line 284
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v3}, Ljava/nio/CharBuffer;->array()[C

    move-result-object v3

    .line 285
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v4}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v4

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v5}, Ljava/nio/CharBuffer;->position()I

    move-result v5

    add-int/2addr v4, v5

    :goto_0
    if-ge v1, v2, :cond_1

    .line 288
    aget-char v5, v0, v1

    .line 289
    invoke-static {v5}, Ljava/lang/Character;->isHighSurrogate(C)Z

    move-result v6

    if-nez v6, :cond_0

    .line 290
    aput-char v5, v3, v4

    add-int/lit8 v1, v1, 0x1

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 292
    :cond_0
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-virtual {p1, v1}, Ljava/nio/CharBuffer;->position(I)Ljava/nio/Buffer;

    .line 293
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v0}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v1

    sub-int/2addr v4, v1

    invoke-virtual {v0, v4}, Ljava/nio/CharBuffer;->position(I)Ljava/nio/Buffer;

    .line 294
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->remaining()I

    move-result v0

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charToIntBuffer(I)V

    .line 295
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->appendArrayInt(Ljava/nio/CharBuffer;)V

    return-void

    .line 302
    :cond_1
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-virtual {p1, v1}, Ljava/nio/CharBuffer;->position(I)Ljava/nio/Buffer;

    .line 303
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v0

    sub-int/2addr v4, v0

    invoke-virtual {p1, v4}, Ljava/nio/CharBuffer;->position(I)Ljava/nio/Buffer;

    return-void
.end method

.method private appendArrayInt(Ljava/nio/CharBuffer;)V
    .locals 9

    .line 307
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->array()[C

    move-result-object v0

    .line 308
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v1

    invoke-virtual {p1}, Ljava/nio/CharBuffer;->position()I

    move-result v2

    add-int/2addr v1, v2

    .line 309
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v2

    invoke-virtual {p1}, Ljava/nio/CharBuffer;->limit()I

    move-result v3

    add-int/2addr v2, v3

    .line 311
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    invoke-virtual {v3}, Ljava/nio/IntBuffer;->array()[I

    move-result-object v3

    .line 312
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    invoke-virtual {v4}, Ljava/nio/IntBuffer;->arrayOffset()I

    move-result v4

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    invoke-virtual {v5}, Ljava/nio/IntBuffer;->position()I

    move-result v5

    add-int/2addr v4, v5

    :goto_0
    const/4 v5, -0x1

    const v6, 0xffff

    if-ge v1, v2, :cond_4

    .line 315
    aget-char v7, v0, v1

    add-int/lit8 v1, v1, 0x1

    .line 317
    iget v8, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->prevHighSurrogate:I

    if-eq v8, v5, :cond_2

    .line 318
    invoke-static {v7}, Ljava/lang/Character;->isLowSurrogate(C)Z

    move-result v8

    if-eqz v8, :cond_0

    .line 319
    iget v6, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->prevHighSurrogate:I

    int-to-char v6, v6

    invoke-static {v6, v7}, Ljava/lang/Character;->toCodePoint(CC)I

    move-result v6

    aput v6, v3, v4

    add-int/lit8 v4, v4, 0x1

    .line 321
    iput v5, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->prevHighSurrogate:I

    goto :goto_0

    .line 324
    :cond_0
    iget v8, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->prevHighSurrogate:I

    aput v8, v3, v4

    add-int/lit8 v4, v4, 0x1

    .line 326
    invoke-static {v7}, Ljava/lang/Character;->isHighSurrogate(C)Z

    move-result v8

    if-eqz v8, :cond_1

    and-int v5, v7, v6

    .line 327
    iput v5, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->prevHighSurrogate:I

    goto :goto_0

    :cond_1
    and-int/2addr v6, v7

    .line 329
    aput v6, v3, v4

    add-int/lit8 v4, v4, 0x1

    .line 331
    iput v5, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->prevHighSurrogate:I

    goto :goto_0

    .line 334
    :cond_2
    invoke-static {v7}, Ljava/lang/Character;->isHighSurrogate(C)Z

    move-result v5

    if-eqz v5, :cond_3

    and-int v5, v7, v6

    .line 335
    iput v5, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->prevHighSurrogate:I

    goto :goto_0

    :cond_3
    and-int v5, v7, v6

    .line 337
    aput v5, v3, v4

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 342
    :cond_4
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->prevHighSurrogate:I

    if-eq v0, v5, :cond_5

    and-int/2addr v0, v6

    .line 344
    aput v0, v3, v4

    add-int/lit8 v4, v4, 0x1

    .line 348
    :cond_5
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->arrayOffset()I

    move-result v0

    sub-int/2addr v1, v0

    invoke-virtual {p1, v1}, Ljava/nio/CharBuffer;->position(I)Ljava/nio/Buffer;

    .line 349
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    invoke-virtual {p1}, Ljava/nio/IntBuffer;->arrayOffset()I

    move-result v0

    sub-int/2addr v4, v0

    invoke-virtual {p1, v4}, Ljava/nio/IntBuffer;->position(I)Ljava/nio/Buffer;

    return-void
.end method

.method private byteToCharBuffer(I)V
    .locals 1

    .line 353
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->flip()Ljava/nio/Buffer;

    .line 355
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v0

    add-int/2addr v0, p1

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {p1}, Ljava/nio/ByteBuffer;->capacity()I

    move-result p1

    div-int/lit8 p1, p1, 0x2

    invoke-static {v0, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    invoke-static {p1}, Ljava/nio/CharBuffer;->allocate(I)Ljava/nio/CharBuffer;

    move-result-object p1

    .line 356
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->hasRemaining()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 357
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->get()B

    move-result v0

    and-int/lit16 v0, v0, 0xff

    int-to-char v0, v0

    invoke-virtual {p1, v0}, Ljava/nio/CharBuffer;->put(C)Ljava/nio/CharBuffer;

    goto :goto_0

    .line 359
    :cond_0
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->CHAR:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    const/4 v0, 0x0

    .line 360
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    .line 361
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    return-void
.end method

.method private byteToIntBuffer(I)V
    .locals 1

    .line 365
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->flip()Ljava/nio/Buffer;

    .line 367
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v0

    add-int/2addr v0, p1

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {p1}, Ljava/nio/ByteBuffer;->capacity()I

    move-result p1

    div-int/lit8 p1, p1, 0x4

    invoke-static {v0, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    invoke-static {p1}, Ljava/nio/IntBuffer;->allocate(I)Ljava/nio/IntBuffer;

    move-result-object p1

    .line 368
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->hasRemaining()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 369
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->get()B

    move-result v0

    and-int/lit16 v0, v0, 0xff

    invoke-virtual {p1, v0}, Ljava/nio/IntBuffer;->put(I)Ljava/nio/IntBuffer;

    goto :goto_0

    .line 371
    :cond_0
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->INT:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    const/4 v0, 0x0

    .line 372
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    .line 373
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    return-void
.end method

.method private charToIntBuffer(I)V
    .locals 2

    .line 377
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v0}, Ljava/nio/CharBuffer;->flip()Ljava/nio/Buffer;

    .line 379
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v0}, Ljava/nio/CharBuffer;->remaining()I

    move-result v0

    add-int/2addr v0, p1

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {p1}, Ljava/nio/CharBuffer;->capacity()I

    move-result p1

    div-int/lit8 p1, p1, 0x2

    invoke-static {v0, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    invoke-static {p1}, Ljava/nio/IntBuffer;->allocate(I)Ljava/nio/IntBuffer;

    move-result-object p1

    .line 380
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v0}, Ljava/nio/CharBuffer;->hasRemaining()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 381
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v0}, Ljava/nio/CharBuffer;->get()C

    move-result v0

    const v1, 0xffff

    and-int/2addr v0, v1

    invoke-virtual {p1, v0}, Ljava/nio/IntBuffer;->put(I)Ljava/nio/IntBuffer;

    goto :goto_0

    .line 383
    :cond_0
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->INT:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    const/4 v0, 0x0

    .line 384
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    .line 385
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    return-void
.end method

.method private static roundUpToNextPowerOfTwo(I)I
    .locals 4

    add-int/lit8 p0, p0, -0x1

    .line 180
    invoke-static {p0}, Ljava/lang/Integer;->numberOfLeadingZeros(I)I

    move-result p0

    rsub-int/lit8 p0, p0, 0x20

    int-to-double v0, p0

    const-wide/high16 v2, 0x4000000000000000L    # 2.0

    .line 181
    invoke-static {v2, v3, v0, v1}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v0

    double-to-int p0, v0

    return p0
.end method


# virtual methods
.method public append(Ljava/nio/CharBuffer;)V
    .locals 1

    .line 217
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->remaining()I

    move-result v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->ensureRemaining(I)V

    .line 218
    invoke-virtual {p1}, Ljava/nio/CharBuffer;->hasArray()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 219
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->appendArray(Ljava/nio/CharBuffer;)V

    return-void

    .line 222
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "TODO"

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public build()Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;
    .locals 7

    .line 165
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$1;->$SwitchMap$org$antlr$v4$runtime$CodePointBuffer$Type:[I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->ordinal()I

    move-result v1

    aget v0, v0, v1

    const/4 v1, 0x1

    if-eq v0, v1, :cond_2

    const/4 v1, 0x2

    if-eq v0, v1, :cond_1

    const/4 v1, 0x3

    if-eq v0, v1, :cond_0

    goto :goto_0

    .line 173
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    invoke-virtual {v0}, Ljava/nio/IntBuffer;->flip()Ljava/nio/Buffer;

    goto :goto_0

    .line 170
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v0}, Ljava/nio/CharBuffer;->flip()Ljava/nio/Buffer;

    goto :goto_0

    .line 167
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->flip()Ljava/nio/Buffer;

    .line 176
    :goto_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    iget-object v5, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    const/4 v6, 0x0

    move-object v1, v0

    invoke-direct/range {v1 .. v6}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;-><init>(Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;Ljava/nio/ByteBuffer;Ljava/nio/CharBuffer;Ljava/nio/IntBuffer;Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$1;)V

    return-object v0
.end method

.method public ensureRemaining(I)V
    .locals 2

    .line 185
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$1;->$SwitchMap$org$antlr$v4$runtime$CodePointBuffer$Type:[I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->ordinal()I

    move-result v1

    aget v0, v0, v1

    const/4 v1, 0x1

    if-eq v0, v1, :cond_2

    const/4 v1, 0x2

    if-eq v0, v1, :cond_1

    const/4 v1, 0x3

    if-eq v0, v1, :cond_0

    goto :goto_0

    .line 205
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    invoke-virtual {v0}, Ljava/nio/IntBuffer;->remaining()I

    move-result v0

    if-ge v0, p1, :cond_3

    .line 206
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    invoke-virtual {v0}, Ljava/nio/IntBuffer;->capacity()I

    move-result v0

    add-int/2addr v0, p1

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->roundUpToNextPowerOfTwo(I)I

    move-result p1

    .line 207
    invoke-static {p1}, Ljava/nio/IntBuffer;->allocate(I)Ljava/nio/IntBuffer;

    move-result-object p1

    .line 208
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    invoke-virtual {v0}, Ljava/nio/IntBuffer;->flip()Ljava/nio/Buffer;

    .line 209
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    invoke-virtual {p1, v0}, Ljava/nio/IntBuffer;->put(Ljava/nio/IntBuffer;)Ljava/nio/IntBuffer;

    .line 210
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    goto :goto_0

    .line 196
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v0}, Ljava/nio/CharBuffer;->remaining()I

    move-result v0

    if-ge v0, p1, :cond_3

    .line 197
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v0}, Ljava/nio/CharBuffer;->capacity()I

    move-result v0

    add-int/2addr v0, p1

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->roundUpToNextPowerOfTwo(I)I

    move-result p1

    .line 198
    invoke-static {p1}, Ljava/nio/CharBuffer;->allocate(I)Ljava/nio/CharBuffer;

    move-result-object p1

    .line 199
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {v0}, Ljava/nio/CharBuffer;->flip()Ljava/nio/Buffer;

    .line 200
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    invoke-virtual {p1, v0}, Ljava/nio/CharBuffer;->put(Ljava/nio/CharBuffer;)Ljava/nio/CharBuffer;

    .line 201
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    goto :goto_0

    .line 187
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v0

    if-ge v0, p1, :cond_3

    .line 188
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->capacity()I

    move-result v0

    add-int/2addr v0, p1

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->roundUpToNextPowerOfTwo(I)I

    move-result p1

    .line 189
    invoke-static {p1}, Ljava/nio/ByteBuffer;->allocate(I)Ljava/nio/ByteBuffer;

    move-result-object p1

    .line 190
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->flip()Ljava/nio/Buffer;

    .line 191
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    invoke-virtual {p1, v0}, Ljava/nio/ByteBuffer;->put(Ljava/nio/ByteBuffer;)Ljava/nio/ByteBuffer;

    .line 192
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    :cond_3
    :goto_0
    return-void
.end method

.method getByteBuffer()Ljava/nio/ByteBuffer;
    .locals 1

    .line 153
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->byteBuffer:Ljava/nio/ByteBuffer;

    return-object v0
.end method

.method getCharBuffer()Ljava/nio/CharBuffer;
    .locals 1

    .line 157
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->charBuffer:Ljava/nio/CharBuffer;

    return-object v0
.end method

.method getIntBuffer()Ljava/nio/IntBuffer;
    .locals 1

    .line 161
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->intBuffer:Ljava/nio/IntBuffer;

    return-object v0
.end method

.method getType()Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;
    .locals 1

    .line 149
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->type:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    return-object v0
.end method
