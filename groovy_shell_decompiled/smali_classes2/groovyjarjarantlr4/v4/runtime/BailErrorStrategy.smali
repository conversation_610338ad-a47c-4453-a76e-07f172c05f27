.class public Lgroovyjarjarantlr4/v4/runtime/BailErrorStrategy;
.super Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;
.source "BailErrorStrategy.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 39
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;-><init>()V

    return-void
.end method


# virtual methods
.method public recover(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 0

    .line 47
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getContext()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object p1

    :goto_0
    if-eqz p1, :cond_0

    .line 48
    iput-object p2, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->exception:Lgroovyjarjarantlr4/v4/runtime/RecognitionException;

    .line 47
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getParent()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object p1

    goto :goto_0

    .line 51
    :cond_0
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/misc/ParseCancellationException;

    invoke-direct {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/misc/ParseCancellationException;-><init>(Ljava/lang/Throwable;)V

    throw p1
.end method

.method public recoverInline(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
        }
    .end annotation

    .line 61
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 62
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getContext()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object p1

    :goto_0
    if-eqz p1, :cond_0

    .line 63
    iput-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->exception:Lgroovyjarjarantlr4/v4/runtime/RecognitionException;

    .line 62
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getParent()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object p1

    goto :goto_0

    .line 66
    :cond_0
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/misc/ParseCancellationException;

    invoke-direct {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/ParseCancellationException;-><init>(Ljava/lang/Throwable;)V

    throw p1
.end method

.method public sync(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 0

    return-void
.end method
