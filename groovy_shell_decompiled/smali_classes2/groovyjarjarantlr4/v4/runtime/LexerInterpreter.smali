.class public Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;
.super Lgroovyjarjarantlr4/v4/runtime/Lexer;
.source "LexerInterpreter.java"


# instance fields
.field protected final atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

.field protected final channelNames:[Ljava/lang/String;

.field protected final grammarFileName:Ljava/lang/String;

.field protected final modeNames:[Ljava/lang/String;

.field protected final ruleNames:[Ljava/lang/String;

.field protected final tokenNames:[Ljava/lang/String;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field private final vocabulary:Lgroovyjarjarantlr4/v4/runtime/Vocabulary;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/Vocabulary;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Lgroovyjarjarantlr4/v4/runtime/CharStream;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    const/4 v4, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v5, p4

    move-object v6, p5

    move-object v7, p6

    .line 37
    invoke-direct/range {v0 .. v7}, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;-><init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/Vocabulary;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Lgroovyjarjarantlr4/v4/runtime/CharStream;",
            ")V"
        }
    .end annotation

    .line 41
    invoke-direct {p0, p7}, Lgroovyjarjarantlr4/v4/runtime/Lexer;-><init>(Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    .line 43
    iget-object p7, p6, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;->LEXER:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    if-ne p7, v0, :cond_2

    .line 47
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->grammarFileName:Ljava/lang/String;

    .line 48
    iput-object p6, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 49
    iget p1, p6, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->maxTokenType:I

    new-array p1, p1, [Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->tokenNames:[Ljava/lang/String;

    const/4 p1, 0x0

    .line 50
    :goto_0
    iget-object p7, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->tokenNames:[Ljava/lang/String;

    array-length v0, p7

    if-ge p1, v0, :cond_0

    .line 51
    invoke-interface {p2, p1}, Lgroovyjarjarantlr4/v4/runtime/Vocabulary;->getDisplayName(I)Ljava/lang/String;

    move-result-object v0

    aput-object v0, p7, p1

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 54
    :cond_0
    invoke-interface {p3}, Ljava/util/Collection;->size()I

    move-result p1

    new-array p1, p1, [Ljava/lang/String;

    invoke-interface {p3, p1}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->ruleNames:[Ljava/lang/String;

    if-eqz p4, :cond_1

    .line 55
    invoke-interface {p4}, Ljava/util/Collection;->size()I

    move-result p1

    new-array p1, p1, [Ljava/lang/String;

    invoke-interface {p4, p1}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/String;

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->channelNames:[Ljava/lang/String;

    .line 56
    invoke-interface {p5}, Ljava/util/Collection;->size()I

    move-result p1

    new-array p1, p1, [Ljava/lang/String;

    invoke-interface {p5, p1}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->modeNames:[Ljava/lang/String;

    .line 57
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->vocabulary:Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    .line 58
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;

    invoke-direct {p1, p0, p6}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;-><init>(Lgroovyjarjarantlr4/v4/runtime/Lexer;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->_interp:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    return-void

    .line 44
    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "The ATN must be a lexer ATN."

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Lgroovyjarjarantlr4/v4/runtime/CharStream;",
            ")V"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 32
    invoke-interface {p2}, Ljava/util/Collection;->size()I

    move-result v0

    new-array v0, v0, [Ljava/lang/String;

    invoke-interface {p2, v0}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/String;

    invoke-static {p2}, Lgroovyjarjarantlr4/v4/runtime/VocabularyImpl;->fromTokenNames([Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v2

    const/4 v4, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v3, p3

    move-object v5, p4

    move-object v6, p5

    move-object v7, p6

    invoke-direct/range {v0 .. v7}, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;-><init>(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;Ljava/util/Collection;Ljava/util/Collection;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    return-void
.end method


# virtual methods
.method public getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;
    .locals 1

    .line 63
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    return-object v0
.end method

.method public getChannelNames()[Ljava/lang/String;
    .locals 1

    .line 84
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->channelNames:[Ljava/lang/String;

    return-object v0
.end method

.method public getGrammarFileName()Ljava/lang/String;
    .locals 1

    .line 68
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->grammarFileName:Ljava/lang/String;

    return-object v0
.end method

.method public getModeNames()[Ljava/lang/String;
    .locals 1

    .line 89
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->modeNames:[Ljava/lang/String;

    return-object v0
.end method

.method public getRuleNames()[Ljava/lang/String;
    .locals 1

    .line 79
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->ruleNames:[Ljava/lang/String;

    return-object v0
.end method

.method public getTokenNames()[Ljava/lang/String;
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 74
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->tokenNames:[Ljava/lang/String;

    return-object v0
.end method

.method public getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;
    .locals 1

    .line 94
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/LexerInterpreter;->vocabulary:Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    if-eqz v0, :cond_0

    return-object v0

    .line 98
    :cond_0
    invoke-super {p0}, Lgroovyjarjarantlr4/v4/runtime/Lexer;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v0

    return-object v0
.end method
