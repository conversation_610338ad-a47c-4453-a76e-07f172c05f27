.class public abstract Lgroovyjarjarantlr4/v4/runtime/misc/AbstractEqualityComparator;
.super Ljava/lang/Object;
.source "AbstractEqualityComparator.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/misc/EqualityComparator;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lgroovyjarjarantlr4/v4/runtime/misc/EqualityComparator<",
        "TT;>;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 14
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
