.class public Lgroovyjarjarantlr4/v4/runtime/misc/ParseCancellationException;
.super Ljava/util/concurrent/CancellationException;
.source "ParseCancellationException.java"


# static fields
.field private static final serialVersionUID:J = -0x30fb7cef1b00c863L


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 25
    invoke-direct {p0}, Ljava/util/concurrent/CancellationException;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 29
    invoke-direct {p0, p1}, Ljava/util/concurrent/CancellationException;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 0

    .line 37
    invoke-direct {p0, p1}, Ljava/util/concurrent/CancellationException;-><init>(Ljava/lang/String;)V

    .line 38
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/misc/ParseCancellationException;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Throwable;)V
    .locals 0

    .line 32
    invoke-direct {p0}, Ljava/util/concurrent/CancellationException;-><init>()V

    .line 33
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/ParseCancellationException;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    return-void
.end method
