.class public Lgroovyjarjarantlr4/v4/runtime/misc/Interval;
.super Ljava/lang/Object;
.source "Interval.java"


# static fields
.field public static final INTERVAL_POOL_MAX_VALUE:I = 0x3e8

.field public static final INVALID:Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

.field private static cache:[Lgroovyjarjarantlr4/v4/runtime/misc/Interval;


# instance fields
.field public final a:I

.field public final b:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 12
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    const/4 v1, -0x1

    const/4 v2, -0x2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;-><init>(II)V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->INVALID:Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    const/16 v0, 0x3e9

    new-array v0, v0, [Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    .line 14
    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->cache:[Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    return-void
.end method

.method public constructor <init>(II)V
    .locals 0

    .line 21
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    return-void
.end method

.method public static of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;
    .locals 1

    if-ne p0, p1, :cond_2

    if-ltz p0, :cond_2

    const/16 v0, 0x3e8

    if-le p0, v0, :cond_0

    goto :goto_0

    .line 34
    :cond_0
    sget-object p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->cache:[Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    aget-object v0, p1, p0

    if-nez v0, :cond_1

    .line 35
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    invoke-direct {v0, p0, p0}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;-><init>(II)V

    aput-object v0, p1, p0

    .line 37
    :cond_1
    sget-object p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->cache:[Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    aget-object p0, p1, p0

    return-object p0

    .line 32
    :cond_2
    :goto_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    invoke-direct {v0, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;-><init>(II)V

    return-object v0
.end method


# virtual methods
.method public adjacent(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z
    .locals 3

    .line 99
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    const/4 v2, 0x1

    add-int/2addr v1, v2

    if-eq v0, v1, :cond_1

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    sub-int/2addr p1, v2

    if-ne v0, p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :cond_1
    :goto_0
    return v2
.end method

.method public differenceNotProperlyContained(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;
    .locals 1

    .line 124
    invoke-virtual {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->startsBeforeNonDisjoint(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 125
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    add-int/lit8 p1, p1, 0x1

    invoke-static {v0, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    invoke-static {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object p1

    goto :goto_0

    .line 130
    :cond_0
    invoke-virtual {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->startsAfterNonDisjoint(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 131
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    add-int/lit8 p1, p1, -0x1

    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object p1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method public disjoint(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z
    .locals 1

    .line 94
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->startsBeforeDisjoint(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->startsAfterDisjoint(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 53
    :cond_0
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 57
    :cond_1
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    .line 58
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget v3, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-ne v1, p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public hashCode()I
    .locals 2

    .line 64
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    const/16 v1, 0x2c9

    add-int/2addr v1, v0

    mul-int/lit8 v1, v1, 0x1f

    .line 65
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    add-int/2addr v1, v0

    return v1
.end method

.method public intersection(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;
    .locals 2

    .line 113
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    invoke-static {v1, p1}, Ljava/lang/Math;->min(II)I

    move-result p1

    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object p1

    return-object p1
.end method

.method public length()I
    .locals 2

    .line 44
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-ge v0, v1, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    sub-int/2addr v0, v1

    add-int/lit8 v0, v0, 0x1

    return v0
.end method

.method public properlyContains(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z
    .locals 2

    .line 103
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-lt v0, v1, :cond_0

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-gt p1, v0, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public startsAfter(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z
    .locals 1

    .line 80
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-le v0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public startsAfterDisjoint(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z
    .locals 1

    .line 84
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-le v0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public startsAfterNonDisjoint(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z
    .locals 2

    .line 89
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-le v0, v1, :cond_0

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-gt v0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public startsBeforeDisjoint(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z
    .locals 1

    .line 71
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-ge v0, p1, :cond_0

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-ge v0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public startsBeforeNonDisjoint(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Z
    .locals 1

    .line 76
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-gt v0, p1, :cond_0

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-lt v0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 138
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public union(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;
    .locals 2

    .line 108
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    invoke-static {v0, v1}, Ljava/lang/Math;->min(II)I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    invoke-static {v1, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object p1

    return-object p1
.end method
