.class public Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;
.super Ljava/util/LinkedHashSet;
.source "OrderedHashSet.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/util/LinkedHashSet<",
        "TT;>;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x494d3c73453bb6c9L


# instance fields
.field protected elements:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "TT;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 19
    invoke-direct {p0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 23
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    return-void
.end method


# virtual methods
.method public add(Ljava/lang/Object;)Z
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)Z"
        }
    .end annotation

    .line 51
    invoke-super {p0, p1}, Ljava/util/LinkedHashSet;->add(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 53
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_0
    return v0
.end method

.method public clear()V
    .locals 1

    .line 65
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 66
    invoke-super {p0}, Ljava/util/LinkedHashSet;->clear()V

    return-void
.end method

.method public clone()Ljava/lang/Object;
    .locals 3

    .line 101
    invoke-super {p0}, Ljava/util/LinkedHashSet;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    .line 102
    new-instance v1, Ljava/util/ArrayList;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    return-object v0
.end method

.method public elements()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "TT;>;"
        }
    .end annotation

    .line 95
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 2

    .line 76
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 81
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    if-eqz v0, :cond_1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    const/4 v1, 0x1

    :cond_1
    return v1
.end method

.method public get(I)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)TT;"
        }
    .end annotation

    .line 26
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public hashCode()I
    .locals 1

    .line 71
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->hashCode()I

    move-result v0

    return v0
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "TT;>;"
        }
    .end annotation

    .line 88
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    return-object v0
.end method

.method public remove(I)Z
    .locals 1

    .line 41
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    move-result-object p1

    .line 42
    invoke-super {p0, p1}, Ljava/util/LinkedHashSet;->remove(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public remove(Ljava/lang/Object;)Z
    .locals 0

    .line 60
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public set(ILjava/lang/Object;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ITT;)TT;"
        }
    .end annotation

    .line 33
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v0

    .line 34
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v1, p1, p2}, Ljava/util/ArrayList;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 35
    invoke-super {p0, v0}, Ljava/util/LinkedHashSet;->remove(Ljava/lang/Object;)Z

    .line 36
    invoke-super {p0, p2}, Ljava/util/LinkedHashSet;->add(Ljava/lang/Object;)Z

    return-object v0
.end method

.method public toArray()[Ljava/lang/Object;
    .locals 1

    .line 108
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->toArray()[Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 113
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/OrderedHashSet;->elements:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
