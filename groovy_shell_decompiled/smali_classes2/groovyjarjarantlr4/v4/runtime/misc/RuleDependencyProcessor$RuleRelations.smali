.class final Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;
.super Ljava/lang/Object;
.source "RuleDependencyProcessor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "RuleRelations"
.end annotation


# instance fields
.field private final children:[Ljava/util/BitSet;

.field private final parents:[Ljava/util/BitSet;


# direct methods
.method public constructor <init>(I)V
    .locals 4

    .line 655
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 656
    new-array v0, p1, [Ljava/util/BitSet;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->parents:[Ljava/util/BitSet;

    const/4 v0, 0x0

    move v1, v0

    :goto_0
    if-ge v1, p1, :cond_0

    .line 658
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->parents:[Ljava/util/BitSet;

    new-instance v3, Ljava/util/BitSet;

    invoke-direct {v3}, Ljava/util/BitSet;-><init>()V

    aput-object v3, v2, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 661
    :cond_0
    new-array v1, p1, [Ljava/util/BitSet;

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->children:[Ljava/util/BitSet;

    :goto_1
    if-ge v0, p1, :cond_1

    .line 663
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->children:[Ljava/util/BitSet;

    new-instance v2, Ljava/util/BitSet;

    invoke-direct {v2}, Ljava/util/BitSet;-><init>()V

    aput-object v2, v1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_1
    return-void
.end method

.method static synthetic access$000(Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;)[Ljava/util/BitSet;
    .locals 0

    .line 651
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->parents:[Ljava/util/BitSet;

    return-object p0
.end method

.method static synthetic access$100(Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;)[Ljava/util/BitSet;
    .locals 0

    .line 651
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->children:[Ljava/util/BitSet;

    return-object p0
.end method


# virtual methods
.method public addRuleInvocation(II)Z
    .locals 2

    const/4 v0, 0x0

    if-gez p1, :cond_0

    return v0

    .line 673
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->children:[Ljava/util/BitSet;

    aget-object v1, v1, p1

    invoke-virtual {v1, p2}, Ljava/util/BitSet;->get(I)Z

    move-result v1

    if-eqz v1, :cond_1

    return v0

    .line 678
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->children:[Ljava/util/BitSet;

    aget-object v0, v0, p1

    invoke-virtual {v0, p2}, Ljava/util/BitSet;->set(I)V

    .line 679
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->parents:[Ljava/util/BitSet;

    aget-object p2, v0, p2

    invoke-virtual {p2, p1}, Ljava/util/BitSet;->set(I)V

    const/4 p1, 0x1

    return p1
.end method

.method public getAncestors(I)Ljava/util/BitSet;
    .locals 3

    .line 684
    new-instance v0, Ljava/util/BitSet;

    invoke-direct {v0}, Ljava/util/BitSet;-><init>()V

    .line 685
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->parents:[Ljava/util/BitSet;

    aget-object p1, v1, p1

    invoke-virtual {v0, p1}, Ljava/util/BitSet;->or(Ljava/util/BitSet;)V

    .line 687
    :cond_0
    invoke-virtual {v0}, Ljava/util/BitSet;->cardinality()I

    move-result p1

    const/4 v1, 0x0

    .line 688
    :goto_0
    invoke-virtual {v0, v1}, Ljava/util/BitSet;->nextSetBit(I)I

    move-result v1

    if-ltz v1, :cond_1

    .line 689
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->parents:[Ljava/util/BitSet;

    aget-object v2, v2, v1

    invoke-virtual {v0, v2}, Ljava/util/BitSet;->or(Ljava/util/BitSet;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 692
    :cond_1
    invoke-virtual {v0}, Ljava/util/BitSet;->cardinality()I

    move-result v1

    if-ne v1, p1, :cond_0

    return-object v0
.end method

.method public getDescendants(I)Ljava/util/BitSet;
    .locals 3

    .line 702
    new-instance v0, Ljava/util/BitSet;

    invoke-direct {v0}, Ljava/util/BitSet;-><init>()V

    .line 703
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->children:[Ljava/util/BitSet;

    aget-object p1, v1, p1

    invoke-virtual {v0, p1}, Ljava/util/BitSet;->or(Ljava/util/BitSet;)V

    .line 705
    :cond_0
    invoke-virtual {v0}, Ljava/util/BitSet;->cardinality()I

    move-result p1

    const/4 v1, 0x0

    .line 706
    :goto_0
    invoke-virtual {v0, v1}, Ljava/util/BitSet;->nextSetBit(I)I

    move-result v1

    if-ltz v1, :cond_1

    .line 707
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/misc/RuleDependencyProcessor$RuleRelations;->children:[Ljava/util/BitSet;

    aget-object v2, v2, v1

    invoke-virtual {v0, v2}, Ljava/util/BitSet;->or(Ljava/util/BitSet;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 710
    :cond_1
    invoke-virtual {v0}, Ljava/util/BitSet;->cardinality()I

    move-result v1

    if-ne v1, p1, :cond_0

    return-object v0
.end method
