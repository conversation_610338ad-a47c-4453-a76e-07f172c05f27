.class public Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;
.super Ljava/lang/Object;
.source "Tuple2.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T1:",
        "Ljava/lang/Object;",
        "T2:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field private final item1:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT1;"
        }
    .end annotation
.end field

.field private final item2:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT2;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT1;TT2;)V"
        }
    .end annotation

    .line 18
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 19
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item1:Ljava/lang/Object;

    .line 20
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item2:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 36
    :cond_0
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 40
    :cond_1
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    .line 41
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item1:Ljava/lang/Object;

    iget-object v3, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item1:Ljava/lang/Object;

    invoke-static {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item2:Ljava/lang/Object;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item2:Ljava/lang/Object;

    invoke-static {v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public final getItem1()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT1;"
        }
    .end annotation

    .line 24
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item1:Ljava/lang/Object;

    return-object v0
.end method

.method public final getItem2()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT2;"
        }
    .end annotation

    .line 28
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item2:Ljava/lang/Object;

    return-object v0
.end method

.method public hashCode()I
    .locals 3

    .line 48
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item1:Ljava/lang/Object;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    goto :goto_0

    :cond_0
    move v0, v1

    :goto_0
    const/16 v2, 0x18b

    add-int/2addr v2, v0

    mul-int/lit8 v2, v2, 0x4f

    .line 49
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item2:Ljava/lang/Object;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v1

    :cond_1
    add-int/2addr v2, v1

    return v2
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Object;

    .line 55
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item1:Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;->item2:Ljava/lang/Object;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    const-string v1, "(%s, %s)"

    invoke-static {v1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
