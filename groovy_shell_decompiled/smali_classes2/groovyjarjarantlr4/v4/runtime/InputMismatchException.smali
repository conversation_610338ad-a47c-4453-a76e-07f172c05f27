.class public Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;
.super Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
.source "InputMismatchException.java"


# static fields
.field private static final serialVersionUID:J = 0x1544c6c8beaaed7bL


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/Parser;)V
    .locals 2

    .line 17
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-direct {p0, p1, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/IntStream;Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V

    .line 18
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;->setOffendingToken(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/Token;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/Parser;ILgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V
    .locals 1

    .line 22
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    invoke-direct {p0, p1, v0, p3}, Lgroovyjarjarantlr4/v4/runtime/RecognitionException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/IntStream;Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V

    .line 23
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;->setOffendingState(I)V

    .line 24
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/InputMismatchException;->setOffendingToken(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/Token;)V

    return-void
.end method
