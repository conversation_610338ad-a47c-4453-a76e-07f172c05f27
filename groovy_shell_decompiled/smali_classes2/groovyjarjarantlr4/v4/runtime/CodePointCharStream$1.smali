.class synthetic Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;
.super Ljava/lang/Object;
.source "CodePointCharStream.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1008
    name = null
.end annotation


# static fields
.field static final synthetic $SwitchMap$org$antlr$v4$runtime$CodePointBuffer$Type:[I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 65
    invoke-static {}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->values()[Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    move-result-object v0

    array-length v0, v0

    new-array v0, v0, [I

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;->$SwitchMap$org$antlr$v4$runtime$CodePointBuffer$Type:[I

    :try_start_0
    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->BYTE:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->ordinal()I

    move-result v1

    const/4 v2, 0x1

    aput v2, v0, v1
    :try_end_0
    .catch Ljava/lang/NoSuchFieldError; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :try_start_1
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;->$SwitchMap$org$antlr$v4$runtime$CodePointBuffer$Type:[I

    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->CHAR:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->ordinal()I

    move-result v1

    const/4 v2, 0x2

    aput v2, v0, v1
    :try_end_1
    .catch Ljava/lang/NoSuchFieldError; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    :try_start_2
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;->$SwitchMap$org$antlr$v4$runtime$CodePointBuffer$Type:[I

    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->INT:Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->ordinal()I

    move-result v1

    const/4 v2, 0x3

    aput v2, v0, v1
    :try_end_2
    .catch Ljava/lang/NoSuchFieldError; {:try_start_2 .. :try_end_2} :catch_2

    :catch_2
    return-void
.end method
