.class public Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;
.super Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
.source "InterpreterRuleContext.java"


# instance fields
.field private final ruleIndex:I


# direct methods
.method private constructor <init>(I)V
    .locals 0

    .line 44
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;-><init>()V

    .line 45
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;->ruleIndex:I

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V
    .locals 0

    .line 40
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;I)V

    .line 41
    iput p3, p0, Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;->ruleIndex:I

    return-void
.end method


# virtual methods
.method public getRuleIndex()I
    .locals 1

    .line 50
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/InterpreterRuleContext;->ruleIndex:I

    return v0
.end method
