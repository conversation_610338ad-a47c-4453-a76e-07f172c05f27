.class public final Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;
.super Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
.source "SingletonEdgeMap.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;,
        Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntrySet;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
        "TT;>;"
    }
.end annotation


# instance fields
.field private final key:I

.field private final value:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(IIILjava/lang/Object;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IIITT;)V"
        }
    .end annotation

    .line 24
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;-><init>(II)V

    if-lt p3, p1, :cond_0

    if-gt p3, p2, :cond_0

    .line 26
    iput p3, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    .line 27
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    .line 29
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    const/4 p1, 0x0

    .line 30
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    :goto_0
    return-void
.end method

.method static synthetic access$200(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;)I
    .locals 0

    .line 18
    iget p0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    return p0
.end method

.method static synthetic access$300(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;)Ljava/lang/Object;
    .locals 0

    .line 18
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    return-object p0
.end method


# virtual methods
.method public clear()Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation

    .line 95
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    if-eqz v0, :cond_0

    .line 96
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->minIndex:I

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->maxIndex:I

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;-><init>(II)V

    return-object v0

    :cond_0
    return-object p0
.end method

.method public bridge synthetic clear()Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 1

    .line 18
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->clear()Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object v0

    return-object v0
.end method

.method public containsKey(I)Z
    .locals 1

    .line 54
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    if-ne p1, v0, :cond_0

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public entrySet()Ljava/util/Set;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/util/Map$Entry<",
            "Ljava/lang/Integer;",
            "TT;>;>;"
        }
    .end annotation

    .line 113
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntrySet;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntrySet;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$1;)V

    return-object v0
.end method

.method public get(I)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)TT;"
        }
    .end annotation

    .line 59
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    if-ne p1, v0, :cond_0

    .line 60
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public getKey()I
    .locals 1

    .line 35
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    return v0
.end method

.method public getValue()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    .line 39
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    return-object v0
.end method

.method public isEmpty()Z
    .locals 1

    .line 49
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ITT;)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation

    .line 68
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->minIndex:I

    if-lt p1, v0, :cond_4

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->maxIndex:I

    if-le p1, v0, :cond_0

    goto :goto_1

    .line 72
    :cond_0
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    if-eq p1, v0, :cond_3

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    if-eqz p2, :cond_2

    .line 75
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/HashEdgeMap;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->minIndex:I

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->maxIndex:I

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/HashEdgeMap;-><init>(II)V

    .line 76
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    invoke-virtual {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object v0

    .line 77
    invoke-virtual {v0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object p1

    return-object p1

    :cond_2
    return-object p0

    .line 73
    :cond_3
    :goto_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->minIndex:I

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->maxIndex:I

    invoke-direct {v0, v1, v2, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;-><init>(IIILjava/lang/Object;)V

    return-object v0

    :cond_4
    :goto_1
    return-object p0
.end method

.method public bridge synthetic put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 18
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation

    .line 86
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    if-ne p1, v0, :cond_0

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    if-eqz p1, :cond_0

    .line 87
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->minIndex:I

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->maxIndex:I

    invoke-direct {p1, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;-><init>(II)V

    return-object p1

    :cond_0
    return-object p0
.end method

.method public bridge synthetic remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 18
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public size()I
    .locals 1

    .line 44
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public toMap()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "TT;>;"
        }
    .end annotation

    .line 104
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 105
    invoke-static {}, Ljava/util/Collections;->emptyMap()Ljava/util/Map;

    move-result-object v0

    return-object v0

    .line 108
    :cond_0
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->key:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->value:Ljava/lang/Object;

    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method
