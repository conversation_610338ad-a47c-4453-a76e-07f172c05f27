.class public Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;
.super Ljava/lang/Object;
.source "DFA.java"


# static fields
.field private static final EMPTY_PRECEDENCE_EDGES:Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap<",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final atnStartState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

.field public final decision:I

.field private final emptyContextEdgeMap:Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap<",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;",
            ">;"
        }
    .end annotation
.end field

.field private final emptyEdgeMap:Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap<",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;",
            ">;"
        }
    .end annotation
.end field

.field private final maxDfaEdge:I

.field private final minDfaEdge:I

.field private final nextStateNumber:Ljava/util/concurrent/atomic/AtomicInteger;

.field private final precedenceDfa:Z

.field public final s0:Ljava/util/concurrent/atomic/AtomicReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/atomic/AtomicReference<",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;",
            ">;"
        }
    .end annotation
.end field

.field public final s0full:Ljava/util/concurrent/atomic/AtomicReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/atomic/AtomicReference<",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;",
            ">;"
        }
    .end annotation
.end field

.field public final states:Ljava/util/concurrent/ConcurrentMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/ConcurrentMap<",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 92
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    const/4 v1, 0x0

    const/16 v2, 0xc8

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;-><init>(II)V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->EMPTY_PRECEDENCE_EDGES:Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V
    .locals 1

    const/4 v0, 0x0

    .line 124
    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;I)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;I)V
    .locals 6

    .line 133
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 38
    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->states:Ljava/util/concurrent/ConcurrentMap;

    .line 49
    new-instance v0, Ljava/util/concurrent/atomic/AtomicReference;

    invoke-direct {v0}, Ljava/util/concurrent/atomic/AtomicReference;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    .line 57
    new-instance v1, Ljava/util/concurrent/atomic/AtomicReference;

    invoke-direct {v1}, Ljava/util/concurrent/atomic/AtomicReference;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0full:Ljava/util/concurrent/atomic/AtomicReference;

    .line 66
    new-instance v2, Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-direct {v2}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>()V

    iput-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->nextStateNumber:Ljava/util/concurrent/atomic/AtomicInteger;

    .line 134
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->atnStartState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 135
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->decision:I

    .line 137
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    sget-object v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;->LEXER:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    const/4 v3, 0x0

    const/4 v4, -0x1

    if-ne p2, v2, :cond_0

    .line 138
    iput v3, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->minDfaEdge:I

    const p2, 0x10ffff

    .line 139
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->maxDfaEdge:I

    goto :goto_0

    .line 142
    :cond_0
    iput v4, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->minDfaEdge:I

    .line 143
    iget-object p2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget p2, p2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->maxTokenType:I

    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->maxDfaEdge:I

    .line 146
    :goto_0
    new-instance p2, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->minDfaEdge:I

    iget v5, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->maxDfaEdge:I

    invoke-direct {p2, v2, v5}, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;-><init>(II)V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->emptyEdgeMap:Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    .line 147
    new-instance p2, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    iget-object v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    const/4 v5, 0x1

    sub-int/2addr v2, v5

    invoke-direct {p2, v4, v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;-><init>(II)V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->emptyContextEdgeMap:Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    .line 157
    instance-of p2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    if-eqz p2, :cond_1

    .line 158
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    iget-boolean p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;->precedenceRuleDecision:Z

    if-eqz p1, :cond_1

    .line 160
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    sget-object p2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->EMPTY_PRECEDENCE_EDGES:Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->getEmptyContextEdgeMap()Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    move-result-object v2

    new-instance v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    invoke-direct {v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;-><init>()V

    invoke-direct {p1, p2, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V

    invoke-virtual {v0, p1}, Ljava/util/concurrent/atomic/AtomicReference;->set(Ljava/lang/Object;)V

    .line 161
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->getEmptyContextEdgeMap()Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    move-result-object v0

    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    invoke-direct {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;-><init>()V

    invoke-direct {p1, p2, v0, v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V

    invoke-virtual {v1, p1}, Ljava/util/concurrent/atomic/AtomicReference;->set(Ljava/lang/Object;)V

    move v3, v5

    .line 165
    :cond_1
    iput-boolean v3, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->precedenceDfa:Z

    return-void
.end method


# virtual methods
.method public addState(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;
    .locals 1

    .line 349
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->nextStateNumber:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->getAndIncrement()I

    move-result v0

    iput v0, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->stateNumber:I

    .line 350
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->states:Ljava/util/concurrent/ConcurrentMap;

    invoke-interface {v0, p1, p1}, Ljava/util/concurrent/ConcurrentMap;->putIfAbsent(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    return-object p1
.end method

.method public getEmptyContextEdgeMap()Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap<",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;",
            ">;"
        }
    .end annotation

    .line 237
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->emptyContextEdgeMap:Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    return-object v0
.end method

.method public getEmptyEdgeMap()Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap<",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;",
            ">;"
        }
    .end annotation

    .line 215
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->emptyEdgeMap:Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    return-object v0
.end method

.method public final getMaxDfaEdge()I
    .locals 1

    .line 187
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->maxDfaEdge:I

    return v0
.end method

.method public final getMinDfaEdge()I
    .locals 1

    .line 176
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->minDfaEdge:I

    return v0
.end method

.method public final getPrecedenceStartState(IZ)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;
    .locals 1

    .line 267
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->isPrecedenceDfa()Z

    move-result v0

    if-eqz v0, :cond_1

    if-eqz p2, :cond_0

    .line 273
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0full:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getTarget(I)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    move-result-object p1

    return-object p1

    .line 276
    :cond_0
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getTarget(I)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    move-result-object p1

    return-object p1

    .line 268
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Only precedence DFAs may contain a precedence start state."

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public isContextSensitive()Z
    .locals 2

    .line 341
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->isPrecedenceDfa()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    .line 342
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0full:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getEdgeMap()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    xor-int/2addr v0, v1

    return v0

    .line 345
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0full:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    return v1
.end method

.method public isEmpty()Z
    .locals 3

    .line 333
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->isPrecedenceDfa()Z

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_1

    .line 334
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getEdgeMap()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0full:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getEdgeMap()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    move v1, v2

    :goto_0
    return v1

    .line 337
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0full:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_2

    goto :goto_1

    :cond_2
    move v1, v2

    :goto_1
    return v1
.end method

.method public final isPrecedenceDfa()Z
    .locals 1

    .line 252
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->precedenceDfa:Z

    return v0
.end method

.method public final setPrecedenceDfa(Z)V
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 327
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->isPrecedenceDfa()Z

    move-result v0

    if-ne p1, v0, :cond_0

    return-void

    .line 328
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "The precedenceDfa field cannot change after a DFA is constructed."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final setPrecedenceStartState(IZLgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V
    .locals 1

    .line 292
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->isPrecedenceDfa()Z

    move-result v0

    if-eqz v0, :cond_2

    if-gez p1, :cond_0

    return-void

    :cond_0
    if-eqz p2, :cond_1

    .line 301
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0full:Ljava/util/concurrent/atomic/AtomicReference;

    monitor-enter p2

    .line 303
    :try_start_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0full:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {v0, p1, p3}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->setTarget(ILgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V

    .line 304
    monitor-exit p2

    goto :goto_0

    :catchall_0
    move-exception p1

    monitor-exit p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1

    .line 307
    :cond_1
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    monitor-enter p2

    .line 309
    :try_start_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {v0, p1, p3}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->setTarget(ILgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V

    .line 310
    monitor-exit p2

    :goto_0
    return-void

    :catchall_1
    move-exception p1

    monitor-exit p2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    throw p1

    .line 293
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Only precedence DFAs may contain a precedence start state."

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public toLexerString()Ljava/lang/String;
    .locals 1

    .line 400
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const-string v0, ""

    return-object v0

    .line 401
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/LexerDFASerializer;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/LexerDFASerializer;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;)V

    .line 402
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 359
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/VocabularyImpl;->EMPTY_VOCABULARY:Lgroovyjarjarantlr4/v4/runtime/VocabularyImpl;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;)Ljava/lang/String;
    .locals 1

    .line 372
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const-string p1, ""

    return-object p1

    .line 376
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;

    invoke-direct {v0, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;)V

    .line 377
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;[Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 391
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const-string p1, ""

    return-object p1

    .line 395
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->atnStartState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-direct {v0, p0, p1, p2, v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;Lgroovyjarjarantlr4/v4/runtime/Vocabulary;[Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    .line 396
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public toString([Ljava/lang/String;)Ljava/lang/String;
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 366
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const-string p1, ""

    return-object p1

    .line 367
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;

    invoke-direct {v0, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;[Ljava/lang/String;)V

    .line 368
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public toString([Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 385
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const-string p1, ""

    return-object p1

    .line 386
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->atnStartState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-direct {v0, p0, p1, p2, v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;[Ljava/lang/String;[Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    .line 387
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFASerializer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
