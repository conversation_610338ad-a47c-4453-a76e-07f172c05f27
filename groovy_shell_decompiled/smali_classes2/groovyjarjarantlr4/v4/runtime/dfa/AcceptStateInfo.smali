.class public Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;
.super Ljava/lang/Object;
.source "AcceptStateInfo.java"


# instance fields
.field private final lexerActionExecutor:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

.field private final prediction:I


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 24
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 25
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;->prediction:I

    const/4 p1, 0x0

    .line 26
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;->lexerActionExecutor:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;)V
    .locals 0

    .line 29
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 30
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;->prediction:I

    .line 31
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;->lexerActionExecutor:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    return-void
.end method


# virtual methods
.method public getLexerActionExecutor()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;
    .locals 1

    .line 50
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;->lexerActionExecutor:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    return-object v0
.end method

.method public getPrediction()I
    .locals 1

    .line 42
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;->prediction:I

    return v0
.end method
