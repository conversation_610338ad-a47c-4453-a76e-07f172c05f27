.class Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;
.super Ljava/lang/Object;
.source "ArrayEdgeMap.java"

# interfaces
.implements Ljava/util/Map$Entry;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->next()Ljava/util/Map$Entry;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Map$Entry<",
        "Ljava/lang/Integer;",
        "TT;>;"
    }
.end annotation


# instance fields
.field private final key:I

.field final synthetic this$1:Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;

.field final synthetic val$currentElement:Ljava/lang/Object;

.field private final value:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;Ljava/lang/Object;)V
    .locals 1

    .line 186
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;->this$1:Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;->val$currentElement:Ljava/lang/Object;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 187
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    iget v0, v0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->minIndex:I

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->access$300(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;)I

    move-result p1

    add-int/2addr v0, p1

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;->key:I

    .line 188
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;->value:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public getKey()Ljava/lang/Integer;
    .locals 1

    .line 192
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;->key:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getKey()Ljava/lang/Object;
    .locals 1

    .line 186
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;->getKey()Ljava/lang/Integer;

    move-result-object v0

    return-object v0
.end method

.method public getValue()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    .line 197
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;->value:Ljava/lang/Object;

    return-object v0
.end method

.method public setValue(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)TT;"
        }
    .end annotation

    .line 202
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Not supported yet."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
