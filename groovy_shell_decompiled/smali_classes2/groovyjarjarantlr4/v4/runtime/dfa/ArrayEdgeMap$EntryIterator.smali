.class Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;
.super Ljava/lang/Object;
.source "ArrayEdgeMap.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "EntryIterator"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "Ljava/util/Map$Entry<",
        "Ljava/lang/Integer;",
        "TT;>;>;"
    }
.end annotation


# instance fields
.field private currentIndex:I

.field final synthetic this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;


# direct methods
.method private constructor <init>(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;)V
    .locals 0

    .line 164
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 p1, -0x1

    .line 165
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->currentIndex:I

    return-void
.end method

.method synthetic constructor <init>(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$1;)V
    .locals 0

    .line 164
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;)V

    return-void
.end method

.method static synthetic access$300(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;)I
    .locals 0

    .line 164
    iget p0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->currentIndex:I

    return p0
.end method


# virtual methods
.method public hasNext()Z
    .locals 3

    .line 169
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->currentIndex:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    invoke-static {v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->access$200(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;)Ljava/util/concurrent/atomic/AtomicReferenceArray;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicReferenceArray;->length()I

    move-result v1

    const/4 v2, 0x1

    sub-int/2addr v1, v2

    if-ge v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    return v2
.end method

.method public bridge synthetic next()Ljava/lang/Object;
    .locals 1

    .line 164
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->next()Ljava/util/Map$Entry;

    move-result-object v0

    return-object v0
.end method

.method public next()Ljava/util/Map$Entry;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map$Entry<",
            "Ljava/lang/Integer;",
            "TT;>;"
        }
    .end annotation

    const/4 v0, 0x0

    :goto_0
    if-nez v0, :cond_0

    .line 175
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->currentIndex:I

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->access$200(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;)Ljava/util/concurrent/atomic/AtomicReferenceArray;

    move-result-object v2

    invoke-virtual {v2}, Ljava/util/concurrent/atomic/AtomicReferenceArray;->length()I

    move-result v2

    add-int/lit8 v2, v2, -0x1

    if-ge v1, v2, :cond_0

    .line 176
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->currentIndex:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->currentIndex:I

    .line 177
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->access$200(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;)Ljava/util/concurrent/atomic/AtomicReferenceArray;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->currentIndex:I

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicReferenceArray;->get(I)Ljava/lang/Object;

    move-result-object v0

    goto :goto_0

    :cond_0
    if-eqz v0, :cond_1

    .line 184
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->currentIndex:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;->currentIndex:I

    .line 186
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;

    invoke-direct {v1, p0, v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator$1;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;Ljava/lang/Object;)V

    return-object v1

    .line 181
    :cond_1
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public remove()V
    .locals 2

    .line 209
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Not supported yet."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
