.class public final Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;
.super Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
.source "EmptyEdgeMap.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
        "TT;>;"
    }
.end annotation


# direct methods
.method public constructor <init>(II)V
    .locals 0

    .line 21
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;-><init>(II)V

    return-void
.end method


# virtual methods
.method public clear()Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation

    return-object p0
.end method

.method public bridge synthetic clear()Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 1

    .line 18
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;->clear()Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object v0

    return-object v0
.end method

.method public containsKey(I)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public entrySet()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/util/Map$Entry<",
            "Ljava/lang/Integer;",
            "TT;>;>;"
        }
    .end annotation

    .line 71
    invoke-static {}, Ljava/util/Collections;->emptyMap()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method public get(I)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)TT;"
        }
    .end annotation

    const/4 p1, 0x0

    return-object p1
.end method

.method public isEmpty()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ITT;)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation

    if-eqz p2, :cond_1

    .line 26
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;->minIndex:I

    if-lt p1, v0, :cond_1

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;->maxIndex:I

    if-le p1, v0, :cond_0

    goto :goto_0

    .line 31
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;->minIndex:I

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;->maxIndex:I

    invoke-direct {v0, v1, v2, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;-><init>(IIILjava/lang/Object;)V

    return-object v0

    :cond_1
    :goto_0
    return-object p0
.end method

.method public bridge synthetic put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 18
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation

    return-object p0
.end method

.method public bridge synthetic remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 18
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;->remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public size()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public toMap()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "TT;>;"
        }
    .end annotation

    .line 66
    invoke-static {}, Ljava/util/Collections;->emptyMap()Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method
