.class Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;
.super Ljava/lang/Object;
.source "SingletonEdgeMap.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "EntryIterator"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "Ljava/util/Map$Entry<",
        "Ljava/lang/Integer;",
        "TT;>;>;"
    }
.end annotation


# instance fields
.field private current:I

.field final synthetic this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;


# direct methods
.method private constructor <init>(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;)V
    .locals 0

    .line 123
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$1;)V
    .locals 0

    .line 123
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;)V

    return-void
.end method


# virtual methods
.method public hasNext()Z
    .locals 2

    .line 128
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->current:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public bridge synthetic next()Ljava/lang/Object;
    .locals 1

    .line 123
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->next()Ljava/util/Map$Entry;

    move-result-object v0

    return-object v0
.end method

.method public next()Ljava/util/Map$Entry;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map$Entry<",
            "Ljava/lang/Integer;",
            "TT;>;"
        }
    .end annotation

    .line 133
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->current:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    .line 137
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->current:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->current:I

    .line 138
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator$1;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator$1;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;)V

    return-object v0

    .line 134
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public remove()V
    .locals 2

    .line 161
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Not supported yet."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
