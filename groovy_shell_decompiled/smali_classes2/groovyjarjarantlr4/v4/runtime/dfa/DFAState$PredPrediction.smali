.class public Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState$PredPrediction;
.super Ljava/lang/Object;
.source "DFAState.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "PredPrediction"
.end annotation


# instance fields
.field public alt:I

.field public pred:Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;I)V
    .locals 0

    .line 81
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 82
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState$PredPrediction;->alt:I

    .line 83
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState$PredPrediction;->pred:Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    return-void
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 2

    .line 87
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState$PredPrediction;->pred:Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState$PredPrediction;->alt:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
