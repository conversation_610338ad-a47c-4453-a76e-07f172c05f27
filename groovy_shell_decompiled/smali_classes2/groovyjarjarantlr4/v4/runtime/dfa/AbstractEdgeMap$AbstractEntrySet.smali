.class public abstract Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap$AbstractEntrySet;
.super Ljava/util/AbstractSet;
.source "AbstractEdgeMap.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x404
    name = "AbstractEntrySet"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/util/AbstractSet<",
        "Ljava/util/Map$Entry<",
        "Ljava/lang/Integer;",
        "TT;>;>;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;


# direct methods
.method protected constructor <init>(Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;)V
    .locals 0

    .line 46
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap$AbstractEntrySet;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    invoke-direct {p0}, Ljava/util/AbstractSet;-><init>()V

    return-void
.end method


# virtual methods
.method public contains(Ljava/lang/Object;)Z
    .locals 3

    .line 49
    instance-of v0, p1, Ljava/util/Map$Entry;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 53
    :cond_0
    check-cast p1, Ljava/util/Map$Entry;

    .line 54
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Ljava/lang/Integer;

    if-eqz v0, :cond_2

    .line 55
    invoke-interface {p1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Integer;

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    .line 56
    invoke-interface {p1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object p1

    .line 57
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap$AbstractEntrySet;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->get(I)Ljava/lang/Object;

    move-result-object v0

    if-eq p1, v0, :cond_1

    if-eqz v0, :cond_2

    .line 58
    invoke-virtual {v0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    :cond_1
    const/4 v1, 0x1

    :cond_2
    return v1
.end method

.method public size()I
    .locals 1

    .line 66
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap$AbstractEntrySet;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->size()I

    move-result v0

    return v0
.end method
