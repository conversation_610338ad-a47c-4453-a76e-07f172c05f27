.class public abstract Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
.super Ljava/lang/Object;
.source "AbstractEdgeMap.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap$AbstractEntrySet;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap<",
        "TT;>;"
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field protected final maxIndex:I

.field protected final minIndex:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(II)V
    .locals 0

    .line 20
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 23
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->minIndex:I

    .line 24
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->maxIndex:I

    return-void
.end method


# virtual methods
.method public abstract clear()Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation
.end method

.method public bridge synthetic clear()Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 1

    .line 15
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->clear()Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object v0

    return-object v0
.end method

.method public abstract put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ITT;)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation
.end method

.method public bridge synthetic put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 15
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public putAll(Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap<",
            "+TT;>;)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation

    .line 33
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    move-object v0, p0

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 34
    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v0, v2, v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object v0

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public bridge synthetic putAll(Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 15
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->putAll(Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public abstract remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
            "TT;>;"
        }
    .end annotation
.end method

.method public bridge synthetic remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 15
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;->remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;

    move-result-object p1

    return-object p1
.end method
