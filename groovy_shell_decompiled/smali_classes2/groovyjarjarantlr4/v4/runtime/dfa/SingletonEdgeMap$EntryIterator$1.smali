.class Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator$1;
.super Ljava/lang/Object;
.source "SingletonEdgeMap.java"

# interfaces
.implements Ljava/util/Map$Entry;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->next()Ljava/util/Map$Entry;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Map$Entry<",
        "Ljava/lang/Integer;",
        "TT;>;"
    }
.end annotation


# instance fields
.field private final key:I

.field final synthetic this$1:Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;

.field private final value:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;)V
    .locals 1

    .line 138
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator$1;->this$1:Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 139
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->access$200(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;)I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator$1;->key:I

    .line 140
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator;->this$0:Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->access$300(Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;)Ljava/lang/Object;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator$1;->value:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public getKey()Ljava/lang/Integer;
    .locals 1

    .line 144
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator$1;->key:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getKey()Ljava/lang/Object;
    .locals 1

    .line 138
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator$1;->getKey()Ljava/lang/Integer;

    move-result-object v0

    return-object v0
.end method

.method public getValue()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    .line 149
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap$EntryIterator$1;->value:Ljava/lang/Object;

    return-object v0
.end method

.method public setValue(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)TT;"
        }
    .end annotation

    .line 154
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Not supported yet."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
