.class public final Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;
.super Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
.source "ArrayEdgeMap.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntryIterator;,
        Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntrySet;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap<",
        "TT;>;"
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field private final arrayData:Ljava/util/concurrent/atomic/AtomicReferenceArray;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/atomic/AtomicReferenceArray<",
            "TT;>;"
        }
    .end annotation
.end field

.field private final size:Ljava/util/concurrent/atomic/AtomicInteger;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(II)V
    .locals 1

    .line 30
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;-><init>(II)V

    .line 31
    new-instance v0, Ljava/util/concurrent/atomic/AtomicReferenceArray;

    sub-int/2addr p2, p1

    add-int/lit8 p2, p2, 0x1

    invoke-direct {v0, p2}, Ljava/util/concurrent/atomic/AtomicReferenceArray;-><init>(I)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->arrayData:Ljava/util/concurrent/atomic/AtomicReferenceArray;

    .line 32
    new-instance p1, Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-direct {p1}, Ljava/util/concurrent/atomic/AtomicInteger;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->size:Ljava/util/concurrent/atomic/AtomicInteger;

    return-void
.end method

.method static synthetic access$200(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;)Ljava/util/concurrent/atomic/AtomicReferenceArray;
    .locals 0

    .line 23
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->arrayData:Ljava/util/concurrent/atomic/AtomicReferenceArray;

    return-object p0
.end method


# virtual methods
.method public bridge synthetic clear()Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 1

    .line 23
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->clear()Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clear()Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 1

    .line 23
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->clear()Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    move-result-object v0

    return-object v0
.end method

.method public clear()Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap<",
            "TT;>;"
        }
    .end annotation

    .line 130
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->minIndex:I

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->maxIndex:I

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/EmptyEdgeMap;-><init>(II)V

    return-object v0
.end method

.method public containsKey(I)Z
    .locals 0

    .line 47
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->get(I)Ljava/lang/Object;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public entrySet()Ljava/util/Set;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/util/Map$Entry<",
            "Ljava/lang/Integer;",
            "TT;>;>;"
        }
    .end annotation

    .line 154
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntrySet;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$EntrySet;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap$1;)V

    return-object v0
.end method

.method public get(I)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)TT;"
        }
    .end annotation

    .line 52
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->minIndex:I

    if-lt p1, v0, :cond_1

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->maxIndex:I

    if-le p1, v0, :cond_0

    goto :goto_0

    .line 56
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->arrayData:Ljava/util/concurrent/atomic/AtomicReferenceArray;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->minIndex:I

    sub-int/2addr p1, v1

    invoke-virtual {v0, p1}, Ljava/util/concurrent/atomic/AtomicReferenceArray;->get(I)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_1
    :goto_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public isEmpty()Z
    .locals 1

    .line 42
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->size()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public bridge synthetic put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 0

    .line 23
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ITT;)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap<",
            "TT;>;"
        }
    .end annotation

    .line 61
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->minIndex:I

    if-lt p1, v0, :cond_1

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->maxIndex:I

    if-gt p1, v0, :cond_1

    .line 62
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->arrayData:Ljava/util/concurrent/atomic/AtomicReferenceArray;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->minIndex:I

    sub-int/2addr p1, v1

    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/atomic/AtomicReferenceArray;->getAndSet(ILjava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-nez p1, :cond_0

    if-eqz p2, :cond_0

    .line 64
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->size:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicInteger;->incrementAndGet()I

    goto :goto_0

    :cond_0
    if-eqz p1, :cond_1

    if-nez p2, :cond_1

    .line 66
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->size:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicInteger;->decrementAndGet()I

    :cond_1
    :goto_0
    return-object p0
.end method

.method public bridge synthetic put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 23
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic putAll(Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 0

    .line 23
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->putAll(Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public putAll(Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap<",
            "+TT;>;)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap<",
            "TT;>;"
        }
    .end annotation

    .line 81
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p0

    .line 85
    :cond_0
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/HashEdgeMap;

    const/4 v1, 0x0

    if-eqz v0, :cond_3

    .line 86
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/dfa/HashEdgeMap;

    .line 87
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/HashEdgeMap;->getKeys()Ljava/util/concurrent/atomic/AtomicIntegerArray;

    move-result-object v0

    .line 88
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/HashEdgeMap;->getValues()[Ljava/lang/Object;

    move-result-object p1

    move-object v2, p0

    .line 90
    :goto_0
    array-length v3, p1

    if-ge v1, v3, :cond_2

    .line 91
    aget-object v3, p1, v1

    if-eqz v3, :cond_1

    .line 93
    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicIntegerArray;->get(I)I

    move-result v4

    invoke-virtual {v2, v4, v3}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object v2

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    return-object v2

    .line 98
    :cond_3
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    if-eqz v0, :cond_5

    .line 99
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    .line 100
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->minIndex:I

    iget v2, v0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->minIndex:I

    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    move-result v1

    .line 101
    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->maxIndex:I

    iget v0, v0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->maxIndex:I

    invoke-static {v2, v0}, Ljava/lang/Math;->min(II)I

    move-result v0

    move-object v2, p0

    :goto_1
    if-gt v1, v0, :cond_4

    .line 104
    invoke-interface {p1, v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;->get(I)Ljava/lang/Object;

    move-result-object v3

    invoke-virtual {v2, v1, v3}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object v2

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_4
    return-object v2

    .line 108
    :cond_5
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;

    if-eqz v0, :cond_6

    .line 109
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;

    .line 111
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->getKey()I

    move-result v0

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/SingletonEdgeMap;->getValue()Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object p1

    return-object p1

    .line 112
    :cond_6
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/SparseEdgeMap;

    if-eqz v0, :cond_8

    .line 113
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/SparseEdgeMap;

    .line 114
    monitor-enter v0

    .line 115
    :try_start_0
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/SparseEdgeMap;->getKeys()[I

    move-result-object p1

    .line 116
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/SparseEdgeMap;->getValues()Ljava/util/List;

    move-result-object v2

    move-object v3, p0

    .line 118
    :goto_2
    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v4

    if-ge v1, v4, :cond_7

    .line 119
    aget v4, p1, v1

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    invoke-virtual {v3, v4, v5}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object v3

    add-int/lit8 v1, v1, 0x1

    goto :goto_2

    .line 121
    :cond_7
    monitor-exit v0

    return-object v3

    :catchall_0
    move-exception p1

    .line 122
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1

    .line 124
    :cond_8
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v2, "EdgeMap of type %s is supported yet."

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    aput-object p1, v3, v1

    invoke-static {v2, v3}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public bridge synthetic putAll(Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 23
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->putAll(Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/AbstractEdgeMap;
    .locals 0

    .line 23
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap<",
            "TT;>;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 75
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->put(ILjava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/EdgeMap;
    .locals 0

    .line 23
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->remove(I)Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;

    move-result-object p1

    return-object p1
.end method

.method public size()I
    .locals 1

    .line 37
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->size:Ljava/util/concurrent/atomic/AtomicInteger;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicInteger;->get()I

    move-result v0

    return v0
.end method

.method public toMap()Ljava/util/Map;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "TT;>;"
        }
    .end annotation

    .line 135
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 136
    invoke-static {}, Ljava/util/Collections;->emptyMap()Ljava/util/Map;

    move-result-object v0

    return-object v0

    .line 139
    :cond_0
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    const/4 v1, 0x0

    .line 140
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->arrayData:Ljava/util/concurrent/atomic/AtomicReferenceArray;

    invoke-virtual {v2}, Ljava/util/concurrent/atomic/AtomicReferenceArray;->length()I

    move-result v2

    if-ge v1, v2, :cond_2

    .line 141
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->arrayData:Ljava/util/concurrent/atomic/AtomicReferenceArray;

    invoke-virtual {v2, v1}, Ljava/util/concurrent/atomic/AtomicReferenceArray;->get(I)Ljava/lang/Object;

    move-result-object v2

    if-nez v2, :cond_1

    goto :goto_1

    .line 146
    :cond_1
    iget v3, p0, Lgroovyjarjarantlr4/v4/runtime/dfa/ArrayEdgeMap;->minIndex:I

    add-int/2addr v3, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v0, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    return-object v0
.end method
