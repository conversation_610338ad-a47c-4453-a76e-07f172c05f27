.class Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$InsertBeforeOp;
.super Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$RewriteOperation;
.source "TokenStreamRewriter.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "InsertBeforeOp"
.end annotation


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/TokenStream;ILjava/lang/Object;)V
    .locals 0

    .line 136
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$RewriteOperation;-><init>(Lgroovyjarjarantlr4/v4/runtime/TokenStream;ILjava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public execute(Ljava/lang/StringBuilder;)I
    .locals 2

    .line 141
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$InsertBeforeOp;->text:Ljava/lang/Object;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 142
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$InsertBeforeOp;->tokens:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$InsertBeforeOp;->index:I

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    .line 143
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$InsertBeforeOp;->tokens:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$InsertBeforeOp;->index:I

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 145
    :cond_0
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$InsertBeforeOp;->index:I

    add-int/lit8 p1, p1, 0x1

    return p1
.end method
