.class public abstract Lgroovyjarjarantlr4/v4/runtime/tree/xpath/XPathElement;
.super Ljava/lang/Object;
.source "XPathElement.java"


# instance fields
.field protected invert:Z

.field protected nodeName:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 20
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 21
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/tree/xpath/XPathElement;->nodeName:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public abstract evaluate(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ")",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">;"
        }
    .end annotation
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 32
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/xpath/XPathElement;->invert:Z

    if-eqz v0, :cond_0

    const-string v0, "!"

    goto :goto_0

    :cond_0
    const-string v0, ""

    .line 33
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "["

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/tree/xpath/XPathElement;->nodeName:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
