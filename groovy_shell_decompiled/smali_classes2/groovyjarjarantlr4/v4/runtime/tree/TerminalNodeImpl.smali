.class public Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;
.super Ljava/lang/Object;
.source "TerminalNodeImpl.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;


# instance fields
.field public parent:Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;

.field public symbol:Lgroovyjarjarantlr4/v4/runtime/Token;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/Token;)V
    .locals 0

    .line 19
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->symbol:Lgroovyjarjarantlr4/v4/runtime/Token;

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeVisitor;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeVisitor<",
            "+TT;>;)TT;"
        }
    .end annotation

    .line 52
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeVisitor;->visitTerminal(Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public bridge synthetic getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 0

    .line 15
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    move-result-object p1

    return-object p1
.end method

.method public getChildCount()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public bridge synthetic getParent()Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;
    .locals 1

    .line 15
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->getParent()Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;

    move-result-object v0

    return-object v0
.end method

.method public getParent()Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;
    .locals 1

    .line 28
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->parent:Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;

    return-object v0
.end method

.method public bridge synthetic getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 1

    .line 15
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->getParent()Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;

    move-result-object v0

    return-object v0
.end method

.method public getPayload()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 1

    .line 35
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->symbol:Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object v0
.end method

.method public bridge synthetic getPayload()Ljava/lang/Object;
    .locals 1

    .line 15
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->getPayload()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    return-object v0
.end method

.method public getSourceInterval()Lgroovyjarjarantlr4/v4/runtime/misc/Interval;
    .locals 2

    .line 39
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->symbol:Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_0

    .line 40
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result v0

    .line 41
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    invoke-direct {v1, v0, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;-><init>(II)V

    return-object v1

    .line 44
    :cond_0
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->INVALID:Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    return-object v0
.end method

.method public getSymbol()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 1

    .line 25
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->symbol:Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object v0
.end method

.method public getText()Ljava/lang/String;
    .locals 1

    .line 57
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->symbol:Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_0

    .line 58
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public setParent(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V
    .locals 0

    .line 31
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->parent:Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 71
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->symbol:Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_1

    .line 72
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    const-string v0, "<EOF>"

    return-object v0

    .line 76
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->symbol:Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_1
    const-string v0, "<null>"

    return-object v0
.end method

.method public toStringTree()Ljava/lang/String;
    .locals 1

    .line 85
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toStringTree(Lgroovyjarjarantlr4/v4/runtime/Parser;)Ljava/lang/String;
    .locals 0

    .line 66
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
