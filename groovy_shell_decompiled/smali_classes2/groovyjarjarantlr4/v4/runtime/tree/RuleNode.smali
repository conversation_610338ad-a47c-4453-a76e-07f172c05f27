.class public interface abstract Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;
.super Ljava/lang/Object;
.source "RuleNode.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;


# virtual methods
.method public abstract getParent()Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;
.end method

.method public abstract getRuleContext()Lgroovyjarjarantlr4/v4/runtime/RuleContext;
.end method
