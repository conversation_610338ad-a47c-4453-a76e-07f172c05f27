.class public Lgroovyjarjarantlr4/v4/runtime/tree/Trees;
.super Ljava/lang/Object;
.source "Trees.java"


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 268
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static _findAllNodes(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;IZLjava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            "IZ",
            "Ljava/util/List<",
            "-",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">;)V"
        }
    .end annotation

    if-eqz p2, :cond_0

    .line 162
    instance-of v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;

    if-eqz v0, :cond_0

    .line 163
    move-object v0, p0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;

    .line 164
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;->getSymbol()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v0

    if-ne v0, p1, :cond_1

    invoke-interface {p3, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_0
    if-nez p2, :cond_1

    .line 166
    instance-of v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    if-eqz v0, :cond_1

    .line 167
    move-object v0, p0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 168
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getRuleIndex()I

    move-result v0

    if-ne v0, p1, :cond_1

    invoke-interface {p3, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    :goto_0
    const/4 v0, 0x0

    .line 171
    :goto_1
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;->getChildCount()I

    move-result v1

    if-ge v0, v1, :cond_2

    .line 172
    invoke-interface {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    move-result-object v1

    invoke-static {v1, p1, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->_findAllNodes(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;IZLjava/util/List;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_2
    return-void
.end method

.method public static descendants(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 194
    invoke-static {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->getDescendants(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static findAllNodes(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;IZ)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            "IZ)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">;"
        }
    .end annotation

    .line 153
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 154
    invoke-static {p0, p1, p2, v0}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->_findAllNodes(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;IZLjava/util/List;)V

    return-object v0
.end method

.method public static findAllRuleNodes(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;I)Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            "I)",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 149
    invoke-static {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->findAllNodes(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;IZ)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static findAllTokenNodes(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;I)Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            "I)",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x1

    .line 145
    invoke-static {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->findAllNodes(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;IZ)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static findNodeSuchThat(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/misc/Predicate;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/Predicate<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;"
        }
    .end annotation

    .line 256
    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/misc/Predicate;->eval(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p0

    :cond_0
    const/4 v0, 0x0

    if-nez p0, :cond_1

    return-object v0

    .line 260
    :cond_1
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChildCount()I

    move-result v1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_3

    .line 262
    invoke-interface {p0, v2}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v3

    invoke-static {v3, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->findNodeSuchThat(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/misc/Predicate;)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v3

    if-eqz v3, :cond_2

    return-object v3

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    return-object v0
.end method

.method public static getAncestors(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ")",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ">;"
        }
    .end annotation

    .line 119
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v0

    if-nez v0, :cond_0

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p0

    return-object p0

    .line 120
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 121
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p0

    :goto_0
    if-eqz p0, :cond_1

    const/4 v1, 0x0

    .line 123
    invoke-interface {v0, v1, p0}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 124
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p0

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public static getChildren(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            ">;"
        }
    .end annotation

    .line 105
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    .line 106
    :goto_0
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChildCount()I

    move-result v2

    if-ge v1, v2, :cond_0

    .line 107
    invoke-interface {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public static getDescendants(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;",
            ">;"
        }
    .end annotation

    .line 181
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 182
    invoke-interface {v0, p0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 184
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;->getChildCount()I

    move-result v1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    .line 186
    invoke-interface {p0, v2}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    move-result-object v3

    invoke-static {v3}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->getDescendants(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;)Ljava/util/List;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public static getNodeText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/Parser;)Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    .line 67
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleNames()[Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    :cond_0
    move-object p1, v0

    :goto_0
    if-eqz p1, :cond_1

    .line 68
    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    .line 69
    :cond_1
    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->getNodeText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Ljava/util/List;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getNodeText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Ljava/util/List;)Ljava/lang/String;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/String;"
        }
    .end annotation

    if-eqz p1, :cond_3

    .line 74
    instance-of v0, p0, Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;

    if-eqz v0, :cond_1

    .line 75
    check-cast p0, Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;

    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/RuleNode;->getRuleContext()Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    move-result-object p0

    .line 76
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->getRuleIndex()I

    move-result v0

    .line 77
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    .line 78
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->getAltNumber()I

    move-result p0

    if-eqz p0, :cond_0

    .line 80
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ":"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_0
    return-object p1

    .line 84
    :cond_1
    instance-of p1, p0, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    if-eqz p1, :cond_2

    .line 85
    invoke-virtual {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 87
    :cond_2
    instance-of p1, p0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;

    if-eqz p1, :cond_3

    .line 88
    move-object p1, p0

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;->getSymbol()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    if-eqz p1, :cond_3

    .line 90
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 96
    :cond_3
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getPayload()Ljava/lang/Object;

    move-result-object p1

    .line 97
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_4

    .line 98
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 100
    :cond_4
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getPayload()Ljava/lang/Object;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getRootOfSubtreeEnclosingRegion(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;II)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
    .locals 3

    .line 207
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;->getChildCount()I

    move-result v0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    .line 209
    invoke-interface {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    move-result-object v2

    .line 210
    invoke-static {v2, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->getRootOfSubtreeEnclosingRegion(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;II)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v2

    if-eqz v2, :cond_0

    return-object v2

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 213
    :cond_1
    instance-of v0, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    if-eqz v0, :cond_3

    .line 214
    check-cast p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 215
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getStart()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result v0

    if-lt p1, v0, :cond_3

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getStop()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    if-eqz p1, :cond_2

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getStop()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result p1

    if-gt p2, p1, :cond_3

    :cond_2
    return-object p0

    :cond_3
    const/4 p0, 0x0

    return-object p0
.end method

.method public static isAncestorOf(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Z
    .locals 2

    const/4 v0, 0x0

    if-eqz p0, :cond_2

    if-eqz p1, :cond_2

    .line 135
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v1

    if-nez v1, :cond_0

    goto :goto_1

    .line 136
    :cond_0
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p1

    :goto_0
    if-eqz p1, :cond_2

    if-ne p0, p1, :cond_1

    const/4 p0, 0x1

    return p0

    .line 139
    :cond_1
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object p1

    goto :goto_0

    :cond_2
    :goto_1
    return v0
.end method

.method public static stripChildrenOutOfRange(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V
    .locals 5

    if-nez p0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x0

    move v1, v0

    .line 239
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getChildCount()I

    move-result v2

    if-ge v1, v2, :cond_3

    .line 240
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    move-result-object v2

    .line 241
    invoke-interface {v2}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;->getSourceInterval()Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v3

    .line 242
    instance-of v4, v2, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    if-eqz v4, :cond_2

    iget v4, v3, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-lt v4, p2, :cond_1

    iget v3, v3, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-le v3, p3, :cond_2

    .line 243
    :cond_1
    invoke-static {v2, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->isAncestorOf(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 244
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/CommonToken;

    const-string v3, "..."

    invoke-direct {v2, v0, v3}, Lgroovyjarjarantlr4/v4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    .line 245
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->children:Ljava/util/List;

    new-instance v4, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;

    invoke-direct {v4, v2}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;-><init>(Lgroovyjarjarantlr4/v4/runtime/Token;)V

    invoke-interface {v3, v1, v4}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    :cond_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method public static toStringTree(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;)Ljava/lang/String;
    .locals 2

    const/4 v0, 0x0

    .line 34
    move-object v1, v0

    check-cast v1, Ljava/util/List;

    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->toStringTree(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Ljava/util/List;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static toStringTree(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Lgroovyjarjarantlr4/v4/runtime/Parser;)Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    .line 42
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleNames()[Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    :cond_0
    move-object p1, v0

    :goto_0
    if-eqz p1, :cond_1

    .line 43
    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    .line 44
    :cond_1
    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->toStringTree(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Ljava/util/List;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static toStringTree(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Ljava/util/List;)Ljava/lang/String;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/tree/Tree;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 51
    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->getNodeText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Ljava/util/List;)Ljava/lang/String;

    move-result-object v0

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->escapeWhitespace(Ljava/lang/String;Z)Ljava/lang/String;

    move-result-object v0

    .line 52
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChildCount()I

    move-result v2

    if-nez v2, :cond_0

    return-object v0

    .line 53
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "("

    .line 54
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 55
    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->getNodeText(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Ljava/util/List;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v2, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->escapeWhitespace(Ljava/lang/String;Z)Ljava/lang/String;

    move-result-object v2

    .line 56
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v2, 0x20

    .line 57
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 58
    :goto_0
    invoke-interface {p0}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChildCount()I

    move-result v3

    if-ge v1, v3, :cond_2

    if-lez v1, :cond_1

    .line 59
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 60
    :cond_1
    invoke-interface {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/tree/Tree;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;

    move-result-object v3

    invoke-static {v3, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/Trees;->toStringTree(Lgroovyjarjarantlr4/v4/runtime/tree/Tree;Ljava/util/List;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    const-string p0, ")"

    .line 62
    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 63
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method
