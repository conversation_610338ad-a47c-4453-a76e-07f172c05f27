.class public interface abstract Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
.super Ljava/lang/Object;
.source "Tree.java"


# virtual methods
.method public abstract getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
.end method

.method public abstract getChildCount()I
.end method

.method public abstract getParent()Lgroovyjarjarantlr4/v4/runtime/tree/Tree;
.end method

.method public abstract getPayload()Ljava/lang/Object;
.end method

.method public abstract toStringTree()Ljava/lang/String;
.end method
