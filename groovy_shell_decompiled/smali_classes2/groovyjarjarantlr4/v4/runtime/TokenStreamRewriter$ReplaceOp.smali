.class Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;
.super Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$RewriteOperation;
.source "TokenStreamRewriter.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "ReplaceOp"
.end annotation


# instance fields
.field protected lastIndex:I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/TokenStream;IILjava/lang/Object;)V
    .locals 0

    .line 165
    invoke-direct {p0, p1, p2, p4}, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$RewriteOperation;-><init>(Lgroovyjarjarantlr4/v4/runtime/TokenStream;ILjava/lang/Object;)V

    .line 166
    iput p3, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->lastIndex:I

    return-void
.end method


# virtual methods
.method public execute(Ljava/lang/StringBuilder;)I
    .locals 1

    .line 170
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->text:Ljava/lang/Object;

    if-eqz v0, :cond_0

    .line 171
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->text:Ljava/lang/Object;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 173
    :cond_0
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->lastIndex:I

    add-int/lit8 p1, p1, 0x1

    return p1
.end method

.method public toString()Ljava/lang/String;
    .locals 4

    .line 177
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->text:Ljava/lang/Object;

    const-string v1, ".."

    if-nez v0, :cond_0

    .line 178
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "<DeleteOp@"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->tokens:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    iget v3, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->index:I

    invoke-interface {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->tokens:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->lastIndex:I

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ">"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 181
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "<ReplaceOp@"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->tokens:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    iget v3, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->index:I

    invoke-interface {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->tokens:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->lastIndex:I

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->get(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":\""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/TokenStreamRewriter$ReplaceOp;->text:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\">"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
