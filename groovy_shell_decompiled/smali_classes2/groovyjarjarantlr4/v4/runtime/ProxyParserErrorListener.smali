.class public Lgroovyjarjarantlr4/v4/runtime/ProxyParserErrorListener;
.super Lgroovyjarjarantlr4/v4/runtime/ProxyErrorListener;
.source "ProxyParserErrorListener.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lgroovyjarjarantlr4/v4/runtime/ProxyErrorListener<",
        "Lgroovyjarjarantlr4/v4/runtime/Token;",
        ">;",
        "Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ljava/util/Collection;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
            "-",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;>;)V"
        }
    .end annotation

    .line 21
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/ProxyErrorListener;-><init>(Ljava/util/Collection;)V

    return-void
.end method


# virtual methods
.method public reportAmbiguity(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IIZLjava/util/BitSet;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V
    .locals 11

    .line 26
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ProxyParserErrorListener;->getDelegates()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;

    .line 27
    instance-of v2, v1, Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;

    if-nez v2, :cond_0

    goto :goto_0

    .line 31
    :cond_0
    move-object v3, v1

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;

    move-object v4, p1

    move-object v5, p2

    move v6, p3

    move v7, p4

    move/from16 v8, p5

    move-object/from16 v9, p6

    move-object/from16 v10, p7

    .line 32
    invoke-interface/range {v3 .. v10}, Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;->reportAmbiguity(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IIZLjava/util/BitSet;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public reportAttemptingFullContext(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IILjava/util/BitSet;Lgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;)V
    .locals 10

    .line 38
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ProxyParserErrorListener;->getDelegates()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;

    .line 39
    instance-of v2, v1, Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;

    if-nez v2, :cond_0

    goto :goto_0

    .line 43
    :cond_0
    move-object v3, v1

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;

    move-object v4, p1

    move-object v5, p2

    move v6, p3

    move v7, p4

    move-object v8, p5

    move-object/from16 v9, p6

    .line 44
    invoke-interface/range {v3 .. v9}, Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;->reportAttemptingFullContext(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IILjava/util/BitSet;Lgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public reportContextSensitivity(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IIILgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;)V
    .locals 10

    .line 50
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ProxyParserErrorListener;->getDelegates()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;

    .line 51
    instance-of v2, v1, Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;

    if-nez v2, :cond_0

    goto :goto_0

    .line 55
    :cond_0
    move-object v3, v1

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;

    move-object v4, p1

    move-object v5, p2

    move v6, p3

    move v7, p4

    move v8, p5

    move-object/from16 v9, p6

    .line 56
    invoke-interface/range {v3 .. v9}, Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;->reportContextSensitivity(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IIILgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;)V

    goto :goto_0

    :cond_1
    return-void
.end method
