.class public Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;
.super Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Operator;
.source "SemanticContext.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "AND"
.end annotation


# instance fields
.field public final opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;)V
    .locals 2

    .line 209
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Operator;-><init>()V

    .line 210
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 211
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;

    if-eqz v1, :cond_0

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 212
    :cond_0
    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 213
    :goto_0
    instance-of p1, p2, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;

    if-eqz p1, :cond_1

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;

    iget-object p1, p2, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    invoke-static {p1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    invoke-interface {v0, p1}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    goto :goto_1

    .line 214
    :cond_1
    invoke-interface {v0, p2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 216
    :goto_1
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;->access$000(Ljava/util/Collection;)Ljava/util/List;

    move-result-object p1

    .line 217
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result p2

    if-nez p2, :cond_2

    .line 219
    invoke-static {p1}, Ljava/util/Collections;->min(Ljava/util/Collection;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$PrecedencePredicate;

    .line 220
    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 223
    :cond_2
    invoke-interface {v0}, Ljava/util/Set;->size()I

    move-result p1

    new-array p1, p1, [Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    invoke-interface {v0, p1}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 1

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    return p1

    .line 234
    :cond_0
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;

    if-nez v0, :cond_1

    const/4 p1, 0x0

    return p1

    .line 235
    :cond_1
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;

    .line 236
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    invoke-static {v0, p1}, Ljava/util/Arrays;->equals([Ljava/lang/Object;[Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public eval(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Z
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "TT;*>;",
            "Lgroovyjarjarantlr4/v4/runtime/RuleContext;",
            ")Z"
        }
    .end annotation

    .line 253
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 254
    invoke-virtual {v4, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;->eval(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Z

    move-result v4

    if-nez v4, :cond_0

    return v2

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x1

    return p1
.end method

.method public evalPrecedence(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "**>;",
            "Lgroovyjarjarantlr4/v4/runtime/RuleContext;",
            ")",
            "Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;"
        }
    .end annotation

    .line 262
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 263
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    array-length v2, v1

    const/4 v3, 0x0

    move v4, v3

    move v5, v4

    :goto_0
    const/4 v6, 0x1

    if-ge v4, v2, :cond_3

    aget-object v7, v1, v4

    .line 264
    invoke-virtual {v7, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;->evalPrecedence(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    move-result-object v8

    if-eq v8, v7, :cond_0

    goto :goto_1

    :cond_0
    move v6, v3

    :goto_1
    or-int/2addr v5, v6

    if-nez v8, :cond_1

    const/4 p1, 0x0

    return-object p1

    .line 270
    :cond_1
    sget-object v6, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->NONE:Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    if-eq v8, v6, :cond_2

    .line 272
    invoke-interface {v0, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_2
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_3
    if-nez v5, :cond_4

    return-object p0

    .line 280
    :cond_4
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_5

    .line 282
    sget-object p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->NONE:Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    return-object p1

    .line 285
    :cond_5
    invoke-interface {v0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    .line 286
    :goto_2
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result p2

    if-ge v6, p2, :cond_6

    .line 287
    invoke-interface {v0, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    invoke-static {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;->and(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;)Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    move-result-object p1

    add-int/lit8 v6, v6, 0x1

    goto :goto_2

    :cond_6
    return-object p1
.end method

.method public getOperands()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;",
            ">;"
        }
    .end annotation

    .line 228
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public hashCode()I
    .locals 2

    .line 241
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    const-class v1, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->hashCode([Ljava/lang/Object;I)I

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 295
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$AND;->opnds:[Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    const-string v1, "&&"

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->join([Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
