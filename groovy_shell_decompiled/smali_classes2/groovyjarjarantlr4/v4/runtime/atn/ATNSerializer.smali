.class public Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;
.super Ljava/lang/Object;
.source "ATNSerializer.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$CodePointSerializer;
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field public atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

.field private ruleNames:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private tokenNames:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 34
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 36
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 37
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->ruleNames:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 40
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 42
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 43
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->ruleNames:Ljava/util/List;

    .line 44
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->tokenNames:Ljava/util/List;

    return-void
.end method

.method static synthetic access$000(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;I)V
    .locals 0

    .line 25
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serializeInt(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;I)V

    return-void
.end method

.method private appendSets(Ljava/lang/StringBuilder;[CIIILgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;)I
    .locals 8

    const/4 v0, 0x0

    move v1, v0

    :goto_0
    if-ge v1, p4, :cond_5

    add-int/lit8 v2, p3, 0x1

    .line 560
    aget-char p3, p2, p3

    invoke-static {p3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result p3

    add-int v3, v1, p5

    .line 561
    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ":"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v3, v2, 0x1

    .line 562
    aget-char v2, p2, v2

    if-eqz v2, :cond_0

    const/4 v2, 0x1

    goto :goto_1

    :cond_0
    move v2, v0

    :goto_1
    if-eqz v2, :cond_1

    const/4 v4, -0x1

    .line 564
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getTokenName(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_1
    move v4, v0

    :goto_2
    if-ge v4, p3, :cond_4

    if-nez v2, :cond_2

    if-lez v4, :cond_3

    :cond_2
    const-string v5, ", "

    .line 569
    invoke-virtual {p1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 572
    :cond_3
    invoke-interface {p6, p2, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;->readUnicode([CI)I

    move-result v5

    .line 573
    invoke-interface {p6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;->size()I

    move-result v6

    add-int/2addr v3, v6

    .line 574
    invoke-interface {p6, p2, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;->readUnicode([CI)I

    move-result v6

    .line 575
    invoke-interface {p6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;->size()I

    move-result v7

    add-int/2addr v3, v7

    .line 576
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getTokenName(I)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    const-string v7, ".."

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getTokenName(I)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v4, v4, 0x1

    goto :goto_2

    :cond_4
    const-string p3, "\n"

    .line 578
    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    move p3, v3

    goto :goto_0

    :cond_5
    return p3
.end method

.method public static getDecoded(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;Ljava/util/List;)Ljava/lang/String;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 638
    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getSerialized(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;

    move-result-object v0

    .line 639
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->toCharArray(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;)[C

    move-result-object v0

    .line 640
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;

    invoke-direct {v1, p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;Ljava/util/List;)V

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->decode([C)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getSerialized(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;"
        }
    .end annotation

    .line 630
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;

    invoke-direct {v0, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)V

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serialize()Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;

    move-result-object p0

    return-object p0
.end method

.method public static getSerializedAsChars(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)[C
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)[C"
        }
    .end annotation

    .line 634
    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getSerialized(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;

    move-result-object p0

    invoke-static {p0}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->toCharArray(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;)[C

    move-result-object p0

    return-object p0
.end method

.method public static getSerializedAsString(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)Ljava/lang/String;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 626
    new-instance v0, Ljava/lang/String;

    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getSerializedAsChars(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)[C

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/lang/String;-><init>([C)V

    return-object v0
.end method

.method private serializeInt(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;I)V
    .locals 1

    int-to-char v0, p2

    .line 654
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    shr-int/lit8 p2, p2, 0x10

    int-to-char p2, p2

    .line 655
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    return-void
.end method

.method private serializeLong(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;J)V
    .locals 1

    long-to-int v0, p2

    .line 649
    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serializeInt(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;I)V

    const/16 v0, 0x20

    shr-long/2addr p2, v0

    long-to-int p2, p2

    .line 650
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serializeInt(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;I)V

    return-void
.end method

.method private static serializeSets(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$CodePointSerializer;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$CodePointSerializer;",
            ")V"
        }
    .end annotation

    .line 400
    invoke-interface {p1}, Ljava/util/Collection;->size()I

    move-result v0

    .line 401
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 403
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v1, -0x1

    .line 404
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v2

    const/4 v3, 0x0

    if-eqz v2, :cond_1

    .line 405
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->getIntervals()Ljava/util/List;

    move-result-object v4

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    iget v4, v4, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-ne v4, v1, :cond_1

    .line 406
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->getIntervals()Ljava/util/List;

    move-result-object v4

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    add-int/lit8 v4, v4, -0x1

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_0

    .line 409
    :cond_1
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->getIntervals()Ljava/util/List;

    move-result-object v4

    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 412
    :goto_0
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 413
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->getIntervals()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    .line 414
    iget v4, v2, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-ne v4, v1, :cond_3

    .line 415
    iget v4, v2, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-ne v4, v1, :cond_2

    goto :goto_1

    .line 419
    :cond_2
    invoke-interface {p2, p0, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$CodePointSerializer;->serializeCodePoint(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;I)V

    goto :goto_2

    .line 423
    :cond_3
    iget v4, v2, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    invoke-interface {p2, p0, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$CodePointSerializer;->serializeCodePoint(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;I)V

    .line 426
    :goto_2
    iget v2, v2, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    invoke-interface {p2, p0, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$CodePointSerializer;->serializeCodePoint(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;I)V

    goto :goto_1

    :cond_4
    return-void
.end method

.method private serializeUUID(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;Ljava/util/UUID;)V
    .locals 2

    .line 644
    invoke-virtual {p2}, Ljava/util/UUID;->getLeastSignificantBits()J

    move-result-wide v0

    invoke-direct {p0, p1, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serializeLong(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;J)V

    .line 645
    invoke-virtual {p2}, Ljava/util/UUID;->getMostSignificantBits()J

    move-result-wide v0

    invoke-direct {p0, p1, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serializeLong(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;J)V

    return-void
.end method


# virtual methods
.method public decode([C)Ljava/lang/String;
    .locals 17

    move-object/from16 v7, p0

    .line 432
    invoke-virtual/range {p1 .. p1}, [C->clone()Ljava/lang/Object;

    move-result-object v0

    move-object v8, v0

    check-cast v8, [C

    const/4 v0, 0x1

    move v1, v0

    .line 434
    :goto_0
    array-length v2, v8

    const/4 v3, 0x2

    if-ge v1, v2, :cond_0

    .line 435
    aget-char v2, v8, v1

    sub-int/2addr v2, v3

    int-to-char v2, v2

    aput-char v2, v8, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 438
    :cond_0
    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v10, 0x0

    .line 440
    aget-char v1, v8, v10

    invoke-static {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v1

    .line 441
    sget v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->SERIALIZED_VERSION:I

    if-ne v1, v2, :cond_11

    .line 446
    invoke-static {v8, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toUUID([CI)Ljava/util/UUID;

    move-result-object v1

    .line 448
    sget-object v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->SERIALIZED_UUID:Ljava/util/UUID;

    invoke-virtual {v1, v2}, Ljava/util/UUID;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_10

    const/16 v0, 0xb

    const/16 v1, 0xa

    .line 454
    aget-char v2, v8, v1

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v2

    const-string v3, "max type "

    .line 455
    invoke-virtual {v9, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v11, "\n"

    invoke-virtual {v2, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 456
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v0

    const/16 v2, 0xc

    move v4, v2

    move v3, v10

    :goto_1
    const-string v12, ":"

    const-string v13, " "

    if-ge v3, v0, :cond_6

    add-int/lit8 v5, v4, 0x1

    .line 458
    aget-char v4, v8, v4

    invoke-static {v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v4

    if-nez v4, :cond_1

    move v4, v5

    goto/16 :goto_4

    :cond_1
    add-int/lit8 v6, v5, 0x1

    .line 460
    aget-char v5, v8, v5

    invoke-static {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v5

    const v14, 0xffff

    if-ne v5, v14, :cond_2

    const/4 v5, -0x1

    :cond_2
    if-ne v4, v2, :cond_3

    add-int/lit8 v14, v6, 0x1

    .line 467
    aget-char v6, v8, v6

    invoke-static {v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v6

    .line 468
    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v15, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v15

    invoke-virtual {v15, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    goto :goto_3

    :cond_3
    const/4 v14, 0x4

    if-eq v4, v14, :cond_5

    const/4 v14, 0x5

    if-eq v4, v14, :cond_5

    const/4 v14, 0x3

    if-ne v4, v14, :cond_4

    goto :goto_2

    :cond_4
    const-string v14, ""

    move-object/from16 v16, v14

    move v14, v6

    move-object/from16 v6, v16

    goto :goto_3

    :cond_5
    :goto_2
    add-int/lit8 v14, v6, 0x1

    .line 471
    aget-char v6, v8, v6

    invoke-static {v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v6

    .line 472
    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v15, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v15

    invoke-virtual {v15, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 474
    :goto_3
    invoke-virtual {v9, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v15

    invoke-virtual {v15, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v12

    sget-object v15, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->serializationNames:Ljava/util/List;

    invoke-interface {v15, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-virtual {v12, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v4, v14

    :goto_4
    add-int/lit8 v3, v3, 0x1

    goto/16 :goto_1

    :cond_6
    add-int/lit8 v0, v4, 0x1

    .line 485
    aget-char v2, v8, v4

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v2

    move v3, v10

    :goto_5
    if-ge v3, v2, :cond_7

    add-int/lit8 v4, v0, 0x1

    .line 487
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    add-int/lit8 v3, v3, 0x1

    move v0, v4

    goto :goto_5

    :cond_7
    add-int/lit8 v2, v0, 0x1

    .line 490
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v0

    move v3, v10

    :goto_6
    if-ge v3, v0, :cond_8

    add-int/lit8 v4, v2, 0x1

    .line 492
    aget-char v2, v8, v2

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    add-int/lit8 v3, v3, 0x1

    move v2, v4

    goto :goto_6

    :cond_8
    add-int/lit8 v0, v2, 0x1

    .line 495
    aget-char v2, v8, v2

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v2

    move v3, v10

    :goto_7
    if-ge v3, v2, :cond_9

    add-int/lit8 v4, v0, 0x1

    .line 497
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    add-int/lit8 v3, v3, 0x1

    move v0, v4

    goto :goto_7

    :cond_9
    add-int/lit8 v2, v0, 0x1

    .line 501
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v0

    move v3, v10

    :goto_8
    if-ge v3, v0, :cond_b

    add-int/lit8 v4, v2, 0x1

    .line 503
    aget-char v2, v8, v2

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v2

    add-int/lit8 v5, v4, 0x1

    .line 505
    aget-char v4, v8, v4

    invoke-static {v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    .line 506
    iget-object v4, v7, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    sget-object v6, Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;->LEXER:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    const-string v14, "rule "

    if-ne v4, v6, :cond_a

    add-int/lit8 v4, v5, 0x1

    .line 507
    aget-char v5, v8, v5

    invoke-static {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v5

    .line 508
    invoke-virtual {v9, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move v2, v4

    goto :goto_9

    .line 511
    :cond_a
    invoke-virtual {v9, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move v2, v5

    :goto_9
    add-int/lit8 v3, v3, 0x1

    goto :goto_8

    :cond_b
    add-int/lit8 v0, v2, 0x1

    .line 514
    aget-char v2, v8, v2

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v2

    move v3, v10

    :goto_a
    if-ge v3, v2, :cond_c

    add-int/lit8 v4, v0, 0x1

    .line 516
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v0

    const-string v5, "mode "

    .line 517
    invoke-virtual {v9, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v3, v3, 0x1

    move v0, v4

    goto :goto_a

    :cond_c
    add-int/lit8 v3, v0, 0x1

    .line 519
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v14

    const/4 v5, 0x0

    .line 520
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializingMode;->UNICODE_BMP:Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializingMode;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->getUnicodeDeserializer(Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializingMode;)Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;

    move-result-object v6

    move-object/from16 v0, p0

    move-object v1, v9

    move-object v2, v8

    move v4, v14

    invoke-direct/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->appendSets(Ljava/lang/StringBuilder;[CIIILgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;)I

    move-result v0

    add-int/lit8 v3, v0, 0x1

    .line 521
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v4

    .line 522
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializingMode;->UNICODE_SMP:Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializingMode;

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->getUnicodeDeserializer(Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializingMode;)Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;

    move-result-object v6

    move-object/from16 v0, p0

    move v5, v14

    invoke-direct/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->appendSets(Ljava/lang/StringBuilder;[CIIILgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;)I

    move-result v0

    add-int/lit8 v1, v0, 0x1

    .line 523
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v0

    move v2, v10

    :goto_b
    if-ge v2, v0, :cond_d

    .line 525
    aget-char v3, v8, v1

    invoke-static {v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v3

    add-int/lit8 v4, v1, 0x1

    .line 526
    aget-char v4, v8, v4

    invoke-static {v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v4

    add-int/lit8 v5, v1, 0x2

    .line 527
    aget-char v5, v8, v5

    invoke-static {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v5

    add-int/lit8 v6, v1, 0x3

    .line 528
    aget-char v6, v8, v6

    invoke-static {v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v6

    add-int/lit8 v14, v1, 0x4

    .line 529
    aget-char v14, v8, v14

    invoke-static {v14}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v14

    add-int/lit8 v15, v1, 0x5

    .line 530
    aget-char v15, v8, v15

    invoke-static {v15}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v15

    .line 531
    invoke-virtual {v9, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v10, "->"

    invoke-virtual {v3, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    sget-object v4, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->serializationNames:Ljava/util/List;

    invoke-interface {v4, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ","

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x6

    add-int/lit8 v2, v2, 0x1

    const/4 v10, 0x0

    goto :goto_b

    :cond_d
    add-int/lit8 v0, v1, 0x1

    .line 537
    aget-char v1, v8, v1

    invoke-static {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v1

    move v2, v0

    const/4 v0, 0x0

    :goto_c
    if-ge v0, v1, :cond_e

    add-int/lit8 v3, v2, 0x1

    .line 539
    aget-char v2, v8, v2

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v2

    .line 540
    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v0, v0, 0x1

    move v2, v3

    goto :goto_c

    .line 542
    :cond_e
    iget-object v0, v7, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;->LEXER:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    if-ne v0, v1, :cond_f

    add-int/lit8 v0, v2, 0x1

    .line 548
    aget-char v1, v8, v2

    invoke-static {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v1

    const/4 v10, 0x0

    :goto_d
    if-ge v10, v1, :cond_f

    .line 550
    invoke-static {}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;->values()[Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;

    move-result-object v2

    add-int/lit8 v3, v0, 0x1

    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result v0

    aget-object v0, v2, v0

    add-int/lit8 v0, v3, 0x1

    .line 551
    aget-char v2, v8, v3

    invoke-static {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    add-int/lit8 v2, v0, 0x1

    .line 552
    aget-char v0, v8, v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    add-int/lit8 v10, v10, 0x1

    move v0, v2

    goto :goto_d

    .line 555
    :cond_f
    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 449
    :cond_10
    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    move-result-object v2

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object v1, v3, v4

    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->SERIALIZED_UUID:Ljava/util/UUID;

    aput-object v1, v3, v0

    const-string v0, "Could not deserialize ATN with UUID %s (expected %s)."

    invoke-static {v2, v0, v3}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    .line 450
    new-instance v1, Ljava/lang/UnsupportedOperationException;

    new-instance v2, Ljava/io/InvalidClassException;

    const-class v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v2, v3, v0}, Ljava/io/InvalidClassException;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-direct {v1, v2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/Throwable;)V

    throw v1

    :cond_11
    new-array v2, v3, [Ljava/lang/Object;

    .line 442
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v3, 0x0

    aput-object v1, v2, v3

    sget v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->SERIALIZED_VERSION:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    aput-object v1, v2, v0

    const-string v0, "Could not deserialize ATN with version %d (expected %d)."

    invoke-static {v0, v2}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    .line 443
    new-instance v1, Ljava/lang/UnsupportedOperationException;

    new-instance v2, Ljava/io/InvalidClassException;

    const-class v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v2, v3, v0}, Ljava/io/InvalidClassException;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-direct {v1, v2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/Throwable;)V

    throw v1
.end method

.method public getTokenName(I)Ljava/lang/String;
    .locals 4

    const/4 v0, -0x1

    if-ne p1, v0, :cond_0

    const-string p1, "EOF"

    return-object p1

    .line 586
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;->LEXER:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    if-ne v0, v1, :cond_6

    if-ltz p1, :cond_6

    const v0, 0xffff

    if-gt p1, v0, :cond_6

    const/16 v0, 0xc

    if-eq p1, v0, :cond_5

    const/16 v0, 0xd

    if-eq p1, v0, :cond_4

    const/16 v0, 0x27

    if-eq p1, v0, :cond_3

    const/16 v1, 0x5c

    if-eq p1, v1, :cond_2

    packed-switch p1, :pswitch_data_0

    int-to-char v1, p1

    .line 605
    invoke-static {v1}, Ljava/lang/Character$UnicodeBlock;->of(C)Ljava/lang/Character$UnicodeBlock;

    move-result-object v2

    sget-object v3, Ljava/lang/Character$UnicodeBlock;->BASIC_LATIN:Ljava/lang/Character$UnicodeBlock;

    if-ne v2, v3, :cond_1

    invoke-static {v1}, Ljava/lang/Character;->isISOControl(C)Z

    move-result v2

    if-nez v2, :cond_1

    .line 607
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-static {v1}, Ljava/lang/Character;->toString(C)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_1
    const/high16 v0, 0x10000

    or-int/2addr p1, v0

    .line 611
    invoke-static {p1}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/String;->toUpperCase()Ljava/lang/String;

    move-result-object p1

    const/4 v0, 0x1

    const/4 v1, 0x5

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p1

    .line 612
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "\'\\u"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "\'"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    :pswitch_0
    const-string p1, "\'\\n\'"

    return-object p1

    :pswitch_1
    const-string p1, "\'\\t\'"

    return-object p1

    :pswitch_2
    const-string p1, "\'\\b\'"

    return-object p1

    :cond_2
    const-string p1, "\'\\\\\'"

    return-object p1

    :cond_3
    const-string p1, "\'\\\'\'"

    return-object p1

    :cond_4
    const-string p1, "\'\\r\'"

    return-object p1

    :cond_5
    const-string p1, "\'\\f\'"

    return-object p1

    .line 617
    :cond_6
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->tokenNames:Ljava/util/List;

    if-eqz v0, :cond_7

    if-ltz p1, :cond_7

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_7

    .line 618
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->tokenNames:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    return-object p1

    .line 621
    :cond_7
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    return-object p1

    nop

    :pswitch_data_0
    .packed-switch 0x8
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public serialize()Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;
    .locals 17

    move-object/from16 v0, p0

    .line 71
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;-><init>()V

    .line 72
    sget v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->SERIALIZED_VERSION:I

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 73
    sget-object v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->SERIALIZED_UUID:Ljava/util/UUID;

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serializeUUID(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;Ljava/util/UUID;)V

    .line 76
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;->ordinal()I

    move-result v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 77
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->maxTokenType:I

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 83
    new-instance v2, Ljava/util/LinkedHashMap;

    invoke-direct {v2}, Ljava/util/LinkedHashMap;-><init>()V

    .line 86
    new-instance v3, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;

    invoke-direct {v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;-><init>()V

    .line 87
    new-instance v4, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;

    invoke-direct {v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;-><init>()V

    .line 88
    new-instance v5, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;

    invoke-direct {v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;-><init>()V

    .line 89
    iget-object v6, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 90
    iget-object v6, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v6}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v6

    const/4 v7, 0x0

    move v8, v7

    :goto_0
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v9

    const/4 v10, 0x7

    const/4 v11, -0x1

    const v12, 0xffff

    const/4 v13, 0x1

    if-eqz v9, :cond_b

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    if-nez v9, :cond_0

    .line 92
    invoke-virtual {v1, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_0

    .line 96
    :cond_0
    invoke-virtual {v9}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getStateType()I

    move-result v14

    .line 97
    instance-of v15, v9, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    if-eqz v15, :cond_2

    .line 98
    move-object v15, v9

    check-cast v15, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    .line 99
    iget-boolean v7, v15, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->nonGreedy:Z

    if-eqz v7, :cond_1

    .line 100
    iget v7, v9, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v3, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 103
    :cond_1
    iget-boolean v7, v15, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->sll:Z

    if-eqz v7, :cond_2

    .line 104
    iget v7, v9, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v4, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 108
    :cond_2
    instance-of v7, v9, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    if-eqz v7, :cond_3

    move-object v7, v9

    check-cast v7, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    iget-boolean v7, v7, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->isPrecedenceRule:Z

    if-eqz v7, :cond_3

    .line 109
    iget v7, v9, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v5, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 112
    :cond_3
    invoke-virtual {v1, v14}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 114
    iget v7, v9, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    if-ne v7, v11, :cond_4

    .line 115
    invoke-virtual {v1, v12}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_1

    .line 118
    :cond_4
    iget v7, v9, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    invoke-virtual {v1, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 121
    :goto_1
    invoke-virtual {v9}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getStateType()I

    move-result v7

    const/16 v11, 0xc

    if-ne v7, v11, :cond_5

    .line 122
    move-object v7, v9

    check-cast v7, Lgroovyjarjarantlr4/v4/runtime/atn/LoopEndState;

    iget-object v7, v7, Lgroovyjarjarantlr4/v4/runtime/atn/LoopEndState;->loopBackState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v7, v7, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v1, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_2

    .line 124
    :cond_5
    instance-of v7, v9, Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;

    if-eqz v7, :cond_6

    .line 125
    move-object v7, v9

    check-cast v7, Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;

    iget-object v7, v7, Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;->endState:Lgroovyjarjarantlr4/v4/runtime/atn/BlockEndState;

    iget v7, v7, Lgroovyjarjarantlr4/v4/runtime/atn/BlockEndState;->stateNumber:I

    invoke-virtual {v1, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 128
    :cond_6
    :goto_2
    invoke-virtual {v9}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getStateType()I

    move-result v7

    if-eq v7, v10, :cond_7

    .line 130
    invoke-virtual {v9}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v7

    add-int/2addr v8, v7

    :cond_7
    const/4 v7, 0x0

    .line 133
    :goto_3
    invoke-virtual {v9}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v11

    if-ge v7, v11, :cond_a

    .line 134
    invoke-virtual {v9, v7}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v11

    .line 135
    sget-object v12, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->serializationTypes:Ljava/util/Map;

    invoke-virtual {v11}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v14

    invoke-interface {v12, v14}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v12

    check-cast v12, Ljava/lang/Integer;

    invoke-virtual {v12}, Ljava/lang/Integer;->intValue()I

    move-result v12

    if-eq v12, v10, :cond_8

    const/16 v14, 0x8

    if-ne v12, v14, :cond_9

    .line 137
    :cond_8
    check-cast v11, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;

    .line 138
    iget-object v11, v11, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;->set:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-static {v13}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v12

    invoke-interface {v2, v11, v12}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_9
    add-int/lit8 v7, v7, 0x1

    goto :goto_3

    :cond_a
    const/4 v7, 0x0

    goto/16 :goto_0

    .line 144
    :cond_b
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->size()I

    move-result v6

    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    const/4 v6, 0x0

    .line 145
    :goto_4
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->size()I

    move-result v7

    if-ge v6, v7, :cond_c

    .line 146
    invoke-virtual {v3, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->get(I)I

    move-result v7

    invoke-virtual {v1, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    add-int/lit8 v6, v6, 0x1

    goto :goto_4

    .line 150
    :cond_c
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->size()I

    move-result v3

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    const/4 v3, 0x0

    .line 151
    :goto_5
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->size()I

    move-result v6

    if-ge v3, v6, :cond_d

    .line 152
    invoke-virtual {v4, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->get(I)I

    move-result v6

    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_5

    .line 156
    :cond_d
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->size()I

    move-result v3

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    const/4 v3, 0x0

    .line 157
    :goto_6
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->size()I

    move-result v4

    if-ge v3, v4, :cond_e

    .line 158
    invoke-virtual {v5, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->get(I)I

    move-result v4

    invoke-virtual {v1, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_6

    .line 161
    :cond_e
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    array-length v3, v3

    .line 162
    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    const/4 v4, 0x0

    :goto_7
    if-ge v4, v3, :cond_12

    .line 164
    iget-object v5, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    aget-object v5, v5, v4

    .line 165
    iget v6, v5, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 166
    iget-object v6, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->ruleNames:Ljava/util/List;

    iget v5, v5, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    invoke-interface {v6, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    const/16 v6, 0x24

    invoke-virtual {v5, v6}, Ljava/lang/String;->indexOf(I)I

    move-result v5

    if-ltz v5, :cond_f

    move v5, v13

    goto :goto_8

    :cond_f
    const/4 v5, 0x0

    .line 167
    :goto_8
    invoke-virtual {v1, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 168
    iget-object v5, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    sget-object v6, Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;->LEXER:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    if-ne v5, v6, :cond_11

    .line 169
    iget-object v5, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToTokenType:[I

    aget v5, v5, v4

    if-ne v5, v11, :cond_10

    .line 170
    invoke-virtual {v1, v12}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_9

    .line 173
    :cond_10
    iget-object v5, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToTokenType:[I

    aget v5, v5, v4

    invoke-virtual {v1, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    :cond_11
    :goto_9
    add-int/lit8 v4, v4, 0x1

    goto :goto_7

    .line 178
    :cond_12
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToStartState:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    .line 179
    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    if-lez v3, :cond_13

    .line 181
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToStartState:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_a
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_13

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 182
    iget v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v1, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_a

    .line 185
    :cond_13
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 186
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 187
    invoke-interface {v2}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_b
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_15

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    .line 188
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->getMaxElement()I

    move-result v6

    if-gt v6, v12, :cond_14

    .line 189
    invoke-interface {v3, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_b

    .line 192
    :cond_14
    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_b

    .line 195
    :cond_15
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$1;

    invoke-direct {v2, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$1;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;)V

    invoke-static {v1, v3, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serializeSets(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$CodePointSerializer;)V

    .line 204
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$2;

    invoke-direct {v2, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$2;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;)V

    invoke-static {v1, v4, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serializeSets(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$CodePointSerializer;)V

    .line 213
    new-instance v2, Ljava/util/HashMap;

    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    .line 215
    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    const/4 v5, 0x0

    :goto_c
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_16

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    add-int/lit8 v7, v5, 0x1

    .line 216
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v2, v6, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move v5, v7

    goto :goto_c

    .line 218
    :cond_16
    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_d
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_17

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    add-int/lit8 v6, v5, 0x1

    .line 219
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-interface {v2, v4, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move v5, v6

    goto :goto_d

    .line 222
    :cond_17
    invoke-virtual {v1, v8}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 223
    iget-object v3, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_18
    :goto_e
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_1f

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    if-nez v4, :cond_19

    goto :goto_e

    .line 229
    :cond_19
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getStateType()I

    move-result v5

    if-ne v5, v10, :cond_1a

    goto :goto_e

    :cond_1a
    const/4 v5, 0x0

    .line 233
    :goto_f
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v6

    if-ge v5, v6, :cond_18

    .line 234
    invoke-virtual {v4, v5}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v6

    .line 236
    iget-object v7, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v7, v7, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    iget-object v8, v6, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v8, v8, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-interface {v7, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    if-eqz v7, :cond_1e

    .line 240
    iget v7, v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    .line 241
    iget-object v8, v6, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v8, v8, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    .line 242
    sget-object v9, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->serializationTypes:Ljava/util/Map;

    invoke-virtual {v6}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v14

    invoke-interface {v9, v14}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Ljava/lang/Integer;

    invoke-virtual {v9}, Ljava/lang/Integer;->intValue()I

    move-result v9

    packed-switch v9, :pswitch_data_0

    :pswitch_0
    const/4 v6, 0x0

    :cond_1b
    :goto_10
    const/4 v14, 0x0

    :goto_11
    const/4 v15, 0x0

    goto/16 :goto_13

    .line 254
    :pswitch_1
    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/PrecedencePredicateTransition;

    .line 255
    iget v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/PrecedencePredicateTransition;->precedence:I

    goto :goto_10

    .line 294
    :pswitch_2
    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;->set:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-interface {v2, v6}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/Integer;

    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    move-result v6

    goto :goto_10

    .line 291
    :pswitch_3
    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;

    iget-object v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/SetTransition;->set:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    invoke-interface {v2, v6}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/Integer;

    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    move-result v6

    goto :goto_10

    .line 281
    :pswitch_4
    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;

    .line 282
    iget v14, v6, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->ruleIndex:I

    .line 283
    iget v15, v6, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->actionIndex:I

    if-ne v15, v11, :cond_1c

    move v15, v12

    .line 288
    :cond_1c
    iget-boolean v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->isCtxDependent:Z

    goto :goto_12

    .line 273
    :pswitch_5
    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;

    iget v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    if-ne v6, v11, :cond_1b

    move v14, v13

    const/4 v6, 0x0

    goto :goto_11

    .line 258
    :pswitch_6
    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;

    .line 259
    iget v14, v6, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;->ruleIndex:I

    .line 260
    iget v15, v6, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;->predIndex:I

    .line 261
    iget-boolean v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;->isCtxDependent:Z

    goto :goto_12

    .line 248
    :pswitch_7
    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    iget-object v8, v6, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v8, v8, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    .line 249
    iget-object v14, v6, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v14, v14, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    .line 250
    iget v15, v6, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->ruleIndex:I

    .line 251
    iget v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->precedence:I

    :goto_12
    move/from16 v16, v14

    move v14, v6

    move/from16 v6, v16

    goto :goto_13

    .line 264
    :pswitch_8
    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/RangeTransition;

    iget v14, v6, Lgroovyjarjarantlr4/v4/runtime/atn/RangeTransition;->from:I

    .line 265
    iget v6, v6, Lgroovyjarjarantlr4/v4/runtime/atn/RangeTransition;->to:I

    move v15, v6

    if-ne v14, v11, :cond_1d

    move v14, v13

    const/4 v6, 0x0

    goto :goto_13

    :cond_1d
    move v6, v14

    const/4 v14, 0x0

    .line 300
    :goto_13
    invoke-virtual {v1, v7}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 301
    invoke-virtual {v1, v8}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 302
    invoke-virtual {v1, v9}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 303
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 304
    invoke-virtual {v1, v15}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 305
    invoke-virtual {v1, v14}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    add-int/lit8 v5, v5, 0x1

    goto/16 :goto_f

    .line 237
    :cond_1e
    new-instance v1, Ljava/lang/IllegalStateException;

    const-string v2, "Cannot serialize a transition to a removed state."

    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v1

    .line 309
    :cond_1f
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    .line 310
    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 311
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_14
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_20

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    .line 312
    iget v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->stateNumber:I

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_14

    .line 318
    :cond_20
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    sget-object v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;->LEXER:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    if-ne v2, v3, :cond_27

    .line 319
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->lexerActions:[Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    array-length v2, v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 320
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->lexerActions:[Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    array-length v3, v2

    const/4 v4, 0x0

    :goto_15
    if-ge v4, v3, :cond_27

    aget-object v5, v2, v4

    .line 321
    invoke-interface {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;->getActionType()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;

    move-result-object v6

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;->ordinal()I

    move-result v6

    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 322
    sget-object v6, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$3;->$SwitchMap$org$antlr$v4$runtime$atn$LexerActionType:[I

    invoke-interface {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;->getActionType()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;

    move-result-object v7

    invoke-virtual {v7}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;->ordinal()I

    move-result v7

    aget v6, v6, v7

    packed-switch v6, :pswitch_data_1

    const/4 v6, 0x0

    .line 370
    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    move-result-object v1

    new-array v2, v13, [Ljava/lang/Object;

    invoke-interface {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;->getActionType()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;

    move-result-object v3

    aput-object v3, v2, v6

    const-string v3, "The specified lexer action type %s is not valid."

    invoke-static {v1, v3, v2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    .line 371
    new-instance v2, Ljava/lang/IllegalArgumentException;

    invoke-direct {v2, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v2

    .line 364
    :pswitch_9
    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/atn/LexerTypeAction;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerTypeAction;->getType()I

    move-result v5

    if-eq v5, v11, :cond_21

    goto :goto_16

    :cond_21
    move v5, v12

    .line 365
    :goto_16
    invoke-virtual {v1, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    const/4 v6, 0x0

    .line 366
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto/16 :goto_1c

    :pswitch_a
    const/4 v6, 0x0

    .line 359
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 360
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto/16 :goto_1c

    :pswitch_b
    const/4 v6, 0x0

    .line 353
    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/atn/LexerPushModeAction;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerPushModeAction;->getMode()I

    move-result v5

    if-eq v5, v11, :cond_22

    goto :goto_17

    :cond_22
    move v5, v12

    .line 354
    :goto_17
    invoke-virtual {v1, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 355
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_1c

    :pswitch_c
    const/4 v6, 0x0

    .line 348
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 349
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_1c

    :pswitch_d
    const/4 v6, 0x0

    .line 343
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 344
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_1c

    :pswitch_e
    const/4 v6, 0x0

    .line 337
    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/atn/LexerModeAction;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerModeAction;->getMode()I

    move-result v5

    if-eq v5, v11, :cond_23

    goto :goto_18

    :cond_23
    move v5, v12

    .line 338
    :goto_18
    invoke-virtual {v1, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    .line 339
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    goto :goto_1c

    .line 330
    :pswitch_f
    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/atn/LexerCustomAction;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerCustomAction;->getRuleIndex()I

    move-result v6

    .line 331
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerCustomAction;->getActionIndex()I

    move-result v5

    if-eq v6, v11, :cond_24

    goto :goto_19

    :cond_24
    move v6, v12

    .line 332
    :goto_19
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    if-eq v5, v11, :cond_25

    goto :goto_1a

    :cond_25
    move v5, v12

    .line 333
    :goto_1a
    invoke-virtual {v1, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    const/4 v6, 0x0

    goto :goto_1c

    .line 324
    :pswitch_10
    check-cast v5, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;->getChannel()I

    move-result v5

    if-eq v5, v11, :cond_26

    goto :goto_1b

    :cond_26
    move v5, v12

    .line 325
    :goto_1b
    invoke-virtual {v1, v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    const/4 v6, 0x0

    .line 326
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    :goto_1c
    add-int/lit8 v4, v4, 0x1

    goto/16 :goto_15

    .line 378
    :cond_27
    :goto_1d
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->size()I

    move-result v2

    if-ge v13, v2, :cond_29

    .line 379
    invoke-virtual {v1, v13}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->get(I)I

    move-result v2

    if-ltz v2, :cond_28

    invoke-virtual {v1, v13}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->get(I)I

    move-result v2

    if-gt v2, v12, :cond_28

    .line 388
    invoke-virtual {v1, v13}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->get(I)I

    move-result v2

    add-int/lit8 v2, v2, 0x2

    and-int/2addr v2, v12

    .line 389
    invoke-virtual {v1, v13, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->set(II)I

    add-int/lit8 v13, v13, 0x1

    goto :goto_1d

    .line 380
    :cond_28
    new-instance v2, Ljava/lang/UnsupportedOperationException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Serialized ATN data element "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v1, v13}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->get(I)I

    move-result v1

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " element "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v13}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " out of range "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const/4 v3, 0x0

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, ".."

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v12}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v2, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v2

    :cond_29
    return-object v1

    :pswitch_data_0
    .packed-switch 0x2
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_0
        :pswitch_1
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x1
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
    .end packed-switch
.end method
