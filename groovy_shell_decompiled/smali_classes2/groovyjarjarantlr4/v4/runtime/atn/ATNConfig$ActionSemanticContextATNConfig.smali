.class Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$ActionSemanticContextATNConfig;
.super Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$SemanticContextATNConfig;
.source "ATNConfig.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "ActionSemanticContextATNConfig"
.end annotation


# instance fields
.field private final lexerActionExecutor:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

.field private final passedThroughNonGreedyDecision:Z


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Z)V
    .locals 0

    .line 570
    invoke-direct {p0, p2, p3, p4, p5}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$SemanticContextATNConfig;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)V

    .line 571
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$ActionSemanticContextATNConfig;->lexerActionExecutor:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    .line 572
    iput-boolean p6, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$ActionSemanticContextATNConfig;->passedThroughNonGreedyDecision:Z

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;ILgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Z)V
    .locals 0

    .line 564
    invoke-direct {p0, p2, p3, p4, p5}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$SemanticContextATNConfig;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;ILgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)V

    .line 565
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$ActionSemanticContextATNConfig;->lexerActionExecutor:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    .line 566
    iput-boolean p6, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$ActionSemanticContextATNConfig;->passedThroughNonGreedyDecision:Z

    return-void
.end method


# virtual methods
.method public getLexerActionExecutor()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;
    .locals 1

    .line 577
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$ActionSemanticContextATNConfig;->lexerActionExecutor:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    return-object v0
.end method

.method public hasPassedThroughNonGreedyDecision()Z
    .locals 1

    .line 582
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig$ActionSemanticContextATNConfig;->passedThroughNonGreedyDecision:Z

    return v0
.end method
