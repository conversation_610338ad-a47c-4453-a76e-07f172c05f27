.class Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$1;
.super Ljava/lang/Object;
.source "ATNSerializer.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$CodePointSerializer;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->serialize()Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;)V
    .locals 0

    .line 198
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer$1;->this$0:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public serializeCodePoint(Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;I)V
    .locals 0

    .line 201
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->add(I)V

    return-void
.end method
