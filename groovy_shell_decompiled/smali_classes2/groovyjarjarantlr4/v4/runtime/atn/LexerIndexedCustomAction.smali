.class public final Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;
.super Ljava/lang/Object;
.source "LexerIndexedCustomAction.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;


# instance fields
.field private final action:Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

.field private final offset:I


# direct methods
.method public constructor <init>(ILgroovyjarjarantlr4/v4/runtime/atn/LexerAction;)V
    .locals 0

    .line 44
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 45
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->offset:I

    .line 46
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->action:Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 116
    :cond_0
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 120
    :cond_1
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;

    .line 121
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->offset:I

    iget v3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->offset:I

    if-ne v1, v3, :cond_2

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->action:Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->action:Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    invoke-virtual {v1, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public execute(Lgroovyjarjarantlr4/v4/runtime/Lexer;)V
    .locals 1

    .line 100
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->action:Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;->execute(Lgroovyjarjarantlr4/v4/runtime/Lexer;)V

    return-void
.end method

.method public getAction()Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;
    .locals 1

    .line 68
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->action:Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    return-object v0
.end method

.method public getActionType()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;
    .locals 1

    .line 79
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->action:Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;->getActionType()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;

    move-result-object v0

    return-object v0
.end method

.method public getOffset()I
    .locals 1

    .line 58
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->offset:I

    return v0
.end method

.method public hashCode()I
    .locals 2

    .line 105
    invoke-static {}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->initialize()I

    move-result v0

    .line 106
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->offset:I

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(II)I

    move-result v0

    .line 107
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerIndexedCustomAction;->action:Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(ILjava/lang/Object;)I

    move-result v0

    const/4 v1, 0x2

    .line 108
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->finish(II)I

    move-result v0

    return v0
.end method

.method public isPositionDependent()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
