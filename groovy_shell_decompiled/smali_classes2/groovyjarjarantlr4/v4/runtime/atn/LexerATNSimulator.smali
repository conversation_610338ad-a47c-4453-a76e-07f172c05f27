.class public Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;
.super Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;
.source "LexerATNSimulator.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z = false

.field public static final MAX_DFA_EDGE:I = 0x10ffff

.field public static final MIN_DFA_EDGE:I = 0x0

.field public static final debug:Z = false

.field public static final dfa_debug:Z = false

.field public static match_calls:I
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field protected charPositionInLine:I

.field protected line:I

.field protected mode:I

.field public optimize_tail_calls:Z

.field protected final prevAccept:Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;

.field protected final recog:Lgroovyjarjarantlr4/v4/runtime/Lexer;

.field protected startIndex:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/Lexer;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V
    .locals 1

    .line 94
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    const/4 p2, 0x1

    .line 32
    iput-boolean p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->optimize_tail_calls:Z

    const/4 v0, -0x1

    .line 71
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    .line 74
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    const/4 p2, 0x0

    .line 77
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    .line 79
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    .line 82
    new-instance p2, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;

    invoke-direct {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;-><init>()V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->prevAccept:Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;

    .line 95
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->recog:Lgroovyjarjarantlr4/v4/runtime/Lexer;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V
    .locals 1

    const/4 v0, 0x0

    .line 90
    invoke-direct {p0, v0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;-><init>(Lgroovyjarjarantlr4/v4/runtime/Lexer;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    return-void
.end method


# virtual methods
.method protected accept(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;IIII)V
    .locals 0

    .line 356
    invoke-interface {p1, p4}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->seek(I)V

    .line 357
    iput p5, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    .line 358
    iput p6, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    if-eqz p2, :cond_0

    .line 360
    iget-object p4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->recog:Lgroovyjarjarantlr4/v4/runtime/Lexer;

    if-eqz p4, :cond_0

    .line 361
    invoke-virtual {p2, p4, p1, p3}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;->execute(Lgroovyjarjarantlr4/v4/runtime/Lexer;Lgroovyjarjarantlr4/v4/runtime/CharStream;I)V

    :cond_0
    return-void
.end method

.method protected addDFAEdge(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;ILgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;
    .locals 1

    .line 639
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 641
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->clearExplicitSemanticContext()V

    .line 645
    :cond_0
    invoke-virtual {p0, p3}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->addDFAState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    move-result-object p3

    if-eqz v0, :cond_1

    return-object p3

    .line 651
    :cond_1
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->addDFAEdge(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;ILgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V

    return-object p3
.end method

.method protected addDFAEdge(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;ILgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V
    .locals 0

    if-eqz p1, :cond_0

    .line 661
    invoke-virtual {p1, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->setTarget(ILgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V

    :cond_0
    return-void
.end method

.method protected addDFAState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;
    .locals 4

    .line 677
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    aget-object v1, v1, v2

    invoke-direct {v0, v1, p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V

    .line 678
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    aget-object v1, v1, v2

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->states:Ljava/util/concurrent/ConcurrentMap;

    invoke-interface {v1, v0}, Ljava/util/concurrent/ConcurrentMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    if-eqz v0, :cond_0

    return-object v0

    .line 681
    :cond_0
    invoke-virtual {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->optimizeConfigs(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;)V

    .line 682
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    aget-object v1, v1, v2

    const/4 v2, 0x1

    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->clone(Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;-><init>(Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V

    const/4 v1, 0x0

    .line 685
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 686
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v3

    instance-of v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    if-eqz v3, :cond_1

    move-object v1, v2

    :cond_2
    if-eqz v1, :cond_3

    .line 693
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToTokenType:[I

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v2

    iget v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    aget p1, p1, v2

    .line 694
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getLexerActionExecutor()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    move-result-object v1

    .line 695
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;

    invoke-direct {v2, p1, v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;-><init>(ILgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;)V

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->setAcceptState(Lgroovyjarjarantlr4/v4/runtime/dfa/AcceptStateInfo;)V

    .line 698
    :cond_3
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    aget-object p1, p1, v1

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->addState(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    move-result-object p1

    return-object p1
.end method

.method protected captureSimState(Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V
    .locals 0

    .line 617
    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->index()I

    move-result p2

    iput p2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->index:I

    .line 618
    iget p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    iput p2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->line:I

    .line 619
    iget p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    iput p2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->charPos:I

    .line 620
    iput-object p3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->dfaState:Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    return-void
.end method

.method protected closure(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;ZZZ)Z
    .locals 13

    move-object v7, p2

    move-object/from16 v8, p3

    .line 403
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v0

    instance-of v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    const/4 v9, 0x0

    if-eqz v0, :cond_4

    .line 413
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v10

    .line 414
    invoke-virtual {v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    .line 415
    invoke-virtual {v8, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->add(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    return v1

    .line 418
    :cond_0
    invoke-virtual {v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->hasEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 419
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v0

    sget-object v2, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    invoke-virtual {p2, v0, v2, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v0

    invoke-virtual {v8, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->add(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    goto :goto_0

    :cond_1
    move/from16 v1, p4

    :goto_0
    move v4, v1

    move v11, v9

    .line 423
    :goto_1
    invoke-virtual {v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v0

    if-ge v11, v0, :cond_3

    .line 424
    invoke-virtual {v10, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v0

    const v1, 0x7fffffff

    if-ne v0, v1, :cond_2

    move-object v12, p0

    goto :goto_2

    .line 429
    :cond_2
    invoke-virtual {v10, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v1

    move-object v12, p0

    .line 430
    iget-object v2, v12, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 431
    invoke-virtual {p2, v0, v1, v9}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v2

    move-object v0, p0

    move-object v1, p1

    move-object/from16 v3, p3

    move/from16 v5, p5

    move/from16 v6, p6

    .line 432
    invoke-virtual/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->closure(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;ZZZ)Z

    move-result v4

    :goto_2
    add-int/lit8 v11, v11, 0x1

    goto :goto_1

    :cond_3
    move-object v12, p0

    return v4

    :cond_4
    move-object v12, p0

    .line 439
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->onlyHasEpsilonTransitions()Z

    move-result v0

    if-nez v0, :cond_6

    if-eqz p4, :cond_5

    .line 440
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->hasPassedThroughNonGreedyDecision()Z

    move-result v0

    if-nez v0, :cond_6

    .line 441
    :cond_5
    invoke-virtual {v8, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->add(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    .line 445
    :cond_6
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v10

    move v11, v9

    move/from16 v9, p4

    .line 446
    :goto_3
    invoke-virtual {v10}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfOptimizedTransitions()I

    move-result v0

    if-ge v11, v0, :cond_8

    .line 447
    invoke-virtual {v10, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getOptimizedTransition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v3

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object/from16 v4, p3

    move/from16 v5, p5

    move/from16 v6, p6

    .line 448
    invoke-virtual/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->getEpsilonTarget(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/Transition;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;ZZ)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v2

    if-eqz v2, :cond_7

    move-object v0, p0

    move-object v1, p1

    move-object/from16 v3, p3

    move v4, v9

    move/from16 v5, p5

    move/from16 v6, p6

    .line 450
    invoke-virtual/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->closure(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;ZZZ)Z

    move-result v0

    move v9, v0

    :cond_7
    add-int/lit8 v11, v11, 0x1

    goto :goto_3

    :cond_8
    return v9
.end method

.method protected computeStartState(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;
    .locals 10

    .line 378
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 379
    new-instance v8, Lgroovyjarjarantlr4/v4/runtime/atn/OrderedATNConfigSet;

    invoke-direct {v8}, Lgroovyjarjarantlr4/v4/runtime/atn/OrderedATNConfigSet;-><init>()V

    const/4 v1, 0x0

    .line 380
    :goto_0
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v2

    if-ge v1, v2, :cond_0

    .line 381
    invoke-virtual {p2, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v2

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    add-int/lit8 v9, v1, 0x1

    .line 382
    invoke-static {v2, v9, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->create(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;ILgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v3

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    move-object v1, p0

    move-object v2, p1

    move-object v4, v8

    .line 383
    invoke-virtual/range {v1 .. v7}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->closure(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;ZZZ)Z

    move v1, v9

    goto :goto_0

    :cond_0
    return-object v8
.end method

.method protected computeTargetState(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;I)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;
    .locals 2

    .line 268
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/OrderedATNConfigSet;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/OrderedATNConfigSet;-><init>()V

    .line 272
    iget-object v1, p2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->configs:Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    invoke-virtual {p0, p1, v1, v0, p3}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->getReachableConfigSet(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;I)V

    .line 274
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 275
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext()Z

    move-result p1

    if-nez p1, :cond_0

    .line 278
    sget-object p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->ERROR:Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {p0, p2, p3, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->addDFAEdge(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;ILgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V

    .line 282
    :cond_0
    sget-object p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->ERROR:Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    return-object p1

    .line 286
    :cond_1
    invoke-virtual {p0, p2, p3, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->addDFAEdge(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;ILgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    move-result-object p1

    return-object p1
.end method

.method public consume(Lgroovyjarjarantlr4/v4/runtime/CharStream;)V
    .locals 3

    const/4 v0, 0x1

    .line 731
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->LA(I)I

    move-result v1

    const/16 v2, 0xa

    if-ne v1, v2, :cond_0

    .line 733
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    add-int/2addr v1, v0

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    const/4 v0, 0x0

    .line 734
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    goto :goto_0

    .line 737
    :cond_0
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    add-int/2addr v1, v0

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    .line 739
    :goto_0
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->consume()V

    return-void
.end method

.method public copyState(Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;)V
    .locals 1

    .line 99
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    .line 100
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    .line 101
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    .line 102
    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    return-void
.end method

.method protected evaluatePredicate(Lgroovyjarjarantlr4/v4/runtime/CharStream;IIZ)Z
    .locals 5

    .line 589
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->recog:Lgroovyjarjarantlr4/v4/runtime/Lexer;

    if-nez v0, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 v1, 0x0

    if-nez p4, :cond_1

    .line 594
    invoke-virtual {v0, v1, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/Lexer;->sempred(Lgroovyjarjarantlr4/v4/runtime/RuleContext;II)Z

    move-result p1

    return p1

    .line 597
    :cond_1
    iget p4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    .line 598
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    .line 599
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->index()I

    move-result v2

    .line 600
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->mark()I

    move-result v3

    .line 602
    :try_start_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->consume(Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    .line 603
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->recog:Lgroovyjarjarantlr4/v4/runtime/Lexer;

    invoke-virtual {v4, v1, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/Lexer;->sempred(Lgroovyjarjarantlr4/v4/runtime/RuleContext;II)Z

    move-result p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 606
    iput p4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    .line 607
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    .line 608
    invoke-interface {p1, v2}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->seek(I)V

    .line 609
    invoke-interface {p1, v3}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->release(I)V

    return p2

    :catchall_0
    move-exception p2

    .line 606
    iput p4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    .line 607
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    .line 608
    invoke-interface {p1, v2}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->seek(I)V

    .line 609
    invoke-interface {p1, v3}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->release(I)V

    throw p2
.end method

.method protected execATN(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)I
    .locals 5

    .line 170
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->isAcceptState()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 172
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->prevAccept:Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;

    invoke-virtual {p0, v0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->captureSimState(Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V

    :cond_0
    const/4 v0, 0x1

    .line 175
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->LA(I)I

    move-result v1

    .line 201
    :goto_0
    invoke-virtual {p0, p2, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->getExistingTargetState(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;I)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    move-result-object v2

    if-nez v2, :cond_1

    .line 203
    invoke-virtual {p0, p1, p2, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->computeTargetState(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;I)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    move-result-object v2

    .line 206
    :cond_1
    sget-object v3, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->ERROR:Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    if-ne v2, v3, :cond_2

    goto :goto_1

    :cond_2
    const/4 v3, -0x1

    if-eq v1, v3, :cond_3

    .line 215
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->consume(Lgroovyjarjarantlr4/v4/runtime/CharStream;)V

    .line 218
    :cond_3
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->isAcceptState()Z

    move-result v4

    if-eqz v4, :cond_4

    .line 219
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->prevAccept:Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;

    invoke-virtual {p0, v4, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->captureSimState(Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)V

    if-ne v1, v3, :cond_4

    .line 229
    :goto_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->prevAccept:Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->configs:Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    invoke-virtual {p0, v0, p1, p2, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->failOrAccept(Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;I)I

    move-result p1

    return p1

    .line 225
    :cond_4
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->LA(I)I

    move-result v1

    move-object p2, v2

    goto :goto_0
.end method

.method protected failOrAccept(Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;I)I
    .locals 7

    .line 292
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->dfaState:Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    if-eqz v0, :cond_0

    .line 293
    iget-object p3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->dfaState:Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getLexerActionExecutor()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    move-result-object v2

    .line 294
    iget v3, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    iget v4, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->index:I

    iget v5, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->line:I

    iget v6, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->charPos:I

    move-object v0, p0

    move-object v1, p2

    invoke-virtual/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->accept(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;IIII)V

    .line 296
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->dfaState:Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getPrediction()I

    move-result p1

    return p1

    :cond_0
    const/4 p1, -0x1

    if-ne p4, p1, :cond_1

    .line 300
    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->index()I

    move-result p4

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    if-ne p4, v0, :cond_1

    return p1

    .line 304
    :cond_1
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;

    iget-object p4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->recog:Lgroovyjarjarantlr4/v4/runtime/Lexer;

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    invoke-direct {p1, p4, p2, v0, p3}, Lgroovyjarjarantlr4/v4/runtime/LexerNoViableAltException;-><init>(Lgroovyjarjarantlr4/v4/runtime/Lexer;Lgroovyjarjarantlr4/v4/runtime/CharStream;ILgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V

    throw p1
.end method

.method public getCharPositionInLine()I
    .locals 1

    .line 723
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    return v0
.end method

.method public final getDFA(I)Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;
    .locals 1

    .line 703
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    aget-object p1, v0, p1

    return-object p1
.end method

.method protected getEpsilonTarget(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/Transition;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;ZZ)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;
    .locals 3

    .line 468
    invoke-virtual {p3}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->getSerializationType()I

    move-result v0

    const/16 v1, 0xa

    if-eq v0, v1, :cond_3

    const/4 v1, 0x0

    const/4 v2, 0x1

    packed-switch v0, :pswitch_data_0

    goto/16 :goto_0

    .line 518
    :pswitch_0
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->hasEmpty()Z

    move-result p1

    if-eqz p1, :cond_0

    .line 531
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getLexerActionExecutor()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    move-result-object p1

    iget-object p4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object p4, p4, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->lexerActions:[Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

    move-object p5, p3

    check-cast p5, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;

    iget p5, p5, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->actionIndex:I

    aget-object p4, p4, p5

    invoke-static {p1, p4}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;->append(Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;)Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    move-result-object p1

    .line 532
    iget-object p3, p3, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p2, p3, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v1

    goto :goto_0

    .line 537
    :cond_0
    iget-object p1, p3, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p2, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v1

    goto :goto_0

    .line 503
    :pswitch_1
    move-object p6, p3

    check-cast p6, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;

    .line 507
    invoke-virtual {p4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->markExplicitSemanticContext()V

    .line 508
    iget p4, p6, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;->ruleIndex:I

    iget p6, p6, Lgroovyjarjarantlr4/v4/runtime/atn/PredicateTransition;->predIndex:I

    invoke-virtual {p0, p1, p4, p6, p5}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->evaluatePredicate(Lgroovyjarjarantlr4/v4/runtime/CharStream;IIZ)Z

    move-result p1

    if-eqz p1, :cond_2

    .line 509
    iget-object p1, p3, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p2, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v1

    goto :goto_0

    .line 470
    :pswitch_2
    move-object p1, p3

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    .line 471
    iget-boolean p4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->optimize_tail_calls:Z

    if-eqz p4, :cond_1

    iget-boolean p4, p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->optimizedTailCall:Z

    if-eqz p4, :cond_1

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p4

    invoke-virtual {p4}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->hasEmpty()Z

    move-result p4

    if-nez p4, :cond_1

    .line 472
    iget-object p1, p3, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p2, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v1

    goto :goto_0

    .line 475
    :cond_1
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p4

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p4, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    .line 476
    iget-object p3, p3, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p2, p3, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v1

    goto :goto_0

    :pswitch_3
    if-eqz p6, :cond_2

    const/4 p1, -0x1

    const p4, 0x10ffff

    const/4 p5, 0x0

    .line 549
    invoke-virtual {p3, p1, p5, p4}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->matches(III)Z

    move-result p1

    if-eqz p1, :cond_2

    .line 550
    iget-object p1, p3, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p2, p1, p5}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v1

    goto :goto_0

    .line 542
    :pswitch_4
    iget-object p1, p3, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p2, p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v1

    :cond_2
    :goto_0
    return-object v1

    .line 482
    :cond_3
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "Precedence predicates are not supported in lexers."

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_3
        :pswitch_0
        :pswitch_3
    .end packed-switch
.end method

.method protected getExistingTargetState(Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;I)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;
    .locals 0

    .line 245
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;->getTarget(I)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    move-result-object p1

    return-object p1
.end method

.method public getLine()I
    .locals 1

    .line 715
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    return v0
.end method

.method protected getReachableConfigSet(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;I)V
    .locals 18

    move-object/from16 v7, p0

    move/from16 v8, p4

    .line 316
    invoke-virtual/range {p2 .. p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->iterator()Ljava/util/Iterator;

    move-result-object v9

    const/4 v10, 0x0

    move v11, v10

    :cond_0
    :goto_0
    invoke-interface {v9}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_7

    invoke-interface {v9}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    move-object v12, v0

    check-cast v12, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 317
    invoke-virtual {v12}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result v0

    const/4 v13, 0x1

    if-ne v0, v11, :cond_1

    move v14, v13

    goto :goto_1

    :cond_1
    move v14, v10

    :goto_1
    if-eqz v14, :cond_2

    .line 318
    invoke-virtual {v12}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->hasPassedThroughNonGreedyDecision()Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_0

    .line 326
    :cond_2
    invoke-virtual {v12}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v0

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfOptimizedTransitions()I

    move-result v15

    move v6, v10

    :goto_2
    if-ge v6, v15, :cond_0

    .line 328
    invoke-virtual {v12}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v0

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getOptimizedTransition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v0

    .line 329
    invoke-virtual {v7, v0, v8}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->getReachableTarget(Lgroovyjarjarantlr4/v4/runtime/atn/Transition;I)Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v0

    if-eqz v0, :cond_5

    .line 331
    invoke-virtual {v12}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getLexerActionExecutor()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    move-result-object v1

    if-eqz v1, :cond_3

    .line 333
    invoke-interface/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->index()I

    move-result v2

    iget v3, v7, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    sub-int/2addr v2, v3

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;->fixOffsetBeforeMatch(I)Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;

    move-result-object v1

    :cond_3
    const/4 v2, -0x1

    if-ne v8, v2, :cond_4

    move/from16 v16, v13

    goto :goto_3

    :cond_4
    move/from16 v16, v10

    .line 337
    :goto_3
    invoke-virtual {v12, v0, v1, v13}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->transform(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionExecutor;Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v2

    const/4 v5, 0x1

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v3, p3

    move v4, v14

    move/from16 v17, v6

    move/from16 v6, v16

    invoke-virtual/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->closure(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;ZZZ)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 340
    invoke-virtual {v12}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result v0

    move v11, v0

    goto :goto_0

    :cond_5
    move/from16 v17, v6

    :cond_6
    add-int/lit8 v6, v17, 0x1

    goto :goto_2

    :cond_7
    return-void
.end method

.method protected getReachableTarget(Lgroovyjarjarantlr4/v4/runtime/atn/Transition;I)Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;
    .locals 2

    const/4 v0, 0x0

    const v1, 0x10ffff

    .line 367
    invoke-virtual {p1, p2, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->matches(III)Z

    move-result p2

    if-eqz p2, :cond_0

    .line 368
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public getText(Lgroovyjarjarantlr4/v4/runtime/CharStream;)Ljava/lang/String;
    .locals 2

    .line 711
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->index()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v0

    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getTokenName(I)Ljava/lang/String;
    .locals 2

    const/4 v0, -0x1

    if-ne p1, v0, :cond_0

    const-string p1, "EOF"

    return-object p1

    .line 746
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    int-to-char p1, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public match(Lgroovyjarjarantlr4/v4/runtime/CharStream;I)I
    .locals 2

    .line 106
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    .line 107
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->mark()I

    move-result v0

    .line 109
    :try_start_0
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->index()I

    move-result v1

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    .line 110
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->prevAccept:Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->reset()V

    .line 111
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    aget-object p2, v1, p2

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    if-nez p2, :cond_0

    .line 113
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->matchATN(Lgroovyjarjarantlr4/v4/runtime/CharStream;)I

    move-result p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 120
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->release(I)V

    return p2

    .line 116
    :cond_0
    :try_start_1
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->execATN(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)I

    move-result p2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 120
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->release(I)V

    return p2

    :catchall_0
    move-exception p2

    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->release(I)V

    throw p2
.end method

.method protected matchATN(Lgroovyjarjarantlr4/v4/runtime/CharStream;)I
    .locals 3

    .line 134
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToStartState:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 142
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->computeStartState(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    move-result-object v0

    .line 143
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 145
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->clearExplicitSemanticContext()V

    .line 148
    :cond_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->addDFAState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    move-result-object v0

    if-nez v1, :cond_1

    .line 150
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    aget-object v1, v1, v2

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    const/4 v2, 0x0

    invoke-virtual {v1, v2, v0}, Ljava/util/concurrent/atomic/AtomicReference;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    .line 151
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    aget-object v0, v0, v1

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->s0:Ljava/util/concurrent/atomic/AtomicReference;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;

    .line 155
    :cond_1
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->execATN(Lgroovyjarjarantlr4/v4/runtime/CharStream;Lgroovyjarjarantlr4/v4/runtime/dfa/DFAState;)I

    move-result p1

    return p1
.end method

.method public reset()V
    .locals 1

    .line 126
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->prevAccept:Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator$SimState;->reset()V

    const/4 v0, -0x1

    .line 127
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->startIndex:I

    const/4 v0, 0x1

    .line 128
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    const/4 v0, 0x0

    .line 129
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    .line 130
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->mode:I

    return-void
.end method

.method public setCharPositionInLine(I)V
    .locals 0

    .line 727
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->charPositionInLine:I

    return-void
.end method

.method public setLine(I)V
    .locals 0

    .line 719
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerATNSimulator;->line:I

    return-void
.end method
