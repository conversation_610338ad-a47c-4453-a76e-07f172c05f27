.class public Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;
.super Ljava/lang/Object;
.source "PredictionContextCache.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;,
        Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$PredictionContextAndInt;
    }
.end annotation


# static fields
.field public static final UNCACHED:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;


# instance fields
.field private final childContexts:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$PredictionContextAndInt;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            ">;"
        }
    .end annotation
.end field

.field private final contexts:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            ">;"
        }
    .end annotation
.end field

.field private final enableCache:Z

.field private final joinContexts:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 19
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;-><init>(Z)V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->UNCACHED:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x1

    .line 31
    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;-><init>(Z)V

    return-void
.end method

.method private constructor <init>(Z)V
    .locals 1

    .line 34
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 21
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->contexts:Ljava/util/Map;

    .line 23
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->childContexts:Ljava/util/Map;

    .line 25
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->joinContexts:Ljava/util/Map;

    .line 35
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->enableCache:Z

    return-void
.end method


# virtual methods
.method public getAsCached(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 1

    .line 39
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->enableCache:Z

    if-nez v0, :cond_0

    return-object p1

    .line 43
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->contexts:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    if-nez v0, :cond_1

    .line 46
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->contexts:Ljava/util/Map;

    invoke-interface {v0, p1, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_1
    move-object p1, v0

    :goto_0
    return-object p1
.end method

.method public getChild(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 2

    .line 53
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->enableCache:Z

    if-nez v0, :cond_0

    .line 54
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    return-object p1

    .line 57
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$PredictionContextAndInt;

    invoke-direct {v0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$PredictionContextAndInt;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)V

    .line 58
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->childContexts:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    if-nez v1, :cond_1

    .line 60
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    .line 61
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->getAsCached(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v1

    .line 62
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->childContexts:Ljava/util/Map;

    invoke-interface {p1, v0, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_1
    return-object v1
.end method

.method public join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 2

    .line 69
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->enableCache:Z

    if-nez v0, :cond_0

    .line 70
    invoke-static {p1, p2, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    return-object p1

    .line 73
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;

    invoke-direct {v0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)V

    .line 74
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->joinContexts:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    if-eqz v1, :cond_1

    return-object v1

    .line 79
    :cond_1
    invoke-static {p1, p2, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    .line 80
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->getAsCached(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    .line 81
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->joinContexts:Ljava/util/Map;

    invoke-interface {p2, v0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p1
.end method
