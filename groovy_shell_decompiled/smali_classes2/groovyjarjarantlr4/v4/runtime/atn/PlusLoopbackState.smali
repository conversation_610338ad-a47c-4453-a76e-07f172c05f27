.class public final Lgroovyjarjarantlr4/v4/runtime/atn/PlusLoopbackState;
.super Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;
.source "PlusLoopbackState.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 12
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;-><init>()V

    return-void
.end method


# virtual methods
.method public getStateType()I
    .locals 1

    const/16 v0, 0xb

    return v0
.end method
