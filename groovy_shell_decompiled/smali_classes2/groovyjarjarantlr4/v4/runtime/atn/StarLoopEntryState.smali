.class public final Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;
.super Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;
.source "StarLoopEntryState.java"


# instance fields
.field public loopBackState:Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopbackState;

.field public precedenceLoopbackStates:Ljava/util/BitSet;

.field public precedenceRuleDecision:Z


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 14
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;-><init>()V

    return-void
.end method


# virtual methods
.method public getStateType()I
    .locals 1

    const/16 v0, 0xa

    return v0
.end method
