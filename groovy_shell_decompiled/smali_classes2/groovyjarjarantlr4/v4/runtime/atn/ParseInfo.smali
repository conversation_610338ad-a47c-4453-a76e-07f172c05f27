.class public Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;
.super Ljava/lang/Object;
.source "ParseInfo.java"


# instance fields
.field protected final atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;)V
    .locals 0

    .line 24
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 25
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    return-void
.end method


# virtual methods
.method public getDFASize()I
    .locals 4

    .line 151
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    const/4 v1, 0x0

    move v2, v1

    .line 152
    :goto_0
    array-length v3, v0

    if-ge v1, v3, :cond_0

    .line 153
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->getDFASize(I)I

    move-result v3

    add-int/2addr v2, v3

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return v2
.end method

.method public getDFASize(I)I
    .locals 1

    .line 163
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    aget-object p1, v0, p1

    .line 164
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->states:Ljava/util/concurrent/ConcurrentMap;

    invoke-interface {p1}, Ljava/util/concurrent/ConcurrentMap;->size()I

    move-result p1

    return p1
.end method

.method public getDecisionInfo()[Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;
    .locals 1

    .line 37
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->getDecisionInfo()[Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;

    move-result-object v0

    return-object v0
.end method

.method public getLLDecisions()Ljava/util/List;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .line 50
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->getDecisionInfo()[Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;

    move-result-object v0

    .line 51
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    const/4 v2, 0x0

    .line 52
    :goto_0
    array-length v3, v0

    if-ge v2, v3, :cond_1

    .line 53
    aget-object v3, v0, v2

    iget-wide v3, v3, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;->LL_Fallback:J

    const-wide/16 v5, 0x0

    cmp-long v3, v3, v5

    if-lez v3, :cond_0

    .line 54
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-object v1
.end method

.method public getTotalATNLookaheadOps()J
    .locals 6

    .line 136
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->getDecisionInfo()[Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;

    move-result-object v0

    const-wide/16 v1, 0x0

    const/4 v3, 0x0

    .line 138
    :goto_0
    array-length v4, v0

    if-ge v3, v4, :cond_0

    .line 139
    aget-object v4, v0, v3

    iget-wide v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;->SLL_ATNTransitions:J

    add-long/2addr v1, v4

    .line 140
    aget-object v4, v0, v3

    iget-wide v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;->LL_ATNTransitions:J

    add-long/2addr v1, v4

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-wide v1
.end method

.method public getTotalLLATNLookaheadOps()J
    .locals 6

    .line 119
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->getDecisionInfo()[Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;

    move-result-object v0

    const-wide/16 v1, 0x0

    const/4 v3, 0x0

    .line 121
    :goto_0
    array-length v4, v0

    if-ge v3, v4, :cond_0

    .line 122
    aget-object v4, v0, v3

    iget-wide v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;->LL_ATNTransitions:J

    add-long/2addr v1, v4

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-wide v1
.end method

.method public getTotalLLLookaheadOps()J
    .locals 6

    .line 93
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->getDecisionInfo()[Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;

    move-result-object v0

    const-wide/16 v1, 0x0

    const/4 v3, 0x0

    .line 95
    :goto_0
    array-length v4, v0

    if-ge v3, v4, :cond_0

    .line 96
    aget-object v4, v0, v3

    iget-wide v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;->LL_TotalLook:J

    add-long/2addr v1, v4

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-wide v1
.end method

.method public getTotalSLLATNLookaheadOps()J
    .locals 6

    .line 106
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->getDecisionInfo()[Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;

    move-result-object v0

    const-wide/16 v1, 0x0

    const/4 v3, 0x0

    .line 108
    :goto_0
    array-length v4, v0

    if-ge v3, v4, :cond_0

    .line 109
    aget-object v4, v0, v3

    iget-wide v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;->SLL_ATNTransitions:J

    add-long/2addr v1, v4

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-wide v1
.end method

.method public getTotalSLLLookaheadOps()J
    .locals 6

    .line 79
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->getDecisionInfo()[Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;

    move-result-object v0

    const-wide/16 v1, 0x0

    const/4 v3, 0x0

    .line 81
    :goto_0
    array-length v4, v0

    if-ge v3, v4, :cond_0

    .line 82
    aget-object v4, v0, v3

    iget-wide v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;->SLL_TotalLook:J

    add-long/2addr v1, v4

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-wide v1
.end method

.method public getTotalTimeInPrediction()J
    .locals 6

    .line 65
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;->atnSimulator:Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;->getDecisionInfo()[Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;

    move-result-object v0

    const-wide/16 v1, 0x0

    const/4 v3, 0x0

    .line 67
    :goto_0
    array-length v4, v0

    if-ge v3, v4, :cond_0

    .line 68
    aget-object v4, v0, v3

    iget-wide v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionInfo;->timeInPrediction:J

    add-long/2addr v1, v4

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-wide v1
.end method
