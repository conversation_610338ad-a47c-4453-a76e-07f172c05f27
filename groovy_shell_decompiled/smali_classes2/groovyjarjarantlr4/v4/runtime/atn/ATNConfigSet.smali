.class public Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;
.super Ljava/lang/Object;
.source "ATNConfigSet.java"

# interfaces
.implements Ljava/util/Set;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet$ATNConfigSetIterator;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Set<",
        "Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;",
        ">;"
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field private cachedHashCode:I

.field private final configs:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;",
            ">;"
        }
    .end annotation
.end field

.field private conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

.field private dipsIntoOuterContext:Z

.field private hasSemanticContext:Z

.field private final mergedConfigs:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/Long;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;",
            ">;"
        }
    .end annotation
.end field

.field private outermostConfigSet:Z

.field private uniqueAlt:I

.field private final unmerged:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 88
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 86
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->cachedHashCode:I

    .line 89
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    .line 90
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    .line 91
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    const/4 v0, 0x0

    .line 93
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    return-void
.end method

.method protected constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;Z)V
    .locals 2

    .line 97
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 86
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->cachedHashCode:I

    if-eqz p2, :cond_0

    const/4 v0, 0x0

    .line 99
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    .line 100
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    goto :goto_0

    .line 101
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->isReadOnly()Z

    move-result v0

    if-nez v0, :cond_1

    .line 102
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/HashMap;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    .line 103
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/ArrayList;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    goto :goto_0

    .line 105
    :cond_1
    new-instance v0, Ljava/util/HashMap;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v1

    invoke-direct {v0, v1}, Ljava/util/HashMap;-><init>(I)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    .line 106
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    .line 109
    :goto_0
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/ArrayList;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    .line 111
    iget-boolean v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->dipsIntoOuterContext:Z

    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->dipsIntoOuterContext:Z

    .line 112
    iget-boolean v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    .line 113
    iget-boolean v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->outermostConfigSet:Z

    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->outermostConfigSet:Z

    if-nez p2, :cond_2

    .line 115
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->isReadOnly()Z

    move-result p2

    if-nez p2, :cond_3

    .line 116
    :cond_2
    iget p2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    .line 117
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    :cond_3
    return-void
.end method

.method static synthetic access$100(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)Ljava/util/ArrayList;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    return-object p0
.end method

.method private updatePropertiesForAddedConfig(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)V
    .locals 4

    .line 310
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    .line 311
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    goto :goto_0

    .line 312
    :cond_0
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result v2

    if-eq v0, v2, :cond_1

    const/4 v0, 0x0

    .line 313
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    .line 316
    :cond_1
    :goto_0
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    sget-object v2, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;->NONE:Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getSemanticContext()Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v1, v2

    or-int/2addr v0, v1

    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    .line 317
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->dipsIntoOuterContext:Z

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getReachesIntoOuterContext()Z

    move-result p1

    or-int/2addr p1, v0

    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->dipsIntoOuterContext:Z

    return-void
.end method

.method private updatePropertiesForMergedConfig(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)V
    .locals 1

    .line 305
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->dipsIntoOuterContext:Z

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getReachesIntoOuterContext()Z

    move-result p1

    or-int/2addr p1, v0

    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->dipsIntoOuterContext:Z

    return-void
.end method


# virtual methods
.method public add(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z
    .locals 1

    const/4 v0, 0x0

    .line 236
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->add(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Z

    move-result p1

    return p1
.end method

.method public add(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Z
    .locals 9

    .line 240
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    if-nez p2, :cond_0

    .line 244
    sget-object p2, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->UNCACHED:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;

    .line 248
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->getKey(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)J

    move-result-wide v0

    .line 249
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    const/4 v3, 0x0

    const/4 v4, 0x1

    if-nez v2, :cond_1

    move v5, v4

    goto :goto_0

    :cond_1
    move v5, v3

    :goto_0
    if-eqz v2, :cond_4

    .line 251
    invoke-virtual {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->canMerge(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;JLgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    move-result v6

    if-eqz v6, :cond_4

    .line 252
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getOuterContextDepth()I

    move-result v0

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getOuterContextDepth()I

    move-result v1

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v0

    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->setOuterContextDepth(I)V

    .line 253
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->isPrecedenceFilterSuppressed()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 254
    invoke-virtual {v2, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->setPrecedenceFilterSuppressed(Z)V

    .line 257
    :cond_2
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v0

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v1

    invoke-static {v0, v1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p2

    .line 258
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->updatePropertiesForMergedConfig(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)V

    .line 259
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    if-ne p1, p2, :cond_3

    return v3

    .line 263
    :cond_3
    invoke-virtual {v2, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->setContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)V

    return v4

    :cond_4
    move v2, v3

    .line 267
    :goto_1
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {v6}, Ljava/util/ArrayList;->size()I

    move-result v6

    if-ge v2, v6, :cond_9

    .line 268
    iget-object v6, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {v6, v2}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 269
    invoke-virtual {p0, p1, v0, v1, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->canMerge(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;JLgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    move-result v7

    if-eqz v7, :cond_8

    .line 270
    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getOuterContextDepth()I

    move-result v7

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getOuterContextDepth()I

    move-result v8

    invoke-static {v7, v8}, Ljava/lang/Math;->max(II)I

    move-result v7

    invoke-virtual {v6, v7}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->setOuterContextDepth(I)V

    .line 271
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->isPrecedenceFilterSuppressed()Z

    move-result v7

    if-eqz v7, :cond_5

    .line 272
    invoke-virtual {v6, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->setPrecedenceFilterSuppressed(Z)V

    .line 275
    :cond_5
    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v7

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v8

    invoke-static {v7, v8, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p2

    .line 276
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->updatePropertiesForMergedConfig(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)V

    .line 277
    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    if-ne p1, p2, :cond_6

    return v3

    .line 281
    :cond_6
    invoke-virtual {v6, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->setContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)V

    if-eqz v5, :cond_7

    .line 284
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p2

    invoke-virtual {p1, p2, v6}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 285
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {p1, v2}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    :cond_7
    return v4

    :cond_8
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 292
    :cond_9
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {p2, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    if-eqz v5, :cond_a

    .line 294
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    invoke-virtual {p2, v0, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    .line 296
    :cond_a
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {p2, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 299
    :goto_2
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->updatePropertiesForAddedConfig(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)V

    return v4
.end method

.method public bridge synthetic add(Ljava/lang/Object;)Z
    .locals 0

    .line 41
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->add(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    move-result p1

    return p1
.end method

.method public addAll(Ljava/util/Collection;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;",
            ">;)Z"
        }
    .end annotation

    const/4 v0, 0x0

    .line 363
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->addAll(Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Z

    move-result p1

    return p1
.end method

.method public addAll(Ljava/util/Collection;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Z
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;",
            ")Z"
        }
    .end annotation

    .line 367
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    .line 370
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    const/4 v0, 0x0

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 371
    invoke-virtual {p0, v1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->add(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Z

    move-result v1

    or-int/2addr v0, v1

    goto :goto_0

    :cond_0
    return v0
.end method

.method protected canMerge(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;JLgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z
    .locals 3

    .line 322
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v0

    iget v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v1

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    const/4 v2, 0x0

    if-eq v0, v1, :cond_0

    return v2

    .line 326
    :cond_0
    invoke-virtual {p0, p4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->getKey(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)J

    move-result-wide v0

    cmp-long p2, p2, v0

    if-eqz p2, :cond_1

    return v2

    .line 330
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getSemanticContext()Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    move-result-object p1

    invoke-virtual {p4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getSemanticContext()Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public clear()V
    .locals 1

    .line 391
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    .line 393
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->clear()V

    .line 394
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    .line 395
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    const/4 v0, 0x0

    .line 397
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->dipsIntoOuterContext:Z

    .line 398
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    .line 399
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    const/4 v0, 0x0

    .line 400
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    return-void
.end method

.method public clearExplicitSemanticContext()V
    .locals 1

    .line 489
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    const/4 v0, 0x0

    .line 490
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    return-void
.end method

.method public clone(Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;
    .locals 1

    .line 179
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    invoke-direct {v0, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;Z)V

    if-nez p1, :cond_0

    .line 180
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->isReadOnly()Z

    move-result p1

    if-eqz p1, :cond_0

    .line 181
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->addAll(Ljava/util/Collection;)Z

    :cond_0
    return-object v0
.end method

.method public contains(Ljava/lang/Object;)Z
    .locals 5

    .line 199
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 203
    :cond_0
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 204
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->getKey(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)J

    move-result-wide v2

    .line 205
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v4

    invoke-virtual {v0, v4}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    if-eqz v0, :cond_1

    .line 206
    invoke-virtual {p0, p1, v2, v3, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->canMerge(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;JLgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 207
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->contains(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    move-result p1

    return p1

    .line 210
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 211
    invoke-virtual {v2, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->contains(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    move-result v2

    if-eqz v2, :cond_2

    const/4 p1, 0x1

    return p1

    :cond_3
    return v1
.end method

.method public containsAll(Ljava/util/Collection;)Z
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "*>;)Z"
        }
    .end annotation

    .line 348
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    .line 349
    instance-of v1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 353
    :cond_1
    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    return v2

    :cond_2
    const/4 p1, 0x1

    return p1
.end method

.method protected final ensureWritable()V
    .locals 2

    .line 549
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->isReadOnly()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 550
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "This ATNConfigSet is read only."

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 409
    :cond_0
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 413
    :cond_1
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    .line 414
    iget-boolean v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->outermostConfigSet:Z

    iget-boolean v3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->outermostConfigSet:Z

    if-ne v1, v3, :cond_2

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    iget-object v3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    invoke-static {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public get(I)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;
    .locals 1

    .line 528
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    return-object p1
.end method

.method public getConflictInfo()Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;
    .locals 1

    .line 499
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    return-object v0
.end method

.method public getConflictingAlts()Ljava/util/BitSet;
    .locals 1

    .line 508
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 512
    :cond_0
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->getConflictedAlts()Ljava/util/BitSet;

    move-result-object v0

    return-object v0
.end method

.method public getDipsIntoOuterContext()Z
    .locals 1

    .line 524
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->dipsIntoOuterContext:Z

    return v0
.end method

.method protected getKey(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)J
    .locals 4

    .line 334
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v0

    iget v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    int-to-long v0, v0

    const/16 v2, 0xc

    shl-long/2addr v0, v2

    .line 335
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result p1

    and-int/lit16 p1, p1, 0xfff

    int-to-long v2, p1

    or-long/2addr v0, v2

    return-wide v0
.end method

.method public getRepresentedAlternatives()Ljava/util/BitSet;
    .locals 3

    .line 129
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    if-eqz v0, :cond_0

    .line 130
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->getConflictedAlts()Ljava/util/BitSet;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/BitSet;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/BitSet;

    return-object v0

    .line 133
    :cond_0
    new-instance v0, Ljava/util/BitSet;

    invoke-direct {v0}, Ljava/util/BitSet;-><init>()V

    .line 134
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 135
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result v2

    invoke-virtual {v0, v2}, Ljava/util/BitSet;->set(I)V

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public getStates()Ljava/util/Set;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;",
            ">;"
        }
    .end annotation

    .line 159
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 160
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 161
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getUniqueAlt()I
    .locals 1

    .line 481
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    return v0
.end method

.method public hasSemanticContext()Z
    .locals 1

    .line 485
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    return v0
.end method

.method public hashCode()I
    .locals 2

    .line 421
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->isReadOnly()Z

    move-result v0

    if-eqz v0, :cond_0

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->cachedHashCode:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    .line 426
    :cond_0
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->outermostConfigSet:Z

    const/4 v1, 0x5

    xor-int/2addr v0, v1

    mul-int/2addr v0, v1

    .line 427
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->hashCode()I

    move-result v1

    xor-int/2addr v0, v1

    .line 429
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->isReadOnly()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 430
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->cachedHashCode:I

    :cond_1
    return v0
.end method

.method public isEmpty()Z
    .locals 1

    .line 194
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    return v0
.end method

.method public isExactConflict()Z
    .locals 1

    .line 516
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    .line 520
    :cond_0
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->isExact()Z

    move-result v0

    return v0
.end method

.method public isOutermostConfigSet()Z
    .locals 1

    .line 146
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->outermostConfigSet:Z

    return v0
.end method

.method public final isReadOnly()Z
    .locals 1

    .line 142
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;",
            ">;"
        }
    .end annotation

    .line 221
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet$ATNConfigSetIterator;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet$ATNConfigSetIterator;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet$1;)V

    return-object v0
.end method

.method public markExplicitSemanticContext()V
    .locals 1

    .line 494
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    const/4 v0, 0x1

    .line 495
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    return-void
.end method

.method public optimizeConfigs(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;)V
    .locals 4

    .line 168
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x0

    .line 172
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v1

    if-ge v0, v1, :cond_1

    .line 173
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 174
    iget-object v2, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->getCachedContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->setContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public remove(I)V
    .locals 4

    .line 532
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    .line 533
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    .line 534
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->remove(Ljava/lang/Object;)Z

    .line 535
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->getKey(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)J

    move-result-wide v0

    .line 536
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    if-ne v2, p1, :cond_0

    .line 537
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->mergedConfigs:Ljava/util/HashMap;

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    :cond_0
    const/4 v0, 0x0

    .line 539
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v1

    if-ge v0, v1, :cond_2

    .line 540
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    if-ne v1, p1, :cond_1

    .line 541
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->unmerged:Ljava/util/ArrayList;

    invoke-virtual {p1, v0}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    return-void

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    :goto_1
    return-void
.end method

.method public remove(Ljava/lang/Object;)Z
    .locals 1

    .line 341
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    .line 343
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Not supported yet."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public removeAll(Ljava/util/Collection;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "*>;)Z"
        }
    .end annotation

    .line 385
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    .line 386
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Not supported yet."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public retainAll(Ljava/util/Collection;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "*>;)Z"
        }
    .end annotation

    .line 379
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    .line 380
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Not supported yet."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public setConflictInfo(Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;)V
    .locals 0

    .line 503
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->ensureWritable()V

    .line 504
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    return-void
.end method

.method public setOutermostConfigSet(Z)V
    .locals 1

    .line 150
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->outermostConfigSet:Z

    if-eqz v0, :cond_1

    if-eqz p1, :cond_0

    goto :goto_0

    .line 151
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1

    .line 155
    :cond_1
    :goto_0
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->outermostConfigSet:Z

    return-void
.end method

.method public size()I
    .locals 1

    .line 189
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    return v0
.end method

.method public toArray()[Ljava/lang/Object;
    .locals 1

    .line 226
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->toArray()[Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public toArray([Ljava/lang/Object;)[Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">([TT;)[TT;"
        }
    .end annotation

    .line 231
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    .line 438
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->toString(Z)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toString(Z)Ljava/lang/String;
    .locals 6

    .line 442
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 443
    new-instance v1, Ljava/util/ArrayList;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->configs:Ljava/util/ArrayList;

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 444
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet$1;

    invoke-direct {v2, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet$1;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V

    invoke-static {v1, v2}, Ljava/util/Collections;->sort(Ljava/util/List;Ljava/util/Comparator;)V

    const-string v2, "["

    .line 459
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v2, 0x0

    .line 460
    :goto_0
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v3

    if-ge v2, v3, :cond_1

    if-lez v2, :cond_0

    const-string v3, ", "

    .line 462
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 464
    :cond_0
    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    const/4 v4, 0x0

    const/4 v5, 0x1

    invoke-virtual {v3, v4, v5, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->toString(Lgroovyjarjarantlr4/v4/runtime/Recognizer;ZZ)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    const-string p1, "]"

    .line 466
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 468
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    if-eqz p1, :cond_2

    const-string p1, ",hasSemanticContext="

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-boolean v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->hasSemanticContext:Z

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 469
    :cond_2
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    if-eqz p1, :cond_3

    const-string p1, ",uniqueAlt="

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->uniqueAlt:I

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 470
    :cond_3
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    if-eqz p1, :cond_4

    const-string p1, ",conflictingAlts="

    .line 471
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->getConflictedAlts()Ljava/util/BitSet;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 472
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->conflictInfo:Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->isExact()Z

    move-result p1

    if-nez p1, :cond_4

    const-string p1, "*"

    .line 473
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 476
    :cond_4
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->dipsIntoOuterContext:Z

    if-eqz p1, :cond_5

    const-string p1, ",dipsIntoOuterContext"

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 477
    :cond_5
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
