.class final Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$1;
.super Ljava/lang/Object;
.source "ATNDeserializer.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->getUnicodeDeserializer(Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializingMode;)Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    .line 103
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public readUnicode([CI)I
    .locals 0

    .line 106
    aget-char p1, p1, p2

    invoke-static {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->toInt(C)I

    move-result p1

    return p1
.end method

.method public size()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
