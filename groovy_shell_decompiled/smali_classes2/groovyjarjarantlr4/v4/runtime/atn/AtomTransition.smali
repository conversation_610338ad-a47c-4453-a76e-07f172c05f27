.class public final Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;
.super Lgroovyjarjarantlr4/v4/runtime/atn/Transition;
.source "AtomTransition.java"


# instance fields
.field public final label:I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;I)V
    .locals 0

    .line 18
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V

    .line 19
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    return-void
.end method


# virtual methods
.method public getSerializationType()I
    .locals 1

    const/4 v0, 0x5

    return v0
.end method

.method public label()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 1

    .line 29
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->of(I)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    return-object v0
.end method

.method public matches(III)Z
    .locals 0

    .line 33
    iget p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    if-ne p2, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 39
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/AtomTransition;->label:I

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
