.class public final Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;
.super Lgroovyjarjarantlr4/v4/runtime/atn/Transition;
.source "ActionTransition.java"


# instance fields
.field public final actionIndex:I

.field public final isCtxDependent:Z

.field public final ruleIndex:I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;I)V
    .locals 2

    const/4 v0, -0x1

    const/4 v1, 0x0

    .line 17
    invoke-direct {p0, p1, p2, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;IIZ)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;IIZ)V
    .locals 0

    .line 21
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V

    .line 22
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->ruleIndex:I

    .line 23
    iput p3, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->actionIndex:I

    .line 24
    iput-boolean p4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->isCtxDependent:Z

    return-void
.end method


# virtual methods
.method public getSerializationType()I
    .locals 1

    const/4 v0, 0x6

    return v0
.end method

.method public isEpsilon()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public matches(III)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 44
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "action_"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->ruleIndex:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ActionTransition;->actionIndex:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
