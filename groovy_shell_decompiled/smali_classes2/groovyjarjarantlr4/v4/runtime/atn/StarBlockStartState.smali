.class public final Lgroovyjarjarantlr4/v4/runtime/atn/StarBlockStartState;
.super Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;
.source "StarBlockStartState.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 10
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;-><init>()V

    return-void
.end method


# virtual methods
.method public getStateType()I
    .locals 1

    const/4 v0, 0x5

    return v0
.end method
