.class public abstract Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext$Operator;
.super Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;
.source "SemanticContext.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "Operator"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 189
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract getOperands()Ljava/util/Collection;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;",
            ">;"
        }
    .end annotation
.end method
