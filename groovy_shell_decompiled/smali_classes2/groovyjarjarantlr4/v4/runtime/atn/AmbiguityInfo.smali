.class public Lgroovyjarjarantlr4/v4/runtime/atn/AmbiguityInfo;
.super Lgroovyjarjarantlr4/v4/runtime/atn/DecisionEventInfo;
.source "AmbiguityInfo.java"


# instance fields
.field private final ambigAlts:Ljava/util/BitSet;


# direct methods
.method public constructor <init>(ILgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;Ljava/util/BitSet;Lgroovyjarjarantlr4/v4/runtime/TokenStream;II)V
    .locals 7

    .line 68
    iget-boolean v6, p2, Lgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;->useContext:Z

    move-object v0, p0

    move v1, p1

    move-object v2, p2

    move-object v3, p4

    move v4, p5

    move v5, p6

    invoke-direct/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionEventInfo;-><init>(ILgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;Lgroovyjarjarantlr4/v4/runtime/TokenStream;IIZ)V

    .line 69
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/runtime/atn/AmbiguityInfo;->ambigAlts:Ljava/util/BitSet;

    return-void
.end method


# virtual methods
.method public getAmbiguousAlternatives()Ljava/util/BitSet;
    .locals 1

    .line 79
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/AmbiguityInfo;->ambigAlts:Ljava/util/BitSet;

    return-object v0
.end method
