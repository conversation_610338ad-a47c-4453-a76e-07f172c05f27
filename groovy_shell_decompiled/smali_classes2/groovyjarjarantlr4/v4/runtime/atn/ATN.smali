.class public Lgroovyjarjarantlr4/v4/runtime/atn/ATN;
.super Ljava/lang/Object;
.source "ATN.java"


# static fields
.field static final synthetic $assertionsDisabled:Z

.field public static final INVALID_ALT_NUMBER:I


# instance fields
.field protected final LL1Table:Ljava/util/concurrent/ConcurrentMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/ConcurrentMap<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field private final contextCache:Ljava/util/concurrent/ConcurrentMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/ConcurrentMap<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            ">;"
        }
    .end annotation
.end field

.field public decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

.field public final decisionToState:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;",
            ">;"
        }
    .end annotation
.end field

.field public final grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

.field private hasUnicodeSMPTransitions:Z

.field public lexerActions:[Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;

.field public final maxTokenType:I

.field public final modeNameToStartState:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/TokensStartState;",
            ">;"
        }
    .end annotation
.end field

.field public modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

.field public final modeToStartState:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/TokensStartState;",
            ">;"
        }
    .end annotation
.end field

.field public ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

.field public ruleToStopState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

.field public ruleToTokenType:[I

.field public final states:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;I)V
    .locals 2

    .line 95
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 30
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    .line 37
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    .line 50
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeNameToStartState:Ljava/util/Map;

    .line 81
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToStartState:Ljava/util/List;

    .line 84
    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->contextCache:Ljava/util/concurrent/ConcurrentMap;

    const/4 v0, 0x0

    new-array v1, v0, [Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    .line 87
    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    new-array v0, v0, [Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    .line 89
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    .line 92
    new-instance v0, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->LL1Table:Ljava/util/concurrent/ConcurrentMap;

    .line 96
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->grammarType:Lgroovyjarjarantlr4/v4/runtime/atn/ATNType;

    .line 97
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->maxTokenType:I

    return-void
.end method


# virtual methods
.method public addState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 156
    iput-object p0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 157
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    iput v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    .line 160
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public final clearDFA()V
    .locals 5

    .line 101
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    new-array v0, v0, [Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    const/4 v0, 0x0

    move v1, v0

    .line 102
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    array-length v3, v2

    if-ge v1, v3, :cond_0

    .line 103
    new-instance v3, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget-object v4, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-direct {v3, v4, v1}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;I)V

    aput-object v3, v2, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 106
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToStartState:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    new-array v1, v1, [Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    .line 107
    :goto_1
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    array-length v2, v1

    if-ge v0, v2, :cond_1

    .line 108
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToStartState:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-direct {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V

    aput-object v2, v1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    .line 111
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->contextCache:Ljava/util/concurrent/ConcurrentMap;

    invoke-interface {v0}, Ljava/util/concurrent/ConcurrentMap;->clear()V

    .line 112
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->LL1Table:Ljava/util/concurrent/ConcurrentMap;

    invoke-interface {v0}, Ljava/util/concurrent/ConcurrentMap;->clear()V

    return-void
.end method

.method public defineDecisionState(Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;)I
    .locals 4

    .line 176
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 177
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    iput v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    .line 178
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    invoke-static {v0, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    .line 179
    array-length v1, v0

    add-int/lit8 v1, v1, -0x1

    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget v3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    invoke-direct {v2, p1, v3}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;I)V

    aput-object v2, v0, v1

    .line 180
    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    return p1
.end method

.method public defineMode(Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/atn/TokensStartState;)V
    .locals 2

    .line 168
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeNameToStartState:Ljava/util/Map;

    invoke-interface {v0, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 169
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToStartState:Ljava/util/List;

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 170
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToStartState:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    invoke-static {p1, v0}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->modeToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    .line 171
    array-length v0, p1

    add-int/lit8 v0, v0, -0x1

    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    invoke-direct {v1, p2}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V

    aput-object v1, p1, v0

    .line 172
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->defineDecisionState(Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;)I

    return-void
.end method

.method public getCachedContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 2

    .line 120
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->contextCache:Ljava/util/concurrent/ConcurrentMap;

    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;

    invoke-direct {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;-><init>()V

    invoke-static {p1, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getCachedContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Ljava/util/concurrent/ConcurrentMap;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    return-object p1
.end method

.method public getContextCacheSize()I
    .locals 1

    .line 116
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->contextCache:Ljava/util/concurrent/ConcurrentMap;

    invoke-interface {v0}, Ljava/util/concurrent/ConcurrentMap;->size()I

    move-result v0

    return v0
.end method

.method public getDecisionState(I)Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;
    .locals 1

    .line 184
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    .line 185
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method

.method public final getDecisionToDFA()[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;
    .locals 1

    .line 125
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    return-object v0
.end method

.method public getExpectedTokens(ILgroovyjarjarantlr4/v4/runtime/RuleContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 4

    if-ltz p1, :cond_3

    .line 232
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_3

    .line 237
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 238
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p1

    const/4 v0, -0x2

    .line 239
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v1

    if-nez v1, :cond_0

    return-object p1

    .line 243
    :cond_0
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v2, 0x0

    new-array v3, v2, [I

    invoke-direct {v1, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 244
    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->addAll(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    .line 245
    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->remove(I)V

    :goto_0
    if-eqz p2, :cond_1

    .line 246
    iget v3, p2, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->invokingState:I

    if-ltz v3, :cond_1

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 247
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    iget v3, p2, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->invokingState:I

    invoke-interface {p1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 248
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    .line 249
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p1

    .line 250
    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->addAll(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    .line 251
    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->remove(I)V

    .line 252
    iget-object p2, p2, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    goto :goto_0

    .line 255
    :cond_1
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result p1

    if-eqz p1, :cond_2

    const/4 p1, -0x1

    .line 256
    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    :cond_2
    return-object v1

    .line 233
    :cond_3
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Invalid state number."

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getNumberOfDecisions()I
    .locals 1

    .line 191
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToState:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method

.method public hasUnicodeSMPTransitions()Z
    .locals 1

    .line 263
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->hasUnicodeSMPTransitions:Z

    return v0
.end method

.method public nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 2

    .line 148
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->nextTokenWithinRule:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    if-eqz v0, :cond_0

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->nextTokenWithinRule:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    return-object p1

    .line 149
    :cond_0
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_LOCAL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    iput-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->nextTokenWithinRule:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    .line 150
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->nextTokenWithinRule:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->setReadonly(Z)V

    .line 151
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->nextTokenWithinRule:Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    return-object p1
.end method

.method public nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 1

    const-string v0, "ctx"

    .line 135
    invoke-static {v0, p2}, Lgroovyjarjarantlr4/v4/runtime/misc/Args;->notNull(Ljava/lang/String;Ljava/lang/Object;)V

    .line 136
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    .line 137
    invoke-virtual {v0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p1

    return-object p1
.end method

.method public removeState(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)V
    .locals 2

    .line 164
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    const/4 v1, 0x0

    invoke-interface {v0, p1, v1}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setHasUnicodeSMPTransitions(Z)V
    .locals 0

    .line 267
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->hasUnicodeSMPTransitions:Z

    return-void
.end method
