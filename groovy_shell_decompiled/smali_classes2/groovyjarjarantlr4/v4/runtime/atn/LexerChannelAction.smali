.class public final Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;
.super Ljava/lang/Object;
.source "LexerChannelAction.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;


# instance fields
.field private final channel:I


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 28
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 29
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;->channel:I

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 3

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 83
    :cond_0
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 87
    :cond_1
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;->channel:I

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;->channel:I

    if-ne v1, p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public execute(Lgroovyjarjarantlr4/v4/runtime/Lexer;)V
    .locals 1

    .line 67
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;->channel:I

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/Lexer;->setChannel(I)V

    return-void
.end method

.method public getActionType()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;
    .locals 1

    .line 47
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;->CHANNEL:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;

    return-object v0
.end method

.method public getChannel()I
    .locals 1

    .line 38
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;->channel:I

    return v0
.end method

.method public hashCode()I
    .locals 2

    .line 72
    invoke-static {}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->initialize()I

    move-result v0

    .line 73
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;->getActionType()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;->ordinal()I

    move-result v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(II)I

    move-result v0

    .line 74
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;->channel:I

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(II)I

    move-result v0

    const/4 v1, 0x2

    .line 75
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->finish(II)I

    move-result v0

    return v0
.end method

.method public isPositionDependent()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    .line 92
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerChannelAction;->channel:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    const-string v1, "channel(%d)"

    invoke-static {v1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
