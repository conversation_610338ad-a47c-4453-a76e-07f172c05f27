.class public final Lgroovyjarjarantlr4/v4/runtime/atn/LexerMoreAction;
.super Ljava/lang/Object;
.source "LexerMoreAction.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/atn/LexerAction;


# static fields
.field public static final INSTANCE:Lgroovyjarjarantlr4/v4/runtime/atn/LexerMoreAction;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 26
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerMoreAction;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerMoreAction;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerMoreAction;->INSTANCE:Lgroovyjarjarantlr4/v4/runtime/atn/LexerMoreAction;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 31
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 0

    if-ne p1, p0, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public execute(Lgroovyjarjarantlr4/v4/runtime/Lexer;)V
    .locals 0

    .line 59
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Lexer;->more()V

    return-void
.end method

.method public getActionType()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;
    .locals 1

    .line 40
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;->MORE:Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;

    return-object v0
.end method

.method public hashCode()I
    .locals 2

    .line 64
    invoke-static {}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->initialize()I

    move-result v0

    .line 65
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerMoreAction;->getActionType()Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/LexerActionType;->ordinal()I

    move-result v1

    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(II)I

    move-result v0

    const/4 v1, 0x1

    .line 66
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->finish(II)I

    move-result v0

    return v0
.end method

.method public isPositionDependent()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    const-string v0, "more"

    return-object v0
.end method
