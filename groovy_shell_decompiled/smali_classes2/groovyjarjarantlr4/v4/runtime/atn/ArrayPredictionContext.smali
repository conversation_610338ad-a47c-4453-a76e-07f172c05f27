.class public Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;
.super Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
.source "ArrayPredictionContext.java"


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field public final parents:[Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

.field public final returnStates:[I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method constructor <init>([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[I)V
    .locals 1

    .line 26
    invoke-static {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->calculateHashCode([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[I)I

    move-result v0

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;-><init>(I)V

    .line 30
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->parents:[Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 31
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    return-void
.end method

.method constructor <init>([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[II)V
    .locals 0

    .line 35
    invoke-direct {p0, p3}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;-><init>(I)V

    .line 39
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->parents:[Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 40
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    return-void
.end method

.method private static appendContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 7

    .line 108
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 109
    invoke-static {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->isEmptyLocal(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 110
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->hasEmpty()Z

    move-result p0

    if-eqz p0, :cond_0

    .line 111
    sget-object p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->EMPTY_LOCAL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    return-object p0

    .line 114
    :cond_0
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    const-string p1, "what to do here?"

    invoke-direct {p0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p0

    :cond_1
    return-object p0

    .line 120
    :cond_2
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_a

    .line 124
    invoke-virtual {p2, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    if-nez v0, :cond_9

    .line 126
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_3

    .line 130
    :cond_3
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v0

    .line 131
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->hasEmpty()Z

    move-result v2

    if-eqz v2, :cond_4

    add-int/lit8 v0, v0, -0x1

    .line 135
    :cond_4
    new-array v2, v0, [Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 136
    new-array v3, v0, [I

    const/4 v4, 0x0

    move v5, v4

    :goto_0
    if-ge v5, v0, :cond_5

    .line 138
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v6

    aput v6, v3, v5

    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    :cond_5
    move v5, v4

    :goto_1
    if-ge v5, v0, :cond_6

    .line 142
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    invoke-static {v6, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->appendContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    aput-object v6, v2, v5

    add-int/lit8 v5, v5, 0x1

    goto :goto_1

    :cond_6
    if-ne v0, v1, :cond_7

    .line 146
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;

    aget-object v1, v2, v4

    aget v2, v3, v4

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)V

    goto :goto_2

    .line 150
    :cond_7
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;

    invoke-direct {v0, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;-><init>([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[I)V

    .line 153
    :goto_2
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->hasEmpty()Z

    move-result v1

    if-eqz v1, :cond_8

    .line 154
    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    goto :goto_3

    :cond_8
    move-object p1, v0

    .line 158
    :goto_3
    invoke-virtual {p2, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-object v0, p1

    :cond_9
    return-object v0

    .line 121
    :cond_a
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    const-string p1, "Appending a tree suffix is not yet supported."

    invoke-direct {p0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method private equals(Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;Ljava/util/Set;)Z
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;",
            "Ljava/util/Set<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;",
            ">;)Z"
        }
    .end annotation

    .line 182
    new-instance v0, Ljava/util/ArrayDeque;

    invoke-direct {v0}, Ljava/util/ArrayDeque;-><init>()V

    .line 183
    new-instance v1, Ljava/util/ArrayDeque;

    invoke-direct {v1}, Ljava/util/ArrayDeque;-><init>()V

    .line 184
    invoke-interface {v0, p0}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 185
    invoke-interface {v1, p1}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 186
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Deque;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_7

    .line 187
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;

    invoke-interface {v0}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    invoke-interface {v1}, Ljava/util/Deque;->pop()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    invoke-direct {p1, v2, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)V

    .line 188
    invoke-interface {p2, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_1

    goto :goto_0

    .line 192
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;->getX()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v2

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v2

    const/4 v3, 0x0

    if-nez v2, :cond_2

    .line 194
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;->getX()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;->getY()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    invoke-virtual {v2, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_0

    return v3

    .line 201
    :cond_2
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;->getY()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v4

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v4

    if-eq v2, v4, :cond_3

    return v3

    :cond_3
    move v4, v3

    :goto_1
    if-ge v4, v2, :cond_0

    .line 207
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;->getX()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v5

    invoke-virtual {v5, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v5

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;->getY()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    invoke-virtual {v6, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v6

    if-eq v5, v6, :cond_4

    return v3

    .line 211
    :cond_4
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;->getX()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v5

    invoke-virtual {v5, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v5

    .line 212
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache$IdentityCommutativePredictionContextOperands;->getY()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    invoke-virtual {v6, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    .line 213
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->hashCode()I

    move-result v7

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->hashCode()I

    move-result v8

    if-eq v7, v8, :cond_5

    return v3

    :cond_5
    if-eq v5, v6, :cond_6

    .line 218
    invoke-interface {v0, v5}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    .line 219
    invoke-interface {v1, v6}, Ljava/util/Deque;->push(Ljava/lang/Object;)V

    :cond_6
    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    :cond_7
    const/4 p1, 0x1

    return p1
.end method


# virtual methods
.method protected addEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 4

    .line 75
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->hasEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p0

    .line 79
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->parents:[Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    array-length v1, v0

    add-int/lit8 v1, v1, 0x1

    invoke-static {v0, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 80
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    array-length v2, v1

    add-int/lit8 v2, v2, 0x1

    invoke-static {v1, v2}, Ljava/util/Arrays;->copyOf([II)[I

    move-result-object v1

    .line 81
    array-length v2, v0

    add-int/lit8 v2, v2, -0x1

    sget-object v3, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    aput-object v3, v0, v2

    .line 82
    array-length v2, v1

    add-int/lit8 v2, v2, -0x1

    const v3, 0x7fffffff

    aput v3, v1, v2

    .line 83
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;

    invoke-direct {v2, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;-><init>([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[I)V

    return-object v2
.end method

.method public appendContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 0

    .line 104
    new-instance p2, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;

    invoke-direct {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;-><init>()V

    invoke-static {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->appendContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    return-object p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 3

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    return p1

    .line 169
    :cond_0
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;

    const/4 v1, 0x0

    if-nez v0, :cond_1

    return v1

    .line 173
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->hashCode()I

    move-result v0

    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result v2

    if-eq v0, v2, :cond_2

    return v1

    .line 177
    :cond_2
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;

    .line 178
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->equals(Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;Ljava/util/Set;)Z

    move-result p1

    return p1
.end method

.method public findReturnState(I)I
    .locals 1

    .line 55
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    invoke-static {v0, p1}, Ljava/util/Arrays;->binarySearch([II)I

    move-result p1

    return p1
.end method

.method public getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 1

    .line 45
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->parents:[Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    aget-object p1, v0, p1

    return-object p1
.end method

.method public getReturnState(I)I
    .locals 1

    .line 50
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    aget p1, v0, p1

    return p1
.end method

.method public hasEmpty()Z
    .locals 3

    .line 70
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    array-length v1, v0

    const/4 v2, 0x1

    sub-int/2addr v1, v2

    aget v0, v0, v1

    const v1, 0x7fffffff

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    return v2
.end method

.method public isEmpty()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method protected removeEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 4

    .line 88
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->hasEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    return-object p0

    .line 92
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    array-length v0, v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_1

    .line 93
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->parents:[Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    const/4 v2, 0x0

    aget-object v1, v1, v2

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    aget v2, v3, v2

    invoke-direct {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)V

    return-object v0

    .line 96
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->parents:[Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    array-length v1, v0

    add-int/lit8 v1, v1, -0x1

    invoke-static {v0, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 97
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    array-length v2, v1

    add-int/lit8 v2, v2, -0x1

    invoke-static {v1, v2}, Ljava/util/Arrays;->copyOf([II)[I

    move-result-object v1

    .line 98
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;

    invoke-direct {v2, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;-><init>([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[I)V

    return-object v2
.end method

.method public size()I
    .locals 1

    .line 60
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    array-length v0, v0

    return v0
.end method
