.class public Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;
.super Ljava/lang/Object;
.source "ConflictInfo.java"


# instance fields
.field private final conflictedAlts:Ljava/util/BitSet;

.field private final exact:Z


# direct methods
.method public constructor <init>(Ljava/util/BitSet;Z)V
    .locals 0

    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 24
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->conflictedAlts:Ljava/util/BitSet;

    .line 25
    iput-boolean p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->exact:Z

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 58
    :cond_0
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 62
    :cond_1
    check-cast p1, <PERSON><PERSON>ovyjarjarantlr4/v4/runtime/atn/ConflictInfo;

    .line 63
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->isExact()Z

    move-result v1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->isExact()Z

    move-result v3

    if-ne v1, v3, :cond_2

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->getConflictedAlts()Ljava/util/BitSet;

    move-result-object v1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->getConflictedAlts()Ljava/util/BitSet;

    move-result-object p1

    invoke-static {v1, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Utils;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public final getConflictedAlts()Ljava/util/BitSet;
    .locals 1

    .line 32
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->conflictedAlts:Ljava/util/BitSet;

    return-object v0
.end method

.method public hashCode()I
    .locals 1

    .line 69
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->getConflictedAlts()Ljava/util/BitSet;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/BitSet;->hashCode()I

    move-result v0

    return v0
.end method

.method public final isExact()Z
    .locals 1

    .line 50
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ConflictInfo;->exact:Z

    return v0
.end method
