.class public final Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopbackState;
.super Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;
.source "StarLoopbackState.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 9
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;-><init>()V

    return-void
.end method


# virtual methods
.method public final getLoopEntryState()Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;
    .locals 1

    const/4 v0, 0x0

    .line 11
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopbackState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v0

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/StarLoopEntryState;

    return-object v0
.end method

.method public getStateType()I
    .locals 1

    const/16 v0, 0x9

    return v0
.end method
