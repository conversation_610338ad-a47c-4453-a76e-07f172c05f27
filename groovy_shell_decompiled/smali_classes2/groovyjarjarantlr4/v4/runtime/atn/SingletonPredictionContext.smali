.class public Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;
.super Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
.source "SingletonPredictionContext.java"


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field public final parent:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

.field public final returnState:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)V
    .locals 1

    .line 18
    invoke-static {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->calculateHashCode(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)I

    move-result v0

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;-><init>(I)V

    .line 20
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->parent:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 21
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->returnState:I

    return-void
.end method


# virtual methods
.method protected addEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 5

    const/4 v0, 0x2

    new-array v1, v0, [Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 63
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->parent:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v2, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    const/4 v4, 0x1

    aput-object v2, v1, v4

    new-array v0, v0, [I

    .line 64
    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->returnState:I

    aput v2, v0, v3

    const v2, 0x7fffffff

    aput v2, v0, v4

    .line 65
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;

    invoke-direct {v2, v1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;-><init>([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[I)V

    return-object v2
.end method

.method public appendContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 1

    .line 58
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->parent:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->appendContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->returnState:I

    invoke-virtual {p2, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->getChild(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    return-object p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 78
    :cond_0
    instance-of v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 82
    :cond_1
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;

    .line 83
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->hashCode()I

    move-result v1

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->hashCode()I

    move-result v3

    if-eq v1, v3, :cond_2

    return v2

    .line 87
    :cond_2
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->returnState:I

    iget v3, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->returnState:I

    if-ne v1, v3, :cond_3

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->parent:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->parent:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_3

    goto :goto_0

    :cond_3
    move v0, v2

    :goto_0
    return v0
.end method

.method public findReturnState(I)I
    .locals 1

    .line 38
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->returnState:I

    if-ne v0, p1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    const/4 p1, -0x1

    :goto_0
    return p1
.end method

.method public getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 0

    .line 27
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->parent:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    return-object p1
.end method

.method public getReturnState(I)I
    .locals 0

    .line 33
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;->returnState:I

    return p1
.end method

.method public hasEmpty()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public isEmpty()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method protected removeEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 0

    return-object p0
.end method

.method public size()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
