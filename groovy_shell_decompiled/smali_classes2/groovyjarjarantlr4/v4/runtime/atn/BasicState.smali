.class public final Lgroovyjarjarantlr4/v4/runtime/atn/BasicState;
.super Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;
.source "BasicState.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 13
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;-><init>()V

    return-void
.end method


# virtual methods
.method public getStateType()I
    .locals 1

    const/4 v0, 0x1

    return v0
.end method
