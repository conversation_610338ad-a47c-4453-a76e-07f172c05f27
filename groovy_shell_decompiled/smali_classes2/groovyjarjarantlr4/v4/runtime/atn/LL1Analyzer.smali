.class public Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;
.super Ljava/lang/Object;
.source "LL1Analyzer.java"


# static fields
.field public static final HIT_PRED:I


# instance fields
.field public final atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V
    .locals 0

    .line 27
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    return-void
.end method


# virtual methods
.method public LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 10

    .line 103
    new-instance v9, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v0, 0x0

    new-array v0, v0, [I

    invoke-direct {v9, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    .line 106
    new-instance v5, Ljava/util/HashSet;

    invoke-direct {v5}, Ljava/util/HashSet;-><init>()V

    new-instance v6, Ljava/util/BitSet;

    invoke-direct {v6}, Ljava/util/BitSet;-><init>()V

    const/4 v7, 0x1

    const/4 v8, 0x1

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, v9

    invoke-virtual/range {v0 .. v8}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->_LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;Ljava/util/Set;Ljava/util/BitSet;ZZ)V

    return-object v9
.end method

.method public LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 2

    .line 80
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStopState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    aget-object v0, v0, v1

    invoke-virtual {p0, p1, v0, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object p1

    return-object p1
.end method

.method protected _LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;Ljava/util/Set;Ljava/util/BitSet;ZZ)V
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            "Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;",
            "Ljava/util/Set<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;",
            ">;",
            "Ljava/util/BitSet;",
            "ZZ)V"
        }
    .end annotation

    move-object/from16 v10, p0

    move-object/from16 v11, p1

    move-object/from16 v0, p3

    move-object/from16 v12, p4

    move-object/from16 v13, p6

    const/4 v14, 0x0

    .line 150
    invoke-static {v11, v14, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->create(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;ILgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    move-result-object v1

    move-object/from16 v15, p5

    .line 151
    invoke-interface {v15, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    return-void

    :cond_0
    const/4 v1, -0x1

    move-object/from16 v9, p2

    if-ne v11, v9, :cond_3

    .line 154
    invoke-static/range {p3 .. p3}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmptyLocal(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Z

    move-result v2

    if-eqz v2, :cond_1

    const/4 v0, -0x2

    .line 155
    invoke-virtual {v12, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    return-void

    .line 157
    :cond_1
    invoke-virtual/range {p3 .. p3}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_3

    if-eqz p8, :cond_2

    .line 159
    invoke-virtual {v12, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    :cond_2
    return-void

    .line 166
    :cond_3
    instance-of v2, v11, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStopState;

    if-eqz v2, :cond_9

    .line 167
    invoke-virtual/range {p3 .. p3}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_5

    invoke-static/range {p3 .. p3}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmptyLocal(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Z

    move-result v2

    if-nez v2, :cond_5

    if-eqz p8, :cond_4

    .line 169
    invoke-virtual {v12, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    :cond_4
    return-void

    .line 175
    :cond_5
    iget v1, v11, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    invoke-virtual {v13, v1}, Ljava/util/BitSet;->get(I)Z

    move-result v16

    .line 177
    :try_start_0
    iget v1, v11, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    invoke-virtual {v13, v1}, Ljava/util/BitSet;->clear(I)V

    move v8, v14

    .line 178
    :goto_0
    invoke-virtual/range {p3 .. p3}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v1

    if-ge v8, v1, :cond_7

    .line 179
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v1

    const v2, 0x7fffffff

    if-ne v1, v2, :cond_6

    move/from16 v17, v8

    goto :goto_1

    .line 183
    :cond_6
    iget-object v1, v10, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-virtual {v0, v8}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v2

    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 185
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v4

    move-object/from16 v1, p0

    move-object/from16 v3, p2

    move-object/from16 v5, p4

    move-object/from16 v6, p5

    move-object/from16 v7, p6

    move/from16 v17, v8

    move/from16 v8, p7

    move/from16 v9, p8

    invoke-virtual/range {v1 .. v9}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->_LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;Ljava/util/Set;Ljava/util/BitSet;ZZ)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :goto_1
    add-int/lit8 v8, v17, 0x1

    move-object/from16 v9, p2

    goto :goto_0

    :cond_7
    if-eqz v16, :cond_9

    .line 190
    iget v1, v11, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    invoke-virtual {v13, v1}, Ljava/util/BitSet;->set(I)V

    goto :goto_2

    :catchall_0
    move-exception v0

    if-eqz v16, :cond_8

    iget v1, v11, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    invoke-virtual {v13, v1}, Ljava/util/BitSet;->set(I)V

    :cond_8
    throw v0

    .line 195
    :cond_9
    :goto_2
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v9

    move v8, v14

    :goto_3
    if-ge v8, v9, :cond_12

    .line 197
    invoke-virtual {v11, v8}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v1

    .line 198
    instance-of v2, v1, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    if-eqz v2, :cond_b

    .line 199
    move-object v7, v1

    check-cast v7, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    .line 200
    iget v2, v7, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->ruleIndex:I

    invoke-virtual {v13, v2}, Ljava/util/BitSet;->get(I)Z

    move-result v2

    if-eqz v2, :cond_a

    move/from16 v17, v8

    move/from16 v18, v9

    goto/16 :goto_6

    .line 204
    :cond_a
    iget-object v2, v7, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v4

    .line 207
    :try_start_1
    iget v2, v7, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->ruleIndex:I

    invoke-virtual {v13, v2}, Ljava/util/BitSet;->set(I)V

    .line 208
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    move-object/from16 v1, p0

    move-object/from16 v3, p2

    move-object/from16 v5, p4

    move-object/from16 v6, p5

    move-object v14, v7

    move-object/from16 v7, p6

    move/from16 v17, v8

    move/from16 v8, p7

    move/from16 v18, v9

    move/from16 v9, p8

    :try_start_2
    invoke-virtual/range {v1 .. v9}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->_LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;Ljava/util/Set;Ljava/util/BitSet;ZZ)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 211
    iget v1, v14, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->ruleIndex:I

    invoke-virtual {v13, v1}, Ljava/util/BitSet;->clear(I)V

    goto :goto_5

    :catchall_1
    move-exception v0

    goto :goto_4

    :catchall_2
    move-exception v0

    move-object v14, v7

    :goto_4
    iget v1, v14, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->ruleIndex:I

    invoke-virtual {v13, v1}, Ljava/util/BitSet;->clear(I)V

    throw v0

    :cond_b
    move/from16 v17, v8

    move/from16 v18, v9

    .line 214
    instance-of v2, v1, Lgroovyjarjarantlr4/v4/runtime/atn/AbstractPredicateTransition;

    if-eqz v2, :cond_d

    if-eqz p7, :cond_c

    .line 216
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-object/from16 v1, p0

    move-object/from16 v3, p2

    move-object/from16 v4, p3

    move-object/from16 v5, p4

    move-object/from16 v6, p5

    move-object/from16 v7, p6

    move/from16 v8, p7

    move/from16 v9, p8

    invoke-virtual/range {v1 .. v9}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->_LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;Ljava/util/Set;Ljava/util/BitSet;ZZ)V

    :goto_5
    const/4 v14, 0x0

    goto :goto_6

    :cond_c
    const/4 v14, 0x0

    .line 219
    invoke-virtual {v12, v14}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->add(I)V

    goto :goto_6

    :cond_d
    const/4 v14, 0x0

    .line 222
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->isEpsilon()Z

    move-result v2

    if-eqz v2, :cond_e

    .line 223
    iget-object v2, v1, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-object/from16 v1, p0

    move-object/from16 v3, p2

    move-object/from16 v4, p3

    move-object/from16 v5, p4

    move-object/from16 v6, p5

    move-object/from16 v7, p6

    move/from16 v8, p7

    move/from16 v9, p8

    invoke-virtual/range {v1 .. v9}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->_LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;Ljava/util/Set;Ljava/util/BitSet;ZZ)V

    goto :goto_6

    .line 225
    :cond_e
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    const-class v3, Lgroovyjarjarantlr4/v4/runtime/atn/WildcardTransition;

    const/4 v4, 0x1

    if-ne v2, v3, :cond_f

    .line 226
    iget-object v1, v10, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->maxTokenType:I

    invoke-static {v4, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v1

    invoke-virtual {v12, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->addAll(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    goto :goto_6

    .line 230
    :cond_f
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->label()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    if-eqz v2, :cond_11

    .line 232
    instance-of v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/NotSetTransition;

    if-eqz v1, :cond_10

    .line 233
    iget-object v1, v10, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->maxTokenType:I

    invoke-static {v4, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v1

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->complement(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    .line 235
    :cond_10
    invoke-virtual {v12, v2}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->addAll(Lgroovyjarjarantlr4/v4/runtime/misc/IntSet;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    :cond_11
    :goto_6
    add-int/lit8 v8, v17, 0x1

    move/from16 v9, v18

    goto/16 :goto_3

    :cond_12
    return-void
.end method

.method public getDecisionLookahead(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)[Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 16

    move-object/from16 v0, p1

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    .line 46
    :cond_0
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v2

    new-array v2, v2, [Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    const/4 v3, 0x0

    move v4, v3

    .line 47
    :goto_0
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->getNumberOfTransitions()I

    move-result v5

    if-ge v4, v5, :cond_3

    .line 48
    new-instance v5, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    new-array v6, v3, [I

    invoke-direct {v5, v6}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;-><init>([I)V

    aput-object v5, v2, v4

    .line 49
    new-instance v12, Ljava/util/HashSet;

    invoke-direct {v12}, Ljava/util/HashSet;-><init>()V

    const/4 v14, 0x0

    .line 51
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v5

    iget-object v8, v5, Lgroovyjarjarantlr4/v4/runtime/atn/Transition;->target:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    const/4 v9, 0x0

    sget-object v10, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_LOCAL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    aget-object v11, v2, v4

    new-instance v13, Ljava/util/BitSet;

    invoke-direct {v13}, Ljava/util/BitSet;-><init>()V

    const/4 v15, 0x0

    move-object/from16 v7, p0

    invoke-virtual/range {v7 .. v15}, Lgroovyjarjarantlr4/v4/runtime/atn/LL1Analyzer;->_LOOK(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;Ljava/util/Set;Ljava/util/BitSet;ZZ)V

    .line 55
    aget-object v5, v2, v4

    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->size()I

    move-result v5

    if-eqz v5, :cond_1

    aget-object v5, v2, v4

    invoke-virtual {v5, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v5

    if-eqz v5, :cond_2

    .line 56
    :cond_1
    aput-object v1, v2, v4

    :cond_2
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_3
    return-object v2
.end method
