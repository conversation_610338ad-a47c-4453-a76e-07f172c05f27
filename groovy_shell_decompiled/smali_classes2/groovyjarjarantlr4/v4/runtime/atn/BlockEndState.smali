.class public final Lgroovyjarjarantlr4/v4/runtime/atn/BlockEndState;
.super Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;
.source "BlockEndState.java"


# instance fields
.field public startState:Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 10
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;-><init>()V

    return-void
.end method


# virtual methods
.method public getStateType()I
    .locals 1

    const/16 v0, 0x8

    return v0
.end method
