.class public Lgroovyjarjarantlr4/v4/runtime/atn/OrderedATNConfigSet;
.super Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;
.source "OrderedATNConfigSet.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 15
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;-><init>()V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;Z)V
    .locals 0

    .line 19
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;Z)V

    return-void
.end method


# virtual methods
.method protected canMerge(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;JLgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z
    .locals 0

    .line 39
    invoke-virtual {p1, p4}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->equals(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)Z

    move-result p1

    return p1
.end method

.method public clone(Z)Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;
    .locals 1

    .line 24
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/OrderedATNConfigSet;

    invoke-direct {v0, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/OrderedATNConfigSet;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;Z)V

    if-nez p1, :cond_0

    .line 25
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/OrderedATNConfigSet;->isReadOnly()Z

    move-result p1

    if-eqz p1, :cond_0

    .line 26
    invoke-virtual {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/OrderedATNConfigSet;->addAll(Ljava/util/Collection;)Z

    :cond_0
    return-object v0
.end method

.method protected getKey(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)J
    .locals 2

    .line 34
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->hashCode()I

    move-result p1

    int-to-long v0, p1

    return-wide v0
.end method
