.class public abstract Lgroovyjarjarantlr4/v4/runtime/atn/BlockStartState;
.super Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;
.source "BlockStartState.java"


# instance fields
.field public endState:Lgroovyjarjarantlr4/v4/runtime/atn/BlockEndState;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 10
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;-><init>()V

    return-void
.end method
