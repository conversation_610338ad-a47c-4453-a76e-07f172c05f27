.class public abstract Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
.super Ljava/lang/Object;
.source "PredictionContext.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityEqualityComparator;,
        Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z = false

.field public static final EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

.field public static final EMPTY_FULL_STATE_KEY:I = 0x7fffffff

.field public static final EMPTY_LOCAL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

.field public static final EMPTY_LOCAL_STATE_KEY:I = -0x80000000

.field private static final INITIAL_HASH:I = 0x1


# instance fields
.field private final cachedHashCode:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 23
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;->LOCAL_CONTEXT:Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_LOCAL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 25
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;->FULL_CONTEXT:Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    return-void
.end method

.method protected constructor <init>(I)V
    .locals 0

    .line 55
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 56
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->cachedHashCode:I

    return-void
.end method

.method private static addEmptyContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 0

    .line 123
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->addEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p0

    return-object p0
.end method

.method protected static calculateEmptyHashCode()I
    .locals 2

    const/4 v0, 0x1

    .line 60
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->initialize(I)I

    move-result v0

    const/4 v1, 0x0

    .line 61
    invoke-static {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->finish(II)I

    move-result v0

    return v0
.end method

.method protected static calculateHashCode(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)I
    .locals 1

    const/4 v0, 0x1

    .line 66
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->initialize(I)I

    move-result v0

    .line 67
    invoke-static {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(ILjava/lang/Object;)I

    move-result p0

    .line 68
    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(II)I

    move-result p0

    const/4 p1, 0x2

    .line 69
    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->finish(II)I

    move-result p0

    return p0
.end method

.method protected static calculateHashCode([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[I)I
    .locals 5

    const/4 v0, 0x1

    .line 74
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->initialize(I)I

    move-result v0

    .line 76
    array-length v1, p0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_0

    aget-object v4, p0, v3

    .line 77
    invoke-static {v0, v4}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(ILjava/lang/Object;)I

    move-result v0

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 80
    :cond_0
    array-length v1, p1

    :goto_1
    if-ge v2, v1, :cond_1

    aget v3, p1, v2

    .line 81
    invoke-static {v0, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->update(II)I

    move-result v0

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 84
    :cond_1
    array-length p0, p0

    mul-int/lit8 p0, p0, 0x2

    invoke-static {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/misc/MurmurHash;->finish(II)I

    move-result p0

    return p0
.end method

.method public static fromRuleContext(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 1

    const/4 v0, 0x1

    .line 102
    invoke-static {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->fromRuleContext(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/RuleContext;Z)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p0

    return-object p0
.end method

.method public static fromRuleContext(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/RuleContext;Z)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 1

    .line 106
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    if-eqz p2, :cond_0

    .line 107
    sget-object p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    goto :goto_0

    :cond_0
    sget-object p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_LOCAL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    :goto_0
    return-object p0

    .line 111
    :cond_1
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    if-eqz v0, :cond_2

    .line 112
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    invoke-static {p0, v0, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->fromRuleContext(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Lgroovyjarjarantlr4/v4/runtime/RuleContext;Z)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p2

    goto :goto_1

    :cond_2
    if-eqz p2, :cond_3

    .line 114
    sget-object p2, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    goto :goto_1

    :cond_3
    sget-object p2, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_LOCAL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 117
    :goto_1
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->invokingState:I

    invoke-interface {p0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    const/4 p1, 0x0

    .line 118
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    .line 119
    iget-object p0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    iget p0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p2, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p0

    return-object p0
.end method

.method public static getCachedContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Ljava/util/concurrent/ConcurrentMap;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            "Ljava/util/concurrent/ConcurrentMap<",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            ">;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;",
            ")",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;"
        }
    .end annotation

    .line 239
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p0

    .line 243
    :cond_0
    invoke-virtual {p2, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    if-eqz v0, :cond_1

    return-object v0

    .line 248
    :cond_1
    invoke-interface {p1, p0}, Ljava/util/concurrent/ConcurrentMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    if-eqz v0, :cond_2

    .line 250
    invoke-virtual {p2, p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object v0

    .line 255
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v0

    new-array v0, v0, [Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    const/4 v1, 0x0

    move v2, v1

    move v3, v2

    .line 256
    :goto_0
    array-length v4, v0

    const/4 v5, 0x1

    if-ge v2, v4, :cond_7

    .line 257
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v4

    invoke-static {v4, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getCachedContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Ljava/util/concurrent/ConcurrentMap;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v4

    if-nez v3, :cond_3

    .line 258
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    if-eq v4, v6, :cond_6

    :cond_3
    if-nez v3, :cond_5

    .line 260
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v0

    new-array v0, v0, [Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move v3, v1

    .line 261
    :goto_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v6

    if-ge v3, v6, :cond_4

    .line 262
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    aput-object v6, v0, v3

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_4
    move v3, v5

    .line 268
    :cond_5
    aput-object v4, v0, v2

    :cond_6
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_7
    if-nez v3, :cond_9

    .line 273
    invoke-interface {p1, p0, p0}, Ljava/util/concurrent/ConcurrentMap;->putIfAbsent(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    if-eqz p1, :cond_8

    goto :goto_2

    :cond_8
    move-object p1, p0

    .line 274
    :goto_2
    invoke-virtual {p2, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p0

    .line 280
    :cond_9
    array-length v2, v0

    if-ne v2, v5, :cond_a

    .line 281
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;

    aget-object v0, v0, v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v1

    invoke-direct {v2, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)V

    goto :goto_3

    .line 284
    :cond_a
    move-object v1, p0

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;

    .line 285
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;->returnStates:[I

    iget v3, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->cachedHashCode:I

    invoke-direct {v2, v0, v1, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;-><init>([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[II)V

    .line 288
    :goto_3
    invoke-interface {p1, v2, v2}, Ljava/util/concurrent/ConcurrentMap;->putIfAbsent(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    if-eqz p1, :cond_b

    move-object v0, p1

    goto :goto_4

    :cond_b
    move-object v0, v2

    .line 289
    :goto_4
    invoke-virtual {p2, v2, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz p1, :cond_c

    goto :goto_5

    :cond_c
    move-object p1, v2

    .line 290
    :goto_5
    invoke-virtual {p2, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext$IdentityHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object v2
.end method

.method public static isEmptyLocal(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Z
    .locals 1

    .line 232
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_LOCAL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    if-ne p0, v0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 1

    .line 131
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->UNCACHED:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;

    invoke-static {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p0

    return-object p0
.end method

.method static join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 16

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    if-ne v0, v1, :cond_0

    return-object v0

    .line 139
    :cond_0
    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_2

    .line 140
    invoke-static/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmptyLocal(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Z

    move-result v2

    if-eqz v2, :cond_1

    goto :goto_0

    :cond_1
    invoke-static/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->addEmptyContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v0

    :goto_0
    return-object v0

    .line 141
    :cond_2
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_4

    .line 142
    invoke-static/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmptyLocal(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Z

    move-result v2

    if-eqz v2, :cond_3

    move-object v0, v1

    goto :goto_1

    :cond_3
    invoke-static/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->addEmptyContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v0

    :goto_1
    return-object v0

    .line 145
    :cond_4
    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v3

    .line 146
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v4

    const/4 v5, 0x1

    const/4 v6, 0x0

    if-ne v3, v5, :cond_7

    if-ne v4, v5, :cond_7

    .line 147
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v7

    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v8

    if-ne v7, v8, :cond_7

    .line 148
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v3

    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v2

    .line 149
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v3

    if-ne v2, v3, :cond_5

    return-object v0

    .line 151
    :cond_5
    invoke-virtual {v1, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v3

    if-ne v2, v3, :cond_6

    return-object v1

    .line 154
    :cond_6
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v0

    invoke-virtual {v2, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v0

    return-object v0

    :cond_7
    add-int v7, v3, v4

    .line 159
    new-array v8, v7, [Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 160
    new-array v9, v7, [I

    move v13, v5

    move v14, v13

    move v10, v6

    move v11, v10

    move v12, v11

    :goto_2
    if-ge v10, v3, :cond_c

    if-ge v11, v4, :cond_c

    .line 166
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v15

    invoke-virtual {v1, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v6

    if-ne v15, v6, :cond_a

    .line 167
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    invoke-virtual {v1, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v15

    invoke-virtual {v2, v6, v15}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->join(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    aput-object v6, v8, v12

    .line 168
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v6

    aput v6, v9, v12

    if-eqz v13, :cond_8

    .line 169
    aget-object v6, v8, v12

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v13

    if-ne v6, v13, :cond_8

    move v13, v5

    goto :goto_3

    :cond_8
    const/4 v13, 0x0

    :goto_3
    if-eqz v14, :cond_9

    .line 170
    aget-object v6, v8, v12

    invoke-virtual {v1, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v14

    if-ne v6, v14, :cond_9

    move v14, v5

    goto :goto_4

    :cond_9
    const/4 v14, 0x0

    :goto_4
    add-int/lit8 v10, v10, 0x1

    add-int/lit8 v11, v11, 0x1

    goto :goto_5

    .line 174
    :cond_a
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v6

    invoke-virtual {v1, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v15

    if-ge v6, v15, :cond_b

    .line 175
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    aput-object v6, v8, v12

    .line 176
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v6

    aput v6, v9, v12

    add-int/lit8 v10, v10, 0x1

    const/4 v14, 0x0

    goto :goto_5

    .line 182
    :cond_b
    invoke-virtual {v1, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v6

    aput-object v6, v8, v12

    .line 183
    invoke-virtual {v1, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v6

    aput v6, v9, v12

    add-int/lit8 v11, v11, 0x1

    const/4 v13, 0x0

    :goto_5
    add-int/lit8 v12, v12, 0x1

    const/4 v6, 0x0

    goto :goto_2

    :cond_c
    :goto_6
    if-ge v10, v3, :cond_d

    .line 192
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v2

    aput-object v2, v8, v12

    .line 193
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v2

    aput v2, v9, v12

    add-int/lit8 v10, v10, 0x1

    add-int/lit8 v12, v12, 0x1

    const/4 v14, 0x0

    goto :goto_6

    :cond_d
    :goto_7
    if-ge v11, v4, :cond_e

    .line 200
    invoke-virtual {v1, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v2

    aput-object v2, v8, v12

    .line 201
    invoke-virtual {v1, v11}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v2

    aput v2, v9, v12

    add-int/lit8 v11, v11, 0x1

    add-int/lit8 v12, v12, 0x1

    const/4 v13, 0x0

    goto :goto_7

    :cond_e
    if-eqz v13, :cond_f

    return-object v0

    :cond_f
    if-eqz v14, :cond_10

    return-object v1

    :cond_10
    if-ge v12, v7, :cond_11

    .line 215
    invoke-static {v8, v12}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object v0

    move-object v8, v0

    check-cast v8, [Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    .line 216
    invoke-static {v9, v12}, Ljava/util/Arrays;->copyOf([II)[I

    move-result-object v9

    .line 219
    :cond_11
    array-length v0, v8

    if-nez v0, :cond_12

    .line 221
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    return-object v0

    .line 223
    :cond_12
    array-length v0, v8

    if-ne v0, v5, :cond_13

    .line 224
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;

    const/4 v1, 0x0

    aget-object v2, v8, v1

    aget v1, v9, v1

    invoke-direct {v0, v2, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)V

    return-object v0

    .line 227
    :cond_13
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;

    invoke-direct {v0, v8, v9}, Lgroovyjarjarantlr4/v4/runtime/atn/ArrayPredictionContext;-><init>([Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;[I)V

    return-object v0
.end method

.method private static removeEmptyContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 0

    .line 127
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->removeEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method protected abstract addEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
.end method

.method public appendContext(ILgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 1

    .line 296
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->appendContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    return-object p1
.end method

.method public abstract appendContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
.end method

.method public abstract equals(Ljava/lang/Object;)Z
.end method

.method public abstract findReturnState(I)I
.end method

.method public getChild(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 1

    .line 302
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;

    invoke-direct {v0, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/SingletonPredictionContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)V

    return-object v0
.end method

.method public abstract getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
.end method

.method public abstract getReturnState(I)I
.end method

.method public abstract hasEmpty()Z
.end method

.method public final hashCode()I
    .locals 1

    .line 311
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->cachedHashCode:I

    return v0
.end method

.method public abstract isEmpty()Z
.end method

.method protected abstract removeEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
.end method

.method public abstract size()I
.end method

.method public toStrings(Lgroovyjarjarantlr4/v4/runtime/Recognizer;I)[Ljava/lang/String;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "**>;I)[",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 323
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->EMPTY_FULL:Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    invoke-virtual {p0, p1, v0, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->toStrings(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)[Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public toStrings(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)[Ljava/lang/String;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "**>;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            "I)[",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 327
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    move v2, v1

    .line 335
    :goto_0
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "["

    .line 336
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v4, 0x1

    move-object v5, p0

    move v8, p3

    move v7, v1

    move v6, v4

    .line 337
    :goto_1
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v9

    if-nez v9, :cond_8

    if-eq v5, p2, :cond_8

    .line 339
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v9

    if-lez v9, :cond_3

    move v9, v4

    :goto_2
    shl-int v10, v4, v9

    .line 341
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v11

    if-ge v10, v11, :cond_0

    add-int/lit8 v9, v9, 0x1

    goto :goto_2

    :cond_0
    add-int/lit8 v10, v10, -0x1

    shr-int v11, v2, v7

    and-int/2addr v10, v11

    .line 347
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v11

    sub-int/2addr v11, v4

    if-lt v10, v11, :cond_1

    move v11, v4

    goto :goto_3

    :cond_1
    move v11, v1

    :goto_3
    and-int/2addr v6, v11

    .line 348
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->size()I

    move-result v11

    if-lt v10, v11, :cond_2

    goto :goto_6

    :cond_2
    add-int/2addr v7, v9

    goto :goto_4

    :cond_3
    move v10, v1

    :goto_4
    const/16 v9, 0x20

    if-eqz p1, :cond_5

    .line 355
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->length()I

    move-result v11

    if-le v11, v4, :cond_4

    .line 357
    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 360
    :cond_4
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v9

    .line 361
    iget-object v9, v9, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-interface {v9, v8}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 362
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;->getRuleNames()[Ljava/lang/String;

    move-result-object v9

    iget v8, v8, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->ruleIndex:I

    aget-object v8, v9, v8

    .line 363
    invoke-virtual {v3, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_5

    .line 365
    :cond_5
    invoke-virtual {v5, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v8

    const v11, 0x7fffffff

    if-eq v8, v11, :cond_7

    .line 366
    invoke-virtual {v5}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->isEmpty()Z

    move-result v8

    if-nez v8, :cond_7

    .line 367
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->length()I

    move-result v8

    if-le v8, v4, :cond_6

    .line 369
    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 372
    :cond_6
    invoke-virtual {v5, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v8

    invoke-virtual {v3, v8}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 375
    :cond_7
    :goto_5
    invoke-virtual {v5, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getReturnState(I)I

    move-result v8

    .line 376
    invoke-virtual {v5, v10}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;->getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object v5

    goto/16 :goto_1

    :cond_8
    const-string v4, "]"

    .line 378
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 379
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    if-eqz v6, :cond_9

    .line 386
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result p1

    new-array p1, p1, [Ljava/lang/String;

    invoke-interface {v0, p1}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/String;

    return-object p1

    :cond_9
    :goto_6
    add-int/lit8 v2, v2, 0x1

    goto/16 :goto_0
.end method
