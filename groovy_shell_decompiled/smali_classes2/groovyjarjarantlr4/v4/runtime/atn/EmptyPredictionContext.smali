.class public final Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;
.super Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
.source "EmptyPredictionContext.java"


# static fields
.field public static final FULL_CONTEXT:Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;

.field public static final LOCAL_CONTEXT:Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;


# instance fields
.field private final fullContext:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 12
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;-><init>(Z)V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;->LOCAL_CONTEXT:Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;

    .line 13
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;

    const/4 v1, 0x1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;-><init>(Z)V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;->FULL_CONTEXT:Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;

    return-void
.end method

.method private constructor <init>(Z)V
    .locals 1

    .line 18
    invoke-static {}, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;->calculateEmptyHashCode()I

    move-result v0

    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;-><init>(I)V

    .line 19
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;->fullContext:Z

    return-void
.end method


# virtual methods
.method protected addEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 0

    return-object p0
.end method

.method public appendContext(ILgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 0

    .line 58
    invoke-virtual {p2, p0, p1}, Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;->getChild(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;

    move-result-object p1

    return-object p1
.end method

.method public appendContext(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContextCache;)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 0

    return-object p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 0

    if-ne p0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public findReturnState(I)I
    .locals 0

    const/4 p1, -0x1

    return p1
.end method

.method public getParent(I)Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 0

    .line 38
    new-instance p1, Ljava/lang/IndexOutOfBoundsException;

    invoke-direct {p1}, Ljava/lang/IndexOutOfBoundsException;-><init>()V

    throw p1
.end method

.method public getReturnState(I)I
    .locals 0

    .line 43
    new-instance p1, Ljava/lang/IndexOutOfBoundsException;

    invoke-direct {p1}, Ljava/lang/IndexOutOfBoundsException;-><init>()V

    throw p1
.end method

.method public hasEmpty()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public isEmpty()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public isFullContext()Z
    .locals 1

    .line 23
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/EmptyPredictionContext;->fullContext:Z

    return v0
.end method

.method protected removeEmptyContext()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;
    .locals 2

    .line 33
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Cannot remove the empty context from itself."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public size()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public toStrings(Lgroovyjarjarantlr4/v4/runtime/Recognizer;I)[Ljava/lang/String;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "**>;I)[",
            "Ljava/lang/String;"
        }
    .end annotation

    const-string p1, "[]"

    .line 83
    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public toStrings(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;I)[Ljava/lang/String;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "**>;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/PredictionContext;",
            "I)[",
            "Ljava/lang/String;"
        }
    .end annotation

    const-string p1, "[]"

    .line 88
    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
