.class Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet$1;
.super Ljava/lang/Object;
.source "ATNConfigSet.java"

# interfaces
.implements Ljava/util/Comparator;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;->toString(Z)Ljava/lang/String;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Comparator<",
        "Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V
    .locals 0

    .line 444
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet$1;->this$0:Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public compare(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)I
    .locals 2

    .line 447
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result v0

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result v1

    if-eq v0, v1, :cond_0

    .line 448
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result p1

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getAlt()I

    move-result p2

    sub-int/2addr p1, p2

    return p1

    .line 450
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v0

    iget v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object v1

    iget v1, v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    if-eq v0, v1, :cond_1

    .line 451
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object p1

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getState()Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    move-result-object p2

    iget p2, p2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->stateNumber:I

    sub-int/2addr p1, p2

    return p1

    .line 454
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getSemanticContext()Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;->getSemanticContext()Lgroovyjarjarantlr4/v4/runtime/atn/SemanticContext;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/String;->compareTo(Ljava/lang/String;)I

    move-result p1

    return p1
.end method

.method public bridge synthetic compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    .line 444
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet$1;->compare(Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfig;)I

    move-result p1

    return p1
.end method
