.class public abstract Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;
.super Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;
.source "DecisionState.java"


# instance fields
.field public decision:I

.field public nonGreedy:Z

.field public sll:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 9
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;-><init>()V

    const/4 v0, -0x1

    .line 10
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/atn/DecisionState;->decision:I

    return-void
.end method
