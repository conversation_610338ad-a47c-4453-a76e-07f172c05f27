.class final Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;
.super Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
.source "CodePointCharStream.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "CodePoint8BitCharStream"
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field private final byteArray:[B


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 145
    const-class v0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    return-void
.end method

.method private constructor <init>(IILjava/lang/String;[BI)V
    .locals 0

    const/4 p5, 0x0

    .line 149
    invoke-direct {p0, p1, p2, p3, p5}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;-><init>(IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;)V

    .line 152
    iput-object p4, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->byteArray:[B

    return-void
.end method

.method synthetic constructor <init>(IILjava/lang/String;[BILgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;)V
    .locals 0

    .line 145
    invoke-direct/range {p0 .. p5}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;-><init>(IILjava/lang/String;[BI)V

    return-void
.end method


# virtual methods
.method public LA(I)I
    .locals 3

    .line 170
    invoke-static {p1}, Ljava/lang/Integer;->signum(I)I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_3

    if-eqz v0, :cond_2

    const/4 v2, 0x1

    if-ne v0, v2, :cond_1

    .line 181
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->position:I

    add-int/2addr v0, p1

    sub-int/2addr v0, v2

    .line 182
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->size:I

    if-lt v0, p1, :cond_0

    return v1

    .line 185
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->byteArray:[B

    aget-byte p1, p1, v0

    :goto_0
    and-int/lit16 p1, p1, 0xff

    return p1

    .line 187
    :cond_1
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "Not reached"

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_2
    const/4 p1, 0x0

    return p1

    .line 172
    :cond_3
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->position:I

    add-int/2addr v0, p1

    if-gez v0, :cond_4

    return v1

    .line 176
    :cond_4
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->byteArray:[B

    aget-byte p1, p1, v0

    goto :goto_0
.end method

.method getInternalStorage()Ljava/lang/Object;
    .locals 1

    .line 192
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->byteArray:[B

    return-object v0
.end method

.method public getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;
    .locals 4

    .line 158
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->size:I

    invoke-static {v0, v1}, Ljava/lang/Math;->min(II)I

    move-result v0

    .line 159
    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    sub-int/2addr v1, p1

    add-int/lit8 v1, v1, 0x1

    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->size:I

    sub-int/2addr p1, v0

    invoke-static {v1, p1}, Ljava/lang/Math;->min(II)I

    move-result p1

    .line 164
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;->byteArray:[B

    const-string v3, "ISO-8859-1"

    invoke-static {v3}, Ljava/nio/charset/Charset;->forName(Ljava/lang/String;)Ljava/nio/charset/Charset;

    move-result-object v3

    invoke-direct {v1, v2, v0, p1, v3}, Ljava/lang/String;-><init>([BIILjava/nio/charset/Charset;)V

    return-object v1
.end method
