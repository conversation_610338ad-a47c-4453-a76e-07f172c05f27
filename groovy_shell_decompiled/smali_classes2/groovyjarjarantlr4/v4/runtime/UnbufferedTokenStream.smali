.class public Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;
.super Ljava/lang/Object;
.source "UnbufferedTokenStream.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/TokenStream;


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field protected currentTokenIndex:I

.field protected lastToken:Lgroovyjarjarantlr4/v4/runtime/Token;

.field protected lastTokenBufferStart:Lgroovyjarjarantlr4/v4/runtime/Token;

.field protected n:I

.field protected numMarkers:I

.field protected p:I

.field protected tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

.field protected tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/TokenSource;)V
    .locals 1

    const/16 v0, 0x100

    .line 69
    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;-><init>(Lgroovyjarjarantlr4/v4/runtime/TokenSource;I)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/TokenSource;I)V
    .locals 1

    .line 72
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 37
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    .line 45
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->numMarkers:I

    .line 66
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->currentTokenIndex:I

    .line 73
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    .line 74
    new-array p1, p2, [Lgroovyjarjarantlr4/v4/runtime/Token;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 75
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    const/4 p1, 0x1

    .line 76
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->fill(I)I

    return-void
.end method


# virtual methods
.method public LA(I)I
    .locals 0

    .line 111
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result p1

    return p1
.end method

.method public LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 3

    const/4 v0, -0x1

    if-ne p1, v0, :cond_0

    .line 92
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object p1

    .line 95
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->sync(I)V

    .line 96
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    add-int/2addr v0, p1

    add-int/lit8 v0, v0, -0x1

    if-ltz v0, :cond_2

    .line 101
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    if-lt v0, p1, :cond_1

    .line 103
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    add-int/lit8 p1, p1, -0x1

    aget-object p1, v0, p1

    return-object p1

    .line 106
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    aget-object p1, p1, v0

    return-object p1

    .line 98
    :cond_2
    new-instance v0, Ljava/lang/IndexOutOfBoundsException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "LT("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ") gives negative index"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method protected add(Lgroovyjarjarantlr4/v4/runtime/Token;)V
    .locals 3

    .line 192
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    array-length v2, v1

    if-lt v0, v2, :cond_0

    .line 193
    array-length v0, v1

    mul-int/lit8 v0, v0, 0x2

    invoke-static {v1, v0}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/runtime/Token;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 196
    :cond_0
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/WritableToken;

    if-eqz v0, :cond_1

    .line 197
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/WritableToken;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->getBufferStartIndex()I

    move-result v1

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    add-int/2addr v1, v2

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/WritableToken;->setTokenIndex(I)V

    .line 200
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    add-int/lit8 v2, v1, 0x1

    iput v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    aput-object p1, v0, v1

    return-void
.end method

.method public consume()V
    .locals 5

    const/4 v0, 0x1

    .line 143
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->LA(I)I

    move-result v1

    const/4 v2, -0x1

    if-eq v1, v2, :cond_1

    .line 148
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    iget v3, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    aget-object v1, v1, v3

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 151
    iget v4, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    sub-int/2addr v4, v0

    if-ne v3, v4, :cond_0

    iget v3, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->numMarkers:I

    if-nez v3, :cond_0

    const/4 v3, 0x0

    .line 152
    iput v3, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    .line 153
    iput v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    .line 154
    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastTokenBufferStart:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 157
    :cond_0
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    add-int/2addr v1, v0

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    .line 158
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->currentTokenIndex:I

    add-int/2addr v1, v0

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->currentTokenIndex:I

    .line 159
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->sync(I)V

    return-void

    .line 144
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "cannot consume EOF"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method protected fill(I)I
    .locals 3

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p1, :cond_1

    .line 180
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    if-lez v1, :cond_0

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    add-int/lit8 v1, v1, -0x1

    aget-object v1, v2, v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    return v0

    .line 184
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/TokenSource;->nextToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v1

    .line 185
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->add(Lgroovyjarjarantlr4/v4/runtime/Token;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return p1
.end method

.method public get(I)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 4

    .line 81
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->getBufferStartIndex()I

    move-result v0

    if-lt p1, v0, :cond_0

    .line 82
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    add-int/2addr v1, v0

    if-ge p1, v1, :cond_0

    .line 86
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    sub-int/2addr p1, v0

    aget-object p1, v1, p1

    return-object p1

    .line 83
    :cond_0
    new-instance v1, Ljava/lang/IndexOutOfBoundsException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "get("

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, ") outside buffer: "

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, ".."

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    add-int/2addr v0, v2

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, p1}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw v1
.end method

.method protected final getBufferStartIndex()I
    .locals 2

    .line 314
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->currentTokenIndex:I

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    sub-int/2addr v0, v1

    return v0
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 285
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenSource;->getSourceName()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getText()Ljava/lang/String;
    .locals 1

    const-string v0, ""

    return-object v0
.end method

.method public getText(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Ljava/lang/String;
    .locals 0

    .line 128
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->getSourceInterval()Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;
    .locals 5

    .line 291
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->getBufferStartIndex()I

    move-result v0

    .line 292
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    array-length v1, v1

    add-int/2addr v1, v0

    add-int/lit8 v1, v1, -0x1

    .line 294
    iget v2, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    .line 295
    iget v3, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-lt v2, v0, :cond_1

    if-gt v3, v1, :cond_1

    sub-int/2addr v2, v0

    sub-int/2addr v3, v0

    .line 304
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    :goto_0
    if-gt v2, v3, :cond_0

    .line 306
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    aget-object v0, v0, v2

    .line 307
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 310
    :cond_0
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 297
    :cond_1
    new-instance v2, Ljava/lang/UnsupportedOperationException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "interval "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v3, " not in token buffer window: "

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ".."

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v2
.end method

.method public getText(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 134
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_0

    instance-of v0, p2, Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_0

    .line 135
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result p1

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result p2

    invoke-static {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 138
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "The specified start and stop symbols are not supported."

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;
    .locals 1

    .line 116
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    return-object v0
.end method

.method public index()I
    .locals 1

    .line 244
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->currentTokenIndex:I

    return v0
.end method

.method public mark()I
    .locals 2

    .line 212
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->numMarkers:I

    if-nez v0, :cond_0

    .line 213
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastTokenBufferStart:Lgroovyjarjarantlr4/v4/runtime/Token;

    :cond_0
    neg-int v1, v0

    add-int/lit8 v1, v1, -0x1

    add-int/lit8 v0, v0, 0x1

    .line 217
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->numMarkers:I

    return v1
.end method

.method public release(I)V
    .locals 3

    .line 223
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->numMarkers:I

    neg-int v1, v0

    if-ne p1, v1, :cond_2

    add-int/lit8 v0, v0, -0x1

    .line 228
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->numMarkers:I

    if-nez v0, :cond_1

    .line 230
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    if-lez p1, :cond_0

    .line 233
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    sub-int/2addr v1, p1

    const/4 v2, 0x0

    invoke-static {v0, p1, v0, v2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 234
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    sub-int/2addr p1, v0

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    .line 235
    iput v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    .line 238
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastTokenBufferStart:Lgroovyjarjarantlr4/v4/runtime/Token;

    :cond_1
    return-void

    .line 225
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "release() called with an invalid marker."

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public seek(I)V
    .locals 4

    .line 249
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->currentTokenIndex:I

    if-ne p1, v0, :cond_0

    return-void

    :cond_0
    if-le p1, v0, :cond_1

    sub-int v0, p1, v0

    .line 254
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->sync(I)V

    .line 255
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->getBufferStartIndex()I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    add-int/2addr v0, v1

    add-int/lit8 v0, v0, -0x1

    invoke-static {p1, v0}, Ljava/lang/Math;->min(II)I

    move-result p1

    .line 258
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->getBufferStartIndex()I

    move-result v0

    sub-int v1, p1, v0

    if-ltz v1, :cond_4

    .line 263
    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    if-ge v1, v2, :cond_3

    .line 268
    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    .line 269
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->currentTokenIndex:I

    if-nez v1, :cond_2

    .line 271
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastTokenBufferStart:Lgroovyjarjarantlr4/v4/runtime/Token;

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    goto :goto_0

    .line 274
    :cond_2
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->tokens:[Lgroovyjarjarantlr4/v4/runtime/Token;

    add-int/lit8 v1, v1, -0x1

    aget-object p1, p1, v1

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->lastToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    :goto_0
    return-void

    .line 264
    :cond_3
    new-instance v1, Ljava/lang/UnsupportedOperationException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "seek to index outside buffer: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, " not in "

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, ".."

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    add-int/2addr v0, v2

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v1

    .line 261
    :cond_4
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "cannot seek to negative index "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public size()I
    .locals 2

    .line 280
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Unbuffered stream cannot know its size"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method protected sync(I)V
    .locals 1

    .line 167
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->p:I

    add-int/2addr v0, p1

    add-int/lit8 v0, v0, -0x1

    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->n:I

    sub-int/2addr v0, p1

    add-int/lit8 v0, v0, 0x1

    if-lez v0, :cond_0

    .line 169
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedTokenStream;->fill(I)I

    :cond_0
    return-void
.end method
