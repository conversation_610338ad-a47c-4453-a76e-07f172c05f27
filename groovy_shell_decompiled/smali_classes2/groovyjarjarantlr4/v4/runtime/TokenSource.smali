.class public interface abstract Lgroovyjarjarantlr4/v4/runtime/TokenSource;
.super Ljava/lang/Object;
.source "TokenSource.java"


# virtual methods
.method public abstract getCharPositionInLine()I
.end method

.method public abstract getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;
.end method

.method public abstract getLine()I
.end method

.method public abstract getSourceName()Ljava/lang/String;
.end method

.method public abstract getTokenFactory()Lgroovyjarjarantlr4/v4/runtime/TokenFactory;
.end method

.method public abstract nextToken()Lgroovyjarjarantlr4/v4/runtime/Token;
.end method

.method public abstract setTokenFactory(Lgroovyjarjarantlr4/v4/runtime/TokenFactory;)V
.end method
