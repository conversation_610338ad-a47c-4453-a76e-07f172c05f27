.class public Lgroovyjarjarantlr4/v4/runtime/BaseErrorListener;
.super Ljava/lang/Object;
.source "BaseErrorListener.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public reportAmbiguity(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IIZLjava/util/BitSet;Lgroovyjarjarantlr4/v4/runtime/atn/ATNConfigSet;)V
    .locals 0

    return-void
.end method

.method public reportAttemptingFullContext(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IILjava/util/BitSet;Lgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;)V
    .locals 0

    return-void
.end method

.method public reportContextSensitivity(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;IIILgroovyjarjarantlr4/v4/runtime/atn/SimulatorState;)V
    .locals 0

    return-void
.end method

.method public syntaxError(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/Token;IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T::",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "TT;*>;TT;II",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/RecognitionException;",
            ")V"
        }
    .end annotation

    return-void
.end method

.method public bridge synthetic syntaxError(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Ljava/lang/Object;IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 0

    .line 23
    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-virtual/range {p0 .. p6}, Lgroovyjarjarantlr4/v4/runtime/BaseErrorListener;->syntaxError(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Lgroovyjarjarantlr4/v4/runtime/Token;IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    return-void
.end method
