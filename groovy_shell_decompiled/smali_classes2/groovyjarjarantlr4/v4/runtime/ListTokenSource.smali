.class public Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;
.super Ljava/lang/Object;
.source "ListTokenSource.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/TokenSource;


# instance fields
.field private _factory:Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

.field protected eofToken:Lgroovyjarjarantlr4/v4/runtime/Token;

.field protected i:I

.field private final sourceName:Ljava/lang/String;

.field protected final tokens:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x0

    .line 63
    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;-><init>(Ljava/util/List;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/List;Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 79
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 52
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CommonTokenFactory;->DEFAULT:Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->_factory:Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

    const-string v0, "tokens cannot be null"

    .line 81
    invoke-static {p1, v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 84
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    .line 85
    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->sourceName:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public getCharPositionInLine()I
    .locals 3

    .line 93
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    .line 94
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getCharPositionInLine()I

    move-result v0

    return v0

    .line 96
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->eofToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_1

    .line 97
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getCharPositionInLine()I

    move-result v0

    return v0

    .line 99
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_3

    .line 102
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 103
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_2

    const/16 v2, 0xa

    .line 105
    invoke-virtual {v1, v2}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v2

    if-ltz v2, :cond_2

    .line 107
    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v0

    sub-int/2addr v0, v2

    add-int/lit8 v0, v0, -0x1

    return v0

    .line 111
    :cond_2
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getCharPositionInLine()I

    move-result v1

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getStopIndex()I

    move-result v2

    add-int/2addr v1, v2

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getStartIndex()I

    move-result v0

    sub-int/2addr v1, v0

    add-int/lit8 v1, v1, 0x1

    return v1

    :cond_3
    const/4 v0, 0x0

    return v0
.end method

.method public getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 2

    .line 190
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    .line 191
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v0

    return-object v0

    .line 193
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->eofToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_1

    .line 194
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v0

    return-object v0

    .line 196
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_2

    .line 197
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v0

    return-object v0

    :cond_2
    const/4 v0, 0x0

    return-object v0
.end method

.method public getLine()I
    .locals 5

    .line 155
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    .line 156
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getLine()I

    move-result v0

    return v0

    .line 158
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->eofToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_1

    .line 159
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getLine()I

    move-result v0

    return v0

    .line 161
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x1

    if-lez v0, :cond_3

    .line 164
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v2

    sub-int/2addr v2, v1

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 165
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getLine()I

    move-result v1

    .line 167
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_3

    const/4 v2, 0x0

    .line 169
    :goto_0
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v3

    if-ge v2, v3, :cond_3

    .line 170
    invoke-virtual {v0, v2}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v4, 0xa

    if-ne v3, v4, :cond_2

    add-int/lit8 v1, v1, 0x1

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    return v1
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 209
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->sourceName:Ljava/lang/String;

    if-eqz v0, :cond_0

    return-object v0

    .line 213
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 215
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/CharStream;->getSourceName()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_1
    const-string v0, "List"

    return-object v0
.end method

.method public getTokenFactory()Lgroovyjarjarantlr4/v4/runtime/TokenFactory;
    .locals 1

    .line 235
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->_factory:Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

    return-object v0
.end method

.method public nextToken()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 12

    .line 124
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, -0x1

    if-lt v0, v1, :cond_2

    .line 125
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->eofToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    if-nez v0, :cond_1

    .line 127
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_0

    .line 128
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getStopIndex()I

    move-result v0

    if-eq v0, v2, :cond_0

    add-int/lit8 v0, v0, 0x1

    move v8, v0

    goto :goto_0

    :cond_0
    move v8, v2

    :goto_0
    add-int/lit8 v0, v8, -0x1

    .line 134
    invoke-static {v2, v0}, Ljava/lang/Math;->max(II)I

    move-result v9

    .line 135
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->_factory:Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object v0

    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Tuple;->create(Ljava/lang/Object;Ljava/lang/Object;)Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;

    move-result-object v4

    const/4 v5, -0x1

    const/4 v7, 0x0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->getLine()I

    move-result v10

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->getCharPositionInLine()I

    move-result v11

    const-string v6, "EOF"

    invoke-interface/range {v3 .. v11}, Lgroovyjarjarantlr4/v4/runtime/TokenFactory;->create(Lgroovyjarjarantlr4/v4/runtime/misc/Tuple2;ILjava/lang/String;IIIII)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->eofToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 138
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->eofToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object v0

    .line 141
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 142
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->tokens:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    add-int/lit8 v3, v3, -0x1

    if-ne v1, v3, :cond_3

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v1

    if-ne v1, v2, :cond_3

    .line 143
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->eofToken:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 146
    :cond_3
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->i:I

    return-object v0
.end method

.method public setTokenFactory(Lgroovyjarjarantlr4/v4/runtime/TokenFactory;)V
    .locals 0

    .line 226
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/ListTokenSource;->_factory:Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

    return-void
.end method
