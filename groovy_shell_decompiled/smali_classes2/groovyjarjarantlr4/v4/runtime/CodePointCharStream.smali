.class public abstract Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
.super Ljava/lang/Object;
.source "CodePointCharStream.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/UnicodeCharStream;
.implements Lgroovyjarjarantlr4/v4/runtime/CharStream;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;,
        Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint16BitCharStream;,
        Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field protected final name:Ljava/lang/String;

.field protected position:I

.field protected final size:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method private constructor <init>(IILjava/lang/String;)V
    .locals 0

    .line 31
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 34
    iput p2, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->size:I

    .line 35
    iput-object p3, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->name:Ljava/lang/String;

    const/4 p1, 0x0

    .line 36
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->position:I

    return-void
.end method

.method synthetic constructor <init>(IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;)V
    .locals 0

    .line 20
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;-><init>(IILjava/lang/String;)V

    return-void
.end method

.method public static fromBuffer(Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
    .locals 1

    const-string v0, "<unknown>"

    .line 47
    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->fromBuffer(Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromBuffer(Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
    .locals 16

    .line 65
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;->$SwitchMap$org$antlr$v4$runtime$CodePointBuffer$Type:[I

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->getType()Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Type;->ordinal()I

    move-result v1

    aget v0, v0, v1

    const/4 v1, 0x1

    if-eq v0, v1, :cond_2

    const/4 v1, 0x2

    if-eq v0, v1, :cond_1

    const/4 v1, 0x3

    if-ne v0, v1, :cond_0

    .line 81
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->position()I

    move-result v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->remaining()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->intArray()[I

    move-result-object v6

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->arrayOffset()I

    move-result v7

    const/4 v8, 0x0

    move-object v2, v0

    move-object/from16 v5, p1

    invoke-direct/range {v2 .. v8}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint32BitCharStream;-><init>(IILjava/lang/String;[IILgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;)V

    return-object v0

    .line 88
    :cond_0
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Not reached"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 74
    :cond_1
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint16BitCharStream;

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->position()I

    move-result v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->remaining()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->charArray()[C

    move-result-object v6

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->arrayOffset()I

    move-result v7

    const/4 v8, 0x0

    move-object v2, v0

    move-object/from16 v5, p1

    invoke-direct/range {v2 .. v8}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint16BitCharStream;-><init>(IILjava/lang/String;[CILgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;)V

    return-object v0

    .line 67
    :cond_2
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->position()I

    move-result v10

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->remaining()I

    move-result v11

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->byteArray()[B

    move-result-object v13

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->arrayOffset()I

    move-result v14

    const/4 v15, 0x0

    move-object v9, v0

    move-object/from16 v12, p1

    invoke-direct/range {v9 .. v15}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream$CodePoint8BitCharStream;-><init>(IILjava/lang/String;[BILgroovyjarjarantlr4/v4/runtime/CodePointCharStream$1;)V

    return-object v0
.end method


# virtual methods
.method public final consume()V
    .locals 2

    .line 93
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->size:I

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->position:I

    sub-int/2addr v0, v1

    if-eqz v0, :cond_0

    add-int/lit8 v1, v1, 0x1

    .line 97
    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->position:I

    return-void

    .line 95
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "cannot consume EOF"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method abstract getInternalStorage()Ljava/lang/Object;
.end method

.method public final getSourceName()Ljava/lang/String;
    .locals 1

    .line 127
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->name:Ljava/lang/String;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 131
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->name:Ljava/lang/String;

    return-object v0

    :cond_1
    :goto_0
    const-string v0, "<unknown>"

    return-object v0
.end method

.method public final index()I
    .locals 1

    .line 102
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->position:I

    return v0
.end method

.method public final mark()I
    .locals 1

    const/4 v0, -0x1

    return v0
.end method

.method public final release(I)V
    .locals 0

    return-void
.end method

.method public final seek(I)V
    .locals 0

    .line 122
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->position:I

    return-void
.end method

.method public final size()I
    .locals 1

    .line 107
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->size:I

    return v0
.end method

.method public final supportsUnicodeCodePoints()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 2

    .line 136
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->size:I

    add-int/lit8 v0, v0, -0x1

    const/4 v1, 0x0

    invoke-static {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
