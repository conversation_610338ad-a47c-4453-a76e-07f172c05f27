.class public Lgroovyjarjarantlr4/v4/runtime/ConsoleErrorListener;
.super Ljava/lang/Object;
.source "ConsoleErrorListener.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener<",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation


# static fields
.field public static final INSTANCE:Lgroovyjarjarantlr4/v4/runtime/ConsoleErrorListener;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 16
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/ConsoleErrorListener;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/ConsoleErrorListener;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/ConsoleErrorListener;->INSTANCE:Lgroovyjarjarantlr4/v4/runtime/ConsoleErrorListener;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 12
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public syntaxError(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Ljava/lang/Object;IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
            "TT;*>;TT;II",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/RecognitionException;",
            ")V"
        }
    .end annotation

    .line 38
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p6, "line "

    invoke-virtual {p2, p6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, ":"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, " "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method
