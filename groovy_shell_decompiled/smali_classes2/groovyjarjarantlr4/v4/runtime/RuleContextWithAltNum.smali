.class public Lgroovyjarjarantlr4/v4/runtime/RuleContextWithAltNum;
.super Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
.source "RuleContextWithAltNum.java"


# instance fields
.field private altNumber:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 24
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;-><init>()V

    const/4 v0, 0x0

    .line 25
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/RuleContextWithAltNum;->altNumber:I

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;I)V
    .locals 0

    .line 29
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;-><init>(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;I)V

    return-void
.end method


# virtual methods
.method public getAltNumber()I
    .locals 1

    .line 34
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/RuleContextWithAltNum;->altNumber:I

    return v0
.end method

.method public setAltNumber(I)V
    .locals 0

    .line 39
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/RuleContextWithAltNum;->altNumber:I

    return-void
.end method
