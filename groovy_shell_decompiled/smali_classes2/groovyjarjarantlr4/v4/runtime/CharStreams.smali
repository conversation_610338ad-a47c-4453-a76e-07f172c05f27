.class public final enum Lgroovyjarjarantlr4/v4/runtime/CharStreams;
.super Ljava/lang/Enum;
.source "CharStreams.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovyjarjarantlr4/v4/runtime/CharStreams;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovyjarjarantlr4/v4/runtime/CharStreams;

.field private static final DEFAULT_BUFFER_SIZE:I = 0x1000


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x0

    new-array v0, v0, [Lgroovyjarjarantlr4/v4/runtime/CharStreams;

    .line 60
    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->$VALUES:[Lgroovyjarjarantlr4/v4/runtime/CharStreams;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 60
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static bufferFromChannel(Ljava/nio/channels/ReadableByteChannel;Ljava/nio/charset/Charset;ILjava/nio/charset/CodingErrorAction;J)Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 257
    :try_start_0
    invoke-static {p2}, Ljava/nio/ByteBuffer;->allocate(I)Ljava/nio/ByteBuffer;

    move-result-object v0

    .line 258
    invoke-static {p2}, Ljava/nio/CharBuffer;->allocate(I)Ljava/nio/CharBuffer;

    move-result-object v1

    const-wide/16 v2, -0x1

    cmp-long v2, p4, v2

    const/4 v3, 0x1

    const/4 v4, 0x0

    if-nez v2, :cond_0

    int-to-long p4, p2

    goto :goto_0

    :cond_0
    const-wide/32 v5, 0x7fffffff

    cmp-long p2, p4, v5

    if-gtz p2, :cond_5

    :goto_0
    long-to-int p2, p4

    .line 265
    invoke-static {p2}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->builder(I)Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;

    move-result-object p2

    .line 266
    invoke-virtual {p1}, Ljava/nio/charset/Charset;->newDecoder()Ljava/nio/charset/CharsetDecoder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/nio/charset/CharsetDecoder;->onMalformedInput(Ljava/nio/charset/CodingErrorAction;)Ljava/nio/charset/CharsetDecoder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/nio/charset/CharsetDecoder;->onUnmappableCharacter(Ljava/nio/charset/CodingErrorAction;)Ljava/nio/charset/CharsetDecoder;

    move-result-object p1

    move p4, v4

    :goto_1
    if-nez p4, :cond_3

    .line 273
    invoke-interface {p0, v0}, Ljava/nio/channels/ReadableByteChannel;->read(Ljava/nio/ByteBuffer;)I

    move-result p4

    const/4 p5, -0x1

    if-ne p4, p5, :cond_1

    move p4, v3

    goto :goto_2

    :cond_1
    move p4, v4

    .line 275
    :goto_2
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->flip()Ljava/nio/Buffer;

    .line 276
    invoke-virtual {p1, v0, v1, p4}, Ljava/nio/charset/CharsetDecoder;->decode(Ljava/nio/ByteBuffer;Ljava/nio/CharBuffer;Z)Ljava/nio/charset/CoderResult;

    move-result-object p5

    .line 280
    invoke-virtual {p5}, Ljava/nio/charset/CoderResult;->isError()Z

    move-result v2

    if-eqz v2, :cond_2

    sget-object v2, Ljava/nio/charset/CodingErrorAction;->REPORT:Ljava/nio/charset/CodingErrorAction;

    invoke-virtual {p3, v2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 281
    invoke-virtual {p5}, Ljava/nio/charset/CoderResult;->throwException()V

    .line 283
    :cond_2
    invoke-virtual {v1}, Ljava/nio/CharBuffer;->flip()Ljava/nio/Buffer;

    .line 284
    invoke-virtual {p2, v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->append(Ljava/nio/CharBuffer;)V

    .line 285
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->compact()Ljava/nio/ByteBuffer;

    .line 286
    invoke-virtual {v1}, Ljava/nio/CharBuffer;->compact()Ljava/nio/CharBuffer;

    goto :goto_1

    .line 290
    :cond_3
    invoke-virtual {p1, v1}, Ljava/nio/charset/CharsetDecoder;->flush(Ljava/nio/CharBuffer;)Ljava/nio/charset/CoderResult;

    move-result-object p1

    .line 291
    invoke-virtual {p1}, Ljava/nio/charset/CoderResult;->isError()Z

    move-result p4

    if-eqz p4, :cond_4

    sget-object p4, Ljava/nio/charset/CodingErrorAction;->REPORT:Ljava/nio/charset/CodingErrorAction;

    invoke-virtual {p3, p4}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p3

    if-eqz p3, :cond_4

    .line 292
    invoke-virtual {p1}, Ljava/nio/charset/CoderResult;->throwException()V

    .line 294
    :cond_4
    invoke-virtual {v1}, Ljava/nio/CharBuffer;->flip()Ljava/nio/Buffer;

    .line 295
    invoke-virtual {p2, v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->append(Ljava/nio/CharBuffer;)V

    .line 297
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->build()Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 300
    invoke-interface {p0}, Ljava/nio/channels/ReadableByteChannel;->close()V

    return-object p1

    .line 263
    :cond_5
    :try_start_1
    new-instance p1, Ljava/io/IOException;

    const-string p2, "inputSize %d larger than max %d"

    const/4 p3, 0x2

    new-array p3, p3, [Ljava/lang/Object;

    invoke-static {p4, p5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p4

    aput-object p4, p3, v4

    const p4, 0x7fffffff

    invoke-static {p4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p4

    aput-object p4, p3, v3

    invoke-static {p2, p3}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :catchall_0
    move-exception p1

    .line 300
    invoke-interface {p0}, Ljava/nio/channels/ReadableByteChannel;->close()V

    throw p1
.end method

.method public static fromChannel(Ljava/nio/channels/ReadableByteChannel;)Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-string v0, "UTF-8"

    .line 148
    invoke-static {v0}, Ljava/nio/charset/Charset;->forName(Ljava/lang/String;)Ljava/nio/charset/Charset;

    move-result-object v0

    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromChannel(Ljava/nio/channels/ReadableByteChannel;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromChannel(Ljava/nio/channels/ReadableByteChannel;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 159
    sget-object p1, Ljava/nio/charset/CodingErrorAction;->REPLACE:Ljava/nio/charset/CodingErrorAction;

    const/16 v0, 0x1000

    const-string v1, "<unknown>"

    invoke-static {p0, v0, p1, v1}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromChannel(Ljava/nio/channels/ReadableByteChannel;ILjava/nio/charset/CodingErrorAction;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromChannel(Ljava/nio/channels/ReadableByteChannel;ILjava/nio/charset/CodingErrorAction;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-string v0, "UTF-8"

    .line 232
    invoke-static {v0}, Ljava/nio/charset/Charset;->forName(Ljava/lang/String;)Ljava/nio/charset/Charset;

    move-result-object v2

    const-wide/16 v6, -0x1

    move-object v1, p0

    move v3, p1

    move-object v4, p2

    move-object v5, p3

    invoke-static/range {v1 .. v7}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromChannel(Ljava/nio/channels/ReadableByteChannel;Ljava/nio/charset/Charset;ILjava/nio/charset/CodingErrorAction;Ljava/lang/String;J)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromChannel(Ljava/nio/channels/ReadableByteChannel;Ljava/nio/charset/Charset;ILjava/nio/charset/CodingErrorAction;Ljava/lang/String;J)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    move-wide v4, p5

    .line 244
    invoke-static/range {v0 .. v5}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->bufferFromChannel(Ljava/nio/channels/ReadableByteChannel;Ljava/nio/charset/Charset;ILjava/nio/charset/CodingErrorAction;J)Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;

    move-result-object p0

    .line 245
    invoke-static {p0, p4}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->fromBuffer(Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromFile(Ljava/io/File;)Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-string v0, "UTF-8"

    .line 72
    invoke-static {v0}, Ljava/nio/charset/Charset;->forName(Ljava/lang/String;)Ljava/nio/charset/Charset;

    move-result-object v0

    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromFile(Ljava/io/File;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromFile(Ljava/io/File;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 82
    invoke-virtual {p0}, Ljava/io/File;->length()J

    move-result-wide v0

    .line 83
    new-instance v2, Ljava/io/FileInputStream;

    invoke-direct {v2, p0}, Ljava/io/FileInputStream;-><init>(Ljava/io/File;)V

    invoke-virtual {p0}, Ljava/io/File;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {v2, p1, p0, v0, v1}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromStream(Ljava/io/InputStream;Ljava/nio/charset/Charset;Ljava/lang/String;J)Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromFileName(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-string v0, "UTF-8"

    .line 93
    invoke-static {v0}, Ljava/nio/charset/Charset;->forName(Ljava/lang/String;)Ljava/nio/charset/Charset;

    move-result-object v0

    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromFileName(Ljava/lang/String;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromFileName(Ljava/lang/String;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 104
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromFile(Ljava/io/File;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromReader(Ljava/io/Reader;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-string v0, "<unknown>"

    .line 171
    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromReader(Ljava/io/Reader;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromReader(Ljava/io/Reader;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/16 v0, 0x1000

    .line 180
    :try_start_0
    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->builder(I)Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;

    move-result-object v1

    .line 181
    invoke-static {v0}, Ljava/nio/CharBuffer;->allocate(I)Ljava/nio/CharBuffer;

    move-result-object v0

    .line 182
    :goto_0
    invoke-virtual {p0, v0}, Ljava/io/Reader;->read(Ljava/nio/CharBuffer;)I

    move-result v2

    const/4 v3, -0x1

    if-eq v2, v3, :cond_0

    .line 183
    invoke-virtual {v0}, Ljava/nio/CharBuffer;->flip()Ljava/nio/Buffer;

    .line 184
    invoke-virtual {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->append(Ljava/nio/CharBuffer;)V

    .line 185
    invoke-virtual {v0}, Ljava/nio/CharBuffer;->compact()Ljava/nio/CharBuffer;

    goto :goto_0

    .line 187
    :cond_0
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->build()Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;

    move-result-object v0

    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->fromBuffer(Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 190
    invoke-virtual {p0}, Ljava/io/Reader;->close()V

    return-object p1

    :catchall_0
    move-exception p1

    invoke-virtual {p0}, Ljava/io/Reader;->close()V

    throw p1
.end method

.method public static fromStream(Ljava/io/InputStream;)Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-string v0, "UTF-8"

    .line 116
    invoke-static {v0}, Ljava/nio/charset/Charset;->forName(Ljava/lang/String;)Ljava/nio/charset/Charset;

    move-result-object v0

    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromStream(Ljava/io/InputStream;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromStream(Ljava/io/InputStream;Ljava/nio/charset/Charset;)Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const-string v0, "<unknown>"

    const-wide/16 v1, -0x1

    .line 127
    invoke-static {p0, p1, v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromStream(Ljava/io/InputStream;Ljava/nio/charset/Charset;Ljava/lang/String;J)Lgroovyjarjarantlr4/v4/runtime/CharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromStream(Ljava/io/InputStream;Ljava/nio/charset/Charset;Ljava/lang/String;J)Lgroovyjarjarantlr4/v4/runtime/CharStream;
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 131
    invoke-static {p0}, Ljava/nio/channels/Channels;->newChannel(Ljava/io/InputStream;)Ljava/nio/channels/ReadableByteChannel;

    move-result-object v0

    sget-object v3, Ljava/nio/charset/CodingErrorAction;->REPLACE:Ljava/nio/charset/CodingErrorAction;

    const/16 v2, 0x1000

    move-object v1, p1

    move-object v4, p2

    move-wide v5, p3

    invoke-static/range {v0 .. v6}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromChannel(Ljava/nio/channels/ReadableByteChannel;Ljava/nio/charset/Charset;ILjava/nio/charset/CodingErrorAction;Ljava/lang/String;J)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromString(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
    .locals 1

    const-string v0, "<unknown>"

    .line 198
    invoke-static {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->fromString(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    move-result-object p0

    return-object p0
.end method

.method public static fromString(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;
    .locals 2

    .line 208
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    invoke-static {v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;->builder(I)Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;

    move-result-object v0

    .line 211
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v1

    invoke-static {v1}, Ljava/nio/CharBuffer;->allocate(I)Ljava/nio/CharBuffer;

    move-result-object v1

    .line 212
    invoke-virtual {v1, p0}, Ljava/nio/CharBuffer;->put(Ljava/lang/String;)Ljava/nio/CharBuffer;

    .line 213
    invoke-virtual {v1}, Ljava/nio/CharBuffer;->flip()Ljava/nio/Buffer;

    .line 214
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->append(Ljava/nio/CharBuffer;)V

    .line 215
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer$Builder;->build()Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;

    move-result-object p0

    invoke-static {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;->fromBuffer(Lgroovyjarjarantlr4/v4/runtime/CodePointBuffer;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CodePointCharStream;

    move-result-object p0

    return-object p0
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/runtime/CharStreams;
    .locals 1

    .line 60
    const-class v0, Lgroovyjarjarantlr4/v4/runtime/CharStreams;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/runtime/CharStreams;

    return-object p0
.end method

.method public static values()[Lgroovyjarjarantlr4/v4/runtime/CharStreams;
    .locals 1

    .line 60
    sget-object v0, Lgroovyjarjarantlr4/v4/runtime/CharStreams;->$VALUES:[Lgroovyjarjarantlr4/v4/runtime/CharStreams;

    invoke-virtual {v0}, [Lgroovyjarjarantlr4/v4/runtime/CharStreams;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarantlr4/v4/runtime/CharStreams;

    return-object v0
.end method
