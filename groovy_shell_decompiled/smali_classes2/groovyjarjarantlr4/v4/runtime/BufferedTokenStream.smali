.class public Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;
.super Ljava/lang/Object;
.source "BufferedTokenStream.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/TokenStream;


# static fields
.field static final synthetic $assertionsDisabled:Z


# instance fields
.field protected fetchedEOF:Z

.field protected p:I

.field protected tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

.field protected tokens:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/TokenSource;)V
    .locals 2

    .line 70
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 40
    new-instance v0, Ljava/util/ArrayList;

    const/16 v1, 0x64

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    const/4 v0, -0x1

    .line 53
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    const-string v0, "tokenSource cannot be null"

    .line 72
    invoke-static {p1, v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 74
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    return-void
.end method


# virtual methods
.method public LA(I)I
    .locals 0

    .line 207
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result p1

    return p1
.end method

.method protected LB(I)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 2

    .line 210
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    sub-int v1, v0, p1

    if-gez v1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 211
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    sub-int/2addr v0, p1

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object p1
.end method

.method public LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 1

    .line 217
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->lazyInit()V

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    if-gez p1, :cond_1

    neg-int p1, p1

    .line 219
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->LB(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p1

    return-object p1

    .line 221
    :cond_1
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    add-int/2addr v0, p1

    add-int/lit8 v0, v0, -0x1

    .line 222
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->sync(I)Z

    .line 223
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    if-lt v0, p1, :cond_2

    .line 225
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object p1

    .line 228
    :cond_2
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object p1
.end method

.method protected adjustSeekIndex(I)I
    .locals 0

    return p1
.end method

.method public consume()V
    .locals 4

    .line 117
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-ltz v0, :cond_1

    .line 118
    iget-boolean v3, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->fetchedEOF:Z

    if-eqz v3, :cond_0

    .line 121
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    sub-int/2addr v3, v2

    if-ge v0, v3, :cond_1

    :goto_0
    move v1, v2

    goto :goto_1

    .line 125
    :cond_0
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    if-ge v0, v3, :cond_1

    goto :goto_0

    :cond_1
    :goto_1
    if-nez v1, :cond_3

    .line 133
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->LA(I)I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_2

    goto :goto_2

    .line 134
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "cannot consume EOF"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 137
    :cond_3
    :goto_2
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    add-int/2addr v0, v2

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->sync(I)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 138
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    add-int/2addr v0, v2

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->adjustSeekIndex(I)I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    :cond_4
    return-void
.end method

.method protected fetch(I)I
    .locals 4

    .line 165
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->fetchedEOF:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    return v1

    :cond_0
    :goto_0
    if-ge v1, p1, :cond_3

    .line 170
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenSource;->nextToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 171
    instance-of v2, v0, Lgroovyjarjarantlr4/v4/runtime/WritableToken;

    if-eqz v2, :cond_1

    .line 172
    move-object v2, v0

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/WritableToken;

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v3

    invoke-interface {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/WritableToken;->setTokenIndex(I)V

    .line 174
    :cond_1
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 175
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v0

    const/4 v2, -0x1

    if-ne v0, v2, :cond_2

    const/4 p1, 0x1

    .line 176
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->fetchedEOF:Z

    add-int/2addr v1, p1

    return v1

    :cond_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_3
    return p1
.end method

.method public fill()V
    .locals 2

    .line 485
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->lazyInit()V

    :cond_0
    const/16 v0, 0x3e8

    .line 488
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->fetch(I)I

    move-result v1

    if-ge v1, v0, :cond_0

    return-void
.end method

.method protected filterForChannel(III)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(III)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 425
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    :goto_0
    if-gt p1, p2, :cond_2

    .line 427
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v1, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/Token;

    const/4 v2, -0x1

    if-ne p3, v2, :cond_0

    .line 429
    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getChannel()I

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 432
    :cond_0
    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getChannel()I

    move-result v2

    if-ne v2, p3, :cond_1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    :goto_1
    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 435
    :cond_2
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_3

    const/4 p1, 0x0

    return-object p1

    :cond_3
    return-object v0
.end method

.method public get(I)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 3

    if-ltz p1, :cond_0

    .line 186
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_0

    .line 189
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    return-object p1

    .line 187
    :cond_0
    new-instance v0, Ljava/lang/IndexOutOfBoundsException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "token index "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " out of range 0.."

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public get(II)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    if-ltz p1, :cond_4

    if-gez p2, :cond_0

    goto :goto_2

    .line 195
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->lazyInit()V

    .line 196
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 197
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lt p2, v1, :cond_1

    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    add-int/lit8 p2, p2, -0x1

    :cond_1
    :goto_0
    if-gt p1, p2, :cond_3

    .line 199
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v1, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 200
    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v2

    const/4 v3, -0x1

    if-ne v2, v3, :cond_2

    goto :goto_1

    .line 201
    :cond_2
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_3
    :goto_1
    return-object v0

    :cond_4
    :goto_2
    const/4 p1, 0x0

    return-object p1
.end method

.method public getHiddenTokensToLeft(I)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    const/4 v0, -0x1

    .line 421
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->getHiddenTokensToLeft(II)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public getHiddenTokensToLeft(II)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 397
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->lazyInit()V

    if-ltz p1, :cond_2

    .line 398
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_2

    const/4 v0, 0x0

    if-nez p1, :cond_0

    return-object v0

    :cond_0
    add-int/lit8 p1, p1, -0x1

    const/4 v1, 0x0

    .line 407
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->previousTokenOnChannel(II)I

    move-result v1

    if-ne v1, p1, :cond_1

    return-object v0

    :cond_1
    add-int/lit8 v1, v1, 0x1

    .line 414
    invoke-virtual {p0, v1, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->filterForChannel(III)Ljava/util/List;

    move-result-object p1

    return-object p1

    .line 399
    :cond_2
    new-instance p2, Ljava/lang/IndexOutOfBoundsException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " not in 0.."

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw p2
.end method

.method public getHiddenTokensToRight(I)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    const/4 v0, -0x1

    .line 389
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->getHiddenTokensToRight(II)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public getHiddenTokensToRight(II)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 368
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->lazyInit()V

    if-ltz p1, :cond_1

    .line 369
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_1

    add-int/lit8 p1, p1, 0x1

    const/4 v0, 0x0

    .line 373
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->nextTokenOnChannel(II)I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    .line 378
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    .line 381
    :cond_0
    invoke-virtual {p0, p1, v0, p2}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->filterForChannel(III)Ljava/util/List;

    move-result-object p1

    return-object p1

    .line 370
    :cond_1
    new-instance p2, Ljava/lang/IndexOutOfBoundsException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " not in 0.."

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw p2
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 440
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenSource;->getSourceName()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getText()Ljava/lang/String;
    .locals 2

    .line 446
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    const/4 v1, 0x0

    invoke-static {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getText(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Ljava/lang/String;
    .locals 0

    .line 470
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->getSourceInterval()Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;
    .locals 5

    .line 452
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    .line 453
    iget p1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    if-ltz v0, :cond_4

    if-gez p1, :cond_0

    goto :goto_2

    .line 455
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->fill()V

    .line 456
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lt p1, v1, :cond_1

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    add-int/lit8 p1, p1, -0x1

    .line 458
    :cond_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    :goto_0
    if-gt v0, p1, :cond_3

    .line 460
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 461
    invoke-interface {v2}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v3

    const/4 v4, -0x1

    if-ne v3, v4, :cond_2

    goto :goto_1

    .line 462
    :cond_2
    invoke-interface {v2}, Lgroovyjarjarantlr4/v4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 464
    :cond_3
    :goto_1
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_4
    :goto_2
    const-string p1, ""

    return-object p1
.end method

.method public getText(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/String;
    .locals 1

    .line 476
    instance-of v0, p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_0

    instance-of v0, p2, Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz v0, :cond_0

    .line 477
    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result p1

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/Token;

    invoke-interface {p2}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result p2

    invoke-static {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->of(II)Lgroovyjarjarantlr4/v4/runtime/misc/Interval;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_0
    const-string p1, ""

    return-object p1
.end method

.method public getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;
    .locals 1

    .line 78
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    return-object v0
.end method

.method public getTokens()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 267
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    return-object v0
.end method

.method public getTokens(II)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 270
    invoke-virtual {p0, p1, p2, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->getTokens(IILjava/util/BitSet;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public getTokens(III)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(III)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 303
    new-instance v0, Ljava/util/BitSet;

    invoke-direct {v0, p3}, Ljava/util/BitSet;-><init>(I)V

    .line 304
    invoke-virtual {v0, p3}, Ljava/util/BitSet;->set(I)V

    .line 305
    invoke-virtual {p0, p1, p2, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->getTokens(IILjava/util/BitSet;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public getTokens(IILjava/util/BitSet;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/BitSet;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 278
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->lazyInit()V

    if-ltz p1, :cond_5

    .line 279
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p2, v0, :cond_5

    if-ltz p2, :cond_5

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_5

    const/4 v0, 0x0

    if-le p1, p2, :cond_0

    return-object v0

    .line 289
    :cond_0
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    :goto_0
    if-gt p1, p2, :cond_3

    .line 291
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/Token;

    if-eqz p3, :cond_1

    .line 292
    invoke-interface {v2}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v3

    invoke-virtual {p3, v3}, Ljava/util/BitSet;->get(I)Z

    move-result v3

    if-eqz v3, :cond_2

    .line 293
    :cond_1
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_2
    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 296
    :cond_3
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_4

    goto :goto_1

    :cond_4
    move-object v0, v1

    :goto_1
    return-object v0

    .line 282
    :cond_5
    new-instance p3, Ljava/lang/IndexOutOfBoundsException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "start "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " or stop "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " not in 0.."

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    add-int/lit8 p2, p2, -0x1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p3, p1}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw p3
.end method

.method public index()I
    .locals 1

    .line 81
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    return v0
.end method

.method protected final lazyInit()V
    .locals 2

    .line 249
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    .line 250
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->setup()V

    :cond_0
    return-void
.end method

.method public mark()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method protected nextTokenOnChannel(II)I
    .locals 2

    .line 315
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->sync(I)Z

    .line 316
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->size()I

    move-result v0

    if-lt p1, v0, :cond_0

    .line 317
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->size()I

    move-result p1

    add-int/lit8 p1, p1, -0x1

    return p1

    .line 320
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 321
    :goto_0
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getChannel()I

    move-result v1

    if-eq v1, p2, :cond_2

    .line 322
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_1

    return p1

    :cond_1
    add-int/lit8 p1, p1, 0x1

    .line 327
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->sync(I)Z

    .line 328
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    goto :goto_0

    :cond_2
    return p1
.end method

.method protected previousTokenOnChannel(II)I
    .locals 3

    .line 345
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->sync(I)Z

    .line 346
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->size()I

    move-result v0

    if-lt p1, v0, :cond_0

    .line 348
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->size()I

    move-result p1

    add-int/lit8 p1, p1, -0x1

    return p1

    :cond_0
    :goto_0
    if-ltz p1, :cond_2

    .line 352
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 353
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v1

    const/4 v2, -0x1

    if-eq v1, v2, :cond_2

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getChannel()I

    move-result v0

    if-ne v0, p2, :cond_1

    goto :goto_1

    :cond_1
    add-int/lit8 p1, p1, -0x1

    goto :goto_0

    :cond_2
    :goto_1
    return p1
.end method

.method public release(I)V
    .locals 0

    return-void
.end method

.method public reset()V
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    const/4 v0, 0x0

    .line 102
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->seek(I)V

    return-void
.end method

.method public seek(I)V
    .locals 0

    .line 107
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->lazyInit()V

    .line 108
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->adjustSeekIndex(I)I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    return-void
.end method

.method public setTokenSource(Lgroovyjarjarantlr4/v4/runtime/TokenSource;)V
    .locals 0

    .line 261
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    .line 262
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->clear()V

    const/4 p1, -0x1

    .line 263
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    const/4 p1, 0x0

    .line 264
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->fetchedEOF:Z

    return-void
.end method

.method protected setup()V
    .locals 1

    const/4 v0, 0x0

    .line 255
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->sync(I)Z

    .line 256
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->adjustSeekIndex(I)I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->p:I

    return-void
.end method

.method public size()I
    .locals 1

    .line 112
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method

.method protected sync(I)Z
    .locals 2

    .line 150
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    sub-int/2addr p1, v0

    const/4 v0, 0x1

    add-int/2addr p1, v0

    if-lez p1, :cond_1

    .line 153
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/BufferedTokenStream;->fetch(I)I

    move-result v1

    if-lt v1, p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :cond_1
    :goto_0
    return v0
.end method
