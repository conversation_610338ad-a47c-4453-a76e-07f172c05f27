.class public abstract Lgroovyjarjarantlr4/v4/runtime/Parser;
.super Lgroovyjarjarantlr4/v4/runtime/Recognizer;
.source "Parser.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/runtime/Parser$TrimToSizeListener;,
        Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lgroovyjarjarantlr4/v4/runtime/Recognizer<",
        "Lgroovyjarjarantlr4/v4/runtime/Token;",
        "Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;",
        ">;"
    }
.end annotation


# static fields
.field private static final bypassAltsAtnCache:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/runtime/atn/ATN;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field protected _buildParseTrees:Z

.field protected _ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

.field protected _errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

.field protected _input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

.field protected _parseListeners:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;",
            ">;"
        }
    .end annotation
.end field

.field protected final _precedenceStack:Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

.field protected _syntaxErrors:I

.field private _tracer:Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;

.field protected matchedEOF:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 94
    new-instance v0, Ljava/util/WeakHashMap;

    invoke-direct {v0}, Ljava/util/WeakHashMap;-><init>()V

    sput-object v0, Lgroovyjarjarantlr4/v4/runtime/Parser;->bypassAltsAtnCache:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V
    .locals 2

    .line 163
    invoke-direct {p0}, Lgroovyjarjarantlr4/v4/runtime/Recognizer;-><init>()V

    .line 104
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/DefaultErrorStrategy;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    .line 117
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_precedenceStack:Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

    const/4 v1, 0x0

    .line 118
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;->push(I)V

    const/4 v0, 0x1

    .line 133
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    .line 164
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setInputStream(Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V

    return-void
.end method


# virtual methods
.method protected addContextToParseTree()V
    .locals 2

    .line 618
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    if-eqz v0, :cond_0

    .line 621
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addChild(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    :cond_0
    return-void
.end method

.method public addParseListener(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V
    .locals 1

    const-string v0, "listener"

    .line 359
    invoke-static {p1, v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 362
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-nez v0, :cond_0

    .line 363
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    .line 366
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public compileParseTreePattern(Ljava/lang/String;I)Lgroovyjarjarantlr4/v4/runtime/tree/pattern/ParseTreePattern;
    .locals 2

    .line 477
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 478
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    move-result-object v0

    .line 479
    instance-of v1, v0, Lgroovyjarjarantlr4/v4/runtime/Lexer;

    if-eqz v1, :cond_0

    .line 480
    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/Lexer;

    .line 481
    invoke-virtual {p0, p1, p2, v0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->compileParseTreePattern(Ljava/lang/String;ILgroovyjarjarantlr4/v4/runtime/Lexer;)Lgroovyjarjarantlr4/v4/runtime/tree/pattern/ParseTreePattern;

    move-result-object p1

    return-object p1

    .line 484
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "Parser can\'t discover a lexer to use"

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public compileParseTreePattern(Ljava/lang/String;ILgroovyjarjarantlr4/v4/runtime/Lexer;)Lgroovyjarjarantlr4/v4/runtime/tree/pattern/ParseTreePattern;
    .locals 1

    .line 494
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/tree/pattern/ParseTreePatternMatcher;

    invoke-direct {v0, p3, p0}, Lgroovyjarjarantlr4/v4/runtime/tree/pattern/ParseTreePatternMatcher;-><init>(Lgroovyjarjarantlr4/v4/runtime/Lexer;Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 495
    invoke-virtual {v0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/tree/pattern/ParseTreePatternMatcher;->compile(Ljava/lang/String;I)Lgroovyjarjarantlr4/v4/runtime/tree/pattern/ParseTreePattern;

    move-result-object p1

    return-object p1
.end method

.method public consume()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 4

    .line 568
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 569
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v1

    const/4 v2, -0x1

    if-eq v1, v2, :cond_0

    .line 570
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->consume()V

    .line 572
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz v1, :cond_1

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_1

    const/4 v1, 0x1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    .line 573
    :goto_0
    iget-boolean v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    if-nez v2, :cond_2

    if-eqz v1, :cond_4

    .line 574
    :cond_2
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    invoke-interface {v1, p0}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->inErrorRecoveryMode(Lgroovyjarjarantlr4/v4/runtime/Parser;)Z

    move-result v1

    if-eqz v1, :cond_3

    .line 575
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p0, v1, v0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->createErrorNode(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    move-result-object v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addErrorNode(Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    move-result-object v1

    .line 576
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz v2, :cond_4

    .line 577
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;

    .line 578
    invoke-interface {v3, v1}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;->visitErrorNode(Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;)V

    goto :goto_1

    .line 583
    :cond_3
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p0, v1, v0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->createTerminalNode(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;

    move-result-object v1

    .line 584
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addChild(Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;)V

    .line 585
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz v2, :cond_4

    .line 586
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;

    .line 587
    invoke-interface {v3, v1}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;->visitTerminal(Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;)V

    goto :goto_2

    :cond_4
    return-object v0
.end method

.method public createErrorNode(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;
    .locals 1

    .line 612
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNodeImpl;

    invoke-direct {v0, p2}, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNodeImpl;-><init>(Lgroovyjarjarantlr4/v4/runtime/Token;)V

    .line 613
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNodeImpl;->setParent(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    return-object v0
.end method

.method public createTerminalNode(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNode;
    .locals 1

    .line 601
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;

    invoke-direct {v0, p2}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;-><init>(Lgroovyjarjarantlr4/v4/runtime/Token;)V

    .line 602
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/runtime/tree/TerminalNodeImpl;->setParent(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    return-object v0
.end method

.method public dumpDFA()V
    .locals 5

    const/4 v0, 0x0

    move v1, v0

    .line 908
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_interp:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    array-length v2, v2

    if-ge v0, v2, :cond_2

    .line 909
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_interp:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    aget-object v2, v2, v0

    .line 910
    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->isEmpty()Z

    move-result v3

    if-nez v3, :cond_1

    if-eqz v1, :cond_0

    .line 911
    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {v1}, Ljava/io/PrintStream;->println()V

    .line 912
    :cond_0
    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Decision "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    iget v4, v2, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->decision:I

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, ":"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 913
    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v3

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleNames()[Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    const/4 v1, 0x1

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public enterLeftFactoredRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V
    .locals 1

    .line 638
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setState(I)V

    .line 639
    iget-boolean p2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    const/4 p3, 0x1

    if-eqz p2, :cond_0

    .line 640
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getChildCount()I

    move-result v0

    sub-int/2addr v0, p3

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getChild(I)Lgroovyjarjarantlr4/v4/runtime/tree/ParseTree;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 641
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->removeLastChild()V

    .line 642
    iput-object p1, p2, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    .line 643
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addChild(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    .line 646
    :cond_0
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 647
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    invoke-interface {p2, p3}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p2

    iput-object p2, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 648
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    if-eqz p1, :cond_1

    .line 649
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->addContextToParseTree()V

    .line 652
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz p1, :cond_2

    .line 653
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->triggerEnterRuleEvent()V

    :cond_2
    return-void
.end method

.method public enterOuterAlt(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;I)V
    .locals 0

    .line 672
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->setAltNumber(I)V

    .line 675
    iget-boolean p2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    if-eqz p2, :cond_0

    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    if-eq p2, p1, :cond_0

    .line 676
    iget-object p2, p2, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    check-cast p2, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    if-eqz p2, :cond_0

    .line 678
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->removeLastChild()V

    .line 679
    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addChild(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    .line 682
    :cond_0
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    return-void
.end method

.method public enterRecursionRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;I)V
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 705
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v0

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    aget-object v0, v0, p2

    iget v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;->stateNumber:I

    const/4 v1, 0x0

    invoke-virtual {p0, p1, v0, p2, v1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->enterRecursionRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;III)V

    return-void
.end method

.method public enterRecursionRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;III)V
    .locals 0

    .line 709
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setState(I)V

    .line 710
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_precedenceStack:Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

    invoke-virtual {p2, p4}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;->push(I)V

    .line 711
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 712
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    const/4 p3, 0x1

    invoke-interface {p2, p3}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p2

    iput-object p2, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 713
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz p1, :cond_0

    .line 714
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->triggerEnterRuleEvent()V

    :cond_0
    return-void
.end method

.method public enterRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V
    .locals 0

    .line 630
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setState(I)V

    .line 631
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 632
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    const/4 p3, 0x1

    invoke-interface {p2, p3}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p2

    iput-object p2, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 633
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    if-eqz p1, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->addContextToParseTree()V

    .line 634
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz p1, :cond_1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->triggerEnterRuleEvent()V

    :cond_1
    return-void
.end method

.method public exitRule()V
    .locals 3

    .line 658
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->matchedEOF:Z

    if-eqz v0, :cond_0

    .line 660
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    const/4 v2, 0x1

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v1

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    goto :goto_0

    .line 663
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    const/4 v2, -0x1

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v1

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 666
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->triggerExitRuleEvent()V

    .line 667
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget v0, v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->invokingState:I

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setState(I)V

    .line 668
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    return-void
.end method

.method public getATNWithBypassAlts()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;
    .locals 4

    .line 447
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getSerializedATN()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 452
    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/Parser;->bypassAltsAtnCache:Ljava/util/Map;

    monitor-enter v1

    .line 453
    :try_start_0
    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    if-nez v2, :cond_0

    .line 455
    new-instance v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializationOptions;

    invoke-direct {v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializationOptions;-><init>()V

    const/4 v3, 0x1

    .line 456
    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializationOptions;->setGenerateRuleBypassTransitions(Z)V

    .line 457
    new-instance v3, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;

    invoke-direct {v3, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializationOptions;)V

    invoke-virtual {v0}, Ljava/lang/String;->toCharArray()[C

    move-result-object v2

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNDeserializer;->deserialize([C)Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v2

    .line 458
    invoke-interface {v1, v0, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 461
    :cond_0
    monitor-exit v1

    return-object v2

    :catchall_0
    move-exception v0

    .line 462
    monitor-exit v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0

    .line 449
    :cond_1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "The current parser does not support an ATN with bypass alternatives."

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public getBuildParseTree()Z
    .locals 1

    .line 287
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    return v0
.end method

.method public getContext()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
    .locals 1

    .line 773
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    return-object v0
.end method

.method public getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 2

    .line 524
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    return-object v0
.end method

.method public getDFAStrings()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 897
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    .line 898
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_interp:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    array-length v2, v2

    if-ge v1, v2, :cond_0

    .line 899
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_interp:Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->decisionToDFA:[Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;

    aget-object v2, v2, v1

    .line 900
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getVocabulary()Lgroovyjarjarantlr4/v4/runtime/Vocabulary;

    move-result-object v3

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleNames()[Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v3, v4}, Lgroovyjarjarantlr4/v4/runtime/dfa/DFA;->toString(Lgroovyjarjarantlr4/v4/runtime/Vocabulary;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public getErrorHandler()Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;
    .locals 1

    .line 500
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    return-object v0
.end method

.method public bridge synthetic getErrorListenerDispatch()Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;
    .locals 1

    .line 38
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getErrorListenerDispatch()Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;

    move-result-object v0

    return-object v0
.end method

.method public getErrorListenerDispatch()Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;
    .locals 2

    .line 787
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/ProxyParserErrorListener;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getErrorListeners()Ljava/util/List;

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/ProxyParserErrorListener;-><init>(Ljava/util/Collection;)V

    return-object v0
.end method

.method public getExpectedTokens()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 3

    .line 852
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getState()I

    move-result v1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getContext()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    move-result-object v2

    invoke-virtual {v0, v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->getExpectedTokens(ILgroovyjarjarantlr4/v4/runtime/RuleContext;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    return-object v0
.end method

.method public getExpectedTokensWithinCurrentRule()Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;
    .locals 3

    .line 857
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 858
    iget-object v1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getState()I

    move-result v2

    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 859
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getInputStream()Lgroovyjarjarantlr4/v4/runtime/IntStream;
    .locals 1

    .line 38
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    return-object v0
.end method

.method public getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;
    .locals 1

    .line 509
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    return-object v0
.end method

.method public getInvokingContext(I)Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
    .locals 2

    .line 764
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    :goto_0
    if-eqz v0, :cond_1

    .line 766
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->getRuleIndex()I

    move-result v1

    if-ne v1, p1, :cond_0

    return-object v0

    .line 767
    :cond_0
    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public getNumberOfSyntaxErrors()I
    .locals 1

    .line 431
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_syntaxErrors:I

    return v0
.end method

.method public getParseInfo()Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;
    .locals 2

    .line 925
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    .line 926
    instance-of v1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    if-eqz v1, :cond_0

    .line 927
    new-instance v1, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-direct {v1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ParseInfo;-><init>(Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;)V

    return-object v1

    :cond_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public getParseListeners()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;",
            ">;"
        }
    .end annotation

    .line 320
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-nez v0, :cond_0

    .line 322
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method public final getPrecedence()I
    .locals 1

    .line 692
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_precedenceStack:Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, -0x1

    return v0

    .line 696
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_precedenceStack:Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;->peek()I

    move-result v0

    return v0
.end method

.method public getRuleContext()Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;
    .locals 1

    .line 869
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    return-object v0
.end method

.method public getRuleIndex(Ljava/lang/String;)I
    .locals 1

    .line 864
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleIndexMap()Ljava/util/Map;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    if-eqz p1, :cond_0

    .line 865
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1

    :cond_0
    const/4 p1, -0x1

    return p1
.end method

.method public getRuleInvocationStack()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 879
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleInvocationStack(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getRuleInvocationStack(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/v4/runtime/RuleContext;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 883
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getRuleNames()[Ljava/lang/String;

    move-result-object v0

    .line 884
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    :goto_0
    if-eqz p1, :cond_1

    .line 887
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->getRuleIndex()I

    move-result v2

    if-gez v2, :cond_0

    const-string v2, "n/a"

    .line 888
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 889
    :cond_0
    aget-object v2, v0, v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 890
    :goto_1
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/runtime/RuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    goto :goto_0

    :cond_1
    return-object v1
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 920
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->getSourceName()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getTokenFactory()Lgroovyjarjarantlr4/v4/runtime/TokenFactory;
    .locals 1

    .line 435
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->getTokenSource()Lgroovyjarjarantlr4/v4/runtime/TokenSource;

    move-result-object v0

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/TokenSource;->getTokenFactory()Lgroovyjarjarantlr4/v4/runtime/TokenFactory;

    move-result-object v0

    return-object v0
.end method

.method public getTrimParseTree()Z
    .locals 2

    .line 315
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getParseListeners()Ljava/util/List;

    move-result-object v0

    sget-object v1, Lgroovyjarjarantlr4/v4/runtime/Parser$TrimToSizeListener;->INSTANCE:Lgroovyjarjarantlr4/v4/runtime/Parser$TrimToSizeListener;

    invoke-interface {v0, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v0

    return v0
.end method

.method public inContext(Ljava/lang/String;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public isExpectedToken(I)Z
    .locals 7

    .line 811
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    iget-object v0, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 812
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 813
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getState()I

    move-result v3

    invoke-interface {v2, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 814
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    .line 815
    invoke-virtual {v2, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v3

    const/4 v4, 0x1

    if-eqz v3, :cond_0

    return v4

    :cond_0
    const/4 v3, -0x2

    .line 819
    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v5

    const/4 v6, 0x0

    if-nez v5, :cond_1

    return v6

    :cond_1
    :goto_0
    if-eqz v1, :cond_3

    .line 821
    iget v5, v1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->invokingState:I

    if-ltz v5, :cond_3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v5

    if-eqz v5, :cond_3

    .line 822
    iget-object v2, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->states:Ljava/util/List;

    iget v5, v1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->invokingState:I

    invoke-interface {v2, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    .line 823
    invoke-virtual {v2, v6}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;->transition(I)Lgroovyjarjarantlr4/v4/runtime/atn/Transition;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;

    .line 824
    iget-object v2, v2, Lgroovyjarjarantlr4/v4/runtime/atn/RuleTransition;->followState:Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->nextTokens(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;)Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;

    move-result-object v2

    .line 825
    invoke-virtual {v2, p1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v5

    if-eqz v5, :cond_2

    return v4

    .line 829
    :cond_2
    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    goto :goto_0

    .line 832
    :cond_3
    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/v4/runtime/misc/IntervalSet;->contains(I)Z

    move-result v0

    if-eqz v0, :cond_4

    const/4 v0, -0x1

    if-ne p1, v0, :cond_4

    return v4

    :cond_4
    return v6
.end method

.method public isMatchedEOF()Z
    .locals 1

    .line 840
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->matchedEOF:Z

    return v0
.end method

.method public isTrace()Z
    .locals 1

    .line 970
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_tracer:Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public match(I)Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
        }
    .end annotation

    .line 204
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 205
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v1

    const/4 v2, -0x1

    if-ne v1, p1, :cond_1

    if-ne p1, v2, :cond_0

    const/4 p1, 0x1

    .line 207
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->matchedEOF:Z

    .line 209
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->reportMatch(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 210
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->consume()Lgroovyjarjarantlr4/v4/runtime/Token;

    goto :goto_0

    .line 213
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    invoke-interface {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->recoverInline(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 214
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    if-eqz p1, :cond_2

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result p1

    if-ne p1, v2, :cond_2

    .line 217
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->createErrorNode(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    move-result-object v1

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addErrorNode(Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    :cond_2
    :goto_0
    return-object v0
.end method

.method public matchWildcard()Lgroovyjarjarantlr4/v4/runtime/Token;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/v4/runtime/RecognitionException;
        }
    .end annotation

    .line 243
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 244
    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getType()I

    move-result v1

    if-lez v1, :cond_0

    .line 245
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    invoke-interface {v1, p0}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->reportMatch(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    .line 246
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->consume()Lgroovyjarjarantlr4/v4/runtime/Token;

    goto :goto_0

    .line 249
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    invoke-interface {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->recoverInline(Lgroovyjarjarantlr4/v4/runtime/Parser;)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    .line 250
    iget-boolean v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    if-eqz v1, :cond_1

    invoke-interface {v0}, Lgroovyjarjarantlr4/v4/runtime/Token;->getTokenIndex()I

    move-result v1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_1

    .line 253
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p0, v1, v0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->createErrorNode(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;Lgroovyjarjarantlr4/v4/runtime/Token;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    move-result-object v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addErrorNode(Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;)Lgroovyjarjarantlr4/v4/runtime/tree/ErrorNode;

    :cond_1
    :goto_0
    return-object v0
.end method

.method public notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Token;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V
    .locals 9

    .line 534
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_syntaxErrors:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_syntaxErrors:I

    const/4 v0, -0x1

    if-eqz p1, :cond_0

    .line 538
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getLine()I

    move-result v0

    .line 539
    invoke-interface {p1}, Lgroovyjarjarantlr4/v4/runtime/Token;->getCharPositionInLine()I

    move-result v1

    move v5, v0

    move v6, v1

    goto :goto_0

    :cond_0
    move v5, v0

    move v6, v5

    .line 542
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getErrorListenerDispatch()Lgroovyjarjarantlr4/v4/runtime/ParserErrorListener;

    move-result-object v2

    move-object v3, p0

    move-object v4, p1

    move-object v7, p2

    move-object v8, p3

    .line 543
    invoke-interface/range {v2 .. v8}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorListener;->syntaxError(Lgroovyjarjarantlr4/v4/runtime/Recognizer;Ljava/lang/Object;IILjava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    return-void
.end method

.method public final notifyErrorListeners(Ljava/lang/String;)V
    .locals 2

    .line 528
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getCurrentToken()Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v0

    const/4 v1, 0x0

    invoke-virtual {p0, v0, p1, v1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->notifyErrorListeners(Lgroovyjarjarantlr4/v4/runtime/Token;Ljava/lang/String;Lgroovyjarjarantlr4/v4/runtime/RecognitionException;)V

    return-void
.end method

.method public precpred(Lgroovyjarjarantlr4/v4/runtime/RuleContext;I)Z
    .locals 0

    .line 782
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_precedenceStack:Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;->peek()I

    move-result p1

    if-lt p2, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public pushNewRecursionContext(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;II)V
    .locals 1

    .line 722
    iget-object p3, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 723
    iput-object p1, p3, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    .line 724
    iput p2, p3, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->invokingState:I

    .line 725
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    const/4 v0, -0x1

    invoke-interface {p2, v0}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object p2

    iput-object p2, p3, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 727
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 728
    iget-object p2, p3, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    iput-object p2, p1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->start:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 729
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    if-eqz p1, :cond_0

    .line 730
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {p1, p3}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addChild(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    .line 733
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz p1, :cond_1

    .line 734
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->triggerEnterRuleEvent()V

    :cond_1
    return-void
.end method

.method public removeParseListener(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V
    .locals 1

    .line 380
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz v0, :cond_0

    .line 381
    invoke-interface {v0, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 382
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x0

    .line 383
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    :cond_0
    return-void
.end method

.method public removeParseListeners()V
    .locals 1

    const/4 v0, 0x0

    .line 395
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    return-void
.end method

.method public reset()V
    .locals 2

    .line 169
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInputStream()Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    move-result-object v0

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->seek(I)V

    .line 170
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    invoke-interface {v0, p0}, Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;->reset(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    const/4 v0, 0x0

    .line 171
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 172
    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_syntaxErrors:I

    .line 173
    iput-boolean v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->matchedEOF:Z

    .line 174
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setTrace(Z)V

    .line 175
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_precedenceStack:Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;->clear()V

    .line 176
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_precedenceStack:Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;->push(I)V

    .line 177
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 179
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;->reset()V

    :cond_1
    return-void
.end method

.method public setBuildParseTree(Z)V
    .locals 0

    .line 276
    iput-boolean p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    return-void
.end method

.method public setContext(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V
    .locals 0

    .line 777
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    return-void
.end method

.method public setErrorHandler(Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;)V
    .locals 0

    .line 504
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_errHandler:Lgroovyjarjarantlr4/v4/runtime/ANTLRErrorStrategy;

    return-void
.end method

.method public setInputStream(Lgroovyjarjarantlr4/v4/runtime/TokenStream;)V
    .locals 1

    const/4 v0, 0x0

    .line 514
    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    .line 515
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->reset()V

    .line 516
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    return-void
.end method

.method public setProfile(Z)V
    .locals 2

    .line 936
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    if-eqz p1, :cond_0

    .line 938
    instance-of p1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    if-nez p1, :cond_1

    .line 939
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    invoke-direct {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setInterpreter(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;)V

    goto :goto_0

    .line 942
    :cond_0
    instance-of p1, v0, Lgroovyjarjarantlr4/v4/runtime/atn/ProfilingATNSimulator;

    if-eqz p1, :cond_1

    .line 943
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v1

    invoke-direct {p1, p0, v1}, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;Lgroovyjarjarantlr4/v4/runtime/atn/ATN;)V

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->setInterpreter(Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;)V

    .line 945
    :cond_1
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getInterpreter()Lgroovyjarjarantlr4/v4/runtime/atn/ATNSimulator;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->getPredictionMode()Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/atn/ParserATNSimulator;->setPredictionMode(Lgroovyjarjarantlr4/v4/runtime/atn/PredictionMode;)V

    return-void
.end method

.method public setTrace(Z)V
    .locals 0

    if-nez p1, :cond_0

    .line 953
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_tracer:Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->removeParseListener(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V

    const/4 p1, 0x0

    .line 954
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_tracer:Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;

    goto :goto_1

    .line 957
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_tracer:Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;

    if-eqz p1, :cond_1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->removeParseListener(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V

    goto :goto_0

    .line 958
    :cond_1
    new-instance p1, Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;

    invoke-direct {p1, p0}, Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;-><init>(Lgroovyjarjarantlr4/v4/runtime/Parser;)V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_tracer:Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;

    .line 959
    :goto_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_tracer:Lgroovyjarjarantlr4/v4/runtime/Parser$TraceListener;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->addParseListener(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V

    :goto_1
    return-void
.end method

.method public setTrimParseTree(Z)V
    .locals 0

    if-eqz p1, :cond_1

    .line 299
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->getTrimParseTree()Z

    move-result p1

    if-eqz p1, :cond_0

    return-void

    .line 303
    :cond_0
    sget-object p1, Lgroovyjarjarantlr4/v4/runtime/Parser$TrimToSizeListener;->INSTANCE:Lgroovyjarjarantlr4/v4/runtime/Parser$TrimToSizeListener;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->addParseListener(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V

    goto :goto_0

    .line 306
    :cond_1
    sget-object p1, Lgroovyjarjarantlr4/v4/runtime/Parser$TrimToSizeListener;->INSTANCE:Lgroovyjarjarantlr4/v4/runtime/Parser$TrimToSizeListener;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/Parser;->removeParseListener(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V

    :goto_0
    return-void
.end method

.method protected triggerEnterRuleEvent()V
    .locals 3

    .line 404
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;

    .line 405
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;->enterEveryRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V

    .line 406
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->enterRule(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method protected triggerExitRuleEvent()V
    .locals 3

    .line 417
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    :goto_0
    if-ltz v0, :cond_0

    .line 418
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;

    .line 419
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->exitRule(Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;)V

    .line 420
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/tree/ParseTreeListener;->exitEveryRule(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V

    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public unrollRecursionContexts(Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;)V
    .locals 3

    .line 739
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_precedenceStack:Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerStack;->pop()I

    .line 740
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_input:Lgroovyjarjarantlr4/v4/runtime/TokenStream;

    const/4 v2, -0x1

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/v4/runtime/Token;

    move-result-object v1

    iput-object v1, v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->stop:Lgroovyjarjarantlr4/v4/runtime/Token;

    .line 741
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 744
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_parseListeners:Ljava/util/List;

    if-eqz v1, :cond_0

    .line 745
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    if-eq v1, p1, :cond_1

    .line 746
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/Parser;->triggerExitRuleEvent()V

    .line 747
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    check-cast v1, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    goto :goto_0

    .line 751
    :cond_0
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_ctx:Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;

    .line 755
    :cond_1
    iput-object p1, v0, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->parent:Lgroovyjarjarantlr4/v4/runtime/RuleContext;

    .line 757
    iget-boolean v1, p0, Lgroovyjarjarantlr4/v4/runtime/Parser;->_buildParseTrees:Z

    if-eqz v1, :cond_2

    if-eqz p1, :cond_2

    .line 759
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/runtime/ParserRuleContext;->addChild(Lgroovyjarjarantlr4/v4/runtime/RuleContext;)V

    :cond_2
    return-void
.end method
