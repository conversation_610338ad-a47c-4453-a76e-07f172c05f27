.class public Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;
.super Ljava/lang/Object;
.source "UnbufferedCharStream.java"

# interfaces
.implements Lgroovyjarjarantlr4/v4/runtime/UnicodeCharStream;
.implements Lgroovyjarjarantlr4/v4/runtime/CharStream;


# instance fields
.field protected currentCharIndex:I

.field protected data:[C

.field protected input:Ljava/io/Reader;

.field protected lastChar:I

.field protected lastCharBufferStart:I

.field protected n:I

.field public name:Ljava/lang/String;

.field protected numMarkers:I

.field protected p:I


# direct methods
.method public constructor <init>()V
    .locals 1

    const/16 v0, 0x100

    .line 79
    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;-><init>(I)V

    return-void
.end method

.method public constructor <init>(I)V
    .locals 2

    .line 83
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 43
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    .line 51
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->numMarkers:I

    const/4 v1, -0x1

    .line 56
    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastChar:I

    .line 70
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->currentCharIndex:I

    .line 84
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    .line 85
    new-array p1, p1, [C

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    return-void
.end method

.method public constructor <init>(Ljava/io/InputStream;)V
    .locals 1

    const/16 v0, 0x100

    .line 89
    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;-><init>(Ljava/io/InputStream;I)V

    return-void
.end method

.method public constructor <init>(Ljava/io/InputStream;I)V
    .locals 0

    .line 97
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;-><init>(I)V

    .line 98
    new-instance p2, Ljava/io/InputStreamReader;

    invoke-direct {p2, p1}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    iput-object p2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->input:Ljava/io/Reader;

    const/4 p1, 0x1

    .line 99
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->fill(I)I

    return-void
.end method

.method public constructor <init>(Ljava/io/Reader;)V
    .locals 1

    const/16 v0, 0x100

    .line 93
    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;-><init>(Ljava/io/Reader;I)V

    return-void
.end method

.method public constructor <init>(Ljava/io/Reader;I)V
    .locals 0

    .line 103
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;-><init>(I)V

    .line 104
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->input:Ljava/io/Reader;

    const/4 p1, 0x1

    .line 105
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->fill(I)I

    return-void
.end method


# virtual methods
.method public LA(I)I
    .locals 2

    const/4 v0, -0x1

    if-ne p1, v0, :cond_0

    .line 181
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastChar:I

    return p1

    .line 182
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->sync(I)V

    .line 183
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    add-int/2addr v1, p1

    add-int/lit8 v1, v1, -0x1

    if-ltz v1, :cond_3

    .line 185
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    if-lt v1, p1, :cond_1

    return v0

    .line 186
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    aget-char p1, p1, v1

    const v1, 0xffff

    if-ne p1, v1, :cond_2

    return v0

    :cond_2
    return p1

    .line 184
    :cond_3
    new-instance p1, Ljava/lang/IndexOutOfBoundsException;

    invoke-direct {p1}, Ljava/lang/IndexOutOfBoundsException;-><init>()V

    throw p1
.end method

.method protected add(I)V
    .locals 3

    .line 173
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    array-length v2, v1

    if-lt v0, v2, :cond_0

    .line 174
    array-length v0, v1

    mul-int/lit8 v0, v0, 0x2

    invoke-static {v1, v0}, Ljava/util/Arrays;->copyOf([CI)[C

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    .line 176
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    add-int/lit8 v2, v1, 0x1

    iput v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    int-to-char p1, p1

    aput-char p1, v0, v1

    return-void
.end method

.method public consume()V
    .locals 5

    const/4 v0, 0x1

    .line 110
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->LA(I)I

    move-result v1

    const/4 v2, -0x1

    if-eq v1, v2, :cond_1

    .line 115
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    iget v3, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    aget-char v1, v1, v3

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastChar:I

    .line 117
    iget v4, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    sub-int/2addr v4, v0

    if-ne v3, v4, :cond_0

    iget v3, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->numMarkers:I

    if-nez v3, :cond_0

    const/4 v3, 0x0

    .line 118
    iput v3, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    .line 119
    iput v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    .line 120
    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastCharBufferStart:I

    .line 123
    :cond_0
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    add-int/2addr v1, v0

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    .line 124
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->currentCharIndex:I

    add-int/2addr v1, v0

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->currentCharIndex:I

    .line 125
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->sync(I)V

    return-void

    .line 111
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "cannot consume EOF"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method protected fill(I)I
    .locals 3

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p1, :cond_1

    .line 148
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    if-lez v1, :cond_0

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    add-int/lit8 v1, v1, -0x1

    aget-char v1, v2, v1

    const v2, 0xffff

    if-ne v1, v2, :cond_0

    return v0

    .line 153
    :cond_0
    :try_start_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->nextChar()I

    move-result v1

    .line 154
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->add(I)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :catch_0
    move-exception p1

    .line 157
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0

    :cond_1
    return p1
.end method

.method protected final getBufferStartIndex()I
    .locals 2

    .line 306
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->currentCharIndex:I

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    sub-int/2addr v0, v1

    return v0
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 276
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->name:Ljava/lang/String;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    .line 280
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->name:Ljava/lang/String;

    return-object v0

    :cond_1
    :goto_0
    const-string v0, "<unknown>"

    return-object v0
.end method

.method public getText(Lgroovyjarjarantlr4/v4/runtime/misc/Interval;)Ljava/lang/String;
    .locals 4

    .line 285
    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-ltz v0, :cond_3

    iget v0, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    add-int/lit8 v1, v1, -0x1

    if-lt v0, v1, :cond_3

    .line 289
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->getBufferStartIndex()I

    move-result v0

    .line 290
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    if-lez v1, :cond_1

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    add-int/lit8 v1, v1, -0x1

    aget-char v1, v2, v1

    const v2, 0xffff

    if-ne v1, v2, :cond_1

    .line 291
    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->length()I

    move-result v2

    add-int/2addr v1, v2

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    add-int/2addr v2, v0

    if-gt v1, v2, :cond_0

    goto :goto_0

    .line 292
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "the interval extends past the end of the stream"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 296
    :cond_1
    :goto_0
    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    if-lt v1, v0, :cond_2

    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->b:I

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    add-int/2addr v2, v0

    if-ge v1, v2, :cond_2

    .line 301
    iget v1, p1, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->a:I

    sub-int/2addr v1, v0

    .line 302
    new-instance v0, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/runtime/misc/Interval;->length()I

    move-result p1

    invoke-direct {v0, v2, v1, p1}, Ljava/lang/String;-><init>([CII)V

    return-object v0

    .line 297
    :cond_2
    new-instance v1, Ljava/lang/UnsupportedOperationException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "interval "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, " outside buffer: "

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, ".."

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    add-int/2addr v0, v2

    add-int/lit8 v0, v0, -0x1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v1

    .line 286
    :cond_3
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "invalid interval"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public index()I
    .locals 1

    .line 232
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->currentCharIndex:I

    return v0
.end method

.method public mark()I
    .locals 2

    .line 200
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->numMarkers:I

    if-nez v0, :cond_0

    .line 201
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastChar:I

    iput v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastCharBufferStart:I

    :cond_0
    neg-int v1, v0

    add-int/lit8 v1, v1, -0x1

    add-int/lit8 v0, v0, 0x1

    .line 205
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->numMarkers:I

    return v1
.end method

.method protected nextChar()I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 169
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->input:Ljava/io/Reader;

    invoke-virtual {v0}, Ljava/io/Reader;->read()I

    move-result v0

    return v0
.end method

.method public release(I)V
    .locals 3

    .line 214
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->numMarkers:I

    neg-int v1, v0

    if-ne p1, v1, :cond_1

    add-int/lit8 v0, v0, -0x1

    .line 219
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->numMarkers:I

    if-nez v0, :cond_0

    .line 220
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    if-lez p1, :cond_0

    .line 223
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    sub-int/2addr v1, p1

    const/4 v2, 0x0

    invoke-static {v0, p1, v0, v2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 224
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    sub-int/2addr p1, v0

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    .line 225
    iput v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    .line 226
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastChar:I

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastCharBufferStart:I

    :cond_0
    return-void

    .line 216
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "release() called with an invalid marker."

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public seek(I)V
    .locals 3

    .line 240
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->currentCharIndex:I

    if-ne p1, v0, :cond_0

    return-void

    :cond_0
    if-le p1, v0, :cond_1

    sub-int v0, p1, v0

    .line 245
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->sync(I)V

    .line 246
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->getBufferStartIndex()I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    add-int/2addr v0, v1

    add-int/lit8 v0, v0, -0x1

    invoke-static {p1, v0}, Ljava/lang/Math;->min(II)I

    move-result p1

    .line 250
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->getBufferStartIndex()I

    move-result v0

    sub-int v0, p1, v0

    if-ltz v0, :cond_4

    .line 254
    iget v1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    if-ge v0, v1, :cond_3

    .line 259
    iput v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    .line 260
    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->currentCharIndex:I

    if-nez v0, :cond_2

    .line 262
    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastCharBufferStart:I

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastChar:I

    goto :goto_0

    .line 265
    :cond_2
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->data:[C

    add-int/lit8 v0, v0, -0x1

    aget-char p1, p1, v0

    iput p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->lastChar:I

    :goto_0
    return-void

    .line 255
    :cond_3
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "seek to index outside buffer: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " not in "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->getBufferStartIndex()I

    move-result v1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ".."

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->getBufferStartIndex()I

    move-result v1

    iget v2, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    add-int/2addr v1, v2

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 252
    :cond_4
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "cannot seek to negative index "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public size()I
    .locals 2

    .line 271
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "Unbuffered stream cannot know its size"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public supportsUnicodeCodePoints()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method protected sync(I)V
    .locals 1

    .line 135
    iget v0, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->p:I

    add-int/2addr v0, p1

    add-int/lit8 v0, v0, -0x1

    iget p1, p0, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->n:I

    sub-int/2addr v0, p1

    add-int/lit8 v0, v0, 0x1

    if-lez v0, :cond_0

    .line 137
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/runtime/UnbufferedCharStream;->fill(I)I

    :cond_0
    return-void
.end method
