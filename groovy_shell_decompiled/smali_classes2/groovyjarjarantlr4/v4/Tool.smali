.class public Lgroovyjarjarantlr4/v4/Tool;
.super Ljava/lang/Object;
.source "Tool.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/v4/Tool$Option;,
        Lgroovyjarjarantlr4/v4/Tool$OptionArgType;
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z = false

.field public static final ALL_GRAMMAR_EXTENSIONS:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final GRAMMAR_EXTENSION:Ljava/lang/String; = ".g4"

.field public static final LEGACY_GRAMMAR_EXTENSION:Ljava/lang/String; = ".g"

.field public static final VERSION:Ljava/lang/String;

.field public static internalOption_PrintGrammarTree:Z

.field public static internalOption_ShowATNConfigsInDFA:Z

.field public static optionDefs:[Lgroovyjarjarantlr4/v4/Tool$Option;


# instance fields
.field public ST_inspector_wait_for_close:Z

.field public final args:[Ljava/lang/String;

.field defaultListener:Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;

.field public errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

.field public exact_output_dir:Z

.field public force_atn:Z

.field public genPackage:Ljava/lang/String;

.field public gen_dependencies:Z

.field public gen_listener:Z

.field public gen_visitor:Z

.field public generate_ATN_dot:Z

.field public grammarEncoding:Ljava/lang/String;

.field protected grammarFiles:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public grammarOptions:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field protected haveOutputDir:Z

.field private final importedGrammars:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovyjarjarantlr4/v4/tool/Grammar;",
            ">;"
        }
    .end annotation
.end field

.field public inputDirectory:Ljava/io/File;

.field public launch_ST_inspector:Z

.field public libDirectory:Ljava/lang/String;

.field listeners:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;",
            ">;"
        }
    .end annotation
.end field

.field public log:Z

.field public logMgr:Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;

.field public longMessages:Z

.field public msgFormat:Ljava/lang/String;

.field public outputDirectory:Ljava/lang/String;

.field protected return_dont_exit:Z

.field public warnings_are_errors:Z


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 70
    const-class v0, Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v0}, Ljava/lang/Class;->getPackage()Ljava/lang/Package;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Package;->getImplementationVersion()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const-string v0, "4.x"

    .line 71
    :goto_0
    sput-object v0, Lgroovyjarjarantlr4/v4/Tool;->VERSION:Ljava/lang/String;

    const-string v0, ".g4"

    const-string v1, ".g"

    .line 77
    filled-new-array {v0, v1}, [Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Lgroovyjarjarantlr4/v4/Tool;->ALL_GRAMMAR_EXTENSIONS:Ljava/util/List;

    const/16 v0, 0x13

    new-array v0, v0, [Lgroovyjarjarantlr4/v4/Tool$Option;

    .line 120
    new-instance v1, Lgroovyjarjarantlr4/v4/Tool$Option;

    sget-object v2, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->STRING:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    const-string v3, "outputDirectory"

    const-string v4, "-o"

    const-string v5, "specify output directory where all output is generated"

    invoke-direct {v1, v3, v4, v2, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/Tool$OptionArgType;Ljava/lang/String;)V

    const/4 v2, 0x0

    aput-object v1, v0, v2

    const/4 v1, 0x1

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    sget-object v3, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->STRING:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    const-string v4, "libDirectory"

    const-string v5, "-lib"

    const-string v6, "specify location of grammars, tokens files"

    invoke-direct {v2, v4, v5, v3, v6}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/Tool$OptionArgType;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/4 v1, 0x2

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "generate_ATN_dot"

    const-string v4, "-atn"

    const-string v5, "generate rule augmented transition network diagrams"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/4 v1, 0x3

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    sget-object v3, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->STRING:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    const-string v4, "grammarEncoding"

    const-string v5, "-encoding"

    const-string v6, "specify grammar file encoding; e.g., euc-jp"

    invoke-direct {v2, v4, v5, v3, v6}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/Tool$OptionArgType;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/4 v1, 0x4

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    sget-object v3, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->STRING:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    const-string v4, "msgFormat"

    const-string v5, "-message-format"

    const-string v6, "specify output style for messages in antlr, gnu, vs2005"

    invoke-direct {v2, v4, v5, v3, v6}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/Tool$OptionArgType;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/4 v1, 0x5

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "longMessages"

    const-string v4, "-long-messages"

    const-string v5, "show exception details when available for errors and warnings"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/4 v1, 0x6

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "gen_listener"

    const-string v4, "-listener"

    const-string v5, "generate parse tree listener (default)"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/4 v1, 0x7

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v4, "-no-listener"

    const-string v5, "don\'t generate parse tree listener"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0x8

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "gen_visitor"

    const-string v4, "-visitor"

    const-string v5, "generate parse tree visitor"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0x9

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v4, "-no-visitor"

    const-string v5, "don\'t generate parse tree visitor (default)"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0xa

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    sget-object v3, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->STRING:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    const-string v4, "genPackage"

    const-string v5, "-package"

    const-string v6, "specify a package/namespace for the generated code"

    invoke-direct {v2, v4, v5, v3, v6}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarantlr4/v4/Tool$OptionArgType;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0xb

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "gen_dependencies"

    const-string v4, "-depend"

    const-string v5, "generate file dependencies"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0xc

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, ""

    const-string v4, "-D<option>=value"

    const-string v5, "set/override a grammar-level option"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0xd

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "warnings_are_errors"

    const-string v4, "-Werror"

    const-string v5, "treat warnings as errors"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0xe

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "launch_ST_inspector"

    const-string v4, "-XdbgST"

    const-string v5, "launch StringTemplate visualizer on generated code"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0xf

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "ST_inspector_wait_for_close"

    const-string v4, "-XdbgSTWait"

    const-string v5, "wait for STViz to close before continuing"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0x10

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "force_atn"

    const-string v4, "-Xforce-atn"

    const-string v5, "use the ATN simulator for all predictions"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0x11

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "log"

    const-string v4, "-Xlog"

    const-string v5, "dump lots of logging info to antlr-timestamp.log"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    const/16 v1, 0x12

    new-instance v2, Lgroovyjarjarantlr4/v4/Tool$Option;

    const-string v3, "exact_output_dir"

    const-string v4, "-Xexact-output-dir"

    const-string v5, "all output goes into -o dir regardless of paths/package"

    invoke-direct {v2, v3, v4, v5}, Lgroovyjarjarantlr4/v4/Tool$Option;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    aput-object v2, v0, v1

    sput-object v0, Lgroovyjarjarantlr4/v4/Tool;->optionDefs:[Lgroovyjarjarantlr4/v4/Tool$Option;

    const/4 v0, 0x0

    .line 147
    sput-boolean v0, Lgroovyjarjarantlr4/v4/Tool;->internalOption_PrintGrammarTree:Z

    .line 148
    sput-boolean v0, Lgroovyjarjarantlr4/v4/Tool;->internalOption_ShowATNConfigsInDFA:Z

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 191
    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/v4/Tool;-><init>([Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>([Ljava/lang/String;)V
    .locals 4

    .line 193
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 104
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->generate_ATN_dot:Z

    const/4 v1, 0x0

    .line 105
    iput-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarEncoding:Ljava/lang/String;

    const-string v2, "antlr"

    .line 106
    iput-object v2, p0, Lgroovyjarjarantlr4/v4/Tool;->msgFormat:Ljava/lang/String;

    .line 107
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->launch_ST_inspector:Z

    .line 108
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->ST_inspector_wait_for_close:Z

    .line 109
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->force_atn:Z

    .line 110
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->log:Z

    const/4 v3, 0x1

    .line 111
    iput-boolean v3, p0, Lgroovyjarjarantlr4/v4/Tool;->gen_listener:Z

    .line 112
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->gen_visitor:Z

    .line 113
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->gen_dependencies:Z

    .line 114
    iput-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->genPackage:Ljava/lang/String;

    .line 115
    iput-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarOptions:Ljava/util/Map;

    .line 116
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->warnings_are_errors:Z

    .line 117
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->longMessages:Z

    .line 118
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->exact_output_dir:Z

    .line 143
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->haveOutputDir:Z

    .line 144
    iput-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->return_dont_exit:Z

    .line 153
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarFiles:Ljava/util/List;

    .line 156
    new-instance v0, Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->logMgr:Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;

    .line 158
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    .line 163
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;-><init>(Lgroovyjarjarantlr4/v4/Tool;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->defaultListener:Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;

    .line 606
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->importedGrammars:Ljava/util/Map;

    .line 194
    iput-object p1, p0, Lgroovyjarjarantlr4/v4/Tool;->args:[Ljava/lang/String;

    .line 195
    new-instance p1, Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-direct {p1, p0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;-><init>(Lgroovyjarjarantlr4/v4/Tool;)V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    .line 198
    invoke-virtual {p1, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->setFormat(Ljava/lang/String;)V

    .line 199
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/Tool;->handleArgs()V

    .line 200
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->msgFormat:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->setFormat(Ljava/lang/String;)V

    return-void
.end method

.method public static findOptionValueAST(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;
    .locals 3

    const/16 v0, 0x2a

    .line 545
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    if-eqz p0, :cond_1

    .line 546
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildCount()I

    move-result v0

    if-lez v0, :cond_1

    .line 547
    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChildren()Ljava/util/List;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    .line 548
    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 549
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getType()I

    move-result v1

    const/16 v2, 0xa

    if-ne v1, v2, :cond_0

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v1

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 p0, 0x1

    .line 552
    invoke-virtual {v0, p0}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object p0

    check-cast p0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    return-object p0

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method private generateInterpreterData(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 7

    .line 704
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "token literal names:\n"

    .line 706
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 707
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenLiteralNames()[Ljava/lang/String;

    move-result-object v1

    .line 708
    array-length v2, v1

    const/4 v3, 0x0

    move v4, v3

    :goto_0
    const-string v5, "\n"

    if-ge v4, v2, :cond_0

    aget-object v6, v1, v4

    .line 709
    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 711
    :cond_0
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "token symbolic names:\n"

    .line 713
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 714
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getTokenSymbolicNames()[Ljava/lang/String;

    move-result-object v1

    .line 715
    array-length v2, v1

    move v4, v3

    :goto_1
    if-ge v4, v2, :cond_1

    aget-object v6, v1, v4

    .line 716
    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v4, v4, 0x1

    goto :goto_1

    .line 718
    :cond_1
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "rule names:\n"

    .line 720
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 721
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRuleNames()[Ljava/lang/String;

    move-result-object v1

    .line 722
    array-length v2, v1

    move v4, v3

    :goto_2
    if-ge v4, v2, :cond_2

    aget-object v6, v1, v4

    .line 723
    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v4, v4, 0x1

    goto :goto_2

    .line 725
    :cond_2
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 727
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v1

    if-eqz v1, :cond_4

    const-string v1, "channel names:\n"

    .line 728
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "DEFAULT_TOKEN_CHANNEL\n"

    .line 729
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "HIDDEN\n"

    .line 730
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 731
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->channelValueToNameList:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    .line 732
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_3

    .line 734
    :cond_3
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "mode names:\n"

    .line 736
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 737
    move-object v1, p1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->modes:Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/MultiMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_4
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_4

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    .line 738
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_4

    .line 741
    :cond_4
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 743
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getRuleNames()[Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    invoke-static {v1, v2}, Lgroovyjarjarantlr4/v4/runtime/atn/ATNSerializer;->getSerialized(Lgroovyjarjarantlr4/v4/runtime/atn/ATN;Ljava/util/List;)Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;

    move-result-object v1

    const-string v2, "atn:\n"

    .line 744
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 745
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/runtime/misc/IntegerList;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 748
    :try_start_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ".interp"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, p1, v1}, Lgroovyjarjarantlr4/v4/Tool;->getOutputFileWriter(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)Ljava/io/Writer;

    move-result-object p1
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 750
    :try_start_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/io/Writer;->write(Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 753
    :try_start_2
    invoke-virtual {p1}, Ljava/io/Writer;->close()V

    goto :goto_5

    :catchall_0
    move-exception v0

    invoke-virtual {p1}, Ljava/io/Writer;->close()V

    throw v0
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    :catch_0
    move-exception p1

    .line 757
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v1, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_WRITE_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v2, v3, [Ljava/lang/Object;

    invoke-virtual {v0, v1, p1, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    :goto_5
    return-void
.end method

.method public static main([Ljava/lang/String;)V
    .locals 6

    const-string v0, "wrote "

    .line 166
    new-instance v1, Lgroovyjarjarantlr4/v4/Tool;

    invoke-direct {v1, p0}, Lgroovyjarjarantlr4/v4/Tool;-><init>([Ljava/lang/String;)V

    .line 167
    array-length p0, p0

    const/4 v2, 0x0

    if-nez p0, :cond_0

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/Tool;->help()V

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/Tool;->exit(I)V

    .line 170
    :cond_0
    :try_start_0
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/Tool;->processGrammarsOnCommandLine()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 173
    iget-boolean p0, v1, Lgroovyjarjarantlr4/v4/Tool;->log:Z

    if-eqz p0, :cond_1

    .line 175
    :try_start_1
    iget-object p0, v1, Lgroovyjarjarantlr4/v4/Tool;->logMgr:Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;->save()Ljava/lang/String;

    move-result-object p0

    .line 176
    sget-object v3, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v3, p0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0

    goto :goto_0

    :catch_0
    move-exception p0

    .line 179
    iget-object v0, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v4, v2, [Ljava/lang/Object;

    invoke-virtual {v0, v3, p0, v4}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    .line 183
    :cond_1
    :goto_0
    iget-boolean p0, v1, Lgroovyjarjarantlr4/v4/Tool;->return_dont_exit:Z

    if-eqz p0, :cond_2

    return-void

    .line 185
    :cond_2
    iget-object p0, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {p0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result p0

    if-lez p0, :cond_3

    const/4 p0, 0x1

    .line 186
    invoke-virtual {v1, p0}, Lgroovyjarjarantlr4/v4/Tool;->exit(I)V

    .line 188
    :cond_3
    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/v4/Tool;->exit(I)V

    return-void

    :catchall_0
    move-exception p0

    .line 173
    iget-boolean v3, v1, Lgroovyjarjarantlr4/v4/Tool;->log:Z

    if-eqz v3, :cond_4

    .line 175
    :try_start_2
    iget-object v3, v1, Lgroovyjarjarantlr4/v4/Tool;->logMgr:Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;->save()Ljava/lang/String;

    move-result-object v3

    .line 176
    sget-object v4, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v4, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_1

    goto :goto_1

    :catch_1
    move-exception v0

    .line 179
    iget-object v1, v1, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v2, v2, [Ljava/lang/Object;

    invoke-virtual {v1, v3, v0, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    .line 180
    :cond_4
    :goto_1
    throw p0
.end method


# virtual methods
.method public addListener(Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 913
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public checkForRuleIssues(Lgroovyjarjarantlr4/v4/tool/Grammar;)Z
    .locals 11

    .line 424
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/16 v1, 0x61

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getFirstChildWithType(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 425
    new-instance v1, Ljava/util/ArrayList;

    const/16 v2, 0x5e

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object v0

    invoke-direct {v1, v0}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 426
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    const/16 v3, 0x24

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 427
    invoke-virtual {v3, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getAllChildrenWithType(I)Ljava/util/List;

    move-result-object v3

    invoke-interface {v1, v3}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 431
    :cond_0
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 432
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    const/4 v2, 0x1

    const/4 v3, 0x0

    move v4, v3

    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 433
    check-cast v5, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    .line 434
    invoke-virtual {v5, v3}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v6

    check-cast v6, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 435
    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v7

    .line 436
    invoke-interface {v0, v7}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;

    if-eqz v8, :cond_1

    .line 438
    invoke-virtual {v8, v3}, Lgroovyjarjarantlr4/v4/tool/ast/RuleAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 439
    iget-object v5, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    iget-object v5, v5, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v8, Lgroovyjarjarantlr4/v4/tool/ErrorType;->RULE_REDEFINITION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v9, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {v6}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v6

    const/4 v10, 0x2

    new-array v10, v10, [Ljava/lang/Object;

    aput-object v7, v10, v3

    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v4

    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result v4

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    aput-object v4, v10, v2

    invoke-virtual {v5, v8, v9, v6, v10}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    move v4, v2

    goto :goto_1

    .line 447
    :cond_1
    invoke-interface {v0, v7, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 484
    :cond_2
    new-instance v1, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;

    invoke-direct {v1, p0, p1, v0}, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/util/Map;)V

    .line 485
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->visitGrammar(Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    if-nez v4, :cond_4

    .line 487
    iget-boolean p1, v1, Lgroovyjarjarantlr4/v4/Tool$1UndefChecker;->badref:Z

    if-eqz p1, :cond_3

    goto :goto_2

    :cond_3
    move v2, v3

    :cond_4
    :goto_2
    return v2
.end method

.method public createGrammar(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)Lgroovyjarjarantlr4/v4/tool/Grammar;
    .locals 2

    .line 568
    iget v0, p1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->grammarType:I

    const/16 v1, 0x1f

    if-ne v0, v1, :cond_0

    new-instance v0, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    invoke-direct {v0, p0, p1}, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)V

    goto :goto_0

    .line 569
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/Grammar;

    invoke-direct {v0, p0, p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)V

    .line 572
    :goto_0
    invoke-static {v0, p1}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->setGrammarPtr(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)V

    return-object v0
.end method

.method public error(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V
    .locals 2

    .line 927
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 928
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->defaultListener:Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;->error(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    return-void

    .line 931
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;

    invoke-interface {v1, p1}, Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;->error(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public exit(I)V
    .locals 0

    .line 950
    invoke-static {p1}, Ljava/lang/System;->exit(I)V

    return-void
.end method

.method public generateATNs(Lgroovyjarjarantlr4/v4/tool/Grammar;)V
    .locals 7

    .line 683
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 684
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 685
    invoke-interface {v1, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 686
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->getAllImportedGrammars()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_0

    .line 687
    invoke-interface {v1, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 688
    :cond_0
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 689
    iget-object v2, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->rules:Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/misc/OrderedHashMap;->values()Ljava/util/Collection;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_2
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/Rule;

    .line 691
    :try_start_0
    iget-object v4, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    iget-object v4, v4, Lgroovyjarjarantlr4/v4/runtime/atn/ATN;->ruleToStartState:[Lgroovyjarjarantlr4/v4/runtime/atn/RuleStartState;

    iget v5, v3, Lgroovyjarjarantlr4/v4/tool/Rule;->index:I

    aget-object v4, v4, v5

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v5

    invoke-virtual {v0, v4, v5}, Lgroovyjarjarantlr4/v4/tool/DOTGenerator;->getDOT(Lgroovyjarjarantlr4/v4/runtime/atn/ATNState;Z)Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_2

    .line 693
    invoke-virtual {p0, p1, v3, v4}, Lgroovyjarjarantlr4/v4/Tool;->writeDOTFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v3

    .line 697
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v5, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_WRITE_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/4 v6, 0x0

    new-array v6, v6, [Ljava/lang/Object;

    invoke-virtual {v4, v5, v3, v6}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    goto :goto_0

    :cond_3
    return-void
.end method

.method public getImportedGrammarFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)Ljava/io/File;
    .locals 2

    .line 802
    new-instance v0, Ljava/io/File;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->inputDirectory:Ljava/io/File;

    invoke-direct {v0, v1, p2}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 803
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v1

    if-nez v1, :cond_0

    .line 804
    new-instance v0, Ljava/io/File;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 805
    invoke-virtual {v0}, Ljava/io/File;->getParent()Ljava/lang/String;

    move-result-object p1

    .line 806
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1, p2}, Ljava/io/File;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 807
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result p1

    if-nez p1, :cond_0

    .line 808
    new-instance v0, Ljava/io/File;

    iget-object p1, p0, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    invoke-direct {v0, p1, p2}, Ljava/io/File;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 809
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    return-object v0
.end method

.method public getListeners()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;",
            ">;"
        }
    .end annotation

    .line 917
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    return-object v0
.end method

.method public getNumErrors()I
    .locals 1

    .line 910
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v0

    return v0
.end method

.method public getOutputDirectory(Ljava/lang/String;)Ljava/io/File;
    .locals 2

    if-eqz p1, :cond_1

    .line 835
    sget-char v0, Ljava/io/File;->separatorChar:C

    invoke-virtual {p1, v0}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 843
    sget-char v1, Ljava/io/File;->separatorChar:C

    invoke-virtual {p1, v1}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v1

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p1

    goto :goto_1

    :cond_1
    :goto_0
    const-string p1, "."

    .line 845
    :goto_1
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->haveOutputDir:Z

    if-eqz v0, :cond_6

    .line 846
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->exact_output_dir:Z

    if-eqz v0, :cond_2

    .line 851
    new-instance p1, Ljava/io/File;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    invoke-direct {p1, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    goto :goto_3

    :cond_2
    if-eqz p1, :cond_4

    .line 853
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/io/File;->isAbsolute()Z

    move-result v0

    if-nez v0, :cond_3

    const-string v0, "~"

    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 862
    :cond_3
    new-instance p1, Ljava/io/File;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    invoke-direct {p1, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    goto :goto_3

    :cond_4
    if-eqz p1, :cond_5

    .line 867
    new-instance v0, Ljava/io/File;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    invoke-direct {v0, v1, p1}, Ljava/io/File;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_2

    .line 870
    :cond_5
    new-instance p1, Ljava/io/File;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    invoke-direct {p1, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    goto :goto_3

    .line 879
    :cond_6
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    :goto_2
    move-object p1, v0

    :goto_3
    return-object p1
.end method

.method public getOutputFileWriter(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)Ljava/io/Writer;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 779
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    if-nez v0, :cond_0

    .line 780
    new-instance p1, Ljava/io/StringWriter;

    invoke-direct {p1}, Ljava/io/StringWriter;-><init>()V

    return-object p1

    .line 784
    :cond_0
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/Tool;->getOutputDirectory(Ljava/lang/String;)Ljava/io/File;

    move-result-object p1

    .line 785
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1, p2}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 787
    invoke-virtual {p1}, Ljava/io/File;->exists()Z

    move-result p2

    if-nez p2, :cond_1

    .line 788
    invoke-virtual {p1}, Ljava/io/File;->mkdirs()Z

    .line 790
    :cond_1
    new-instance p1, Ljava/io/FileOutputStream;

    invoke-direct {p1, v0}, Ljava/io/FileOutputStream;-><init>(Ljava/io/File;)V

    .line 792
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarEncoding:Ljava/lang/String;

    if-eqz p2, :cond_2

    .line 793
    new-instance p2, Ljava/io/OutputStreamWriter;

    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarEncoding:Ljava/lang/String;

    invoke-direct {p2, p1, v0}, Ljava/io/OutputStreamWriter;-><init>(Ljava/io/OutputStream;Ljava/lang/String;)V

    goto :goto_0

    .line 796
    :cond_2
    new-instance p2, Ljava/io/OutputStreamWriter;

    invoke-direct {p2, p1}, Ljava/io/OutputStreamWriter;-><init>(Ljava/io/OutputStream;)V

    .line 798
    :goto_0
    new-instance p1, Ljava/io/BufferedWriter;

    invoke-direct {p1, p2}, Ljava/io/BufferedWriter;-><init>(Ljava/io/Writer;)V

    return-object p1
.end method

.method protected handleArgs()V
    .locals 13

    const/4 v0, 0x0

    move v1, v0

    .line 205
    :cond_0
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/Tool;->args:[Ljava/lang/String;

    const/4 v3, 0x1

    if-eqz v2, :cond_8

    array-length v4, v2

    if-ge v1, v4, :cond_8

    .line 206
    aget-object v2, v2, v1

    add-int/lit8 v1, v1, 0x1

    const-string v4, "-D"

    .line 208
    invoke-virtual {v2, v4}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v4

    if-eqz v4, :cond_1

    .line 209
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/Tool;->handleOptionSetArg(Ljava/lang/String;)V

    goto :goto_0

    .line 212
    :cond_1
    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v4

    const/16 v5, 0x2d

    if-eq v4, v5, :cond_2

    .line 213
    iget-object v3, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarFiles:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_0

    iget-object v3, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarFiles:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 217
    :cond_2
    sget-object v4, Lgroovyjarjarantlr4/v4/Tool;->optionDefs:[Lgroovyjarjarantlr4/v4/Tool$Option;

    array-length v5, v4

    move v6, v0

    move v7, v6

    :goto_1
    if-ge v6, v5, :cond_7

    aget-object v8, v4, v6

    .line 218
    iget-object v9, v8, Lgroovyjarjarantlr4/v4/Tool$Option;->name:Ljava/lang/String;

    invoke-virtual {v2, v9}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_6

    const/4 v7, 0x0

    .line 221
    iget-object v9, v8, Lgroovyjarjarantlr4/v4/Tool$Option;->argType:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    sget-object v10, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->STRING:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    if-ne v9, v10, :cond_3

    .line 222
    iget-object v7, p0, Lgroovyjarjarantlr4/v4/Tool;->args:[Ljava/lang/String;

    aget-object v7, v7, v1

    add-int/lit8 v1, v1, 0x1

    .line 226
    :cond_3
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v9

    .line 228
    :try_start_0
    iget-object v10, v8, Lgroovyjarjarantlr4/v4/Tool$Option;->fieldName:Ljava/lang/String;

    invoke-virtual {v9, v10}, Ljava/lang/Class;->getField(Ljava/lang/String;)Ljava/lang/reflect/Field;

    move-result-object v9

    if-nez v7, :cond_5

    const-string v7, "-no-"

    .line 230
    invoke-virtual {v2, v7}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_4

    invoke-virtual {v9, p0, v0}, Ljava/lang/reflect/Field;->setBoolean(Ljava/lang/Object;Z)V

    goto :goto_2

    .line 231
    :cond_4
    invoke-virtual {v9, p0, v3}, Ljava/lang/reflect/Field;->setBoolean(Ljava/lang/Object;Z)V

    goto :goto_2

    .line 233
    :cond_5
    invoke-virtual {v9, p0, v7}, Ljava/lang/reflect/Field;->set(Ljava/lang/Object;Ljava/lang/Object;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    .line 236
    :catch_0
    iget-object v7, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v9, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INTERNAL_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v10, v3, [Ljava/lang/Object;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    const-string v12, "can\'t access field "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v11

    iget-object v8, v8, Lgroovyjarjarantlr4/v4/Tool$Option;->fieldName:Ljava/lang/String;

    invoke-virtual {v11, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    aput-object v8, v10, v0

    invoke-virtual {v7, v9, v10}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    :goto_2
    move v7, v3

    :cond_6
    add-int/lit8 v6, v6, 0x1

    goto :goto_1

    :cond_7
    if-nez v7, :cond_0

    .line 241
    iget-object v4, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v5, Lgroovyjarjarantlr4/v4/tool/ErrorType;->INVALID_CMDLINE_ARG:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v3, v3, [Ljava/lang/Object;

    aput-object v2, v3, v0

    invoke-virtual {v4, v5, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 244
    :cond_8
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    const-string v2, "\\"

    const-string v4, "/"

    const-string v5, "."

    if-eqz v1, :cond_b

    .line 245
    invoke-virtual {v1, v4}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_9

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_a

    .line 247
    :cond_9
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v6

    sub-int/2addr v6, v3

    invoke-virtual {v1, v0, v6}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    .line 250
    :cond_a
    new-instance v1, Ljava/io/File;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    invoke-direct {v1, v6}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 251
    iput-boolean v3, p0, Lgroovyjarjarantlr4/v4/Tool;->haveOutputDir:Z

    .line 252
    invoke-virtual {v1}, Ljava/io/File;->exists()Z

    move-result v6

    if-eqz v6, :cond_c

    invoke-virtual {v1}, Ljava/io/File;->isDirectory()Z

    move-result v1

    if-nez v1, :cond_c

    .line 253
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v6, Lgroovyjarjarantlr4/v4/tool/ErrorType;->OUTPUT_DIR_IS_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v7, v3, [Ljava/lang/Object;

    iget-object v8, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    aput-object v8, v7, v0

    invoke-virtual {v1, v6, v7}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    .line 254
    iput-object v5, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    goto :goto_3

    .line 258
    :cond_b
    iput-object v5, p0, Lgroovyjarjarantlr4/v4/Tool;->outputDirectory:Ljava/lang/String;

    .line 260
    :cond_c
    :goto_3
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    if-eqz v1, :cond_f

    .line 261
    invoke-virtual {v1, v4}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_d

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_e

    .line 263
    :cond_d
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v2

    sub-int/2addr v2, v3

    invoke-virtual {v1, v0, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    iput-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    .line 265
    :cond_e
    new-instance v1, Ljava/io/File;

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    invoke-direct {v1, v2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 266
    invoke-virtual {v1}, Ljava/io/File;->exists()Z

    move-result v1

    if-nez v1, :cond_10

    .line 267
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->DIR_NOT_FOUND:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v4, v3, [Ljava/lang/Object;

    iget-object v6, p0, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    aput-object v6, v4, v0

    invoke-virtual {v1, v2, v4}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    .line 268
    iput-object v5, p0, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    goto :goto_4

    .line 272
    :cond_f
    iput-object v5, p0, Lgroovyjarjarantlr4/v4/Tool;->libDirectory:Ljava/lang/String;

    .line 274
    :cond_10
    :goto_4
    iget-boolean v0, p0, Lgroovyjarjarantlr4/v4/Tool;->launch_ST_inspector:Z

    if-eqz v0, :cond_11

    .line 275
    sput-boolean v3, Lorg/stringtemplate/v4/STGroup;->trackCreationEvents:Z

    .line 276
    iput-boolean v3, p0, Lgroovyjarjarantlr4/v4/Tool;->return_dont_exit:Z

    :cond_11
    return-void
.end method

.method protected handleOptionSetArg(Ljava/lang/String;)V
    .locals 5

    const/16 v0, 0x3d

    .line 281
    invoke-virtual {p1, v0}, Ljava/lang/String;->indexOf(I)I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-lez v0, :cond_4

    .line 282
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v3

    const/4 v4, 0x3

    if-le v3, v4, :cond_4

    const/4 v3, 0x2

    .line 283
    invoke-virtual {p1, v3, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v3

    add-int/2addr v0, v2

    .line 284
    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v0

    .line 285
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v4

    if-nez v4, :cond_0

    .line 286
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->BAD_OPTION_SET_SYNTAX:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v2, v2, [Ljava/lang/Object;

    aput-object p1, v2, v1

    invoke-virtual {v0, v3, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    return-void

    .line 289
    :cond_0
    sget-object p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->parserOptions:Ljava/util/Set;

    invoke-interface {p1, v3}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_2

    sget-object p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->lexerOptions:Ljava/util/Set;

    invoke-interface {p1, v3}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    goto :goto_0

    .line 296
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorType;->ILLEGAL_OPTION:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v2, v2, [Ljava/lang/Object;

    aput-object v3, v2, v1

    const/4 v1, 0x0

    invoke-virtual {p1, v0, v1, v1, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    goto :goto_1

    .line 292
    :cond_2
    :goto_0
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarOptions:Ljava/util/Map;

    if-nez p1, :cond_3

    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarOptions:Ljava/util/Map;

    .line 293
    :cond_3
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarOptions:Ljava/util/Map;

    invoke-interface {p1, v3, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 303
    :cond_4
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->BAD_OPTION_SET_SYNTAX:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-array v2, v2, [Ljava/lang/Object;

    aput-object p1, v2, v1

    invoke-virtual {v0, v3, v2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;[Ljava/lang/Object;)V

    :goto_1
    return-void
.end method

.method public help()V
    .locals 8

    .line 899
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ANTLR Parser Generator  Version "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarantlr4/v4/Tool;->VERSION:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/Tool;->info(Ljava/lang/String;)V

    .line 900
    sget-object v0, Lgroovyjarjarantlr4/v4/Tool;->optionDefs:[Lgroovyjarjarantlr4/v4/Tool$Option;

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 901
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v6, v4, Lgroovyjarjarantlr4/v4/Tool$Option;->name:Ljava/lang/String;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    iget-object v6, v4, Lgroovyjarjarantlr4/v4/Tool$Option;->argType:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    sget-object v7, Lgroovyjarjarantlr4/v4/Tool$OptionArgType;->NONE:Lgroovyjarjarantlr4/v4/Tool$OptionArgType;

    if-eq v6, v7, :cond_0

    const-string v6, " ___"

    goto :goto_1

    :cond_0
    const-string v6, ""

    :goto_1
    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    const/4 v6, 0x2

    new-array v6, v6, [Ljava/lang/Object;

    aput-object v5, v6, v2

    .line 902
    iget-object v4, v4, Lgroovyjarjarantlr4/v4/Tool$Option;->description:Ljava/lang/String;

    const/4 v5, 0x1

    aput-object v4, v6, v5

    const-string v4, " %-19s %s"

    invoke-static {v4, v6}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    .line 903
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr4/v4/Tool;->info(Ljava/lang/String;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public info(Ljava/lang/String;)V
    .locals 2

    .line 920
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 921
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->defaultListener:Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;->info(Ljava/lang/String;)V

    return-void

    .line 924
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;

    invoke-interface {v1, p1}, Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;->info(Ljava/lang/String;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public loadGrammar(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/Grammar;
    .locals 1

    .line 599
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/Tool;->parseGrammar(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object v0

    .line 600
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/Tool;->createGrammar(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v0

    .line 601
    iput-object p1, v0, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    const/4 p1, 0x0

    .line 602
    invoke-virtual {p0, v0, p1}, Lgroovyjarjarantlr4/v4/Tool;->process(Lgroovyjarjarantlr4/v4/tool/Grammar;Z)V

    return-object v0
.end method

.method public loadImportedGrammar(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;)Lgroovyjarjarantlr4/v4/tool/Grammar;
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 614
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v0

    .line 615
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->importedGrammars:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/Grammar;

    if-nez v1, :cond_4

    .line 617
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "load "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, " from "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const-string v3, "grammar"

    invoke-virtual {v1, v3, v2}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    .line 619
    sget-object v1, Lgroovyjarjarantlr4/v4/Tool;->ALL_GRAMMAR_EXTENSIONS:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    const/4 v2, 0x0

    move-object v3, v2

    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    .line 620
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, p1, v3}, Lgroovyjarjarantlr4/v4/Tool;->getImportedGrammarFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)Ljava/io/File;

    move-result-object v3

    if-eqz v3, :cond_0

    :cond_1
    if-nez v3, :cond_2

    .line 627
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_FIND_IMPORTED_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p2

    const/4 v4, 0x1

    new-array v4, v4, [Ljava/lang/Object;

    const/4 v5, 0x0

    aput-object v0, v4, v5

    invoke-virtual {v1, v3, p1, p2, v4}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V

    return-object v2

    .line 631
    :cond_2
    invoke-virtual {v3}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object p2

    .line 632
    new-instance v0, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarEncoding:Ljava/lang/String;

    invoke-direct {v0, p2, v1}, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 633
    iget-object p1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/Tool;->parse(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/CharStream;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object p1

    if-nez p1, :cond_3

    return-object v2

    .line 638
    :cond_3
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/Tool;->createGrammar(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v1

    .line 639
    iput-object p2, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    .line 640
    iget-object p2, p0, Lgroovyjarjarantlr4/v4/Tool;->importedGrammars:Ljava/util/Map;

    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getGrammarName()Ljava/lang/String;

    move-result-object p1

    invoke-interface {p2, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_4
    return-object v1
.end method

.method public log(Ljava/lang/String;)V
    .locals 1

    const/4 v0, 0x0

    .line 908
    invoke-virtual {p0, v0, p1}, Lgroovyjarjarantlr4/v4/Tool;->log(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public log(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 907
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->logMgr:Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarantlr4/v4/runtime/misc/LogManager;->log(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public panic()V
    .locals 2

    .line 952
    new-instance v0, Ljava/lang/Error;

    const-string v1, "ANTLR panic"

    invoke-direct {v0, v1}, Ljava/lang/Error;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public parse(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/CharStream;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;
    .locals 5

    const/4 v0, 0x0

    .line 652
    :try_start_0
    new-instance v1, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;

    invoke-direct {v1, p2}, Lgroovyjarjarantlr4/v4/parse/GrammarASTAdaptor;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;)V

    .line 653
    new-instance v2, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;

    invoke-direct {v2, p2, p0}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;Lgroovyjarjarantlr4/v4/Tool;)V

    .line 654
    new-instance p2, Lgroovyjarjarantlr4/runtime/CommonTokenStream;

    invoke-direct {p2, v2}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;-><init>(Lgroovyjarjarantlr4/runtime/TokenSource;)V

    .line 655
    iput-object p2, v2, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;->tokens:Lgroovyjarjarantlr4/runtime/CommonTokenStream;

    .line 656
    new-instance v3, Lgroovyjarjarantlr4/v4/parse/ToolANTLRParser;

    invoke-direct {v3, p2, p0}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRParser;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/v4/Tool;)V

    .line 657
    invoke-virtual {v3, v1}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRParser;->setTreeAdaptor(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;)V
    :try_end_0
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_1

    const/4 p2, 0x0

    .line 659
    :try_start_1
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRParser;->grammarSpec()Lgroovyjarjarantlr4/v4/parse/ANTLRParser$grammarSpec_return;

    move-result-object v1

    .line 660
    invoke-virtual {v1}, Lgroovyjarjarantlr4/runtime/ParserRuleReturnScope;->getTree()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    .line 661
    instance-of v4, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-eqz v4, :cond_3

    .line 662
    move-object v4, v1

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v2}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRLexer;->getNumberOfSyntaxErrors()I

    move-result v2

    if-gtz v2, :cond_1

    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/parse/ToolANTLRParser;->getNumberOfSyntaxErrors()I

    move-result v2

    if-lez v2, :cond_0

    goto :goto_0

    :cond_0
    move v2, p2

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    iput-boolean v2, v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->hasErrors:Z

    .line 664
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarOptions:Ljava/util/Map;

    if-eqz v2, :cond_2

    .line 665
    move-object v3, v1

    check-cast v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    iput-object v2, v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->cmdLineOptions:Ljava/util/Map;

    .line 667
    :cond_2
    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;
    :try_end_1
    .catch Lgroovyjarjarantlr4/v4/parse/v3TreeGrammarException; {:try_start_1 .. :try_end_1} :catch_0
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_1 .. :try_end_1} :catch_1

    return-object v1

    :catch_0
    move-exception v1

    .line 671
    :try_start_2
    iget-object v2, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v3, Lgroovyjarjarantlr4/v4/tool/ErrorType;->V3_TREE_GRAMMAR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/parse/v3TreeGrammarException;->location:Lgroovyjarjarantlr4/runtime/Token;

    new-array p2, p2, [Ljava/lang/Object;

    invoke-virtual {v2, v3, p1, v1, p2}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->grammarError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/Token;[Ljava/lang/Object;)V
    :try_end_2
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_2 .. :try_end_2} :catch_1

    :cond_3
    return-object v0

    :catch_1
    const-string p1, "can\'t generate this message at moment; antlr recovers"

    .line 677
    invoke-static {p1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->internalError(Ljava/lang/String;)V

    return-object v0
.end method

.method public parseGrammar(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;
    .locals 5

    .line 578
    :try_start_0
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 579
    invoke-virtual {v0}, Ljava/io/File;->isAbsolute()Z

    move-result v1

    if-nez v1, :cond_0

    .line 580
    new-instance v0, Ljava/io/File;

    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->inputDirectory:Ljava/io/File;

    invoke-direct {v0, v1, p1}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 583
    :cond_0
    new-instance v1, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;

    invoke-virtual {v0}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarEncoding:Ljava/lang/String;

    invoke-direct {v1, v0, v2}, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 584
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarantlr4/v4/Tool;->parse(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/CharStream;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object p1
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception v0

    .line 588
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->CANNOT_OPEN_FILE:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    invoke-virtual {v1, v2, v0, v3}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->toolError(Lgroovyjarjarantlr4/v4/tool/ErrorType;Ljava/lang/Throwable;[Ljava/lang/Object;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method public parseGrammarFromString(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;
    .locals 1

    .line 647
    new-instance v0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;-><init>(Ljava/lang/String;)V

    const-string p1, "<string>"

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/v4/Tool;->parse(Ljava/lang/String;Lgroovyjarjarantlr4/runtime/CharStream;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object p1

    return-object p1
.end method

.method public process(Lgroovyjarjarantlr4/v4/tool/Grammar;Z)V
    .locals 3

    .line 338
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->loadImportedGrammars()V

    .line 340
    new-instance v0, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;

    invoke-direct {v0, p1, p0}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/Tool;)V

    .line 341
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->process()V

    .line 345
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-eqz v1, :cond_1

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    iget v1, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->grammarType:I

    const/16 v2, 0x51

    if-ne v1, v2, :cond_1

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    iget-boolean v1, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->hasErrors:Z

    if-nez v1, :cond_1

    .line 348
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/GrammarTransformPipeline;->extractImplicitLexer(Lgroovyjarjarantlr4/v4/tool/Grammar;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 350
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarOptions:Ljava/util/Map;

    if-eqz v1, :cond_0

    .line 351
    iput-object v1, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->cmdLineOptions:Ljava/util/Map;

    .line 354
    :cond_0
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    invoke-direct {v1, p0, v0}, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)V

    .line 355
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    iput-object v0, v1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->fileName:Ljava/lang/String;

    .line 356
    iput-object p1, v1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->originalGrammar:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 357
    iput-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->implicitLexer:Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    .line 358
    iput-object p1, v1, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;->implicitLexerOwner:Lgroovyjarjarantlr4/v4/tool/Grammar;

    .line 360
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v0

    .line 361
    invoke-virtual {p0, v1, p2}, Lgroovyjarjarantlr4/v4/Tool;->processNonCombinedGrammar(Lgroovyjarjarantlr4/v4/tool/Grammar;Z)V

    .line 362
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v1

    if-le v1, v0, :cond_1

    return-void

    .line 370
    :cond_1
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->implicitLexer:Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    if-eqz v0, :cond_2

    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->implicitLexer:Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/v4/tool/Grammar;->importVocab(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 373
    :cond_2
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/Tool;->processNonCombinedGrammar(Lgroovyjarjarantlr4/v4/tool/Grammar;Z)V

    return-void
.end method

.method public processGrammarsOnCommandLine()V
    .locals 3

    .line 308
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->grammarFiles:Ljava/util/List;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/Tool;->sortGrammarByTokenVocab(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    .line 310
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    .line 311
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/v4/Tool;->createGrammar(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;)Lgroovyjarjarantlr4/v4/tool/Grammar;

    move-result-object v2

    .line 312
    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->fileName:Ljava/lang/String;

    iput-object v1, v2, Lgroovyjarjarantlr4/v4/tool/Grammar;->fileName:Ljava/lang/String;

    .line 313
    iget-boolean v1, p0, Lgroovyjarjarantlr4/v4/Tool;->gen_dependencies:Z

    if-eqz v1, :cond_1

    .line 314
    new-instance v1, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;

    invoke-direct {v1, p0, v2}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;-><init>(Lgroovyjarjarantlr4/v4/Tool;Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 322
    sget-object v2, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/BuildDependencyGenerator;->getDependencies()Lorg/stringtemplate/v4/ST;

    move-result-object v1

    invoke-virtual {v1}, Lorg/stringtemplate/v4/ST;->render()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 325
    :cond_1
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v1

    if-nez v1, :cond_0

    const/4 v1, 0x1

    .line 326
    invoke-virtual {p0, v2, v1}, Lgroovyjarjarantlr4/v4/Tool;->process(Lgroovyjarjarantlr4/v4/tool/Grammar;Z)V

    goto :goto_0

    :cond_2
    return-void
.end method

.method public processNonCombinedGrammar(Lgroovyjarjarantlr4/v4/tool/Grammar;Z)V
    .locals 3

    .line 377
    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    if-eqz v0, :cond_8

    iget-object v0, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    iget-boolean v0, v0, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->hasErrors:Z

    if-eqz v0, :cond_0

    goto/16 :goto_1

    .line 378
    :cond_0
    sget-boolean v0, Lgroovyjarjarantlr4/v4/Tool;->internalOption_PrintGrammarTree:Z

    if-eqz v0, :cond_1

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->ast:Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->toStringTree()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 380
    :cond_1
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/Tool;->checkForRuleIssues(Lgroovyjarjarantlr4/v4/tool/Grammar;)Z

    move-result v0

    if-eqz v0, :cond_2

    return-void

    .line 383
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v0

    .line 385
    new-instance v1, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;

    invoke-direct {v1, p1}, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 386
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/semantics/SemanticPipeline;->process()V

    .line 388
    iget-object v1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->getNumErrors()I

    move-result v1

    if-le v1, v0, :cond_3

    return-void

    .line 392
    :cond_3
    invoke-virtual {p1}, Lgroovyjarjarantlr4/v4/tool/Grammar;->isLexer()Z

    move-result v1

    if-eqz v1, :cond_4

    new-instance v1, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory;

    move-object v2, p1

    check-cast v2, Lgroovyjarjarantlr4/v4/tool/LexerGrammar;

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/automata/LexerATNFactory;-><init>(Lgroovyjarjarantlr4/v4/tool/LexerGrammar;)V

    goto :goto_0

    .line 393
    :cond_4
    new-instance v1, Lgroovyjarjarantlr4/v4/automata/ParserATNFactory;

    invoke-direct {v1, p1}, Lgroovyjarjarantlr4/v4/automata/ParserATNFactory;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 394
    :goto_0
    invoke-interface {v1}, Lgroovyjarjarantlr4/v4/automata/ATNFactory;->createATN()Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    move-result-object v1

    iput-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->atn:Lgroovyjarjarantlr4/v4/runtime/atn/ATN;

    .line 396
    iget-boolean v1, p0, Lgroovyjarjarantlr4/v4/Tool;->generate_ATN_dot:Z

    if-eqz v1, :cond_5

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/v4/Tool;->generateATNs(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    :cond_5
    if-eqz p2, :cond_6

    .line 398
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/Tool;->getNumErrors()I

    move-result v1

    if-nez v1, :cond_6

    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/v4/Tool;->generateInterpreterData(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 401
    :cond_6
    new-instance v1, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;

    invoke-direct {v1, p1}, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 402
    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/analysis/AnalysisPipeline;->process()V

    .line 406
    iget-object v1, p1, Lgroovyjarjarantlr4/v4/tool/Grammar;->tool:Lgroovyjarjarantlr4/v4/Tool;

    invoke-virtual {v1}, Lgroovyjarjarantlr4/v4/Tool;->getNumErrors()I

    move-result v1

    if-le v1, v0, :cond_7

    return-void

    :cond_7
    if-eqz p2, :cond_8

    .line 410
    new-instance p2, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;

    invoke-direct {p2, p1}, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;-><init>(Lgroovyjarjarantlr4/v4/tool/Grammar;)V

    .line 411
    invoke-virtual {p2}, Lgroovyjarjarantlr4/v4/codegen/CodeGenPipeline;->process()V

    :cond_8
    :goto_1
    return-void
.end method

.method public removeListener(Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;)V
    .locals 1

    .line 915
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    return-void
.end method

.method public removeListeners()V
    .locals 1

    .line 916
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    return-void
.end method

.method public sortGrammarByTokenVocab(Ljava/util/List;)Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;",
            ">;"
        }
    .end annotation

    .line 492
    new-instance v0, Lgroovyjarjarantlr4/v4/misc/Graph;

    invoke-direct {v0}, Lgroovyjarjarantlr4/v4/misc/Graph;-><init>()V

    .line 493
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 494
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_6

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    .line 495
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/v4/Tool;->parseGrammar(Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    move-result-object v3

    if-eqz v3, :cond_0

    .line 496
    instance-of v4, v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarASTErrorNode;

    if-eqz v4, :cond_1

    goto :goto_0

    .line 497
    :cond_1
    move-object v4, v3

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    iget-boolean v4, v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->hasErrors:Z

    if-eqz v4, :cond_2

    goto :goto_0

    .line 499
    :cond_2
    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 500
    iput-object v2, v3, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->fileName:Ljava/lang/String;

    const/4 v2, 0x0

    .line 501
    invoke-virtual {v3, v2}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getChild(I)Lgroovyjarjarantlr4/runtime/tree/Tree;

    move-result-object v4

    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getText()Ljava/lang/String;

    move-result-object v4

    const-string v5, "tokenVocab"

    .line 503
    invoke-static {v3, v5}, Lgroovyjarjarantlr4/v4/Tool;->findOptionValueAST(Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;Ljava/lang/String;)Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;

    move-result-object v3

    if-eqz v3, :cond_5

    .line 506
    invoke-virtual {v3}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarAST;->getText()Ljava/lang/String;

    move-result-object v3

    .line 508
    invoke-virtual {v3}, Ljava/lang/String;->length()I

    move-result v5

    .line 509
    invoke-virtual {v3, v2}, Ljava/lang/String;->charAt(I)C

    move-result v2

    add-int/lit8 v6, v5, -0x1

    .line 510
    invoke-virtual {v3, v6}, Ljava/lang/String;->charAt(I)C

    move-result v7

    const/4 v8, 0x2

    if-lt v5, v8, :cond_3

    const/16 v5, 0x27

    if-ne v2, v5, :cond_3

    if-ne v7, v5, :cond_3

    const/4 v2, 0x1

    .line 512
    invoke-virtual {v3, v2, v6}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v3

    :cond_3
    const/16 v2, 0x2f

    .line 516
    invoke-virtual {v3, v2}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v2

    if-ltz v2, :cond_4

    add-int/lit8 v2, v2, 0x1

    .line 518
    invoke-virtual {v3, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v3

    .line 520
    :cond_4
    invoke-virtual {v0, v4, v3}, Lgroovyjarjarantlr4/v4/misc/Graph;->addEdge(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 524
    :cond_5
    invoke-virtual {v0, v4, v4}, Lgroovyjarjarantlr4/v4/misc/Graph;->addEdge(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_0

    .line 527
    :cond_6
    invoke-virtual {v0}, Lgroovyjarjarantlr4/v4/misc/Graph;->sort()Ljava/util/List;

    move-result-object p1

    .line 530
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 531
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_7
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_9

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    .line 532
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_8
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_7

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;

    .line 533
    invoke-virtual {v4}, Lgroovyjarjarantlr4/v4/tool/ast/GrammarRootAST;->getGrammarName()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v5, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_8

    .line 534
    invoke-interface {v0, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_9
    return-object v0
.end method

.method public version()V
    .locals 2

    .line 947
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ANTLR Parser Generator  Version "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarantlr4/v4/Tool;->VERSION:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/v4/Tool;->info(Ljava/lang/String;)V

    return-void
.end method

.method public warning(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V
    .locals 3

    .line 934
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 935
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->defaultListener:Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/v4/tool/DefaultToolListener;->warning(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    goto :goto_1

    .line 938
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/v4/Tool;->listeners:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;

    invoke-interface {v1, p1}, Lgroovyjarjarantlr4/v4/tool/ANTLRToolListener;->warning(Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    goto :goto_0

    .line 941
    :cond_1
    :goto_1
    iget-boolean p1, p0, Lgroovyjarjarantlr4/v4/Tool;->warnings_are_errors:Z

    if-eqz p1, :cond_2

    .line 942
    iget-object p1, p0, Lgroovyjarjarantlr4/v4/Tool;->errMgr:Lgroovyjarjarantlr4/v4/tool/ErrorManager;

    sget-object v0, Lgroovyjarjarantlr4/v4/tool/ErrorType;->WARNING_TREATED_AS_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    new-instance v1, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;

    sget-object v2, Lgroovyjarjarantlr4/v4/tool/ErrorType;->WARNING_TREATED_AS_ERROR:Lgroovyjarjarantlr4/v4/tool/ErrorType;

    invoke-direct {v1, v2}, Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;-><init>(Lgroovyjarjarantlr4/v4/tool/ErrorType;)V

    invoke-virtual {p1, v0, v1}, Lgroovyjarjarantlr4/v4/tool/ErrorManager;->emit(Lgroovyjarjarantlr4/v4/tool/ErrorType;Lgroovyjarjarantlr4/v4/tool/ANTLRMessage;)V

    :cond_2
    return-void
.end method

.method protected writeDOTFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Lgroovyjarjarantlr4/v4/tool/Rule;Ljava/lang/String;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 885
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->g:Lgroovyjarjarantlr4/v4/tool/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr4/v4/tool/Grammar;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object p2, p2, Lgroovyjarjarantlr4/v4/tool/Rule;->name:Ljava/lang/String;

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarantlr4/v4/Tool;->writeDOTFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method protected writeDOTFile(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 889
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, ".dot"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/v4/Tool;->getOutputFileWriter(Lgroovyjarjarantlr4/v4/tool/Grammar;Ljava/lang/String;)Ljava/io/Writer;

    move-result-object p1

    .line 891
    :try_start_0
    invoke-virtual {p1, p3}, Ljava/io/Writer;->write(Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 894
    invoke-virtual {p1}, Ljava/io/Writer;->close()V

    return-void

    :catchall_0
    move-exception p2

    invoke-virtual {p1}, Ljava/io/Writer;->close()V

    throw p2
.end method
