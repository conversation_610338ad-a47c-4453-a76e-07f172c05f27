.class public Lgroovyjarjarantlr4/runtime/debug/Profiler$DecisionDescriptor;
.super Ljava/lang/Object;
.source "Profiler.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/debug/Profiler;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "DecisionDescriptor"
.end annotation


# instance fields
.field public avgk:F

.field public couldBacktrack:Z

.field public decision:I

.field public fileName:Ljava/lang/String;

.field public line:I

.field public maxk:I

.field public n:I

.field public numBacktrackOccurrences:I

.field public numSemPredEvals:I

.field public pos:I

.field public ruleName:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 85
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
