.class public Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;
.super Lgroovyjarjarantlr4/runtime/tree/TreeParser;
.source "DebugTreeParser.java"


# instance fields
.field protected dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

.field public isCyclicDecision:Z


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 2

    .line 54
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;

    invoke-direct {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    move-object p1, v0

    :goto_0
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    .line 38
    iput-object v1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    const/4 p1, 0x0

    .line 43
    iput-boolean p1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->isCyclicDecision:Z

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V
    .locals 1

    .line 58
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;

    invoke-direct {v0, p1, p2}, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    move-object p1, v0

    :goto_0
    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 1

    .line 49
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;

    invoke-direct {v0, p1, p2}, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    move-object p1, v0

    :goto_0
    invoke-direct {p0, p1, p3}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    const/4 p1, 0x0

    .line 38
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    const/4 p1, 0x0

    .line 43
    iput-boolean p1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->isCyclicDecision:Z

    .line 50
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->setDebugListener(Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    return-void
.end method


# virtual methods
.method public beginBacktrack(I)V
    .locals 1

    .line 107
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->beginBacktrack(I)V

    return-void
.end method

.method public beginResync()V
    .locals 1

    .line 98
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->beginResync()V

    return-void
.end method

.method public endBacktrack(IZ)V
    .locals 1

    .line 111
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->endBacktrack(IZ)V

    return-void
.end method

.method public endResync()V
    .locals 1

    .line 103
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->endResync()V

    return-void
.end method

.method public getDebugListener()Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;
    .locals 1

    .line 72
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    return-object v0
.end method

.method protected getMissingSymbol(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/RecognitionException;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;
    .locals 0

    .line 91
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->getMissingSymbol(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/RecognitionException;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;

    move-result-object p1

    .line 92
    iget-object p2, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {p2, p1}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->consumeNode(Ljava/lang/Object;)V

    return-object p1
.end method

.method public reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 1

    .line 82
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->recognitionException(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    return-void
.end method

.method public reportError(Ljava/io/IOException;)V
    .locals 1

    .line 76
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 77
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {p1, v0}, Ljava/io/IOException;->printStackTrace(Ljava/io/PrintStream;)V

    return-void
.end method

.method public setDebugListener(Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V
    .locals 1

    .line 65
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    instance-of v0, v0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;

    if-eqz v0, :cond_0

    .line 66
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    check-cast v0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/runtime/debug/DebugTreeNodeStream;->setDebugListener(Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    .line 68
    :cond_0
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugTreeParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    return-void
.end method
