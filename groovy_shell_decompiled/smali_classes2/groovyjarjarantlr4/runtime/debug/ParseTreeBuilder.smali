.class public Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;
.super Lgroovyjarjarantlr4/runtime/debug/BlankDebugEventListener;
.source "ParseTreeBuilder.java"


# static fields
.field public static final EPSILON_PAYLOAD:Ljava/lang/String; = "<epsilon>"


# instance fields
.field backtracking:I

.field callStack:Ljava/util/Stack;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Stack<",
            "Lgroovyjarjarantlr4/runtime/tree/ParseTree;",
            ">;"
        }
    .end annotation
.end field

.field hiddenTokens:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 2

    .line 48
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/debug/BlankDebugEventListener;-><init>()V

    .line 44
    new-instance v0, Ljava/util/Stack;

    invoke-direct {v0}, Ljava/util/Stack;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->callStack:Ljava/util/Stack;

    .line 45
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->hiddenTokens:Ljava/util/List;

    const/4 v0, 0x0

    .line 46
    iput v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    .line 49
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<grammar "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ">"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->create(Ljava/lang/Object;)Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    move-result-object p1

    .line 50
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->callStack:Ljava/util/Stack;

    invoke-virtual {v0, p1}, Ljava/util/Stack;->push(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public consumeHiddenToken(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 1

    .line 105
    iget v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    if-lez v0, :cond_0

    return-void

    .line 106
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->hiddenTokens:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public consumeToken(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 2

    .line 95
    iget v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    if-lez v0, :cond_0

    return-void

    .line 96
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->callStack:Ljava/util/Stack;

    invoke-virtual {v0}, Ljava/util/Stack;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    .line 97
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->create(Ljava/lang/Object;)Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    move-result-object p1

    .line 98
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->hiddenTokens:Ljava/util/List;

    iput-object v1, p1, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->hiddenTokens:Ljava/util/List;

    .line 99
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->hiddenTokens:Ljava/util/List;

    .line 100
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    return-void
.end method

.method public create(Ljava/lang/Object;)Lgroovyjarjarantlr4/runtime/tree/ParseTree;
    .locals 1

    .line 61
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;-><init>(Ljava/lang/Object;)V

    return-object v0
.end method

.method public enterDecision(IZ)V
    .locals 0

    .line 70
    iget p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    return-void
.end method

.method public enterRule(Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 76
    iget p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    if-lez p1, :cond_0

    return-void

    .line 77
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->callStack:Ljava/util/Stack;

    invoke-virtual {p1}, Ljava/util/Stack;->peek()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    .line 78
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->create(Ljava/lang/Object;)Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    move-result-object p2

    .line 79
    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 80
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->callStack:Ljava/util/Stack;

    invoke-virtual {p1, p2}, Ljava/util/Stack;->push(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public epsilonNode()Lgroovyjarjarantlr4/runtime/tree/ParseTree;
    .locals 1

    const-string v0, "<epsilon>"

    .line 65
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->create(Ljava/lang/Object;)Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    move-result-object v0

    return-object v0
.end method

.method public exitDecision(I)V
    .locals 0

    .line 72
    iget p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    return-void
.end method

.method public exitRule(Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 85
    iget p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    if-lez p1, :cond_0

    return-void

    .line 86
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->callStack:Ljava/util/Stack;

    invoke-virtual {p1}, Ljava/util/Stack;->peek()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    .line 87
    invoke-virtual {p1}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->getChildCount()I

    move-result p2

    if-nez p2, :cond_1

    .line 88
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->epsilonNode()Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    move-result-object p2

    invoke-virtual {p1, p2}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    .line 90
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->callStack:Ljava/util/Stack;

    invoke-virtual {p1}, Ljava/util/Stack;->pop()Ljava/lang/Object;

    return-void
.end method

.method public getTree()Lgroovyjarjarantlr4/runtime/tree/ParseTree;
    .locals 2

    .line 54
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->callStack:Ljava/util/Stack;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/Stack;->elementAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    return-object v0
.end method

.method public recognitionException(Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 1

    .line 111
    iget v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->backtracking:I

    if-lez v0, :cond_0

    return-void

    .line 112
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->callStack:Ljava/util/Stack;

    invoke-virtual {v0}, Ljava/util/Stack;->peek()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    .line 113
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/debug/ParseTreeBuilder;->create(Ljava/lang/Object;)Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    move-result-object p1

    .line 114
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->addChild(Lgroovyjarjarantlr4/runtime/tree/Tree;)V

    return-void
.end method
