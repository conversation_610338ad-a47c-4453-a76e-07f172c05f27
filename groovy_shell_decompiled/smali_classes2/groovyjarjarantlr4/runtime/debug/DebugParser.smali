.class public Lgroovyjarjarantlr4/runtime/debug/DebugParser;
.super Lgroovyjarjarantlr4/runtime/Parser;
.source "DebugParser.java"


# instance fields
.field protected dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

.field public isCyclicDecision:Z


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 2

    .line 52
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;

    invoke-direct {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    move-object p1, v0

    :goto_0
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/Parser;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    .line 36
    iput-object v1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    const/4 p1, 0x0

    .line 41
    iput-boolean p1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->isCyclicDecision:Z

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V
    .locals 1

    .line 56
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;

    invoke-direct {v0, p1, p2}, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    move-object p1, v0

    :goto_0
    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/debug/DebugParser;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 1

    .line 47
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;

    invoke-direct {v0, p1, p2}, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    move-object p1, v0

    :goto_0
    invoke-direct {p0, p1, p3}, Lgroovyjarjarantlr4/runtime/Parser;-><init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    const/4 p1, 0x0

    .line 36
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    const/4 p1, 0x0

    .line 41
    iput-boolean p1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->isCyclicDecision:Z

    .line 48
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->setDebugListener(Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    return-void
.end method


# virtual methods
.method public beginBacktrack(I)V
    .locals 1

    .line 89
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->beginBacktrack(I)V

    return-void
.end method

.method public beginResync()V
    .locals 1

    .line 80
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->beginResync()V

    return-void
.end method

.method public endBacktrack(IZ)V
    .locals 1

    .line 93
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0, p1, p2}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->endBacktrack(IZ)V

    return-void
.end method

.method public endResync()V
    .locals 1

    .line 85
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->endResync()V

    return-void
.end method

.method public getDebugListener()Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;
    .locals 1

    .line 70
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    return-object v0
.end method

.method public reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 1

    .line 98
    invoke-super {p0, p1}, Lgroovyjarjarantlr4/runtime/Parser;->reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    .line 99
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;->recognitionException(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    return-void
.end method

.method public reportError(Ljava/io/IOException;)V
    .locals 1

    .line 74
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 75
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {p1, v0}, Ljava/io/IOException;->printStackTrace(Ljava/io/PrintStream;)V

    return-void
.end method

.method public setDebugListener(Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V
    .locals 1

    .line 63
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    instance-of v0, v0, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;

    if-eqz v0, :cond_0

    .line 64
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    check-cast v0, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr4/runtime/debug/DebugTokenStream;->setDebugListener(Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;)V

    .line 66
    :cond_0
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/debug/DebugParser;->dbg:Lgroovyjarjarantlr4/runtime/debug/DebugEventListener;

    return-void
.end method
