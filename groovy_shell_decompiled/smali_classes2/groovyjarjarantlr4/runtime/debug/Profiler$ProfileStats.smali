.class public Lgroovyjarjarantlr4/runtime/debug/Profiler$ProfileStats;
.super Ljava/lang/Object;
.source "Profiler.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/debug/Profiler;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ProfileStats"
.end annotation


# instance fields
.field public Version:Ljava/lang/String;

.field public averageDecisionPercentBacktracks:F

.field public avgDecisionMaxCyclicLookaheads:I

.field public avgDecisionMaxFixedLookaheads:I

.field public avgkPerBacktrackingDecisionEvent:F

.field public avgkPerDecisionEvent:F

.field public maxDecisionMaxCyclicLookaheads:I

.field public maxDecisionMaxFixedLookaheads:I

.field public maxRuleInvocationDepth:I

.field public minDecisionMaxCyclicLookaheads:I

.field public minDecisionMaxFixedLookaheads:I

.field public name:Ljava/lang/String;

.field public numBacktrackOccurrences:I

.field public numCharsMatched:I

.field public numCyclicDecisions:I

.field public numDecisionEvents:I

.field public numDecisionsCovered:I

.field public numDecisionsThatDoBacktrack:I

.field public numDecisionsThatPotentiallyBacktrack:I

.field public numFixedDecisions:I

.field public numGuessingRuleInvocations:I

.field public numHiddenCharsMatched:I

.field public numHiddenTokens:I

.field public numMemoizationCacheEntries:I

.field public numMemoizationCacheHits:I

.field public numMemoizationCacheMisses:I

.field public numReportedErrors:I

.field public numRuleInvocations:I

.field public numSemanticPredicates:I

.field public numTokens:I

.field public numUniqueRulesInvoked:I

.field public stddevDecisionMaxCyclicLookaheads:I

.field public stddevDecisionMaxFixedLookaheads:I


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 44
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
