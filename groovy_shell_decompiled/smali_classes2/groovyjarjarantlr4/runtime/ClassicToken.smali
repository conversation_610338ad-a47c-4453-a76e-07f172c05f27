.class public Lgroovyjarjarantlr4/runtime/ClassicToken;
.super Ljava/lang/Object;
.source "ClassicToken.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/Token;


# instance fields
.field protected channel:I

.field protected charPositionInLine:I

.field protected index:I

.field protected line:I

.field protected text:Ljava/lang/String;

.field protected type:I


# direct methods
.method public constructor <init>(I)V
    .locals 1

    .line 47
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 42
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    .line 48
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->type:I

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;)V
    .locals 1

    .line 59
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 42
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    .line 60
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->type:I

    .line 61
    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->text:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;I)V
    .locals 1

    .line 64
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 42
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    .line 65
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->type:I

    .line 66
    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->text:Ljava/lang/String;

    .line 67
    iput p3, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 1

    .line 51
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 42
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    .line 52
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->text:Ljava/lang/String;

    .line 53
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->type:I

    .line 54
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->line:I

    .line 55
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->charPositionInLine:I

    .line 56
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getChannel()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    return-void
.end method


# virtual methods
.method public getChannel()I
    .locals 1

    .line 107
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    return v0
.end method

.method public getCharPositionInLine()I
    .locals 1

    .line 97
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->charPositionInLine:I

    return v0
.end method

.method public getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getLine()I
    .locals 1

    .line 92
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->line:I

    return v0
.end method

.method public getText()Ljava/lang/String;
    .locals 1

    .line 82
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->text:Ljava/lang/String;

    return-object v0
.end method

.method public getTokenIndex()I
    .locals 1

    .line 122
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->index:I

    return v0
.end method

.method public getType()I
    .locals 1

    .line 72
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->type:I

    return v0
.end method

.method public setChannel(I)V
    .locals 0

    .line 112
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    return-void
.end method

.method public setCharPositionInLine(I)V
    .locals 0

    .line 102
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->charPositionInLine:I

    return-void
.end method

.method public setInputStream(Lgroovyjarjarantlr4/runtime/CharStream;)V
    .locals 0

    return-void
.end method

.method public setLine(I)V
    .locals 0

    .line 77
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->line:I

    return-void
.end method

.method public setText(Ljava/lang/String;)V
    .locals 0

    .line 87
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->text:Ljava/lang/String;

    return-void
.end method

.method public setTokenIndex(I)V
    .locals 0

    .line 127
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->index:I

    return-void
.end method

.method public setType(I)V
    .locals 0

    .line 117
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->type:I

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 4

    .line 142
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    if-lez v0, :cond_0

    .line 143
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, ",channel="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->channel:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const-string v0, ""

    .line 145
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/ClassicToken;->getText()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_1

    const-string v2, "\n"

    const-string v3, "\\\\n"

    .line 147
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "\r"

    const-string v3, "\\\\r"

    .line 148
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "\t"

    const-string v3, "\\\\t"

    .line 149
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    :cond_1
    const-string v1, "<no text>"

    .line 154
    :goto_1
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "[@"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/ClassicToken;->getTokenIndex()I

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, ",\'"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "\',<"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v2, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->type:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ">"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ","

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/runtime/ClassicToken;->line:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/ClassicToken;->getCharPositionInLine()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
