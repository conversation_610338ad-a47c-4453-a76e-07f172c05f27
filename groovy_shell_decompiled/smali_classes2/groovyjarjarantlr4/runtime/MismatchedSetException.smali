.class public Lgroovyjarjarantlr4/runtime/MismatchedSetException;
.super Lgroovyjarjarantlr4/runtime/RecognitionException;
.source "MismatchedSetException.java"


# instance fields
.field public expecting:Lgroovyjarjarantlr4/runtime/BitSet;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 34
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/RecognitionException;-><init>()V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/BitSet;Lgroovyjarjarantlr4/runtime/IntStream;)V
    .locals 0

    .line 37
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/runtime/RecognitionException;-><init>(Lgroovyjarjarantlr4/runtime/IntStream;)V

    .line 38
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/MismatchedSetException;->expecting:Lgroovyjarjarantlr4/runtime/BitSet;

    return-void
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 2

    .line 43
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "MismatchedSetException("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/MismatchedSetException;->getUnexpectedType()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "!="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/MismatchedSetException;->expecting:Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
