.class public abstract Lgroovyjarjarantlr4/runtime/Lexer;
.super Lgroovyjarjarantlr4/runtime/BaseRecognizer;
.source "Lexer.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/TokenSource;


# instance fields
.field protected input:Lgroovyjarjarantlr4/runtime/CharStream;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 39
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;-><init>()V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/CharStream;)V
    .locals 0

    .line 42
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;-><init>()V

    .line 43
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/CharStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 0

    .line 47
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;-><init>(Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    .line 48
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    return-void
.end method


# virtual methods
.method public emit()Lgroovyjarjarantlr4/runtime/Token;
    .locals 7

    .line 169
    new-instance v6, Lgroovyjarjarantlr4/runtime/CommonToken;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->type:I

    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v3, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->channel:I

    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v4, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharIndex:I

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharIndex()I

    move-result v0

    add-int/lit8 v5, v0, -0x1

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;IIII)V

    .line 170
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartLine:I

    invoke-interface {v6, v0}, Lgroovyjarjarantlr4/runtime/Token;->setLine(I)V

    .line 171
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->text:Ljava/lang/String;

    invoke-interface {v6, v0}, Lgroovyjarjarantlr4/runtime/Token;->setText(Ljava/lang/String;)V

    .line 172
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharPositionInLine:I

    invoke-interface {v6, v0}, Lgroovyjarjarantlr4/runtime/Token;->setCharPositionInLine(I)V

    .line 173
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr4/runtime/Lexer;->emit(Lgroovyjarjarantlr4/runtime/Token;)V

    return-object v6
.end method

.method public emit(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 1

    .line 156
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-object p1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->token:Lgroovyjarjarantlr4/runtime/Token;

    return-void
.end method

.method public getCharErrorDisplay(I)Ljava/lang/String;
    .locals 2

    int-to-char v0, p1

    .line 317
    invoke-static {v0}, Ljava/lang/String;->valueOf(C)Ljava/lang/String;

    move-result-object v0

    const/4 v1, -0x1

    if-eq p1, v1, :cond_3

    const/16 v1, 0xd

    if-eq p1, v1, :cond_2

    const/16 v1, 0x9

    if-eq p1, v1, :cond_1

    const/16 v1, 0xa

    if-eq p1, v1, :cond_0

    goto :goto_0

    :cond_0
    const-string v0, "\\n"

    goto :goto_0

    :cond_1
    const-string v0, "\\t"

    goto :goto_0

    :cond_2
    const-string v0, "\\r"

    goto :goto_0

    :cond_3
    const-string v0, "<EOF>"

    .line 332
    :goto_0
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "\'"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getCharIndex()I
    .locals 1

    .line 242
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/CharStream;->index()I

    move-result v0

    return v0
.end method

.method public getCharPositionInLine()I
    .locals 1

    .line 237
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/CharStream;->getCharPositionInLine()I

    move-result v0

    return v0
.end method

.method public getCharStream()Lgroovyjarjarantlr4/runtime/CharStream;
    .locals 1

    .line 142
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    return-object v0
.end method

.method public getEOFToken()Lgroovyjarjarantlr4/runtime/Token;
    .locals 7

    .line 113
    new-instance v6, Lgroovyjarjarantlr4/runtime/CommonToken;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/CharStream;->index()I

    move-result v4

    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/CharStream;->index()I

    move-result v5

    const/4 v2, -0x1

    const/4 v3, 0x0

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(Lgroovyjarjarantlr4/runtime/CharStream;IIII)V

    .line 116
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getLine()I

    move-result v0

    invoke-interface {v6, v0}, Lgroovyjarjarantlr4/runtime/Token;->setLine(I)V

    .line 117
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharPositionInLine()I

    move-result v0

    invoke-interface {v6, v0}, Lgroovyjarjarantlr4/runtime/Token;->setCharPositionInLine(I)V

    return-object v6
.end method

.method public getErrorMessage(Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    .line 281
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;

    const-string v1, "mismatched character "

    if-eqz v0, :cond_0

    .line 282
    move-object p2, p1

    check-cast p2, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;

    .line 283
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->c:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharErrorDisplay(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " expecting "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget p2, p2, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;->expecting:I

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharErrorDisplay(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto/16 :goto_0

    .line 285
    :cond_0
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/NoViableAltException;

    if-eqz v0, :cond_1

    .line 286
    move-object p2, p1

    check-cast p2, Lgroovyjarjarantlr4/runtime/NoViableAltException;

    .line 290
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "no viable alternative at character "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->c:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharErrorDisplay(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto/16 :goto_0

    .line 292
    :cond_1
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/EarlyExitException;

    if-eqz v0, :cond_2

    .line 293
    move-object p2, p1

    check-cast p2, Lgroovyjarjarantlr4/runtime/EarlyExitException;

    .line 295
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "required (...)+ loop did not match anything at character "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->c:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharErrorDisplay(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto/16 :goto_0

    .line 297
    :cond_2
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/MismatchedNotSetException;

    const-string v2, " expecting set "

    if-eqz v0, :cond_3

    .line 298
    move-object p2, p1

    check-cast p2, Lgroovyjarjarantlr4/runtime/MismatchedNotSetException;

    .line 299
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->c:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharErrorDisplay(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object p2, p2, Lgroovyjarjarantlr4/runtime/MismatchedNotSetException;->expecting:Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    .line 301
    :cond_3
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/MismatchedSetException;

    if-eqz v0, :cond_4

    .line 302
    move-object p2, p1

    check-cast p2, Lgroovyjarjarantlr4/runtime/MismatchedSetException;

    .line 303
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->c:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharErrorDisplay(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object p2, p2, Lgroovyjarjarantlr4/runtime/MismatchedSetException;->expecting:Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    .line 305
    :cond_4
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/MismatchedRangeException;

    if-eqz v0, :cond_5

    .line 306
    move-object p2, p1

    check-cast p2, Lgroovyjarjarantlr4/runtime/MismatchedRangeException;

    .line 307
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->c:I

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharErrorDisplay(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v0, p2, Lgroovyjarjarantlr4/runtime/MismatchedRangeException;->a:I

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharErrorDisplay(I)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ".."

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget p2, p2, Lgroovyjarjarantlr4/runtime/MismatchedRangeException;->b:I

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharErrorDisplay(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    .line 311
    :cond_5
    invoke-super {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getErrorMessage(Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    :goto_0
    return-object p1
.end method

.method public getLine()I
    .locals 1

    .line 233
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/CharStream;->getLine()I

    move-result v0

    return v0
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 147
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/CharStream;->getSourceName()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getText()Ljava/lang/String;
    .locals 3

    .line 249
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->text:Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 250
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->text:Ljava/lang/String;

    return-object v0

    .line 252
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v1, v1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharIndex:I

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharIndex()I

    move-result v2

    add-int/lit8 v2, v2, -0x1

    invoke-interface {v0, v1, v2}, Lgroovyjarjarantlr4/runtime/CharStream;->substring(II)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public abstract mTokens()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation
.end method

.method public match(I)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/MismatchedTokenException;
        }
    .end annotation

    .line 201
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/CharStream;->LA(I)I

    move-result v0

    if-eq v0, p1, :cond_1

    .line 202
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    if-lez v0, :cond_0

    .line 203
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    return-void

    .line 206
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-direct {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;-><init>(ILgroovyjarjarantlr4/runtime/IntStream;)V

    .line 208
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/Lexer;->recover(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    .line 209
    throw v0

    .line 211
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/CharStream;->consume()V

    .line 212
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 v0, 0x0

    iput-boolean v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    return-void
.end method

.method public match(Ljava/lang/String;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/MismatchedTokenException;
        }
    .end annotation

    const/4 v0, 0x0

    move v1, v0

    .line 179
    :goto_0
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v2

    if-ge v1, v2, :cond_2

    .line 180
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    const/4 v3, 0x1

    invoke-interface {v2, v3}, Lgroovyjarjarantlr4/runtime/CharStream;->LA(I)I

    move-result v2

    invoke-virtual {p1, v1}, Ljava/lang/String;->charAt(I)C

    move-result v4

    if-eq v2, v4, :cond_1

    .line 181
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    if-lez v0, :cond_0

    .line 182
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v3, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    return-void

    .line 185
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;

    invoke-virtual {p1, v1}, Ljava/lang/String;->charAt(I)C

    move-result p1

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-direct {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;-><init>(ILgroovyjarjarantlr4/runtime/IntStream;)V

    .line 187
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/Lexer;->recover(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    .line 188
    throw v0

    :cond_1
    add-int/lit8 v1, v1, 0x1

    .line 191
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/CharStream;->consume()V

    .line 192
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v0, v2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    goto :goto_0

    :cond_2
    return-void
.end method

.method public matchAny()V
    .locals 1

    .line 197
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/CharStream;->consume()V

    return-void
.end method

.method public matchRange(II)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/MismatchedRangeException;
        }
    .end annotation

    .line 218
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/CharStream;->LA(I)I

    move-result v0

    if-lt v0, p1, :cond_1

    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/CharStream;->LA(I)I

    move-result v0

    if-le v0, p2, :cond_0

    goto :goto_0

    .line 228
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/CharStream;->consume()V

    .line 229
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 p2, 0x0

    iput-boolean p2, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    return-void

    .line 219
    :cond_1
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    if-lez v0, :cond_2

    .line 220
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    return-void

    .line 223
    :cond_2
    new-instance v0, Lgroovyjarjarantlr4/runtime/MismatchedRangeException;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-direct {v0, p1, p2, v1}, Lgroovyjarjarantlr4/runtime/MismatchedRangeException;-><init>(IILgroovyjarjarantlr4/runtime/IntStream;)V

    .line 225
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/Lexer;->recover(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    .line 226
    throw v0
.end method

.method public nextToken()Lgroovyjarjarantlr4/runtime/Token;
    .locals 3

    .line 75
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 v1, 0x0

    iput-object v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 76
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 v2, 0x0

    iput v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->channel:I

    .line 77
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/CharStream;->index()I

    move-result v2

    iput v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharIndex:I

    .line 78
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/CharStream;->getCharPositionInLine()I

    move-result v2

    iput v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharPositionInLine:I

    .line 79
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/CharStream;->getLine()I

    move-result v2

    iput v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartLine:I

    .line 80
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-object v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->text:Ljava/lang/String;

    .line 81
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/CharStream;->LA(I)I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    .line 82
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getEOFToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    return-object v0

    .line 85
    :cond_0
    :try_start_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->mTokens()V

    .line 86
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->token:Lgroovyjarjarantlr4/runtime/Token;

    if-nez v0, :cond_1

    .line 87
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->emit()Lgroovyjarjarantlr4/runtime/Token;

    goto :goto_1

    .line 89
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->token:Lgroovyjarjarantlr4/runtime/Token;

    sget-object v1, Lgroovyjarjarantlr4/runtime/Token;->SKIP_TOKEN:Lgroovyjarjarantlr4/runtime/Token;

    if-ne v0, v1, :cond_2

    goto :goto_0

    .line 92
    :cond_2
    :goto_1
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->token:Lgroovyjarjarantlr4/runtime/Token;
    :try_end_0
    .catch Lgroovyjarjarantlr4/runtime/MismatchedRangeException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Lgroovyjarjarantlr4/runtime/MismatchedTokenException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v0

    :catch_0
    move-exception v0

    .line 103
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/Lexer;->reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    .line 104
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/Lexer;->recover(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    goto :goto_0

    :catch_1
    move-exception v0

    .line 99
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/Lexer;->reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    goto :goto_0

    :catch_2
    move-exception v0

    .line 95
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/Lexer;->reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    goto :goto_0
.end method

.method public recover(Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 0

    .line 343
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/CharStream;->consume()V

    return-void
.end method

.method public reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 1

    .line 275
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getTokenNames()[Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarantlr4/runtime/Lexer;->displayRecognitionError([Ljava/lang/String;Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    return-void
.end method

.method public reset()V
    .locals 3

    .line 53
    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->reset()V

    .line 55
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 56
    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/CharStream;->seek(I)V

    .line 58
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    if-nez v0, :cond_1

    return-void

    .line 61
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 v2, 0x0

    iput-object v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 62
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->type:I

    .line 63
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->channel:I

    .line 64
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 v1, -0x1

    iput v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharIndex:I

    .line 65
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharPositionInLine:I

    .line 66
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartLine:I

    .line 67
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-object v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->text:Ljava/lang/String;

    return-void
.end method

.method public setCharStream(Lgroovyjarjarantlr4/runtime/CharStream;)V
    .locals 1

    const/4 v0, 0x0

    .line 136
    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    .line 137
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->reset()V

    .line 138
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    return-void
.end method

.method public setText(Ljava/lang/String;)V
    .locals 1

    .line 259
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-object p1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->text:Ljava/lang/String;

    return-void
.end method

.method public skip()V
    .locals 2

    .line 128
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Lexer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    sget-object v1, Lgroovyjarjarantlr4/runtime/Token;->SKIP_TOKEN:Lgroovyjarjarantlr4/runtime/Token;

    iput-object v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->token:Lgroovyjarjarantlr4/runtime/Token;

    return-void
.end method

.method public traceIn(Ljava/lang/String;I)V
    .locals 3

    .line 347
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    const/4 v2, 0x1

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/runtime/CharStream;->LT(I)I

    move-result v1

    int-to-char v1, v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " line="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getLine()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharPositionInLine()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 348
    invoke-super {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->traceIn(Ljava/lang/String;ILjava/lang/Object;)V

    return-void
.end method

.method public traceOut(Ljava/lang/String;I)V
    .locals 3

    .line 352
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/Lexer;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    const/4 v2, 0x1

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/runtime/CharStream;->LT(I)I

    move-result v1

    int-to-char v1, v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " line="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getLine()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Lexer;->getCharPositionInLine()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 353
    invoke-super {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->traceOut(Ljava/lang/String;ILjava/lang/Object;)V

    return-void
.end method
