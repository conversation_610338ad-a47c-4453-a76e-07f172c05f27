.class public Lgroovyjarjarantlr4/runtime/ParserRuleReturnScope;
.super Lgroovyjarjarantlr4/runtime/RuleReturnScope;
.source "ParserRuleReturnScope.java"


# instance fields
.field public start:Lgroovyjarjarantlr4/runtime/Token;

.field public stop:Lgroovyjarjarantlr4/runtime/Token;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 48
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/RuleReturnScope;-><init>()V

    return-void
.end method


# virtual methods
.method public getStart()Ljava/lang/Object;
    .locals 1

    .line 51
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/ParserRuleReturnScope;->start:Lgroovyjarjarantlr4/runtime/Token;

    return-object v0
.end method

.method public getStop()Ljava/lang/Object;
    .locals 1

    .line 53
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/ParserRuleReturnScope;->stop:Lgroovyjarjarantlr4/runtime/Token;

    return-object v0
.end method

.method public getTree()Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
