.class public Lgroovyjarjarantlr4/runtime/BufferedTokenStream;
.super Ljava/lang/Object;
.source "BufferedTokenStream.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/TokenStream;


# instance fields
.field protected lastMarker:I

.field protected p:I

.field protected range:I

.field protected tokenSource:Lgroovyjarjarantlr4/runtime/TokenSource;

.field protected tokens:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 71
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 57
    new-instance v0, Ljava/util/ArrayList;

    const/16 v1, 0x64

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    const/4 v0, -0x1

    .line 67
    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    .line 69
    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->range:I

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenSource;)V
    .locals 2

    .line 73
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 57
    new-instance v0, Ljava/util/ArrayList;

    const/16 v1, 0x64

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    const/4 v0, -0x1

    .line 67
    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    .line 69
    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->range:I

    .line 74
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/runtime/TokenSource;

    return-void
.end method


# virtual methods
.method public LA(I)I
    .locals 0

    .line 174
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->LT(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p1

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result p1

    return p1
.end method

.method protected LB(I)Lgroovyjarjarantlr4/runtime/Token;
    .locals 2

    .line 177
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    sub-int v1, v0, p1

    if-gez v1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 178
    :cond_0
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    sub-int/2addr v0, p1

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/runtime/Token;

    return-object p1
.end method

.method public LT(I)Lgroovyjarjarantlr4/runtime/Token;
    .locals 2

    .line 183
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->setup()V

    :cond_0
    if-nez p1, :cond_1

    const/4 p1, 0x0

    return-object p1

    :cond_1
    if-gez p1, :cond_2

    neg-int p1, p1

    .line 185
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->LB(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p1

    return-object p1

    .line 187
    :cond_2
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    add-int/2addr v0, p1

    add-int/lit8 v0, v0, -0x1

    .line 188
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->sync(I)V

    .line 189
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    if-lt v0, p1, :cond_3

    .line 191
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/runtime/Token;

    return-object p1

    .line 193
    :cond_3
    iget p1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->range:I

    if-le v0, p1, :cond_4

    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->range:I

    .line 194
    :cond_4
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/runtime/Token;

    return-object p1
.end method

.method public consume()V
    .locals 2

    .line 128
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->setup()V

    .line 129
    :cond_0
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    .line 130
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->sync(I)V

    return-void
.end method

.method protected fetch(I)V
    .locals 3

    const/4 v0, 0x1

    :goto_0
    if-gt v0, p1, :cond_1

    .line 143
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/runtime/TokenSource;

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/TokenSource;->nextToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v1

    .line 144
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    invoke-interface {v1, v2}, Lgroovyjarjarantlr4/runtime/Token;->setTokenIndex(I)V

    .line 146
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 147
    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    goto :goto_1

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    return-void
.end method

.method public fill()V
    .locals 3

    .line 279
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->setup()V

    .line 280
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    iget v2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v0

    if-ne v0, v1, :cond_1

    return-void

    .line 282
    :cond_1
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    add-int/lit8 v0, v0, 0x1

    .line 283
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->sync(I)V

    .line 284
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v2

    if-eq v2, v1, :cond_2

    add-int/lit8 v0, v0, 0x1

    .line 286
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->sync(I)V

    goto :goto_0

    :cond_2
    return-void
.end method

.method public get(I)Lgroovyjarjarantlr4/runtime/Token;
    .locals 3

    if-ltz p1, :cond_0

    .line 153
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_0

    .line 156
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/runtime/Token;

    return-object p1

    .line 154
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "token index "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " out of range 0.."

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/util/NoSuchElementException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public get(II)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation

    if-ltz p1, :cond_5

    if-gez p2, :cond_0

    goto :goto_2

    .line 162
    :cond_0
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->setup()V

    .line 163
    :cond_1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 164
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-lt p2, v2, :cond_2

    iget-object p2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    add-int/lit8 p2, p2, -0x1

    :cond_2
    :goto_0
    if-gt p1, p2, :cond_4

    .line 166
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/runtime/Token;

    .line 167
    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v3

    if-ne v3, v1, :cond_3

    goto :goto_1

    .line 168
    :cond_3
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_4
    :goto_1
    return-object v0

    :cond_5
    :goto_2
    const/4 p1, 0x0

    return-object p1
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 245
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/runtime/TokenSource;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/TokenSource;->getSourceName()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getTokenSource()Lgroovyjarjarantlr4/runtime/TokenSource;
    .locals 1

    .line 78
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/runtime/TokenSource;

    return-object v0
.end method

.method public getTokens()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 206
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    return-object v0
.end method

.method public getTokens(II)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 209
    move-object v1, v0

    check-cast v1, Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-virtual {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->getTokens(IILgroovyjarjarantlr4/runtime/BitSet;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public getTokens(III)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(III)",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 241
    invoke-static {p3}, Lgroovyjarjarantlr4/runtime/BitSet;->of(I)Lgroovyjarjarantlr4/runtime/BitSet;

    move-result-object p3

    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->getTokens(IILgroovyjarjarantlr4/runtime/BitSet;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public getTokens(IILgroovyjarjarantlr4/runtime/BitSet;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Lgroovyjarjarantlr4/runtime/BitSet;",
            ")",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 217
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->setup()V

    .line 218
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lt p2, v0, :cond_1

    iget-object p2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    add-int/lit8 p2, p2, -0x1

    :cond_1
    if-gez p1, :cond_2

    const/4 p1, 0x0

    :cond_2
    const/4 v0, 0x0

    if-le p1, p2, :cond_3

    return-object v0

    .line 223
    :cond_3
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    :goto_0
    if-gt p1, p2, :cond_6

    .line 225
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/runtime/Token;

    if-eqz p3, :cond_4

    .line 226
    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v3

    invoke-virtual {p3, v3}, Lgroovyjarjarantlr4/runtime/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_5

    .line 227
    :cond_4
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_5
    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 230
    :cond_6
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_7

    goto :goto_1

    :cond_7
    move-object v0, v1

    :goto_1
    return-object v0
.end method

.method public getTokens(IILjava/util/List;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;)",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation

    .line 237
    new-instance v0, Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-direct {v0, p3}, Lgroovyjarjarantlr4/runtime/BitSet;-><init>(Ljava/util/List;)V

    invoke-virtual {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->getTokens(IILgroovyjarjarantlr4/runtime/BitSet;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public index()I
    .locals 1

    .line 81
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    return v0
.end method

.method public mark()I
    .locals 2

    .line 88
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->setup()V

    .line 89
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->index()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->lastMarker:I

    return v0
.end method

.method public range()I
    .locals 1

    .line 84
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->range:I

    return v0
.end method

.method public release(I)V
    .locals 0

    return-void
.end method

.method public reset()V
    .locals 1

    const/4 v0, 0x0

    .line 109
    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    .line 110
    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->lastMarker:I

    return-void
.end method

.method public rewind()V
    .locals 1

    .line 105
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->lastMarker:I

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->seek(I)V

    return-void
.end method

.method public rewind(I)V
    .locals 0

    .line 100
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->seek(I)V

    return-void
.end method

.method public seek(I)V
    .locals 0

    .line 114
    iput p1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    return-void
.end method

.method public setTokenSource(Lgroovyjarjarantlr4/runtime/TokenSource;)V
    .locals 0

    .line 201
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokenSource:Lgroovyjarjarantlr4/runtime/TokenSource;

    .line 202
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->clear()V

    const/4 p1, -0x1

    .line 203
    iput p1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    return-void
.end method

.method protected setup()V
    .locals 1

    const/4 v0, 0x0

    .line 197
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->sync(I)V

    iput v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    return-void
.end method

.method public size()I
    .locals 1

    .line 117
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    return v0
.end method

.method protected sync(I)V
    .locals 1

    .line 135
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    sub-int/2addr p1, v0

    add-int/lit8 p1, p1, 0x1

    if-lez p1, :cond_0

    .line 137
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->fetch(I)V

    :cond_0
    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 250
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->setup()V

    .line 251
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->fill()V

    const/4 v0, 0x0

    .line 252
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->toString(II)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toString(II)Ljava/lang/String;
    .locals 4

    if-ltz p1, :cond_5

    if-gez p2, :cond_0

    goto :goto_2

    .line 258
    :cond_0
    iget v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->setup()V

    .line 259
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lt p2, v0, :cond_2

    iget-object p2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    add-int/lit8 p2, p2, -0x1

    .line 260
    :cond_2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    :goto_0
    if-gt p1, p2, :cond_4

    .line 262
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/runtime/Token;

    .line 263
    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v3

    if-ne v3, v1, :cond_3

    goto :goto_1

    .line 264
    :cond_3
    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 266
    :cond_4
    :goto_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_5
    :goto_2
    const/4 p1, 0x0

    return-object p1
.end method

.method public toString(Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;
    .locals 0

    if-eqz p1, :cond_0

    if-eqz p2, :cond_0

    .line 272
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getTokenIndex()I

    move-result p1

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/Token;->getTokenIndex()I

    move-result p2

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->toString(II)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_0
    const/4 p1, 0x0

    return-object p1
.end method
