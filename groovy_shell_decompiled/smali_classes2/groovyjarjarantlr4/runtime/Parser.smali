.class public Lgroovyjarjarantlr4/runtime/Parser;
.super Lgroovyjarjarantlr4/runtime/BaseRecognizer;
.source "Parser.java"


# instance fields
.field public input:Lgroovyjarjarantlr4/runtime/TokenStream;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenStream;)V
    .locals 0

    .line 37
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;-><init>()V

    .line 38
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/Parser;->setTokenStream(Lgroovyjarjarantlr4/runtime/TokenStream;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 0

    .line 42
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;-><init>(Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    .line 43
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/Parser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-void
.end method


# virtual methods
.method protected getCurrentInputSymbol(Lgroovyjarjarantlr4/runtime/IntStream;)Ljava/lang/Object;
    .locals 1

    .line 56
    check-cast p1, Lgroovyjarjarantlr4/runtime/TokenStream;

    const/4 v0, 0x1

    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p1

    return-object p1
.end method

.method protected getMissingSymbol(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/RecognitionException;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;
    .locals 1

    const/4 p2, -0x1

    if-ne p3, p2, :cond_0

    const-string p4, "<missing EOF>"

    goto :goto_0

    .line 67
    :cond_0
    new-instance p4, Ljava/lang/StringBuilder;

    invoke-direct {p4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "<missing "

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p4

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Parser;->getTokenNames()[Ljava/lang/String;

    move-result-object v0

    aget-object v0, v0, p3

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p4

    const-string v0, ">"

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p4

    invoke-virtual {p4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p4

    .line 68
    :goto_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-direct {v0, p3, p4}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    .line 69
    check-cast p1, Lgroovyjarjarantlr4/runtime/TokenStream;

    const/4 p3, 0x1

    invoke-interface {p1, p3}, Lgroovyjarjarantlr4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p3

    .line 70
    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result p4

    if-ne p4, p2, :cond_1

    .line 71
    invoke-interface {p1, p2}, Lgroovyjarjarantlr4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p3

    .line 73
    :cond_1
    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result p1

    iput p1, v0, Lgroovyjarjarantlr4/runtime/CommonToken;->line:I

    .line 74
    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result p1

    iput p1, v0, Lgroovyjarjarantlr4/runtime/CommonToken;->charPositionInLine:I

    const/4 p1, 0x0

    .line 75
    iput p1, v0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    .line 76
    invoke-interface {p3}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object p1

    iput-object p1, v0, Lgroovyjarjarantlr4/runtime/CommonToken;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    return-object v0
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 93
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Parser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/TokenStream;->getSourceName()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getTokenStream()Lgroovyjarjarantlr4/runtime/TokenStream;
    .locals 1

    .line 88
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Parser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-object v0
.end method

.method public reset()V
    .locals 2

    .line 48
    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->reset()V

    .line 49
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Parser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    if-eqz v0, :cond_0

    const/4 v1, 0x0

    .line 50
    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/TokenStream;->seek(I)V

    :cond_0
    return-void
.end method

.method public setTokenStream(Lgroovyjarjarantlr4/runtime/TokenStream;)V
    .locals 1

    const/4 v0, 0x0

    .line 82
    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/Parser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    .line 83
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/Parser;->reset()V

    .line 84
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/Parser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-void
.end method

.method public traceIn(Ljava/lang/String;I)V
    .locals 2

    .line 97
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Parser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    invoke-super {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->traceIn(Ljava/lang/String;ILjava/lang/Object;)V

    return-void
.end method

.method public traceOut(Ljava/lang/String;I)V
    .locals 2

    .line 101
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/Parser;->input:Lgroovyjarjarantlr4/runtime/TokenStream;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    invoke-super {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->traceOut(Ljava/lang/String;ILjava/lang/Object;)V

    return-void
.end method
