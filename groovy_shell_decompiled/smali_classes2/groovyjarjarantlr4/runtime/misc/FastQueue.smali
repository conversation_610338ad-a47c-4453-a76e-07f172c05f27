.class public Lgroovyjarjarantlr4/runtime/misc/FastQueue;
.super Ljava/lang/Object;
.source "FastQueue.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field protected data:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "TT;>;"
        }
    .end annotation
.end field

.field protected p:I

.field protected range:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 44
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 46
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->data:Ljava/util/List;

    const/4 v0, 0x0

    .line 48
    iput v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->p:I

    const/4 v0, -0x1

    .line 49
    iput v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->range:I

    return-void
.end method


# virtual methods
.method public add(Ljava/lang/Object;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)V"
        }
    .end annotation

    .line 66
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->data:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public clear()V
    .locals 1

    const/4 v0, 0x0

    .line 52
    iput v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->p:I

    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->data:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->clear()V

    return-void
.end method

.method public elementAt(I)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)TT;"
        }
    .end annotation

    .line 80
    iget v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->p:I

    add-int/2addr v0, p1

    .line 81
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->data:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    const-string v1, "queue index "

    if-ge v0, p1, :cond_2

    if-ltz v0, :cond_1

    .line 87
    iget p1, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->range:I

    if-le v0, p1, :cond_0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->range:I

    .line 88
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->data:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 85
    :cond_1
    new-instance p1, Ljava/util/NoSuchElementException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " < 0"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/util/NoSuchElementException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 82
    :cond_2
    new-instance p1, Ljava/util/NoSuchElementException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " > last index "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->data:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/util/NoSuchElementException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public head()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 72
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->elementAt(I)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public range()I
    .locals 1

    .line 70
    iget v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->range:I

    return v0
.end method

.method public remove()Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 56
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->elementAt(I)Ljava/lang/Object;

    move-result-object v0

    .line 57
    iget v1, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->p:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->p:I

    .line 59
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->data:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ne v1, v2, :cond_0

    .line 61
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->clear()V

    :cond_0
    return-object v0
.end method

.method public reset()V
    .locals 0

    .line 51
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->clear()V

    return-void
.end method

.method public size()I
    .locals 2

    .line 68
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->data:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->p:I

    sub-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 4

    .line 94
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 95
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->size()I

    move-result v1

    const/4 v2, 0x0

    :cond_0
    :goto_0
    if-ge v2, v1, :cond_1

    .line 97
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr4/runtime/misc/FastQueue;->elementAt(I)Ljava/lang/Object;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    add-int/lit8 v2, v2, 0x1

    if-ge v2, v1, :cond_0

    const-string v3, " "

    .line 98
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 100
    :cond_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
