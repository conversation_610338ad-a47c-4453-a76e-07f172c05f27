.class public Lgroovyjarjarantlr4/runtime/misc/IntArray;
.super Ljava/lang/Object;
.source "IntArray.java"


# static fields
.field public static final INITIAL_SIZE:I = 0xa


# instance fields
.field public data:[I

.field protected p:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 44
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 47
    iput v0, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->p:I

    return-void
.end method


# virtual methods
.method public add(I)V
    .locals 2

    .line 50
    iget v0, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->p:I

    add-int/lit8 v0, v0, 0x1

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/misc/IntArray;->ensureCapacity(I)V

    .line 51
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->data:[I

    iget v1, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->p:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->p:I

    aput p1, v0, v1

    return-void
.end method

.method public clear()V
    .locals 1

    const/4 v0, -0x1

    .line 70
    iput v0, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->p:I

    return-void
.end method

.method public ensureCapacity(I)V
    .locals 3

    .line 74
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->data:[I

    if-nez v0, :cond_0

    const/16 p1, 0xa

    new-array p1, p1, [I

    .line 75
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->data:[I

    goto :goto_1

    :cond_0
    add-int/lit8 v1, p1, 0x1

    .line 77
    array-length v2, v0

    if-lt v1, v2, :cond_2

    .line 78
    array-length v2, v0

    mul-int/lit8 v2, v2, 0x2

    if-le p1, v2, :cond_1

    goto :goto_0

    :cond_1
    move v1, v2

    .line 82
    :goto_0
    new-array p1, v1, [I

    .line 83
    array-length v1, v0

    const/4 v2, 0x0

    invoke-static {v0, v2, p1, v2, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 84
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->data:[I

    :cond_2
    :goto_1
    return-void
.end method

.method public pop()I
    .locals 2

    .line 59
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->data:[I

    iget v1, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->p:I

    aget v0, v0, v1

    add-int/lit8 v1, v1, -0x1

    .line 60
    iput v1, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->p:I

    return v0
.end method

.method public push(I)V
    .locals 0

    .line 55
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/misc/IntArray;->add(I)V

    return-void
.end method

.method public size()I
    .locals 1

    .line 66
    iget v0, p0, Lgroovyjarjarantlr4/runtime/misc/IntArray;->p:I

    return v0
.end method
