.class public abstract Lgroovyjarjarantlr4/runtime/SerializedGrammar$Node;
.super Ljava/lang/Object;
.source "SerializedGrammar.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/SerializedGrammar;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x404
    name = "Node"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/SerializedGrammar;


# direct methods
.method protected constructor <init>(Lgroovyjarjarantlr4/runtime/SerializedGrammar;)V
    .locals 0

    .line 60
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Node;->this$0:Lgroovyjarjarantlr4/runtime/SerializedGrammar;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract toString()Ljava/lang/String;
.end method
