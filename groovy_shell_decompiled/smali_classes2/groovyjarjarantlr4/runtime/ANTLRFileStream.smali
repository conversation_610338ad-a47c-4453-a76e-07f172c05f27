.class public Lgroovyjarjarantlr4/runtime/ANTLRFileStream;
.super Lgroovyjarjarantlr4/runtime/ANTLRStringStream;
.source "ANTLRFileStream.java"


# instance fields
.field protected fileName:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 42
    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 45
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;-><init>()V

    .line 46
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;->fileName:Ljava/lang/String;

    .line 47
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;->load(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method


# virtual methods
.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 77
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;->fileName:Ljava/lang/String;

    return-object v0
.end method

.method public load(Ljava/lang/String;Ljava/lang/String;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    if-nez p1, :cond_0

    return-void

    .line 56
    :cond_0
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 57
    invoke-virtual {v0}, Ljava/io/File;->length()J

    move-result-wide v0

    long-to-int v0, v0

    .line 59
    new-instance v1, Ljava/io/FileInputStream;

    invoke-direct {v1, p1}, Ljava/io/FileInputStream;-><init>(Ljava/lang/String;)V

    if-eqz p2, :cond_1

    .line 61
    new-instance p1, Ljava/io/InputStreamReader;

    invoke-direct {p1, v1, p2}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;Ljava/lang/String;)V

    goto :goto_0

    .line 64
    :cond_1
    new-instance p1, Ljava/io/InputStreamReader;

    invoke-direct {p1, v1}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    .line 67
    :goto_0
    :try_start_0
    new-array p2, v0, [C

    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;->data:[C

    .line 68
    iget-object p2, p0, Lgroovyjarjarantlr4/runtime/ANTLRFileStream;->data:[C

    invoke-virtual {p1, p2}, Ljava/io/InputStreamReader;->read([C)I

    move-result p2

    iput p2, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->n:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 71
    invoke-virtual {p1}, Ljava/io/InputStreamReader;->close()V

    return-void

    :catchall_0
    move-exception p2

    invoke-virtual {p1}, Ljava/io/InputStreamReader;->close()V

    throw p2
.end method
