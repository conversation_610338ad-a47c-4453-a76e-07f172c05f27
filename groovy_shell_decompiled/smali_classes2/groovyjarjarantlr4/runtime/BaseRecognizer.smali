.class public abstract Lgroovyjarjarantlr4/runtime/BaseRecognizer;
.super Ljava/lang/Object;
.source "BaseRecognizer.java"


# static fields
.field public static final DEFAULT_TOKEN_CHANNEL:I = 0x0

.field public static final HIDDEN:I = 0x63

.field public static final INITIAL_FOLLOW_STACK_SIZE:I = 0x64

.field public static final MEMO_RULE_FAILED:I = -0x2

.field public static final MEMO_RULE_UNKNOWN:I = -0x1

.field public static final NEXT_TOKEN_RULE_NAME:Ljava/lang/String; = "nextToken"


# instance fields
.field protected state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 59
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 60
    new-instance v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    invoke-direct {v0}, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 0

    .line 63
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    if-nez p1, :cond_0

    .line 65
    new-instance p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    invoke-direct {p1}, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;-><init>()V

    .line 67
    :cond_0
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    return-void
.end method

.method public static getRuleInvocationStack(Ljava/lang/Throwable;Ljava/lang/String;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Throwable;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 728
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 729
    invoke-virtual {p0}, Ljava/lang/Throwable;->getStackTrace()[Ljava/lang/StackTraceElement;

    move-result-object p0

    .line 731
    array-length v1, p0

    add-int/lit8 v1, v1, -0x1

    :goto_0
    if-ltz v1, :cond_3

    .line 732
    aget-object v2, p0, v1

    .line 733
    invoke-virtual {v2}, Ljava/lang/StackTraceElement;->getClassName()Ljava/lang/String;

    move-result-object v3

    const-string v4, "groovyjarjarantlr4.runtime."

    invoke-virtual {v3, v4}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_1

    .line 736
    :cond_0
    invoke-virtual {v2}, Ljava/lang/StackTraceElement;->getMethodName()Ljava/lang/String;

    move-result-object v3

    const-string v4, "nextToken"

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    goto :goto_1

    .line 739
    :cond_1
    invoke-virtual {v2}, Ljava/lang/StackTraceElement;->getClassName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v3, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_2

    goto :goto_1

    .line 742
    :cond_2
    invoke-virtual {v2}, Ljava/lang/StackTraceElement;->getMethodName()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_1
    add-int/lit8 v1, v1, -0x1

    goto :goto_0

    :cond_3
    return-object v0
.end method


# virtual methods
.method public alreadyParsedRule(Lgroovyjarjarantlr4/runtime/IntStream;I)Z
    .locals 2

    .line 815
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->index()I

    move-result v0

    invoke-virtual {p0, p2, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getRuleMemoization(II)I

    move-result p2

    const/4 v0, -0x1

    if-ne p2, v0, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    const/4 v0, -0x2

    const/4 v1, 0x1

    if-ne p2, v0, :cond_1

    .line 821
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    goto :goto_0

    :cond_1
    add-int/2addr p2, v1

    .line 825
    invoke-interface {p1, p2}, Lgroovyjarjarantlr4/runtime/IntStream;->seek(I)V

    :goto_0
    return v1
.end method

.method public beginResync()V
    .locals 0

    return-void
.end method

.method protected combineFollows(Z)Lgroovyjarjarantlr4/runtime/BitSet;
    .locals 4

    .line 533
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    .line 534
    new-instance v1, Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-direct {v1}, Lgroovyjarjarantlr4/runtime/BitSet;-><init>()V

    :goto_0
    if-ltz v0, :cond_1

    .line 536
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v2, v2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    aget-object v2, v2, v0

    .line 541
    invoke-virtual {v1, v2}, Lgroovyjarjarantlr4/runtime/BitSet;->orInPlace(Lgroovyjarjarantlr4/runtime/BitSet;)V

    if-eqz p1, :cond_0

    const/4 v3, 0x1

    .line 544
    invoke-virtual {v2, v3}, Lgroovyjarjarantlr4/runtime/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_1

    if-lez v0, :cond_0

    .line 548
    invoke-virtual {v1, v3}, Lgroovyjarjarantlr4/runtime/BitSet;->remove(I)V

    :cond_0
    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_1
    return-object v1
.end method

.method protected computeContextSensitiveRuleFOLLOW()Lgroovyjarjarantlr4/runtime/BitSet;
    .locals 1

    const/4 v0, 0x1

    .line 525
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->combineFollows(Z)Lgroovyjarjarantlr4/runtime/BitSet;

    move-result-object v0

    return-object v0
.end method

.method protected computeErrorRecoverySet()Lgroovyjarjarantlr4/runtime/BitSet;
    .locals 1

    const/4 v0, 0x0

    .line 469
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->combineFollows(Z)Lgroovyjarjarantlr4/runtime/BitSet;

    move-result-object v0

    return-object v0
.end method

.method public consumeUntil(Lgroovyjarjarantlr4/runtime/IntStream;I)V
    .locals 3

    const/4 v0, 0x1

    .line 677
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result v1

    :goto_0
    const/4 v2, -0x1

    if-eq v1, v2, :cond_0

    if-eq v1, p2, :cond_0

    .line 679
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->consume()V

    .line 680
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result v1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public consumeUntil(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/BitSet;)V
    .locals 3

    const/4 v0, 0x1

    .line 687
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result v1

    :goto_0
    const/4 v2, -0x1

    if-eq v1, v2, :cond_0

    .line 688
    invoke-virtual {p2, v1}, Lgroovyjarjarantlr4/runtime/BitSet;->member(I)Z

    move-result v1

    if-nez v1, :cond_0

    .line 690
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->consume()V

    .line 691
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result v1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public displayRecognitionError([Ljava/lang/String;Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 1

    .line 192
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getErrorHeader(Lgroovyjarjarantlr4/runtime/RecognitionException;)Ljava/lang/String;

    move-result-object v0

    .line 193
    invoke-virtual {p0, p2, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getErrorMessage(Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 194
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->emitErrorMessage(Ljava/lang/String;)V

    return-void
.end method

.method public emitErrorMessage(Ljava/lang/String;)V
    .locals 1

    .line 344
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method

.method public endResync()V
    .locals 0

    return-void
.end method

.method public failed()Z
    .locals 1

    .line 752
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-boolean v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    return v0
.end method

.method public getBacktrackingLevel()I
    .locals 1

    .line 747
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    return v0
.end method

.method protected getCurrentInputSymbol(Lgroovyjarjarantlr4/runtime/IntStream;)Ljava/lang/Object;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public getErrorHeader(Lgroovyjarjarantlr4/runtime/RecognitionException;)Ljava/lang/String;
    .locals 3

    .line 312
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getSourceName()Ljava/lang/String;

    move-result-object v0

    const-string v1, ":"

    if-eqz v0, :cond_0

    .line 313
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getSourceName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " line "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v2, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->line:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->charPositionInLine:I

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 315
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "line "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v2, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->line:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->charPositionInLine:I

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getErrorMessage(Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/String;)Ljava/lang/String;
    .locals 6

    .line 220
    invoke-virtual {p1}, Lgroovyjarjarantlr4/runtime/RecognitionException;->getMessage()Ljava/lang/String;

    move-result-object v0

    .line 221
    instance-of v1, p1, Lgroovyjarjarantlr4/runtime/UnwantedTokenException;

    const-string v2, " expecting "

    const-string v3, "EOF"

    const/4 v4, -0x1

    if-eqz v1, :cond_1

    .line 222
    check-cast p1, Lgroovyjarjarantlr4/runtime/UnwantedTokenException;

    .line 224
    iget v0, p1, Lgroovyjarjarantlr4/runtime/UnwantedTokenException;->expecting:I

    if-ne v0, v4, :cond_0

    goto :goto_0

    .line 228
    :cond_0
    iget v0, p1, Lgroovyjarjarantlr4/runtime/UnwantedTokenException;->expecting:I

    aget-object v3, p2, v0

    .line 230
    :goto_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "extraneous input "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p1}, Lgroovyjarjarantlr4/runtime/UnwantedTokenException;->getUnexpectedToken()Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getTokenErrorDisplay(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_4

    .line 233
    :cond_1
    instance-of v1, p1, Lgroovyjarjarantlr4/runtime/MissingTokenException;

    if-eqz v1, :cond_3

    .line 234
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/runtime/MissingTokenException;

    .line 236
    iget v1, v0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->expecting:I

    if-ne v1, v4, :cond_2

    goto :goto_1

    .line 240
    :cond_2
    iget v0, v0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->expecting:I

    aget-object v3, p2, v0

    .line 242
    :goto_1
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "missing "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " at "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getTokenErrorDisplay(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_4

    .line 244
    :cond_3
    instance-of v1, p1, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;

    const-string v5, "mismatched input "

    if-eqz v1, :cond_5

    .line 245
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;

    .line 247
    iget v1, v0, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;->expecting:I

    if-ne v1, v4, :cond_4

    goto :goto_2

    .line 251
    :cond_4
    iget v0, v0, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;->expecting:I

    aget-object v3, p2, v0

    .line 253
    :goto_2
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getTokenErrorDisplay(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_4

    .line 256
    :cond_5
    instance-of v1, p1, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;

    if-eqz v1, :cond_7

    .line 257
    check-cast p1, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;

    .line 259
    iget v0, p1, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;->expecting:I

    if-ne v0, v4, :cond_6

    goto :goto_3

    .line 263
    :cond_6
    iget v0, p1, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;->expecting:I

    aget-object v3, p2, v0

    .line 265
    :goto_3
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "mismatched tree node: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;->node:Ljava/lang/Object;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_4

    .line 268
    :cond_7
    instance-of p2, p1, Lgroovyjarjarantlr4/runtime/NoViableAltException;

    if-eqz p2, :cond_8

    .line 273
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "no viable alternative at input "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getTokenErrorDisplay(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_4

    .line 275
    :cond_8
    instance-of p2, p1, Lgroovyjarjarantlr4/runtime/EarlyExitException;

    if-eqz p2, :cond_9

    .line 278
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "required (...)+ loop did not match anything at input "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getTokenErrorDisplay(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto/16 :goto_4

    .line 281
    :cond_9
    instance-of p2, p1, Lgroovyjarjarantlr4/runtime/MismatchedSetException;

    const-string v1, " expecting set "

    if-eqz p2, :cond_a

    .line 282
    move-object p2, p1

    check-cast p2, Lgroovyjarjarantlr4/runtime/MismatchedSetException;

    .line 283
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getTokenErrorDisplay(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object p2, p2, Lgroovyjarjarantlr4/runtime/MismatchedSetException;->expecting:Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_4

    .line 286
    :cond_a
    instance-of p2, p1, Lgroovyjarjarantlr4/runtime/MismatchedNotSetException;

    if-eqz p2, :cond_b

    .line 287
    move-object p2, p1

    check-cast p2, Lgroovyjarjarantlr4/runtime/MismatchedNotSetException;

    .line 288
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getTokenErrorDisplay(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object p2, p2, Lgroovyjarjarantlr4/runtime/MismatchedNotSetException;->expecting:Lgroovyjarjarantlr4/runtime/BitSet;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_4

    .line 291
    :cond_b
    instance-of p2, p1, Lgroovyjarjarantlr4/runtime/FailedPredicateException;

    if-eqz p2, :cond_c

    .line 292
    check-cast p1, Lgroovyjarjarantlr4/runtime/FailedPredicateException;

    .line 293
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "rule "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object v0, p1, Lgroovyjarjarantlr4/runtime/FailedPredicateException;->ruleName:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " failed predicate: {"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/FailedPredicateException;->predicateText:Ljava/lang/String;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "}?"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    :cond_c
    :goto_4
    return-object v0
.end method

.method public getGrammarFileName()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method protected getMissingSymbol(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/RecognitionException;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public getNumberOfSyntaxErrors()I
    .locals 1

    .line 307
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->syntaxErrors:I

    return v0
.end method

.method public getRuleInvocationStack()Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 714
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    .line 715
    new-instance v1, Ljava/lang/Throwable;

    invoke-direct {v1}, Ljava/lang/Throwable;-><init>()V

    invoke-static {v1, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getRuleInvocationStack(Ljava/lang/Throwable;Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getRuleMemoization(II)I
    .locals 2

    .line 794
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    aget-object v0, v0, p1

    if-nez v0, :cond_0

    .line 795
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    aput-object v1, v0, p1

    .line 797
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    aget-object p1, v0, p1

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    if-nez p1, :cond_1

    const/4 p1, -0x1

    return p1

    .line 802
    :cond_1
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    return p1
.end method

.method public getRuleMemoizationCacheSize()I
    .locals 3

    const/4 v0, 0x0

    move v1, v0

    .line 854
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v2, v2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    if-eqz v2, :cond_1

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v2, v2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    array-length v2, v2

    if-ge v0, v2, :cond_1

    .line 855
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v2, v2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    aget-object v2, v2, v0

    if-eqz v2, :cond_0

    .line 857
    invoke-interface {v2}, Ljava/util/Map;->size()I

    move-result v2

    add-int/2addr v1, v2

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return v1
.end method

.method public abstract getSourceName()Ljava/lang/String;
.end method

.method public getTokenErrorDisplay(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;
    .locals 2

    .line 327
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_1

    .line 329
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    const-string v0, "<EOF>"

    goto :goto_0

    .line 333
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "<"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ">"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    :cond_1
    :goto_0
    const-string p1, "\n"

    const-string v1, "\\\\n"

    .line 336
    invoke-virtual {v0, p1, v1}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "\r"

    const-string v1, "\\\\r"

    .line 337
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "\t"

    const-string v1, "\\\\t"

    .line 338
    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 339
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "\'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getTokenNames()[Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public match(Lgroovyjarjarantlr4/runtime/IntStream;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 104
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getCurrentInputSymbol(Lgroovyjarjarantlr4/runtime/IntStream;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x1

    .line 105
    invoke-interface {p1, v1}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result v2

    if-ne v2, p2, :cond_0

    .line 106
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->consume()V

    .line 107
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 p2, 0x0

    iput-boolean p2, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    .line 108
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean p2, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    return-object v0

    .line 111
    :cond_0
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v2, v2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    if-lez v2, :cond_1

    .line 112
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    return-object v0

    .line 115
    :cond_1
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->recoverFromMismatchedToken(Lgroovyjarjarantlr4/runtime/IntStream;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public matchAny(Lgroovyjarjarantlr4/runtime/IntStream;)V
    .locals 2

    .line 121
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 v1, 0x0

    iput-boolean v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    .line 122
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    .line 123
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->consume()V

    return-void
.end method

.method public memoize(Lgroovyjarjarantlr4/runtime/IntStream;II)V
    .locals 3

    .line 837
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-boolean v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    if-eqz v0, :cond_0

    const/4 p1, -0x2

    goto :goto_0

    :cond_0
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->index()I

    move-result p1

    add-int/lit8 p1, p1, -0x1

    .line 838
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    if-nez v0, :cond_1

    .line 839
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "!!!!!!!!! memo array is null for "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getGrammarFileName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 841
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    array-length v0, v0

    if-lt p2, v0, :cond_2

    .line 842
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "!!!!!!!!! memo size is "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v2, v2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    array-length v2, v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ", but rule index is "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 844
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    aget-object v0, v0, p2

    if-eqz v0, :cond_3

    .line 845
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    aget-object p2, v0, p2

    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {p2, p3, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_3
    return-void
.end method

.method public mismatchIsMissingToken(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/BitSet;)Z
    .locals 3

    const/4 v0, 0x0

    if-nez p2, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x1

    .line 137
    invoke-virtual {p2, v1}, Lgroovyjarjarantlr4/runtime/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 138
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->computeContextSensitiveRuleFOLLOW()Lgroovyjarjarantlr4/runtime/BitSet;

    move-result-object v2

    .line 139
    invoke-virtual {p2, v2}, Lgroovyjarjarantlr4/runtime/BitSet;->or(Lgroovyjarjarantlr4/runtime/BitSet;)Lgroovyjarjarantlr4/runtime/BitSet;

    move-result-object p2

    .line 140
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v2, v2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    if-ltz v2, :cond_1

    .line 141
    invoke-virtual {p2, v1}, Lgroovyjarjarantlr4/runtime/BitSet;->remove(I)V

    .line 154
    :cond_1
    invoke-interface {p1, v1}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result p1

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr4/runtime/BitSet;->member(I)Z

    move-result p1

    if-nez p1, :cond_3

    invoke-virtual {p2, v1}, Lgroovyjarjarantlr4/runtime/BitSet;->member(I)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    return v0

    :cond_3
    :goto_0
    return v1
.end method

.method public mismatchIsUnwantedToken(Lgroovyjarjarantlr4/runtime/IntStream;I)Z
    .locals 1

    const/4 v0, 0x2

    .line 127
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result p1

    if-ne p1, p2, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method protected pushFollow(Lgroovyjarjarantlr4/runtime/BitSet;)V
    .locals 4

    .line 697
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    add-int/lit8 v0, v0, 0x1

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v1, v1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    array-length v1, v1

    if-lt v0, v1, :cond_0

    .line 698
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    array-length v0, v0

    mul-int/lit8 v0, v0, 0x2

    new-array v0, v0, [Lgroovyjarjarantlr4/runtime/BitSet;

    .line 699
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v1, v1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v2, v2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    array-length v2, v2

    const/4 v3, 0x0

    invoke-static {v1, v3, v0, v3, v2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 700
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-object v0, v1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    .line 702
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v2, v1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    add-int/lit8 v2, v2, 0x1

    iput v2, v1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    aput-object p1, v0, v2

    return-void
.end method

.method public recover(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 1

    .line 354
    iget-object p2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget p2, p2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->lastErrorIndex:I

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->index()I

    move-result v0

    if-ne p2, v0, :cond_0

    .line 359
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->consume()V

    .line 361
    :cond_0
    iget-object p2, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->index()I

    move-result v0

    iput v0, p2, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->lastErrorIndex:I

    .line 362
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->computeErrorRecoverySet()Lgroovyjarjarantlr4/runtime/BitSet;

    move-result-object p2

    .line 363
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->beginResync()V

    .line 364
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->consumeUntil(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/BitSet;)V

    .line 365
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->endResync()V

    return-void
.end method

.method public recoverFromMismatchedSet(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/RecognitionException;Lgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 627
    invoke-virtual {p0, p1, p3}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->mismatchIsMissingToken(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/BitSet;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 629
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    const/4 v0, 0x0

    .line 631
    invoke-virtual {p0, p1, p2, v0, p3}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getMissingSymbol(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/RecognitionException;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 634
    :cond_0
    throw p2
.end method

.method protected recoverFromMismatchedToken(Lgroovyjarjarantlr4/runtime/IntStream;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 593
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->mismatchIsUnwantedToken(Lgroovyjarjarantlr4/runtime/IntStream;I)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 594
    new-instance p3, Lgroovyjarjarantlr4/runtime/UnwantedTokenException;

    invoke-direct {p3, p2, p1}, Lgroovyjarjarantlr4/runtime/UnwantedTokenException;-><init>(ILgroovyjarjarantlr4/runtime/IntStream;)V

    .line 600
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->beginResync()V

    .line 601
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->consume()V

    .line 602
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->endResync()V

    .line 603
    invoke-virtual {p0, p3}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    .line 605
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getCurrentInputSymbol(Lgroovyjarjarantlr4/runtime/IntStream;)Ljava/lang/Object;

    move-result-object p2

    .line 606
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->consume()V

    return-object p2

    .line 610
    :cond_0
    invoke-virtual {p0, p1, p3}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->mismatchIsMissingToken(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/BitSet;)Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v0, 0x0

    .line 611
    invoke-virtual {p0, p1, v0, p2, p3}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getMissingSymbol(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/RecognitionException;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;

    move-result-object p3

    .line 612
    new-instance v0, Lgroovyjarjarantlr4/runtime/MissingTokenException;

    invoke-direct {v0, p2, p1, p3}, Lgroovyjarjarantlr4/runtime/MissingTokenException;-><init>(ILgroovyjarjarantlr4/runtime/IntStream;Ljava/lang/Object;)V

    .line 613
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    return-object p3

    .line 617
    :cond_1
    new-instance p3, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;

    invoke-direct {p3, p2, p1}, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;-><init>(ILgroovyjarjarantlr4/runtime/IntStream;)V

    .line 618
    throw p3
.end method

.method public reportError(Lgroovyjarjarantlr4/runtime/RecognitionException;)V
    .locals 3

    .line 179
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-boolean v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    if-eqz v0, :cond_0

    return-void

    .line 183
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->syntaxErrors:I

    const/4 v2, 0x1

    add-int/2addr v1, v2

    iput v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->syntaxErrors:I

    .line 184
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    .line 186
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getTokenNames()[Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->displayRecognitionError([Ljava/lang/String;Lgroovyjarjarantlr4/runtime/RecognitionException;)V

    return-void
.end method

.method public reset()V
    .locals 3

    .line 73
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v1, -0x1

    .line 76
    iput v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    .line 77
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 v2, 0x0

    iput-boolean v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    .line 78
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput v1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->lastErrorIndex:I

    .line 79
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    .line 80
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->syntaxErrors:I

    .line 82
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput v2, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    .line 83
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    if-eqz v0, :cond_1

    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    array-length v0, v0

    if-ge v2, v0, :cond_1

    .line 84
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    const/4 v1, 0x0

    aput-object v1, v0, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public setBacktrackingLevel(I)V
    .locals 1

    .line 749
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput p1, v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    return-void
.end method

.method public toStrings(Ljava/util/List;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;)",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 776
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    const/4 v1, 0x0

    .line 777
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_1

    .line 778
    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method public traceIn(Ljava/lang/String;ILjava/lang/Object;)V
    .locals 2

    .line 864
    sget-object p2, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "enter "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    .line 865
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    if-lez p1, :cond_0

    .line 866
    sget-object p1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, " backtracking="

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p3, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget p3, p3, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    .line 868
    :cond_0
    sget-object p1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {p1}, Ljava/io/PrintStream;->println()V

    return-void
.end method

.method public traceOut(Ljava/lang/String;ILjava/lang/Object;)V
    .locals 2

    .line 875
    sget-object p2, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "exit "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    .line 876
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    if-lez p1, :cond_1

    .line 877
    sget-object p1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, " backtracking="

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object p3, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget p3, p3, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    .line 878
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iget-boolean p1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    if-eqz p1, :cond_0

    sget-object p1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string p2, " failed"

    invoke-virtual {p1, p2}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    goto :goto_0

    .line 879
    :cond_0
    sget-object p1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string p2, " succeeded"

    invoke-virtual {p1, p2}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    .line 881
    :cond_1
    :goto_0
    sget-object p1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {p1}, Ljava/io/PrintStream;->println()V

    return-void
.end method
