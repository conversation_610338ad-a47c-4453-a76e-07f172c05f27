.class public Lgroovyjarjarantlr4/runtime/SerializedGrammar$TokenRef;
.super Lgroovyjarjarantlr4/runtime/SerializedGrammar$Node;
.source "SerializedGrammar.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/SerializedGrammar;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "TokenRef"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/SerializedGrammar;

.field ttype:I


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/SerializedGrammar;I)V
    .locals 0

    .line 86
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar$TokenRef;->this$0:Lgroovyjarjarantlr4/runtime/SerializedGrammar;

    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Node;-><init>(Lgroovyjarjarantlr4/runtime/SerializedGrammar;)V

    iput p2, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar$TokenRef;->ttype:I

    return-void
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 1

    .line 88
    iget v0, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar$TokenRef;->ttype:I

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
