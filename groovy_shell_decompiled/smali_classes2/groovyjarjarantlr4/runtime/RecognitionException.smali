.class public Lgroovyjarjarantlr4/runtime/RecognitionException;
.super Ljava/lang/Exception;
.source "RecognitionException.java"


# instance fields
.field public approximateLineInfo:Z

.field public c:I

.field public charPositionInLine:I

.field public index:I

.field public transient input:Lgroovyjarjarantlr4/runtime/IntStream;

.field public line:I

.field public node:Ljava/lang/Object;

.field public token:Lgroovyjarjarantlr4/runtime/Token;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 99
    invoke-direct {p0}, Ljava/lang/Exception;-><init>()V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/IntStream;)V
    .locals 2

    .line 102
    invoke-direct {p0}, Ljava/lang/Exception;-><init>()V

    .line 103
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->input:Lgroovyjarjarantlr4/runtime/IntStream;

    .line 104
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/IntStream;->index()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->index:I

    .line 105
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/TokenStream;

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    .line 106
    move-object v0, p1

    check-cast v0, Lgroovyjarjarantlr4/runtime/TokenStream;

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/TokenStream;->LT(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 107
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->line:I

    .line 108
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->charPositionInLine:I

    .line 110
    :cond_0
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    if-eqz v0, :cond_1

    .line 111
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/RecognitionException;->extractInformationFromTreeNodeStream(Lgroovyjarjarantlr4/runtime/IntStream;)V

    goto :goto_0

    .line 113
    :cond_1
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/CharStream;

    if-eqz v0, :cond_2

    .line 114
    invoke-interface {p1, v1}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->c:I

    .line 115
    check-cast p1, Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/CharStream;->getLine()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->line:I

    .line 116
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/CharStream;->getCharPositionInLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->charPositionInLine:I

    goto :goto_0

    .line 119
    :cond_2
    invoke-interface {p1, v1}, Lgroovyjarjarantlr4/runtime/IntStream;->LA(I)I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->c:I

    :goto_0
    return-void
.end method


# virtual methods
.method protected extractInformationFromTreeNodeStream(Lgroovyjarjarantlr4/runtime/IntStream;)V
    .locals 7

    .line 124
    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    const/4 v0, 0x1

    .line 126
    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object v1

    iput-object v1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    .line 129
    instance-of v1, p1, Lgroovyjarjarantlr4/runtime/tree/PositionTrackingStream;

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    .line 130
    move-object v1, p1

    check-cast v1, Lgroovyjarjarantlr4/runtime/tree/PositionTrackingStream;

    const/4 v3, 0x0

    invoke-interface {v1, v3}, Lgroovyjarjarantlr4/runtime/tree/PositionTrackingStream;->getKnownPositionElement(Z)Ljava/lang/Object;

    move-result-object v4

    if-nez v4, :cond_2

    .line 132
    invoke-interface {v1, v0}, Lgroovyjarjarantlr4/runtime/tree/PositionTrackingStream;->getKnownPositionElement(Z)Ljava/lang/Object;

    move-result-object v4

    if-eqz v4, :cond_0

    move v3, v0

    .line 133
    :cond_0
    iput-boolean v3, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->approximateLineInfo:Z

    goto :goto_0

    :cond_1
    move-object v4, v2

    .line 137
    :cond_2
    :goto_0
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object v1

    if-eqz v4, :cond_3

    goto :goto_1

    .line 138
    :cond_3
    iget-object v4, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    :goto_1
    invoke-interface {v1, v4}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getToken(Ljava/lang/Object;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v3

    if-eqz v3, :cond_6

    .line 140
    iput-object v3, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 141
    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result v4

    if-gtz v4, :cond_5

    const/4 v3, -0x1

    .line 144
    invoke-interface {p1, v3}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object v4

    move v5, v3

    :goto_2
    if-eqz v4, :cond_8

    .line 146
    invoke-interface {v1, v4}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getToken(Ljava/lang/Object;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v4

    if-eqz v4, :cond_4

    .line 147
    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result v6

    if-lez v6, :cond_4

    .line 149
    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->line:I

    .line 150
    invoke-interface {v4}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->charPositionInLine:I

    .line 151
    iput-boolean v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->approximateLineInfo:Z

    goto :goto_3

    :cond_4
    add-int/2addr v5, v3

    .line 157
    :try_start_0
    invoke-interface {p1, v5}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object v4
    :try_end_0
    .catch Ljava/lang/UnsupportedOperationException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    :catch_0
    move-object v4, v2

    goto :goto_2

    .line 164
    :cond_5
    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->line:I

    .line 165
    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->charPositionInLine:I

    goto :goto_3

    .line 168
    :cond_6
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/tree/Tree;

    if-eqz v0, :cond_7

    .line 169
    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/Tree;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->line:I

    .line 170
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/Tree;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->getCharPositionInLine()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->charPositionInLine:I

    .line 171
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    if-eqz v0, :cond_8

    .line 172
    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/CommonTree;

    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/tree/CommonTree;->token:Lgroovyjarjarantlr4/runtime/Token;

    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    goto :goto_3

    .line 176
    :cond_7
    invoke-interface {v1, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getType(Ljava/lang/Object;)I

    move-result p1

    .line 177
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    invoke-interface {v1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getText(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    .line 178
    new-instance v1, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-direct {v1, p1, v0}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    iput-object v1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    :cond_8
    :goto_3
    return-void
.end method

.method public getUnexpectedType()I
    .locals 2

    .line 184
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->input:Lgroovyjarjarantlr4/runtime/IntStream;

    instance-of v1, v0, Lgroovyjarjarantlr4/runtime/TokenStream;

    if-eqz v1, :cond_0

    .line 185
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v0

    return v0

    .line 187
    :cond_0
    instance-of v1, v0, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    if-eqz v1, :cond_1

    .line 188
    check-cast v0, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    .line 189
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object v0

    .line 190
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getType(Ljava/lang/Object;)I

    move-result v0

    return v0

    .line 193
    :cond_1
    iget v0, p0, Lgroovyjarjarantlr4/runtime/RecognitionException;->c:I

    return v0
.end method
