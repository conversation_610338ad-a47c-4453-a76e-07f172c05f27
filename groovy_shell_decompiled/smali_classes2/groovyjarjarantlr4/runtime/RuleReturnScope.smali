.class public Lgroovyjarjarantlr4/runtime/RuleReturnScope;
.super Ljava/lang/Object;
.source "RuleReturnScope.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 31
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getStart()Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getStop()Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getTemplate()Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getTree()Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
