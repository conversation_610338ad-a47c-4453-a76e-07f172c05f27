.class public Lgroovyjarjarantlr4/runtime/RecognizerSharedState;
.super Ljava/lang/Object;
.source "RecognizerSharedState.java"


# instance fields
.field public _fsp:I

.field public backtracking:I

.field public channel:I

.field public errorRecovery:Z

.field public failed:Z

.field public following:[Lgroovyjarjarantlr4/runtime/BitSet;

.field public lastErrorIndex:I

.field public ruleMemo:[Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public syntaxErrors:I

.field public text:Ljava/lang/String;

.field public token:Lgroovyjarjarantlr4/runtime/Token;

.field public tokenStartCharIndex:I

.field public tokenStartCharPositionInLine:I

.field public tokenStartLine:I

.field public type:I


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 121
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x64

    new-array v0, v0, [Lgroovyjarjarantlr4/runtime/BitSet;

    .line 44
    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    const/4 v0, -0x1

    .line 45
    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    const/4 v1, 0x0

    .line 51
    iput-boolean v1, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    .line 59
    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->lastErrorIndex:I

    .line 64
    iput-boolean v1, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    .line 67
    iput v1, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->syntaxErrors:I

    .line 72
    iput v1, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    .line 102
    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharIndex:I

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 4

    .line 124
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x64

    new-array v0, v0, [Lgroovyjarjarantlr4/runtime/BitSet;

    .line 44
    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    const/4 v1, -0x1

    .line 45
    iput v1, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    const/4 v2, 0x0

    .line 51
    iput-boolean v2, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    .line 59
    iput v1, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->lastErrorIndex:I

    .line 64
    iput-boolean v2, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    .line 67
    iput v2, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->syntaxErrors:I

    .line 72
    iput v2, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    .line 102
    iput v1, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharIndex:I

    .line 125
    array-length v0, v0

    iget-object v1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    array-length v3, v1

    if-ge v0, v3, :cond_0

    .line 126
    array-length v0, v1

    new-array v0, v0, [Lgroovyjarjarantlr4/runtime/BitSet;

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    .line 128
    :cond_0
    iget-object v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->following:[Lgroovyjarjarantlr4/runtime/BitSet;

    array-length v3, v0

    invoke-static {v0, v2, v1, v2, v3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 129
    iget v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->_fsp:I

    .line 130
    iget-boolean v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    iput-boolean v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    .line 131
    iget v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->lastErrorIndex:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->lastErrorIndex:I

    .line 132
    iget-boolean v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    iput-boolean v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    .line 133
    iget v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->syntaxErrors:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->syntaxErrors:I

    .line 134
    iget v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->backtracking:I

    .line 135
    iget-object v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    if-eqz v0, :cond_1

    .line 136
    array-length v0, v0

    new-array v0, v0, [Ljava/util/Map;

    check-cast v0, [Ljava/util/Map;

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    .line 137
    iget-object v1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->ruleMemo:[Ljava/util/Map;

    array-length v3, v1

    invoke-static {v1, v2, v0, v2, v3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 139
    :cond_1
    iget-object v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->token:Lgroovyjarjarantlr4/runtime/Token;

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 140
    iget v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharIndex:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharIndex:I

    .line 141
    iget v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharPositionInLine:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->tokenStartCharPositionInLine:I

    .line 142
    iget v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->channel:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->channel:I

    .line 143
    iget v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->type:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->type:I

    .line 144
    iget-object p1, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->text:Ljava/lang/String;

    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->text:Ljava/lang/String;

    return-void
.end method
