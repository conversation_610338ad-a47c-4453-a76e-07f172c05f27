.class public abstract Lgroovyjarjarantlr4/runtime/tree/TreeWizard$Visitor;
.super Ljava/lang/Object;
.source "TreeWizard.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/tree/TreeWizard$ContextVisitor;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/tree/TreeWizard;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "Visitor"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 62
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract visit(Ljava/lang/Object;)V
.end method

.method public visit(Ljava/lang/Object;Ljava/lang/Object;ILjava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "I",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 65
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$Visitor;->visit(Ljava/lang/Object;)V

    return-void
.end method
