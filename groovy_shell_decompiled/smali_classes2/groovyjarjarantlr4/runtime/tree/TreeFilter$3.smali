.class Lgroovyjarjarantlr4/runtime/tree/TreeFilter$3;
.super Ljava/lang/Object;
.source "TreeFilter.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/tree/TreeFilter;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeFilter;)V
    .locals 0

    .line 127
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$3;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public rule()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 130
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$3;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->bottomup()V

    return-void
.end method
