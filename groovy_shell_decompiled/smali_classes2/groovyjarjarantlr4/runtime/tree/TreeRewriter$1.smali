.class Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$1;
.super Ljava/lang/Object;
.source "TreeRewriter.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/tree/TreeVisitorAction;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->downup(Ljava/lang/Object;Z)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;)V
    .locals 0

    .line 91
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$1;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public post(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 95
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$1;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;

    iget-object v1, v0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->bottomup_ftpr:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;

    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->applyRepeatedly(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public pre(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 93
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$1;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;

    iget-object v1, v0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->topdown_fptr:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;

    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->applyOnce(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
