.class public Lgroovyjarjarantlr4/runtime/tree/TreeParser;
.super Lgroovyjarjarantlr4/runtime/BaseRecognizer;
.source "TreeParser.java"


# static fields
.field public static final DOWN:I = 0x2

.field public static final UP:I = 0x3

.field static dotdot:Ljava/lang/String; = ".*[^.]\\.\\.[^.].*"

.field static dotdotPattern:Ljava/util/regex/Pattern; = null

.field static doubleEtc:Ljava/lang/String; = ".*\\.\\.\\.\\s+\\.\\.\\..*"

.field static doubleEtcPattern:Ljava/util/regex/Pattern;


# instance fields
.field protected input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, ".*[^.]\\.\\.[^.].*"

    .line 46
    invoke-static {v0}, Ljava/util/regex/Pattern;->compile(Ljava/lang/String;)Ljava/util/regex/Pattern;

    move-result-object v0

    sput-object v0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->dotdotPattern:Ljava/util/regex/Pattern;

    .line 47
    sget-object v0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->doubleEtc:Ljava/lang/String;

    invoke-static {v0}, Ljava/util/regex/Pattern;->compile(Ljava/lang/String;)Ljava/util/regex/Pattern;

    move-result-object v0

    sput-object v0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->doubleEtcPattern:Ljava/util/regex/Pattern;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V
    .locals 0

    .line 52
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;-><init>()V

    .line 53
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->setTreeNodeStream(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 0

    .line 57
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;-><init>(Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    .line 58
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->setTreeNodeStream(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V

    return-void
.end method

.method protected static getAncestor(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;[Ljava/lang/String;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    :goto_0
    if-eqz p2, :cond_1

    .line 232
    invoke-interface {p0, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getType(Ljava/lang/Object;)I

    move-result v0

    aget-object v0, p1, v0

    .line 233
    invoke-virtual {v0, p3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p2

    .line 234
    :cond_0
    invoke-interface {p0, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getParent(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    goto :goto_0

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method public static inContext(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;[Ljava/lang/String;Ljava/lang/Object;Ljava/lang/String;)Z
    .locals 5

    .line 192
    sget-object v0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->dotdotPattern:Ljava/util/regex/Pattern;

    invoke-virtual {v0, p3}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    move-result-object v0

    .line 193
    sget-object v1, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->doubleEtcPattern:Ljava/util/regex/Pattern;

    invoke-virtual {v1, p3}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    move-result-object v1

    .line 194
    invoke-virtual {v0}, Ljava/util/regex/Matcher;->find()Z

    move-result v0

    if-nez v0, :cond_7

    .line 197
    invoke-virtual {v1}, Ljava/util/regex/Matcher;->find()Z

    move-result v0

    if-nez v0, :cond_6

    const-string v0, "\\.\\.\\."

    const-string v1, " ... "

    .line 200
    invoke-virtual {p3, v0, v1}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p3

    .line 201
    invoke-virtual {p3}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object p3

    const-string v0, "\\s+"

    .line 202
    invoke-virtual {p3, v0}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p3

    .line 203
    array-length v0, p3

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    .line 204
    invoke-interface {p0, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getParent(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    :goto_0
    const/4 v2, 0x0

    if-ltz v0, :cond_4

    if-eqz p2, :cond_4

    .line 206
    aget-object v3, p3, v0

    const-string v4, "..."

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    if-nez v0, :cond_0

    return v1

    :cond_0
    add-int/lit8 v3, v0, -0x1

    .line 209
    aget-object v3, p3, v3

    .line 210
    invoke-static {p0, p1, p2, v3}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->getAncestor(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;[Ljava/lang/String;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p2

    if-nez p2, :cond_1

    return v2

    :cond_1
    add-int/lit8 v0, v0, -0x1

    .line 215
    :cond_2
    invoke-interface {p0, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getType(Ljava/lang/Object;)I

    move-result v3

    aget-object v3, p1, v3

    .line 216
    aget-object v4, p3, v0

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_3

    return v2

    :cond_3
    add-int/lit8 v0, v0, -0x1

    .line 222
    invoke-interface {p0, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getParent(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    goto :goto_0

    :cond_4
    if-nez p2, :cond_5

    if-ltz v0, :cond_5

    return v2

    :cond_5
    return v1

    .line 198
    :cond_6
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string p1, "invalid syntax: ... ..."

    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0

    .line 195
    :cond_7
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string p1, "invalid syntax: .."

    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0
.end method


# virtual methods
.method protected getCurrentInputSymbol(Lgroovyjarjarantlr4/runtime/IntStream;)Ljava/lang/Object;
    .locals 1

    .line 85
    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    const/4 v0, 0x1

    invoke-interface {p1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getErrorHeader(Lgroovyjarjarantlr4/runtime/RecognitionException;)Ljava/lang/String;
    .locals 2

    .line 150
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->getGrammarFileName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ": node from "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-boolean v1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->approximateLineInfo:Z

    if-eqz v1, :cond_0

    const-string v1, "after "

    goto :goto_0

    :cond_0
    const-string v1, ""

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "line "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->line:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget p1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->charPositionInLine:I

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getErrorMessage(Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/String;)Ljava/lang/String;
    .locals 4

    .line 159
    instance-of v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;

    if-eqz v0, :cond_0

    .line 160
    iget-object v0, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->input:Lgroovyjarjarantlr4/runtime/IntStream;

    check-cast v0, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object v0

    .line 161
    iget-object v1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getToken(Ljava/lang/Object;)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object v1

    iput-object v1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 162
    iget-object v1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    if-nez v1, :cond_0

    .line 163
    new-instance v1, Lgroovyjarjarantlr4/runtime/CommonToken;

    iget-object v2, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    invoke-interface {v0, v2}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getType(Ljava/lang/Object;)I

    move-result v2

    iget-object v3, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->node:Ljava/lang/Object;

    invoke-interface {v0, v3}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getText(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {v1, v2, v0}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    iput-object v1, p1, Lgroovyjarjarantlr4/runtime/RecognitionException;->token:Lgroovyjarjarantlr4/runtime/Token;

    .line 167
    :cond_0
    invoke-super {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->getErrorMessage(Lgroovyjarjarantlr4/runtime/RecognitionException;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected getMissingSymbol(Lgroovyjarjarantlr4/runtime/IntStream;Lgroovyjarjarantlr4/runtime/RecognitionException;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;
    .locals 0

    .line 94
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "<missing "

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->getTokenNames()[Ljava/lang/String;

    move-result-object p4

    aget-object p4, p4, p3

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p4, ">"

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 96
    iget-object p2, p2, Lgroovyjarjarantlr4/runtime/RecognitionException;->input:Lgroovyjarjarantlr4/runtime/IntStream;

    check-cast p2, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object p2

    .line 97
    new-instance p4, Lgroovyjarjarantlr4/runtime/CommonToken;

    invoke-direct {p4, p3, p1}, Lgroovyjarjarantlr4/runtime/CommonToken;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p4}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->create(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 80
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getSourceName()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getTreeNodeStream()Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;
    .locals 1

    .line 75
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    return-object v0
.end method

.method public inContext(Ljava/lang/String;)Z
    .locals 4

    .line 181
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->getTokenNames()[Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    const/4 v3, 0x1

    invoke-interface {v2, v3}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v0, v1, v2, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->inContext(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;[Ljava/lang/String;Ljava/lang/Object;Ljava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public matchAny(Lgroovyjarjarantlr4/runtime/IntStream;)V
    .locals 4

    .line 106
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    const/4 v0, 0x0

    iput-boolean v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->errorRecovery:Z

    .line 107
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    iput-boolean v0, p1, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;->failed:Z

    .line 108
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    const/4 v1, 0x1

    invoke-interface {p1, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object p1

    .line 109
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object v2

    invoke-interface {v2, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getChildCount(Ljava/lang/Object;)I

    move-result v2

    if-nez v2, :cond_0

    .line 110
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->consume()V

    return-void

    .line 116
    :cond_0
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object v2

    invoke-interface {v2, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getType(Ljava/lang/Object;)I

    move-result p1

    :cond_1
    :goto_0
    const/4 v2, -0x1

    if-eq p1, v2, :cond_4

    const/4 v2, 0x3

    if-ne p1, v2, :cond_2

    if-eqz v0, :cond_4

    .line 118
    :cond_2
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->consume()V

    .line 119
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {p1, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object p1

    .line 120
    iget-object v3, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {v3}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object v3

    invoke-interface {v3, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getType(Ljava/lang/Object;)I

    move-result p1

    const/4 v3, 0x2

    if-ne p1, v3, :cond_3

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_3
    if-ne p1, v2, :cond_1

    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    .line 128
    :cond_4
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->consume()V

    return-void
.end method

.method protected recoverFromMismatchedToken(Lgroovyjarjarantlr4/runtime/IntStream;ILgroovyjarjarantlr4/runtime/BitSet;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 141
    new-instance p3, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;

    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    invoke-direct {p3, p2, p1}, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;-><init>(ILgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V

    throw p3
.end method

.method public reset()V
    .locals 2

    .line 63
    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->reset()V

    .line 64
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    if-eqz v0, :cond_0

    const/4 v1, 0x0

    .line 65
    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->seek(I)V

    :cond_0
    return-void
.end method

.method public setTreeNodeStream(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V
    .locals 0

    .line 71
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    return-void
.end method

.method public traceIn(Ljava/lang/String;I)V
    .locals 2

    .line 240
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object v0

    invoke-super {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->traceIn(Ljava/lang/String;ILjava/lang/Object;)V

    return-void
.end method

.method public traceOut(Ljava/lang/String;I)V
    .locals 2

    .line 244
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeParser;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->LT(I)Ljava/lang/Object;

    move-result-object v0

    invoke-super {p0, p1, p2, v0}, Lgroovyjarjarantlr4/runtime/BaseRecognizer;->traceOut(Ljava/lang/String;ILjava/lang/Object;)V

    return-void
.end method
