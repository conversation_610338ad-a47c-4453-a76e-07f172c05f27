.class public Lgroovyjarjarantlr4/runtime/tree/RewriteRuleTokenStream;
.super Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;
.source "RewriteRuleTokenStream.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;)V
    .locals 0

    .line 37
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 0

    .line 45
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 53
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method protected dup(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 76
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "dup can\'t be called for a token stream."

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public nextNode()Ljava/lang/Object;
    .locals 2

    .line 58
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleTokenStream;->_next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;

    .line 59
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleTokenStream;->adaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    invoke-interface {v1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->create(Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public nextToken()Lgroovyjarjarantlr4/runtime/Token;
    .locals 1

    .line 63
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleTokenStream;->_next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;

    return-object v0
.end method

.method protected toTree(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    return-object p1
.end method
