.class Lgroovyjarjarantlr4/runtime/tree/TreeFilter$2;
.super Ljava/lang/Object;
.source "TreeFilter.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/tree/TreeFilter;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeFilter;)V
    .locals 0

    .line 120
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$2;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public rule()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    .line 123
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$2;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;

    invoke-virtual {v0}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->topdown()V

    return-void
.end method
