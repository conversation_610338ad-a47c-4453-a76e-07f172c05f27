.class public Lgroovyjarjarantlr4/runtime/tree/RewriteEarlyExitException;
.super Lgroovyjarjarantlr4/runtime/tree/RewriteCardinalityException;
.source "RewriteEarlyExitException.java"


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 33
    invoke-direct {p0, v0}, Lgroovyjarjarantlr4/runtime/tree/RewriteCardinalityException;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 36
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/runtime/tree/RewriteCardinalityException;-><init>(Ljava/lang/String;)V

    return-void
.end method
