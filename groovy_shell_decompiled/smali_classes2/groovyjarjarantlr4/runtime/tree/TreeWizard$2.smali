.class Lgroovyjarjarantlr4/runtime/tree/TreeWizard$2;
.super Ljava/lang/Object;
.source "TreeWizard.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/tree/TreeWizard$ContextVisitor;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/runtime/tree/TreeWizard;->find(Ljava/lang/Object;Ljava/lang/String;)Ljava/util/List;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/tree/TreeWizard;

.field final synthetic val$subtrees:Ljava/util/List;

.field final synthetic val$tpattern:Lgroovyjarjarantlr4/runtime/tree/TreeWizard$TreePattern;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeWizard;Lgroovyjarjarantlr4/runtime/tree/TreeWizard$TreePattern;Ljava/util/List;)V
    .locals 0

    .line 230
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$2;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeWizard;

    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$2;->val$tpattern:Lgroovyjarjarantlr4/runtime/tree/TreeWizard$TreePattern;

    iput-object p3, p0, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$2;->val$subtrees:Ljava/util/List;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Ljava/lang/Object;Ljava/lang/Object;ILjava/util/Map;)V
    .locals 0

    .line 233
    iget-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$2;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeWizard;

    iget-object p3, p0, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$2;->val$tpattern:Lgroovyjarjarantlr4/runtime/tree/TreeWizard$TreePattern;

    const/4 p4, 0x0

    invoke-virtual {p2, p1, p3, p4}, Lgroovyjarjarantlr4/runtime/tree/TreeWizard;->_parse(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeWizard$TreePattern;Ljava/util/Map;)Z

    move-result p2

    if-eqz p2, :cond_0

    .line 234
    iget-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$2;->val$subtrees:Ljava/util/List;

    invoke-interface {p2, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method
