.class Lgroovyjarjarantlr4/runtime/tree/TreeWizard$1;
.super Lgroovyjarjarantlr4/runtime/tree/TreeWizard$Visitor;
.source "TreeWizard.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/runtime/tree/TreeWizard;->find(Ljava/lang/Object;I)Ljava/util/List;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/tree/TreeWizard;

.field final synthetic val$nodes:Ljava/util/List;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeWizard;Ljava/util/List;)V
    .locals 0

    .line 205
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$1;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeWizard;

    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$1;->val$nodes:Ljava/util/List;

    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$Visitor;-><init>()V

    return-void
.end method


# virtual methods
.method public visit(Ljava/lang/Object;)V
    .locals 1

    .line 208
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$1;->val$nodes:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method
