.class public abstract Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;
.super Ljava/lang/Object;
.source "RewriteRuleElementStream.java"


# instance fields
.field protected adaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

.field protected cursor:I

.field protected dirty:Z

.field protected elementDescription:Ljava/lang/String;

.field protected elements:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field protected singleElement:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;)V
    .locals 1

    .line 77
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 50
    iput v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->cursor:I

    .line 68
    iput-boolean v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->dirty:Z

    .line 78
    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elementDescription:Ljava/lang/String;

    .line 79
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->adaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 0

    .line 88
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;)V

    .line 89
    invoke-virtual {p0, p3}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->add(Ljava/lang/Object;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 97
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;)V

    const/4 p1, 0x0

    .line 98
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->singleElement:Ljava/lang/Object;

    .line 99
    iput-object p3, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elements:Ljava/util/List;

    return-void
.end method


# virtual methods
.method protected _next()Ljava/lang/Object;
    .locals 3

    .line 156
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->size()I

    move-result v0

    if-eqz v0, :cond_3

    .line 160
    iget v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->cursor:I

    const/4 v2, 0x1

    if-lt v1, v0, :cond_1

    if-ne v0, v2, :cond_0

    .line 162
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->singleElement:Ljava/lang/Object;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->toTree(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0

    .line 165
    :cond_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/RewriteCardinalityException;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elementDescription:Ljava/lang/String;

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/RewriteCardinalityException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 168
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->singleElement:Ljava/lang/Object;

    if-eqz v0, :cond_2

    add-int/2addr v1, v2

    .line 169
    iput v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->cursor:I

    .line 170
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->toTree(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0

    .line 173
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elements:Ljava/util/List;

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->toTree(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    .line 174
    iget v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->cursor:I

    add-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->cursor:I

    return-object v0

    .line 158
    :cond_3
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/RewriteEmptyStreamException;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elementDescription:Ljava/lang/String;

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/RewriteEmptyStreamException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public add(Ljava/lang/Object;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    .line 117
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elements:Ljava/util/List;

    if-eqz v0, :cond_1

    .line 118
    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void

    .line 121
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->singleElement:Ljava/lang/Object;

    if-nez v0, :cond_2

    .line 122
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->singleElement:Ljava/lang/Object;

    return-void

    .line 126
    :cond_2
    new-instance v0, Ljava/util/ArrayList;

    const/4 v1, 0x5

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elements:Ljava/util/List;

    .line 127
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->singleElement:Ljava/lang/Object;

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/4 v0, 0x0

    .line 128
    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->singleElement:Ljava/lang/Object;

    .line 129
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elements:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method protected abstract dup(Ljava/lang/Object;)Ljava/lang/Object;
.end method

.method public getDescription()Ljava/lang/String;
    .locals 1

    .line 209
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elementDescription:Ljava/lang/String;

    return-object v0
.end method

.method public hasNext()Z
    .locals 3

    .line 193
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->singleElement:Ljava/lang/Object;

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    iget v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->cursor:I

    if-lt v0, v1, :cond_2

    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elements:Ljava/util/List;

    if-eqz v0, :cond_1

    iget v2, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->cursor:I

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge v2, v0, :cond_1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :cond_2
    :goto_0
    return v1
.end method

.method public nextTree()Ljava/lang/Object;
    .locals 2

    .line 138
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->size()I

    move-result v0

    .line 139
    iget-boolean v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->dirty:Z

    if-nez v1, :cond_1

    iget v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->cursor:I

    if-lt v1, v0, :cond_0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 145
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->_next()Ljava/lang/Object;

    move-result-object v0

    return-object v0

    .line 141
    :cond_1
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->_next()Ljava/lang/Object;

    move-result-object v0

    .line 142
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->dup(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public reset()V
    .locals 1

    const/4 v0, 0x0

    .line 108
    iput v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->cursor:I

    const/4 v0, 0x1

    .line 109
    iput-boolean v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->dirty:Z

    return-void
.end method

.method public size()I
    .locals 2

    .line 199
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->singleElement:Ljava/lang/Object;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 202
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;->elements:Ljava/util/List;

    if-eqz v1, :cond_1

    .line 203
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v0

    :cond_1
    return v0
.end method

.method protected toTree(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    return-object p1
.end method
