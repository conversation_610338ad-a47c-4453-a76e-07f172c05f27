.class public Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;
.super Lgroovyjarjarantlr4/runtime/tree/TreeParser;
.source "TreeRewriter.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;
    }
.end annotation


# instance fields
.field bottomup_ftpr:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;

.field protected originalAdaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

.field protected originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

.field protected showTransformations:Z

.field topdown_fptr:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V
    .locals 1

    .line 45
    new-instance v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    invoke-direct {v0}, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;-><init>()V

    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 0

    .line 48
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    const/4 p2, 0x0

    .line 39
    iput-boolean p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->showTransformations:Z

    .line 109
    new-instance p2, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$2;

    invoke-direct {p2, p0}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$2;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;)V

    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->topdown_fptr:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;

    .line 114
    new-instance p2, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$3;

    invoke-direct {p2, p0}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$3;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;)V

    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->bottomup_ftpr:Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;

    .line 49
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->originalAdaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    .line 50
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTokenStream()Lgroovyjarjarantlr4/runtime/TokenStream;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-void
.end method


# virtual methods
.method public applyOnce(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;)Ljava/lang/Object;
    .locals 2

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 57
    :cond_0
    :try_start_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    invoke-direct {v0}, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    .line 58
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->originalAdaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    invoke-direct {v0, v1, p1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/Object;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    .line 59
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    check-cast v0, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;->setTokenStream(Lgroovyjarjarantlr4/runtime/TokenStream;)V

    const/4 v0, 0x1

    .line 60
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->setBacktrackingLevel(I)V

    .line 61
    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;->rule()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;

    const/4 v0, 0x0

    .line 62
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->setBacktrackingLevel(I)V

    .line 63
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->failed()Z

    move-result v0

    if-eqz v0, :cond_1

    return-object p1

    .line 64
    :cond_1
    iget-boolean v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->showTransformations:Z

    if-eqz v0, :cond_2

    if-eqz p2, :cond_2

    invoke-virtual {p2}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;->getTree()Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    invoke-virtual {p2}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;->getTree()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 67
    invoke-virtual {p2}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;->getTree()Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->reportTransformation(Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_2
    if-eqz p2, :cond_3

    .line 69
    invoke-virtual {p2}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;->getTree()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_3

    invoke-virtual {p2}, Lgroovyjarjarantlr4/runtime/tree/TreeRuleReturnScope;->getTree()Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_3
    return-object p1
.end method

.method public applyRepeatedly(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;)Ljava/lang/Object;
    .locals 3

    const/4 v0, 0x1

    move v1, v0

    :goto_0
    if-eqz v1, :cond_0

    .line 79
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->applyOnce(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$fptr;)Ljava/lang/Object;

    move-result-object v1

    .line 80
    invoke-virtual {p1, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    xor-int/2addr p1, v0

    move-object v2, v1

    move v1, p1

    move-object p1, v2

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public bottomup()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    const/4 v0, 0x0

    return-object v0
.end method

.method public downup(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    .line 86
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->downup(Ljava/lang/Object;Z)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public downup(Ljava/lang/Object;Z)Ljava/lang/Object;
    .locals 1

    .line 89
    iput-boolean p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;->showTransformations:Z

    .line 90
    new-instance p2, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;

    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;

    invoke-direct {v0}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;-><init>()V

    invoke-direct {p2, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;)V

    .line 91
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$1;

    invoke-direct {v0, p0}, Lgroovyjarjarantlr4/runtime/tree/TreeRewriter$1;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeRewriter;)V

    .line 97
    invoke-virtual {p2, p1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;->visit(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeVisitorAction;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public reportTransformation(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 2

    .line 105
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/Tree;

    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/Tree;->toStringTree()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " -> "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    check-cast p2, Lgroovyjarjarantlr4/runtime/tree/Tree;

    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/tree/Tree;->toStringTree()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method

.method public topdown()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    const/4 v0, 0x0

    return-object v0
.end method
