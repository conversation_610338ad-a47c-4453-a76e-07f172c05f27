.class public Lgroovyjarjarantlr4/runtime/tree/TreeFilter;
.super Lgroovyjarjarantlr4/runtime/tree/TreeParser;
.source "TreeFilter.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;
    }
.end annotation


# instance fields
.field bottomup_fptr:Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;

.field protected originalAdaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

.field protected originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

.field topdown_fptr:Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V
    .locals 1

    .line 87
    new-instance v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    invoke-direct {v0}, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;-><init>()V

    invoke-direct {p0, p1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V
    .locals 0

    .line 90
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/TreeParser;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;Lgroovyjarjarantlr4/runtime/RecognizerSharedState;)V

    .line 120
    new-instance p2, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$2;

    invoke-direct {p2, p0}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$2;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeFilter;)V

    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->topdown_fptr:Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;

    .line 127
    new-instance p2, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$3;

    invoke-direct {p2, p0}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$3;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeFilter;)V

    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->bottomup_fptr:Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;

    .line 91
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTreeAdaptor()Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->originalAdaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    .line 92
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;->getTokenStream()Lgroovyjarjarantlr4/runtime/TokenStream;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    return-void
.end method


# virtual methods
.method public applyOnce(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;)V
    .locals 2

    if-nez p1, :cond_0

    return-void

    .line 99
    :cond_0
    :try_start_0
    new-instance v0, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    invoke-direct {v0}, Lgroovyjarjarantlr4/runtime/RecognizerSharedState;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->state:Lgroovyjarjarantlr4/runtime/RecognizerSharedState;

    .line 100
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->originalAdaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    invoke-direct {v0, v1, p1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/Object;)V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    .line 101
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->input:Lgroovyjarjarantlr4/runtime/tree/TreeNodeStream;

    check-cast p1, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;

    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->originalTokenStream:Lgroovyjarjarantlr4/runtime/TokenStream;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeNodeStream;->setTokenStream(Lgroovyjarjarantlr4/runtime/TokenStream;)V

    const/4 p1, 0x1

    .line 102
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->setBacktrackingLevel(I)V

    .line 103
    invoke-interface {p2}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;->rule()V

    const/4 p1, 0x0

    .line 104
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->setBacktrackingLevel(I)V
    :try_end_0
    .catch Lgroovyjarjarantlr4/runtime/RecognitionException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    return-void
.end method

.method public bottomup()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    return-void
.end method

.method public downup(Ljava/lang/Object;)V
    .locals 2

    .line 110
    new-instance v0, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;

    new-instance v1, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;

    invoke-direct {v1}, Lgroovyjarjarantlr4/runtime/tree/CommonTreeAdaptor;-><init>()V

    invoke-direct {v0, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;)V

    .line 111
    new-instance v1, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$1;

    invoke-direct {v1, p0}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$1;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeFilter;)V

    .line 117
    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeVisitor;->visit(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeVisitorAction;)Ljava/lang/Object;

    return-void
.end method

.method public topdown()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr4/runtime/RecognitionException;
        }
    .end annotation

    return-void
.end method
