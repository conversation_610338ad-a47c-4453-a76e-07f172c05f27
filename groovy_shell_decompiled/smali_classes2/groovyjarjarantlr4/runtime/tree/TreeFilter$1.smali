.class Lgroovyjarjarantlr4/runtime/tree/TreeFilter$1;
.super Ljava/lang/Object;
.source "TreeFilter.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/tree/TreeVisitorAction;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->downup(Ljava/lang/Object;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;


# direct methods
.method constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeFilter;)V
    .locals 0

    .line 111
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$1;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public post(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 115
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$1;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;

    iget-object v1, v0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->bottomup_fptr:Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;

    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->applyOnce(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;)V

    return-object p1
.end method

.method public pre(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 113
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter$1;->this$0:Lgroovyjarjarantlr4/runtime/tree/TreeFilter;

    iget-object v1, v0, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->topdown_fptr:Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;

    invoke-virtual {v0, p1, v1}, Lgroovyjarjarantlr4/runtime/tree/TreeFilter;->applyOnce(Ljava/lang/Object;Lgroovyjarjarantlr4/runtime/tree/TreeFilter$fptr;)V

    return-object p1
.end method
