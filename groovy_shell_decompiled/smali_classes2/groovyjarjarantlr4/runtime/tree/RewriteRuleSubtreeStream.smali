.class public Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;
.super Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;
.source "RewriteRuleSubtreeStream.java"


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;)V
    .locals 0

    .line 35
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 0

    .line 43
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 51
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleElementStream;-><init>(Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;Ljava/lang/String;Ljava/util/List;)V

    return-void
.end method


# virtual methods
.method protected dup(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 87
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->adaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->dupTree(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public nextNode()Ljava/lang/Object;
    .locals 4

    .line 69
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->size()I

    move-result v0

    .line 70
    iget-boolean v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->dirty:Z

    if-nez v1, :cond_2

    iget v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->cursor:I

    const/4 v2, 0x1

    if-lt v1, v0, :cond_0

    if-ne v0, v2, :cond_0

    goto :goto_1

    .line 77
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->_next()Ljava/lang/Object;

    move-result-object v0

    .line 78
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->adaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    invoke-interface {v1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->isNil(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->adaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    invoke-interface {v1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getChildCount(Ljava/lang/Object;)I

    move-result v1

    if-ne v1, v2, :cond_1

    .line 79
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->adaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    const/4 v3, 0x0

    invoke-interface {v1, v0, v3}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->getChild(Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object v0

    goto :goto_0

    .line 81
    :cond_1
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->adaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    invoke-interface {v1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->dupNode(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0

    .line 73
    :cond_2
    :goto_1
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->_next()Ljava/lang/Object;

    move-result-object v0

    .line 74
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/RewriteRuleSubtreeStream;->adaptor:Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;

    invoke-interface {v1, v0}, Lgroovyjarjarantlr4/runtime/tree/TreeAdaptor;->dupNode(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
