.class public Lgroovyjarjarantlr4/runtime/tree/TreeWizard$WildcardTreePattern;
.super Lgroovyjarjarantlr4/runtime/tree/TreeWizard$TreePattern;
.source "TreeWizard.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/tree/TreeWizard;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "WildcardTreePattern"
.end annotation


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 0

    .line 92
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/runtime/tree/TreeWizard$TreePattern;-><init>(Lgroovyjarjarantlr4/runtime/Token;)V

    return-void
.end method
