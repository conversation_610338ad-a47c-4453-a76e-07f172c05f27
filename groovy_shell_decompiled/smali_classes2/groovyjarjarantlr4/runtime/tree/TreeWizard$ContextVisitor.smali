.class public interface abstract Lgroovyjarjarantlr4/runtime/tree/TreeWizard$ContextVisitor;
.super Ljava/lang/Object;
.source "TreeWizard.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/tree/TreeWizard;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ContextVisitor"
.end annotation


# virtual methods
.method public abstract visit(Ljava/lang/Object;Ljava/lang/Object;ILjava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "I",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation
.end method
