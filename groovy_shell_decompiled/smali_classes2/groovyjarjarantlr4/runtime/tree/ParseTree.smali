.class public Lgroovyjarjarantlr4/runtime/tree/ParseTree;
.super Lgroovyjarjarantlr4/runtime/tree/BaseTree;
.source "ParseTree.java"


# instance fields
.field public hiddenTokens:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/runtime/Token;",
            ">;"
        }
    .end annotation
.end field

.field public payload:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 0

    .line 43
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/tree/BaseTree;-><init>()V

    .line 44
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->payload:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public _toStringLeaves(Ljava/lang/StringBuffer;)V
    .locals 2

    .line 118
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->payload:Ljava/lang/Object;

    instance-of v0, v0, Lgroovyjarjarantlr4/runtime/Token;

    if-eqz v0, :cond_0

    .line 119
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->toStringWithHiddenTokens()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    return-void

    :cond_0
    const/4 v0, 0x0

    .line 122
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->children:Ljava/util/List;

    if-eqz v1, :cond_1

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->children:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_1

    .line 123
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->children:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/runtime/tree/ParseTree;

    .line 124
    invoke-virtual {v1, p1}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->_toStringLeaves(Ljava/lang/StringBuffer;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public dupNode()Lgroovyjarjarantlr4/runtime/tree/Tree;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getText()Ljava/lang/String;
    .locals 1

    .line 59
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getTokenStartIndex()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public getTokenStopIndex()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public getType()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public setTokenStartIndex(I)V
    .locals 0

    return-void
.end method

.method public setTokenStopIndex(I)V
    .locals 0

    return-void
.end method

.method public toInputString()Ljava/lang/String;
    .locals 1

    .line 112
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    .line 113
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->_toStringLeaves(Ljava/lang/StringBuffer;)V

    .line 114
    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 82
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->payload:Ljava/lang/Object;

    instance-of v1, v0, Lgroovyjarjarantlr4/runtime/Token;

    if-eqz v1, :cond_1

    .line 83
    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;

    .line 84
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    const-string v0, "<EOF>"

    return-object v0

    .line 87
    :cond_0
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 89
    :cond_1
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public toStringWithHiddenTokens()Ljava/lang/String;
    .locals 3

    .line 96
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 97
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->hiddenTokens:Ljava/util/List;

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    .line 98
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->hiddenTokens:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_0

    .line 99
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->hiddenTokens:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/runtime/Token;

    .line 100
    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 103
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/tree/ParseTree;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "<EOF>"

    .line 104
    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 105
    :cond_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
