.class public Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;
.super Ljava/lang/Object;
.source "TokenRewriteStream.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/TokenRewriteStream;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "RewriteOperation"
.end annotation


# instance fields
.field protected index:I

.field protected instructionIndex:I

.field protected text:Ljava/lang/Object;

.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/TokenRewriteStream;


# direct methods
.method protected constructor <init>(Lgroovyjarjarantlr4/runtime/TokenRewriteStream;I)V
    .locals 0

    .line 97
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;->this$0:Lgroovyjarjarantlr4/runtime/TokenRewriteStream;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 98
    iput p2, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;->index:I

    return-void
.end method

.method protected constructor <init>(Lgroovyjarjarantlr4/runtime/TokenRewriteStream;ILjava/lang/Object;)V
    .locals 0

    .line 101
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;->this$0:Lgroovyjarjarantlr4/runtime/TokenRewriteStream;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 102
    iput p2, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;->index:I

    .line 103
    iput-object p3, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;->text:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public execute(Ljava/lang/StringBuffer;)I
    .locals 0

    .line 109
    iget p1, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;->index:I

    return p1
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 113
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    const/16 v1, 0x24

    .line 114
    invoke-virtual {v0, v1}, Ljava/lang/String;->indexOf(I)I

    move-result v1

    add-int/lit8 v1, v1, 0x1

    .line 115
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    .line 116
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "<"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "@"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;->this$0:Lgroovyjarjarantlr4/runtime/TokenRewriteStream;

    iget-object v1, v1, Lgroovyjarjarantlr4/runtime/TokenRewriteStream;->tokens:Ljava/util/List;

    iget v2, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;->index:I

    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":\""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;->text:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\">"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
