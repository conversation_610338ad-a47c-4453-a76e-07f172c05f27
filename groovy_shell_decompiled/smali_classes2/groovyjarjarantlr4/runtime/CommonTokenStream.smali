.class public Lgroovyjarjarantlr4/runtime/CommonTokenStream;
.super Lgroovyjarjarantlr4/runtime/BufferedTokenStream;
.source "CommonTokenStream.java"


# instance fields
.field protected channel:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 52
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;-><init>()V

    const/4 v0, 0x0

    .line 50
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->channel:I

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenSource;)V
    .locals 0

    .line 55
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;-><init>(Lgroovyjarjarantlr4/runtime/TokenSource;)V

    const/4 p1, 0x0

    .line 50
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->channel:I

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenSource;I)V
    .locals 0

    .line 59
    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;-><init>(Lgroovyjarjarantlr4/runtime/TokenSource;)V

    .line 60
    iput p2, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->channel:I

    return-void
.end method


# virtual methods
.method protected LB(I)Lgroovyjarjarantlr4/runtime/Token;
    .locals 3

    const/4 v0, 0x0

    if-eqz p1, :cond_3

    .line 77
    iget v1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    sub-int/2addr v1, p1

    if-gez v1, :cond_0

    goto :goto_1

    .line 79
    :cond_0
    iget v1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    const/4 v2, 0x1

    :goto_0
    if-gt v2, p1, :cond_1

    add-int/lit8 v1, v1, -0x1

    .line 84
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->skipOffTokenChannelsReverse(I)I

    move-result v1

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    if-gez v1, :cond_2

    return-object v0

    .line 88
    :cond_2
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/runtime/Token;

    return-object p1

    :cond_3
    :goto_1
    return-object v0
.end method

.method public LT(I)Lgroovyjarjarantlr4/runtime/Token;
    .locals 2

    .line 94
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->setup()V

    :cond_0
    if-nez p1, :cond_1

    const/4 p1, 0x0

    return-object p1

    :cond_1
    if-gez p1, :cond_2

    neg-int p1, p1

    .line 96
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->LB(I)Lgroovyjarjarantlr4/runtime/Token;

    move-result-object p1

    return-object p1

    .line 97
    :cond_2
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    const/4 v1, 0x1

    :goto_0
    if-ge v1, p1, :cond_3

    add-int/lit8 v0, v0, 0x1

    .line 102
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->skipOffTokenChannels(I)I

    move-result v0

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 105
    :cond_3
    iget p1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->range:I

    if-le v0, p1, :cond_4

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->range:I

    .line 106
    :cond_4
    iget-object p1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->tokens:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarantlr4/runtime/Token;

    return-object p1
.end method

.method public consume()V
    .locals 2

    .line 66
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->setup()V

    .line 67
    :cond_0
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    .line 68
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->sync(I)V

    .line 69
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->tokens:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getChannel()I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->channel:I

    if-eq v0, v1, :cond_1

    .line 70
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    .line 71
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->sync(I)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public getNumberOfOnChannelTokens()I
    .locals 5

    .line 149
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->fill()V

    const/4 v0, 0x0

    move v1, v0

    .line 150
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v0, v2, :cond_2

    .line 151
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr4/runtime/Token;

    .line 152
    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getChannel()I

    move-result v3

    iget v4, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->channel:I

    if-ne v3, v4, :cond_0

    add-int/lit8 v1, v1, 0x1

    .line 153
    :cond_0
    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v2

    const/4 v3, -0x1

    if-ne v2, v3, :cond_1

    goto :goto_1

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    :goto_1
    return v1
.end method

.method public reset()V
    .locals 1

    .line 130
    invoke-super {p0}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->reset()V

    const/4 v0, 0x0

    .line 131
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->skipOffTokenChannels(I)I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    return-void
.end method

.method public setTokenSource(Lgroovyjarjarantlr4/runtime/TokenSource;)V
    .locals 0

    .line 161
    invoke-super {p0, p1}, Lgroovyjarjarantlr4/runtime/BufferedTokenStream;->setTokenSource(Lgroovyjarjarantlr4/runtime/TokenSource;)V

    const/4 p1, 0x0

    .line 162
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->channel:I

    return-void
.end method

.method protected setup()V
    .locals 3

    const/4 v0, 0x0

    .line 136
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    .line 137
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->sync(I)V

    .line 139
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v1}, Lgroovyjarjarantlr4/runtime/Token;->getChannel()I

    move-result v1

    iget v2, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->channel:I

    if-eq v1, v2, :cond_0

    add-int/lit8 v0, v0, 0x1

    .line 141
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->sync(I)V

    goto :goto_0

    .line 143
    :cond_0
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->p:I

    return-void
.end method

.method protected skipOffTokenChannels(I)I
    .locals 2

    .line 113
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->sync(I)V

    .line 114
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getChannel()I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->channel:I

    if-eq v0, v1, :cond_0

    add-int/lit8 p1, p1, 0x1

    .line 116
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->sync(I)V

    goto :goto_0

    :cond_0
    return p1
.end method

.method protected skipOffTokenChannelsReverse(I)I
    .locals 2

    :goto_0
    if-ltz p1, :cond_0

    .line 122
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->tokens:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getChannel()I

    move-result v0

    iget v1, p0, Lgroovyjarjarantlr4/runtime/CommonTokenStream;->channel:I

    if-eq v0, v1, :cond_0

    add-int/lit8 p1, p1, -0x1

    goto :goto_0

    :cond_0
    return p1
.end method
