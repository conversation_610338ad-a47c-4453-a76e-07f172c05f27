.class public Lgroovyjarjarantlr4/runtime/CommonToken;
.super Ljava/lang/Object;
.source "CommonToken.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/Token;
.implements Ljava/io/Serializable;


# instance fields
.field protected channel:I

.field protected charPositionInLine:I

.field protected index:I

.field protected transient input:Lgroovyjarjarantlr4/runtime/CharStream;

.field protected line:I

.field protected start:I

.field protected stop:I

.field protected text:Ljava/lang/String;

.field protected type:I


# direct methods
.method public constructor <init>(I)V
    .locals 2

    .line 54
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 35
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->charPositionInLine:I

    const/4 v1, 0x0

    .line 36
    iput v1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    .line 46
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->index:I

    .line 55
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->type:I

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;)V
    .locals 2

    .line 66
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 35
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->charPositionInLine:I

    const/4 v1, 0x0

    .line 36
    iput v1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    .line 46
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->index:I

    .line 67
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->type:I

    .line 68
    iput v1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    .line 69
    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->text:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/CharStream;IIII)V
    .locals 2

    .line 58
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 35
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->charPositionInLine:I

    const/4 v1, 0x0

    .line 36
    iput v1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    .line 46
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->index:I

    .line 59
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    .line 60
    iput p2, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->type:I

    .line 61
    iput p3, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    .line 62
    iput p4, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->start:I

    .line 63
    iput p5, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->stop:I

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr4/runtime/Token;)V
    .locals 2

    .line 72
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 35
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->charPositionInLine:I

    const/4 v1, 0x0

    .line 36
    iput v1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    .line 46
    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->index:I

    .line 73
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->text:Ljava/lang/String;

    .line 74
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->type:I

    .line 75
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getLine()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->line:I

    .line 76
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getTokenIndex()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->index:I

    .line 77
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getCharPositionInLine()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->charPositionInLine:I

    .line 78
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getChannel()I

    move-result v0

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    .line 79
    invoke-interface {p1}, Lgroovyjarjarantlr4/runtime/Token;->getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    .line 80
    instance-of v0, p1, Lgroovyjarjarantlr4/runtime/CommonToken;

    if-eqz v0, :cond_0

    .line 81
    check-cast p1, Lgroovyjarjarantlr4/runtime/CommonToken;

    iget v0, p1, Lgroovyjarjarantlr4/runtime/CommonToken;->start:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->start:I

    .line 82
    iget p1, p1, Lgroovyjarjarantlr4/runtime/CommonToken;->stop:I

    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->stop:I

    :cond_0
    return-void
.end method


# virtual methods
.method public getChannel()I
    .locals 1

    .line 140
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    return v0
.end method

.method public getCharPositionInLine()I
    .locals 1

    .line 130
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->charPositionInLine:I

    return v0
.end method

.method public getInputStream()Lgroovyjarjarantlr4/runtime/CharStream;
    .locals 1

    .line 181
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    return-object v0
.end method

.method public getLine()I
    .locals 1

    .line 125
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->line:I

    return v0
.end method

.method public getStartIndex()I
    .locals 1

    .line 154
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->start:I

    return v0
.end method

.method public getStopIndex()I
    .locals 1

    .line 162
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->stop:I

    return v0
.end method

.method public getText()Ljava/lang/String;
    .locals 3

    .line 98
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->text:Ljava/lang/String;

    if-eqz v0, :cond_0

    return-object v0

    .line 101
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    if-nez v0, :cond_1

    const/4 v0, 0x0

    return-object v0

    .line 104
    :cond_1
    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/CharStream;->size()I

    move-result v0

    .line 105
    iget v1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->start:I

    if-ge v1, v0, :cond_2

    iget v2, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->stop:I

    if-ge v2, v0, :cond_2

    .line 106
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    invoke-interface {v0, v1, v2}, Lgroovyjarjarantlr4/runtime/CharStream;->substring(II)Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_2
    const-string v0, "<EOF>"

    return-object v0
.end method

.method public getTokenIndex()I
    .locals 1

    .line 171
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->index:I

    return v0
.end method

.method public getType()I
    .locals 1

    .line 88
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->type:I

    return v0
.end method

.method public setChannel(I)V
    .locals 0

    .line 145
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    return-void
.end method

.method public setCharPositionInLine(I)V
    .locals 0

    .line 135
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->charPositionInLine:I

    return-void
.end method

.method public setInputStream(Lgroovyjarjarantlr4/runtime/CharStream;)V
    .locals 0

    .line 186
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->input:Lgroovyjarjarantlr4/runtime/CharStream;

    return-void
.end method

.method public setLine(I)V
    .locals 0

    .line 93
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->line:I

    return-void
.end method

.method public setStartIndex(I)V
    .locals 0

    .line 158
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->start:I

    return-void
.end method

.method public setStopIndex(I)V
    .locals 0

    .line 166
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->stop:I

    return-void
.end method

.method public setText(Ljava/lang/String;)V
    .locals 0

    .line 120
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->text:Ljava/lang/String;

    return-void
.end method

.method public setTokenIndex(I)V
    .locals 0

    .line 176
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->index:I

    return-void
.end method

.method public setType(I)V
    .locals 0

    .line 150
    iput p1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->type:I

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 6

    .line 192
    iget v0, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    if-lez v0, :cond_0

    .line 193
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, ",channel="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->channel:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    const-string v0, ""

    .line 195
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/CommonToken;->getText()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_1

    const-string v2, "\n"

    const-string v3, "\\\\n"

    .line 197
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "\r"

    const-string v3, "\\\\r"

    .line 198
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "\t"

    const-string v3, "\\\\t"

    .line 199
    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    :cond_1
    const-string v1, "<no text>"

    .line 204
    :goto_1
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "[@"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/CommonToken;->getTokenIndex()I

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, ","

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget v4, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->start:I

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v4, ":"

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget v5, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->stop:I

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v5, "=\'"

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "\',<"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v2, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->type:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ">"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/runtime/CommonToken;->line:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/CommonToken;->getCharPositionInLine()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
