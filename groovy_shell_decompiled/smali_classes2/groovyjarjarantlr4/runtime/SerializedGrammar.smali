.class public Lgroovyjarjarantlr4/runtime/SerializedGrammar;
.super Ljava/lang/Object;
.source "SerializedGrammar.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovyjarjarantlr4/runtime/SerializedGrammar$RuleRef;,
        Lgroovyjarjarantlr4/runtime/SerializedGrammar$TokenRef;,
        Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;,
        Lgroovyjarjarantlr4/runtime/SerializedGrammar$Node;,
        Lgroovyjarjarantlr4/runtime/SerializedGrammar$Rule;
    }
.end annotation


# static fields
.field public static final COOKIE:Ljava/lang/String; = "$ANTLR"

.field public static final FORMAT_VERSION:I = 0x1


# instance fields
.field public name:Ljava/lang/String;

.field public rules:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/runtime/SerializedGrammar$Rule;",
            ">;"
        }
    .end annotation
.end field

.field public type:C


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 98
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 99
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "loading "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 100
    new-instance v0, Ljava/io/FileInputStream;

    invoke-direct {v0, p1}, Ljava/io/FileInputStream;-><init>(Ljava/lang/String;)V

    .line 101
    new-instance p1, Ljava/io/BufferedInputStream;

    invoke-direct {p1, v0}, Ljava/io/BufferedInputStream;-><init>(Ljava/io/InputStream;)V

    .line 102
    new-instance v0, Ljava/io/DataInputStream;

    invoke-direct {v0, p1}, Ljava/io/DataInputStream;-><init>(Ljava/io/InputStream;)V

    .line 103
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->readFile(Ljava/io/DataInputStream;)V

    .line 104
    invoke-virtual {v0}, Ljava/io/DataInputStream;->close()V

    return-void
.end method


# virtual methods
.method protected readAlt(Ljava/io/DataInputStream;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/io/DataInputStream;",
            ")",
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/runtime/SerializedGrammar$Node;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 156
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 157
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    move-result v1

    const/16 v2, 0x41

    if-ne v1, v2, :cond_6

    .line 159
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    move-result v1

    :goto_0
    const/16 v2, 0x3b

    if-eq v1, v2, :cond_5

    const/16 v2, 0x2d

    if-eq v1, v2, :cond_4

    const/16 v2, 0x42

    if-eq v1, v2, :cond_3

    const/16 v2, 0x72

    if-eq v1, v2, :cond_2

    const/16 v2, 0x74

    if-eq v1, v2, :cond_1

    const/16 v2, 0x7e

    if-eq v1, v2, :cond_0

    goto :goto_1

    .line 179
    :cond_0
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readShort()S

    goto :goto_1

    .line 163
    :cond_1
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readShort()S

    move-result v1

    .line 164
    new-instance v2, Lgroovyjarjarantlr4/runtime/SerializedGrammar$TokenRef;

    invoke-direct {v2, p0, v1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar$TokenRef;-><init>(Lgroovyjarjarantlr4/runtime/SerializedGrammar;I)V

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 168
    :cond_2
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readShort()S

    move-result v1

    .line 169
    new-instance v2, Lgroovyjarjarantlr4/runtime/SerializedGrammar$RuleRef;

    invoke-direct {v2, p0, v1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar$RuleRef;-><init>(Lgroovyjarjarantlr4/runtime/SerializedGrammar;I)V

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 182
    :cond_3
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->readBlock(Ljava/io/DataInputStream;)Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;

    move-result-object v1

    .line 183
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 175
    :cond_4
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readChar()C

    .line 176
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readChar()C

    .line 186
    :goto_1
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    move-result v1

    goto :goto_0

    :cond_5
    return-object v0

    .line 158
    :cond_6
    new-instance p1, Ljava/io/IOException;

    const-string v0, "missing A on start of alt"

    invoke-direct {p1, v0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method protected readBlock(Ljava/io/DataInputStream;)Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 143
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readShort()S

    move-result v0

    .line 145
    new-array v1, v0, [Ljava/util/List;

    check-cast v1, [Ljava/util/List;

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    .line 148
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->readAlt(Ljava/io/DataInputStream;)Ljava/util/List;

    move-result-object v3

    .line 149
    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 152
    :cond_0
    new-instance p1, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;

    invoke-direct {p1, p0, v1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;-><init>(Lgroovyjarjarantlr4/runtime/SerializedGrammar;[Ljava/util/List;)V

    return-object p1
.end method

.method protected readFile(Ljava/io/DataInputStream;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 108
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->readString(Ljava/io/DataInputStream;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "$ANTLR"

    .line 109
    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 110
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    .line 111
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    move-result v0

    int-to-char v0, v0

    .line 112
    iput-char v0, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->type:C

    .line 113
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->readString(Ljava/io/DataInputStream;)Ljava/lang/String;

    move-result-object v1

    .line 114
    iput-object v1, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->name:Ljava/lang/String;

    .line 115
    sget-object v2, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v3, " grammar "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 116
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readShort()S

    move-result v0

    .line 117
    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "num rules = "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 118
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->readRules(Ljava/io/DataInputStream;I)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->rules:Ljava/util/List;

    return-void

    .line 109
    :cond_0
    new-instance p1, Ljava/io/IOException;

    const-string v0, "not a serialized grammar file"

    invoke-direct {p1, v0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method protected readRule(Ljava/io/DataInputStream;)Lgroovyjarjarantlr4/runtime/SerializedGrammar$Rule;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 131
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    move-result v0

    const/16 v1, 0x52

    if-ne v0, v1, :cond_1

    .line 133
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->readString(Ljava/io/DataInputStream;)Ljava/lang/String;

    move-result-object v0

    .line 134
    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "rule: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 135
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    .line 136
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->readBlock(Ljava/io/DataInputStream;)Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;

    move-result-object v1

    .line 137
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    move-result p1

    const/16 v2, 0x2e

    if-ne p1, v2, :cond_0

    .line 139
    new-instance p1, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Rule;

    invoke-direct {p1, p0, v0, v1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Rule;-><init>(Lgroovyjarjarantlr4/runtime/SerializedGrammar;Ljava/lang/String;Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;)V

    return-object p1

    .line 138
    :cond_0
    new-instance p1, Ljava/io/IOException;

    const-string v0, "missing . on end of rule"

    invoke-direct {p1, v0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 132
    :cond_1
    new-instance p1, Ljava/io/IOException;

    const-string v0, "missing R on start of rule"

    invoke-direct {p1, v0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method protected readRules(Ljava/io/DataInputStream;I)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/io/DataInputStream;",
            "I)",
            "Ljava/util/List<",
            "+",
            "Lgroovyjarjarantlr4/runtime/SerializedGrammar$Rule;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 122
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    :goto_0
    if-ge v1, p2, :cond_0

    .line 124
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->readRule(Ljava/io/DataInputStream;)Lgroovyjarjarantlr4/runtime/SerializedGrammar$Rule;

    move-result-object v2

    .line 125
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method protected readString(Ljava/io/DataInputStream;)Ljava/lang/String;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 193
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    move-result v0

    .line 194
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    :goto_0
    const/16 v2, 0x3b

    if-eq v0, v2, :cond_0

    int-to-char v0, v0

    .line 196
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 197
    invoke-virtual {p1}, Ljava/io/DataInputStream;->readByte()B

    move-result v0

    goto :goto_0

    .line 199
    :cond_0
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 204
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 205
    iget-char v1, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->type:C

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " grammar "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->name:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 206
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar;->rules:Ljava/util/List;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 207
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
