.class public Lgroovyjarjarantlr4/runtime/MissingTokenException;
.super Lgroovyjarjarantlr4/runtime/MismatchedTokenException;
.source "MissingTokenException.java"


# instance fields
.field public inserted:Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 36
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;-><init>()V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/IntStream;Ljava/lang/Object;)V
    .locals 0

    .line 39
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr4/runtime/MismatchedTokenException;-><init>(ILgroovyjarjarantlr4/runtime/IntStream;)V

    .line 40
    iput-object p3, p0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->inserted:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public getMissingType()I
    .locals 1

    .line 44
    iget v0, p0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->expecting:I

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 49
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->inserted:Ljava/lang/Object;

    const-string v1, ")"

    if-eqz v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->token:Lgroovyjarjarantlr4/runtime/Token;

    if-eqz v0, :cond_0

    .line 50
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "MissingTokenException(inserted "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->inserted:Ljava/lang/Object;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " at "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 52
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->token:Lgroovyjarjarantlr4/runtime/Token;

    if-eqz v0, :cond_1

    .line 53
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "MissingTokenException(at "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/MissingTokenException;->token:Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v2}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_1
    const-string v0, "MissingTokenException"

    return-object v0
.end method
