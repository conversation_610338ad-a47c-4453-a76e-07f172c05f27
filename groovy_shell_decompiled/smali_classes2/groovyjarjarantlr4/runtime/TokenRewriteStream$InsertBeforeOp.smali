.class Lgroovyjarjarantlr4/runtime/TokenRewriteStream$InsertBeforeOp;
.super Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;
.source "TokenRewriteStream.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/TokenRewriteStream;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = "InsertBeforeOp"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/TokenRewriteStream;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/TokenRewriteStream;ILjava/lang/Object;)V
    .locals 0

    .line 122
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$InsertBeforeOp;->this$0:Lgroovyjarjarantlr4/runtime/TokenRewriteStream;

    .line 123
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$RewriteOperation;-><init>(Lgroovyjarjarantlr4/runtime/TokenRewriteStream;ILjava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public execute(Ljava/lang/StringBuffer;)I
    .locals 2

    .line 127
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$InsertBeforeOp;->text:Ljava/lang/Object;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    .line 128
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$InsertBeforeOp;->this$0:Lgroovyjarjarantlr4/runtime/TokenRewriteStream;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream;->tokens:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$InsertBeforeOp;->index:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getType()I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    .line 129
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$InsertBeforeOp;->this$0:Lgroovyjarjarantlr4/runtime/TokenRewriteStream;

    iget-object v0, v0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream;->tokens:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$InsertBeforeOp;->index:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/Token;

    invoke-interface {v0}, Lgroovyjarjarantlr4/runtime/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 131
    :cond_0
    iget p1, p0, Lgroovyjarjarantlr4/runtime/TokenRewriteStream$InsertBeforeOp;->index:I

    add-int/lit8 p1, p1, 0x1

    return p1
.end method
