.class public Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;
.super Lgroovyjarjarantlr4/runtime/RecognitionException;
.source "MismatchedTreeNodeException.java"


# instance fields
.field public expecting:I


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 37
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/RecognitionException;-><init>()V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarantlr4/runtime/tree/TreeNodeStream;)V
    .locals 0

    .line 41
    invoke-direct {p0, p2}, Lgroovyjarjarantlr4/runtime/RecognitionException;-><init>(Lgroovyjarjarantlr4/runtime/IntStream;)V

    .line 42
    iput p1, p0, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;->expecting:I

    return-void
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 2

    .line 47
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "MismatchedTreeNodeException("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;->getUnexpectedType()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "!="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarantlr4/runtime/MismatchedTreeNodeException;->expecting:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
