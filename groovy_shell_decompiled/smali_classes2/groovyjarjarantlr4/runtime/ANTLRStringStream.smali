.class public Lgroovyjarjarantlr4/runtime/ANTLRStringStream;
.super Ljava/lang/Object;
.source "ANTLRStringStream.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/CharStream;


# instance fields
.field protected charPositionInLine:I

.field protected data:[C

.field protected lastMarker:I

.field protected line:I

.field protected markDepth:I

.field protected markers:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarantlr4/runtime/CharStreamState;",
            ">;"
        }
    .end annotation
.end field

.field protected n:I

.field public name:Ljava/lang/String;

.field protected p:I


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 69
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 45
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    const/4 v1, 0x1

    .line 48
    iput v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->line:I

    .line 51
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->charPositionInLine:I

    .line 54
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markDepth:I

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 1

    .line 74
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;-><init>()V

    .line 75
    invoke-virtual {p1}, Ljava/lang/String;->toCharArray()[C

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->data:[C

    .line 76
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p1

    iput p1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->n:I

    return-void
.end method

.method public constructor <init>([CI)V
    .locals 0

    .line 81
    invoke-direct {p0}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;-><init>()V

    .line 82
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->data:[C

    .line 83
    iput p2, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->n:I

    return-void
.end method


# virtual methods
.method public LA(I)I
    .locals 4

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    const/4 v0, -0x1

    if-gez p1, :cond_1

    add-int/lit8 p1, p1, 0x1

    .line 122
    iget v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    add-int/2addr v1, p1

    add-int/lit8 v1, v1, -0x1

    if-gez v1, :cond_1

    return v0

    .line 127
    :cond_1
    iget v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    add-int v2, v1, p1

    add-int/lit8 v2, v2, -0x1

    iget v3, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->n:I

    if-lt v2, v3, :cond_2

    return v0

    .line 133
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->data:[C

    add-int/2addr v1, p1

    add-int/lit8 v1, v1, -0x1

    aget-char p1, v0, v1

    return p1
.end method

.method public LT(I)I
    .locals 0

    .line 138
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->LA(I)I

    move-result p1

    return p1
.end method

.method public consume()V
    .locals 3

    .line 100
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    iget v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->n:I

    if-ge v0, v1, :cond_1

    .line 101
    iget v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->charPositionInLine:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->charPositionInLine:I

    .line 102
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->data:[C

    aget-char v1, v1, v0

    const/16 v2, 0xa

    if-ne v1, v2, :cond_0

    .line 107
    iget v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->line:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->line:I

    const/4 v1, 0x0

    .line 108
    iput v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->charPositionInLine:I

    :cond_0
    add-int/lit8 v0, v0, 0x1

    .line 110
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    :cond_1
    return-void
.end method

.method public getCharPositionInLine()I
    .locals 1

    .line 227
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->charPositionInLine:I

    return v0
.end method

.method public getLine()I
    .locals 1

    .line 222
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->line:I

    return v0
.end method

.method public getSourceName()Ljava/lang/String;
    .locals 1

    .line 242
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->name:Ljava/lang/String;

    return-object v0
.end method

.method public index()I
    .locals 1

    .line 147
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    return v0
.end method

.method public mark()I
    .locals 2

    .line 157
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markers:Ljava/util/List;

    if-nez v0, :cond_0

    .line 158
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markers:Ljava/util/List;

    const/4 v1, 0x0

    .line 159
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 161
    :cond_0
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markDepth:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markDepth:I

    .line 163
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markers:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-lt v0, v1, :cond_1

    .line 164
    new-instance v0, Lgroovyjarjarantlr4/runtime/CharStreamState;

    invoke-direct {v0}, Lgroovyjarjarantlr4/runtime/CharStreamState;-><init>()V

    .line 165
    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markers:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 168
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markers:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markDepth:I

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/CharStreamState;

    .line 170
    :goto_0
    iget v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    iput v1, v0, Lgroovyjarjarantlr4/runtime/CharStreamState;->p:I

    .line 171
    iget v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->line:I

    iput v1, v0, Lgroovyjarjarantlr4/runtime/CharStreamState;->line:I

    .line 172
    iget v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->charPositionInLine:I

    iput v1, v0, Lgroovyjarjarantlr4/runtime/CharStreamState;->charPositionInLine:I

    .line 173
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markDepth:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->lastMarker:I

    return v0
.end method

.method public release(I)V
    .locals 0

    .line 195
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markDepth:I

    add-int/lit8 p1, p1, -0x1

    .line 197
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markDepth:I

    return-void
.end method

.method public reset()V
    .locals 2

    const/4 v0, 0x0

    .line 91
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    const/4 v1, 0x1

    .line 92
    iput v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->line:I

    .line 93
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->charPositionInLine:I

    .line 94
    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markDepth:I

    return-void
.end method

.method public rewind()V
    .locals 1

    .line 189
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->lastMarker:I

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->rewind(I)V

    return-void
.end method

.method public rewind(I)V
    .locals 2

    .line 179
    iget-object v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->markers:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr4/runtime/CharStreamState;

    .line 181
    iget v1, v0, Lgroovyjarjarantlr4/runtime/CharStreamState;->p:I

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->seek(I)V

    .line 182
    iget v1, v0, Lgroovyjarjarantlr4/runtime/CharStreamState;->line:I

    iput v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->line:I

    .line 183
    iget v0, v0, Lgroovyjarjarantlr4/runtime/CharStreamState;->charPositionInLine:I

    iput v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->charPositionInLine:I

    .line 184
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->release(I)V

    return-void
.end method

.method public seek(I)V
    .locals 1

    .line 205
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    if-gt p1, v0, :cond_0

    .line 206
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    return-void

    .line 210
    :cond_0
    :goto_0
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->p:I

    if-ge v0, p1, :cond_1

    .line 211
    invoke-virtual {p0}, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->consume()V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public setCharPositionInLine(I)V
    .locals 0

    .line 237
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->charPositionInLine:I

    return-void
.end method

.method public setLine(I)V
    .locals 0

    .line 232
    iput p1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->line:I

    return-void
.end method

.method public size()I
    .locals 1

    .line 152
    iget v0, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->n:I

    return v0
.end method

.method public substring(II)Ljava/lang/String;
    .locals 2

    .line 217
    new-instance v0, Ljava/lang/String;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->data:[C

    sub-int/2addr p2, p1

    add-int/lit8 p2, p2, 0x1

    invoke-direct {v0, v1, p1, p2}, Ljava/lang/String;-><init>([CII)V

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 246
    new-instance v0, Ljava/lang/String;

    iget-object v1, p0, Lgroovyjarjarantlr4/runtime/ANTLRStringStream;->data:[C

    invoke-direct {v0, v1}, Ljava/lang/String;-><init>([C)V

    return-object v0
.end method
