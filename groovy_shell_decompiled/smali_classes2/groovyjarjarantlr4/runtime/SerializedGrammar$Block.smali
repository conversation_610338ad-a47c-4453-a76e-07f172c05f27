.class public Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;
.super Lgroovyjarjarantlr4/runtime/SerializedGrammar$Node;
.source "SerializedGrammar.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovyjarjarantlr4/runtime/SerializedGrammar;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "Block"
.end annotation


# instance fields
.field alts:[Ljava/util/List;

.field final synthetic this$0:Lgroovyjarjarantlr4/runtime/SerializedGrammar;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr4/runtime/SerializedGrammar;[Ljava/util/List;)V
    .locals 0

    .line 67
    iput-object p1, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;->this$0:Lgroovyjarjarantlr4/runtime/SerializedGrammar;

    invoke-direct {p0, p1}, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Node;-><init>(Lgroovyjarjarantlr4/runtime/SerializedGrammar;)V

    .line 68
    iput-object p2, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;->alts:[Ljava/util/List;

    return-void
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 4

    .line 72
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "("

    .line 73
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    .line 74
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr4/runtime/SerializedGrammar$Block;->alts:[Ljava/util/List;

    array-length v3, v2

    if-ge v1, v3, :cond_1

    .line 75
    aget-object v2, v2, v1

    if-lez v1, :cond_0

    const-string v3, "|"

    .line 76
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 77
    :cond_0
    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    const-string v1, ")"

    .line 79
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 80
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
