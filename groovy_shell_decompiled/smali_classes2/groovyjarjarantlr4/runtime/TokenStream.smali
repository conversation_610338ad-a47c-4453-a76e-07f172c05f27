.class public interface abstract Lgroovyjarjarantlr4/runtime/TokenStream;
.super Ljava/lang/Object;
.source "TokenStream.java"

# interfaces
.implements Lgroovyjarjarantlr4/runtime/IntStream;


# virtual methods
.method public abstract LT(I)Lgroovyjarjarantlr4/runtime/Token;
.end method

.method public abstract get(I)Lgroovyjarjarantlr4/runtime/Token;
.end method

.method public abstract getTokenSource()Lgroovyjarjarantlr4/runtime/TokenSource;
.end method

.method public abstract range()I
.end method

.method public abstract toString(II)Ljava/lang/String;
.end method

.method public abstract toString(Lgroovyjarjarantlr4/runtime/Token;Lgroovyjarjarantlr4/runtime/Token;)Ljava/lang/String;
.end method
