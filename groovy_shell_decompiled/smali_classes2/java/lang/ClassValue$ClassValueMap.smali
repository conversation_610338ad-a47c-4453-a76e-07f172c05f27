.class Ljava/lang/ClassValue$ClassValueMap;
.super Ljava/util/WeakHashMap;
.source "ClassValue.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ljava/lang/ClassValue;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "ClassValueMap"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/util/WeakHashMap<",
        "Ljava/lang/ClassValue$Identity;",
        "Ljava/lang/ClassValue$Entry<",
        "*>;>;"
    }
.end annotation


# static fields
.field static final synthetic $assertionsDisabled:Z = false

.field private static final CACHE_LOAD_LIMIT:I = 0x43

.field private static final INITIAL_ENTRIES:I = 0x20

.field private static final PROBE_LIMIT:I = 0x6


# instance fields
.field private cacheArray:[Ljava/lang/ClassValue$Entry;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Ljava/lang/ClassValue$Entry<",
            "*>;"
        }
    .end annotation
.end field

.field private cacheLoad:I

.field private cacheLoadLimit:I

.field private final type:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 289
    const-class v0, Ljava/lang/ClassValue;

    return-void
.end method

.method constructor <init>(Ljava/lang/Class;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)V"
        }
    .end annotation

    .line 301
    invoke-direct {p0}, Ljava/util/WeakHashMap;-><init>()V

    .line 302
    iput-object p1, p0, Ljava/lang/ClassValue$ClassValueMap;->type:Ljava/lang/Class;

    const/16 p1, 0x20

    .line 303
    invoke-direct {p0, p1}, Ljava/lang/ClassValue$ClassValueMap;->sizeCache(I)V

    return-void
.end method

.method private addToCache(Ljava/lang/ClassValue$Entry;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/ClassValue$Entry<",
            "TT;>;)V"
        }
    .end annotation

    .line 571
    invoke-virtual {p1}, Ljava/lang/ClassValue$Entry;->classValueOrNull()Ljava/lang/ClassValue;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 573
    invoke-direct {p0, v0, p1}, Ljava/lang/ClassValue$ClassValueMap;->addToCache(Ljava/lang/ClassValue;Ljava/lang/ClassValue$Entry;)V

    :cond_0
    return-void
.end method

.method private addToCache(Ljava/lang/ClassValue;Ljava/lang/ClassValue$Entry;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/ClassValue<",
            "TT;>;",
            "Ljava/lang/ClassValue$Entry<",
            "TT;>;)V"
        }
    .end annotation

    .line 579
    invoke-virtual {p0}, Ljava/lang/ClassValue$ClassValueMap;->getCache()[Ljava/lang/ClassValue$Entry;

    move-result-object v0

    .line 580
    array-length v1, v0

    const/4 v2, 0x1

    sub-int/2addr v1, v2

    .line 581
    iget p1, p1, Ljava/lang/ClassValue;->hashCodeForCache:I

    and-int/2addr p1, v1

    const/4 v3, 0x0

    .line 582
    invoke-direct {p0, v0, p1, p2, v3}, Ljava/lang/ClassValue$ClassValueMap;->placeInCache([Ljava/lang/ClassValue$Entry;ILjava/lang/ClassValue$Entry;Z)Ljava/lang/ClassValue$Entry;

    move-result-object p2

    if-nez p2, :cond_0

    return-void

    .line 586
    :cond_0
    invoke-static {v0, p1, p2}, Ljava/lang/ClassValue$ClassValueMap;->entryDislocation([Ljava/lang/ClassValue$Entry;ILjava/lang/ClassValue$Entry;)I

    move-result v3

    sub-int/2addr p1, v3

    move v3, p1

    :goto_0
    add-int/lit8 v4, p1, 0x6

    if-ge v3, v4, :cond_2

    and-int v4, v3, v1

    .line 589
    invoke-direct {p0, v0, v4, p2, v2}, Ljava/lang/ClassValue$ClassValueMap;->placeInCache([Ljava/lang/ClassValue$Entry;ILjava/lang/ClassValue$Entry;Z)Ljava/lang/ClassValue$Entry;

    move-result-object v4

    if-nez v4, :cond_1

    return-void

    :cond_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method private checkCacheLoad()V
    .locals 2

    .line 473
    iget v0, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoad:I

    iget v1, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoadLimit:I

    if-lt v0, v1, :cond_0

    .line 474
    invoke-direct {p0}, Ljava/lang/ClassValue$ClassValueMap;->reduceCacheLoad()V

    :cond_0
    return-void
.end method

.method private static entryDislocation([Ljava/lang/ClassValue$Entry;ILjava/lang/ClassValue$Entry;)I
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/ClassValue$Entry<",
            "*>;I",
            "Ljava/lang/ClassValue$Entry<",
            "*>;)I"
        }
    .end annotation

    .line 455
    invoke-virtual {p2}, Ljava/lang/ClassValue$Entry;->classValueOrNull()Ljava/lang/ClassValue;

    move-result-object p2

    if-nez p2, :cond_0

    const/4 p0, 0x0

    return p0

    .line 457
    :cond_0
    array-length p0, p0

    add-int/lit8 p0, p0, -0x1

    .line 458
    iget p2, p2, Ljava/lang/ClassValue;->hashCodeForCache:I

    sub-int/2addr p1, p2

    and-int/2addr p0, p1

    return p0
.end method

.method private findReplacement([Ljava/lang/ClassValue$Entry;I)Ljava/lang/ClassValue$Entry;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/ClassValue$Entry<",
            "*>;I)",
            "Ljava/lang/ClassValue$Entry<",
            "*>;"
        }
    .end annotation

    .line 523
    array-length v0, p1

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    add-int/lit8 v2, p2, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, -0x1

    move v6, v3

    move-object v7, v4

    :goto_0
    add-int/lit8 v8, p2, 0x6

    if-ge v2, v8, :cond_5

    and-int v8, v2, v0

    .line 525
    aget-object v8, p1, v8

    if-nez v8, :cond_0

    goto :goto_3

    .line 527
    :cond_0
    invoke-virtual {v8}, Ljava/lang/ClassValue$Entry;->isLive()Z

    move-result v9

    if-nez v9, :cond_1

    goto :goto_2

    .line 528
    :cond_1
    invoke-static {p1, v2, v8}, Ljava/lang/ClassValue$ClassValueMap;->entryDislocation([Ljava/lang/ClassValue$Entry;ILjava/lang/ClassValue$Entry;)I

    move-result v9

    if-nez v9, :cond_2

    goto :goto_2

    :cond_2
    sub-int v9, v2, v9

    if-gt v9, p2, :cond_4

    if-ne v9, p2, :cond_3

    move v5, v1

    move v6, v2

    :goto_1
    move-object v7, v8

    goto :goto_2

    :cond_3
    if-gtz v5, :cond_4

    move v6, v2

    move v5, v3

    goto :goto_1

    :cond_4
    :goto_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_5
    :goto_3
    if-ltz v5, :cond_7

    add-int/lit8 p2, v6, 0x1

    and-int/2addr p2, v0

    .line 547
    aget-object p2, p1, p2

    if-eqz p2, :cond_6

    and-int p2, v6, v0

    .line 549
    sget-object v0, Ljava/lang/ClassValue$Entry;->DEAD_ENTRY:Ljava/lang/ClassValue$Entry;

    aput-object v0, p1, p2

    goto :goto_4

    :cond_6
    and-int p2, v6, v0

    .line 551
    aput-object v4, p1, p2

    .line 552
    iget p1, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoad:I

    sub-int/2addr p1, v1

    iput p1, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoad:I

    :cond_7
    :goto_4
    return-object v7
.end method

.method static loadFromCache([Ljava/lang/ClassValue$Entry;I)Ljava/lang/ClassValue$Entry;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/ClassValue$Entry<",
            "*>;I)",
            "Ljava/lang/ClassValue$Entry<",
            "*>;"
        }
    .end annotation

    .line 408
    array-length v0, p0

    add-int/lit8 v0, v0, -0x1

    and-int/2addr p1, v0

    aget-object p0, p0, p1

    return-object p0
.end method

.method private overwrittenEntry(Ljava/lang/ClassValue$Entry;)Ljava/lang/ClassValue$Entry;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/ClassValue$Entry<",
            "TT;>;)",
            "Ljava/lang/ClassValue$Entry<",
            "TT;>;"
        }
    .end annotation

    if-nez p1, :cond_0

    .line 616
    iget p1, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoad:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoad:I

    goto :goto_0

    .line 617
    :cond_0
    invoke-virtual {p1}, Ljava/lang/ClassValue$Entry;->isLive()Z

    move-result v0

    if-eqz v0, :cond_1

    return-object p1

    :cond_1
    :goto_0
    const/4 p1, 0x0

    return-object p1
.end method

.method private placeInCache([Ljava/lang/ClassValue$Entry;ILjava/lang/ClassValue$Entry;Z)Ljava/lang/ClassValue$Entry;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/ClassValue$Entry<",
            "*>;I",
            "Ljava/lang/ClassValue$Entry<",
            "*>;Z)",
            "Ljava/lang/ClassValue$Entry<",
            "*>;"
        }
    .end annotation

    .line 600
    aget-object v0, p1, p2

    invoke-direct {p0, v0}, Ljava/lang/ClassValue$ClassValueMap;->overwrittenEntry(Ljava/lang/ClassValue$Entry;)Ljava/lang/ClassValue$Entry;

    move-result-object v0

    if-eqz p4, :cond_0

    if-eqz v0, :cond_0

    return-object p3

    .line 605
    :cond_0
    aput-object p3, p1, p2

    return-object v0
.end method

.method static probeBackupLocations([Ljava/lang/ClassValue$Entry;Ljava/lang/ClassValue;)Ljava/lang/ClassValue$Entry;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">([",
            "Ljava/lang/ClassValue$Entry<",
            "*>;",
            "Ljava/lang/ClassValue<",
            "TT;>;)",
            "Ljava/lang/ClassValue$Entry<",
            "TT;>;"
        }
    .end annotation

    .line 421
    array-length v0, p0

    add-int/lit8 v0, v0, -0x1

    .line 422
    iget v1, p1, Ljava/lang/ClassValue;->hashCodeForCache:I

    and-int/2addr v1, v0

    .line 423
    aget-object v2, p0, v1

    const/4 v3, 0x0

    if-nez v2, :cond_0

    return-object v3

    :cond_0
    const/4 v4, -0x1

    add-int/lit8 v5, v1, 0x1

    :goto_0
    add-int/lit8 v6, v1, 0x6

    if-ge v5, v6, :cond_6

    and-int v6, v5, v0

    .line 430
    aget-object v7, p0, v6

    if-nez v7, :cond_1

    goto :goto_3

    .line 434
    :cond_1
    invoke-virtual {p1, v7}, Ljava/lang/ClassValue;->match(Ljava/lang/ClassValue$Entry;)Z

    move-result v8

    if-eqz v8, :cond_4

    .line 436
    aput-object v7, p0, v1

    if-ltz v4, :cond_2

    .line 438
    sget-object v1, Ljava/lang/ClassValue$Entry;->DEAD_ENTRY:Ljava/lang/ClassValue$Entry;

    aput-object v1, p0, v6

    goto :goto_1

    :cond_2
    move v4, v5

    :goto_1
    and-int/2addr v0, v4

    .line 442
    invoke-static {p0, v4, v2}, Ljava/lang/ClassValue$ClassValueMap;->entryDislocation([Ljava/lang/ClassValue$Entry;ILjava/lang/ClassValue$Entry;)I

    move-result v1

    const/4 v3, 0x6

    if-ge v1, v3, :cond_3

    goto :goto_2

    .line 444
    :cond_3
    sget-object v2, Ljava/lang/ClassValue$Entry;->DEAD_ENTRY:Ljava/lang/ClassValue$Entry;

    :goto_2
    aput-object v2, p0, v0

    .line 445
    invoke-virtual {p1, v7}, Ljava/lang/ClassValue;->castEntry(Ljava/lang/ClassValue$Entry;)Ljava/lang/ClassValue$Entry;

    move-result-object p0

    return-object p0

    .line 448
    :cond_4
    invoke-virtual {v7}, Ljava/lang/ClassValue$Entry;->isLive()Z

    move-result v6

    if-nez v6, :cond_5

    if-gez v4, :cond_5

    move v4, v5

    :cond_5
    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    :cond_6
    :goto_3
    return-object v3
.end method

.method static probeHomeLocation([Ljava/lang/ClassValue$Entry;Ljava/lang/ClassValue;)Ljava/lang/ClassValue$Entry;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">([",
            "Ljava/lang/ClassValue$Entry<",
            "*>;",
            "Ljava/lang/ClassValue<",
            "TT;>;)",
            "Ljava/lang/ClassValue$Entry<",
            "TT;>;"
        }
    .end annotation

    .line 414
    iget v0, p1, Ljava/lang/ClassValue;->hashCodeForCache:I

    invoke-static {p0, v0}, Ljava/lang/ClassValue$ClassValueMap;->loadFromCache([Ljava/lang/ClassValue$Entry;I)Ljava/lang/ClassValue$Entry;

    move-result-object p0

    invoke-virtual {p1, p0}, Ljava/lang/ClassValue;->castEntry(Ljava/lang/ClassValue$Entry;)Ljava/lang/ClassValue$Entry;

    move-result-object p0

    return-object p0
.end method

.method private reduceCacheLoad()V
    .locals 5

    .line 479
    invoke-direct {p0}, Ljava/lang/ClassValue$ClassValueMap;->removeStaleEntries()V

    .line 480
    iget v0, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoad:I

    iget v1, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoadLimit:I

    if-ge v0, v1, :cond_0

    return-void

    .line 482
    :cond_0
    invoke-virtual {p0}, Ljava/lang/ClassValue$ClassValueMap;->getCache()[Ljava/lang/ClassValue$Entry;

    move-result-object v0

    .line 483
    array-length v1, v0

    const v2, 0x3fffffff    # 1.9999999f

    if-le v1, v2, :cond_1

    return-void

    .line 485
    :cond_1
    array-length v1, v0

    mul-int/lit8 v1, v1, 0x2

    invoke-direct {p0, v1}, Ljava/lang/ClassValue$ClassValueMap;->sizeCache(I)V

    .line 486
    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_3

    aget-object v3, v0, v2

    if-eqz v3, :cond_2

    .line 487
    invoke-virtual {v3}, Ljava/lang/ClassValue$Entry;->isLive()Z

    move-result v4

    if-eqz v4, :cond_2

    .line 488
    invoke-direct {p0, v3}, Ljava/lang/ClassValue$ClassValueMap;->addToCache(Ljava/lang/ClassValue$Entry;)V

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method private removeStaleEntries()V
    .locals 3

    .line 565
    invoke-virtual {p0}, Ljava/lang/ClassValue$ClassValueMap;->getCache()[Ljava/lang/ClassValue$Entry;

    move-result-object v0

    .line 566
    array-length v1, v0

    add-int/lit8 v1, v1, 0x6

    add-int/lit8 v1, v1, -0x1

    const/4 v2, 0x0

    invoke-direct {p0, v0, v2, v1}, Ljava/lang/ClassValue$ClassValueMap;->removeStaleEntries([Ljava/lang/ClassValue$Entry;II)V

    return-void
.end method

.method private removeStaleEntries(Ljava/lang/ClassValue;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ClassValue<",
            "*>;)V"
        }
    .end annotation

    .line 560
    invoke-virtual {p0}, Ljava/lang/ClassValue$ClassValueMap;->getCache()[Ljava/lang/ClassValue$Entry;

    move-result-object v0

    iget p1, p1, Ljava/lang/ClassValue;->hashCodeForCache:I

    const/4 v1, 0x6

    invoke-direct {p0, v0, p1, v1}, Ljava/lang/ClassValue$ClassValueMap;->removeStaleEntries([Ljava/lang/ClassValue$Entry;II)V

    return-void
.end method

.method private removeStaleEntries([Ljava/lang/ClassValue$Entry;II)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/ClassValue$Entry<",
            "*>;II)V"
        }
    .end annotation

    .line 498
    array-length v0, p1

    add-int/lit8 v0, v0, -0x1

    const/4 v1, 0x0

    move v2, p2

    move v3, v1

    :goto_0
    add-int v4, p2, p3

    if-ge v2, v4, :cond_2

    and-int v4, v2, v0

    .line 501
    aget-object v5, p1, v4

    if-eqz v5, :cond_1

    .line 502
    invoke-virtual {v5}, Ljava/lang/ClassValue$Entry;->isLive()Z

    move-result v5

    if-eqz v5, :cond_0

    goto :goto_1

    .line 507
    :cond_0
    invoke-direct {p0, p1, v2}, Ljava/lang/ClassValue$ClassValueMap;->findReplacement([Ljava/lang/ClassValue$Entry;I)Ljava/lang/ClassValue$Entry;

    move-result-object v5

    .line 509
    aput-object v5, p1, v4

    if-nez v5, :cond_1

    add-int/lit8 v3, v3, 0x1

    :cond_1
    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 512
    :cond_2
    iget p1, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoad:I

    sub-int/2addr p1, v3

    invoke-static {v1, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoad:I

    return-void
.end method

.method private sizeCache(I)V
    .locals 4

    const/4 v0, 0x0

    .line 466
    iput v0, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoad:I

    int-to-double v0, p1

    const-wide v2, 0x4050c00000000000L    # 67.0

    mul-double/2addr v0, v2

    const-wide/high16 v2, 0x4059000000000000L    # 100.0

    div-double/2addr v0, v2

    double-to-int v0, v0

    .line 467
    iput v0, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheLoadLimit:I

    .line 468
    new-array p1, p1, [Ljava/lang/ClassValue$Entry;

    iput-object p1, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheArray:[Ljava/lang/ClassValue$Entry;

    return-void
.end method


# virtual methods
.method declared-synchronized changeEntry(Ljava/lang/ClassValue;Ljava/lang/Object;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/ClassValue<",
            "TT;>;TT;)V"
        }
    .end annotation

    monitor-enter p0

    .line 384
    :try_start_0
    iget-object v0, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, v0}, Ljava/lang/ClassValue$ClassValueMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ClassValue$Entry;

    .line 385
    invoke-virtual {p1}, Ljava/lang/ClassValue;->version()Ljava/lang/ClassValue$Version;

    move-result-object v1

    if-eqz v0, :cond_1

    .line 387
    invoke-virtual {v0}, Ljava/lang/ClassValue$Entry;->version()Ljava/lang/ClassValue$Version;

    move-result-object v2

    if-ne v2, v1, :cond_0

    invoke-virtual {v0}, Ljava/lang/ClassValue$Entry;->value()Ljava/lang/Object;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-ne v0, p2, :cond_0

    .line 389
    monitor-exit p0

    return-void

    .line 390
    :cond_0
    :try_start_1
    invoke-virtual {p1}, Ljava/lang/ClassValue;->bumpVersion()V

    .line 391
    invoke-direct {p0, p1}, Ljava/lang/ClassValue$ClassValueMap;->removeStaleEntries(Ljava/lang/ClassValue;)V

    .line 393
    :cond_1
    invoke-static {v1, p2}, Ljava/lang/ClassValue;->makeEntry(Ljava/lang/ClassValue$Version;Ljava/lang/Object;)Ljava/lang/ClassValue$Entry;

    move-result-object p2

    .line 394
    iget-object v0, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, v0, p2}, Ljava/lang/ClassValue$ClassValueMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 396
    invoke-direct {p0}, Ljava/lang/ClassValue$ClassValueMap;->checkCacheLoad()V

    .line 397
    invoke-direct {p0, p1, p2}, Ljava/lang/ClassValue$ClassValueMap;->addToCache(Ljava/lang/ClassValue;Ljava/lang/ClassValue$Entry;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 398
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method declared-synchronized finishEntry(Ljava/lang/ClassValue;Ljava/lang/ClassValue$Entry;)Ljava/lang/ClassValue$Entry;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/ClassValue<",
            "TT;>;",
            "Ljava/lang/ClassValue$Entry<",
            "TT;>;)",
            "Ljava/lang/ClassValue$Entry<",
            "TT;>;"
        }
    .end annotation

    monitor-enter p0

    .line 347
    :try_start_0
    iget-object v0, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, v0}, Ljava/lang/ClassValue$ClassValueMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ClassValue$Entry;

    const/4 v1, 0x0

    if-ne p2, v0, :cond_0

    .line 351
    iget-object p1, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, p1}, Ljava/lang/ClassValue$ClassValueMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 352
    monitor-exit p0

    return-object v1

    :cond_0
    if-eqz v0, :cond_2

    .line 353
    :try_start_1
    invoke-virtual {v0}, Ljava/lang/ClassValue$Entry;->isPromise()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-virtual {v0}, Ljava/lang/ClassValue$Entry;->version()Ljava/lang/ClassValue$Version;

    move-result-object v0

    invoke-virtual {p2}, Ljava/lang/ClassValue$Entry;->version()Ljava/lang/ClassValue$Version;

    move-result-object v2

    if-ne v0, v2, :cond_2

    .line 356
    invoke-virtual {p1}, Ljava/lang/ClassValue;->version()Ljava/lang/ClassValue$Version;

    move-result-object v0

    .line 357
    invoke-virtual {p2}, Ljava/lang/ClassValue$Entry;->version()Ljava/lang/ClassValue$Version;

    move-result-object v1

    if-eq v1, v0, :cond_1

    .line 358
    invoke-virtual {p2, v0}, Ljava/lang/ClassValue$Entry;->refreshVersion(Ljava/lang/ClassValue$Version;)Ljava/lang/ClassValue$Entry;

    move-result-object p2

    .line 359
    :cond_1
    iget-object v0, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, v0, p2}, Ljava/lang/ClassValue$ClassValueMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 361
    invoke-direct {p0}, Ljava/lang/ClassValue$ClassValueMap;->checkCacheLoad()V

    .line 362
    invoke-direct {p0, p1, p2}, Ljava/lang/ClassValue$ClassValueMap;->addToCache(Ljava/lang/ClassValue;Ljava/lang/ClassValue$Entry;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 363
    monitor-exit p0

    return-object p2

    .line 366
    :cond_2
    monitor-exit p0

    return-object v1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method getCache()[Ljava/lang/ClassValue$Entry;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()[",
            "Ljava/lang/ClassValue$Entry<",
            "*>;"
        }
    .end annotation

    .line 306
    iget-object v0, p0, Ljava/lang/ClassValue$ClassValueMap;->cacheArray:[Ljava/lang/ClassValue$Entry;

    return-object v0
.end method

.method declared-synchronized removeEntry(Ljava/lang/ClassValue;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ClassValue<",
            "*>;)V"
        }
    .end annotation

    monitor-enter p0

    .line 374
    :try_start_0
    iget-object v0, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, v0}, Ljava/lang/ClassValue$ClassValueMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 375
    invoke-virtual {p1}, Ljava/lang/ClassValue;->bumpVersion()V

    .line 376
    invoke-direct {p0, p1}, Ljava/lang/ClassValue$ClassValueMap;->removeStaleEntries(Ljava/lang/ClassValue;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 378
    :cond_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method declared-synchronized startEntry(Ljava/lang/ClassValue;)Ljava/lang/ClassValue$Entry;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/ClassValue<",
            "TT;>;)",
            "Ljava/lang/ClassValue$Entry<",
            "TT;>;"
        }
    .end annotation

    monitor-enter p0

    .line 311
    :try_start_0
    iget-object v0, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, v0}, Ljava/lang/ClassValue$ClassValueMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ClassValue$Entry;

    .line 312
    invoke-virtual {p1}, Ljava/lang/ClassValue;->version()Ljava/lang/ClassValue$Version;

    move-result-object v1

    if-nez v0, :cond_0

    .line 314
    invoke-virtual {v1}, Ljava/lang/ClassValue$Version;->promise()Ljava/lang/ClassValue$Entry;

    move-result-object v0

    .line 317
    iget-object p1, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, p1, v0}, Ljava/lang/ClassValue$ClassValueMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 319
    monitor-exit p0

    return-object v0

    .line 320
    :cond_0
    :try_start_1
    invoke-virtual {v0}, Ljava/lang/ClassValue$Entry;->isPromise()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 323
    invoke-virtual {v0}, Ljava/lang/ClassValue$Entry;->version()Ljava/lang/ClassValue$Version;

    move-result-object v2

    if-eq v2, v1, :cond_1

    .line 324
    invoke-virtual {v1}, Ljava/lang/ClassValue$Version;->promise()Ljava/lang/ClassValue$Entry;

    move-result-object v0

    .line 325
    iget-object p1, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, p1, v0}, Ljava/lang/ClassValue$ClassValueMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 327
    :cond_1
    monitor-exit p0

    return-object v0

    .line 330
    :cond_2
    :try_start_2
    invoke-virtual {v0}, Ljava/lang/ClassValue$Entry;->version()Ljava/lang/ClassValue$Version;

    move-result-object v2

    if-eq v2, v1, :cond_3

    .line 333
    invoke-virtual {v0, v1}, Ljava/lang/ClassValue$Entry;->refreshVersion(Ljava/lang/ClassValue$Version;)Ljava/lang/ClassValue$Entry;

    move-result-object v0

    .line 334
    iget-object v1, p1, Ljava/lang/ClassValue;->identity:Ljava/lang/ClassValue$Identity;

    invoke-virtual {p0, v1, v0}, Ljava/lang/ClassValue$ClassValueMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 337
    :cond_3
    invoke-direct {p0}, Ljava/lang/ClassValue$ClassValueMap;->checkCacheLoad()V

    .line 338
    invoke-direct {p0, p1, v0}, Ljava/lang/ClassValue$ClassValueMap;->addToCache(Ljava/lang/ClassValue;Ljava/lang/ClassValue$Entry;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 339
    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method
