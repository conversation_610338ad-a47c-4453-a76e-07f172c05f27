.class public Lgroovyjarjarantlr/PythonCodeGenerator;
.super Lgroovyjarjarantlr/CodeGenerator;
.source "PythonCodeGenerator.java"


# static fields
.field protected static final NONUNIQUE:Ljava/lang/String;

.field public static final caseSizeThreshold:I = 0x7f

.field public static final initHeaderAction:Ljava/lang/String; = "__init__"

.field public static final mainHeaderAction:Ljava/lang/String; = "__main__"


# instance fields
.field astVarNumber:I

.field commonExtraArgs:Ljava/lang/String;

.field commonExtraParams:Ljava/lang/String;

.field commonLocalVars:Ljava/lang/String;

.field currentASTResult:Ljava/lang/String;

.field currentRule:Lgroovyjarjarantlr/RuleBlock;

.field declaredASTVariables:Ljava/util/Hashtable;

.field exceptionThrown:Ljava/lang/String;

.field protected genAST:Z

.field labeledElementASTType:Ljava/lang/String;

.field labeledElementInit:Ljava/lang/String;

.field labeledElementType:Ljava/lang/String;

.field lexerClassName:Ljava/lang/String;

.field lt1Value:Ljava/lang/String;

.field parserClassName:Ljava/lang/String;

.field protected saveText:Z

.field private semPreds:Lgroovyjarjarantlr/collections/impl/Vector;

.field protected syntacticPredLevel:I

.field throwNoViable:Ljava/lang/String;

.field treeVariableMap:Ljava/util/Hashtable;

.field treeWalkerClassName:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 70
    new-instance v0, Ljava/lang/String;

    invoke-direct {v0}, Ljava/lang/String;-><init>()V

    sput-object v0, Lgroovyjarjarantlr/PythonCodeGenerator;->NONUNIQUE:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 90
    invoke-direct {p0}, Lgroovyjarjarantlr/CodeGenerator;-><init>()V

    const/4 v0, 0x0

    .line 22
    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    .line 25
    iput-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    .line 28
    iput-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    .line 59
    new-instance v0, Ljava/util/Hashtable;

    invoke-direct {v0}, Ljava/util/Hashtable;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeVariableMap:Ljava/util/Hashtable;

    .line 64
    new-instance v0, Ljava/util/Hashtable;

    invoke-direct {v0}, Ljava/util/Hashtable;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->declaredASTVariables:Ljava/util/Hashtable;

    const/4 v0, 0x1

    .line 67
    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->astVarNumber:I

    .line 91
    new-instance v1, Lgroovyjarjarantlr/PythonCharFormatter;

    invoke-direct {v1}, Lgroovyjarjarantlr/PythonCharFormatter;-><init>()V

    iput-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->charFormatter:Lgroovyjarjarantlr/CharFormatter;

    .line 92
    iput-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    return-void
.end method

.method private GenRuleInvocation(Lgroovyjarjarantlr/RuleRefElement;)V
    .locals 7

    .line 2942
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "self."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, "("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2945
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    const-string v1, ", "

    if-eqz v0, :cond_2

    .line 2947
    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    const-string v0, "True"

    .line 2948
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    const-string v0, "False"

    .line 2951
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2953
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraArgs:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p1, Lgroovyjarjarantlr/RuleRefElement;->args:Ljava/lang/String;

    if-eqz v0, :cond_2

    .line 2954
    :cond_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2959
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraArgs:Ljava/lang/String;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2960
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraArgs:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p1, Lgroovyjarjarantlr/RuleRefElement;->args:Ljava/lang/String;

    if-eqz v0, :cond_3

    .line 2961
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2965
    :cond_3
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/Grammar;->getSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/GrammarSymbol;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr/RuleSymbol;

    .line 2966
    iget-object v1, p1, Lgroovyjarjarantlr/RuleRefElement;->args:Ljava/lang/String;

    if-eqz v1, :cond_6

    .line 2968
    new-instance v1, Lgroovyjarjarantlr/ActionTransInfo;

    invoke-direct {v1}, Lgroovyjarjarantlr/ActionTransInfo;-><init>()V

    .line 2969
    iget-object v2, p1, Lgroovyjarjarantlr/RuleRefElement;->args:Ljava/lang/String;

    const/4 v3, 0x0

    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {p0, v2, v3, v4, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->processActionForSpecialSymbols(Ljava/lang/String;ILgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object v2

    .line 2970
    iget-boolean v3, v1, Lgroovyjarjarantlr/ActionTransInfo;->assignToRoot:Z

    if-nez v3, :cond_4

    iget-object v1, v1, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    if-eqz v1, :cond_5

    .line 2971
    :cond_4
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "Arguments of rule reference \'"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget-object v4, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v4, "\' cannot set or ref #"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {v4}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v4}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLine()I

    move-result v5

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getColumn()I

    move-result v6

    invoke-virtual {v1, v3, v4, v5, v6}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;Ljava/lang/String;II)V

    .line 2974
    :cond_5
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2977
    iget-object v0, v0, Lgroovyjarjarantlr/RuleSymbol;->block:Lgroovyjarjarantlr/RuleBlock;

    iget-object v0, v0, Lgroovyjarjarantlr/RuleBlock;->argAction:Ljava/lang/String;

    if-nez v0, :cond_7

    .line 2978
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "Rule \'"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v2, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, "\' accepts no arguments"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLine()I

    move-result v3

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getColumn()I

    move-result p1

    invoke-virtual {v0, v1, v2, v3, p1}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    goto :goto_1

    .line 2984
    :cond_6
    iget-object v0, v0, Lgroovyjarjarantlr/RuleSymbol;->block:Lgroovyjarjarantlr/RuleBlock;

    iget-object v0, v0, Lgroovyjarjarantlr/RuleBlock;->argAction:Ljava/lang/String;

    if-eqz v0, :cond_7

    .line 2985
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "Missing parameters on reference to rule "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v2, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLine()I

    move-result v3

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getColumn()I

    move-result p1

    invoke-virtual {v0, v1, v2, v3, p1}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    :cond_7
    :goto_1
    const-string p1, ")"

    .line 2988
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->_println(Ljava/lang/String;)V

    .line 2991
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p1, p1, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz p1, :cond_8

    const-string p1, "_t = self._retTree"

    .line 2992
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_8
    return-void
.end method

.method private genBitSet(Lgroovyjarjarantlr/collections/impl/BitSet;I)V
    .locals 9

    .line 1400
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v1, 0x0

    .line 1403
    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v2, ""

    .line 1405
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v2, "### generate bit set"

    .line 1406
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1407
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "def mk"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->getBitsetName(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    const-string v3, "(): "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1409
    iget v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v2, v2, 0x1

    iput v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1410
    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/BitSet;->lengthInLongWords()I

    move-result v2

    const/16 v3, 0x8

    if-ge v2, v3, :cond_0

    const-string v1, "### var1"

    .line 1413
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1414
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "data = [ "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/BitSet;->toStringOfWords()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v1, "]"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto/16 :goto_4

    .line 1419
    :cond_0
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "data = [0L] * "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v2

    const-string v3, " ### init list"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1421
    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/BitSet;->toPackedArray()[J

    move-result-object p1

    .line 1423
    :goto_0
    array-length v2, p1

    if-ge v1, v2, :cond_5

    .line 1425
    aget-wide v2, p1, v1

    const-wide/16 v4, 0x0

    cmp-long v2, v2, v4

    if-nez v2, :cond_1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    add-int/lit8 v2, v1, 0x1

    .line 1432
    array-length v3, p1

    const-string v4, "L"

    if-eq v2, v3, :cond_4

    aget-wide v5, p1, v1

    aget-wide v7, p1, v2

    cmp-long v3, v5, v7

    if-eqz v3, :cond_2

    goto :goto_2

    .line 1442
    :cond_2
    :goto_1
    array-length v3, p1

    if-ge v2, v3, :cond_3

    aget-wide v5, p1, v2

    aget-wide v7, p1, v1

    cmp-long v3, v5, v7

    if-nez v3, :cond_3

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 1445
    :cond_3
    aget-wide v5, p1, v1

    .line 1447
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v7, "for x in xrange("

    invoke-virtual {v3, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v3, ", "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v3, "):"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1448
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1449
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "data[x] = "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v5, v6}, Ljava/lang/StringBuffer;->append(J)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1450
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v1, v1, -0x1

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    goto :goto_3

    .line 1435
    :cond_4
    :goto_2
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v5, "data["

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v5, "] ="

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    aget-wide v5, p1, v1

    invoke-virtual {v3, v5, v6}, Ljava/lang/StringBuffer;->append(J)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :goto_3
    move v1, v2

    goto/16 :goto_0

    :cond_5
    :goto_4
    const-string p1, "return data"

    .line 1455
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1456
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1459
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->getBitsetName(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v1, " = antlr.BitSet(mk"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->getBitsetName(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string p2, "())"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1463
    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    return-void
.end method

.method private genBlockFinish(Lgroovyjarjarantlr/PythonBlockFinishingInfo;Ljava/lang/String;)V
    .locals 1

    .line 1468
    iget-boolean v0, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->needAnErrorClause:Z

    if-eqz v0, :cond_2

    iget-boolean v0, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedAnIf:Z

    if-nez v0, :cond_0

    iget-boolean v0, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedSwitch:Z

    if-eqz v0, :cond_2

    .line 1470
    :cond_0
    iget-boolean v0, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedAnIf:Z

    if-eqz v0, :cond_1

    const-string v0, "else:"

    .line 1472
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1474
    :cond_1
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1475
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1476
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, -0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1479
    :cond_2
    iget-object p2, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->postscript:Ljava/lang/String;

    if-eqz p2, :cond_3

    .line 1480
    iget-object p1, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->postscript:Ljava/lang/String;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_3
    return-void
.end method

.method private genBlockFinish1(Lgroovyjarjarantlr/PythonBlockFinishingInfo;Ljava/lang/String;)V
    .locals 1

    .line 1487
    iget-boolean v0, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->needAnErrorClause:Z

    if-eqz v0, :cond_2

    iget-boolean v0, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedAnIf:Z

    if-nez v0, :cond_0

    iget-boolean v0, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedSwitch:Z

    if-eqz v0, :cond_2

    .line 1490
    :cond_0
    iget-boolean v0, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedAnIf:Z

    if-eqz v0, :cond_1

    const-string v0, "else:"

    .line 1493
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1495
    :cond_1
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1496
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1497
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, -0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1498
    iget-boolean p2, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedAnIf:Z

    .line 1505
    :cond_2
    iget-object p2, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->postscript:Ljava/lang/String;

    if-eqz p2, :cond_3

    .line 1506
    iget-object p1, p1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->postscript:Ljava/lang/String;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_3
    return-void
.end method

.method private genElementAST(Lgroovyjarjarantlr/AlternativeElement;)V
    .locals 10

    .line 2004
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/TreeWalkerGrammar;

    const-string v1, "_in = "

    const-string v2, "_AST"

    const-string v3, "tmp"

    const/4 v4, 0x1

    if-eqz v0, :cond_1

    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-nez v0, :cond_1

    .line 2009
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    .line 2010
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    .line 2012
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v5, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->astVarNumber:I

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    .line 2013
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->astVarNumber:I

    add-int/2addr v3, v4

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->astVarNumber:I

    .line 2015
    invoke-direct {p0, p1, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->mapTreeVariable(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;)V

    .line 2017
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_0
    return-void

    .line 2022
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v0, :cond_10

    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v0, :cond_10

    .line 2023
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    const/4 v5, 0x3

    if-eqz v0, :cond_3

    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_2

    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getAutoGenType()I

    move-result v0

    if-eq v0, v5, :cond_3

    :cond_2
    move v0, v4

    goto :goto_0

    :cond_3
    const/4 v0, 0x0

    .line 2034
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getAutoGenType()I

    move-result v6

    if-eq v6, v5, :cond_4

    instance-of v5, p1, Lgroovyjarjarantlr/TokenRefElement;

    if-eqz v5, :cond_4

    move v0, v4

    .line 2040
    :cond_4
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v5, v5, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    .line 2047
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v5

    if-eqz v5, :cond_5

    .line 2048
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v3

    .line 2049
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v5

    goto :goto_1

    .line 2052
    :cond_5
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    .line 2054
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v6, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->astVarNumber:I

    invoke-virtual {v3, v6}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    .line 2056
    iget v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->astVarNumber:I

    add-int/2addr v6, v4

    iput v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->astVarNumber:I

    move-object v9, v5

    move-object v5, v3

    move-object v3, v9

    :goto_1
    if-eqz v0, :cond_8

    .line 2062
    instance-of v6, p1, Lgroovyjarjarantlr/GrammarAtom;

    if-eqz v6, :cond_7

    .line 2063
    move-object v6, p1

    check-cast v6, Lgroovyjarjarantlr/GrammarAtom;

    .line 2064
    invoke-virtual {v6}, Lgroovyjarjarantlr/GrammarAtom;->getASTNodeType()Ljava/lang/String;

    move-result-object v7

    if-eqz v7, :cond_6

    .line 2065
    invoke-virtual {v6}, Lgroovyjarjarantlr/GrammarAtom;->getASTNodeType()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p0, p1, v5, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_2

    .line 2068
    :cond_6
    iget-object v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    invoke-virtual {p0, p1, v5, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_2

    .line 2072
    :cond_7
    iget-object v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    invoke-virtual {p0, p1, v5, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;Ljava/lang/String;)V

    .line 2077
    :cond_8
    :goto_2
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v6, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    .line 2080
    invoke-direct {p0, p1, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->mapTreeVariable(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;)V

    .line 2081
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v5, v5, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v5, :cond_9

    .line 2083
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v5, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    const-string v6, "_in = None"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2094
    :cond_9
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v5

    const-string v6, " = "

    const-string v7, ""

    if-eqz v5, :cond_b

    .line 2095
    instance-of v5, p1, Lgroovyjarjarantlr/GrammarAtom;

    if-eqz v5, :cond_a

    .line 2096
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v5, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    move-object v8, p1

    check-cast v8, Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {p0, v8, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->getASTCreateString(Lgroovyjarjarantlr/GrammarAtom;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_3

    .line 2099
    :cond_a
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v5, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->getASTCreateString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2104
    :cond_b
    :goto_3
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v3

    if-nez v3, :cond_d

    if-eqz v0, :cond_d

    .line 2105
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    .line 2106
    instance-of v3, p1, Lgroovyjarjarantlr/GrammarAtom;

    if-eqz v3, :cond_c

    .line 2107
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    move-object v5, p1

    check-cast v5, Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {p0, v5, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->getASTCreateString(Lgroovyjarjarantlr/GrammarAtom;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_4

    .line 2110
    :cond_c
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->getASTCreateString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2113
    :goto_4
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v3, v3, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v3, :cond_d

    .line 2115
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2119
    :cond_d
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    if-eqz v0, :cond_10

    .line 2120
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getAutoGenType()I

    move-result p1

    const-string v0, ")"

    if-eq p1, v4, :cond_f

    const/4 v1, 0x2

    if-eq p1, v1, :cond_e

    goto :goto_5

    .line 2125
    :cond_e
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "self.makeASTRoot(currentAST, "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_5

    .line 2122
    :cond_f
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "self.addASTChild(currentAST, "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_10
    :goto_5
    return-void
.end method

.method private genErrorCatchForElement(Lgroovyjarjarantlr/AlternativeElement;)V
    .locals 3

    .line 2141
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 2142
    :cond_0
    iget-object v0, p1, Lgroovyjarjarantlr/AlternativeElement;->enclosingRuleName:Ljava/lang/String;

    .line 2143
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v1, v1, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v1, :cond_1

    .line 2144
    iget-object v0, p1, Lgroovyjarjarantlr/AlternativeElement;->enclosingRuleName:Ljava/lang/String;

    invoke-static {v0}, Lgroovyjarjarantlr/CodeGenerator;->encodeLexerRuleName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 2146
    :cond_1
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/Grammar;->getSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/GrammarSymbol;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr/RuleSymbol;

    if-nez v0, :cond_2

    .line 2148
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v2, "Enclosing rule not found!"

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    .line 2150
    :cond_2
    iget-object v0, v0, Lgroovyjarjarantlr/RuleSymbol;->block:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/RuleBlock;->findExceptionSpec(Ljava/lang/String;)Lgroovyjarjarantlr/ExceptionSpec;

    move-result-object p1

    if-eqz p1, :cond_3

    .line 2152
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2153
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genErrorHandler(Lgroovyjarjarantlr/ExceptionSpec;)V

    :cond_3
    return-void
.end method

.method private genErrorHandler(Lgroovyjarjarantlr/ExceptionSpec;)V
    .locals 7

    const/4 v0, 0x0

    move v1, v0

    .line 2160
    :goto_0
    iget-object v2, p1, Lgroovyjarjarantlr/ExceptionSpec;->handlers:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v2}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v2

    if-ge v1, v2, :cond_4

    .line 2161
    iget-object v2, p1, Lgroovyjarjarantlr/ExceptionSpec;->handlers:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/collections/impl/Vector;->elementAt(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr/ExceptionHandler;

    .line 2164
    iget-object v3, v2, Lgroovyjarjarantlr/ExceptionHandler;->exceptionTypeAndName:Lgroovyjarjarantlr/Token;

    invoke-virtual {v3}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v3

    .line 2165
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->removeAssignmentFromDeclaration(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    .line 2166
    invoke-virtual {v3}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v3

    .line 2169
    invoke-virtual {v3}, Ljava/lang/String;->length()I

    move-result v4

    add-int/lit8 v4, v4, -0x1

    :goto_1
    const-string v5, ""

    if-ltz v4, :cond_1

    .line 2171
    invoke-virtual {v3, v4}, Ljava/lang/String;->charAt(I)C

    move-result v5

    invoke-static {v5}, Ljava/lang/Character;->isLetterOrDigit(C)Z

    move-result v5

    if-nez v5, :cond_0

    invoke-virtual {v3, v4}, Ljava/lang/String;->charAt(I)C

    move-result v5

    const/16 v6, 0x5f

    if-eq v5, v6, :cond_0

    .line 2174
    invoke-virtual {v3, v0, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v5

    add-int/lit8 v4, v4, 0x1

    .line 2175
    invoke-virtual {v3, v4}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v3

    goto :goto_2

    :cond_0
    add-int/lit8 v4, v4, -0x1

    goto :goto_1

    :cond_1
    move-object v3, v5

    .line 2180
    :goto_2
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    const-string v6, "except "

    invoke-virtual {v4, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v5, ", "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v5, ":"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2181
    iget v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v4, v4, 0x1

    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2182
    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v4, v4, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    if-eqz v4, :cond_2

    const-string v4, "if not self.inputState.guessing:"

    .line 2183
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2184
    iget v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v4, v4, 0x1

    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2188
    :cond_2
    new-instance v4, Lgroovyjarjarantlr/ActionTransInfo;

    invoke-direct {v4}, Lgroovyjarjarantlr/ActionTransInfo;-><init>()V

    .line 2189
    iget-object v5, v2, Lgroovyjarjarantlr/ExceptionHandler;->action:Lgroovyjarjarantlr/Token;

    invoke-virtual {v5}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v5

    iget-object v2, v2, Lgroovyjarjarantlr/ExceptionHandler;->action:Lgroovyjarjarantlr/Token;

    invoke-virtual {v2}, Lgroovyjarjarantlr/Token;->getLine()I

    move-result v2

    iget-object v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {p0, v5, v2, v6, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->processActionForSpecialSymbols(Ljava/lang/String;ILgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->printAction(Ljava/lang/String;)V

    .line 2195
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v2, v2, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    if-eqz v2, :cond_3

    .line 2196
    iget v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v2, v2, -0x1

    iput v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v2, "else:"

    .line 2197
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2198
    iget v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v2, v2, 0x1

    iput v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2200
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "raise "

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2201
    iget v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v2, v2, -0x1

    iput v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2204
    :cond_3
    iget v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v2, v2, -0x1

    iput v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v1, v1, 0x1

    goto/16 :goto_0

    :cond_4
    return-void
.end method

.method private genErrorTryForElement(Lgroovyjarjarantlr/AlternativeElement;)V
    .locals 3

    .line 2210
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 2211
    :cond_0
    iget-object v0, p1, Lgroovyjarjarantlr/AlternativeElement;->enclosingRuleName:Ljava/lang/String;

    .line 2212
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v1, v1, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v1, :cond_1

    .line 2213
    iget-object v0, p1, Lgroovyjarjarantlr/AlternativeElement;->enclosingRuleName:Ljava/lang/String;

    invoke-static {v0}, Lgroovyjarjarantlr/CodeGenerator;->encodeLexerRuleName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 2215
    :cond_1
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/Grammar;->getSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/GrammarSymbol;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr/RuleSymbol;

    if-nez v0, :cond_2

    .line 2217
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v2, "Enclosing rule not found!"

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    .line 2219
    :cond_2
    iget-object v0, v0, Lgroovyjarjarantlr/RuleSymbol;->block:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/RuleBlock;->findExceptionSpec(Ljava/lang/String;)Lgroovyjarjarantlr/ExceptionSpec;

    move-result-object p1

    if-eqz p1, :cond_3

    const-string p1, "try: # for error handling"

    .line 2221
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2222
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    :cond_3
    return-void
.end method

.method private genLiteralsTest()V
    .locals 1

    const-string v0, "### option { testLiterals=true } "

    .line 2289
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "_ttype = self.testLiteralsTable(_ttype)"

    .line 2290
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    return-void
.end method

.method private genLiteralsTestForPartialToken()V
    .locals 1

    const-string v0, "_ttype = self.testLiteralsTable(self.text.getString(), _begin, self.text.length()-_begin, _ttype)"

    .line 2294
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    return-void
.end method

.method private getValueString(IZ)Ljava/lang/String;
    .locals 2

    .line 3459
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_1

    .line 3460
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->charFormatter:Lgroovyjarjarantlr/CharFormatter;

    invoke-interface {v0, p1}, Lgroovyjarjarantlr/CharFormatter;->literalChar(I)Ljava/lang/String;

    move-result-object p1

    if-eqz p2, :cond_0

    .line 3462
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v0, "u\'"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string p2, "\'"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    :cond_0
    return-object p1

    .line 3467
    :cond_1
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object p2, p2, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {p2, p1}, Lgroovyjarjarantlr/TokenManager;->getTokenSymbolAt(I)Lgroovyjarjarantlr/TokenSymbol;

    move-result-object p2

    if-nez p2, :cond_2

    .line 3472
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v0, ""

    invoke-virtual {p2, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 3476
    :cond_2
    invoke-virtual {p2}, Lgroovyjarjarantlr/TokenSymbol;->getId()Ljava/lang/String;

    move-result-object v0

    .line 3477
    instance-of v1, p2, Lgroovyjarjarantlr/StringLiteralSymbol;

    if-nez v1, :cond_3

    return-object v0

    .line 3486
    :cond_3
    check-cast p2, Lgroovyjarjarantlr/StringLiteralSymbol;

    .line 3487
    invoke-virtual {p2}, Lgroovyjarjarantlr/StringLiteralSymbol;->getLabel()Ljava/lang/String;

    move-result-object p2

    if-eqz p2, :cond_4

    goto :goto_0

    .line 3493
    :cond_4
    invoke-direct {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->mangleLiteral(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    if-nez p2, :cond_5

    .line 3495
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p2

    :cond_5
    :goto_0
    return-object p2
.end method

.method static isEmpty(Ljava/lang/String;)Z
    .locals 5

    const/4 v0, 0x0

    const/4 v1, 0x1

    move v2, v0

    :goto_0
    if-eqz v1, :cond_1

    .line 3710
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v3

    if-ge v2, v3, :cond_1

    .line 3711
    invoke-virtual {p0, v2}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v4, 0x9

    if-eq v3, v4, :cond_0

    const/16 v4, 0xa

    if-eq v3, v4, :cond_0

    const/16 v4, 0xc

    if-eq v3, v4, :cond_0

    const/16 v4, 0xd

    if-eq v3, v4, :cond_0

    const/16 v4, 0x20

    if-eq v3, v4, :cond_0

    move v1, v0

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return v1
.end method

.method private lookaheadString(I)Ljava/lang/String;
    .locals 2

    .line 3518
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v0, :cond_0

    const-string p1, "_t.getType()"

    return-object p1

    .line 3521
    :cond_0
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "self.LA("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, ")"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private mangleLiteral(Ljava/lang/String;)Ljava/lang/String;
    .locals 5

    .line 3531
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-object v0, v0, Lgroovyjarjarantlr/Tool;->literalsPrefix:Ljava/lang/String;

    const/4 v1, 0x1

    move v2, v1

    .line 3532
    :goto_0
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v3

    sub-int/2addr v3, v1

    if-ge v2, v3, :cond_1

    .line 3533
    invoke-virtual {p1, v2}, Ljava/lang/String;->charAt(I)C

    move-result v3

    invoke-static {v3}, Ljava/lang/Character;->isLetter(C)Z

    move-result v3

    if-nez v3, :cond_0

    invoke-virtual {p1, v2}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v4, 0x5f

    if-eq v3, v4, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 3537
    :cond_0
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v3, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {p1, v2}, Ljava/lang/String;->charAt(I)C

    move-result v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuffer;->append(C)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 3539
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-boolean p1, p1, Lgroovyjarjarantlr/Tool;->upperCaseMangledLiterals:Z

    if-eqz p1, :cond_2

    .line 3540
    invoke-virtual {v0}, Ljava/lang/String;->toUpperCase()Ljava/lang/String;

    move-result-object v0

    :cond_2
    return-object v0
.end method

.method private mapTreeVariable(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;)V
    .locals 2

    .line 3625
    instance-of v0, p1, Lgroovyjarjarantlr/TreeElement;

    if-eqz v0, :cond_0

    .line 3626
    check-cast p1, Lgroovyjarjarantlr/TreeElement;

    iget-object p1, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->mapTreeVariable(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;)V

    return-void

    :cond_0
    const/4 v0, 0x0

    .line 3634
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    if-nez v1, :cond_2

    .line 3635
    instance-of v1, p1, Lgroovyjarjarantlr/TokenRefElement;

    if-eqz v1, :cond_1

    .line 3637
    check-cast p1, Lgroovyjarjarantlr/TokenRefElement;

    iget-object v0, p1, Lgroovyjarjarantlr/TokenRefElement;->atomText:Ljava/lang/String;

    goto :goto_0

    .line 3639
    :cond_1
    instance-of v1, p1, Lgroovyjarjarantlr/RuleRefElement;

    if-eqz v1, :cond_2

    .line 3641
    check-cast p1, Lgroovyjarjarantlr/RuleRefElement;

    iget-object v0, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    :cond_2
    :goto_0
    if-eqz v0, :cond_4

    .line 3646
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeVariableMap:Ljava/util/Hashtable;

    invoke-virtual {p1, v0}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-eqz p1, :cond_3

    .line 3648
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeVariableMap:Ljava/util/Hashtable;

    invoke-virtual {p1, v0}, Ljava/util/Hashtable;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 3649
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeVariableMap:Ljava/util/Hashtable;

    sget-object p2, Lgroovyjarjarantlr/PythonCodeGenerator;->NONUNIQUE:Ljava/lang/String;

    invoke-virtual {p1, v0, p2}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 3652
    :cond_3
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeVariableMap:Ljava/util/Hashtable;

    invoke-virtual {p1, v0, p2}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_4
    :goto_1
    return-void
.end method

.method private setupGrammarParameters(Lgroovyjarjarantlr/Grammar;)V
    .locals 9

    .line 3764
    instance-of v0, p1, Lgroovyjarjarantlr/ParserGrammar;

    const-string v1, "None"

    const-string v2, "groovyjarjarantlr.RecognitionException"

    const-string v3, "className"

    const-string v4, "ASTLabelType"

    const-string v5, ""

    const-string v6, "\""

    if-eqz v0, :cond_2

    .line 3766
    iput-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    .line 3767
    invoke-virtual {p1, v4}, Lgroovyjarjarantlr/Grammar;->hasOption(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 3769
    invoke-virtual {p1, v4}, Lgroovyjarjarantlr/Grammar;->getOption(Ljava/lang/String;)Lgroovyjarjarantlr/Token;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 3771
    invoke-virtual {v0}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v6, v6}, Lgroovyjarjarantlr/StringUtils;->stripFrontBack(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 3773
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    .line 3777
    :cond_0
    iput-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementType:Ljava/lang/String;

    .line 3778
    iput-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementInit:Ljava/lang/String;

    .line 3779
    iput-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraArgs:Ljava/lang/String;

    const-string v0, "self"

    .line 3780
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraParams:Ljava/lang/String;

    .line 3781
    iput-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonLocalVars:Ljava/lang/String;

    const-string v0, "self.LT(1)"

    .line 3782
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    .line 3783
    iput-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->exceptionThrown:Ljava/lang/String;

    const-string v0, "raise antlr.NoViableAltException(self.LT(1), self.getFilename())"

    .line 3784
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->throwNoViable:Ljava/lang/String;

    const-string v0, "Parser"

    .line 3785
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->parserClassName:Ljava/lang/String;

    .line 3786
    invoke-virtual {p1, v3}, Lgroovyjarjarantlr/Grammar;->hasOption(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 3788
    invoke-virtual {p1, v3}, Lgroovyjarjarantlr/Grammar;->getOption(Ljava/lang/String;)Lgroovyjarjarantlr/Token;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 3790
    invoke-virtual {p1}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1, v6, v6}, Lgroovyjarjarantlr/StringUtils;->stripFrontBack(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 3792
    iput-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->parserClassName:Ljava/lang/String;

    :cond_1
    return-void

    .line 3799
    :cond_2
    instance-of v0, p1, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_4

    const-string v0, "char "

    .line 3801
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementType:Ljava/lang/String;

    const-string v0, "\'\\0\'"

    .line 3802
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementInit:Ljava/lang/String;

    .line 3803
    iput-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraArgs:Ljava/lang/String;

    const-string v0, "self, _createToken"

    .line 3804
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraParams:Ljava/lang/String;

    const-string v0, "_ttype = 0\n        _token = None\n        _begin = self.text.length()"

    .line 3805
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonLocalVars:Ljava/lang/String;

    const-string v0, "self.LA(1)"

    .line 3806
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    .line 3807
    iput-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->exceptionThrown:Ljava/lang/String;

    const-string v0, "self.raise_NoViableAlt(self.LA(1))"

    .line 3808
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->throwNoViable:Ljava/lang/String;

    const-string v0, "Lexer"

    .line 3809
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lexerClassName:Ljava/lang/String;

    .line 3810
    invoke-virtual {p1, v3}, Lgroovyjarjarantlr/Grammar;->hasOption(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 3812
    invoke-virtual {p1, v3}, Lgroovyjarjarantlr/Grammar;->getOption(Ljava/lang/String;)Lgroovyjarjarantlr/Token;

    move-result-object p1

    if-eqz p1, :cond_3

    .line 3814
    invoke-virtual {p1}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1, v6, v6}, Lgroovyjarjarantlr/StringUtils;->stripFrontBack(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_3

    .line 3816
    iput-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lexerClassName:Ljava/lang/String;

    :cond_3
    return-void

    .line 3823
    :cond_4
    instance-of v0, p1, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v0, :cond_8

    .line 3825
    iput-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    .line 3826
    iput-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementType:Ljava/lang/String;

    .line 3827
    invoke-virtual {p1, v4}, Lgroovyjarjarantlr/Grammar;->hasOption(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 3828
    invoke-virtual {p1, v4}, Lgroovyjarjarantlr/Grammar;->getOption(Ljava/lang/String;)Lgroovyjarjarantlr/Token;

    move-result-object v0

    if-eqz v0, :cond_5

    .line 3830
    invoke-virtual {v0}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, v6, v6}, Lgroovyjarjarantlr/StringUtils;->stripFrontBack(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_5

    .line 3832
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    .line 3833
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementType:Ljava/lang/String;

    .line 3837
    :cond_5
    invoke-virtual {p1, v4}, Lgroovyjarjarantlr/Grammar;->hasOption(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_6

    .line 3838
    new-instance v0, Lgroovyjarjarantlr/Token;

    const/4 v7, 0x6

    const-string v8, "<4>AST"

    invoke-direct {v0, v7, v8}, Lgroovyjarjarantlr/Token;-><init>(ILjava/lang/String;)V

    invoke-virtual {p1, v4, v0}, Lgroovyjarjarantlr/Grammar;->setOption(Ljava/lang/String;Lgroovyjarjarantlr/Token;)Z

    .line 3840
    :cond_6
    iput-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementInit:Ljava/lang/String;

    const-string v0, "_t"

    .line 3841
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraArgs:Ljava/lang/String;

    const-string v1, "self, _t"

    .line 3842
    iput-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraParams:Ljava/lang/String;

    .line 3843
    iput-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonLocalVars:Ljava/lang/String;

    .line 3844
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    .line 3845
    iput-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->exceptionThrown:Ljava/lang/String;

    const-string v0, "raise antlr.NoViableAltException(_t)"

    .line 3846
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->throwNoViable:Ljava/lang/String;

    const-string v0, "Walker"

    .line 3847
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeWalkerClassName:Ljava/lang/String;

    .line 3848
    invoke-virtual {p1, v3}, Lgroovyjarjarantlr/Grammar;->hasOption(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_7

    .line 3850
    invoke-virtual {p1, v3}, Lgroovyjarjarantlr/Grammar;->getOption(Ljava/lang/String;)Lgroovyjarjarantlr/Token;

    move-result-object p1

    if-eqz p1, :cond_7

    .line 3852
    invoke-virtual {p1}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1, v6, v6}, Lgroovyjarjarantlr/StringUtils;->stripFrontBack(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_7

    .line 3854
    iput-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeWalkerClassName:Ljava/lang/String;

    :cond_7
    return-void

    .line 3862
    :cond_8
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v0, "Unknown grammar type"

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    return-void
.end method

.method private static suitableForCaseExpression(Lgroovyjarjarantlr/Alternative;)Z
    .locals 2

    .line 1993
    iget v0, p0, Lgroovyjarjarantlr/Alternative;->lookaheadDepth:I

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr/Alternative;->semPred:Ljava/lang/String;

    if-nez v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr/Alternative;->cache:[Lgroovyjarjarantlr/Lookahead;

    aget-object v0, v0, v1

    invoke-virtual {v0}, Lgroovyjarjarantlr/Lookahead;->containsEpsilon()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object p0, p0, Lgroovyjarjarantlr/Alternative;->cache:[Lgroovyjarjarantlr/Lookahead;

    aget-object p0, p0, v1

    iget-object p0, p0, Lgroovyjarjarantlr/Lookahead;->fset:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0}, Lgroovyjarjarantlr/collections/impl/BitSet;->degree()I

    move-result p0

    const/16 v0, 0x7f

    if-gt p0, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    return v1
.end method


# virtual methods
.method protected _printAction(Ljava/lang/String;)V
    .locals 13

    if-nez p1, :cond_0

    return-void

    .line 3904
    :cond_0
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    move v3, v1

    move v5, v3

    move v4, v2

    :goto_0
    const/16 v6, 0x20

    const/16 v7, 0xd

    const/16 v8, 0xa

    if-ge v3, v0, :cond_5

    if-eqz v4, :cond_5

    add-int/lit8 v9, v3, 0x1

    .line 3912
    invoke-virtual {p1, v3}, Ljava/lang/String;->charAt(I)C

    move-result v3

    if-eq v3, v8, :cond_4

    if-eq v3, v7, :cond_2

    if-eq v3, v6, :cond_1

    move v4, v1

    :cond_1
    move v3, v9

    goto :goto_0

    :cond_2
    if-gt v9, v0, :cond_3

    .line 3918
    invoke-virtual {p1, v9}, Ljava/lang/String;->charAt(I)C

    move-result v3

    if-ne v3, v8, :cond_3

    add-int/lit8 v9, v9, 0x1

    :cond_3
    move v5, v9

    move v3, v5

    goto :goto_0

    :cond_4
    move v3, v9

    move v5, v3

    goto :goto_0

    :cond_5
    if-nez v4, :cond_6

    add-int/lit8 v3, v3, -0x1

    :cond_6
    sub-int v4, v3, v5

    sub-int/2addr v0, v2

    :goto_1
    if-le v0, v3, :cond_7

    .line 3937
    invoke-virtual {p1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v5

    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->isspace(C)Z

    move-result v5

    if-eqz v5, :cond_7

    add-int/lit8 v0, v0, -0x1

    goto :goto_1

    :cond_7
    move v5, v1

    :goto_2
    if-gt v3, v0, :cond_11

    .line 3946
    invoke-virtual {p1, v3}, Ljava/lang/String;->charAt(I)C

    move-result v9

    const/16 v10, 0x9

    if-eq v9, v10, :cond_b

    if-eq v9, v8, :cond_a

    if-eq v9, v7, :cond_9

    if-eq v9, v6, :cond_8

    .line 3965
    iget-object v10, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {v10, v9}, Ljava/io/PrintWriter;->print(C)V

    goto :goto_3

    .line 3962
    :cond_8
    iget-object v9, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    const-string v10, " "

    invoke-virtual {v9, v10}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    goto :goto_3

    :cond_9
    add-int/lit8 v5, v3, 0x1

    if-gt v5, v0, :cond_a

    .line 3953
    invoke-virtual {p1, v5}, Ljava/lang/String;->charAt(I)C

    move-result v9

    if-ne v9, v8, :cond_a

    move v3, v5

    :cond_a
    move v5, v2

    goto :goto_3

    .line 3958
    :cond_b
    sget-object v9, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v10, "warning: tab characters used in Python action"

    invoke-virtual {v9, v10}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 3959
    iget-object v9, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    const-string v10, "        "

    invoke-virtual {v9, v10}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    :goto_3
    if-eqz v5, :cond_10

    .line 3971
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    const-string v9, "\n"

    invoke-virtual {v5, v9}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 3972
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printTabs()V

    add-int/lit8 v3, v3, 0x1

    move v5, v1

    move v10, v5

    :goto_4
    if-gt v3, v0, :cond_10

    .line 3978
    invoke-virtual {p1, v3}, Ljava/lang/String;->charAt(I)C

    move-result v11

    .line 3979
    invoke-virtual {p0, v11}, Lgroovyjarjarantlr/PythonCodeGenerator;->isspace(C)Z

    move-result v12

    if-nez v12, :cond_c

    add-int/lit8 v3, v3, -0x1

    goto :goto_7

    :cond_c
    if-eq v11, v8, :cond_e

    if-eq v11, v7, :cond_d

    goto :goto_5

    :cond_d
    add-int/lit8 v5, v3, 0x1

    if-gt v5, v0, :cond_e

    .line 3988
    invoke-virtual {p1, v5}, Ljava/lang/String;->charAt(I)C

    move-result v11

    if-ne v11, v8, :cond_e

    move v3, v5

    :cond_e
    move v5, v2

    :goto_5
    if-eqz v5, :cond_f

    .line 3997
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {v5, v9}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 3998
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printTabs()V

    move v5, v1

    move v10, v5

    goto :goto_6

    :cond_f
    if-ge v10, v4, :cond_10

    add-int/lit8 v10, v10, 0x1

    :goto_6
    add-int/lit8 v3, v3, 0x1

    goto :goto_4

    :cond_10
    :goto_7
    add-int/2addr v3, v2

    goto :goto_2

    .line 4015
    :cond_11
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Ljava/io/PrintWriter;->println()V

    return-void
.end method

.method protected _printJavadoc(Ljava/lang/String;)V
    .locals 10

    .line 4062
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    .line 4066
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    const-string v2, "\n"

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 4067
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printTabs()V

    .line 4068
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    const-string v3, "###"

    invoke-virtual {v1, v3}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    const/4 v1, 0x0

    move v4, v1

    move v5, v4

    :goto_0
    if-ge v4, v0, :cond_5

    .line 4072
    invoke-virtual {p1, v4}, Ljava/lang/String;->charAt(I)C

    move-result v6

    const/16 v7, 0x9

    const/4 v8, 0x1

    if-eq v6, v7, :cond_3

    const/16 v7, 0xa

    if-eq v6, v7, :cond_2

    const/16 v9, 0xd

    if-eq v6, v9, :cond_1

    const/16 v7, 0x20

    if-eq v6, v7, :cond_0

    .line 4090
    iget-object v7, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {v7, v6}, Ljava/io/PrintWriter;->print(C)V

    goto :goto_1

    .line 4087
    :cond_0
    iget-object v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    const-string v7, " "

    invoke-virtual {v6, v7}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    add-int/lit8 v5, v4, 0x1

    if-gt v5, v0, :cond_2

    .line 4079
    invoke-virtual {p1, v5}, Ljava/lang/String;->charAt(I)C

    move-result v6

    if-ne v6, v7, :cond_2

    move v4, v5

    :cond_2
    move v5, v8

    goto :goto_1

    .line 4084
    :cond_3
    iget-object v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    const-string v7, "\t"

    invoke-virtual {v6, v7}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    :goto_1
    if-eqz v5, :cond_4

    .line 4096
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {v5, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    .line 4097
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printTabs()V

    .line 4098
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {v5, v3}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    move v5, v1

    :cond_4
    add-int/2addr v4, v8

    goto :goto_0

    .line 4103
    :cond_5
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Ljava/io/PrintWriter;->println()V

    return-void
.end method

.method protected addSemPred(Ljava/lang/String;)I
    .locals 1

    .line 101
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->semPreds:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/collections/impl/Vector;->appendElement(Ljava/lang/Object;)V

    .line 102
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->semPreds:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result p1

    add-int/lit8 p1, p1, -0x1

    return p1
.end method

.method protected checkCurrentOutputStream()V
    .locals 1

    .line 114
    :try_start_0
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    if-eqz v0, :cond_0

    goto :goto_0

    .line 115
    :cond_0
    new-instance v0, Ljava/lang/NullPointerException;

    invoke-direct {v0}, Ljava/lang/NullPointerException;-><init>()V

    throw v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    const-string v0, "current output is not set"

    .line 119
    invoke-static {v0}, Lgroovyjarjarantlr/Utils;->error(Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method public exitIfError()V
    .locals 2

    .line 106
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    invoke-virtual {v0}, Lgroovyjarjarantlr/Tool;->hasError()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 107
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v1, "Exiting due to errors."

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/Tool;->fatalError(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method protected extractIdOfAction(Ljava/lang/String;II)Ljava/lang/String;
    .locals 0

    .line 132
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->removeAssignmentFromDeclaration(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 135
    invoke-virtual {p1}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected extractTypeOfAction(Ljava/lang/String;II)Ljava/lang/String;
    .locals 0

    const-string p1, ""

    return-object p1
.end method

.method protected flushTokens()V
    .locals 6

    const-string v0, ""

    const/4 v1, 0x0

    .line 157
    :try_start_0
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->checkCurrentOutputStream()V

    .line 159
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v2, "### import antlr.Token "

    .line 160
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v2, "from antlr import Token"

    .line 161
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v2, "### >>>The Known Token Types <<<"

    .line 162
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 166
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    .line 169
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->behavior:Lgroovyjarjarantlr/DefineGrammarSymbols;

    iget-object v3, v3, Lgroovyjarjarantlr/DefineGrammarSymbols;->tokenManagers:Ljava/util/Hashtable;

    invoke-virtual {v3}, Ljava/util/Hashtable;->elements()Ljava/util/Enumeration;

    move-result-object v3

    .line 172
    :goto_0
    invoke-interface {v3}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v4

    if-eqz v4, :cond_2

    .line 174
    invoke-interface {v3}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr/TokenManager;

    .line 177
    invoke-interface {v4}, Lgroovyjarjarantlr/TokenManager;->isReadOnly()Z

    move-result v5

    if-nez v5, :cond_1

    if-nez v1, :cond_0

    .line 183
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->genTokenTypes(Lgroovyjarjarantlr/TokenManager;)V

    const/4 v1, 0x1

    .line 188
    :cond_0
    iput-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    .line 191
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->genTokenInterchange(Lgroovyjarjarantlr/TokenManager;)V

    .line 192
    iput-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    .line 195
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->exitIfError()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 199
    :catch_0
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->exitIfError()V

    .line 201
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->checkCurrentOutputStream()V

    .line 202
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    return-void
.end method

.method public gen()V
    .locals 3

    .line 211
    :try_start_0
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->behavior:Lgroovyjarjarantlr/DefineGrammarSymbols;

    iget-object v0, v0, Lgroovyjarjarantlr/DefineGrammarSymbols;->grammars:Ljava/util/Hashtable;

    invoke-virtual {v0}, Ljava/util/Hashtable;->elements()Ljava/util/Enumeration;

    move-result-object v0

    .line 212
    :goto_0
    invoke-interface {v0}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 213
    invoke-interface {v0}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr/Grammar;

    .line 215
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->analyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr/Grammar;->setGrammarAnalyzer(Lgroovyjarjarantlr/LLkGrammarAnalyzer;)V

    .line 216
    invoke-virtual {v1, p0}, Lgroovyjarjarantlr/Grammar;->setCodeGenerator(Lgroovyjarjarantlr/CodeGenerator;)V

    .line 217
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->analyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    invoke-interface {v2, v1}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->setGrammar(Lgroovyjarjarantlr/Grammar;)V

    .line 219
    invoke-direct {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->setupGrammarParameters(Lgroovyjarjarantlr/Grammar;)V

    .line 220
    invoke-virtual {v1}, Lgroovyjarjarantlr/Grammar;->generate()V

    .line 223
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->exitIfError()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 229
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const/4 v2, 0x0

    invoke-virtual {v1, v0, v2}, Lgroovyjarjarantlr/Tool;->reportException(Ljava/lang/Exception;Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public gen(Lgroovyjarjarantlr/ActionElement;)V
    .locals 3

    .line 237
    iget-boolean v0, p1, Lgroovyjarjarantlr/ActionElement;->isSemPred:Z

    if-eqz v0, :cond_0

    .line 238
    iget-object v0, p1, Lgroovyjarjarantlr/ActionElement;->actionText:Ljava/lang/String;

    iget p1, p1, Lgroovyjarjarantlr/ActionElement;->line:I

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genSemPred(Ljava/lang/String;I)V

    goto/16 :goto_0

    .line 242
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    if-eqz v0, :cond_1

    const-string v0, "if not self.inputState.guessing:"

    .line 243
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 244
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 249
    :cond_1
    new-instance v0, Lgroovyjarjarantlr/ActionTransInfo;

    invoke-direct {v0}, Lgroovyjarjarantlr/ActionTransInfo;-><init>()V

    .line 250
    iget-object v1, p1, Lgroovyjarjarantlr/ActionElement;->actionText:Ljava/lang/String;

    invoke-virtual {p1}, Lgroovyjarjarantlr/ActionElement;->getLine()I

    move-result p1

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {p0, v1, p1, v2, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->processActionForSpecialSymbols(Ljava/lang/String;ILgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object p1

    .line 255
    iget-object v1, v0, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    if-eqz v1, :cond_2

    .line 260
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    iget-object v2, v0, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, " = currentAST.root"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 264
    :cond_2
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printAction(Ljava/lang/String;)V

    .line 266
    iget-boolean p1, v0, Lgroovyjarjarantlr/ActionTransInfo;->assignToRoot:Z

    if-eqz p1, :cond_3

    .line 268
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "currentAST.root = "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    iget-object v1, v0, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v1, ""

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 270
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "if ("

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    iget-object v1, v0, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v1, " != None) and ("

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    iget-object v1, v0, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v1, ".getFirstChild() != None):"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 271
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 272
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "currentAST.child = "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    iget-object v2, v0, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    invoke-virtual {p1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v2, ".getFirstChild()"

    invoke-virtual {p1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 273
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string p1, "else:"

    .line 274
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 275
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 276
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    iget-object v0, v0, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 277
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string p1, "currentAST.advanceChildToEnd()"

    .line 278
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 281
    :cond_3
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean p1, p1, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    if-eqz p1, :cond_4

    .line 282
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    :cond_4
    :goto_0
    return-void
.end method

.method public gen(Lgroovyjarjarantlr/AlternativeBlock;)V
    .locals 3

    .line 291
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v0, :cond_0

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "gen("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, ")"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 292
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockPreamble(Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 293
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockInitAction(Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 296
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    .line 297
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeBlock;->getLabel()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_1

    .line 298
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeBlock;->getLabel()Ljava/lang/String;

    move-result-object v1

    iput-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    .line 301
    :cond_1
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr/Grammar;->theLLkAnalyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    invoke-interface {v1, p1}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->deterministic(Lgroovyjarjarantlr/AlternativeBlock;)Z

    .line 304
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v2, 0x1

    .line 305
    invoke-virtual {p0, p1, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->genCommonBlock(Lgroovyjarjarantlr/AlternativeBlock;Z)Lgroovyjarjarantlr/PythonBlockFinishingInfo;

    move-result-object p1

    .line 306
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->throwNoViable:Ljava/lang/String;

    invoke-direct {p0, p1, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockFinish(Lgroovyjarjarantlr/PythonBlockFinishingInfo;Ljava/lang/String;)V

    .line 307
    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 311
    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    return-void
.end method

.method public gen(Lgroovyjarjarantlr/BlockEndElement;)V
    .locals 3

    .line 320
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v0, :cond_0

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "genRuleEnd("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v1, ")"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public gen(Lgroovyjarjarantlr/CharLiteralElement;)V
    .locals 3

    .line 327
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v0, :cond_0

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "genChar("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, ")"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 329
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr/CharLiteralElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 330
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/CharLiteralElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 333
    :cond_1
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    const/4 v1, 0x1

    if-eqz v0, :cond_2

    .line 334
    invoke-virtual {p1}, Lgroovyjarjarantlr/CharLiteralElement;->getAutoGenType()I

    move-result v2

    if-ne v2, v1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v1, 0x0

    :goto_0
    iput-boolean v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    .line 335
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genMatch(Lgroovyjarjarantlr/GrammarAtom;)V

    .line 336
    iput-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    return-void
.end method

.method public gen(Lgroovyjarjarantlr/CharRangeElement;)V
    .locals 3

    .line 354
    invoke-virtual {p1}, Lgroovyjarjarantlr/CharRangeElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v0, :cond_0

    .line 355
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/CharRangeElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 357
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_2

    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz v0, :cond_1

    invoke-virtual {p1}, Lgroovyjarjarantlr/CharRangeElement;->getAutoGenType()I

    move-result v0

    const/4 v1, 0x3

    if-ne v0, v1, :cond_2

    :cond_1
    const/4 v0, 0x1

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_3

    const-string v1, "_saveIndex = self.text.length()"

    .line 362
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 365
    :cond_3
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "self.matchRange(u"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v2, p1, Lgroovyjarjarantlr/CharRangeElement;->beginText:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, ", u"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object p1, p1, Lgroovyjarjarantlr/CharRangeElement;->endText:Ljava/lang/String;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v1, ")"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    if-eqz v0, :cond_4

    const-string p1, "self.text.setLength(_saveIndex)"

    .line 368
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_4
    return-void
.end method

.method public gen(Lgroovyjarjarantlr/LexerGrammar;)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 376
    iget-boolean v0, p1, Lgroovyjarjarantlr/LexerGrammar;->debuggingOutput:Z

    if-eqz v0, :cond_0

    .line 377
    new-instance v0, Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-direct {v0}, Lgroovyjarjarantlr/collections/impl/Vector;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->semPreds:Lgroovyjarjarantlr/collections/impl/Vector;

    .line 379
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->setGrammar(Lgroovyjarjarantlr/Grammar;)V

    .line 380
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-nez v0, :cond_1

    .line 381
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v1, "Internal error generating lexer"

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    .line 386
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr/Grammar;->getClassName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->setupOutput(Ljava/lang/String;)V

    const/4 v0, 0x0

    .line 388
    iput-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    const/4 v1, 0x1

    .line 389
    iput-boolean v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    .line 391
    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 394
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genHeader()V

    const-string v2, "### import antlr and other modules .."

    .line 397
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v2, "import sys"

    .line 398
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v2, "import antlr"

    .line 399
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v2, ""

    .line 400
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v3, "version = sys.version.split()[0]"

    .line 401
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v3, "if version < \'2.2.1\':"

    .line 402
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 403
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v1

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v3, "False = 0"

    .line 404
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 405
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v3, v1

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v3, "if version < \'2.3\':"

    .line 406
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 407
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v1

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v3, "True = not False"

    .line 408
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 409
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v3, v1

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v3, "### header action >>> "

    .line 411
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 412
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->behavior:Lgroovyjarjarantlr/DefineGrammarSymbols;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/DefineGrammarSymbols;->getHeaderAction(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printActionCode(Ljava/lang/String;I)V

    const-string v3, "### header action <<< "

    .line 413
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v3, "### preamble action >>> "

    .line 416
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 417
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->preambleAction:Lgroovyjarjarantlr/Token;

    invoke-virtual {v3}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printActionCode(Ljava/lang/String;I)V

    const-string v3, "### preamble action <<< "

    .line 418
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 422
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->superClass:Ljava/lang/String;

    if-eqz v3, :cond_2

    .line 423
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->superClass:Ljava/lang/String;

    goto :goto_0

    .line 426
    :cond_2
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "groovyjarjarantlr."

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v4}, Lgroovyjarjarantlr/Grammar;->getSuperClass()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    .line 431
    :goto_0
    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr/Grammar;->options:Ljava/util/Hashtable;

    const-string v5, "classHeaderPrefix"

    invoke-virtual {v4, v5}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr/Token;

    const-string v5, "\""

    if-eqz v4, :cond_3

    .line 433
    invoke-virtual {v4}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4, v5, v5}, Lgroovyjarjarantlr/StringUtils;->stripFrontBack(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    :cond_3
    const-string v4, "### >>>The Literals<<<"

    .line 440
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v4, "literals = {}"

    .line 441
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 442
    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {v4}, Lgroovyjarjarantlr/TokenManager;->getTokenSymbolKeys()Ljava/util/Enumeration;

    move-result-object v4

    .line 443
    :cond_4
    :goto_1
    invoke-interface {v4}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v6

    if-eqz v6, :cond_6

    .line 444
    invoke-interface {v4}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    .line 445
    invoke-virtual {v6, v0}, Ljava/lang/String;->charAt(I)C

    move-result v7

    const/16 v8, 0x22

    if-eq v7, v8, :cond_5

    goto :goto_1

    .line 448
    :cond_5
    iget-object v7, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v7, v7, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {v7, v6}, Lgroovyjarjarantlr/TokenManager;->getTokenSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/TokenSymbol;

    move-result-object v6

    .line 449
    instance-of v7, v6, Lgroovyjarjarantlr/StringLiteralSymbol;

    if-eqz v7, :cond_4

    .line 450
    check-cast v6, Lgroovyjarjarantlr/StringLiteralSymbol;

    .line 451
    new-instance v7, Ljava/lang/StringBuffer;

    invoke-direct {v7}, Ljava/lang/StringBuffer;-><init>()V

    const-string v8, "literals[u"

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v6}, Lgroovyjarjarantlr/StringLiteralSymbol;->getId()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    const-string v8, "] = "

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v6}, Lgroovyjarjarantlr/StringLiteralSymbol;->getTokenType()I

    move-result v6

    invoke-virtual {v7, v6}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_1

    .line 454
    :cond_6
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 455
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->flushTokens()V

    .line 458
    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->genJavadocComment(Lgroovyjarjarantlr/Grammar;)V

    .line 461
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    const-string v6, "class "

    invoke-virtual {v4, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    iget-object v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lexerClassName:Ljava/lang/String;

    invoke-virtual {v4, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v6, "("

    invoke-virtual {v4, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v6, ") :"

    invoke-virtual {v4, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 462
    iget v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v4, v1

    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 464
    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->printGrammarAction(Lgroovyjarjarantlr/Grammar;)V

    const-string v4, "def __init__(self, *argv, **kwargs) :"

    .line 470
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 471
    iget v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v4, v1

    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 472
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v4, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v4, ".__init__(self, *argv, **kwargs)"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 477
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "self.caseSensitiveLiterals = "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget-boolean v4, p1, Lgroovyjarjarantlr/LexerGrammar;->caseSensitiveLiterals:Z

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->toString(Z)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 478
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "self.setCaseSensitive("

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget-boolean p1, p1, Lgroovyjarjarantlr/LexerGrammar;->caseSensitive:Z

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->toString(Z)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v3, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v3, ")"

    invoke-virtual {p1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string p1, "self.literals = literals"

    .line 479
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 483
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean p1, p1, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-eqz p1, :cond_9

    const-string p1, "ruleNames[] = ["

    .line 484
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 485
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr/Grammar;->rules:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/Vector;->elements()Ljava/util/Enumeration;

    move-result-object p1

    .line 487
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v1

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 488
    :cond_7
    :goto_2
    invoke-interface {p1}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v3

    if-eqz v3, :cond_8

    .line 489
    invoke-interface {p1}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr/GrammarSymbol;

    .line 490
    instance-of v4, v3, Lgroovyjarjarantlr/RuleSymbol;

    if-eqz v4, :cond_7

    .line 491
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    check-cast v3, Lgroovyjarjarantlr/RuleSymbol;

    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v4, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v4, "\","

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_2

    .line 493
    :cond_8
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr p1, v1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string p1, "]"

    .line 494
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 497
    :cond_9
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genHeaderInit(Lgroovyjarjarantlr/Grammar;)V

    .line 499
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr p1, v1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 508
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genNextToken()V

    .line 509
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 512
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object p1, p1, Lgroovyjarjarantlr/Grammar;->rules:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/Vector;->elements()Ljava/util/Enumeration;

    move-result-object p1

    move v1, v0

    .line 514
    :goto_3
    invoke-interface {p1}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v3

    if-eqz v3, :cond_b

    .line 515
    invoke-interface {p1}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr/RuleSymbol;

    .line 517
    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v4

    const-string v5, "mnextToken"

    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_a

    add-int/lit8 v4, v1, 0x1

    .line 518
    invoke-virtual {p0, v3, v0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genRule(Lgroovyjarjarantlr/RuleSymbol;ZI)V

    move v1, v4

    .line 520
    :cond_a
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->exitIfError()V

    goto :goto_3

    .line 524
    :cond_b
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean p1, p1, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-eqz p1, :cond_c

    .line 525
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genSemPredMap()V

    .line 528
    :cond_c
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->bitsetsUsed:Lgroovyjarjarantlr/collections/impl/Vector;

    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    check-cast v0, Lgroovyjarjarantlr/LexerGrammar;

    iget-object v0, v0, Lgroovyjarjarantlr/LexerGrammar;->charVocabulary:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0}, Lgroovyjarjarantlr/collections/impl/BitSet;->size()I

    move-result v0

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBitsets(Lgroovyjarjarantlr/collections/impl/Vector;I)V

    .line 529
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 531
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genHeaderMain(Lgroovyjarjarantlr/Grammar;)V

    .line 534
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Ljava/io/PrintWriter;->close()V

    const/4 p1, 0x0

    .line 535
    iput-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    return-void
.end method

.method public gen(Lgroovyjarjarantlr/OneOrMoreBlock;)V
    .locals 10

    .line 604
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 606
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockPreamble(Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 608
    invoke-virtual {p1}, Lgroovyjarjarantlr/OneOrMoreBlock;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 610
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "_cnt_"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {p1}, Lgroovyjarjarantlr/OneOrMoreBlock;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    .line 613
    :cond_0
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "_cnt"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget v1, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->ID:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    .line 615
    :goto_0
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, ""

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, "= 0"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "while True:"

    .line 616
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 617
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v2, 0x1

    add-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 618
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 621
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockInitAction(Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 624
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    .line 625
    invoke-virtual {p1}, Lgroovyjarjarantlr/OneOrMoreBlock;->getLabel()Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_1

    .line 626
    invoke-virtual {p1}, Lgroovyjarjarantlr/OneOrMoreBlock;->getLabel()Ljava/lang/String;

    move-result-object v4

    iput-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    .line 629
    :cond_1
    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v4, v4, Lgroovyjarjarantlr/Grammar;->theLLkAnalyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    invoke-interface {v4, p1}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->deterministic(Lgroovyjarjarantlr/OneOrMoreBlock;)Z

    .line 642
    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v4, v4, Lgroovyjarjarantlr/Grammar;->maxk:I

    .line 644
    iget-boolean v5, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->greedy:Z

    const/4 v6, 0x0

    if-nez v5, :cond_2

    iget v5, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->exitLookaheadDepth:I

    iget-object v7, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v7, v7, Lgroovyjarjarantlr/Grammar;->maxk:I

    if-gt v5, v7, :cond_2

    iget-object v5, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->exitCache:[Lgroovyjarjarantlr/Lookahead;

    iget v7, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->exitLookaheadDepth:I

    aget-object v5, v5, v7

    invoke-virtual {v5}, Lgroovyjarjarantlr/Lookahead;->containsEpsilon()Z

    move-result v5

    if-eqz v5, :cond_2

    .line 649
    iget v4, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->exitLookaheadDepth:I

    goto :goto_1

    .line 653
    :cond_2
    iget-boolean v5, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->greedy:Z

    if-nez v5, :cond_3

    iget v5, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->exitLookaheadDepth:I

    const v7, 0x7fffffff

    if-ne v5, v7, :cond_3

    :goto_1
    move v5, v2

    goto :goto_2

    :cond_3
    move v5, v6

    :goto_2
    const-string v7, "if "

    const-string v8, "break"

    if-eqz v5, :cond_4

    .line 663
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    const-string v9, "### nongreedy (...)+ loop; exit depth is "

    invoke-virtual {v5, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    iget v9, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->exitLookaheadDepth:I

    invoke-virtual {v5, v9}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 665
    iget-object v5, p1, Lgroovyjarjarantlr/OneOrMoreBlock;->exitCache:[Lgroovyjarjarantlr/Lookahead;

    invoke-virtual {p0, v5, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->getLookaheadTestExpression([Lgroovyjarjarantlr/Lookahead;I)Ljava/lang/String;

    move-result-object v4

    const-string v5, "### nongreedy exit test"

    .line 670
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 671
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v5, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    const-string v9, " >= 1 and "

    invoke-virtual {v5, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v5, ":"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 672
    iget v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v4, v2

    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 673
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 674
    iget v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v4, v2

    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 678
    :cond_4
    iget v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 679
    invoke-virtual {p0, p1, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->genCommonBlock(Lgroovyjarjarantlr/AlternativeBlock;Z)Lgroovyjarjarantlr/PythonBlockFinishingInfo;

    move-result-object p1

    .line 680
    invoke-direct {p0, p1, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockFinish(Lgroovyjarjarantlr/PythonBlockFinishingInfo;Ljava/lang/String;)V

    .line 681
    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 685
    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 686
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v4, " += 1"

    invoke-virtual {p1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 687
    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 688
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr p1, v2

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 689
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, " < 1:"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 690
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr p1, v2

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 691
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->throwNoViable:Ljava/lang/String;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 692
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr p1, v2

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 694
    iput-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    return-void
.end method

.method public gen(Lgroovyjarjarantlr/ParserGrammar;)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 703
    iget-boolean v0, p1, Lgroovyjarjarantlr/ParserGrammar;->debuggingOutput:Z

    if-eqz v0, :cond_0

    .line 704
    new-instance v0, Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-direct {v0}, Lgroovyjarjarantlr/collections/impl/Vector;-><init>()V

    iput-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->semPreds:Lgroovyjarjarantlr/collections/impl/Vector;

    .line 706
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->setGrammar(Lgroovyjarjarantlr/Grammar;)V

    .line 707
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p1, p1, Lgroovyjarjarantlr/ParserGrammar;

    if-nez p1, :cond_1

    .line 708
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v0, "Internal error generating parser"

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    .line 713
    :cond_1
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p1}, Lgroovyjarjarantlr/Grammar;->getClassName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->setupOutput(Ljava/lang/String;)V

    .line 715
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean p1, p1, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    iput-boolean p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    const/4 p1, 0x0

    .line 717
    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 720
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genHeader()V

    const-string v0, "### import antlr and other modules .."

    .line 723
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "import sys"

    .line 724
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "import antlr"

    .line 725
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, ""

    .line 726
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "version = sys.version.split()[0]"

    .line 727
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "if version < \'2.2.1\':"

    .line 728
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 729
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v2, 0x1

    add-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "False = 0"

    .line 730
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 731
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "if version < \'2.3\':"

    .line 732
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 733
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "True = not False"

    .line 734
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 735
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "### header action >>> "

    .line 737
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 738
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->behavior:Lgroovyjarjarantlr/DefineGrammarSymbols;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/DefineGrammarSymbols;->getHeaderAction(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printActionCode(Ljava/lang/String;I)V

    const-string v1, "### header action <<< "

    .line 739
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "### preamble action>>>"

    .line 741
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 743
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr/Grammar;->preambleAction:Lgroovyjarjarantlr/Token;

    invoke-virtual {v1}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printActionCode(Ljava/lang/String;I)V

    const-string v1, "### preamble action <<<"

    .line 744
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 746
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->flushTokens()V

    .line 750
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr/Grammar;->superClass:Ljava/lang/String;

    if-eqz v1, :cond_2

    .line 751
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr/Grammar;->superClass:Ljava/lang/String;

    goto :goto_0

    .line 753
    :cond_2
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "groovyjarjarantlr."

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v3}, Lgroovyjarjarantlr/Grammar;->getSuperClass()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    .line 756
    :goto_0
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->genJavadocComment(Lgroovyjarjarantlr/Grammar;)V

    .line 760
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->options:Ljava/util/Hashtable;

    const-string v4, "classHeaderPrefix"

    invoke-virtual {v3, v4}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr/Token;

    const-string v4, "\""

    if-eqz v3, :cond_3

    .line 762
    invoke-virtual {v3}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3, v4, v4}, Lgroovyjarjarantlr/StringUtils;->stripFrontBack(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 768
    :cond_3
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v5, "class "

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->parserClassName:Ljava/lang/String;

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v5, "("

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->print(Ljava/lang/String;)V

    const-string v3, "):"

    .line 769
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 770
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v2

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 774
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v3, v3, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-eqz v3, :cond_6

    const-string v3, "_ruleNames = ["

    .line 775
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 777
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->rules:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v3}, Lgroovyjarjarantlr/collections/impl/Vector;->elements()Ljava/util/Enumeration;

    move-result-object v3

    .line 779
    iget v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v5, v2

    iput v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 780
    :cond_4
    :goto_1
    invoke-interface {v3}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v5

    if-eqz v5, :cond_5

    .line 781
    invoke-interface {v3}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovyjarjarantlr/GrammarSymbol;

    .line 782
    instance-of v6, v5, Lgroovyjarjarantlr/RuleSymbol;

    if-eqz v6, :cond_4

    .line 783
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v6, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    check-cast v5, Lgroovyjarjarantlr/RuleSymbol;

    invoke-virtual {v5}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v6, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    const-string v6, "\","

    invoke-virtual {v5, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_1

    .line 785
    :cond_5
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v3, v2

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v3, "]"

    .line 786
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 790
    :cond_6
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->printGrammarAction(Lgroovyjarjarantlr/Grammar;)V

    .line 793
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v3, "def __init__(self, *args, **kwargs):"

    .line 794
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 795
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v2

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 796
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v3, ".__init__(self, *args, **kwargs)"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "self.tokenNames = _tokenNames"

    .line 797
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 800
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v1, v1, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-eqz v1, :cond_7

    const-string v1, "self.ruleNames  = _ruleNames"

    .line 801
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "self.semPredNames = _semPredNames"

    .line 802
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "self.setupDebugging(self.tokenBuf)"

    .line 803
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 805
    :cond_7
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v1, v1, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v1, :cond_8

    const-string v1, "self.buildTokenTypeASTClassMap()"

    .line 806
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "self.astFactory = antlr.ASTFactory(self.getTokenTypeToASTClassMap())"

    .line 807
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 808
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    if-eqz v1, :cond_8

    .line 810
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "self.astFactory.setASTNodeClass("

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v3, ")"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 814
    :cond_8
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genHeaderInit(Lgroovyjarjarantlr/Grammar;)V

    .line 815
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 818
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr/Grammar;->rules:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v1}, Lgroovyjarjarantlr/collections/impl/Vector;->elements()Ljava/util/Enumeration;

    move-result-object v1

    move v3, p1

    .line 820
    :goto_2
    invoke-interface {v1}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v4

    if-eqz v4, :cond_b

    .line 821
    invoke-interface {v1}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarantlr/GrammarSymbol;

    .line 822
    instance-of v5, v4, Lgroovyjarjarantlr/RuleSymbol;

    if-eqz v5, :cond_a

    .line 823
    check-cast v4, Lgroovyjarjarantlr/RuleSymbol;

    .line 824
    iget-object v5, v4, Lgroovyjarjarantlr/RuleSymbol;->references:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v5}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v5

    if-nez v5, :cond_9

    move v5, v2

    goto :goto_3

    :cond_9
    move v5, p1

    :goto_3
    add-int/lit8 v6, v3, 0x1

    invoke-virtual {p0, v4, v5, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->genRule(Lgroovyjarjarantlr/RuleSymbol;ZI)V

    move v3, v6

    .line 826
    :cond_a
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->exitIfError()V

    goto :goto_2

    .line 830
    :cond_b
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v1, v1, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v1, :cond_c

    .line 831
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genTokenASTNodeMap()V

    .line 835
    :cond_c
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genTokenStrings()V

    .line 838
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->bitsetsUsed:Lgroovyjarjarantlr/collections/impl/Vector;

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {v2}, Lgroovyjarjarantlr/TokenManager;->maxTokenType()I

    move-result v2

    invoke-virtual {p0, v1, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBitsets(Lgroovyjarjarantlr/collections/impl/Vector;I)V

    .line 841
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v1, v1, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-eqz v1, :cond_d

    .line 842
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genSemPredMap()V

    .line 845
    :cond_d
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 847
    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 848
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genHeaderMain(Lgroovyjarjarantlr/Grammar;)V

    .line 850
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Ljava/io/PrintWriter;->close()V

    const/4 p1, 0x0

    .line 851
    iput-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    return-void
.end method

.method public gen(Lgroovyjarjarantlr/RuleRefElement;)V
    .locals 6

    .line 858
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    const-string v1, ")"

    if-eqz v0, :cond_0

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "genRR("

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 859
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v2, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/Grammar;->getSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/GrammarSymbol;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr/RuleSymbol;

    const-string v2, "Rule \'"

    if-eqz v0, :cond_12

    .line 860
    invoke-virtual {v0}, Lgroovyjarjarantlr/RuleSymbol;->isDefined()Z

    move-result v3

    if-nez v3, :cond_1

    goto/16 :goto_2

    .line 865
    :cond_1
    instance-of v3, v0, Lgroovyjarjarantlr/RuleSymbol;

    if-nez v3, :cond_2

    .line 867
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "\'"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v2, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, "\' does not name a grammar rule"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLine()I

    move-result v3

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getColumn()I

    move-result p1

    invoke-virtual {v0, v1, v2, v3, p1}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;Ljava/lang/String;II)V

    return-void

    .line 871
    :cond_2
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genErrorTryForElement(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 875
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v3, v3, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v3, :cond_3

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLabel()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_3

    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v3, :cond_3

    .line 878
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLabel()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v4, " = antlr.ifelse(_t == antlr.ASTNULL, None, "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 882
    :cond_3
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v1, v1, Lgroovyjarjarantlr/LexerGrammar;

    const/4 v3, 0x3

    if-eqz v1, :cond_5

    iget-boolean v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz v1, :cond_4

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getAutoGenType()I

    move-result v1

    if-ne v1, v3, :cond_5

    :cond_4
    const-string v1, "_saveIndex = self.text.length()"

    .line 883
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 887
    :cond_5
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printTabs()V

    .line 888
    iget-object v1, p1, Lgroovyjarjarantlr/RuleRefElement;->idAssign:Ljava/lang/String;

    if-eqz v1, :cond_7

    .line 890
    iget-object v0, v0, Lgroovyjarjarantlr/RuleSymbol;->block:Lgroovyjarjarantlr/RuleBlock;

    iget-object v0, v0, Lgroovyjarjarantlr/RuleBlock;->returnAction:Ljava/lang/String;

    if-nez v0, :cond_6

    .line 891
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v2, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, "\' has no return type"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLine()I

    move-result v4

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getColumn()I

    move-result v5

    invoke-virtual {v0, v1, v2, v4, v5}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    .line 893
    :cond_6
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    iget-object v1, p1, Lgroovyjarjarantlr/RuleRefElement;->idAssign:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, "="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    goto :goto_0

    .line 897
    :cond_7
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v1, v1, Lgroovyjarjarantlr/LexerGrammar;

    if-nez v1, :cond_8

    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v1, :cond_8

    iget-object v0, v0, Lgroovyjarjarantlr/RuleSymbol;->block:Lgroovyjarjarantlr/RuleBlock;

    iget-object v0, v0, Lgroovyjarjarantlr/RuleBlock;->returnAction:Ljava/lang/String;

    if-eqz v0, :cond_8

    .line 898
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v2, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, "\' returns a value"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLine()I

    move-result v4

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getColumn()I

    move-result v5

    invoke-virtual {v0, v1, v2, v4, v5}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    .line 903
    :cond_8
    :goto_0
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->GenRuleInvocation(Lgroovyjarjarantlr/RuleRefElement;)V

    .line 906
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_a

    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz v0, :cond_9

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getAutoGenType()I

    move-result v0

    if-ne v0, v3, :cond_a

    :cond_9
    const-string v0, "self.text.setLength(_saveIndex)"

    .line 907
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 911
    :cond_a
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v0, :cond_11

    .line 912
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    if-eqz v0, :cond_c

    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v0, :cond_b

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_c

    :cond_b
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    if-eqz v0, :cond_c

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getAutoGenType()I

    .line 923
    :cond_c
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v0, :cond_d

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_d

    .line 925
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, "_AST = self.returnAST"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 927
    :cond_d
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    if-eqz v0, :cond_10

    .line 928
    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getAutoGenType()I

    move-result v0

    const/4 v1, 0x1

    if-eq v0, v1, :cond_f

    const/4 v1, 0x2

    if-eq v0, v1, :cond_e

    goto :goto_1

    .line 933
    :cond_e
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v1, "Internal: encountered ^ after rule reference"

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;)V

    goto :goto_1

    :cond_f
    const-string v0, "self.addASTChild(currentAST, self.returnAST)"

    .line 930
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 941
    :cond_10
    :goto_1
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_11

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_11

    .line 942
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = self._returnToken"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 948
    :cond_11
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genErrorCatchForElement(Lgroovyjarjarantlr/AlternativeElement;)V

    return-void

    .line 862
    :cond_12
    :goto_2
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v2, p1, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, "\' is not defined"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getLine()I

    move-result v3

    invoke-virtual {p1}, Lgroovyjarjarantlr/RuleRefElement;->getColumn()I

    move-result p1

    invoke-virtual {v0, v1, v2, v3, p1}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;Ljava/lang/String;II)V

    return-void
.end method

.method public gen(Lgroovyjarjarantlr/StringLiteralElement;)V
    .locals 3

    .line 955
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v0, :cond_0

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "genString("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, ")"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 958
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr/StringLiteralElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_1

    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v0, :cond_1

    .line 959
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/StringLiteralElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, ""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 963
    :cond_1
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genElementAST(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 966
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    const/4 v1, 0x1

    if-eqz v0, :cond_2

    .line 967
    invoke-virtual {p1}, Lgroovyjarjarantlr/StringLiteralElement;->getAutoGenType()I

    move-result v2

    if-ne v2, v1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v1, 0x0

    :goto_0
    iput-boolean v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    .line 970
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genMatch(Lgroovyjarjarantlr/GrammarAtom;)V

    .line 972
    iput-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    .line 975
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p1, p1, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz p1, :cond_3

    const-string p1, "_t = _t.getNextSibling()"

    .line 976
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_3
    return-void
.end method

.method public gen(Lgroovyjarjarantlr/TokenRangeElement;)V
    .locals 2

    .line 984
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genErrorTryForElement(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 985
    invoke-virtual {p1}, Lgroovyjarjarantlr/TokenRangeElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v0, :cond_0

    .line 986
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/TokenRangeElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 990
    :cond_0
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genElementAST(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 993
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "self.matchRange(u"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p1, Lgroovyjarjarantlr/TokenRangeElement;->beginText:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, ", u"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p1, Lgroovyjarjarantlr/TokenRangeElement;->endText:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 994
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genErrorCatchForElement(Lgroovyjarjarantlr/AlternativeElement;)V

    return-void
.end method

.method public gen(Lgroovyjarjarantlr/TokenRefElement;)V
    .locals 3

    .line 1001
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v0, :cond_0

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "genTokenRef("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, ")"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 1002
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_1

    .line 1003
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v1, "Token reference found in lexer"

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    .line 1005
    :cond_1
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genErrorTryForElement(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 1007
    invoke-virtual {p1}, Lgroovyjarjarantlr/TokenRefElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_2

    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v0, :cond_2

    .line 1008
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/TokenRefElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, ""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1012
    :cond_2
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genElementAST(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 1014
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genMatch(Lgroovyjarjarantlr/GrammarAtom;)V

    .line 1015
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genErrorCatchForElement(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 1018
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p1, p1, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz p1, :cond_3

    const-string p1, "_t = _t.getNextSibling()"

    .line 1019
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_3
    return-void
.end method

.method public gen(Lgroovyjarjarantlr/TreeElement;)V
    .locals 6

    .line 1025
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "_t"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget v1, p1, Lgroovyjarjarantlr/TreeElement;->ID:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = _t"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1028
    iget-object v0, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {v0}, Lgroovyjarjarantlr/GrammarAtom;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 1029
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    iget-object v1, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {v1}, Lgroovyjarjarantlr/GrammarAtom;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = antlr.ifelse(_t == antlr.ASTNULL, None, _t)"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1033
    :cond_0
    iget-object v0, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {v0}, Lgroovyjarjarantlr/GrammarAtom;->getAutoGenType()I

    move-result v0

    const/4 v1, 0x3

    const/4 v2, 0x1

    if-ne v0, v1, :cond_1

    .line 1034
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v1}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lgroovyjarjarantlr/TreeElement;->getLine()I

    move-result v3

    invoke-virtual {p1}, Lgroovyjarjarantlr/TreeElement;->getColumn()I

    move-result v4

    const-string v5, "Suffixing a root node with \'!\' is not implemented"

    invoke-virtual {v0, v5, v1, v3, v4}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;Ljava/lang/String;II)V

    .line 1036
    iget-object v0, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/GrammarAtom;->setAutoGenType(I)V

    .line 1038
    :cond_1
    iget-object v0, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {v0}, Lgroovyjarjarantlr/GrammarAtom;->getAutoGenType()I

    move-result v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_2

    .line 1039
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v1}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lgroovyjarjarantlr/TreeElement;->getLine()I

    move-result v3

    invoke-virtual {p1}, Lgroovyjarjarantlr/TreeElement;->getColumn()I

    move-result v4

    const-string v5, "Suffixing a root node with \'^\' is redundant; already a root"

    invoke-virtual {v0, v5, v1, v3, v4}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    .line 1041
    iget-object v0, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/GrammarAtom;->setAutoGenType(I)V

    .line 1045
    :cond_2
    iget-object v0, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    invoke-direct {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genElementAST(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 1046
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v0, :cond_3

    .line 1048
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "_currentAST"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget v1, p1, Lgroovyjarjarantlr/TreeElement;->ID:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = currentAST.copy()"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "currentAST.root = currentAST.child"

    .line 1050
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "currentAST.child = None"

    .line 1051
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1055
    :cond_3
    iget-object v0, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    instance-of v0, v0, Lgroovyjarjarantlr/WildcardElement;

    if-eqz v0, :cond_4

    const-string v0, "if not _t: raise antlr.MismatchedTokenException()"

    .line 1056
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 1059
    :cond_4
    iget-object v0, p1, Lgroovyjarjarantlr/TreeElement;->root:Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genMatch(Lgroovyjarjarantlr/GrammarAtom;)V

    :goto_0
    const-string v0, "_t = _t.getFirstChild()"

    .line 1062
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const/4 v0, 0x0

    .line 1065
    :goto_1
    invoke-virtual {p1}, Lgroovyjarjarantlr/TreeElement;->getAlternatives()Lgroovyjarjarantlr/collections/impl/Vector;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v1

    if-ge v0, v1, :cond_6

    .line 1066
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr/TreeElement;->getAlternativeAt(I)Lgroovyjarjarantlr/Alternative;

    move-result-object v1

    .line 1067
    iget-object v1, v1, Lgroovyjarjarantlr/Alternative;->head:Lgroovyjarjarantlr/AlternativeElement;

    :goto_2
    if-eqz v1, :cond_5

    .line 1069
    invoke-virtual {v1}, Lgroovyjarjarantlr/AlternativeElement;->generate()V

    .line 1070
    iget-object v1, v1, Lgroovyjarjarantlr/AlternativeElement;->next:Lgroovyjarjarantlr/AlternativeElement;

    goto :goto_2

    :cond_5
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    .line 1074
    :cond_6
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    const-string v1, ""

    if-eqz v0, :cond_7

    .line 1077
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "currentAST = _currentAST"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget v2, p1, Lgroovyjarjarantlr/TreeElement;->ID:I

    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1080
    :cond_7
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "_t = _t"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget p1, p1, Lgroovyjarjarantlr/TreeElement;->ID:I

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string p1, "_t = _t.getNextSibling()"

    .line 1082
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    return-void
.end method

.method public gen(Lgroovyjarjarantlr/TreeWalkerGrammar;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1090
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->setGrammar(Lgroovyjarjarantlr/Grammar;)V

    .line 1091
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p1, p1, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-nez p1, :cond_0

    .line 1092
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v0, "Internal error generating tree-walker"

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    .line 1097
    :cond_0
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p1}, Lgroovyjarjarantlr/Grammar;->getClassName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->setupOutput(Ljava/lang/String;)V

    .line 1099
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean p1, p1, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    iput-boolean p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    const/4 p1, 0x0

    .line 1100
    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1103
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genHeader()V

    const-string v0, "### import antlr and other modules .."

    .line 1106
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "import sys"

    .line 1107
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "import antlr"

    .line 1108
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, ""

    .line 1109
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "version = sys.version.split()[0]"

    .line 1110
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "if version < \'2.2.1\':"

    .line 1111
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1112
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v2, 0x1

    add-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "False = 0"

    .line 1113
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1114
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "if version < \'2.3\':"

    .line 1115
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1116
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "True = not False"

    .line 1117
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1118
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "### header action >>> "

    .line 1120
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1121
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->behavior:Lgroovyjarjarantlr/DefineGrammarSymbols;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/DefineGrammarSymbols;->getHeaderAction(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printActionCode(Ljava/lang/String;I)V

    const-string v1, "### header action <<< "

    .line 1122
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1124
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->flushTokens()V

    const-string v1, "### user code>>>"

    .line 1126
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1128
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr/Grammar;->preambleAction:Lgroovyjarjarantlr/Token;

    invoke-virtual {v1}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printActionCode(Ljava/lang/String;I)V

    const-string v1, "### user code<<<"

    .line 1129
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1133
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr/Grammar;->superClass:Ljava/lang/String;

    if-eqz v1, :cond_1

    .line 1134
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr/Grammar;->superClass:Ljava/lang/String;

    goto :goto_0

    .line 1137
    :cond_1
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "groovyjarjarantlr."

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v3}, Lgroovyjarjarantlr/Grammar;->getSuperClass()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    .line 1139
    :goto_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1143
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->options:Ljava/util/Hashtable;

    const-string v4, "classHeaderPrefix"

    invoke-virtual {v3, v4}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr/Token;

    if-eqz v3, :cond_2

    .line 1145
    invoke-virtual {v3}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v3

    const-string v4, "\""

    invoke-static {v3, v4, v4}, Lgroovyjarjarantlr/StringUtils;->stripFrontBack(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 1152
    :cond_2
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->genJavadocComment(Lgroovyjarjarantlr/Grammar;)V

    .line 1154
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "class "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeWalkerClassName:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v4, "("

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v4, "):"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1155
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v2

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1158
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v3, "# ctor .."

    .line 1159
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v3, "def __init__(self, *args, **kwargs):"

    .line 1160
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1161
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v2

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1162
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v3, ".__init__(self, *args, **kwargs)"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "self.tokenNames = _tokenNames"

    .line 1163
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1164
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genHeaderInit(Lgroovyjarjarantlr/Grammar;)V

    .line 1165
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v1, v2

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1166
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1169
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printGrammarAction(Lgroovyjarjarantlr/Grammar;)V

    .line 1172
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr/Grammar;->rules:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v0}, Lgroovyjarjarantlr/collections/impl/Vector;->elements()Ljava/util/Enumeration;

    move-result-object v0

    move v1, p1

    .line 1175
    :goto_1
    invoke-interface {v0}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v3

    if-eqz v3, :cond_5

    .line 1176
    invoke-interface {v0}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr/GrammarSymbol;

    .line 1177
    instance-of v4, v3, Lgroovyjarjarantlr/RuleSymbol;

    if-eqz v4, :cond_4

    .line 1178
    check-cast v3, Lgroovyjarjarantlr/RuleSymbol;

    .line 1179
    iget-object v4, v3, Lgroovyjarjarantlr/RuleSymbol;->references:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v4}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v4

    if-nez v4, :cond_3

    move v4, v2

    goto :goto_2

    :cond_3
    move v4, p1

    :goto_2
    add-int/lit8 v5, v1, 0x1

    invoke-virtual {p0, v3, v4, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genRule(Lgroovyjarjarantlr/RuleSymbol;ZI)V

    move v1, v5

    .line 1181
    :cond_4
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->exitIfError()V

    goto :goto_1

    .line 1185
    :cond_5
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genTokenStrings()V

    .line 1188
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->bitsetsUsed:Lgroovyjarjarantlr/collections/impl/Vector;

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v1, v1, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {v1}, Lgroovyjarjarantlr/TokenManager;->maxTokenType()I

    move-result v1

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBitsets(Lgroovyjarjarantlr/collections/impl/Vector;I)V

    .line 1190
    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1191
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genHeaderMain(Lgroovyjarjarantlr/Grammar;)V

    .line 1193
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    invoke-virtual {p1}, Ljava/io/PrintWriter;->close()V

    const/4 p1, 0x0

    .line 1194
    iput-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    return-void
.end method

.method public gen(Lgroovyjarjarantlr/WildcardElement;)V
    .locals 2

    .line 1202
    invoke-virtual {p1}, Lgroovyjarjarantlr/WildcardElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v0, :cond_0

    .line 1203
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/WildcardElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, ""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1207
    :cond_0
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genElementAST(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 1209
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/TreeWalkerGrammar;

    const/4 v1, 0x1

    if-eqz v0, :cond_1

    const-string p1, "if not _t:"

    .line 1210
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1211
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr p1, v1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string p1, "raise antlr.MismatchedTokenException()"

    .line 1212
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1213
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr p1, v1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    goto :goto_0

    .line 1215
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_5

    .line 1216
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    const/4 v1, 0x3

    if-eqz v0, :cond_3

    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz v0, :cond_2

    invoke-virtual {p1}, Lgroovyjarjarantlr/WildcardElement;->getAutoGenType()I

    move-result v0

    if-ne v0, v1, :cond_3

    :cond_2
    const-string v0, "_saveIndex = self.text.length()"

    .line 1218
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_3
    const-string v0, "self.matchNot(antlr.EOF_CHAR)"

    .line 1220
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1221
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_6

    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz v0, :cond_4

    invoke-virtual {p1}, Lgroovyjarjarantlr/WildcardElement;->getAutoGenType()I

    move-result p1

    if-ne p1, v1, :cond_6

    :cond_4
    const-string p1, "self.text.setLength(_saveIndex)"

    .line 1223
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 1227
    :cond_5
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v0, "self.matchNot("

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const/4 v0, 0x0

    invoke-direct {p0, v1, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->getValueString(IZ)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, ")"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1231
    :cond_6
    :goto_0
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p1, p1, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz p1, :cond_7

    const-string p1, "_t = _t.getNextSibling()"

    .line 1232
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_7
    return-void
.end method

.method public gen(Lgroovyjarjarantlr/ZeroOrMoreBlock;)V
    .locals 9

    .line 1241
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1242
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockPreamble(Lgroovyjarjarantlr/AlternativeBlock;)V

    const-string v0, "while True:"

    .line 1244
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1245
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v1, 0x1

    add-int/2addr v0, v1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1246
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1249
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockInitAction(Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 1252
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    .line 1253
    invoke-virtual {p1}, Lgroovyjarjarantlr/ZeroOrMoreBlock;->getLabel()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_0

    .line 1254
    invoke-virtual {p1}, Lgroovyjarjarantlr/ZeroOrMoreBlock;->getLabel()Ljava/lang/String;

    move-result-object v3

    iput-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    .line 1257
    :cond_0
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->theLLkAnalyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    invoke-interface {v3, p1}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->deterministic(Lgroovyjarjarantlr/ZeroOrMoreBlock;)Z

    .line 1270
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v3, v3, Lgroovyjarjarantlr/Grammar;->maxk:I

    .line 1272
    iget-boolean v4, p1, Lgroovyjarjarantlr/ZeroOrMoreBlock;->greedy:Z

    const/4 v5, 0x0

    if-nez v4, :cond_1

    iget v4, p1, Lgroovyjarjarantlr/ZeroOrMoreBlock;->exitLookaheadDepth:I

    iget-object v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v6, v6, Lgroovyjarjarantlr/Grammar;->maxk:I

    if-gt v4, v6, :cond_1

    iget-object v4, p1, Lgroovyjarjarantlr/ZeroOrMoreBlock;->exitCache:[Lgroovyjarjarantlr/Lookahead;

    iget v6, p1, Lgroovyjarjarantlr/ZeroOrMoreBlock;->exitLookaheadDepth:I

    aget-object v4, v4, v6

    invoke-virtual {v4}, Lgroovyjarjarantlr/Lookahead;->containsEpsilon()Z

    move-result v4

    if-eqz v4, :cond_1

    .line 1276
    iget v3, p1, Lgroovyjarjarantlr/ZeroOrMoreBlock;->exitLookaheadDepth:I

    goto :goto_0

    .line 1278
    :cond_1
    iget-boolean v4, p1, Lgroovyjarjarantlr/ZeroOrMoreBlock;->greedy:Z

    if-nez v4, :cond_2

    iget v4, p1, Lgroovyjarjarantlr/ZeroOrMoreBlock;->exitLookaheadDepth:I

    const v6, 0x7fffffff

    if-ne v4, v6, :cond_2

    :goto_0
    move v4, v1

    goto :goto_1

    :cond_2
    move v4, v5

    :goto_1
    const-string v6, "break"

    if-eqz v4, :cond_4

    .line 1283
    iget-boolean v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v4, :cond_3

    .line 1284
    sget-object v4, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v7, Ljava/lang/StringBuffer;

    invoke-direct {v7}, Ljava/lang/StringBuffer;-><init>()V

    const-string v8, "nongreedy (...)* loop; exit depth is "

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    iget v8, p1, Lgroovyjarjarantlr/ZeroOrMoreBlock;->exitLookaheadDepth:I

    invoke-virtual {v7, v8}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v4, v7}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 1287
    :cond_3
    iget-object v4, p1, Lgroovyjarjarantlr/ZeroOrMoreBlock;->exitCache:[Lgroovyjarjarantlr/Lookahead;

    invoke-virtual {p0, v4, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->getLookaheadTestExpression([Lgroovyjarjarantlr/Lookahead;I)Ljava/lang/String;

    move-result-object v3

    const-string v4, "###  nongreedy exit test"

    .line 1290
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1291
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    const-string v7, "if ("

    invoke-virtual {v4, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v4, "):"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1292
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v1

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1293
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1294
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v3, v1

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1298
    :cond_4
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1299
    invoke-virtual {p0, p1, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->genCommonBlock(Lgroovyjarjarantlr/AlternativeBlock;Z)Lgroovyjarjarantlr/PythonBlockFinishingInfo;

    move-result-object p1

    .line 1300
    invoke-direct {p0, p1, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockFinish(Lgroovyjarjarantlr/PythonBlockFinishingInfo;Ljava/lang/String;)V

    .line 1301
    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1303
    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1304
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr p1, v1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1307
    iput-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    return-void
.end method

.method protected genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;)V
    .locals 1

    .line 2227
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;)V

    return-void
.end method

.method protected genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;)V
    .locals 1

    .line 2231
    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, p1, v0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method protected genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 2236
    iget-object p3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->declaredASTVariables:Ljava/util/Hashtable;

    invoke-virtual {p3, p1}, Ljava/util/Hashtable;->contains(Ljava/lang/Object;)Z

    move-result p3

    if-eqz p3, :cond_0

    return-void

    .line 2240
    :cond_0
    new-instance p3, Ljava/lang/StringBuffer;

    invoke-direct {p3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p3, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    const-string p3, "_AST = None"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2243
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->declaredASTVariables:Ljava/util/Hashtable;

    invoke-virtual {p2, p1, p1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method protected genAlt(Lgroovyjarjarantlr/Alternative;Lgroovyjarjarantlr/AlternativeBlock;)V
    .locals 8

    .line 1316
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_0

    .line 1317
    invoke-virtual {p1}, Lgroovyjarjarantlr/Alternative;->getAutoGen()Z

    move-result v3

    if-eqz v3, :cond_0

    move v3, v2

    goto :goto_0

    :cond_0
    move v3, v1

    :goto_0
    iput-boolean v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    .line 1319
    iget-boolean v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz v3, :cond_1

    .line 1320
    invoke-virtual {p1}, Lgroovyjarjarantlr/Alternative;->getAutoGen()Z

    move-result v4

    if-eqz v4, :cond_1

    move v1, v2

    :cond_1
    iput-boolean v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    .line 1323
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeVariableMap:Ljava/util/Hashtable;

    .line 1324
    new-instance v4, Ljava/util/Hashtable;

    invoke-direct {v4}, Ljava/util/Hashtable;-><init>()V

    iput-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeVariableMap:Ljava/util/Hashtable;

    .line 1327
    iget-object v4, p1, Lgroovyjarjarantlr/Alternative;->exceptionSpec:Lgroovyjarjarantlr/ExceptionSpec;

    if-eqz v4, :cond_2

    const-string v4, "try:"

    .line 1328
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1329
    iget v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v4, v2

    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    :cond_2
    const-string v4, "pass"

    .line 1332
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1333
    iget-object v4, p1, Lgroovyjarjarantlr/Alternative;->head:Lgroovyjarjarantlr/AlternativeElement;

    .line 1334
    :goto_1
    instance-of v5, v4, Lgroovyjarjarantlr/BlockEndElement;

    if-nez v5, :cond_3

    .line 1335
    invoke-virtual {v4}, Lgroovyjarjarantlr/AlternativeElement;->generate()V

    .line 1336
    iget-object v4, v4, Lgroovyjarjarantlr/AlternativeElement;->next:Lgroovyjarjarantlr/AlternativeElement;

    goto :goto_1

    .line 1339
    :cond_3
    iget-boolean v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    if-eqz v4, :cond_5

    .line 1340
    instance-of v4, p2, Lgroovyjarjarantlr/RuleBlock;

    if-eqz v4, :cond_4

    .line 1342
    check-cast p2, Lgroovyjarjarantlr/RuleBlock;

    .line 1343
    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v4, v4, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    .line 1345
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p2}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v4, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    const-string v4, "_AST = currentAST.root"

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1346
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean p2, p2, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    goto :goto_2

    .line 1349
    :cond_4
    invoke-virtual {p2}, Lgroovyjarjarantlr/AlternativeBlock;->getLabel()Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_5

    .line 1350
    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v5}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p2}, Lgroovyjarjarantlr/AlternativeBlock;->getLine()I

    move-result v6

    invoke-virtual {p2}, Lgroovyjarjarantlr/AlternativeBlock;->getColumn()I

    move-result p2

    const-string v7, "Labeled subrules not yet supported"

    invoke-virtual {v4, v7, v5, v6, p2}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    .line 1356
    :cond_5
    :goto_2
    iget-object p2, p1, Lgroovyjarjarantlr/Alternative;->exceptionSpec:Lgroovyjarjarantlr/ExceptionSpec;

    if-eqz p2, :cond_6

    .line 1357
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr p2, v2

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1358
    iget-object p1, p1, Lgroovyjarjarantlr/Alternative;->exceptionSpec:Lgroovyjarjarantlr/ExceptionSpec;

    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genErrorHandler(Lgroovyjarjarantlr/ExceptionSpec;)V

    .line 1361
    :cond_6
    iput-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    .line 1362
    iput-boolean v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    .line 1364
    iput-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeVariableMap:Ljava/util/Hashtable;

    return-void
.end method

.method protected genBitsets(Lgroovyjarjarantlr/collections/impl/Vector;I)V
    .locals 2

    const-string v0, ""

    .line 1380
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const/4 v0, 0x0

    .line 1381
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    .line 1382
    invoke-virtual {p1, v0}, Lgroovyjarjarantlr/collections/impl/Vector;->elementAt(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 1384
    invoke-virtual {v1, p2}, Lgroovyjarjarantlr/collections/impl/BitSet;->growToInclude(I)V

    .line 1385
    invoke-direct {p0, v1, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBitSet(Lgroovyjarjarantlr/collections/impl/BitSet;I)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method protected genBlockInitAction(Lgroovyjarjarantlr/AlternativeBlock;)V
    .locals 3

    .line 1516
    iget-object v0, p1, Lgroovyjarjarantlr/AlternativeBlock;->initAction:Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 1517
    iget-object v0, p1, Lgroovyjarjarantlr/AlternativeBlock;->initAction:Ljava/lang/String;

    invoke-virtual {p1}, Lgroovyjarjarantlr/AlternativeBlock;->getLine()I

    move-result p1

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    const/4 v2, 0x0

    invoke-virtual {p0, v0, p1, v1, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->processActionForSpecialSymbols(Ljava/lang/String;ILgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printAction(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method protected genBlockPreamble(Lgroovyjarjarantlr/AlternativeBlock;)V
    .locals 6

    .line 1528
    instance-of v0, p1, Lgroovyjarjarantlr/RuleBlock;

    if-eqz v0, :cond_7

    .line 1529
    check-cast p1, Lgroovyjarjarantlr/RuleBlock;

    .line 1530
    iget-object v0, p1, Lgroovyjarjarantlr/RuleBlock;->labeledElements:Lgroovyjarjarantlr/collections/impl/Vector;

    if-eqz v0, :cond_7

    const/4 v0, 0x0

    .line 1531
    :goto_0
    iget-object v1, p1, Lgroovyjarjarantlr/RuleBlock;->labeledElements:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v1}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v1

    if-ge v0, v1, :cond_7

    .line 1532
    iget-object v1, p1, Lgroovyjarjarantlr/RuleBlock;->labeledElements:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/collections/impl/Vector;->elementAt(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarantlr/AlternativeElement;

    .line 1539
    instance-of v2, v1, Lgroovyjarjarantlr/RuleRefElement;

    const-string v3, " = "

    if-nez v2, :cond_2

    instance-of v4, v1, Lgroovyjarjarantlr/AlternativeBlock;

    if-eqz v4, :cond_0

    instance-of v4, v1, Lgroovyjarjarantlr/RuleBlock;

    if-nez v4, :cond_0

    instance-of v4, v1, Lgroovyjarjarantlr/SynPredBlock;

    if-nez v4, :cond_0

    goto :goto_1

    .line 1579
    :cond_0
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementInit:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1583
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v2, v2, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v2, :cond_6

    .line 1584
    instance-of v2, v1, Lgroovyjarjarantlr/GrammarAtom;

    if-eqz v2, :cond_1

    move-object v2, v1

    check-cast v2, Lgroovyjarjarantlr/GrammarAtom;

    invoke-virtual {v2}, Lgroovyjarjarantlr/GrammarAtom;->getASTNodeType()Ljava/lang/String;

    move-result-object v3

    if-eqz v3, :cond_1

    .line 1587
    invoke-virtual {v2}, Lgroovyjarjarantlr/GrammarAtom;->getASTNodeType()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v1, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;Ljava/lang/String;)V

    goto/16 :goto_2

    .line 1590
    :cond_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;)V

    goto/16 :goto_2

    :cond_2
    :goto_1
    if-nez v2, :cond_3

    .line 1546
    move-object v2, v1

    check-cast v2, Lgroovyjarjarantlr/AlternativeBlock;

    iget-boolean v4, v2, Lgroovyjarjarantlr/AlternativeBlock;->not:Z

    if-eqz v4, :cond_3

    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->analyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v5, v5, Lgroovyjarjarantlr/LexerGrammar;

    invoke-interface {v4, v2, v5}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->subruleCanBeInverted(Lgroovyjarjarantlr/AlternativeBlock;Z)Z

    move-result v2

    if-eqz v2, :cond_3

    .line 1554
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementInit:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1555
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v2, v2, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v2, :cond_6

    .line 1556
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;)V

    goto :goto_2

    .line 1560
    :cond_3
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v2, v2, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v2, :cond_4

    .line 1564
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genASTDeclaration(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 1566
    :cond_4
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v2, v2, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v2, :cond_5

    .line 1567
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    const-string v4, " = None"

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1569
    :cond_5
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v2, v2, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v2, :cond_6

    .line 1572
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementInit:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_6
    :goto_2
    add-int/lit8 v0, v0, 0x1

    goto/16 :goto_0

    :cond_7
    return-void
.end method

.method protected genCases(Lgroovyjarjarantlr/collections/impl/BitSet;)V
    .locals 3

    .line 1603
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v0, :cond_0

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "genCases("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, ")"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 1606
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/BitSet;->toArray()[I

    move-result-object p1

    .line 1608
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    const-string v0, "elif la1 and la1 in "

    .line 1611
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->print(Ljava/lang/String;)V

    .line 1613
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    const/4 v1, 0x0

    if-eqz v0, :cond_2

    const-string v0, "u\'"

    .line 1615
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    move v0, v1

    .line 1616
    :goto_0
    array-length v2, p1

    if-ge v0, v2, :cond_1

    .line 1617
    aget v2, p1, v0

    invoke-direct {p0, v2, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->getValueString(IZ)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    const-string p1, "\':\n"

    .line 1619
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    return-void

    :cond_2
    const-string v0, "["

    .line 1624
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    move v0, v1

    .line 1625
    :cond_3
    :goto_1
    array-length v2, p1

    if-ge v0, v2, :cond_4

    .line 1626
    aget v2, p1, v0

    invoke-direct {p0, v2, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->getValueString(IZ)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    add-int/lit8 v0, v0, 0x1

    .line 1627
    array-length v2, p1

    if-ge v0, v2, :cond_3

    const-string v2, ","

    .line 1628
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    goto :goto_1

    :cond_4
    const-string p1, "]:\n"

    .line 1630
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    return-void
.end method

.method public genCommonBlock(Lgroovyjarjarantlr/AlternativeBlock;Z)Lgroovyjarjarantlr/PythonBlockFinishingInfo;
    .locals 20

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    .line 1646
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1651
    new-instance v2, Lgroovyjarjarantlr/PythonBlockFinishingInfo;

    invoke-direct {v2}, Lgroovyjarjarantlr/PythonBlockFinishingInfo;-><init>()V

    .line 1655
    iget-boolean v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-eqz v3, :cond_0

    .line 1656
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/AlternativeBlock;->getAutoGen()Z

    move-result v6

    if-eqz v6, :cond_0

    move v6, v5

    goto :goto_0

    :cond_0
    move v6, v4

    :goto_0
    iput-boolean v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    .line 1658
    iget-boolean v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz v6, :cond_1

    .line 1659
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/AlternativeBlock;->getAutoGen()Z

    move-result v7

    if-eqz v7, :cond_1

    move v7, v5

    goto :goto_1

    :cond_1
    move v7, v4

    :goto_1
    iput-boolean v7, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    .line 1662
    iget-boolean v7, v1, Lgroovyjarjarantlr/AlternativeBlock;->not:Z

    const-string v8, ""

    if-eqz v7, :cond_6

    iget-object v7, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->analyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    iget-object v9, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v9, v9, Lgroovyjarjarantlr/LexerGrammar;

    invoke-interface {v7, v1, v9}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->subruleCanBeInverted(Lgroovyjarjarantlr/AlternativeBlock;Z)Z

    move-result v7

    if-eqz v7, :cond_6

    .line 1667
    iget-boolean v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v3, :cond_2

    sget-object v3, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v4, "special case: ~(subrule)"

    invoke-virtual {v3, v4}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 1668
    :cond_2
    iget-object v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->analyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    invoke-interface {v3, v5, v1}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->look(ILgroovyjarjarantlr/AlternativeBlock;)Lgroovyjarjarantlr/Lookahead;

    move-result-object v3

    .line 1670
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/AlternativeBlock;->getLabel()Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_3

    iget v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    if-nez v4, :cond_3

    .line 1672
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/AlternativeBlock;->getLabel()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v5, " = "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    iget-object v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->lt1Value:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1676
    :cond_3
    invoke-direct/range {p0 .. p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genElementAST(Lgroovyjarjarantlr/AlternativeElement;)V

    .line 1679
    iget-object v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v1, v1, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v1, :cond_4

    const-string v8, "_t, "

    .line 1684
    :cond_4
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "self.match("

    invoke-virtual {v1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    iget-object v3, v3, Lgroovyjarjarantlr/Lookahead;->fset:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->markBitsetForGen(Lgroovyjarjarantlr/collections/impl/BitSet;)I

    move-result v3

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->getBitsetName(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v3, ")"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1687
    iget-object v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v1, v1, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v1, :cond_5

    const-string v1, "_t = _t.getNextSibling()"

    .line 1688
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_5
    return-object v2

    .line 1695
    :cond_6
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/AlternativeBlock;->getAlternatives()Lgroovyjarjarantlr/collections/impl/Vector;

    move-result-object v7

    invoke-virtual {v7}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v7

    if-ne v7, v5, :cond_9

    .line 1697
    invoke-virtual {v1, v4}, Lgroovyjarjarantlr/AlternativeBlock;->getAlternativeAt(I)Lgroovyjarjarantlr/Alternative;

    move-result-object v7

    .line 1699
    iget-object v9, v7, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    if-eqz v9, :cond_7

    .line 1700
    iget-object v9, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-object v10, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v10}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v1, v4}, Lgroovyjarjarantlr/AlternativeBlock;->getAlternativeAt(I)Lgroovyjarjarantlr/Alternative;

    move-result-object v11

    iget-object v11, v11, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    invoke-virtual {v11}, Lgroovyjarjarantlr/SynPredBlock;->getLine()I

    move-result v11

    invoke-virtual {v1, v4}, Lgroovyjarjarantlr/AlternativeBlock;->getAlternativeAt(I)Lgroovyjarjarantlr/Alternative;

    move-result-object v12

    iget-object v12, v12, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    invoke-virtual {v12}, Lgroovyjarjarantlr/SynPredBlock;->getColumn()I

    move-result v12

    const-string v13, "Syntactic predicate superfluous for single alternative"

    invoke-virtual {v9, v13, v10, v11, v12}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    :cond_7
    if-eqz p2, :cond_9

    .line 1709
    iget-object v3, v7, Lgroovyjarjarantlr/Alternative;->semPred:Ljava/lang/String;

    if-eqz v3, :cond_8

    .line 1711
    iget-object v3, v7, Lgroovyjarjarantlr/Alternative;->semPred:Ljava/lang/String;

    iget v4, v1, Lgroovyjarjarantlr/AlternativeBlock;->line:I

    invoke-virtual {v0, v3, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->genSemPred(Ljava/lang/String;I)V

    .line 1713
    :cond_8
    invoke-virtual {v0, v7, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genAlt(Lgroovyjarjarantlr/Alternative;Lgroovyjarjarantlr/AlternativeBlock;)V

    return-object v2

    :cond_9
    move v7, v4

    move v9, v7

    .line 1728
    :goto_2
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/AlternativeBlock;->getAlternatives()Lgroovyjarjarantlr/collections/impl/Vector;

    move-result-object v10

    invoke-virtual {v10}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v10

    if-ge v7, v10, :cond_b

    .line 1729
    invoke-virtual {v1, v7}, Lgroovyjarjarantlr/AlternativeBlock;->getAlternativeAt(I)Lgroovyjarjarantlr/Alternative;

    move-result-object v10

    .line 1730
    invoke-static {v10}, Lgroovyjarjarantlr/PythonCodeGenerator;->suitableForCaseExpression(Lgroovyjarjarantlr/Alternative;)Z

    move-result v10

    if-eqz v10, :cond_a

    add-int/lit8 v9, v9, 0x1

    :cond_a
    add-int/lit8 v7, v7, 0x1

    goto :goto_2

    .line 1736
    :cond_b
    iget v7, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->makeSwitchThreshold:I

    const-string v10, "else:"

    const-string v11, "_t = antlr.ASTNULL"

    const-string v12, "if not _t:"

    if-lt v9, v7, :cond_10

    .line 1739
    invoke-direct {v0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->lookaheadString(I)Ljava/lang/String;

    move-result-object v7

    .line 1742
    iget-object v9, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v9, v9, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v9, :cond_c

    .line 1743
    invoke-virtual {v0, v12}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1744
    iget v9, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v9, v5

    iput v9, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1745
    invoke-virtual {v0, v11}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1746
    iget v9, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v9, v5

    iput v9, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1749
    :cond_c
    new-instance v9, Ljava/lang/StringBuffer;

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    const-string v13, "la1 = "

    invoke-virtual {v9, v13}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v9

    invoke-virtual {v9, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v7, "if False:"

    .line 1751
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1752
    iget v7, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v7, v5

    iput v7, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v7, "pass"

    .line 1753
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1755
    iget v7, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v7, v5

    iput v7, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    move v7, v4

    .line 1757
    :goto_3
    iget-object v9, v1, Lgroovyjarjarantlr/AlternativeBlock;->alternatives:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v9}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v9

    if-ge v7, v9, :cond_f

    .line 1759
    invoke-virtual {v1, v7}, Lgroovyjarjarantlr/AlternativeBlock;->getAlternativeAt(I)Lgroovyjarjarantlr/Alternative;

    move-result-object v9

    .line 1762
    invoke-static {v9}, Lgroovyjarjarantlr/PythonCodeGenerator;->suitableForCaseExpression(Lgroovyjarjarantlr/Alternative;)Z

    move-result v13

    if-nez v13, :cond_d

    goto :goto_4

    .line 1765
    :cond_d
    iget-object v13, v9, Lgroovyjarjarantlr/Alternative;->cache:[Lgroovyjarjarantlr/Lookahead;

    aget-object v13, v13, v5

    .line 1766
    iget-object v14, v13, Lgroovyjarjarantlr/Lookahead;->fset:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v14}, Lgroovyjarjarantlr/collections/impl/BitSet;->degree()I

    move-result v14

    if-nez v14, :cond_e

    invoke-virtual {v13}, Lgroovyjarjarantlr/Lookahead;->containsEpsilon()Z

    move-result v14

    if-nez v14, :cond_e

    .line 1768
    iget-object v13, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-object v14, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v14}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v14

    iget-object v15, v9, Lgroovyjarjarantlr/Alternative;->head:Lgroovyjarjarantlr/AlternativeElement;

    invoke-virtual {v15}, Lgroovyjarjarantlr/AlternativeElement;->getLine()I

    move-result v15

    iget-object v9, v9, Lgroovyjarjarantlr/Alternative;->head:Lgroovyjarjarantlr/AlternativeElement;

    invoke-virtual {v9}, Lgroovyjarjarantlr/AlternativeElement;->getColumn()I

    move-result v9

    const-string v4, "Alternate omitted due to empty prediction set"

    invoke-virtual {v13, v4, v14, v15, v9}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    goto :goto_4

    .line 1776
    :cond_e
    iget-object v4, v13, Lgroovyjarjarantlr/Lookahead;->fset:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->genCases(Lgroovyjarjarantlr/collections/impl/BitSet;)V

    .line 1777
    iget v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v4, v5

    iput v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1778
    invoke-virtual {v0, v9, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genAlt(Lgroovyjarjarantlr/Alternative;Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 1779
    iget v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v4, v5

    iput v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    :goto_4
    add-int/lit8 v7, v7, 0x1

    const/4 v4, 0x0

    goto :goto_3

    .line 1783
    :cond_f
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1784
    iget v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v4, v5

    iput v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    move v4, v5

    goto :goto_5

    :cond_10
    const/4 v4, 0x0

    .line 1800
    :goto_5
    iget-object v7, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v7, v7, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v7, :cond_11

    iget-object v7, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v7, v7, Lgroovyjarjarantlr/Grammar;->maxk:I

    goto :goto_6

    :cond_11
    const/4 v7, 0x0

    :goto_6
    const/4 v9, 0x0

    :goto_7
    if-ltz v7, :cond_25

    const/4 v13, 0x0

    .line 1803
    :goto_8
    iget-object v14, v1, Lgroovyjarjarantlr/AlternativeBlock;->alternatives:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v14}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v14

    if-ge v13, v14, :cond_24

    .line 1805
    invoke-virtual {v1, v13}, Lgroovyjarjarantlr/AlternativeBlock;->getAlternativeAt(I)Lgroovyjarjarantlr/Alternative;

    move-result-object v14

    .line 1806
    iget-boolean v15, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v15, :cond_12

    sget-object v15, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    move-object/from16 v17, v8

    const-string v8, "genAlt: "

    invoke-virtual {v5, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5, v13}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v15, v5}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    goto :goto_9

    :cond_12
    move-object/from16 v17, v8

    :goto_9
    if-eqz v4, :cond_13

    .line 1811
    invoke-static {v14}, Lgroovyjarjarantlr/PythonCodeGenerator;->suitableForCaseExpression(Lgroovyjarjarantlr/Alternative;)Z

    move-result v5

    if-eqz v5, :cond_13

    .line 1812
    iget-boolean v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v5, :cond_16

    .line 1813
    sget-object v5, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v8, "ignoring alt because it was in the switch"

    invoke-virtual {v5, v8}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    goto :goto_b

    .line 1820
    :cond_13
    iget-object v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v5, v5, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v5, :cond_18

    .line 1825
    iget v5, v14, Lgroovyjarjarantlr/Alternative;->lookaheadDepth:I

    const v8, 0x7fffffff

    if-ne v5, v8, :cond_14

    .line 1828
    iget-object v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v5, v5, Lgroovyjarjarantlr/Grammar;->maxk:I

    :cond_14
    :goto_a
    const/4 v8, 0x1

    if-lt v5, v8, :cond_15

    .line 1830
    iget-object v8, v14, Lgroovyjarjarantlr/Alternative;->cache:[Lgroovyjarjarantlr/Lookahead;

    aget-object v8, v8, v5

    invoke-virtual {v8}, Lgroovyjarjarantlr/Lookahead;->containsEpsilon()Z

    move-result v8

    if-eqz v8, :cond_15

    add-int/lit8 v5, v5, -0x1

    goto :goto_a

    :cond_15
    if-eq v5, v7, :cond_17

    .line 1837
    iget-boolean v8, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    if-eqz v8, :cond_16

    .line 1838
    sget-object v8, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v14, Ljava/lang/StringBuffer;

    invoke-direct {v14}, Ljava/lang/StringBuffer;-><init>()V

    const-string v15, "ignoring alt because effectiveDepth!=altDepth"

    invoke-virtual {v14, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v14

    invoke-virtual {v14, v5}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v5

    const-string v14, "!="

    invoke-virtual {v5, v14}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5, v7}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v8, v5}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    :cond_16
    :goto_b
    move-object/from16 v19, v2

    move/from16 p2, v4

    move/from16 v18, v6

    goto/16 :goto_11

    .line 1844
    :cond_17
    invoke-virtual {v0, v14, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->lookaheadIsEmpty(Lgroovyjarjarantlr/Alternative;I)Z

    move-result v8

    .line 1845
    invoke-virtual {v0, v14, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->getLookaheadTestExpression(Lgroovyjarjarantlr/Alternative;I)Ljava/lang/String;

    move-result-object v5

    goto :goto_c

    .line 1850
    :cond_18
    iget-object v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v5, v5, Lgroovyjarjarantlr/Grammar;->maxk:I

    invoke-virtual {v0, v14, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->lookaheadIsEmpty(Lgroovyjarjarantlr/Alternative;I)Z

    move-result v8

    .line 1851
    iget-object v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v5, v5, Lgroovyjarjarantlr/Grammar;->maxk:I

    invoke-virtual {v0, v14, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->getLookaheadTestExpression(Lgroovyjarjarantlr/Alternative;I)Ljava/lang/String;

    move-result-object v5

    .line 1856
    :goto_c
    iget-object v15, v14, Lgroovyjarjarantlr/Alternative;->cache:[Lgroovyjarjarantlr/Lookahead;

    const/16 v16, 0x1

    aget-object v15, v15, v16

    iget-object v15, v15, Lgroovyjarjarantlr/Lookahead;->fset:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v15}, Lgroovyjarjarantlr/collections/impl/BitSet;->degree()I

    move-result v15

    move/from16 p2, v4

    const/16 v4, 0x7f

    move/from16 v18, v6

    const-string v6, ":"

    if-le v15, v4, :cond_1a

    invoke-static {v14}, Lgroovyjarjarantlr/PythonCodeGenerator;->suitableForCaseExpression(Lgroovyjarjarantlr/Alternative;)Z

    move-result v4

    if-eqz v4, :cond_1a

    if-nez v9, :cond_19

    .line 1859
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    const-string v8, "<m1> if "

    invoke-virtual {v4, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_d

    .line 1862
    :cond_19
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    const-string v8, "<m2> elif "

    invoke-virtual {v4, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :goto_d
    move-object/from16 v19, v2

    goto/16 :goto_10

    :cond_1a
    if-eqz v8, :cond_1c

    .line 1867
    iget-object v4, v14, Lgroovyjarjarantlr/Alternative;->semPred:Ljava/lang/String;

    if-nez v4, :cond_1c

    iget-object v4, v14, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    if-nez v4, :cond_1c

    if-nez v9, :cond_1b

    const-string v4, "##<m3> <closing"

    .line 1876
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_e

    :cond_1b
    const-string v4, "else: ## <m4>"

    .line 1880
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1881
    iget v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v5, 0x1

    add-int/2addr v4, v5

    iput v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    :goto_e
    const/4 v4, 0x0

    .line 1885
    iput-boolean v4, v2, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->needAnErrorClause:Z

    goto :goto_d

    .line 1893
    :cond_1c
    iget-object v4, v14, Lgroovyjarjarantlr/Alternative;->semPred:Ljava/lang/String;

    if-eqz v4, :cond_1f

    .line 1898
    new-instance v4, Lgroovyjarjarantlr/ActionTransInfo;

    invoke-direct {v4}, Lgroovyjarjarantlr/ActionTransInfo;-><init>()V

    .line 1899
    iget-object v8, v14, Lgroovyjarjarantlr/Alternative;->semPred:Ljava/lang/String;

    iget v15, v1, Lgroovyjarjarantlr/AlternativeBlock;->line:I

    move-object/from16 v19, v2

    iget-object v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {v0, v8, v15, v2, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->processActionForSpecialSymbols(Ljava/lang/String;ILgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object v2

    .line 1908
    iget-object v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v4, v4, Lgroovyjarjarantlr/ParserGrammar;

    const-string v8, "))"

    const-string v15, "("

    if-nez v4, :cond_1d

    iget-object v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v4, v4, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v4, :cond_1e

    :cond_1d
    iget-object v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v4, v4, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-eqz v4, :cond_1e

    .line 1912
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v4, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v5, " and fireSemanticPredicateEvaluated(antlr.debug.SemanticPredicateEvent.PREDICTING, "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    iget-object v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->charFormatter:Lgroovyjarjarantlr/CharFormatter;

    invoke-interface {v5, v2}, Lgroovyjarjarantlr/CharFormatter;->escapeString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->addSemPred(Ljava/lang/String;)I

    move-result v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v5, ", "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v5

    goto :goto_f

    .line 1918
    :cond_1e
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v4, v15}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v5, " and ("

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v5

    goto :goto_f

    :cond_1f
    move-object/from16 v19, v2

    :goto_f
    if-lez v9, :cond_21

    .line 1925
    iget-object v2, v14, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    if-eqz v2, :cond_20

    .line 1927
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1928
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v4, 0x1

    add-int/2addr v2, v4

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1929
    iget-object v2, v14, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    invoke-virtual {v0, v2, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->genSynPred(Lgroovyjarjarantlr/SynPredBlock;Ljava/lang/String;)V

    goto :goto_10

    .line 1934
    :cond_20
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "elif "

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_10

    .line 1939
    :cond_21
    iget-object v2, v14, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    if-eqz v2, :cond_22

    .line 1941
    iget-object v2, v14, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    invoke-virtual {v0, v2, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->genSynPred(Lgroovyjarjarantlr/SynPredBlock;Ljava/lang/String;)V

    goto :goto_10

    .line 1947
    :cond_22
    iget-object v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v2, v2, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v2, :cond_23

    .line 1948
    invoke-virtual {v0, v12}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1949
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v4, 0x1

    add-int/2addr v2, v4

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1950
    invoke-virtual {v0, v11}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 1951
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v2, v4

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1953
    :cond_23
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "if "

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :goto_10
    add-int/lit8 v9, v9, 0x1

    .line 1960
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v4, 0x1

    add-int/2addr v2, v4

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 1961
    invoke-virtual {v0, v14, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genAlt(Lgroovyjarjarantlr/Alternative;Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 1963
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v2, v4

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    :goto_11
    add-int/lit8 v13, v13, 0x1

    move/from16 v4, p2

    move-object/from16 v8, v17

    move/from16 v6, v18

    move-object/from16 v2, v19

    const/4 v5, 0x1

    goto/16 :goto_8

    :cond_24
    move-object/from16 v19, v2

    move/from16 p2, v4

    move/from16 v18, v6

    move-object/from16 v17, v8

    add-int/lit8 v7, v7, -0x1

    const/4 v5, 0x1

    goto/16 :goto_7

    :cond_25
    move-object/from16 v19, v2

    move/from16 p2, v4

    move/from16 v18, v6

    move-object/from16 v17, v8

    .line 1972
    iput-boolean v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    move/from16 v1, v18

    .line 1975
    iput-boolean v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz p2, :cond_27

    move-object/from16 v2, v17

    move-object/from16 v1, v19

    .line 1979
    iput-object v2, v1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->postscript:Ljava/lang/String;

    const/4 v3, 0x1

    .line 1980
    iput-boolean v3, v1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedSwitch:Z

    if-lez v9, :cond_26

    move v4, v3

    goto :goto_12

    :cond_26
    const/4 v4, 0x0

    .line 1981
    :goto_12
    iput-boolean v4, v1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedAnIf:Z

    goto :goto_14

    :cond_27
    move-object/from16 v2, v17

    move-object/from16 v1, v19

    const/4 v3, 0x1

    .line 1984
    iput-object v2, v1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->postscript:Ljava/lang/String;

    const/4 v2, 0x0

    .line 1985
    iput-boolean v2, v1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedSwitch:Z

    if-lez v9, :cond_28

    move v4, v3

    goto :goto_13

    :cond_28
    move v4, v2

    .line 1986
    :goto_13
    iput-boolean v4, v1, Lgroovyjarjarantlr/PythonBlockFinishingInfo;->generatedAnIf:Z

    :goto_14
    return-object v1
.end method

.method protected genHeader()V
    .locals 4

    .line 2248
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "### $ANTLR "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    sget-object v1, Lgroovyjarjarantlr/Tool;->version:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, ": "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, "\""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-object v3, v3, Lgroovyjarjarantlr/Tool;->grammarFile:Ljava/lang/String;

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/Tool;->fileMinusPath(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v2, " -> "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v1}, Lgroovyjarjarantlr/Grammar;->getClassName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, ".py\"$"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    return-void
.end method

.method protected genHeaderInit(Lgroovyjarjarantlr/Grammar;)V
    .locals 2

    .line 570
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/Grammar;->getClassName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, "."

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, "__init__"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    .line 571
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->behavior:Lgroovyjarjarantlr/DefineGrammarSymbols;

    invoke-virtual {v1, p1}, Lgroovyjarjarantlr/DefineGrammarSymbols;->getHeaderAction(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 573
    invoke-static {p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->isEmpty(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 574
    iget-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->behavior:Lgroovyjarjarantlr/DefineGrammarSymbols;

    invoke-virtual {p1, v0}, Lgroovyjarjarantlr/DefineGrammarSymbols;->getHeaderAction(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 576
    :cond_0
    invoke-static {p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->isEmpty(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    goto :goto_0

    .line 579
    :cond_1
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "### __init__ header action >>> "

    .line 580
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const/4 v1, 0x0

    .line 581
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printActionCode(Ljava/lang/String;I)V

    .line 582
    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string p1, "### __init__ header action <<< "

    .line 583
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method protected genHeaderMain(Lgroovyjarjarantlr/Grammar;)V
    .locals 5

    .line 540
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1}, Lgroovyjarjarantlr/Grammar;->getClassName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, "."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, "__main__"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    .line 541
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->behavior:Lgroovyjarjarantlr/DefineGrammarSymbols;

    invoke-virtual {v2, v0}, Lgroovyjarjarantlr/DefineGrammarSymbols;->getHeaderAction(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 543
    invoke-static {v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->isEmpty(Ljava/lang/String;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 544
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->behavior:Lgroovyjarjarantlr/DefineGrammarSymbols;

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/DefineGrammarSymbols;->getHeaderAction(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 546
    :cond_0
    invoke-static {v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->isEmpty(Ljava/lang/String;)Z

    move-result v1

    const-string v2, "### __main__ header action <<< "

    const-string v3, "### __main__ header action >>> "

    const/4 v4, 0x0

    if-eqz v1, :cond_1

    .line 547
    instance-of p1, p1, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz p1, :cond_2

    .line 548
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 549
    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 550
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 551
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genLexerTest()V

    .line 552
    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 553
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 554
    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    goto :goto_0

    .line 557
    :cond_1
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 558
    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, ""

    .line 559
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 560
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 561
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printMainFunc(Ljava/lang/String;)V

    .line 562
    iput v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 563
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 564
    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    :cond_2
    :goto_0
    return-void
.end method

.method protected genJavadocComment(Lgroovyjarjarantlr/Grammar;)V
    .locals 1

    .line 4108
    iget-object v0, p1, Lgroovyjarjarantlr/Grammar;->comment:Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 4109
    iget-object p1, p1, Lgroovyjarjarantlr/Grammar;->comment:Ljava/lang/String;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->_printJavadoc(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method protected genJavadocComment(Lgroovyjarjarantlr/RuleSymbol;)V
    .locals 1

    .line 4115
    iget-object v0, p1, Lgroovyjarjarantlr/RuleSymbol;->comment:Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 4116
    iget-object p1, p1, Lgroovyjarjarantlr/RuleSymbol;->comment:Ljava/lang/String;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->_printJavadoc(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method protected genLexerTest()V
    .locals 4

    .line 2264
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr/Grammar;->getClassName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "if __name__ == \'__main__\' :"

    .line 2265
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2266
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "import sys"

    .line 2267
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "import antlr"

    .line 2268
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2269
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "import "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, ""

    .line 2270
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v2, "### create lexer - shall read from stdin"

    .line 2271
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v2, "try:"

    .line 2272
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2273
    iget v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v2, v2, 0x1

    iput v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2274
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "for token in "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v2, ".Lexer():"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2275
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "print token"

    .line 2276
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2277
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2278
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2279
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "except antlr.TokenStreamException, e:"

    .line 2280
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2281
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "print \"error: exception caught while lexing: \", e"

    .line 2282
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2283
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2284
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    return-void
.end method

.method protected genMatch(Lgroovyjarjarantlr/GrammarAtom;)V
    .locals 3

    .line 2301
    instance-of v0, p1, Lgroovyjarjarantlr/StringLiteralElement;

    if-eqz v0, :cond_1

    .line 2302
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_0

    .line 2303
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genMatchUsingAtomText(Lgroovyjarjarantlr/GrammarAtom;)V

    goto :goto_0

    .line 2306
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genMatchUsingAtomTokenType(Lgroovyjarjarantlr/GrammarAtom;)V

    goto :goto_0

    .line 2309
    :cond_1
    instance-of v0, p1, Lgroovyjarjarantlr/CharLiteralElement;

    if-eqz v0, :cond_3

    .line 2310
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_2

    .line 2311
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genMatchUsingAtomText(Lgroovyjarjarantlr/GrammarAtom;)V

    goto :goto_0

    .line 2314
    :cond_2
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "cannot ref character literals in grammar: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;)V

    goto :goto_0

    .line 2317
    :cond_3
    instance-of v0, p1, Lgroovyjarjarantlr/TokenRefElement;

    if-eqz v0, :cond_4

    .line 2318
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genMatchUsingAtomText(Lgroovyjarjarantlr/GrammarAtom;)V

    goto :goto_0

    .line 2320
    :cond_4
    instance-of v0, p1, Lgroovyjarjarantlr/WildcardElement;

    if-eqz v0, :cond_5

    .line 2321
    check-cast p1, Lgroovyjarjarantlr/WildcardElement;

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->gen(Lgroovyjarjarantlr/WildcardElement;)V

    :cond_5
    :goto_0
    return-void
.end method

.method protected genMatch(Lgroovyjarjarantlr/collections/impl/BitSet;)V
    .locals 0

    return-void
.end method

.method protected genMatchUsingAtomText(Lgroovyjarjarantlr/GrammarAtom;)V
    .locals 3

    .line 2328
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v0, :cond_0

    const-string v0, "_t,"

    goto :goto_0

    :cond_0
    const-string v0, ""

    .line 2333
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v1, v1, Lgroovyjarjarantlr/LexerGrammar;

    const/4 v2, 0x3

    if-eqz v1, :cond_2

    iget-boolean v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz v1, :cond_1

    invoke-virtual {p1}, Lgroovyjarjarantlr/GrammarAtom;->getAutoGenType()I

    move-result v1

    if-ne v1, v2, :cond_2

    :cond_1
    const-string v1, "_saveIndex = self.text.length()"

    .line 2336
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2340
    :cond_2
    iget-boolean v1, p1, Lgroovyjarjarantlr/GrammarAtom;->not:Z

    if-eqz v1, :cond_3

    const-string v1, "self.matchNot("

    goto :goto_1

    :cond_3
    const-string v1, "self.match("

    :goto_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->print(Ljava/lang/String;)V

    .line 2341
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2344
    iget-object v0, p1, Lgroovyjarjarantlr/GrammarAtom;->atomText:Ljava/lang/String;

    const-string v1, "EOF"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    const-string v0, "EOF_TYPE"

    .line 2346
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    goto :goto_2

    .line 2349
    :cond_4
    iget-object v0, p1, Lgroovyjarjarantlr/GrammarAtom;->atomText:Ljava/lang/String;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    :goto_2
    const-string v0, ")"

    .line 2351
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->_println(Ljava/lang/String;)V

    .line 2353
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_6

    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    if-eqz v0, :cond_5

    invoke-virtual {p1}, Lgroovyjarjarantlr/GrammarAtom;->getAutoGenType()I

    move-result p1

    if-ne p1, v2, :cond_6

    :cond_5
    const-string p1, "self.text.setLength(_saveIndex)"

    .line 2354
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_6
    return-void
.end method

.method protected genMatchUsingAtomTokenType(Lgroovyjarjarantlr/GrammarAtom;)V
    .locals 3

    .line 2361
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v0, :cond_0

    const-string v0, "_t,"

    goto :goto_0

    :cond_0
    const-string v0, ""

    .line 2367
    :goto_0
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {p1}, Lgroovyjarjarantlr/GrammarAtom;->getType()I

    move-result v1

    const/4 v2, 0x1

    invoke-direct {p0, v1, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->getValueString(IZ)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    .line 2370
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    iget-boolean p1, p1, Lgroovyjarjarantlr/GrammarAtom;->not:Z

    if-eqz p1, :cond_1

    const-string p1, "self.matchNot("

    goto :goto_1

    :cond_1
    const-string p1, "self.match("

    :goto_1
    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, ")"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    return-void
.end method

.method public genNextToken()V
    .locals 14

    const/4 v0, 0x0

    move v1, v0

    .line 2382
    :goto_0
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr/Grammar;->rules:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v2}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v2

    const-string v3, "public"

    const/4 v4, 0x1

    if-ge v1, v2, :cond_1

    .line 2383
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr/Grammar;->rules:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/collections/impl/Vector;->elementAt(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarantlr/RuleSymbol;

    .line 2384
    invoke-virtual {v2}, Lgroovyjarjarantlr/RuleSymbol;->isDefined()Z

    move-result v5

    if-eqz v5, :cond_0

    iget-object v2, v2, Lgroovyjarjarantlr/RuleSymbol;->access:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    move v1, v4

    goto :goto_1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    move v1, v0

    :goto_1
    const-string v2, "raise antlr.TokenStreamException(str(cse))"

    const-string v5, "try:"

    const-string v6, ""

    if-nez v1, :cond_2

    .line 2390
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "def nextToken(self): "

    .line 2391
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2392
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2393
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2394
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "self.uponEOF()"

    .line 2395
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2396
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "except antlr.CharStreamIOException, csioe:"

    .line 2397
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2398
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "raise antlr.TokenStreamIOException(csioe.io)"

    .line 2399
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2400
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "except antlr.CharStreamException, cse:"

    .line 2401
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2402
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2403
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2404
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "return antlr.CommonToken(type=EOF_TYPE, text=\"\")"

    .line 2405
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2406
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    return-void

    .line 2411
    :cond_2
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v7, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v7, v7, Lgroovyjarjarantlr/Grammar;->rules:Lgroovyjarjarantlr/collections/impl/Vector;

    const-string v8, "nextToken"

    invoke-static {v1, v7, v8}, Lgroovyjarjarantlr/MakeGrammar;->createNextTokenRule(Lgroovyjarjarantlr/Grammar;Lgroovyjarjarantlr/collections/impl/Vector;Ljava/lang/String;)Lgroovyjarjarantlr/RuleBlock;

    move-result-object v1

    .line 2415
    new-instance v7, Lgroovyjarjarantlr/RuleSymbol;

    const-string v8, "mnextToken"

    invoke-direct {v7, v8}, Lgroovyjarjarantlr/RuleSymbol;-><init>(Ljava/lang/String;)V

    .line 2416
    invoke-virtual {v7}, Lgroovyjarjarantlr/RuleSymbol;->setDefined()V

    .line 2417
    invoke-virtual {v7, v1}, Lgroovyjarjarantlr/RuleSymbol;->setBlock(Lgroovyjarjarantlr/RuleBlock;)V

    const-string v8, "private"

    .line 2418
    iput-object v8, v7, Lgroovyjarjarantlr/RuleSymbol;->access:Ljava/lang/String;

    .line 2419
    iget-object v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v8, v7}, Lgroovyjarjarantlr/Grammar;->define(Lgroovyjarjarantlr/RuleSymbol;)V

    .line 2421
    iget-object v7, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v7, v7, Lgroovyjarjarantlr/Grammar;->theLLkAnalyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    invoke-interface {v7, v1}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->deterministic(Lgroovyjarjarantlr/AlternativeBlock;)Z

    const/4 v7, 0x0

    .line 2425
    iget-object v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    check-cast v8, Lgroovyjarjarantlr/LexerGrammar;

    iget-boolean v8, v8, Lgroovyjarjarantlr/LexerGrammar;->filterMode:Z

    if-eqz v8, :cond_3

    .line 2426
    iget-object v7, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    check-cast v7, Lgroovyjarjarantlr/LexerGrammar;

    iget-object v7, v7, Lgroovyjarjarantlr/LexerGrammar;->filterRule:Ljava/lang/String;

    .line 2429
    :cond_3
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v8, "def nextToken(self):"

    .line 2430
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2431
    iget v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v8, v4

    iput v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v8, "while True:"

    .line 2432
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2433
    iget v9, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v9, v4

    iput v9, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v9, "try: ### try again .."

    .line 2434
    invoke-virtual {p0, v9}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2435
    iget v9, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v9, v4

    iput v9, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2436
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2437
    iget v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v8, v4

    iput v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2438
    iget v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v8, "_token = None"

    .line 2439
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v8, "_ttype = INVALID_TYPE"

    .line 2440
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2441
    iget-object v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    check-cast v8, Lgroovyjarjarantlr/LexerGrammar;

    iget-boolean v8, v8, Lgroovyjarjarantlr/LexerGrammar;->filterMode:Z

    if-eqz v8, :cond_7

    const-string v8, "self.setCommitToPath(False)"

    .line 2443
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    if-eqz v7, :cond_7

    .line 2447
    iget-object v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-static {v7}, Lgroovyjarjarantlr/CodeGenerator;->encodeLexerRuleName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Lgroovyjarjarantlr/Grammar;->isDefined(Ljava/lang/String;)Z

    move-result v8

    const-string v9, " does not exist in this lexer"

    const-string v10, "Filter rule "

    if-nez v8, :cond_4

    .line 2448
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v8, Ljava/lang/StringBuffer;

    invoke-direct {v8}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v8, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v3, v8}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;)V

    goto :goto_2

    .line 2453
    :cond_4
    iget-object v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-static {v7}, Lgroovyjarjarantlr/CodeGenerator;->encodeLexerRuleName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v8, v11}, Lgroovyjarjarantlr/Grammar;->getSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/GrammarSymbol;

    move-result-object v8

    check-cast v8, Lgroovyjarjarantlr/RuleSymbol;

    .line 2455
    invoke-virtual {v8}, Lgroovyjarjarantlr/RuleSymbol;->isDefined()Z

    move-result v11

    if-nez v11, :cond_5

    .line 2456
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v8, Ljava/lang/StringBuffer;

    invoke-direct {v8}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v8, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v3, v8}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;)V

    goto :goto_2

    .line 2459
    :cond_5
    iget-object v8, v8, Lgroovyjarjarantlr/RuleSymbol;->access:Ljava/lang/String;

    invoke-virtual {v8, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_6

    .line 2460
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v8, Ljava/lang/StringBuffer;

    invoke-direct {v8}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v8, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    const-string v9, " must be protected"

    invoke-virtual {v8, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v3, v8}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;)V

    :cond_6
    :goto_2
    const-string v3, "_m = self.mark()"

    .line 2464
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_7
    const-string v3, "self.resetText()"

    .line 2467
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v8, "try: ## for char stream error handling"

    .line 2469
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2470
    iget v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v8, v4

    iput v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2471
    iget v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v8, "try: ##for lexical error handling"

    .line 2474
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2475
    iget v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v8, v4

    iput v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2476
    iget v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    move v9, v0

    .line 2480
    :goto_3
    invoke-virtual {v1}, Lgroovyjarjarantlr/RuleBlock;->getAlternatives()Lgroovyjarjarantlr/collections/impl/Vector;

    move-result-object v10

    invoke-virtual {v10}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v10

    if-ge v9, v10, :cond_9

    .line 2482
    invoke-virtual {v1, v9}, Lgroovyjarjarantlr/RuleBlock;->getAlternativeAt(I)Lgroovyjarjarantlr/Alternative;

    move-result-object v10

    .line 2483
    iget-object v11, v10, Lgroovyjarjarantlr/Alternative;->cache:[Lgroovyjarjarantlr/Lookahead;

    aget-object v11, v11, v4

    invoke-virtual {v11}, Lgroovyjarjarantlr/Lookahead;->containsEpsilon()Z

    move-result v11

    if-eqz v11, :cond_8

    .line 2486
    iget-object v10, v10, Lgroovyjarjarantlr/Alternative;->head:Lgroovyjarjarantlr/AlternativeElement;

    check-cast v10, Lgroovyjarjarantlr/RuleRefElement;

    .line 2487
    iget-object v10, v10, Lgroovyjarjarantlr/RuleRefElement;->targetRule:Ljava/lang/String;

    invoke-static {v10}, Lgroovyjarjarantlr/CodeGenerator;->decodeLexerRuleName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    .line 2488
    iget-object v11, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v12, Ljava/lang/StringBuffer;

    invoke-direct {v12}, Ljava/lang/StringBuffer;-><init>()V

    const-string v13, "public lexical rule "

    invoke-virtual {v12, v13}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v12

    invoke-virtual {v12, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    const-string v12, " is optional (can match \"nothing\")"

    invoke-virtual {v10, v12}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v10

    invoke-virtual {v10}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v11, v10}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;)V

    :cond_8
    add-int/lit8 v9, v9, 0x1

    goto :goto_3

    :cond_9
    const-string v9, "line.separator"

    .line 2493
    invoke-static {v9}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    .line 2496
    invoke-virtual {p0, v1, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genCommonBlock(Lgroovyjarjarantlr/AlternativeBlock;Z)Lgroovyjarjarantlr/PythonBlockFinishingInfo;

    move-result-object v0

    .line 2506
    iget-object v9, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    check-cast v9, Lgroovyjarjarantlr/LexerGrammar;

    iget-boolean v9, v9, Lgroovyjarjarantlr/LexerGrammar;->filterMode:Z

    if-eqz v9, :cond_b

    if-nez v7, :cond_a

    .line 2512
    new-instance v9, Ljava/lang/StringBuffer;

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v9, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v9, "self.filterdefault(self.LA(1))"

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    goto :goto_4

    .line 2516
    :cond_a
    new-instance v9, Ljava/lang/StringBuffer;

    invoke-direct {v9}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v9, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v9, "self.filterdefault(self.LA(1), self.m"

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v9, ", False)"

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    goto :goto_4

    :cond_b
    const-string v6, "self.default(self.LA(1))"

    .line 2533
    :goto_4
    invoke-direct {p0, v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockFinish1(Lgroovyjarjarantlr/PythonBlockFinishingInfo;Ljava/lang/String;)V

    .line 2536
    iput v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2539
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    check-cast v0, Lgroovyjarjarantlr/LexerGrammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/LexerGrammar;->filterMode:Z

    if-eqz v0, :cond_c

    if-eqz v7, :cond_c

    const-string v0, "self.commit()"

    .line 2540
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_c
    const-string v0, "if not self._returnToken:"

    .line 2546
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2547
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "raise antlr.TryAgain ### found SKIP token"

    .line 2548
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2549
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2553
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    check-cast v0, Lgroovyjarjarantlr/LexerGrammar;

    invoke-virtual {v0}, Lgroovyjarjarantlr/LexerGrammar;->getTestLiterals()Z

    move-result v0

    if-eqz v0, :cond_d

    const-string v0, "### option { testLiterals=true } "

    .line 2555
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "self.testForLiteral(self._returnToken)"

    .line 2557
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_d
    const-string v0, "### return token to caller"

    .line 2561
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "return self._returnToken"

    .line 2562
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2565
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "### handle lexical errors ...."

    .line 2566
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "except antlr.RecognitionException, e:"

    .line 2567
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2568
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2569
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    check-cast v0, Lgroovyjarjarantlr/LexerGrammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/LexerGrammar;->filterMode:Z

    const-string v6, "self.consume()"

    if-eqz v0, :cond_f

    const-string v0, "raise antlr.TryAgain()"

    if-nez v7, :cond_e

    const-string v3, "if not self.getCommitToPath():"

    .line 2573
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2574
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v4

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2575
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2576
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2577
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    goto :goto_5

    :cond_e
    const-string v8, "if not self.getCommitToPath(): "

    .line 2581
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2582
    iget v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v8, v4

    iput v8, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v8, "self.rewind(_m)"

    .line 2583
    invoke-virtual {p0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2584
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2585
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2586
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v4

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2587
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v5, "self.m"

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    const-string v5, "(False)"

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2588
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v3, v4

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v3, "except antlr.RecognitionException, ee:"

    .line 2589
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2590
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v4

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v3, "### horrendous failure: error in filter rule"

    .line 2591
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v3, "self.reportError(ee)"

    .line 2592
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2593
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2594
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v3, v4

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2595
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2596
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2599
    :cond_f
    :goto_5
    invoke-virtual {v1}, Lgroovyjarjarantlr/RuleBlock;->getDefaultErrorHandler()Z

    move-result v0

    if-eqz v0, :cond_10

    const-string v0, "self.reportError(e)"

    .line 2600
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2601
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_6

    :cond_10
    const-string v0, "raise antlr.TokenStreamRecognitionException(e)"

    .line 2605
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2607
    :goto_6
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2612
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "### handle char stream errors ..."

    .line 2613
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "except antlr.CharStreamException,cse:"

    .line 2614
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2615
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "if isinstance(cse, antlr.CharStreamIOException):"

    .line 2616
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2617
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "raise antlr.TokenStreamIOException(cse.io)"

    .line 2618
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2619
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "else:"

    .line 2620
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2621
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2622
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2623
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2624
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2628
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2632
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "except antlr.TryAgain:"

    .line 2634
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2635
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "pass"

    .line 2636
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2637
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2639
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v4

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    return-void
.end method

.method public genRule(Lgroovyjarjarantlr/RuleSymbol;ZI)V
    .locals 16

    move-object/from16 v0, p0

    move/from16 v1, p3

    const/4 v2, 0x1

    .line 2664
    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2665
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->isDefined()Z

    move-result v3

    if-nez v3, :cond_0

    .line 2666
    iget-object v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "undefined rule: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;)V

    return-void

    .line 2671
    :cond_0
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getBlock()Lgroovyjarjarantlr/RuleBlock;

    move-result-object v3

    .line 2673
    iput-object v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    .line 2674
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v4

    iput-object v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentASTResult:Ljava/lang/String;

    .line 2677
    iget-object v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->declaredASTVariables:Ljava/util/Hashtable;

    invoke-virtual {v4}, Ljava/util/Hashtable;->clear()V

    .line 2680
    iget-boolean v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    const/4 v5, 0x0

    if-eqz v4, :cond_1

    .line 2681
    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getAutoGen()Z

    move-result v6

    if-eqz v6, :cond_1

    move v6, v2

    goto :goto_0

    :cond_1
    move v6, v5

    :goto_0
    iput-boolean v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    .line 2684
    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getAutoGen()Z

    move-result v6

    iput-boolean v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->saveText:Z

    .line 2687
    invoke-virtual/range {p0 .. p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->genJavadocComment(Lgroovyjarjarantlr/RuleSymbol;)V

    .line 2690
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    const-string v7, "def "

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v7, "("

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->print(Ljava/lang/String;)V

    .line 2693
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraParams:Ljava/lang/String;

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2694
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonExtraParams:Ljava/lang/String;

    invoke-virtual {v6}, Ljava/lang/String;->length()I

    move-result v6

    if-eqz v6, :cond_2

    iget-object v6, v3, Lgroovyjarjarantlr/RuleBlock;->argAction:Ljava/lang/String;

    if-eqz v6, :cond_2

    const-string v6, ","

    .line 2695
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2700
    :cond_2
    iget-object v6, v3, Lgroovyjarjarantlr/RuleBlock;->argAction:Ljava/lang/String;

    const-string v7, "):"

    const-string v8, ""

    if-eqz v6, :cond_3

    .line 2702
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->_println(Ljava/lang/String;)V

    .line 2703
    iget v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v6, v2

    iput v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2704
    iget-object v6, v3, Lgroovyjarjarantlr/RuleBlock;->argAction:Ljava/lang/String;

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2705
    iget v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v6, v2

    iput v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2706
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/PythonCodeGenerator;->print(Ljava/lang/String;)V

    goto :goto_1

    .line 2710
    :cond_3
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 2713
    :goto_1
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2714
    iget v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v6, v2

    iput v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2717
    iget-object v6, v3, Lgroovyjarjarantlr/RuleBlock;->returnAction:Ljava/lang/String;

    if-eqz v6, :cond_5

    .line 2718
    iget-object v6, v3, Lgroovyjarjarantlr/RuleBlock;->returnAction:Ljava/lang/String;

    const/16 v7, 0x3d

    invoke-virtual {v6, v7}, Ljava/lang/String;->indexOf(I)I

    move-result v6

    if-ltz v6, :cond_4

    .line 2719
    iget-object v6, v3, Lgroovyjarjarantlr/RuleBlock;->returnAction:Ljava/lang/String;

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_2

    .line 2722
    :cond_4
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    iget-object v7, v3, Lgroovyjarjarantlr/RuleBlock;->returnAction:Ljava/lang/String;

    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getLine()I

    move-result v9

    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getColumn()I

    move-result v10

    invoke-virtual {v0, v7, v9, v10}, Lgroovyjarjarantlr/PythonCodeGenerator;->extractIdOfAction(Ljava/lang/String;II)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v7, " = None"

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2726
    :cond_5
    :goto_2
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->commonLocalVars:Ljava/lang/String;

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2728
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v6, v6, Lgroovyjarjarantlr/Grammar;->traceRules:Z

    const-string v7, "\")"

    if-eqz v6, :cond_7

    .line 2729
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v6, v6, Lgroovyjarjarantlr/TreeWalkerGrammar;

    const-string v9, "self.traceIn(\""

    if-eqz v6, :cond_6

    .line 2730
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v9, "\",_t)"

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_3

    .line 2733
    :cond_6
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2737
    :cond_7
    :goto_3
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v6, v6, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v6, :cond_9

    .line 2740
    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v6

    const-string v9, "mEOF"

    invoke-virtual {v6, v9}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_8

    const-string v6, "_ttype = EOF_TYPE"

    .line 2741
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_4

    .line 2743
    :cond_8
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    const-string v9, "_ttype = "

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v9, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :goto_4
    const-string v6, "_saveIndex = 0"

    .line 2744
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2748
    :cond_9
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v6, v6, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    const-string v9, ", _ttype)"

    const-string v10, ", 0)"

    if-eqz v6, :cond_b

    .line 2749
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v6, v6, Lgroovyjarjarantlr/ParserGrammar;

    const-string v11, "self.fireEnterRule("

    if-eqz v6, :cond_a

    .line 2750
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v6, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_5

    .line 2751
    :cond_a
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v6, v6, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v6, :cond_b

    .line 2752
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v6, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2755
    :cond_b
    :goto_5
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v6, v6, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-nez v6, :cond_c

    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v6, v6, Lgroovyjarjarantlr/Grammar;->traceRules:Z

    if-eqz v6, :cond_d

    :cond_c
    const-string v6, "try: ### debugging"

    .line 2756
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2757
    iget v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v6, v2

    iput v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2761
    :cond_d
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v6, v6, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v6, :cond_e

    .line 2763
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v6, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v11, "_AST_in = None"

    invoke-virtual {v6, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v6, "if _t != antlr.ASTNULL:"

    .line 2764
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2765
    iget v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v6, v2

    iput v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2766
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v6, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v11, "_AST_in = _t"

    invoke-virtual {v6, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2767
    iget v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v6, v2

    iput v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2769
    :cond_e
    iget-object v6, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v6, v6, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v6, :cond_f

    const-string v6, "self.returnAST = None"

    .line 2772
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v6, "currentAST = antlr.ASTPair()"

    .line 2773
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2775
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v6, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v11, "_AST = None"

    invoke-virtual {v6, v11}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2778
    :cond_f
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockPreamble(Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 2779
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockInitAction(Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 2782
    invoke-virtual {v3, v8}, Lgroovyjarjarantlr/RuleBlock;->findExceptionSpec(Ljava/lang/String;)Lgroovyjarjarantlr/ExceptionSpec;

    move-result-object v6

    if-nez v6, :cond_10

    .line 2785
    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getDefaultErrorHandler()Z

    move-result v11

    if-eqz v11, :cond_11

    :cond_10
    const-string v11, "try:      ## for error handling"

    .line 2786
    invoke-virtual {v0, v11}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2787
    iget v11, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v11, v2

    iput v11, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2789
    :cond_11
    iget v11, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2791
    iget-object v12, v3, Lgroovyjarjarantlr/RuleBlock;->alternatives:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v12}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v12

    if-ne v12, v2, :cond_14

    .line 2794
    invoke-virtual {v3, v5}, Lgroovyjarjarantlr/RuleBlock;->getAlternativeAt(I)Lgroovyjarjarantlr/Alternative;

    move-result-object v5

    .line 2795
    iget-object v12, v5, Lgroovyjarjarantlr/Alternative;->semPred:Ljava/lang/String;

    if-eqz v12, :cond_12

    .line 2797
    iget-object v13, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    iget v13, v13, Lgroovyjarjarantlr/RuleBlock;->line:I

    invoke-virtual {v0, v12, v13}, Lgroovyjarjarantlr/PythonCodeGenerator;->genSemPred(Ljava/lang/String;I)V

    .line 2798
    :cond_12
    iget-object v12, v5, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    if-eqz v12, :cond_13

    .line 2800
    iget-object v12, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    iget-object v13, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v13}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v13

    iget-object v14, v5, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    invoke-virtual {v14}, Lgroovyjarjarantlr/SynPredBlock;->getLine()I

    move-result v14

    iget-object v15, v5, Lgroovyjarjarantlr/Alternative;->synPred:Lgroovyjarjarantlr/SynPredBlock;

    invoke-virtual {v15}, Lgroovyjarjarantlr/SynPredBlock;->getColumn()I

    move-result v15

    const-string v2, "Syntactic predicate ignored for single alternative"

    invoke-virtual {v12, v2, v13, v14, v15}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    .line 2807
    :cond_13
    invoke-virtual {v0, v5, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->genAlt(Lgroovyjarjarantlr/Alternative;Lgroovyjarjarantlr/AlternativeBlock;)V

    goto :goto_6

    .line 2812
    :cond_14
    iget-object v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr/Grammar;->theLLkAnalyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    invoke-interface {v2, v3}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->deterministic(Lgroovyjarjarantlr/AlternativeBlock;)Z

    .line 2814
    invoke-virtual {v0, v3, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->genCommonBlock(Lgroovyjarjarantlr/AlternativeBlock;Z)Lgroovyjarjarantlr/PythonBlockFinishingInfo;

    move-result-object v2

    .line 2815
    iget-object v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->throwNoViable:Ljava/lang/String;

    invoke-direct {v0, v2, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->genBlockFinish(Lgroovyjarjarantlr/PythonBlockFinishingInfo;Ljava/lang/String;)V

    .line 2817
    :goto_6
    iput v11, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    if-nez v6, :cond_15

    .line 2820
    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getDefaultErrorHandler()Z

    move-result v2

    if-eqz v2, :cond_16

    .line 2822
    :cond_15
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v5, 0x1

    sub-int/2addr v2, v5

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2823
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_16
    if-eqz v6, :cond_17

    .line 2828
    invoke-direct {v0, v6}, Lgroovyjarjarantlr/PythonCodeGenerator;->genErrorHandler(Lgroovyjarjarantlr/ExceptionSpec;)V

    goto/16 :goto_8

    .line 2830
    :cond_17
    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getDefaultErrorHandler()Z

    move-result v2

    if-eqz v2, :cond_1b

    .line 2832
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v5, "except "

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    iget-object v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->exceptionThrown:Ljava/lang/String;

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    const-string v5, ", ex:"

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2833
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v5, 0x1

    add-int/2addr v2, v5

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2835
    iget-object v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v2, v2, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    if-eqz v2, :cond_18

    const-string v2, "if not self.inputState.guessing:"

    .line 2836
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2837
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v2, v5

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    :cond_18
    const-string v2, "self.reportError(ex)"

    .line 2839
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2840
    iget-object v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v2, v2, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-nez v2, :cond_19

    .line 2842
    iget-object v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr/Grammar;->theLLkAnalyzer:Lgroovyjarjarantlr/LLkGrammarAnalyzer;

    iget-object v5, v3, Lgroovyjarjarantlr/RuleBlock;->endNode:Lgroovyjarjarantlr/RuleEndElement;

    const/4 v6, 0x1

    invoke-interface {v2, v6, v5}, Lgroovyjarjarantlr/LLkGrammarAnalyzer;->FOLLOW(ILgroovyjarjarantlr/RuleEndElement;)Lgroovyjarjarantlr/Lookahead;

    move-result-object v2

    .line 2843
    iget-object v2, v2, Lgroovyjarjarantlr/Lookahead;->fset:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->markBitsetForGen(Lgroovyjarjarantlr/collections/impl/BitSet;)I

    move-result v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->getBitsetName(I)Ljava/lang/String;

    move-result-object v2

    const-string v5, "self.consume()"

    .line 2844
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2845
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    const-string v6, "self.consumeUntil("

    invoke-virtual {v5, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    const-string v5, ")"

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const/4 v5, 0x1

    goto :goto_7

    :cond_19
    const-string v2, "if _t:"

    .line 2849
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2850
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v5, 0x1

    add-int/2addr v2, v5

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v2, "_t = _t.getNextSibling()"

    .line 2851
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2852
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v2, v5

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2854
    :goto_7
    iget-object v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v2, v2, Lgroovyjarjarantlr/Grammar;->hasSyntacticPredicate:Z

    if-eqz v2, :cond_1a

    .line 2855
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v2, v5

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v2, "else:"

    .line 2857
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2858
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v2, v5

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v2, "raise ex"

    .line 2859
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2860
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v2, v5

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2863
    :cond_1a
    iget v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v2, v5

    iput v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2864
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2868
    :cond_1b
    :goto_8
    iget-object v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v2, v2, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-eqz v2, :cond_1c

    .line 2869
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v5, "self.returnAST = "

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    const-string v5, "_AST"

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2873
    :cond_1c
    iget-object v2, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v2, v2, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz v2, :cond_1d

    const-string v2, "self._retTree = _t"

    .line 2874
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2878
    :cond_1d
    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getTestLiterals()Z

    move-result v2

    if-eqz v2, :cond_1f

    move-object/from16 v2, p1

    .line 2879
    iget-object v5, v2, Lgroovyjarjarantlr/RuleSymbol;->access:Ljava/lang/String;

    const-string v6, "protected"

    invoke-virtual {v5, v6}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_1e

    .line 2880
    invoke-direct/range {p0 .. p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genLiteralsTestForPartialToken()V

    goto :goto_9

    .line 2883
    :cond_1e
    invoke-direct/range {p0 .. p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->genLiteralsTest()V

    goto :goto_9

    :cond_1f
    move-object/from16 v2, p1

    .line 2888
    :goto_9
    iget-object v5, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v5, v5, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v5, :cond_20

    const-string v5, "self.set_return_token(_createToken, _token, _ttype, _begin)"

    .line 2890
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2893
    :cond_20
    iget-object v5, v3, Lgroovyjarjarantlr/RuleBlock;->returnAction:Ljava/lang/String;

    if-eqz v5, :cond_21

    .line 2897
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    const-string v6, "return "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    iget-object v6, v3, Lgroovyjarjarantlr/RuleBlock;->returnAction:Ljava/lang/String;

    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getLine()I

    move-result v11

    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getColumn()I

    move-result v3

    invoke-virtual {v0, v6, v11, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->extractIdOfAction(Ljava/lang/String;II)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2908
    :cond_21
    iget-object v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v3, v3, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-nez v3, :cond_23

    iget-object v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v3, v3, Lgroovyjarjarantlr/Grammar;->traceRules:Z

    if-eqz v3, :cond_22

    goto :goto_a

    :cond_22
    const/4 v2, 0x1

    goto/16 :goto_d

    .line 2909
    :cond_23
    :goto_a
    iget v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v5, 0x1

    sub-int/2addr v3, v5

    iput v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v3, "finally:  ### debugging"

    .line 2910
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2911
    iget v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/2addr v3, v5

    iput v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2914
    iget-object v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v3, v3, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-eqz v3, :cond_25

    .line 2915
    iget-object v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v3, v3, Lgroovyjarjarantlr/ParserGrammar;

    const-string v5, "self.fireExitRule("

    if-eqz v3, :cond_24

    .line 2916
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v10}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_b

    .line 2917
    :cond_24
    iget-object v3, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v3, v3, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v3, :cond_25

    .line 2918
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v9}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2920
    :cond_25
    :goto_b
    iget-object v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v1, v1, Lgroovyjarjarantlr/Grammar;->traceRules:Z

    if-eqz v1, :cond_27

    .line 2921
    iget-object v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v1, v1, Lgroovyjarjarantlr/TreeWalkerGrammar;

    const-string v3, "self.traceOut(\""

    if-eqz v1, :cond_26

    .line 2922
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, "\", _t)"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_c

    .line 2925
    :cond_26
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual/range {p1 .. p1}, Lgroovyjarjarantlr/RuleSymbol;->getId()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2928
    :cond_27
    :goto_c
    iget v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v2, 0x1

    sub-int/2addr v1, v2

    iput v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2930
    :goto_d
    iget v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v1, v2

    iput v1, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 2931
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 2934
    iput-boolean v4, v0, Lgroovyjarjarantlr/PythonCodeGenerator;->genAST:Z

    return-void
.end method

.method protected genSemPred(Ljava/lang/String;I)V
    .locals 2

    .line 2998
    new-instance v0, Lgroovyjarjarantlr/ActionTransInfo;

    invoke-direct {v0}, Lgroovyjarjarantlr/ActionTransInfo;-><init>()V

    .line 2999
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {p0, p1, p2, v1, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->processActionForSpecialSymbols(Ljava/lang/String;ILgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object p1

    .line 3002
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->charFormatter:Lgroovyjarjarantlr/CharFormatter;

    invoke-interface {p2, p1}, Lgroovyjarjarantlr/CharFormatter;->escapeString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    .line 3006
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-eqz v0, :cond_1

    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/ParserGrammar;

    if-nez v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz v0, :cond_1

    .line 3008
    :cond_0
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "fireSemanticPredicateEvaluated(antlr.debug.SemanticPredicateEvent.VALIDATING,"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->addSemPred(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, ", "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, ")"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    .line 3012
    :cond_1
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "if not "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, ":"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3013
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, 0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3014
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v0, "raise antlr.SemanticException(\""

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string p2, "\")"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3015
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    return-void
.end method

.method protected genSemPredMap()V
    .locals 3

    .line 3022
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->semPreds:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v0}, Lgroovyjarjarantlr/collections/impl/Vector;->elements()Ljava/util/Enumeration;

    move-result-object v0

    const-string v1, "_semPredNames = ["

    .line 3023
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3024
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v1, v1, 0x1

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3025
    :goto_0
    invoke-interface {v0}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 3026
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "\""

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-interface {v0}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, "\","

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 3028
    :cond_0
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v0, "]"

    .line 3029
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    return-void
.end method

.method protected genSynPred(Lgroovyjarjarantlr/SynPredBlock;Ljava/lang/String;)V
    .locals 5

    .line 3033
    iget-boolean v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->DEBUG_CODE_GENERATOR:Z

    const-string v1, ")"

    if-eqz v0, :cond_0

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "gen=>("

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 3036
    :cond_0
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "synPredMatched"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    iget v3, p1, Lgroovyjarjarantlr/SynPredBlock;->ID:I

    invoke-virtual {v0, v3}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v3, " = False"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3038
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "if "

    invoke-virtual {v0, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    const-string v0, ":"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3039
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, 0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3042
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p2, p2, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz p2, :cond_1

    .line 3043
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "_t"

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    iget v4, p1, Lgroovyjarjarantlr/SynPredBlock;->ID:I

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p2

    const-string v4, " = _t"

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_0

    .line 3046
    :cond_1
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "_m"

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    iget v4, p1, Lgroovyjarjarantlr/SynPredBlock;->ID:I

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p2

    const-string v4, " = self.mark()"

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3050
    :goto_0
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p2, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    iget v4, p1, Lgroovyjarjarantlr/SynPredBlock;->ID:I

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p2

    const-string v4, " = True"

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string p2, "self.inputState.guessing += 1"

    .line 3051
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3054
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean p2, p2, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    if-eqz p2, :cond_3

    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p2, p2, Lgroovyjarjarantlr/ParserGrammar;

    if-nez p2, :cond_2

    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p2, p2, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz p2, :cond_3

    :cond_2
    const-string p2, "self.fireSyntacticPredicateStarted()"

    .line 3056
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3059
    :cond_3
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    add-int/lit8 p2, p2, 0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    const-string p2, "try:"

    .line 3060
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3061
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, 0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3062
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->gen(Lgroovyjarjarantlr/AlternativeBlock;)V

    .line 3063
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, -0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3064
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "except "

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    iget-object v4, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->exceptionThrown:Ljava/lang/String;

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    const-string v4, ", pe:"

    invoke-virtual {p2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3065
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, 0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3066
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p2, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    iget v2, p1, Lgroovyjarjarantlr/SynPredBlock;->ID:I

    invoke-virtual {p2, v2}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3067
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, -0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3070
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p2, p2, Lgroovyjarjarantlr/TreeWalkerGrammar;

    if-eqz p2, :cond_4

    .line 3071
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "_t = _t"

    invoke-virtual {p2, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    iget v1, p1, Lgroovyjarjarantlr/SynPredBlock;->ID:I

    invoke-virtual {p2, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p2

    const-string v1, ""

    invoke-virtual {p2, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_1

    .line 3074
    :cond_4
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "self.rewind(_m"

    invoke-virtual {p2, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    iget v2, p1, Lgroovyjarjarantlr/SynPredBlock;->ID:I

    invoke-virtual {p2, v2}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :goto_1
    const-string p2, "self.inputState.guessing -= 1"

    .line 3077
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3080
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean p2, p2, Lgroovyjarjarantlr/Grammar;->debuggingOutput:Z

    const-string v1, "if synPredMatched"

    if-eqz p2, :cond_6

    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p2, p2, Lgroovyjarjarantlr/ParserGrammar;

    if-nez p2, :cond_5

    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of p2, p2, Lgroovyjarjarantlr/LexerGrammar;

    if-eqz p2, :cond_6

    .line 3082
    :cond_5
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p2, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    iget v2, p1, Lgroovyjarjarantlr/SynPredBlock;->ID:I

    invoke-virtual {p2, v2}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3083
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, 0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string p2, "self.fireSyntacticPredicateSucceeded()"

    .line 3084
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3085
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, -0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string p2, "else:"

    .line 3086
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3087
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, 0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string p2, "self.fireSyntacticPredicateFailed()"

    .line 3088
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3089
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, -0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3092
    :cond_6
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    add-int/lit8 p2, p2, -0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->syntacticPredLevel:I

    .line 3093
    iget p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p2, p2, -0x1

    iput p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3098
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p2, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    iget p1, p1, Lgroovyjarjarantlr/SynPredBlock;->ID:I

    invoke-virtual {p2, p1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    return-void
.end method

.method protected genTokenASTNodeMap()V
    .locals 8

    const-string v0, ""

    .line 3150
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v0, "def buildTokenTypeASTClassMap(self):"

    .line 3151
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3154
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v1, 0x1

    add-int/2addr v0, v1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3158
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v0, v0, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {v0}, Lgroovyjarjarantlr/TokenManager;->getVocabulary()Lgroovyjarjarantlr/collections/impl/Vector;

    move-result-object v0

    const/4 v2, 0x0

    move v3, v2

    move v4, v3

    .line 3159
    :goto_0
    invoke-virtual {v0}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v5

    if-ge v2, v5, :cond_2

    .line 3160
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/collections/impl/Vector;->elementAt(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    if-eqz v5, :cond_1

    .line 3162
    iget-object v6, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v6, v6, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {v6, v5}, Lgroovyjarjarantlr/TokenManager;->getTokenSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/TokenSymbol;

    move-result-object v5

    if-eqz v5, :cond_1

    .line 3163
    invoke-virtual {v5}, Lgroovyjarjarantlr/TokenSymbol;->getASTNodeType()Ljava/lang/String;

    move-result-object v6

    if-eqz v6, :cond_1

    add-int/lit8 v3, v3, 0x1

    if-nez v4, :cond_0

    const-string v4, "self.tokenTypeToASTClassMap = {}"

    .line 3167
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    move v4, v1

    .line 3170
    :cond_0
    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    const-string v7, "self.tokenTypeToASTClassMap["

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v5}, Lgroovyjarjarantlr/TokenSymbol;->getTokenType()I

    move-result v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v7, "] = "

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v5}, Lgroovyjarjarantlr/TokenSymbol;->getASTNodeType()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v6, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    if-nez v3, :cond_3

    const-string v0, "self.tokenTypeToASTClassMap = None"

    .line 3180
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3182
    :cond_3
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    sub-int/2addr v0, v1

    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    return-void
.end method

.method public genTokenStrings()V
    .locals 8

    .line 3112
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v1, 0x0

    .line 3113
    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v2, ""

    .line 3115
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v3, "_tokenNames = ["

    .line 3116
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3117
    iget v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v3, v3, 0x1

    iput v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3121
    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v3, v3, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {v3}, Lgroovyjarjarantlr/TokenManager;->getVocabulary()Lgroovyjarjarantlr/collections/impl/Vector;

    move-result-object v3

    .line 3122
    :goto_0
    invoke-virtual {v3}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v4

    if-ge v1, v4, :cond_3

    .line 3123
    invoke-virtual {v3, v1}, Lgroovyjarjarantlr/collections/impl/Vector;->elementAt(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    const-string v5, "<"

    if-nez v4, :cond_0

    .line 3125
    new-instance v4, Ljava/lang/StringBuffer;

    invoke-direct {v4}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v4, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    const-string v6, ">"

    invoke-virtual {v4, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v4

    :cond_0
    const-string v6, "\""

    .line 3127
    invoke-virtual {v4, v6}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v7

    if-nez v7, :cond_1

    invoke-virtual {v4, v5}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v5

    if-nez v5, :cond_1

    .line 3128
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v5, v5, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {v5, v4}, Lgroovyjarjarantlr/TokenManager;->getTokenSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/TokenSymbol;

    move-result-object v5

    if-eqz v5, :cond_1

    .line 3129
    invoke-virtual {v5}, Lgroovyjarjarantlr/TokenSymbol;->getParaphrase()Ljava/lang/String;

    move-result-object v7

    if-eqz v7, :cond_1

    .line 3130
    invoke-virtual {v5}, Lgroovyjarjarantlr/TokenSymbol;->getParaphrase()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4, v6, v6}, Lgroovyjarjarantlr/StringUtils;->stripFrontBack(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    .line 3133
    :cond_1
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->charFormatter:Lgroovyjarjarantlr/CharFormatter;

    invoke-interface {v5, v4}, Lgroovyjarjarantlr/CharFormatter;->literalString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->print(Ljava/lang/String;)V

    .line 3134
    invoke-virtual {v3}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v4

    add-int/lit8 v4, v4, -0x1

    if-eq v1, v4, :cond_2

    const-string v4, ", "

    .line 3135
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/PythonCodeGenerator;->_print(Ljava/lang/String;)V

    .line 3137
    :cond_2
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->_println(Ljava/lang/String;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 3141
    :cond_3
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v1, v1, -0x1

    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v1, "]"

    .line 3142
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3143
    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    return-void
.end method

.method protected genTokenTypes(Lgroovyjarjarantlr/TokenManager;)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 3193
    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3201
    invoke-interface {p1}, Lgroovyjarjarantlr/TokenManager;->getVocabulary()Lgroovyjarjarantlr/collections/impl/Vector;

    move-result-object v0

    const-string v1, "SKIP                = antlr.SKIP"

    .line 3204
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "INVALID_TYPE        = antlr.INVALID_TYPE"

    .line 3205
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "EOF_TYPE            = antlr.EOF_TYPE"

    .line 3206
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "EOF                 = antlr.EOF"

    .line 3207
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "NULL_TREE_LOOKAHEAD = antlr.NULL_TREE_LOOKAHEAD"

    .line 3208
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const-string v1, "MIN_USER_TYPE       = antlr.MIN_USER_TYPE"

    .line 3209
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    const/4 v1, 0x4

    .line 3211
    :goto_0
    invoke-virtual {v0}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v2

    if-ge v1, v2, :cond_5

    .line 3213
    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/collections/impl/Vector;->elementAt(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    if-eqz v2, :cond_4

    const-string v3, "\""

    .line 3216
    invoke-virtual {v2, v3}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v3

    const-string v4, " = "

    if-eqz v3, :cond_3

    .line 3219
    invoke-interface {p1, v2}, Lgroovyjarjarantlr/TokenManager;->getTokenSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/TokenSymbol;

    move-result-object v3

    check-cast v3, Lgroovyjarjarantlr/StringLiteralSymbol;

    if-nez v3, :cond_0

    .line 3221
    iget-object v5, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v6, Ljava/lang/StringBuffer;

    invoke-direct {v6}, Ljava/lang/StringBuffer;-><init>()V

    const-string v7, "String literal "

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    const-string v7, " not in symbol table"

    invoke-virtual {v6, v7}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    .line 3223
    :cond_0
    iget-object v5, v3, Lgroovyjarjarantlr/StringLiteralSymbol;->label:Ljava/lang/String;

    if-eqz v5, :cond_1

    .line 3225
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    iget-object v3, v3, Lgroovyjarjarantlr/StringLiteralSymbol;->label:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_1

    .line 3229
    :cond_1
    invoke-direct {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->mangleLiteral(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    if-eqz v5, :cond_2

    .line 3232
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 3234
    iput-object v5, v3, Lgroovyjarjarantlr/StringLiteralSymbol;->label:Ljava/lang/String;

    goto :goto_1

    .line 3238
    :cond_2
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v5, "### "

    invoke-virtual {v3, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    goto :goto_1

    :cond_3
    const-string v3, "<"

    .line 3242
    invoke-virtual {v2, v3}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_4

    .line 3243
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuffer;->append(I)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    :cond_4
    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto/16 :goto_0

    .line 3249
    :cond_5
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 3251
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->exitIfError()V

    return-void
.end method

.method public getASTCreateString(Lgroovyjarjarantlr/GrammarAtom;Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    if-eqz p1, :cond_0

    .line 3279
    invoke-virtual {p1}, Lgroovyjarjarantlr/GrammarAtom;->getASTNodeType()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 3282
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "self.astFactory.create("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    const-string v0, ", "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p2

    invoke-virtual {p1}, Lgroovyjarjarantlr/GrammarAtom;->getASTNodeType()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string p2, ")"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 3287
    :cond_0
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->getASTCreateString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getASTCreateString(Lgroovyjarjarantlr/collections/impl/Vector;)Ljava/lang/String;
    .locals 3

    .line 3258
    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v0

    if-nez v0, :cond_0

    const-string p1, ""

    return-object p1

    .line 3261
    :cond_0
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const-string v1, "antlr.make("

    .line 3262
    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    const/4 v1, 0x0

    .line 3263
    :cond_1
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v2

    if-ge v1, v2, :cond_2

    .line 3264
    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/collections/impl/Vector;->elementAt(I)Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    add-int/lit8 v1, v1, 0x1

    .line 3265
    invoke-virtual {p1}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v2

    if-ge v1, v2, :cond_1

    const-string v2, ", "

    .line 3266
    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    goto :goto_0

    :cond_2
    const-string p1, ")"

    .line 3269
    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 3270
    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getASTCreateString(Ljava/lang/String;)Ljava/lang/String;
    .locals 7

    const-string v0, ""

    if-nez p1, :cond_0

    move-object p1, v0

    :cond_0
    const/4 v1, 0x0

    move v2, v1

    move v3, v2

    .line 3304
    :goto_0
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v4

    const/16 v5, 0x2c

    if-ge v2, v4, :cond_2

    .line 3305
    invoke-virtual {p1, v2}, Ljava/lang/String;->charAt(I)C

    move-result v4

    if-ne v4, v5, :cond_1

    add-int/lit8 v3, v3, 0x1

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    const/4 v2, 0x2

    const-string v4, ")"

    const-string v6, "self.astFactory.create("

    if-ge v3, v2, :cond_7

    .line 3310
    invoke-virtual {p1, v5}, Ljava/lang/String;->indexOf(I)I

    move-result v2

    .line 3311
    invoke-virtual {p1, v5}, Ljava/lang/String;->lastIndexOf(I)I

    if-lez v3, :cond_3

    .line 3314
    invoke-virtual {p1, v1, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    :cond_3
    move-object v1, p1

    .line 3316
    :goto_1
    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-object v2, v2, Lgroovyjarjarantlr/Grammar;->tokenManager:Lgroovyjarjarantlr/TokenManager;

    invoke-interface {v2, v1}, Lgroovyjarjarantlr/TokenManager;->getTokenSymbol(Ljava/lang/String;)Lgroovyjarjarantlr/TokenSymbol;

    move-result-object v1

    if-eqz v1, :cond_5

    .line 3318
    invoke-virtual {v1}, Lgroovyjarjarantlr/TokenSymbol;->getASTNodeType()Ljava/lang/String;

    move-result-object v1

    if-nez v3, :cond_4

    const-string v0, ", \"\""

    :cond_4
    if-eqz v1, :cond_5

    .line 3325
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v2, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 3330
    :cond_5
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->labeledElementASTType:Ljava/lang/String;

    const-string v1, "AST"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 3331
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v0, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 3333
    :cond_6
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v0, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 3337
    :cond_7
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v0, v6}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected getLookaheadTestExpression(Lgroovyjarjarantlr/Alternative;I)Ljava/lang/String;
    .locals 2

    .line 3373
    iget v0, p1, Lgroovyjarjarantlr/Alternative;->lookaheadDepth:I

    const v1, 0x7fffffff

    if-ne v0, v1, :cond_0

    .line 3378
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v0, v0, Lgroovyjarjarantlr/Grammar;->maxk:I

    :cond_0
    if-nez p2, :cond_1

    const-string p1, "True"

    return-object p1

    .line 3388
    :cond_1
    iget-object p1, p1, Lgroovyjarjarantlr/Alternative;->cache:[Lgroovyjarjarantlr/Lookahead;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->getLookaheadTestExpression([Lgroovyjarjarantlr/Lookahead;I)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected getLookaheadTestExpression([Lgroovyjarjarantlr/Lookahead;I)Ljava/lang/String;
    .locals 5

    .line 3341
    new-instance v0, Ljava/lang/StringBuffer;

    const/16 v1, 0x64

    invoke-direct {v0, v1}, Ljava/lang/StringBuffer;-><init>(I)V

    const-string v1, "("

    .line 3344
    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    const/4 v1, 0x1

    move v2, v1

    :goto_0
    if-gt v1, p2, :cond_2

    .line 3346
    aget-object v3, p1, v1

    iget-object v3, v3, Lgroovyjarjarantlr/Lookahead;->fset:Lgroovyjarjarantlr/collections/impl/BitSet;

    if-nez v2, :cond_0

    const-string v2, ") and ("

    .line 3348
    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    :cond_0
    const/4 v2, 0x0

    .line 3355
    aget-object v4, p1, v1

    invoke-virtual {v4}, Lgroovyjarjarantlr/Lookahead;->containsEpsilon()Z

    move-result v4

    if-eqz v4, :cond_1

    const-string v3, "True"

    .line 3356
    invoke-virtual {v0, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    goto :goto_1

    .line 3359
    :cond_1
    invoke-virtual {p0, v1, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->getLookaheadTestTerm(ILgroovyjarjarantlr/collections/impl/BitSet;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    const-string p1, ")"

    .line 3363
    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 3364
    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected getLookaheadTestTerm(ILgroovyjarjarantlr/collections/impl/BitSet;)Ljava/lang/String;
    .locals 4

    .line 3401
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->lookaheadString(I)Ljava/lang/String;

    move-result-object v0

    .line 3404
    invoke-virtual {p2}, Lgroovyjarjarantlr/collections/impl/BitSet;->toArray()[I

    move-result-object v1

    .line 3405
    invoke-static {v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->elementsAreRange([I)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 3406
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->getRangeExpression(I[I)Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 3412
    :cond_0
    invoke-virtual {p2}, Lgroovyjarjarantlr/collections/impl/BitSet;->degree()I

    move-result p1

    if-nez p1, :cond_1

    const-string p1, "True"

    return-object p1

    .line 3417
    :cond_1
    iget v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->bitsetTestThreshold:I

    if-lt p1, v2, :cond_2

    .line 3418
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->markBitsetForGen(Lgroovyjarjarantlr/collections/impl/BitSet;)I

    move-result p1

    .line 3419
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->getBitsetName(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string p2, ".member("

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string p2, ")"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1

    .line 3423
    :cond_2
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    const/4 p2, 0x0

    .line 3424
    :goto_0
    array-length v2, v1

    if-ge p2, v2, :cond_4

    .line 3426
    aget v2, v1, p2

    const/4 v3, 0x1

    invoke-direct {p0, v2, v3}, Lgroovyjarjarantlr/PythonCodeGenerator;->getValueString(IZ)Ljava/lang/String;

    move-result-object v2

    if-lez p2, :cond_3

    const-string v3, " or "

    .line 3429
    invoke-virtual {p1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 3430
    :cond_3
    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    const-string v3, "=="

    .line 3431
    invoke-virtual {p1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 3432
    invoke-virtual {p1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    .line 3435
    :cond_4
    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public getRangeExpression(I[I)Ljava/lang/String;
    .locals 4

    .line 3444
    invoke-static {p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->elementsAreRange([I)Z

    move-result v0

    if-nez v0, :cond_0

    .line 3445
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    const-string v1, "getRangeExpression called with non-range"

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    :cond_0
    const/4 v0, 0x0

    .line 3447
    aget v0, p2, v0

    .line 3448
    array-length v1, p2

    const/4 v2, 0x1

    sub-int/2addr v1, v2

    aget p2, p2, v1

    .line 3449
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "("

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->lookaheadString(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v3, " >= "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-direct {p0, v0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->getValueString(IZ)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    const-string v1, " and "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-direct {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->lookaheadString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, " <= "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-direct {p0, p2, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->getValueString(IZ)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string p2, ")"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method protected isspace(C)Z
    .locals 1

    const/16 v0, 0x9

    if-eq p1, v0, :cond_0

    const/16 v0, 0xa

    if-eq p1, v0, :cond_0

    const/16 v0, 0xd

    if-eq p1, v0, :cond_0

    const/16 v0, 0x20

    if-eq p1, v0, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    :goto_0
    return p1
.end method

.method protected lookaheadIsEmpty(Lgroovyjarjarantlr/Alternative;I)Z
    .locals 4

    .line 3503
    iget v0, p1, Lgroovyjarjarantlr/Alternative;->lookaheadDepth:I

    const v1, 0x7fffffff

    if-ne v0, v1, :cond_0

    .line 3505
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget v0, v0, Lgroovyjarjarantlr/Grammar;->maxk:I

    :cond_0
    const/4 v1, 0x1

    move v2, v1

    :goto_0
    if-gt v2, v0, :cond_2

    if-gt v2, p2, :cond_2

    .line 3508
    iget-object v3, p1, Lgroovyjarjarantlr/Alternative;->cache:[Lgroovyjarjarantlr/Lookahead;

    aget-object v3, v3, v2

    iget-object v3, v3, Lgroovyjarjarantlr/Lookahead;->fset:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 3509
    invoke-virtual {v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->degree()I

    move-result v3

    if-eqz v3, :cond_1

    const/4 p1, 0x0

    return p1

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    return v1
.end method

.method public mapTreeId(Ljava/lang/String;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;
    .locals 6

    .line 3554
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    if-nez v0, :cond_0

    return-object p1

    .line 3558
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    instance-of v0, v0, Lgroovyjarjarantlr/TreeWalkerGrammar;

    const-string v1, "_in"

    const/4 v2, 0x1

    const/4 v3, 0x0

    if-eqz v0, :cond_2

    .line 3559
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    iget-boolean v0, v0, Lgroovyjarjarantlr/Grammar;->buildAST:Z

    if-nez v0, :cond_1

    goto :goto_0

    .line 3563
    :cond_1
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v4, 0x3

    if-le v0, v4, :cond_2

    invoke-virtual {p1, v1}, Ljava/lang/String;->lastIndexOf(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v5

    sub-int/2addr v5, v4

    if-ne v0, v5, :cond_2

    .line 3565
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    sub-int/2addr v0, v4

    invoke-virtual {p1, v3, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    :cond_2
    move v2, v3

    .line 3572
    :goto_0
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    iget-object v0, v0, Lgroovyjarjarantlr/RuleBlock;->labeledElements:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v0}, Lgroovyjarjarantlr/collections/impl/Vector;->size()I

    move-result v0

    const-string v4, "_AST"

    if-ge v3, v0, :cond_5

    .line 3573
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    iget-object v0, v0, Lgroovyjarjarantlr/RuleBlock;->labeledElements:Lgroovyjarjarantlr/collections/impl/Vector;

    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/collections/impl/Vector;->elementAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarantlr/AlternativeElement;

    .line 3574
    invoke-virtual {v0}, Lgroovyjarjarantlr/AlternativeElement;->getLabel()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    if-eqz v2, :cond_3

    goto :goto_1

    .line 3575
    :cond_3
    new-instance p2, Ljava/lang/StringBuffer;

    invoke-direct {p2}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p2, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    :goto_1
    return-object p1

    :cond_4
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 3582
    :cond_5
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->treeVariableMap:Ljava/util/Hashtable;

    invoke-virtual {v0, p1}, Ljava/util/Hashtable;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    if-eqz v0, :cond_9

    .line 3584
    sget-object p2, Lgroovyjarjarantlr/PythonCodeGenerator;->NONUNIQUE:Ljava/lang/String;

    const/4 v3, 0x0

    const-string v4, " in rule "

    const-string v5, "Ambiguous reference to AST element "

    if-ne v0, p2, :cond_6

    .line 3586
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v0, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {v0}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;)V

    return-object v3

    .line 3591
    :cond_6
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {p2}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_7

    .line 3594
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v0, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {v0}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;)V

    return-object v3

    :cond_7
    if-eqz v2, :cond_8

    .line 3599
    new-instance p1, Ljava/lang/StringBuffer;

    invoke-direct {p1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    :cond_8
    return-object v0

    .line 3605
    :cond_9
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {v0}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_b

    .line 3606
    new-instance v0, Ljava/lang/StringBuffer;

    if-eqz v2, :cond_a

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v0, "_AST_in"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    goto :goto_2

    :cond_a
    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    :goto_2
    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    if-eqz p2, :cond_b

    if-nez v2, :cond_b

    .line 3609
    iput-object p1, p2, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    :cond_b
    return-object p1
.end method

.method protected od(Ljava/lang/String;IILjava/lang/String;)V
    .locals 3

    .line 4019
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {v0, p4}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    :goto_0
    if-gt p2, p3, :cond_3

    .line 4023
    invoke-virtual {p1, p2}, Ljava/lang/String;->charAt(I)C

    move-result p4

    const/16 v0, 0x9

    if-eq p4, v0, :cond_2

    const/16 v0, 0xa

    if-eq p4, v0, :cond_1

    const/16 v0, 0x20

    if-eq p4, v0, :cond_0

    .line 4035
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, " "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p4}, Ljava/lang/StringBuffer;->append(C)Ljava/lang/StringBuffer;

    move-result-object p4

    invoke-virtual {p4, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p4

    invoke-virtual {p4}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p4

    invoke-virtual {v0, p4}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    goto :goto_1

    .line 4032
    :cond_0
    sget-object p4, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v0, " sp "

    invoke-virtual {p4, v0}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    goto :goto_1

    .line 4026
    :cond_1
    sget-object p4, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v0, " nl "

    invoke-virtual {p4, v0}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    goto :goto_1

    .line 4029
    :cond_2
    sget-object p4, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v0, " ht "

    invoke-virtual {p4, v0}, Ljava/io/PrintStream;->print(Ljava/lang/String;)V

    :goto_1
    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    .line 4038
    :cond_3
    sget-object p1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string p2, ""

    invoke-virtual {p1, p2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    return-void
.end method

.method protected printAction(Ljava/lang/String;)V
    .locals 0

    if-eqz p1, :cond_0

    .line 4043
    invoke-virtual {p0}, Lgroovyjarjarantlr/PythonCodeGenerator;->printTabs()V

    .line 4044
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->_printAction(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method protected printActionCode(Ljava/lang/String;I)V
    .locals 0

    .line 3759
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarantlr/PythonCodeGenerator;->processActionCode(Ljava/lang/String;I)Ljava/lang/String;

    move-result-object p1

    .line 3760
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printAction(Ljava/lang/String;)V

    return-void
.end method

.method protected printGrammarAction(Lgroovyjarjarantlr/Grammar;)V
    .locals 3

    const-string v0, "### user action >>>"

    .line 4049
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 4050
    iget-object v0, p1, Lgroovyjarjarantlr/Grammar;->classMemberAction:Lgroovyjarjarantlr/Token;

    invoke-virtual {v0}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v0

    iget-object p1, p1, Lgroovyjarjarantlr/Grammar;->classMemberAction:Lgroovyjarjarantlr/Token;

    invoke-virtual {p1}, Lgroovyjarjarantlr/Token;->getLine()I

    move-result p1

    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    const/4 v2, 0x0

    invoke-virtual {p0, v0, p1, v1, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->processActionForSpecialSymbols(Ljava/lang/String;ILgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printAction(Ljava/lang/String;)V

    const-string p1, "### user action <<<"

    .line 4057
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    return-void
.end method

.method protected printMainFunc(Ljava/lang/String;)V
    .locals 3

    .line 588
    iget v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const/4 v1, 0x0

    .line 589
    iput v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    const-string v2, "if __name__ == \'__main__\':"

    .line 590
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/PythonCodeGenerator;->println(Ljava/lang/String;)V

    .line 591
    iget v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 v2, v2, 0x1

    iput v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 592
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarantlr/PythonCodeGenerator;->printActionCode(Ljava/lang/String;I)V

    .line 593
    iget p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    .line 594
    iput v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    return-void
.end method

.method protected printTabs()V
    .locals 3

    const/4 v0, 0x0

    .line 83
    :goto_0
    iget v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->tabs:I

    if-ge v0, v1, :cond_0

    .line 85
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    const-string v2, "    "

    invoke-virtual {v1, v2}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method protected processActionCode(Ljava/lang/String;I)Ljava/lang/String;
    .locals 4

    const-string v0, "Error reading action:"

    if-eqz p1, :cond_1

    .line 3731
    invoke-static {p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->isEmpty(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_1

    .line 3734
    :cond_0
    new-instance v1, Lgroovyjarjarantlr/actions/python/CodeLexer;

    iget-object v2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {v2}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    invoke-direct {v1, p1, v2, p2, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;-><init>(Ljava/lang/String;Ljava/lang/String;ILgroovyjarjarantlr/Tool;)V

    const/4 p2, 0x1

    .line 3743
    :try_start_0
    invoke-virtual {v1, p2}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mACTION(Z)V

    .line 3744
    invoke-virtual {v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getTokenObject()Lgroovyjarjarantlr/Token;

    move-result-object p2

    invoke-virtual {p2}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object p1
    :try_end_0
    .catch Lgroovyjarjarantlr/RecognitionException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Lgroovyjarjarantlr/TokenStreamException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Lgroovyjarjarantlr/CharStreamException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 3753
    :catch_0
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    goto :goto_0

    .line 3750
    :catch_1
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    goto :goto_0

    :catch_2
    move-exception p2

    .line 3747
    invoke-virtual {v1, p2}, Lgroovyjarjarantlr/actions/python/CodeLexer;->reportError(Lgroovyjarjarantlr/RecognitionException;)V

    :goto_0
    return-object p1

    :cond_1
    :goto_1
    const-string p1, ""

    return-object p1
.end method

.method protected processActionForSpecialSymbols(Ljava/lang/String;ILgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;
    .locals 2

    const-string v0, "Error reading action:"

    if-eqz p1, :cond_3

    .line 3664
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v1

    if-nez v1, :cond_0

    goto :goto_1

    .line 3667
    :cond_0
    invoke-static {p1}, Lgroovyjarjarantlr/PythonCodeGenerator;->isEmpty(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_1

    const-string p1, ""

    return-object p1

    .line 3672
    :cond_1
    iget-object v1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    if-nez v1, :cond_2

    return-object p1

    .line 3678
    :cond_2
    new-instance v1, Lgroovyjarjarantlr/actions/python/ActionLexer;

    invoke-direct {v1, p1, p3, p0, p4}, Lgroovyjarjarantlr/actions/python/ActionLexer;-><init>(Ljava/lang/String;Lgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/CodeGenerator;Lgroovyjarjarantlr/ActionTransInfo;)V

    .line 3685
    invoke-virtual {v1, p2}, Lgroovyjarjarantlr/actions/python/ActionLexer;->setLineOffset(I)V

    .line 3686
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->grammar:Lgroovyjarjarantlr/Grammar;

    invoke-virtual {p2}, Lgroovyjarjarantlr/Grammar;->getFilename()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v1, p2}, Lgroovyjarjarantlr/actions/python/ActionLexer;->setFilename(Ljava/lang/String;)V

    .line 3687
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    invoke-virtual {v1, p2}, Lgroovyjarjarantlr/actions/python/ActionLexer;->setTool(Lgroovyjarjarantlr/Tool;)V

    const/4 p2, 0x1

    .line 3690
    :try_start_0
    invoke-virtual {v1, p2}, Lgroovyjarjarantlr/actions/python/ActionLexer;->mACTION(Z)V

    .line 3691
    invoke-virtual {v1}, Lgroovyjarjarantlr/actions/python/ActionLexer;->getTokenObject()Lgroovyjarjarantlr/Token;

    move-result-object p2

    invoke-virtual {p2}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object p1
    :try_end_0
    .catch Lgroovyjarjarantlr/RecognitionException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Lgroovyjarjarantlr/TokenStreamException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Lgroovyjarjarantlr/CharStreamException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 3700
    :catch_0
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance p3, Ljava/lang/StringBuffer;

    invoke-direct {p3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p3, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p3

    invoke-virtual {p3, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    goto :goto_0

    .line 3697
    :catch_1
    iget-object p2, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance p3, Ljava/lang/StringBuffer;

    invoke-direct {p3}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {p3, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p3

    invoke-virtual {p3, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Lgroovyjarjarantlr/Tool;->panic(Ljava/lang/String;)V

    goto :goto_0

    :catch_2
    move-exception p2

    .line 3694
    invoke-virtual {v1, p2}, Lgroovyjarjarantlr/actions/python/ActionLexer;->reportError(Lgroovyjarjarantlr/RecognitionException;)V

    :goto_0
    return-object p1

    :cond_3
    :goto_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public setupOutput(Ljava/lang/String;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 3870
    iget-object v0, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    const-string v1, ".py"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/Tool;->openOutputFile(Ljava/lang/String;)Ljava/io/PrintWriter;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarantlr/PythonCodeGenerator;->currentOutput:Ljava/io/PrintWriter;

    return-void
.end method

.method toString(Z)Ljava/lang/String;
    .locals 0

    if-eqz p1, :cond_0

    const-string p1, "True"

    goto :goto_0

    :cond_0
    const-string p1, "False"

    :goto_0
    return-object p1
.end method
