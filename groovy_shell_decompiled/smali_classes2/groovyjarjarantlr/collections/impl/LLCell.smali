.class Lgroovyjarjarantlr/collections/impl/LLCell;
.super Ljava/lang/Object;
.source "LLCell.java"


# instance fields
.field data:Ljava/lang/Object;

.field next:Lgroovyjarjarantlr/collections/impl/LLCell;


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 0

    .line 22
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 23
    iput-object p1, p0, Lgroovyjarjarantlr/collections/impl/LLCell;->data:Ljava/lang/Object;

    return-void
.end method
