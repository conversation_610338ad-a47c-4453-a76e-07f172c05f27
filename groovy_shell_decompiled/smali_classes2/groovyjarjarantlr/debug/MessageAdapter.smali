.class public Lgroovyjarjarantlr/debug/MessageAdapter;
.super Ljava/lang/Object;
.source "MessageAdapter.java"

# interfaces
.implements Lgroovyjarjarantlr/debug/MessageListener;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public doneParsing(Lgroovyjarjarantlr/debug/TraceEvent;)V
    .locals 0

    return-void
.end method

.method public refresh()V
    .locals 0

    return-void
.end method

.method public reportError(Lgroovyjarjarantlr/debug/MessageEvent;)V
    .locals 0

    return-void
.end method

.method public reportWarning(Lgroovyjarjarantlr/debug/MessageEvent;)V
    .locals 0

    return-void
.end method
