.class public Lgroovyjarjarantlr/debug/ParserTokenAdapter;
.super Ljava/lang/Object;
.source "ParserTokenAdapter.java"

# interfaces
.implements Lgroovyjarjarantlr/debug/ParserTokenListener;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public doneParsing(Lgroovyjarjarantlr/debug/TraceEvent;)V
    .locals 0

    return-void
.end method

.method public parserConsume(Lgroovyjarjarantlr/debug/ParserTokenEvent;)V
    .locals 0

    return-void
.end method

.method public parserLA(Lgroovyjarjarantlr/debug/ParserTokenEvent;)V
    .locals 0

    return-void
.end method

.method public refresh()V
    .locals 0

    return-void
.end method
