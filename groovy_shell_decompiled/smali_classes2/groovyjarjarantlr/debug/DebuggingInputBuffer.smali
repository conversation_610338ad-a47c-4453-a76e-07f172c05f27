.class public Lgroovyjarjarantlr/debug/DebuggingInputBuffer;
.super Lgroovyjarjarantlr/InputBuffer;
.source "DebuggingInputBuffer.java"


# instance fields
.field private buffer:Lgroovyjarjarantlr/InputBuffer;

.field private debugMode:Z

.field private inputBufferEventSupport:Lgroovyjarjarantlr/debug/InputBufferEventSupport;


# direct methods
.method public constructor <init>(Lgroovyjarjarantlr/InputBuffer;)V
    .locals 1

    .line 14
    invoke-direct {p0}, Lgroovyjarjarantlr/InputBuffer;-><init>()V

    const/4 v0, 0x1

    .line 11
    iput-boolean v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->debugMode:Z

    .line 15
    iput-object p1, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->buffer:Lgroovyjarjarantlr/InputBuffer;

    .line 16
    new-instance p1, Lgroovyjarjarantlr/debug/InputBufferEventSupport;

    invoke-direct {p1, p0}, Lgroovyjarjarantlr/debug/InputBufferEventSupport;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->inputBufferEventSupport:Lgroovyjarjarantlr/debug/InputBufferEventSupport;

    return-void
.end method


# virtual methods
.method public LA(I)C
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/CharStreamException;
        }
    .end annotation

    .line 42
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->buffer:Lgroovyjarjarantlr/InputBuffer;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/InputBuffer;->LA(I)C

    move-result v0

    .line 43
    iget-boolean v1, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->debugMode:Z

    if-eqz v1, :cond_0

    .line 44
    iget-object v1, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->inputBufferEventSupport:Lgroovyjarjarantlr/debug/InputBufferEventSupport;

    invoke-virtual {v1, v0, p1}, Lgroovyjarjarantlr/debug/InputBufferEventSupport;->fireLA(CI)V

    :cond_0
    return v0
.end method

.method public addInputBufferListener(Lgroovyjarjarantlr/debug/InputBufferListener;)V
    .locals 1

    .line 19
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->inputBufferEventSupport:Lgroovyjarjarantlr/debug/InputBufferEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/InputBufferEventSupport;->addInputBufferListener(Lgroovyjarjarantlr/debug/InputBufferListener;)V

    return-void
.end method

.method public consume()V
    .locals 2

    .line 23
    :try_start_0
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->buffer:Lgroovyjarjarantlr/InputBuffer;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/InputBuffer;->LA(I)C

    move-result v0
    :try_end_0
    .catch Lgroovyjarjarantlr/CharStreamException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    const/16 v0, 0x20

    .line 25
    :goto_0
    iget-object v1, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->buffer:Lgroovyjarjarantlr/InputBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/InputBuffer;->consume()V

    .line 26
    iget-boolean v1, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->debugMode:Z

    if-eqz v1, :cond_0

    .line 27
    iget-object v1, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->inputBufferEventSupport:Lgroovyjarjarantlr/debug/InputBufferEventSupport;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/debug/InputBufferEventSupport;->fireConsume(C)V

    :cond_0
    return-void
.end method

.method public fill(I)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/CharStreamException;
        }
    .end annotation

    .line 30
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->buffer:Lgroovyjarjarantlr/InputBuffer;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/InputBuffer;->fill(I)V

    return-void
.end method

.method public getInputBufferListeners()Ljava/util/Vector;
    .locals 1

    .line 33
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->inputBufferEventSupport:Lgroovyjarjarantlr/debug/InputBufferEventSupport;

    invoke-virtual {v0}, Lgroovyjarjarantlr/debug/InputBufferEventSupport;->getInputBufferListeners()Ljava/util/Vector;

    move-result-object v0

    return-object v0
.end method

.method public isDebugMode()Z
    .locals 1

    .line 36
    iget-boolean v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->debugMode:Z

    return v0
.end method

.method public isMarked()Z
    .locals 1

    .line 39
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->buffer:Lgroovyjarjarantlr/InputBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/InputBuffer;->isMarked()Z

    move-result v0

    return v0
.end method

.method public mark()I
    .locals 2

    .line 48
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->buffer:Lgroovyjarjarantlr/InputBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/InputBuffer;->mark()I

    move-result v0

    .line 49
    iget-object v1, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->inputBufferEventSupport:Lgroovyjarjarantlr/debug/InputBufferEventSupport;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/debug/InputBufferEventSupport;->fireMark(I)V

    return v0
.end method

.method public removeInputBufferListener(Lgroovyjarjarantlr/debug/InputBufferListener;)V
    .locals 1

    .line 53
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->inputBufferEventSupport:Lgroovyjarjarantlr/debug/InputBufferEventSupport;

    if-eqz v0, :cond_0

    .line 54
    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/InputBufferEventSupport;->removeInputBufferListener(Lgroovyjarjarantlr/debug/InputBufferListener;)V

    :cond_0
    return-void
.end method

.method public rewind(I)V
    .locals 1

    .line 57
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->buffer:Lgroovyjarjarantlr/InputBuffer;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/InputBuffer;->rewind(I)V

    .line 58
    iget-object v0, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->inputBufferEventSupport:Lgroovyjarjarantlr/debug/InputBufferEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/InputBufferEventSupport;->fireRewind(I)V

    return-void
.end method

.method public setDebugMode(Z)V
    .locals 0

    .line 61
    iput-boolean p1, p0, Lgroovyjarjarantlr/debug/DebuggingInputBuffer;->debugMode:Z

    return-void
.end method
