.class public Lgroovyjarjarantlr/debug/SyntacticPredicateAdapter;
.super Ljava/lang/Object;
.source "SyntacticPredicateAdapter.java"

# interfaces
.implements Lgroovyjarjarantlr/debug/SyntacticPredicateListener;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public doneParsing(Lgroovyjarjarantlr/debug/TraceEvent;)V
    .locals 0

    return-void
.end method

.method public refresh()V
    .locals 0

    return-void
.end method

.method public syntacticPredicateFailed(Lgroovyjarjarantlr/debug/SyntacticPredicateEvent;)V
    .locals 0

    return-void
.end method

.method public syntacticPredicateStarted(Lgroovyjarjarantlr/debug/SyntacticPredicateEvent;)V
    .locals 0

    return-void
.end method

.method public syntacticPredicateSucceeded(Lgroovyjarjarantlr/debug/SyntacticPredicateEvent;)V
    .locals 0

    return-void
.end method
