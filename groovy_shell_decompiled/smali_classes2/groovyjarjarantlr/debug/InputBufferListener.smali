.class public interface abstract Lgroovyjarjarantlr/debug/InputBufferListener;
.super Ljava/lang/Object;
.source "InputBufferListener.java"

# interfaces
.implements Lgroovyjarjarantlr/debug/ListenerBase;


# virtual methods
.method public abstract inputBufferConsume(Lgroovyjarjarantlr/debug/InputBufferEvent;)V
.end method

.method public abstract inputBufferLA(Lgroovyjarjarantlr/debug/InputBufferEvent;)V
.end method

.method public abstract inputBufferMark(Lgroovyjarjarantlr/debug/InputBufferEvent;)V
.end method

.method public abstract inputBufferRewind(Lgroovyjarjarantlr/debug/InputBufferEvent;)V
.end method
