.class public Lgroovyjarjarantlr/debug/SemanticPredicateAdapter;
.super Ljava/lang/Object;
.source "SemanticPredicateAdapter.java"

# interfaces
.implements Lgroovyjarjarantlr/debug/SemanticPredicateListener;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public doneParsing(Lgroovyjarjarantlr/debug/TraceEvent;)V
    .locals 0

    return-void
.end method

.method public refresh()V
    .locals 0

    return-void
.end method

.method public semanticPredicateEvaluated(Lgroovyjarjarantlr/debug/SemanticPredicateEvent;)V
    .locals 0

    return-void
.end method
