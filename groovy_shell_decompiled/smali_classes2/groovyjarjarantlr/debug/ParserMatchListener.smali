.class public interface abstract Lgroovyjarjarantlr/debug/ParserMatchListener;
.super Ljava/lang/Object;
.source "ParserMatchListener.java"

# interfaces
.implements Lgroovyjarjarantlr/debug/ListenerBase;


# virtual methods
.method public abstract parserMatch(Lgroovyjarjarantlr/debug/ParserMatchEvent;)V
.end method

.method public abstract parserMatchNot(Lgroovyjarjarantlr/debug/ParserMatchEvent;)V
.end method

.method public abstract parserMismatch(Lgroovyjarjarantlr/debug/ParserMatchEvent;)V
.end method

.method public abstract parserMismatchNot(Lgroovyjarjarantlr/debug/ParserMatchEvent;)V
.end method
