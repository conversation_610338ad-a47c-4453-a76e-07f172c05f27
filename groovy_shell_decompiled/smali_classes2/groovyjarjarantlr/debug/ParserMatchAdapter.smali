.class public Lgroovyjarjarantlr/debug/ParserMatchAdapter;
.super Ljava/lang/Object;
.source "ParserMatchAdapter.java"

# interfaces
.implements Lgroovyjarjarantlr/debug/ParserMatchListener;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public doneParsing(Lgroovyjarjarantlr/debug/TraceEvent;)V
    .locals 0

    return-void
.end method

.method public parserMatch(Lgroovyjarjarantlr/debug/ParserMatchEvent;)V
    .locals 0

    return-void
.end method

.method public parserMatchNot(Lgroovyjarjarantlr/debug/ParserMatchEvent;)V
    .locals 0

    return-void
.end method

.method public parserMismatch(Lgroovyjarjarantlr/debug/ParserMatchEvent;)V
    .locals 0

    return-void
.end method

.method public parserMismatchNot(Lgroovyjarjarantlr/debug/ParserMatchEvent;)V
    .locals 0

    return-void
.end method

.method public refresh()V
    .locals 0

    return-void
.end method
