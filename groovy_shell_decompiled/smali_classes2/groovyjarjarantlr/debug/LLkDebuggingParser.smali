.class public Lgroovyjarjarantlr/debug/LLkDebuggingParser;
.super Lgroovyjarjarantlr/LLkParser;
.source "LLkDebuggingParser.java"

# interfaces
.implements Lgroovyjarjarantlr/debug/DebuggingParser;


# static fields
.field static synthetic class$antlr$TokenBuffer:Ljava/lang/Class;

.field static synthetic class$antlr$TokenStream:Ljava/lang/Class;

.field static synthetic class$antlr$debug$LLkDebuggingParser:Ljava/lang/Class;


# instance fields
.field private _notDebugMode:Z

.field protected parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

.field protected ruleNames:[Ljava/lang/String;

.field protected semPredNames:[Ljava/lang/String;


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 21
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/LLkParser;-><init>(I)V

    .line 13
    new-instance p1, Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-direct {p1, p0}, Lgroovyjarjarantlr/debug/ParserEventSupport;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    const/4 p1, 0x0

    .line 15
    iput-boolean p1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->_notDebugMode:Z

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr/ParserSharedInputState;I)V
    .locals 0

    .line 24
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr/LLkParser;-><init>(Lgroovyjarjarantlr/ParserSharedInputState;I)V

    .line 13
    new-instance p1, Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-direct {p1, p0}, Lgroovyjarjarantlr/debug/ParserEventSupport;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    const/4 p1, 0x0

    .line 15
    iput-boolean p1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->_notDebugMode:Z

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr/TokenBuffer;I)V
    .locals 0

    .line 27
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr/LLkParser;-><init>(Lgroovyjarjarantlr/TokenBuffer;I)V

    .line 13
    new-instance p1, Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-direct {p1, p0}, Lgroovyjarjarantlr/debug/ParserEventSupport;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    const/4 p1, 0x0

    .line 15
    iput-boolean p1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->_notDebugMode:Z

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr/TokenStream;I)V
    .locals 0

    .line 30
    invoke-direct {p0, p1, p2}, Lgroovyjarjarantlr/LLkParser;-><init>(Lgroovyjarjarantlr/TokenStream;I)V

    .line 13
    new-instance p1, Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-direct {p1, p0}, Lgroovyjarjarantlr/debug/ParserEventSupport;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    const/4 p1, 0x0

    .line 15
    iput-boolean p1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->_notDebugMode:Z

    return-void
.end method

.method static synthetic class$(Ljava/lang/String;)Ljava/lang/Class;
    .locals 1

    .line 216
    :try_start_0
    invoke-static {p0}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    new-instance v0, Ljava/lang/NoClassDefFoundError;

    invoke-virtual {p0}, Ljava/lang/ClassNotFoundException;->getMessage()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/lang/NoClassDefFoundError;-><init>(Ljava/lang/String;)V

    throw v0
.end method


# virtual methods
.method public LA(I)I
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 107
    invoke-super {p0, p1}, Lgroovyjarjarantlr/LLkParser;->LA(I)I

    move-result v0

    .line 108
    iget-object v1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v1, p1, v0}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireLA(II)V

    return v0
.end method

.method public addMessageListener(Lgroovyjarjarantlr/debug/MessageListener;)V
    .locals 1

    .line 33
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->addMessageListener(Lgroovyjarjarantlr/debug/MessageListener;)V

    return-void
.end method

.method public addParserListener(Lgroovyjarjarantlr/debug/ParserListener;)V
    .locals 1

    .line 36
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->addParserListener(Lgroovyjarjarantlr/debug/ParserListener;)V

    return-void
.end method

.method public addParserMatchListener(Lgroovyjarjarantlr/debug/ParserMatchListener;)V
    .locals 1

    .line 39
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->addParserMatchListener(Lgroovyjarjarantlr/debug/ParserMatchListener;)V

    return-void
.end method

.method public addParserTokenListener(Lgroovyjarjarantlr/debug/ParserTokenListener;)V
    .locals 1

    .line 42
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->addParserTokenListener(Lgroovyjarjarantlr/debug/ParserTokenListener;)V

    return-void
.end method

.method public addSemanticPredicateListener(Lgroovyjarjarantlr/debug/SemanticPredicateListener;)V
    .locals 1

    .line 45
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->addSemanticPredicateListener(Lgroovyjarjarantlr/debug/SemanticPredicateListener;)V

    return-void
.end method

.method public addSyntacticPredicateListener(Lgroovyjarjarantlr/debug/SyntacticPredicateListener;)V
    .locals 1

    .line 48
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->addSyntacticPredicateListener(Lgroovyjarjarantlr/debug/SyntacticPredicateListener;)V

    return-void
.end method

.method public addTraceListener(Lgroovyjarjarantlr/debug/TraceListener;)V
    .locals 1

    .line 51
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->addTraceListener(Lgroovyjarjarantlr/debug/TraceListener;)V

    return-void
.end method

.method public consume()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    const/4 v0, 0x1

    .line 56
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->LA(I)I

    move-result v0

    .line 57
    invoke-super {p0}, Lgroovyjarjarantlr/LLkParser;->consume()V

    .line 58
    iget-object v1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireConsume(I)V

    return-void
.end method

.method protected fireEnterRule(II)V
    .locals 2

    .line 61
    invoke-virtual {p0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->isDebugMode()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 62
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v1, v1, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v0, p1, v1, p2}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireEnterRule(III)V

    :cond_0
    return-void
.end method

.method protected fireExitRule(II)V
    .locals 2

    .line 65
    invoke-virtual {p0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->isDebugMode()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 66
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v1, v1, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v0, p1, v1, p2}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireExitRule(III)V

    :cond_0
    return-void
.end method

.method protected fireSemanticPredicateEvaluated(IIZ)Z
    .locals 2

    .line 69
    invoke-virtual {p0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->isDebugMode()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 70
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v1, v1, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v0, p1, p2, p3, v1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireSemanticPredicateEvaluated(IIZI)Z

    move-result p1

    return p1

    :cond_0
    return p3
.end method

.method protected fireSyntacticPredicateFailed()V
    .locals 2

    .line 75
    invoke-virtual {p0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->isDebugMode()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 76
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v1, v1, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireSyntacticPredicateFailed(I)V

    :cond_0
    return-void
.end method

.method protected fireSyntacticPredicateStarted()V
    .locals 2

    .line 79
    invoke-virtual {p0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->isDebugMode()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 80
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v1, v1, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireSyntacticPredicateStarted(I)V

    :cond_0
    return-void
.end method

.method protected fireSyntacticPredicateSucceeded()V
    .locals 2

    .line 83
    invoke-virtual {p0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->isDebugMode()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 84
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v1, v1, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v0, v1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireSyntacticPredicateSucceeded(I)V

    :cond_0
    return-void
.end method

.method public getRuleName(I)Ljava/lang/String;
    .locals 1

    .line 87
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->ruleNames:[Ljava/lang/String;

    aget-object p1, v0, p1

    return-object p1
.end method

.method public getSemPredName(I)Ljava/lang/String;
    .locals 1

    .line 90
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->semPredNames:[Ljava/lang/String;

    aget-object p1, v0, p1

    return-object p1
.end method

.method public declared-synchronized goToSleep()V
    .locals 1

    monitor-enter p0

    .line 93
    :try_start_0
    invoke-virtual {p0}, Ljava/lang/Object;->wait()V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0

    .line 95
    :catch_0
    :goto_0
    monitor-exit p0

    return-void
.end method

.method public isDebugMode()Z
    .locals 1

    .line 97
    iget-boolean v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->_notDebugMode:Z

    xor-int/lit8 v0, v0, 0x1

    return v0
.end method

.method public isGuessing()Z
    .locals 1

    .line 100
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v0, v0, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    if-lez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public match(I)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/MismatchedTokenException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    const/4 v0, 0x1

    .line 116
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->LT(I)Lgroovyjarjarantlr/Token;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v1

    .line 117
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->LA(I)I

    move-result v0

    .line 119
    :try_start_0
    invoke-super {p0, p1}, Lgroovyjarjarantlr/LLkParser;->match(I)V

    .line 120
    iget-object v2, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v3, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v3, v3, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v2, p1, v1, v3}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireMatch(ILjava/lang/String;I)V
    :try_end_0
    .catch Lgroovyjarjarantlr/MismatchedTokenException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v2

    .line 123
    iget-object v3, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v3, v3, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    if-nez v3, :cond_0

    .line 124
    iget-object v3, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v4, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v4, v4, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v3, v0, p1, v1, v4}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireMismatch(IILjava/lang/String;I)V

    .line 125
    :cond_0
    throw v2
.end method

.method public match(Lgroovyjarjarantlr/collections/impl/BitSet;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/MismatchedTokenException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    const/4 v0, 0x1

    .line 133
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->LT(I)Lgroovyjarjarantlr/Token;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v1

    .line 134
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->LA(I)I

    move-result v0

    .line 136
    :try_start_0
    invoke-super {p0, p1}, Lgroovyjarjarantlr/LLkParser;->match(Lgroovyjarjarantlr/collections/impl/BitSet;)V

    .line 137
    iget-object v2, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v3, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v3, v3, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v2, v0, p1, v1, v3}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireMatch(ILgroovyjarjarantlr/collections/impl/BitSet;Ljava/lang/String;I)V
    :try_end_0
    .catch Lgroovyjarjarantlr/MismatchedTokenException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v2

    .line 140
    iget-object v3, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v3, v3, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    if-nez v3, :cond_0

    .line 141
    iget-object v3, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v4, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v4, v4, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v3, v0, p1, v1, v4}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireMismatch(ILgroovyjarjarantlr/collections/impl/BitSet;Ljava/lang/String;I)V

    .line 142
    :cond_0
    throw v2
.end method

.method public matchNot(I)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/MismatchedTokenException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    const/4 v0, 0x1

    .line 146
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->LT(I)Lgroovyjarjarantlr/Token;

    move-result-object v1

    invoke-virtual {v1}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v1

    .line 147
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->LA(I)I

    move-result v0

    .line 149
    :try_start_0
    invoke-super {p0, p1}, Lgroovyjarjarantlr/LLkParser;->matchNot(I)V

    .line 150
    iget-object v2, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v3, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v3, v3, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v2, v0, p1, v1, v3}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireMatchNot(IILjava/lang/String;I)V
    :try_end_0
    .catch Lgroovyjarjarantlr/MismatchedTokenException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v2

    .line 153
    iget-object v3, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v3, v3, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    if-nez v3, :cond_0

    .line 154
    iget-object v3, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    iget-object v4, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->inputState:Lgroovyjarjarantlr/ParserSharedInputState;

    iget v4, v4, Lgroovyjarjarantlr/ParserSharedInputState;->guessing:I

    invoke-virtual {v3, v0, p1, v1, v4}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireMismatchNot(IILjava/lang/String;I)V

    .line 155
    :cond_0
    throw v2
.end method

.method public removeMessageListener(Lgroovyjarjarantlr/debug/MessageListener;)V
    .locals 1

    .line 159
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->removeMessageListener(Lgroovyjarjarantlr/debug/MessageListener;)V

    return-void
.end method

.method public removeParserListener(Lgroovyjarjarantlr/debug/ParserListener;)V
    .locals 1

    .line 162
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->removeParserListener(Lgroovyjarjarantlr/debug/ParserListener;)V

    return-void
.end method

.method public removeParserMatchListener(Lgroovyjarjarantlr/debug/ParserMatchListener;)V
    .locals 1

    .line 165
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->removeParserMatchListener(Lgroovyjarjarantlr/debug/ParserMatchListener;)V

    return-void
.end method

.method public removeParserTokenListener(Lgroovyjarjarantlr/debug/ParserTokenListener;)V
    .locals 1

    .line 168
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->removeParserTokenListener(Lgroovyjarjarantlr/debug/ParserTokenListener;)V

    return-void
.end method

.method public removeSemanticPredicateListener(Lgroovyjarjarantlr/debug/SemanticPredicateListener;)V
    .locals 1

    .line 171
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->removeSemanticPredicateListener(Lgroovyjarjarantlr/debug/SemanticPredicateListener;)V

    return-void
.end method

.method public removeSyntacticPredicateListener(Lgroovyjarjarantlr/debug/SyntacticPredicateListener;)V
    .locals 1

    .line 174
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->removeSyntacticPredicateListener(Lgroovyjarjarantlr/debug/SyntacticPredicateListener;)V

    return-void
.end method

.method public removeTraceListener(Lgroovyjarjarantlr/debug/TraceListener;)V
    .locals 1

    .line 177
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->removeTraceListener(Lgroovyjarjarantlr/debug/TraceListener;)V

    return-void
.end method

.method public reportError(Lgroovyjarjarantlr/RecognitionException;)V
    .locals 1

    .line 181
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireReportError(Ljava/lang/Exception;)V

    .line 182
    invoke-super {p0, p1}, Lgroovyjarjarantlr/LLkParser;->reportError(Lgroovyjarjarantlr/RecognitionException;)V

    return-void
.end method

.method public reportError(Ljava/lang/String;)V
    .locals 1

    .line 186
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireReportError(Ljava/lang/String;)V

    .line 187
    invoke-super {p0, p1}, Lgroovyjarjarantlr/LLkParser;->reportError(Ljava/lang/String;)V

    return-void
.end method

.method public reportWarning(Ljava/lang/String;)V
    .locals 1

    .line 191
    iget-object v0, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->parserEventSupport:Lgroovyjarjarantlr/debug/ParserEventSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/debug/ParserEventSupport;->fireReportWarning(Ljava/lang/String;)V

    .line 192
    invoke-super {p0, p1}, Lgroovyjarjarantlr/LLkParser;->reportWarning(Ljava/lang/String;)V

    return-void
.end method

.method public setDebugMode(Z)V
    .locals 0

    xor-int/lit8 p1, p1, 0x1

    .line 195
    iput-boolean p1, p0, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->_notDebugMode:Z

    return-void
.end method

.method public setupDebugging(Lgroovyjarjarantlr/TokenBuffer;)V
    .locals 1

    const/4 v0, 0x0

    .line 198
    invoke-virtual {p0, v0, p1}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->setupDebugging(Lgroovyjarjarantlr/TokenStream;Lgroovyjarjarantlr/TokenBuffer;)V

    return-void
.end method

.method public setupDebugging(Lgroovyjarjarantlr/TokenStream;)V
    .locals 1

    const/4 v0, 0x0

    .line 201
    invoke-virtual {p0, p1, v0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->setupDebugging(Lgroovyjarjarantlr/TokenStream;Lgroovyjarjarantlr/TokenBuffer;)V

    return-void
.end method

.method protected setupDebugging(Lgroovyjarjarantlr/TokenStream;Lgroovyjarjarantlr/TokenBuffer;)V
    .locals 7

    const/4 v0, 0x1

    .line 205
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->setDebugMode(Z)V

    :try_start_0
    const-string v1, "javax.swing.JButton"

    .line 209
    invoke-static {v1}, Lgroovyjarjarantlr/Utils;->loadClass(Ljava/lang/String;)Ljava/lang/Class;
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    goto :goto_1

    .line 212
    :catch_1
    :try_start_1
    sget-object v1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v2, "Swing is required to use ParseView, but is not present in your CLASSPATH"

    invoke-virtual {v1, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 213
    invoke-static {v0}, Ljava/lang/System;->exit(I)V

    :goto_0
    const-string v1, "groovyjarjarantlr.parseview.ParseView"

    .line 215
    invoke-static {v1}, Lgroovyjarjarantlr/Utils;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v1

    const/4 v2, 0x3

    new-array v3, v2, [Ljava/lang/Class;

    .line 216
    sget-object v4, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->class$antlr$debug$LLkDebuggingParser:Ljava/lang/Class;

    if-nez v4, :cond_0

    const-string v4, "groovyjarjarantlr.debug.LLkDebuggingParser"

    invoke-static {v4}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->class$(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v4

    sput-object v4, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->class$antlr$debug$LLkDebuggingParser:Ljava/lang/Class;

    :cond_0
    const/4 v5, 0x0

    aput-object v4, v3, v5

    sget-object v4, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->class$antlr$TokenStream:Ljava/lang/Class;

    if-nez v4, :cond_1

    const-string v4, "groovyjarjarantlr.TokenStream"

    invoke-static {v4}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->class$(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v4

    sput-object v4, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->class$antlr$TokenStream:Ljava/lang/Class;

    :cond_1
    aput-object v4, v3, v0

    sget-object v4, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->class$antlr$TokenBuffer:Ljava/lang/Class;

    if-nez v4, :cond_2

    const-string v4, "groovyjarjarantlr.TokenBuffer"

    invoke-static {v4}, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->class$(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v4

    sput-object v4, Lgroovyjarjarantlr/debug/LLkDebuggingParser;->class$antlr$TokenBuffer:Ljava/lang/Class;

    :cond_2
    const/4 v6, 0x2

    aput-object v4, v3, v6

    invoke-virtual {v1, v3}, Ljava/lang/Class;->getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v1

    new-array v2, v2, [Ljava/lang/Object;

    aput-object p0, v2, v5

    aput-object p1, v2, v0

    aput-object p2, v2, v6

    .line 217
    invoke-virtual {v1, v2}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    goto :goto_2

    .line 220
    :goto_1
    sget-object p2, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "Error initializing ParseView: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 221
    sget-object p1, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string p2, "Please report this to Scott Stanchfield, <EMAIL>"

    invoke-virtual {p1, p2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 222
    invoke-static {v0}, Ljava/lang/System;->exit(I)V

    :goto_2
    return-void
.end method

.method public declared-synchronized wakeUp()V
    .locals 1

    monitor-enter p0

    .line 226
    :try_start_0
    invoke-virtual {p0}, Ljava/lang/Object;->notify()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 227
    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method
