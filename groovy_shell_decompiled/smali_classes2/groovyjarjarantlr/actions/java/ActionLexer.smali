.class public Lgroovyjarjarantlr/actions/java/ActionLexer;
.super Lgroovyjarjarantlr/CharScanner;
.source "ActionLexer.java"

# interfaces
.implements Lgroovyjarjarantlr/actions/java/ActionLexerTokenTypes;
.implements Lgroovyjarjarantlr/TokenStream;


# static fields
.field public static final _tokenSet_0:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_1:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_10:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_11:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_12:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_13:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_14:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_15:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_16:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_17:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_18:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_19:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_2:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_20:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_21:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_22:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_23:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_24:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_25:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_3:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_4:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_5:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_6:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_7:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_8:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_9:Lgroovyjarjarantlr/collections/impl/BitSet;


# instance fields
.field private antlrTool:Lgroovyjarjarantlr/Tool;

.field protected currentRule:Lgroovyjarjarantlr/RuleBlock;

.field protected generator:Lgroovyjarjarantlr/CodeGenerator;

.field protected lineOffset:I

.field transInfo:Lgroovyjarjarantlr/ActionTransInfo;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 2333
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_0()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_0:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2340
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_1()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_1:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2347
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_2()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_2:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2352
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_3()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_3:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2357
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_4()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_4:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2362
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_5()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_5:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2367
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_6()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_6:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2375
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_7()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_7:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2383
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_8()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_8:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2388
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_9()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_9:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2393
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_10()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_10:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2398
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_11()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_11:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2403
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_12()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_12:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2408
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_13()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_13:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2413
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_14()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_14:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2418
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_15()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_15:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2423
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_16()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_16:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2428
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_17()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_17:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2433
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_18()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_18:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2438
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_19()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_19:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2443
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_20()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_20:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2448
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_21()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_21:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2453
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_22()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_22:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2458
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_23()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_23:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2463
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_24()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_24:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 2468
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mk_tokenSet_25()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_25:Lgroovyjarjarantlr/collections/impl/BitSet;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr/InputBuffer;)V
    .locals 1

    .line 108
    new-instance v0, Lgroovyjarjarantlr/LexerSharedInputState;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr/LexerSharedInputState;-><init>(Lgroovyjarjarantlr/InputBuffer;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;-><init>(Lgroovyjarjarantlr/LexerSharedInputState;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr/LexerSharedInputState;)V
    .locals 0

    .line 111
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/CharScanner;-><init>(Lgroovyjarjarantlr/LexerSharedInputState;)V

    const/4 p1, 0x0

    .line 59
    iput p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->lineOffset:I

    const/4 p1, 0x1

    .line 112
    iput-boolean p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->caseSensitiveLiterals:Z

    .line 113
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->setCaseSensitive(Z)V

    .line 114
    new-instance p1, Ljava/util/Hashtable;

    invoke-direct {p1}, Ljava/util/Hashtable;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->literals:Ljava/util/Hashtable;

    return-void
.end method

.method public constructor <init>(Ljava/io/InputStream;)V
    .locals 1

    .line 102
    new-instance v0, Lgroovyjarjarantlr/ByteBuffer;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr/ByteBuffer;-><init>(Ljava/io/InputStream;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;-><init>(Lgroovyjarjarantlr/InputBuffer;)V

    return-void
.end method

.method public constructor <init>(Ljava/io/Reader;)V
    .locals 1

    .line 105
    new-instance v0, Lgroovyjarjarantlr/CharBuffer;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr/CharBuffer;-><init>(Ljava/io/Reader;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;-><init>(Lgroovyjarjarantlr/InputBuffer;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarantlr/RuleBlock;Lgroovyjarjarantlr/CodeGenerator;Lgroovyjarjarantlr/ActionTransInfo;)V
    .locals 1

    .line 67
    new-instance v0, Ljava/io/StringReader;

    invoke-direct {v0, p1}, Ljava/io/StringReader;-><init>(Ljava/lang/String;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;-><init>(Ljava/io/Reader;)V

    .line 68
    iput-object p2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    .line 69
    iput-object p3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->generator:Lgroovyjarjarantlr/CodeGenerator;

    .line 70
    iput-object p4, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->transInfo:Lgroovyjarjarantlr/ActionTransInfo;

    return-void
.end method

.method private static final mk_tokenSet_0()[J
    .locals 4

    const/16 v0, 0x8

    new-array v0, v0, [J

    const/4 v1, 0x0

    const-wide v2, -0x1800000008L

    aput-wide v2, v0, v1

    const/4 v1, 0x1

    :goto_0
    const/4 v2, 0x3

    if-gt v1, v2, :cond_0

    const-wide/16 v2, -0x1

    .line 2330
    aput-wide v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static final mk_tokenSet_1()[J
    .locals 4

    const/16 v0, 0x8

    new-array v0, v0, [J

    const/4 v1, 0x0

    const-wide v2, -0x840000000008L

    aput-wide v2, v0, v1

    const/4 v1, 0x1

    :goto_0
    const/4 v2, 0x3

    if-gt v1, v2, :cond_0

    const-wide/16 v2, -0x1

    .line 2337
    aput-wide v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static final mk_tokenSet_10()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2390
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ff010d00002600L
        0x7fffffe8ffffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_11()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2395
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ff089400000000L
        0x7fffffe87fffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_12()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2400
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ff1a9500002600L    # 1.99479437137812E-289
        0x7fffffea7fffffeL    # 3.7857645700037357E-270
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_13()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2405
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x2000ff0100002600L    # 1.58453465115535E-154
        0x28000000
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_14()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2410
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ff038d00002600L
        0x7fffffe8ffffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_15()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2415
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x2000000100002600L    # 1.491669568808863E-154
        0x0
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_16()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2420
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x2000be0100002600L
        0x20000000
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_17()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2425
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0xbe0100002600L
        0x20000000
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_18()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2430
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x10c00000000L
        0x7fffffe8ffffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_19()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2435
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0xac0100002600L
        0x0
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_2()[J
    .locals 4

    const/16 v0, 0x8

    new-array v0, v0, [J

    const/4 v1, 0x0

    const-wide v2, -0x809c00002408L

    aput-wide v2, v0, v1

    const/4 v1, 0x1

    :goto_0
    const/4 v2, 0x3

    if-gt v1, v2, :cond_0

    const-wide/16 v2, -0x1

    .line 2344
    aput-wide v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static final mk_tokenSet_20()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2440
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ffad8d00002600L
        0x7fffffe8ffffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_21()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2445
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ff5b9500002600L
        0x7fffffeaffffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_22()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2450
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ff0a9500002600L
        0x7fffffe87fffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_23()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2455
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ff089500002600L
        0x7fffffe87fffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_24()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2460
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3fffe9500002600L
        0x7fffffea7fffffeL    # 3.7857645700037357E-270
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_25()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2465
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ffbe9500002600L
        0x7fffffea7fffffeL    # 3.7857645700037357E-270
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_3()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2349
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x0
        0x7fffffe87fffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_4()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2354
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x100002600L    # 2.122000597E-314
        0x0
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_5()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2359
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x10100002600L
        0x0
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_6()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2364
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ff099500002600L
        0x7fffffe87fffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method

.method private static final mk_tokenSet_7()[J
    .locals 4

    const/16 v0, 0x8

    new-array v0, v0, [J

    const/4 v1, 0x0

    const-wide v2, -0x400000008L

    aput-wide v2, v0, v1

    const-wide/32 v1, -0x10000001

    const/4 v3, 0x1

    aput-wide v1, v0, v3

    const/4 v1, 0x2

    :goto_0
    const/4 v2, 0x3

    if-gt v1, v2, :cond_0

    const-wide/16 v2, -0x1

    .line 2372
    aput-wide v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static final mk_tokenSet_8()[J
    .locals 4

    const/16 v0, 0x8

    new-array v0, v0, [J

    const/4 v1, 0x0

    const-wide v2, -0x8000000008L

    aput-wide v2, v0, v1

    const-wide/32 v1, -0x10000001

    const/4 v3, 0x1

    aput-wide v1, v0, v3

    const/4 v1, 0x2

    :goto_0
    const/4 v2, 0x3

    if-gt v1, v2, :cond_0

    const-wide/16 v2, -0x1

    .line 2380
    aput-wide v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static final mk_tokenSet_9()[J
    .locals 1

    const/4 v0, 0x5

    new-array v0, v0, [J

    .line 2385
    fill-array-data v0, :array_0

    return-object v0

    nop

    :array_0
    .array-data 8
        0x3ff000000000000L
        0x7fffffe87fffffeL
        0x0
        0x0
        0x0
    .end array-data
.end method


# virtual methods
.method public final mACTION(Z)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 156
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    const/4 v3, 0x1

    .line 164
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    const/16 v5, 0x23

    if-eq v4, v5, :cond_4

    const/16 v5, 0x24

    if-eq v4, v5, :cond_3

    .line 176
    sget-object v4, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_0:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v5

    invoke-virtual {v4, v5}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v4

    if-eqz v4, :cond_0

    .line 177
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mSTUFF(Z)V

    goto :goto_2

    :cond_0
    if-lt v2, v3, :cond_2

    if-eqz p1, :cond_1

    const/4 p1, 0x4

    .line 187
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 188
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    .line 190
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 180
    :cond_2
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 172
    :cond_3
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ITEM(Z)V

    goto :goto_2

    .line 167
    :cond_4
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mAST_ITEM(Z)V

    :goto_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0
.end method

.method protected final mARG(Z)V
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    move-object/from16 v0, p0

    .line 1697
    iget-object v1, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    const/4 v2, 0x1

    .line 1702
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x22

    const/16 v5, 0x27

    const/16 v6, 0xff

    const/4 v7, 0x2

    const/4 v8, 0x0

    const/4 v9, 0x3

    if-eq v3, v5, :cond_2

    packed-switch v3, :pswitch_data_0

    .line 1716
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_18:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v10

    invoke-virtual {v3, v10}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v9, :cond_0

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v6, :cond_0

    invoke-virtual {v0, v9}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v9, :cond_0

    invoke-virtual {v0, v9}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v6, :cond_0

    .line 1717
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTREE_ELEMENT(Z)V

    goto :goto_0

    .line 1719
    :cond_0
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v4, :cond_1

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v9, :cond_1

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v6, :cond_1

    invoke-virtual {v0, v9}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v9, :cond_1

    invoke-virtual {v0, v9}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v6, :cond_1

    .line 1720
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mSTRING(Z)V

    goto :goto_0

    .line 1723
    :cond_1
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1712
    :pswitch_0
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mINT_OR_FLOAT(Z)V

    goto :goto_0

    .line 1705
    :cond_2
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mCHAR(Z)V

    .line 1730
    :goto_0
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_19:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v10

    invoke-virtual {v3, v10}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_c

    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_20:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v10

    invoke-virtual {v3, v10}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_c

    invoke-virtual {v0, v9}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v9, :cond_c

    invoke-virtual {v0, v9}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v6, :cond_c

    .line 1732
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v10, 0x20

    const/16 v11, 0xd

    const/16 v12, 0xa

    const/16 v13, 0x9

    const/16 v14, 0x2b

    const/16 v15, 0x2a

    const/16 v6, 0x2f

    const/16 v7, 0x2d

    if-eq v3, v13, :cond_4

    if-eq v3, v12, :cond_4

    if-eq v3, v11, :cond_4

    if-eq v3, v10, :cond_4

    if-eq v3, v7, :cond_5

    if-eq v3, v6, :cond_5

    if-eq v3, v15, :cond_5

    if-ne v3, v14, :cond_3

    goto :goto_1

    .line 1744
    :cond_3
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1735
    :cond_4
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1749
    :cond_5
    :goto_1
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v15, :cond_9

    if-eq v3, v14, :cond_8

    if-eq v3, v7, :cond_7

    if-ne v3, v6, :cond_6

    .line 1767
    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_2

    .line 1772
    :cond_6
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1757
    :cond_7
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_2

    .line 1752
    :cond_8
    invoke-virtual {v0, v14}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_2

    .line 1762
    :cond_9
    invoke-virtual {v0, v15}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1777
    :goto_2
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v13, :cond_a

    if-eq v3, v12, :cond_a

    if-eq v3, v11, :cond_a

    if-eq v3, v10, :cond_a

    const/16 v6, 0x5f

    if-eq v3, v6, :cond_b

    if-eq v3, v4, :cond_b

    const/16 v6, 0x23

    if-eq v3, v6, :cond_b

    if-eq v3, v5, :cond_b

    const/16 v6, 0x28

    if-eq v3, v6, :cond_b

    packed-switch v3, :pswitch_data_1

    packed-switch v3, :pswitch_data_2

    packed-switch v3, :pswitch_data_3

    .line 1805
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1780
    :cond_a
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1809
    :cond_b
    :pswitch_1
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mARG(Z)V

    const/16 v6, 0xff

    const/4 v7, 0x2

    goto/16 :goto_0

    :cond_c
    if-eqz p1, :cond_d

    const/16 v2, 0x10

    .line 1818
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object v2

    .line 1819
    new-instance v3, Ljava/lang/String;

    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v4

    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v5

    sub-int/2addr v5, v1

    invoke-direct {v3, v4, v1, v5}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_3

    :cond_d
    const/4 v2, 0x0

    .line 1821
    :goto_3
    iput-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x30
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x30
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x41
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0x61
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch
.end method

.method protected final mAST_CONSTRUCTOR(Z)V
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    move-object/from16 v0, p0

    .line 973
    iget-object v1, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    .line 980
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    const/16 v3, 0x5b

    .line 981
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 982
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    const/4 v2, 0x1

    .line 984
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x23

    const/16 v5, 0x22

    const/16 v6, 0x5f

    const/16 v7, 0x28

    const/4 v8, 0x0

    const/16 v9, 0x20

    const/16 v10, 0xd

    const/16 v11, 0x9

    const/16 v12, 0xa

    if-eq v3, v11, :cond_0

    if-eq v3, v12, :cond_0

    if-eq v3, v10, :cond_0

    if-eq v3, v9, :cond_0

    if-eq v3, v7, :cond_1

    if-eq v3, v6, :cond_1

    if-eq v3, v5, :cond_1

    if-eq v3, v4, :cond_1

    packed-switch v3, :pswitch_data_0

    packed-switch v3, :pswitch_data_1

    packed-switch v3, :pswitch_data_2

    .line 1014
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 987
    :cond_0
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    .line 988
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 989
    iget-object v13, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v13, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1018
    :cond_1
    :pswitch_0
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    .line 1019
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mAST_CTOR_ELEMENT(Z)V

    .line 1020
    iget-object v13, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v13, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1021
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 1023
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v13

    const/16 v14, 0x5d

    const/16 v15, 0x2c

    if-eq v13, v11, :cond_3

    if-eq v13, v12, :cond_3

    if-eq v13, v10, :cond_3

    if-eq v13, v9, :cond_3

    if-eq v13, v15, :cond_4

    if-ne v13, v14, :cond_2

    goto :goto_0

    .line 1037
    :cond_2
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1026
    :cond_3
    iget-object v13, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v13}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v13

    .line 1027
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1028
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v13}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1042
    :cond_4
    :goto_0
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v13

    if-ne v13, v15, :cond_9

    sget-object v13, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_10:Lgroovyjarjarantlr/collections/impl/BitSet;

    const/4 v14, 0x2

    invoke-virtual {v0, v14}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v14

    invoke-virtual {v13, v14}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v13

    if-eqz v13, :cond_9

    const/4 v13, 0x3

    invoke-virtual {v0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v14

    if-lt v14, v13, :cond_9

    invoke-virtual {v0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v13

    const/16 v14, 0xff

    if-gt v13, v14, :cond_9

    .line 1043
    iget-object v13, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v13}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v13

    .line 1044
    invoke-virtual {v0, v15}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1045
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v13}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1047
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v13

    if-eq v13, v11, :cond_5

    if-eq v13, v12, :cond_5

    if-eq v13, v10, :cond_5

    if-eq v13, v9, :cond_5

    if-eq v13, v7, :cond_6

    if-eq v13, v6, :cond_6

    if-eq v13, v5, :cond_6

    if-eq v13, v4, :cond_6

    packed-switch v13, :pswitch_data_3

    packed-switch v13, :pswitch_data_4

    packed-switch v13, :pswitch_data_5

    .line 1077
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1050
    :cond_5
    iget-object v13, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v13}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v13

    .line 1051
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1052
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v13}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1081
    :cond_6
    :pswitch_1
    iget-object v13, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v13}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v13

    .line 1082
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mAST_CTOR_ELEMENT(Z)V

    .line 1083
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v13}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1084
    iget-object v13, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 1086
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v14

    if-eq v14, v11, :cond_8

    if-eq v14, v12, :cond_8

    if-eq v14, v10, :cond_8

    if-eq v14, v9, :cond_8

    if-eq v14, v15, :cond_c

    const/16 v4, 0x5d

    if-ne v14, v4, :cond_7

    goto :goto_2

    .line 1100
    :cond_7
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1089
    :cond_8
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v4

    .line 1090
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1091
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    goto :goto_2

    .line 1105
    :cond_9
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    if-eq v4, v15, :cond_b

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    const/16 v13, 0x5d

    if-ne v4, v13, :cond_a

    goto :goto_1

    .line 1108
    :cond_a
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :cond_b
    :goto_1
    const/4 v13, 0x0

    .line 1113
    :cond_c
    :goto_2
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    if-eq v4, v15, :cond_e

    const/16 v14, 0x5d

    if-ne v4, v14, :cond_d

    const/4 v4, 0x0

    goto/16 :goto_3

    .line 1185
    :cond_d
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1116
    :cond_e
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v4

    .line 1117
    invoke-virtual {v0, v15}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1118
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1120
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    if-eq v4, v11, :cond_f

    if-eq v4, v12, :cond_f

    if-eq v4, v10, :cond_f

    if-eq v4, v9, :cond_f

    if-eq v4, v7, :cond_10

    if-eq v4, v6, :cond_10

    if-eq v4, v5, :cond_10

    const/16 v5, 0x23

    if-eq v4, v5, :cond_10

    packed-switch v4, :pswitch_data_6

    packed-switch v4, :pswitch_data_7

    packed-switch v4, :pswitch_data_8

    .line 1150
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1123
    :cond_f
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v4

    .line 1124
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1125
    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5, v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1154
    :cond_10
    :pswitch_2
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v4

    .line 1155
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mAST_CTOR_ELEMENT(Z)V

    .line 1156
    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5, v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1157
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 1159
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v5

    if-eq v5, v11, :cond_12

    if-eq v5, v12, :cond_12

    if-eq v5, v10, :cond_12

    if-eq v5, v9, :cond_12

    const/16 v6, 0x5d

    if-ne v5, v6, :cond_11

    goto :goto_3

    .line 1173
    :cond_11
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1162
    :cond_12
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    .line 1163
    invoke-virtual {v0, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1164
    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1189
    :goto_3
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    const/16 v5, 0x5d

    .line 1190
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1191
    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1193
    invoke-virtual {v3}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v2

    const-string v3, ","

    if-eqz v13, :cond_13

    .line 1195
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v5, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v13}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    :cond_13
    if-eqz v4, :cond_14

    .line 1198
    new-instance v5, Ljava/lang/StringBuffer;

    invoke-direct {v5}, Ljava/lang/StringBuffer;-><init>()V

    invoke-virtual {v5, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v4}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    .line 1200
    :cond_14
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->generator:Lgroovyjarjarantlr/CodeGenerator;

    const/4 v5, 0x0

    invoke-virtual {v4, v5, v2}, Lgroovyjarjarantlr/CodeGenerator;->getASTCreateString(Lgroovyjarjarantlr/GrammarAtom;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    if-eqz p1, :cond_15

    .line 1203
    invoke-virtual {v0, v12}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object v14

    .line 1204
    new-instance v2, Ljava/lang/String;

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v3

    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v4

    sub-int/2addr v4, v1

    invoke-direct {v2, v3, v1, v4}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {v14, v2}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_4

    :cond_15
    move-object v14, v5

    .line 1206
    :goto_4
    iput-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x30
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x41
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x61
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0x30
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch

    :pswitch_data_4
    .packed-switch 0x41
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch

    :pswitch_data_5
    .packed-switch 0x61
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch

    :pswitch_data_6
    .packed-switch 0x30
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
    .end packed-switch

    :pswitch_data_7
    .packed-switch 0x41
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
    .end packed-switch

    :pswitch_data_8
    .packed-switch 0x61
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
    .end packed-switch
.end method

.method protected final mAST_CTOR_ELEMENT(Z)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 1646
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 1650
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/4 v3, 0x0

    const/16 v4, 0xff

    const/4 v5, 0x2

    const/4 v6, 0x3

    const/16 v7, 0x22

    if-ne v2, v7, :cond_0

    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v6, :cond_0

    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v4, :cond_0

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v6, :cond_0

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v4, :cond_0

    .line 1651
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mSTRING(Z)V

    goto :goto_0

    .line 1653
    :cond_0
    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_18:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v7

    invoke-virtual {v2, v7}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v6, :cond_1

    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v4, :cond_1

    .line 1654
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTREE_ELEMENT(Z)V

    goto :goto_0

    .line 1656
    :cond_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v4, 0x30

    if-lt v2, v4, :cond_3

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v4, 0x39

    if-gt v2, v4, :cond_3

    .line 1657
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mINT(Z)V

    :goto_0
    if-eqz p1, :cond_2

    const/16 p1, 0xb

    .line 1664
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 1665
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_2
    const/4 p1, 0x0

    .line 1667
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 1660
    :cond_3
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method protected final mAST_ITEM(Z)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 250
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 257
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/4 v3, 0x2

    const/16 v4, 0x23

    if-ne v2, v4, :cond_0

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v5, 0x28

    if-ne v2, v5, :cond_0

    .line 258
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    .line 259
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 260
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 261
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTREE(Z)V

    .line 262
    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    goto/16 :goto_0

    .line 264
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v5, 0x3d

    const/4 v6, 0x0

    if-ne v2, v4, :cond_3

    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_3:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v7

    invoke-virtual {v2, v7}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_3

    .line 265
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    .line 266
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 267
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 268
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mID(Z)V

    .line 269
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 271
    invoke-virtual {v2}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v2

    .line 272
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->generator:Lgroovyjarjarantlr/CodeGenerator;

    iget-object v4, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->transInfo:Lgroovyjarjarantlr/ActionTransInfo;

    invoke-virtual {v3, v2, v4}, Lgroovyjarjarantlr/CodeGenerator;->mapTreeId(Ljava/lang/String;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 274
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    .line 278
    :cond_1
    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_4:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 279
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 286
    :cond_2
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-ne v1, v5, :cond_7

    .line 287
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mVAR_ASSIGN(Z)V

    goto/16 :goto_0

    .line 294
    :cond_3
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_4

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v7, 0x5b

    if-ne v2, v7, :cond_4

    .line 295
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    .line 296
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 297
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 298
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mAST_CONSTRUCTOR(Z)V

    .line 299
    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    goto :goto_0

    .line 301
    :cond_4
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_9

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_9

    const-string v2, "##"

    .line 302
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 304
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {v3}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    const-string v3, "_AST"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    .line 305
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->transInfo:Lgroovyjarjarantlr/ActionTransInfo;

    if-eqz v3, :cond_5

    .line 306
    iput-object v2, v3, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    .line 310
    :cond_5
    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_4:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_6

    .line 311
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 318
    :cond_6
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-ne v1, v5, :cond_7

    .line 319
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mVAR_ASSIGN(Z)V

    :cond_7
    :goto_0
    if-eqz p1, :cond_8

    const/4 p1, 0x6

    .line 331
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 332
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_8
    const/4 p1, 0x0

    .line 334
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 327
    :cond_9
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method protected final mCHAR(Z)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 631
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/16 v1, 0x27

    .line 635
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    const/4 v2, 0x1

    .line 637
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x5c

    if-ne v3, v4, :cond_0

    const/4 v2, 0x0

    .line 638
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mESC(Z)V

    goto :goto_0

    .line 640
    :cond_0
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_8:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_2

    .line 641
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchNot(C)V

    .line 648
    :goto_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    if-eqz p1, :cond_1

    const/16 p1, 0x16

    .line 650
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 651
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    .line 653
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 644
    :cond_2
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method protected final mCOMMENT(Z)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 580
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 584
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/4 v3, 0x0

    const/4 v4, 0x2

    const/16 v5, 0x2f

    if-ne v2, v5, :cond_0

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_0

    .line 585
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mSL_COMMENT(Z)V

    goto :goto_0

    .line 587
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_2

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v4, 0x2a

    if-ne v2, v4, :cond_2

    .line 588
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mML_COMMENT(Z)V

    :goto_0
    if-eqz p1, :cond_1

    const/16 p1, 0x13

    .line 595
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 596
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    .line 598
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 591
    :cond_2
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method protected final mDIGIT(Z)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 2314
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/16 v1, 0x30

    const/16 v2, 0x39

    .line 2318
    invoke-virtual {p0, v1, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchRange(CC)V

    if-eqz p1, :cond_0

    const/16 p1, 0x19

    .line 2320
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 2321
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    .line 2323
    :goto_0
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void
.end method

.method protected final mESC(Z)V
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 2206
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/16 v1, 0x5c

    .line 2210
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    const/4 v2, 0x1

    .line 2212
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x22

    if-eq v3, v4, :cond_d

    const/16 v4, 0x27

    if-eq v3, v4, :cond_c

    if-eq v3, v1, :cond_b

    const/16 v1, 0x62

    if-eq v3, v1, :cond_a

    const/16 v1, 0x66

    if-eq v3, v1, :cond_9

    const/16 v1, 0x6e

    if-eq v3, v1, :cond_8

    const/16 v1, 0x72

    if-eq v3, v1, :cond_7

    const/16 v1, 0x74

    if-eq v3, v1, :cond_6

    const/4 v1, 0x0

    const/16 v4, 0x39

    const/16 v5, 0x30

    const/4 v6, 0x2

    const/16 v7, 0xff

    const/4 v8, 0x3

    packed-switch v3, :pswitch_data_0

    .line 2302
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    :pswitch_0
    const/16 v3, 0x34

    const/16 v9, 0x37

    .line 2285
    invoke-virtual {p0, v3, v9}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchRange(CC)V

    .line 2288
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v5, :cond_0

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v4, :cond_0

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v8, :cond_0

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v7, :cond_0

    .line 2289
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mDIGIT(Z)V

    goto/16 :goto_0

    .line 2291
    :cond_0
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-lt v1, v8, :cond_1

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-gt v1, v7, :cond_1

    goto/16 :goto_0

    .line 2294
    :cond_1
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    :pswitch_1
    const/16 v3, 0x33

    .line 2256
    invoke-virtual {p0, v5, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchRange(CC)V

    .line 2259
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v5, :cond_4

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v4, :cond_4

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v8, :cond_4

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v7, :cond_4

    .line 2260
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mDIGIT(Z)V

    .line 2262
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v5, :cond_2

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v4, :cond_2

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v8, :cond_2

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v7, :cond_2

    .line 2263
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mDIGIT(Z)V

    goto :goto_0

    .line 2265
    :cond_2
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-lt v1, v8, :cond_3

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-gt v1, v7, :cond_3

    goto :goto_0

    .line 2268
    :cond_3
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 2273
    :cond_4
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-lt v1, v8, :cond_5

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-gt v1, v7, :cond_5

    goto :goto_0

    .line 2276
    :cond_5
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 2225
    :cond_6
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    .line 2220
    :cond_7
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    .line 2215
    :cond_8
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    .line 2235
    :cond_9
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    .line 2230
    :cond_a
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    .line 2250
    :cond_b
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    .line 2245
    :cond_c
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    .line 2240
    :cond_d
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    :goto_0
    if-eqz p1, :cond_e

    const/16 p1, 0x18

    .line 2307
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 2308
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_e
    const/4 p1, 0x0

    .line 2310
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x30
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method protected final mID(Z)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 811
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 816
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x7a

    const/16 v4, 0x61

    const/16 v5, 0x5a

    const/16 v6, 0x41

    const/16 v7, 0x5f

    packed-switch v2, :pswitch_data_0

    .line 846
    :pswitch_0
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 825
    :pswitch_1
    invoke-virtual {p0, v4, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchRange(CC)V

    goto :goto_0

    .line 841
    :pswitch_2
    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    .line 836
    :pswitch_3
    invoke-virtual {p0, v6, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchRange(CC)V

    .line 853
    :goto_0
    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_9:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v8

    invoke-virtual {v2, v8}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 855
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-eq v2, v7, :cond_0

    packed-switch v2, :pswitch_data_1

    packed-switch v2, :pswitch_data_2

    packed-switch v2, :pswitch_data_3

    .line 892
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 864
    :pswitch_4
    invoke-virtual {p0, v4, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchRange(CC)V

    goto :goto_0

    .line 875
    :pswitch_5
    invoke-virtual {p0, v6, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchRange(CC)V

    goto :goto_0

    :pswitch_6
    const/16 v2, 0x30

    const/16 v8, 0x39

    .line 882
    invoke-virtual {p0, v2, v8}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchRange(CC)V

    goto :goto_0

    .line 887
    :cond_0
    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    :cond_1
    if-eqz p1, :cond_2

    const/16 p1, 0x11

    .line 904
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 905
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_2
    const/4 p1, 0x0

    .line 907
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    :pswitch_data_0
    .packed-switch 0x41
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_2
        :pswitch_0
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x30
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x41
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0x61
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
    .end packed-switch
.end method

.method protected final mID_ELEMENT(Z)Z
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    move-object/from16 v0, p0

    .line 1367
    iget-object v1, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    const/4 v2, 0x1

    .line 1372
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mID(Z)V

    .line 1373
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 1375
    sget-object v4, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_4:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v5

    invoke-virtual {v4, v5}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v5

    const/4 v6, 0x2

    const/4 v7, 0x0

    if-eqz v5, :cond_0

    sget-object v5, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_13:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v8

    invoke-virtual {v5, v8}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v5

    if-eqz v5, :cond_0

    .line 1376
    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v5

    .line 1377
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1378
    iget-object v8, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v8, v5}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    goto :goto_0

    .line 1380
    :cond_0
    sget-object v5, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_13:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v8

    invoke-virtual {v5, v8}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v5

    if-eqz v5, :cond_17

    .line 1388
    :goto_0
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v5

    const/16 v8, 0x3d

    const/16 v9, 0x20

    const/16 v10, 0xd

    const/16 v11, 0xa

    const/16 v12, 0x9

    if-eq v5, v12, :cond_11

    if-eq v5, v11, :cond_11

    if-eq v5, v10, :cond_11

    if-eq v5, v9, :cond_11

    if-eq v5, v8, :cond_11

    const/16 v8, 0x5f

    const/16 v13, 0x28

    const/16 v14, 0x5d

    const/16 v15, 0x5b

    if-eq v5, v15, :cond_9

    if-eq v5, v14, :cond_11

    packed-switch v5, :pswitch_data_0

    .line 1630
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :pswitch_0
    const/16 v2, 0x2e

    .line 1585
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1586
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mID_ELEMENT(Z)Z

    goto/16 :goto_7

    .line 1391
    :pswitch_1
    invoke-virtual {v0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1393
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v4, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    const/16 v4, 0xff

    const/4 v5, 0x3

    if-eqz v3, :cond_1

    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_14:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v14

    invoke-virtual {v3, v14}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-virtual {v0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v5, :cond_1

    invoke-virtual {v0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v4, :cond_1

    .line 1394
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    .line 1395
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1396
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    goto :goto_1

    .line 1398
    :cond_1
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_14:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v14

    invoke-virtual {v3, v14}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_8

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v5, :cond_8

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v4, :cond_8

    .line 1406
    :goto_1
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v12, :cond_5

    if-eq v3, v11, :cond_5

    if-eq v3, v10, :cond_5

    if-eq v3, v9, :cond_5

    if-eq v3, v8, :cond_2

    const/16 v4, 0x22

    if-eq v3, v4, :cond_2

    const/16 v4, 0x23

    if-eq v3, v4, :cond_2

    packed-switch v3, :pswitch_data_1

    packed-switch v3, :pswitch_data_2

    packed-switch v3, :pswitch_data_3

    packed-switch v3, :pswitch_data_4

    .line 1483
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1425
    :cond_2
    :pswitch_2
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mARG(Z)V

    .line 1429
    :goto_2
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x2c

    if-ne v3, v4, :cond_5

    .line 1430
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1432
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v12, :cond_3

    if-eq v3, v11, :cond_3

    if-eq v3, v10, :cond_3

    if-eq v3, v9, :cond_3

    if-eq v3, v8, :cond_4

    const/16 v4, 0x22

    if-eq v3, v4, :cond_4

    const/16 v4, 0x23

    if-eq v3, v4, :cond_4

    const/16 v4, 0x27

    if-eq v3, v4, :cond_4

    if-eq v3, v13, :cond_4

    packed-switch v3, :pswitch_data_5

    packed-switch v3, :pswitch_data_6

    packed-switch v3, :pswitch_data_7

    .line 1462
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1435
    :cond_3
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    .line 1436
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1437
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1466
    :cond_4
    :pswitch_3
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mARG(Z)V

    goto :goto_2

    .line 1488
    :cond_5
    :pswitch_4
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x29

    if-eq v3, v12, :cond_7

    if-eq v3, v11, :cond_7

    if-eq v3, v10, :cond_7

    if-eq v3, v9, :cond_7

    if-ne v3, v4, :cond_6

    goto :goto_3

    .line 1502
    :cond_6
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1491
    :cond_7
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    .line 1492
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1493
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1506
    :goto_3
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto/16 :goto_7

    .line 1401
    :cond_8
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :cond_9
    move v3, v7

    .line 1515
    :goto_4
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    if-ne v4, v15, :cond_f

    .line 1516
    invoke-virtual {v0, v15}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1518
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    if-eq v4, v12, :cond_b

    if-eq v4, v11, :cond_b

    if-eq v4, v10, :cond_b

    if-eq v4, v9, :cond_b

    if-eq v4, v8, :cond_a

    const/16 v5, 0x22

    if-eq v4, v5, :cond_a

    const/16 v6, 0x23

    const/16 v5, 0x27

    if-eq v4, v6, :cond_c

    if-eq v4, v5, :cond_c

    if-eq v4, v13, :cond_c

    packed-switch v4, :pswitch_data_8

    packed-switch v4, :pswitch_data_9

    packed-switch v4, :pswitch_data_a

    .line 1548
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :cond_a
    const/16 v5, 0x27

    const/16 v6, 0x23

    goto :goto_5

    :cond_b
    const/16 v5, 0x27

    const/16 v6, 0x23

    .line 1521
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v4

    .line 1522
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1523
    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5, v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1552
    :cond_c
    :goto_5
    :pswitch_5
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mARG(Z)V

    .line 1554
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    if-eq v4, v12, :cond_e

    if-eq v4, v11, :cond_e

    if-eq v4, v10, :cond_e

    if-eq v4, v9, :cond_e

    if-ne v4, v14, :cond_d

    goto :goto_6

    .line 1568
    :cond_d
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1557
    :cond_e
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v4

    .line 1558
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1559
    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5, v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1572
    :goto_6
    invoke-virtual {v0, v14}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    add-int/lit8 v3, v3, 0x1

    goto/16 :goto_4

    :cond_f
    if-lt v3, v2, :cond_10

    :goto_7
    move v2, v7

    goto/16 :goto_9

    .line 1575
    :cond_10
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1595
    :cond_11
    :pswitch_6
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->generator:Lgroovyjarjarantlr/CodeGenerator;

    invoke-virtual {v3}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v3

    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->transInfo:Lgroovyjarjarantlr/ActionTransInfo;

    invoke-virtual {v4, v3, v5}, Lgroovyjarjarantlr/CodeGenerator;->mapTreeId(Ljava/lang/String;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object v3

    .line 1596
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    .line 1599
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_15:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_14

    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_16:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_14

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->transInfo:Lgroovyjarjarantlr/ActionTransInfo;

    if-eqz v3, :cond_14

    iget-object v3, v3, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    if-eqz v3, :cond_14

    .line 1601
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v12, :cond_13

    if-eq v3, v11, :cond_13

    if-eq v3, v10, :cond_13

    if-eq v3, v9, :cond_13

    const/16 v4, 0x3d

    if-ne v3, v4, :cond_12

    goto :goto_8

    .line 1613
    :cond_12
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1604
    :cond_13
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1617
    :goto_8
    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mVAR_ASSIGN(Z)V

    goto :goto_9

    .line 1619
    :cond_14
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_17:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_16

    :goto_9
    if-eqz p1, :cond_15

    const/16 v3, 0xc

    .line 1635
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object v3

    .line 1636
    new-instance v4, Ljava/lang/String;

    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v5

    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v6

    sub-int/2addr v6, v1

    invoke-direct {v4, v5, v1, v6}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {v3, v4}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_a

    :cond_15
    const/4 v3, 0x0

    .line 1638
    :goto_a
    iput-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return v2

    .line 1622
    :cond_16
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 1383
    :cond_17
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :pswitch_data_0
    .packed-switch 0x28
        :pswitch_1
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_0
        :pswitch_6
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x27
        :pswitch_2
        :pswitch_2
        :pswitch_4
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x30
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0x41
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
    .end packed-switch

    :pswitch_data_4
    .packed-switch 0x61
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
    .end packed-switch

    :pswitch_data_5
    .packed-switch 0x30
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
    .end packed-switch

    :pswitch_data_6
    .packed-switch 0x41
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
    .end packed-switch

    :pswitch_data_7
    .packed-switch 0x61
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_3
    .end packed-switch

    :pswitch_data_8
    .packed-switch 0x30
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
    .end packed-switch

    :pswitch_data_9
    .packed-switch 0x41
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
    .end packed-switch

    :pswitch_data_a
    .packed-switch 0x61
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
    .end packed-switch
.end method

.method protected final mINT(Z)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 1671
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    const/4 v3, 0x1

    .line 1679
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    const/16 v5, 0x30

    if-lt v4, v5, :cond_0

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    const/16 v5, 0x39

    if-gt v4, v5, :cond_0

    .line 1680
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mDIGIT(Z)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    if-lt v2, v3, :cond_2

    if-eqz p1, :cond_1

    const/16 p1, 0x1a

    .line 1690
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 1691
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    .line 1693
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 1683
    :cond_2
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method protected final mINT_OR_FLOAT(Z)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 2067
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    const/4 v3, 0x1

    .line 2075
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    const/16 v5, 0x39

    const/16 v6, 0x30

    const/4 v7, 0x2

    if-lt v4, v6, :cond_0

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    if-gt v4, v5, :cond_0

    sget-object v4, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_24:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v8

    invoke-virtual {v4, v8}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v4

    if-eqz v4, :cond_0

    .line 2076
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mDIGIT(Z)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    if-lt v2, v3, :cond_7

    .line 2086
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v4, 0x4c

    if-ne v2, v4, :cond_1

    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_25:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v8

    invoke-virtual {v2, v8}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 2087
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_2

    .line 2089
    :cond_1
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v4, 0x6c

    if-ne v2, v4, :cond_2

    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_25:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v8

    invoke-virtual {v2, v8}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 2090
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_2

    .line 2092
    :cond_2
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v4, 0x2e

    if-ne v2, v4, :cond_3

    .line 2093
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 2097
    :goto_1
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v6, :cond_4

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v5, :cond_4

    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_25:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v4

    invoke-virtual {v2, v4}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_4

    .line 2098
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mDIGIT(Z)V

    goto :goto_1

    .line 2107
    :cond_3
    sget-object v1, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_25:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v1

    if-eqz v1, :cond_6

    :cond_4
    :goto_2
    if-eqz p1, :cond_5

    const/16 p1, 0x1b

    .line 2115
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 2116
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_3

    :cond_5
    const/4 p1, 0x0

    .line 2118
    :goto_3
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 2110
    :cond_6
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 2079
    :cond_7
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method protected final mML_COMMENT(Z)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 2165
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const-string v1, "/*"

    .line 2169
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    :goto_0
    const/4 v1, 0x1

    .line 2174
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x2a

    const/4 v4, 0x2

    if-ne v2, v3, :cond_0

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x2f

    if-ne v2, v3, :cond_0

    goto/16 :goto_1

    .line 2175
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0xa

    const/16 v5, 0xd

    const/16 v6, 0xff

    const/4 v7, 0x3

    if-ne v2, v5, :cond_1

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v3, :cond_1

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v7, :cond_1

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v6, :cond_1

    .line 2176
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 2177
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 2178
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    goto :goto_0

    .line 2180
    :cond_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_2

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v7, :cond_2

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v6, :cond_2

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v7, :cond_2

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v6, :cond_2

    .line 2181
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 2182
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    goto :goto_0

    .line 2184
    :cond_2
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v3, :cond_3

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v7, :cond_3

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v6, :cond_3

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v7, :cond_3

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v6, :cond_3

    .line 2185
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 2186
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    goto/16 :goto_0

    .line 2188
    :cond_3
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v7, :cond_4

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-gt v1, v6, :cond_4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-lt v1, v7, :cond_4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-gt v1, v6, :cond_4

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-lt v1, v7, :cond_4

    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    if-gt v1, v6, :cond_4

    const v1, 0xffff

    .line 2189
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchNot(C)V

    goto/16 :goto_0

    :cond_4
    :goto_1
    const-string v1, "*/"

    .line 2197
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    if-eqz p1, :cond_5

    const/16 p1, 0x15

    .line 2199
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 2200
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_2

    :cond_5
    const/4 p1, 0x0

    .line 2202
    :goto_2
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void
.end method

.method protected final mSL_COMMENT(Z)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 2122
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const-string v1, "//"

    .line 2126
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    :goto_0
    const/4 v1, 0x1

    .line 2131
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/4 v3, 0x2

    const/16 v4, 0xd

    const/16 v5, 0xa

    if-eq v2, v5, :cond_1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_0

    goto :goto_1

    .line 2132
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/4 v6, 0x3

    if-lt v2, v6, :cond_1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v7, 0xff

    if-gt v2, v7, :cond_1

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v6, :cond_1

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v7, :cond_1

    const v1, 0xffff

    .line 2133
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchNot(C)V

    goto :goto_0

    .line 2142
    :cond_1
    :goto_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_2

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_2

    const-string v1, "\r\n"

    .line 2143
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    goto :goto_2

    .line 2145
    :cond_2
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_3

    .line 2146
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_2

    .line 2148
    :cond_3
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_5

    .line 2149
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 2156
    :goto_2
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    if-eqz p1, :cond_4

    const/16 p1, 0x14

    .line 2158
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 2159
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_3

    :cond_4
    const/4 p1, 0x0

    .line 2161
    :goto_3
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 2152
    :cond_5
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method protected final mSTRING(Z)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 602
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/16 v1, 0x22

    .line 606
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    :goto_0
    const/4 v2, 0x1

    .line 610
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x5c

    if-ne v3, v4, :cond_0

    const/4 v2, 0x0

    .line 611
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mESC(Z)V

    goto :goto_0

    .line 613
    :cond_0
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_7:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_1

    .line 614
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->matchNot(C)V

    goto :goto_0

    .line 622
    :cond_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    if-eqz p1, :cond_2

    const/16 p1, 0x17

    .line 624
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 625
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_2
    const/4 p1, 0x0

    .line 627
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void
.end method

.method protected final mSTUFF(Z)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 194
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 198
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0xa

    if-eq v2, v3, :cond_8

    const/16 v4, 0x22

    const/4 v5, 0x0

    if-eq v2, v4, :cond_7

    const/16 v4, 0x27

    if-eq v2, v4, :cond_6

    .line 216
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/4 v4, 0x2

    const/16 v6, 0x2f

    if-ne v2, v6, :cond_1

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v7, 0x2a

    if-eq v2, v7, :cond_0

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v6, :cond_1

    .line 217
    :cond_0
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mCOMMENT(Z)V

    goto/16 :goto_0

    .line 219
    :cond_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v5, 0xd

    if-ne v2, v5, :cond_2

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v3, :cond_2

    const-string v1, "\r\n"

    .line 220
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 221
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    goto :goto_0

    .line 223
    :cond_2
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v6, :cond_3

    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_1:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_3

    .line 224
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 226
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Lgroovyjarjarantlr/collections/impl/BitSet;)V

    goto :goto_0

    .line 229
    :cond_3
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_4

    .line 230
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 231
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    goto :goto_0

    .line 233
    :cond_4
    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_2:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_5

    .line 235
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Lgroovyjarjarantlr/collections/impl/BitSet;)V

    goto :goto_0

    .line 239
    :cond_5
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 206
    :cond_6
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mCHAR(Z)V

    goto :goto_0

    .line 201
    :cond_7
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mSTRING(Z)V

    goto :goto_0

    .line 211
    :cond_8
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 212
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    :goto_0
    if-eqz p1, :cond_9

    const/4 p1, 0x5

    .line 243
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 244
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_9
    const/4 p1, 0x0

    .line 246
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void
.end method

.method protected final mTEXT_ARG(Z)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 1210
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 1215
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x9

    const/16 v4, 0xd

    const/4 v5, 0x0

    if-eq v2, v3, :cond_0

    const/16 v3, 0xa

    if-eq v2, v3, :cond_0

    if-eq v2, v4, :cond_0

    const/16 v3, 0x20

    if-eq v2, v3, :cond_0

    const/16 v3, 0x22

    if-eq v2, v3, :cond_1

    const/16 v3, 0x24

    if-eq v2, v3, :cond_1

    const/16 v3, 0x27

    if-eq v2, v3, :cond_1

    const/16 v3, 0x2b

    if-eq v2, v3, :cond_1

    const/16 v3, 0x5f

    if-eq v2, v3, :cond_1

    packed-switch v2, :pswitch_data_0

    packed-switch v2, :pswitch_data_1

    packed-switch v2, :pswitch_data_2

    .line 1243
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 1218
    :cond_0
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    :cond_1
    :pswitch_0
    move v2, v5

    .line 1251
    :goto_0
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_11:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    invoke-virtual {v3, v6}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_4

    const/4 v3, 0x2

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    const/4 v7, 0x3

    if-lt v6, v7, :cond_4

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    const/16 v7, 0xff

    if-gt v6, v7, :cond_4

    .line 1252
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG_ELEMENT(Z)V

    .line 1254
    sget-object v6, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_4:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v7

    invoke-virtual {v6, v7}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v6

    if-eqz v6, :cond_2

    sget-object v6, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_12:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v6, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_2

    .line 1255
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    goto :goto_1

    .line 1257
    :cond_2
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_12:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    invoke-virtual {v3, v6}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_3

    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 1260
    :cond_3
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    :cond_4
    if-lt v2, v1, :cond_6

    if-eqz p1, :cond_5

    .line 1273
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 1274
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_2

    :cond_5
    const/4 p1, 0x0

    .line 1276
    :goto_2
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 1266
    :cond_6
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    nop

    :pswitch_data_0
    .packed-switch 0x30
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x41
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x61
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method protected final mTEXT_ARG_ELEMENT(Z)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 1825
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 1829
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x22

    const/4 v4, 0x0

    if-eq v2, v3, :cond_4

    const/16 v3, 0x24

    if-eq v2, v3, :cond_3

    const/16 v3, 0x27

    if-eq v2, v3, :cond_2

    const/16 v3, 0x2b

    if-eq v2, v3, :cond_1

    const/16 v3, 0x5f

    if-eq v2, v3, :cond_0

    packed-switch v2, :pswitch_data_0

    packed-switch v2, :pswitch_data_1

    packed-switch v2, :pswitch_data_2

    .line 1877
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 1862
    :pswitch_0
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mINT_OR_FLOAT(Z)V

    goto :goto_0

    .line 1845
    :cond_0
    :pswitch_1
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG_ID_ELEMENT(Z)V

    goto :goto_0

    .line 1872
    :cond_1
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_0

    .line 1855
    :cond_2
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mCHAR(Z)V

    goto :goto_0

    .line 1867
    :cond_3
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ITEM(Z)V

    goto :goto_0

    .line 1850
    :cond_4
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mSTRING(Z)V

    :goto_0
    if-eqz p1, :cond_5

    const/16 p1, 0xe

    .line 1881
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 1882
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_5
    const/4 p1, 0x0

    .line 1884
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    :pswitch_data_0
    .packed-switch 0x30
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x41
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x61
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch
.end method

.method protected final mTEXT_ARG_ID_ELEMENT(Z)V
    .locals 14
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 1888
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 1893
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mID(Z)V

    .line 1894
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 1896
    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_4:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    const/4 v4, 0x2

    const/4 v5, 0x0

    if-eqz v3, :cond_0

    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_21:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    invoke-virtual {v3, v6}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 1897
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    .line 1898
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1899
    iget-object v6, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    goto :goto_0

    .line 1901
    :cond_0
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_21:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    invoke-virtual {v3, v6}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_10

    .line 1909
    :goto_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v6, 0x9

    if-eq v3, v6, :cond_e

    const/16 v7, 0xa

    if-eq v3, v7, :cond_e

    const/16 v8, 0xd

    if-eq v3, v8, :cond_e

    const/16 v9, 0x20

    if-eq v3, v9, :cond_e

    const/16 v10, 0x22

    if-eq v3, v10, :cond_e

    const/16 v10, 0x24

    if-eq v3, v10, :cond_e

    const/16 v10, 0x2e

    if-eq v3, v10, :cond_d

    const/16 v10, 0x5d

    if-eq v3, v10, :cond_e

    const/16 v11, 0x5f

    if-eq v3, v11, :cond_e

    const/16 v11, 0x2b

    if-eq v3, v11, :cond_e

    const/16 v11, 0x2c

    if-eq v3, v11, :cond_e

    const/16 v12, 0xff

    const/4 v13, 0x3

    packed-switch v3, :pswitch_data_0

    packed-switch v3, :pswitch_data_1

    packed-switch v3, :pswitch_data_2

    packed-switch v3, :pswitch_data_3

    .line 2055
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    :pswitch_0
    move v2, v5

    .line 1979
    :goto_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v11, 0x5b

    if-ne v3, v11, :cond_5

    .line 1980
    invoke-virtual {p0, v11}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1982
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_4:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v11

    invoke-virtual {v3, v11}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_1

    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_23:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v11

    invoke-virtual {v3, v11}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-virtual {p0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v13, :cond_1

    invoke-virtual {p0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v12, :cond_1

    .line 1983
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    .line 1984
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1985
    iget-object v11, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v11, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    goto :goto_2

    .line 1987
    :cond_1
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_23:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v11

    invoke-virtual {v3, v11}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v13, :cond_4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v12, :cond_4

    invoke-virtual {p0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v13, :cond_4

    invoke-virtual {p0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v12, :cond_4

    .line 1994
    :goto_2
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG(Z)V

    .line 1996
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v6, :cond_3

    if-eq v3, v7, :cond_3

    if-eq v3, v8, :cond_3

    if-eq v3, v9, :cond_3

    if-ne v3, v10, :cond_2

    goto :goto_3

    .line 2010
    :cond_2
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 1999
    :cond_3
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    .line 2000
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 2001
    iget-object v11, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v11, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 2014
    :goto_3
    invoke-virtual {p0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    add-int/lit8 v2, v2, 0x1

    goto/16 :goto_1

    .line 1990
    :cond_4
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    :cond_5
    if-lt v2, v1, :cond_6

    goto/16 :goto_7

    .line 2017
    :cond_6
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    :pswitch_1
    const/16 v3, 0x28

    .line 1912
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1914
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_7

    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_22:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_7

    invoke-virtual {p0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v13, :cond_7

    invoke-virtual {p0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v12, :cond_7

    .line 1915
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    .line 1916
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1917
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    goto :goto_4

    .line 1919
    :cond_7
    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_22:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_c

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v13, :cond_c

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v12, :cond_c

    .line 1929
    :cond_8
    :goto_4
    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_23:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_9

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v13, :cond_9

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v12, :cond_9

    invoke-virtual {p0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-lt v2, v13, :cond_9

    invoke-virtual {p0, v13}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-gt v2, v12, :cond_9

    .line 1930
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG(Z)V

    .line 1934
    :goto_5
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v11, :cond_8

    .line 1935
    invoke-virtual {p0, v11}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1936
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG(Z)V

    goto :goto_5

    .line 1952
    :cond_9
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x29

    if-eq v2, v6, :cond_b

    if-eq v2, v7, :cond_b

    if-eq v2, v8, :cond_b

    if-eq v2, v9, :cond_b

    if-ne v2, v3, :cond_a

    goto :goto_6

    .line 1966
    :cond_a
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 1955
    :cond_b
    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    .line 1956
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 1957
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1970
    :goto_6
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_7

    .line 1922
    :cond_c
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 2027
    :cond_d
    invoke-virtual {p0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 2028
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG_ID_ELEMENT(Z)V

    :cond_e
    :goto_7
    :pswitch_2
    if-eqz p1, :cond_f

    const/16 p1, 0xf

    .line 2060
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 2061
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_8

    :cond_f
    const/4 p1, 0x0

    .line 2063
    :goto_8
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 1904
    :cond_10
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    nop

    :pswitch_data_0
    .packed-switch 0x27
        :pswitch_2
        :pswitch_1
        :pswitch_2
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x30
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x41
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_0
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0x61
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
        :pswitch_2
    .end packed-switch
.end method

.method protected final mTEXT_ITEM(Z)V
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    move-object/from16 v0, p0

    .line 338
    iget-object v1, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    const/4 v2, 0x1

    .line 348
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0xff

    const-string v5, ": unknown rule or bad lookahead computation"

    const/16 v6, 0x46

    const-string v8, ")"

    const/16 v9, 0x24

    const/16 v12, 0x20

    const/16 v13, 0xd

    const/16 v14, 0xa

    const/16 v15, 0x9

    const/4 v7, 0x3

    const/4 v10, 0x2

    const/16 v11, 0x28

    if-ne v3, v9, :cond_5

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v6, :cond_5

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v6, 0x4f

    if-ne v3, v6, :cond_5

    const-string v3, "$FOLLOW"

    .line 349
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 351
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_5:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    invoke-virtual {v3, v6}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_2

    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_6:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    invoke-virtual {v3, v6}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v7, :cond_2

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v4, :cond_2

    .line 353
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v15, :cond_1

    if-eq v3, v14, :cond_1

    if-eq v3, v13, :cond_1

    if-eq v3, v12, :cond_1

    if-ne v3, v11, :cond_0

    goto :goto_0

    .line 365
    :cond_0
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :cond_1
    const/4 v3, 0x0

    .line 356
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 369
    :goto_0
    invoke-virtual {v0, v11}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 370
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG(Z)V

    .line 371
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    const/16 v4, 0x29

    .line 372
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_1

    :cond_2
    const/4 v3, 0x0

    .line 379
    :goto_1
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {v4}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object v4

    if-eqz v3, :cond_3

    .line 381
    invoke-virtual {v3}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v4

    .line 383
    :cond_3
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->generator:Lgroovyjarjarantlr/CodeGenerator;

    invoke-virtual {v3, v4, v2}, Lgroovyjarjarantlr/CodeGenerator;->getFOLLOWBitSet(Ljava/lang/String;I)Ljava/lang/String;

    move-result-object v2

    if-nez v2, :cond_4

    .line 386
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "$FOLLOW("

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->reportError(Ljava/lang/String;)V

    goto/16 :goto_8

    .line 390
    :cond_4
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto/16 :goto_8

    .line 394
    :cond_5
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v9, :cond_b

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v6, 0x46

    if-ne v3, v6, :cond_b

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v6, 0x49

    if-ne v3, v6, :cond_b

    const-string v3, "$FIRST"

    .line 395
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 397
    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_5:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    invoke-virtual {v3, v6}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_8

    sget-object v3, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_6:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    invoke-virtual {v3, v6}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_8

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-lt v3, v7, :cond_8

    invoke-virtual {v0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-gt v3, v4, :cond_8

    .line 399
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v15, :cond_7

    if-eq v3, v14, :cond_7

    if-eq v3, v13, :cond_7

    if-eq v3, v12, :cond_7

    if-ne v3, v11, :cond_6

    goto :goto_2

    .line 411
    :cond_6
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :cond_7
    const/4 v3, 0x0

    .line 402
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 415
    :goto_2
    invoke-virtual {v0, v11}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 416
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG(Z)V

    .line 417
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    const/16 v4, 0x29

    .line 418
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_3

    :cond_8
    const/4 v3, 0x0

    .line 425
    :goto_3
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {v4}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object v4

    if-eqz v3, :cond_9

    .line 427
    invoke-virtual {v3}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v4

    .line 429
    :cond_9
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->generator:Lgroovyjarjarantlr/CodeGenerator;

    invoke-virtual {v3, v4, v2}, Lgroovyjarjarantlr/CodeGenerator;->getFIRSTBitSet(Ljava/lang/String;I)Ljava/lang/String;

    move-result-object v2

    if-nez v2, :cond_a

    .line 432
    new-instance v2, Ljava/lang/StringBuffer;

    invoke-direct {v2}, Ljava/lang/StringBuffer;-><init>()V

    const-string v3, "$FIRST("

    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->reportError(Ljava/lang/String;)V

    goto/16 :goto_8

    .line 436
    :cond_a
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto/16 :goto_8

    .line 440
    :cond_b
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v9, :cond_e

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x61

    if-ne v3, v4, :cond_e

    const-string v3, "$append"

    .line 441
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 443
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v15, :cond_d

    if-eq v3, v14, :cond_d

    if-eq v3, v13, :cond_d

    if-eq v3, v12, :cond_d

    if-ne v3, v11, :cond_c

    goto :goto_4

    .line 455
    :cond_c
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :cond_d
    const/4 v3, 0x0

    .line 446
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 459
    :goto_4
    invoke-virtual {v0, v11}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 460
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG(Z)V

    .line 461
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    const/16 v3, 0x29

    .line 462
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 464
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "text.append("

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v2}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    .line 465
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto/16 :goto_8

    .line 468
    :cond_e
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v9, :cond_18

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x73

    if-ne v3, v4, :cond_18

    const-string v3, "$set"

    .line 469
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 471
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x54

    if-ne v3, v4, :cond_11

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v5, 0x65

    if-ne v3, v5, :cond_11

    const-string v3, "Text"

    .line 472
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 474
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v15, :cond_10

    if-eq v3, v14, :cond_10

    if-eq v3, v13, :cond_10

    if-eq v3, v12, :cond_10

    if-ne v3, v11, :cond_f

    goto :goto_5

    .line 486
    :cond_f
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :cond_10
    const/4 v3, 0x0

    .line 477
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 490
    :goto_5
    invoke-virtual {v0, v11}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 491
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG(Z)V

    .line 492
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    const/16 v3, 0x29

    .line 493
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 496
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "text.setLength(_begin); text.append("

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v2}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2, v8}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    .line 497
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto/16 :goto_8

    .line 500
    :cond_11
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v4, :cond_14

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v5, 0x6f

    if-ne v3, v5, :cond_14

    const-string v3, "Token"

    .line 501
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 503
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v15, :cond_13

    if-eq v3, v14, :cond_13

    if-eq v3, v13, :cond_13

    if-eq v3, v12, :cond_13

    if-ne v3, v11, :cond_12

    goto :goto_6

    .line 515
    :cond_12
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :cond_13
    const/4 v3, 0x0

    .line 506
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 519
    :goto_6
    invoke-virtual {v0, v11}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 520
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG(Z)V

    .line 521
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    const/16 v3, 0x29

    .line 522
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 524
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "_token = "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v2}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    .line 525
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto/16 :goto_8

    .line 528
    :cond_14
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v4, :cond_17

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x79

    if-ne v3, v4, :cond_17

    const-string v3, "Type"

    .line 529
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 531
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v15, :cond_16

    if-eq v3, v14, :cond_16

    if-eq v3, v13, :cond_16

    if-eq v3, v12, :cond_16

    if-ne v3, v11, :cond_15

    goto :goto_7

    .line 543
    :cond_15
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    :cond_16
    const/4 v3, 0x0

    .line 534
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 547
    :goto_7
    invoke-virtual {v0, v11}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 548
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTEXT_ARG(Z)V

    .line 549
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    const/16 v3, 0x29

    .line 550
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 552
    new-instance v3, Ljava/lang/StringBuffer;

    invoke-direct {v3}, Ljava/lang/StringBuffer;-><init>()V

    const-string v4, "_ttype = "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v3

    invoke-virtual {v2}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v2

    .line 553
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto :goto_8

    .line 557
    :cond_17
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 562
    :cond_18
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v9, :cond_1a

    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0x67

    if-ne v3, v4, :cond_1a

    const-string v2, "$getText"

    .line 563
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 565
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    const-string v3, "new String(text.getBuffer(),_begin,text.length()-_begin)"

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    :goto_8
    if-eqz p1, :cond_19

    const/4 v2, 0x7

    .line 573
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object v7

    .line 574
    new-instance v2, Ljava/lang/String;

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v3

    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v4

    sub-int/2addr v4, v1

    invoke-direct {v2, v3, v1, v4}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {v7, v2}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_9

    :cond_19
    const/4 v7, 0x0

    .line 576
    :goto_9
    iput-object v7, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 569
    :cond_1a
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1
.end method

.method protected final mTREE(Z)V
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    move-object/from16 v0, p0

    .line 657
    iget-object v1, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    .line 665
    new-instance v2, Lgroovyjarjarantlr/collections/impl/Vector;

    const/16 v3, 0xa

    invoke-direct {v2, v3}, Lgroovyjarjarantlr/collections/impl/Vector;-><init>(I)V

    .line 668
    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v4

    const/16 v5, 0x28

    .line 669
    invoke-virtual {v0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 670
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6, v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    const/4 v4, 0x1

    .line 672
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    const/16 v7, 0x23

    const/16 v8, 0x22

    const/16 v9, 0x5f

    const/4 v10, 0x0

    const/16 v11, 0x20

    const/16 v12, 0xd

    const/16 v13, 0x9

    if-eq v6, v13, :cond_0

    if-eq v6, v3, :cond_0

    if-eq v6, v12, :cond_0

    if-eq v6, v11, :cond_0

    if-eq v6, v5, :cond_1

    if-eq v6, v9, :cond_1

    if-eq v6, v8, :cond_1

    if-eq v6, v7, :cond_1

    packed-switch v6, :pswitch_data_0

    packed-switch v6, :pswitch_data_1

    .line 700
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 675
    :cond_0
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v6

    .line 676
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 677
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 704
    :cond_1
    :pswitch_0
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v6

    .line 705
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTREE_ELEMENT(Z)V

    .line 706
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 707
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 708
    invoke-virtual {v6}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v2, v6}, Lgroovyjarjarantlr/collections/impl/Vector;->appendElement(Ljava/lang/Object;)V

    .line 710
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    const/16 v14, 0x29

    const/16 v15, 0x2c

    if-eq v6, v13, :cond_3

    if-eq v6, v3, :cond_3

    if-eq v6, v12, :cond_3

    if-eq v6, v11, :cond_3

    if-eq v6, v14, :cond_4

    if-ne v6, v15, :cond_2

    goto :goto_0

    .line 724
    :cond_2
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 713
    :cond_3
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v6

    .line 714
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 715
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 731
    :cond_4
    :goto_0
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    if-ne v6, v15, :cond_9

    .line 732
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v6

    .line 733
    invoke-virtual {v0, v15}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 734
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 736
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    if-eq v6, v13, :cond_5

    if-eq v6, v3, :cond_5

    if-eq v6, v12, :cond_5

    if-eq v6, v11, :cond_5

    if-eq v6, v5, :cond_6

    if-eq v6, v9, :cond_6

    if-eq v6, v8, :cond_6

    if-eq v6, v7, :cond_6

    packed-switch v6, :pswitch_data_2

    packed-switch v6, :pswitch_data_3

    .line 764
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 739
    :cond_5
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v6

    .line 740
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 741
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 768
    :cond_6
    :pswitch_1
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v6

    .line 769
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTREE_ELEMENT(Z)V

    .line 770
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 771
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 772
    invoke-virtual {v6}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v2, v6}, Lgroovyjarjarantlr/collections/impl/Vector;->appendElement(Ljava/lang/Object;)V

    .line 774
    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v6

    if-eq v6, v13, :cond_8

    if-eq v6, v3, :cond_8

    if-eq v6, v12, :cond_8

    if-eq v6, v11, :cond_8

    const/16 v14, 0x29

    if-eq v6, v14, :cond_4

    if-ne v6, v15, :cond_7

    goto :goto_0

    .line 788
    :cond_7
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {v0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v3

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v4

    invoke-virtual/range {p0 .. p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v5

    invoke-direct {v1, v2, v3, v4, v5}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1

    .line 777
    :cond_8
    iget-object v6, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v6

    .line 778
    invoke-virtual {v0, v10}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mWS(Z)V

    .line 779
    iget-object v14, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v14, v6}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    goto/16 :goto_0

    .line 799
    :cond_9
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->generator:Lgroovyjarjarantlr/CodeGenerator;

    invoke-virtual {v4, v2}, Lgroovyjarjarantlr/CodeGenerator;->getASTCreateString(Lgroovyjarjarantlr/collections/impl/Vector;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    .line 800
    iget-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    const/16 v3, 0x29

    .line 801
    invoke-virtual {v0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 802
    iget-object v3, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    if-eqz p1, :cond_a

    const/16 v2, 0x8

    .line 804
    invoke-virtual {v0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object v2

    .line 805
    new-instance v3, Ljava/lang/String;

    iget-object v4, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v4}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v4

    iget-object v5, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v5}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v5

    sub-int/2addr v5, v1

    invoke-direct {v3, v4, v1, v5}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_a
    const/4 v2, 0x0

    .line 807
    :goto_1
    iput-object v2, v0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x41
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x61
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x41
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0x61
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch
.end method

.method protected final mTREE_ELEMENT(Z)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 1280
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 1286
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x22

    const/4 v4, 0x0

    const/4 v5, 0x0

    if-eq v2, v3, :cond_6

    const/16 v3, 0x28

    if-eq v2, v3, :cond_5

    const/16 v6, 0x5f

    if-eq v2, v6, :cond_4

    packed-switch v2, :pswitch_data_0

    packed-switch v2, :pswitch_data_1

    .line 1321
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/4 v6, 0x2

    const/16 v7, 0x23

    if-ne v2, v7, :cond_0

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v3, :cond_0

    .line 1322
    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    .line 1323
    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1324
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1325
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTREE(Z)V

    goto/16 :goto_0

    .line 1327
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v7, :cond_1

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x5b

    if-ne v2, v3, :cond_1

    .line 1328
    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    .line 1329
    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1330
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1331
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mAST_CONSTRUCTOR(Z)V

    goto/16 :goto_0

    .line 1333
    :cond_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v7, :cond_2

    sget-object v2, Lgroovyjarjarantlr/actions/java/ActionLexer;->_tokenSet_3:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 1334
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    .line 1335
    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 1336
    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 1337
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mID_ELEMENT(Z)Z

    move-result v1

    .line 1338
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    if-nez v1, :cond_7

    .line 1342
    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->generator:Lgroovyjarjarantlr/CodeGenerator;

    invoke-virtual {v2}, Lgroovyjarjarantlr/Token;->getText()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2, v4}, Lgroovyjarjarantlr/CodeGenerator;->mapTreeId(Ljava/lang/String;Lgroovyjarjarantlr/ActionTransInfo;)Ljava/lang/String;

    move-result-object v1

    .line 1343
    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto :goto_0

    .line 1347
    :cond_2
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v7, :cond_3

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v2

    if-ne v2, v7, :cond_3

    const-string v1, "##"

    .line 1348
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(Ljava/lang/String;)V

    .line 1349
    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->currentRule:Lgroovyjarjarantlr/RuleBlock;

    invoke-virtual {v2}, Lgroovyjarjarantlr/RuleBlock;->getRuleName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    const-string v2, "_AST"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto :goto_0

    .line 1352
    :cond_3
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1

    .line 1294
    :pswitch_0
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mAST_CONSTRUCTOR(Z)V

    goto :goto_0

    .line 1312
    :cond_4
    :pswitch_1
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mID_ELEMENT(Z)Z

    goto :goto_0

    .line 1289
    :cond_5
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mTREE(Z)V

    goto :goto_0

    .line 1317
    :cond_6
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mSTRING(Z)V

    :cond_7
    :goto_0
    if-eqz p1, :cond_8

    const/16 p1, 0x9

    .line 1356
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object v4

    .line 1357
    new-instance p1, Ljava/lang/String;

    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v2

    sub-int/2addr v2, v0

    invoke-direct {p1, v1, v0, v2}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {v4, p1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    .line 1359
    :cond_8
    iput-object v4, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x41
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x61
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
    .end packed-switch
.end method

.method protected final mVAR_ASSIGN(Z)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 953
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/16 v1, 0x3d

    .line 957
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    const/4 v2, 0x1

    .line 961
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-eq v3, v1, :cond_0

    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->transInfo:Lgroovyjarjarantlr/ActionTransInfo;

    if-eqz v1, :cond_0

    iget-object v1, v1, Lgroovyjarjarantlr/ActionTransInfo;->refRuleRoot:Ljava/lang/String;

    if-eqz v1, :cond_0

    .line 962
    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->transInfo:Lgroovyjarjarantlr/ActionTransInfo;

    iput-boolean v2, v1, Lgroovyjarjarantlr/ActionTransInfo;->assignToRoot:Z

    :cond_0
    if-eqz p1, :cond_1

    const/16 p1, 0x12

    .line 966
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 967
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    .line 969
    :goto_0
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void
.end method

.method protected final mWS(Z)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 911
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x0

    :goto_0
    const/4 v2, 0x1

    .line 919
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v4, 0xa

    const/16 v5, 0xd

    if-ne v3, v5, :cond_0

    const/4 v3, 0x2

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v4, :cond_0

    .line 920
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 921
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 922
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    goto :goto_1

    .line 924
    :cond_0
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v6, 0x20

    if-ne v3, v6, :cond_1

    .line 925
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_1

    .line 927
    :cond_1
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    const/16 v6, 0x9

    if-ne v3, v6, :cond_2

    .line 928
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    goto :goto_1

    .line 930
    :cond_2
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v5, :cond_3

    .line 931
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 932
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    goto :goto_1

    .line 934
    :cond_3
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v3

    if-ne v3, v4, :cond_4

    .line 935
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/java/ActionLexer;->match(C)V

    .line 936
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->newline()V

    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_4
    if-lt v1, v2, :cond_6

    if-eqz p1, :cond_5

    const/16 p1, 0x1c

    .line 946
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 947
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_2

    :cond_5
    const/4 p1, 0x0

    .line 949
    :goto_2
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 939
    :cond_6
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method public nextToken()Lgroovyjarjarantlr/Token;
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 123
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->resetText()V

    const/4 v0, 0x1

    .line 126
    :try_start_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    const/4 v2, 0x3

    if-lt v1, v2, :cond_0

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    const/16 v2, 0xff

    if-gt v1, v2, :cond_0

    .line 127
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->mACTION(Z)V

    .line 128
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    goto :goto_1

    .line 131
    :cond_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v1

    const v2, 0xffff

    if-ne v1, v2, :cond_2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->uponEOF()V

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object v0

    iput-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 135
    :goto_1
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    if-nez v0, :cond_1

    goto :goto_0

    .line 136
    :cond_1
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    invoke-virtual {v0}, Lgroovyjarjarantlr/Token;->getType()I

    move-result v0

    .line 137
    iget-object v1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/Token;->setType(I)V

    .line 138
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-object v0

    .line 132
    :cond_2
    new-instance v1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v3

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v4

    invoke-direct {v1, v0, v2, v3, v4}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw v1
    :try_end_0
    .catch Lgroovyjarjarantlr/RecognitionException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Lgroovyjarjarantlr/CharStreamException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    move-exception v0

    goto :goto_2

    :catch_1
    move-exception v0

    .line 141
    :try_start_1
    new-instance v1, Lgroovyjarjarantlr/TokenStreamRecognitionException;

    invoke-direct {v1, v0}, Lgroovyjarjarantlr/TokenStreamRecognitionException;-><init>(Lgroovyjarjarantlr/RecognitionException;)V

    throw v1
    :try_end_1
    .catch Lgroovyjarjarantlr/CharStreamException; {:try_start_1 .. :try_end_1} :catch_0

    .line 145
    :goto_2
    instance-of v1, v0, Lgroovyjarjarantlr/CharStreamIOException;

    if-eqz v1, :cond_3

    .line 146
    new-instance v1, Lgroovyjarjarantlr/TokenStreamIOException;

    check-cast v0, Lgroovyjarjarantlr/CharStreamIOException;

    iget-object v0, v0, Lgroovyjarjarantlr/CharStreamIOException;->io:Ljava/io/IOException;

    invoke-direct {v1, v0}, Lgroovyjarjarantlr/TokenStreamIOException;-><init>(Ljava/io/IOException;)V

    throw v1

    .line 149
    :cond_3
    new-instance v1, Lgroovyjarjarantlr/TokenStreamException;

    invoke-virtual {v0}, Lgroovyjarjarantlr/CharStreamException;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v1, v0}, Lgroovyjarjarantlr/TokenStreamException;-><init>(Ljava/lang/String;)V

    throw v1
.end method

.method public reportError(Lgroovyjarjarantlr/RecognitionException;)V
    .locals 4

    .line 84
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "Syntax error in action: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-virtual {v0, p1, v1, v2, v3}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;Ljava/lang/String;II)V

    return-void
.end method

.method public reportError(Ljava/lang/String;)V
    .locals 4

    .line 89
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-virtual {v0, p1, v1, v2, v3}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;Ljava/lang/String;II)V

    return-void
.end method

.method public reportWarning(Ljava/lang/String;)V
    .locals 4

    .line 94
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    .line 95
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;)V

    goto :goto_0

    .line 98
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/java/ActionLexer;->getColumn()I

    move-result v3

    invoke-virtual {v0, p1, v1, v2, v3}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    :goto_0
    return-void
.end method

.method public setLineOffset(I)V
    .locals 0

    .line 75
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/java/ActionLexer;->setLine(I)V

    return-void
.end method

.method public setTool(Lgroovyjarjarantlr/Tool;)V
    .locals 0

    .line 79
    iput-object p1, p0, Lgroovyjarjarantlr/actions/java/ActionLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    return-void
.end method
