.class public interface abstract Lgroovyjarjarantlr/actions/python/CodeLexerTokenTypes;
.super Ljava/lang/Object;
.source "CodeLexerTokenTypes.java"


# static fields
.field public static final ACTION:I = 0x4

.field public static final COMMENT:I = 0x6

.field public static final EOF:I = 0x1

.field public static final IGNWS:I = 0x8

.field public static final ML_COMMENT:I = 0x9

.field public static final NULL_TREE_LOOKAHEAD:I = 0x3

.field public static final SL_COMMENT:I = 0x7

.field public static final STUFF:I = 0x5
