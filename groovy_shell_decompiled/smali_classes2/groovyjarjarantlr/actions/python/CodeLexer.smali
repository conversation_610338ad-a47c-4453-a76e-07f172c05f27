.class public Lgroovyjarjarantlr/actions/python/CodeLexer;
.super Lgroovyjarjarantlr/CharScanner;
.source "CodeLexer.java"

# interfaces
.implements Lgroovyjarjarantlr/actions/python/CodeLexerTokenTypes;
.implements Lgroovyjarjarantlr/TokenStream;


# static fields
.field public static final _tokenSet_0:Lgroovyjarjarantlr/collections/impl/BitSet;

.field public static final _tokenSet_1:Lgroovyjarjarantlr/collections/impl/BitSet;


# instance fields
.field private antlrTool:Lgroovyjarjarantlr/Tool;

.field protected lineOffset:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 374
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mk_tokenSet_0()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_tokenSet_0:Lgroovyjarjarantlr/collections/impl/BitSet;

    .line 381
    new-instance v0, Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-static {}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mk_tokenSet_1()[J

    move-result-object v1

    invoke-direct {v0, v1}, Lgroovyjarjarantlr/collections/impl/BitSet;-><init>([J)V

    sput-object v0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_tokenSet_1:Lgroovyjarjarantlr/collections/impl/BitSet;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr/InputBuffer;)V
    .locals 1

    .line 84
    new-instance v0, Lgroovyjarjarantlr/LexerSharedInputState;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr/LexerSharedInputState;-><init>(Lgroovyjarjarantlr/InputBuffer;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr/actions/python/CodeLexer;-><init>(Lgroovyjarjarantlr/LexerSharedInputState;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarantlr/LexerSharedInputState;)V
    .locals 0

    .line 87
    invoke-direct {p0, p1}, Lgroovyjarjarantlr/CharScanner;-><init>(Lgroovyjarjarantlr/LexerSharedInputState;)V

    const/4 p1, 0x0

    .line 36
    iput p1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->lineOffset:I

    const/4 p1, 0x1

    .line 88
    iput-boolean p1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->caseSensitiveLiterals:Z

    .line 89
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->setCaseSensitive(Z)V

    .line 90
    new-instance p1, Ljava/util/Hashtable;

    invoke-direct {p1}, Ljava/util/Hashtable;-><init>()V

    iput-object p1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->literals:Ljava/util/Hashtable;

    return-void
.end method

.method public constructor <init>(Ljava/io/InputStream;)V
    .locals 1

    .line 78
    new-instance v0, Lgroovyjarjarantlr/ByteBuffer;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr/ByteBuffer;-><init>(Ljava/io/InputStream;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr/actions/python/CodeLexer;-><init>(Lgroovyjarjarantlr/InputBuffer;)V

    return-void
.end method

.method public constructor <init>(Ljava/io/Reader;)V
    .locals 1

    .line 81
    new-instance v0, Lgroovyjarjarantlr/CharBuffer;

    invoke-direct {v0, p1}, Lgroovyjarjarantlr/CharBuffer;-><init>(Ljava/io/Reader;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr/actions/python/CodeLexer;-><init>(Lgroovyjarjarantlr/InputBuffer;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;ILgroovyjarjarantlr/Tool;)V
    .locals 1

    .line 46
    new-instance v0, Ljava/io/StringReader;

    invoke-direct {v0, p1}, Ljava/io/StringReader;-><init>(Ljava/lang/String;)V

    invoke-direct {p0, v0}, Lgroovyjarjarantlr/actions/python/CodeLexer;-><init>(Ljava/io/Reader;)V

    .line 47
    invoke-virtual {p0, p3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->setLine(I)V

    .line 48
    invoke-virtual {p0, p2}, Lgroovyjarjarantlr/actions/python/CodeLexer;->setFilename(Ljava/lang/String;)V

    .line 49
    iput-object p4, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    return-void
.end method

.method private static final mk_tokenSet_0()[J
    .locals 4

    const/16 v0, 0x8

    new-array v0, v0, [J

    const/4 v1, 0x0

    const-wide v2, -0x840000000008L

    aput-wide v2, v0, v1

    const/4 v1, 0x1

    :goto_0
    const/4 v2, 0x3

    if-gt v1, v2, :cond_0

    const-wide/16 v2, -0x1

    .line 371
    aput-wide v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static final mk_tokenSet_1()[J
    .locals 4

    const/16 v0, 0x8

    new-array v0, v0, [J

    const/4 v1, 0x0

    const-wide v2, -0x800000002408L

    aput-wide v2, v0, v1

    const/4 v1, 0x1

    :goto_0
    const/4 v2, 0x3

    if-gt v1, v2, :cond_0

    const-wide/16 v2, -0x1

    .line 378
    aput-wide v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method


# virtual methods
.method public final mACTION(Z)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 128
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    :goto_0
    const/4 v1, 0x1

    .line 135
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/4 v3, 0x3

    if-lt v2, v3, :cond_0

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v1

    const/16 v2, 0xff

    if-gt v1, v2, :cond_0

    const/4 v1, 0x0

    .line 136
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mSTUFF(Z)V

    goto :goto_0

    :cond_0
    if-eqz p1, :cond_1

    const/4 p1, 0x4

    .line 145
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 146
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    .line 148
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void
.end method

.method protected final mCOMMENT(Z)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 194
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 198
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/4 v3, 0x0

    const/4 v4, 0x2

    const/16 v5, 0x2f

    if-ne v2, v5, :cond_0

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_0

    .line 199
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mSL_COMMENT(Z)V

    goto :goto_0

    .line 201
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_2

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/16 v4, 0x2a

    if-ne v2, v4, :cond_2

    .line 202
    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mML_COMMENT(Z)V

    :goto_0
    if-eqz p1, :cond_1

    const/4 p1, 0x6

    .line 209
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 210
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    .line 212
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 205
    :cond_2
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method protected final mIGNWS(Z)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 341
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    :goto_0
    const/4 v1, 0x1

    .line 348
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/16 v3, 0xff

    const/4 v4, 0x3

    const/16 v5, 0x20

    const/4 v6, 0x2

    if-ne v2, v5, :cond_0

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-lt v2, v4, :cond_0

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-gt v2, v3, :cond_0

    .line 349
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    goto :goto_0

    .line 351
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v1

    const/16 v2, 0x9

    if-ne v1, v2, :cond_1

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v1

    if-lt v1, v4, :cond_1

    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v1

    if-gt v1, v3, :cond_1

    .line 352
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    goto :goto_0

    :cond_1
    if-eqz p1, :cond_2

    const/16 p1, 0x8

    .line 361
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 362
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_2
    const/4 p1, 0x0

    .line 364
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void
.end method

.method protected final mML_COMMENT(Z)V
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 267
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    .line 274
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    const-string v2, "/*"

    .line 275
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(Ljava/lang/String;)V

    .line 276
    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 279
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    const-string v2, "#"

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    :goto_0
    const/4 v1, 0x1

    .line 285
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x2a

    const/4 v4, 0x2

    if-ne v2, v3, :cond_0

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/16 v3, 0x2f

    if-ne v2, v3, :cond_0

    goto/16 :goto_1

    .line 286
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const-string v3, "# "

    const/4 v5, 0x0

    const/16 v6, 0xa

    const/16 v7, 0xd

    if-ne v2, v7, :cond_1

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v6, :cond_1

    .line 287
    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    .line 288
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    .line 289
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    .line 290
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mIGNWS(Z)V

    .line 291
    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 293
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->newline()V

    .line 294
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto :goto_0

    .line 297
    :cond_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/16 v8, 0xff

    const/4 v9, 0x3

    if-ne v2, v7, :cond_2

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-lt v2, v9, :cond_2

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-gt v2, v8, :cond_2

    .line 298
    invoke-virtual {p0, v7}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    .line 299
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    .line 300
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mIGNWS(Z)V

    .line 301
    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 303
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->newline()V

    .line 304
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto :goto_0

    .line 307
    :cond_2
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v6, :cond_3

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-lt v2, v9, :cond_3

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-gt v2, v8, :cond_3

    .line 308
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    .line 309
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    .line 310
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mIGNWS(Z)V

    .line 311
    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 313
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->newline()V

    .line 314
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1, v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 317
    :cond_3
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-lt v2, v9, :cond_4

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v1

    if-gt v1, v8, :cond_4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v1

    if-lt v1, v9, :cond_4

    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v1

    if-gt v1, v8, :cond_4

    const v1, 0xffff

    .line 318
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->matchNot(C)V

    goto/16 :goto_0

    .line 328
    :cond_4
    :goto_1
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    const-string v2, "\n"

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    .line 330
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    const-string v2, "*/"

    .line 331
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(Ljava/lang/String;)V

    .line 332
    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    if-eqz p1, :cond_5

    const/16 p1, 0x9

    .line 334
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 335
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_2

    :cond_5
    const/4 p1, 0x0

    .line 337
    :goto_2
    iput-object p1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void
.end method

.method protected final mSL_COMMENT(Z)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 216
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    .line 220
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v1

    const-string v2, "//"

    .line 221
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(Ljava/lang/String;)V

    .line 222
    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2, v1}, Lgroovyjarjarantlr/ANTLRStringBuffer;->setLength(I)V

    .line 225
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    const-string v2, "#"

    invoke-virtual {v1, v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->append(Ljava/lang/String;)V

    :goto_0
    const/4 v1, 0x1

    .line 231
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/4 v3, 0x2

    const/16 v4, 0xd

    const/16 v5, 0xa

    if-eq v2, v5, :cond_1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_0

    goto :goto_1

    .line 232
    :cond_0
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/4 v6, 0x3

    if-lt v2, v6, :cond_1

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/16 v7, 0xff

    if-gt v2, v7, :cond_1

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-lt v2, v6, :cond_1

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-gt v2, v7, :cond_1

    const v1, 0xffff

    .line 233
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->matchNot(C)V

    goto :goto_0

    .line 242
    :cond_1
    :goto_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_2

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_2

    const-string v1, "\r\n"

    .line 243
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(Ljava/lang/String;)V

    goto :goto_2

    .line 245
    :cond_2
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_3

    .line 246
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    goto :goto_2

    .line 248
    :cond_3
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_5

    .line 249
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    .line 257
    :goto_2
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->newline()V

    if-eqz p1, :cond_4

    const/4 p1, 0x7

    .line 260
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 261
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_3

    :cond_4
    const/4 p1, 0x0

    .line 263
    :goto_3
    iput-object p1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 252
    :cond_5
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method protected final mSTUFF(Z)V
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/RecognitionException;,
            Lgroovyjarjarantlr/CharStreamException;,
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 152
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v0}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v0

    const/4 v1, 0x1

    .line 156
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/4 v3, 0x2

    const/16 v4, 0x2f

    if-ne v2, v4, :cond_1

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/16 v5, 0x2a

    if-eq v2, v5, :cond_0

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_1

    :cond_0
    const/4 v1, 0x0

    .line 157
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mCOMMENT(Z)V

    goto :goto_0

    .line 159
    :cond_1
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    const/16 v5, 0xa

    const/16 v6, 0xd

    if-ne v2, v6, :cond_2

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_2

    const-string v1, "\r\n"

    .line 160
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(Ljava/lang/String;)V

    .line 161
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->newline()V

    goto :goto_0

    .line 163
    :cond_2
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v4, :cond_3

    sget-object v2, Lgroovyjarjarantlr/actions/python/CodeLexer;->_tokenSet_0:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v3}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_3

    .line 164
    invoke-virtual {p0, v4}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    .line 166
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(Lgroovyjarjarantlr/collections/impl/BitSet;)V

    goto :goto_0

    .line 169
    :cond_3
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v6, :cond_4

    .line 170
    invoke-virtual {p0, v6}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    .line 171
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->newline()V

    goto :goto_0

    .line 173
    :cond_4
    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v2

    if-ne v2, v5, :cond_5

    .line 174
    invoke-virtual {p0, v5}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(C)V

    .line 175
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->newline()V

    goto :goto_0

    .line 177
    :cond_5
    sget-object v2, Lgroovyjarjarantlr/actions/python/CodeLexer;->_tokenSet_1:Lgroovyjarjarantlr/collections/impl/BitSet;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v3

    invoke-virtual {v2, v3}, Lgroovyjarjarantlr/collections/impl/BitSet;->member(I)Z

    move-result v3

    if-eqz v3, :cond_7

    .line 179
    invoke-virtual {p0, v2}, Lgroovyjarjarantlr/actions/python/CodeLexer;->match(Lgroovyjarjarantlr/collections/impl/BitSet;)V

    :goto_0
    if-eqz p1, :cond_6

    const/4 p1, 0x5

    .line 187
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->makeToken(I)Lgroovyjarjarantlr/Token;

    move-result-object p1

    .line 188
    new-instance v1, Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v2}, Lgroovyjarjarantlr/ANTLRStringBuffer;->getBuffer()[C

    move-result-object v2

    iget-object v3, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->text:Lgroovyjarjarantlr/ANTLRStringBuffer;

    invoke-virtual {v3}, Lgroovyjarjarantlr/ANTLRStringBuffer;->length()I

    move-result v3

    sub-int/2addr v3, v0

    invoke-direct {v1, v2, v0, v3}, Ljava/lang/String;-><init>([CII)V

    invoke-virtual {p1, v1}, Lgroovyjarjarantlr/Token;->setText(Ljava/lang/String;)V

    goto :goto_1

    :cond_6
    const/4 p1, 0x0

    .line 190
    :goto_1
    iput-object p1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    return-void

    .line 183
    :cond_7
    new-instance p1, Lgroovyjarjarantlr/NoViableAltForCharException;

    invoke-virtual {p0, v1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->LA(I)C

    move-result v0

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getColumn()I

    move-result v3

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarantlr/NoViableAltForCharException;-><init>(CLjava/lang/String;II)V

    throw p1
.end method

.method public nextToken()Lgroovyjarjarantlr/Token;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovyjarjarantlr/TokenStreamException;
        }
    .end annotation

    .line 99
    :goto_0
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->resetText()V

    const/4 v0, 0x1

    .line 103
    :try_start_0
    invoke-virtual {p0, v0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->mACTION(Z)V

    .line 104
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    .line 107
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    if-nez v0, :cond_0

    goto :goto_0

    .line 108
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    invoke-virtual {v0}, Lgroovyjarjarantlr/Token;->getType()I

    move-result v0

    .line 109
    iget-object v1, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;

    invoke-virtual {v1, v0}, Lgroovyjarjarantlr/Token;->setType(I)V

    .line 110
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->_returnToken:Lgroovyjarjarantlr/Token;
    :try_end_0
    .catch Lgroovyjarjarantlr/RecognitionException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Lgroovyjarjarantlr/CharStreamException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v0

    :catch_0
    move-exception v0

    goto :goto_1

    :catch_1
    move-exception v0

    .line 113
    :try_start_1
    new-instance v1, Lgroovyjarjarantlr/TokenStreamRecognitionException;

    invoke-direct {v1, v0}, Lgroovyjarjarantlr/TokenStreamRecognitionException;-><init>(Lgroovyjarjarantlr/RecognitionException;)V

    throw v1
    :try_end_1
    .catch Lgroovyjarjarantlr/CharStreamException; {:try_start_1 .. :try_end_1} :catch_0

    .line 117
    :goto_1
    instance-of v1, v0, Lgroovyjarjarantlr/CharStreamIOException;

    if-eqz v1, :cond_1

    .line 118
    new-instance v1, Lgroovyjarjarantlr/TokenStreamIOException;

    check-cast v0, Lgroovyjarjarantlr/CharStreamIOException;

    iget-object v0, v0, Lgroovyjarjarantlr/CharStreamIOException;->io:Ljava/io/IOException;

    invoke-direct {v1, v0}, Lgroovyjarjarantlr/TokenStreamIOException;-><init>(Ljava/io/IOException;)V

    throw v1

    .line 121
    :cond_1
    new-instance v1, Lgroovyjarjarantlr/TokenStreamException;

    invoke-virtual {v0}, Lgroovyjarjarantlr/CharStreamException;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v1, v0}, Lgroovyjarjarantlr/TokenStreamException;-><init>(Ljava/lang/String;)V

    throw v1
.end method

.method public reportError(Lgroovyjarjarantlr/RecognitionException;)V
    .locals 4

    .line 58
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    new-instance v1, Ljava/lang/StringBuffer;

    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    const-string v2, "Syntax error in action: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getColumn()I

    move-result v3

    invoke-virtual {v0, p1, v1, v2, v3}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;Ljava/lang/String;II)V

    return-void
.end method

.method public reportError(Ljava/lang/String;)V
    .locals 4

    .line 65
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getColumn()I

    move-result v3

    invoke-virtual {v0, p1, v1, v2, v3}, Lgroovyjarjarantlr/Tool;->error(Ljava/lang/String;Ljava/lang/String;II)V

    return-void
.end method

.method public reportWarning(Ljava/lang/String;)V
    .locals 4

    .line 70
    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getFilename()Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    .line 71
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    invoke-virtual {v0, p1}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;)V

    goto :goto_0

    .line 74
    :cond_0
    iget-object v0, p0, Lgroovyjarjarantlr/actions/python/CodeLexer;->antlrTool:Lgroovyjarjarantlr/Tool;

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getFilename()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getLine()I

    move-result v2

    invoke-virtual {p0}, Lgroovyjarjarantlr/actions/python/CodeLexer;->getColumn()I

    move-result v3

    invoke-virtual {v0, p1, v1, v2, v3}, Lgroovyjarjarantlr/Tool;->warning(Ljava/lang/String;Ljava/lang/String;II)V

    :goto_0
    return-void
.end method

.method public setLineOffset(I)V
    .locals 0

    .line 53
    invoke-virtual {p0, p1}, Lgroovyjarjarantlr/actions/python/CodeLexer;->setLine(I)V

    return-void
.end method
