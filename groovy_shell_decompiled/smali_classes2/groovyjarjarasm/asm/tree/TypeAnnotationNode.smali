.class public Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;
.super Lgroovyjarjarasm/asm/tree/AnnotationNode;
.source "TypeAnnotationNode.java"


# instance fields
.field public typePath:Lgroovyjarjarasm/asm/TypePath;

.field public typeRef:I


# direct methods
.method public constructor <init>(IILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;)V
    .locals 0

    .line 81
    invoke-direct {p0, p1, p4}, Lgroovyjarjarasm/asm/tree/AnnotationNode;-><init>(ILjava/lang/String;)V

    .line 82
    iput p2, p0, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typeRef:I

    .line 83
    iput-object p3, p0, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typePath:Lgroovyjarjarasm/asm/TypePath;

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 62
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;-><init>(IILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;)V

    .line 63
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    const-class p2, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    if-ne p1, p2, :cond_0

    return-void

    .line 64
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
.end method
