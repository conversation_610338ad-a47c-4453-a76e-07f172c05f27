.class public Lgroovyjarjarasm/asm/tree/ParameterNode;
.super Ljava/lang/Object;
.source "ParameterNode.java"


# instance fields
.field public access:I

.field public name:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0

    .line 55
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 56
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ParameterNode;->name:Ljava/lang/String;

    .line 57
    iput p2, p0, Lgroovyjarjarasm/asm/tree/ParameterNode;->access:I

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 2

    .line 66
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ParameterNode;->name:Ljava/lang/String;

    iget v1, p0, Lgroovyjarjarasm/asm/tree/ParameterNode;->access:I

    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitParameter(Ljava/lang/String;I)V

    return-void
.end method
