.class public Lgroovyjarjarasm/asm/tree/IincInsnNode;
.super Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
.source "IincInsnNode.java"


# instance fields
.field public incr:I

.field public var:I


# direct methods
.method public constructor <init>(II)V
    .locals 1

    const/16 v0, 0x84

    .line 54
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;-><init>(I)V

    .line 55
    iput p1, p0, Lgroovyjarjarasm/asm/tree/IincInsnNode;->var:I

    .line 56
    iput p2, p0, Lgroovyjarjarasm/asm/tree/IincInsnNode;->incr:I

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 2

    .line 66
    iget v0, p0, Lgroovyjarjarasm/asm/tree/IincInsnNode;->var:I

    iget v1, p0, Lgroovyjarjarasm/asm/tree/IincInsnNode;->incr:I

    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIincInsn(II)V

    .line 67
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/IincInsnNode;->acceptAnnotations(Lgroovyjarjarasm/asm/MethodVisitor;)V

    return-void
.end method

.method public clone(Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            ">;)",
            "Lgroovyjarjarasm/asm/tree/AbstractInsnNode;"
        }
    .end annotation

    .line 72
    new-instance p1, Lgroovyjarjarasm/asm/tree/IincInsnNode;

    iget v0, p0, Lgroovyjarjarasm/asm/tree/IincInsnNode;->var:I

    iget v1, p0, Lgroovyjarjarasm/asm/tree/IincInsnNode;->incr:I

    invoke-direct {p1, v0, v1}, Lgroovyjarjarasm/asm/tree/IincInsnNode;-><init>(II)V

    invoke-virtual {p1, p0}, Lgroovyjarjarasm/asm/tree/IincInsnNode;->cloneAnnotations(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object p1

    return-object p1
.end method

.method public getType()I
    .locals 1

    const/16 v0, 0xa

    return v0
.end method
