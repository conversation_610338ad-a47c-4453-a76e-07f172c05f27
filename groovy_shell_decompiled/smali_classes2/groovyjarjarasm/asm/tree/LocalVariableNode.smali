.class public Lgroovyjarjarasm/asm/tree/LocalVariableNode;
.super Ljava/lang/Object;
.source "LocalVariableNode.java"


# instance fields
.field public desc:Ljava/lang/String;

.field public end:Lgroovyjarjarasm/asm/tree/LabelNode;

.field public index:I

.field public name:Ljava/lang/String;

.field public signature:Ljava/lang/String;

.field public start:Lgroovyjarjarasm/asm/tree/LabelNode;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/tree/LabelNode;Lgroovyjarjarasm/asm/tree/LabelNode;I)V
    .locals 0

    .line 74
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 75
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->name:Ljava/lang/String;

    .line 76
    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->desc:Ljava/lang/String;

    .line 77
    iput-object p3, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->signature:Ljava/lang/String;

    .line 78
    iput-object p4, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->start:Lgroovyjarjarasm/asm/tree/LabelNode;

    .line 79
    iput-object p5, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->end:Lgroovyjarjarasm/asm/tree/LabelNode;

    .line 80
    iput p6, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->index:I

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 7

    .line 89
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->name:Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->desc:Ljava/lang/String;

    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->signature:Ljava/lang/String;

    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->start:Lgroovyjarjarasm/asm/tree/LabelNode;

    .line 90
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/tree/LabelNode;->getLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v4

    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->end:Lgroovyjarjarasm/asm/tree/LabelNode;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/tree/LabelNode;->getLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v5

    iget v6, p0, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->index:I

    move-object v0, p1

    .line 89
    invoke-virtual/range {v0 .. v6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V

    return-void
.end method
