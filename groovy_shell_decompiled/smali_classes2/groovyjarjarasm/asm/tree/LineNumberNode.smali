.class public Lgroovyjarjarasm/asm/tree/LineNumberNode;
.super Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
.source "LineNumberNode.java"


# instance fields
.field public line:I

.field public start:Lgroovyjarjarasm/asm/tree/LabelNode;


# direct methods
.method public constructor <init>(ILgroovyjarjarasm/asm/tree/LabelNode;)V
    .locals 1

    const/4 v0, -0x1

    .line 55
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;-><init>(I)V

    .line 56
    iput p1, p0, Lgroovyjarjarasm/asm/tree/LineNumberNode;->line:I

    .line 57
    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/LineNumberNode;->start:Lgroovyjarjarasm/asm/tree/LabelNode;

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 2

    .line 67
    iget v0, p0, Lgroovyjarjarasm/asm/tree/LineNumberNode;->line:I

    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/LineNumberNode;->start:Lgroovyjarjarasm/asm/tree/LabelNode;

    invoke-virtual {v1}, Lgroovyjarjarasm/asm/tree/LabelNode;->getLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v1

    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLineNumber(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public clone(Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            ">;)",
            "Lgroovyjarjarasm/asm/tree/AbstractInsnNode;"
        }
    .end annotation

    .line 72
    new-instance v0, Lgroovyjarjarasm/asm/tree/LineNumberNode;

    iget v1, p0, Lgroovyjarjarasm/asm/tree/LineNumberNode;->line:I

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/LineNumberNode;->start:Lgroovyjarjarasm/asm/tree/LabelNode;

    invoke-static {v2, p1}, Lgroovyjarjarasm/asm/tree/LineNumberNode;->clone(Lgroovyjarjarasm/asm/tree/LabelNode;Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p1

    invoke-direct {v0, v1, p1}, Lgroovyjarjarasm/asm/tree/LineNumberNode;-><init>(ILgroovyjarjarasm/asm/tree/LabelNode;)V

    return-object v0
.end method

.method public getType()I
    .locals 1

    const/16 v0, 0xf

    return v0
.end method
