.class public Lgroovyjarjarasm/asm/tree/JumpInsnNode;
.super Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
.source "JumpInsnNode.java"


# instance fields
.field public label:Lgroovyjarjarasm/asm/tree/LabelNode;


# direct methods
.method public constructor <init>(ILgroovyjarjarasm/asm/tree/LabelNode;)V
    .locals 0

    .line 57
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;-><init>(I)V

    .line 58
    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/JumpInsnNode;->label:Lgroovyjarjarasm/asm/tree/LabelNode;

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 2

    .line 79
    iget v0, p0, Lgroovyjarjarasm/asm/tree/JumpInsnNode;->opcode:I

    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/JumpInsnNode;->label:Lgroovyjarjarasm/asm/tree/LabelNode;

    invoke-virtual {v1}, Lgroovyjarjarasm/asm/tree/LabelNode;->getLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v1

    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    .line 80
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/JumpInsnNode;->acceptAnnotations(Lgroovyjarjarasm/asm/MethodVisitor;)V

    return-void
.end method

.method public clone(Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            ">;)",
            "Lgroovyjarjarasm/asm/tree/AbstractInsnNode;"
        }
    .end annotation

    .line 85
    new-instance v0, Lgroovyjarjarasm/asm/tree/JumpInsnNode;

    iget v1, p0, Lgroovyjarjarasm/asm/tree/JumpInsnNode;->opcode:I

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/JumpInsnNode;->label:Lgroovyjarjarasm/asm/tree/LabelNode;

    invoke-static {v2, p1}, Lgroovyjarjarasm/asm/tree/JumpInsnNode;->clone(Lgroovyjarjarasm/asm/tree/LabelNode;Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p1

    invoke-direct {v0, v1, p1}, Lgroovyjarjarasm/asm/tree/JumpInsnNode;-><init>(ILgroovyjarjarasm/asm/tree/LabelNode;)V

    invoke-virtual {v0, p0}, Lgroovyjarjarasm/asm/tree/JumpInsnNode;->cloneAnnotations(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object p1

    return-object p1
.end method

.method public getType()I
    .locals 1

    const/4 v0, 0x7

    return v0
.end method

.method public setOpcode(I)V
    .locals 0

    .line 69
    iput p1, p0, Lgroovyjarjarasm/asm/tree/JumpInsnNode;->opcode:I

    return-void
.end method
