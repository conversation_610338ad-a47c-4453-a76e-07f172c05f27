.class public Lgroovyjarjarasm/asm/tree/InsnNode;
.super Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
.source "InsnNode.java"


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 55
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;-><init>(I)V

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 1

    .line 65
    iget v0, p0, Lgroovyjarjarasm/asm/tree/InsnNode;->opcode:I

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 66
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/InsnNode;->acceptAnnotations(Lgroovyjarjarasm/asm/MethodVisitor;)V

    return-void
.end method

.method public clone(Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            ">;)",
            "Lgroovyjarjarasm/asm/tree/AbstractInsnNode;"
        }
    .end annotation

    .line 71
    new-instance p1, Lgroovyjarjarasm/asm/tree/InsnNode;

    iget v0, p0, Lgroovyjarjarasm/asm/tree/InsnNode;->opcode:I

    invoke-direct {p1, v0}, Lgroovyjarjarasm/asm/tree/InsnNode;-><init>(I)V

    invoke-virtual {p1, p0}, Lgroovyjarjarasm/asm/tree/InsnNode;->cloneAnnotations(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object p1

    return-object p1
.end method

.method public getType()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method
