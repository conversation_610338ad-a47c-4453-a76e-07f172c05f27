.class public Lgroovyjarjarasm/asm/tree/InnerClassNode;
.super Ljava/lang/Object;
.source "InnerClassNode.java"


# instance fields
.field public access:I

.field public innerName:Ljava/lang/String;

.field public name:Ljava/lang/String;

.field public outerName:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 0

    .line 76
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 77
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/InnerClassNode;->name:Ljava/lang/String;

    .line 78
    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/InnerClassNode;->outerName:Ljava/lang/String;

    .line 79
    iput-object p3, p0, Lgroovyjarjarasm/asm/tree/InnerClassNode;->innerName:Ljava/lang/String;

    .line 80
    iput p4, p0, Lgroovyjarjarasm/asm/tree/InnerClassNode;->access:I

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/ClassVisitor;)V
    .locals 4

    .line 89
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/InnerClassNode;->name:Ljava/lang/String;

    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/InnerClassNode;->outerName:Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/InnerClassNode;->innerName:Ljava/lang/String;

    iget v3, p0, Lgroovyjarjarasm/asm/tree/InnerClassNode;->access:I

    invoke-virtual {p1, v0, v1, v2, v3}, Lgroovyjarjarasm/asm/ClassVisitor;->visitInnerClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method
