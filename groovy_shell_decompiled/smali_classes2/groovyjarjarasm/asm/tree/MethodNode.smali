.class public Lgroovyjarjarasm/asm/tree/MethodNode;
.super Lgroovyjarjarasm/asm/MethodVisitor;
.source "MethodNode.java"


# instance fields
.field public access:I

.field public annotationDefault:Ljava/lang/Object;

.field public attrs:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/Attribute;",
            ">;"
        }
    .end annotation
.end field

.field public desc:Ljava/lang/String;

.field public exceptions:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public instructions:Lgroovyjarjarasm/asm/tree/InsnList;

.field public invisibleAnnotableParameterCount:I

.field public invisibleAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/AnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public invisibleLocalVariableAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/LocalVariableAnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public invisibleParameterAnnotations:[Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/AnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public invisibleTypeAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public localVariables:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/LocalVariableNode;",
            ">;"
        }
    .end annotation
.end field

.field public maxLocals:I

.field public maxStack:I

.field public name:Ljava/lang/String;

.field public parameters:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/ParameterNode;",
            ">;"
        }
    .end annotation
.end field

.field public signature:Ljava/lang/String;

.field public tryCatchBlocks:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;",
            ">;"
        }
    .end annotation
.end field

.field public visibleAnnotableParameterCount:I

.field public visibleAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/AnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public visibleLocalVariableAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/LocalVariableAnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public visibleParameterAnnotations:[Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/AnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public visibleTypeAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field private visited:Z


# direct methods
.method public constructor <init>()V
    .locals 2

    const/high16 v0, 0x90000

    .line 158
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/tree/MethodNode;-><init>(I)V

    .line 159
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovyjarjarasm/asm/tree/MethodNode;

    if-ne v0, v1, :cond_0

    return-void

    .line 160
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method

.method public constructor <init>(I)V
    .locals 0

    .line 171
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;-><init>(I)V

    .line 172
    new-instance p1, Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/InsnList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    return-void
.end method

.method public constructor <init>(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 0

    .line 220
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;-><init>(I)V

    .line 221
    iput p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->access:I

    .line 222
    iput-object p3, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->name:Ljava/lang/String;

    .line 223
    iput-object p4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->desc:Ljava/lang/String;

    .line 224
    iput-object p5, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->signature:Ljava/lang/String;

    .line 225
    invoke-static {p6}, Lgroovyjarjarasm/asm/tree/Util;->asArrayList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->exceptions:Ljava/util/List;

    and-int/lit16 p1, p2, 0x400

    if-nez p1, :cond_0

    .line 227
    new-instance p1, Ljava/util/ArrayList;

    const/4 p2, 0x5

    invoke-direct {p1, p2}, Ljava/util/ArrayList;-><init>(I)V

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->localVariables:Ljava/util/List;

    .line 229
    :cond_0
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->tryCatchBlocks:Ljava/util/List;

    .line 230
    new-instance p1, Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/InsnList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 7

    const/high16 v1, 0x90000

    move-object v0, p0

    move v2, p1

    move-object v3, p2

    move-object v4, p3

    move-object v5, p4

    move-object v6, p5

    .line 194
    invoke-direct/range {v0 .. v6}, Lgroovyjarjarasm/asm/tree/MethodNode;-><init>(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    .line 195
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    const-class p2, Lgroovyjarjarasm/asm/tree/MethodNode;

    if-ne p1, p2, :cond_0

    return-void

    .line 196
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
.end method

.method private getLabelNodes([Lgroovyjarjarasm/asm/Label;)[Lgroovyjarjarasm/asm/tree/LabelNode;
    .locals 4

    .line 540
    array-length v0, p1

    new-array v0, v0, [Lgroovyjarjarasm/asm/tree/LabelNode;

    .line 541
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    .line 542
    aget-object v3, p1, v2

    invoke-virtual {p0, v3}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object v3

    aput-object v3, v0, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private getLabelNodes([Ljava/lang/Object;)[Ljava/lang/Object;
    .locals 5

    .line 548
    array-length v0, p1

    new-array v0, v0, [Ljava/lang/Object;

    .line 549
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    .line 550
    aget-object v3, p1, v2

    .line 551
    instance-of v4, v3, Lgroovyjarjarasm/asm/Label;

    if-eqz v4, :cond_0

    .line 552
    check-cast v3, Lgroovyjarjarasm/asm/Label;

    invoke-virtual {p0, v3}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object v3

    .line 554
    :cond_0
    aput-object v3, v0, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-object v0
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/ClassVisitor;)V
    .locals 7

    .line 643
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->exceptions:Ljava/util/List;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    new-array v1, v1, [Ljava/lang/String;

    invoke-interface {v0, v1}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/lang/String;

    :goto_0
    move-object v6, v0

    .line 644
    iget v2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->access:I

    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->name:Ljava/lang/String;

    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->desc:Ljava/lang/String;

    iget-object v5, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->signature:Ljava/lang/String;

    move-object v1, p1

    .line 645
    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 647
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/MethodNode;->accept(Lgroovyjarjarasm/asm/MethodVisitor;)V

    :cond_1
    return-void
.end method

.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 9

    .line 658
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->parameters:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 659
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_0

    .line 660
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->parameters:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarasm/asm/tree/ParameterNode;

    invoke-virtual {v3, p1}, Lgroovyjarjarasm/asm/tree/ParameterNode;->accept(Lgroovyjarjarasm/asm/MethodVisitor;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 664
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->annotationDefault:Ljava/lang/Object;

    if-eqz v0, :cond_1

    .line 665
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAnnotationDefault()Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v0

    const/4 v2, 0x0

    .line 666
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->annotationDefault:Ljava/lang/Object;

    invoke-static {v0, v2, v3}, Lgroovyjarjarasm/asm/tree/AnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;Ljava/lang/String;Ljava/lang/Object;)V

    if-eqz v0, :cond_1

    .line 668
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/AnnotationVisitor;->visitEnd()V

    .line 671
    :cond_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleAnnotations:Ljava/util/List;

    const/4 v2, 0x1

    if-eqz v0, :cond_2

    .line 672
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v1

    :goto_1
    if-ge v3, v0, :cond_2

    .line 673
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleAnnotations:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    .line 674
    iget-object v5, v4, Lgroovyjarjarasm/asm/tree/AnnotationNode;->desc:Ljava/lang/String;

    invoke-virtual {p1, v5, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v5

    invoke-virtual {v4, v5}, Lgroovyjarjarasm/asm/tree/AnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 677
    :cond_2
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleAnnotations:Ljava/util/List;

    if-eqz v0, :cond_3

    .line 678
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v1

    :goto_2
    if-ge v3, v0, :cond_3

    .line 679
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleAnnotations:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    .line 680
    iget-object v5, v4, Lgroovyjarjarasm/asm/tree/AnnotationNode;->desc:Ljava/lang/String;

    invoke-virtual {p1, v5, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v5

    invoke-virtual {v4, v5}, Lgroovyjarjarasm/asm/tree/AnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    .line 683
    :cond_3
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_4

    .line 684
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v1

    :goto_3
    if-ge v3, v0, :cond_4

    .line 685
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    .line 686
    iget v5, v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typeRef:I

    iget-object v6, v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typePath:Lgroovyjarjarasm/asm/TypePath;

    iget-object v7, v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->desc:Ljava/lang/String;

    .line 687
    invoke-virtual {p1, v5, v6, v7, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v5

    .line 686
    invoke-virtual {v4, v5}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_3

    .line 691
    :cond_4
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_5

    .line 692
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v1

    :goto_4
    if-ge v3, v0, :cond_5

    .line 693
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    .line 694
    iget v5, v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typeRef:I

    iget-object v6, v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typePath:Lgroovyjarjarasm/asm/TypePath;

    iget-object v7, v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->desc:Ljava/lang/String;

    .line 695
    invoke-virtual {p1, v5, v6, v7, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v5

    .line 694
    invoke-virtual {v4, v5}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_4

    .line 699
    :cond_5
    iget v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleAnnotableParameterCount:I

    if-lez v0, :cond_6

    .line 700
    invoke-virtual {p1, v0, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAnnotableParameterCount(IZ)V

    .line 702
    :cond_6
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleParameterAnnotations:[Ljava/util/List;

    if-eqz v0, :cond_9

    .line 703
    array-length v0, v0

    move v3, v1

    :goto_5
    if-ge v3, v0, :cond_9

    .line 704
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleParameterAnnotations:[Ljava/util/List;

    aget-object v4, v4, v3

    if-nez v4, :cond_7

    goto :goto_7

    .line 708
    :cond_7
    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v5

    move v6, v1

    :goto_6
    if-ge v6, v5, :cond_8

    .line 709
    invoke-interface {v4, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    .line 710
    iget-object v8, v7, Lgroovyjarjarasm/asm/tree/AnnotationNode;->desc:Ljava/lang/String;

    invoke-virtual {p1, v3, v8, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v8

    invoke-virtual {v7, v8}, Lgroovyjarjarasm/asm/tree/AnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v6, v6, 0x1

    goto :goto_6

    :cond_8
    :goto_7
    add-int/lit8 v3, v3, 0x1

    goto :goto_5

    .line 714
    :cond_9
    iget v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleAnnotableParameterCount:I

    if-lez v0, :cond_a

    .line 715
    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAnnotableParameterCount(IZ)V

    .line 717
    :cond_a
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleParameterAnnotations:[Ljava/util/List;

    if-eqz v0, :cond_d

    .line 718
    array-length v0, v0

    move v3, v1

    :goto_8
    if-ge v3, v0, :cond_d

    .line 719
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleParameterAnnotations:[Ljava/util/List;

    aget-object v4, v4, v3

    if-nez v4, :cond_b

    goto :goto_a

    .line 723
    :cond_b
    invoke-interface {v4}, Ljava/util/List;->size()I

    move-result v5

    move v6, v1

    :goto_9
    if-ge v6, v5, :cond_c

    .line 724
    invoke-interface {v4, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    .line 725
    iget-object v8, v7, Lgroovyjarjarasm/asm/tree/AnnotationNode;->desc:Ljava/lang/String;

    invoke-virtual {p1, v3, v8, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v8

    invoke-virtual {v7, v8}, Lgroovyjarjarasm/asm/tree/AnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v6, v6, 0x1

    goto :goto_9

    :cond_c
    :goto_a
    add-int/lit8 v3, v3, 0x1

    goto :goto_8

    .line 730
    :cond_d
    iget-boolean v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visited:Z

    if-eqz v0, :cond_e

    .line 731
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/tree/InsnList;->resetLabels()V

    .line 733
    :cond_e
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->attrs:Ljava/util/List;

    if-eqz v0, :cond_f

    .line 734
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v1

    :goto_b
    if-ge v3, v0, :cond_f

    .line 735
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->attrs:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/Attribute;

    invoke-virtual {p1, v4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_b

    .line 739
    :cond_f
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/tree/InsnList;->size()I

    move-result v0

    if-lez v0, :cond_14

    .line 740
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitCode()V

    .line 742
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->tryCatchBlocks:Ljava/util/List;

    if-eqz v0, :cond_10

    .line 743
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v1

    :goto_c
    if-ge v3, v0, :cond_10

    .line 744
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->tryCatchBlocks:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;

    invoke-virtual {v4, v3}, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->updateIndex(I)V

    .line 745
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->tryCatchBlocks:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;

    invoke-virtual {v4, p1}, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->accept(Lgroovyjarjarasm/asm/MethodVisitor;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_c

    .line 749
    :cond_10
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/tree/InsnList;->accept(Lgroovyjarjarasm/asm/MethodVisitor;)V

    .line 751
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->localVariables:Ljava/util/List;

    if-eqz v0, :cond_11

    .line 752
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v1

    :goto_d
    if-ge v3, v0, :cond_11

    .line 753
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->localVariables:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/LocalVariableNode;

    invoke-virtual {v4, p1}, Lgroovyjarjarasm/asm/tree/LocalVariableNode;->accept(Lgroovyjarjarasm/asm/MethodVisitor;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_d

    .line 757
    :cond_11
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleLocalVariableAnnotations:Ljava/util/List;

    if-eqz v0, :cond_12

    .line 758
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v1

    :goto_e
    if-ge v3, v0, :cond_12

    .line 759
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleLocalVariableAnnotations:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/LocalVariableAnnotationNode;

    invoke-virtual {v4, p1, v2}, Lgroovyjarjarasm/asm/tree/LocalVariableAnnotationNode;->accept(Lgroovyjarjarasm/asm/MethodVisitor;Z)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_e

    .line 762
    :cond_12
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleLocalVariableAnnotations:Ljava/util/List;

    if-eqz v0, :cond_13

    .line 763
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v1

    :goto_f
    if-ge v3, v0, :cond_13

    .line 764
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleLocalVariableAnnotations:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/LocalVariableAnnotationNode;

    invoke-virtual {v4, p1, v1}, Lgroovyjarjarasm/asm/tree/LocalVariableAnnotationNode;->accept(Lgroovyjarjarasm/asm/MethodVisitor;Z)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_f

    .line 767
    :cond_13
    iget v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->maxStack:I

    iget v1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->maxLocals:I

    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 768
    iput-boolean v2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visited:Z

    .line 770
    :cond_14
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    return-void
.end method

.method public check(I)V
    .locals 5

    const/4 v0, 0x1

    const/high16 v1, 0x40000

    if-ne p1, v1, :cond_18

    .line 573
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->parameters:Ljava/util/List;

    if-eqz v1, :cond_1

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    .line 574
    :cond_0
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 576
    :cond_1
    :goto_0
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleTypeAnnotations:Ljava/util/List;

    if-eqz v1, :cond_3

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_1

    .line 577
    :cond_2
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 579
    :cond_3
    :goto_1
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleTypeAnnotations:Ljava/util/List;

    if-eqz v1, :cond_5

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_4

    goto :goto_2

    .line 580
    :cond_4
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 582
    :cond_5
    :goto_2
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->tryCatchBlocks:Ljava/util/List;

    if-eqz v1, :cond_a

    .line 583
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    sub-int/2addr v1, v0

    :goto_3
    if-ltz v1, :cond_a

    .line 584
    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->tryCatchBlocks:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;

    .line 585
    iget-object v3, v2, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->visibleTypeAnnotations:Ljava/util/List;

    if-eqz v3, :cond_7

    iget-object v3, v2, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->visibleTypeAnnotations:Ljava/util/List;

    .line 586
    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_6

    goto :goto_4

    .line 587
    :cond_6
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 589
    :cond_7
    :goto_4
    iget-object v3, v2, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->invisibleTypeAnnotations:Ljava/util/List;

    if-eqz v3, :cond_9

    iget-object v2, v2, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->invisibleTypeAnnotations:Ljava/util/List;

    .line 590
    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_8

    goto :goto_5

    .line 591
    :cond_8
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    :cond_9
    :goto_5
    add-int/lit8 v1, v1, -0x1

    goto :goto_3

    .line 595
    :cond_a
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-virtual {v1}, Lgroovyjarjarasm/asm/tree/InsnList;->size()I

    move-result v1

    sub-int/2addr v1, v0

    :goto_6
    if-ltz v1, :cond_14

    .line 596
    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-virtual {v2, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->get(I)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object v2

    .line 597
    iget-object v3, v2, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->visibleTypeAnnotations:Ljava/util/List;

    if-eqz v3, :cond_c

    iget-object v3, v2, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->visibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_b

    goto :goto_7

    .line 598
    :cond_b
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 600
    :cond_c
    :goto_7
    iget-object v3, v2, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->invisibleTypeAnnotations:Ljava/util/List;

    if-eqz v3, :cond_e

    iget-object v3, v2, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->invisibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v3

    if-eqz v3, :cond_d

    goto :goto_8

    .line 601
    :cond_d
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 603
    :cond_e
    :goto_8
    instance-of v3, v2, Lgroovyjarjarasm/asm/tree/MethodInsnNode;

    if-eqz v3, :cond_11

    .line 604
    move-object v3, v2

    check-cast v3, Lgroovyjarjarasm/asm/tree/MethodInsnNode;

    iget-boolean v3, v3, Lgroovyjarjarasm/asm/tree/MethodInsnNode;->itf:Z

    .line 605
    iget v2, v2, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->opcode:I

    const/16 v4, 0xb9

    if-ne v2, v4, :cond_f

    move v2, v0

    goto :goto_9

    :cond_f
    const/4 v2, 0x0

    :goto_9
    if-ne v3, v2, :cond_10

    goto :goto_a

    .line 606
    :cond_10
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 608
    :cond_11
    instance-of v3, v2, Lgroovyjarjarasm/asm/tree/LdcInsnNode;

    if-eqz v3, :cond_13

    .line 609
    check-cast v2, Lgroovyjarjarasm/asm/tree/LdcInsnNode;

    iget-object v2, v2, Lgroovyjarjarasm/asm/tree/LdcInsnNode;->cst:Ljava/lang/Object;

    .line 610
    instance-of v3, v2, Lgroovyjarjarasm/asm/Handle;

    if-nez v3, :cond_12

    instance-of v3, v2, Lgroovyjarjarasm/asm/Type;

    if-eqz v3, :cond_13

    check-cast v2, Lgroovyjarjarasm/asm/Type;

    .line 611
    invoke-virtual {v2}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v2

    const/16 v3, 0xb

    if-eq v2, v3, :cond_12

    goto :goto_a

    .line 612
    :cond_12
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    :cond_13
    :goto_a
    add-int/lit8 v1, v1, -0x1

    goto :goto_6

    .line 616
    :cond_14
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleLocalVariableAnnotations:Ljava/util/List;

    if-eqz v1, :cond_16

    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_15

    goto :goto_b

    .line 617
    :cond_15
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 619
    :cond_16
    :goto_b
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleLocalVariableAnnotations:Ljava/util/List;

    if-eqz v1, :cond_18

    .line 620
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_17

    goto :goto_c

    .line 621
    :cond_17
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    :cond_18
    :goto_c
    const/high16 v1, 0x70000

    if-ge p1, v1, :cond_1b

    .line 625
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/tree/InsnList;->size()I

    move-result p1

    sub-int/2addr p1, v0

    :goto_d
    if-ltz p1, :cond_1b

    .line 626
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/tree/InsnList;->get(I)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object v0

    .line 627
    instance-of v1, v0, Lgroovyjarjarasm/asm/tree/LdcInsnNode;

    if-eqz v1, :cond_1a

    .line 628
    check-cast v0, Lgroovyjarjarasm/asm/tree/LdcInsnNode;

    iget-object v0, v0, Lgroovyjarjarasm/asm/tree/LdcInsnNode;->cst:Ljava/lang/Object;

    .line 629
    instance-of v0, v0, Lgroovyjarjarasm/asm/ConstantDynamic;

    if-nez v0, :cond_19

    goto :goto_e

    .line 630
    :cond_19
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    :cond_1a
    :goto_e
    add-int/lit8 p1, p1, -0x1

    goto :goto_d

    :cond_1b
    return-void
.end method

.method protected getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;
    .locals 1

    .line 533
    iget-object v0, p1, Lgroovyjarjarasm/asm/Label;->info:Ljava/lang/Object;

    instance-of v0, v0, Lgroovyjarjarasm/asm/tree/LabelNode;

    if-nez v0, :cond_0

    .line 534
    new-instance v0, Lgroovyjarjarasm/asm/tree/LabelNode;

    invoke-direct {v0}, Lgroovyjarjarasm/asm/tree/LabelNode;-><init>()V

    iput-object v0, p1, Lgroovyjarjarasm/asm/Label;->info:Ljava/lang/Object;

    .line 536
    :cond_0
    iget-object p1, p1, Lgroovyjarjarasm/asm/Label;->info:Ljava/lang/Object;

    check-cast p1, Lgroovyjarjarasm/asm/tree/LabelNode;

    return-object p1
.end method

.method public visitAnnotableParameterCount(IZ)V
    .locals 0

    if-eqz p2, :cond_0

    .line 284
    iput p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleAnnotableParameterCount:I

    goto :goto_0

    .line 286
    :cond_0
    iput p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleAnnotableParameterCount:I

    :goto_0
    return-void
.end method

.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 1

    .line 260
    new-instance v0, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    invoke-direct {v0, p1}, Lgroovyjarjarasm/asm/tree/AnnotationNode;-><init>(Ljava/lang/String;)V

    if-eqz p2, :cond_0

    .line 262
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleAnnotations:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleAnnotations:Ljava/util/List;

    goto :goto_0

    .line 264
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleAnnotations:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleAnnotations:Ljava/util/List;

    :goto_0
    return-object v0
.end method

.method public visitAnnotationDefault()Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 3

    .line 248
    new-instance v0, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    new-instance v1, Lgroovyjarjarasm/asm/tree/MethodNode$1;

    const/4 v2, 0x0

    invoke-direct {v1, p0, v2}, Lgroovyjarjarasm/asm/tree/MethodNode$1;-><init>(Lgroovyjarjarasm/asm/tree/MethodNode;I)V

    invoke-direct {v0, v1}, Lgroovyjarjarasm/asm/tree/AnnotationNode;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method public visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 1

    .line 315
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->attrs:Ljava/util/List;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->attrs:Ljava/util/List;

    return-void
.end method

.method public visitCode()V
    .locals 0

    return-void
.end method

.method public visitEnd()V
    .locals 0

    return-void
.end method

.method public visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 362
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/FieldInsnNode;

    invoke-direct {v1, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/tree/FieldInsnNode;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V
    .locals 8

    .line 330
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v7, Lgroovyjarjarasm/asm/tree/FrameNode;

    const/4 v1, 0x0

    if-nez p3, :cond_0

    move-object v4, v1

    goto :goto_0

    .line 334
    :cond_0
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNodes([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p3

    move-object v4, p3

    :goto_0
    if-nez p5, :cond_1

    move-object v6, v1

    goto :goto_1

    .line 336
    :cond_1
    invoke-direct {p0, p5}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNodes([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p3

    move-object v6, p3

    :goto_1
    move-object v1, v7

    move v2, p1

    move v3, p2

    move v5, p4

    invoke-direct/range {v1 .. v6}, Lgroovyjarjarasm/asm/tree/FrameNode;-><init>(II[Ljava/lang/Object;I[Ljava/lang/Object;)V

    .line 330
    invoke-virtual {v0, v7}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitIincInsn(II)V
    .locals 2

    .line 410
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/IincInsnNode;

    invoke-direct {v1, p1, p2}, Lgroovyjarjarasm/asm/tree/IincInsnNode;-><init>(II)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitInsn(I)V
    .locals 2

    .line 341
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/InsnNode;

    invoke-direct {v1, p1}, Lgroovyjarjarasm/asm/tree/InsnNode;-><init>(I)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitInsnAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 3

    .line 433
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/tree/InsnList;->getLast()Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object v0

    .line 434
    :goto_0
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->getOpcode()I

    move-result v1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_0

    .line 435
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->getPrevious()Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object v0

    goto :goto_0

    .line 438
    :cond_0
    new-instance v1, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    invoke-direct {v1, p1, p2, p3}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;-><init>(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;)V

    if-eqz p4, :cond_1

    .line 440
    iget-object p1, v0, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->visibleTypeAnnotations:Ljava/util/List;

    .line 441
    invoke-static {p1, v1}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, v0, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->visibleTypeAnnotations:Ljava/util/List;

    goto :goto_1

    .line 443
    :cond_1
    iget-object p1, v0, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->invisibleTypeAnnotations:Ljava/util/List;

    .line 444
    invoke-static {p1, v1}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, v0, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;->invisibleTypeAnnotations:Ljava/util/List;

    :goto_1
    return-object v1
.end method

.method public visitIntInsn(II)V
    .locals 2

    .line 346
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/IntInsnNode;

    invoke-direct {v1, p1, p2}, Lgroovyjarjarasm/asm/tree/IntInsnNode;-><init>(II)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public varargs visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V
    .locals 2

    .line 388
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;

    invoke-direct {v1, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 395
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/JumpInsnNode;

    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p2

    invoke-direct {v1, p1, p2}, Lgroovyjarjarasm/asm/tree/JumpInsnNode;-><init>(ILgroovyjarjarasm/asm/tree/LabelNode;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitLabel(Lgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 400
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitLdcInsn(Ljava/lang/Object;)V
    .locals 2

    .line 405
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/LdcInsnNode;

    invoke-direct {v1, p1}, Lgroovyjarjarasm/asm/tree/LdcInsnNode;-><init>(Ljava/lang/Object;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitLineNumber(ILgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 510
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/LineNumberNode;

    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p2

    invoke-direct {v1, p1, p2}, Lgroovyjarjarasm/asm/tree/LineNumberNode;-><init>(ILgroovyjarjarasm/asm/tree/LabelNode;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V
    .locals 8

    .line 480
    new-instance v7, Lgroovyjarjarasm/asm/tree/LocalVariableNode;

    .line 482
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object v4

    invoke-virtual {p0, p5}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object v5

    move-object v0, v7

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move v6, p6

    invoke-direct/range {v0 .. v6}, Lgroovyjarjarasm/asm/tree/LocalVariableNode;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/tree/LabelNode;Lgroovyjarjarasm/asm/tree/LabelNode;I)V

    .line 483
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->localVariables:Ljava/util/List;

    invoke-static {p1, v7}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->localVariables:Ljava/util/List;

    return-void
.end method

.method public visitLocalVariableAnnotation(ILgroovyjarjarasm/asm/TypePath;[Lgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;[ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 8

    .line 495
    new-instance v7, Lgroovyjarjarasm/asm/tree/LocalVariableAnnotationNode;

    .line 497
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNodes([Lgroovyjarjarasm/asm/Label;)[Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object v3

    invoke-direct {p0, p4}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNodes([Lgroovyjarjarasm/asm/Label;)[Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object v4

    move-object v0, v7

    move v1, p1

    move-object v2, p2

    move-object v5, p5

    move-object v6, p6

    invoke-direct/range {v0 .. v6}, Lgroovyjarjarasm/asm/tree/LocalVariableAnnotationNode;-><init>(ILgroovyjarjarasm/asm/TypePath;[Lgroovyjarjarasm/asm/tree/LabelNode;[Lgroovyjarjarasm/asm/tree/LabelNode;[ILjava/lang/String;)V

    if-eqz p7, :cond_0

    .line 499
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleLocalVariableAnnotations:Ljava/util/List;

    .line 500
    invoke-static {p1, v7}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleLocalVariableAnnotations:Ljava/util/List;

    goto :goto_0

    .line 502
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleLocalVariableAnnotations:Ljava/util/List;

    .line 503
    invoke-static {p1, v7}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleLocalVariableAnnotations:Ljava/util/List;

    :goto_0
    return-object v7
.end method

.method public visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 421
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/LookupSwitchInsnNode;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p1

    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNodes([Lgroovyjarjarasm/asm/Label;)[Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p3

    invoke-direct {v1, p1, p2, p3}, Lgroovyjarjarasm/asm/tree/LookupSwitchInsnNode;-><init>(Lgroovyjarjarasm/asm/tree/LabelNode;[I[Lgroovyjarjarasm/asm/tree/LabelNode;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitMaxs(II)V
    .locals 0

    .line 515
    iput p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->maxStack:I

    .line 516
    iput p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->maxLocals:I

    return-void
.end method

.method public visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 7

    .line 372
    iget v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->api:I

    const/high16 v1, 0x50000

    if-ge v0, v1, :cond_0

    and-int/lit16 v0, p1, 0x100

    if-nez v0, :cond_0

    .line 374
    invoke-super/range {p0 .. p5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void

    :cond_0
    and-int/lit16 v2, p1, -0x101

    .line 379
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v0, Lgroovyjarjarasm/asm/tree/MethodInsnNode;

    move-object v1, v0

    move-object v3, p2

    move-object v4, p3

    move-object v5, p4

    move v6, p5

    invoke-direct/range {v1 .. v6}, Lgroovyjarjarasm/asm/tree/MethodInsnNode;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitMultiANewArrayInsn(Ljava/lang/String;I)V
    .locals 2

    .line 426
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;

    invoke-direct {v1, p1, p2}, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;-><init>(Ljava/lang/String;I)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitParameter(Ljava/lang/String;I)V
    .locals 2

    .line 239
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->parameters:Ljava/util/List;

    if-nez v0, :cond_0

    .line 240
    new-instance v0, Ljava/util/ArrayList;

    const/4 v1, 0x5

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->parameters:Ljava/util/List;

    .line 242
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->parameters:Ljava/util/List;

    new-instance v1, Lgroovyjarjarasm/asm/tree/ParameterNode;

    invoke-direct {v1, p1, p2}, Lgroovyjarjarasm/asm/tree/ParameterNode;-><init>(Ljava/lang/String;I)V

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 1

    .line 294
    new-instance v0, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    invoke-direct {v0, p2}, Lgroovyjarjarasm/asm/tree/AnnotationNode;-><init>(Ljava/lang/String;)V

    if-eqz p3, :cond_1

    .line 296
    iget-object p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleParameterAnnotations:[Ljava/util/List;

    if-nez p2, :cond_0

    .line 297
    iget-object p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->desc:Ljava/lang/String;

    invoke-static {p2}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object p2

    array-length p2, p2

    .line 298
    new-array p2, p2, [Ljava/util/List;

    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleParameterAnnotations:[Ljava/util/List;

    .line 300
    :cond_0
    iget-object p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleParameterAnnotations:[Ljava/util/List;

    aget-object p3, p2, p1

    .line 301
    invoke-static {p3, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p3

    aput-object p3, p2, p1

    goto :goto_0

    .line 303
    :cond_1
    iget-object p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleParameterAnnotations:[Ljava/util/List;

    if-nez p2, :cond_2

    .line 304
    iget-object p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->desc:Ljava/lang/String;

    invoke-static {p2}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object p2

    array-length p2, p2

    .line 305
    new-array p2, p2, [Ljava/util/List;

    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleParameterAnnotations:[Ljava/util/List;

    .line 307
    :cond_2
    iget-object p2, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleParameterAnnotations:[Ljava/util/List;

    aget-object p3, p2, p1

    .line 308
    invoke-static {p3, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p3

    aput-object p3, p2, p1

    :goto_0
    return-object v0
.end method

.method public varargs visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 416
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/TableSwitchInsnNode;

    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p3

    invoke-direct {p0, p4}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNodes([Lgroovyjarjarasm/asm/Label;)[Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p4

    invoke-direct {v1, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/tree/TableSwitchInsnNode;-><init>(IILgroovyjarjarasm/asm/tree/LabelNode;[Lgroovyjarjarasm/asm/tree/LabelNode;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 460
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->tryCatchBlocks:Ljava/util/List;

    const v1, 0xffff00

    and-int/2addr v1, p1

    shr-int/lit8 v1, v1, 0x8

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;

    .line 461
    new-instance v1, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    invoke-direct {v1, p1, p2, p3}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;-><init>(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;)V

    if-eqz p4, :cond_0

    .line 463
    iget-object p1, v0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->visibleTypeAnnotations:Ljava/util/List;

    .line 464
    invoke-static {p1, v1}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, v0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->visibleTypeAnnotations:Ljava/util/List;

    goto :goto_0

    .line 466
    :cond_0
    iget-object p1, v0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->invisibleTypeAnnotations:Ljava/util/List;

    .line 467
    invoke-static {p1, v1}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, v0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->invisibleTypeAnnotations:Ljava/util/List;

    :goto_0
    return-object v1
.end method

.method public visitTryCatchBlock(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Ljava/lang/String;)V
    .locals 1

    .line 452
    new-instance v0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;

    .line 453
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p1

    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p2

    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/tree/MethodNode;->getLabelNode(Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/tree/LabelNode;

    move-result-object p3

    invoke-direct {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;-><init>(Lgroovyjarjarasm/asm/tree/LabelNode;Lgroovyjarjarasm/asm/tree/LabelNode;Lgroovyjarjarasm/asm/tree/LabelNode;Ljava/lang/String;)V

    .line 454
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->tryCatchBlocks:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->tryCatchBlocks:Ljava/util/List;

    return-void
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 1

    .line 272
    new-instance v0, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    invoke-direct {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;-><init>(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;)V

    if-eqz p4, :cond_0

    .line 274
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleTypeAnnotations:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->visibleTypeAnnotations:Ljava/util/List;

    goto :goto_0

    .line 276
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleTypeAnnotations:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->invisibleTypeAnnotations:Ljava/util/List;

    :goto_0
    return-object v0
.end method

.method public visitTypeInsn(ILjava/lang/String;)V
    .locals 2

    .line 356
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/TypeInsnNode;

    invoke-direct {v1, p1, p2}, Lgroovyjarjarasm/asm/tree/TypeInsnNode;-><init>(ILjava/lang/String;)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method

.method public visitVarInsn(II)V
    .locals 2

    .line 351
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MethodNode;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    new-instance v1, Lgroovyjarjarasm/asm/tree/VarInsnNode;

    invoke-direct {v1, p1, p2}, Lgroovyjarjarasm/asm/tree/VarInsnNode;-><init>(II)V

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->add(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)V

    return-void
.end method
