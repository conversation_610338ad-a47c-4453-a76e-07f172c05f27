.class public Lgroovyjarjarasm/asm/tree/LabelNode;
.super Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
.source "LabelNode.java"


# instance fields
.field private value:Lgroovyjarjarasm/asm/Label;


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, -0x1

    .line 40
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;-><init>(I)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/Label;)V
    .locals 1

    const/4 v0, -0x1

    .line 44
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;-><init>(I)V

    .line 45
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/LabelNode;->value:Lgroovyjarjarasm/asm/Label;

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 1

    .line 68
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/tree/LabelNode;->getLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v0

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public clone(Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            ">;)",
            "Lgroovyjarjarasm/asm/tree/AbstractInsnNode;"
        }
    .end annotation

    .line 73
    invoke-interface {p1, p0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    return-object p1
.end method

.method public getLabel()Lgroovyjarjarasm/asm/Label;
    .locals 1

    .line 60
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/LabelNode;->value:Lgroovyjarjarasm/asm/Label;

    if-nez v0, :cond_0

    .line 61
    new-instance v0, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v0}, Lgroovyjarjarasm/asm/Label;-><init>()V

    iput-object v0, p0, Lgroovyjarjarasm/asm/tree/LabelNode;->value:Lgroovyjarjarasm/asm/Label;

    .line 63
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/LabelNode;->value:Lgroovyjarjarasm/asm/Label;

    return-object v0
.end method

.method public getType()I
    .locals 1

    const/16 v0, 0x8

    return v0
.end method

.method public resetLabel()V
    .locals 1

    const/4 v0, 0x0

    .line 77
    iput-object v0, p0, Lgroovyjarjarasm/asm/tree/LabelNode;->value:Lgroovyjarjarasm/asm/Label;

    return-void
.end method
