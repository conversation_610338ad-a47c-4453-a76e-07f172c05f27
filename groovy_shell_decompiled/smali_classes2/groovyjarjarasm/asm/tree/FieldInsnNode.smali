.class public Lgroovyjarjarasm/asm/tree/FieldInsnNode;
.super Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
.source "FieldInsnNode.java"


# instance fields
.field public desc:Ljava/lang/String;

.field public name:Ljava/lang/String;

.field public owner:Ljava/lang/String;


# direct methods
.method public constructor <init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 65
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;-><init>(I)V

    .line 66
    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->owner:Ljava/lang/String;

    .line 67
    iput-object p3, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->name:Ljava/lang/String;

    .line 68
    iput-object p4, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->desc:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 4

    .line 88
    iget v0, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->opcode:I

    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->owner:Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->name:Ljava/lang/String;

    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->desc:Ljava/lang/String;

    invoke-virtual {p1, v0, v1, v2, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 89
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->acceptAnnotations(Lgroovyjarjarasm/asm/MethodVisitor;)V

    return-void
.end method

.method public clone(Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            ">;)",
            "Lgroovyjarjarasm/asm/tree/AbstractInsnNode;"
        }
    .end annotation

    .line 94
    new-instance p1, Lgroovyjarjarasm/asm/tree/FieldInsnNode;

    iget v0, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->opcode:I

    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->owner:Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->name:Ljava/lang/String;

    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->desc:Ljava/lang/String;

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarasm/asm/tree/FieldInsnNode;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {p1, p0}, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->cloneAnnotations(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object p1

    return-object p1
.end method

.method public getType()I
    .locals 1

    const/4 v0, 0x4

    return v0
.end method

.method public setOpcode(I)V
    .locals 0

    .line 78
    iput p1, p0, Lgroovyjarjarasm/asm/tree/FieldInsnNode;->opcode:I

    return-void
.end method
