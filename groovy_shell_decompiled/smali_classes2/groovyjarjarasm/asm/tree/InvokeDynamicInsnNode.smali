.class public Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;
.super Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
.source "InvokeDynamicInsnNode.java"


# instance fields
.field public bsm:Lgroovyjarjarasm/asm/Handle;

.field public bsmArgs:[Ljava/lang/Object;

.field public desc:Ljava/lang/String;

.field public name:Ljava/lang/String;


# direct methods
.method public varargs constructor <init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V
    .locals 1

    const/16 v0, 0xba

    .line 70
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;-><init>(I)V

    .line 71
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->name:Ljava/lang/String;

    .line 72
    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->desc:Ljava/lang/String;

    .line 73
    iput-object p3, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->bsm:Lgroovyjarjarasm/asm/Handle;

    .line 74
    iput-object p4, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->bsmArgs:[Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 4

    .line 84
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->name:Ljava/lang/String;

    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->desc:Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->bsm:Lgroovyjarjarasm/asm/Handle;

    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->bsmArgs:[Ljava/lang/Object;

    invoke-virtual {p1, v0, v1, v2, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    .line 85
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->acceptAnnotations(Lgroovyjarjarasm/asm/MethodVisitor;)V

    return-void
.end method

.method public clone(Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            ">;)",
            "Lgroovyjarjarasm/asm/tree/AbstractInsnNode;"
        }
    .end annotation

    .line 90
    new-instance p1, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;

    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->name:Ljava/lang/String;

    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->desc:Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->bsm:Lgroovyjarjarasm/asm/Handle;

    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->bsmArgs:[Ljava/lang/Object;

    invoke-direct {p1, v0, v1, v2, v3}, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    invoke-virtual {p1, p0}, Lgroovyjarjarasm/asm/tree/InvokeDynamicInsnNode;->cloneAnnotations(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object p1

    return-object p1
.end method

.method public getType()I
    .locals 1

    const/4 v0, 0x6

    return v0
.end method
