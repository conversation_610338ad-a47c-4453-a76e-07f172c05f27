.class public Lgroovyjarjarasm/asm/tree/ClassNode;
.super Lgroovyjarjarasm/asm/ClassVisitor;
.source "ClassNode.java"


# instance fields
.field public access:I

.field public attrs:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/Attribute;",
            ">;"
        }
    .end annotation
.end field

.field public fields:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/FieldNode;",
            ">;"
        }
    .end annotation
.end field

.field public innerClasses:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/InnerClassNode;",
            ">;"
        }
    .end annotation
.end field

.field public interfaces:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public invisibleAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/AnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public invisibleTypeAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public methods:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/MethodNode;",
            ">;"
        }
    .end annotation
.end field

.field public module:Lgroovyjarjarasm/asm/tree/ModuleNode;

.field public name:Ljava/lang/String;

.field public nestHostClass:Ljava/lang/String;

.field public nestMembers:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public outerClass:Ljava/lang/String;

.field public outerMethod:Ljava/lang/String;

.field public outerMethodDesc:Ljava/lang/String;

.field public permittedSubclasses:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public recordComponents:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/RecordComponentNode;",
            ">;"
        }
    .end annotation
.end field

.field public signature:Ljava/lang/String;

.field public sourceDebug:Ljava/lang/String;

.field public sourceFile:Ljava/lang/String;

.field public superName:Ljava/lang/String;

.field public version:I

.field public visibleAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/AnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public visibleTypeAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 2

    const/high16 v0, 0x90000

    .line 166
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/tree/ClassNode;-><init>(I)V

    .line 167
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovyjarjarasm/asm/tree/ClassNode;

    if-ne v0, v1, :cond_0

    return-void

    .line 168
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method

.method public constructor <init>(I)V
    .locals 0

    .line 179
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/ClassVisitor;-><init>(I)V

    .line 180
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->interfaces:Ljava/util/List;

    .line 181
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->innerClasses:Ljava/util/List;

    .line 182
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->fields:Ljava/util/List;

    .line 183
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->methods:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/ClassVisitor;)V
    .locals 8

    .line 386
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->interfaces:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    new-array v7, v0, [Ljava/lang/String;

    .line 387
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->interfaces:Ljava/util/List;

    invoke-interface {v0, v7}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    .line 388
    iget v2, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->version:I

    iget v3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->access:I

    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->name:Ljava/lang/String;

    iget-object v5, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->signature:Ljava/lang/String;

    iget-object v6, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->superName:Ljava/lang/String;

    move-object v1, p1

    invoke-virtual/range {v1 .. v7}, Lgroovyjarjarasm/asm/ClassVisitor;->visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    .line 390
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->sourceFile:Ljava/lang/String;

    if-nez v0, :cond_0

    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->sourceDebug:Ljava/lang/String;

    if-eqz v1, :cond_1

    .line 391
    :cond_0
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->sourceDebug:Ljava/lang/String;

    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/ClassVisitor;->visitSource(Ljava/lang/String;Ljava/lang/String;)V

    .line 394
    :cond_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->module:Lgroovyjarjarasm/asm/tree/ModuleNode;

    if-eqz v0, :cond_2

    .line 395
    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/tree/ModuleNode;->accept(Lgroovyjarjarasm/asm/ClassVisitor;)V

    .line 398
    :cond_2
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->nestHostClass:Ljava/lang/String;

    if-eqz v0, :cond_3

    .line 399
    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/ClassVisitor;->visitNestHost(Ljava/lang/String;)V

    .line 402
    :cond_3
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->outerClass:Ljava/lang/String;

    if-eqz v0, :cond_4

    .line 403
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->outerMethod:Ljava/lang/String;

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->outerMethodDesc:Ljava/lang/String;

    invoke-virtual {p1, v0, v1, v2}, Lgroovyjarjarasm/asm/ClassVisitor;->visitOuterClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 406
    :cond_4
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleAnnotations:Ljava/util/List;

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_5

    .line 407
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v2

    :goto_0
    if-ge v3, v0, :cond_5

    .line 408
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleAnnotations:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    .line 409
    iget-object v5, v4, Lgroovyjarjarasm/asm/tree/AnnotationNode;->desc:Ljava/lang/String;

    invoke-virtual {p1, v5, v1}, Lgroovyjarjarasm/asm/ClassVisitor;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v5

    invoke-virtual {v4, v5}, Lgroovyjarjarasm/asm/tree/AnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 412
    :cond_5
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleAnnotations:Ljava/util/List;

    if-eqz v0, :cond_6

    .line 413
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v2

    :goto_1
    if-ge v3, v0, :cond_6

    .line 414
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleAnnotations:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    .line 415
    iget-object v5, v4, Lgroovyjarjarasm/asm/tree/AnnotationNode;->desc:Ljava/lang/String;

    invoke-virtual {p1, v5, v2}, Lgroovyjarjarasm/asm/ClassVisitor;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v5

    invoke-virtual {v4, v5}, Lgroovyjarjarasm/asm/tree/AnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 418
    :cond_6
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_7

    .line 419
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v3, v2

    :goto_2
    if-ge v3, v0, :cond_7

    .line 420
    iget-object v4, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    .line 421
    iget v5, v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typeRef:I

    iget-object v6, v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typePath:Lgroovyjarjarasm/asm/TypePath;

    iget-object v7, v4, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->desc:Ljava/lang/String;

    .line 422
    invoke-virtual {p1, v5, v6, v7, v1}, Lgroovyjarjarasm/asm/ClassVisitor;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v5

    .line 421
    invoke-virtual {v4, v5}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    .line 426
    :cond_7
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_8

    .line 427
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v1, v2

    :goto_3
    if-ge v1, v0, :cond_8

    .line 428
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    .line 429
    iget v4, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typeRef:I

    iget-object v5, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typePath:Lgroovyjarjarasm/asm/TypePath;

    iget-object v6, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->desc:Ljava/lang/String;

    .line 430
    invoke-virtual {p1, v4, v5, v6, v2}, Lgroovyjarjarasm/asm/ClassVisitor;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v4

    .line 429
    invoke-virtual {v3, v4}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    .line 435
    :cond_8
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->attrs:Ljava/util/List;

    if-eqz v0, :cond_9

    .line 436
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v1, v2

    :goto_4
    if-ge v1, v0, :cond_9

    .line 437
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->attrs:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarasm/asm/Attribute;

    invoke-virtual {p1, v3}, Lgroovyjarjarasm/asm/ClassVisitor;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_4

    .line 441
    :cond_9
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->nestMembers:Ljava/util/List;

    if-eqz v0, :cond_a

    .line 442
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v1, v2

    :goto_5
    if-ge v1, v0, :cond_a

    .line 443
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->nestMembers:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {p1, v3}, Lgroovyjarjarasm/asm/ClassVisitor;->visitNestMember(Ljava/lang/String;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_5

    .line 447
    :cond_a
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->permittedSubclasses:Ljava/util/List;

    if-eqz v0, :cond_b

    .line 448
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v1, v2

    :goto_6
    if-ge v1, v0, :cond_b

    .line 449
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->permittedSubclasses:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {p1, v3}, Lgroovyjarjarasm/asm/ClassVisitor;->visitPermittedSubclass(Ljava/lang/String;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_6

    .line 453
    :cond_b
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->innerClasses:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v1, v2

    :goto_7
    if-ge v1, v0, :cond_c

    .line 454
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->innerClasses:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarasm/asm/tree/InnerClassNode;

    invoke-virtual {v3, p1}, Lgroovyjarjarasm/asm/tree/InnerClassNode;->accept(Lgroovyjarjarasm/asm/ClassVisitor;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_7

    .line 457
    :cond_c
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->recordComponents:Ljava/util/List;

    if-eqz v0, :cond_d

    .line 458
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v1, v2

    :goto_8
    if-ge v1, v0, :cond_d

    .line 459
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->recordComponents:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarasm/asm/tree/RecordComponentNode;

    invoke-virtual {v3, p1}, Lgroovyjarjarasm/asm/tree/RecordComponentNode;->accept(Lgroovyjarjarasm/asm/ClassVisitor;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_8

    .line 463
    :cond_d
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->fields:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v1, v2

    :goto_9
    if-ge v1, v0, :cond_e

    .line 464
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->fields:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarasm/asm/tree/FieldNode;

    invoke-virtual {v3, p1}, Lgroovyjarjarasm/asm/tree/FieldNode;->accept(Lgroovyjarjarasm/asm/ClassVisitor;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_9

    .line 467
    :cond_e
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->methods:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    :goto_a
    if-ge v2, v0, :cond_f

    .line 468
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->methods:Ljava/util/List;

    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/tree/MethodNode;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/tree/MethodNode;->accept(Lgroovyjarjarasm/asm/ClassVisitor;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_a

    .line 470
    :cond_f
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ClassVisitor;->visitEnd()V

    return-void
.end method

.method public check(I)V
    .locals 2

    const/high16 v0, 0x90000

    if-ge p1, v0, :cond_1

    .line 325
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->permittedSubclasses:Ljava/util/List;

    if-nez v0, :cond_0

    goto :goto_0

    .line 326
    :cond_0
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    :cond_1
    :goto_0
    const/high16 v0, 0x80000

    if-ge p1, v0, :cond_3

    .line 328
    iget v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->access:I

    const/high16 v1, 0x10000

    and-int/2addr v0, v1

    if-nez v0, :cond_2

    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->recordComponents:Ljava/util/List;

    if-nez v0, :cond_2

    goto :goto_1

    .line 329
    :cond_2
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    :cond_3
    :goto_1
    const/high16 v0, 0x70000

    if-ge p1, v0, :cond_5

    .line 331
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->nestHostClass:Ljava/lang/String;

    if-nez v0, :cond_4

    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->nestMembers:Ljava/util/List;

    if-nez v0, :cond_4

    goto :goto_2

    .line 332
    :cond_4
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    :cond_5
    :goto_2
    const/high16 v0, 0x60000

    if-ge p1, v0, :cond_7

    .line 334
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->module:Lgroovyjarjarasm/asm/tree/ModuleNode;

    if-nez v0, :cond_6

    goto :goto_3

    .line 335
    :cond_6
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    :cond_7
    :goto_3
    const/high16 v0, 0x50000

    if-ge p1, v0, :cond_b

    .line 338
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_9

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_8

    goto :goto_4

    .line 339
    :cond_8
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 341
    :cond_9
    :goto_4
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_b

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_a

    goto :goto_5

    .line 342
    :cond_a
    new-instance p1, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/tree/UnsupportedClassVersionException;-><init>()V

    throw p1

    .line 346
    :cond_b
    :goto_5
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleAnnotations:Ljava/util/List;

    if-eqz v0, :cond_c

    .line 347
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    :goto_6
    if-ltz v0, :cond_c

    .line 348
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleAnnotations:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/tree/AnnotationNode;->check(I)V

    add-int/lit8 v0, v0, -0x1

    goto :goto_6

    .line 351
    :cond_c
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleAnnotations:Ljava/util/List;

    if-eqz v0, :cond_d

    .line 352
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    :goto_7
    if-ltz v0, :cond_d

    .line 353
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleAnnotations:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/tree/AnnotationNode;->check(I)V

    add-int/lit8 v0, v0, -0x1

    goto :goto_7

    .line 356
    :cond_d
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_e

    .line 357
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    :goto_8
    if-ltz v0, :cond_e

    .line 358
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->check(I)V

    add-int/lit8 v0, v0, -0x1

    goto :goto_8

    .line 361
    :cond_e
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_f

    .line 362
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    :goto_9
    if-ltz v0, :cond_f

    .line 363
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->check(I)V

    add-int/lit8 v0, v0, -0x1

    goto :goto_9

    .line 366
    :cond_f
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->recordComponents:Ljava/util/List;

    if-eqz v0, :cond_10

    .line 367
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    :goto_a
    if-ltz v0, :cond_10

    .line 368
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->recordComponents:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/tree/RecordComponentNode;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/tree/RecordComponentNode;->check(I)V

    add-int/lit8 v0, v0, -0x1

    goto :goto_a

    .line 371
    :cond_10
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->fields:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    :goto_b
    if-ltz v0, :cond_11

    .line 372
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->fields:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/tree/FieldNode;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/tree/FieldNode;->check(I)V

    add-int/lit8 v0, v0, -0x1

    goto :goto_b

    .line 374
    :cond_11
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->methods:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    :goto_c
    if-ltz v0, :cond_12

    .line 375
    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->methods:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/tree/MethodNode;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/tree/MethodNode;->check(I)V

    add-int/lit8 v0, v0, -0x1

    goto :goto_c

    :cond_12
    return-void
.end method

.method public visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 0

    .line 198
    iput p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->version:I

    .line 199
    iput p2, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->access:I

    .line 200
    iput-object p3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->name:Ljava/lang/String;

    .line 201
    iput-object p4, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->signature:Ljava/lang/String;

    .line 202
    iput-object p5, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->superName:Ljava/lang/String;

    .line 203
    invoke-static {p6}, Lgroovyjarjarasm/asm/tree/Util;->asArrayList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->interfaces:Ljava/util/List;

    return-void
.end method

.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 1

    .line 232
    new-instance v0, Lgroovyjarjarasm/asm/tree/AnnotationNode;

    invoke-direct {v0, p1}, Lgroovyjarjarasm/asm/tree/AnnotationNode;-><init>(Ljava/lang/String;)V

    if-eqz p2, :cond_0

    .line 234
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleAnnotations:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleAnnotations:Ljava/util/List;

    goto :goto_0

    .line 236
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleAnnotations:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleAnnotations:Ljava/util/List;

    :goto_0
    return-object v0
.end method

.method public visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 1

    .line 255
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->attrs:Ljava/util/List;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->attrs:Ljava/util/List;

    return-void
.end method

.method public visitEnd()V
    .locals 0

    return-void
.end method

.method public visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/FieldVisitor;
    .locals 7

    .line 290
    new-instance v6, Lgroovyjarjarasm/asm/tree/FieldNode;

    move-object v0, v6

    move v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarasm/asm/tree/FieldNode;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)V

    .line 291
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->fields:Ljava/util/List;

    invoke-interface {p1, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object v6
.end method

.method public visitInnerClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 1

    .line 271
    new-instance v0, Lgroovyjarjarasm/asm/tree/InnerClassNode;

    invoke-direct {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/tree/InnerClassNode;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V

    .line 272
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->innerClasses:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;
    .locals 7

    .line 302
    new-instance v6, Lgroovyjarjarasm/asm/tree/MethodNode;

    move-object v0, v6

    move v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    invoke-direct/range {v0 .. v5}, Lgroovyjarjarasm/asm/tree/MethodNode;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    .line 303
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->methods:Ljava/util/List;

    invoke-interface {p1, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object v6
.end method

.method public visitModule(Ljava/lang/String;ILjava/lang/String;)Lgroovyjarjarasm/asm/ModuleVisitor;
    .locals 1

    .line 214
    new-instance v0, Lgroovyjarjarasm/asm/tree/ModuleNode;

    invoke-direct {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/tree/ModuleNode;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    iput-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->module:Lgroovyjarjarasm/asm/tree/ModuleNode;

    return-object v0
.end method

.method public visitNestHost(Ljava/lang/String;)V
    .locals 0

    .line 220
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->nestHostClass:Ljava/lang/String;

    return-void
.end method

.method public visitNestMember(Ljava/lang/String;)V
    .locals 1

    .line 260
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->nestMembers:Ljava/util/List;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->nestMembers:Ljava/util/List;

    return-void
.end method

.method public visitOuterClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 225
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->outerClass:Ljava/lang/String;

    .line 226
    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->outerMethod:Ljava/lang/String;

    .line 227
    iput-object p3, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->outerMethodDesc:Ljava/lang/String;

    return-void
.end method

.method public visitPermittedSubclass(Ljava/lang/String;)V
    .locals 1

    .line 265
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->permittedSubclasses:Ljava/util/List;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->permittedSubclasses:Ljava/util/List;

    return-void
.end method

.method public visitRecordComponent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/RecordComponentVisitor;
    .locals 1

    .line 278
    new-instance v0, Lgroovyjarjarasm/asm/tree/RecordComponentNode;

    invoke-direct {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/tree/RecordComponentNode;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 279
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->recordComponents:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->recordComponents:Ljava/util/List;

    return-object v0
.end method

.method public visitSource(Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 208
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->sourceFile:Ljava/lang/String;

    .line 209
    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->sourceDebug:Ljava/lang/String;

    return-void
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 1

    .line 244
    new-instance v0, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    invoke-direct {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;-><init>(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;)V

    if-eqz p4, :cond_0

    .line 246
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleTypeAnnotations:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->visibleTypeAnnotations:Ljava/util/List;

    goto :goto_0

    .line 248
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleTypeAnnotations:Ljava/util/List;

    invoke-static {p1, v0}, Lgroovyjarjarasm/asm/tree/Util;->add(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/ClassNode;->invisibleTypeAnnotations:Ljava/util/List;

    :goto_0
    return-object v0
.end method
