.class public Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;
.super Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
.source "MultiANewArrayInsnNode.java"


# instance fields
.field public desc:Ljava/lang/String;

.field public dims:I


# direct methods
.method public constructor <init>(Ljava/lang/String;I)V
    .locals 1

    const/16 v0, 0xc5

    .line 54
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/tree/AbstractInsnNode;-><init>(I)V

    .line 55
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;->desc:Ljava/lang/String;

    .line 56
    iput p2, p0, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;->dims:I

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 2

    .line 66
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;->desc:Ljava/lang/String;

    iget v1, p0, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;->dims:I

    invoke-virtual {p1, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMultiANewArrayInsn(Ljava/lang/String;I)V

    .line 67
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;->acceptAnnotations(Lgroovyjarjarasm/asm/MethodVisitor;)V

    return-void
.end method

.method public clone(Ljava/util/Map;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            "Lgroovyjarjarasm/asm/tree/LabelNode;",
            ">;)",
            "Lgroovyjarjarasm/asm/tree/AbstractInsnNode;"
        }
    .end annotation

    .line 72
    new-instance p1, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;

    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;->desc:Ljava/lang/String;

    iget v1, p0, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;->dims:I

    invoke-direct {p1, v0, v1}, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;-><init>(Ljava/lang/String;I)V

    invoke-virtual {p1, p0}, Lgroovyjarjarasm/asm/tree/MultiANewArrayInsnNode;->cloneAnnotations(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)Lgroovyjarjarasm/asm/tree/AbstractInsnNode;

    move-result-object p1

    return-object p1
.end method

.method public getType()I
    .locals 1

    const/16 v0, 0xd

    return v0
.end method
