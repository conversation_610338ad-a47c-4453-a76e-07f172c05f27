.class public Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;
.super Ljava/lang/Object;
.source "TryCatchBlockNode.java"


# instance fields
.field public end:Lgroovyjarjarasm/asm/tree/LabelNode;

.field public handler:Lgroovyjarjarasm/asm/tree/LabelNode;

.field public invisibleTypeAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;",
            ">;"
        }
    .end annotation
.end field

.field public start:Lgroovyjarjarasm/asm/tree/LabelNode;

.field public type:Ljava/lang/String;

.field public visibleTypeAnnotations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovyjarjarasm/asm/tree/LabelNode;Lgroovyjarjarasm/asm/tree/LabelNode;Lgroovyjarjarasm/asm/tree/LabelNode;Ljava/lang/String;)V
    .locals 0

    .line 74
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 75
    iput-object p1, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->start:Lgroovyjarjarasm/asm/tree/LabelNode;

    .line 76
    iput-object p2, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->end:Lgroovyjarjarasm/asm/tree/LabelNode;

    .line 77
    iput-object p3, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->handler:Lgroovyjarjarasm/asm/tree/LabelNode;

    .line 78
    iput-object p4, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->type:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public accept(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 8

    .line 108
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->start:Lgroovyjarjarasm/asm/tree/LabelNode;

    .line 109
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/tree/LabelNode;->getLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->end:Lgroovyjarjarasm/asm/tree/LabelNode;

    invoke-virtual {v1}, Lgroovyjarjarasm/asm/tree/LabelNode;->getLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v1

    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->handler:Lgroovyjarjarasm/asm/tree/LabelNode;

    if-nez v2, :cond_0

    const/4 v2, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v2}, Lgroovyjarjarasm/asm/tree/LabelNode;->getLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v2

    :goto_0
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->type:Ljava/lang/String;

    .line 108
    invoke-virtual {p1, v0, v1, v2, v3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTryCatchBlock(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Ljava/lang/String;)V

    .line 110
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->visibleTypeAnnotations:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    .line 111
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v2, v1

    :goto_1
    if-ge v2, v0, :cond_1

    .line 112
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->visibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    .line 113
    iget v4, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typeRef:I

    iget-object v5, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typePath:Lgroovyjarjarasm/asm/TypePath;

    iget-object v6, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->desc:Ljava/lang/String;

    const/4 v7, 0x1

    .line 114
    invoke-virtual {p1, v4, v5, v6, v7}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v4

    .line 113
    invoke-virtual {v3, v4}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 118
    :cond_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->invisibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_2

    .line 119
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v2, v1

    :goto_2
    if-ge v2, v0, :cond_2

    .line 120
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->invisibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    .line 121
    iget v4, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typeRef:I

    iget-object v5, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typePath:Lgroovyjarjarasm/asm/TypePath;

    iget-object v6, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->desc:Ljava/lang/String;

    .line 122
    invoke-virtual {p1, v4, v5, v6, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v4

    .line 121
    invoke-virtual {v3, v4}, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->accept(Lgroovyjarjarasm/asm/AnnotationVisitor;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    :cond_2
    return-void
.end method

.method public updateIndex(I)V
    .locals 4

    shl-int/lit8 p1, p1, 0x8

    const/high16 v0, 0x42000000    # 32.0f

    or-int/2addr p1, v0

    .line 90
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->visibleTypeAnnotations:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 91
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_0

    .line 92
    iget-object v3, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->visibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    iput p1, v3, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typeRef:I

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 95
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->invisibleTypeAnnotations:Ljava/util/List;

    if-eqz v0, :cond_1

    .line 96
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    :goto_1
    if-ge v1, v0, :cond_1

    .line 97
    iget-object v2, p0, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->invisibleTypeAnnotations:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;

    iput p1, v2, Lgroovyjarjarasm/asm/tree/TypeAnnotationNode;->typeRef:I

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_1
    return-void
.end method
