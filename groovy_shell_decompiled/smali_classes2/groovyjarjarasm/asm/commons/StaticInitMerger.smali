.class public Lgroovyjarjarasm/asm/commons/StaticInitMerger;
.super Lgroovyjarjarasm/asm/ClassVisitor;
.source "StaticInitMerger.java"


# instance fields
.field private mergedClinitVisitor:Lgroovyjarjarasm/asm/MethodVisitor;

.field private numClinitMethods:I

.field private owner:Ljava/lang/String;

.field private final renamedClinitMethodPrefix:Ljava/lang/String;


# direct methods
.method protected constructor <init>(ILjava/lang/String;Lgroovyjarjarasm/asm/ClassVisitor;)V
    .locals 0

    .line 77
    invoke-direct {p0, p1, p3}, Lgroovyjarjarasm/asm/ClassVisitor;-><init>(ILgroovyjarjarasm/asm/ClassVisitor;)V

    .line 78
    iput-object p2, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->renamedClinitMethodPrefix:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovyjarjarasm/asm/ClassVisitor;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 64
    invoke-direct {p0, v0, p1, p2}, Lgroovyjarjarasm/asm/commons/StaticInitMerger;-><init>(ILjava/lang/String;Lgroovyjarjarasm/asm/ClassVisitor;)V

    return-void
.end method


# virtual methods
.method public visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 0

    .line 89
    invoke-super/range {p0 .. p6}, Lgroovyjarjarasm/asm/ClassVisitor;->visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    .line 90
    iput-object p3, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->owner:Ljava/lang/String;

    return-void
.end method

.method public visitEnd()V
    .locals 2

    .line 118
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->mergedClinitVisitor:Lgroovyjarjarasm/asm/MethodVisitor;

    if-eqz v0, :cond_0

    const/16 v1, 0xb1

    .line 119
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 120
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->mergedClinitVisitor:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v1, 0x0

    invoke-virtual {v0, v1, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 122
    :cond_0
    invoke-super {p0}, Lgroovyjarjarasm/asm/ClassVisitor;->visitEnd()V

    return-void
.end method

.method public visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;
    .locals 9

    const-string v0, "<clinit>"

    .line 101
    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    const/16 v2, 0xa

    .line 103
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->renamedClinitMethodPrefix:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v0, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->numClinitMethods:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->numClinitMethods:I

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const/16 v4, 0xa

    move-object v3, p0

    move-object v5, p1

    move-object v6, p3

    move-object v7, p4

    move-object v8, p5

    .line 104
    invoke-super/range {v3 .. v8}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p4

    .line 106
    iget-object p5, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->mergedClinitVisitor:Lgroovyjarjarasm/asm/MethodVisitor;

    if-nez p5, :cond_0

    const/4 v5, 0x0

    const/4 v6, 0x0

    move-object v1, p0

    move-object v3, p2

    move-object v4, p3

    .line 107
    invoke-super/range {v1 .. v6}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p2

    iput-object p2, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->mergedClinitVisitor:Lgroovyjarjarasm/asm/MethodVisitor;

    .line 109
    :cond_0
    iget-object v3, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->mergedClinitVisitor:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v4, 0xb8

    iget-object v5, p0, Lgroovyjarjarasm/asm/commons/StaticInitMerger;->owner:Ljava/lang/String;

    const/4 v8, 0x0

    move-object v6, p1

    move-object v7, p3

    invoke-virtual/range {v3 .. v8}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    goto :goto_0

    .line 111
    :cond_1
    invoke-super/range {p0 .. p5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p4

    :goto_0
    return-object p4
.end method
