.class public abstract Lgroovyjarjarasm/asm/commons/Remapper;
.super Ljava/lang/Object;
.source "Remapper.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 44
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private mapType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;
    .locals 3

    .line 69
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    packed-switch v0, :pswitch_data_0

    return-object p1

    .line 81
    :pswitch_0
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapMethodDesc(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getMethodType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    return-object p1

    .line 78
    :pswitch_1
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/Remapper;->map(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 79
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    :cond_0
    return-object p1

    .line 71
    :pswitch_2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v1, 0x0

    .line 72
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDimensions()I

    move-result v2

    if-ge v1, v2, :cond_1

    const/16 v2, 0x5b

    .line 73
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 75
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getElementType()Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 76
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    return-object p1

    :pswitch_data_0
    .packed-switch 0x9
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method


# virtual methods
.method protected createRemappingSignatureAdapter(Lgroovyjarjarasm/asm/signature/SignatureVisitor;)Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 0
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 231
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->createSignatureRemapper(Lgroovyjarjarasm/asm/signature/SignatureVisitor;)Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    move-result-object p1

    return-object p1
.end method

.method protected createSignatureRemapper(Lgroovyjarjarasm/asm/signature/SignatureVisitor;)Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 242
    new-instance v0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;

    invoke-direct {v0, p1, p0}, Lgroovyjarjarasm/asm/commons/SignatureRemapper;-><init>(Lgroovyjarjarasm/asm/signature/SignatureVisitor;Lgroovyjarjarasm/asm/commons/Remapper;)V

    return-object v0
.end method

.method public map(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    return-object p1
.end method

.method public mapAnnotationAttributeName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    return-object p2
.end method

.method public mapDesc(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 55
    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public mapFieldName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    return-object p2
.end method

.method public mapInnerClassName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    .line 271
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    .line 273
    invoke-virtual {p2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p3

    :cond_0
    const/16 v0, 0x2f

    .line 276
    invoke-virtual {p1, v0}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v1

    .line 277
    invoke-virtual {p2, v0}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v0

    const/4 v2, -0x1

    if-eq v1, v2, :cond_1

    if-eq v0, v2, :cond_1

    .line 279
    invoke-virtual {p1, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    return-object p3

    :cond_1
    const-string p1, "$"

    .line 286
    invoke-virtual {p2, p1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_3

    const/16 p1, 0x24

    .line 287
    invoke-virtual {p2, p1}, Ljava/lang/String;->lastIndexOf(I)I

    move-result p1

    :goto_0
    add-int/lit8 p1, p1, 0x1

    .line 288
    invoke-virtual {p2}, Ljava/lang/String;->length()I

    move-result p3

    if-ge p1, p3, :cond_2

    .line 289
    invoke-virtual {p2, p1}, Ljava/lang/String;->charAt(I)C

    move-result p3

    invoke-static {p3}, Ljava/lang/Character;->isDigit(C)Z

    move-result p3

    if-eqz p3, :cond_2

    goto :goto_0

    .line 292
    :cond_2
    invoke-virtual {p2, p1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_3
    return-object p3
.end method

.method public mapInvokeDynamicMethodName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    return-object p1
.end method

.method public mapMethodDesc(Ljava/lang/String;)Ljava/lang/String;
    .locals 5

    const-string v0, "()V"

    .line 134
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object p1

    .line 138
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "("

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 139
    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object v1

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    .line 140
    invoke-direct {p0, v4}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;

    move-result-object v4

    invoke-virtual {v4}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 142
    :cond_1
    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getReturnType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    .line 143
    sget-object v1, Lgroovyjarjarasm/asm/Type;->VOID_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p1, v1, :cond_2

    const-string p1, ")V"

    .line 144
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    :cond_2
    const/16 v1, 0x29

    .line 146
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 148
    :goto_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public mapMethodName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    return-object p2
.end method

.method public mapModuleName(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    return-object p1
.end method

.method public mapPackageName(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    return-object p1
.end method

.method public mapRecordComponentName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    return-object p2
.end method

.method public mapSignature(Ljava/lang/String;Z)Ljava/lang/String;
    .locals 2

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 209
    :cond_0
    new-instance v0, Lgroovyjarjarasm/asm/signature/SignatureReader;

    invoke-direct {v0, p1}, Lgroovyjarjarasm/asm/signature/SignatureReader;-><init>(Ljava/lang/String;)V

    .line 210
    new-instance p1, Lgroovyjarjarasm/asm/signature/SignatureWriter;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/signature/SignatureWriter;-><init>()V

    .line 211
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->createSignatureRemapper(Lgroovyjarjarasm/asm/signature/SignatureVisitor;)Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    move-result-object v1

    if-eqz p2, :cond_1

    .line 213
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/signature/SignatureReader;->acceptType(Lgroovyjarjarasm/asm/signature/SignatureVisitor;)V

    goto :goto_0

    .line 215
    :cond_1
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/signature/SignatureReader;->accept(Lgroovyjarjarasm/asm/signature/SignatureVisitor;)V

    .line 217
    :goto_0
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/signature/SignatureWriter;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public mapType(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 99
    :cond_0
    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public mapTypes([Ljava/lang/String;)[Ljava/lang/String;
    .locals 3

    const/4 v0, 0x0

    const/4 v1, 0x0

    .line 112
    :goto_0
    array-length v2, p1

    if-ge v1, v2, :cond_2

    .line 113
    aget-object v2, p1, v1

    .line 114
    invoke-virtual {p0, v2}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_1

    if-nez v0, :cond_0

    .line 117
    invoke-virtual {p1}, [Ljava/lang/String;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/lang/String;

    .line 119
    :cond_0
    aput-object v2, v0, v1

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    if-eqz v0, :cond_3

    move-object p1, v0

    :cond_3
    return-object p1
.end method

.method public mapValue(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 162
    instance-of v0, p1, Lgroovyjarjarasm/asm/Type;

    if-eqz v0, :cond_0

    .line 163
    check-cast p1, Lgroovyjarjarasm/asm/Type;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    return-object p1

    .line 165
    :cond_0
    instance-of v0, p1, Lgroovyjarjarasm/asm/Handle;

    const/4 v1, 0x0

    if-eqz v0, :cond_4

    .line 166
    check-cast p1, Lgroovyjarjarasm/asm/Handle;

    .line 167
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getTag()I

    move-result v0

    const/4 v2, 0x4

    if-gt v0, v2, :cond_1

    const/4 v1, 0x1

    .line 169
    :cond_1
    new-instance v0, Lgroovyjarjarasm/asm/Handle;

    .line 170
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getTag()I

    move-result v3

    .line 171
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getOwner()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p0, v2}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    if-eqz v1, :cond_2

    .line 173
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getOwner()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getDesc()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p0, v2, v5, v6}, Lgroovyjarjarasm/asm/commons/Remapper;->mapFieldName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    goto :goto_0

    .line 174
    :cond_2
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getOwner()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getDesc()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p0, v2, v5, v6}, Lgroovyjarjarasm/asm/commons/Remapper;->mapMethodName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    :goto_0
    move-object v5, v2

    if-eqz v1, :cond_3

    .line 175
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getDesc()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapDesc(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    :cond_3
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getDesc()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapMethodDesc(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    :goto_1
    move-object v6, v1

    .line 176
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->isInterface()Z

    move-result v7

    move-object v2, v0

    invoke-direct/range {v2 .. v7}, Lgroovyjarjarasm/asm/Handle;-><init>(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-object v0

    .line 178
    :cond_4
    instance-of v0, p1, Lgroovyjarjarasm/asm/ConstantDynamic;

    if-eqz v0, :cond_6

    .line 179
    check-cast p1, Lgroovyjarjarasm/asm/ConstantDynamic;

    .line 180
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getBootstrapMethodArgumentCount()I

    move-result v0

    .line 181
    new-array v2, v0, [Ljava/lang/Object;

    :goto_2
    if-ge v1, v0, :cond_5

    .line 184
    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getBootstrapMethodArgument(I)Ljava/lang/Object;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovyjarjarasm/asm/commons/Remapper;->mapValue(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    aput-object v3, v2, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_2

    .line 186
    :cond_5
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getDescriptor()Ljava/lang/String;

    move-result-object v0

    .line 187
    new-instance v1, Lgroovyjarjarasm/asm/ConstantDynamic;

    .line 188
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3, v0}, Lgroovyjarjarasm/asm/commons/Remapper;->mapInvokeDynamicMethodName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    .line 189
    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/Remapper;->mapDesc(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 190
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getBootstrapMethod()Lgroovyjarjarasm/asm/Handle;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapValue(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarasm/asm/Handle;

    invoke-direct {v1, v3, v0, p1, v2}, Lgroovyjarjarasm/asm/ConstantDynamic;-><init>(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    return-object v1

    :cond_6
    return-object p1
.end method
