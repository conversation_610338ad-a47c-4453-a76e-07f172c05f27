.class public final Lgroovyjarjarasm/asm/commons/ModuleResolutionAttribute;
.super Lgroovyjarjarasm/asm/Attribute;
.source "ModuleResolutionAttribute.java"


# static fields
.field public static final RESOLUTION_DO_NOT_RESOLVE_BY_DEFAULT:I = 0x1

.field public static final RESOLUTION_WARN_DEPRECATED:I = 0x2

.field public static final RESOLUTION_WARN_DEPRECATED_FOR_REMOVAL:I = 0x4

.field public static final RESOLUTION_WARN_INCUBATING:I = 0x8


# instance fields
.field public resolution:I


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 88
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/commons/ModuleResolutionAttribute;-><init>(I)V

    return-void
.end method

.method public constructor <init>(I)V
    .locals 1

    const-string v0, "ModuleResolution"

    .line 79
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/Attribute;-><init>(Ljava/lang/String;)V

    .line 80
    iput p1, p0, Lgroovyjarjarasm/asm/commons/ModuleResolutionAttribute;->resolution:I

    return-void
.end method


# virtual methods
.method protected read(Lgroovyjarjarasm/asm/ClassReader;II[CI[Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/Attribute;
    .locals 0

    .line 99
    new-instance p3, Lgroovyjarjarasm/asm/commons/ModuleResolutionAttribute;

    invoke-virtual {p1, p2}, Lgroovyjarjarasm/asm/ClassReader;->readUnsignedShort(I)I

    move-result p1

    invoke-direct {p3, p1}, Lgroovyjarjarasm/asm/commons/ModuleResolutionAttribute;-><init>(I)V

    return-object p3
.end method

.method protected write(Lgroovyjarjarasm/asm/ClassWriter;[BIII)Lgroovyjarjarasm/asm/ByteVector;
    .locals 0

    .line 109
    new-instance p1, Lgroovyjarjarasm/asm/ByteVector;

    invoke-direct {p1}, Lgroovyjarjarasm/asm/ByteVector;-><init>()V

    .line 110
    iget p2, p0, Lgroovyjarjarasm/asm/commons/ModuleResolutionAttribute;->resolution:I

    invoke-virtual {p1, p2}, Lgroovyjarjarasm/asm/ByteVector;->putShort(I)Lgroovyjarjarasm/asm/ByteVector;

    return-object p1
.end method
