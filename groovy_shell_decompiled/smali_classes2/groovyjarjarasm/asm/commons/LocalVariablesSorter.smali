.class public Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;
.super Lgroovyjarjarasm/asm/MethodVisitor;
.source "LocalVariablesSorter.java"


# static fields
.field private static final OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;


# instance fields
.field protected final firstLocal:I

.field protected nextLocal:I

.field private remappedLocalTypes:[Ljava/lang/Object;

.field private remappedVariableIndices:[I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, "java/lang/Object"

    .line 51
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    return-void
.end method

.method protected constructor <init>(IILjava/lang/String;Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 1

    .line 101
    invoke-direct {p0, p1, p4}, Lgroovyjarjarasm/asm/MethodVisitor;-><init>(ILgroovyjarjarasm/asm/MethodVisitor;)V

    const/16 p1, 0x28

    new-array p1, p1, [I

    .line 58
    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedVariableIndices:[I

    const/16 p1, 0x14

    new-array p1, p1, [Ljava/lang/Object;

    .line 64
    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    and-int/lit8 p1, p2, 0x8

    const/4 p2, 0x0

    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    move p1, p2

    .line 102
    :goto_0
    iput p1, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->nextLocal:I

    .line 103
    invoke-static {p3}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    array-length p3, p1

    :goto_1
    if-ge p2, p3, :cond_1

    aget-object p4, p1, p2

    .line 104
    iget v0, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->nextLocal:I

    invoke-virtual {p4}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result p4

    add-int/2addr v0, p4

    iput v0, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->nextLocal:I

    add-int/lit8 p2, p2, 0x1

    goto :goto_1

    .line 106
    :cond_1
    iget p1, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->nextLocal:I

    iput p1, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->firstLocal:I

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 84
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;-><init>(IILjava/lang/String;Lgroovyjarjarasm/asm/MethodVisitor;)V

    .line 85
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    const-class p2, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;

    if-ne p1, p2, :cond_0

    return-void

    .line 86
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
.end method

.method private remap(ILgroovyjarjarasm/asm/Type;)I
    .locals 4

    .line 325
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result v0

    add-int/2addr v0, p1

    iget v1, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->firstLocal:I

    if-gt v0, v1, :cond_0

    return p1

    :cond_0
    mul-int/lit8 p1, p1, 0x2

    .line 328
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result v0

    add-int/2addr p1, v0

    add-int/lit8 p1, p1, -0x1

    .line 329
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedVariableIndices:[I

    array-length v0, v0

    if-lt p1, v0, :cond_1

    mul-int/lit8 v1, v0, 0x2

    add-int/lit8 v2, p1, 0x1

    .line 331
    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    move-result v1

    new-array v1, v1, [I

    .line 332
    iget-object v2, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedVariableIndices:[I

    const/4 v3, 0x0

    invoke-static {v2, v3, v1, v3, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 333
    iput-object v1, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedVariableIndices:[I

    .line 335
    :cond_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedVariableIndices:[I

    aget v0, v0, p1

    if-nez v0, :cond_2

    .line 337
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->newLocalMapping(Lgroovyjarjarasm/asm/Type;)I

    move-result v0

    .line 338
    invoke-virtual {p0, v0, p2}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->setLocalType(ILgroovyjarjarasm/asm/Type;)V

    .line 339
    iget-object p2, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedVariableIndices:[I

    add-int/lit8 v1, v0, 0x1

    aput v1, p2, p1

    goto :goto_0

    :cond_2
    add-int/lit8 v0, v0, -0x1

    :goto_0
    return v0
.end method

.method private setFrameLocal(ILjava/lang/Object;)V
    .locals 4

    .line 315
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    array-length v0, v0

    if-lt p1, v0, :cond_0

    mul-int/lit8 v1, v0, 0x2

    add-int/lit8 v2, p1, 0x1

    .line 317
    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    move-result v1

    new-array v1, v1, [Ljava/lang/Object;

    .line 318
    iget-object v2, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    const/4 v3, 0x0

    invoke-static {v2, v3, v1, v3, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 319
    iput-object v1, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    .line 321
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    aput-object p2, v0, p1

    return-void
.end method


# virtual methods
.method public newLocal(Lgroovyjarjarasm/asm/Type;)I
    .locals 2

    .line 253
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    packed-switch v0, :pswitch_data_0

    .line 277
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 274
    :pswitch_0
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    .line 271
    :pswitch_1
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    .line 268
    :pswitch_2
    sget-object v0, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    goto :goto_0

    .line 265
    :pswitch_3
    sget-object v0, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    goto :goto_0

    .line 262
    :pswitch_4
    sget-object v0, Lgroovyjarjarasm/asm/Opcodes;->FLOAT:Ljava/lang/Integer;

    goto :goto_0

    .line 259
    :pswitch_5
    sget-object v0, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    .line 279
    :goto_0
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->newLocalMapping(Lgroovyjarjarasm/asm/Type;)I

    move-result v1

    .line 280
    invoke-virtual {p0, v1, p1}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->setLocalType(ILgroovyjarjarasm/asm/Type;)V

    .line 281
    invoke-direct {p0, v1, v0}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->setFrameLocal(ILjava/lang/Object;)V

    return v1

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method protected newLocalMapping(Lgroovyjarjarasm/asm/Type;)I
    .locals 1

    .line 347
    iget v0, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->nextLocal:I

    .line 348
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result p1

    add-int/2addr p1, v0

    iput p1, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->nextLocal:I

    return v0
.end method

.method protected setLocalType(ILgroovyjarjarasm/asm/Type;)V
    .locals 0

    return-void
.end method

.method protected updateNewLocals([Ljava/lang/Object;)V
    .locals 0

    return-void
.end method

.method public visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V
    .locals 12

    move-object v6, p0

    const/4 v0, -0x1

    move v1, p1

    if-ne v1, v0, :cond_d

    .line 193
    iget-object v0, v6, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    array-length v2, v0

    new-array v7, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    .line 194
    invoke-static {v0, v3, v7, v3, v2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 196
    iget-object v0, v6, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->updateNewLocals([Ljava/lang/Object;)V

    move v0, v3

    move v2, v0

    :goto_0
    const/4 v4, 0x2

    const/4 v5, 0x1

    move v8, p2

    if-ge v0, v8, :cond_8

    .line 202
    aget-object v9, p3, v0

    .line 203
    sget-object v10, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    if-eq v9, v10, :cond_5

    .line 204
    sget-object v10, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    .line 205
    sget-object v11, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    if-ne v9, v11, :cond_0

    .line 206
    sget-object v10, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    goto :goto_1

    .line 207
    :cond_0
    sget-object v11, Lgroovyjarjarasm/asm/Opcodes;->FLOAT:Ljava/lang/Integer;

    if-ne v9, v11, :cond_1

    .line 208
    sget-object v10, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    goto :goto_1

    .line 209
    :cond_1
    sget-object v11, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    if-ne v9, v11, :cond_2

    .line 210
    sget-object v10, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    goto :goto_1

    .line 211
    :cond_2
    sget-object v11, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    if-ne v9, v11, :cond_3

    .line 212
    sget-object v10, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    goto :goto_1

    .line 213
    :cond_3
    instance-of v11, v9, Ljava/lang/String;

    if-eqz v11, :cond_4

    .line 214
    move-object v10, v9

    check-cast v10, Ljava/lang/String;

    invoke-static {v10}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v10

    .line 216
    :cond_4
    :goto_1
    invoke-direct {p0, v2, v10}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remap(ILgroovyjarjarasm/asm/Type;)I

    move-result v10

    invoke-direct {p0, v10, v9}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->setFrameLocal(ILjava/lang/Object;)V

    .line 218
    :cond_5
    sget-object v10, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    if-eq v9, v10, :cond_7

    sget-object v10, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    if-ne v9, v10, :cond_6

    goto :goto_2

    :cond_6
    move v4, v5

    :cond_7
    :goto_2
    add-int/2addr v2, v4

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_8
    move v0, v3

    :goto_3
    move v2, v0

    .line 225
    :goto_4
    iget-object v8, v6, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    array-length v9, v8

    if-ge v3, v9, :cond_c

    .line 226
    aget-object v8, v8, v3

    .line 227
    sget-object v9, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    if-eq v8, v9, :cond_a

    sget-object v9, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    if-ne v8, v9, :cond_9

    goto :goto_5

    :cond_9
    move v9, v5

    goto :goto_6

    :cond_a
    :goto_5
    move v9, v4

    :goto_6
    add-int/2addr v3, v9

    if-eqz v8, :cond_b

    .line 228
    sget-object v9, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    if-eq v8, v9, :cond_b

    .line 229
    iget-object v2, v6, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    add-int/lit8 v9, v0, 0x1

    aput-object v8, v2, v0

    move v0, v9

    goto :goto_3

    .line 232
    :cond_b
    iget-object v8, v6, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    add-int/lit8 v9, v0, 0x1

    sget-object v10, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    aput-object v10, v8, v0

    move v0, v9

    goto :goto_4

    :cond_c
    move-object v0, p0

    move v1, p1

    move-object v3, v8

    move/from16 v4, p4

    move-object/from16 v5, p5

    .line 237
    invoke-super/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V

    .line 240
    iput-object v7, v6, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remappedLocalTypes:[Ljava/lang/Object;

    return-void

    .line 188
    :cond_d
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "LocalVariablesSorter only accepts expanded frames (see ClassReader.EXPAND_FRAMES)"

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public visitIincInsn(II)V
    .locals 1

    .line 142
    sget-object v0, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-direct {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remap(ILgroovyjarjarasm/asm/Type;)I

    move-result p1

    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIincInsn(II)V

    return-void
.end method

.method public visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V
    .locals 8

    .line 158
    invoke-static {p2}, Lgroovyjarjarasm/asm/Type;->getType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    invoke-direct {p0, p6, v0}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remap(ILgroovyjarjarasm/asm/Type;)I

    move-result v7

    move-object v1, p0

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move-object v5, p4

    move-object v6, p5

    .line 159
    invoke-super/range {v1 .. v7}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V

    return-void
.end method

.method public visitLocalVariableAnnotation(ILgroovyjarjarasm/asm/TypePath;[Lgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;[ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 12

    move-object/from16 v0, p5

    .line 171
    invoke-static/range {p6 .. p6}, Lgroovyjarjarasm/asm/Type;->getType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v1

    .line 172
    array-length v2, v0

    new-array v8, v2, [I

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_0

    .line 174
    aget v4, v0, v3

    move-object v11, p0

    invoke-direct {p0, v4, v1}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remap(ILgroovyjarjarasm/asm/Type;)I

    move-result v4

    aput v4, v8, v3

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    move-object v11, p0

    move-object v3, p0

    move v4, p1

    move-object v5, p2

    move-object v6, p3

    move-object/from16 v7, p4

    move-object/from16 v9, p6

    move/from16 v10, p7

    .line 176
    invoke-super/range {v3 .. v10}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLocalVariableAnnotation(ILgroovyjarjarasm/asm/TypePath;[Lgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;[ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v0

    return-object v0
.end method

.method public visitMaxs(II)V
    .locals 0

    .line 147
    iget p2, p0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->nextLocal:I

    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    return-void
.end method

.method public visitVarInsn(II)V
    .locals 2

    const/16 v0, 0xa9

    if-eq p1, v0, :cond_0

    packed-switch p1, :pswitch_data_0

    packed-switch p1, :pswitch_data_1

    .line 135
    new-instance p2, Ljava/lang/IllegalArgumentException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Invalid opcode "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p2

    .line 119
    :pswitch_0
    sget-object v0, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    goto :goto_0

    .line 123
    :pswitch_1
    sget-object v0, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    goto :goto_0

    .line 115
    :pswitch_2
    sget-object v0, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    goto :goto_0

    .line 127
    :pswitch_3
    sget-object v0, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    goto :goto_0

    .line 132
    :cond_0
    :pswitch_4
    sget-object v0, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    .line 137
    :goto_0
    invoke-direct {p0, p2, v0}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;->remap(ILgroovyjarjarasm/asm/Type;)I

    move-result p2

    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x15
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
        :pswitch_4
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x36
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
        :pswitch_4
    .end packed-switch
.end method
