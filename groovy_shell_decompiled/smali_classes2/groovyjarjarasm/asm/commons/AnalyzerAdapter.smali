.class public Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;
.super Lgroovyjarjarasm/asm/MethodVisitor;
.source "AnalyzerAdapter.java"


# instance fields
.field private labels:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/Label;",
            ">;"
        }
    .end annotation
.end field

.field public locals:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private maxLocals:I

.field private maxStack:I

.field private owner:Ljava/lang/String;

.field public stack:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public uninitializedTypes:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method protected constructor <init>(ILjava/lang/String;ILjava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 0

    .line 146
    invoke-direct {p0, p1, p6}, Lgroovyjarjarasm/asm/MethodVisitor;-><init>(ILgroovyjarjarasm/asm/MethodVisitor;)V

    .line 147
    iput-object p2, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->owner:Ljava/lang/String;

    .line 148
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    .line 149
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    .line 150
    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->uninitializedTypes:Ljava/util/Map;

    and-int/lit8 p1, p3, 0x8

    if-nez p1, :cond_1

    const-string p1, "<init>"

    .line 153
    invoke-virtual {p1, p4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 154
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    sget-object p2, Lgroovyjarjarasm/asm/Opcodes;->UNINITIALIZED_THIS:Ljava/lang/Integer;

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 156
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 159
    :cond_1
    :goto_0
    invoke-static {p5}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    array-length p2, p1

    const/4 p3, 0x0

    :goto_1
    if-ge p3, p2, :cond_2

    aget-object p4, p1, p3

    .line 160
    invoke-virtual {p4}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result p5

    packed-switch p5, :pswitch_data_0

    .line 186
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 183
    :pswitch_0
    iget-object p5, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-virtual {p4}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p4

    invoke-interface {p5, p4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 180
    :pswitch_1
    iget-object p5, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-virtual {p4}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p4

    invoke-interface {p5, p4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 176
    :pswitch_2
    iget-object p4, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    sget-object p5, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    invoke-interface {p4, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 177
    iget-object p4, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    sget-object p5, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-interface {p4, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 172
    :pswitch_3
    iget-object p4, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    sget-object p5, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    invoke-interface {p4, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 173
    iget-object p4, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    sget-object p5, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-interface {p4, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 169
    :pswitch_4
    iget-object p4, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    sget-object p5, Lgroovyjarjarasm/asm/Opcodes;->FLOAT:Ljava/lang/Integer;

    invoke-interface {p4, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 166
    :pswitch_5
    iget-object p4, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    sget-object p5, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    invoke-interface {p4, p5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :goto_2
    add-int/lit8 p3, p3, 0x1

    goto :goto_1

    .line 189
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result p1

    iput p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public constructor <init>(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 7

    const/high16 v1, 0x90000

    move-object v0, p0

    move-object v2, p1

    move v3, p2

    move-object v4, p3

    move-object v5, p4

    move-object v6, p5

    .line 121
    invoke-direct/range {v0 .. v6}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;-><init>(ILjava/lang/String;ILjava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/MethodVisitor;)V

    .line 122
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    const-class p2, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;

    if-ne p1, p2, :cond_0

    return-void

    .line 123
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
.end method

.method private execute(IILjava/lang/String;)V
    .locals 5

    const/16 v0, 0xa8

    if-eq p1, v0, :cond_6

    const/16 v0, 0xa9

    if-eq p1, v0, :cond_6

    .line 545
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 546
    iput-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    return-void

    :cond_0
    packed-switch p1, :pswitch_data_0

    packed-switch p1, :pswitch_data_1

    const/4 v0, 0x2

    const/4 v2, 0x1

    packed-switch p1, :pswitch_data_2

    const/4 v3, 0x3

    const/4 v4, 0x4

    packed-switch p1, :pswitch_data_3

    packed-switch p1, :pswitch_data_4

    packed-switch p1, :pswitch_data_5

    packed-switch p1, :pswitch_data_6

    .line 906
    new-instance p2, Ljava/lang/IllegalArgumentException;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Invalid opcode "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p2

    .line 902
    :pswitch_0
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 903
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 898
    :pswitch_1
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    .line 899
    invoke-static {p3}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 894
    :pswitch_2
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    .line 895
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "["

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-static {p3}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 863
    :pswitch_3
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    packed-switch p2, :pswitch_data_7

    .line 890
    new-instance p1, Ljava/lang/IllegalArgumentException;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Invalid array type "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :pswitch_4
    const-string p1, "[J"

    .line 887
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    :pswitch_5
    const-string p1, "[I"

    .line 878
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    :pswitch_6
    const-string p1, "[S"

    .line 875
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    :pswitch_7
    const-string p1, "[B"

    .line 872
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    :pswitch_8
    const-string p1, "[D"

    .line 884
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    :pswitch_9
    const-string p1, "[F"

    .line 881
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    :pswitch_a
    const-string p1, "[C"

    .line 869
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    :pswitch_b
    const-string p1, "[Z"

    .line 866
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 860
    :pswitch_c
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    const/4 p2, 0x0

    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 856
    :pswitch_d
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(Ljava/lang/String;)V

    .line 857
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    goto/16 :goto_0

    .line 852
    :pswitch_e
    invoke-direct {p0, v2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 853
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 849
    :pswitch_f
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 846
    :pswitch_10
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 842
    :pswitch_11
    invoke-direct {p0, v4}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 843
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 836
    :pswitch_12
    invoke-direct {p0, v2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 837
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 829
    :pswitch_13
    invoke-direct {p0, v2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 830
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 831
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 824
    :pswitch_14
    invoke-direct {p0, v2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 825
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->FLOAT:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 819
    :pswitch_15
    invoke-direct {p0, v2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 820
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 821
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 815
    :pswitch_16
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    invoke-direct {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->set(ILjava/lang/Object;)V

    goto/16 :goto_0

    .line 810
    :pswitch_17
    invoke-direct {p0, v3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 811
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 812
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 803
    :pswitch_18
    invoke-direct {p0, v4}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 804
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 805
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 783
    :pswitch_19
    invoke-direct {p0, v4}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 784
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 785
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 748
    :pswitch_1a
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 749
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p2

    .line 750
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 751
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 736
    :pswitch_1b
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 737
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p2

    .line 738
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p3

    .line 739
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object v0

    .line 740
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 741
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 742
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 743
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 744
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 745
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 726
    :pswitch_1c
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 727
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p2

    .line 728
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p3

    .line 729
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 730
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 731
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 732
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 733
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 718
    :pswitch_1d
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 719
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p2

    .line 720
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 721
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 722
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 723
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 709
    :pswitch_1e
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 710
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p2

    .line 711
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p3

    .line 712
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 713
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 714
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 715
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 702
    :pswitch_1f
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 703
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p2

    .line 704
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 705
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 706
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 697
    :pswitch_20
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 698
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 699
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 694
    :pswitch_21
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    goto/16 :goto_0

    .line 681
    :pswitch_22
    invoke-direct {p0, v2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    goto/16 :goto_0

    .line 662
    :pswitch_23
    invoke-direct {p0, v4}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    goto/16 :goto_0

    .line 658
    :pswitch_24
    invoke-direct {p0, v3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    goto/16 :goto_0

    .line 641
    :pswitch_25
    invoke-direct {p0, v2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 642
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 643
    invoke-direct {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->set(ILjava/lang/Object;)V

    add-int/lit8 p1, p2, 0x1

    .line 644
    sget-object p3, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->set(ILjava/lang/Object;)V

    if-lez p2, :cond_5

    sub-int/2addr p2, v2

    .line 646
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->get(I)Ljava/lang/Object;

    move-result-object p1

    .line 647
    sget-object p3, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    if-eq p1, p3, :cond_1

    sget-object p3, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    if-ne p1, p3, :cond_5

    .line 648
    :cond_1
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->set(ILjava/lang/Object;)V

    goto/16 :goto_0

    .line 630
    :pswitch_26
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 631
    invoke-direct {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->set(ILjava/lang/Object;)V

    if-lez p2, :cond_5

    sub-int/2addr p2, v2

    .line 633
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->get(I)Ljava/lang/Object;

    move-result-object p1

    .line 634
    sget-object p3, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    if-eq p1, p3, :cond_2

    sget-object p3, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    if-ne p1, p3, :cond_5

    .line 635
    :cond_2
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->set(ILjava/lang/Object;)V

    goto/16 :goto_0

    .line 617
    :pswitch_27
    invoke-direct {p0, v2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 618
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p1

    .line 619
    instance-of p2, p1, Ljava/lang/String;

    if-eqz p2, :cond_3

    .line 620
    check-cast p1, Ljava/lang/String;

    invoke-virtual {p1, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    goto/16 :goto_0

    .line 621
    :cond_3
    sget-object p2, Lgroovyjarjarasm/asm/Opcodes;->NULL:Ljava/lang/Integer;

    if-ne p1, p2, :cond_4

    .line 622
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    :cond_4
    const-string p1, "java/lang/Object"

    .line 624
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_0

    .line 612
    :pswitch_28
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 613
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 614
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 795
    :pswitch_29
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 796
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->FLOAT:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 606
    :pswitch_2a
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 607
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 608
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 772
    :pswitch_2b
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    .line 773
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 601
    :pswitch_2c
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->get(I)Ljava/lang/Object;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 602
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 597
    :pswitch_2d
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->get(I)Ljava/lang/Object;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 591
    :pswitch_2e
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 592
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 587
    :pswitch_2f
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->FLOAT:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 581
    :pswitch_30
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 582
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 577
    :pswitch_31
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 566
    :pswitch_32
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->NULL:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 908
    :cond_5
    :goto_0
    :pswitch_33
    iput-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    return-void

    .line 543
    :cond_6
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "JSR/RET are not supported"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_33
        :pswitch_32
        :pswitch_31
        :pswitch_31
        :pswitch_31
        :pswitch_31
        :pswitch_31
        :pswitch_31
        :pswitch_31
        :pswitch_30
        :pswitch_30
        :pswitch_2f
        :pswitch_2f
        :pswitch_2f
        :pswitch_2e
        :pswitch_2e
        :pswitch_31
        :pswitch_31
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x15
        :pswitch_2d
        :pswitch_2c
        :pswitch_2d
        :pswitch_2c
        :pswitch_2d
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x2e
        :pswitch_2b
        :pswitch_2a
        :pswitch_29
        :pswitch_28
        :pswitch_27
        :pswitch_2b
        :pswitch_2b
        :pswitch_2b
        :pswitch_26
        :pswitch_25
        :pswitch_26
        :pswitch_25
        :pswitch_26
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0x4f
        :pswitch_24
        :pswitch_23
        :pswitch_24
        :pswitch_23
        :pswitch_24
        :pswitch_24
        :pswitch_24
        :pswitch_24
        :pswitch_22
        :pswitch_21
        :pswitch_20
        :pswitch_1f
        :pswitch_1e
        :pswitch_1d
        :pswitch_1c
        :pswitch_1b
        :pswitch_1a
        :pswitch_2b
        :pswitch_19
        :pswitch_29
        :pswitch_18
        :pswitch_2b
        :pswitch_19
        :pswitch_29
        :pswitch_18
        :pswitch_2b
        :pswitch_19
        :pswitch_29
        :pswitch_18
        :pswitch_2b
        :pswitch_19
        :pswitch_29
        :pswitch_18
        :pswitch_2b
        :pswitch_19
        :pswitch_29
        :pswitch_18
        :pswitch_33
        :pswitch_33
        :pswitch_33
        :pswitch_33
        :pswitch_2b
        :pswitch_17
        :pswitch_2b
        :pswitch_17
        :pswitch_2b
        :pswitch_17
        :pswitch_2b
        :pswitch_19
        :pswitch_2b
        :pswitch_19
        :pswitch_2b
        :pswitch_19
        :pswitch_16
        :pswitch_15
        :pswitch_14
        :pswitch_13
        :pswitch_2b
        :pswitch_29
        :pswitch_28
        :pswitch_12
        :pswitch_15
        :pswitch_13
        :pswitch_2b
        :pswitch_2a
        :pswitch_29
        :pswitch_33
        :pswitch_33
        :pswitch_33
        :pswitch_11
        :pswitch_2b
        :pswitch_2b
        :pswitch_11
        :pswitch_11
        :pswitch_22
        :pswitch_22
        :pswitch_22
        :pswitch_22
        :pswitch_22
        :pswitch_22
        :pswitch_21
        :pswitch_21
        :pswitch_21
        :pswitch_21
        :pswitch_21
        :pswitch_21
        :pswitch_21
        :pswitch_21
        :pswitch_33
    .end packed-switch

    :pswitch_data_4
    .packed-switch 0xaa
        :pswitch_22
        :pswitch_22
        :pswitch_22
        :pswitch_21
        :pswitch_22
        :pswitch_21
        :pswitch_22
        :pswitch_33
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
    .end packed-switch

    :pswitch_data_5
    .packed-switch 0xbb
        :pswitch_c
        :pswitch_3
        :pswitch_2
        :pswitch_12
        :pswitch_22
        :pswitch_1
        :pswitch_12
        :pswitch_22
        :pswitch_22
    .end packed-switch

    :pswitch_data_6
    .packed-switch 0xc5
        :pswitch_0
        :pswitch_22
        :pswitch_22
    .end packed-switch

    :pswitch_data_7
    .packed-switch 0x4
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
    .end packed-switch
.end method

.method private get(I)Ljava/lang/Object;
    .locals 2

    .line 459
    iget v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    add-int/lit8 v1, p1, 0x1

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v0

    iput v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    .line 460
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p1, v0, :cond_0

    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    goto :goto_0

    :cond_0
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    :goto_0
    return-object p1
.end method

.method private pop()Ljava/lang/Object;
    .locals 2

    .line 514
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-interface {v0, v1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method private pop(I)V
    .locals 2

    .line 518
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    sub-int p1, v0, p1

    add-int/lit8 v0, v0, -0x1

    :goto_0
    if-lt v0, p1, :cond_0

    .line 521
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method private pop(Ljava/lang/String;)V
    .locals 4

    const/4 v0, 0x0

    .line 526
    invoke-virtual {p1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v2, 0x28

    if-ne v1, v2, :cond_1

    .line 529
    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    .line 530
    array-length v1, p1

    move v2, v0

    :goto_0
    if-ge v0, v1, :cond_0

    aget-object v3, p1, v0

    .line 531
    invoke-virtual {v3}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result v3

    add-int/2addr v2, v3

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 533
    :cond_0
    invoke-direct {p0, v2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    goto :goto_2

    :cond_1
    const/16 p1, 0x4a

    if-eq v1, p1, :cond_3

    const/16 p1, 0x44

    if-ne v1, p1, :cond_2

    goto :goto_1

    :cond_2
    const/4 p1, 0x1

    .line 537
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    goto :goto_2

    :cond_3
    :goto_1
    const/4 p1, 0x2

    .line 535
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(I)V

    :goto_2
    return-void
.end method

.method private push(Ljava/lang/Object;)V
    .locals 1

    .line 472
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 473
    iget p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxStack:I

    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    invoke-static {p1, v0}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxStack:I

    return-void
.end method

.method private pushDescriptor(Ljava/lang/String;)V
    .locals 3

    const/4 v0, 0x0

    .line 478
    invoke-virtual {p1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v2, 0x28

    if-ne v1, v2, :cond_0

    .line 479
    invoke-static {p1}, Lgroovyjarjarasm/asm/Type;->getReturnType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p1

    .line 481
    :cond_0
    invoke-virtual {p1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    const/16 v1, 0x46

    if-eq v0, v1, :cond_6

    const/16 v1, 0x4c

    if-eq v0, v1, :cond_5

    const/16 v1, 0x53

    if-eq v0, v1, :cond_4

    const/16 v1, 0x56

    if-eq v0, v1, :cond_3

    const/16 v1, 0x49

    if-eq v0, v1, :cond_4

    const/16 v1, 0x4a

    if-eq v0, v1, :cond_2

    const/16 v1, 0x5a

    if-eq v0, v1, :cond_4

    const/16 v1, 0x5b

    if-eq v0, v1, :cond_1

    packed-switch v0, :pswitch_data_0

    .line 509
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    .line 499
    :pswitch_0
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 500
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    return-void

    .line 503
    :cond_1
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_0

    .line 495
    :cond_2
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 496
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    :cond_3
    return-void

    .line 489
    :cond_4
    :pswitch_1
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    return-void

    .line 506
    :cond_5
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x1

    sub-int/2addr v0, v1

    invoke-virtual {p1, v1, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    :goto_0
    return-void

    .line 492
    :cond_6
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->FLOAT:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    return-void

    :pswitch_data_0
    .packed-switch 0x42
        :pswitch_1
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method private set(ILjava/lang/Object;)V
    .locals 2

    .line 464
    iget v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    add-int/lit8 v1, p1, 0x1

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v0

    iput v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    .line 465
    :goto_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lt p1, v0, :cond_0

    .line 466
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    sget-object v1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 468
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private static visitFrameTypes(I[Ljava/lang/Object;Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I[",
            "Ljava/lang/Object;",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p0, :cond_2

    .line 222
    aget-object v1, p1, v0

    .line 223
    invoke-interface {p2, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 224
    sget-object v2, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    if-eq v1, v2, :cond_0

    sget-object v2, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    if-ne v1, v2, :cond_1

    .line 225
    :cond_0
    sget-object v1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-interface {p2, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method


# virtual methods
.method public visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 280
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    const/4 p2, 0x0

    .line 281
    invoke-direct {p0, p1, p2, p4}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    return-void
.end method

.method public visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V
    .locals 1

    const/4 v0, -0x1

    if-ne p1, v0, :cond_1

    .line 204
    invoke-super/range {p0 .. p5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V

    .line 206
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    if-eqz p1, :cond_0

    .line 207
    invoke-interface {p1}, Ljava/util/List;->clear()V

    .line 208
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {p1}, Ljava/util/List;->clear()V

    goto :goto_0

    .line 210
    :cond_0
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    .line 211
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    .line 213
    :goto_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-static {p2, p3, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->visitFrameTypes(I[Ljava/lang/Object;Ljava/util/List;)V

    .line 214
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-static {p4, p5, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->visitFrameTypes(I[Ljava/lang/Object;Ljava/util/List;)V

    .line 215
    iget p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    iget-object p2, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    invoke-static {p1, p2}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    .line 216
    iget p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxStack:I

    iget-object p2, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    invoke-static {p1, p2}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxStack:I

    return-void

    .line 200
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "AnalyzerAdapter only accepts expanded frames (see ClassReader.EXPAND_FRAMES)"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public visitIincInsn(II)V
    .locals 1

    .line 404
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIincInsn(II)V

    .line 405
    iget p2, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    add-int/lit8 v0, p1, 0x1

    invoke-static {p2, v0}, Ljava/lang/Math;->max(II)I

    move-result p2

    iput p2, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    const/16 p2, 0x84

    const/4 v0, 0x0

    .line 406
    invoke-direct {p0, p2, p1, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    return-void
.end method

.method public visitInsn(I)V
    .locals 2

    .line 232
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    const/4 v0, 0x0

    const/4 v1, 0x0

    .line 233
    invoke-direct {p0, p1, v0, v1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    const/16 v0, 0xac

    if-lt p1, v0, :cond_0

    const/16 v0, 0xb1

    if-le p1, v0, :cond_1

    :cond_0
    const/16 v0, 0xbf

    if-ne p1, v0, :cond_2

    .line 235
    :cond_1
    iput-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    .line 236
    iput-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    :cond_2
    return-void
.end method

.method public visitIntInsn(II)V
    .locals 1

    .line 242
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    const/4 v0, 0x0

    .line 243
    invoke-direct {p0, p1, p2, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    return-void
.end method

.method public varargs visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V
    .locals 0

    .line 335
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    .line 336
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    const/4 p3, 0x0

    if-nez p1, :cond_0

    .line 337
    iput-object p3, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    return-void

    .line 340
    :cond_0
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(Ljava/lang/String;)V

    .line 341
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    .line 342
    iput-object p3, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    return-void
.end method

.method public visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 347
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    const/4 p2, 0x0

    const/4 v0, 0x0

    .line 348
    invoke-direct {p0, p1, p2, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    const/16 p2, 0xa7

    if-ne p1, p2, :cond_0

    .line 350
    iput-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    .line 351
    iput-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    :cond_0
    return-void
.end method

.method public visitLabel(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 357
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 358
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    if-nez v0, :cond_0

    .line 359
    new-instance v0, Ljava/util/ArrayList;

    const/4 v1, 0x3

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    .line 361
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLdcInsn(Ljava/lang/Object;)V
    .locals 2

    .line 366
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    .line 367
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 368
    iput-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    return-void

    .line 371
    :cond_0
    instance-of v0, p1, Ljava/lang/Integer;

    if-eqz v0, :cond_1

    .line 372
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->INTEGER:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto/16 :goto_1

    .line 373
    :cond_1
    instance-of v0, p1, Ljava/lang/Long;

    if-eqz v0, :cond_2

    .line 374
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->LONG:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 375
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_1

    .line 376
    :cond_2
    instance-of v0, p1, Ljava/lang/Float;

    if-eqz v0, :cond_3

    .line 377
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->FLOAT:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_1

    .line 378
    :cond_3
    instance-of v0, p1, Ljava/lang/Double;

    if-eqz v0, :cond_4

    .line 379
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->DOUBLE:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    .line 380
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->TOP:Ljava/lang/Integer;

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_1

    .line 381
    :cond_4
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_5

    const-string p1, "java/lang/String"

    .line 382
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_1

    .line 383
    :cond_5
    instance-of v0, p1, Lgroovyjarjarasm/asm/Type;

    if-eqz v0, :cond_9

    .line 384
    check-cast p1, Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result p1

    const/16 v0, 0xa

    if-eq p1, v0, :cond_8

    const/16 v0, 0x9

    if-ne p1, v0, :cond_6

    goto :goto_0

    :cond_6
    const/16 v0, 0xb

    if-ne p1, v0, :cond_7

    const-string p1, "java/lang/invoke/MethodType"

    .line 388
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_1

    .line 390
    :cond_7
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    :cond_8
    :goto_0
    const-string p1, "java/lang/Class"

    .line 386
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_1

    .line 392
    :cond_9
    instance-of v0, p1, Lgroovyjarjarasm/asm/Handle;

    if-eqz v0, :cond_a

    const-string p1, "java/lang/invoke/MethodHandle"

    .line 393
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->push(Ljava/lang/Object;)V

    goto :goto_1

    .line 394
    :cond_a
    instance-of v0, p1, Lgroovyjarjarasm/asm/ConstantDynamic;

    if-eqz v0, :cond_b

    .line 395
    check-cast p1, Lgroovyjarjarasm/asm/ConstantDynamic;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getDescriptor()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    .line 399
    :goto_1
    iput-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    return-void

    .line 397
    :cond_b
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1
.end method

.method public visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V
    .locals 3

    const/4 v0, 0x0

    .line 440
    invoke-virtual {p2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    .line 441
    iget v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    const/16 v2, 0x4a

    if-eq v0, v2, :cond_1

    const/16 v2, 0x44

    if-ne v0, v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x2

    :goto_1
    add-int/2addr v0, p6

    .line 442
    invoke-static {v1, v0}, Ljava/lang/Math;->max(II)I

    move-result v0

    iput v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    .line 444
    invoke-super/range {p0 .. p6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V

    return-void
.end method

.method public visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V
    .locals 0

    .line 420
    invoke-super {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V

    const/16 p1, 0xab

    const/4 p2, 0x0

    const/4 p3, 0x0

    .line 421
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    .line 422
    iput-object p3, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    .line 423
    iput-object p3, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    return-void
.end method

.method public visitMaxs(II)V
    .locals 1

    .line 449
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    if-eqz v0, :cond_0

    .line 450
    iget v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxStack:I

    invoke-static {v0, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxStack:I

    .line 451
    iget p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    invoke-static {p1, p2}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    .line 452
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    iget p2, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxStack:I

    iget v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    invoke-virtual {p1, p2, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    :cond_0
    return-void
.end method

.method public visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 2

    .line 291
    iget v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->api:I

    const/high16 v1, 0x50000

    if-ge v0, v1, :cond_0

    and-int/lit16 v0, p1, 0x100

    if-nez v0, :cond_0

    .line 293
    invoke-super/range {p0 .. p5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void

    .line 296
    :cond_0
    invoke-super/range {p0 .. p5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    and-int/lit16 p1, p1, -0x101

    .line 299
    iget-object p2, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    const/4 p5, 0x0

    if-nez p2, :cond_1

    .line 300
    iput-object p5, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    return-void

    .line 303
    :cond_1
    invoke-direct {p0, p4}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop(Ljava/lang/String;)V

    const/16 p2, 0xb8

    if-eq p1, p2, :cond_6

    .line 305
    invoke-direct {p0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pop()Ljava/lang/Object;

    move-result-object p2

    const/16 v0, 0xb7

    if-ne p1, v0, :cond_6

    const-string p1, "<init>"

    .line 306
    invoke-virtual {p3, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_6

    .line 308
    sget-object p1, Lgroovyjarjarasm/asm/Opcodes;->UNINITIALIZED_THIS:Ljava/lang/Integer;

    if-ne p2, p1, :cond_2

    .line 309
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->owner:Ljava/lang/String;

    goto :goto_0

    .line 311
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->uninitializedTypes:Ljava/util/Map;

    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    :goto_0
    const/4 p3, 0x0

    move v0, p3

    .line 313
    :goto_1
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_4

    .line 314
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    if-ne v1, p2, :cond_3

    .line 315
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    invoke-interface {v1, v0, p1}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    :cond_3
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    .line 318
    :cond_4
    :goto_2
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-ge p3, v0, :cond_6

    .line 319
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {v0, p3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    if-ne v0, p2, :cond_5

    .line 320
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    invoke-interface {v0, p3, p1}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    :cond_5
    add-int/lit8 p3, p3, 0x1

    goto :goto_2

    .line 325
    :cond_6
    invoke-direct {p0, p4}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->pushDescriptor(Ljava/lang/String;)V

    .line 326
    iput-object p5, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    return-void
.end method

.method public visitMultiANewArrayInsn(Ljava/lang/String;I)V
    .locals 1

    .line 428
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMultiANewArrayInsn(Ljava/lang/String;I)V

    const/16 v0, 0xc5

    .line 429
    invoke-direct {p0, v0, p2, p1}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    return-void
.end method

.method public varargs visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V
    .locals 0

    .line 412
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V

    const/16 p1, 0xaa

    const/4 p2, 0x0

    const/4 p3, 0x0

    .line 413
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    .line 414
    iput-object p3, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->locals:Ljava/util/List;

    .line 415
    iput-object p3, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->stack:Ljava/util/List;

    return-void
.end method

.method public visitTypeInsn(ILjava/lang/String;)V
    .locals 3

    const/16 v0, 0xbb

    if-ne p1, v0, :cond_1

    .line 261
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    if-nez v0, :cond_0

    .line 262
    new-instance v0, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v0}, Lgroovyjarjarasm/asm/Label;-><init>()V

    .line 263
    new-instance v1, Ljava/util/ArrayList;

    const/4 v2, 0x3

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    iput-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    .line 264
    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 265
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    if-eqz v1, :cond_0

    .line 266
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 269
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->labels:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/Label;

    .line 270
    iget-object v2, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->uninitializedTypes:Ljava/util/Map;

    invoke-interface {v2, v1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 273
    :cond_1
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    const/4 v0, 0x0

    .line 274
    invoke-direct {p0, p1, v0, p2}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    return-void
.end method

.method public visitVarInsn(II)V
    .locals 3

    .line 248
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    const/4 v0, 0x1

    const/16 v1, 0x16

    if-eq p1, v1, :cond_1

    const/16 v1, 0x18

    if-eq p1, v1, :cond_1

    const/16 v1, 0x37

    if-eq p1, v1, :cond_1

    const/16 v1, 0x39

    if-ne p1, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    move v1, v0

    .line 254
    :goto_1
    iget v2, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    if-eqz v1, :cond_2

    const/4 v0, 0x2

    :cond_2
    add-int/2addr v0, p2

    invoke-static {v2, v0}, Ljava/lang/Math;->max(II)I

    move-result v0

    iput v0, p0, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->maxLocals:I

    const/4 v0, 0x0

    .line 255
    invoke-direct {p0, p1, p2, v0}, Lgroovyjarjarasm/asm/commons/AnalyzerAdapter;->execute(IILjava/lang/String;)V

    return-void
.end method
