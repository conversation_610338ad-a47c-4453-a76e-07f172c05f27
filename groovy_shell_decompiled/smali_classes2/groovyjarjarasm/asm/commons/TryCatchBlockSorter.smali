.class public Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;
.super Lgroovyjarjarasm/asm/tree/MethodNode;
.source "TryCatchBlockSorter.java"


# direct methods
.method protected constructor <init>(ILgroovyjarjarasm/asm/MethodVisitor;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 7

    move-object v0, p0

    move v1, p1

    move v2, p3

    move-object v3, p4

    move-object v4, p5

    move-object v5, p6

    move-object v6, p7

    .line 94
    invoke-direct/range {v0 .. v6}, Lgroovyjarjarasm/asm/tree/MethodNode;-><init>(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    .line 95
    iput-object p2, p0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/MethodVisitor;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 8

    const/high16 v1, 0x90000

    move-object v0, p0

    move-object v2, p1

    move v3, p2

    move-object v4, p3

    move-object v5, p4

    move-object v6, p5

    move-object v7, p6

    .line 73
    invoke-direct/range {v0 .. v7}, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;-><init>(ILgroovyjarjarasm/asm/MethodVisitor;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    .line 81
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    const-class p2, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;

    if-ne p1, p2, :cond_0

    return-void

    .line 82
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
.end method


# virtual methods
.method public visitEnd()V
    .locals 2

    .line 101
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->tryCatchBlocks:Ljava/util/List;

    new-instance v1, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter$1;

    invoke-direct {v1, p0}, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter$1;-><init>(Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;)V

    invoke-static {v0, v1}, Ljava/util/Collections;->sort(Ljava/util/List;Ljava/util/Comparator;)V

    const/4 v0, 0x0

    .line 119
    :goto_0
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->tryCatchBlocks:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    .line 120
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->tryCatchBlocks:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;

    invoke-virtual {v1, v0}, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->updateIndex(I)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 122
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    if-eqz v0, :cond_1

    .line 123
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->accept(Lgroovyjarjarasm/asm/MethodVisitor;)V

    :cond_1
    return-void
.end method
