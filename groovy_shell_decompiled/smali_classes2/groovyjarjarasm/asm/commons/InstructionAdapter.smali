.class public Lgroovyjarjarasm/asm/commons/InstructionAdapter;
.super Lgroovyjarjarasm/asm/MethodVisitor;
.source "InstructionAdapter.java"


# static fields
.field public static final OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, "Ljava/lang/Object;"

    .line 46
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    return-void
.end method

.method protected constructor <init>(ILgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 0

    .line 70
    invoke-direct {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;-><init>(ILgroovyjarjarasm/asm/MethodVisitor;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 56
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;-><init>(ILgroovyjarjarasm/asm/MethodVisitor;)V

    .line 57
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    const-class v0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;

    if-ne p1, v0, :cond_0

    return-void

    .line 58
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
.end method

.method static cast(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    if-eq p1, p2, :cond_e

    .line 910
    sget-object v0, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p1, v0, :cond_2

    .line 911
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_0

    const/16 p1, 0x90

    .line 912
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto/16 :goto_0

    .line 913
    :cond_0
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_1

    const/16 p1, 0x8f

    .line 914
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto/16 :goto_0

    :cond_1
    const/16 p1, 0x8e

    .line 916
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 917
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-static {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 919
    :cond_2
    sget-object v0, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p1, v0, :cond_5

    .line 920
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_3

    const/16 p1, 0x8d

    .line 921
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto/16 :goto_0

    .line 922
    :cond_3
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_4

    const/16 p1, 0x8c

    .line 923
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto/16 :goto_0

    :cond_4
    const/16 p1, 0x8b

    .line 925
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 926
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-static {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 928
    :cond_5
    sget-object v0, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p1, v0, :cond_8

    .line 929
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_6

    const/16 p1, 0x8a

    .line 930
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 931
    :cond_6
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_7

    const/16 p1, 0x89

    .line 932
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    :cond_7
    const/16 p1, 0x88

    .line 934
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 935
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-static {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 938
    :cond_8
    sget-object p1, Lgroovyjarjarasm/asm/Type;->BYTE_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_9

    const/16 p1, 0x91

    .line 939
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 940
    :cond_9
    sget-object p1, Lgroovyjarjarasm/asm/Type;->CHAR_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_a

    const/16 p1, 0x92

    .line 941
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 942
    :cond_a
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_b

    const/16 p1, 0x87

    .line 943
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 944
    :cond_b
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_c

    const/16 p1, 0x86

    .line 945
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 946
    :cond_c
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_d

    const/16 p1, 0x85

    .line 947
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 948
    :cond_d
    sget-object p1, Lgroovyjarjarasm/asm/Type;->SHORT_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p2, p1, :cond_e

    const/16 p1, 0x93

    .line 949
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    :cond_e
    :goto_0
    return-void
.end method

.method static newarray(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 1232
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    packed-switch v0, :pswitch_data_0

    const/16 v0, 0xbd

    .line 1258
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    return-void

    :pswitch_0
    const/4 p1, 0x7

    goto :goto_0

    :pswitch_1
    const/16 p1, 0xb

    goto :goto_0

    :pswitch_2
    const/4 p1, 0x6

    goto :goto_0

    :pswitch_3
    const/16 p1, 0xa

    goto :goto_0

    :pswitch_4
    const/16 p1, 0x9

    goto :goto_0

    :pswitch_5
    const/16 p1, 0x8

    goto :goto_0

    :pswitch_6
    const/4 p1, 0x5

    goto :goto_0

    :pswitch_7
    const/4 p1, 0x4

    :goto_0
    const/16 v0, 0xbc

    .line 1261
    invoke-virtual {p0, v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    return-void

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method


# virtual methods
.method public aconst(Ljava/lang/Object;)V
    .locals 1

    if-nez p1, :cond_0

    .line 696
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 698
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public add(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 840
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x60

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public aload(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 792
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x2e

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public and(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 876
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x7e

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public anew(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 1212
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p1

    const/16 v1, 0xbb

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    return-void
.end method

.method public areturn(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 1044
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xac

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public arraylength()V
    .locals 2

    .line 1265
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xbe

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public astore(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 800
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x4f

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public athrow()V
    .locals 2

    .line 1269
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xbf

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 898
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {v0, p1, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    return-void
.end method

.method public cconst(Lgroovyjarjarasm/asm/ConstantDynamic;)V
    .locals 1

    .line 784
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    return-void
.end method

.method public checkcast(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 1273
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p1

    const/16 v1, 0xc0

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    return-void
.end method

.method public cmpg(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 964
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    sget-object v1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p1, v1, :cond_0

    const/16 p1, 0x96

    goto :goto_0

    :cond_0
    const/16 p1, 0x98

    :goto_0
    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public cmpl(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 960
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    sget-object v1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p1, v1, :cond_0

    const/16 p1, 0x95

    goto :goto_0

    :cond_0
    const/16 p1, 0x97

    :goto_0
    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dconst(D)V
    .locals 4

    .line 752
    invoke-static {p1, p2}, Ljava/lang/Double;->doubleToLongBits(D)J

    move-result-wide v0

    const-wide/16 v2, 0x0

    cmp-long v2, v0, v2

    if-eqz v2, :cond_1

    const-wide/high16 v2, 0x3ff0000000000000L    # 1.0

    cmp-long v0, v0, v2

    if-nez v0, :cond_0

    goto :goto_0

    .line 756
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {p1, p2}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    goto :goto_1

    .line 754
    :cond_1
    :goto_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    double-to-int p1, p1

    add-int/lit8 p1, p1, 0xe

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    :goto_1
    return-void
.end method

.method public div(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 852
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x6c

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dup()V
    .locals 2

    .line 812
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x59

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dup2()V
    .locals 2

    .line 816
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5c

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dup2X1()V
    .locals 2

    .line 828
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5d

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dup2X2()V
    .locals 2

    .line 832
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5e

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dupX1()V
    .locals 2

    .line 820
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5a

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dupX2()V
    .locals 2

    .line 824
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5b

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public fconst(F)V
    .locals 5

    .line 738
    invoke-static {p1}, Ljava/lang/Float;->floatToIntBits(F)I

    move-result v0

    int-to-long v1, v0

    const-wide/16 v3, 0x0

    cmp-long v1, v1, v3

    if-eqz v1, :cond_1

    const/high16 v1, 0x3f800000    # 1.0f

    if-eq v0, v1, :cond_1

    const/high16 v1, 0x40000000    # 2.0f

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 742
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    goto :goto_1

    .line 740
    :cond_1
    :goto_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    float-to-int p1, p1

    add-int/lit8 p1, p1, 0xb

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    :goto_1
    return-void
.end method

.method public getfield(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 1056
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xb4

    invoke-virtual {v0, v1, p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public getstatic(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 1048
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xb2

    invoke-virtual {v0, v1, p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public goTo(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1024
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa7

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public hconst(Lgroovyjarjarasm/asm/Handle;)V
    .locals 1

    .line 775
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    return-void
.end method

.method public iconst(I)V
    .locals 2

    const/4 v0, -0x1

    if-lt p1, v0, :cond_0

    const/4 v0, 0x5

    if-gt p1, v0, :cond_0

    .line 709
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    add-int/lit8 p1, p1, 0x3

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    :cond_0
    const/16 v0, -0x80

    if-lt p1, v0, :cond_1

    const/16 v0, 0x7f

    if-gt p1, v0, :cond_1

    .line 711
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x10

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    goto :goto_0

    :cond_1
    const/16 v0, -0x8000

    if-lt p1, v0, :cond_2

    const/16 v0, 0x7fff

    if-gt p1, v0, :cond_2

    .line 713
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x11

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    goto :goto_0

    .line 715
    :cond_2
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public ifacmpeq(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1016
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa5

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifacmpne(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1020
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa6

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifeq(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 968
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x99

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifge(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 980
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x9c

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifgt(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 984
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x9d

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ificmpeq(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 992
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x9f

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ificmpge(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1004
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa2

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ificmpgt(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1008
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa3

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ificmple(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1012
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa4

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ificmplt(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1000
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa1

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ificmpne(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 996
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa0

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifle(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 988
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x9e

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public iflt(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 976
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x9b

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifne(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 972
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x9a

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifnonnull(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1297
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xc7

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifnull(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1293
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xc6

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public iinc(II)V
    .locals 1

    .line 888
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIincInsn(II)V

    return-void
.end method

.method public instanceOf(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 1277
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p1

    const/16 v1, 0xc1

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    return-void
.end method

.method public invokedynamic(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V
    .locals 1

    .line 1208
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    return-void
.end method

.method public invokeinterface(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 6

    .line 1189
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xb9

    const/4 v5, 0x1

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method public invokespecial(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 7
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 1113
    iget v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->api:I

    const/high16 v1, 0x50000

    if-lt v0, v1, :cond_0

    const/4 v0, 0x0

    .line 1114
    invoke-virtual {p0, p1, p2, p3, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokespecial(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void

    .line 1117
    :cond_0
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v2, 0xb7

    const/4 v6, 0x0

    move-object v3, p1

    move-object v4, p2

    move-object v5, p3

    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method public invokespecial(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 6

    .line 1131
    iget v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->api:I

    const/high16 v1, 0x50000

    if-ge v0, v1, :cond_1

    if-nez p4, :cond_0

    .line 1135
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokespecial(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 1133
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "INVOKESPECIAL on interfaces require ASM 5"

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 1138
    :cond_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xb7

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move v5, p4

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method public invokestatic(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 7
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 1152
    iget v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->api:I

    const/high16 v1, 0x50000

    if-lt v0, v1, :cond_0

    const/4 v0, 0x0

    .line 1153
    invoke-virtual {p0, p1, p2, p3, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokestatic(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void

    .line 1156
    :cond_0
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v2, 0xb8

    const/4 v6, 0x0

    move-object v3, p1

    move-object v4, p2

    move-object v5, p3

    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method public invokestatic(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 6

    .line 1170
    iget v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->api:I

    const/high16 v1, 0x50000

    if-ge v0, v1, :cond_1

    if-nez p4, :cond_0

    .line 1174
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokestatic(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 1172
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "INVOKESTATIC on interfaces require ASM 5"

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 1177
    :cond_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xb8

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move v5, p4

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method public invokevirtual(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 1074
    iget v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->api:I

    const/high16 v1, 0x50000

    if-lt v0, v1, :cond_0

    const/4 v0, 0x0

    .line 1075
    invoke-virtual {p0, p1, p2, p3, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokevirtual(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void

    .line 1078
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xb6

    invoke-virtual {v0, v1, p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public invokevirtual(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 6

    .line 1092
    iget v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->api:I

    const/high16 v1, 0x50000

    if-ge v0, v1, :cond_1

    if-nez p4, :cond_0

    .line 1096
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokevirtual(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 1094
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string p2, "INVOKEVIRTUAL on interfaces require ASM 5"

    invoke-direct {p1, p2}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 1099
    :cond_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xb6

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move v5, p4

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method public jsr(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1028
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa8

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public lcmp()V
    .locals 2

    .line 956
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x94

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public lconst(J)V
    .locals 2

    const-wide/16 v0, 0x0

    cmp-long v0, p1, v0

    if-eqz v0, :cond_1

    const-wide/16 v0, 0x1

    cmp-long v0, p1, v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 728
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    goto :goto_1

    .line 726
    :cond_1
    :goto_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    long-to-int p1, p1

    add-int/lit8 p1, p1, 0x9

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    :goto_1
    return-void
.end method

.method public load(ILgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 788
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x15

    invoke-virtual {p2, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p2

    invoke-virtual {v0, p2, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    return-void
.end method

.method public lookupswitch(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 1040
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public mark(Lgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 1301
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public monitorenter()V
    .locals 2

    .line 1281
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xc2

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public monitorexit()V
    .locals 2

    .line 1285
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xc3

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public mul(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 848
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x68

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public multianewarray(Ljava/lang/String;I)V
    .locals 1

    .line 1289
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMultiANewArrayInsn(Ljava/lang/String;I)V

    return-void
.end method

.method public neg(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 860
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x74

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public newarray(Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 1221
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/Type;)V

    return-void
.end method

.method public nop()V
    .locals 2

    .line 681
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public or(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 880
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x80

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public pop()V
    .locals 2

    .line 804
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x57

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public pop2()V
    .locals 2

    .line 808
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x58

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public putfield(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 1060
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xb5

    invoke-virtual {v0, v1, p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public putstatic(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 1052
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xb3

    invoke-virtual {v0, v1, p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public rem(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 856
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x70

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public ret(I)V
    .locals 2

    .line 1032
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa9

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    return-void
.end method

.method public shl(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 864
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x78

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public shr(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 868
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x7a

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public store(ILgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 796
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x36

    invoke-virtual {p2, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p2

    invoke-virtual {v0, p2, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    return-void
.end method

.method public sub(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 844
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x64

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public swap()V
    .locals 2

    .line 836
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5f

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public varargs tableswitch(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 1036
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public tconst(Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 766
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    return-void
.end method

.method public ushr(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 872
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x7c

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    packed-switch p1, :pswitch_data_0

    .line 505
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 502
    :pswitch_0
    invoke-virtual {p0, p2, p3, p4}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->putfield(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 499
    :pswitch_1
    invoke-virtual {p0, p2, p3, p4}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->getfield(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 496
    :pswitch_2
    invoke-virtual {p0, p2, p3, p4}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->putstatic(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 493
    :pswitch_3
    invoke-virtual {p0, p2, p3, p4}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->getstatic(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    :goto_0
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0xb2
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public visitIincInsn(II)V
    .locals 0

    .line 658
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iinc(II)V

    return-void
.end method

.method public visitInsn(I)V
    .locals 2

    const/16 v0, 0xbe

    if-eq p1, v0, :cond_3

    const/16 v0, 0xbf

    if-eq p1, v0, :cond_2

    const/16 v0, 0xc2

    if-eq p1, v0, :cond_1

    const/16 v0, 0xc3

    if-eq p1, v0, :cond_0

    packed-switch p1, :pswitch_data_0

    packed-switch p1, :pswitch_data_1

    packed-switch p1, :pswitch_data_2

    packed-switch p1, :pswitch_data_3

    packed-switch p1, :pswitch_data_4

    .line 378
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 363
    :pswitch_0
    sget-object p1, Lgroovyjarjarasm/asm/Type;->VOID_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->areturn(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 360
    :pswitch_1
    sget-object p1, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->areturn(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 357
    :pswitch_2
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->areturn(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 354
    :pswitch_3
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->areturn(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 351
    :pswitch_4
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->areturn(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 348
    :pswitch_5
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->areturn(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 345
    :pswitch_6
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cmpg(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 342
    :pswitch_7
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cmpl(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 339
    :pswitch_8
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cmpg(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 336
    :pswitch_9
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cmpl(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 333
    :pswitch_a
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->lcmp()V

    goto/16 :goto_0

    .line 330
    :pswitch_b
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->SHORT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 327
    :pswitch_c
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->CHAR_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 324
    :pswitch_d
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->BYTE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 321
    :pswitch_e
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 318
    :pswitch_f
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 315
    :pswitch_10
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 312
    :pswitch_11
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 309
    :pswitch_12
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 306
    :pswitch_13
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 303
    :pswitch_14
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 300
    :pswitch_15
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 297
    :pswitch_16
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 294
    :pswitch_17
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 291
    :pswitch_18
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 288
    :pswitch_19
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    sget-object v0, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1, v0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 285
    :pswitch_1a
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->xor(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 282
    :pswitch_1b
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->xor(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 279
    :pswitch_1c
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->or(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 276
    :pswitch_1d
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->or(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 273
    :pswitch_1e
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->and(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 270
    :pswitch_1f
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->and(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 267
    :pswitch_20
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ushr(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 264
    :pswitch_21
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ushr(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 261
    :pswitch_22
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->shr(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 258
    :pswitch_23
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->shr(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 255
    :pswitch_24
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->shl(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 252
    :pswitch_25
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->shl(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 249
    :pswitch_26
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->neg(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 246
    :pswitch_27
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->neg(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 243
    :pswitch_28
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->neg(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 240
    :pswitch_29
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->neg(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 237
    :pswitch_2a
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->rem(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 234
    :pswitch_2b
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->rem(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 231
    :pswitch_2c
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->rem(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 228
    :pswitch_2d
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->rem(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 225
    :pswitch_2e
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->div(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 222
    :pswitch_2f
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->div(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 219
    :pswitch_30
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->div(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 216
    :pswitch_31
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->div(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 213
    :pswitch_32
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mul(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 210
    :pswitch_33
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mul(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 207
    :pswitch_34
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mul(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 204
    :pswitch_35
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mul(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 201
    :pswitch_36
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->sub(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 198
    :pswitch_37
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->sub(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 195
    :pswitch_38
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->sub(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 192
    :pswitch_39
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->sub(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 189
    :pswitch_3a
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->add(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 186
    :pswitch_3b
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->add(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 183
    :pswitch_3c
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->add(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 180
    :pswitch_3d
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->add(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 177
    :pswitch_3e
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->swap()V

    goto/16 :goto_0

    .line 174
    :pswitch_3f
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->dup2X2()V

    goto/16 :goto_0

    .line 171
    :pswitch_40
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->dup2X1()V

    goto/16 :goto_0

    .line 168
    :pswitch_41
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->dup2()V

    goto/16 :goto_0

    .line 165
    :pswitch_42
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->dupX2()V

    goto/16 :goto_0

    .line 162
    :pswitch_43
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->dupX1()V

    goto/16 :goto_0

    .line 159
    :pswitch_44
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->dup()V

    goto/16 :goto_0

    .line 156
    :pswitch_45
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->pop2()V

    goto/16 :goto_0

    .line 153
    :pswitch_46
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->pop()V

    goto/16 :goto_0

    .line 150
    :pswitch_47
    sget-object p1, Lgroovyjarjarasm/asm/Type;->SHORT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->astore(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 147
    :pswitch_48
    sget-object p1, Lgroovyjarjarasm/asm/Type;->CHAR_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->astore(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 144
    :pswitch_49
    sget-object p1, Lgroovyjarjarasm/asm/Type;->BYTE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->astore(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 141
    :pswitch_4a
    sget-object p1, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->astore(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 138
    :pswitch_4b
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->astore(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 135
    :pswitch_4c
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->astore(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 132
    :pswitch_4d
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->astore(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 129
    :pswitch_4e
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->astore(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 126
    :pswitch_4f
    sget-object p1, Lgroovyjarjarasm/asm/Type;->SHORT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aload(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_0

    .line 123
    :pswitch_50
    sget-object p1, Lgroovyjarjarasm/asm/Type;->CHAR_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aload(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 120
    :pswitch_51
    sget-object p1, Lgroovyjarjarasm/asm/Type;->BYTE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aload(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 117
    :pswitch_52
    sget-object p1, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aload(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 114
    :pswitch_53
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aload(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 111
    :pswitch_54
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aload(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 108
    :pswitch_55
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aload(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 105
    :pswitch_56
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aload(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    :pswitch_57
    add-int/lit8 p1, p1, -0xe

    int-to-double v0, p1

    .line 102
    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->dconst(D)V

    goto :goto_0

    :pswitch_58
    add-int/lit8 p1, p1, -0xb

    int-to-float p1, p1

    .line 98
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->fconst(F)V

    goto :goto_0

    :pswitch_59
    add-int/lit8 p1, p1, -0x9

    int-to-long v0, p1

    .line 93
    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->lconst(J)V

    goto :goto_0

    :pswitch_5a
    add-int/lit8 p1, p1, -0x3

    .line 89
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iconst(I)V

    goto :goto_0

    :pswitch_5b
    const/4 p1, 0x0

    .line 80
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aconst(Ljava/lang/Object;)V

    goto :goto_0

    .line 77
    :pswitch_5c
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->nop()V

    goto :goto_0

    .line 375
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->monitorexit()V

    goto :goto_0

    .line 372
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->monitorenter()V

    goto :goto_0

    .line 369
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->athrow()V

    goto :goto_0

    .line 366
    :cond_3
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->arraylength()V

    :goto_0
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_5c
        :pswitch_5b
        :pswitch_5a
        :pswitch_5a
        :pswitch_5a
        :pswitch_5a
        :pswitch_5a
        :pswitch_5a
        :pswitch_5a
        :pswitch_59
        :pswitch_59
        :pswitch_58
        :pswitch_58
        :pswitch_58
        :pswitch_57
        :pswitch_57
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x2e
        :pswitch_56
        :pswitch_55
        :pswitch_54
        :pswitch_53
        :pswitch_52
        :pswitch_51
        :pswitch_50
        :pswitch_4f
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x4f
        :pswitch_4e
        :pswitch_4d
        :pswitch_4c
        :pswitch_4b
        :pswitch_4a
        :pswitch_49
        :pswitch_48
        :pswitch_47
        :pswitch_46
        :pswitch_45
        :pswitch_44
        :pswitch_43
        :pswitch_42
        :pswitch_41
        :pswitch_40
        :pswitch_3f
        :pswitch_3e
        :pswitch_3d
        :pswitch_3c
        :pswitch_3b
        :pswitch_3a
        :pswitch_39
        :pswitch_38
        :pswitch_37
        :pswitch_36
        :pswitch_35
        :pswitch_34
        :pswitch_33
        :pswitch_32
        :pswitch_31
        :pswitch_30
        :pswitch_2f
        :pswitch_2e
        :pswitch_2d
        :pswitch_2c
        :pswitch_2b
        :pswitch_2a
        :pswitch_29
        :pswitch_28
        :pswitch_27
        :pswitch_26
        :pswitch_25
        :pswitch_24
        :pswitch_23
        :pswitch_22
        :pswitch_21
        :pswitch_20
        :pswitch_1f
        :pswitch_1e
        :pswitch_1d
        :pswitch_1c
        :pswitch_1b
        :pswitch_1a
    .end packed-switch

    :pswitch_data_3
    .packed-switch 0x85
        :pswitch_19
        :pswitch_18
        :pswitch_17
        :pswitch_16
        :pswitch_15
        :pswitch_14
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
    .end packed-switch

    :pswitch_data_4
    .packed-switch 0xac
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public visitIntInsn(II)V
    .locals 1

    const/16 v0, 0x10

    if-eq p1, v0, :cond_2

    const/16 v0, 0x11

    if-eq p1, v0, :cond_1

    const/16 v0, 0xbc

    if-ne p1, v0, :cond_0

    packed-switch p2, :pswitch_data_0

    .line 418
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 412
    :pswitch_0
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 406
    :pswitch_1
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 403
    :pswitch_2
    sget-object p1, Lgroovyjarjarasm/asm/Type;->SHORT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 400
    :pswitch_3
    sget-object p1, Lgroovyjarjarasm/asm/Type;->BYTE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 415
    :pswitch_4
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 409
    :pswitch_5
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 397
    :pswitch_6
    sget-object p1, Lgroovyjarjarasm/asm/Type;->CHAR_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 394
    :pswitch_7
    sget-object p1, Lgroovyjarjarasm/asm/Type;->BOOLEAN_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 422
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 389
    :cond_1
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iconst(I)V

    goto :goto_0

    .line 386
    :cond_2
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iconst(I)V

    :goto_0
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x4
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public varargs visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V
    .locals 0

    .line 547
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokedynamic(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    return-void
.end method

.method public visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V
    .locals 1

    const/16 v0, 0xc6

    if-eq p1, v0, :cond_1

    const/16 v0, 0xc7

    if-eq p1, v0, :cond_0

    packed-switch p1, :pswitch_data_0

    .line 608
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 599
    :pswitch_0
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->jsr(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 596
    :pswitch_1
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->goTo(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 593
    :pswitch_2
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ifacmpne(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 590
    :pswitch_3
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ifacmpeq(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 587
    :pswitch_4
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ificmple(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 584
    :pswitch_5
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ificmpgt(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 581
    :pswitch_6
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ificmpge(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 578
    :pswitch_7
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ificmplt(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 575
    :pswitch_8
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ificmpne(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 572
    :pswitch_9
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ificmpeq(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 569
    :pswitch_a
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ifle(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 566
    :pswitch_b
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ifgt(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 563
    :pswitch_c
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ifge(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 560
    :pswitch_d
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iflt(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 557
    :pswitch_e
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ifne(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 554
    :pswitch_f
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ifeq(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 605
    :cond_0
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ifnonnull(Lgroovyjarjarasm/asm/Label;)V

    goto :goto_0

    .line 602
    :cond_1
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ifnull(Lgroovyjarjarasm/asm/Label;)V

    :goto_0
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x99
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public visitLabel(Lgroovyjarjarasm/asm/Label;)V
    .locals 0

    .line 614
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mark(Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public visitLdcInsn(Ljava/lang/Object;)V
    .locals 2

    .line 619
    iget v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->api:I

    const/high16 v1, 0x50000

    if-ge v0, v1, :cond_1

    instance-of v0, p1, Lgroovyjarjarasm/asm/Handle;

    if-nez v0, :cond_0

    instance-of v0, p1, Lgroovyjarjarasm/asm/Type;

    if-eqz v0, :cond_1

    move-object v0, p1

    check-cast v0, Lgroovyjarjarasm/asm/Type;

    .line 621
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const/16 v1, 0xb

    if-eq v0, v1, :cond_0

    goto :goto_0

    .line 622
    :cond_0
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "This feature requires ASM5"

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 624
    :cond_1
    :goto_0
    iget v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->api:I

    const/high16 v1, 0x70000

    if-ge v0, v1, :cond_3

    instance-of v0, p1, Lgroovyjarjarasm/asm/ConstantDynamic;

    if-nez v0, :cond_2

    goto :goto_1

    .line 625
    :cond_2
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "This feature requires ASM7"

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 627
    :cond_3
    :goto_1
    instance-of v0, p1, Ljava/lang/Integer;

    if-eqz v0, :cond_4

    .line 628
    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iconst(I)V

    goto/16 :goto_2

    .line 629
    :cond_4
    instance-of v0, p1, Ljava/lang/Byte;

    if-eqz v0, :cond_5

    .line 630
    check-cast p1, Ljava/lang/Byte;

    invoke-virtual {p1}, Ljava/lang/Byte;->intValue()I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iconst(I)V

    goto/16 :goto_2

    .line 631
    :cond_5
    instance-of v0, p1, Ljava/lang/Character;

    if-eqz v0, :cond_6

    .line 632
    check-cast p1, Ljava/lang/Character;

    invoke-virtual {p1}, Ljava/lang/Character;->charValue()C

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iconst(I)V

    goto/16 :goto_2

    .line 633
    :cond_6
    instance-of v0, p1, Ljava/lang/Short;

    if-eqz v0, :cond_7

    .line 634
    check-cast p1, Ljava/lang/Short;

    invoke-virtual {p1}, Ljava/lang/Short;->intValue()I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iconst(I)V

    goto :goto_2

    .line 635
    :cond_7
    instance-of v0, p1, Ljava/lang/Boolean;

    if-eqz v0, :cond_8

    .line 636
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->iconst(I)V

    goto :goto_2

    .line 637
    :cond_8
    instance-of v0, p1, Ljava/lang/Float;

    if-eqz v0, :cond_9

    .line 638
    check-cast p1, Ljava/lang/Float;

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    move-result p1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->fconst(F)V

    goto :goto_2

    .line 639
    :cond_9
    instance-of v0, p1, Ljava/lang/Long;

    if-eqz v0, :cond_a

    .line 640
    check-cast p1, Ljava/lang/Long;

    invoke-virtual {p1}, Ljava/lang/Long;->longValue()J

    move-result-wide v0

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->lconst(J)V

    goto :goto_2

    .line 641
    :cond_a
    instance-of v0, p1, Ljava/lang/Double;

    if-eqz v0, :cond_b

    .line 642
    check-cast p1, Ljava/lang/Double;

    invoke-virtual {p1}, Ljava/lang/Double;->doubleValue()D

    move-result-wide v0

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->dconst(D)V

    goto :goto_2

    .line 643
    :cond_b
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_c

    .line 644
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->aconst(Ljava/lang/Object;)V

    goto :goto_2

    .line 645
    :cond_c
    instance-of v0, p1, Lgroovyjarjarasm/asm/Type;

    if-eqz v0, :cond_d

    .line 646
    check-cast p1, Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->tconst(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_2

    .line 647
    :cond_d
    instance-of v0, p1, Lgroovyjarjarasm/asm/Handle;

    if-eqz v0, :cond_e

    .line 648
    check-cast p1, Lgroovyjarjarasm/asm/Handle;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->hconst(Lgroovyjarjarasm/asm/Handle;)V

    goto :goto_2

    .line 649
    :cond_e
    instance-of v0, p1, Lgroovyjarjarasm/asm/ConstantDynamic;

    if-eqz v0, :cond_f

    .line 650
    check-cast p1, Lgroovyjarjarasm/asm/ConstantDynamic;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cconst(Lgroovyjarjarasm/asm/ConstantDynamic;)V

    :goto_2
    return-void

    .line 652
    :cond_f
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1
.end method

.method public visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V
    .locals 0

    .line 669
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->lookupswitch(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 2

    .line 516
    iget v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->api:I

    const/high16 v1, 0x50000

    if-ge v0, v1, :cond_0

    and-int/lit16 v0, p1, 0x100

    if-nez v0, :cond_0

    .line 518
    invoke-super/range {p0 .. p5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void

    :cond_0
    and-int/lit16 p1, p1, -0x101

    packed-switch p1, :pswitch_data_0

    .line 537
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 534
    :pswitch_0
    invoke-virtual {p0, p2, p3, p4}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokeinterface(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 531
    :pswitch_1
    invoke-virtual {p0, p2, p3, p4, p5}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokestatic(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    goto :goto_0

    .line 525
    :pswitch_2
    invoke-virtual {p0, p2, p3, p4, p5}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokespecial(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    goto :goto_0

    .line 528
    :pswitch_3
    invoke-virtual {p0, p2, p3, p4, p5}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->invokevirtual(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    :goto_0
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0xb6
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public visitMultiANewArrayInsn(Ljava/lang/String;I)V
    .locals 0

    .line 674
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->multianewarray(Ljava/lang/String;I)V

    return-void
.end method

.method public varargs visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V
    .locals 0

    .line 664
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->tableswitch(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public visitTypeInsn(ILjava/lang/String;)V
    .locals 1

    .line 469
    invoke-static {p2}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p2

    const/16 v0, 0xbb

    if-eq p1, v0, :cond_3

    const/16 v0, 0xbd

    if-eq p1, v0, :cond_2

    const/16 v0, 0xc0

    if-eq p1, v0, :cond_1

    const/16 v0, 0xc1

    if-ne p1, v0, :cond_0

    .line 481
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->instanceOf(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 484
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 478
    :cond_1
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->checkcast(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 475
    :cond_2
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 472
    :cond_3
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->anew(Lgroovyjarjarasm/asm/Type;)V

    :goto_0
    return-void
.end method

.method public visitVarInsn(II)V
    .locals 1

    const/16 v0, 0xa9

    if-eq p1, v0, :cond_0

    packed-switch p1, :pswitch_data_0

    packed-switch p1, :pswitch_data_1

    .line 463
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 457
    :pswitch_0
    sget-object p1, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->store(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 454
    :pswitch_1
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->store(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 451
    :pswitch_2
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->store(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 448
    :pswitch_3
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->store(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 445
    :pswitch_4
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->store(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 442
    :pswitch_5
    sget-object p1, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->load(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 439
    :pswitch_6
    sget-object p1, Lgroovyjarjarasm/asm/Type;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->load(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 436
    :pswitch_7
    sget-object p1, Lgroovyjarjarasm/asm/Type;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->load(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 433
    :pswitch_8
    sget-object p1, Lgroovyjarjarasm/asm/Type;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->load(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 430
    :pswitch_9
    sget-object p1, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->load(ILgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 460
    :cond_0
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->ret(I)V

    :goto_0
    return-void

    :pswitch_data_0
    .packed-switch 0x15
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x36
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public xor(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 884
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x82

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method
