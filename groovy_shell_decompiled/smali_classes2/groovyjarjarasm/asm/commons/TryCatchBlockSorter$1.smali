.class Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter$1;
.super Ljava/lang/Object;
.source "TryCatchBlockSorter.java"

# interfaces
.implements Ljava/util/Comparator;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->visitEnd()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Comparator<",
        "Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;


# direct methods
.method constructor <init>(Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;)V
    .locals 0

    .line 103
    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter$1;->this$0:Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private blockLength(Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;)I
    .locals 2

    .line 113
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter$1;->this$0:Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;

    iget-object v0, v0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    iget-object v1, p1, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->start:Lgroovyjarjarasm/asm/tree/LabelNode;

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/tree/InsnList;->indexOf(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)I

    move-result v0

    .line 114
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter$1;->this$0:Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;

    iget-object v1, v1, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter;->instructions:Lgroovyjarjarasm/asm/tree/InsnList;

    iget-object p1, p1, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;->end:Lgroovyjarjarasm/asm/tree/LabelNode;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/tree/InsnList;->indexOf(Lgroovyjarjarasm/asm/tree/AbstractInsnNode;)I

    move-result p1

    sub-int/2addr p1, v0

    return p1
.end method


# virtual methods
.method public compare(Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;)I
    .locals 0

    .line 109
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter$1;->blockLength(Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;)I

    move-result p1

    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter$1;->blockLength(Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;)I

    move-result p2

    sub-int/2addr p1, p2

    return p1
.end method

.method public bridge synthetic compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    .line 103
    check-cast p1, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;

    check-cast p2, Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/TryCatchBlockSorter$1;->compare(Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;Lgroovyjarjarasm/asm/tree/TryCatchBlockNode;)I

    move-result p1

    return p1
.end method
