.class public Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;
.super Lgroovyjarjarasm/asm/RecordComponentVisitor;
.source "RecordComponentRemapper.java"


# instance fields
.field protected final remapper:Lgroovyjarjarasm/asm/commons/Remapper;


# direct methods
.method protected constructor <init>(ILgroovyjarjarasm/asm/RecordComponentVisitor;Lgroovyjarjarasm/asm/commons/Remapper;)V
    .locals 0

    .line 69
    invoke-direct {p0, p1, p2}, Lgroovyjarjarasm/asm/RecordComponentVisitor;-><init>(ILgroovyjarjarasm/asm/RecordComponentVisitor;)V

    .line 70
    iput-object p3, p0, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->remapper:Lgroovyjarjarasm/asm/commons/Remapper;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/RecordComponentVisitor;Lgroovyjarjarasm/asm/commons/Remapper;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 56
    invoke-direct {p0, v0, p1, p2}, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;-><init>(ILgroovyjarjarasm/asm/RecordComponentVisitor;Lgroovyjarjarasm/asm/commons/Remapper;)V

    return-void
.end method


# virtual methods
.method protected createAnnotationRemapper(Lgroovyjarjarasm/asm/AnnotationVisitor;)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 4
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 102
    new-instance v0, Lgroovyjarjarasm/asm/commons/AnnotationRemapper;

    iget v1, p0, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->api:I

    iget-object v2, p0, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->remapper:Lgroovyjarjarasm/asm/commons/Remapper;

    const/4 v3, 0x0

    invoke-direct {v0, v1, v3, p1, v2}, Lgroovyjarjarasm/asm/commons/AnnotationRemapper;-><init>(ILjava/lang/String;Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/commons/Remapper;)V

    return-object v0
.end method

.method protected createAnnotationRemapper(Ljava/lang/String;Lgroovyjarjarasm/asm/AnnotationVisitor;)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 3

    .line 115
    new-instance v0, Lgroovyjarjarasm/asm/commons/AnnotationRemapper;

    iget v1, p0, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->api:I

    iget-object v2, p0, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->remapper:Lgroovyjarjarasm/asm/commons/Remapper;

    invoke-direct {v0, v1, p1, p2, v2}, Lgroovyjarjarasm/asm/commons/AnnotationRemapper;-><init>(ILjava/lang/String;Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/commons/Remapper;)V

    .line 116
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->createAnnotationRemapper(Lgroovyjarjarasm/asm/AnnotationVisitor;)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/commons/AnnotationRemapper;->orDeprecatedValue(Lgroovyjarjarasm/asm/AnnotationVisitor;)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    return-object p1
.end method

.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 1

    .line 75
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->remapper:Lgroovyjarjarasm/asm/commons/Remapper;

    .line 76
    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapDesc(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-super {p0, v0, p2}, Lgroovyjarjarasm/asm/RecordComponentVisitor;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p2

    if-nez p2, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    .line 79
    :cond_0
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->createAnnotationRemapper(Ljava/lang/String;Lgroovyjarjarasm/asm/AnnotationVisitor;)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    :goto_0
    return-object p1
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 1

    .line 85
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->remapper:Lgroovyjarjarasm/asm/commons/Remapper;

    .line 86
    invoke-virtual {v0, p3}, Lgroovyjarjarasm/asm/commons/Remapper;->mapDesc(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-super {p0, p1, p2, v0, p4}, Lgroovyjarjarasm/asm/RecordComponentVisitor;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    .line 89
    :cond_0
    invoke-virtual {p0, p3, p1}, Lgroovyjarjarasm/asm/commons/RecordComponentRemapper;->createAnnotationRemapper(Ljava/lang/String;Lgroovyjarjarasm/asm/AnnotationVisitor;)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    :goto_0
    return-object p1
.end method
