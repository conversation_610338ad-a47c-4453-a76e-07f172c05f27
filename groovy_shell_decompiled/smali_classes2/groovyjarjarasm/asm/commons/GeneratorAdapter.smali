.class public Lgroovyjarjarasm/asm/commons/GeneratorAdapter;
.super Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;
.source "GeneratorAdapter.java"


# static fields
.field public static final ADD:I = 0x60

.field public static final AND:I = 0x7e

.field private static final BOOLEAN_TYPE:Lgroovyjarjarasm/asm/Type;

.field private static final BOOLEAN_VALUE:Lgroovyjarjarasm/asm/commons/Method;

.field private static final BYTE_TYPE:Lgroovyjarjarasm/asm/Type;

.field private static final CHARACTER_TYPE:Lgroovyjarjarasm/asm/Type;

.field private static final CHAR_VALUE:Lgroovyjarjarasm/asm/commons/Method;

.field private static final CLASS_DESCRIPTOR:Ljava/lang/String; = "Ljava/lang/Class;"

.field public static final DIV:I = 0x6c

.field private static final DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

.field private static final DOUBLE_VALUE:Lgroovyjarjarasm/asm/commons/Method;

.field public static final EQ:I = 0x99

.field private static final FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

.field private static final FLOAT_VALUE:Lgroovyjarjarasm/asm/commons/Method;

.field public static final GE:I = 0x9c

.field public static final GT:I = 0x9d

.field private static final INTEGER_TYPE:Lgroovyjarjarasm/asm/Type;

.field private static final INT_VALUE:Lgroovyjarjarasm/asm/commons/Method;

.field public static final LE:I = 0x9e

.field private static final LONG_TYPE:Lgroovyjarjarasm/asm/Type;

.field private static final LONG_VALUE:Lgroovyjarjarasm/asm/commons/Method;

.field public static final LT:I = 0x9b

.field public static final MUL:I = 0x68

.field public static final NE:I = 0x9a

.field public static final NEG:I = 0x74

.field private static final NUMBER_TYPE:Lgroovyjarjarasm/asm/Type;

.field private static final OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

.field public static final OR:I = 0x80

.field public static final REM:I = 0x70

.field public static final SHL:I = 0x78

.field private static final SHORT_TYPE:Lgroovyjarjarasm/asm/Type;

.field public static final SHR:I = 0x7a

.field public static final SUB:I = 0x64

.field public static final USHR:I = 0x7c

.field public static final XOR:I = 0x82


# instance fields
.field private final access:I

.field private final argumentTypes:[Lgroovyjarjarasm/asm/Type;

.field private final localTypes:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovyjarjarasm/asm/Type;",
            ">;"
        }
    .end annotation
.end field

.field private final name:Ljava/lang/String;

.field private final returnType:Lgroovyjarjarasm/asm/Type;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, "java/lang/Byte"

    .line 87
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->BYTE_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "java/lang/Boolean"

    .line 89
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->BOOLEAN_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "java/lang/Short"

    .line 91
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->SHORT_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "java/lang/Character"

    .line 93
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->CHARACTER_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "java/lang/Integer"

    .line 95
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->INTEGER_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "java/lang/Float"

    .line 97
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "java/lang/Long"

    .line 99
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "java/lang/Double"

    .line 101
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "java/lang/Number"

    .line 103
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->NUMBER_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "java/lang/Object"

    .line 105
    invoke-static {v0}, Lgroovyjarjarasm/asm/Type;->getObjectType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    const-string v0, "boolean booleanValue()"

    .line 107
    invoke-static {v0}, Lgroovyjarjarasm/asm/commons/Method;->getMethod(Ljava/lang/String;)Lgroovyjarjarasm/asm/commons/Method;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->BOOLEAN_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    const-string v0, "char charValue()"

    .line 109
    invoke-static {v0}, Lgroovyjarjarasm/asm/commons/Method;->getMethod(Ljava/lang/String;)Lgroovyjarjarasm/asm/commons/Method;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->CHAR_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    const-string v0, "int intValue()"

    .line 111
    invoke-static {v0}, Lgroovyjarjarasm/asm/commons/Method;->getMethod(Ljava/lang/String;)Lgroovyjarjarasm/asm/commons/Method;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->INT_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    const-string v0, "float floatValue()"

    .line 113
    invoke-static {v0}, Lgroovyjarjarasm/asm/commons/Method;->getMethod(Ljava/lang/String;)Lgroovyjarjarasm/asm/commons/Method;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->FLOAT_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    const-string v0, "long longValue()"

    .line 115
    invoke-static {v0}, Lgroovyjarjarasm/asm/commons/Method;->getMethod(Ljava/lang/String;)Lgroovyjarjarasm/asm/commons/Method;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->LONG_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    const-string v0, "double doubleValue()"

    .line 117
    invoke-static {v0}, Lgroovyjarjarasm/asm/commons/Method;->getMethod(Ljava/lang/String;)Lgroovyjarjarasm/asm/commons/Method;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->DOUBLE_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    return-void
.end method

.method protected constructor <init>(ILgroovyjarjarasm/asm/MethodVisitor;ILjava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 226
    invoke-direct {p0, p1, p3, p5, p2}, Lgroovyjarjarasm/asm/commons/LocalVariablesSorter;-><init>(IILjava/lang/String;Lgroovyjarjarasm/asm/MethodVisitor;)V

    .line 186
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->localTypes:Ljava/util/List;

    .line 227
    iput p3, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->access:I

    .line 228
    iput-object p4, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->name:Ljava/lang/String;

    .line 229
    invoke-static {p5}, Lgroovyjarjarasm/asm/Type;->getReturnType(Ljava/lang/String;)Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->returnType:Lgroovyjarjarasm/asm/Type;

    .line 230
    invoke-static {p5}, Lgroovyjarjarasm/asm/Type;->getArgumentTypes(Ljava/lang/String;)[Lgroovyjarjarasm/asm/Type;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarasm/asm/commons/Method;Lgroovyjarjarasm/asm/MethodVisitor;)V
    .locals 1

    .line 244
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/commons/Method;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2}, Lgroovyjarjarasm/asm/commons/Method;->getDescriptor()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p0, p3, p1, v0, p2}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;-><init>(Lgroovyjarjarasm/asm/MethodVisitor;ILjava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(ILgroovyjarjarasm/asm/commons/Method;Ljava/lang/String;[Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/ClassVisitor;)V
    .locals 6

    .line 269
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/commons/Method;->getName()Ljava/lang/String;

    move-result-object v2

    .line 270
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/commons/Method;->getDescriptor()Ljava/lang/String;

    move-result-object v3

    if-nez p4, :cond_0

    const/4 p4, 0x0

    goto :goto_0

    .line 272
    :cond_0
    invoke-static {p4}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->getInternalNames([Lgroovyjarjarasm/asm/Type;)[Ljava/lang/String;

    move-result-object p4

    :goto_0
    move-object v5, p4

    move-object v0, p5

    move v1, p1

    move-object v4, p3

    .line 267
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p3

    .line 264
    invoke-direct {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;-><init>(ILgroovyjarjarasm/asm/commons/Method;Lgroovyjarjarasm/asm/MethodVisitor;)V

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/MethodVisitor;ILjava/lang/String;Ljava/lang/String;)V
    .locals 6

    const/high16 v1, 0x90000

    move-object v0, p0

    move-object v2, p1

    move v3, p2

    move-object v4, p3

    move-object v5, p4

    .line 204
    invoke-direct/range {v0 .. v5}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;-><init>(ILgroovyjarjarasm/asm/MethodVisitor;ILjava/lang/String;Ljava/lang/String;)V

    .line 205
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    const-class p2, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;

    if-ne p1, p2, :cond_0

    return-void

    .line 206
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
.end method

.method private fieldInsn(ILgroovyjarjarasm/asm/Type;Ljava/lang/String;Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 1125
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p4}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p4

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method private getArgIndex(I)I
    .locals 3

    .line 467
    iget v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->access:I

    and-int/lit8 v0, v0, 0x8

    const/4 v1, 0x0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    move v0, v1

    :goto_0
    if-ge v1, p1, :cond_1

    .line 469
    iget-object v2, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    aget-object v2, v2, v1

    invoke-virtual {v2}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result v2

    add-int/2addr v0, v2

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return v0
.end method

.method private static getBoxedType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;
    .locals 1

    .line 770
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    packed-switch v0, :pswitch_data_0

    return-object p0

    .line 786
    :pswitch_0
    sget-object p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->DOUBLE_TYPE:Lgroovyjarjarasm/asm/Type;

    return-object p0

    .line 784
    :pswitch_1
    sget-object p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->LONG_TYPE:Lgroovyjarjarasm/asm/Type;

    return-object p0

    .line 782
    :pswitch_2
    sget-object p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->FLOAT_TYPE:Lgroovyjarjarasm/asm/Type;

    return-object p0

    .line 780
    :pswitch_3
    sget-object p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->INTEGER_TYPE:Lgroovyjarjarasm/asm/Type;

    return-object p0

    .line 776
    :pswitch_4
    sget-object p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->SHORT_TYPE:Lgroovyjarjarasm/asm/Type;

    return-object p0

    .line 772
    :pswitch_5
    sget-object p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->BYTE_TYPE:Lgroovyjarjarasm/asm/Type;

    return-object p0

    .line 778
    :pswitch_6
    sget-object p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->CHARACTER_TYPE:Lgroovyjarjarasm/asm/Type;

    return-object p0

    .line 774
    :pswitch_7
    sget-object p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->BOOLEAN_TYPE:Lgroovyjarjarasm/asm/Type;

    return-object p0

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method private static getInternalNames([Lgroovyjarjarasm/asm/Type;)[Ljava/lang/String;
    .locals 4

    .line 282
    array-length v0, p0

    new-array v1, v0, [Ljava/lang/String;

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    .line 284
    aget-object v3, p0, v2

    invoke-virtual {v3}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method private invokeInsn(ILgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;Z)V
    .locals 6

    .line 1186
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const/16 v1, 0x9

    if-ne v0, v1, :cond_0

    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p2

    goto :goto_0

    :cond_0
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p2

    :goto_0
    move-object v2, p2

    .line 1187
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p3}, Lgroovyjarjarasm/asm/commons/Method;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p3}, Lgroovyjarjarasm/asm/commons/Method;->getDescriptor()Ljava/lang/String;

    move-result-object v4

    move v1, p1

    move v5, p4

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method private loadInsn(Lgroovyjarjarasm/asm/Type;I)V
    .locals 2

    .line 481
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x15

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    return-void
.end method

.method private storeInsn(Lgroovyjarjarasm/asm/Type;I)V
    .locals 2

    .line 491
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x36

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    return-void
.end method

.method private typeInsn(ILgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 1260
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    return-void
.end method


# virtual methods
.method public arrayLength()V
    .locals 2

    .line 1287
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xbe

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public arrayLoad(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 630
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x2e

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public arrayStore(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 639
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x4f

    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public box(Lgroovyjarjarasm/asm/Type;)V
    .locals 5

    .line 799
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const/16 v1, 0xa

    if-eq v0, v1, :cond_3

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const/16 v1, 0x9

    if-ne v0, v1, :cond_0

    goto :goto_1

    .line 802
    :cond_0
    sget-object v0, Lgroovyjarjarasm/asm/Type;->VOID_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p1, v0, :cond_1

    const/4 p1, 0x0

    .line 803
    move-object v0, p1

    check-cast v0, Ljava/lang/String;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->push(Ljava/lang/String;)V

    goto :goto_1

    .line 805
    :cond_1
    invoke-static {p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->getBoxedType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    .line 806
    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->newInstance(Lgroovyjarjarasm/asm/Type;)V

    .line 807
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result v1

    const/4 v2, 0x2

    if-ne v1, v2, :cond_2

    .line 809
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->dupX2()V

    .line 810
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->dupX2()V

    .line 811
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->pop()V

    goto :goto_0

    .line 814
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->dupX1()V

    .line 815
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->swap()V

    .line 817
    :goto_0
    new-instance v1, Lgroovyjarjarasm/asm/commons/Method;

    sget-object v2, Lgroovyjarjarasm/asm/Type;->VOID_TYPE:Lgroovyjarjarasm/asm/Type;

    const/4 v3, 0x1

    new-array v3, v3, [Lgroovyjarjarasm/asm/Type;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    const-string p1, "<init>"

    invoke-direct {v1, p1, v2, v3}, Lgroovyjarjarasm/asm/commons/Method;-><init>(Ljava/lang/String;Lgroovyjarjarasm/asm/Type;[Lgroovyjarjarasm/asm/Type;)V

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->invokeConstructor(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;)V

    :cond_3
    :goto_1
    return-void
.end method

.method public cast(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V
    .locals 3

    if-eq p1, p2, :cond_1

    .line 755
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const/4 v1, 0x1

    if-lt v0, v1, :cond_0

    .line 756
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const/16 v2, 0x8

    if-gt v0, v2, :cond_0

    .line 757
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    if-lt v0, v1, :cond_0

    .line 758
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    if-gt v0, v2, :cond_0

    .line 761
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {v0, p1, p2}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->cast(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V

    goto :goto_0

    .line 759
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Cannot cast from "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " to "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_1
    :goto_0
    return-void
.end method

.method public catchException(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 1361
    new-instance v0, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v0}, Lgroovyjarjarasm/asm/Label;-><init>()V

    if-nez p3, :cond_0

    .line 1363
    iget-object p3, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v1, 0x0

    invoke-virtual {p3, p1, p2, v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTryCatchBlock(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Ljava/lang/String;)V

    goto :goto_0

    .line 1365
    :cond_0
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p3}, Lgroovyjarjarasm/asm/Type;->getInternalName()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {v1, p1, p2, v0, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTryCatchBlock(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Ljava/lang/String;)V

    .line 1367
    :goto_0
    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mark(Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public checkCast(Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 1316
    sget-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/Type;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    const/16 v0, 0xc0

    .line 1317
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->typeInsn(ILgroovyjarjarasm/asm/Type;)V

    :cond_0
    return-void
.end method

.method public dup()V
    .locals 2

    .line 658
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x59

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dup2()V
    .locals 2

    .line 663
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5c

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dup2X1()V
    .locals 2

    .line 678
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5d

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dup2X2()V
    .locals 2

    .line 683
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5e

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dupX1()V
    .locals 2

    .line 668
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5a

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public dupX2()V
    .locals 2

    .line 673
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5b

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public endMethod()V
    .locals 2

    .line 1346
    iget v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->access:I

    and-int/lit16 v0, v0, 0x400

    if-nez v0, :cond_0

    .line 1347
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v1, 0x0

    invoke-virtual {v0, v1, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    .line 1349
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    return-void
.end method

.method public getAccess()I
    .locals 1

    .line 290
    iget v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->access:I

    return v0
.end method

.method public getArgumentTypes()[Lgroovyjarjarasm/asm/Type;
    .locals 1

    .line 302
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    invoke-virtual {v0}, [Lgroovyjarjarasm/asm/Type;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovyjarjarasm/asm/Type;

    return-object v0
.end method

.method public getField(Lgroovyjarjarasm/asm/Type;Ljava/lang/String;Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    const/16 v0, 0xb4

    .line 1158
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->fieldInsn(ILgroovyjarjarasm/asm/Type;Ljava/lang/String;Lgroovyjarjarasm/asm/Type;)V

    return-void
.end method

.method public getLocalType(I)Lgroovyjarjarasm/asm/Type;
    .locals 2

    .line 568
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->localTypes:Ljava/util/List;

    iget v1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->firstLocal:I

    sub-int/2addr p1, v1

    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovyjarjarasm/asm/Type;

    return-object p1
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    .line 294
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->name:Ljava/lang/String;

    return-object v0
.end method

.method public getReturnType()Lgroovyjarjarasm/asm/Type;
    .locals 1

    .line 298
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->returnType:Lgroovyjarjarasm/asm/Type;

    return-object v0
.end method

.method public getStatic(Lgroovyjarjarasm/asm/Type;Ljava/lang/String;Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    const/16 v0, 0xb2

    .line 1136
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->fieldInsn(ILgroovyjarjarasm/asm/Type;Ljava/lang/String;Lgroovyjarjarasm/asm/Type;)V

    return-void
.end method

.method public goTo(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1024
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa7

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifCmp(Lgroovyjarjarasm/asm/Type;ILgroovyjarjarasm/asm/Label;)V
    .locals 3

    .line 927
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const/16 v1, 0x9d

    const/16 v2, 0x9c

    packed-switch v0, :pswitch_data_0

    packed-switch p2, :pswitch_data_1

    .line 970
    new-instance p1, Ljava/lang/IllegalArgumentException;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Bad comparison mode "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :pswitch_0
    const/16 v0, 0x99

    if-ne p2, v0, :cond_0

    .line 940
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 p2, 0xa5

    invoke-virtual {p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void

    :cond_0
    const/16 v0, 0x9a

    if-ne p2, v0, :cond_1

    .line 943
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 p2, 0xa6

    invoke-virtual {p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void

    .line 946
    :cond_1
    new-instance p2, Ljava/lang/IllegalArgumentException;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Bad comparison for type "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p2

    .line 932
    :pswitch_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    if-eq p2, v2, :cond_3

    if-ne p2, v1, :cond_2

    goto :goto_0

    :cond_2
    const/16 v0, 0x98

    goto :goto_1

    :cond_3
    :goto_0
    const/16 v0, 0x97

    :goto_1
    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_4

    .line 929
    :pswitch_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v0, 0x94

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_4

    .line 935
    :pswitch_3
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    if-eq p2, v2, :cond_5

    if-ne p2, v1, :cond_4

    goto :goto_2

    :cond_4
    const/16 v0, 0x96

    goto :goto_3

    :cond_5
    :goto_2
    const/16 v0, 0x95

    :goto_3
    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 975
    :goto_4
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void

    :pswitch_4
    const/16 p1, 0xa4

    goto :goto_5

    :pswitch_5
    const/16 p1, 0xa3

    goto :goto_5

    :pswitch_6
    const/16 p1, 0xa2

    goto :goto_5

    :pswitch_7
    const/16 p1, 0xa1

    goto :goto_5

    :pswitch_8
    const/16 p1, 0xa0

    goto :goto_5

    :pswitch_9
    const/16 p1, 0x9f

    .line 972
    :goto_5
    iget-object p2, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p2, p1, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void

    :pswitch_data_0
    .packed-switch 0x6
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x99
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
    .end packed-switch
.end method

.method public ifICmp(ILgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 986
    sget-object v0, Lgroovyjarjarasm/asm/Type;->INT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, v0, p1, p2}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->ifCmp(Lgroovyjarjarasm/asm/Type;ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifNonNull(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1015
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xc7

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifNull(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1006
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xc6

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public ifZCmp(ILgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 997
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public iinc(II)V
    .locals 1

    .line 744
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIincInsn(II)V

    return-void
.end method

.method public instanceOf(Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    const/16 v0, 0xc1

    .line 1327
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->typeInsn(ILgroovyjarjarasm/asm/Type;)V

    return-void
.end method

.method public invokeConstructor(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;)V
    .locals 2

    const/16 v0, 0xb7

    const/4 v1, 0x0

    .line 1207
    invoke-direct {p0, v0, p1, p2, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->invokeInsn(ILgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;Z)V

    return-void
.end method

.method public varargs invokeDynamic(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V
    .locals 1

    .line 1246
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    return-void
.end method

.method public invokeInterface(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;)V
    .locals 2

    const/16 v0, 0xb9

    const/4 v1, 0x1

    .line 1227
    invoke-direct {p0, v0, p1, p2, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->invokeInsn(ILgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;Z)V

    return-void
.end method

.method public invokeStatic(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;)V
    .locals 2

    const/16 v0, 0xb8

    const/4 v1, 0x0

    .line 1217
    invoke-direct {p0, v0, p1, p2, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->invokeInsn(ILgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;Z)V

    return-void
.end method

.method public invokeVirtual(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;)V
    .locals 2

    const/16 v0, 0xb6

    const/4 v1, 0x0

    .line 1197
    invoke-direct {p0, v0, p1, p2, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->invokeInsn(ILgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;Z)V

    return-void
.end method

.method public loadArg(I)V
    .locals 1

    .line 508
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    aget-object v0, v0, p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->getArgIndex(I)I

    move-result p1

    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->loadInsn(Lgroovyjarjarasm/asm/Type;I)V

    return-void
.end method

.method public loadArgArray()V
    .locals 2

    .line 536
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    array-length v0, v0

    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->push(I)V

    .line 537
    sget-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->newArray(Lgroovyjarjarasm/asm/Type;)V

    const/4 v0, 0x0

    .line 538
    :goto_0
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    array-length v1, v1

    if-ge v0, v1, :cond_0

    .line 539
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->dup()V

    .line 540
    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->push(I)V

    .line 541
    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->loadArg(I)V

    .line 542
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    aget-object v1, v1, v0

    invoke-virtual {p0, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->box(Lgroovyjarjarasm/asm/Type;)V

    .line 543
    sget-object v1, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->OBJECT_TYPE:Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p0, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->arrayStore(Lgroovyjarjarasm/asm/Type;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public loadArgs()V
    .locals 2

    .line 528
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    array-length v0, v0

    const/4 v1, 0x0

    invoke-virtual {p0, v1, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->loadArgs(II)V

    return-void
.end method

.method public loadArgs(II)V
    .locals 4

    .line 518
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->getArgIndex(I)I

    move-result v0

    const/4 v1, 0x0

    :goto_0
    if-ge v1, p2, :cond_0

    .line 520
    iget-object v2, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    add-int v3, p1, v1

    aget-object v2, v2, v3

    .line 521
    invoke-direct {p0, v2, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->loadInsn(Lgroovyjarjarasm/asm/Type;I)V

    .line 522
    invoke-virtual {v2}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result v2

    add-int/2addr v0, v2

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public loadLocal(I)V
    .locals 1

    .line 587
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->getLocalType(I)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->loadInsn(Lgroovyjarjarasm/asm/Type;I)V

    return-void
.end method

.method public loadLocal(ILgroovyjarjarasm/asm/Type;)V
    .locals 0

    .line 598
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->setLocalType(ILgroovyjarjarasm/asm/Type;)V

    .line 599
    invoke-direct {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->loadInsn(Lgroovyjarjarasm/asm/Type;I)V

    return-void
.end method

.method public loadThis()V
    .locals 3

    .line 496
    iget v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->access:I

    and-int/lit8 v0, v0, 0x8

    if-nez v0, :cond_0

    .line 499
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x19

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    return-void

    .line 497
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "no \'this\' pointer within static method"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public mark()Lgroovyjarjarasm/asm/Label;
    .locals 2

    .line 913
    new-instance v0, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v0}, Lgroovyjarjarasm/asm/Label;-><init>()V

    .line 914
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    return-object v0
.end method

.method public mark(Lgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 904
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public math(ILgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 728
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p2, p1}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public monitorEnter()V
    .locals 2

    .line 1332
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xc2

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public monitorExit()V
    .locals 2

    .line 1337
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xc3

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public newArray(Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 1278
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/commons/InstructionAdapter;->newarray(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/Type;)V

    return-void
.end method

.method public newInstance(Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    const/16 v0, 0xbb

    .line 1269
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->typeInsn(ILgroovyjarjarasm/asm/Type;)V

    return-void
.end method

.method public newLabel()Lgroovyjarjarasm/asm/Label;
    .locals 1

    .line 895
    new-instance v0, Lgroovyjarjarasm/asm/Label;

    invoke-direct {v0}, Lgroovyjarjarasm/asm/Label;-><init>()V

    return-object v0
.end method

.method public not()V
    .locals 2

    .line 733
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v1, 0x4

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    .line 734
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x82

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public pop()V
    .locals 2

    .line 648
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x57

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public pop2()V
    .locals 2

    .line 653
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x58

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public push(D)V
    .locals 4

    .line 368
    invoke-static {p1, p2}, Ljava/lang/Double;->doubleToLongBits(D)J

    move-result-wide v0

    const-wide/16 v2, 0x0

    cmp-long v2, v0, v2

    if-eqz v2, :cond_1

    const-wide/high16 v2, 0x3ff0000000000000L    # 1.0

    cmp-long v0, v0, v2

    if-nez v0, :cond_0

    goto :goto_0

    .line 372
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {p1, p2}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    goto :goto_1

    .line 370
    :cond_1
    :goto_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    double-to-int p1, p1

    add-int/lit8 p1, p1, 0xe

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    :goto_1
    return-void
.end method

.method public push(F)V
    .locals 5

    .line 354
    invoke-static {p1}, Ljava/lang/Float;->floatToIntBits(F)I

    move-result v0

    int-to-long v1, v0

    const-wide/16 v3, 0x0

    cmp-long v1, v1, v3

    if-eqz v1, :cond_1

    const/high16 v1, 0x3f800000    # 1.0f

    if-eq v0, v1, :cond_1

    const/high16 v1, 0x40000000    # 2.0f

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 358
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    goto :goto_1

    .line 356
    :cond_1
    :goto_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    float-to-int p1, p1

    add-int/lit8 p1, p1, 0xb

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    :goto_1
    return-void
.end method

.method public push(I)V
    .locals 2

    const/4 v0, -0x1

    if-lt p1, v0, :cond_0

    const/4 v0, 0x5

    if-gt p1, v0, :cond_0

    .line 325
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    add-int/lit8 p1, p1, 0x3

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    :cond_0
    const/16 v0, -0x80

    if-lt p1, v0, :cond_1

    const/16 v0, 0x7f

    if-gt p1, v0, :cond_1

    .line 327
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x10

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    goto :goto_0

    :cond_1
    const/16 v0, -0x8000

    if-lt p1, v0, :cond_2

    const/16 v0, 0x7fff

    if-gt p1, v0, :cond_2

    .line 329
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x11

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    goto :goto_0

    .line 331
    :cond_2
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public push(J)V
    .locals 2

    const-wide/16 v0, 0x0

    cmp-long v0, p1, v0

    if-eqz v0, :cond_1

    const-wide/16 v0, 0x1

    cmp-long v0, p1, v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 344
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    goto :goto_1

    .line 342
    :cond_1
    :goto_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    long-to-int p1, p1

    add-int/lit8 p1, p1, 0x9

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    :goto_1
    return-void
.end method

.method public push(Lgroovyjarjarasm/asm/ConstantDynamic;)V
    .locals 1

    if-nez p1, :cond_0

    .line 450
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 452
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public push(Lgroovyjarjarasm/asm/Handle;)V
    .locals 1

    if-nez p1, :cond_0

    .line 437
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 439
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public push(Lgroovyjarjarasm/asm/Type;)V
    .locals 4

    if-nez p1, :cond_0

    .line 396
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 398
    :cond_0
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const-string v1, "Ljava/lang/Class;"

    const-string v2, "TYPE"

    const/16 v3, 0xb2

    packed-switch v0, :pswitch_data_0

    .line 424
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    goto :goto_0

    .line 421
    :pswitch_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "java/lang/Double"

    invoke-virtual {p1, v3, v0, v2, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 418
    :pswitch_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "java/lang/Long"

    invoke-virtual {p1, v3, v0, v2, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 415
    :pswitch_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "java/lang/Float"

    invoke-virtual {p1, v3, v0, v2, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 412
    :pswitch_3
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "java/lang/Integer"

    invoke-virtual {p1, v3, v0, v2, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 409
    :pswitch_4
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "java/lang/Short"

    invoke-virtual {p1, v3, v0, v2, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 406
    :pswitch_5
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "java/lang/Byte"

    invoke-virtual {p1, v3, v0, v2, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 403
    :pswitch_6
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "java/lang/Character"

    invoke-virtual {p1, v3, v0, v2, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 400
    :pswitch_7
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const-string v0, "java/lang/Boolean"

    invoke-virtual {p1, v3, v0, v2, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    :goto_0
    return-void

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public push(Ljava/lang/String;)V
    .locals 1

    if-nez p1, :cond_0

    .line 383
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    goto :goto_0

    .line 385
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public push(Z)V
    .locals 0

    .line 315
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->push(I)V

    return-void
.end method

.method public putField(Lgroovyjarjarasm/asm/Type;Ljava/lang/String;Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    const/16 v0, 0xb5

    .line 1169
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->fieldInsn(ILgroovyjarjarasm/asm/Type;Ljava/lang/String;Lgroovyjarjarasm/asm/Type;)V

    return-void
.end method

.method public putStatic(Lgroovyjarjarasm/asm/Type;Ljava/lang/String;Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    const/16 v0, 0xb3

    .line 1147
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->fieldInsn(ILgroovyjarjarasm/asm/Type;Ljava/lang/String;Lgroovyjarjarasm/asm/Type;)V

    return-void
.end method

.method public ret(I)V
    .locals 2

    .line 1034
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xa9

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    return-void
.end method

.method public returnValue()V
    .locals 3

    .line 1108
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->returnType:Lgroovyjarjarasm/asm/Type;

    const/16 v2, 0xac

    invoke-virtual {v1, v2}, Lgroovyjarjarasm/asm/Type;->getOpcode(I)I

    move-result v1

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method protected setLocalType(ILgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 573
    iget v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->firstLocal:I

    sub-int/2addr p1, v0

    .line 574
    :goto_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->localTypes:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    add-int/lit8 v1, p1, 0x1

    if-ge v0, v1, :cond_0

    .line 575
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->localTypes:Ljava/util/List;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 577
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->localTypes:Ljava/util/List;

    invoke-interface {v0, p1, p2}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public storeArg(I)V
    .locals 1

    .line 553
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->argumentTypes:[Lgroovyjarjarasm/asm/Type;

    aget-object v0, v0, p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->getArgIndex(I)I

    move-result p1

    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->storeInsn(Lgroovyjarjarasm/asm/Type;I)V

    return-void
.end method

.method public storeLocal(I)V
    .locals 1

    .line 609
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->getLocalType(I)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->storeInsn(Lgroovyjarjarasm/asm/Type;I)V

    return-void
.end method

.method public storeLocal(ILgroovyjarjarasm/asm/Type;)V
    .locals 0

    .line 620
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->setLocalType(ILgroovyjarjarasm/asm/Type;)V

    .line 621
    invoke-direct {p0, p2, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->storeInsn(Lgroovyjarjarasm/asm/Type;I)V

    return-void
.end method

.method public swap()V
    .locals 2

    .line 688
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0x5f

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public swap(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 698
    invoke-virtual {p2}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result p2

    const/4 v0, 0x1

    if-ne p2, v0, :cond_1

    .line 699
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result p1

    if-ne p1, v0, :cond_0

    .line 700
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->swap()V

    goto :goto_0

    .line 702
    :cond_0
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->dupX2()V

    .line 703
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->pop()V

    goto :goto_0

    .line 706
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSize()I

    move-result p1

    if-ne p1, v0, :cond_2

    .line 707
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->dup2X1()V

    .line 708
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->pop2()V

    goto :goto_0

    .line 710
    :cond_2
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->dup2X2()V

    .line 711
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->pop2()V

    :goto_0
    return-void
.end method

.method public tableSwitch([ILgroovyjarjarasm/asm/commons/TableSwitchGenerator;)V
    .locals 5

    .line 1045
    array-length v0, p1

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    .line 1048
    :cond_0
    array-length v0, p1

    int-to-float v0, v0

    array-length v3, p1

    sub-int/2addr v3, v2

    aget v3, p1, v3

    aget v4, p1, v1

    sub-int/2addr v3, v4

    add-int/2addr v3, v2

    int-to-float v3, v3

    div-float/2addr v0, v3

    :goto_0
    const/high16 v3, 0x3f000000    # 0.5f

    cmpl-float v0, v0, v3

    if-ltz v0, :cond_1

    move v1, v2

    .line 1050
    :cond_1
    invoke-virtual {p0, p1, p2, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->tableSwitch([ILgroovyjarjarasm/asm/commons/TableSwitchGenerator;Z)V

    return-void
.end method

.method public tableSwitch([ILgroovyjarjarasm/asm/commons/TableSwitchGenerator;Z)V
    .locals 10

    const/4 v0, 0x1

    move v1, v0

    .line 1063
    :goto_0
    array-length v2, p1

    if-ge v1, v2, :cond_1

    .line 1064
    aget v2, p1, v1

    add-int/lit8 v3, v1, -0x1

    aget v3, p1, v3

    if-lt v2, v3, :cond_0

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 1065
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "keys must be sorted in ascending order"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 1068
    :cond_1
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->newLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v1

    .line 1069
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->newLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v2

    .line 1070
    array-length v3, p1

    if-lez v3, :cond_6

    .line 1071
    array-length v3, p1

    const/4 v4, 0x0

    if-eqz p3, :cond_4

    .line 1073
    aget p3, p1, v4

    add-int/lit8 v5, v3, -0x1

    .line 1074
    aget v5, p1, v5

    sub-int v6, v5, p3

    add-int/2addr v6, v0

    .line 1076
    new-array v0, v6, [Lgroovyjarjarasm/asm/Label;

    .line 1077
    invoke-static {v0, v1}, Ljava/util/Arrays;->fill([Ljava/lang/Object;Ljava/lang/Object;)V

    move v7, v4

    :goto_1
    if-ge v7, v3, :cond_2

    .line 1079
    aget v8, p1, v7

    sub-int/2addr v8, p3

    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->newLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v9

    aput-object v9, v0, v8

    add-int/lit8 v7, v7, 0x1

    goto :goto_1

    .line 1081
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {p1, p3, v5, v1, v0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V

    :goto_2
    if-ge v4, v6, :cond_6

    .line 1083
    aget-object p1, v0, v4

    if-eq p1, v1, :cond_3

    .line 1085
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mark(Lgroovyjarjarasm/asm/Label;)V

    add-int p1, v4, p3

    .line 1086
    invoke-interface {p2, p1, v2}, Lgroovyjarjarasm/asm/commons/TableSwitchGenerator;->generateCase(ILgroovyjarjarasm/asm/Label;)V

    :cond_3
    add-int/lit8 v4, v4, 0x1

    goto :goto_2

    .line 1090
    :cond_4
    new-array p3, v3, [Lgroovyjarjarasm/asm/Label;

    move v0, v4

    :goto_3
    if-ge v0, v3, :cond_5

    .line 1092
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->newLabel()Lgroovyjarjarasm/asm/Label;

    move-result-object v5

    aput-object v5, p3, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_3

    .line 1094
    :cond_5
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    invoke-virtual {v0, v1, p1, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V

    :goto_4
    if-ge v4, v3, :cond_6

    .line 1096
    aget-object v0, p3, v4

    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mark(Lgroovyjarjarasm/asm/Label;)V

    .line 1097
    aget v0, p1, v4

    invoke-interface {p2, v0, v2}, Lgroovyjarjarasm/asm/commons/TableSwitchGenerator;->generateCase(ILgroovyjarjarasm/asm/Label;)V

    add-int/lit8 v4, v4, 0x1

    goto :goto_4

    .line 1101
    :cond_6
    invoke-virtual {p0, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mark(Lgroovyjarjarasm/asm/Label;)V

    .line 1102
    invoke-interface {p2}, Lgroovyjarjarasm/asm/commons/TableSwitchGenerator;->generateDefault()V

    .line 1103
    invoke-virtual {p0, v2}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mark(Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public throwException()V
    .locals 2

    .line 1292
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    const/16 v1, 0xbf

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public throwException(Lgroovyjarjarasm/asm/Type;Ljava/lang/String;)V
    .locals 0

    .line 1303
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->newInstance(Lgroovyjarjarasm/asm/Type;)V

    .line 1304
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->dup()V

    .line 1305
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->push(Ljava/lang/String;)V

    const-string p2, "void <init> (String)"

    .line 1306
    invoke-static {p2}, Lgroovyjarjarasm/asm/commons/Method;->getMethod(Ljava/lang/String;)Lgroovyjarjarasm/asm/commons/Method;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->invokeConstructor(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;)V

    .line 1307
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->throwException()V

    return-void
.end method

.method public unbox(Lgroovyjarjarasm/asm/Type;)V
    .locals 2

    .line 846
    sget-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->NUMBER_TYPE:Lgroovyjarjarasm/asm/Type;

    .line 848
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v1

    packed-switch v1, :pswitch_data_0

    const/4 v1, 0x0

    goto :goto_0

    .line 860
    :pswitch_0
    sget-object v1, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->DOUBLE_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    goto :goto_0

    .line 866
    :pswitch_1
    sget-object v1, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->LONG_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    goto :goto_0

    .line 863
    :pswitch_2
    sget-object v1, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->FLOAT_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    goto :goto_0

    .line 871
    :pswitch_3
    sget-object v1, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->INT_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    goto :goto_0

    .line 852
    :pswitch_4
    sget-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->CHARACTER_TYPE:Lgroovyjarjarasm/asm/Type;

    .line 853
    sget-object v1, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->CHAR_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    goto :goto_0

    .line 856
    :pswitch_5
    sget-object v0, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->BOOLEAN_TYPE:Lgroovyjarjarasm/asm/Type;

    .line 857
    sget-object v1, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->BOOLEAN_VALUE:Lgroovyjarjarasm/asm/commons/Method;

    goto :goto_0

    :pswitch_6
    return-void

    :goto_0
    if-nez v1, :cond_0

    .line 878
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->checkCast(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_1

    .line 880
    :cond_0
    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->checkCast(Lgroovyjarjarasm/asm/Type;)V

    .line 881
    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->invokeVirtual(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;)V

    :goto_1
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_3
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public valueOf(Lgroovyjarjarasm/asm/Type;)V
    .locals 4

    .line 828
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const/16 v1, 0xa

    if-eq v0, v1, :cond_2

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v0

    const/16 v1, 0x9

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 831
    :cond_0
    sget-object v0, Lgroovyjarjarasm/asm/Type;->VOID_TYPE:Lgroovyjarjarasm/asm/Type;

    if-ne p1, v0, :cond_1

    const/4 p1, 0x0

    .line 832
    move-object v0, p1

    check-cast v0, Ljava/lang/String;

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->push(Ljava/lang/String;)V

    goto :goto_0

    .line 834
    :cond_1
    invoke-static {p1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->getBoxedType(Lgroovyjarjarasm/asm/Type;)Lgroovyjarjarasm/asm/Type;

    move-result-object v0

    .line 835
    new-instance v1, Lgroovyjarjarasm/asm/commons/Method;

    const/4 v2, 0x1

    new-array v2, v2, [Lgroovyjarjarasm/asm/Type;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, "valueOf"

    invoke-direct {v1, p1, v0, v2}, Lgroovyjarjarasm/asm/commons/Method;-><init>(Ljava/lang/String;Lgroovyjarjarasm/asm/Type;[Lgroovyjarjarasm/asm/Type;)V

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/commons/GeneratorAdapter;->invokeStatic(Lgroovyjarjarasm/asm/Type;Lgroovyjarjarasm/asm/commons/Method;)V

    :cond_2
    :goto_0
    return-void
.end method
