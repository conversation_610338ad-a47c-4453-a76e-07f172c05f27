.class public Lgroovyjarjarasm/asm/commons/SignatureRemapper;
.super Lgroovyjarjarasm/asm/signature/SignatureVisitor;
.source "SignatureRemapper.java"


# instance fields
.field private classNames:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final remapper:Lgroovyjarjarasm/asm/commons/Remapper;

.field private final signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;


# direct methods
.method protected constructor <init>(ILgroovyjarjarasm/asm/signature/SignatureVisitor;Lgroovyjarjarasm/asm/commons/Remapper;)V
    .locals 0

    .line 69
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;-><init>(I)V

    .line 46
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->classNames:Ljava/util/ArrayList;

    .line 70
    iput-object p2, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    .line 71
    iput-object p3, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->remapper:Lgroovyjarjarasm/asm/commons/Remapper;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/signature/SignatureVisitor;Lgroovyjarjarasm/asm/commons/Remapper;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 56
    invoke-direct {p0, v0, p1, p2}, Lgroovyjarjarasm/asm/commons/SignatureRemapper;-><init>(ILgroovyjarjarasm/asm/signature/SignatureVisitor;Lgroovyjarjarasm/asm/commons/Remapper;)V

    return-void
.end method


# virtual methods
.method public visitArrayType()Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 106
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitArrayType()Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    return-object p0
.end method

.method public visitBaseType(C)V
    .locals 1

    .line 112
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitBaseType(C)V

    return-void
.end method

.method public visitClassBound()Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 117
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitClassBound()Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    return-object p0
.end method

.method public visitClassType(Ljava/lang/String;)V
    .locals 2

    .line 76
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->classNames:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 77
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->remapper:Lgroovyjarjarasm/asm/commons/Remapper;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitClassType(Ljava/lang/String;)V

    return-void
.end method

.method public visitEnd()V
    .locals 2

    .line 170
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitEnd()V

    .line 171
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->classNames:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    return-void
.end method

.method public visitExceptionType()Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 123
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitExceptionType()Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    return-object p0
.end method

.method public visitFormalTypeParameter(Ljava/lang/String;)V
    .locals 1

    .line 96
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitFormalTypeParameter(Ljava/lang/String;)V

    return-void
.end method

.method public visitInnerClassType(Ljava/lang/String;)V
    .locals 4

    .line 82
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->classNames:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    .line 83
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const/16 v2, 0x24

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 84
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->classNames:Ljava/util/ArrayList;

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 85
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v3, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->remapper:Lgroovyjarjarasm/asm/commons/Remapper;

    invoke-virtual {v3, v0}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 86
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->remapper:Lgroovyjarjarasm/asm/commons/Remapper;

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/commons/Remapper;->mapType(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 88
    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 89
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    goto :goto_0

    .line 90
    :cond_0
    invoke-virtual {p1, v2}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    .line 91
    :goto_0
    iget-object v1, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitInnerClassType(Ljava/lang/String;)V

    return-void
.end method

.method public visitInterface()Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 129
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitInterface()Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    return-object p0
.end method

.method public visitInterfaceBound()Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 135
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitInterfaceBound()Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    return-object p0
.end method

.method public visitParameterType()Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 141
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitParameterType()Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    return-object p0
.end method

.method public visitReturnType()Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 147
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitReturnType()Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    return-object p0
.end method

.method public visitSuperclass()Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 153
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitSuperclass()Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    return-object p0
.end method

.method public visitTypeArgument(C)Lgroovyjarjarasm/asm/signature/SignatureVisitor;
    .locals 1

    .line 164
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitTypeArgument(C)Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    return-object p0
.end method

.method public visitTypeArgument()V
    .locals 1

    .line 159
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitTypeArgument()V

    return-void
.end method

.method public visitTypeVariable(Ljava/lang/String;)V
    .locals 1

    .line 101
    iget-object v0, p0, Lgroovyjarjarasm/asm/commons/SignatureRemapper;->signatureVisitor:Lgroovyjarjarasm/asm/signature/SignatureVisitor;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/signature/SignatureVisitor;->visitTypeVariable(Ljava/lang/String;)V

    return-void
.end method
