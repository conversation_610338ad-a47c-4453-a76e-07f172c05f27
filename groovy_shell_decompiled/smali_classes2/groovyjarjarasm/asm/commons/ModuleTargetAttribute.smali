.class public final Lgroovyjarjarasm/asm/commons/ModuleTargetAttribute;
.super Lgroovyjarjarasm/asm/Attribute;
.source "ModuleTargetAttribute.java"


# instance fields
.field public platform:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 62
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/commons/ModuleTargetAttribute;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 1

    const-string v0, "ModuleTarget"

    .line 53
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/Attribute;-><init>(Ljava/lang/String;)V

    .line 54
    iput-object p1, p0, Lgroovyjarjarasm/asm/commons/ModuleTargetAttribute;->platform:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method protected read(Lgroovyjarjarasm/asm/ClassReader;II[CI[Lgroovyjarjarasm/asm/Label;)Lgroovyjarjarasm/asm/Attribute;
    .locals 0

    .line 73
    new-instance p3, Lgroovyjarjarasm/asm/commons/ModuleTargetAttribute;

    invoke-virtual {p1, p2, p4}, Lgroovyjarjarasm/asm/ClassReader;->readUTF8(I[C)Ljava/lang/String;

    move-result-object p1

    invoke-direct {p3, p1}, Lgroovyjarjarasm/asm/commons/ModuleTargetAttribute;-><init>(Ljava/lang/String;)V

    return-object p3
.end method

.method protected write(Lgroovyjarjarasm/asm/ClassWriter;[BIII)Lgroovyjarjarasm/asm/ByteVector;
    .locals 0

    .line 83
    new-instance p2, Lgroovyjarjarasm/asm/ByteVector;

    invoke-direct {p2}, Lgroovyjarjarasm/asm/ByteVector;-><init>()V

    .line 84
    iget-object p3, p0, Lgroovyjarjarasm/asm/commons/ModuleTargetAttribute;->platform:Ljava/lang/String;

    if-nez p3, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {p1, p3}, Lgroovyjarjarasm/asm/ClassWriter;->newUTF8(Ljava/lang/String;)I

    move-result p1

    :goto_0
    invoke-virtual {p2, p1}, Lgroovyjarjarasm/asm/ByteVector;->putShort(I)Lgroovyjarjarasm/asm/ByteVector;

    return-object p2
.end method
