.class final Lgroovyjarjarasm/asm/Constants;
.super Ljava/lang/Object;
.source "Constants.java"


# static fields
.field static final ACC_CONSTRUCTOR:I = 0x40000

.field static final ALOAD_0:I = 0x2a

.field static final ALOAD_1:I = 0x2b

.field static final ALOAD_2:I = 0x2c

.field static final ALOAD_3:I = 0x2d

.field static final ANNOTATION_DEFAULT:Ljava/lang/String; = "AnnotationDefault"

.field static final ASM_GOTO:I = 0xd8

.field static final ASM_GOTO_W:I = 0xdc

.field static final ASM_IFEQ:I = 0xca

.field static final ASM_IFGE:I = 0xcd

.field static final ASM_IFGT:I = 0xce

.field static final ASM_IFLE:I = 0xcf

.field static final ASM_IFLT:I = 0xcc

.field static final ASM_IFNE:I = 0xcb

.field static final ASM_IFNONNULL:I = 0xdb

.field static final ASM_IFNULL:I = 0xda

.field static final ASM_IFNULL_OPCODE_DELTA:I = 0x14

.field static final ASM_IF_ACMPEQ:I = 0xd6

.field static final ASM_IF_ACMPNE:I = 0xd7

.field static final ASM_IF_ICMPEQ:I = 0xd0

.field static final ASM_IF_ICMPGE:I = 0xd3

.field static final ASM_IF_ICMPGT:I = 0xd4

.field static final ASM_IF_ICMPLE:I = 0xd5

.field static final ASM_IF_ICMPLT:I = 0xd2

.field static final ASM_IF_ICMPNE:I = 0xd1

.field static final ASM_JSR:I = 0xd9

.field static final ASM_OPCODE_DELTA:I = 0x31

.field static final ASTORE_0:I = 0x4b

.field static final ASTORE_1:I = 0x4c

.field static final ASTORE_2:I = 0x4d

.field static final ASTORE_3:I = 0x4e

.field static final BOOTSTRAP_METHODS:Ljava/lang/String; = "BootstrapMethods"

.field static final CODE:Ljava/lang/String; = "Code"

.field static final CONSTANT_VALUE:Ljava/lang/String; = "ConstantValue"

.field static final DEPRECATED:Ljava/lang/String; = "Deprecated"

.field static final DLOAD_0:I = 0x26

.field static final DLOAD_1:I = 0x27

.field static final DLOAD_2:I = 0x28

.field static final DLOAD_3:I = 0x29

.field static final DSTORE_0:I = 0x47

.field static final DSTORE_1:I = 0x48

.field static final DSTORE_2:I = 0x49

.field static final DSTORE_3:I = 0x4a

.field static final ENCLOSING_METHOD:Ljava/lang/String; = "EnclosingMethod"

.field static final EXCEPTIONS:Ljava/lang/String; = "Exceptions"

.field static final FLOAD_0:I = 0x22

.field static final FLOAD_1:I = 0x23

.field static final FLOAD_2:I = 0x24

.field static final FLOAD_3:I = 0x25

.field static final FSTORE_0:I = 0x43

.field static final FSTORE_1:I = 0x44

.field static final FSTORE_2:I = 0x45

.field static final FSTORE_3:I = 0x46

.field static final F_INSERT:I = 0x100

.field static final GOTO_W:I = 0xc8

.field static final ILOAD_0:I = 0x1a

.field static final ILOAD_1:I = 0x1b

.field static final ILOAD_2:I = 0x1c

.field static final ILOAD_3:I = 0x1d

.field static final INNER_CLASSES:Ljava/lang/String; = "InnerClasses"

.field static final ISTORE_0:I = 0x3b

.field static final ISTORE_1:I = 0x3c

.field static final ISTORE_2:I = 0x3d

.field static final ISTORE_3:I = 0x3e

.field static final JSR_W:I = 0xc9

.field static final LDC2_W:I = 0x14

.field static final LDC_W:I = 0x13

.field static final LINE_NUMBER_TABLE:Ljava/lang/String; = "LineNumberTable"

.field static final LLOAD_0:I = 0x1e

.field static final LLOAD_1:I = 0x1f

.field static final LLOAD_2:I = 0x20

.field static final LLOAD_3:I = 0x21

.field static final LOCAL_VARIABLE_TABLE:Ljava/lang/String; = "LocalVariableTable"

.field static final LOCAL_VARIABLE_TYPE_TABLE:Ljava/lang/String; = "LocalVariableTypeTable"

.field static final LSTORE_0:I = 0x3f

.field static final LSTORE_1:I = 0x40

.field static final LSTORE_2:I = 0x41

.field static final LSTORE_3:I = 0x42

.field static final METHOD_PARAMETERS:Ljava/lang/String; = "MethodParameters"

.field static final MODULE:Ljava/lang/String; = "Module"

.field static final MODULE_MAIN_CLASS:Ljava/lang/String; = "ModuleMainClass"

.field static final MODULE_PACKAGES:Ljava/lang/String; = "ModulePackages"

.field static final NEST_HOST:Ljava/lang/String; = "NestHost"

.field static final NEST_MEMBERS:Ljava/lang/String; = "NestMembers"

.field static final PERMITTED_SUBCLASSES:Ljava/lang/String; = "PermittedSubclasses"

.field static final RECORD:Ljava/lang/String; = "Record"

.field static final RUNTIME_INVISIBLE_ANNOTATIONS:Ljava/lang/String; = "RuntimeInvisibleAnnotations"

.field static final RUNTIME_INVISIBLE_PARAMETER_ANNOTATIONS:Ljava/lang/String; = "RuntimeInvisibleParameterAnnotations"

.field static final RUNTIME_INVISIBLE_TYPE_ANNOTATIONS:Ljava/lang/String; = "RuntimeInvisibleTypeAnnotations"

.field static final RUNTIME_VISIBLE_ANNOTATIONS:Ljava/lang/String; = "RuntimeVisibleAnnotations"

.field static final RUNTIME_VISIBLE_PARAMETER_ANNOTATIONS:Ljava/lang/String; = "RuntimeVisibleParameterAnnotations"

.field static final RUNTIME_VISIBLE_TYPE_ANNOTATIONS:Ljava/lang/String; = "RuntimeVisibleTypeAnnotations"

.field static final SIGNATURE:Ljava/lang/String; = "Signature"

.field static final SOURCE_DEBUG_EXTENSION:Ljava/lang/String; = "SourceDebugExtension"

.field static final SOURCE_FILE:Ljava/lang/String; = "SourceFile"

.field static final STACK_MAP_TABLE:Ljava/lang/String; = "StackMapTable"

.field static final SYNTHETIC:Ljava/lang/String; = "Synthetic"

.field static final WIDE:I = 0xc4

.field static final WIDE_JUMP_OPCODE_DELTA:I = 0x21


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 183
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static checkAsmExperimental(Ljava/lang/Object;)V
    .locals 3

    .line 186
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    .line 187
    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    const/16 v1, 0x2e

    const/16 v2, 0x2f

    invoke-virtual {v0, v1, v2}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object v0

    .line 188
    invoke-static {v0}, Lgroovyjarjarasm/asm/Constants;->isWhitelisted(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_0

    .line 189
    invoke-virtual {p0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object p0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".class"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/lang/ClassLoader;->getResourceAsStream(Ljava/lang/String;)Ljava/io/InputStream;

    move-result-object p0

    invoke-static {p0}, Lgroovyjarjarasm/asm/Constants;->checkIsPreview(Ljava/io/InputStream;)V

    :cond_0
    return-void
.end method

.method static checkIsPreview(Ljava/io/InputStream;)V
    .locals 2

    if-eqz p0, :cond_1

    .line 210
    :try_start_0
    new-instance v0, Ljava/io/DataInputStream;

    invoke-direct {v0, p0}, Ljava/io/DataInputStream;-><init>(Ljava/io/InputStream;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 211
    :try_start_1
    invoke-virtual {v0}, Ljava/io/DataInputStream;->readInt()I

    .line 212
    invoke-virtual {v0}, Ljava/io/DataInputStream;->readUnsignedShort()I

    move-result p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 213
    :try_start_2
    invoke-virtual {v0}, Ljava/io/DataInputStream;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    const v0, 0xffff

    if-ne p0, v0, :cond_0

    return-void

    .line 217
    :cond_0
    new-instance p0, Ljava/lang/IllegalStateException;

    const-string v0, "ASM9_EXPERIMENTAL can only be used by classes compiled with --enable-preview"

    invoke-direct {p0, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p0

    :catchall_0
    move-exception p0

    .line 210
    :try_start_3
    invoke-virtual {v0}, Ljava/io/DataInputStream;->close()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    :catchall_1
    :try_start_4
    throw p0
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_0

    :catch_0
    move-exception p0

    .line 214
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "I/O error, can\'t check class version"

    invoke-direct {v0, v1, p0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0

    .line 207
    :cond_1
    new-instance p0, Ljava/lang/IllegalStateException;

    const-string v0, "Bytecode not available, can\'t check class version"

    invoke-direct {p0, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method static isWhitelisted(Ljava/lang/String;)Z
    .locals 4

    const-string v0, "groovyjarjarasm/asm/"

    .line 194
    invoke-virtual {p0, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    const-string v0, "(Annotation|Class|Field|Method|Module|RecordComponent|Signature)"

    const-string v2, "Test$"

    .line 198
    invoke-virtual {p0, v2}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "groovyjarjarasm/asm/util/Trace"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "Visitor(\\$.*)?"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 199
    invoke-static {v2, p0}, Ljava/util/regex/Pattern;->matches(Ljava/lang/String;Ljava/lang/CharSequence;)Z

    move-result v2

    if-nez v2, :cond_1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "groovyjarjarasm/asm/util/Check"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "Adapter(\\$.*)?"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 201
    invoke-static {v0, p0}, Ljava/util/regex/Pattern;->matches(Ljava/lang/String;Ljava/lang/CharSequence;)Z

    move-result p0

    if-eqz p0, :cond_2

    :cond_1
    const/4 v1, 0x1

    :cond_2
    return v1
.end method
