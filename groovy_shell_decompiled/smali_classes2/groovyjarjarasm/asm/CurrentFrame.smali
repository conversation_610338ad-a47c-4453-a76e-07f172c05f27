.class final Lgroovyjarjarasm/asm/CurrentFrame;
.super Lgroovyjarjarasm/asm/Frame;
.source "CurrentFrame.java"


# direct methods
.method constructor <init>(Lgroovyjarjarasm/asm/Label;)V
    .locals 0

    .line 40
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/Frame;-><init>(Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method


# virtual methods
.method execute(IILgroovyjarjarasm/asm/Symbol;Lgroovyjarjarasm/asm/SymbolTable;)V
    .locals 0

    .line 51
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/Frame;->execute(IILgroovyjarjarasm/asm/Symbol;Lgroovyjarjarasm/asm/SymbolTable;)V

    .line 52
    new-instance p1, Lgroovyjarjarasm/asm/Frame;

    const/4 p2, 0x0

    invoke-direct {p1, p2}, Lgroovyjarjarasm/asm/Frame;-><init>(Lgroovyjarjarasm/asm/Label;)V

    const/4 p2, 0x0

    .line 53
    invoke-virtual {p0, p4, p1, p2}, Lgroovyjarjarasm/asm/CurrentFrame;->merge(Lgroovyjarjarasm/asm/SymbolTable;Lgroovyjarjarasm/asm/Frame;I)Z

    .line 54
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/CurrentFrame;->copyFrom(Lgroovyjarjarasm/asm/Frame;)V

    return-void
.end method
