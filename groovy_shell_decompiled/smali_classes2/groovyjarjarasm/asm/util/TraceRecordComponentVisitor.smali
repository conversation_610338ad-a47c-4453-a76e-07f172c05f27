.class public final Lgroovyjarjarasm/asm/util/TraceRecordComponentVisitor;
.super Lgroovyjarjarasm/asm/RecordComponentVisitor;
.source "TraceRecordComponentVisitor.java"


# instance fields
.field public final printer:Lgroovyjarjarasm/asm/util/Printer;


# direct methods
.method public constructor <init>(Lgroovyjarjarasm/asm/RecordComponentVisitor;Lgroovyjarjarasm/asm/util/Printer;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 65
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/RecordComponentVisitor;-><init>(ILgroovyjarjarasm/asm/RecordComponentVisitor;)V

    .line 66
    iput-object p2, p0, Lgroovyjarjarasm/asm/util/TraceRecordComponentVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/util/Printer;)V
    .locals 1

    const/4 v0, 0x0

    .line 53
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/util/TraceRecordComponentVisitor;-><init>(Lgroovyjarjarasm/asm/RecordComponentVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-void
.end method


# virtual methods
.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 71
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceRecordComponentVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitRecordComponentAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 72
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 73
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/RecordComponentVisitor;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 1

    .line 87
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceRecordComponentVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitRecordComponentAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    .line 88
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/RecordComponentVisitor;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitEnd()V
    .locals 1

    .line 93
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceRecordComponentVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/Printer;->visitRecordComponentEnd()V

    .line 94
    invoke-super {p0}, Lgroovyjarjarasm/asm/RecordComponentVisitor;->visitEnd()V

    return-void
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 79
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceRecordComponentVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    .line 80
    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitRecordComponentTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 81
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 82
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/RecordComponentVisitor;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method
