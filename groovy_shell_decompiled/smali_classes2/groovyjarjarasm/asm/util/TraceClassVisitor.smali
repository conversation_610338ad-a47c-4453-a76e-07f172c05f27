.class public final Lgroovyjarjarasm/asm/util/TraceClassVisitor;
.super Lgroovyjarjarasm/asm/ClassVisitor;
.source "TraceClassVisitor.java"


# instance fields
.field public final p:Lgroovyjarjarasm/asm/util/Printer;

.field private final printWriter:Ljava/io/PrintWriter;


# direct methods
.method public constructor <init>(Lgroovyjarjarasm/asm/ClassVisitor;Lgroovyjarjarasm/asm/util/Printer;Ljava/io/PrintWriter;)V
    .locals 1

    const/high16 v0, 0x10a0000

    .line 122
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/ClassVisitor;-><init>(ILgroovyjarjarasm/asm/ClassVisitor;)V

    .line 123
    iput-object p3, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->printWriter:Ljava/io/PrintWriter;

    .line 124
    iput-object p2, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/ClassVisitor;Ljava/io/PrintWriter;)V
    .locals 1

    .line 110
    new-instance v0, Lgroovyjarjarasm/asm/util/Textifier;

    invoke-direct {v0}, Lgroovyjarjarasm/asm/util/Textifier;-><init>()V

    invoke-direct {p0, p1, v0, p2}, Lgroovyjarjarasm/asm/util/TraceClassVisitor;-><init>(Lgroovyjarjarasm/asm/ClassVisitor;Lgroovyjarjarasm/asm/util/Printer;Ljava/io/PrintWriter;)V

    return-void
.end method

.method public constructor <init>(Ljava/io/PrintWriter;)V
    .locals 1

    const/4 v0, 0x0

    .line 100
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/util/TraceClassVisitor;-><init>(Lgroovyjarjarasm/asm/ClassVisitor;Ljava/io/PrintWriter;)V

    return-void
.end method


# virtual methods
.method public visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 7

    .line 135
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    move v1, p1

    move v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    invoke-virtual/range {v0 .. v6}, Lgroovyjarjarasm/asm/util/Printer;->visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    .line 136
    invoke-super/range {p0 .. p6}, Lgroovyjarjarasm/asm/ClassVisitor;->visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V

    return-void
.end method

.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 165
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitClassAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 166
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 167
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/ClassVisitor;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 1

    .line 180
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitClassAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    .line 181
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/ClassVisitor;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitEnd()V
    .locals 2

    .line 237
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/Printer;->visitClassEnd()V

    .line 238
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->printWriter:Ljava/io/PrintWriter;

    if-eqz v0, :cond_0

    .line 239
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v1, v0}, Lgroovyjarjarasm/asm/util/Printer;->print(Ljava/io/PrintWriter;)V

    .line 240
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->printWriter:Ljava/io/PrintWriter;

    invoke-virtual {v0}, Ljava/io/PrintWriter;->flush()V

    .line 242
    :cond_0
    invoke-super {p0}, Lgroovyjarjarasm/asm/ClassVisitor;->visitEnd()V

    return-void
.end method

.method public visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/FieldVisitor;
    .locals 6

    .line 218
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    move v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/util/Printer;->visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 219
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceFieldVisitor;

    .line 220
    invoke-super/range {p0 .. p5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/FieldVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceFieldVisitor;-><init>(Lgroovyjarjarasm/asm/FieldVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitInnerClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 1

    .line 199
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitInnerClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V

    .line 200
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/ClassVisitor;->visitInnerClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method

.method public visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;
    .locals 6

    .line 230
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    move v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/util/Printer;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 231
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;

    .line 232
    invoke-super/range {p0 .. p5}, Lgroovyjarjarasm/asm/ClassVisitor;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/MethodVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;-><init>(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitModule(Ljava/lang/String;ILjava/lang/String;)Lgroovyjarjarasm/asm/ModuleVisitor;
    .locals 2

    .line 147
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/Printer;->visitModule(Ljava/lang/String;ILjava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 148
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceModuleVisitor;

    invoke-super {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/ClassVisitor;->visitModule(Ljava/lang/String;ILjava/lang/String;)Lgroovyjarjarasm/asm/ModuleVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceModuleVisitor;-><init>(Lgroovyjarjarasm/asm/ModuleVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitNestHost(Ljava/lang/String;)V
    .locals 1

    .line 153
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitNestHost(Ljava/lang/String;)V

    .line 154
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/ClassVisitor;->visitNestHost(Ljava/lang/String;)V

    return-void
.end method

.method public visitNestMember(Ljava/lang/String;)V
    .locals 1

    .line 186
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitNestMember(Ljava/lang/String;)V

    .line 187
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/ClassVisitor;->visitNestMember(Ljava/lang/String;)V

    return-void
.end method

.method public visitOuterClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 159
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/Printer;->visitOuterClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 160
    invoke-super {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/ClassVisitor;->visitOuterClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public visitPermittedSubclass(Ljava/lang/String;)V
    .locals 1

    .line 192
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitPermittedSubclass(Ljava/lang/String;)V

    .line 193
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/ClassVisitor;->visitPermittedSubclass(Ljava/lang/String;)V

    return-void
.end method

.method public visitRecordComponent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/RecordComponentVisitor;
    .locals 2

    .line 206
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/Printer;->visitRecordComponent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 207
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceRecordComponentVisitor;

    .line 208
    invoke-super {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/ClassVisitor;->visitRecordComponent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/RecordComponentVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceRecordComponentVisitor;-><init>(Lgroovyjarjarasm/asm/RecordComponentVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitSource(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 141
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitSource(Ljava/lang/String;Ljava/lang/String;)V

    .line 142
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/ClassVisitor;->visitSource(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 173
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceClassVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitClassTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 174
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 175
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/ClassVisitor;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method
