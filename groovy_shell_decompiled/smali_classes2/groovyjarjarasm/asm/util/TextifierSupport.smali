.class public interface abstract Lgroovyjarjarasm/asm/util/TextifierSupport;
.super Ljava/lang/Object;
.source "TextifierSupport.java"


# virtual methods
.method public abstract textify(Ljava/lang/StringBuilder;Ljava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/StringBuilder;",
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/Label;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation
.end method
