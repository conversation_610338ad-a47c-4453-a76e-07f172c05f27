.class public Lgroovyjarjarasm/asm/util/ASMifier;
.super Lgroovyjarjarasm/asm/util/Printer;
.source "ASMifier.java"


# static fields
.field private static final ACCESS_CLASS:I = 0x40000

.field private static final ACCESS_FIELD:I = 0x80000

.field private static final ACCESS_INNER:I = 0x100000

.field private static final ACCESS_MODULE:I = 0x200000

.field private static final ANNOTATION_VISITOR:Ljava/lang/String; = "annotationVisitor"

.field private static final ANNOTATION_VISITOR0:Ljava/lang/String; = "annotationVisitor0 = "

.field private static final CLASS_VERSIONS:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private static final COMMA:Ljava/lang/String; = "\", \""

.field private static final END_ARRAY:Ljava/lang/String; = " });\n"

.field private static final END_PARAMETERS:Ljava/lang/String; = ");\n\n"

.field private static final FRAME_TYPES:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private static final NEW_OBJECT_ARRAY:Ljava/lang/String; = ", new Object[] {"

.field private static final USAGE:Ljava/lang/String; = "Prints the ASM code to generate the given class.\nUsage: ASMifier [-nodebug] <fully qualified class name or class file name>"

.field private static final VISIT_END:Ljava/lang/String; = ".visitEnd();\n"


# instance fields
.field protected final id:I

.field protected labelNames:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/Label;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field protected final name:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    const-string v0, "Opcodes.TOP"

    const-string v1, "Opcodes.INTEGER"

    const-string v2, "Opcodes.FLOAT"

    const-string v3, "Opcodes.DOUBLE"

    const-string v4, "Opcodes.LONG"

    const-string v5, "Opcodes.NULL"

    const-string v6, "Opcodes.UNINITIALIZED_THIS"

    .line 78
    filled-new-array/range {v0 .. v6}, [Ljava/lang/String;

    move-result-object v0

    .line 80
    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    .line 79
    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/util/ASMifier;->FRAME_TYPES:Ljava/util/List;

    .line 92
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    const v1, 0x3002d

    .line 93
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V1_1"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x2e

    .line 94
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V1_2"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x2f

    .line 95
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V1_3"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x30

    .line 96
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V1_4"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x31

    .line 97
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V1_5"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x32

    .line 98
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V1_6"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x33

    .line 99
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V1_7"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x34

    .line 100
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V1_8"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x35

    .line 101
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V9"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x36

    .line 102
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V10"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x37

    .line 103
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V11"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x38

    .line 104
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V12"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x39

    .line 105
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V13"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3a

    .line 106
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V14"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3b

    .line 107
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V15"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3c

    .line 108
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V16"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3d

    .line 109
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V17"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3e

    .line 110
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V18"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3f

    .line 111
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V19"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x40

    .line 112
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V20"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x41

    .line 113
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "V21"

    invoke-virtual {v0, v1, v2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 114
    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/util/ASMifier;->CLASS_VERSIONS:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    const/high16 v0, 0x90000

    const-string v1, "classWriter"

    const/4 v2, 0x0

    .line 133
    invoke-direct {p0, v0, v1, v2}, Lgroovyjarjarasm/asm/util/ASMifier;-><init>(ILjava/lang/String;I)V

    .line 134
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovyjarjarasm/asm/util/ASMifier;

    if-ne v0, v1, :cond_0

    return-void

    .line 135
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method

.method protected constructor <init>(ILjava/lang/String;I)V
    .locals 0

    .line 149
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Printer;-><init>(I)V

    .line 150
    iput-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 151
    iput p3, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    return-void
.end method

.method private appendAccessFlags(I)V
    .locals 8

    and-int/lit8 v0, p1, 0x1

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 1276
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_PUBLIC"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    :goto_0
    and-int/lit8 v2, p1, 0x2

    if-eqz v2, :cond_1

    .line 1280
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_PRIVATE"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_1
    and-int/lit8 v2, p1, 0x4

    if-eqz v2, :cond_2

    .line 1284
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_PROTECTED"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_2
    and-int/lit8 v2, p1, 0x10

    const-string v3, " | "

    if-eqz v2, :cond_4

    if-nez v0, :cond_3

    .line 1289
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1291
    :cond_3
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_FINAL"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_4
    and-int/lit8 v2, p1, 0x8

    if-eqz v2, :cond_6

    if-nez v0, :cond_5

    .line 1296
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1298
    :cond_5
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_STATIC"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_6
    and-int/lit8 v2, p1, 0x20

    const/high16 v4, 0x200000

    const/high16 v5, 0x40000

    if-eqz v2, :cond_a

    if-nez v0, :cond_7

    .line 1304
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_7
    and-int v0, p1, v5

    if-nez v0, :cond_9

    and-int v0, p1, v4

    if-nez v0, :cond_8

    .line 1308
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_SYNCHRONIZED"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 1310
    :cond_8
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_TRANSITIVE"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 1313
    :cond_9
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_SUPER"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :goto_1
    move v0, v1

    :cond_a
    and-int/lit8 v2, p1, 0x40

    const/high16 v6, 0x80000

    if-eqz v2, :cond_e

    if-nez v0, :cond_b

    .line 1320
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_b
    and-int v0, p1, v6

    if-nez v0, :cond_d

    and-int v0, p1, v4

    if-nez v0, :cond_c

    .line 1324
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_BRIDGE"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_2

    .line 1326
    :cond_c
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_STATIC_PHASE"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_2

    .line 1329
    :cond_d
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_VOLATILE"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :goto_2
    move v0, v1

    :cond_e
    and-int/lit16 v2, p1, 0x80

    const/high16 v4, 0xc0000

    if-eqz v2, :cond_10

    and-int v7, p1, v4

    if-nez v7, :cond_10

    if-nez v0, :cond_f

    .line 1336
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1338
    :cond_f
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v7, "ACC_VARARGS"

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_10
    if-eqz v2, :cond_12

    and-int v2, p1, v6

    if-eqz v2, :cond_12

    if-nez v0, :cond_11

    .line 1343
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1345
    :cond_11
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_TRANSIENT"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_12
    and-int/lit16 v2, p1, 0x100

    if-eqz v2, :cond_14

    and-int v2, p1, v4

    if-nez v2, :cond_14

    if-nez v0, :cond_13

    .line 1351
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1353
    :cond_13
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_NATIVE"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_14
    and-int/lit16 v2, p1, 0x4000

    if-eqz v2, :cond_16

    const/high16 v2, 0x1c0000

    and-int/2addr v2, p1

    if-eqz v2, :cond_16

    if-nez v0, :cond_15

    .line 1359
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1361
    :cond_15
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_ENUM"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_16
    and-int/lit16 v2, p1, 0x2000

    if-eqz v2, :cond_18

    const/high16 v2, 0x140000

    and-int/2addr v2, p1

    if-eqz v2, :cond_18

    if-nez v0, :cond_17

    .line 1367
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1369
    :cond_17
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_ANNOTATION"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_18
    and-int/lit16 v2, p1, 0x400

    if-eqz v2, :cond_1a

    if-nez v0, :cond_19

    .line 1374
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1376
    :cond_19
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_ABSTRACT"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_1a
    and-int/lit16 v2, p1, 0x200

    if-eqz v2, :cond_1c

    if-nez v0, :cond_1b

    .line 1381
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1383
    :cond_1b
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_INTERFACE"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_1c
    and-int/lit16 v2, p1, 0x800

    if-eqz v2, :cond_1e

    if-nez v0, :cond_1d

    .line 1388
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1390
    :cond_1d
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_STRICT"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_1e
    and-int/lit16 v2, p1, 0x1000

    if-eqz v2, :cond_20

    if-nez v0, :cond_1f

    .line 1395
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1397
    :cond_1f
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_SYNTHETIC"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_20
    const/high16 v2, 0x20000

    and-int/2addr v2, p1

    if-eqz v2, :cond_22

    if-nez v0, :cond_21

    .line 1402
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1404
    :cond_21
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_DEPRECATED"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_22
    const/high16 v2, 0x10000

    and-int/2addr v2, p1

    if-eqz v2, :cond_24

    if-nez v0, :cond_23

    .line 1409
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1411
    :cond_23
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ACC_RECORD"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move v0, v1

    :cond_24
    const v2, 0x8000

    and-int/2addr v2, p1

    if-eqz v2, :cond_27

    if-nez v0, :cond_25

    .line 1416
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_25
    and-int/2addr p1, v5

    if-nez p1, :cond_26

    .line 1419
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "ACC_MANDATED"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_3

    .line 1421
    :cond_26
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "ACC_MODULE"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_3

    :cond_27
    move v1, v0

    :goto_3
    if-eqz v1, :cond_28

    .line 1426
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x30

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_28
    return-void
.end method

.method private appendFrameTypes(I[Ljava/lang/Object;)V
    .locals 4

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p1, :cond_3

    if-lez v0, :cond_0

    .line 1572
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, ", "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1574
    :cond_0
    aget-object v1, p2, v0

    instance-of v1, v1, Ljava/lang/String;

    if-eqz v1, :cond_1

    .line 1575
    aget-object v1, p2, v0

    invoke-virtual {p0, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    goto :goto_1

    .line 1576
    :cond_1
    aget-object v1, p2, v0

    instance-of v1, v1, Ljava/lang/Integer;

    if-eqz v1, :cond_2

    .line 1577
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    sget-object v2, Lgroovyjarjarasm/asm/util/ASMifier;->FRAME_TYPES:Ljava/util/List;

    aget-object v3, p2, v0

    check-cast v3, Ljava/lang/Integer;

    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    move-result v3

    invoke-interface {v2, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 1579
    :cond_2
    aget-object v1, p2, v0

    check-cast v1, Lgroovyjarjarasm/asm/Label;

    invoke-virtual {p0, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    :goto_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method private declareFrameTypes(I[Ljava/lang/Object;)V
    .locals 2

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p1, :cond_1

    .line 1556
    aget-object v1, p2, v0

    instance-of v1, v1, Lgroovyjarjarasm/asm/Label;

    if-eqz v1, :cond_0

    .line 1557
    aget-object v1, p2, v0

    check-cast v1, Lgroovyjarjarasm/asm/Label;

    invoke-virtual {p0, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public static main([Ljava/lang/String;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 163
    new-instance v0, Ljava/io/PrintWriter;

    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Ljava/io/PrintWriter;-><init>(Ljava/io/OutputStream;Z)V

    new-instance v1, Ljava/io/PrintWriter;

    sget-object v3, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-direct {v1, v3, v2}, Ljava/io/PrintWriter;-><init>(Ljava/io/OutputStream;Z)V

    invoke-static {p0, v0, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->main([Ljava/lang/String;Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V

    return-void
.end method

.method static main([Ljava/lang/String;Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 178
    new-instance v0, Lgroovyjarjarasm/asm/util/ASMifier;

    invoke-direct {v0}, Lgroovyjarjarasm/asm/util/ASMifier;-><init>()V

    const-string v1, "Prints the ASM code to generate the given class.\nUsage: ASMifier [-nodebug] <fully qualified class name or class file name>"

    invoke-static {p0, v1, v0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->main([Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/util/Printer;Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V

    return-void
.end method

.method private varargs visitExportOrOpen(Ljava/lang/String;Ljava/lang/String;I[Ljava/lang/String;)V
    .locals 2

    .line 495
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 496
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 497
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 498
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ", "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/high16 p1, 0x200000

    or-int/2addr p1, p3

    .line 499
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendAccessFlags(I)V

    if-eqz p4, :cond_2

    .line 500
    array-length p1, p4

    if-lez p1, :cond_2

    .line 501
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p3, ", new String[] {"

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 502
    :goto_0
    array-length p1, p4

    if-ge v1, p1, :cond_1

    .line 503
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v1, :cond_0

    const-string p3, " "

    goto :goto_1

    :cond_0
    move-object p3, p2

    :goto_1
    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 504
    aget-object p1, p4, v1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 506
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " }"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 508
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 509
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method private visitMemberEnd()V
    .locals 2

    .line 1246
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1247
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitEnd();\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1248
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method


# virtual methods
.method protected appendConstant(Ljava/lang/Object;)V
    .locals 8

    if-nez p1, :cond_0

    .line 1439
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "null"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1440
    :cond_0
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_1

    .line 1441
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    check-cast p1, Ljava/lang/String;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendString(Ljava/lang/StringBuilder;Ljava/lang/String;)V

    goto/16 :goto_12

    .line 1442
    :cond_1
    instance-of v0, p1, Lgroovyjarjarasm/asm/Type;

    const-string v1, "\")"

    if-eqz v0, :cond_2

    .line 1443
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "Type.getType(\""

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1444
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    check-cast p1, Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1445
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1446
    :cond_2
    instance-of v0, p1, Lgroovyjarjarasm/asm/Handle;

    const-string v2, "\", "

    const-string v3, "\", \""

    const/16 v4, 0x29

    if-eqz v0, :cond_3

    .line 1447
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "new Handle("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1448
    check-cast p1, Lgroovyjarjarasm/asm/Handle;

    .line 1449
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "Opcodes."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/ASMifier;->HANDLE_TAG:[Ljava/lang/String;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getTag()I

    move-result v5

    aget-object v1, v1, v5

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", \""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1450
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getOwner()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1451
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1452
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getDesc()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1453
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->isInterface()Z

    move-result p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1454
    :cond_3
    instance-of v0, p1, Lgroovyjarjarasm/asm/ConstantDynamic;

    const/4 v5, 0x0

    if-eqz v0, :cond_6

    .line 1455
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "new ConstantDynamic(\""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1456
    check-cast p1, Lgroovyjarjarasm/asm/ConstantDynamic;

    .line 1457
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1458
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getDescriptor()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1459
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getBootstrapMethod()Lgroovyjarjarasm/asm/Handle;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 1460
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, ", new Object[] {"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1461
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/ConstantDynamic;->getBootstrapMethodArgumentCount()I

    move-result v0

    :goto_0
    if-ge v5, v0, :cond_5

    .line 1463
    invoke-virtual {p1, v5}, Lgroovyjarjarasm/asm/ConstantDynamic;->getBootstrapMethodArgument(I)Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {p0, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    add-int/lit8 v1, v0, -0x1

    if-eq v5, v1, :cond_4

    .line 1465
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, ", "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_4
    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 1468
    :cond_5
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "})"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1469
    :cond_6
    instance-of v0, p1, Ljava/lang/Byte;

    if-eqz v0, :cond_7

    .line 1470
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "new Byte((byte)"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1471
    :cond_7
    instance-of v0, p1, Ljava/lang/Boolean;

    if-eqz v0, :cond_9

    .line 1472
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    if-eqz p1, :cond_8

    const-string p1, "Boolean.TRUE"

    goto :goto_1

    :cond_8
    const-string p1, "Boolean.FALSE"

    :goto_1
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1473
    :cond_9
    instance-of v0, p1, Ljava/lang/Short;

    if-eqz v0, :cond_a

    .line 1474
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "new Short((short)"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1475
    :cond_a
    instance-of v0, p1, Ljava/lang/Character;

    if-eqz v0, :cond_b

    .line 1476
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "new Character((char)"

    .line 1477
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    check-cast p1, Ljava/lang/Character;

    .line 1478
    invoke-virtual {p1}, Ljava/lang/Character;->charValue()C

    move-result p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1479
    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1480
    :cond_b
    instance-of v0, p1, Ljava/lang/Integer;

    if-eqz v0, :cond_c

    .line 1481
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "new Integer("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1482
    :cond_c
    instance-of v0, p1, Ljava/lang/Float;

    if-eqz v0, :cond_d

    .line 1483
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "new Float(\""

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1484
    :cond_d
    instance-of v0, p1, Ljava/lang/Long;

    if-eqz v0, :cond_e

    .line 1485
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "new Long("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "L)"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1486
    :cond_e
    instance-of v0, p1, Ljava/lang/Double;

    if-eqz v0, :cond_f

    .line 1487
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "new Double(\""

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1488
    :cond_f
    instance-of v0, p1, [B

    const-string v1, ""

    const-string v2, ","

    const/16 v3, 0x7d

    if-eqz v0, :cond_12

    .line 1489
    check-cast p1, [B

    .line 1490
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v4, "new byte[] {"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1491
    :goto_2
    array-length v0, p1

    if-ge v5, v0, :cond_11

    .line 1492
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v5, :cond_10

    move-object v4, v1

    goto :goto_3

    :cond_10
    move-object v4, v2

    :goto_3
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget-byte v4, p1, v5

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    goto :goto_2

    .line 1494
    :cond_11
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1495
    :cond_12
    instance-of v0, p1, [Z

    if-eqz v0, :cond_15

    .line 1496
    check-cast p1, [Z

    .line 1497
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v4, "new boolean[] {"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1498
    :goto_4
    array-length v0, p1

    if-ge v5, v0, :cond_14

    .line 1499
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v5, :cond_13

    move-object v4, v1

    goto :goto_5

    :cond_13
    move-object v4, v2

    :goto_5
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget-boolean v4, p1, v5

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    goto :goto_4

    .line 1501
    :cond_14
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1502
    :cond_15
    instance-of v0, p1, [S

    if-eqz v0, :cond_18

    .line 1503
    check-cast p1, [S

    .line 1504
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v4, "new short[] {"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1505
    :goto_6
    array-length v0, p1

    if-ge v5, v0, :cond_17

    .line 1506
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v5, :cond_16

    move-object v4, v1

    goto :goto_7

    :cond_16
    move-object v4, v2

    :goto_7
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v4, "(short)"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget-short v4, p1, v5

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    goto :goto_6

    .line 1508
    :cond_17
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1509
    :cond_18
    instance-of v0, p1, [C

    if-eqz v0, :cond_1b

    .line 1510
    check-cast p1, [C

    .line 1511
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v4, "new char[] {"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1512
    :goto_8
    array-length v0, p1

    if-ge v5, v0, :cond_1a

    .line 1513
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v5, :cond_19

    move-object v4, v1

    goto :goto_9

    :cond_19
    move-object v4, v2

    :goto_9
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v4, "(char)"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget-char v4, p1, v5

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    goto :goto_8

    .line 1515
    :cond_1a
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1516
    :cond_1b
    instance-of v0, p1, [I

    if-eqz v0, :cond_1e

    .line 1517
    check-cast p1, [I

    .line 1518
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v4, "new int[] {"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1519
    :goto_a
    array-length v0, p1

    if-ge v5, v0, :cond_1d

    .line 1520
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v5, :cond_1c

    move-object v4, v1

    goto :goto_b

    :cond_1c
    move-object v4, v2

    :goto_b
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget v4, p1, v5

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    goto :goto_a

    .line 1522
    :cond_1d
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_12

    .line 1523
    :cond_1e
    instance-of v0, p1, [J

    if-eqz v0, :cond_21

    .line 1524
    check-cast p1, [J

    .line 1525
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v4, "new long[] {"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1526
    :goto_c
    array-length v0, p1

    if-ge v5, v0, :cond_20

    .line 1527
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v5, :cond_1f

    move-object v4, v1

    goto :goto_d

    :cond_1f
    move-object v4, v2

    :goto_d
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget-wide v6, p1, v5

    invoke-virtual {v0, v6, v7}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v4, 0x4c

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    goto :goto_c

    .line 1529
    :cond_20
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_12

    .line 1530
    :cond_21
    instance-of v0, p1, [F

    if-eqz v0, :cond_24

    .line 1531
    check-cast p1, [F

    .line 1532
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v4, "new float[] {"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1533
    :goto_e
    array-length v0, p1

    if-ge v5, v0, :cond_23

    .line 1534
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v5, :cond_22

    move-object v4, v1

    goto :goto_f

    :cond_22
    move-object v4, v2

    :goto_f
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget v4, p1, v5

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v4, 0x66

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    goto :goto_e

    .line 1536
    :cond_23
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_12

    .line 1537
    :cond_24
    instance-of v0, p1, [D

    if-eqz v0, :cond_27

    .line 1538
    check-cast p1, [D

    .line 1539
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v4, "new double[] {"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1540
    :goto_10
    array-length v0, p1

    if-ge v5, v0, :cond_26

    .line 1541
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v5, :cond_25

    move-object v4, v1

    goto :goto_11

    :cond_25
    move-object v4, v2

    :goto_11
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget-wide v6, p1, v5

    invoke-virtual {v0, v6, v7}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v4, 0x64

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v5, v5, 0x1

    goto :goto_10

    .line 1543
    :cond_26
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_27
    :goto_12
    return-void
.end method

.method protected appendLabel(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1611
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->labelNames:Ljava/util/Map;

    invoke-interface {v1, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method protected createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 2

    .line 1265
    new-instance v0, Lgroovyjarjarasm/asm/util/ASMifier;

    iget v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->api:I

    invoke-direct {v0, v1, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;-><init>(ILjava/lang/String;I)V

    return-object v0
.end method

.method protected declareLabel(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1592
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->labelNames:Ljava/util/Map;

    if-nez v0, :cond_0

    .line 1593
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->labelNames:Ljava/util/Map;

    .line 1595
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->labelNames:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    if-nez v0, :cond_1

    .line 1597
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "label"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->labelNames:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->size()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 1598
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->labelNames:Ljava/util/Map;

    invoke-interface {v1, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1599
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "Label "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " = new Label();\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_1
    return-void
.end method

.method public visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 7

    const/4 v0, 0x0

    if-nez p3, :cond_0

    const-string v1, "module-info"

    goto :goto_0

    :cond_0
    const/16 v1, 0x2f

    .line 197
    invoke-virtual {p3, v1}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v2

    const/4 v3, -0x1

    if-ne v2, v3, :cond_1

    move-object v1, p3

    goto :goto_0

    .line 201
    :cond_1
    iget-object v3, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "package asm."

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {p3, v0, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v5

    const/16 v6, 0x2e

    invoke-virtual {v5, v1, v6}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v4, ";\n"

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v3, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v2, v2, 0x1

    .line 202
    invoke-virtual {p3, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v1

    const-string v2, "[-\\(\\)]"

    const-string v3, "_"

    invoke-virtual {v1, v2, v3}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 205
    :goto_0
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.AnnotationVisitor;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 206
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.Attribute;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 207
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.ClassReader;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 208
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.ClassWriter;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 209
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.ConstantDynamic;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 210
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.FieldVisitor;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 211
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.Handle;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 212
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.Label;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 213
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.MethodVisitor;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 214
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.Opcodes;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 215
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.RecordComponentVisitor;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 216
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.Type;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 217
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v3, "import org.objectweb.asm.TypePath;\n"

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 218
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "public class "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, "Dump implements Opcodes {\n\n"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v2, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 219
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v2, "public static byte[] dump () throws Exception {\n\n"

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 220
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v2, "ClassWriter classWriter = new ClassWriter(0);\n"

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 221
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v2, "FieldVisitor fieldVisitor;\n"

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 222
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v2, "RecordComponentVisitor recordComponentVisitor;\n"

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 223
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v2, "MethodVisitor methodVisitor;\n"

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 224
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v2, "AnnotationVisitor annotationVisitor0;\n\n"

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 226
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 227
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "classWriter.visit("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 228
    sget-object v1, Lgroovyjarjarasm/asm/util/ASMifier;->CLASS_VERSIONS:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    if-eqz v1, :cond_2

    .line 230
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 232
    :cond_2
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 234
    :goto_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, ", "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/high16 p1, 0x40000

    or-int/2addr p1, p2

    .line 235
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendAccessFlags(I)V

    .line 236
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 237
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 238
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 239
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 240
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 241
    invoke-virtual {p0, p5}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 242
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p6, :cond_5

    .line 243
    array-length p1, p6

    if-lez p1, :cond_5

    .line 244
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "new String[] {"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 245
    :goto_2
    array-length p1, p6

    if-ge v0, p1, :cond_4

    .line 246
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v0, :cond_3

    const-string p2, " "

    goto :goto_3

    :cond_3
    move-object p2, v1

    :goto_3
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 247
    aget-object p1, p6, v0

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_2

    .line 249
    :cond_4
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " }"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_4

    .line 251
    :cond_5
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "null"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 253
    :goto_4
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 254
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visit(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2

    .line 547
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 548
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "annotationVisitor"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visit("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 549
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 550
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 551
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 552
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 553
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitAnnotableParameterCount(IZ)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 2

    .line 706
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 707
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 708
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitAnnotableParameterCount("

    .line 709
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 710
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    .line 711
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 712
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    .line 713
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 714
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p0
.end method

.method public bridge synthetic visitAnnotableParameterCount(IZ)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAnnotableParameterCount(IZ)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitAnnotation(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 2

    .line 571
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 572
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "{\n"

    .line 573
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "AnnotationVisitor annotationVisitor"

    .line 574
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    add-int/lit8 v1, v1, 0x1

    .line 575
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " = annotationVisitor"

    .line 576
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 577
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitAnnotation("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 578
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 579
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 580
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 581
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 582
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 583
    iget p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    add-int/lit8 p1, p1, 0x1

    const-string p2, "annotationVisitor"

    invoke-virtual {p0, p2, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 584
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object v0

    invoke-interface {p2, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 585
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v0, "}\n"

    invoke-interface {p2, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 3

    .line 1150
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1151
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "{\n"

    .line 1152
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "annotationVisitor0 = "

    .line 1153
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 1154
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ".visitAnnotation("

    .line 1155
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1156
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 1157
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1158
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, "annotationVisitor"

    .line 1159
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 1160
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object v0

    invoke-interface {p2, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 1161
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v0, "}\n"

    invoke-interface {p2, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public bridge synthetic visitAnnotation(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAnnotation(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitAnnotationDefault()Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 3

    .line 680
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 681
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "{\n"

    .line 682
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "annotationVisitor0 = "

    .line 683
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 684
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ".visitAnnotationDefault();\n"

    .line 685
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 686
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string v0, "annotationVisitor"

    .line 687
    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object v0

    .line 688
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 689
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v2, "}\n"

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object v0
.end method

.method public bridge synthetic visitAnnotationDefault()Lgroovyjarjarasm/asm/util/Printer;
    .locals 1

    .line 51
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAnnotationDefault()Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object v0

    return-object v0
.end method

.method public visitAnnotationEnd()V
    .locals 2

    .line 609
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 610
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "annotationVisitor"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitEnd();\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 611
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitArray(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 2

    .line 591
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 592
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "{\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 593
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "AnnotationVisitor annotationVisitor"

    .line 594
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    add-int/lit8 v1, v1, 0x1

    .line 595
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " = annotationVisitor"

    .line 596
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 597
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitArray("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 598
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 599
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 600
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 601
    iget p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    add-int/lit8 p1, p1, 0x1

    const-string v0, "annotationVisitor"

    invoke-virtual {p0, v0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 602
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 603
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v1, "}\n"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public bridge synthetic visitArray(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->visitArray(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 3

    .line 1230
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1231
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "// ATTRIBUTE "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p1, Lgroovyjarjarasm/asm/Attribute;->type:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0xa

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1232
    instance-of v0, p1, Lgroovyjarjarasm/asm/util/ASMifierSupport;

    if-eqz v0, :cond_1

    .line 1233
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->labelNames:Ljava/util/Map;

    if-nez v0, :cond_0

    .line 1234
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->labelNames:Ljava/util/Map;

    .line 1236
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "{\n"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1237
    check-cast p1, Lgroovyjarjarasm/asm/util/ASMifierSupport;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->labelNames:Ljava/util/Map;

    const-string v2, "attribute"

    invoke-interface {p1, v0, v2, v1}, Lgroovyjarjarasm/asm/util/ASMifierSupport;->asmify(Ljava/lang/StringBuilder;Ljava/lang/String;Ljava/util/Map;)V

    .line 1238
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ".visitAttribute(attribute);\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1239
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "}\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1241
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitClassAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 0

    .line 310
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitClassAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitClassAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitClassAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 0

    .line 321
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitClassEnd()V
    .locals 2

    .line 442
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v1, "classWriter.visitEnd();\n\n"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 443
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v1, "return classWriter.toByteArray();\n"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 444
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v1, "}\n"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 445
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitClassTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 0

    .line 316
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitClassTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitClassTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitCode()V
    .locals 3

    .line 745
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ".visitCode();\n"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitEnum(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 558
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 559
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "annotationVisitor"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->id:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitEnum("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 560
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 561
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 562
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 563
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 564
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 565
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 566
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public varargs visitExport(Ljava/lang/String;I[Ljava/lang/String;)V
    .locals 1

    const-string v0, "moduleVisitor.visitExport("

    .line 485
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->visitExportOrOpen(Ljava/lang/String;Ljava/lang/String;I[Ljava/lang/String;)V

    return-void
.end method

.method public visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 3

    .line 384
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 385
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "{\n"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 386
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "fieldVisitor = classWriter.visitField("

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/high16 v0, 0x80000

    or-int/2addr p1, v0

    .line 387
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendAccessFlags(I)V

    .line 388
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 389
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 390
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 391
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 392
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 393
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 394
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 395
    invoke-virtual {p0, p5}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 396
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 397
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, "fieldVisitor"

    .line 398
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 399
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object p3

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 400
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string p3, "}\n"

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public bridge synthetic visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual/range {p0 .. p5}, Lgroovyjarjarasm/asm/util/ASMifier;->visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitFieldAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 0

    .line 645
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitFieldAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitFieldAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitFieldAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 0

    .line 656
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitFieldEnd()V
    .locals 0

    .line 661
    invoke-direct {p0}, Lgroovyjarjarasm/asm/util/ASMifier;->visitMemberEnd()V

    return-void
.end method

.method public visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 852
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 853
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitFieldInsn("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/ASMifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v1, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 854
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 855
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 856
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 857
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 858
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 859
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 860
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitFieldTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 0

    .line 651
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitFieldTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitFieldTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V
    .locals 3

    .line 755
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    const/16 v0, 0x7d

    const/4 v1, -0x1

    const-string v2, ", new Object[] {"

    if-eq p1, v1, :cond_4

    if-eqz p1, :cond_4

    const/4 p4, 0x1

    if-eq p1, p4, :cond_3

    const/4 p3, 0x2

    if-eq p1, p3, :cond_2

    const/4 p2, 0x3

    if-eq p1, p2, :cond_1

    const/4 p2, 0x4

    if-ne p1, p2, :cond_0

    .line 793
    invoke-direct {p0, p4, p5}, Lgroovyjarjarasm/asm/util/ASMifier;->declareFrameTypes(I[Ljava/lang/Object;)V

    .line 794
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 795
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ".visitFrame(Opcodes.F_SAME1, 0, null, 1, new Object[] {"

    .line 796
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 797
    invoke-direct {p0, p4, p5}, Lgroovyjarjarasm/asm/util/ASMifier;->appendFrameTypes(I[Ljava/lang/Object;)V

    .line 798
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto/16 :goto_1

    .line 801
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 790
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ".visitFrame(Opcodes.F_SAME, 0, null, 0, null"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_1

    .line 783
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p3, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 784
    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p3, ".visitFrame(Opcodes.F_CHOP,"

    .line 785
    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 786
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ", null, 0, null"

    .line 787
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 773
    :cond_3
    invoke-direct {p0, p2, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->declareFrameTypes(I[Ljava/lang/Object;)V

    .line 774
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p4, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 775
    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p4, ".visitFrame(Opcodes.F_APPEND,"

    .line 776
    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 777
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 778
    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 779
    invoke-direct {p0, p2, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendFrameTypes(I[Ljava/lang/Object;)V

    .line 780
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "}, 0, null"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 759
    :cond_4
    invoke-direct {p0, p2, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->declareFrameTypes(I[Ljava/lang/Object;)V

    .line 760
    invoke-direct {p0, p4, p5}, Lgroovyjarjarasm/asm/util/ASMifier;->declareFrameTypes(I[Ljava/lang/Object;)V

    if-ne p1, v1, :cond_5

    .line 762
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ".visitFrame(Opcodes.F_NEW, "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 764
    :cond_5
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ".visitFrame(Opcodes.F_FULL, "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 766
    :goto_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 767
    invoke-direct {p0, p2, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendFrameTypes(I[Ljava/lang/Object;)V

    .line 768
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "}, "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 769
    invoke-direct {p0, p4, p5}, Lgroovyjarjarasm/asm/util/ASMifier;->appendFrameTypes(I[Ljava/lang/Object;)V

    .line 770
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 803
    :goto_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 804
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitIincInsn(II)V
    .locals 2

    .line 942
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 943
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 944
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitIincInsn("

    .line 945
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 946
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    .line 947
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 948
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    .line 949
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 950
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitInnerClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 2

    .line 345
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 346
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "classWriter.visitInnerClass("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 347
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 348
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 349
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 350
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 351
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 352
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/high16 p1, 0x100000

    or-int/2addr p1, p4

    .line 353
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendAccessFlags(I)V

    .line 354
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 355
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitInsn(I)V
    .locals 2

    .line 809
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 810
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitInsn("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/ASMifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v1, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ");\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 811
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitInsnAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 6

    const-string v1, "visitInsnAnnotation"

    move-object v0, p0

    move v2, p1

    move-object v3, p2

    move-object v4, p3

    move v5, p4

    .line 1014
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/util/ASMifier;->visitTypeAnnotation(Ljava/lang/String;ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitInsnAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitInsnAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitIntInsn(II)V
    .locals 2

    .line 816
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 817
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 818
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitIntInsn("

    .line 819
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/ASMifier;->OPCODES:[Ljava/lang/String;

    aget-object v1, v1, p1

    .line 820
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", "

    .line 821
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0xbc

    if-ne p1, v1, :cond_0

    .line 822
    sget-object p1, Lgroovyjarjarasm/asm/util/ASMifier;->TYPES:[Ljava/lang/String;

    aget-object p1, p1, p2

    goto :goto_0

    :cond_0
    invoke-static {p2}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object p1

    :goto_0
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    .line 823
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 824
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public varargs visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V
    .locals 3

    .line 893
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 894
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ".visitInvokeDynamicInsn("

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 895
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 896
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 897
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 898
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 899
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 900
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ", new Object[]{"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 901
    :goto_0
    array-length p1, p4

    if-ge v1, p1, :cond_1

    .line 902
    aget-object p1, p4, v1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 903
    array-length p1, p4

    add-int/lit8 p1, p1, -0x1

    if-eq v1, p1, :cond_0

    .line 904
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 907
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "});\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 908
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 913
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 914
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 915
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitJumpInsn("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/ASMifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v1, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 916
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 917
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 918
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLabel(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 923
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 924
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 925
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitLabel("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 926
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 927
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 928
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLdcInsn(Ljava/lang/Object;)V
    .locals 2

    .line 933
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 934
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitLdcInsn("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 935
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 936
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 937
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLineNumber(ILgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1112
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1113
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitLineNumber("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1114
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1115
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1116
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V
    .locals 2

    .line 1050
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1051
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitLocalVariable("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1052
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 1053
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1054
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 1055
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1056
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 1057
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1058
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1059
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1060
    invoke-virtual {p0, p5}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1061
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1062
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLocalVariableAnnotation(ILgroovyjarjarasm/asm/TypePath;[Lgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;[ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 3

    .line 1074
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1075
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "{\n"

    .line 1076
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "annotationVisitor0 = "

    .line 1077
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 1078
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ".visitLocalVariableAnnotation("

    .line 1079
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 1080
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    if-nez p2, :cond_0

    .line 1082
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ", null, "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1084
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", TypePath.fromString(\""

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "\"), "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1086
    :goto_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "new Label[] {"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move p1, v1

    .line 1087
    :goto_1
    array-length p2, p3

    const-string v0, " "

    const-string v2, ", "

    if-ge p1, p2, :cond_2

    .line 1088
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez p1, :cond_1

    goto :goto_2

    :cond_1
    move-object v0, v2

    :goto_2
    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1089
    aget-object p2, p3, p1

    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    add-int/lit8 p1, p1, 0x1

    goto :goto_1

    .line 1091
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " }, new Label[] {"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move p1, v1

    .line 1092
    :goto_3
    array-length p2, p4

    if-ge p1, p2, :cond_4

    .line 1093
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez p1, :cond_3

    move-object p3, v0

    goto :goto_4

    :cond_3
    move-object p3, v2

    :goto_4
    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1094
    aget-object p2, p4, p1

    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    add-int/lit8 p1, p1, 0x1

    goto :goto_3

    .line 1096
    :cond_4
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " }, new int[] {"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move p1, v1

    .line 1097
    :goto_5
    array-length p2, p5

    if-ge p1, p2, :cond_6

    .line 1098
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez p1, :cond_5

    move-object p3, v0

    goto :goto_6

    :cond_5
    move-object p3, v2

    :goto_6
    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    aget p3, p5, p1

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    add-int/lit8 p1, p1, 0x1

    goto :goto_5

    .line 1100
    :cond_6
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " }, "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1101
    invoke-virtual {p0, p6}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 1102
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p7}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1103
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, "annotationVisitor"

    .line 1104
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 1105
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object p3

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 1106
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string p3, "}\n"

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V
    .locals 4

    .line 981
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 982
    array-length v0, p3

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_0

    aget-object v3, p3, v2

    .line 983
    invoke-virtual {p0, v3}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 985
    :cond_0
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 987
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ".visitLookupSwitchInsn("

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 988
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 989
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", new int[] {"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move p1, v1

    .line 990
    :goto_1
    array-length v0, p2

    const-string v2, " "

    const-string v3, ", "

    if-ge p1, v0, :cond_2

    .line 991
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez p1, :cond_1

    goto :goto_2

    :cond_1
    move-object v2, v3

    :goto_2
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget v2, p2, p1

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    add-int/lit8 p1, p1, 0x1

    goto :goto_1

    .line 993
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " }, new Label[] {"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 994
    :goto_3
    array-length p1, p3

    if-ge v1, p1, :cond_4

    .line 995
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v1, :cond_3

    move-object p2, v2

    goto :goto_4

    :cond_3
    move-object p2, v3

    :goto_4
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 996
    aget-object p1, p3, v1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_3

    .line 998
    :cond_4
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " });\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 999
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitMainClass(Ljava/lang/String;)V
    .locals 2

    .line 454
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 455
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "moduleVisitor.visitMainClass("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 456
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 457
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 458
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitMaxs(II)V
    .locals 2

    .line 1121
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1122
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 1123
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitMaxs("

    .line 1124
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 1125
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    .line 1126
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1127
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    .line 1128
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1129
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 3

    .line 411
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 412
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "{\n"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 413
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "methodVisitor = classWriter.visitMethod("

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 414
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendAccessFlags(I)V

    .line 415
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 416
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 417
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 418
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 419
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 420
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 421
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p5, :cond_2

    .line 422
    array-length p1, p5

    if-lez p1, :cond_2

    .line 423
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "new String[] {"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move p1, v1

    .line 424
    :goto_0
    array-length p2, p5

    if-ge p1, p2, :cond_1

    .line 425
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez p1, :cond_0

    const-string p3, " "

    goto :goto_1

    :cond_0
    move-object p3, v0

    :goto_1
    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 426
    aget-object p2, p5, p1

    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 428
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " }"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_2

    .line 430
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "null"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 432
    :goto_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 433
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, "methodVisitor"

    .line 434
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 435
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object p3

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 436
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string p3, "}\n"

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public bridge synthetic visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual/range {p0 .. p5}, Lgroovyjarjarasm/asm/util/ASMifier;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitMethodAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 0

    .line 695
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitMethodAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitMethodAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitMethodAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 0

    .line 740
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitMethodEnd()V
    .locals 0

    .line 1134
    invoke-direct {p0}, Lgroovyjarjarasm/asm/util/ASMifier;->visitMemberEnd()V

    return-void
.end method

.method public visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 2

    .line 870
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 871
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 872
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitMethodInsn("

    .line 873
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/ASMifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v1, p1

    .line 874
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    .line 875
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 876
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 877
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 878
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 879
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 880
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 881
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 882
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-eqz p5, :cond_0

    const-string p2, "true"

    goto :goto_0

    :cond_0
    const-string p2, "false"

    :goto_0
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 883
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 884
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitMethodTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 0

    .line 701
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitMethodTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitMethodTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitModule(Ljava/lang/String;ILjava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 3

    .line 270
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 271
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "{\n"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 272
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "ModuleVisitor moduleVisitor = classWriter.visitModule("

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 273
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 274
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/high16 p1, 0x200000

    or-int/2addr p1, p2

    .line 275
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendAccessFlags(I)V

    .line 276
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 277
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 278
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 279
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, "moduleVisitor"

    .line 280
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 281
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object p3

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 282
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string p3, "}\n"

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public visitModuleEnd()V
    .locals 2

    .line 537
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string v1, "moduleVisitor.visitEnd();\n"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitMultiANewArrayInsn(Ljava/lang/String;I)V
    .locals 2

    .line 1004
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1005
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitMultiANewArrayInsn("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1006
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 1007
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1008
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitNestHost(Ljava/lang/String;)V
    .locals 2

    .line 288
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 289
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "classWriter.visitNestHost("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 290
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 291
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 292
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitNestMember(Ljava/lang/String;)V
    .locals 2

    .line 326
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 327
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "classWriter.visitNestMember("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 328
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 329
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 330
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public varargs visitOpen(Ljava/lang/String;I[Ljava/lang/String;)V
    .locals 1

    const-string v0, "moduleVisitor.visitOpen("

    .line 490
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->visitExportOrOpen(Ljava/lang/String;Ljava/lang/String;I[Ljava/lang/String;)V

    return-void
.end method

.method public visitOuterClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 297
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 298
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "classWriter.visitOuterClass("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 299
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 300
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 301
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 302
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 303
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 304
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 305
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitPackage(Ljava/lang/String;)V
    .locals 2

    .line 463
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 464
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "moduleVisitor.visitPackage("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 465
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 466
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 467
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitParameter(Ljava/lang/String;I)V
    .locals 2

    .line 670
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 671
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitParameter("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 672
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendString(Ljava/lang/StringBuilder;Ljava/lang/String;)V

    .line 673
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 674
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendAccessFlags(I)V

    .line 675
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 3

    .line 721
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 722
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "{\n"

    .line 723
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "annotationVisitor0 = "

    .line 724
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 725
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ".visitParameterAnnotation("

    .line 726
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 727
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    .line 728
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 729
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 730
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 731
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, "annotationVisitor"

    .line 732
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 733
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object p3

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 734
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string p3, "}\n"

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public bridge synthetic visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitPermittedSubclass(Ljava/lang/String;)V
    .locals 2

    .line 335
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 336
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "classWriter.visitPermittedSubclass("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 337
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 338
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 339
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public varargs visitProvide(Ljava/lang/String;[Ljava/lang/String;)V
    .locals 3

    .line 523
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 524
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "moduleVisitor.visitProvide("

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 525
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 526
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ",  new String[] {"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 527
    :goto_0
    array-length p1, p2

    if-ge v1, p1, :cond_1

    .line 528
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v1, :cond_0

    const-string v0, " "

    goto :goto_1

    :cond_0
    const-string v0, ", "

    :goto_1
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 529
    aget-object p1, p2, v1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 531
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " });\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 532
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitRecordComponent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 3

    .line 361
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 362
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "{\n"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 363
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "recordComponentVisitor = classWriter.visitRecordComponent("

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 364
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 365
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 366
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 367
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 368
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 369
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 370
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, "recordComponentVisitor"

    .line 371
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 372
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object p3

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 373
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string p3, "}\n"

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public bridge synthetic visitRecordComponent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->visitRecordComponent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitRecordComponentAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 0

    .line 620
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitRecordComponentAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->visitRecordComponentAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitRecordComponentAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 0

    .line 631
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitRecordComponentEnd()V
    .locals 0

    .line 636
    invoke-direct {p0}, Lgroovyjarjarasm/asm/util/ASMifier;->visitMemberEnd()V

    return-void
.end method

.method public visitRecordComponentTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 0

    .line 626
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitRecordComponentTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitRecordComponentTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitRequire(Ljava/lang/String;ILjava/lang/String;)V
    .locals 2

    .line 472
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 473
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "moduleVisitor.visitRequire("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 474
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 475
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/high16 p1, 0x200000

    or-int/2addr p1, p2

    .line 476
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendAccessFlags(I)V

    .line 477
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 478
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 479
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 480
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitSource(Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 259
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 260
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "classWriter.visitSource("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 261
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 262
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 263
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 264
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 265
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public varargs visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V
    .locals 4

    .line 956
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 957
    array-length v0, p4

    move v2, v1

    :goto_0
    if-ge v2, v0, :cond_0

    aget-object v3, p4, v2

    .line 958
    invoke-virtual {p0, v3}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 960
    :cond_0
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 962
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 963
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, ".visitTableSwitchInsn("

    .line 964
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 965
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    .line 966
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 967
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 968
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 969
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 970
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ", new Label[] {"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 971
    :goto_1
    array-length p1, p4

    if-ge v1, p1, :cond_2

    .line 972
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-nez v1, :cond_1

    const-string p2, " "

    goto :goto_2

    :cond_1
    move-object p2, v0

    :goto_2
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 973
    aget-object p1, p4, v1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 975
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " });\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 976
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 6

    const-string v1, "visitTryCatchAnnotation"

    move-object v0, p0

    move v2, p1

    move-object v3, p2

    move-object v4, p3

    move v5, p4

    .line 1039
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/util/ASMifier;->visitTypeAnnotation(Ljava/lang/String;ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitTryCatchBlock(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Ljava/lang/String;)V
    .locals 2

    .line 1020
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1021
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1022
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1023
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->declareLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1024
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitTryCatchBlock("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1025
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1026
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1027
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1028
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1029
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/ASMifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1030
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1031
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 1032
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1033
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 6

    const-string v1, "visitTypeAnnotation"

    move-object v0, p0

    move v2, p1

    move-object v3, p2

    move-object v4, p3

    move v5, p4

    .line 1179
    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/util/ASMifier;->visitTypeAnnotation(Ljava/lang/String;ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    return-object p1
.end method

.method public visitTypeAnnotation(Ljava/lang/String;ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/ASMifier;
    .locals 3

    .line 1201
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1202
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "{\n"

    .line 1203
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "annotationVisitor0 = "

    .line 1204
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 1205
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v2, 0x2e

    .line 1206
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 1207
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x28

    .line 1208
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1209
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    if-nez p3, :cond_0

    .line 1211
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ", null, "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1213
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ", TypePath.fromString(\""

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "\"), "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1215
    :goto_0
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 1216
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ", "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p5}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1217
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, "annotationVisitor"

    .line 1218
    invoke-virtual {p0, p1, v1}, Lgroovyjarjarasm/asm/util/ASMifier;->createASMifier(Ljava/lang/String;I)Lgroovyjarjarasm/asm/util/ASMifier;

    move-result-object p1

    .line 1219
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/util/ASMifier;->getText()Ljava/util/List;

    move-result-object p3

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 1220
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    const-string p3, "}\n"

    invoke-interface {p2, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p1
.end method

.method public visitTypeInsn(ILjava/lang/String;)V
    .locals 2

    .line 842
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 843
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitTypeInsn("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/ASMifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v1, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 844
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 845
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, ");\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 846
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitUse(Ljava/lang/String;)V
    .locals 2

    .line 514
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 515
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "moduleVisitor.visitUse("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 516
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/ASMifier;->appendConstant(Ljava/lang/Object;)V

    .line 517
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ");\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 518
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitVarInsn(II)V
    .locals 2

    .line 829
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 830
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->name:Ljava/lang/String;

    .line 831
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".visitVarInsn("

    .line 832
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/ASMifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v1, p1

    .line 833
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    .line 834
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 835
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ");\n"

    .line 836
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 837
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/ASMifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/ASMifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method
