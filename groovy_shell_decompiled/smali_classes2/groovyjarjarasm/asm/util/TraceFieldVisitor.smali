.class public final Lgroovyjarjarasm/asm/util/TraceFieldVisitor;
.super Lgroovyjarjarasm/asm/FieldVisitor;
.source "TraceFieldVisitor.java"


# instance fields
.field public final p:Lgroovyjarjarasm/asm/util/Printer;


# direct methods
.method public constructor <init>(Lgroovyjarjarasm/asm/FieldVisitor;Lgroovyjarjarasm/asm/util/Printer;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 63
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/FieldVisitor;-><init>(ILgroovyjarjarasm/asm/FieldVisitor;)V

    .line 64
    iput-object p2, p0, Lgroovyjarjarasm/asm/util/TraceFieldVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/util/Printer;)V
    .locals 1

    const/4 v0, 0x0

    .line 53
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/util/TraceFieldVisitor;-><init>(Lgroovyjarjarasm/asm/FieldVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-void
.end method


# virtual methods
.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 69
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceFieldVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitFieldAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 70
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 71
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/FieldVisitor;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 1

    .line 84
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceFieldVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitFieldAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    .line 85
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/FieldVisitor;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitEnd()V
    .locals 1

    .line 90
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceFieldVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/Printer;->visitFieldEnd()V

    .line 91
    invoke-super {p0}, Lgroovyjarjarasm/asm/FieldVisitor;->visitEnd()V

    return-void
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 77
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceFieldVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitFieldTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 78
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 79
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/FieldVisitor;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method
