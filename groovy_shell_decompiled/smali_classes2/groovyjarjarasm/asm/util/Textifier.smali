.class public Lgroovyjarjarasm/asm/util/Textifier;
.super Lgroovyjarjarasm/asm/util/Printer;
.source "Textifier.java"


# static fields
.field public static final CLASS_SIGNATURE:I = 0x5

.field private static final CLASS_SUFFIX:Ljava/lang/String; = ".class"

.field private static final DEPRECATED:Ljava/lang/String; = "// DEPRECATED\n"

.field public static final FIELD_DESCRIPTOR:I = 0x1

.field public static final FIELD_SIGNATURE:I = 0x2

.field private static final FRAME_TYPES:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final HANDLE_DESCRIPTOR:I = 0x9

.field public static final INTERNAL_NAME:I = 0x0

.field private static final INVISIBLE:Ljava/lang/String; = " // invisible\n"

.field public static final METHOD_DESCRIPTOR:I = 0x3

.field public static final METHOD_SIGNATURE:I = 0x4

.field private static final RECORD:Ljava/lang/String; = "// RECORD\n"

.field private static final USAGE:Ljava/lang/String; = "Prints a disassembled view of the given class.\nUsage: Textifier [-nodebug] <fully qualified class name or class file name>"


# instance fields
.field private access:I

.field protected labelNames:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lgroovyjarjarasm/asm/Label;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field protected ltab:Ljava/lang/String;

.field private numAnnotationValues:I

.field protected tab:Ljava/lang/String;

.field protected tab2:Ljava/lang/String;

.field protected tab3:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    const-string v0, "T"

    const-string v1, "I"

    const-string v2, "F"

    const-string v3, "D"

    const-string v4, "J"

    const-string v5, "N"

    const-string v6, "U"

    .line 86
    filled-new-array/range {v0 .. v6}, [Ljava/lang/String;

    move-result-object v0

    .line 87
    invoke-static {v0}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Lgroovyjarjarasm/asm/util/Textifier;->FRAME_TYPES:Ljava/util/List;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    const/high16 v0, 0x90000

    .line 117
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;-><init>(I)V

    .line 118
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovyjarjarasm/asm/util/Textifier;

    if-ne v0, v1, :cond_0

    return-void

    .line 119
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method

.method protected constructor <init>(I)V
    .locals 0

    .line 130
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Printer;-><init>(I)V

    const-string p1, "  "

    .line 90
    iput-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    const-string p1, "    "

    .line 93
    iput-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    const-string p1, "      "

    .line 96
    iput-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab3:Ljava/lang/String;

    const-string p1, "   "

    .line 99
    iput-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->ltab:Ljava/lang/String;

    return-void
.end method

.method private addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 3

    .line 1588
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/util/Textifier;->createTextifier()Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object v0

    .line 1589
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/Textifier;->getText()Ljava/util/List;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    if-eqz p1, :cond_0

    .line 1591
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    invoke-interface {v1, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    return-object v0
.end method

.method private appendAccess(I)V
    .locals 2

    and-int/lit8 v0, p1, 0x1

    if-eqz v0, :cond_0

    .line 1270
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "public "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_0
    and-int/lit8 v0, p1, 0x2

    if-eqz v0, :cond_1

    .line 1273
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "private "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_1
    and-int/lit8 v0, p1, 0x4

    if-eqz v0, :cond_2

    .line 1276
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "protected "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_2
    and-int/lit8 v0, p1, 0x10

    if-eqz v0, :cond_3

    .line 1279
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "final "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_3
    and-int/lit8 v0, p1, 0x8

    if-eqz v0, :cond_4

    .line 1282
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "static "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_4
    and-int/lit8 v0, p1, 0x20

    if-eqz v0, :cond_5

    .line 1285
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "synchronized "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_5
    and-int/lit8 v0, p1, 0x40

    if-eqz v0, :cond_6

    .line 1288
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "volatile "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_6
    and-int/lit16 v0, p1, 0x80

    if-eqz v0, :cond_7

    .line 1291
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "transient "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_7
    and-int/lit16 v0, p1, 0x400

    if-eqz v0, :cond_8

    .line 1294
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "abstract "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_8
    and-int/lit16 v0, p1, 0x800

    if-eqz v0, :cond_9

    .line 1297
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "strictfp "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_9
    and-int/lit16 v0, p1, 0x1000

    if-eqz v0, :cond_a

    .line 1300
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "synthetic "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_a
    const v0, 0x8000

    and-int/2addr v0, p1

    if-eqz v0, :cond_b

    .line 1303
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "mandated "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_b
    and-int/lit16 p1, p1, 0x4000

    if-eqz p1, :cond_c

    .line 1306
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "enum "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_c
    return-void
.end method

.method private appendFrameTypes(I[Ljava/lang/Object;)V
    .locals 5

    const/4 v0, 0x0

    move v1, v0

    :goto_0
    if-ge v1, p1, :cond_4

    if-lez v1, :cond_0

    .line 1564
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v3, 0x20

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1566
    :cond_0
    aget-object v2, p2, v1

    instance-of v2, v2, Ljava/lang/String;

    if-eqz v2, :cond_2

    .line 1567
    aget-object v2, p2, v1

    check-cast v2, Ljava/lang/String;

    .line 1568
    invoke-virtual {v2, v0}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v4, 0x5b

    if-ne v3, v4, :cond_1

    const/4 v3, 0x1

    .line 1569
    invoke-virtual {p0, v3, v2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    goto :goto_1

    .line 1571
    :cond_1
    invoke-virtual {p0, v0, v2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    goto :goto_1

    .line 1573
    :cond_2
    aget-object v2, p2, v1

    instance-of v2, v2, Ljava/lang/Integer;

    if-eqz v2, :cond_3

    .line 1574
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    sget-object v3, Lgroovyjarjarasm/asm/util/Textifier;->FRAME_TYPES:Ljava/util/List;

    aget-object v4, p2, v1

    check-cast v4, Ljava/lang/Integer;

    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    move-result v4

    invoke-interface {v3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 1576
    :cond_3
    aget-object v2, p2, v1

    check-cast v2, Lgroovyjarjarasm/asm/Label;

    invoke-virtual {p0, v2}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    :goto_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_4
    return-void
.end method

.method private appendJavaDeclaration(Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 1348
    new-instance v0, Lgroovyjarjarasm/asm/util/TraceSignatureVisitor;

    iget v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->access:I

    invoke-direct {v0, v1}, Lgroovyjarjarasm/asm/util/TraceSignatureVisitor;-><init>(I)V

    .line 1349
    new-instance v1, Lgroovyjarjarasm/asm/signature/SignatureReader;

    invoke-direct {v1, p2}, Lgroovyjarjarasm/asm/signature/SignatureReader;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1, v0}, Lgroovyjarjarasm/asm/signature/SignatureReader;->accept(Lgroovyjarjarasm/asm/signature/SignatureVisitor;)V

    .line 1350
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "// declaration: "

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1351
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/TraceSignatureVisitor;->getReturnType()Ljava/lang/String;

    move-result-object p2

    if-eqz p2, :cond_0

    .line 1352
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/TraceSignatureVisitor;->getReturnType()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1353
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v1, 0x20

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1355
    :cond_0
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1356
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/TraceSignatureVisitor;->getDeclaration()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1357
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/TraceSignatureVisitor;->getExceptions()Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 1358
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " throws "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/TraceSignatureVisitor;->getExceptions()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1360
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    return-void
.end method

.method private appendRawAccess(I)V
    .locals 2

    .line 1316
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "// access flags 0x"

    .line 1317
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 1318
    invoke-static {p1}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/String;->toUpperCase()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0xa

    .line 1319
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    return-void
.end method

.method private appendTypeReference(I)V
    .locals 3

    .line 1461
    new-instance v0, Lgroovyjarjarasm/asm/TypeReference;

    invoke-direct {v0, p1}, Lgroovyjarjarasm/asm/TypeReference;-><init>(I)V

    .line 1462
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getSort()I

    move-result p1

    if-eqz p1, :cond_1

    const/4 v1, 0x1

    if-eq p1, v1, :cond_0

    const-string v1, ", "

    packed-switch p1, :pswitch_data_0

    packed-switch p1, :pswitch_data_1

    .line 1550
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 1545
    :pswitch_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "METHOD_REFERENCE_TYPE_ARGUMENT "

    .line 1546
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1547
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeArgumentIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1540
    :pswitch_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "CONSTRUCTOR_REFERENCE_TYPE_ARGUMENT "

    .line 1541
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1542
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeArgumentIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1535
    :pswitch_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "METHOD_INVOCATION_TYPE_ARGUMENT "

    .line 1536
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1537
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeArgumentIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1530
    :pswitch_3
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "CONSTRUCTOR_INVOCATION_TYPE_ARGUMENT "

    .line 1531
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1532
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeArgumentIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1527
    :pswitch_4
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "CAST "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeArgumentIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1524
    :pswitch_5
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "METHOD_REFERENCE"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1521
    :pswitch_6
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "CONSTRUCTOR_REFERENCE"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1518
    :pswitch_7
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "NEW"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1515
    :pswitch_8
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "INSTANCEOF"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1512
    :pswitch_9
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "EXCEPTION_PARAMETER "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTryCatchBlockIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1509
    :pswitch_a
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "RESOURCE_VARIABLE"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1506
    :pswitch_b
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "LOCAL_VARIABLE"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1503
    :pswitch_c
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "THROWS "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getExceptionIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1498
    :pswitch_d
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "METHOD_FORMAL_PARAMETER "

    .line 1499
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1500
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getFormalParameterIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1495
    :pswitch_e
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "METHOD_RECEIVER"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_0

    .line 1492
    :pswitch_f
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "METHOD_RETURN"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1489
    :pswitch_10
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "FIELD"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1482
    :pswitch_11
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "METHOD_TYPE_PARAMETER_BOUND "

    .line 1483
    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1484
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeParameterIndex()I

    move-result v2

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1485
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1486
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeParameterBoundIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1475
    :pswitch_12
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "CLASS_TYPE_PARAMETER_BOUND "

    .line 1476
    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1477
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeParameterIndex()I

    move-result v2

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1478
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1479
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeParameterBoundIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1472
    :pswitch_13
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "CLASS_EXTENDS "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getSuperTypeIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1467
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "METHOD_TYPE_PARAMETER "

    .line 1468
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1469
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeParameterIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1464
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "CLASS_TYPE_PARAMETER "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/TypeReference;->getTypeParameterIndex()I

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    :goto_0
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x10
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x40
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static main([Ljava/lang/String;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 142
    new-instance v0, Ljava/io/PrintWriter;

    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Ljava/io/PrintWriter;-><init>(Ljava/io/OutputStream;Z)V

    new-instance v1, Ljava/io/PrintWriter;

    sget-object v3, Ljava/lang/System;->err:Ljava/io/PrintStream;

    invoke-direct {v1, v3, v2}, Ljava/io/PrintWriter;-><init>(Ljava/io/OutputStream;Z)V

    invoke-static {p0, v0, v1}, Lgroovyjarjarasm/asm/util/Textifier;->main([Ljava/lang/String;Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V

    return-void
.end method

.method static main([Ljava/lang/String;Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 157
    new-instance v0, Lgroovyjarjarasm/asm/util/Textifier;

    invoke-direct {v0}, Lgroovyjarjarasm/asm/util/Textifier;-><init>()V

    const-string v1, "Prints a disassembled view of the given class.\nUsage: Textifier [-nodebug] <fully qualified class name or class file name>"

    invoke-static {p0, v1, v0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->main([Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/util/Printer;Ljava/io/PrintWriter;Ljava/io/PrintWriter;)V

    return-void
.end method

.method private maybeAppendComma(I)V
    .locals 1

    if-lez p1, :cond_0

    .line 1451
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_0
    return-void
.end method

.method private visitAnnotationValue(Ljava/lang/String;)V
    .locals 2

    .line 707
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 708
    iget v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->numAnnotationValues:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->numAnnotationValues:I

    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->maybeAppendComma(I)V

    if-eqz p1, :cond_0

    .line 710
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x3d

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_0
    return-void
.end method

.method private visitBoolean(Z)V
    .locals 1

    .line 664
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    return-void
.end method

.method private visitByte(B)V
    .locals 2

    .line 660
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "(byte)"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    return-void
.end method

.method private visitChar(C)V
    .locals 2

    .line 652
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "(char)"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    return-void
.end method

.method private visitDouble(D)V
    .locals 1

    .line 648
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1, p2}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0x44

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    return-void
.end method

.method private varargs visitExportOrOpen(Ljava/lang/String;Ljava/lang/String;I[Ljava/lang/String;)V
    .locals 3

    .line 505
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 506
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 507
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p4, :cond_0

    .line 508
    array-length p1, p4

    if-lez p1, :cond_0

    .line 509
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " to"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 511
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0x3b

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 513
    :goto_0
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendRawAccess(I)V

    if-eqz p4, :cond_2

    .line 514
    array-length p1, p4

    if-lez p1, :cond_2

    .line 515
    :goto_1
    array-length p1, p4

    if-ge v1, p1, :cond_2

    .line 516
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    aget-object p2, p4, v1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 517
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    array-length p2, p4

    add-int/lit8 p2, p2, -0x1

    if-eq v1, p2, :cond_1

    const-string p2, ",\n"

    goto :goto_2

    :cond_1
    const-string p2, ";\n"

    :goto_2
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 520
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method private visitFloat(F)V
    .locals 1

    .line 644
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x46

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    return-void
.end method

.method private visitInt(I)V
    .locals 1

    .line 636
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    return-void
.end method

.method private visitLong(J)V
    .locals 1

    .line 640
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1, p2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0x4c

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    return-void
.end method

.method private visitShort(S)V
    .locals 2

    .line 656
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "(short)"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    return-void
.end method

.method private visitString(Ljava/lang/String;)V
    .locals 1

    .line 668
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendString(Ljava/lang/StringBuilder;Ljava/lang/String;)V

    return-void
.end method

.method private visitType(Lgroovyjarjarasm/asm/Type;)V
    .locals 1

    .line 672
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getClassName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ".class"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method


# virtual methods
.method protected appendDescriptor(ILjava/lang/String;)V
    .locals 1

    const/4 v0, 0x5

    if-eq p1, v0, :cond_1

    const/4 v0, 0x2

    if-eq p1, v0, :cond_1

    const/4 v0, 0x4

    if-ne p1, v0, :cond_0

    goto :goto_0

    .line 1337
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    :cond_1
    :goto_0
    if-eqz p2, :cond_2

    .line 1334
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "// signature "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_2
    :goto_1
    return-void
.end method

.method protected appendHandle(Lgroovyjarjarasm/asm/Handle;)V
    .locals 4

    .line 1387
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getTag()I

    move-result v0

    .line 1388
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "// handle kind 0x"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-static {v0}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " : "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    const/4 v2, 0x1

    packed-switch v0, :pswitch_data_0

    .line 1424
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 1404
    :pswitch_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v3, "INVOKEINTERFACE"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 1420
    :pswitch_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v3, "NEWINVOKESPECIAL"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 1408
    :pswitch_2
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v3, "INVOKESPECIAL"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 1412
    :pswitch_3
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v3, "INVOKESTATIC"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 1416
    :pswitch_4
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v3, "INVOKEVIRTUAL"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 1401
    :pswitch_5
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "PUTSTATIC"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1398
    :pswitch_6
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "PUTFIELD"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1395
    :pswitch_7
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "GETSTATIC"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1392
    :pswitch_8
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, "GETFIELD"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :goto_0
    move v2, v1

    .line 1426
    :goto_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v3, 0xa

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1427
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v3, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab3:Ljava/lang/String;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1428
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getOwner()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v1, v0}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1429
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v1, 0x2e

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1430
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-nez v2, :cond_0

    .line 1432
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v1, 0x28

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_0
    const/16 v0, 0x9

    .line 1434
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->getDesc()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v0, v1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    if-nez v2, :cond_1

    .line 1436
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1438
    :cond_1
    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Handle;->isInterface()Z

    move-result p1

    if-eqz p1, :cond_2

    .line 1439
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, " itf"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_2
    return-void

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method protected appendLabel(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1370
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->labelNames:Ljava/util/Map;

    if-nez v0, :cond_0

    .line 1371
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->labelNames:Ljava/util/Map;

    .line 1373
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->labelNames:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    if-nez v0, :cond_1

    .line 1375
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "L"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->labelNames:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->size()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 1376
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->labelNames:Ljava/util/Map;

    invoke-interface {v1, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1378
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    return-void
.end method

.method protected createTextifier()Lgroovyjarjarasm/asm/util/Textifier;
    .locals 2

    .line 1602
    new-instance v0, Lgroovyjarjarasm/asm/util/Textifier;

    iget v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->api:I

    invoke-direct {v0, v1}, Lgroovyjarjarasm/asm/util/Textifier;-><init>(I)V

    return-object v0
.end method

.method public visit(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)V
    .locals 5

    const v0, 0x8000

    and-int/2addr v0, p2

    if-eqz v0, :cond_0

    return-void

    .line 176
    :cond_0
    iput p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->access:I

    const v0, 0xffff

    and-int/2addr v0, p1

    ushr-int/lit8 v1, p1, 0x10

    .line 179
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v3, 0x0

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 180
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v4, "// class version "

    .line 181
    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    .line 182
    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v2, 0x2e

    .line 183
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 184
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " ("

    .line 185
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 186
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ")\n"

    .line 187
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/high16 p1, 0x20000

    and-int/2addr p1, p2

    if-eqz p1, :cond_1

    .line 189
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "// DEPRECATED\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_1
    const/high16 p1, 0x10000

    and-int/2addr p1, p2

    if-eqz p1, :cond_2

    .line 192
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "// RECORD\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 194
    :cond_2
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendRawAccess(I)V

    const/4 p1, 0x5

    .line 196
    invoke-virtual {p0, p1, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    if-eqz p4, :cond_3

    .line 198
    invoke-direct {p0, p3, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendJavaDeclaration(Ljava/lang/String;Ljava/lang/String;)V

    :cond_3
    const p1, -0x8021

    and-int/2addr p1, p2

    .line 201
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendAccess(I)V

    and-int/lit16 p1, p2, 0x2000

    if-eqz p1, :cond_4

    .line 203
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "@interface "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    :cond_4
    and-int/lit16 p1, p2, 0x200

    if-eqz p1, :cond_5

    .line 205
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "interface "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    :cond_5
    and-int/lit16 p1, p2, 0x4000

    if-nez p1, :cond_6

    .line 207
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "class "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 209
    :cond_6
    :goto_0
    invoke-virtual {p0, v3, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    if-eqz p5, :cond_7

    const-string p1, "java/lang/Object"

    .line 211
    invoke-virtual {p1, p5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_7

    .line 212
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " extends "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 213
    invoke-virtual {p0, v3, p5}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    :cond_7
    if-eqz p6, :cond_9

    .line 215
    array-length p1, p6

    if-lez p1, :cond_9

    .line 216
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " implements "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move p1, v3

    .line 217
    :goto_1
    array-length p2, p6

    if-ge p1, p2, :cond_9

    .line 218
    aget-object p2, p6, p1

    invoke-virtual {p0, v3, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 219
    array-length p2, p6

    add-int/lit8 p2, p2, -0x1

    if-eq p1, p2, :cond_8

    .line 220
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p3, 0x20

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_8
    add-int/lit8 p1, p1, 0x1

    goto :goto_1

    .line 224
    :cond_9
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " {\n\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 226
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visit(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 3

    .line 558
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotationValue(Ljava/lang/String;)V

    .line 559
    instance-of p1, p2, Ljava/lang/String;

    if-eqz p1, :cond_0

    .line 560
    check-cast p2, Ljava/lang/String;

    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitString(Ljava/lang/String;)V

    goto/16 :goto_8

    .line 561
    :cond_0
    instance-of p1, p2, Lgroovyjarjarasm/asm/Type;

    if-eqz p1, :cond_1

    .line 562
    check-cast p2, Lgroovyjarjarasm/asm/Type;

    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitType(Lgroovyjarjarasm/asm/Type;)V

    goto/16 :goto_8

    .line 563
    :cond_1
    instance-of p1, p2, Ljava/lang/Byte;

    if-eqz p1, :cond_2

    .line 564
    check-cast p2, Ljava/lang/Byte;

    invoke-virtual {p2}, Ljava/lang/Byte;->byteValue()B

    move-result p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitByte(B)V

    goto/16 :goto_8

    .line 565
    :cond_2
    instance-of p1, p2, Ljava/lang/Boolean;

    if-eqz p1, :cond_3

    .line 566
    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitBoolean(Z)V

    goto/16 :goto_8

    .line 567
    :cond_3
    instance-of p1, p2, Ljava/lang/Short;

    if-eqz p1, :cond_4

    .line 568
    check-cast p2, Ljava/lang/Short;

    invoke-virtual {p2}, Ljava/lang/Short;->shortValue()S

    move-result p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitShort(S)V

    goto/16 :goto_8

    .line 569
    :cond_4
    instance-of p1, p2, Ljava/lang/Character;

    if-eqz p1, :cond_5

    .line 570
    check-cast p2, Ljava/lang/Character;

    invoke-virtual {p2}, Ljava/lang/Character;->charValue()C

    move-result p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitChar(C)V

    goto/16 :goto_8

    .line 571
    :cond_5
    instance-of p1, p2, Ljava/lang/Integer;

    if-eqz p1, :cond_6

    .line 572
    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitInt(I)V

    goto/16 :goto_8

    .line 573
    :cond_6
    instance-of p1, p2, Ljava/lang/Float;

    if-eqz p1, :cond_7

    .line 574
    check-cast p2, Ljava/lang/Float;

    invoke-virtual {p2}, Ljava/lang/Float;->floatValue()F

    move-result p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitFloat(F)V

    goto/16 :goto_8

    .line 575
    :cond_7
    instance-of p1, p2, Ljava/lang/Long;

    if-eqz p1, :cond_8

    .line 576
    check-cast p2, Ljava/lang/Long;

    invoke-virtual {p2}, Ljava/lang/Long;->longValue()J

    move-result-wide p1

    invoke-direct {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitLong(J)V

    goto/16 :goto_8

    .line 577
    :cond_8
    instance-of p1, p2, Ljava/lang/Double;

    if-eqz p1, :cond_9

    .line 578
    check-cast p2, Ljava/lang/Double;

    invoke-virtual {p2}, Ljava/lang/Double;->doubleValue()D

    move-result-wide p1

    invoke-direct {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitDouble(D)V

    goto/16 :goto_8

    .line 579
    :cond_9
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->isArray()Z

    move-result p1

    if-eqz p1, :cond_12

    .line 580
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x7b

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 581
    instance-of p1, p2, [B

    const/4 v0, 0x0

    if-eqz p1, :cond_a

    .line 582
    check-cast p2, [B

    .line 583
    :goto_0
    array-length p1, p2

    if-ge v0, p1, :cond_11

    .line 584
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->maybeAppendComma(I)V

    .line 585
    aget-byte p1, p2, v0

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitByte(B)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 587
    :cond_a
    instance-of p1, p2, [Z

    if-eqz p1, :cond_b

    .line 588
    check-cast p2, [Z

    .line 589
    :goto_1
    array-length p1, p2

    if-ge v0, p1, :cond_11

    .line 590
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->maybeAppendComma(I)V

    .line 591
    aget-boolean p1, p2, v0

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitBoolean(Z)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    .line 593
    :cond_b
    instance-of p1, p2, [S

    if-eqz p1, :cond_c

    .line 594
    check-cast p2, [S

    .line 595
    :goto_2
    array-length p1, p2

    if-ge v0, p1, :cond_11

    .line 596
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->maybeAppendComma(I)V

    .line 597
    aget-short p1, p2, v0

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitShort(S)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_2

    .line 599
    :cond_c
    instance-of p1, p2, [C

    if-eqz p1, :cond_d

    .line 600
    check-cast p2, [C

    .line 601
    :goto_3
    array-length p1, p2

    if-ge v0, p1, :cond_11

    .line 602
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->maybeAppendComma(I)V

    .line 603
    aget-char p1, p2, v0

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitChar(C)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_3

    .line 605
    :cond_d
    instance-of p1, p2, [I

    if-eqz p1, :cond_e

    .line 606
    check-cast p2, [I

    .line 607
    :goto_4
    array-length p1, p2

    if-ge v0, p1, :cond_11

    .line 608
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->maybeAppendComma(I)V

    .line 609
    aget p1, p2, v0

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitInt(I)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_4

    .line 611
    :cond_e
    instance-of p1, p2, [J

    if-eqz p1, :cond_f

    .line 612
    check-cast p2, [J

    .line 613
    :goto_5
    array-length p1, p2

    if-ge v0, p1, :cond_11

    .line 614
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->maybeAppendComma(I)V

    .line 615
    aget-wide v1, p2, v0

    invoke-direct {p0, v1, v2}, Lgroovyjarjarasm/asm/util/Textifier;->visitLong(J)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_5

    .line 617
    :cond_f
    instance-of p1, p2, [F

    if-eqz p1, :cond_10

    .line 618
    check-cast p2, [F

    .line 619
    :goto_6
    array-length p1, p2

    if-ge v0, p1, :cond_11

    .line 620
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->maybeAppendComma(I)V

    .line 621
    aget p1, p2, v0

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitFloat(F)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_6

    .line 623
    :cond_10
    instance-of p1, p2, [D

    if-eqz p1, :cond_11

    .line 624
    check-cast p2, [D

    .line 625
    :goto_7
    array-length p1, p2

    if-ge v0, p1, :cond_11

    .line 626
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->maybeAppendComma(I)V

    .line 627
    aget-wide v1, p2, v0

    invoke-direct {p0, v1, v2}, Lgroovyjarjarasm/asm/util/Textifier;->visitDouble(D)V

    add-int/lit8 v0, v0, 0x1

    goto :goto_7

    .line 630
    :cond_11
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0x7d

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 632
    :cond_12
    :goto_8
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public bridge synthetic visitAnnotableParameterCount(IZ)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotableParameterCount(IZ)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitAnnotableParameterCount(IZ)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 2

    .line 796
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 797
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "// annotable parameter count: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 798
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 799
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-eqz p2, :cond_0

    const-string p2, " (visible)\n"

    goto :goto_0

    :cond_0
    const-string p2, " (invisible)\n"

    :goto_0
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 800
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p0
.end method

.method public bridge synthetic visitAnnotation(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotation(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitAnnotation(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 1

    .line 685
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotationValue(Ljava/lang/String;)V

    .line 686
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x40

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/4 p1, 0x1

    .line 687
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 688
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0x28

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 689
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, ")"

    .line 690
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 2

    .line 1202
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1203
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0x40

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/4 v0, 0x1

    .line 1204
    invoke-virtual {p0, v0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1205
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x28

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1206
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    if-eqz p2, :cond_0

    const-string p1, ")\n"

    goto :goto_0

    :cond_0
    const-string p1, ") // invisible\n"

    .line 1207
    :goto_0
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitAnnotationDefault()Lgroovyjarjarasm/asm/util/Printer;
    .locals 1

    .line 51
    invoke-virtual {p0}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotationDefault()Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object v0

    return-object v0
.end method

.method public visitAnnotationDefault()Lgroovyjarjarasm/asm/util/Textifier;
    .locals 3

    .line 779
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "default="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string v0, "\n"

    .line 780
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object v0

    return-object v0
.end method

.method public visitAnnotationEnd()V
    .locals 0

    return-void
.end method

.method public bridge synthetic visitArray(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitArray(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitArray(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 1

    .line 695
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotationValue(Ljava/lang/String;)V

    .line 696
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x7b

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 697
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string p1, "}"

    .line 698
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 2

    .line 1243
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1244
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "ATTRIBUTE "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1245
    iget-object v0, p1, Lgroovyjarjarasm/asm/Attribute;->type:Ljava/lang/String;

    const/4 v1, -0x1

    invoke-virtual {p0, v1, v0}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1247
    instance-of v0, p1, Lgroovyjarjarasm/asm/util/TextifierSupport;

    if-eqz v0, :cond_1

    .line 1248
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->labelNames:Ljava/util/Map;

    if-nez v0, :cond_0

    .line 1249
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->labelNames:Ljava/util/Map;

    .line 1251
    :cond_0
    check-cast p1, Lgroovyjarjarasm/asm/util/TextifierSupport;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->labelNames:Ljava/util/Map;

    invoke-interface {p1, v0, v1}, Lgroovyjarjarasm/asm/util/TextifierSupport;->textify(Ljava/lang/StringBuilder;Ljava/util/Map;)V

    goto :goto_0

    .line 1253
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, " : unknown\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1256
    :goto_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public bridge synthetic visitClassAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitClassAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitClassAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 2

    .line 284
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    const-string v1, "\n"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 285
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitClassAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 2

    .line 297
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    const-string v1, "\n"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 298
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitClassEnd()V
    .locals 2

    .line 454
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    const-string v1, "}\n"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitClassTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 2

    .line 291
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    const-string v1, "\n"

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 292
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Textifier;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitCode()V
    .locals 0

    return-void
.end method

.method public visitEnum(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 677
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotationValue(Ljava/lang/String;)V

    const/4 p1, 0x1

    .line 678
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 679
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0x2e

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 680
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public varargs visitExport(Ljava/lang/String;I[Ljava/lang/String;)V
    .locals 1

    const-string v0, "exports "

    .line 495
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/Textifier;->visitExportOrOpen(Ljava/lang/String;Ljava/lang/String;I[Ljava/lang/String;)V

    return-void
.end method

.method public bridge synthetic visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual/range {p0 .. p5}, Lgroovyjarjarasm/asm/util/Textifier;->visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitField(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 3

    .line 366
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 367
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v1, 0xa

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/high16 v0, 0x20000

    and-int/2addr v0, p1

    if-eqz v0, :cond_0

    .line 369
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "// DEPRECATED\n"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 371
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 372
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendRawAccess(I)V

    if-eqz p4, :cond_1

    .line 374
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v0, 0x2

    .line 375
    invoke-virtual {p0, v0, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 376
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 377
    invoke-direct {p0, p2, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendJavaDeclaration(Ljava/lang/String;Ljava/lang/String;)V

    .line 380
    :cond_1
    iget-object p4, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 381
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendAccess(I)V

    const/4 p1, 0x1

    .line 383
    invoke-virtual {p0, p1, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 384
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p3, 0x20

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p5, :cond_3

    .line 386
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " = "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 387
    instance-of p1, p5, Ljava/lang/String;

    if-eqz p1, :cond_2

    .line 388
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0x22

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 390
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, p5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 394
    :cond_3
    :goto_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 395
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/4 p1, 0x0

    .line 396
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitFieldAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitFieldAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitFieldAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 0

    .line 745
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitFieldAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 0

    .line 756
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitFieldEnd()V
    .locals 0

    return-void
.end method

.method public visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 3

    .line 910
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 911
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v2, Lgroovyjarjarasm/asm/util/Textifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v2, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x20

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 912
    invoke-virtual {p0, v1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 913
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0x2e

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " : "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 p1, 0x1

    .line 914
    invoke-virtual {p0, p1, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 915
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 916
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitFieldTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 751
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Textifier;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V
    .locals 2

    .line 838
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 839
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->ltab:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 840
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "FRAME "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v0, -0x1

    const/16 v1, 0x5d

    if-eq p1, v0, :cond_4

    if-eqz p1, :cond_4

    const/4 p4, 0x1

    if-eq p1, p4, :cond_3

    const/4 p3, 0x2

    if-eq p1, p3, :cond_2

    const/4 p2, 0x3

    if-eq p1, p2, :cond_1

    const/4 p2, 0x4

    if-ne p1, p2, :cond_0

    .line 862
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "SAME1 "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 863
    invoke-direct {p0, p4, p5}, Lgroovyjarjarasm/asm/util/Textifier;->appendFrameTypes(I[Ljava/lang/Object;)V

    goto :goto_0

    .line 866
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    invoke-direct {p1}, Ljava/lang/IllegalArgumentException;-><init>()V

    throw p1

    .line 859
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "SAME"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 856
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p3, "CHOP "

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 851
    :cond_3
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p4, "APPEND ["

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 852
    invoke-direct {p0, p2, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendFrameTypes(I[Ljava/lang/Object;)V

    .line 853
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 844
    :cond_4
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "FULL ["

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 845
    invoke-direct {p0, p2, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendFrameTypes(I[Ljava/lang/Object;)V

    .line 846
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "] ["

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 847
    invoke-direct {p0, p4, p5}, Lgroovyjarjarasm/asm/util/Textifier;->appendFrameTypes(I[Ljava/lang/Object;)V

    .line 848
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 868
    :goto_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 869
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitIincInsn(II)V
    .locals 2

    .line 1018
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1019
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    .line 1020
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "IINC "

    .line 1021
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    .line 1022
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x20

    .line 1023
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    .line 1024
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0xa

    .line 1025
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1026
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitInnerClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 3

    .line 322
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 323
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    and-int/lit8 v0, p4, -0x21

    .line 324
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->appendRawAccess(I)V

    .line 325
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 326
    invoke-direct {p0, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendAccess(I)V

    .line 327
    iget-object p4, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "INNERCLASS "

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 328
    invoke-virtual {p0, v1, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 329
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p4, 0x20

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 330
    invoke-virtual {p0, v1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 331
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 332
    invoke-virtual {p0, v1, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 333
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 334
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitInsn(I)V
    .locals 2

    .line 874
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 875
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/Textifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v1, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 876
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitInsnAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 1072
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Textifier;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitIntInsn(II)V
    .locals 2

    .line 881
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 882
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    .line 883
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/Textifier;->OPCODES:[Ljava/lang/String;

    aget-object v1, v1, p1

    .line 884
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0x20

    .line 885
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0xbc

    if-ne p1, v1, :cond_0

    .line 886
    sget-object p1, Lgroovyjarjarasm/asm/util/Textifier;->TYPES:[Ljava/lang/String;

    aget-object p1, p1, p2

    goto :goto_0

    :cond_0
    invoke-static {p2}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object p1

    :goto_0
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0xa

    .line 887
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 888
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public varargs visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V
    .locals 4

    .line 944
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 945
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "INVOKEDYNAMIC"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v2, 0x20

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 946
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 p1, 0x3

    .line 947
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 948
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, " ["

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 949
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0xa

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 950
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab3:Ljava/lang/String;

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 951
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendHandle(Lgroovyjarjarasm/asm/Handle;)V

    .line 952
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 953
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab3:Ljava/lang/String;

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "// arguments:"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 954
    array-length p2, p4

    if-nez p2, :cond_0

    .line 955
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " none"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_2

    .line 957
    :cond_0
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 958
    array-length p2, p4

    :goto_0
    if-ge v1, p2, :cond_5

    aget-object p3, p4, v1

    .line 959
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v3, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab3:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 960
    instance-of v2, p3, Ljava/lang/String;

    if-eqz v2, :cond_1

    .line 961
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    check-cast p3, Ljava/lang/String;

    invoke-static {v2, p3}, Lgroovyjarjarasm/asm/util/Printer;->appendString(Ljava/lang/StringBuilder;Ljava/lang/String;)V

    goto :goto_1

    .line 962
    :cond_1
    instance-of v2, p3, Lgroovyjarjarasm/asm/Type;

    if-eqz v2, :cond_3

    .line 963
    check-cast p3, Lgroovyjarjarasm/asm/Type;

    .line 964
    invoke-virtual {p3}, Lgroovyjarjarasm/asm/Type;->getSort()I

    move-result v2

    const/16 v3, 0xb

    if-ne v2, v3, :cond_2

    .line 965
    invoke-virtual {p3}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p0, p1, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    goto :goto_1

    .line 967
    :cond_2
    invoke-direct {p0, p3}, Lgroovyjarjarasm/asm/util/Textifier;->visitType(Lgroovyjarjarasm/asm/Type;)V

    goto :goto_1

    .line 969
    :cond_3
    instance-of v2, p3, Lgroovyjarjarasm/asm/Handle;

    if-eqz v2, :cond_4

    .line 970
    check-cast p3, Lgroovyjarjarasm/asm/Handle;

    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendHandle(Lgroovyjarjarasm/asm/Handle;)V

    goto :goto_1

    .line 972
    :cond_4
    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 974
    :goto_1
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v2, ", \n"

    invoke-virtual {p3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 976
    :cond_5
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->length()I

    move-result p3

    sub-int/2addr p3, p1

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 978
    :goto_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 979
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "]\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 980
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 985
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 986
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/Textifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v1, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x20

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 987
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 988
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 989
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLabel(Lgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 994
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 995
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->ltab:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 996
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 997
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 998
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLdcInsn(Ljava/lang/Object;)V
    .locals 2

    .line 1003
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1004
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "LDC "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1005
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_0

    .line 1006
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    check-cast p1, Ljava/lang/String;

    invoke-static {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->appendString(Ljava/lang/StringBuilder;Ljava/lang/String;)V

    goto :goto_0

    .line 1007
    :cond_0
    instance-of v0, p1, Lgroovyjarjarasm/asm/Type;

    if-eqz v0, :cond_1

    .line 1008
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    check-cast p1, Lgroovyjarjarasm/asm/Type;

    invoke-virtual {p1}, Lgroovyjarjarasm/asm/Type;->getDescriptor()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ".class"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 1010
    :cond_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 1012
    :goto_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1013
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLineNumber(ILgroovyjarjarasm/asm/Label;)V
    .locals 2

    .line 1166
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1167
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "LINENUMBER "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x20

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1168
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1169
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1170
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V
    .locals 2

    .line 1116
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1117
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "LOCALVARIABLE "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v1, 0x20

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/4 v0, 0x1

    .line 1118
    invoke-virtual {p0, v0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1119
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1120
    invoke-virtual {p0, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1121
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1122
    invoke-virtual {p0, p5}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1123
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const/16 p4, 0xa

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    if-eqz p3, :cond_0

    .line 1126
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p4, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 p2, 0x2

    .line 1127
    invoke-virtual {p0, p2, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1128
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p4, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1129
    invoke-direct {p0, p1, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendJavaDeclaration(Ljava/lang/String;Ljava/lang/String;)V

    .line 1131
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitLocalVariableAnnotation(ILgroovyjarjarasm/asm/TypePath;[Lgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;[ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 3

    .line 1143
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1144
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "LOCALVARIABLE @"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v0, 0x1

    .line 1145
    invoke-virtual {p0, v0, p6}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1146
    iget-object p6, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x28

    invoke-virtual {p6, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1147
    iget-object p6, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p6, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 1149
    iget-object p6, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p6, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1150
    iget-object p6, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ") : "

    invoke-virtual {p6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1151
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendTypeReference(I)V

    .line 1152
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p6, ", "

    invoke-virtual {p1, p6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 1153
    :goto_0
    array-length p1, p3

    if-ge v1, p1, :cond_0

    .line 1154
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " [ "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1155
    aget-object p1, p3, v1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1156
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " - "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1157
    aget-object p1, p4, v1

    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1158
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    aget p2, p5, v1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " ]"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 1160
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-eqz p7, :cond_1

    const-string p2, "\n"

    goto :goto_1

    :cond_1
    const-string p2, " // invisible\n"

    :goto_1
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1161
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V
    .locals 4

    .line 1047
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1048
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "LOOKUPSWITCH\n"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1049
    :goto_0
    array-length v0, p3

    const/16 v2, 0xa

    if-ge v1, v0, :cond_0

    .line 1050
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v3, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab3:Ljava/lang/String;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    aget v3, p2, v1

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v3, ": "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1051
    aget-object v0, p3, v1

    invoke-virtual {p0, v0}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1052
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 1054
    :cond_0
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab3:Ljava/lang/String;

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "default: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1055
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1056
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1057
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitMainClass(Ljava/lang/String;)V
    .locals 2

    .line 463
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 464
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "  // main class "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 465
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitMaxs(II)V
    .locals 3

    .line 1175
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1176
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "MAXSTACK = "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1177
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-interface {p1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 1179
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1180
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, "MAXLOCALS = "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1181
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public bridge synthetic visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual/range {p0 .. p5}, Lgroovyjarjarasm/asm/util/Textifier;->visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 4

    .line 406
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 407
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v2, 0xa

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/high16 v0, 0x20000

    and-int/2addr v0, p1

    if-eqz v0, :cond_0

    .line 409
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v3, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v3, "// DEPRECATED\n"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 411
    :cond_0
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v3, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 412
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendRawAccess(I)V

    if-eqz p4, :cond_1

    .line 415
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v3, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v0, 0x4

    .line 416
    invoke-virtual {p0, v0, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 417
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v3, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 418
    invoke-direct {p0, p2, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendJavaDeclaration(Ljava/lang/String;Ljava/lang/String;)V

    .line 421
    :cond_1
    iget-object p4, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    and-int/lit16 p4, p1, -0xc1

    .line 422
    invoke-direct {p0, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendAccess(I)V

    and-int/lit16 p4, p1, 0x100

    if-eqz p4, :cond_2

    .line 424
    iget-object p4, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "native "

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_2
    and-int/lit16 p4, p1, 0x80

    if-eqz p4, :cond_3

    .line 427
    iget-object p4, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "varargs "

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_3
    and-int/lit8 p4, p1, 0x40

    if-eqz p4, :cond_4

    .line 430
    iget-object p4, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "bridge "

    invoke-virtual {p4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 432
    :cond_4
    iget p4, p0, Lgroovyjarjarasm/asm/util/Textifier;->access:I

    and-int/lit16 p4, p4, 0x200

    if-eqz p4, :cond_5

    and-int/lit16 p1, p1, 0x408

    if-nez p1, :cond_5

    .line 434
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p4, "default "

    invoke-virtual {p1, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 437
    :cond_5
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 p1, 0x3

    .line 438
    invoke-virtual {p0, p1, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    if-eqz p5, :cond_6

    .line 439
    array-length p1, p5

    if-lez p1, :cond_6

    .line 440
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " throws "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 441
    array-length p1, p5

    move p2, v1

    :goto_0
    if-ge p2, p1, :cond_6

    aget-object p3, p5, p2

    .line 442
    invoke-virtual {p0, v1, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 443
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p4, 0x20

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    .line 447
    :cond_6
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 448
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/4 p1, 0x0

    .line 449
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitMethodAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitMethodAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitMethodAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 0

    .line 785
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitMethodAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 0

    .line 823
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitMethodEnd()V
    .locals 0

    return-void
.end method

.method public visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 3

    .line 926
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 927
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v2, Lgroovyjarjarasm/asm/util/Textifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v2, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x20

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 928
    invoke-virtual {p0, v1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 929
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0x2e

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/4 p1, 0x3

    .line 930
    invoke-virtual {p0, p1, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    if-eqz p5, :cond_0

    .line 932
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, " (itf)"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 934
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 935
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitMethodTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 791
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Textifier;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitModule(Ljava/lang/String;ILjava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 2

    .line 245
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    and-int/lit8 p2, p2, 0x20

    if-eqz p2, :cond_0

    .line 247
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "open "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 249
    :cond_0
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, "module "

    .line 250
    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    .line 251
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, " { "

    .line 252
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    if-nez p3, :cond_1

    const-string p2, ""

    goto :goto_0

    .line 253
    :cond_1
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "// "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    :goto_0
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "\n\n"

    .line 254
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 255
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/4 p1, 0x0

    .line 256
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitModuleEnd()V
    .locals 0

    return-void
.end method

.method public visitMultiANewArrayInsn(Ljava/lang/String;I)V
    .locals 2

    .line 1062
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1063
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "MULTIANEWARRAY "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v0, 0x1

    .line 1064
    invoke-virtual {p0, v0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1065
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x20

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1066
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitNestHost(Ljava/lang/String;)V
    .locals 3

    .line 261
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 262
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "NESTHOST "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 263
    invoke-virtual {p0, v1, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 264
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 265
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitNestMember(Ljava/lang/String;)V
    .locals 3

    .line 303
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 304
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "NESTMEMBER "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 305
    invoke-virtual {p0, v1, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 306
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 307
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public varargs visitOpen(Ljava/lang/String;I[Ljava/lang/String;)V
    .locals 1

    const-string v0, "opens "

    .line 500
    invoke-direct {p0, v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/Textifier;->visitExportOrOpen(Ljava/lang/String;Ljava/lang/String;I[Ljava/lang/String;)V

    return-void
.end method

.method public visitOuterClass(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 3

    .line 270
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 271
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "OUTERCLASS "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 272
    invoke-virtual {p0, v1, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 273
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x20

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    if-eqz p2, :cond_0

    .line 275
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_0
    const/4 p1, 0x3

    .line 277
    invoke-virtual {p0, p1, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 278
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 279
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitPackage(Ljava/lang/String;)V
    .locals 2

    .line 470
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 471
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "  // package "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 472
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitParameter(Ljava/lang/String;I)V
    .locals 2

    .line 770
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 771
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "// parameter "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 772
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendAccess(I)V

    .line 773
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x20

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p2

    if-nez p1, :cond_0

    const-string p1, "<no name>"

    :cond_0
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 774
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public bridge synthetic visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/Textifier;->visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 3

    .line 807
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 808
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v2, 0x40

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/4 v0, 0x1

    .line 809
    invoke-virtual {p0, v0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 810
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x28

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 811
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p2, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 813
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 814
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-eqz p3, :cond_0

    const-string p3, ") // parameter "

    goto :goto_0

    :cond_0
    const-string p3, ") // invisible, parameter "

    .line 815
    :goto_0
    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    .line 816
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0xa

    .line 817
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 818
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitPermittedSubclass(Ljava/lang/String;)V
    .locals 3

    .line 312
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 313
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "PERMITTEDSUBCLASS "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 314
    invoke-virtual {p0, v1, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 315
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 316
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public varargs visitProvide(Ljava/lang/String;[Ljava/lang/String;)V
    .locals 3

    .line 534
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 535
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "provides "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 536
    invoke-virtual {p0, v1, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 537
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, " with\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move p1, v1

    .line 538
    :goto_0
    array-length v0, p2

    if-ge p1, v0, :cond_1

    .line 539
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 540
    aget-object v0, p2, p1

    invoke-virtual {p0, v1, v0}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 541
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    array-length v2, p2

    add-int/lit8 v2, v2, -0x1

    if-eq p1, v2, :cond_0

    const-string v2, ",\n"

    goto :goto_1

    :cond_0
    const-string v2, ";\n"

    :goto_1
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 543
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitRecordComponent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;
    .locals 2

    .line 340
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 341
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "RECORDCOMPONENT "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p3, :cond_0

    .line 343
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v0, 0x2

    .line 344
    invoke-virtual {p0, v0, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 345
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 346
    invoke-direct {p0, p1, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendJavaDeclaration(Ljava/lang/String;Ljava/lang/String;)V

    .line 349
    :cond_0
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 p3, 0x1

    .line 351
    invoke-virtual {p0, p3, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 352
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p3, 0x20

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 354
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 355
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/4 p1, 0x0

    .line 356
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic visitRecordComponentAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 51
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitRecordComponentAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitRecordComponentAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 0

    .line 720
    invoke-virtual {p0, p1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitRecordComponentAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 0

    .line 731
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitRecordComponentEnd()V
    .locals 0

    return-void
.end method

.method public visitRecordComponentTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 0

    .line 726
    invoke-virtual {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Textifier;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitRequire(Ljava/lang/String;ILjava/lang/String;)V
    .locals 2

    .line 477
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 478
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "requires "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    and-int/lit8 v0, p2, 0x20

    if-eqz v0, :cond_0

    .line 480
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "transitive "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    :cond_0
    and-int/lit8 v0, p2, 0x40

    if-eqz v0, :cond_1

    .line 483
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v1, "static "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 485
    :cond_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x3b

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 486
    invoke-direct {p0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendRawAccess(I)V

    if-eqz p3, :cond_2

    .line 488
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p2, "  // version "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 490
    :cond_2
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitSource(Ljava/lang/String;Ljava/lang/String;)V
    .locals 3

    .line 231
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    const/16 v0, 0xa

    if-eqz p1, :cond_0

    .line 233
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "// compiled from: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    :cond_0
    if-eqz p2, :cond_1

    .line 236
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, "// debug info: "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 238
    :cond_1
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->length()I

    move-result p1

    if-lez p1, :cond_2

    .line 239
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_2
    return-void
.end method

.method public varargs visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V
    .locals 3

    .line 1032
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v0, 0x0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1033
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v1, "TABLESWITCH\n"

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1034
    :goto_0
    array-length p2, p4

    const/16 v1, 0xa

    if-ge v0, p2, :cond_0

    .line 1035
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab3:Ljava/lang/String;

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    add-int v2, p1, v0

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v2, ": "

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1036
    aget-object p2, p4, v0

    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1037
    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 1039
    :cond_0
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab3:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, "default: "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1040
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1041
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1042
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;
    .locals 3

    .line 1094
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1095
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "TRYCATCHBLOCK @"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v0, 0x1

    .line 1096
    invoke-virtual {p0, v0, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1097
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x28

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1098
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p3, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 1100
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p3, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1101
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ") : "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1102
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendTypeReference(I)V

    .line 1103
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p3, ", "

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 1104
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-eqz p4, :cond_0

    const-string p2, "\n"

    goto :goto_0

    :cond_0
    const-string p2, " // invisible\n"

    :goto_0
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1105
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitTryCatchBlock(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Ljava/lang/String;)V
    .locals 3

    .line 1078
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1079
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "TRYCATCHBLOCK "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1080
    invoke-virtual {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1081
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x20

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1082
    invoke-virtual {p0, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1083
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1084
    invoke-virtual {p0, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 1085
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1086
    invoke-virtual {p0, v1, p4}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1087
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1088
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Textifier;
    .locals 3

    .line 1223
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1224
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const/16 v2, 0x40

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    const/4 v0, 0x1

    .line 1225
    invoke-virtual {p0, v0, p3}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 1226
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 v0, 0x28

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 1227
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p3, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 1229
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p3, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 1230
    iget-object p3, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ") : "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1231
    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendTypeReference(I)V

    .line 1232
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string p3, ", "

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 1233
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    if-eqz p4, :cond_0

    const-string p2, "\n"

    goto :goto_0

    :cond_0
    const-string p2, " // invisible\n"

    :goto_0
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 1234
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovyjarjarasm/asm/util/Textifier;->addNewTextifier(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Textifier;

    move-result-object p1

    return-object p1
.end method

.method public visitTypeInsn(ILjava/lang/String;)V
    .locals 3

    .line 900
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 901
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v2, Lgroovyjarjarasm/asm/util/Textifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v2, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x20

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 902
    invoke-virtual {p0, v1, p2}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 903
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 904
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitUse(Ljava/lang/String;)V
    .locals 3

    .line 525
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 526
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v2, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, "uses "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 527
    invoke-virtual {p0, v1, p1}, Lgroovyjarjarasm/asm/util/Textifier;->appendDescriptor(ILjava/lang/String;)V

    .line 528
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const-string v0, ";\n"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 529
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public visitVarInsn(II)V
    .locals 2

    .line 893
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->setLength(I)V

    .line 894
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    iget-object v1, p0, Lgroovyjarjarasm/asm/util/Textifier;->tab2:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lgroovyjarjarasm/asm/util/Textifier;->OPCODES:[Ljava/lang/String;

    aget-object p1, v1, p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 v0, 0x20

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const/16 p2, 0xa

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 895
    iget-object p1, p0, Lgroovyjarjarasm/asm/util/Textifier;->text:Ljava/util/List;

    iget-object p2, p0, Lgroovyjarjarasm/asm/util/Textifier;->stringBuilder:Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void
.end method
