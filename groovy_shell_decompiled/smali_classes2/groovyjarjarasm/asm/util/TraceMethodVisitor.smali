.class public final Lgroovyjarjarasm/asm/util/TraceMethodVisitor;
.super Lgroovyjarjarasm/asm/MethodVisitor;
.source "TraceMethodVisitor.java"


# instance fields
.field public final p:Lgroovyjarjarasm/asm/util/Printer;


# direct methods
.method public constructor <init>(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/util/Printer;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 65
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;-><init>(ILgroovyjarjarasm/asm/MethodVisitor;)V

    .line 66
    iput-object p2, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/util/Printer;)V
    .locals 1

    const/4 v0, 0x0

    .line 55
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;-><init>(Lgroovyjarjarasm/asm/MethodVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-void
.end method


# virtual methods
.method public visitAnnotableParameterCount(IZ)V
    .locals 1

    .line 104
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitAnnotableParameterCount(IZ)Lgroovyjarjarasm/asm/util/Printer;

    .line 105
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAnnotableParameterCount(IZ)V

    return-void
.end method

.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 77
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitMethodAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 78
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 79
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitAnnotationDefault()Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 3

    .line 98
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/Printer;->visitAnnotationDefault()Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 99
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    invoke-super {p0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAnnotationDefault()Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object v2

    invoke-direct {v1, v2, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 1

    .line 92
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitMethodAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    .line 93
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method public visitCode()V
    .locals 1

    .line 118
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/Printer;->visitCode()V

    .line 119
    invoke-super {p0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitCode()V

    return-void
.end method

.method public visitEnd()V
    .locals 1

    .line 309
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/Printer;->visitMethodEnd()V

    .line 310
    invoke-super {p0}, Lgroovyjarjarasm/asm/MethodVisitor;->visitEnd()V

    return-void
.end method

.method public visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 160
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 161
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFieldInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V
    .locals 6

    .line 129
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    move v1, p1

    move v2, p2

    move-object v3, p3

    move v4, p4

    move-object v5, p5

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/util/Printer;->visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V

    .line 130
    invoke-super/range {p0 .. p5}, Lgroovyjarjarasm/asm/MethodVisitor;->visitFrame(II[Ljava/lang/Object;I[Ljava/lang/Object;)V

    return-void
.end method

.method public visitIincInsn(II)V
    .locals 1

    .line 220
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitIincInsn(II)V

    .line 221
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIincInsn(II)V

    return-void
.end method

.method public visitInsn(I)V
    .locals 1

    .line 135
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitInsn(I)V

    .line 136
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsn(I)V

    return-void
.end method

.method public visitInsnAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 246
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitInsnAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 247
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 248
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInsnAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitIntInsn(II)V
    .locals 1

    .line 141
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitIntInsn(II)V

    .line 142
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitIntInsn(II)V

    return-void
.end method

.method public varargs visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V
    .locals 1

    .line 196
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    .line 197
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitInvokeDynamicInsn(Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Handle;[Ljava/lang/Object;)V

    return-void
.end method

.method public visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 202
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    .line 203
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitJumpInsn(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public visitLabel(Lgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 208
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    .line 209
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLabel(Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public visitLdcInsn(Ljava/lang/Object;)V
    .locals 1

    .line 214
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitLdcInsn(Ljava/lang/Object;)V

    .line 215
    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLdcInsn(Ljava/lang/Object;)V

    return-void
.end method

.method public visitLineNumber(ILgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 297
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitLineNumber(ILgroovyjarjarasm/asm/Label;)V

    .line 298
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLineNumber(ILgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V
    .locals 7

    .line 274
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move v6, p6

    invoke-virtual/range {v0 .. v6}, Lgroovyjarjarasm/asm/util/Printer;->visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V

    .line 275
    invoke-super/range {p0 .. p6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLocalVariable(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;I)V

    return-void
.end method

.method public visitLocalVariableAnnotation(ILgroovyjarjarasm/asm/TypePath;[Lgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;[ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 8

    .line 287
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    move v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move-object v5, p5

    move-object v6, p6

    move v7, p7

    .line 288
    invoke-virtual/range {v0 .. v7}, Lgroovyjarjarasm/asm/util/Printer;->visitLocalVariableAnnotation(ILgroovyjarjarasm/asm/TypePath;[Lgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;[ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 289
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 290
    invoke-super/range {p0 .. p7}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLocalVariableAnnotation(ILgroovyjarjarasm/asm/TypePath;[Lgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;[ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 233
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/Printer;->visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V

    .line 234
    invoke-super {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitLookupSwitchInsn(Lgroovyjarjarasm/asm/Label;[I[Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public visitMaxs(II)V
    .locals 1

    .line 303
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitMaxs(II)V

    .line 304
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMaxs(II)V

    return-void
.end method

.method public visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 7

    .line 173
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    iget v0, v0, Lgroovyjarjarasm/asm/util/Printer;->api:I

    const/high16 v1, 0x50000

    if-ge v0, v1, :cond_2

    const/16 v0, 0xb9

    if-ne p1, v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-ne p5, v0, :cond_1

    .line 181
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    .line 175
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "INVOKESPECIAL/STATIC on interfaces require ASM5"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 183
    :cond_2
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    move v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    invoke-virtual/range {v0 .. v5}, Lgroovyjarjarasm/asm/util/Printer;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 185
    :goto_1
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    if-eqz v0, :cond_3

    .line 186
    iget-object v1, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->mv:Lgroovyjarjarasm/asm/MethodVisitor;

    move v2, p1

    move-object v3, p2

    move-object v4, p3

    move-object v5, p4

    move v6, p5

    invoke-virtual/range {v1 .. v6}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMethodInsn(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    :cond_3
    return-void
.end method

.method public visitMultiANewArrayInsn(Ljava/lang/String;I)V
    .locals 1

    .line 239
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitMultiANewArrayInsn(Ljava/lang/String;I)V

    .line 240
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitMultiANewArrayInsn(Ljava/lang/String;I)V

    return-void
.end method

.method public visitParameter(Ljava/lang/String;I)V
    .locals 1

    .line 71
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitParameter(Ljava/lang/String;I)V

    .line 72
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitParameter(Ljava/lang/String;I)V

    return-void
.end method

.method public visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 111
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/Printer;->visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 112
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 113
    invoke-super {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/MethodVisitor;->visitParameterAnnotation(ILjava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public varargs visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V
    .locals 1

    .line 227
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V

    .line 228
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTableSwitchInsn(IILgroovyjarjarasm/asm/Label;[Lgroovyjarjarasm/asm/Label;)V

    return-void
.end method

.method public visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 261
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 262
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 263
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTryCatchAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitTryCatchBlock(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Ljava/lang/String;)V
    .locals 1

    .line 254
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitTryCatchBlock(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Ljava/lang/String;)V

    .line 255
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTryCatchBlock(Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Lgroovyjarjarasm/asm/Label;Ljava/lang/String;)V

    return-void
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 85
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/util/Printer;->visitMethodTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 86
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    .line 87
    invoke-super {p0, p1, p2, p3, p4}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitTypeInsn(ILjava/lang/String;)V
    .locals 1

    .line 153
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitTypeInsn(ILjava/lang/String;)V

    .line 154
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitTypeInsn(ILjava/lang/String;)V

    return-void
.end method

.method public visitVarInsn(II)V
    .locals 1

    .line 147
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceMethodVisitor;->p:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitVarInsn(II)V

    .line 148
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/MethodVisitor;->visitVarInsn(II)V

    return-void
.end method
