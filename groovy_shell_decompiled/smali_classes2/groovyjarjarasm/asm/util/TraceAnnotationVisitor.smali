.class public final Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;
.super Lgroovyjarjarasm/asm/AnnotationVisitor;
.source "TraceAnnotationVisitor.java"


# instance fields
.field private final printer:Lgroovyjarjarasm/asm/util/Printer;


# direct methods
.method public constructor <init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 60
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/AnnotationVisitor;-><init>(ILgroovyjarjarasm/asm/AnnotationVisitor;)V

    .line 61
    iput-object p2, p0, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    return-void
.end method

.method public constructor <init>(Lgroovyjarjarasm/asm/util/Printer;)V
    .locals 1

    const/4 v0, 0x0

    .line 49
    invoke-direct {p0, v0, p1}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-void
.end method


# virtual methods
.method public visit(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    .line 66
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visit(Ljava/lang/String;Ljava/lang/Object;)V

    .line 67
    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/AnnotationVisitor;->visit(Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method

.method public visitAnnotation(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 78
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjarasm/asm/util/Printer;->visitAnnotation(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 79
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    invoke-super {p0, p1, p2}, Lgroovyjarjarasm/asm/AnnotationVisitor;->visitAnnotation(Ljava/lang/String;Ljava/lang/String;)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitArray(Ljava/lang/String;)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 2

    .line 84
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1}, Lgroovyjarjarasm/asm/util/Printer;->visitArray(Ljava/lang/String;)Lgroovyjarjarasm/asm/util/Printer;

    move-result-object v0

    .line 85
    new-instance v1, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;

    invoke-super {p0, p1}, Lgroovyjarjarasm/asm/AnnotationVisitor;->visitArray(Ljava/lang/String;)Lgroovyjarjarasm/asm/AnnotationVisitor;

    move-result-object p1

    invoke-direct {v1, p1, v0}, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;-><init>(Lgroovyjarjarasm/asm/AnnotationVisitor;Lgroovyjarjarasm/asm/util/Printer;)V

    return-object v1
.end method

.method public visitEnd()V
    .locals 1

    .line 90
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/util/Printer;->visitAnnotationEnd()V

    .line 91
    invoke-super {p0}, Lgroovyjarjarasm/asm/AnnotationVisitor;->visitEnd()V

    return-void
.end method

.method public visitEnum(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 72
    iget-object v0, p0, Lgroovyjarjarasm/asm/util/TraceAnnotationVisitor;->printer:Lgroovyjarjarasm/asm/util/Printer;

    invoke-virtual {v0, p1, p2, p3}, Lgroovyjarjarasm/asm/util/Printer;->visitEnum(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 73
    invoke-super {p0, p1, p2, p3}, Lgroovyjarjarasm/asm/AnnotationVisitor;->visitEnum(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method
