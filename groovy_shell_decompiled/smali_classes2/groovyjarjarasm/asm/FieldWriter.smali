.class final Lgroovyjarjarasm/asm/FieldWriter;
.super Lgroovyjarjarasm/asm/FieldVisitor;
.source "FieldWriter.java"


# instance fields
.field private final accessFlags:I

.field private constantValueIndex:I

.field private final descriptorIndex:I

.field private firstAttribute:Lgroovyjarjarasm/asm/Attribute;

.field private lastRuntimeInvisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

.field private lastRuntimeInvisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

.field private lastRuntimeVisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

.field private lastRuntimeVisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

.field private final nameIndex:I

.field private signatureIndex:I

.field private final symbolTable:Lgroovyjarjarasm/asm/SymbolTable;


# direct methods
.method constructor <init>(Lgroovyjarjarasm/asm/SymbolTable;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    const/high16 v0, 0x90000

    .line 127
    invoke-direct {p0, v0}, Lgroovyjarjarasm/asm/FieldVisitor;-><init>(I)V

    .line 128
    iput-object p1, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    .line 129
    iput p2, p0, Lgroovyjarjarasm/asm/FieldWriter;->accessFlags:I

    .line 130
    invoke-virtual {p1, p3}, Lgroovyjarjarasm/asm/SymbolTable;->addConstantUtf8(Ljava/lang/String;)I

    move-result p2

    iput p2, p0, Lgroovyjarjarasm/asm/FieldWriter;->nameIndex:I

    .line 131
    invoke-virtual {p1, p4}, Lgroovyjarjarasm/asm/SymbolTable;->addConstantUtf8(Ljava/lang/String;)I

    move-result p2

    iput p2, p0, Lgroovyjarjarasm/asm/FieldWriter;->descriptorIndex:I

    if-eqz p5, :cond_0

    .line 133
    invoke-virtual {p1, p5}, Lgroovyjarjarasm/asm/SymbolTable;->addConstantUtf8(Ljava/lang/String;)I

    move-result p2

    iput p2, p0, Lgroovyjarjarasm/asm/FieldWriter;->signatureIndex:I

    :cond_0
    if-eqz p6, :cond_1

    .line 136
    invoke-virtual {p1, p6}, Lgroovyjarjarasm/asm/SymbolTable;->addConstant(Ljava/lang/Object;)Lgroovyjarjarasm/asm/Symbol;

    move-result-object p1

    iget p1, p1, Lgroovyjarjarasm/asm/Symbol;->index:I

    iput p1, p0, Lgroovyjarjarasm/asm/FieldWriter;->constantValueIndex:I

    :cond_1
    return-void
.end method


# virtual methods
.method final collectAttributePrototypes(Lgroovyjarjarasm/asm/Attribute$Set;)V
    .locals 1

    .line 282
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->firstAttribute:Lgroovyjarjarasm/asm/Attribute;

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/Attribute$Set;->addAttributes(Lgroovyjarjarasm/asm/Attribute;)V

    return-void
.end method

.method computeFieldInfoSize()I
    .locals 5

    .line 195
    iget v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->constantValueIndex:I

    if-eqz v0, :cond_0

    .line 197
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    const-string v1, "ConstantValue"

    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/SymbolTable;->addConstantUtf8(Ljava/lang/String;)I

    const/16 v0, 0x10

    goto :goto_0

    :cond_0
    const/16 v0, 0x8

    .line 200
    :goto_0
    iget-object v1, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    iget v2, p0, Lgroovyjarjarasm/asm/FieldWriter;->accessFlags:I

    iget v3, p0, Lgroovyjarjarasm/asm/FieldWriter;->signatureIndex:I

    invoke-static {v1, v2, v3}, Lgroovyjarjarasm/asm/Attribute;->computeAttributesSize(Lgroovyjarjarasm/asm/SymbolTable;II)I

    move-result v1

    add-int/2addr v0, v1

    .line 201
    iget-object v1, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    iget-object v2, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    iget-object v3, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    iget-object v4, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    .line 202
    invoke-static {v1, v2, v3, v4}, Lgroovyjarjarasm/asm/AnnotationWriter;->computeAnnotationsSize(Lgroovyjarjarasm/asm/AnnotationWriter;Lgroovyjarjarasm/asm/AnnotationWriter;Lgroovyjarjarasm/asm/AnnotationWriter;Lgroovyjarjarasm/asm/AnnotationWriter;)I

    move-result v1

    add-int/2addr v0, v1

    .line 207
    iget-object v1, p0, Lgroovyjarjarasm/asm/FieldWriter;->firstAttribute:Lgroovyjarjarasm/asm/Attribute;

    if-eqz v1, :cond_1

    .line 208
    iget-object v2, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    invoke-virtual {v1, v2}, Lgroovyjarjarasm/asm/Attribute;->computeAttributesSize(Lgroovyjarjarasm/asm/SymbolTable;)I

    move-result v1

    add-int/2addr v0, v1

    :cond_1
    return v0
.end method

.method putFieldInfo(Lgroovyjarjarasm/asm/ByteVector;)V
    .locals 9

    .line 220
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    invoke-virtual {v0}, Lgroovyjarjarasm/asm/SymbolTable;->getMajorVersion()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    const/16 v3, 0x31

    if-ge v0, v3, :cond_0

    move v0, v2

    goto :goto_0

    :cond_0
    move v0, v1

    :goto_0
    if-eqz v0, :cond_1

    const/16 v3, 0x1000

    goto :goto_1

    :cond_1
    move v3, v1

    .line 223
    :goto_1
    iget v4, p0, Lgroovyjarjarasm/asm/FieldWriter;->accessFlags:I

    not-int v3, v3

    and-int/2addr v3, v4

    invoke-virtual {p1, v3}, Lgroovyjarjarasm/asm/ByteVector;->putShort(I)Lgroovyjarjarasm/asm/ByteVector;

    move-result-object v3

    iget v4, p0, Lgroovyjarjarasm/asm/FieldWriter;->nameIndex:I

    invoke-virtual {v3, v4}, Lgroovyjarjarasm/asm/ByteVector;->putShort(I)Lgroovyjarjarasm/asm/ByteVector;

    move-result-object v3

    iget v4, p0, Lgroovyjarjarasm/asm/FieldWriter;->descriptorIndex:I

    invoke-virtual {v3, v4}, Lgroovyjarjarasm/asm/ByteVector;->putShort(I)Lgroovyjarjarasm/asm/ByteVector;

    .line 227
    iget v3, p0, Lgroovyjarjarasm/asm/FieldWriter;->constantValueIndex:I

    if-eqz v3, :cond_2

    move v1, v2

    .line 230
    :cond_2
    iget v2, p0, Lgroovyjarjarasm/asm/FieldWriter;->accessFlags:I

    and-int/lit16 v3, v2, 0x1000

    if-eqz v3, :cond_3

    if-eqz v0, :cond_3

    add-int/lit8 v1, v1, 0x1

    .line 233
    :cond_3
    iget v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->signatureIndex:I

    if-eqz v0, :cond_4

    add-int/lit8 v1, v1, 0x1

    :cond_4
    const/high16 v0, 0x20000

    and-int/2addr v0, v2

    if-eqz v0, :cond_5

    add-int/lit8 v1, v1, 0x1

    .line 239
    :cond_5
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    if-eqz v0, :cond_6

    add-int/lit8 v1, v1, 0x1

    .line 242
    :cond_6
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    if-eqz v0, :cond_7

    add-int/lit8 v1, v1, 0x1

    .line 245
    :cond_7
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    if-eqz v0, :cond_8

    add-int/lit8 v1, v1, 0x1

    .line 248
    :cond_8
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    if-eqz v0, :cond_9

    add-int/lit8 v1, v1, 0x1

    .line 251
    :cond_9
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->firstAttribute:Lgroovyjarjarasm/asm/Attribute;

    if-eqz v0, :cond_a

    .line 252
    invoke-virtual {v0}, Lgroovyjarjarasm/asm/Attribute;->getAttributeCount()I

    move-result v0

    add-int/2addr v1, v0

    .line 254
    :cond_a
    invoke-virtual {p1, v1}, Lgroovyjarjarasm/asm/ByteVector;->putShort(I)Lgroovyjarjarasm/asm/ByteVector;

    .line 257
    iget v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->constantValueIndex:I

    if-eqz v0, :cond_b

    .line 258
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    const-string v1, "ConstantValue"

    .line 259
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/SymbolTable;->addConstantUtf8(Ljava/lang/String;)I

    move-result v0

    invoke-virtual {p1, v0}, Lgroovyjarjarasm/asm/ByteVector;->putShort(I)Lgroovyjarjarasm/asm/ByteVector;

    move-result-object v0

    const/4 v1, 0x2

    .line 260
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/ByteVector;->putInt(I)Lgroovyjarjarasm/asm/ByteVector;

    move-result-object v0

    iget v1, p0, Lgroovyjarjarasm/asm/FieldWriter;->constantValueIndex:I

    .line 261
    invoke-virtual {v0, v1}, Lgroovyjarjarasm/asm/ByteVector;->putShort(I)Lgroovyjarjarasm/asm/ByteVector;

    .line 263
    :cond_b
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    iget v1, p0, Lgroovyjarjarasm/asm/FieldWriter;->accessFlags:I

    iget v2, p0, Lgroovyjarjarasm/asm/FieldWriter;->signatureIndex:I

    invoke-static {v0, v1, v2, p1}, Lgroovyjarjarasm/asm/Attribute;->putAttributes(Lgroovyjarjarasm/asm/SymbolTable;IILgroovyjarjarasm/asm/ByteVector;)V

    .line 264
    iget-object v3, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    iget-object v4, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    iget-object v5, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    iget-object v6, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    iget-object v7, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    move-object v8, p1

    invoke-static/range {v3 .. v8}, Lgroovyjarjarasm/asm/AnnotationWriter;->putAnnotations(Lgroovyjarjarasm/asm/SymbolTable;Lgroovyjarjarasm/asm/AnnotationWriter;Lgroovyjarjarasm/asm/AnnotationWriter;Lgroovyjarjarasm/asm/AnnotationWriter;Lgroovyjarjarasm/asm/AnnotationWriter;Lgroovyjarjarasm/asm/ByteVector;)V

    .line 271
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->firstAttribute:Lgroovyjarjarasm/asm/Attribute;

    if-eqz v0, :cond_c

    .line 272
    iget-object v1, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    invoke-virtual {v0, v1, p1}, Lgroovyjarjarasm/asm/Attribute;->putAttributes(Lgroovyjarjarasm/asm/SymbolTable;Lgroovyjarjarasm/asm/ByteVector;)V

    :cond_c
    return-void
.end method

.method public visitAnnotation(Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 1

    if-eqz p2, :cond_0

    .line 147
    iget-object p2, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    .line 148
    invoke-static {p2, p1, v0}, Lgroovyjarjarasm/asm/AnnotationWriter;->create(Lgroovyjarjarasm/asm/SymbolTable;Ljava/lang/String;Lgroovyjarjarasm/asm/AnnotationWriter;)Lgroovyjarjarasm/asm/AnnotationWriter;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    return-object p1

    .line 150
    :cond_0
    iget-object p2, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    .line 151
    invoke-static {p2, p1, v0}, Lgroovyjarjarasm/asm/AnnotationWriter;->create(Lgroovyjarjarasm/asm/SymbolTable;Ljava/lang/String;Lgroovyjarjarasm/asm/AnnotationWriter;)Lgroovyjarjarasm/asm/AnnotationWriter;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    return-object p1
.end method

.method public visitAttribute(Lgroovyjarjarasm/asm/Attribute;)V
    .locals 1

    .line 172
    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->firstAttribute:Lgroovyjarjarasm/asm/Attribute;

    iput-object v0, p1, Lgroovyjarjarasm/asm/Attribute;->nextAttribute:Lgroovyjarjarasm/asm/Attribute;

    .line 173
    iput-object p1, p0, Lgroovyjarjarasm/asm/FieldWriter;->firstAttribute:Lgroovyjarjarasm/asm/Attribute;

    return-void
.end method

.method public visitEnd()V
    .locals 0

    return-void
.end method

.method public visitTypeAnnotation(ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Z)Lgroovyjarjarasm/asm/AnnotationVisitor;
    .locals 1

    if-eqz p4, :cond_0

    .line 159
    iget-object p4, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    .line 160
    invoke-static {p4, p1, p2, p3, v0}, Lgroovyjarjarasm/asm/AnnotationWriter;->create(Lgroovyjarjarasm/asm/SymbolTable;ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Lgroovyjarjarasm/asm/AnnotationWriter;)Lgroovyjarjarasm/asm/AnnotationWriter;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeVisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    return-object p1

    .line 163
    :cond_0
    iget-object p4, p0, Lgroovyjarjarasm/asm/FieldWriter;->symbolTable:Lgroovyjarjarasm/asm/SymbolTable;

    iget-object v0, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    .line 164
    invoke-static {p4, p1, p2, p3, v0}, Lgroovyjarjarasm/asm/AnnotationWriter;->create(Lgroovyjarjarasm/asm/SymbolTable;ILgroovyjarjarasm/asm/TypePath;Ljava/lang/String;Lgroovyjarjarasm/asm/AnnotationWriter;)Lgroovyjarjarasm/asm/AnnotationWriter;

    move-result-object p1

    iput-object p1, p0, Lgroovyjarjarasm/asm/FieldWriter;->lastRuntimeInvisibleTypeAnnotation:Lgroovyjarjarasm/asm/AnnotationWriter;

    return-object p1
.end method
