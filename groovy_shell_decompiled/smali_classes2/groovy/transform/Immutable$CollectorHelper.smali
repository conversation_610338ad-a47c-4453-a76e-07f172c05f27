.class public final Lgroovy/transform/Immutable$CollectorHelper;
.super Ljava/lang/Object;
.source "Immutable.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/transform/Immutable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "CollectorHelper"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;


# direct methods
.method public static synthetic $static_methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    const-class v0, Lgroovy/transform/Immutable$CollectorHelper;

    instance-of v1, p1, [Ljava/lang/Object;

    const/4 v2, 0x1

    xor-int/2addr v1, v2

    const-string v3, ""

    const/4 v4, 0x0

    if-eqz v1, :cond_0

    const-class v1, Lgroovy/transform/Immutable;

    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v2, [Ljava/lang/Object;

    aput-object p0, v6, v4

    filled-new-array {v3, v3}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v5, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v3, p0

    check-cast v3, Ljava/lang/String;

    new-array v2, v2, [Ljava/lang/Object;

    aput-object p1, v2, v4

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_0
    const-class v1, [Ljava/lang/Object;

    invoke-static {p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Ljava/lang/Object;

    array-length v1, v1

    if-ne v1, v2, :cond_1

    move v1, v2

    goto :goto_0

    :cond_1
    move v1, v4

    :goto_0
    if-eqz v1, :cond_2

    const-class v1, Lgroovy/transform/Immutable;

    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v2, [Ljava/lang/Object;

    aput-object p0, v6, v4

    filled-new-array {v3, v3}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v5, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v3, p0

    check-cast v3, Ljava/lang/String;

    new-array v2, v2, [Ljava/lang/Object;

    const-class v3, [Ljava/lang/Object;

    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {p1, v4}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p1

    aput-object p1, v2, v4

    invoke-static {v0, v1, p0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0

    :cond_2
    const-class v1, Lgroovy/transform/Immutable;

    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v2, [Ljava/lang/Object;

    aput-object p0, v6, v4

    filled-new-array {v3, v3}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v5, v6, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v3, p0

    check-cast v3, Ljava/lang/String;

    new-array v3, v4, [Ljava/lang/Object;

    new-array v5, v2, [Ljava/lang/Object;

    aput-object p1, v5, v4

    new-array p1, v2, [I

    aput v4, p1, v4

    invoke-static {v3, v5, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p1

    invoke-static {v0, v1, p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 5

    const-class v0, Lgroovy/transform/Immutable$CollectorHelper;

    const-class v1, Lgroovy/transform/Immutable;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p0, v3, v4

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v3, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v2, p0

    check-cast v2, Ljava/lang/String;

    invoke-static {v0, v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic $static_propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    const-class v0, Lgroovy/transform/Immutable;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p0, v2, v3

    const-string p0, ""

    filled-new-array {p0, p0}, [Ljava/lang/String;

    move-result-object p0

    invoke-direct {v1, v2, p0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    move-object v1, p0

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x0

    invoke-static {p1, v1, v0, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>()V
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0}, Lgroovy/transform/Immutable$CollectorHelper;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/transform/Immutable$CollectorHelper;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public static value()[[Ljava/lang/Object;
    .locals 13
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    const/16 v0, 0x8

    new-array v0, v0, [[Ljava/lang/Object;

    const/4 v1, 0x2

    new-array v2, v1, [Ljava/lang/Object;

    const-class v3, Lgroovy/transform/ToString;

    const/4 v4, 0x0

    aput-object v3, v2, v4

    const/4 v3, 0x4

    new-array v5, v3, [Ljava/lang/Object;

    const-string v6, "cache"

    aput-object v6, v5, v4

    const/4 v7, 0x1

    invoke-static {v7}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v8

    aput-object v8, v5, v7

    const-string v9, "includeSuperProperties"

    aput-object v9, v5, v1

    const/4 v10, 0x3

    aput-object v8, v5, v10

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v5

    aput-object v5, v2, v7

    aput-object v2, v0, v4

    new-array v2, v1, [Ljava/lang/Object;

    const-class v5, Lgroovy/transform/EqualsAndHashCode;

    aput-object v5, v2, v4

    new-array v5, v1, [Ljava/lang/Object;

    aput-object v6, v5, v4

    aput-object v8, v5, v7

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v5

    aput-object v5, v2, v7

    aput-object v2, v0, v7

    new-array v2, v1, [Ljava/lang/Object;

    const-class v5, Lgroovy/transform/ImmutableBase;

    aput-object v5, v2, v4

    new-array v5, v4, [Ljava/lang/Object;

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v5

    aput-object v5, v2, v7

    aput-object v2, v0, v1

    new-array v2, v1, [Ljava/lang/Object;

    const-class v5, Lgroovy/transform/ImmutableOptions;

    aput-object v5, v2, v4

    new-array v5, v4, [Ljava/lang/Object;

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v5

    aput-object v5, v2, v7

    aput-object v2, v0, v10

    new-array v2, v1, [Ljava/lang/Object;

    const-class v5, Lgroovy/transform/PropertyOptions;

    aput-object v5, v2, v4

    new-array v5, v1, [Ljava/lang/Object;

    const-string v6, "propertyHandler"

    aput-object v6, v5, v4

    const-class v6, Lgroovy/transform/options/ImmutablePropertyHandler;

    aput-object v6, v5, v7

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v5

    aput-object v5, v2, v7

    aput-object v2, v0, v3

    new-array v2, v1, [Ljava/lang/Object;

    const-class v5, Lgroovy/transform/TupleConstructor;

    aput-object v5, v2, v4

    new-array v5, v1, [Ljava/lang/Object;

    const-string v6, "defaults"

    aput-object v6, v5, v4

    invoke-static {v4}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v6

    aput-object v6, v5, v7

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v5

    aput-object v5, v2, v7

    const/4 v5, 0x5

    aput-object v2, v0, v5

    new-array v2, v1, [Ljava/lang/Object;

    const-class v6, Lgroovy/transform/MapConstructor;

    aput-object v6, v2, v4

    const/4 v6, 0x6

    new-array v11, v6, [Ljava/lang/Object;

    const-string v12, "noArg"

    aput-object v12, v11, v4

    aput-object v8, v11, v7

    aput-object v9, v11, v1

    aput-object v8, v11, v10

    const-string v9, "includeFields"

    aput-object v9, v11, v3

    aput-object v8, v11, v5

    invoke-static {v11}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v3

    aput-object v3, v2, v7

    aput-object v2, v0, v6

    new-array v1, v1, [Ljava/lang/Object;

    const-class v2, Lgroovy/transform/KnownImmutable;

    aput-object v2, v1, v4

    new-array v2, v4, [Ljava/lang/Object;

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v2

    aput-object v2, v1, v7

    const/4 v2, 0x7

    aput-object v1, v0, v2

    return-object v0
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/transform/Immutable$CollectorHelper;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/transform/Immutable$CollectorHelper;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/transform/Immutable$CollectorHelper;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lgroovy/transform/Immutable$CollectorHelper;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lgroovy/transform/Immutable$CollectorHelper;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/transform/Immutable$CollectorHelper;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public synthetic methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    const-class v0, Lgroovy/transform/Immutable$CollectorHelper;

    instance-of v1, p2, [Ljava/lang/Object;

    const/4 v2, 0x1

    xor-int/2addr v1, v2

    const-string v3, ""

    const/4 v4, 0x0

    if-eqz v1, :cond_0

    const-class v1, Lgroovy/transform/Immutable;

    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v2, [Ljava/lang/Object;

    aput-object p1, v6, v4

    filled-new-array {v3, v3}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v5, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v3, p1

    check-cast v3, Ljava/lang/String;

    new-array v2, v2, [Ljava/lang/Object;

    aput-object p2, v2, v4

    invoke-static {v0, v1, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    const-class v1, [Ljava/lang/Object;

    invoke-static {p2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [Ljava/lang/Object;

    array-length v1, v1

    if-ne v1, v2, :cond_1

    move v1, v2

    goto :goto_0

    :cond_1
    move v1, v4

    :goto_0
    if-eqz v1, :cond_2

    const-class v1, Lgroovy/transform/Immutable;

    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v2, [Ljava/lang/Object;

    aput-object p1, v6, v4

    filled-new-array {v3, v3}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v5, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v3, p1

    check-cast v3, Ljava/lang/String;

    new-array v2, v2, [Ljava/lang/Object;

    const-class v3, [Ljava/lang/Object;

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {p2, v4}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    aput-object p2, v2, v4

    invoke-static {v0, v1, p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_2
    const-class v1, Lgroovy/transform/Immutable;

    new-instance v5, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v6, v2, [Ljava/lang/Object;

    aput-object p1, v6, v4

    filled-new-array {v3, v3}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v5, v6, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v3, p1

    check-cast v3, Ljava/lang/String;

    new-array v3, v4, [Ljava/lang/Object;

    new-array v5, v2, [Ljava/lang/Object;

    aput-object p2, v5, v4

    new-array p2, v2, [I

    aput v4, p2, v4

    invoke-static {v3, v5, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->despreadList([Ljava/lang/Object;[Ljava/lang/Object;[I)[Ljava/lang/Object;

    move-result-object p2

    invoke-static {v0, v1, p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;)Ljava/lang/Object;
    .locals 5

    const-class v0, Lgroovy/transform/Immutable$CollectorHelper;

    const-class v1, Lgroovy/transform/Immutable;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v3, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Ljava/lang/String;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic propertyMissing(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 4

    const-class v0, Lgroovy/transform/Immutable;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v2, 0x1

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    const-string p1, ""

    filled-new-array {p1, p1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, v2, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    const/4 v1, 0x0

    invoke-static {p2, v1, v0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lgroovy/transform/Immutable$CollectorHelper;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method
