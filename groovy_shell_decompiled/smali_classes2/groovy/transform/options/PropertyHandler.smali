.class public abstract Lgroovy/transform/options/PropertyHandler;
.super Ljava/lang/Object;
.source "PropertyHandler.java"


# annotations
.annotation runtime Lorg/apache/groovy/lang/annotation/Incubating;
.end annotation


# static fields
.field private static final PROPERTY_OPTIONS_CLASS:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "+",
            "Ljava/lang/annotation/Annotation;",
            ">;"
        }
    .end annotation
.end field

.field public static final PROPERTY_OPTIONS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 45
    const-class v0, Lgroovy/transform/PropertyOptions;

    sput-object v0, Lgroovy/transform/options/PropertyHandler;->PROPERTY_OPTIONS_CLASS:Ljava/lang/Class;

    const/4 v1, 0x0

    .line 46
    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/PropertyHandler;->PROPERTY_OPTIONS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 44
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static createPropertyHandler(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lgroovy/lang/GroovyClassLoader;Lorg/codehaus/groovy/ast/ClassNode;)Lgroovy/transform/options/PropertyHandler;
    .locals 5

    .line 92
    sget-object v0, Lgroovy/transform/options/PropertyHandler;->PROPERTY_OPTIONS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getAnnotations(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p2

    .line 93
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x0

    if-eqz v0, :cond_0

    move-object p2, v2

    goto :goto_0

    :cond_0
    invoke-interface {p2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/ast/AnnotationNode;

    :goto_0
    if-nez p2, :cond_1

    .line 94
    new-instance p0, Lgroovy/transform/options/DefaultPropertyHandler;

    invoke-direct {p0}, Lgroovy/transform/options/DefaultPropertyHandler;-><init>()V

    return-object p0

    .line 96
    :cond_1
    const-class v0, Lgroovy/transform/options/DefaultPropertyHandler;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const-string v3, "propertyHandler"

    invoke-virtual {p0, p2, v3, v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberClassValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    if-nez v0, :cond_2

    const-string p1, "Couldn\'t determine propertyHandler class"

    .line 99
    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v2

    .line 103
    :cond_2
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v3

    .line 105
    :try_start_0
    invoke-virtual {p1, v3}, Lgroovy/lang/GroovyClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p1

    new-array v4, v1, [Ljava/lang/Class;

    invoke-virtual {p1, v4}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object p1

    new-array v1, v1, [Ljava/lang/Object;

    invoke-virtual {p1, v1}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    .line 106
    const-class v1, Lgroovy/transform/options/PropertyHandler;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v4

    invoke-virtual {v1, v4}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-nez v1, :cond_3

    .line 107
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "The propertyHandler class \'"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "\' on "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getAnnotationName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " is not a propertyHandler"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v2

    .line 111
    :cond_3
    check-cast p1, Lgroovy/transform/options/PropertyHandler;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 113
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Can\'t load propertyHandler \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "\' "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-object v2
.end method


# virtual methods
.method public createPropGetter(Lorg/codehaus/groovy/ast/PropertyNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0

    .line 70
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getGetterBlock()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method public abstract createPropInit(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/stmt/Statement;
.end method

.method public createPropSetter(Lorg/codehaus/groovy/ast/PropertyNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0

    .line 79
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getSetterBlock()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method protected isValidAttribute(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z
    .locals 2

    .line 83
    invoke-virtual {p1, p2, p3}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 84
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Error during "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getAnnotationName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " processing: Annotation attribute \'"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string v0, "\' not supported for property handler "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    .line 85
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    .line 84
    invoke-virtual {p1, p3, p2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    const/4 p1, 0x0

    return p1

    :cond_0
    const/4 p1, 0x1

    return p1
.end method

.method public abstract validateAttributes(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;)Z
.end method

.method public validateProperties(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/transform/AbstractASTTransformation;",
            "Lorg/codehaus/groovy/ast/stmt/BlockStatement;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/PropertyNode;",
            ">;)Z"
        }
    .end annotation

    const/4 p1, 0x1

    return p1
.end method
