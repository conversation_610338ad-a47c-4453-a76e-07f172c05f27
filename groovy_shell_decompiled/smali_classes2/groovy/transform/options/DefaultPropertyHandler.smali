.class public Lgroovy/transform/options/DefaultPropertyHandler;
.super Lgroovy/transform/options/PropertyHandler;
.source "DefaultPropertyHandler.java"


# static fields
.field private static final IMMUTABLE_XFORM_TYPE:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 52
    const-class v0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/DefaultPropertyHandler;->IMMUTABLE_XFORM_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 51
    invoke-direct {p0}, Lgroovy/transform/options/PropertyHandler;-><init>()V

    return-void
.end method

.method private static assignFieldS(ZLorg/codehaus/groovy/ast/Parameter;Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 4

    const/4 v0, 0x1

    new-array v0, v0, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 97
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    .line 98
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    const-string v3, "get"

    invoke-static {v1, v3, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    .line 99
    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 100
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    const-string v3, "containsKey"

    invoke-static {p1, v3, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    .line 101
    invoke-virtual {p1, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    if-eqz p0, :cond_0

    .line 103
    invoke-static {p2, v1}, Lgroovy/transform/options/DefaultPropertyHandler;->setViaSetterS(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    goto :goto_0

    .line 104
    :cond_0
    invoke-static {p2, v1}, Lgroovy/transform/options/DefaultPropertyHandler;->assignToFieldS(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    .line 102
    :goto_0
    invoke-static {p1, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p0

    return-object p0
.end method

.method private static assignToFieldS(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 1

    const-string v0, "this"

    .line 89
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-static {v0, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object p0

    invoke-static {p0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private static setViaSetterS(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0

    .line 93
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public createPropInit(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 4

    .line 72
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 73
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p4

    const/4 v1, 0x1

    .line 74
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    const-string v3, "useSetters"

    invoke-virtual {p1, p2, v3, v2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result p1

    .line 75
    invoke-virtual {p3, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getProperty(Ljava/lang/String;)Lorg/codehaus/groovy/ast/PropertyNode;

    move-result-object p2

    if-eqz p2, :cond_0

    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result p2

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    if-eqz p5, :cond_1

    .line 77
    invoke-static {p1, p5, v0}, Lgroovy/transform/options/DefaultPropertyHandler;->assignFieldS(ZLorg/codehaus/groovy/ast/Parameter;Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1

    .line 79
    :cond_1
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p2

    if-eqz p1, :cond_2

    if-eqz v1, :cond_2

    .line 81
    invoke-static {v0, p2}, Lgroovy/transform/options/DefaultPropertyHandler;->setViaSetterS(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1

    .line 83
    :cond_2
    invoke-static {v0, p2}, Lgroovy/transform/options/DefaultPropertyHandler;->assignToFieldS(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method public validateAttributes(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method public validateProperties(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/transform/AbstractASTTransformation;",
            "Lorg/codehaus/groovy/ast/stmt/BlockStatement;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/PropertyNode;",
            ">;)Z"
        }
    .end annotation

    .line 63
    instance-of v0, p1, Lorg/codehaus/groovy/transform/MapConstructorASTTransformation;

    if-eqz v0, :cond_0

    const-string v0, "args"

    .line 64
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v1

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    new-instance v3, Lorg/codehaus/groovy/ast/expr/MapExpression;

    invoke-direct {v3}, Lorg/codehaus/groovy/ast/expr/MapExpression;-><init>()V

    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v1

    invoke-virtual {p2, v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 65
    sget-object v1, Lgroovy/transform/options/DefaultPropertyHandler;->IMMUTABLE_XFORM_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v2, "this"

    filled-new-array {v2, v0}, [Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    const-string v2, "checkPropNames"

    invoke-static {v1, v2, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 67
    :cond_0
    invoke-super {p0, p1, p2, p3, p4}, Lgroovy/transform/options/PropertyHandler;->validateProperties(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Z

    move-result p1

    return p1
.end method
