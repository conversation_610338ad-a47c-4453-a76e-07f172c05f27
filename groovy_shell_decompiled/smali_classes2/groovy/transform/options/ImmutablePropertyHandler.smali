.class public Lgroovy/transform/options/ImmutablePropertyHandler;
.super Lgroovy/transform/options/PropertyHandler;
.source "ImmutablePropertyHandler.java"


# static fields
.field private static final CLONEABLE_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final COLLECTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final DGM_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final MAP_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final MAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final READONLYEXCEPTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final SELF_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final SET_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final SORTEDMAP_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

.field private static final SORTEDSET_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 81
    const-class v0, Ljava/lang/Cloneable;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->CLONEABLE_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 82
    const-class v0, Ljava/util/Collection;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->COLLECTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 83
    const-class v0, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->DGM_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 84
    const-class v0, Lorg/codehaus/groovy/transform/ImmutableASTTransformation;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->SELF_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 85
    const-class v0, Ljava/util/Map;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->MAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 86
    const-class v0, Ljava/util/SortedSet;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->SORTEDSET_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 87
    const-class v0, Ljava/util/SortedMap;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->SORTEDMAP_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 88
    const-class v0, Ljava/util/Set;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->SET_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 89
    const-class v0, Ljava/util/Map;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->MAP_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 90
    const-class v0, Lgroovy/lang/ReadOnlyPropertyException;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->READONLYEXCEPTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 80
    invoke-direct {p0}, Lgroovy/transform/options/PropertyHandler;-><init>()V

    return-void
.end method

.method private static assignFieldWithDefault(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 4

    if-nez p0, :cond_0

    return-object p2

    :cond_0
    const/4 v0, 0x1

    new-array v0, v0, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 272
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    .line 273
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    const-string v3, "get"

    invoke-static {v1, v3, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    .line 274
    invoke-virtual {v1, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 275
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    const-string v1, "containsKey"

    invoke-static {p0, v1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p0

    .line 276
    invoke-virtual {p0, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 277
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->getField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p1

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/FieldNode;->setInitialValueExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 278
    invoke-static {p0, p2, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p0

    return-object p0
.end method

.method private static assignWithDefault(ZLorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0

    if-nez p0, :cond_0

    return-object p3

    .line 285
    :cond_0
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object p0

    invoke-static {p0, p1, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p0

    return-object p0
.end method

.method private static createCheckImmutable(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;Ljava/util/List;Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Lorg/codehaus/groovy/ast/expr/Expression;"
        }
    .end annotation

    const/4 v0, 0x5

    new-array v0, v0, [Lorg/codehaus/groovy/ast/expr/Expression;

    const-string v1, "getClass"

    .line 323
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p0

    const/4 v1, 0x1

    aput-object p0, v0, v1

    const/4 p0, 0x2

    aput-object p1, v0, p0

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->list2args(Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/ListExpression;

    move-result-object p0

    const/4 p1, 0x3

    aput-object p0, v0, p1

    invoke-static {p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->classList2args(Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/ListExpression;

    move-result-object p0

    const/4 p1, 0x4

    aput-object p0, v0, p1

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p0

    .line 324
    sget-object p1, Lgroovy/transform/options/ImmutablePropertyHandler;->SELF_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string p2, "checkImmutable"

    invoke-static {p1, p2, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p0

    return-object p0
.end method

.method private static createConstructorStatementArrayOrCloneable(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 5

    const-string v0, "this"

    .line 385
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    .line 386
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    if-eqz p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    .line 387
    :goto_0
    invoke-static {p0, v2}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 388
    invoke-static {v2, v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneArrayOrCloneableExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    invoke-static {v0, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v3

    .line 390
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v2

    if-eqz p2, :cond_1

    .line 391
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object v4

    goto :goto_1

    :cond_1
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v4

    .line 389
    :goto_1
    invoke-static {v2, v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v2

    .line 394
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    if-eqz v3, :cond_3

    .line 395
    instance-of v4, v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v4, :cond_2

    move-object v4, v3

    check-cast v4, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v4

    if-eqz v4, :cond_2

    goto :goto_2

    .line 398
    :cond_2
    invoke-static {v3, v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneArrayOrCloneableExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    invoke-static {v0, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    goto :goto_3

    :cond_3
    :goto_2
    if-eqz p2, :cond_4

    .line 396
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object p2

    goto :goto_3

    :cond_4
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    .line 400
    :goto_3
    invoke-static {p1, p0, v2, p2}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignFieldWithDefault(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private static createConstructorStatementArrayOrCloneable(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 4

    const-string v0, "this"

    .line 370
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    .line 371
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 372
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    .line 373
    invoke-static {p0, p1}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    if-eqz v1, :cond_1

    .line 375
    instance-of v3, v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v3, :cond_0

    move-object v3, v1

    check-cast v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_0

    .line 378
    :cond_0
    invoke-static {v1, v2}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneArrayOrCloneableExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    goto :goto_1

    .line 376
    :cond_1
    :goto_0
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    .line 380
    :goto_1
    invoke-static {p0, v2}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneArrayOrCloneableExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    .line 381
    invoke-static {p1, v1, p0, v0}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignWithDefault(ZLorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private createConstructorStatementCollection(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 6

    const-string v0, "this"

    .line 347
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    .line 348
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    if-eqz p2, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    .line 349
    :goto_0
    invoke-static {p1, v2}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 350
    sget-object v3, Lgroovy/transform/options/ImmutablePropertyHandler;->CLONEABLE_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 351
    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isInstanceOfX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v3

    .line 352
    invoke-static {v2, v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneArrayOrCloneableExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    invoke-virtual {p0, v4, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    invoke-static {v0, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v4

    .line 353
    invoke-virtual {p0, v2, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    invoke-static {v0, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v5

    .line 350
    invoke-static {v3, v4, v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v3

    .line 355
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v2

    if-eqz p3, :cond_1

    .line 356
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object v4

    goto :goto_1

    :cond_1
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v4

    .line 354
    :goto_1
    invoke-static {v2, v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v2

    .line 358
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    if-eqz v3, :cond_3

    .line 360
    instance-of v4, v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v4, :cond_2

    move-object v4, v3

    check-cast v4, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v4

    if-eqz v4, :cond_2

    goto :goto_2

    .line 363
    :cond_2
    invoke-virtual {p0, v3, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p3

    invoke-static {v0, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p3

    goto :goto_3

    :cond_3
    :goto_2
    if-eqz p3, :cond_4

    .line 361
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p3

    invoke-static {p3}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object p3

    goto :goto_3

    :cond_4
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p3

    .line 365
    :goto_3
    invoke-static {p2, p1, v2, p3}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignFieldWithDefault(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method private createConstructorStatementCollection(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 5

    const-string v0, "this"

    .line 329
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    .line 330
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 331
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 333
    instance-of v3, v2, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v3, :cond_0

    move-object v3, v2

    check-cast v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_0

    .line 336
    :cond_0
    invoke-virtual {p0, v2, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    goto :goto_1

    .line 334
    :cond_1
    :goto_0
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    .line 338
    :goto_1
    invoke-static {p1, p2}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 339
    sget-object v3, Lgroovy/transform/options/ImmutablePropertyHandler;->CLONEABLE_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 340
    invoke-static {p1, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isInstanceOfX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v3

    .line 341
    invoke-static {p1, v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneArrayOrCloneableExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    invoke-virtual {p0, v4, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    invoke-static {v0, v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v4

    .line 342
    invoke-virtual {p0, p1, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    .line 339
    invoke-static {v3, v4, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v0

    .line 343
    invoke-static {p2, v2, p1, v0}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignWithDefault(ZLorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method private static createConstructorStatementDate(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 4

    const-string v0, "this"

    .line 423
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    if-eqz p1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    .line 424
    :goto_0
    invoke-static {p0, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 425
    invoke-static {v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneDateExpr(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    .line 427
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v1

    if-eqz p2, :cond_1

    .line 428
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object v3

    goto :goto_1

    :cond_1
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v3

    .line 426
    :goto_1
    invoke-static {v1, v3, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v1

    .line 431
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 432
    instance-of v3, v2, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v3, :cond_2

    move-object v3, v2

    check-cast v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v3

    if-eqz v3, :cond_2

    goto :goto_2

    .line 435
    :cond_2
    invoke-static {v2}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneDateExpr(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    invoke-static {v0, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    goto :goto_3

    :cond_3
    :goto_2
    if-eqz p2, :cond_4

    .line 433
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object p2

    goto :goto_3

    :cond_4
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    .line 437
    :goto_3
    invoke-static {p1, p0, v1, p2}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignFieldWithDefault(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private static createConstructorStatementDate(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 3

    const-string v0, "this"

    .line 409
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    .line 410
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    if-eqz v1, :cond_1

    .line 412
    instance-of v2, v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v2, :cond_0

    move-object v2, v1

    check-cast v2, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    .line 415
    :cond_0
    invoke-static {v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneDateExpr(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    goto :goto_1

    .line 413
    :cond_1
    :goto_0
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    .line 417
    :goto_1
    invoke-static {p0, p1}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    .line 418
    invoke-static {p0}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneDateExpr(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    .line 419
    invoke-static {p1, v1, p0, v0}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignWithDefault(ZLorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private static createConstructorStatementDefault(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 5

    .line 247
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const-string v1, "this"

    .line 248
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v1

    if-eqz p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    .line 249
    :goto_0
    invoke-static {p0, v2}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 250
    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->castX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/CastExpression;

    move-result-object v3

    invoke-static {v1, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v3

    if-eqz p2, :cond_1

    .line 252
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v2

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object v4

    invoke-static {v2, v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v3

    .line 254
    :cond_1
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 256
    instance-of v4, v2, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v4, :cond_2

    move-object v4, v2

    check-cast v4, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v4

    if-eqz v4, :cond_2

    goto :goto_1

    .line 263
    :cond_2
    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    goto :goto_2

    .line 257
    :cond_3
    :goto_1
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->isPrimitiveType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 258
    sget-object p2, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    goto :goto_2

    :cond_4
    if-eqz p2, :cond_5

    .line 260
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object p2

    goto :goto_2

    :cond_5
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    .line 265
    :goto_2
    invoke-static {p1, p0, v3, p2}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignFieldWithDefault(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private static createConstructorStatementDefault(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 4

    .line 227
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    const-string v1, "this"

    .line 228
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v1

    .line 229
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 231
    instance-of v3, v2, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v3, :cond_0

    move-object v3, v2

    check-cast v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_0

    .line 238
    :cond_0
    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    goto :goto_1

    .line 232
    :cond_1
    :goto_0
    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->isPrimitiveType(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 233
    sget-object v2, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    goto :goto_1

    .line 235
    :cond_2
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    :goto_1
    const/4 v3, 0x0

    .line 240
    invoke-virtual {p0, v3}, Lorg/codehaus/groovy/ast/FieldNode;->setInitialValueExpression(Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 241
    invoke-static {p0, p1}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    .line 242
    invoke-static {v0, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->castX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/CastExpression;

    move-result-object v0

    invoke-static {v1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    .line 243
    invoke-static {p1, v2, p0, v0}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignWithDefault(ZLorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private static createConstructorStatementGuarded(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;ZLjava/util/List;Ljava/util/List;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            "Z",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Lorg/codehaus/groovy/ast/stmt/Statement;"
        }
    .end annotation

    const-string p0, "this"

    .line 290
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-static {p0, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object p0

    .line 291
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 293
    instance-of v1, v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v1, :cond_0

    move-object v1, v0

    check-cast v1, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    .line 296
    :cond_0
    invoke-static {p1, v0, p3, p4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createCheckImmutable(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;Ljava/util/List;Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-static {p0, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    goto :goto_1

    .line 294
    :cond_1
    :goto_0
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    .line 298
    :goto_1
    invoke-static {p1, p2}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 299
    invoke-static {p1, v1, p3, p4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createCheckImmutable(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;Ljava/util/List;Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    .line 300
    invoke-static {p2, v0, v1, p0}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignWithDefault(ZLorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private static createConstructorStatementGuarded(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Ljava/util/List;Ljava/util/List;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            "Lorg/codehaus/groovy/ast/Parameter;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;Z)",
            "Lorg/codehaus/groovy/ast/stmt/Statement;"
        }
    .end annotation

    const-string v0, "this"

    .line 304
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v0

    if-eqz p1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    .line 305
    :goto_0
    invoke-static {p0, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 306
    invoke-static {p0, v1, p2, p3}, Lgroovy/transform/options/ImmutablePropertyHandler;->createCheckImmutable(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;Ljava/util/List;Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    .line 308
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v1

    if-eqz p4, :cond_1

    .line 309
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object v3

    goto :goto_1

    :cond_1
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v3

    .line 307
    :goto_1
    invoke-static {v1, v3, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v1

    .line 311
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_3

    .line 313
    instance-of v3, v2, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v3, :cond_2

    move-object v3, v2

    check-cast v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v3

    if-eqz v3, :cond_2

    goto :goto_2

    .line 316
    :cond_2
    invoke-static {p0, v2, p2, p3}, Lgroovy/transform/options/ImmutablePropertyHandler;->createCheckImmutable(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/expr/Expression;Ljava/util/List;Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    invoke-static {v0, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    goto :goto_3

    :cond_3
    :goto_2
    if-eqz p4, :cond_4

    .line 314
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->makeThrowStmt(Ljava/lang/String;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object p2

    goto :goto_3

    :cond_4
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignNullS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    .line 318
    :goto_3
    invoke-static {p1, p0, v1, p2}, Lgroovy/transform/options/ImmutablePropertyHandler;->assignFieldWithDefault(Lorg/codehaus/groovy/ast/Parameter;Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private createGetterBodyArrayOrCloneable(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 1

    .line 145
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    .line 146
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {v0, p1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneArrayOrCloneableExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    .line 147
    invoke-static {v0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->safeExpression(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method private createGetterBodyDate(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 1

    .line 151
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    .line 152
    invoke-static {p1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->cloneDateExpr(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 153
    invoke-static {p1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->safeExpression(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method private static createGetterBodyDefault(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0

    .line 140
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    .line 141
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    return-object p0
.end method

.method private createIfInstanceOfAsImmutableS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 170
    invoke-static {p1, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isInstanceOfX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v0

    invoke-virtual {p0, p1, p2}, Lgroovy/transform/options/ImmutablePropertyHandler;->createAsImmutableX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {v0, p1, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ternaryX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TernaryExpression;

    move-result-object p1

    return-object p1
.end method

.method private static getParam(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 0

    if-eqz p1, :cond_0

    .line 404
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->findArg(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p1, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p0

    :goto_0
    return-object p0
.end method

.method private static isKnownImmutable(Ljava/lang/String;Ljava/util/List;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)Z"
        }
    .end annotation

    .line 441
    invoke-interface {p1, p0}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method


# virtual methods
.method protected checkFinalArgNotOverridden(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 4

    .line 445
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p2

    .line 446
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->findArg(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 448
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->notX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/NotExpression;

    move-result-object v0

    sget-object v1, Lgroovy/transform/options/ImmutablePropertyHandler;->READONLYEXCEPTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x2

    new-array v2, v2, [Lorg/codehaus/groovy/ast/expr/Expression;

    .line 450
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p2

    const/4 v3, 0x0

    aput-object p2, v2, v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    const/4 p2, 0x1

    aput-object p1, v2, p2

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    .line 449
    invoke-static {v1, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->throwS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/ThrowStatement;

    move-result-object p1

    .line 447
    invoke-static {v0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    return-object p1
.end method

.method protected cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 6

    .line 157
    sget-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->SORTEDSET_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v1, Lgroovy/transform/options/ImmutablePropertyHandler;->SORTEDMAP_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v2, Lgroovy/transform/options/ImmutablePropertyHandler;->SET_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v3, Lgroovy/transform/options/ImmutablePropertyHandler;->MAP_CLASSNODE:Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v4, Lorg/codehaus/groovy/ast/ClassHelper;->LIST_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    sget-object v5, Lgroovy/transform/options/ImmutablePropertyHandler;->COLLECTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    .line 162
    invoke-virtual {p0, p1, v5}, Lgroovy/transform/options/ImmutablePropertyHandler;->createAsImmutableX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    .line 161
    invoke-direct {p0, p1, v4, v5}, Lgroovy/transform/options/ImmutablePropertyHandler;->createIfInstanceOfAsImmutableS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v4

    .line 160
    invoke-direct {p0, p1, v3, v4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createIfInstanceOfAsImmutableS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    .line 159
    invoke-direct {p0, p1, v2, v3}, Lgroovy/transform/options/ImmutablePropertyHandler;->createIfInstanceOfAsImmutableS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    .line 158
    invoke-direct {p0, p1, v1, v2}, Lgroovy/transform/options/ImmutablePropertyHandler;->createIfInstanceOfAsImmutableS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    .line 157
    invoke-direct {p0, p1, v0, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->createIfInstanceOfAsImmutableS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->castX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/CastExpression;

    move-result-object p1

    return-object p1
.end method

.method protected createAsImmutableX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 1

    .line 174
    sget-object v0, Lgroovy/transform/options/ImmutablePropertyHandler;->DGM_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->castX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/CastExpression;

    move-result-object p1

    const-string p2, "asImmutable"

    invoke-static {v0, p2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p1

    return-object p1
.end method

.method protected createConstructorStatement(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 6

    .line 202
    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->getKnownImmutableClasses(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v0

    .line 203
    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->getKnownImmutables(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v1

    .line 204
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v2

    .line 205
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    .line 207
    invoke-static {p2}, Lorg/codehaus/groovy/transform/NullCheckASTTransformation;->hasIncludeGenerated(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v4

    .line 208
    invoke-static {v3, v0}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->isKnownImmutableType(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Z

    move-result v5

    if-nez v5, :cond_7

    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object p3

    invoke-static {p3, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->isKnownImmutable(Ljava/lang/String;Ljava/util/List;)Z

    move-result p3

    if-eqz p3, :cond_0

    goto :goto_2

    .line 210
    :cond_0
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->isArray()Z

    move-result p3

    if-nez p3, :cond_6

    invoke-static {v3}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->implementsCloneable(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-eqz p3, :cond_1

    goto :goto_1

    .line 212
    :cond_1
    invoke-static {v3}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->derivesFromDate(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-eqz p3, :cond_2

    .line 213
    invoke-static {v2, p4, v4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementDate(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Z)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_3

    .line 214
    :cond_2
    sget-object p3, Lgroovy/transform/options/ImmutablePropertyHandler;->COLLECTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v3, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isOrImplements(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v5

    if-nez v5, :cond_5

    invoke-virtual {v3, p3}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-nez p3, :cond_5

    sget-object p3, Lgroovy/transform/options/ImmutablePropertyHandler;->MAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v3, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isOrImplements(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v5

    if-nez v5, :cond_5

    invoke-virtual {v3, p3}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-eqz p3, :cond_3

    goto :goto_0

    .line 216
    :cond_3
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->isResolved()Z

    move-result p3

    if-eqz p3, :cond_4

    .line 217
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p4

    const-string v0, "compiling"

    invoke-static {p2, p3, p4, v0}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->createErrorMessage(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2, v2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 218
    sget-object p1, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    goto :goto_3

    .line 220
    :cond_4
    invoke-static {v2, p4, v1, v0, v4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementGuarded(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Ljava/util/List;Ljava/util/List;Z)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_3

    .line 215
    :cond_5
    :goto_0
    invoke-direct {p0, v2, p4, v4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementCollection(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Z)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_3

    .line 211
    :cond_6
    :goto_1
    invoke-static {v2, p4, v4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementArrayOrCloneable(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Z)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_3

    .line 209
    :cond_7
    :goto_2
    invoke-static {v2, p4, v4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementDefault(Lorg/codehaus/groovy/ast/FieldNode;Lorg/codehaus/groovy/ast/Parameter;Z)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    :goto_3
    return-object p1
.end method

.method protected createConstructorStatement(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Z)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 5
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 179
    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->getKnownImmutableClasses(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v0

    .line 180
    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->getKnownImmutables(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v1

    .line 181
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v2

    .line 182
    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    .line 184
    invoke-static {v3, v0}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->isKnownImmutableType(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Z

    move-result v4

    if-nez v4, :cond_7

    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/PropertyNode;->getName()Ljava/lang/String;

    move-result-object p3

    invoke-static {p3, v1}, Lgroovy/transform/options/ImmutablePropertyHandler;->isKnownImmutable(Ljava/lang/String;Ljava/util/List;)Z

    move-result p3

    if-eqz p3, :cond_0

    goto :goto_2

    .line 186
    :cond_0
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->isArray()Z

    move-result p3

    if-nez p3, :cond_6

    invoke-static {v3}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->implementsCloneable(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-eqz p3, :cond_1

    goto :goto_1

    .line 188
    :cond_1
    invoke-static {v3}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->derivesFromDate(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-eqz p3, :cond_2

    .line 189
    invoke-static {v2, p4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementDate(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_3

    .line 190
    :cond_2
    sget-object p3, Lgroovy/transform/options/ImmutablePropertyHandler;->COLLECTION_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v3, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isOrImplements(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v4

    if-nez v4, :cond_5

    invoke-virtual {v3, p3}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-nez p3, :cond_5

    sget-object p3, Lgroovy/transform/options/ImmutablePropertyHandler;->MAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {v3, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isOrImplements(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v4

    if-nez v4, :cond_5

    invoke-virtual {v3, p3}, Lorg/codehaus/groovy/ast/ClassNode;->isDerivedFrom(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result p3

    if-eqz p3, :cond_3

    goto :goto_0

    .line 192
    :cond_3
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->isResolved()Z

    move-result p3

    if-eqz p3, :cond_4

    .line 193
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v2}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p4

    const-string v0, "compiling"

    invoke-static {p2, p3, p4, v0}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->createErrorMessage(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2, v2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 194
    sget-object p1, Lorg/codehaus/groovy/ast/stmt/EmptyStatement;->INSTANCE:Lorg/codehaus/groovy/ast/stmt/EmptyStatement;

    goto :goto_3

    .line 196
    :cond_4
    invoke-static {p2, v2, p4, v1, v0}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementGuarded(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;ZLjava/util/List;Ljava/util/List;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_3

    .line 191
    :cond_5
    :goto_0
    invoke-direct {p0, v2, p4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementCollection(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_3

    .line 187
    :cond_6
    :goto_1
    invoke-static {v2, p4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementArrayOrCloneable(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_3

    .line 185
    :cond_7
    :goto_2
    invoke-static {v2, p4}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatementDefault(Lorg/codehaus/groovy/ast/FieldNode;Z)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    :goto_3
    return-object p1
.end method

.method public createPropGetter(Lorg/codehaus/groovy/ast/PropertyNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 3

    .line 94
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p1

    .line 95
    new-instance v0, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 96
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 98
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/ClassNode;->isArray()Z

    move-result v2

    if-nez v2, :cond_2

    invoke-static {v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->implementsCloneable(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    .line 100
    :cond_0
    invoke-static {v1}, Lorg/apache/groovy/ast/tools/ImmutablePropertyUtils;->derivesFromDate(Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 101
    invoke-direct {p0, p1}, Lgroovy/transform/options/ImmutablePropertyHandler;->createGetterBodyDate(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_1

    .line 103
    :cond_1
    invoke-static {p1}, Lgroovy/transform/options/ImmutablePropertyHandler;->createGetterBodyDefault(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    goto :goto_1

    .line 99
    :cond_2
    :goto_0
    invoke-direct {p0, p1}, Lgroovy/transform/options/ImmutablePropertyHandler;->createGetterBodyArrayOrCloneable(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    .line 105
    :goto_1
    invoke-virtual {v0, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object v0
.end method

.method public createPropInit(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 1

    .line 131
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p2

    .line 132
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 133
    :cond_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 134
    invoke-virtual {p0, p3, p2}, Lgroovy/transform/options/ImmutablePropertyHandler;->checkFinalArgNotOverridden(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1

    .line 136
    :cond_1
    invoke-virtual {p0, p1, p3, p4, p5}, Lgroovy/transform/options/ImmutablePropertyHandler;->createConstructorStatement(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method public createPropSetter(Lorg/codehaus/groovy/ast/PropertyNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public validateAttributes(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;)Z
    .locals 1

    const-string v0, "useSuper"

    .line 116
    invoke-virtual {p0, p1, p2, v0}, Lgroovy/transform/options/ImmutablePropertyHandler;->isValidAttribute(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public validateProperties(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/transform/AbstractASTTransformation;",
            "Lorg/codehaus/groovy/ast/stmt/BlockStatement;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/PropertyNode;",
            ">;)Z"
        }
    .end annotation

    .line 122
    instance-of v0, p1, Lorg/codehaus/groovy/transform/MapConstructorASTTransformation;

    if-eqz v0, :cond_0

    const-string v0, "args"

    .line 123
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v1

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    new-instance v3, Lorg/codehaus/groovy/ast/expr/MapExpression;

    invoke-direct {v3}, Lorg/codehaus/groovy/ast/expr/MapExpression;-><init>()V

    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    invoke-static {v1, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v1

    invoke-virtual {p2, v1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 124
    sget-object v1, Lgroovy/transform/options/ImmutablePropertyHandler;->SELF_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const-string v2, "this"

    filled-new-array {v2, v0}, [Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    const-string v2, "checkPropNames"

    invoke-static {v1, v2, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 126
    :cond_0
    invoke-super {p0, p1, p2, p3, p4}, Lgroovy/transform/options/PropertyHandler;->validateProperties(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Z

    move-result p1

    return p1
.end method
