.class public final enum Lgroovy/transform/options/Visibility;
.super Ljava/lang/Enum;
.source "Visibility.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovy/transform/options/Visibility;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovy/transform/options/Visibility;

.field public static final enum PACKAGE_PRIVATE:Lgroovy/transform/options/Visibility;

.field public static final enum PRIVATE:Lgroovy/transform/options/Visibility;

.field public static final enum PROTECTED:Lgroovy/transform/options/Visibility;

.field public static final enum PUBLIC:Lgroovy/transform/options/Visibility;

.field public static final enum UNDEFINED:Lgroovy/transform/options/Visibility;


# instance fields
.field private final modifier:I


# direct methods
.method private static synthetic $values()[Lgroovy/transform/options/Visibility;
    .locals 3

    const/4 v0, 0x5

    new-array v0, v0, [Lgroovy/transform/options/Visibility;

    .line 28
    sget-object v1, Lgroovy/transform/options/Visibility;->PUBLIC:Lgroovy/transform/options/Visibility;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/transform/options/Visibility;->PROTECTED:Lgroovy/transform/options/Visibility;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/transform/options/Visibility;->PACKAGE_PRIVATE:Lgroovy/transform/options/Visibility;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/transform/options/Visibility;->PRIVATE:Lgroovy/transform/options/Visibility;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/transform/options/Visibility;->UNDEFINED:Lgroovy/transform/options/Visibility;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 5

    .line 29
    new-instance v0, Lgroovy/transform/options/Visibility;

    const-string v1, "PUBLIC"

    const/4 v2, 0x0

    const/4 v3, 0x1

    invoke-direct {v0, v1, v2, v3}, Lgroovy/transform/options/Visibility;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lgroovy/transform/options/Visibility;->PUBLIC:Lgroovy/transform/options/Visibility;

    .line 30
    new-instance v0, Lgroovy/transform/options/Visibility;

    const-string v1, "PROTECTED"

    const/4 v4, 0x4

    invoke-direct {v0, v1, v3, v4}, Lgroovy/transform/options/Visibility;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lgroovy/transform/options/Visibility;->PROTECTED:Lgroovy/transform/options/Visibility;

    .line 31
    new-instance v0, Lgroovy/transform/options/Visibility;

    const-string v1, "PACKAGE_PRIVATE"

    const/4 v3, 0x2

    invoke-direct {v0, v1, v3, v2}, Lgroovy/transform/options/Visibility;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lgroovy/transform/options/Visibility;->PACKAGE_PRIVATE:Lgroovy/transform/options/Visibility;

    .line 32
    new-instance v0, Lgroovy/transform/options/Visibility;

    const-string v1, "PRIVATE"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2, v3}, Lgroovy/transform/options/Visibility;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lgroovy/transform/options/Visibility;->PRIVATE:Lgroovy/transform/options/Visibility;

    .line 33
    new-instance v0, Lgroovy/transform/options/Visibility;

    const-string v1, "UNDEFINED"

    const/4 v2, -0x1

    invoke-direct {v0, v1, v4, v2}, Lgroovy/transform/options/Visibility;-><init>(Ljava/lang/String;II)V

    sput-object v0, Lgroovy/transform/options/Visibility;->UNDEFINED:Lgroovy/transform/options/Visibility;

    .line 28
    invoke-static {}, Lgroovy/transform/options/Visibility;->$values()[Lgroovy/transform/options/Visibility;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/Visibility;->$VALUES:[Lgroovy/transform/options/Visibility;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 37
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 38
    iput p3, p0, Lgroovy/transform/options/Visibility;->modifier:I

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovy/transform/options/Visibility;
    .locals 1

    .line 28
    const-class v0, Lgroovy/transform/options/Visibility;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovy/transform/options/Visibility;

    return-object p0
.end method

.method public static values()[Lgroovy/transform/options/Visibility;
    .locals 1

    .line 28
    sget-object v0, Lgroovy/transform/options/Visibility;->$VALUES:[Lgroovy/transform/options/Visibility;

    invoke-virtual {v0}, [Lgroovy/transform/options/Visibility;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovy/transform/options/Visibility;

    return-object v0
.end method


# virtual methods
.method public getModifier()I
    .locals 2

    .line 42
    iget v0, p0, Lgroovy/transform/options/Visibility;->modifier:I

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    return v0

    .line 43
    :cond_0
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "getModifier() not supported for UNDEFINED"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
