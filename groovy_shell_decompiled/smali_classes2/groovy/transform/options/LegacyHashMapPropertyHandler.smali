.class public Lgroovy/transform/options/LegacyHashMapPropertyHandler;
.super Lgroovy/transform/options/ImmutablePropertyHandler;
.source "LegacyHashMapPropertyHandler.java"


# static fields
.field private static final HMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 61
    const-class v0, Ljava/util/HashMap;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/Class;Z)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/options/LegacyHashMapPropertyHandler;->HMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 60
    invoke-direct {p0}, Lgroovy/transform/options/ImmutablePropertyHandler;-><init>()V

    return-void
.end method

.method private createLegacyConstructorStatementMapSpecial(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 8

    .line 64
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    .line 65
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/expr/Expression;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    .line 66
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialValueExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 68
    instance-of v3, v2, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    if-eqz v3, :cond_0

    move-object v3, v2

    check-cast v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->isNullExpression()Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_0

    .line 71
    :cond_0
    invoke-virtual {p0, v2, v1}, Lgroovy/transform/options/LegacyHashMapPropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v2

    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    goto :goto_1

    .line 69
    :cond_1
    :goto_0
    sget-object v2, Lorg/codehaus/groovy/ast/expr/ConstantExpression;->EMPTY_EXPRESSION:Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    invoke-static {v0, v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v2

    .line 73
    :goto_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->findArg(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    const-string v4, "args"

    .line 74
    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v4

    .line 76
    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v5

    .line 78
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    const-string v6, "containsKey"

    invoke-static {v4, v6, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isTrueX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object p1

    .line 79
    invoke-static {v0, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v6

    .line 80
    invoke-virtual {p0, v4, v1}, Lgroovy/transform/options/LegacyHashMapPropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v7

    invoke-static {v0, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v7

    .line 77
    invoke-static {p1, v6, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    const-string v6, "size"

    .line 82
    invoke-static {v4, v6}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->isOneX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v6

    .line 83
    invoke-virtual {p0, v3, v1}, Lgroovy/transform/options/LegacyHashMapPropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v3

    invoke-static {v0, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v3

    .line 84
    invoke-virtual {p0, v4, v1}, Lgroovy/transform/options/LegacyHashMapPropertyHandler;->cloneCollectionExpr(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    .line 81
    invoke-static {v6, v3, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object v0

    .line 75
    invoke-static {v5, p1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    .line 86
    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->equalsNullX(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/BooleanExpression;

    move-result-object v0

    invoke-static {v0, v2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ifElseS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/stmt/Statement;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/IfStatement;

    move-result-object p1

    return-object p1
.end method


# virtual methods
.method public createPropInit(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/PropertyNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/stmt/Statement;
    .locals 0

    .line 105
    invoke-virtual {p4}, Lorg/codehaus/groovy/ast/PropertyNode;->getField()Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p1

    .line 106
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result p2

    if-eqz p2, :cond_0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->isStatic()Z

    move-result p2

    if-eqz p2, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 107
    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result p2

    if-eqz p2, :cond_1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    if-eqz p2, :cond_1

    .line 108
    invoke-virtual {p0, p3, p1}, Lgroovy/transform/options/LegacyHashMapPropertyHandler;->checkFinalArgNotOverridden(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1

    .line 110
    :cond_1
    invoke-direct {p0, p1}, Lgroovy/transform/options/LegacyHashMapPropertyHandler;->createLegacyConstructorStatementMapSpecial(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    return-object p1
.end method

.method public validateAttributes(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;)Z
    .locals 1

    .line 91
    instance-of v0, p1, Lorg/codehaus/groovy/transform/TupleConstructorASTTransformation;

    if-nez v0, :cond_0

    invoke-super {p0, p1, p2}, Lgroovy/transform/options/ImmutablePropertyHandler;->validateAttributes(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public validateProperties(Lorg/codehaus/groovy/transform/AbstractASTTransformation;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Z
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/transform/AbstractASTTransformation;",
            "Lorg/codehaus/groovy/ast/stmt/BlockStatement;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/PropertyNode;",
            ">;)Z"
        }
    .end annotation

    .line 96
    invoke-interface {p4}, Ljava/util/List;->size()I

    move-result p2

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-ne p2, v1, :cond_1

    invoke-interface {p4, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/ast/PropertyNode;

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/PropertyNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    sget-object v2, Lgroovy/transform/options/LegacyHashMapPropertyHandler;->HMAP_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p2, v2}, Lorg/codehaus/groovy/ast/ClassNode;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    return v1

    .line 97
    :cond_1
    :goto_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Error during "

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getAnnotationName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v2, " processing. Property handler "

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v2, " only accepts a single HashMap property"

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p4}, Ljava/util/List;->size()I

    move-result v2

    if-ne v2, v1, :cond_2

    invoke-interface {p4, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lorg/codehaus/groovy/ast/ASTNode;

    :cond_2
    invoke-virtual {p1, p2, p3}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return v0
.end method
