.class public interface abstract annotation Lgroovy/transform/ImmutableOptions;
.super Ljava/lang/Object;
.source "ImmutableOptions.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/ImmutableOptions;
        knownImmutableClasses = {}
        knownImmutables = {}
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;
    }
.end annotation


# virtual methods
.method public abstract knownImmutableClasses()[Ljava/lang/Class;
.end method

.method public abstract knownImmutables()[Ljava/lang/String;
.end method
