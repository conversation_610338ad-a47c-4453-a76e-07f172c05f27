.class public interface abstract annotation Lgroovy/transform/VisibilityOptions;
.super Ljava/lang/Object;
.source "VisibilityOptions.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/VisibilityOptions;
        constructor = .enum Lgroovy/transform/options/Visibility;->UNDEFINED:Lgroovy/transform/options/Visibility;
        id = "<DummyUndefinedMarkerString-DoNotUse>"
        method = .enum Lgroovy/transform/options/Visibility;->UNDEFINED:Lgroovy/transform/options/Visibility;
        type = .enum Lgroovy/transform/options/Visibility;->UNDEFINED:Lgroovy/transform/options/Visibility;
        value = .enum Lgroovy/transform/options/Visibility;->UNDEFINED:Lgroovy/transform/options/Visibility;
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->CONSTRUCTOR:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->METHOD:Ljava/lang/annotation/ElementType;
    }
.end annotation


# virtual methods
.method public abstract constructor()Lgroovy/transform/options/Visibility;
.end method

.method public abstract id()Ljava/lang/String;
.end method

.method public abstract method()Lgroovy/transform/options/Visibility;
.end method

.method public abstract type()Lgroovy/transform/options/Visibility;
.end method

.method public abstract value()Lgroovy/transform/options/Visibility;
.end method
