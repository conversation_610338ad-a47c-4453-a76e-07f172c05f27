.class public interface abstract annotation Lgroovy/transform/ExternalizeMethods;
.super Ljava/lang/Object;
.source "ExternalizeMethods.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/ExternalizeMethods;
        excludes = {}
        includeFields = false
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformationClass;
    value = {
        "org.codehaus.groovy.transform.ExternalizeMethodsASTTransformation"
    }
.end annotation


# virtual methods
.method public abstract excludes()[Ljava/lang/String;
.end method

.method public abstract includeFields()Z
.end method
