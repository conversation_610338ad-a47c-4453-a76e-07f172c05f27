.class public Lgroovy/transform/builder/ExternalStrategy;
.super Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;
.source "ExternalStrategy.java"


# static fields
.field private static final DEFAULT_INITIAL_VALUE:Lorg/codehaus/groovy/ast/expr/Expression;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 91
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;-><init>()V

    return-void
.end method

.method private static createBuildMethod(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/transform/BuilderASTTransformation;",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;",
            ">;)",
            "Lorg/codehaus/groovy/ast/MethodNode;"
        }
    .end annotation

    const-string p0, "buildMethodName"

    const-string v0, "build"

    .line 131
    invoke-static {p1, p0, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 132
    new-instance v7, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v7}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 133
    invoke-static {p2, p3, v7}, Lgroovy/transform/builder/ExternalStrategy;->initializeInstance(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/BlockStatement;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    .line 134
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {v7, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 135
    new-instance p0, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v5, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_PARAMS:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v6, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v3, 0x1

    move-object v1, p0

    move-object v4, p2

    invoke-direct/range {v1 .. v7}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p0
.end method

.method private createBuilderMethodForField(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;Ljava/lang/String;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 11

    .line 139
    invoke-virtual {p2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "class"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string v0, "clazz"

    goto :goto_0

    :cond_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v0

    .line 140
    :goto_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, p3, v1}, Lgroovy/transform/builder/ExternalStrategy;->getSetterName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    .line 141
    new-instance p3, Lorg/codehaus/groovy/ast/MethodNode;

    const/4 v4, 0x1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    const/4 v1, 0x1

    new-array v2, v1, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual {p2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    invoke-static {p2, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p2

    const/4 v6, 0x0

    aput-object p2, v2, v6

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p2

    sget-object v7, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x2

    new-array v2, v2, [Lorg/codehaus/groovy/ast/stmt/Statement;

    const-string v8, "this"

    .line 142
    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v9

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v10

    invoke-static {v9, v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v9

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-static {v9, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    aput-object v0, v2, v6

    .line 143
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {v8, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    aput-object p1, v2, v1

    .line 141
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v8

    move-object v2, p3

    move-object v6, p2

    invoke-direct/range {v2 .. v8}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p3
.end method

.method private static createFieldCopy(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 8

    .line 148
    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v0

    .line 149
    new-instance v7, Lorg/codehaus/groovy/ast/FieldNode;

    const-string v1, "class"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    const-string v0, "clazz"

    :cond_0
    move-object v2, v0

    const/4 v3, 0x2

    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    sget-object v6, Lgroovy/transform/builder/ExternalStrategy;->DEFAULT_INITIAL_VALUE:Lorg/codehaus/groovy/ast/expr/Expression;

    move-object v1, v7

    move-object v5, p0

    invoke-direct/range {v1 .. v6}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object v7
.end method

.method private static initializeInstance(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/BlockStatement;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;",
            ">;",
            "Lorg/codehaus/groovy/ast/stmt/BlockStatement;",
            ")",
            "Lorg/codehaus/groovy/ast/expr/Expression;"
        }
    .end annotation

    .line 153
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "_the"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    .line 154
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p0

    invoke-static {v0, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {p2, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 155
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result p1

    if-eqz p1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;

    .line 156
    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v2

    const-string v3, "class"

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    const-string v2, "clazz"

    goto :goto_1

    :cond_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v2

    :goto_1
    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {v2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    invoke-static {v1, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    :cond_1
    return-object v0
.end method


# virtual methods
.method public build(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V
    .locals 12

    .line 95
    instance-of v0, p2, Lorg/codehaus/groovy/ast/ClassNode;

    const-string v1, "Error during "

    if-nez v0, :cond_0

    .line 96
    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    sget-object v0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string v0, " processing: building for "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    .line 97
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string v0, " not supported by "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    .line 96
    invoke-virtual {p1, p3, p2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 100
    :cond_0
    check-cast p2, Lorg/codehaus/groovy/ast/ClassNode;

    const-string v0, "prefix"

    const-string v2, ""

    .line 101
    invoke-static {p3, v0, v2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v2, "forClass"

    .line 102
    invoke-virtual {p1, p3, v2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->getMemberClassValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    if-nez v2, :cond_1

    .line 104
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    sget-object v0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " processing: \'forClass\' must be specified for "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2, p3}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 107
    :cond_1
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 108
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    const-string v3, "<DummyUndefinedMarkerString-DoNotUse>"

    .line 109
    invoke-interface {v9, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-object v3, p0

    move-object v4, p1

    move-object v5, p3

    move-object v6, v2

    move-object v7, v1

    move-object v8, v9

    .line 110
    invoke-virtual/range {v3 .. v8}, Lgroovy/transform/builder/ExternalStrategy;->getIncludeExclude(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/util/List;)Z

    move-result v3

    if-nez v3, :cond_2

    return-void

    .line 111
    :cond_2
    invoke-interface {v9}, Ljava/util/List;->size()I

    move-result v3

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-ne v3, v5, :cond_3

    invoke-interface {v9, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-static {v3}, Lgroovy/transform/Undefined;->isUndefined(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_3

    const/4 v9, 0x0

    :cond_3
    move-object v11, v9

    const-string v3, "builderClassName"

    .line 112
    invoke-virtual {p0, p1, p3, v3}, Lgroovy/transform/builder/ExternalStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_4

    return-void

    :cond_4
    const-string v3, "builderMethodName"

    .line 113
    invoke-virtual {p0, p1, p3, v3}, Lgroovy/transform/builder/ExternalStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_5

    return-void

    :cond_5
    const-string v3, "force"

    .line 114
    invoke-virtual {p0, p1, p3, v3}, Lgroovy/transform/builder/ExternalStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_6

    return-void

    .line 115
    :cond_6
    invoke-static {v5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    const-string v6, "allNames"

    invoke-virtual {p1, p3, v6, v3}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v9

    .line 116
    invoke-static {v4}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    const-string v4, "allProperties"

    invoke-virtual {p1, p3, v4, v3}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v3

    xor-int/lit8 v10, v3, 0x1

    move-object v3, p0

    move-object v4, p1

    move-object v5, p3

    move-object v6, v2

    move-object v7, v1

    move-object v8, v11

    .line 117
    invoke-virtual/range {v3 .. v10}, Lgroovy/transform/builder/ExternalStrategy;->getPropertyInfos(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/util/List;ZZ)Ljava/util/List;

    move-result-object v1

    if-eqz v11, :cond_7

    .line 119
    invoke-interface {v11}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_7

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    .line 120
    invoke-virtual {p0, p1, p3, v4, v1}, Lgroovy/transform/builder/ExternalStrategy;->checkKnownProperty(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/util/List;)V

    goto :goto_0

    .line 123
    :cond_7
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_8

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;

    .line 124
    invoke-static {p2, v4}, Lgroovy/transform/builder/ExternalStrategy;->createFieldCopy(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v5

    invoke-virtual {p2, v5}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 125
    invoke-direct {p0, p2, v4, v0}, Lgroovy/transform/builder/ExternalStrategy;->createBuilderMethodForField(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;Ljava/lang/String;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v4

    invoke-static {p2, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    goto :goto_1

    .line 127
    :cond_8
    invoke-static {p1, p3, v2, v1}, Lgroovy/transform/builder/ExternalStrategy;->createBuildMethod(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    invoke-static {p2, p1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method
