.class public Lgroovy/transform/builder/SimpleStrategy;
.super Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;
.source "SimpleStrategy.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 82
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;-><init>()V

    return-void
.end method


# virtual methods
.method public build(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V
    .locals 20

    move-object/from16 v6, p0

    move-object/from16 v7, p1

    move-object/from16 v0, p2

    move-object/from16 v8, p3

    .line 84
    instance-of v1, v0, Lorg/codehaus/groovy/ast/ClassNode;

    if-nez v1, :cond_0

    .line 85
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Error during "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    sget-object v2, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " processing: building for "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    .line 86
    invoke-virtual/range {p2 .. p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " not supported by "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual/range {p0 .. p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 85
    invoke-virtual {v7, v1, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    return-void

    .line 89
    :cond_0
    move-object v14, v0

    check-cast v14, Lorg/codehaus/groovy/ast/ClassNode;

    const-string v0, "builderClassName"

    .line 90
    invoke-virtual {v6, v7, v8, v0}, Lgroovy/transform/builder/SimpleStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    const-string v0, "buildMethodName"

    .line 91
    invoke-virtual {v6, v7, v8, v0}, Lgroovy/transform/builder/SimpleStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_2

    return-void

    :cond_2
    const-string v0, "builderMethodName"

    .line 92
    invoke-virtual {v6, v7, v8, v0}, Lgroovy/transform/builder/SimpleStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_3

    return-void

    :cond_3
    const-string v0, "forClass"

    .line 93
    invoke-virtual {v6, v7, v8, v0}, Lgroovy/transform/builder/SimpleStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_4

    return-void

    :cond_4
    const-string v0, "includeSuperProperties"

    .line 94
    invoke-virtual {v6, v7, v8, v0}, Lgroovy/transform/builder/SimpleStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_5

    return-void

    :cond_5
    const-string v0, "allProperties"

    .line 95
    invoke-virtual {v6, v7, v8, v0}, Lgroovy/transform/builder/SimpleStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_6

    return-void

    :cond_6
    const-string v0, "force"

    .line 96
    invoke-virtual {v6, v7, v8, v0}, Lgroovy/transform/builder/SimpleStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_7

    return-void

    :cond_7
    const/4 v15, 0x1

    .line 97
    invoke-static {v15}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    const-string v1, "useSetters"

    invoke-virtual {v7, v8, v1, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v16

    .line 98
    invoke-static {v15}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    const-string v1, "allNames"

    invoke-virtual {v7, v8, v1, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v13

    .line 100
    new-instance v12, Ljava/util/ArrayList;

    invoke-direct {v12}, Ljava/util/ArrayList;-><init>()V

    .line 101
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    const-string v0, "<DummyUndefinedMarkerString-DoNotUse>"

    .line 102
    invoke-interface {v9, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p3

    move-object v3, v14

    move-object v4, v12

    move-object v5, v9

    .line 103
    invoke-virtual/range {v0 .. v5}, Lgroovy/transform/builder/SimpleStrategy;->getIncludeExclude(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/util/List;)Z

    move-result v0

    if-nez v0, :cond_8

    return-void

    .line 104
    :cond_8
    invoke-interface {v9}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x0

    if-ne v0, v15, :cond_9

    invoke-interface {v9, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    invoke-static {v0}, Lgroovy/transform/Undefined;->isUndefined(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_9

    const/4 v9, 0x0

    :cond_9
    move-object v0, v9

    const-string v2, "prefix"

    const-string v3, "set"

    .line 105
    invoke-static {v8, v2, v3}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 106
    invoke-virtual {v6, v7, v8, v14}, Lgroovy/transform/builder/SimpleStrategy;->getFields(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v4

    if-eqz v0, :cond_a

    .line 108
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v5

    :goto_0
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    move-result v9

    if-eqz v9, :cond_a

    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Ljava/lang/String;

    .line 109
    invoke-virtual {v6, v7, v8, v9, v4}, Lgroovy/transform/builder/SimpleStrategy;->checkKnownField(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/util/List;)V

    goto :goto_0

    .line 112
    :cond_a
    invoke-interface {v4}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_1
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_d

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/FieldNode;

    .line 113
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v7

    .line 114
    invoke-static {v7, v12, v0, v13}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->shouldSkipUndefinedAware(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Z)Z

    move-result v8

    if-nez v8, :cond_c

    .line 115
    invoke-virtual {v6, v2, v7}, Lgroovy/transform/builder/SimpleStrategy;->getSetterName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v8

    .line 116
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v9

    invoke-static {v9, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v9

    .line 117
    invoke-static {v14}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v11

    new-array v10, v15, [Lorg/codehaus/groovy/ast/Parameter;

    aput-object v9, v10, v1

    invoke-static {v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v17

    sget-object v18, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v10, 0x2

    new-array v10, v10, [Lorg/codehaus/groovy/ast/stmt/Statement;

    if-eqz v16, :cond_b

    .line 118
    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result v19

    if-nez v19, :cond_b

    .line 119
    invoke-virtual {v6, v3, v7}, Lgroovy/transform/builder/SimpleStrategy;->getSetterName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-static {v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v7

    invoke-static {v5, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v5

    goto :goto_2

    .line 120
    :cond_b
    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->fieldX(Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/expr/FieldExpression;

    move-result-object v5

    invoke-static {v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v7

    invoke-static {v5, v7}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v5

    .line 118
    :goto_2
    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v5

    aput-object v5, v10, v1

    const-string v5, "this"

    .line 122
    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v5

    aput-object v5, v10, v15

    .line 117
    invoke-static {v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v5

    move-object v7, v14

    const/4 v9, 0x1

    move-object v10, v11

    move-object/from16 v11, v17

    move-object/from16 v17, v12

    move-object/from16 v12, v18

    move/from16 v18, v13

    move-object v13, v5

    invoke-static/range {v7 .. v13}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    goto :goto_3

    :cond_c
    move-object/from16 v17, v12

    move/from16 v18, v13

    :goto_3
    move-object/from16 v12, v17

    move/from16 v13, v18

    goto/16 :goto_1

    :cond_d
    return-void
.end method

.method protected getFields(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/transform/BuilderASTTransformation;",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;"
        }
    .end annotation

    .line 130
    invoke-static {p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getInstancePropertyFields(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method
