.class public Lgroovy/transform/builder/DefaultStrategy;
.super Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;
.source "DefaultStrategy.java"


# static fields
.field private static final DEFAULT_INITIAL_VALUE:Lorg/codehaus/groovy/ast/expr/Expression; = null

.field private static final PUBLIC_STATIC:I = 0x9


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 166
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;-><init>()V

    return-void
.end method

.method private static createBuildMethod(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;",
            ">;)",
            "Lorg/codehaus/groovy/ast/MethodNode;"
        }
    .end annotation

    const-string v0, "buildMethodName"

    const-string v1, "build"

    .line 262
    invoke-static {p0, v0, v1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    .line 263
    new-instance v8, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v8}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 264
    invoke-static {p1, p2, v8}, Lgroovy/transform/builder/DefaultStrategy;->initializeInstance(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/BlockStatement;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {v8, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 265
    new-instance p0, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    sget-object v6, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_PARAMS:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v7, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v4, 0x1

    move-object v2, p0

    invoke-direct/range {v2 .. v8}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p0
.end method

.method private static createBuildMethodForMethod(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;[Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 9

    const-string v0, "buildMethodName"

    const-string v1, "build"

    .line 241
    invoke-static {p0, v0, v1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    .line 242
    new-instance v8, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v8}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 244
    instance-of p0, p2, Lorg/codehaus/groovy/ast/ConstructorNode;

    if-eqz p0, :cond_0

    .line 245
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    .line 246
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p2

    invoke-static {p1, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {v8, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    .line 248
    :cond_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args([Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p3

    invoke-static {p0, p1, p3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {v8, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 249
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    :goto_0
    move-object v5, p0

    .line 251
    new-instance p0, Lorg/codehaus/groovy/ast/MethodNode;

    const/4 v4, 0x1

    sget-object v6, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_PARAMS:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v7, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    move-object v2, p0

    invoke-direct/range {v2 .. v8}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p0
.end method

.method private static createBuilder(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 3

    .line 228
    new-instance v0, Lorg/codehaus/groovy/ast/InnerClassNode;

    invoke-static {p0, p1}, Lgroovy/transform/builder/DefaultStrategy;->getFullName(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;

    move-result-object p0

    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/16 v2, 0x9

    invoke-direct {v0, p1, p0, v2, v1}, Lorg/codehaus/groovy/ast/InnerClassNode;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;)V

    return-object v0
.end method

.method private static createBuilderFactoryMethod(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0

    .line 223
    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedInnerClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 224
    invoke-static {p0, p2}, Lgroovy/transform/builder/DefaultStrategy;->createBuilderMethod(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p0

    invoke-static {p1, p0}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method private static createBuilderMethod(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 9

    const-string v0, "builderMethodName"

    const-string v1, "builder"

    .line 255
    invoke-static {p0, v0, v1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    .line 256
    new-instance v8, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v8}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 257
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {v8, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 258
    new-instance p0, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v6, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_PARAMS:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v7, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/16 v4, 0x9

    move-object v2, p0

    move-object v5, p1

    invoke-direct/range {v2 .. v8}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p0
.end method

.method private createBuilderMethodForProp(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;Ljava/lang/String;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 11

    .line 269
    invoke-virtual {p2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 270
    invoke-virtual {p2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object p2

    .line 271
    invoke-virtual {p0, p3, p2}, Lgroovy/transform/builder/DefaultStrategy;->getSetterName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 272
    new-instance p3, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->newClass(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    const/4 v1, 0x1

    new-array v3, v1, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {v0, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v5

    const/4 v6, 0x0

    aput-object v5, v3, v6

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v5

    sget-object v7, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v3, 0x2

    new-array v3, v3, [Lorg/codehaus/groovy/ast/stmt/Statement;

    const-string v8, "this"

    .line 273
    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v9

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v10

    invoke-static {v9, v10}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v9

    invoke-static {p2, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p2

    invoke-static {v9, p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p2

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p2

    aput-object p2, v3, v6

    .line 274
    invoke-static {v8, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    aput-object p1, v3, v1

    .line 272
    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object p1

    const/4 v3, 0x1

    move-object v1, p3

    move-object v6, v7

    move-object v7, p1

    invoke-direct/range {v1 .. v7}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p3
.end method

.method private static createFieldCopy(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 7

    .line 286
    new-instance v6, Lorg/codehaus/groovy/ast/FieldNode;

    sget-object v5, Lgroovy/transform/builder/DefaultStrategy;->DEFAULT_INITIAL_VALUE:Lorg/codehaus/groovy/ast/expr/Expression;

    const/4 v2, 0x2

    move-object v0, v6

    move-object v1, p1

    move-object v3, p2

    move-object v4, p0

    invoke-direct/range {v0 .. v5}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object v6
.end method

.method private static createFieldCopy(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 8

    .line 279
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object v0

    .line 280
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-static {v1, p0, v0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->extractSuperClassGenerics(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)V

    .line 281
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpecRecurse(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 282
    new-instance v0, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/Parameter;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/Parameter;->getInitialExpression()Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v7

    const/4 v4, 0x2

    move-object v2, v0

    move-object v6, p0

    invoke-direct/range {v2 .. v7}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object v0
.end method

.method private static getCorrectedType(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0

    .line 217
    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object p2

    .line 218
    invoke-static {p1, p0, p2}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->extractSuperClassGenerics(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)V

    .line 219
    invoke-static {p2, p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpecRecurse(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    return-object p0
.end method

.method private static getFullName(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/lang/String;
    .locals 2

    .line 232
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "Builder"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "builderClassName"

    invoke-static {p0, v1, v0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    .line 233
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "$"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static getPrefix(Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;
    .locals 2

    const-string v0, "prefix"

    const-string v1, ""

    .line 237
    invoke-static {p0, v0, v1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static initializeInstance(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/BlockStatement;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;",
            ">;",
            "Lorg/codehaus/groovy/ast/stmt/BlockStatement;",
            ")",
            "Lorg/codehaus/groovy/ast/expr/Expression;"
        }
    .end annotation

    .line 290
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "_the"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->localVarX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    .line 291
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p0

    invoke-static {v0, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->declS(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {p2, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 292
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result p1

    if-eqz p1, :cond_0

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;

    .line 293
    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {v2, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    invoke-static {v1, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    :cond_0
    return-object v0
.end method


# virtual methods
.method public build(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V
    .locals 1

    const-string v0, "forClass"

    .line 171
    invoke-virtual {p0, p1, p3, v0}, Lgroovy/transform/builder/DefaultStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const-string v0, "force"

    .line 172
    invoke-virtual {p0, p1, p3, v0}, Lgroovy/transform/builder/DefaultStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    .line 173
    :cond_1
    instance-of v0, p2, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v0, :cond_2

    .line 174
    check-cast p2, Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, p1, p2, p3}, Lgroovy/transform/builder/DefaultStrategy;->buildClass(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V

    goto :goto_0

    .line 175
    :cond_2
    instance-of v0, p2, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v0, :cond_3

    .line 176
    check-cast p2, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-virtual {p0, p1, p2, p3}, Lgroovy/transform/builder/DefaultStrategy;->buildMethod(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V

    :cond_3
    :goto_0
    return-void
.end method

.method public buildClass(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V
    .locals 10

    .line 196
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 197
    new-instance v7, Ljava/util/ArrayList;

    invoke-direct {v7}, Ljava/util/ArrayList;-><init>()V

    const-string v0, "<DummyUndefinedMarkerString-DoNotUse>"

    .line 198
    invoke-interface {v7, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-object v0, p0

    move-object v1, p1

    move-object v2, p3

    move-object v3, p2

    move-object v4, v6

    move-object v5, v7

    .line 199
    invoke-virtual/range {v0 .. v5}, Lgroovy/transform/builder/DefaultStrategy;->getIncludeExclude(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/util/List;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 200
    :cond_0
    invoke-interface {v7}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-ne v0, v2, :cond_1

    invoke-interface {v7, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    invoke-static {v0}, Lgroovy/transform/Undefined;->isUndefined(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v0, 0x0

    move-object v5, v0

    goto :goto_0

    :cond_1
    move-object v5, v7

    .line 201
    :goto_0
    invoke-static {p3, p2}, Lgroovy/transform/builder/DefaultStrategy;->createBuilder(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v8

    .line 202
    invoke-static {p3, p2, v8}, Lgroovy/transform/builder/DefaultStrategy;->createBuilderFactoryMethod(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 204
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    const-string v3, "allNames"

    invoke-virtual {p1, p3, v3, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v7

    .line 205
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    const-string v1, "allProperties"

    invoke-virtual {p1, p3, v1, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v0

    xor-int/lit8 v9, v0, 0x1

    move-object v0, p0

    move-object v1, p1

    move-object v2, p3

    move-object v3, p2

    move-object v4, v6

    move v6, v7

    move v7, v9

    .line 206
    invoke-virtual/range {v0 .. v7}, Lgroovy/transform/builder/DefaultStrategy;->getPropertyInfos(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/util/List;ZZ)Ljava/util/List;

    move-result-object p1

    .line 207
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;

    .line 208
    invoke-virtual {v1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    invoke-static {p2, v2, v8}, Lgroovy/transform/builder/DefaultStrategy;->getCorrectedType(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    .line 209
    invoke-virtual {v1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;->getName()Ljava/lang/String;

    move-result-object v1

    .line 210
    invoke-static {p2, v1, v2}, Lgroovy/transform/builder/DefaultStrategy;->createFieldCopy(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v3

    invoke-virtual {v8, v3}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 211
    new-instance v3, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;

    invoke-direct {v3, v1, v2}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)V

    invoke-static {p3}, Lgroovy/transform/builder/DefaultStrategy;->getPrefix(Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v8, v3, v1}, Lgroovy/transform/builder/DefaultStrategy;->createBuilderMethodForProp(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;Ljava/lang/String;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v1

    invoke-static {v8, v1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    goto :goto_1

    .line 213
    :cond_2
    invoke-static {p3, p2, p1}, Lgroovy/transform/builder/DefaultStrategy;->createBuildMethod(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    invoke-static {v8, p1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method public buildMethod(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V
    .locals 7

    const-string v0, "includes"

    .line 181
    invoke-virtual {p1, p3, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->getMemberValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const-string v0, "excludes"

    invoke-virtual {p1, p3, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->getMemberValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 182
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Error during "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " processing: includes/excludes only allowed on classes"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0, p3}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 185
    :cond_1
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 186
    invoke-static {p3, p1}, Lgroovy/transform/builder/DefaultStrategy;->createBuilder(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    .line 187
    invoke-static {p3, p1, v0}, Lgroovy/transform/builder/DefaultStrategy;->createBuilderFactoryMethod(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 188
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    array-length v2, v1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_2

    aget-object v4, v1, v3

    .line 189
    invoke-static {p1, v4}, Lgroovy/transform/builder/DefaultStrategy;->createFieldCopy(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v5

    invoke-virtual {v0, v5}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 190
    new-instance v5, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->getName()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-direct {v5, v6, v4}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;-><init>(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)V

    invoke-static {p3}, Lgroovy/transform/builder/DefaultStrategy;->getPrefix(Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;

    move-result-object v4

    invoke-direct {p0, v0, v5, v4}, Lgroovy/transform/builder/DefaultStrategy;->createBuilderMethodForProp(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy$PropertyInfo;Ljava/lang/String;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object v4

    invoke-static {v0, v4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 192
    :cond_2
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v1

    invoke-static {p3, p1, p2, v1}, Lgroovy/transform/builder/DefaultStrategy;->createBuildMethodForMethod(Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;[Lorg/codehaus/groovy/ast/Parameter;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    invoke-static {v0, p1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method
