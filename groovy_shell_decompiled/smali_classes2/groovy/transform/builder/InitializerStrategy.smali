.class public Lgroovy/transform/builder/InitializerStrategy;
.super Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;
.source "InitializerStrategy.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/transform/builder/InitializerStrategy$UNSET;,
        Lgroovy/transform/builder/InitializerStrategy$SET;
    }
.end annotation


# static fields
.field private static final DEFAULT_INITIAL_VALUE:Lorg/codehaus/groovy/ast/expr/Expression;

.field private static final TUPLECONS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 137
    const-class v0, Lgroovy/transform/TupleConstructor;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/builder/InitializerStrategy;->TUPLECONS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 122
    invoke-direct {p0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation$AbstractBuilderStrategy;-><init>()V

    return-void
.end method

.method private static addFields(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ")V"
        }
    .end annotation

    .line 208
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/FieldNode;

    .line 209
    invoke-static {p0, v0}, Lgroovy/transform/builder/InitializerStrategy;->createFieldCopy(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method private buildCommon(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/AnnotationNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ")V"
        }
    .end annotation

    const-string v0, "prefix"

    const-string v1, ""

    .line 214
    invoke-static {p2, v0, v1}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "buildMethodName"

    const-string v2, "create"

    .line 215
    invoke-static {p2, v1, v2}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 216
    invoke-static {p1, p4}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedInnerClass(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 217
    invoke-static {p4, p1, p3}, Lgroovy/transform/builder/InitializerStrategy;->createBuilderConstructors(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)V

    const-string v2, "builderMethodName"

    const-string v3, "createInitializer"

    .line 218
    invoke-static {p2, v2, v3}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    .line 219
    invoke-interface {p3}, Ljava/util/List;->size()I

    move-result v2

    invoke-static {v1, p4, v2, p2}, Lgroovy/transform/builder/InitializerStrategy;->createBuilderMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;ILjava/lang/String;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p2

    invoke-static {p1, p2}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    const/4 p1, 0x0

    .line 220
    :goto_0
    invoke-interface {p3}, Ljava/util/List;->size()I

    move-result p2

    if-ge p1, p2, :cond_0

    .line 221
    invoke-direct {p0, p4, p3, v0, p1}, Lgroovy/transform/builder/InitializerStrategy;->createBuilderMethodForField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;I)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p2

    invoke-static {p4, p2}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 223
    :cond_0
    invoke-static {p4, v1, p3}, Lgroovy/transform/builder/InitializerStrategy;->createBuildMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/util/List;)Lorg/codehaus/groovy/ast/MethodNode;

    move-result-object p1

    invoke-static {p4, p1}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method private static convertParamsToFields(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;)Ljava/util/List;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "[",
            "Lorg/codehaus/groovy/ast/Parameter;",
            ")",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;"
        }
    .end annotation

    .line 227
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 228
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, p1, v2

    .line 229
    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object v4

    .line 230
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-static {v4, v5}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpecRecurse(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v9

    .line 231
    new-instance v4, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getName()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/Parameter;->getModifiers()I

    move-result v8

    sget-object v11, Lgroovy/transform/builder/InitializerStrategy;->DEFAULT_INITIAL_VALUE:Lorg/codehaus/groovy/ast/expr/Expression;

    move-object v6, v4

    move-object v10, p0

    invoke-direct/range {v6 .. v11}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 232
    invoke-interface {v0, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 233
    invoke-virtual {p0, v4}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static createBuildMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/util/List;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;)",
            "Lorg/codehaus/groovy/ast/MethodNode;"
        }
    .end annotation

    .line 331
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p2

    invoke-static {p2}, Lgroovy/transform/builder/InitializerStrategy;->unsetGenTypes(I)[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object p2

    invoke-static {p0, p2}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->makeClassSafeWithGenerics(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/GenericsType;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    .line 332
    new-instance p0, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v4, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_PARAMS:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v5, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 p2, 0x1

    new-array p2, p2, [Lorg/codehaus/groovy/ast/stmt/Statement;

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    const/4 v1, 0x0

    aput-object v0, p2, v1

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v6

    const/16 v2, 0x9

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p0
.end method

.method private static createBuildeeConstructors(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;ZZ)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/transform/BuilderASTTransformation;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;ZZ)V"
        }
    .end annotation

    .line 281
    invoke-static {p1, p2, p3}, Lgroovy/transform/builder/InitializerStrategy;->createInitializerConstructor(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Lorg/codehaus/groovy/ast/ConstructorNode;

    if-eqz p4, :cond_0

    const/4 p0, 0x1

    new-array p0, p0, [Lorg/codehaus/groovy/ast/stmt/Statement;

    const/4 p2, 0x0

    .line 283
    invoke-static {}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorSuperS()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p4

    aput-object p4, p0, p2

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object p0

    .line 284
    invoke-static {p3, p0, p5}, Lgroovy/transform/builder/InitializerStrategy;->initializeFields(Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Z)V

    const/4 p2, 0x2

    .line 285
    invoke-static {p3, p1}, Lgroovy/transform/builder/InitializerStrategy;->getParams(Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p3

    sget-object p4, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p1, p2, p3, p4, p0}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedConstructor(Lorg/codehaus/groovy/ast/ClassNode;I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;

    :cond_0
    return-void
.end method

.method private static createBuildeeMethods(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;)V"
        }
    .end annotation

    .line 290
    invoke-interface {p3}, Ljava/util/List;->size()I

    move-result v0

    invoke-static {v0}, Lgroovy/transform/builder/InitializerStrategy;->setGenTypes(I)[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v0

    invoke-static {p2, v0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->makeClassSafeWithGenerics(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/GenericsType;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    .line 291
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const-string v1, "initializer"

    .line 292
    invoke-static {p2, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 293
    invoke-interface {p3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :goto_0
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/FieldNode;

    .line 294
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v4

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 296
    :cond_0
    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "$"

    invoke-virtual {p3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    .line 297
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getName()Ljava/lang/String;

    move-result-object v3

    const/16 v4, 0x9

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    const/4 v2, 0x1

    new-array v6, v2, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {p2, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p2

    const/4 v1, 0x0

    aput-object p2, v6, v1

    invoke-static {v6}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v6

    sget-object v7, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    new-array p2, v2, [Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 298
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args(Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    invoke-static {p0, p3, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    aput-object v0, p2, v1

    invoke-static {p2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v8

    move-object v2, p0

    .line 297
    invoke-static/range {v2 .. v8}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    .line 299
    invoke-static {p0, p1, p3}, Lgroovy/transform/builder/InitializerStrategy;->renameMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;)V

    return-void
.end method

.method private static createBuilderConstructors(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;)V"
        }
    .end annotation

    .line 273
    sget-object v0, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_PARAMS:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v1, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v2, 0x1

    new-array v3, v2, [Lorg/codehaus/groovy/ast/stmt/Statement;

    invoke-static {}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorSuperS()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v4

    const/4 v5, 0x0

    aput-object v4, v3, v5

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v3

    const/4 v4, 0x2

    invoke-static {p0, v4, v0, v1, v3}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedConstructor(Lorg/codehaus/groovy/ast/ClassNode;I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;

    new-array v0, v2, [Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 275
    invoke-static {}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorSuperS()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v1

    aput-object v1, v0, v5

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v0

    .line 276
    invoke-static {p2, v0, v5}, Lgroovy/transform/builder/InitializerStrategy;->initializeFields(Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Z)V

    .line 277
    invoke-static {p2, p1}, Lgroovy/transform/builder/InitializerStrategy;->getParams(Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    sget-object p2, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p0, v4, p1, p2, v0}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedConstructor(Lorg/codehaus/groovy/ast/ClassNode;I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;

    return-void
.end method

.method private createBuilderForAnnotatedClass(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;ZZZ)V
    .locals 12

    move-object v6, p0

    move-object v7, p1

    move-object v8, p2

    move-object v9, p3

    .line 153
    new-instance v10, Ljava/util/ArrayList;

    invoke-direct {v10}, Ljava/util/ArrayList;-><init>()V

    .line 154
    new-instance v11, Ljava/util/ArrayList;

    invoke-direct {v11}, Ljava/util/ArrayList;-><init>()V

    const-string v0, "<DummyUndefinedMarkerString-DoNotUse>"

    .line 155
    invoke-interface {v11, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    move-object v0, p0

    move-object v1, p1

    move-object v2, p3

    move-object v3, p2

    move-object v4, v10

    move-object v5, v11

    .line 156
    invoke-virtual/range {v0 .. v5}, Lgroovy/transform/builder/InitializerStrategy;->getIncludeExclude(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/util/List;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 157
    :cond_0
    invoke-interface {v11}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-ne v0, v2, :cond_1

    invoke-interface {v11, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    invoke-static {v0}, Lgroovy/transform/Undefined;->isUndefined(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v11, 0x0

    .line 158
    :cond_1
    invoke-virtual {p0, p1, p3, p2}, Lgroovy/transform/builder/InitializerStrategy;->getFields(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/List;

    move-result-object v0

    move/from16 v3, p5

    .line 159
    invoke-static {v0, v11, v10, v3}, Lgroovy/transform/builder/InitializerStrategy;->filterFields(Ljava/util/List;Ljava/util/List;Ljava/util/List;Z)Ljava/util/List;

    move-result-object v3

    .line 160
    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 161
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Error during "

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v4, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v4, " processing: at least one property is required for this strategy"

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0, p3}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 164
    :cond_2
    invoke-static {p2, p3}, Lgroovy/transform/builder/InitializerStrategy;->getBuilderClassName(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;

    move-result-object v0

    invoke-interface {v3}, Ljava/util/List;->size()I

    move-result v4

    invoke-static {p2, v0, v4}, Lgroovy/transform/builder/InitializerStrategy;->createInnerHelperClass(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;I)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    .line 165
    invoke-static {p2, v3, v4}, Lgroovy/transform/builder/InitializerStrategy;->addFields(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 167
    invoke-direct {p0, p2, p3, v3, v4}, Lgroovy/transform/builder/InitializerStrategy;->buildCommon(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 168
    sget-object v0, Lgroovy/transform/builder/InitializerStrategy;->TUPLECONS_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p2, v0}, Lorg/apache/groovy/ast/tools/AnnotatedNodeUtils;->hasAnnotation(Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/ClassNode;)Z

    move-result v0

    if-eqz v0, :cond_4

    if-eqz p6, :cond_3

    goto :goto_0

    :cond_3
    move v5, v1

    goto :goto_1

    :cond_4
    :goto_0
    move v5, v2

    :goto_1
    move-object v0, p1

    move-object v1, p2

    move-object v2, v4

    move v4, v5

    move/from16 v5, p4

    .line 169
    invoke-static/range {v0 .. v5}, Lgroovy/transform/builder/InitializerStrategy;->createBuildeeConstructors(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;ZZ)V

    return-void
.end method

.method private createBuilderForAnnotatedMethod(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/AnnotationNode;Z)V
    .locals 9

    const-string v0, "includes"

    .line 173
    invoke-virtual {p1, p3, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->getMemberValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    const-string v1, "Error during "

    if-nez v0, :cond_0

    const-string v0, "excludes"

    invoke-virtual {p1, p3, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->getMemberValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 174
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v2, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " processing: includes/excludes only allowed on classes"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0, p3}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 177
    :cond_1
    instance-of v0, p2, Lorg/codehaus/groovy/ast/ConstructorNode;

    if-eqz v0, :cond_2

    const/4 v2, 0x2

    .line 178
    invoke-virtual {p2, v2}, Lorg/codehaus/groovy/ast/MethodNode;->setModifiers(I)V

    goto :goto_0

    .line 180
    :cond_2
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->isStatic()Z

    move-result v2

    if-nez v2, :cond_3

    .line 181
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    sget-object v3, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, " processing: method builders only allowed on static methods"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1, v2, p3}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    :cond_3
    const/16 v2, 0x100a

    .line 184
    invoke-virtual {p2, v2}, Lorg/codehaus/groovy/ast/MethodNode;->setModifiers(I)V

    .line 186
    :goto_0
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    .line 187
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 188
    array-length v3, v2

    if-nez v3, :cond_4

    .line 189
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    sget-object v3, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->MY_TYPE_NAME:Ljava/lang/String;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v3, " processing: at least one parameter is required for this strategy"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1, p3}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->addError(Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)V

    .line 192
    :cond_4
    invoke-static {v4, p3}, Lgroovy/transform/builder/InitializerStrategy;->getBuilderClassName(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;

    move-result-object v1

    array-length v3, v2

    invoke-static {v4, v1, v3}, Lgroovy/transform/builder/InitializerStrategy;->createInnerHelperClass(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;I)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 193
    invoke-static {v5, v2}, Lgroovy/transform/builder/InitializerStrategy;->convertParamsToFields(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;)Ljava/util/List;

    move-result-object v6

    .line 195
    invoke-direct {p0, v4, p3, v6, v5}, Lgroovy/transform/builder/InitializerStrategy;->buildCommon(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;)V

    if-eqz v0, :cond_5

    const/4 v7, 0x0

    move-object v3, p1

    move v8, p4

    .line 197
    invoke-static/range {v3 .. v8}, Lgroovy/transform/builder/InitializerStrategy;->createBuildeeConstructors(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;ZZ)V

    goto :goto_1

    .line 199
    :cond_5
    invoke-static {v4, p2, v5, v6}, Lgroovy/transform/builder/InitializerStrategy;->createBuildeeMethods(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)V

    :goto_1
    return-void
.end method

.method private static createBuilderMethod(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;ILjava/lang/String;)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 7

    .line 250
    new-instance v6, Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    invoke-direct {v6}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;-><init>()V

    .line 251
    invoke-static {p1, p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callX(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/StaticMethodCallExpression;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p0

    invoke-virtual {v6, p0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    .line 252
    invoke-static {p2}, Lgroovy/transform/builder/InitializerStrategy;->unsetGenTypes(I)[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object p0

    invoke-static {p1, p0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->makeClassSafeWithGenerics(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/GenericsType;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    .line 253
    new-instance p0, Lorg/codehaus/groovy/ast/MethodNode;

    sget-object v4, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_PARAMS:[Lorg/codehaus/groovy/ast/Parameter;

    sget-object v5, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/16 v2, 0x9

    move-object v0, p0

    move-object v1, p3

    invoke-direct/range {v0 .. v6}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p0
.end method

.method private createBuilderMethodForField(Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;Ljava/lang/String;I)Lorg/codehaus/groovy/ast/MethodNode;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;",
            "Ljava/lang/String;",
            "I)",
            "Lorg/codehaus/groovy/ast/MethodNode;"
        }
    .end annotation

    .line 336
    invoke-interface {p2, p4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v0

    .line 337
    invoke-virtual {p0, p3, v0}, Lgroovy/transform/builder/InitializerStrategy;->getSetterName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    .line 338
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result p3

    new-array p3, p3, [Lorg/codehaus/groovy/ast/GenericsType;

    .line 339
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    const/4 v3, 0x0

    move v4, v3

    .line 340
    :goto_0
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v5

    const-string v6, "this"

    if-ge v4, v5, :cond_2

    if-ne v4, p4, :cond_0

    .line 341
    new-instance v5, Lorg/codehaus/groovy/ast/GenericsType;

    const-class v7, Lgroovy/transform/builder/InitializerStrategy$SET;

    invoke-static {v7}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v7

    invoke-direct {v5, v7}, Lorg/codehaus/groovy/ast/GenericsType;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    goto :goto_1

    :cond_0
    invoke-static {v4}, Lgroovy/transform/builder/InitializerStrategy;->makePlaceholder(I)Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v5

    :goto_1
    aput-object v5, p3, v4

    if-ne v4, p4, :cond_1

    .line 342
    invoke-static {v6}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v6

    invoke-static {v5, v6}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v5

    goto :goto_2

    :cond_1
    invoke-interface {p2, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {v5}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v5

    :goto_2
    invoke-interface {v1, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 344
    :cond_2
    invoke-static {p1, p3}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->makeClassSafeWithGenerics(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/GenericsType;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    .line 345
    invoke-interface {p2, p4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lorg/codehaus/groovy/ast/FieldNode;

    .line 346
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p3

    invoke-static {p3}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object p3

    .line 347
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p4

    invoke-static {p4, p1, p3}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->extractSuperClassGenerics(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)V

    .line 348
    invoke-virtual {p2}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p3, p1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpecRecurse(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 349
    new-instance p2, Lorg/codehaus/groovy/ast/MethodNode;

    const/4 p3, 0x1

    const/4 p4, 0x1

    new-array v5, p4, [Lorg/codehaus/groovy/ast/Parameter;

    invoke-static {p1, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v7

    aput-object v7, v5, v3

    invoke-static {v5}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v5

    sget-object v7, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v8, 0x2

    new-array v8, v8, [Lorg/codehaus/groovy/ast/stmt/Statement;

    .line 350
    invoke-static {v6}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v6

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->constX(Ljava/lang/Object;)Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object v9

    invoke-static {v6, v9}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v6

    invoke-static {v0, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object p1

    invoke-static {v6, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    aput-object p1, v8, v3

    .line 351
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args(Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object p1

    invoke-static {v4, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorX(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/ConstructorCallExpression;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->returnS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object p1

    aput-object p1, v8, p4

    .line 349
    invoke-static {v8}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object p1

    move-object v1, p2

    move v3, p3

    move-object v6, v7

    move-object v7, p1

    invoke-direct/range {v1 .. v7}, Lorg/codehaus/groovy/ast/MethodNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)V

    return-object p2
.end method

.method private static createFieldCopy(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/FieldNode;)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 8

    .line 363
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object v0

    .line 364
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-static {v1, p0, v0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->extractSuperClassGenerics(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)V

    .line 365
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpecRecurse(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    .line 366
    new-instance v0, Lorg/codehaus/groovy/ast/FieldNode;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/FieldNode;->getModifiers()I

    move-result v4

    sget-object v7, Lgroovy/transform/builder/InitializerStrategy;->DEFAULT_INITIAL_VALUE:Lorg/codehaus/groovy/ast/expr/Expression;

    move-object v2, v0

    move-object v6, p0

    invoke-direct/range {v2 .. v7}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    return-object v0
.end method

.method private static createInitializerConstructor(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/List;)Lorg/codehaus/groovy/ast/ConstructorNode;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;)",
            "Lorg/codehaus/groovy/ast/ConstructorNode;"
        }
    .end annotation

    .line 321
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v0

    invoke-static {v0}, Lgroovy/transform/builder/InitializerStrategy;->setGenTypes(I)[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v0

    invoke-static {p1, v0}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->makeClassSafeWithGenerics(Lorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/GenericsType;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 322
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const-string v1, "initializer"

    .line 323
    invoke-static {p1, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v2

    .line 324
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/FieldNode;

    .line 325
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v4

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v4, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v3

    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_0
    const/4 p2, 0x1

    new-array v2, p2, [Lorg/codehaus/groovy/ast/Parameter;

    .line 327
    invoke-static {p1, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, v2, v1

    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->params([Lorg/codehaus/groovy/ast/Parameter;)[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    sget-object v2, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->NO_EXCEPTIONS:[Lorg/codehaus/groovy/ast/ClassNode;

    new-array v3, p2, [Lorg/codehaus/groovy/ast/stmt/Statement;

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->args(Ljava/util/List;)Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ctorThisS(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    aput-object v0, v3, v1

    invoke-static {v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->block([Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/stmt/BlockStatement;

    move-result-object v0

    invoke-static {p0, p2, p1, v2, v0}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedConstructor(Lorg/codehaus/groovy/ast/ClassNode;I[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/ConstructorNode;

    move-result-object p0

    return-object p0
.end method

.method private static createInnerHelperClass(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;I)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 3

    .line 239
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "$"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 240
    new-instance v0, Lorg/codehaus/groovy/ast/InnerClassNode;

    sget-object v1, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    const/16 v2, 0x9

    invoke-direct {v0, p0, p1, v2, v1}, Lorg/codehaus/groovy/ast/InnerClassNode;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;)V

    .line 241
    new-array p0, p2, [Lorg/codehaus/groovy/ast/GenericsType;

    const/4 p1, 0x0

    :goto_0
    if-ge p1, p2, :cond_0

    .line 243
    invoke-static {p1}, Lgroovy/transform/builder/InitializerStrategy;->makePlaceholder(I)Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object v1

    aput-object v1, p0, p1

    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    .line 245
    :cond_0
    invoke-virtual {v0, p0}, Lorg/codehaus/groovy/ast/ClassNode;->setGenericsTypes([Lorg/codehaus/groovy/ast/GenericsType;)V

    return-object v0
.end method

.method private static filterFields(Ljava/util/List;Ljava/util/List;Ljava/util/List;Z)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;Z)",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;"
        }
    .end annotation

    .line 370
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 371
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/ast/FieldNode;

    .line 372
    invoke-virtual {v1}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2, p2, p1, p3}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->shouldSkipUndefinedAware(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Z)Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    .line 373
    :cond_0
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method private static getBuilderClassName(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;)Ljava/lang/String;
    .locals 1

    .line 204
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getNameWithoutPackage()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "Initializer"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    const-string v0, "builderClassName"

    invoke-static {p1, v0, p0}, Lorg/codehaus/groovy/transform/AbstractASTTransformation;->getMemberStringValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static getParams(Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;)[Lorg/codehaus/groovy/ast/Parameter;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ")[",
            "Lorg/codehaus/groovy/ast/Parameter;"
        }
    .end annotation

    .line 309
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v0

    new-array v1, v0, [Lorg/codehaus/groovy/ast/Parameter;

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    .line 311
    invoke-interface {p0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lorg/codehaus/groovy/ast/FieldNode;

    .line 312
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->getDeclaringClass()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->createGenericsSpec(Lorg/codehaus/groovy/ast/ClassNode;)Ljava/util/Map;

    move-result-object v4

    .line 313
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-static {v5, p1, v4}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->extractSuperClassGenerics(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Ljava/util/Map;)V

    .line 314
    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-static {v4, v5}, Lorg/codehaus/groovy/ast/tools/GenericsUtils;->correctToGenericsSpecRecurse(Ljava/util/Map;Lorg/codehaus/groovy/ast/ClassNode;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v4

    .line 315
    new-instance v5, Lorg/codehaus/groovy/ast/Parameter;

    invoke-virtual {v3}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v5, v4, v3}, Lorg/codehaus/groovy/ast/Parameter;-><init>(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)V

    aput-object v5, v1, v2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method private static initializeFields(Ljava/util/List;Lorg/codehaus/groovy/ast/stmt/BlockStatement;Z)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/codehaus/groovy/ast/FieldNode;",
            ">;",
            "Lorg/codehaus/groovy/ast/stmt/BlockStatement;",
            "Z)V"
        }
    .end annotation

    .line 379
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/ast/FieldNode;

    .line 380
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v1

    if-eqz p2, :cond_0

    .line 382
    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->isFinal()Z

    move-result v2

    if-nez v2, :cond_0

    .line 383
    invoke-static {v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-static {v2, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->callThisX(Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    move-result-object v0

    goto :goto_1

    :cond_0
    const-string v2, "this"

    .line 384
    invoke-static {v2}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v2

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->propX(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;)Lorg/codehaus/groovy/ast/expr/PropertyExpression;

    move-result-object v2

    invoke-virtual {v0}, Lorg/codehaus/groovy/ast/FieldNode;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-static {v0, v1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->param(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->varX(Lorg/codehaus/groovy/ast/Variable;)Lorg/codehaus/groovy/ast/expr/VariableExpression;

    move-result-object v0

    invoke-static {v2, v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->assignX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;

    move-result-object v0

    .line 382
    :goto_1
    invoke-static {v0}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->stmt(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v0

    .line 381
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/stmt/BlockStatement;->addStatement(Lorg/codehaus/groovy/ast/stmt/Statement;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method private static makePlaceholder(I)Lorg/codehaus/groovy/ast/GenericsType;
    .locals 2

    .line 356
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "T"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lorg/codehaus/groovy/ast/ClassHelper;->makeWithoutCaching(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    .line 357
    sget-object v0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setRedirect(Lorg/codehaus/groovy/ast/ClassNode;)V

    const/4 v0, 0x1

    .line 358
    invoke-virtual {p0, v0}, Lorg/codehaus/groovy/ast/ClassNode;->setGenericsPlaceHolder(Z)V

    .line 359
    new-instance v0, Lorg/codehaus/groovy/ast/GenericsType;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/ast/GenericsType;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    return-object v0
.end method

.method private static renameMethod(Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;)V
    .locals 7

    .line 304
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getModifiers()I

    move-result v2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getReturnType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object v4

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getExceptions()[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v5

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getCode()Lorg/codehaus/groovy/ast/stmt/Statement;

    move-result-object v6

    move-object v0, p0

    move-object v1, p2

    invoke-static/range {v0 .. v6}, Lorg/apache/groovy/ast/tools/ClassNodeUtils;->addGeneratedMethod(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;[Lorg/codehaus/groovy/ast/Parameter;[Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/stmt/Statement;)Lorg/codehaus/groovy/ast/MethodNode;

    .line 305
    invoke-virtual {p0, p1}, Lorg/codehaus/groovy/ast/ClassNode;->removeMethod(Lorg/codehaus/groovy/ast/MethodNode;)V

    return-void
.end method

.method private static setGenTypes(I)[Lorg/codehaus/groovy/ast/GenericsType;
    .locals 4

    .line 265
    new-array v0, p0, [Lorg/codehaus/groovy/ast/GenericsType;

    const/4 v1, 0x0

    :goto_0
    if-ge v1, p0, :cond_0

    .line 267
    new-instance v2, Lorg/codehaus/groovy/ast/GenericsType;

    const-class v3, Lgroovy/transform/builder/InitializerStrategy$SET;

    invoke-static {v3}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-direct {v2, v3}, Lorg/codehaus/groovy/ast/GenericsType;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    aput-object v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method private static unsetGenTypes(I)[Lorg/codehaus/groovy/ast/GenericsType;
    .locals 4

    .line 257
    new-array v0, p0, [Lorg/codehaus/groovy/ast/GenericsType;

    const/4 v1, 0x0

    :goto_0
    if-ge v1, p0, :cond_0

    .line 259
    new-instance v2, Lorg/codehaus/groovy/ast/GenericsType;

    const-class v3, Lgroovy/transform/builder/InitializerStrategy$UNSET;

    invoke-static {v3}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-direct {v2, v3}, Lorg/codehaus/groovy/ast/GenericsType;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    aput-object v2, v0, v1

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method


# virtual methods
.method public build(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotatedNode;Lorg/codehaus/groovy/ast/AnnotationNode;)V
    .locals 10

    const-string v0, "forClass"

    .line 140
    invoke-virtual {p0, p1, p3, v0}, Lgroovy/transform/builder/InitializerStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const-string v0, "allProperties"

    .line 141
    invoke-virtual {p0, p1, p3, v0}, Lgroovy/transform/builder/InitializerStrategy;->unsupportedAttribute(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    const/4 v0, 0x1

    .line 142
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v2, "useSetters"

    invoke-virtual {p1, p3, v2, v1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v7

    .line 143
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v2, "allNames"

    invoke-virtual {p1, p3, v2, v1}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v8

    .line 144
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    const-string v1, "force"

    invoke-virtual {p1, p3, v1, v0}, Lorg/codehaus/groovy/transform/BuilderASTTransformation;->memberHasValue(Lorg/codehaus/groovy/ast/AnnotationNode;Ljava/lang/String;Ljava/lang/Object;)Z

    move-result v9

    .line 145
    instance-of v0, p2, Lorg/codehaus/groovy/ast/ClassNode;

    if-eqz v0, :cond_2

    .line 146
    move-object v5, p2

    check-cast v5, Lorg/codehaus/groovy/ast/ClassNode;

    move-object v3, p0

    move-object v4, p1

    move-object v6, p3

    invoke-direct/range {v3 .. v9}, Lgroovy/transform/builder/InitializerStrategy;->createBuilderForAnnotatedClass(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/AnnotationNode;ZZZ)V

    goto :goto_0

    .line 147
    :cond_2
    instance-of v0, p2, Lorg/codehaus/groovy/ast/MethodNode;

    if-eqz v0, :cond_3

    .line 148
    check-cast p2, Lorg/codehaus/groovy/ast/MethodNode;

    invoke-direct {p0, p1, p2, p3, v7}, Lgroovy/transform/builder/InitializerStrategy;->createBuilderForAnnotatedMethod(Lorg/codehaus/groovy/transform/BuilderASTTransformation;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/AnnotationNode;Z)V

    :cond_3
    :goto_0
    return-void
.end method
