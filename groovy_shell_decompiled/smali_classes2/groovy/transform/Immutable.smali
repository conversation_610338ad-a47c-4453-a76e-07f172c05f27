.class public interface abstract annotation Lgroovy/transform/Immutable;
.super Ljava/lang/Object;
.source "Immutable.groovy"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/Immutable;
        copyWith = false
        knownImmutableClasses = {}
        knownImmutables = {}
    .end subannotation
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/transform/Immutable$CollectorHelper;
    }
.end annotation

.annotation runtime Lgroovy/transform/AnnotationCollector;
    mode = .enum Lgroovy/transform/AnnotationCollectorMode;->PREFER_EXPLICIT_MERGED:Lgroovy/transform/AnnotationCollectorMode;
    serializeClass = Lgroovy/transform/Immutable$CollectorHelper;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;
    }
.end annotation


# virtual methods
.method public abstract copyWith()Z
.end method

.method public abstract knownImmutableClasses()[Ljava/lang/Class;
.end method

.method public abstract knownImmutables()[Ljava/lang/String;
.end method
