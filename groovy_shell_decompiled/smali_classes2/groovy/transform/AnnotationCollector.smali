.class public interface abstract annotation Lgroovy/transform/AnnotationCollector;
.super Ljava/lang/Object;
.source "AnnotationCollector.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/AnnotationCollector;
        mode = .enum Lgroovy/transform/AnnotationCollectorMode;->DUPLICATE:Lgroovy/transform/AnnotationCollectorMode;
        processor = "org.codehaus.groovy.transform.AnnotationCollectorTransform"
        serializeClass = Lgroovy/transform/Undefined$CLASS;
        value = {}
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->ANNOTATION_TYPE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;
    }
.end annotation


# virtual methods
.method public abstract mode()Lgroovy/transform/AnnotationCollectorMode;
.end method

.method public abstract processor()Ljava/lang/String;
.end method

.method public abstract serializeClass()Ljava/lang/Class;
.end method

.method public abstract value()[Ljava/lang/Class;
.end method
