.class public final Lgroovy/transform/Undefined;
.super Ljava/lang/Object;
.source "Undefined.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/transform/Undefined$CLASS;,
        Lgroovy/transform/Undefined$EXCEPTION;
    }
.end annotation


# static fields
.field public static final STRING:Ljava/lang/String; = "<DummyUndefinedMarkerString-DoNotUse>"


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 28
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static isUndefined(Ljava/lang/String;)Z
    .locals 1

    const-string v0, "<DummyUndefinedMarkerString-DoNotUse>"

    .line 34
    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method public static isUndefined(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 1

    .line 35
    const-class v0, Lgroovy/transform/Undefined$CLASS;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method

.method public static isUndefinedException(Lorg/codehaus/groovy/ast/ClassNode;)Z
    .locals 1

    .line 36
    const-class v0, Lgroovy/transform/Undefined$EXCEPTION;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    return p0
.end method
