.class public interface abstract annotation Lgroovy/transform/Canonical;
.super Ljava/lang/Object;
.source "Canonical.groovy"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/transform/Canonical$CollectorHelper;
    }
.end annotation

.annotation runtime Lgroovy/transform/AnnotationCollector;
    mode = .enum Lgroovy/transform/AnnotationCollectorMode;->PREFER_EXPLICIT_MERGED:Lgroovy/transform/AnnotationCollectorMode;
    serializeClass = Lgroovy/transform/Canonical$CollectorHelper;
    value = {
        Lgroovy/transform/ToString;,
        Lgroovy/transform/TupleConstructor;,
        Lgroovy/transform/EqualsAndHashCode;
    }
.end annotation
