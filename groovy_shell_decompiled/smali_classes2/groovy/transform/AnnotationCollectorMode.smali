.class public final enum Lgroovy/transform/AnnotationCollectorMode;
.super Ljava/lang/Enum;
.source "AnnotationCollectorMode.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovy/transform/AnnotationCollectorMode;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovy/transform/AnnotationCollectorMode;

.field public static final enum DUPLICATE:Lgroovy/transform/AnnotationCollectorMode;

.field public static final enum PREFER_COLLECTOR:Lgroovy/transform/AnnotationCollectorMode;

.field public static final enum PREFER_COLLECTOR_MERGED:Lgroovy/transform/AnnotationCollectorMode;

.field public static final enum PREFER_EXPLICIT:Lgroovy/transform/AnnotationCollectorMode;

.field public static final enum PREFER_EXPLICIT_MERGED:Lgroovy/transform/AnnotationCollectorMode;


# direct methods
.method private static synthetic $values()[Lgroovy/transform/AnnotationCollectorMode;
    .locals 3

    const/4 v0, 0x5

    new-array v0, v0, [Lgroovy/transform/AnnotationCollectorMode;

    .line 21
    sget-object v1, Lgroovy/transform/AnnotationCollectorMode;->DUPLICATE:Lgroovy/transform/AnnotationCollectorMode;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/transform/AnnotationCollectorMode;->PREFER_COLLECTOR:Lgroovy/transform/AnnotationCollectorMode;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/transform/AnnotationCollectorMode;->PREFER_EXPLICIT:Lgroovy/transform/AnnotationCollectorMode;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/transform/AnnotationCollectorMode;->PREFER_COLLECTOR_MERGED:Lgroovy/transform/AnnotationCollectorMode;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/transform/AnnotationCollectorMode;->PREFER_EXPLICIT_MERGED:Lgroovy/transform/AnnotationCollectorMode;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    .line 26
    new-instance v0, Lgroovy/transform/AnnotationCollectorMode;

    const-string v1, "DUPLICATE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovy/transform/AnnotationCollectorMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/transform/AnnotationCollectorMode;->DUPLICATE:Lgroovy/transform/AnnotationCollectorMode;

    .line 31
    new-instance v0, Lgroovy/transform/AnnotationCollectorMode;

    const-string v1, "PREFER_COLLECTOR"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lgroovy/transform/AnnotationCollectorMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/transform/AnnotationCollectorMode;->PREFER_COLLECTOR:Lgroovy/transform/AnnotationCollectorMode;

    .line 36
    new-instance v0, Lgroovy/transform/AnnotationCollectorMode;

    const-string v1, "PREFER_EXPLICIT"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lgroovy/transform/AnnotationCollectorMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/transform/AnnotationCollectorMode;->PREFER_EXPLICIT:Lgroovy/transform/AnnotationCollectorMode;

    .line 41
    new-instance v0, Lgroovy/transform/AnnotationCollectorMode;

    const-string v1, "PREFER_COLLECTOR_MERGED"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Lgroovy/transform/AnnotationCollectorMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/transform/AnnotationCollectorMode;->PREFER_COLLECTOR_MERGED:Lgroovy/transform/AnnotationCollectorMode;

    .line 46
    new-instance v0, Lgroovy/transform/AnnotationCollectorMode;

    const-string v1, "PREFER_EXPLICIT_MERGED"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Lgroovy/transform/AnnotationCollectorMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/transform/AnnotationCollectorMode;->PREFER_EXPLICIT_MERGED:Lgroovy/transform/AnnotationCollectorMode;

    .line 21
    invoke-static {}, Lgroovy/transform/AnnotationCollectorMode;->$values()[Lgroovy/transform/AnnotationCollectorMode;

    move-result-object v0

    sput-object v0, Lgroovy/transform/AnnotationCollectorMode;->$VALUES:[Lgroovy/transform/AnnotationCollectorMode;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 21
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovy/transform/AnnotationCollectorMode;
    .locals 1

    .line 21
    const-class v0, Lgroovy/transform/AnnotationCollectorMode;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovy/transform/AnnotationCollectorMode;

    return-object p0
.end method

.method public static values()[Lgroovy/transform/AnnotationCollectorMode;
    .locals 1

    .line 21
    sget-object v0, Lgroovy/transform/AnnotationCollectorMode;->$VALUES:[Lgroovy/transform/AnnotationCollectorMode;

    invoke-virtual {v0}, [Lgroovy/transform/AnnotationCollectorMode;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovy/transform/AnnotationCollectorMode;

    return-object v0
.end method
