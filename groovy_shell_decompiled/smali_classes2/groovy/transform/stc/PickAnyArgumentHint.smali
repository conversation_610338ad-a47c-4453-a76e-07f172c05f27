.class public Lgroovy/transform/stc/PickAnyArgumentHint;
.super Lgroovy/transform/stc/SingleSignatureClosureHint;
.source "PickAnyArgumentHint.java"


# instance fields
.field private final genericTypeIndex:I

.field private final parameterIndex:I


# direct methods
.method public constructor <init>()V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, -0x1

    .line 52
    invoke-direct {p0, v0, v1}, Lgroovy/transform/stc/PickAnyArgumentHint;-><init>(II)V

    return-void
.end method

.method public constructor <init>(II)V
    .locals 0

    .line 61
    invoke-direct {p0}, Lgroovy/transform/stc/SingleSignatureClosureHint;-><init>()V

    .line 62
    iput p1, p0, Lgroovy/transform/stc/PickAnyArgumentHint;->parameterIndex:I

    .line 63
    iput p2, p0, Lgroovy/transform/stc/PickAnyArgumentHint;->genericTypeIndex:I

    return-void
.end method


# virtual methods
.method public getParameterTypes(Lorg/codehaus/groovy/ast/MethodNode;[Ljava/lang/String;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/ast/ASTNode;)[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0

    .line 68
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p1

    iget p2, p0, Lgroovy/transform/stc/PickAnyArgumentHint;->parameterIndex:I

    aget-object p1, p1, p2

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/Parameter;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    .line 69
    iget p2, p0, Lgroovy/transform/stc/PickAnyArgumentHint;->genericTypeIndex:I

    if-ltz p2, :cond_0

    .line 70
    invoke-static {p1, p2}, Lgroovy/transform/stc/PickAnyArgumentHint;->pickGenericType(Lorg/codehaus/groovy/ast/ClassNode;I)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    :cond_0
    const/4 p2, 0x1

    new-array p2, p2, [Lorg/codehaus/groovy/ast/ClassNode;

    const/4 p3, 0x0

    aput-object p1, p2, p3

    return-object p2
.end method
