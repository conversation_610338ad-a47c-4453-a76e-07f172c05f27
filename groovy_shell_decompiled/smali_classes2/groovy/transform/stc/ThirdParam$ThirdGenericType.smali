.class public Lgroovy/transform/stc/ThirdParam$ThirdGenericType;
.super Lgroovy/transform/stc/PickAnyArgumentHint;
.source "ThirdParam.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/transform/stc/ThirdParam;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ThirdGenericType"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x2

    .line 73
    invoke-direct {p0, v0, v0}, Lgroovy/transform/stc/PickAnyArgumentHint;-><init>(II)V

    return-void
.end method
