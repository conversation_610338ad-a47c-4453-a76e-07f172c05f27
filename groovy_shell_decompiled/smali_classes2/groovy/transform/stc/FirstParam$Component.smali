.class public Lgroovy/transform/stc/FirstParam$Component;
.super Lgroovy/transform/stc/FirstParam;
.source "FirstParam.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/transform/stc/FirstParam;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Component"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 81
    invoke-direct {p0}, Lgroovy/transform/stc/FirstParam;-><init>()V

    return-void
.end method


# virtual methods
.method public getParameterTypes(Lorg/codehaus/groovy/ast/MethodNode;[Ljava/lang/String;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/ast/ASTNode;)[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0

    .line 84
    invoke-super/range {p0 .. p5}, Lgroovy/transform/stc/FirstParam;->getParameterTypes(Lorg/codehaus/groovy/ast/MethodNode;[Ljava/lang/String;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/ast/ASTNode;)[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    const/4 p2, 0x0

    .line 85
    aget-object p3, p1, p2

    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/ClassNode;->getComponentType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p3

    aput-object p3, p1, p2

    return-object p1
.end method
