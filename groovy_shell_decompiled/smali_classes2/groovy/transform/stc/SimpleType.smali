.class public Lgroovy/transform/stc/SimpleType;
.super Lgroovy/transform/stc/SingleSignatureClosureHint;
.source "SimpleType.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 27
    invoke-direct {p0}, Lgroovy/transform/stc/SingleSignatureClosureHint;-><init>()V

    return-void
.end method


# virtual methods
.method public getParameterTypes(Lorg/codehaus/groovy/ast/MethodNode;[Ljava/lang/String;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/ast/ASTNode;)[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 2

    .line 30
    array-length p1, p2

    new-array p5, p1, [Lorg/codehaus/groovy/ast/ClassNode;

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p1, :cond_0

    .line 32
    aget-object v1, p2, v0

    invoke-virtual {p0, p3, p4, v1}, Lgroovy/transform/stc/SimpleType;->findClassNode(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    aput-object v1, p5, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-object p5
.end method
