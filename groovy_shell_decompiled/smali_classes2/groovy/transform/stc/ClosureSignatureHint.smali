.class public abstract Lgroovy/transform/stc/ClosureSignatureHint;
.super Ljava/lang/Object;
.source "ClosureSignatureHint.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 53
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static pickGenericType(Lorg/codehaus/groovy/ast/ClassNode;I)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 62
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/ClassNode;->getGenericsTypes()[Lorg/codehaus/groovy/ast/GenericsType;

    move-result-object p0

    if-eqz p0, :cond_1

    .line 63
    array-length v0, p0

    if-ge v0, p1, :cond_0

    goto :goto_0

    .line 66
    :cond_0
    aget-object p0, p0, p1

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/GenericsType;->getType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    return-object p0

    .line 64
    :cond_1
    :goto_0
    sget-object p0, Lorg/codehaus/groovy/ast/ClassHelper;->OBJECT_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    return-object p0
.end method

.method public static pickGenericType(Lorg/codehaus/groovy/ast/MethodNode;II)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0

    .line 77
    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/MethodNode;->getParameters()[Lorg/codehaus/groovy/ast/Parameter;

    move-result-object p0

    .line 78
    aget-object p0, p0, p1

    invoke-virtual {p0}, Lorg/codehaus/groovy/ast/Parameter;->getOriginType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    .line 79
    invoke-static {p0, p2}, Lgroovy/transform/stc/ClosureSignatureHint;->pickGenericType(Lorg/codehaus/groovy/ast/ClassNode;I)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method protected findClassNode(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;
    .locals 2

    const-string v0, "[]"

    .line 130
    invoke-virtual {p3, v0}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 131
    invoke-virtual {p3}, Ljava/lang/String;->length()I

    move-result v0

    add-int/lit8 v0, v0, -0x2

    invoke-virtual {p3, v1, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p0, p1, p2, p3}, Lgroovy/transform/stc/ClosureSignatureHint;->findClassNode(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->makeArray()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    return-object p1

    .line 133
    :cond_0
    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/control/CompilationUnit;->getClassNode(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    if-nez p2, :cond_1

    .line 136
    :try_start_0
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/SourceUnit;->getClassLoader()Lgroovy/lang/GroovyClassLoader;

    move-result-object p1

    invoke-static {p3, v1, p1}, Ljava/lang/Class;->forName(Ljava/lang/String;ZLjava/lang/ClassLoader;)Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 138
    :catch_0
    invoke-static {p3}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p2

    :cond_1
    :goto_0
    return-object p2
.end method

.method public abstract getClosureSignatures(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;[Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Lorg/codehaus/groovy/control/SourceUnit;",
            "Lorg/codehaus/groovy/control/CompilationUnit;",
            "[",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/ASTNode;",
            ")",
            "Ljava/util/List<",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation
.end method
