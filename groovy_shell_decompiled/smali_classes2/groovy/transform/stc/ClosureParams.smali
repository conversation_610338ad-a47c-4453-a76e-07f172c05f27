.class public interface abstract annotation Lgroovy/transform/stc/ClosureParams;
.super Ljava/lang/Object;
.source "ClosureParams.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/stc/ClosureParams;
        conflictResolutionStrategy = Lgroovy/transform/stc/ClosureSignatureConflictResolver;
        options = {}
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->RUNTIME:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->PARAMETER:Ljava/lang/annotation/ElementType;
    }
.end annotation


# virtual methods
.method public abstract conflictResolutionStrategy()Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "+",
            "Lgroovy/transform/stc/ClosureSignatureConflictResolver;",
            ">;"
        }
    .end annotation
.end method

.method public abstract options()[Ljava/lang/String;
.end method

.method public abstract value()Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "+",
            "Lgroovy/transform/stc/ClosureSignatureHint;",
            ">;"
        }
    .end annotation
.end method
