.class public Lgroovy/transform/stc/SecondParam;
.super Lgroovy/transform/stc/PickAnyArgumentHint;
.source "SecondParam.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/transform/stc/SecondParam$Component;,
        Lgroovy/transform/stc/SecondParam$ThirdGenericType;,
        Lgroovy/transform/stc/SecondParam$SecondGenericType;,
        Lgroovy/transform/stc/SecondParam$FirstGenericType;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 2

    const/4 v0, 0x1

    const/4 v1, -0x1

    .line 37
    invoke-direct {p0, v0, v1}, Lgroovy/transform/stc/PickAnyArgumentHint;-><init>(II)V

    return-void
.end method
