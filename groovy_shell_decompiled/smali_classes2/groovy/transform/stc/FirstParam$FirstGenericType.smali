.class public Lgroovy/transform/stc/FirstParam$FirstGenericType;
.super Lgroovy/transform/stc/PickAnyArgumentHint;
.source "FirstParam.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/transform/stc/FirstParam;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "FirstGenericType"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 48
    invoke-direct {p0, v0, v0}, Lgroovy/transform/stc/PickAnyArgumentHint;-><init>(II)V

    return-void
.end method
