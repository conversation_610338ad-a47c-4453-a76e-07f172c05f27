.class public Lgroovy/transform/stc/SecondParam$SecondGenericType;
.super Lgroovy/transform/stc/PickAnyArgumentHint;
.source "SecondParam.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/transform/stc/SecondParam;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "SecondGenericType"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x1

    .line 60
    invoke-direct {p0, v0, v0}, Lgroovy/transform/stc/PickAnyArgumentHint;-><init>(II)V

    return-void
.end method
