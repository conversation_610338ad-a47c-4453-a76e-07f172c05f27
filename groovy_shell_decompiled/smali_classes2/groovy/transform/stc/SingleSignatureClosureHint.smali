.class public abstract Lgroovy/transform/stc/SingleSignatureClosureHint;
.super Lgroovy/transform/stc/ClosureSignatureHint;
.source "SingleSignatureClosureHint.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 36
    invoke-direct {p0}, Lgroovy/transform/stc/ClosureSignatureHint;-><init>()V

    return-void
.end method


# virtual methods
.method public getClosureSignatures(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;[Ljava/lang/String;Lorg/codehaus/groovy/ast/ASTNode;)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Lorg/codehaus/groovy/control/SourceUnit;",
            "Lorg/codehaus/groovy/control/CompilationUnit;",
            "[",
            "Ljava/lang/String;",
            "Lorg/codehaus/groovy/ast/ASTNode;",
            ")",
            "Ljava/util/List<",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    move-object v0, p0

    move-object v1, p1

    move-object v2, p4

    move-object v3, p2

    move-object v4, p3

    move-object v5, p5

    .line 41
    invoke-virtual/range {v0 .. v5}, Lgroovy/transform/stc/SingleSignatureClosureHint;->getParameterTypes(Lorg/codehaus/groovy/ast/MethodNode;[Ljava/lang/String;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/ast/ASTNode;)[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    invoke-static {p1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public abstract getParameterTypes(Lorg/codehaus/groovy/ast/MethodNode;[Ljava/lang/String;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/ast/ASTNode;)[Lorg/codehaus/groovy/ast/ClassNode;
.end method
