.class public Lgroovy/transform/stc/PickFirstResolver;
.super Lgroovy/transform/stc/ClosureSignatureConflictResolver;
.source "PickFirstResolver.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 39
    invoke-direct {p0}, Lgroovy/transform/stc/ClosureSignatureConflictResolver;-><init>()V

    return-void
.end method


# virtual methods
.method public resolve(Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/ClosureExpression;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;[Ljava/lang/String;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            "Lorg/codehaus/groovy/ast/expr/ClosureExpression;",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Lorg/codehaus/groovy/control/SourceUnit;",
            "Lorg/codehaus/groovy/control/CompilationUnit;",
            "[",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    const/4 p2, 0x0

    .line 43
    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Lorg/codehaus/groovy/ast/ClassNode;

    invoke-static {p1}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method
