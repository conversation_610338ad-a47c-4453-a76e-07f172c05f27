.class public Lgroovy/transform/stc/SecondParam$FirstGenericType;
.super Lgroovy/transform/stc/PickAnyArgumentHint;
.source "SecondParam.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/transform/stc/SecondParam;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "FirstGenericType"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 2

    const/4 v0, 0x1

    const/4 v1, 0x0

    .line 48
    invoke-direct {p0, v0, v1}, Lgroovy/transform/stc/PickAnyArgumentHint;-><init>(II)V

    return-void
.end method
