.class public Lgroovy/transform/stc/ThirdParam$Component;
.super Lgroovy/transform/stc/ThirdParam;
.source "ThirdParam.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/transform/stc/ThirdParam;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Component"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 82
    invoke-direct {p0}, Lgroovy/transform/stc/ThirdParam;-><init>()V

    return-void
.end method


# virtual methods
.method public getParameterTypes(Lorg/codehaus/groovy/ast/MethodNode;[Ljava/lang/String;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/ast/ASTNode;)[Lorg/codehaus/groovy/ast/ClassNode;
    .locals 0

    .line 85
    invoke-super/range {p0 .. p5}, Lgroovy/transform/stc/ThirdParam;->getParameterTypes(Lorg/codehaus/groovy/ast/MethodNode;[Ljava/lang/String;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/ast/ASTNode;)[Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p1

    const/4 p2, 0x0

    .line 86
    aget-object p3, p1, p2

    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/ClassNode;->getComponentType()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object p3

    aput-object p3, p1, p2

    return-object p1
.end method
