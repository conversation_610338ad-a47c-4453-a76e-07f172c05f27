.class public Lgroovy/transform/stc/FirstParam;
.super Lgroovy/transform/stc/PickAnyArgumentHint;
.source "FirstParam.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/transform/stc/FirstParam$Component;,
        Lgroovy/transform/stc/FirstParam$ThirdGenericType;,
        Lgroovy/transform/stc/FirstParam$SecondGenericType;,
        Lgroovy/transform/stc/FirstParam$FirstGenericType;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, -0x1

    .line 37
    invoke-direct {p0, v0, v1}, Lgroovy/transform/stc/PickAnyArgumentHint;-><init>(II)V

    return-void
.end method
