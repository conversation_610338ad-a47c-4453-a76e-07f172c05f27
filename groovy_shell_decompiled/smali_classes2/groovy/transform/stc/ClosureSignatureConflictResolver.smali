.class public Lgroovy/transform/stc/ClosureSignatureConflictResolver;
.super Ljava/lang/Object;
.source "ClosureSignatureConflictResolver.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 36
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public resolve(Ljava/util/List;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/ClosureExpression;Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/control/CompilationUnit;[Ljava/lang/String;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            "Lorg/codehaus/groovy/ast/expr/Expression;",
            "Lorg/codehaus/groovy/ast/expr/ClosureExpression;",
            "Lorg/codehaus/groovy/ast/MethodNode;",
            "Lorg/codehaus/groovy/control/SourceUnit;",
            "Lorg/codehaus/groovy/control/CompilationUnit;",
            "[",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "[",
            "Lorg/codehaus/groovy/ast/ClassNode;",
            ">;"
        }
    .end annotation

    return-object p1
.end method
