.class final Lgroovy/transform/stc/MapEntryOrKeyValue$Options;
.super Ljava/lang/Object;
.source "MapEntryOrKeyValue.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/transform/stc/MapEntryOrKeyValue;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "Options"
.end annotation


# instance fields
.field final generateIndex:Z

.field final parameterIndex:I


# direct methods
.method private constructor <init>(IZ)V
    .locals 0

    .line 92
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 93
    iput p1, p0, Lgroovy/transform/stc/MapEntryOrKeyValue$Options;->parameterIndex:I

    .line 94
    iput-boolean p2, p0, Lgroovy/transform/stc/MapEntryOrKeyValue$Options;->generateIndex:Z

    return-void
.end method

.method static parse(Lorg/codehaus/groovy/ast/MethodNode;Lorg/codehaus/groovy/ast/ASTNode;[Ljava/lang/String;)Lgroovy/transform/stc/MapEntryOrKeyValue$Options;
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lgroovy/transform/stc/IncorrectTypeHintException;
        }
    .end annotation

    .line 100
    array-length v0, p2

    const/4 v1, 0x0

    move v2, v1

    move v3, v2

    move v4, v3

    :goto_0
    if-ge v2, v0, :cond_3

    aget-object v5, p2, v2

    const-string v6, "="

    .line 101
    invoke-virtual {v5, v6}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v5

    .line 102
    array-length v6, v5

    const/4 v7, 0x2

    if-ne v6, v7, :cond_2

    .line 103
    aget-object v6, v5, v1

    const/4 v7, 0x1

    .line 104
    aget-object v5, v5, v7

    const-string v7, "argNum"

    .line 105
    invoke-virtual {v7, v6}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_0

    .line 106
    invoke-static {v5}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v3

    goto :goto_1

    :cond_0
    const-string v4, "index"

    .line 107
    invoke-virtual {v4, v6}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_1

    .line 108
    invoke-static {v5}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v4

    :goto_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 110
    :cond_1
    new-instance p2, Lgroovy/transform/stc/IncorrectTypeHintException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Unrecognized option: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ASTNode;->getLineNumber()I

    move-result v1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ASTNode;->getColumnNumber()I

    move-result p1

    invoke-direct {p2, p0, v0, v1, p1}, Lgroovy/transform/stc/IncorrectTypeHintException;-><init>(Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;II)V

    throw p2

    .line 113
    :cond_2
    new-instance p2, Lgroovy/transform/stc/IncorrectTypeHintException;

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ASTNode;->getLineNumber()I

    move-result v0

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ASTNode;->getColumnNumber()I

    move-result p1

    const-string v1, "Incorrect option format. Should be argNum=<num> or index=<boolean> "

    invoke-direct {p2, p0, v1, v0, p1}, Lgroovy/transform/stc/IncorrectTypeHintException;-><init>(Lorg/codehaus/groovy/ast/MethodNode;Ljava/lang/String;II)V

    throw p2

    .line 116
    :cond_3
    new-instance p0, Lgroovy/transform/stc/MapEntryOrKeyValue$Options;

    invoke-direct {p0, v3, v4}, Lgroovy/transform/stc/MapEntryOrKeyValue$Options;-><init>(IZ)V

    return-object p0
.end method
