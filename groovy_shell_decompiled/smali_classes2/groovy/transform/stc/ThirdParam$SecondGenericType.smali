.class public Lgroovy/transform/stc/ThirdParam$SecondGenericType;
.super Lgroovy/transform/stc/PickAnyArgumentHint;
.source "ThirdParam.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/transform/stc/ThirdParam;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "SecondGenericType"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 2

    const/4 v0, 0x2

    const/4 v1, 0x1

    .line 61
    invoke-direct {p0, v0, v1}, Lgroovy/transform/stc/PickAnyArgumentHint;-><init>(II)V

    return-void
.end method
