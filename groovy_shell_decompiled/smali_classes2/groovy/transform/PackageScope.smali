.class public interface abstract annotation Lgroovy/transform/PackageScope;
.super Ljava/lang/Object;
.source "PackageScope.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/PackageScope;
        value = {
            .enum Lgroovy/transform/PackageScopeTarget;->CLASS:Lgroovy/transform/PackageScopeTarget;
        }
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->METHOD:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->CONSTRUCTOR:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->FIELD:Ljava/lang/annotation/ElementType;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformationClass;
    value = {
        "org.codehaus.groovy.transform.PackageScopeASTTransformation"
    }
.end annotation


# virtual methods
.method public abstract value()[Lgroovy/transform/PackageScopeTarget;
.end method
