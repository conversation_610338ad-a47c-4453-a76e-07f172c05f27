.class public interface abstract annotation Lgroovy/transform/ThreadInterrupt;
.super Ljava/lang/Object;
.source "ThreadInterrupt.groovy"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/ThreadInterrupt;
        applyToAllClasses = true
        applyToAllMembers = true
        checkOnMethodStart = true
        thrown = Ljava/lang/InterruptedException;
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->PACKAGE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->METHOD:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->FIELD:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->LOCAL_VARIABLE:Ljava/lang/annotation/ElementType;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformationClass;
    value = {
        "org.codehaus.groovy.transform.ThreadInterruptibleASTTransformation"
    }
.end annotation


# virtual methods
.method public abstract applyToAllClasses()Z
.end method

.method public abstract applyToAllMembers()Z
.end method

.method public abstract checkOnMethodStart()Z
.end method

.method public abstract thrown()Ljava/lang/Class;
.end method
