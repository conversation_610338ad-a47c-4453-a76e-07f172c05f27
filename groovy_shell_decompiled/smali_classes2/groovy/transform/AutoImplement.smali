.class public interface abstract annotation Lgroovy/transform/AutoImplement;
.super Ljava/lang/Object;
.source "AutoImplement.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/AutoImplement;
        code = Lgroovy/transform/Undefined$CLASS;
        exception = Lgroovy/transform/Undefined$EXCEPTION;
        message = "<DummyUndefinedMarkerString-DoNotUse>"
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformationClass;
    value = {
        "org.codehaus.groovy.transform.AutoImplementASTTransformation"
    }
.end annotation


# virtual methods
.method public abstract code()Ljava/lang/Class;
.end method

.method public abstract exception()Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "+",
            "Ljava/lang/RuntimeException;",
            ">;"
        }
    .end annotation
.end method

.method public abstract message()Ljava/lang/String;
.end method
