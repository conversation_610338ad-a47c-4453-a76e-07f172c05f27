.class public interface abstract annotation Lgroovy/transform/WithReadLock;
.super Ljava/lang/Object;
.source "WithReadLock.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/transform/WithReadLock;
        value = ""
    .end subannotation
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->METHOD:Ljava/lang/annotation/ElementType;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformationClass;
    value = {
        "org.codehaus.groovy.transform.ReadWriteLockASTTransformation"
    }
.end annotation


# virtual methods
.method public abstract value()Ljava/lang/String;
.end method
