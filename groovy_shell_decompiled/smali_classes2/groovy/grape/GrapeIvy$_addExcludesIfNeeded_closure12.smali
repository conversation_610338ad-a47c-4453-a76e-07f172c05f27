.class public final Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;
.super Lgroovy/lang/Closure;
.source "GrapeIvy.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/grape/GrapeIvy;->addExcludesIfNeeded(Ljava/util/Map;Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_addExcludesIfNeeded_closure12"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic md:Lgroovy/lang/Reference;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;->md:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Ljava/util/Map;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-virtual {p0, p1}, Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;->doCall(Ljava/util/Map;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Ljava/util/Map;)Ljava/lang/Object;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 560
    new-instance v0, Lorg/apache/ivy/core/module/descriptor/DefaultExcludeRule;

    .line 561
    new-instance v1, Lorg/apache/ivy/core/module/id/ArtifactId;

    .line 562
    new-instance v2, Lorg/apache/ivy/core/module/id/ModuleId;

    const-string v3, "group"

    invoke-interface {p1, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    move-object v4, v3

    check-cast v4, Ljava/lang/String;

    const-string v4, "module"

    invoke-interface {p1, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v4, p1

    check-cast v4, Ljava/lang/String;

    invoke-direct {v2, v3, p1}, Lorg/apache/ivy/core/module/id/ModuleId;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    sget-object p1, Lorg/apache/ivy/plugins/matcher/PatternMatcher;->ANY_EXPRESSION:Ljava/lang/String;

    sget-object v3, Lorg/apache/ivy/plugins/matcher/PatternMatcher;->ANY_EXPRESSION:Ljava/lang/String;

    sget-object v4, Lorg/apache/ivy/plugins/matcher/PatternMatcher;->ANY_EXPRESSION:Ljava/lang/String;

    invoke-direct {v1, v2, p1, v3, v4}, Lorg/apache/ivy/core/module/id/ArtifactId;-><init>(Lorg/apache/ivy/core/module/id/ModuleId;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    sget-object p1, Lorg/apache/ivy/plugins/matcher/ExactPatternMatcher;->INSTANCE:Lorg/apache/ivy/plugins/matcher/ExactPatternMatcher;

    const/4 v2, 0x0

    invoke-direct {v0, v1, p1, v2}, Lorg/apache/ivy/core/module/descriptor/DefaultExcludeRule;-><init>(Lorg/apache/ivy/core/module/id/ArtifactId;Lorg/apache/ivy/plugins/matcher/PatternMatcher;Ljava/util/Map;)V

    .line 568
    move-object p1, v0

    check-cast p1, Lorg/apache/ivy/core/module/descriptor/DefaultExcludeRule;

    const-string p1, "default"

    invoke-virtual {v0, p1}, Lorg/apache/ivy/core/module/descriptor/DefaultExcludeRule;->addConfiguration(Ljava/lang/String;)V

    .line 569
    iget-object p1, p0, Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;->md:Lgroovy/lang/Reference;

    invoke-virtual {p1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-virtual {p1, v0}, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;->addExcludeRule(Lorg/apache/ivy/core/module/descriptor/ExcludeRule;)V

    return-object v2
.end method

.method public getMd()Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;->md:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    return-object v0
.end method
