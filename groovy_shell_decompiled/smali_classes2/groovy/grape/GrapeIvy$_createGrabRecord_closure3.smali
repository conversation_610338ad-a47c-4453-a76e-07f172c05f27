.class public final Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;
.super Lgroovy/lang/Closure;
.source "GrapeIvy.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/grape/GrapeIvy;->createGrabRecord(Ljava/util/Map;)Lgroovy/grape/IvyGrabRecord;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_createGrabRecord_closure3"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic keys:Lgroovy/lang/Reference;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;->keys:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;->doCall(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public call(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-virtual {p0, p1}, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;->doCall(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    const/4 v0, 0x0

    .line 219
    invoke-static {v0}, Lgroovy/grape/GrapeIvy;->pfaccess$1(Lgroovy/grape/GrapeIvy;)Ljava/util/Map;

    move-result-object v1

    invoke-static {v1, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->getAt(Ljava/util/Map;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    const-class v2, Ljava/util/Set;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Set;

    .line 220
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    const/4 v3, 0x0

    const/4 v4, 0x1

    if-eqz v2, :cond_0

    iget-object v2, p0, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;->keys:Lgroovy/lang/Reference;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    const-class v5, Ljava/lang/Iterable;

    invoke-static {v2, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Iterable;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->disjoint(Ljava/lang/Iterable;Ljava/lang/Iterable;)Z

    move-result v2

    xor-int/2addr v2, v4

    if-eqz v2, :cond_0

    move v2, v4

    goto :goto_0

    :cond_0
    move v2, v3

    :goto_0
    if-nez v2, :cond_1

    return-object v0

    .line 221
    :cond_1
    new-instance v0, Ljava/lang/RuntimeException;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v4, v4, [Ljava/lang/Object;

    iget-object v5, p0, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;->keys:Lgroovy/lang/Reference;

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    const-class v6, Ljava/util/Set;

    invoke-static {v5, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/util/Set;

    invoke-static {v5, v1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->intersect(Ljava/util/Set;Ljava/lang/Iterable;)Ljava/util/Set;

    move-result-object v1

    invoke-static {v1, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->plus(Ljava/util/Set;Ljava/lang/Object;)Ljava/util/Set;

    move-result-object p1

    aput-object p1, v4, v3

    const-string p1, "Grab: mutually exclusive arguments: "

    const-string v1, ""

    filled-new-array {p1, v1}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v4, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    invoke-direct {v0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    check-cast v0, Ljava/lang/Throwable;

    throw v0
.end method

.method public getKeys()Ljava/util/Set;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;->keys:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Ljava/util/Set;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Set;

    return-object v0
.end method
