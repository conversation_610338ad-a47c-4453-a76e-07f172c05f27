.class public final Lgroovy/grape/GrapeIvy$_addIvyListener_closure10$_closure20;
.super Lgroovy/lang/Closure;
.source "GrapeIvy.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/grape/GrapeIvy$_addIvyListener_closure10;->doCall(Ljava/lang/Object;)[Lorg/apache/ivy/util/extendable/ExtendableItem;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_closure20"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10$_closure20;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10$_closure20;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10$_closure20;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10$_closure20;->doCall(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public call(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-virtual {p0, p1}, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10$_closure20;->doCall(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public doCall()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10$_closure20;->doCall(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public doCall(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 499
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    .line 500
    move-object v0, p0

    check-cast v0, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10$_closure20;

    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10$_closure20;->getThisObject()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lgroovy/grape/GrapeIvy;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovy/grape/GrapeIvy;

    invoke-virtual {v0}, Lgroovy/grape/GrapeIvy;->getResolvedDependencies()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 501
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v2, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object p1, v3, v4

    const-string p1, "Resolving "

    const-string v4, ""

    filled-new-array {p1, v4}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v2, v3, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v0, v2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->println(Ljava/io/PrintStream;Ljava/lang/Object;)V

    :cond_0
    return-object v1
.end method
