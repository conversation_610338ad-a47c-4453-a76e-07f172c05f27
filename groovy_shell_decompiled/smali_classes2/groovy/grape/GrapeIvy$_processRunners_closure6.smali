.class public final Lgroovy/grape/GrapeIvy$_processRunners_closure6;
.super Lgroovy/lang/Closure;
.source "GrapeIvy.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/grape/GrapeIvy;->processRunners(Ljava/io/InputStream;Ljava/lang/String;Ljava/lang/ClassLoader;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_processRunners_closure6"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic loader:Lgroovy/lang/Reference;

.field private synthetic name:Lgroovy/lang/Reference;

.field private synthetic registry:Lgroovy/lang/Reference;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->registry:Lgroovy/lang/Reference;

    iput-object p4, p0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->name:Lgroovy/lang/Reference;

    iput-object p5, p0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->loader:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/grape/GrapeIvy$_processRunners_closure6;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Ljava/lang/String;)Lorg/apache/groovy/plugin/GroovyRunner;
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-virtual {p0, p1}, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->doCall(Ljava/lang/String;)Lorg/apache/groovy/plugin/GroovyRunner;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Ljava/lang/String;)Lorg/apache/groovy/plugin/GroovyRunner;
    .locals 8

    .line 410
    invoke-virtual {p1}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    const/4 v1, 0x1

    xor-int/2addr v0, v1

    const/4 v2, 0x0

    if-eqz v0, :cond_0

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->getAt(Ljava/lang/String;I)Ljava/lang/String;

    move-result-object v0

    const/16 v3, 0x23

    invoke-static {v3}, Ljava/lang/Character;->valueOf(C)Ljava/lang/Character;

    move-result-object v3

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    move v0, v1

    goto :goto_0

    :cond_0
    move v0, v2

    :goto_0
    if-eqz v0, :cond_1

    .line 412
    :try_start_0
    iget-object v0, p0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->loader:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ClassLoader;

    invoke-virtual {v0, p1}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->newInstance(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    const-class v3, Lorg/apache/groovy/plugin/GroovyRunner;

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/apache/groovy/plugin/GroovyRunner;

    const-class v3, Lgroovy/grape/GrapeIvy$_processRunners_closure6;

    iget-object v4, p0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->registry:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    const-string v5, "putAt"

    move-object v6, v5

    check-cast v6, Ljava/lang/String;

    const/4 v6, 0x2

    new-array v6, v6, [Ljava/lang/Object;

    iget-object v7, p0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->name:Lgroovy/lang/Reference;

    invoke-virtual {v7}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    aput-object v7, v6, v2

    aput-object v0, v6, v1

    invoke-static {v3, v4, v5, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->invokeMethodN(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    return-object v0

    :catchall_0
    move-exception p1

    goto :goto_1

    :catch_0
    move-exception v0

    .line 414
    :try_start_1
    new-instance v3, Ljava/lang/IllegalStateException;

    new-instance v4, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v1, v1, [Ljava/lang/Object;

    aput-object p1, v1, v2

    const-string p1, "Error registering runner class \'"

    const-string v2, "\'"

    filled-new-array {p1, v2}, [Ljava/lang/String;

    move-result-object p1

    invoke-direct {v4, v1, p1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    move-object v1, p1

    check-cast v1, Ljava/lang/String;

    invoke-direct {v3, p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    check-cast v3, Ljava/lang/Throwable;

    throw v3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 415
    :goto_1
    throw p1

    :cond_1
    const/4 p1, 0x0

    const-class v0, Lorg/apache/groovy/plugin/GroovyRunner;

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/apache/groovy/plugin/GroovyRunner;

    return-object p1
.end method

.method public getLoader()Ljava/lang/ClassLoader;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->loader:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Ljava/lang/ClassLoader;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ClassLoader;

    return-object v0
.end method

.method public getName()Ljava/lang/String;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->name:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Ljava/lang/String;

    return-object v0
.end method

.method public getRegistry()Lorg/apache/groovy/plugin/GroovyRunnerRegistry;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy$_processRunners_closure6;->registry:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lorg/apache/groovy/plugin/GroovyRunnerRegistry;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/apache/groovy/plugin/GroovyRunnerRegistry;

    return-object v0
.end method
