.class public interface abstract Lgroovy/grape/GrapeEngine;
.super Ljava/lang/Object;
.source "GrapeEngine.java"


# static fields
.field public static final CALLEE_DEPTH:Ljava/lang/String; = "calleeDepth"

.field public static final DEFAULT_CALLEE_DEPTH:I = 0x3


# virtual methods
.method public abstract addResolver(Ljava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract enumerateGrapes()Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;>;"
        }
    .end annotation
.end method

.method public abstract grab(Ljava/lang/String;)Ljava/lang/Object;
.end method

.method public abstract grab(Ljava/util/Map;)Ljava/lang/Object;
.end method

.method public varargs abstract grab(Ljava/util/Map;[Ljava/util/Map;)Ljava/lang/Object;
.end method

.method public abstract listDependencies(Ljava/lang/ClassLoader;)[Ljava/util/Map;
.end method

.method public varargs abstract resolve(Ljava/util/Map;Ljava/util/List;[Ljava/util/Map;)[Ljava/net/URI;
.end method

.method public varargs abstract resolve(Ljava/util/Map;[Ljava/util/Map;)[Ljava/net/URI;
.end method
