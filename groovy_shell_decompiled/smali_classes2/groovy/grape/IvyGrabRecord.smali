.class public Lgroovy/grape/IvyGrabRecord;
.super Ljava/lang/Object;
.source "GrapeIvy.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation runtime Lgroovy/transform/EqualsAndHashCode;
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private changing:Z

.field private classifier:Ljava/lang/String;

.field private conf:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private ext:Ljava/lang/String;

.field private force:Z

.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private mrid:Lorg/apache/ivy/core/module/id/ModuleRevisionId;

.field private transitive:Z

.field private type:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/grape/IvyGrabRecord;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/grape/IvyGrabRecord;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/grape/IvyGrabRecord;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/grape/IvyGrabRecord;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public canEqual(Ljava/lang/Object;)Z
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    instance-of p1, p1, Lgroovy/grape/IvyGrabRecord;

    return p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    const/4 v0, 0x0

    const/4 v1, 0x1

    if-nez p1, :cond_0

    move v2, v1

    goto :goto_0

    :cond_0
    move v2, v0

    :goto_0
    if-eqz v2, :cond_1

    return v0

    :cond_1
    if-ne p0, p1, :cond_2

    move v2, v1

    goto :goto_1

    :cond_2
    move v2, v0

    :goto_1
    if-eqz v2, :cond_3

    return v1

    :cond_3
    instance-of v2, p1, Lgroovy/grape/IvyGrabRecord;

    xor-int/2addr v2, v1

    if-eqz v2, :cond_4

    return v0

    :cond_4
    check-cast p1, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {p1, p0}, Lgroovy/grape/IvyGrabRecord;->canEqual(Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v2, v1

    if-eqz v2, :cond_5

    return v0

    :cond_5
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v2

    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v2, v1

    if-eqz v2, :cond_6

    return v0

    :cond_6
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getConf()Ljava/util/List;

    move-result-object v2

    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getConf()Ljava/util/List;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v2, v1

    if-eqz v2, :cond_7

    return v0

    :cond_7
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getExt()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getExt()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v2, v1

    if-eqz v2, :cond_8

    return v0

    :cond_8
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getType()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getType()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v2, v1

    if-eqz v2, :cond_9

    return v0

    :cond_9
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getClassifier()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getClassifier()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v2, v1

    if-eqz v2, :cond_a

    return v0

    :cond_a
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->isForce()Z

    move-result v2

    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->isForce()Z

    move-result v3

    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v2, v1

    if-eqz v2, :cond_b

    return v0

    :cond_b
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->isChanging()Z

    move-result v2

    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->isChanging()Z

    move-result v3

    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v2, v1

    if-eqz v2, :cond_c

    return v0

    :cond_c
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->isTransitive()Z

    move-result v2

    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->isTransitive()Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-static {v2, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    xor-int/2addr p1, v1

    if-eqz p1, :cond_d

    return v0

    :cond_d
    return v1
.end method

.method public getChanging()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/grape/IvyGrabRecord;->changing:Z

    return v0
.end method

.method public getClassifier()Ljava/lang/String;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/IvyGrabRecord;->classifier:Ljava/lang/String;

    return-object v0
.end method

.method public getConf()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/IvyGrabRecord;->conf:Ljava/util/List;

    return-object v0
.end method

.method public getExt()Ljava/lang/String;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/IvyGrabRecord;->ext:Ljava/lang/String;

    return-object v0
.end method

.method public getForce()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/grape/IvyGrabRecord;->force:Z

    return v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/IvyGrabRecord;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/grape/IvyGrabRecord;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/IvyGrabRecord;->mrid:Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    return-object v0
.end method

.method public getTransitive()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/grape/IvyGrabRecord;->transitive:Z

    return v0
.end method

.method public getType()Ljava/lang/String;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/IvyGrabRecord;->type:Ljava/lang/String;

    return-object v0
.end method

.method public hashCode()I
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lorg/codehaus/groovy/util/HashCodeHelper;->initHash()I

    move-result v0

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v1

    invoke-static {v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotIdentical(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/util/HashCodeHelper;->updateHash(ILjava/lang/Object;)I

    move-result v0

    :cond_0
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getConf()Ljava/util/List;

    move-result-object v1

    invoke-static {v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotIdentical(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getConf()Ljava/util/List;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/util/HashCodeHelper;->updateHash(ILjava/lang/Object;)I

    move-result v0

    :cond_1
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getExt()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotIdentical(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getExt()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/util/HashCodeHelper;->updateHash(ILjava/lang/Object;)I

    move-result v0

    :cond_2
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getType()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotIdentical(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getType()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/util/HashCodeHelper;->updateHash(ILjava/lang/Object;)I

    move-result v0

    :cond_3
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getClassifier()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotIdentical(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->getClassifier()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/util/HashCodeHelper;->updateHash(ILjava/lang/Object;)I

    move-result v0

    :cond_4
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->isForce()Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    invoke-static {v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotIdentical(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->isForce()Z

    move-result v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/util/HashCodeHelper;->updateHash(IZ)I

    move-result v0

    :cond_5
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->isChanging()Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    invoke-static {v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotIdentical(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->isChanging()Z

    move-result v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/util/HashCodeHelper;->updateHash(IZ)I

    move-result v0

    :cond_6
    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->isTransitive()Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    invoke-static {v1, p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotIdentical(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_7

    invoke-virtual {p0}, Lgroovy/grape/IvyGrabRecord;->isTransitive()Z

    move-result v1

    invoke-static {v0, v1}, Lorg/codehaus/groovy/util/HashCodeHelper;->updateHash(IZ)I

    move-result v0

    :cond_7
    return v0
.end method

.method public isChanging()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/grape/IvyGrabRecord;->changing:Z

    return v0
.end method

.method public isForce()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/grape/IvyGrabRecord;->force:Z

    return v0
.end method

.method public isTransitive()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/grape/IvyGrabRecord;->transitive:Z

    return v0
.end method

.method public setChanging(Z)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-boolean p1, p0, Lgroovy/grape/IvyGrabRecord;->changing:Z

    return-void
.end method

.method public setClassifier(Ljava/lang/String;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/IvyGrabRecord;->classifier:Ljava/lang/String;

    return-void
.end method

.method public setConf(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/IvyGrabRecord;->conf:Ljava/util/List;

    return-void
.end method

.method public setExt(Ljava/lang/String;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/IvyGrabRecord;->ext:Ljava/lang/String;

    return-void
.end method

.method public setForce(Z)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-boolean p1, p0, Lgroovy/grape/IvyGrabRecord;->force:Z

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/IvyGrabRecord;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setMrid(Lorg/apache/ivy/core/module/id/ModuleRevisionId;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/IvyGrabRecord;->mrid:Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    return-void
.end method

.method public setTransitive(Z)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-boolean p1, p0, Lgroovy/grape/IvyGrabRecord;->transitive:Z

    return-void
.end method

.method public setType(Ljava/lang/String;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/IvyGrabRecord;->type:Ljava/lang/String;

    return-void
.end method
