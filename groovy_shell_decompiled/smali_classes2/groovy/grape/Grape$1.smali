.class Lgroovy/grape/Grape$1;
.super Ljava/lang/Object;
.source "Grape.java"

# interfaces
.implements Ljava/security/PrivilegedAction;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/grape/Grape;->grab(Ljava/util/Map;[Ljava/util/Map;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/security/PrivilegedAction<",
        "Ljava/lang/Void;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic val$args:Ljava/util/Map;

.field final synthetic val$dependencies:[Ljava/util/Map;


# direct methods
.method constructor <init>(Ljava/util/Map;[Ljava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 160
    iput-object p1, p0, Lgroovy/grape/Grape$1;->val$args:Ljava/util/Map;

    iput-object p2, p0, Lgroovy/grape/Grape$1;->val$dependencies:[Ljava/util/Map;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic run()Ljava/lang/Object;
    .locals 1

    .line 160
    invoke-virtual {p0}, Lgroovy/grape/Grape$1;->run()Ljava/lang/Void;

    move-result-object v0

    return-object v0
.end method

.method public run()Ljava/lang/Void;
    .locals 4

    .line 163
    invoke-static {}, Lgroovy/grape/Grape;->getInstance()Lgroovy/grape/GrapeEngine;

    move-result-object v0

    if-eqz v0, :cond_3

    .line 165
    iget-object v1, p0, Lgroovy/grape/Grape$1;->val$args:Ljava/util/Map;

    const-string v2, "autoDownload"

    invoke-interface {v1, v2}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    .line 166
    iget-object v1, p0, Lgroovy/grape/Grape$1;->val$args:Ljava/util/Map;

    invoke-static {}, Lgroovy/grape/Grape;->access$000()Z

    move-result v3

    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    invoke-interface {v1, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 168
    :cond_0
    iget-object v1, p0, Lgroovy/grape/Grape$1;->val$args:Ljava/util/Map;

    const-string v2, "disableChecksums"

    invoke-interface {v1, v2}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    .line 169
    iget-object v1, p0, Lgroovy/grape/Grape$1;->val$args:Ljava/util/Map;

    invoke-static {}, Lgroovy/grape/Grape;->access$100()Z

    move-result v3

    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    invoke-interface {v1, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 171
    :cond_1
    iget-object v1, p0, Lgroovy/grape/Grape$1;->val$args:Ljava/util/Map;

    const-string v2, "calleeDepth"

    invoke-interface {v1, v2}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    .line 172
    iget-object v1, p0, Lgroovy/grape/Grape$1;->val$args:Ljava/util/Map;

    const/4 v3, 0x5

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-interface {v1, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 174
    :cond_2
    iget-object v1, p0, Lgroovy/grape/Grape$1;->val$args:Ljava/util/Map;

    iget-object v2, p0, Lgroovy/grape/Grape$1;->val$dependencies:[Ljava/util/Map;

    invoke-interface {v0, v1, v2}, Lgroovy/grape/GrapeEngine;->grab(Ljava/util/Map;[Ljava/util/Map;)Ljava/lang/Object;

    :cond_3
    const/4 v0, 0x0

    return-object v0
.end method
