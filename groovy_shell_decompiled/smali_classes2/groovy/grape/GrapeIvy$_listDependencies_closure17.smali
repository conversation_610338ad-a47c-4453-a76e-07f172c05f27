.class public final Lgroovy/grape/GrapeIvy$_listDependencies_closure17;
.super Lgroovy/lang/Closure;
.source "GrapeIvy.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/grape/GrapeIvy;->listDependencies(Ljava/lang/ClassLoader;)[Ljava/util/Map;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_listDependencies_closure17"
.end annotation


# static fields
.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/grape/GrapeIvy$_listDependencies_closure17;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/grape/GrapeIvy$_listDependencies_closure17;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/grape/GrapeIvy$_listDependencies_closure17;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Lgroovy/grape/IvyGrabRecord;)Ljava/util/LinkedHashMap;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/grape/IvyGrabRecord;",
            ")",
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-virtual {p0, p1}, Lgroovy/grape/GrapeIvy$_listDependencies_closure17;->doCall(Lgroovy/grape/IvyGrabRecord;)Ljava/util/LinkedHashMap;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Lgroovy/grape/IvyGrabRecord;)Ljava/util/LinkedHashMap;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/grape/IvyGrabRecord;",
            ")",
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x6

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    const-string v2, "group"

    aput-object v2, v0, v1

    .line 674
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v1

    invoke-virtual {v1}, Lorg/apache/ivy/core/module/id/ModuleRevisionId;->getOrganisation()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    const/4 v1, 0x2

    const-string v3, "module"

    aput-object v3, v0, v1

    .line 675
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v1

    invoke-virtual {v1}, Lorg/apache/ivy/core/module/id/ModuleRevisionId;->getName()Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x3

    aput-object v1, v0, v3

    const/4 v1, 0x4

    const-string v3, "version"

    aput-object v3, v0, v1

    .line 676
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v1

    invoke-virtual {v1}, Lorg/apache/ivy/core/module/id/ModuleRevisionId;->getRevision()Ljava/lang/String;

    move-result-object v1

    const/4 v3, 0x5

    aput-object v1, v0, v3

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    const-class v1, Ljava/util/LinkedHashMap;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/LinkedHashMap;

    .line 678
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getConf()Ljava/util/List;

    move-result-object v1

    const/4 v3, 0x0

    invoke-static {v3}, Lgroovy/grape/GrapeIvy;->pfaccess$0(Lgroovy/grape/GrapeIvy;)Ljava/util/List;

    move-result-object v4

    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 679
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getConf()Ljava/util/List;

    move-result-object v1

    const-string v4, "conf"

    move-object v5, v4

    check-cast v5, Ljava/lang/String;

    invoke-static {v1, v3, v0, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    .line 681
    :cond_0
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->isChanging()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 682
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->isChanging()Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v4, "changing"

    move-object v5, v4

    check-cast v5, Ljava/lang/String;

    invoke-static {v1, v3, v0, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    .line 684
    :cond_1
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->isTransitive()Z

    move-result v1

    xor-int/2addr v1, v2

    if-eqz v1, :cond_2

    .line 685
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->isTransitive()Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v4, "transitive"

    move-object v5, v4

    check-cast v5, Ljava/lang/String;

    invoke-static {v1, v3, v0, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    .line 687
    :cond_2
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->isForce()Z

    move-result v1

    xor-int/2addr v1, v2

    if-eqz v1, :cond_3

    .line 688
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->isForce()Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v2, "force"

    move-object v4, v2

    check-cast v4, Ljava/lang/String;

    invoke-static {v1, v3, v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    .line 690
    :cond_3
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getClassifier()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_4

    .line 691
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getClassifier()Ljava/lang/String;

    move-result-object v1

    const-string v2, "classifier"

    move-object v4, v2

    check-cast v4, Ljava/lang/String;

    invoke-static {v1, v3, v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    .line 693
    :cond_4
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getExt()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_5

    .line 694
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getExt()Ljava/lang/String;

    move-result-object v1

    const-string v2, "ext"

    move-object v4, v2

    check-cast v4, Ljava/lang/String;

    invoke-static {v1, v3, v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    .line 696
    :cond_5
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getType()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_6

    .line 697
    invoke-virtual {p1}, Lgroovy/grape/IvyGrabRecord;->getType()Ljava/lang/String;

    move-result-object p1

    const-string v1, "type"

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    invoke-static {p1, v3, v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    :cond_6
    return-object v0
.end method
