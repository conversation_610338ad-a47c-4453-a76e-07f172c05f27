.class public Lgroovy/grape/GrapeIvy;
.super Ljava/lang/Object;
.source "GrapeIvy.groovy"

# interfaces
.implements Lgroovy/grape/GrapeEngine;
.implements Lgroovy/lang/GroovyObject;


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference; = null

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo; = null

.field private static final DEFAULT_CONF:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private static final METAINF_PREFIX:Ljava/lang/String; = "META-INF/services/"

.field private static final MUTUALLY_EXCLUSIVE_KEYS:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation
.end field

.field private static final RUNNER_PROVIDER_CONFIG:Ljava/lang/String;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private downloadedArtifacts:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private enableGrapes:Z

.field private final grabRecordsForCurrDependencies:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lgroovy/grape/IvyGrabRecord;",
            ">;"
        }
    .end annotation
.end field

.field private ivyInstance:Lorg/apache/ivy/Ivy;

.field private final loadedDeps:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/ClassLoader;",
            "Ljava/util/Set<",
            "Lgroovy/grape/IvyGrabRecord;",
            ">;>;"
        }
    .end annotation
.end field

.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;

.field private resolvedDependencies:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private settings:Lorg/apache/ivy/core/settings/IvySettings;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0xb

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lgroovy/grape/GrapeIvy;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lgroovy/grape/GrapeIvy;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 2

    const/4 v0, 0x0

    const-string v1, "inject"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v1, "conf"

    aput-object v1, p0, v0

    const/4 v0, 0x2

    const-string v1, "scope"

    aput-object v1, p0, v0

    const/4 v0, 0x3

    const-string v1, "configuration"

    aput-object v1, p0, v0

    const/4 v0, 0x4

    const-string v1, "startsWith"

    aput-object v1, p0, v0

    const/4 v0, 0x5

    const-string v1, "endsWith"

    aput-object v1, p0, v0

    const/4 v0, 0x6

    const-string v1, "getAt"

    aput-object v1, p0, v0

    const/4 v0, 0x7

    const-string v1, "toList"

    aput-object v1, p0, v0

    const/16 v0, 0x8

    const-string v1, "split"

    aput-object v1, p0, v0

    const/16 v0, 0x9

    const-string v1, "addURL"

    aput-object v1, p0, v0

    const/16 v0, 0xa

    const-string v1, "toURL"

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lgroovy/grape/GrapeIvy;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lgroovy/grape/GrapeIvy;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lgroovy/grape/GrapeIvy;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 8

    .line 66
    const-class v0, Lorg/apache/groovy/plugin/GroovyRunner;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lgroovy/grape/GrapeIvy;->RUNNER_PROVIDER_CONFIG:Ljava/lang/String;

    const-string v0, "default"

    .line 67
    invoke-static {v0}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Lgroovy/grape/GrapeIvy;->DEFAULT_CONF:Ljava/util/List;

    const/4 v0, 0x4

    new-array v1, v0, [Ljava/lang/Object;

    const/4 v2, 0x5

    new-array v2, v2, [Ljava/lang/Object;

    const-string v3, "group"

    const/4 v4, 0x0

    aput-object v3, v2, v4

    const-string v3, "groupId"

    const/4 v5, 0x1

    aput-object v3, v2, v5

    const-string v3, "organisation"

    const/4 v6, 0x2

    aput-object v3, v2, v6

    const-string v3, "organization"

    const/4 v7, 0x3

    aput-object v3, v2, v7

    const-string v3, "org"

    aput-object v3, v2, v0

    .line 69
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    aput-object v0, v1, v4

    new-array v0, v7, [Ljava/lang/Object;

    const-string v2, "module"

    aput-object v2, v0, v4

    const-string v2, "artifactId"

    aput-object v2, v0, v5

    const-string v2, "artifact"

    aput-object v2, v0, v6

    .line 70
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    aput-object v0, v1, v5

    new-array v0, v7, [Ljava/lang/Object;

    const-string v2, "version"

    aput-object v2, v0, v4

    const-string v2, "revision"

    aput-object v2, v0, v5

    const-string v2, "rev"

    aput-object v2, v0, v6

    .line 71
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    aput-object v0, v1, v6

    new-array v0, v7, [Ljava/lang/Object;

    const-string v2, "conf"

    aput-object v2, v0, v4

    const-string v2, "scope"

    aput-object v2, v0, v5

    const-string v2, "configuration"

    aput-object v2, v0, v6

    .line 72
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    aput-object v0, v1, v7

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Lgroovy/grape/GrapeIvy;->processGrabArgs(Ljava/util/List;)Ljava/util/Map;

    move-result-object v0

    sput-object v0, Lgroovy/grape/GrapeIvy;->MUTUALLY_EXCLUSIVE_KEYS:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>()V
    .locals 9

    const-class v0, Lgroovy/grape/GrapeIvy;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v1, 0x1

    iput-boolean v1, p0, Lgroovy/grape/GrapeIvy;->enableGrapes:Z

    const/4 v2, 0x0

    new-array v3, v2, [Ljava/lang/Object;

    .line 84
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    const-class v4, Ljava/util/Set;

    invoke-static {v3, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Set;

    iput-object v3, p0, Lgroovy/grape/GrapeIvy;->downloadedArtifacts:Ljava/util/Set;

    new-array v3, v2, [Ljava/lang/Object;

    .line 85
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    const-class v4, Ljava/util/Set;

    invoke-static {v3, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Set;

    iput-object v3, p0, Lgroovy/grape/GrapeIvy;->resolvedDependencies:Ljava/util/Set;

    new-array v3, v2, [Ljava/lang/Object;

    .line 87
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    const-class v4, Ljava/util/WeakHashMap;

    invoke-static {v3, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/WeakHashMap;

    iput-object v3, p0, Lgroovy/grape/GrapeIvy;->loadedDeps:Ljava/util/Map;

    new-array v3, v2, [Ljava/lang/Object;

    .line 89
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    const-class v4, Ljava/util/LinkedHashSet;

    invoke-static {v3, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/LinkedHashSet;

    iput-object v3, p0, Lgroovy/grape/GrapeIvy;->grabRecordsForCurrDependencies:Ljava/util/Set;

    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v3

    iput-object v3, p0, Lgroovy/grape/GrapeIvy;->metaClass:Lgroovy/lang/MetaClass;

    .line 92
    new-instance v3, Lorg/apache/ivy/util/DefaultMessageLogger;

    const-string v4, "ivy.message.logger.level"

    const-string v5, "-1"

    invoke-static {v4, v5}, Ljava/lang/System;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    sget-object v5, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    invoke-static {v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result v4

    invoke-direct {v3, v4}, Lorg/apache/ivy/util/DefaultMessageLogger;-><init>(I)V

    invoke-static {v3}, Lorg/apache/ivy/util/Message;->setDefaultLogger(Lorg/apache/ivy/util/MessageLogger;)V

    .line 94
    new-instance v3, Lorg/apache/ivy/core/settings/IvySettings;

    invoke-direct {v3}, Lorg/apache/ivy/core/settings/IvySettings;-><init>()V

    iput-object v3, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    .line 95
    new-instance v4, Ljava/io/File;

    const-string v5, "user.home"

    invoke-static {v5}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v4}, Ljava/io/File;->toURI()Ljava/net/URI;

    move-result-object v4

    invoke-virtual {v4}, Ljava/net/URI;->toURL()Ljava/net/URL;

    move-result-object v4

    const-class v5, Ljava/lang/String;

    invoke-static {v4, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/String;

    const-string v5, "user.home.url"

    invoke-virtual {v3, v5, v4}, Lorg/apache/ivy/core/settings/IvySettings;->setVariable(Ljava/lang/String;Ljava/lang/String;)V

    .line 96
    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy;->getLocalGrapeConfig()Ljava/io/File;

    move-result-object v3

    .line 97
    invoke-virtual {v3}, Ljava/io/File;->exists()Z

    move-result v4

    const-string v5, "defaultGrapeConfig.xml"

    if-eqz v4, :cond_0

    .line 99
    :try_start_0
    iget-object v4, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    invoke-virtual {v4, v3}, Lorg/apache/ivy/core/settings/IvySettings;->load(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/text/ParseException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v0

    goto :goto_0

    :catch_0
    move-exception v4

    .line 101
    :try_start_1
    sget-object v6, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v8, 0x2

    new-array v8, v8, [Ljava/lang/Object;

    invoke-virtual {v3}, Ljava/io/File;->getCanonicalPath()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v8, v2

    move-object v2, v4

    check-cast v2, Ljava/text/ParseException;

    invoke-virtual {v4}, Ljava/text/ParseException;->getMessage()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v8, v1

    const-string v1, "Local Ivy config file \'"

    const-string v2, "\' appears corrupt - ignoring it and using default config instead\nError was: "

    const-string v3, ""

    filled-new-array {v1, v2, v3}, [Ljava/lang/String;

    move-result-object v1

    invoke-direct {v7, v8, v1}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v6, v7}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->println(Ljava/io/PrintStream;Ljava/lang/Object;)V

    .line 102
    iget-object v1, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    invoke-virtual {v0, v5}, Ljava/lang/Class;->getResource(Ljava/lang/String;)Ljava/net/URL;

    move-result-object v0

    invoke-virtual {v1, v0}, Lorg/apache/ivy/core/settings/IvySettings;->load(Ljava/net/URL;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    .line 103
    :goto_0
    throw v0

    .line 105
    :cond_0
    iget-object v1, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    invoke-virtual {v0, v5}, Ljava/lang/Class;->getResource(Ljava/lang/String;)Ljava/net/URL;

    move-result-object v0

    invoke-virtual {v1, v0}, Lorg/apache/ivy/core/settings/IvySettings;->load(Ljava/net/URL;)V

    .line 107
    :goto_1
    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy;->getGrapeCacheDir()Ljava/io/File;

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/apache/ivy/core/settings/IvySettings;->setDefaultCache(Ljava/io/File;)V

    .line 108
    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    const-string v1, "ivy.default.configuration.m2compatible"

    const-string v2, "true"

    invoke-virtual {v0, v1, v2}, Lorg/apache/ivy/core/settings/IvySettings;->setVariable(Ljava/lang/String;Ljava/lang/String;)V

    .line 110
    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    invoke-static {v0}, Lorg/apache/ivy/Ivy;->newInstance(Lorg/apache/ivy/core/settings/IvySettings;)Lorg/apache/ivy/Ivy;

    move-result-object v0

    iput-object v0, p0, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    .line 111
    invoke-static {}, Lorg/apache/ivy/core/IvyContext;->getContext()Lorg/apache/ivy/core/IvyContext;

    move-result-object v0

    iget-object v1, p0, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    invoke-virtual {v0, v1}, Lorg/apache/ivy/core/IvyContext;->setIvy(Lorg/apache/ivy/Ivy;)V

    return-void
.end method

.method public static synthetic access$0(Lgroovy/grape/GrapeIvy;Lorg/w3c/dom/NodeList;Ljava/lang/String;Ljava/io/File;)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lgroovy/grape/GrapeIvy;->processArtifacts(Lorg/w3c/dom/NodeList;Ljava/lang/String;Ljava/io/File;)V

    return-void
.end method

.method private addExcludesIfNeeded(Ljava/util/Map;Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;)Ljava/lang/Object;
    .locals 1

    new-instance v0, Lgroovy/lang/Reference;

    invoke-direct {v0, p2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const-string p2, "excludes"

    .line 559
    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-eqz p1, :cond_0

    new-instance p2, Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;

    invoke-direct {p2, p0, p0, v0}, Lgroovy/grape/GrapeIvy$_addExcludesIfNeeded_closure12;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each(Ljava/lang/Object;Lgroovy/lang/Closure;)Ljava/lang/Object;

    move-result-object p1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method private addIvyListener()Ljava/lang/Object;
    .locals 3

    .line 495
    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    invoke-virtual {v0}, Lorg/apache/ivy/Ivy;->getEventManager()Lorg/apache/ivy/core/event/EventManager;

    move-result-object v0

    new-instance v1, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10;

    invoke-direct {v1, p0, p0}, Lgroovy/grape/GrapeIvy$_addIvyListener_closure10;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    const-class v2, Lorg/apache/ivy/core/event/IvyListener;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/apache/ivy/core/event/IvyListener;

    invoke-virtual {v0, v1}, Lorg/apache/ivy/core/event/EventManager;->addIvyListener(Lorg/apache/ivy/core/event/IvyListener;)V

    const/4 v0, 0x0

    return-object v0
.end method

.method private addURL(Ljava/lang/ClassLoader;Ljava/net/URI;)V
    .locals 3

    invoke-static {}, Lgroovy/grape/GrapeIvy;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x9

    .line 313
    aget-object v1, v0, v1

    const/16 v2, 0xa

    aget-object v0, v0, v2

    invoke-interface {v0, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {v1, p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method private getConfList(Ljava/util/Map;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    invoke-static {}, Lgroovy/grape/GrapeIvy;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x1

    .line 244
    aget-object v2, v0, v1

    invoke-interface {v2, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x2

    aget-object v2, v0, v2

    invoke-interface {v2, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    goto :goto_0

    :cond_1
    const/4 v2, 0x3

    aget-object v2, v0, v2

    invoke-interface {v2, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    sget-object v2, Lgroovy/grape/GrapeIvy;->DEFAULT_CONF:Ljava/util/List;

    .line 245
    :goto_0
    instance-of p1, v2, Ljava/lang/String;

    if-eqz p1, :cond_5

    const/4 p1, 0x4

    .line 246
    aget-object p1, v0, p1

    const-string v3, "["

    invoke-interface {p1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_3

    const/4 p1, 0x5

    aget-object p1, v0, p1

    const-string v3, "]"

    invoke-interface {p1, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_3

    move p1, v1

    goto :goto_1

    :cond_3
    const/4 p1, 0x0

    :goto_1
    if-eqz p1, :cond_4

    const/4 p1, 0x6

    aget-object p1, v0, p1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    const/4 v4, -0x2

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    invoke-static {v3, v4, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createRange(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/util/List;

    move-result-object v1

    invoke-interface {p1, v2, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    :cond_4
    const/4 p1, 0x7

    .line 247
    aget-object p1, v0, p1

    const/16 v1, 0x8

    aget-object v0, v0, v1

    const-string v1, ","

    invoke-interface {v0, v2, v1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 249
    :cond_5
    const-class p1, Ljava/util/List;

    invoke-static {v2, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/List;

    return-object p1
.end method

.method private getLoadedDepsForLoader(Ljava/lang/ClassLoader;)Ljava/util/Set;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ClassLoader;",
            ")",
            "Ljava/util/Set<",
            "Lgroovy/grape/IvyGrabRecord;",
            ">;"
        }
    .end annotation

    .line 667
    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->loadedDeps:Ljava/util/Map;

    new-instance v1, Lgroovy/grape/GrapeIvy$_getLoadedDepsForLoader_lambda16;

    const-class v2, Lgroovy/grape/GrapeIvy;

    invoke-direct {v1, v2, v2}, Lgroovy/grape/GrapeIvy$_getLoadedDepsForLoader_lambda16;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    new-instance v2, Lgroovy/grape/GrapeIvy$$ExternalSyntheticLambda0;

    invoke-direct {v2, v1}, Lgroovy/grape/GrapeIvy$$ExternalSyntheticLambda0;-><init>(Lgroovy/grape/GrapeIvy$_getLoadedDepsForLoader_lambda16;)V

    invoke-interface {v0, p1, v2}, Ljava/util/Map;->computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;

    move-result-object p1

    const-class v0, Ljava/util/Set;

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Set;

    return-object p1
.end method

.method private isValidTargetClassLoader(Ljava/lang/ClassLoader;)Z
    .locals 0

    if-eqz p1, :cond_0

    .line 186
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    invoke-direct {p0, p1}, Lgroovy/grape/GrapeIvy;->isValidTargetClassLoaderClass(Ljava/lang/Class;)Z

    move-result p1

    return p1
.end method

.method private isValidTargetClassLoaderClass(Ljava/lang/Class;)Z
    .locals 4

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    move v2, v0

    goto :goto_0

    :cond_0
    move v2, v1

    :goto_0
    if-eqz v2, :cond_5

    .line 190
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    const-string v3, "groovy.lang.GroovyClassLoader"

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_2

    .line 191
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v2

    const-string v3, "org.codehaus.groovy.tools.RootLoader"

    invoke-static {v2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    goto :goto_1

    :cond_1
    move v2, v1

    goto :goto_2

    :cond_2
    :goto_1
    move v2, v0

    :goto_2
    if-nez v2, :cond_4

    .line 192
    invoke-virtual {p1}, Ljava/lang/Class;->getSuperclass()Ljava/lang/Class;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovy/grape/GrapeIvy;->isValidTargetClassLoaderClass(Ljava/lang/Class;)Z

    move-result p1

    if-eqz p1, :cond_3

    goto :goto_3

    :cond_3
    move p1, v1

    goto :goto_4

    :cond_4
    :goto_3
    move p1, v0

    :goto_4
    if-eqz p1, :cond_5

    goto :goto_5

    :cond_5
    move v0, v1

    :goto_5
    return v0
.end method

.method public static synthetic pfaccess$0(Lgroovy/grape/GrapeIvy;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/grape/GrapeIvy;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    const-class p0, Lgroovy/grape/GrapeIvy;

    const-string v0, "DEFAULT_CONF"

    move-object v1, v0

    check-cast v1, Ljava/lang/String;

    invoke-static {p0, p0, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getField(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    const-class v0, Ljava/util/List;

    invoke-static {p0, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/util/List;

    return-object p0
.end method

.method public static synthetic pfaccess$1(Lgroovy/grape/GrapeIvy;)Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/grape/GrapeIvy;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation

    const-class p0, Lgroovy/grape/GrapeIvy;

    const-string v0, "MUTUALLY_EXCLUSIVE_KEYS"

    move-object v1, v0

    check-cast v1, Ljava/lang/String;

    invoke-static {p0, p0, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->getField(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    const-class v0, Ljava/util/Map;

    invoke-static {p0, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/util/Map;

    return-object p0
.end method

.method public static synthetic pfaccess$2(Lgroovy/grape/GrapeIvy;)Ljava/util/Set;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/grape/GrapeIvy;",
            ")",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object p0, p0, Lgroovy/grape/GrapeIvy;->downloadedArtifacts:Ljava/util/Set;

    return-object p0
.end method

.method public static synthetic pfaccess$3(Lgroovy/grape/GrapeIvy;)Ljava/util/Set;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/grape/GrapeIvy;",
            ")",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    iget-object p0, p0, Lgroovy/grape/GrapeIvy;->resolvedDependencies:Ljava/util/Set;

    return-object p0
.end method

.method public static synthetic pfaccess$4(Lgroovy/grape/GrapeIvy;)Ljava/util/Set;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/grape/GrapeIvy;",
            ")",
            "Ljava/util/Set<",
            "Lgroovy/grape/IvyGrabRecord;",
            ">;"
        }
    .end annotation

    iget-object p0, p0, Lgroovy/grape/GrapeIvy;->grabRecordsForCurrDependencies:Ljava/util/Set;

    return-object p0
.end method

.method private processArtifacts(Lorg/w3c/dom/NodeList;Ljava/lang/String;Ljava/io/File;)V
    .locals 11

    .line 543
    invoke-interface {p1}, Lorg/w3c/dom/NodeList;->getLength()I

    move-result v0

    const/4 v1, 0x0

    move v2, v1

    :goto_0
    const/4 v3, 0x1

    if-ge v2, v0, :cond_0

    move v4, v3

    goto :goto_1

    :cond_0
    move v4, v1

    :goto_1
    if-eqz v4, :cond_4

    .line 544
    invoke-interface {p1, v2}, Lorg/w3c/dom/NodeList;->item(I)Lorg/w3c/dom/Node;

    move-result-object v4

    .line 545
    invoke-interface {v4}, Lorg/w3c/dom/Node;->getAttributes()Lorg/w3c/dom/NamedNodeMap;

    move-result-object v4

    const-string v5, "name"

    .line 546
    invoke-interface {v4, v5}, Lorg/w3c/dom/NamedNodeMap;->getNamedItem(Ljava/lang/String;)Lorg/w3c/dom/Node;

    move-result-object v5

    invoke-interface {v5}, Lorg/w3c/dom/Node;->getTextContent()Ljava/lang/String;

    move-result-object v5

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    aput-object p2, v7, v1

    const-string v8, "-"

    const-string v9, ""

    filled-new-array {v8, v9}, [Ljava/lang/String;

    move-result-object v10

    invoke-direct {v6, v7, v10}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v5, v6}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->plus(Ljava/lang/String;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v5

    const-string v6, "m"

    const-string v7, "classifier"

    .line 547
    invoke-interface {v4, v6, v7}, Lorg/w3c/dom/NamedNodeMap;->getNamedItemNS(Ljava/lang/String;Ljava/lang/String;)Lorg/w3c/dom/Node;

    move-result-object v6

    if-eqz v6, :cond_1

    invoke-interface {v6}, Lorg/w3c/dom/Node;->getTextContent()Ljava/lang/String;

    move-result-object v6

    goto :goto_2

    :cond_1
    const/4 v6, 0x0

    .line 548
    :goto_2
    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_2

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v10, v3, [Ljava/lang/Object;

    aput-object v6, v10, v1

    filled-new-array {v8, v9}, [Ljava/lang/String;

    move-result-object v6

    invoke-direct {v7, v10, v6}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v5, v7}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->plus(Ljava/lang/String;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v5

    .line 549
    :cond_2
    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v7, v3, [Ljava/lang/Object;

    const-string v8, "ext"

    invoke-interface {v4, v8}, Lorg/w3c/dom/NamedNodeMap;->getNamedItem(Ljava/lang/String;)Lorg/w3c/dom/Node;

    move-result-object v4

    invoke-interface {v4}, Lorg/w3c/dom/Node;->getTextContent()Ljava/lang/String;

    move-result-object v4

    aput-object v4, v7, v1

    const-string v4, "."

    filled-new-array {v4, v9}, [Ljava/lang/String;

    move-result-object v4

    invoke-direct {v6, v7, v4}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v5, v6}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->plus(Ljava/lang/String;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v4

    .line 550
    new-instance v5, Ljava/io/File;

    invoke-direct {v5, p3, v4}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 551
    invoke-virtual {v5}, Ljava/io/File;->exists()Z

    move-result v4

    if-eqz v4, :cond_3

    .line 552
    sget-object v4, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v6, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {v5}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v7

    aput-object v7, v3, v1

    const-string v7, "Deleting "

    filled-new-array {v7, v9}, [Ljava/lang/String;

    move-result-object v7

    invoke-direct {v6, v3, v7}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v4, v6}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->println(Ljava/io/PrintStream;Ljava/lang/Object;)V

    .line 553
    invoke-virtual {v5}, Ljava/io/File;->delete()Z

    :cond_3
    add-int/lit8 v2, v2, 0x1

    goto/16 :goto_0

    :cond_4
    return-void
.end method

.method private processCategoryMethods(Ljava/lang/ClassLoader;Ljava/io/File;)Ljava/lang/Object;
    .locals 7

    .line 318
    invoke-virtual {p2}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->toLowerCase()Ljava/lang/String;

    move-result-object v0

    const-string v1, ".jar"

    invoke-virtual {v0, v1}, Ljava/lang/String;->endsWith(Ljava/lang/String;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_4

    .line 319
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v0

    .line 320
    instance-of v2, v0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;

    if-eqz v2, :cond_4

    const/4 v2, 0x0

    const/4 v3, 0x1

    .line 321
    :try_start_0
    new-instance v4, Ljava/util/jar/JarFile;

    invoke-direct {v4, p2}, Ljava/util/jar/JarFile;-><init>(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/util/zip/ZipException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_8

    .line 322
    :try_start_1
    sget-object v5, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;->MODULE_META_INF_FILE:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/util/jar/JarFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v5

    .line 323
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v6

    xor-int/2addr v6, v3

    if-eqz v6, :cond_0

    .line 324
    sget-object v5, Lorg/codehaus/groovy/runtime/m12n/ExtensionModuleScanner;->LEGACY_MODULE_META_INF_FILE:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/util/jar/JarFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v5

    .line 326
    :cond_0
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_3

    .line 327
    new-instance v1, Ljava/util/Properties;

    invoke-direct {v1}, Ljava/util/Properties;-><init>()V

    .line 329
    invoke-virtual {v4, v5}, Ljava/util/jar/JarFile;->getInputStream(Ljava/util/zip/ZipEntry;)Ljava/io/InputStream;

    move-result-object v5
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_4

    .line 330
    :try_start_2
    invoke-virtual {v1, v5}, Ljava/util/Properties;->load(Ljava/io/InputStream;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    if-eqz v5, :cond_1

    .line 331
    :try_start_3
    invoke-virtual {v5}, Ljava/io/InputStream;->close()V

    :cond_1
    new-array v5, v2, [Ljava/lang/Object;

    .line 333
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v5

    const-class v6, Ljava/util/LinkedHashMap;

    invoke-static {v5, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/util/LinkedHashMap;

    .line 334
    const-class v6, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;

    invoke-static {v0, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;

    invoke-virtual {v0, v1, p1, v5}, Lorg/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl;->registerExtensionModuleFromProperties(Ljava/util/Properties;Ljava/lang/ClassLoader;Ljava/util/Map;)V

    .line 336
    new-instance p1, Lgroovy/grape/GrapeIvy$_processCategoryMethods_closure4;

    invoke-direct {p1, p0, p0}, Lgroovy/grape/GrapeIvy$_processCategoryMethods_closure4;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-static {v5, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each(Ljava/util/Map;Lgroovy/lang/Closure;)Ljava/util/Map;

    move-result-object p1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_4

    :try_start_4
    move-object v0, v4

    check-cast v0, Ljava/util/jar/JarFile;

    invoke-virtual {v4}, Ljava/util/jar/JarFile;->close()V
    :try_end_4
    .catch Ljava/util/zip/ZipException; {:try_start_4 .. :try_end_4} :catch_0
    .catchall {:try_start_4 .. :try_end_4} :catchall_8

    return-object p1

    :catchall_0
    move-exception p1

    .line 331
    :try_start_5
    move-object v0, p1

    check-cast v0, Ljava/lang/Throwable;

    throw p1
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    :catchall_1
    move-exception v0

    if-eqz v5, :cond_2

    :try_start_6
    invoke-virtual {v5}, Ljava/io/InputStream;->close()V
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_2

    goto :goto_0

    :catchall_2
    move-exception v1

    :try_start_7
    invoke-virtual {p1, v1}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_3

    goto :goto_0

    :catchall_3
    move-exception p1

    :try_start_8
    throw p1

    :cond_2
    :goto_0
    throw v0
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_4

    .line 347
    :cond_3
    :try_start_9
    move-object p1, v4

    check-cast p1, Ljava/util/jar/JarFile;

    invoke-virtual {v4}, Ljava/util/jar/JarFile;->close()V
    :try_end_9
    .catch Ljava/util/zip/ZipException; {:try_start_9 .. :try_end_9} :catch_0
    .catchall {:try_start_9 .. :try_end_9} :catchall_8

    return-object v1

    :catchall_4
    move-exception p1

    .line 348
    :try_start_a
    move-object v0, p1

    check-cast v0, Ljava/lang/Throwable;

    throw p1
    :try_end_a
    .catchall {:try_start_a .. :try_end_a} :catchall_5

    :catchall_5
    move-exception v0

    :try_start_b
    move-object v1, v4

    check-cast v1, Ljava/util/jar/JarFile;

    invoke-virtual {v4}, Ljava/util/jar/JarFile;->close()V
    :try_end_b
    .catchall {:try_start_b .. :try_end_b} :catchall_6

    goto :goto_1

    :catchall_6
    move-exception v1

    :try_start_c
    invoke-virtual {p1, v1}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V
    :try_end_c
    .catchall {:try_start_c .. :try_end_c} :catchall_7

    :goto_1
    :try_start_d
    throw v0

    :catchall_7
    move-exception p1

    throw p1
    :try_end_d
    .catch Ljava/util/zip/ZipException; {:try_start_d .. :try_end_d} :catch_0
    .catchall {:try_start_d .. :try_end_d} :catchall_8

    :catchall_8
    move-exception p1

    goto :goto_2

    :catch_0
    move-exception p1

    .line 349
    :try_start_e
    new-instance v0, Ljava/lang/RuntimeException;

    new-instance v1, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v3, v3, [Ljava/lang/Object;

    aput-object p2, v3, v2

    const-string p2, "Grape could not load jar \'"

    const-string v2, "\'"

    filled-new-array {p2, v2}, [Ljava/lang/String;

    move-result-object p2

    invoke-direct {v1, v3, p2}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    move-object v1, p2

    check-cast v1, Ljava/lang/String;

    invoke-direct {v0, p2, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    check-cast v0, Ljava/lang/Throwable;

    throw v0
    :try_end_e
    .catchall {:try_start_e .. :try_end_e} :catchall_8

    .line 350
    :goto_2
    throw p1

    :cond_4
    return-object v1
.end method

.method private static processGrabArgs(Ljava/util/List;)Ljava/util/Map;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;)",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation

    const-class v0, Lgroovy/grape/GrapeIvy;

    invoke-static {}, Lgroovy/grape/GrapeIvy;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    const/4 v2, 0x0

    .line 77
    aget-object v1, v1, v2

    new-array v2, v2, [Ljava/lang/Object;

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v2

    new-instance v3, Lgroovy/grape/GrapeIvy$_processGrabArgs_closure1;

    invoke-direct {v3, v0, v0}, Lgroovy/grape/GrapeIvy$_processGrabArgs_closure1;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v1, p0, v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    const-class v0, Ljava/util/Map;

    invoke-static {p0, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/util/Map;

    return-object p0
.end method

.method private processMetaInfServices(Ljava/lang/ClassLoader;Ljava/io/File;)Ljava/util/Collection;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ClassLoader;",
            "Ljava/io/File;",
            ")",
            "Ljava/util/Collection<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/lang/Object;

    .line 368
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    const-class v2, Ljava/util/ArrayList;

    invoke-static {v1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/ArrayList;

    .line 369
    :try_start_0
    new-instance v2, Ljava/util/zip/ZipFile;

    invoke-direct {v2, p2}, Ljava/util/zip/ZipFile;-><init>(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/util/zip/ZipException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_c

    :try_start_1
    const-string v3, "org.codehaus.groovy.runtime.SerializedCategoryMethods"

    .line 371
    sget-object v4, Lgroovy/grape/GrapeIvy;->METAINF_PREFIX:Ljava/lang/String;

    invoke-static {v4, v3}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->plus(Ljava/lang/String;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v2, v5}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v5

    const/4 v6, 0x1

    if-eqz v5, :cond_0

    move v7, v6

    goto :goto_0

    :cond_0
    move v7, v0

    :goto_0
    if-eqz v7, :cond_2

    .line 373
    invoke-virtual {v1, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 375
    invoke-virtual {v2, v5}, Ljava/util/zip/ZipFile;->getInputStream(Ljava/util/zip/ZipEntry;)Ljava/io/InputStream;

    move-result-object v3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_8

    .line 376
    :try_start_2
    invoke-virtual {p0, v3}, Lgroovy/grape/GrapeIvy;->processSerializedCategoryMethods(Ljava/io/InputStream;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    if-eqz v3, :cond_2

    .line 377
    :try_start_3
    invoke-virtual {v3}, Ljava/io/InputStream;->close()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_8

    goto :goto_2

    :catchall_0
    move-exception p1

    :try_start_4
    move-object p2, p1

    check-cast p2, Ljava/lang/Throwable;

    throw p1
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    :catchall_1
    move-exception p2

    if-eqz v3, :cond_1

    :try_start_5
    invoke-virtual {v3}, Ljava/io/InputStream;->close()V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_2

    goto :goto_1

    :catchall_2
    move-exception v0

    :try_start_6
    invoke-virtual {p1, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_3

    goto :goto_1

    :catchall_3
    move-exception p1

    :try_start_7
    throw p1

    :cond_1
    :goto_1
    throw p2

    :cond_2
    :goto_2
    const-string v3, "org.codehaus.groovy.plugins.Runners"

    .line 381
    invoke-static {v4, v3}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->plus(Ljava/lang/String;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v2, v5}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v5

    if-eqz v5, :cond_3

    move v7, v6

    goto :goto_3

    :cond_3
    move v7, v0

    :goto_3
    if-eqz v7, :cond_5

    .line 383
    invoke-virtual {v1, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 385
    invoke-virtual {v2, v5}, Ljava/util/zip/ZipFile;->getInputStream(Ljava/util/zip/ZipEntry;)Ljava/io/InputStream;

    move-result-object v3
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_8

    .line 386
    :try_start_8
    invoke-virtual {p2}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, v3, p2, p1}, Lgroovy/grape/GrapeIvy;->processRunners(Ljava/io/InputStream;Ljava/lang/String;Ljava/lang/ClassLoader;)V
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_4

    if-eqz v3, :cond_5

    .line 387
    :try_start_9
    invoke-virtual {v3}, Ljava/io/InputStream;->close()V
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_8

    goto :goto_5

    :catchall_4
    move-exception p1

    :try_start_a
    move-object p2, p1

    check-cast p2, Ljava/lang/Throwable;

    throw p1
    :try_end_a
    .catchall {:try_start_a .. :try_end_a} :catchall_5

    :catchall_5
    move-exception p2

    if-eqz v3, :cond_4

    :try_start_b
    invoke-virtual {v3}, Ljava/io/InputStream;->close()V
    :try_end_b
    .catchall {:try_start_b .. :try_end_b} :catchall_6

    goto :goto_4

    :catchall_6
    move-exception v0

    :try_start_c
    invoke-virtual {p1, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V
    :try_end_c
    .catchall {:try_start_c .. :try_end_c} :catchall_7

    goto :goto_4

    :catchall_7
    move-exception p1

    :try_start_d
    throw p1

    :cond_4
    :goto_4
    throw p2

    .line 391
    :cond_5
    :goto_5
    sget-object p1, Lgroovy/grape/GrapeIvy;->RUNNER_PROVIDER_CONFIG:Ljava/lang/String;

    invoke-static {v4, p1}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->plus(Ljava/lang/String;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v2, p2}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object p2

    if-eqz p2, :cond_6

    move v0, v6

    :cond_6
    if-eqz v0, :cond_7

    .line 392
    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z
    :try_end_d
    .catchall {:try_start_d .. :try_end_d} :catchall_8

    .line 394
    :cond_7
    :try_start_e
    invoke-virtual {v2}, Ljava/util/zip/ZipFile;->close()V
    :try_end_e
    .catch Ljava/util/zip/ZipException; {:try_start_e .. :try_end_e} :catch_0
    .catchall {:try_start_e .. :try_end_e} :catchall_c

    goto :goto_7

    :catchall_8
    move-exception p1

    :try_start_f
    move-object p2, p1

    check-cast p2, Ljava/lang/Throwable;

    throw p1
    :try_end_f
    .catchall {:try_start_f .. :try_end_f} :catchall_9

    :catchall_9
    move-exception p2

    :try_start_10
    invoke-virtual {v2}, Ljava/util/zip/ZipFile;->close()V
    :try_end_10
    .catchall {:try_start_10 .. :try_end_10} :catchall_a

    goto :goto_6

    :catchall_a
    move-exception v0

    :try_start_11
    invoke-virtual {p1, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V
    :try_end_11
    .catchall {:try_start_11 .. :try_end_11} :catchall_b

    :goto_6
    :try_start_12
    throw p2

    :catchall_b
    move-exception p1

    throw p1
    :try_end_12
    .catch Ljava/util/zip/ZipException; {:try_start_12 .. :try_end_12} :catch_0
    .catchall {:try_start_12 .. :try_end_12} :catchall_c

    :catchall_c
    move-exception p1

    .line 397
    throw p1

    :catch_0
    :goto_7
    return-object v1
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/grape/GrapeIvy;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/grape/GrapeIvy;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/grape/GrapeIvy;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public addResolver(Ljava/util/Map;)V
    .locals 3
    .param p1    # Ljava/util/Map;
        .annotation runtime Lgroovy/transform/NamedParams;
            value = {
                .subannotation Lgroovy/transform/NamedParam;
                    required = true
                    type = Ljava/lang/String;
                    value = "name"
                .end subannotation,
                .subannotation Lgroovy/transform/NamedParam;
                    required = true
                    type = Ljava/lang/String;
                    value = "root"
                .end subannotation,
                .subannotation Lgroovy/transform/NamedParam;
                    required = false
                    type = Ljava/lang/Boolean;
                    value = "m2Compatible"
                .end subannotation
            }
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 710
    new-instance v0, Lorg/apache/ivy/plugins/resolver/IBiblioResolver;

    invoke-direct {v0}, Lorg/apache/ivy/plugins/resolver/IBiblioResolver;-><init>()V

    const-string v1, "name"

    .line 711
    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    move-object v2, v0

    check-cast v2, Lorg/apache/ivy/plugins/resolver/IBiblioResolver;

    invoke-virtual {v0, v1}, Lorg/apache/ivy/plugins/resolver/IBiblioResolver;->setName(Ljava/lang/String;)V

    const-string v1, "root"

    .line 712
    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    invoke-virtual {v0, v1}, Lorg/apache/ivy/plugins/resolver/IBiblioResolver;->setRoot(Ljava/lang/String;)V

    .line 713
    iget-object v1, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    check-cast v1, Lorg/apache/ivy/plugins/resolver/ResolverSettings;

    invoke-virtual {v0, v1}, Lorg/apache/ivy/plugins/resolver/IBiblioResolver;->setSettings(Lorg/apache/ivy/plugins/resolver/ResolverSettings;)V

    .line 714
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    const-string v2, "m2Compatible"

    invoke-interface {p1, v2, v1}, Ljava/util/Map;->getOrDefault(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    invoke-virtual {v0, p1}, Lorg/apache/ivy/plugins/resolver/IBiblioResolver;->setM2compatible(Z)V

    .line 717
    iget-object p1, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    const-string v1, "downloadGrapes"

    invoke-virtual {p1, v1}, Lorg/apache/ivy/core/settings/IvySettings;->getResolver(Ljava/lang/String;)Lorg/apache/ivy/plugins/resolver/DependencyResolver;

    move-result-object p1

    const-class v1, Lorg/apache/ivy/plugins/resolver/ChainResolver;

    invoke-static {p1, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/apache/ivy/plugins/resolver/ChainResolver;

    .line 718
    invoke-virtual {p1}, Lorg/apache/ivy/plugins/resolver/ChainResolver;->getResolvers()Ljava/util/List;

    move-result-object p1

    const/4 v1, 0x0

    invoke-interface {p1, v1, v0}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 720
    iget-object p1, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    invoke-static {p1}, Lorg/apache/ivy/Ivy;->newInstance(Lorg/apache/ivy/core/settings/IvySettings;)Lorg/apache/ivy/Ivy;

    move-result-object p1

    iput-object p1, p0, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    new-array p1, v1, [Ljava/lang/Object;

    .line 721
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    const-class v0, Ljava/util/Set;

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Set;

    iput-object p1, p0, Lgroovy/grape/GrapeIvy;->resolvedDependencies:Ljava/util/Set;

    new-array p1, v1, [Ljava/lang/Object;

    .line 722
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    const-class v0, Ljava/util/Set;

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Set;

    iput-object p1, p0, Lgroovy/grape/GrapeIvy;->downloadedArtifacts:Ljava/util/Set;

    return-void
.end method

.method public chooseClassLoader(Ljava/util/Map;)Ljava/lang/ClassLoader;
    .locals 5

    const-string v0, "classLoader"

    .line 164
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v1, Ljava/lang/ClassLoader;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/ClassLoader;

    .line 165
    invoke-direct {p0, v0}, Lgroovy/grape/GrapeIvy;->isValidTargetClassLoader(Ljava/lang/ClassLoader;)Z

    move-result v1

    const/4 v2, 0x1

    xor-int/2addr v1, v2

    if-eqz v1, :cond_8

    const-string v0, "refObject"

    .line 166
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    goto :goto_0

    :cond_0
    move-object v0, v1

    :goto_0
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    const/4 v4, 0x0

    if-eqz v3, :cond_1

    goto :goto_3

    :cond_1
    const-string v0, "calleeDepth"

    .line 167
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result p1

    if-eqz p1, :cond_2

    move v0, v2

    goto :goto_1

    :cond_2
    move v0, v4

    :goto_1
    if-eqz v0, :cond_3

    goto :goto_2

    :cond_3
    move p1, v2

    :goto_2
    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ReflectionUtils;->getCallingClass(I)Ljava/lang/Class;

    move-result-object v0

    :goto_3
    if-eqz v0, :cond_4

    .line 168
    invoke-virtual {v0}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    :cond_4
    move-object v0, v1

    .line 169
    :goto_4
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_5

    invoke-direct {p0, v0}, Lgroovy/grape/GrapeIvy;->isValidTargetClassLoader(Ljava/lang/ClassLoader;)Z

    move-result p1

    xor-int/2addr p1, v2

    if-eqz p1, :cond_5

    move p1, v2

    goto :goto_5

    :cond_5
    move p1, v4

    :goto_5
    if-eqz p1, :cond_6

    .line 170
    invoke-virtual {v0}, Ljava/lang/ClassLoader;->getParent()Ljava/lang/ClassLoader;

    move-result-object v0

    goto :goto_4

    .line 178
    :cond_6
    invoke-direct {p0, v0}, Lgroovy/grape/GrapeIvy;->isValidTargetClassLoader(Ljava/lang/ClassLoader;)Z

    move-result p1

    xor-int/2addr p1, v2

    if-nez p1, :cond_7

    goto :goto_6

    .line 179
    :cond_7
    new-instance p1, Ljava/lang/RuntimeException;

    const-string v0, "No suitable ClassLoader found for grab"

    invoke-direct {p1, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    check-cast p1, Ljava/lang/Throwable;

    throw p1

    :cond_8
    :goto_6
    return-object v0
.end method

.method public createGrabRecord(Ljava/util/Map;)Lgroovy/grape/IvyGrabRecord;
    .locals 9

    const-string v0, "module"

    .line 196
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    const-string v0, "artifactId"

    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    goto :goto_0

    :cond_1
    const-string v0, "artifact"

    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    :goto_0
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Ljava/lang/String;

    .line 197
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x1

    xor-int/2addr v1, v2

    if-nez v1, :cond_12

    .line 202
    new-instance v1, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure2;

    invoke-direct {v1, p0, p0}, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure2;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-static {p1, v1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each(Ljava/util/Map;Lgroovy/lang/Closure;)Ljava/util/Map;

    .line 217
    invoke-interface {p1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v1

    move-object v3, v1

    check-cast v3, Ljava/util/Set;

    new-instance v3, Lgroovy/lang/Reference;

    invoke-direct {v3, v1}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 218
    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Set;

    new-instance v4, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;

    invoke-direct {v4, p0, p0, v3}, Lgroovy/grape/GrapeIvy$_createGrabRecord_closure3;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-static {v1, v4}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each(Ljava/util/Set;Lgroovy/lang/Closure;)Ljava/util/Set;

    const-string v1, "group"

    .line 225
    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    const-string v4, ""

    if-eqz v3, :cond_2

    goto :goto_1

    :cond_2
    const-string v1, "groupId"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_3

    goto :goto_1

    :cond_3
    const-string v1, "organisation"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_4

    goto :goto_1

    :cond_4
    const-string v1, "organization"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_5

    goto :goto_1

    :cond_5
    const-string v1, "org"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_6

    goto :goto_1

    :cond_6
    move-object v1, v4

    :goto_1
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    move-object v3, v1

    check-cast v3, Ljava/lang/String;

    const-string v3, "version"

    .line 227
    invoke-interface {p1, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_7

    goto :goto_2

    :cond_7
    const-string v3, "revision"

    invoke-interface {p1, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_8

    goto :goto_2

    :cond_8
    const-string v3, "rev"

    invoke-interface {p1, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_9

    goto :goto_2

    :cond_9
    const-string v3, "*"

    :goto_2
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    move-object v5, v3

    check-cast v5, Ljava/lang/String;

    const/16 v5, 0x2a

    .line 228
    invoke-static {v5}, Ljava/lang/Character;->valueOf(C)Ljava/lang/Character;

    move-result-object v5

    invoke-static {v3, v5}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_a

    const-string v3, "latest.default"

    :cond_a
    const-string v5, "classifier"

    .line 229
    invoke-interface {p1, v5}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_b

    goto :goto_3

    :cond_b
    const/4 v5, 0x0

    :goto_3
    invoke-static {v5}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v5

    move-object v6, v5

    check-cast v6, Ljava/lang/String;

    const-string v6, "ext"

    .line 230
    invoke-interface {p1, v6}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v7

    const-string v8, "type"

    if-eqz v7, :cond_c

    goto :goto_4

    :cond_c
    invoke-interface {p1, v8}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_d

    goto :goto_4

    :cond_d
    move-object v6, v4

    :goto_4
    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v6

    move-object v7, v6

    check-cast v7, Ljava/lang/String;

    .line 231
    invoke-interface {p1, v8}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_e

    move-object v4, v7

    :cond_e
    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    move-object v7, v4

    check-cast v7, Ljava/lang/String;

    .line 233
    invoke-static {v1, v0, v3}, Lorg/apache/ivy/core/module/id/ModuleRevisionId;->newInstance(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v0

    const-string v1, "force"

    .line 235
    invoke-interface {p1, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_f

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    goto :goto_5

    :cond_f
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    :goto_5
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    const-string v3, "changing"

    .line 236
    invoke-interface {p1, v3}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_10

    invoke-interface {p1, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    goto :goto_6

    :cond_10
    const/4 v3, 0x0

    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v3

    :goto_6
    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v3

    const-string v7, "transitive"

    .line 237
    invoke-interface {p1, v7}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_11

    invoke-interface {p1, v7}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    goto :goto_7

    :cond_11
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v2

    :goto_7
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    .line 239
    new-instance v7, Lgroovy/grape/IvyGrabRecord;

    invoke-direct {v7}, Lgroovy/grape/IvyGrabRecord;-><init>()V

    invoke-virtual {v7, v0}, Lgroovy/grape/IvyGrabRecord;->setMrid(Lorg/apache/ivy/core/module/id/ModuleRevisionId;)V

    invoke-direct {p0, p1}, Lgroovy/grape/GrapeIvy;->getConfList(Ljava/util/Map;)Ljava/util/List;

    move-result-object p1

    invoke-virtual {v7, p1}, Lgroovy/grape/IvyGrabRecord;->setConf(Ljava/util/List;)V

    invoke-virtual {v7, v1}, Lgroovy/grape/IvyGrabRecord;->setForce(Z)V

    invoke-virtual {v7, v3}, Lgroovy/grape/IvyGrabRecord;->setChanging(Z)V

    invoke-virtual {v7, v2}, Lgroovy/grape/IvyGrabRecord;->setTransitive(Z)V

    invoke-virtual {v7, v6}, Lgroovy/grape/IvyGrabRecord;->setExt(Ljava/lang/String;)V

    invoke-virtual {v7, v4}, Lgroovy/grape/IvyGrabRecord;->setType(Ljava/lang/String;)V

    invoke-virtual {v7, v5}, Lgroovy/grape/IvyGrabRecord;->setClassifier(Ljava/lang/String;)V

    return-object v7

    .line 198
    :cond_12
    new-instance p1, Ljava/lang/RuntimeException;

    const-string v0, "grab requires at least a module: or artifactId: or artifact: argument"

    invoke-direct {p1, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    check-cast p1, Ljava/lang/Throwable;

    throw p1
.end method

.method public enumerateGrapes()Ljava/util/Map;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;>;"
        }
    .end annotation

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/Object;

    .line 575
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    const-class v1, Ljava/util/LinkedHashMap;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/LinkedHashMap;

    new-instance v1, Lgroovy/lang/Reference;

    invoke-direct {v1, v0}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const-string v0, "ivy-(.*)\\.xml"

    .line 576
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->bitwiseNegate(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v2, Ljava/util/regex/Pattern;

    invoke-static {v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/regex/Pattern;

    new-instance v2, Lgroovy/lang/Reference;

    invoke-direct {v2, v0}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 577
    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy;->getGrapeCacheDir()Ljava/io/File;

    move-result-object v0

    new-instance v3, Lgroovy/grape/GrapeIvy$_enumerateGrapes_closure13;

    invoke-direct {v3, p0, p0, v1, v2}, Lgroovy/grape/GrapeIvy$_enumerateGrapes_closure13;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ResourceGroovyMethods;->eachDir(Ljava/io/File;Lgroovy/lang/Closure;)V

    .line 589
    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/LinkedHashMap;

    return-object v0
.end method

.method public varargs getDependencies(Ljava/util/Map;[Lgroovy/grape/IvyGrabRecord;)Lorg/apache/ivy/core/report/ResolveReport;
    .locals 28

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object/from16 v0, p2

    .line 420
    iget-object v3, v1, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    invoke-virtual {v3}, Lorg/apache/ivy/Ivy;->getResolutionCacheManager()Lorg/apache/ivy/core/cache/ResolutionCacheManager;

    move-result-object v3

    .line 421
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v4

    .line 422
    new-instance v12, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v6

    move-object v7, v6

    check-cast v7, Ljava/lang/Long;

    invoke-virtual {v6}, Ljava/lang/Long;->toString()Ljava/lang/String;

    move-result-object v6

    new-instance v7, Lgroovy/lang/IntRange;

    const/4 v13, 0x1

    const/4 v8, -0x2

    const/4 v9, -0x1

    invoke-direct {v7, v13, v8, v9}, Lgroovy/lang/IntRange;-><init>(ZII)V

    invoke-static {v6, v7}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->getAt(Ljava/lang/String;Lgroovy/lang/IntRange;)Ljava/lang/String;

    move-result-object v6

    const-string v7, "working"

    invoke-static {v7, v6}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->plus(Ljava/lang/String;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v6

    const-string v7, "caller"

    const-string v8, "all-caller"

    invoke-static {v7, v8, v6}, Lorg/apache/ivy/core/module/id/ModuleRevisionId;->newInstance(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v6

    const-string v7, "integration"

    const/4 v14, 0x0

    invoke-direct {v12, v6, v7, v14, v13}, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;-><init>(Lorg/apache/ivy/core/module/id/ModuleRevisionId;Ljava/lang/String;Ljava/util/Date;Z)V

    .line 423
    const-class v6, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-static {v12, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    new-instance v7, Lorg/apache/ivy/core/module/descriptor/Configuration;

    const-string v8, "default"

    invoke-direct {v7, v8}, Lorg/apache/ivy/core/module/descriptor/Configuration;-><init>(Ljava/lang/String;)V

    invoke-virtual {v6, v7}, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;->addConfiguration(Lorg/apache/ivy/core/module/descriptor/Configuration;)V

    .line 424
    const-class v6, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-static {v12, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-virtual {v6, v4, v5}, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;->setLastModified(J)V

    .line 426
    const-class v4, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-static {v12, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-direct {v1, v2, v4}, Lgroovy/grape/GrapeIvy;->addExcludesIfNeeded(Ljava/util/Map;Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;)Ljava/lang/Object;

    .line 428
    new-instance v4, Lgroovy/lang/Reference;

    invoke-direct {v4, v14}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const/4 v5, 0x2

    const/4 v15, 0x0

    if-eqz v0, :cond_f

    array-length v11, v0

    move v6, v15

    :goto_0
    if-ge v6, v11, :cond_f

    aget-object v7, v0, v6

    move-object v8, v4

    check-cast v8, Lgroovy/lang/Reference;

    invoke-virtual {v4, v7}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    add-int/lit8 v16, v6, 0x1

    .line 429
    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->getConf()Ljava/util/List;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_0

    goto :goto_1

    :cond_0
    new-array v6, v13, [Ljava/lang/Object;

    const-string v7, "*"

    aput-object v7, v6, v15

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v6

    :goto_1
    move-object v10, v6

    .line 430
    const-class v6, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-static {v12, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-virtual {v6}, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;->getDependencies()[Lorg/apache/ivy/core/module/descriptor/DependencyDescriptor;

    move-result-object v6

    const-class v7, [Ljava/lang/Object;

    invoke-static {v6, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [Ljava/lang/Object;

    new-instance v7, Lgroovy/grape/GrapeIvy$_getDependencies_closure7;

    invoke-direct {v7, v1, v1, v4}, Lgroovy/grape/GrapeIvy$_getDependencies_closure7;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-static {v6, v7}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->find([Ljava/lang/Object;Lgroovy/lang/Closure;)Ljava/lang/Object;

    move-result-object v6

    const-class v7, Lorg/apache/ivy/core/module/descriptor/DefaultDependencyDescriptor;

    invoke-static {v6, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/apache/ivy/core/module/descriptor/DefaultDependencyDescriptor;

    new-instance v9, Lgroovy/lang/Reference;

    invoke-direct {v9, v6}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 433
    invoke-virtual {v9}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/apache/ivy/core/module/descriptor/DefaultDependencyDescriptor;

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v6

    xor-int/2addr v6, v13

    if-eqz v6, :cond_1

    .line 434
    new-instance v8, Lorg/apache/ivy/core/module/descriptor/DefaultDependencyDescriptor;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v17

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->isForce()Z

    move-result v18

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->isChanging()Z

    move-result v19

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->isTransitive()Z

    move-result v20

    move-object v6, v8

    move-object v7, v12

    move-object v14, v8

    move-object/from16 v8, v17

    move-object v13, v9

    move/from16 v9, v18

    move-object v15, v10

    move/from16 v10, v19

    move/from16 v19, v11

    move/from16 v11, v20

    invoke-direct/range {v6 .. v11}, Lorg/apache/ivy/core/module/descriptor/DefaultDependencyDescriptor;-><init>(Lorg/apache/ivy/core/module/descriptor/ModuleDescriptor;Lorg/apache/ivy/core/module/id/ModuleRevisionId;ZZZ)V

    move-object v9, v13

    check-cast v9, Lgroovy/lang/Reference;

    invoke-virtual {v13, v14}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    .line 435
    new-instance v6, Lgroovy/grape/GrapeIvy$_getDependencies_closure8;

    invoke-direct {v6, v1, v1, v13}, Lgroovy/grape/GrapeIvy$_getDependencies_closure8;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-static {v15, v6}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each(Ljava/util/List;Lgroovy/lang/Closure;)Ljava/util/List;

    .line 436
    const-class v6, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-static {v12, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;

    invoke-virtual {v13}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lorg/apache/ivy/core/module/descriptor/DefaultDependencyDescriptor;

    invoke-virtual {v6, v7}, Lorg/apache/ivy/core/module/descriptor/DefaultModuleDescriptor;->addDependency(Lorg/apache/ivy/core/module/descriptor/DependencyDescriptor;)V

    goto :goto_2

    :cond_1
    move-object v13, v9

    move-object v15, v10

    move/from16 v19, v11

    .line 439
    :goto_2
    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->getClassifier()Ljava/lang/String;

    move-result-object v6

    if-eqz v6, :cond_2

    const/4 v6, 0x1

    goto :goto_3

    :cond_2
    const/4 v6, 0x0

    :goto_3
    const-string v7, "jar"

    if-nez v6, :cond_6

    .line 440
    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->getExt()Ljava/lang/String;

    move-result-object v6

    if-eqz v6, :cond_3

    const/4 v6, 0x1

    goto :goto_4

    :cond_3
    const/4 v6, 0x0

    :goto_4
    if-eqz v6, :cond_4

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->getExt()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_4

    const/4 v6, 0x1

    goto :goto_5

    :cond_4
    const/4 v6, 0x0

    :goto_5
    if-eqz v6, :cond_5

    goto :goto_6

    :cond_5
    const/4 v6, 0x0

    goto :goto_7

    :cond_6
    :goto_6
    const/4 v6, 0x1

    :goto_7
    if-nez v6, :cond_a

    .line 441
    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->getType()Ljava/lang/String;

    move-result-object v6

    if-eqz v6, :cond_7

    const/4 v6, 0x1

    goto :goto_8

    :cond_7
    const/4 v6, 0x0

    :goto_8
    if-eqz v6, :cond_8

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v6}, Lgroovy/grape/IvyGrabRecord;->getType()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_8

    const/4 v6, 0x1

    goto :goto_9

    :cond_8
    const/4 v6, 0x0

    :goto_9
    if-eqz v6, :cond_9

    goto :goto_a

    :cond_9
    const/4 v6, 0x0

    goto :goto_b

    :cond_a
    :goto_a
    const/4 v6, 0x1

    :goto_b
    if-eqz v6, :cond_e

    .line 443
    new-instance v6, Lorg/apache/ivy/core/module/descriptor/DefaultDependencyArtifactDescriptor;

    invoke-virtual {v13}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    move-object/from16 v22, v8

    check-cast v22, Lorg/apache/ivy/core/module/descriptor/DefaultDependencyDescriptor;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v8}, Lgroovy/grape/IvyGrabRecord;->getMrid()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v8

    invoke-virtual {v8}, Lorg/apache/ivy/core/module/id/ModuleRevisionId;->getName()Ljava/lang/String;

    move-result-object v23

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v8}, Lgroovy/grape/IvyGrabRecord;->getType()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_b

    move-object/from16 v24, v8

    goto :goto_c

    :cond_b
    move-object/from16 v24, v7

    :goto_c
    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v8}, Lgroovy/grape/IvyGrabRecord;->getExt()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_c

    move-object/from16 v25, v8

    goto :goto_d

    :cond_c
    move-object/from16 v25, v7

    :goto_d
    const/16 v26, 0x0

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v7}, Lgroovy/grape/IvyGrabRecord;->getClassifier()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_d

    new-array v7, v5, [Ljava/lang/Object;

    const-string v8, "classifier"

    const/4 v9, 0x0

    aput-object v8, v7, v9

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v8

    check-cast v8, Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {v8}, Lgroovy/grape/IvyGrabRecord;->getClassifier()Ljava/lang/String;

    move-result-object v8

    const/4 v9, 0x1

    aput-object v8, v7, v9

    invoke-static {v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v7

    goto :goto_e

    :cond_d
    const/4 v7, 0x0

    :goto_e
    const-class v8, Ljava/util/Map;

    invoke-static {v7, v8}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v7

    move-object/from16 v27, v7

    check-cast v27, Ljava/util/Map;

    move-object/from16 v21, v6

    invoke-direct/range {v21 .. v27}, Lorg/apache/ivy/core/module/descriptor/DefaultDependencyArtifactDescriptor;-><init>(Lorg/apache/ivy/core/module/descriptor/DependencyDescriptor;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/net/URL;Ljava/util/Map;)V

    new-instance v7, Lgroovy/lang/Reference;

    invoke-direct {v7, v6}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 444
    new-instance v6, Lgroovy/grape/GrapeIvy$_getDependencies_closure9;

    invoke-direct {v6, v1, v1, v13, v7}, Lgroovy/grape/GrapeIvy$_getDependencies_closure9;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V

    invoke-static {v15, v6}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each(Ljava/util/List;Lgroovy/lang/Closure;)Ljava/util/List;

    :cond_e
    move/from16 v6, v16

    move/from16 v11, v19

    const/4 v13, 0x1

    const/4 v14, 0x0

    const/4 v15, 0x0

    goto/16 :goto_0

    .line 449
    :cond_f
    new-instance v4, Lorg/apache/ivy/core/resolve/ResolveOptions;

    invoke-direct {v4}, Lorg/apache/ivy/core/resolve/ResolveOptions;-><init>()V

    .line 450
    sget-object v0, Lgroovy/grape/GrapeIvy;->DEFAULT_CONF:Ljava/util/List;

    const-class v6, [Ljava/lang/String;

    invoke-static {v0, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/lang/String;

    invoke-virtual {v4, v0}, Lorg/apache/ivy/core/resolve/ResolveOptions;->setConfs([Ljava/lang/String;)Lorg/apache/ivy/core/resolve/ResolveOptions;

    const/4 v6, 0x0

    invoke-virtual {v4, v6}, Lorg/apache/ivy/core/resolve/ResolveOptions;->setOutputReport(Z)Lorg/apache/ivy/core/resolve/ResolveOptions;

    const-string v0, "validate"

    .line 451
    invoke-interface {v2, v0}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_10

    invoke-interface {v2, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    goto :goto_f

    :cond_10
    const/4 v0, 0x0

    :goto_f
    invoke-virtual {v4, v0}, Lorg/apache/ivy/core/resolve/ResolveOptions;->setValidate(Z)Lorg/apache/ivy/core/resolve/ResolveOptions;

    .line 453
    iget-object v0, v1, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    invoke-virtual {v0}, Lorg/apache/ivy/Ivy;->getSettings()Lorg/apache/ivy/core/settings/IvySettings;

    move-result-object v0

    const-string v6, "autoDownload"

    invoke-interface {v2, v6}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    invoke-static {v6}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_11

    const-string v6, "downloadGrapes"

    goto :goto_10

    :cond_11
    const-string v6, "cachedGrapes"

    :goto_10
    invoke-virtual {v0, v6}, Lorg/apache/ivy/core/settings/IvySettings;->setDefaultResolver(Ljava/lang/String;)V

    const-string v0, "disableChecksums"

    .line 454
    invoke-interface {v2, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v0

    const-string v6, ""

    if-eqz v0, :cond_12

    .line 455
    iget-object v0, v1, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    invoke-virtual {v0}, Lorg/apache/ivy/Ivy;->getSettings()Lorg/apache/ivy/core/settings/IvySettings;

    move-result-object v0

    const-string v7, "ivy.checksums"

    invoke-virtual {v0, v7, v6}, Lorg/apache/ivy/core/settings/IvySettings;->setVariable(Ljava/lang/String;Ljava/lang/String;)V

    :cond_12
    const-string v0, "groovy.grape.report.downloads"

    .line 457
    invoke-static {v0}, Ljava/lang/Boolean;->getBoolean(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_13

    .line 459
    invoke-direct/range {p0 .. p0}, Lgroovy/grape/GrapeIvy;->addIvyListener()Ljava/lang/Object;

    :cond_13
    const/16 v0, 0x8

    move v8, v0

    .line 466
    :goto_11
    :try_start_0
    iget-object v0, v1, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    invoke-virtual {v0, v12, v4}, Lorg/apache/ivy/Ivy;->resolve(Lorg/apache/ivy/core/module/descriptor/ModuleDescriptor;Lorg/apache/ivy/core/resolve/ResolveOptions;)Lorg/apache/ivy/core/report/ResolveReport;

    move-result-object v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 478
    invoke-virtual {v0}, Lorg/apache/ivy/core/report/ResolveReport;->hasError()Z

    move-result v4

    if-nez v4, :cond_19

    .line 481
    invoke-virtual {v0}, Lorg/apache/ivy/core/report/ResolveReport;->getDownloadSize()J

    move-result-wide v8

    invoke-static {v8, v9}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_14

    if-eqz v7, :cond_14

    const/4 v4, 0x1

    goto :goto_12

    :cond_14
    const/4 v4, 0x0

    :goto_12
    if-eqz v4, :cond_17

    .line 482
    sget-object v4, Ljava/lang/System;->err:Ljava/io/PrintStream;

    new-instance v7, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v8, 0x3

    new-array v8, v8, [Ljava/lang/Object;

    invoke-virtual {v0}, Lorg/apache/ivy/core/report/ResolveReport;->getDownloadSize()J

    move-result-wide v9

    const/16 v11, 0xa

    shr-long/2addr v9, v11

    invoke-static {v9, v10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v9

    const/4 v10, 0x0

    aput-object v9, v8, v10

    invoke-virtual {v0}, Lorg/apache/ivy/core/report/ResolveReport;->getDownloadTime()J

    move-result-wide v9

    invoke-static {v9, v10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v9

    const/4 v10, 0x1

    aput-object v9, v8, v10

    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    invoke-virtual {v0}, Lorg/apache/ivy/core/report/ResolveReport;->getAllArtifactsReports()[Lorg/apache/ivy/core/report/ArtifactDownloadReport;

    move-result-object v10

    if-eqz v10, :cond_16

    if-eqz v10, :cond_16

    array-length v11, v10

    const/4 v15, 0x0

    :goto_13
    if-ge v15, v11, :cond_16

    aget-object v12, v10, v15

    add-int/lit8 v15, v15, 0x1

    if-eqz v12, :cond_15

    invoke-virtual {v12}, Lorg/apache/ivy/core/report/ArtifactDownloadReport;->toString()Ljava/lang/String;

    move-result-object v12

    goto :goto_14

    :cond_15
    const/4 v12, 0x0

    :goto_14
    invoke-virtual {v9, v12}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_13

    :cond_16
    const-string v10, "\n  "

    invoke-static {v9, v10}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->join(Ljava/lang/Iterable;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v9

    aput-object v9, v8, v5

    const-string v5, "Downloaded "

    const-string v9, " Kbytes in "

    const-string v10, "ms:\n  "

    filled-new-array {v5, v9, v10, v6}, [Ljava/lang/String;

    move-result-object v5

    invoke-direct {v7, v8, v5}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v4, v7}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->println(Ljava/io/PrintStream;Ljava/lang/Object;)V

    .line 484
    :cond_17
    invoke-virtual {v0}, Lorg/apache/ivy/core/report/ResolveReport;->getModuleDescriptor()Lorg/apache/ivy/core/module/descriptor/ModuleDescriptor;

    move-result-object v4

    const-string v5, "preserveFiles"

    .line 486
    invoke-interface {v2, v5}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    const/4 v5, 0x1

    xor-int/2addr v2, v5

    if-eqz v2, :cond_18

    .line 487
    invoke-interface {v4}, Lorg/apache/ivy/core/module/descriptor/ModuleDescriptor;->getModuleRevisionId()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v2

    invoke-interface {v3, v2}, Lorg/apache/ivy/core/cache/ResolutionCacheManager;->getResolvedIvyFileInCache(Lorg/apache/ivy/core/module/id/ModuleRevisionId;)Ljava/io/File;

    move-result-object v2

    invoke-virtual {v2}, Ljava/io/File;->delete()Z

    .line 488
    invoke-interface {v4}, Lorg/apache/ivy/core/module/descriptor/ModuleDescriptor;->getModuleRevisionId()Lorg/apache/ivy/core/module/id/ModuleRevisionId;

    move-result-object v2

    invoke-interface {v3, v2}, Lorg/apache/ivy/core/cache/ResolutionCacheManager;->getResolvedIvyPropertiesInCache(Lorg/apache/ivy/core/module/id/ModuleRevisionId;)Ljava/io/File;

    move-result-object v2

    invoke-virtual {v2}, Ljava/io/File;->delete()Z

    :cond_18
    return-object v0

    .line 479
    :cond_19
    new-instance v2, Ljava/lang/RuntimeException;

    new-instance v3, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v4, 0x1

    new-array v4, v4, [Ljava/lang/Object;

    invoke-virtual {v0}, Lorg/apache/ivy/core/report/ResolveReport;->getAllProblemMessages()Ljava/util/List;

    move-result-object v0

    const/4 v5, 0x0

    aput-object v0, v4, v5

    const-string v0, "Error grabbing Grapes -- "

    filled-new-array {v0, v6}, [Ljava/lang/String;

    move-result-object v0

    invoke-direct {v3, v4, v0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    move-object v3, v0

    check-cast v3, Ljava/lang/String;

    invoke-direct {v2, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    check-cast v2, Ljava/lang/Throwable;

    throw v2

    :catchall_0
    move-exception v0

    goto :goto_18

    :catch_0
    move-exception v0

    add-int/lit8 v9, v8, -0x1

    if-eqz v8, :cond_1a

    const/4 v8, 0x1

    goto :goto_15

    :cond_1a
    const/4 v8, 0x0

    :goto_15
    if-eqz v8, :cond_1e

    if-eqz v7, :cond_1b

    .line 470
    :try_start_1
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v8, "Grab Error: retrying..."

    invoke-virtual {v0, v8}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    :cond_1b
    const/4 v0, 0x4

    if-le v9, v0, :cond_1c

    const/4 v0, 0x1

    goto :goto_16

    :cond_1c
    const/4 v0, 0x0

    :goto_16
    if-eqz v0, :cond_1d

    const/16 v0, 0x15e

    goto :goto_17

    :cond_1d
    const/16 v0, 0x3e8

    :goto_17
    int-to-long v10, v0

    const/4 v8, 0x0

    .line 471
    invoke-static {v8, v10, v11}, Lorg/codehaus/groovy/runtime/DefaultGroovyStaticMethods;->sleep(Ljava/lang/Object;J)V

    move v8, v9

    goto/16 :goto_11

    .line 474
    :cond_1e
    new-instance v2, Ljava/lang/RuntimeException;

    new-instance v3, Lorg/codehaus/groovy/runtime/GStringImpl;

    const/4 v4, 0x1

    new-array v4, v4, [Ljava/lang/Object;

    move-object v5, v0

    check-cast v5, Ljava/io/IOException;

    invoke-virtual {v0}, Ljava/io/IOException;->getMessage()Ljava/lang/String;

    move-result-object v0

    const/4 v5, 0x0

    aput-object v0, v4, v5

    const-string v0, "Error grabbing grapes -- "

    filled-new-array {v0, v6}, [Ljava/lang/String;

    move-result-object v0

    invoke-direct {v3, v4, v0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    move-object v3, v0

    check-cast v3, Ljava/lang/String;

    invoke-direct {v2, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    check-cast v2, Ljava/lang/Throwable;

    throw v2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 475
    :goto_18
    throw v0
.end method

.method public getDownloadedArtifacts()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->downloadedArtifacts:Ljava/util/Set;

    return-object v0
.end method

.method public getEnableGrapes()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/grape/GrapeIvy;->enableGrapes:Z

    return v0
.end method

.method public final getGrabRecordsForCurrDependencies()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Lgroovy/grape/IvyGrabRecord;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->grabRecordsForCurrDependencies:Ljava/util/Set;

    return-object v0
.end method

.method public getGrapeCacheDir()Ljava/io/File;
    .locals 5

    .line 145
    new-instance v0, Ljava/io/File;

    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy;->getGrapeDir()Ljava/io/File;

    move-result-object v1

    const-string v2, "grapes"

    invoke-direct {v0, v1, v2}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 146
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v1

    const/4 v2, 0x1

    xor-int/2addr v1, v2

    if-eqz v1, :cond_0

    .line 147
    invoke-virtual {v0}, Ljava/io/File;->mkdirs()Z

    goto :goto_0

    .line 148
    :cond_0
    invoke-virtual {v0}, Ljava/io/File;->isDirectory()Z

    move-result v1

    xor-int/2addr v1, v2

    if-nez v1, :cond_1

    :goto_0
    return-object v0

    .line 149
    :cond_1
    new-instance v1, Ljava/lang/RuntimeException;

    new-instance v3, Lorg/codehaus/groovy/runtime/GStringImpl;

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v4, 0x0

    aput-object v0, v2, v4

    const-string v0, "The grape cache dir "

    const-string v4, " is not a directory"

    filled-new-array {v0, v4}, [Ljava/lang/String;

    move-result-object v0

    invoke-direct {v3, v2, v0}, Lorg/codehaus/groovy/runtime/GStringImpl;-><init>([Ljava/lang/Object;[Ljava/lang/String;)V

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    move-object v2, v0

    check-cast v2, Ljava/lang/String;

    invoke-direct {v1, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    check-cast v1, Ljava/lang/Throwable;

    throw v1
.end method

.method public getGrapeDir()Ljava/io/File;
    .locals 2

    const-string v0, "grape.root"

    .line 131
    invoke-static {v0}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    if-eqz v1, :cond_1

    .line 133
    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy;->getGroovyRoot()Ljava/io/File;

    move-result-object v0

    return-object v0

    .line 135
    :cond_1
    new-instance v1, Ljava/io/File;

    invoke-direct {v1, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 137
    :try_start_0
    invoke-virtual {v1}, Ljava/io/File;->getCanonicalFile()Ljava/io/File;

    move-result-object v1
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception v0

    .line 140
    throw v0

    :catch_0
    :goto_1
    return-object v1
.end method

.method public getGroovyRoot()Ljava/io/File;
    .locals 3

    const-string v0, "groovy.root"

    .line 115
    invoke-static {v0}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    if-eqz v1, :cond_1

    .line 118
    new-instance v0, Ljava/io/File;

    const-string v1, "user.home"

    invoke-static {v1}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    const-string v2, ".groovy"

    invoke-direct {v0, v1, v2}, Ljava/io/File;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    .line 120
    :cond_1
    new-instance v1, Ljava/io/File;

    invoke-direct {v1, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    move-object v0, v1

    .line 123
    :goto_1
    :try_start_0
    invoke-virtual {v0}, Ljava/io/File;->getCanonicalFile()Ljava/io/File;

    move-result-object v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_2

    :catchall_0
    move-exception v0

    .line 126
    throw v0

    :catch_0
    :goto_2
    return-object v0
.end method

.method public getIvyInstance()Lorg/apache/ivy/Ivy;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    return-object v0
.end method

.method public final getLoadedDeps()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/ClassLoader;",
            "Ljava/util/Set<",
            "Lgroovy/grape/IvyGrabRecord;",
            ">;>;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->loadedDeps:Ljava/util/Map;

    return-object v0
.end method

.method public getLocalGrapeConfig()Ljava/io/File;
    .locals 3

    const-string v0, "grape.config"

    .line 155
    invoke-static {v0}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 156
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 157
    new-instance v1, Ljava/io/File;

    invoke-direct {v1, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    return-object v1

    .line 159
    :cond_0
    new-instance v0, Ljava/io/File;

    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy;->getGrapeDir()Ljava/io/File;

    move-result-object v1

    const-string v2, "grapeConfig.xml"

    invoke-direct {v0, v1, v2}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/grape/GrapeIvy;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getResolvedDependencies()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->resolvedDependencies:Ljava/util/Set;

    return-object v0
.end method

.method public getSettings()Lorg/apache/ivy/core/settings/IvySettings;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    return-object v0
.end method

.method public grab(Ljava/lang/String;)Ljava/lang/Object;
    .locals 3

    const/4 v0, 0x6

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    const-string v2, "group"

    aput-object v2, v0, v1

    const/4 v1, 0x1

    const-string v2, "groovy.endorsed"

    aput-object v2, v0, v1

    const/4 v1, 0x2

    const-string v2, "module"

    aput-object v2, v0, v1

    const/4 v1, 0x3

    aput-object p1, v0, v1

    const/4 p1, 0x4

    const-string v1, "version"

    aput-object v1, v0, p1

    .line 254
    invoke-static {}, Lgroovy/lang/GroovySystem;->getVersion()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x5

    aput-object p1, v0, v1

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovy/grape/GrapeIvy;->grab(Ljava/util/Map;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public grab(Ljava/util/Map;)Ljava/lang/Object;
    .locals 5

    const-string v0, "calleeDepth"

    .line 259
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    const/4 v3, 0x1

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    sget v1, Lgroovy/grape/GrapeIvy;->DEFAULT_CALLEE_DEPTH:I

    add-int/2addr v1, v3

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    :goto_0
    const/4 v2, 0x0

    move-object v4, v0

    check-cast v4, Ljava/lang/String;

    invoke-static {v1, v2, p1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->setProperty(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;)V

    new-array v0, v3, [Ljava/util/Map;

    const/4 v1, 0x0

    aput-object p1, v0, v1

    .line 260
    invoke-virtual {p0, p1, v0}, Lgroovy/grape/GrapeIvy;->grab(Ljava/util/Map;[Ljava/util/Map;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public varargs grab(Ljava/util/Map;[Ljava/util/Map;)Ljava/lang/Object;
    .locals 7

    const-string v0, "calleeDepth"

    const-string v1, "classLoader"

    const-string v2, "refObject"

    .line 266
    iget-object v3, p0, Lgroovy/grape/GrapeIvy;->grabRecordsForCurrDependencies:Ljava/util/Set;

    invoke-interface {v3}, Ljava/util/Set;->clear()V

    const/4 v3, 0x6

    const/4 v4, 0x0

    :try_start_0
    new-array v3, v3, [Ljava/lang/Object;

    const/4 v5, 0x0

    aput-object v2, v3, v5

    .line 271
    invoke-interface {p1, v2}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/4 v6, 0x1

    aput-object v2, v3, v6

    const/4 v2, 0x2

    aput-object v1, v3, v2

    const/4 v2, 0x3

    .line 272
    invoke-interface {p1, v1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    aput-object v1, v3, v2

    const/4 v1, 0x4

    aput-object v0, v3, v1

    const/4 v1, 0x5

    .line 273
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    :cond_0
    sget v0, Lgroovy/grape/GrapeIvy;->DEFAULT_CALLEE_DEPTH:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    :goto_0
    aput-object v0, v3, v1

    invoke-static {v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovy/grape/GrapeIvy;->chooseClassLoader(Ljava/util/Map;)Ljava/lang/ClassLoader;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_1
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 278
    :try_start_1
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v1

    xor-int/2addr v1, v6

    if-eqz v1, :cond_1

    return-object v4

    .line 280
    :cond_1
    invoke-virtual {p0, v0, p1, p2}, Lgroovy/grape/GrapeIvy;->resolve(Ljava/lang/ClassLoader;Ljava/util/Map;[Ljava/util/Map;)[Ljava/net/URI;

    move-result-object p2

    if-eqz p2, :cond_2

    .line 281
    array-length v1, p2

    move v2, v5

    :goto_1
    if-ge v2, v1, :cond_2

    aget-object v3, p2, v2

    add-int/lit8 v2, v2, 0x1

    .line 282
    invoke-direct {p0, v0, v3}, Lgroovy/grape/GrapeIvy;->addURL(Ljava/lang/ClassLoader;Ljava/net/URI;)V

    goto :goto_1

    :cond_2
    if-eqz p2, :cond_5

    .line 285
    array-length v1, p2

    move v2, v5

    :cond_3
    :goto_2
    if-ge v5, v1, :cond_4

    aget-object v3, p2, v5

    add-int/lit8 v5, v5, 0x1

    .line 287
    new-instance v6, Ljava/io/File;

    invoke-direct {v6, v3}, Ljava/io/File;-><init>(Ljava/net/URI;)V

    .line 288
    invoke-direct {p0, v0, v6}, Lgroovy/grape/GrapeIvy;->processCategoryMethods(Ljava/lang/ClassLoader;Ljava/io/File;)Ljava/lang/Object;

    .line 289
    invoke-direct {p0, v0, v6}, Lgroovy/grape/GrapeIvy;->processMetaInfServices(Ljava/lang/ClassLoader;Ljava/io/File;)Ljava/util/Collection;

    move-result-object v3

    xor-int/lit8 v6, v2, 0x1

    if-eqz v6, :cond_3

    .line 291
    sget-object v2, Lgroovy/grape/GrapeIvy;->RUNNER_PROVIDER_CONFIG:Ljava/lang/String;

    invoke-interface {v3, v2}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result v2

    goto :goto_2

    :cond_4
    move v5, v2

    :cond_5
    if-eqz v5, :cond_6

    .line 295
    invoke-static {}, Lorg/apache/groovy/plugin/GroovyRunnerRegistry;->getInstance()Lorg/apache/groovy/plugin/GroovyRunnerRegistry;

    move-result-object p2

    invoke-virtual {p2, v0}, Lorg/apache/groovy/plugin/GroovyRunnerRegistry;->load(Ljava/lang/ClassLoader;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :cond_6
    return-object v4

    :catch_0
    move-exception p2

    move-object v4, v0

    goto :goto_3

    :catchall_0
    move-exception p1

    goto :goto_4

    :catch_1
    move-exception p2

    .line 299
    :goto_3
    :try_start_2
    invoke-direct {p0, v4}, Lgroovy/grape/GrapeIvy;->getLoadedDepsForLoader(Ljava/lang/ClassLoader;)Ljava/util/Set;

    move-result-object v0

    .line 300
    iget-object v1, p0, Lgroovy/grape/GrapeIvy;->grabRecordsForCurrDependencies:Ljava/util/Set;

    invoke-interface {v0, v1}, Ljava/util/Set;->removeAll(Ljava/util/Collection;)Z

    .line 301
    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->grabRecordsForCurrDependencies:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->clear()V

    const-string v0, "noExceptions"

    .line 303
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_7

    return-object p2

    .line 306
    :cond_7
    check-cast p2, Ljava/lang/Throwable;

    throw p2
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 307
    :goto_4
    throw p1
.end method

.method public isEnableGrapes()Z
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-boolean v0, p0, Lgroovy/grape/GrapeIvy;->enableGrapes:Z

    return v0
.end method

.method public listDependencies(Ljava/lang/ClassLoader;)[Ljava/util/Map;
    .locals 1

    .line 672
    iget-object v0, p0, Lgroovy/grape/GrapeIvy;->loadedDeps:Ljava/util/Map;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->getAt(Ljava/util/Map;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    if-eqz p1, :cond_0

    const-class v0, Ljava/lang/Iterable;

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Iterable;

    new-instance v0, Lgroovy/grape/GrapeIvy$_listDependencies_closure17;

    invoke-direct {v0, p0, p0}, Lgroovy/grape/GrapeIvy$_listDependencies_closure17;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->collect(Ljava/lang/Iterable;Lgroovy/lang/Closure;)Ljava/util/List;

    move-result-object p1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    .line 701
    :goto_0
    const-class v0, [Ljava/util/Map;

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/util/Map;

    return-object p1
.end method

.method public processOtherServices(Ljava/lang/ClassLoader;Ljava/io/File;)V
    .locals 0

    .line 356
    invoke-direct {p0, p1, p2}, Lgroovy/grape/GrapeIvy;->processMetaInfServices(Ljava/lang/ClassLoader;Ljava/io/File;)Ljava/util/Collection;

    return-void
.end method

.method public processRunners(Ljava/io/InputStream;Ljava/lang/String;Ljava/lang/ClassLoader;)V
    .locals 6

    new-instance v4, Lgroovy/lang/Reference;

    invoke-direct {v4, p2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    new-instance v5, Lgroovy/lang/Reference;

    invoke-direct {v5, p3}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 408
    invoke-static {}, Lorg/apache/groovy/plugin/GroovyRunnerRegistry;->getInstance()Lorg/apache/groovy/plugin/GroovyRunnerRegistry;

    move-result-object p2

    new-instance v3, Lgroovy/lang/Reference;

    invoke-direct {v3, p2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 409
    new-instance p2, Ljava/util/ArrayList;

    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/IOGroovyMethods;->getText(Ljava/io/InputStream;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->readLines(Ljava/lang/CharSequence;)Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_2

    const/4 p3, 0x0

    if-eqz p1, :cond_0

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    goto :goto_0

    :cond_0
    move-object p1, p3

    :goto_0
    if-eqz p1, :cond_2

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Ljava/lang/String;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v0

    goto :goto_2

    :cond_1
    move-object v0, p3

    :goto_2
    invoke-virtual {p2, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_2
    new-instance p1, Lgroovy/grape/GrapeIvy$_processRunners_closure6;

    move-object v0, p1

    move-object v1, p0

    move-object v2, p0

    invoke-direct/range {v0 .. v5}, Lgroovy/grape/GrapeIvy$_processRunners_closure6;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V

    invoke-static {p2, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each(Ljava/util/List;Lgroovy/lang/Closure;)Ljava/util/List;

    return-void
.end method

.method public processSerializedCategoryMethods(Ljava/io/InputStream;)V
    .locals 1

    .line 402
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/IOGroovyMethods;->getText(Ljava/io/InputStream;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/StringGroovyMethods;->readLines(Ljava/lang/CharSequence;)Ljava/util/List;

    move-result-object p1

    new-instance v0, Lgroovy/grape/GrapeIvy$_processSerializedCategoryMethods_closure5;

    invoke-direct {v0, p0, p0}, Lgroovy/grape/GrapeIvy$_processSerializedCategoryMethods_closure5;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each(Ljava/util/List;Lgroovy/lang/Closure;)Ljava/util/List;

    return-void
.end method

.method public varargs resolve(Ljava/lang/ClassLoader;Ljava/util/Map;Ljava/util/List;[Ljava/util/Map;)[Ljava/net/URI;
    .locals 5

    new-instance v0, Lgroovy/lang/Reference;

    invoke-direct {v0, p3}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 621
    iget-boolean p3, p0, Lgroovy/grape/GrapeIvy;->enableGrapes:Z

    const/4 v1, 0x1

    xor-int/2addr p3, v1

    const/4 v2, 0x0

    if-eqz p3, :cond_0

    new-array p1, v1, [I

    aput v2, p1, v2

    .line 622
    const-class p2, Ljava/net/URI;

    invoke-static {p2, p1}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;[I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/net/URI;

    return-object p1

    .line 625
    :cond_0
    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Ljava/util/List;

    if-eqz p3, :cond_1

    move p3, v1

    goto :goto_0

    :cond_1
    move p3, v2

    .line 626
    :goto_0
    invoke-direct {p0, p1}, Lgroovy/grape/GrapeIvy;->getLoadedDepsForLoader(Ljava/lang/ClassLoader;)Ljava/util/Set;

    move-result-object p1

    new-instance v3, Lgroovy/lang/Reference;

    invoke-direct {v3, p1}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 627
    const-class p1, [Ljava/lang/Object;

    invoke-static {p4, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    new-instance p4, Lgroovy/grape/GrapeIvy$_resolve_closure14;

    invoke-direct {p4, p0, p0, v3}, Lgroovy/grape/GrapeIvy$_resolve_closure14;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-static {p1, p4}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each([Ljava/lang/Object;Lgroovy/lang/Closure;)[Ljava/lang/Object;

    .line 640
    :try_start_0
    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Set;

    const-class p4, [Lgroovy/grape/IvyGrabRecord;

    invoke-static {p1, p4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Lgroovy/grape/IvyGrabRecord;

    const-class p4, [Ljava/lang/Object;

    invoke-static {p1, p4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Object;

    invoke-static {p1, v1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->reverse([Ljava/lang/Object;Z)[Ljava/lang/Object;

    move-result-object p1

    const-class p4, [Lgroovy/grape/IvyGrabRecord;

    invoke-static {p1, p4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Lgroovy/grape/IvyGrabRecord;

    invoke-virtual {p0, p2, p1}, Lgroovy/grape/GrapeIvy;->getDependencies(Ljava/util/Map;[Lgroovy/grape/IvyGrabRecord;)Lorg/apache/ivy/core/report/ResolveReport;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    new-array p2, v2, [Ljava/lang/Object;

    .line 647
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p2

    const-class p4, Ljava/util/ArrayList;

    invoke-static {p2, p4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/util/ArrayList;

    .line 648
    invoke-virtual {p1}, Lorg/apache/ivy/core/report/ResolveReport;->getAllArtifactsReports()[Lorg/apache/ivy/core/report/ArtifactDownloadReport;

    move-result-object p4

    if-eqz p4, :cond_3

    array-length v1, p4

    :cond_2
    :goto_1
    if-ge v2, v1, :cond_3

    aget-object v3, p4, v2

    add-int/lit8 v2, v2, 0x1

    .line 650
    invoke-virtual {v3}, Lorg/apache/ivy/core/report/ArtifactDownloadReport;->getLocalFile()Ljava/io/File;

    move-result-object v4

    invoke-static {v4}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_2

    .line 651
    invoke-virtual {v3}, Lorg/apache/ivy/core/report/ArtifactDownloadReport;->getLocalFile()Ljava/io/File;

    move-result-object v3

    invoke-virtual {v3}, Ljava/io/File;->toURI()Ljava/net/URI;

    move-result-object v3

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->plus(Ljava/util/List;Ljava/lang/Object;)Ljava/util/List;

    move-result-object p2

    const-class v3, Ljava/util/ArrayList;

    invoke-static {p2, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/util/ArrayList;

    goto :goto_1

    :cond_3
    if-eqz p3, :cond_4

    .line 656
    invoke-virtual {p1}, Lorg/apache/ivy/core/report/ResolveReport;->getDependencies()Ljava/util/List;

    move-result-object p1

    new-instance p3, Lgroovy/grape/GrapeIvy$_resolve_closure15;

    invoke-direct {p3, p0, p0, v0}, Lgroovy/grape/GrapeIvy$_resolve_closure15;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;)V

    invoke-static {p1, p3}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->each(Ljava/util/List;Lgroovy/lang/Closure;)Ljava/util/List;

    .line 662
    :cond_4
    const-class p1, [Ljava/net/URI;

    invoke-static {p2, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->asType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/net/URI;

    return-object p1

    :catchall_0
    move-exception p1

    goto :goto_2

    :catch_0
    move-exception p1

    .line 642
    :try_start_1
    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/util/Set;

    iget-object p3, p0, Lgroovy/grape/GrapeIvy;->grabRecordsForCurrDependencies:Ljava/util/Set;

    invoke-interface {p2, p3}, Ljava/util/Set;->removeAll(Ljava/util/Collection;)Z

    .line 643
    iget-object p2, p0, Lgroovy/grape/GrapeIvy;->grabRecordsForCurrDependencies:Ljava/util/Set;

    invoke-interface {p2}, Ljava/util/Set;->clear()V

    .line 644
    check-cast p1, Ljava/lang/Throwable;

    throw p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 645
    :goto_2
    throw p1
.end method

.method public varargs resolve(Ljava/lang/ClassLoader;Ljava/util/Map;[Ljava/util/Map;)[Ljava/net/URI;
    .locals 1

    const/4 v0, 0x0

    .line 616
    invoke-virtual {p0, p1, p2, v0, p3}, Lgroovy/grape/GrapeIvy;->resolve(Ljava/lang/ClassLoader;Ljava/util/Map;Ljava/util/List;[Ljava/util/Map;)[Ljava/net/URI;

    move-result-object p1

    return-object p1
.end method

.method public varargs resolve(Ljava/util/Map;Ljava/util/List;[Ljava/util/Map;)[Ljava/net/URI;
    .locals 5

    const/4 v0, 0x6

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    const-string v2, "refObject"

    aput-object v2, v0, v1

    .line 601
    invoke-interface {p1, v2}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/4 v3, 0x1

    aput-object v2, v0, v3

    const/4 v2, 0x2

    const-string v4, "classLoader"

    aput-object v4, v0, v2

    .line 602
    invoke-interface {p1, v4}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/4 v4, 0x3

    aput-object v2, v0, v4

    const/4 v2, 0x4

    const-string v4, "calleeDepth"

    aput-object v4, v0, v2

    .line 603
    invoke-interface {p1, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_0

    goto :goto_0

    :cond_0
    sget v2, Lgroovy/grape/GrapeIvy;->DEFAULT_CALLEE_DEPTH:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    :goto_0
    const/4 v4, 0x5

    aput-object v2, v0, v4

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v0

    invoke-virtual {p0, v0}, Lgroovy/grape/GrapeIvy;->chooseClassLoader(Ljava/util/Map;)Ljava/lang/ClassLoader;

    move-result-object v0

    .line 608
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    xor-int/2addr v2, v3

    if-eqz v2, :cond_1

    new-array p1, v3, [I

    aput v1, p1, v1

    .line 609
    const-class p2, Ljava/net/URI;

    invoke-static {p2, p1}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;[I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/net/URI;

    return-object p1

    .line 612
    :cond_1
    invoke-virtual {p0, v0, p1, p2, p3}, Lgroovy/grape/GrapeIvy;->resolve(Ljava/lang/ClassLoader;Ljava/util/Map;Ljava/util/List;[Ljava/util/Map;)[Ljava/net/URI;

    move-result-object p1

    return-object p1
.end method

.method public varargs resolve(Ljava/util/Map;[Ljava/util/Map;)[Ljava/net/URI;
    .locals 1

    const/4 v0, 0x0

    .line 594
    invoke-virtual {p0, p1, v0, p2}, Lgroovy/grape/GrapeIvy;->resolve(Ljava/util/Map;Ljava/util/List;[Ljava/util/Map;)[Ljava/net/URI;

    move-result-object p1

    return-object p1
.end method

.method public setDownloadedArtifacts(Ljava/util/Set;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/GrapeIvy;->downloadedArtifacts:Ljava/util/Set;

    return-void
.end method

.method public setEnableGrapes(Z)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-boolean p1, p0, Lgroovy/grape/GrapeIvy;->enableGrapes:Z

    return-void
.end method

.method public setIvyInstance(Lorg/apache/ivy/Ivy;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/GrapeIvy;->ivyInstance:Lorg/apache/ivy/Ivy;

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/GrapeIvy;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setResolvedDependencies(Ljava/util/Set;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/GrapeIvy;->resolvedDependencies:Ljava/util/Set;

    return-void
.end method

.method public setSettings(Lorg/apache/ivy/core/settings/IvySettings;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/grape/GrapeIvy;->settings:Lorg/apache/ivy/core/settings/IvySettings;

    return-void
.end method

.method public uninstallArtifact(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 7

    new-instance v3, Lgroovy/lang/Reference;

    invoke-direct {v3, p1}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    new-instance v4, Lgroovy/lang/Reference;

    invoke-direct {v4, p2}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    new-instance v6, Lgroovy/lang/Reference;

    invoke-direct {v6, p3}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    const-string p1, "ivy-(.*)\\.xml"

    .line 519
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->bitwiseNegate(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class p2, Ljava/util/regex/Pattern;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/regex/Pattern;

    new-instance v5, Lgroovy/lang/Reference;

    invoke-direct {v5, p1}, Lgroovy/lang/Reference;-><init>(Ljava/lang/Object;)V

    .line 520
    invoke-virtual {p0}, Lgroovy/grape/GrapeIvy;->getGrapeCacheDir()Ljava/io/File;

    move-result-object p1

    new-instance p2, Lgroovy/grape/GrapeIvy$_uninstallArtifact_closure11;

    move-object v0, p2

    move-object v1, p0

    move-object v2, p0

    invoke-direct/range {v0 .. v6}, Lgroovy/grape/GrapeIvy$_uninstallArtifact_closure11;-><init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ResourceGroovyMethods;->eachDir(Ljava/io/File;Lgroovy/lang/Closure;)V

    return-void
.end method
