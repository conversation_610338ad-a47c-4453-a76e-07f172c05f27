.class public final enum Lgroovy/util/ObservableMap$ChangeType;
.super Ljava/lang/Enum;
.source "ObservableMap.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "ChangeType"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovy/util/ObservableMap$ChangeType;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovy/util/ObservableMap$ChangeType;

.field public static final enum ADDED:Lgroovy/util/ObservableMap$ChangeType;

.field public static final enum CLEARED:Lgroovy/util/ObservableMap$ChangeType;

.field public static final enum MULTI:Lgroovy/util/ObservableMap$ChangeType;

.field public static final enum NONE:Lgroovy/util/ObservableMap$ChangeType;

.field public static final enum REMOVED:Lgroovy/util/ObservableMap$ChangeType;

.field public static final enum UPDATED:Lgroovy/util/ObservableMap$ChangeType;

.field public static final newValue:Ljava/lang/Object;

.field public static final oldValue:Ljava/lang/Object;


# direct methods
.method private static synthetic $values()[Lgroovy/util/ObservableMap$ChangeType;
    .locals 3

    const/4 v0, 0x6

    new-array v0, v0, [Lgroovy/util/ObservableMap$ChangeType;

    .line 307
    sget-object v1, Lgroovy/util/ObservableMap$ChangeType;->ADDED:Lgroovy/util/ObservableMap$ChangeType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableMap$ChangeType;->UPDATED:Lgroovy/util/ObservableMap$ChangeType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableMap$ChangeType;->REMOVED:Lgroovy/util/ObservableMap$ChangeType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableMap$ChangeType;->CLEARED:Lgroovy/util/ObservableMap$ChangeType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableMap$ChangeType;->MULTI:Lgroovy/util/ObservableMap$ChangeType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableMap$ChangeType;->NONE:Lgroovy/util/ObservableMap$ChangeType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 4

    .line 308
    new-instance v0, Lgroovy/util/ObservableMap$ChangeType;

    const-string v1, "ADDED"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovy/util/ObservableMap$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableMap$ChangeType;->ADDED:Lgroovy/util/ObservableMap$ChangeType;

    new-instance v0, Lgroovy/util/ObservableMap$ChangeType;

    const-string v1, "UPDATED"

    const/4 v3, 0x1

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableMap$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableMap$ChangeType;->UPDATED:Lgroovy/util/ObservableMap$ChangeType;

    new-instance v0, Lgroovy/util/ObservableMap$ChangeType;

    const-string v1, "REMOVED"

    const/4 v3, 0x2

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableMap$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableMap$ChangeType;->REMOVED:Lgroovy/util/ObservableMap$ChangeType;

    new-instance v0, Lgroovy/util/ObservableMap$ChangeType;

    const-string v1, "CLEARED"

    const/4 v3, 0x3

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableMap$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableMap$ChangeType;->CLEARED:Lgroovy/util/ObservableMap$ChangeType;

    new-instance v0, Lgroovy/util/ObservableMap$ChangeType;

    const-string v1, "MULTI"

    const/4 v3, 0x4

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableMap$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableMap$ChangeType;->MULTI:Lgroovy/util/ObservableMap$ChangeType;

    new-instance v0, Lgroovy/util/ObservableMap$ChangeType;

    const-string v1, "NONE"

    const/4 v3, 0x5

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableMap$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableMap$ChangeType;->NONE:Lgroovy/util/ObservableMap$ChangeType;

    .line 307
    invoke-static {}, Lgroovy/util/ObservableMap$ChangeType;->$values()[Lgroovy/util/ObservableMap$ChangeType;

    move-result-object v0

    sput-object v0, Lgroovy/util/ObservableMap$ChangeType;->$VALUES:[Lgroovy/util/ObservableMap$ChangeType;

    new-array v0, v2, [Ljava/lang/Object;

    .line 310
    sput-object v0, Lgroovy/util/ObservableMap$ChangeType;->oldValue:Ljava/lang/Object;

    new-array v0, v2, [Ljava/lang/Object;

    .line 311
    sput-object v0, Lgroovy/util/ObservableMap$ChangeType;->newValue:Ljava/lang/Object;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 307
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static resolve(I)Lgroovy/util/ObservableMap$ChangeType;
    .locals 1

    if-eqz p0, :cond_4

    const/4 v0, 0x2

    if-eq p0, v0, :cond_3

    const/4 v0, 0x3

    if-eq p0, v0, :cond_2

    const/4 v0, 0x4

    if-eq p0, v0, :cond_1

    const/4 v0, 0x5

    if-eq p0, v0, :cond_0

    .line 327
    sget-object p0, Lgroovy/util/ObservableMap$ChangeType;->UPDATED:Lgroovy/util/ObservableMap$ChangeType;

    return-object p0

    .line 324
    :cond_0
    sget-object p0, Lgroovy/util/ObservableMap$ChangeType;->NONE:Lgroovy/util/ObservableMap$ChangeType;

    return-object p0

    .line 322
    :cond_1
    sget-object p0, Lgroovy/util/ObservableMap$ChangeType;->MULTI:Lgroovy/util/ObservableMap$ChangeType;

    return-object p0

    .line 320
    :cond_2
    sget-object p0, Lgroovy/util/ObservableMap$ChangeType;->CLEARED:Lgroovy/util/ObservableMap$ChangeType;

    return-object p0

    .line 318
    :cond_3
    sget-object p0, Lgroovy/util/ObservableMap$ChangeType;->REMOVED:Lgroovy/util/ObservableMap$ChangeType;

    return-object p0

    .line 316
    :cond_4
    sget-object p0, Lgroovy/util/ObservableMap$ChangeType;->ADDED:Lgroovy/util/ObservableMap$ChangeType;

    return-object p0
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovy/util/ObservableMap$ChangeType;
    .locals 1

    .line 307
    const-class v0, Lgroovy/util/ObservableMap$ChangeType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovy/util/ObservableMap$ChangeType;

    return-object p0
.end method

.method public static values()[Lgroovy/util/ObservableMap$ChangeType;
    .locals 1

    .line 307
    sget-object v0, Lgroovy/util/ObservableMap$ChangeType;->$VALUES:[Lgroovy/util/ObservableMap$ChangeType;

    invoke-virtual {v0}, [Lgroovy/util/ObservableMap$ChangeType;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovy/util/ObservableMap$ChangeType;

    return-object v0
.end method
