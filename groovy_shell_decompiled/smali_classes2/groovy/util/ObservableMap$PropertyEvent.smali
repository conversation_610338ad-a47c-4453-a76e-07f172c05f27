.class public abstract Lgroovy/util/ObservableMap$PropertyEvent;
.super Lgroovyjarjaropenbeans/PropertyChangeEvent;
.source "ObservableMap.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "PropertyEvent"
.end annotation


# static fields
.field private static final serialVersionUID:J = -0x739476cb34647ae2L


# instance fields
.field private type:Lgroovy/util/ObservableMap$ChangeType;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lgroovy/util/ObservableMap$ChangeType;)V
    .locals 0

    .line 337
    invoke-direct {p0, p1, p2, p3, p4}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 338
    iput-object p5, p0, Lgroovy/util/ObservableMap$PropertyEvent;->type:Lgroovy/util/ObservableMap$ChangeType;

    return-void
.end method


# virtual methods
.method public getChangeType()Lgroovy/util/ObservableMap$ChangeType;
    .locals 1

    .line 346
    iget-object v0, p0, Lgroovy/util/ObservableMap$PropertyEvent;->type:Lgroovy/util/ObservableMap$ChangeType;

    return-object v0
.end method

.method public getType()I
    .locals 1

    .line 342
    iget-object v0, p0, Lgroovy/util/ObservableMap$PropertyEvent;->type:Lgroovy/util/ObservableMap$ChangeType;

    invoke-virtual {v0}, Lgroovy/util/ObservableMap$ChangeType;->ordinal()I

    move-result v0

    return v0
.end method

.method public getTypeAsString()Ljava/lang/String;
    .locals 1

    .line 350
    iget-object v0, p0, Lgroovy/util/ObservableMap$PropertyEvent;->type:Lgroovy/util/ObservableMap$ChangeType;

    invoke-virtual {v0}, Lgroovy/util/ObservableMap$ChangeType;->name()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->toUpperCase()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
