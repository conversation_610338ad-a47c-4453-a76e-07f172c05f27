.class public Lgroovy/util/ObservableSet;
.super Ljava/lang/Object;
.source "ObservableSet.java"

# interfaces
.implements Ljava/util/Set;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/util/ObservableSet$ElementAddedEvent;,
        Lgroovy/util/ObservableSet$ElementEvent;,
        Lgroovy/util/ObservableSet$MultiElementAddedEvent;,
        Lgroovy/util/ObservableSet$ElementClearedEvent;,
        Lgroovy/util/ObservableSet$ElementRemovedEvent;,
        Lgroovy/util/ObservableSet$MultiElementRemovedEvent;,
        Lgroovy/util/ObservableSet$ObservableIterator;,
        Lgroovy/util/ObservableSet$ChangeType;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Set<",
        "TE;>;"
    }
.end annotation


# static fields
.field public static final CONTENT_PROPERTY:Ljava/lang/String; = "content"

.field public static final SIZE_PROPERTY:Ljava/lang/String; = "size"


# instance fields
.field private final delegate:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "TE;>;"
        }
    .end annotation
.end field

.field private final pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

.field private final test:Lgroovy/lang/Closure;


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 77
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    const/4 v1, 0x0

    invoke-direct {p0, v0, v1}, Lgroovy/util/ObservableSet;-><init>(Ljava/util/Set;Lgroovy/lang/Closure;)V

    return-void
.end method

.method public constructor <init>(Lgroovy/lang/Closure;)V
    .locals 1

    .line 85
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    invoke-direct {p0, v0, p1}, Lgroovy/util/ObservableSet;-><init>(Ljava/util/Set;Lgroovy/lang/Closure;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/Set;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "TE;>;)V"
        }
    .end annotation

    const/4 v0, 0x0

    .line 81
    invoke-direct {p0, p1, v0}, Lgroovy/util/ObservableSet;-><init>(Ljava/util/Set;Lgroovy/lang/Closure;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/Set;Lgroovy/lang/Closure;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Set<",
            "TE;>;",
            "Lgroovy/lang/Closure;",
            ")V"
        }
    .end annotation

    .line 88
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 89
    iput-object p1, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    .line 90
    iput-object p2, p0, Lgroovy/util/ObservableSet;->test:Lgroovy/lang/Closure;

    .line 91
    new-instance p1, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-direct {p1, p0}, Lgroovyjarjaropenbeans/PropertyChangeSupport;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    return-void
.end method


# virtual methods
.method public add(Ljava/lang/Object;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TE;)Z"
        }
    .end annotation

    .line 189
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result v0

    .line 190
    iget-object v1, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v1, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 192
    iget-object v2, p0, Lgroovy/util/ObservableSet;->test:Lgroovy/lang/Closure;

    if-eqz v2, :cond_0

    .line 193
    invoke-virtual {v2, p1}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 194
    instance-of v3, v2, Ljava/lang/Boolean;

    if-eqz v3, :cond_1

    check-cast v2, Ljava/lang/Boolean;

    invoke-virtual {v2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v2

    if-eqz v2, :cond_1

    .line 195
    invoke-virtual {p0, p1}, Lgroovy/util/ObservableSet;->fireElementAddedEvent(Ljava/lang/Object;)V

    .line 196
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Lgroovy/util/ObservableSet;->fireSizeChangedEvent(II)V

    goto :goto_0

    .line 199
    :cond_0
    invoke-virtual {p0, p1}, Lgroovy/util/ObservableSet;->fireElementAddedEvent(Ljava/lang/Object;)V

    .line 200
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Lgroovy/util/ObservableSet;->fireSizeChangedEvent(II)V

    :cond_1
    :goto_0
    return v1
.end method

.method public addAll(Ljava/util/Collection;)Z
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+TE;>;)Z"
        }
    .end annotation

    .line 221
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    if-eqz p1, :cond_1

    .line 223
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 224
    iget-object v3, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v3, v2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_0

    goto :goto_0

    .line 225
    :cond_0
    invoke-interface {v0, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 229
    :cond_1
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result v1

    .line 230
    iget-object v2, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v2, p1}, Ljava/util/Set;->addAll(Ljava/util/Collection;)Z

    move-result v2

    if-eqz v2, :cond_5

    if-eqz p1, :cond_5

    .line 233
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 234
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_2
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_4

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    .line 235
    iget-object v5, p0, Lgroovy/util/ObservableSet;->test:Lgroovy/lang/Closure;

    if-eqz v5, :cond_3

    .line 236
    invoke-virtual {v5, v4}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    .line 237
    instance-of v6, v5, Ljava/lang/Boolean;

    if-eqz v6, :cond_2

    check-cast v5, Ljava/lang/Boolean;

    invoke-virtual {v5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v5

    if-eqz v5, :cond_2

    invoke-interface {v0, v4}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_2

    .line 238
    invoke-interface {v3, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 240
    :cond_3
    invoke-interface {v0, v4}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v5

    if-nez v5, :cond_2

    .line 241
    invoke-interface {v3, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 244
    :cond_4
    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_5

    .line 245
    invoke-virtual {p0, v3}, Lgroovy/util/ObservableSet;->fireMultiElementAddedEvent(Ljava/util/List;)V

    .line 246
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result p1

    invoke-virtual {p0, v1, p1}, Lgroovy/util/ObservableSet;->fireSizeChangedEvent(II)V

    :cond_5
    return v2
.end method

.method public addPropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    .line 137
    iget-object v0, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->addPropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    return-void
.end method

.method public addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    .line 141
    iget-object v0, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    return-void
.end method

.method public clear()V
    .locals 3

    .line 302
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result v0

    .line 303
    new-instance v1, Ljava/util/ArrayList;

    iget-object v2, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 304
    iget-object v2, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v2}, Ljava/util/Set;->clear()V

    .line 305
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_0

    .line 306
    invoke-virtual {p0, v1}, Lgroovy/util/ObservableSet;->fireElementClearedEvent(Ljava/util/List;)V

    .line 308
    :cond_0
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result v1

    invoke-virtual {p0, v0, v1}, Lgroovy/util/ObservableSet;->fireSizeChangedEvent(II)V

    return-void
.end method

.method public contains(Ljava/lang/Object;)Z
    .locals 1

    .line 173
    iget-object v0, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public containsAll(Ljava/util/Collection;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "*>;)Z"
        }
    .end annotation

    .line 217
    iget-object v0, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->containsAll(Ljava/util/Collection;)Z

    move-result p1

    return p1
.end method

.method protected fireElementAddedEvent(Ljava/lang/Object;)V
    .locals 1

    .line 107
    new-instance v0, Lgroovy/util/ObservableSet$ElementAddedEvent;

    invoke-direct {v0, p0, p1}, Lgroovy/util/ObservableSet$ElementAddedEvent;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableSet;->fireElementEvent(Lgroovy/util/ObservableSet$ElementEvent;)V

    return-void
.end method

.method protected fireElementClearedEvent(Ljava/util/List;)V
    .locals 1

    .line 115
    new-instance v0, Lgroovy/util/ObservableSet$ElementClearedEvent;

    invoke-direct {v0, p0, p1}, Lgroovy/util/ObservableSet$ElementClearedEvent;-><init>(Ljava/lang/Object;Ljava/util/List;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableSet;->fireElementEvent(Lgroovy/util/ObservableSet$ElementEvent;)V

    return-void
.end method

.method protected fireElementEvent(Lgroovy/util/ObservableSet$ElementEvent;)V
    .locals 1

    .line 127
    iget-object v0, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->firePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void
.end method

.method protected fireElementRemovedEvent(Ljava/lang/Object;)V
    .locals 1

    .line 119
    new-instance v0, Lgroovy/util/ObservableSet$ElementRemovedEvent;

    invoke-direct {v0, p0, p1}, Lgroovy/util/ObservableSet$ElementRemovedEvent;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableSet;->fireElementEvent(Lgroovy/util/ObservableSet$ElementEvent;)V

    return-void
.end method

.method protected fireMultiElementAddedEvent(Ljava/util/List;)V
    .locals 1

    .line 111
    new-instance v0, Lgroovy/util/ObservableSet$MultiElementAddedEvent;

    invoke-direct {v0, p0, p1}, Lgroovy/util/ObservableSet$MultiElementAddedEvent;-><init>(Ljava/lang/Object;Ljava/util/List;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableSet;->fireElementEvent(Lgroovy/util/ObservableSet$ElementEvent;)V

    return-void
.end method

.method protected fireMultiElementRemovedEvent(Ljava/util/List;)V
    .locals 1

    .line 123
    new-instance v0, Lgroovy/util/ObservableSet$MultiElementRemovedEvent;

    invoke-direct {v0, p0, p1}, Lgroovy/util/ObservableSet$MultiElementRemovedEvent;-><init>(Ljava/lang/Object;Ljava/util/List;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableSet;->fireElementEvent(Lgroovy/util/ObservableSet$ElementEvent;)V

    return-void
.end method

.method protected fireSizeChangedEvent(II)V
    .locals 3

    .line 131
    iget-object v0, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    new-instance v1, Lgroovyjarjaropenbeans/PropertyChangeEvent;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    const-string v2, "size"

    invoke-direct {v1, p0, v2, p1, p2}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-virtual {v0, v1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->firePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void
.end method

.method public getContent()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "TE;>;"
        }
    .end annotation

    .line 95
    iget-object v0, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableSet(Ljava/util/Set;)Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method protected getDelegateSet()Ljava/util/Set;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Set<",
            "TE;>;"
        }
    .end annotation

    .line 99
    iget-object v0, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    return-object v0
.end method

.method public getPropertyChangeListeners()[Lgroovyjarjaropenbeans/PropertyChangeListener;
    .locals 1

    .line 145
    iget-object v0, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->getPropertyChangeListeners()[Lgroovyjarjaropenbeans/PropertyChangeListener;

    move-result-object v0

    return-object v0
.end method

.method public getPropertyChangeListeners(Ljava/lang/String;)[Lgroovyjarjaropenbeans/PropertyChangeListener;
    .locals 1

    .line 149
    iget-object v0, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->getPropertyChangeListeners(Ljava/lang/String;)[Lgroovyjarjaropenbeans/PropertyChangeListener;

    move-result-object p1

    return-object p1
.end method

.method protected getTest()Lgroovy/lang/Closure;
    .locals 1

    .line 103
    iget-object v0, p0, Lgroovy/util/ObservableSet;->test:Lgroovy/lang/Closure;

    return-object v0
.end method

.method public hasListeners(Ljava/lang/String;)Z
    .locals 1

    .line 161
    iget-object v0, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->hasListeners(Ljava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public isEmpty()Z
    .locals 1

    .line 169
    iget-object v0, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->isEmpty()Z

    move-result v0

    return v0
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "TE;>;"
        }
    .end annotation

    .line 177
    new-instance v0, Lgroovy/util/ObservableSet$ObservableIterator;

    iget-object v1, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    invoke-direct {v0, p0, v1}, Lgroovy/util/ObservableSet$ObservableIterator;-><init>(Lgroovy/util/ObservableSet;Ljava/util/Iterator;)V

    return-object v0
.end method

.method public remove(Ljava/lang/Object;)Z
    .locals 2

    .line 207
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result v0

    .line 208
    iget-object v1, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v1, p1}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 210
    invoke-virtual {p0, p1}, Lgroovy/util/ObservableSet;->fireElementRemovedEvent(Ljava/lang/Object;)V

    .line 211
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Lgroovy/util/ObservableSet;->fireSizeChangedEvent(II)V

    :cond_0
    return v1
.end method

.method public removeAll(Ljava/util/Collection;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "*>;)Z"
        }
    .end annotation

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return p1

    .line 284
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 285
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 286
    iget-object v3, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v3, v2}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 287
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 291
    :cond_2
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result v1

    .line 292
    iget-object v2, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v2, p1}, Ljava/util/Set;->removeAll(Ljava/util/Collection;)Z

    move-result p1

    if-eqz p1, :cond_3

    .line 293
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_3

    .line 294
    invoke-virtual {p0, v0}, Lgroovy/util/ObservableSet;->fireMultiElementRemovedEvent(Ljava/util/List;)V

    .line 295
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result v0

    invoke-virtual {p0, v1, v0}, Lgroovy/util/ObservableSet;->fireSizeChangedEvent(II)V

    :cond_3
    return p1
.end method

.method public removePropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    .line 153
    iget-object v0, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->removePropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    return-void
.end method

.method public removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    .line 157
    iget-object v0, p0, Lgroovy/util/ObservableSet;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    return-void
.end method

.method public retainAll(Ljava/util/Collection;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "*>;)Z"
        }
    .end annotation

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return p1

    .line 258
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 260
    instance-of v1, p1, Ljava/util/Set;

    if-nez v1, :cond_1

    .line 261
    new-instance v1, Ljava/util/HashSet;

    invoke-direct {v1, p1}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    move-object p1, v1

    .line 263
    :cond_1
    iget-object v1, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_2
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 264
    invoke-interface {p1, v2}, Ljava/util/Collection;->contains(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_2

    .line 265
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 269
    :cond_3
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result v1

    .line 270
    iget-object v2, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v2, p1}, Ljava/util/Set;->retainAll(Ljava/util/Collection;)Z

    move-result p1

    if-eqz p1, :cond_4

    .line 271
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_4

    .line 272
    invoke-virtual {p0, v0}, Lgroovy/util/ObservableSet;->fireMultiElementRemovedEvent(Ljava/util/List;)V

    .line 273
    invoke-virtual {p0}, Lgroovy/util/ObservableSet;->size()I

    move-result v0

    invoke-virtual {p0, v1, v0}, Lgroovy/util/ObservableSet;->fireSizeChangedEvent(II)V

    :cond_4
    return p1
.end method

.method public size()I
    .locals 1

    .line 165
    iget-object v0, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->size()I

    move-result v0

    return v0
.end method

.method public toArray()[Ljava/lang/Object;
    .locals 1

    .line 181
    iget-object v0, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->toArray()[Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public toArray([Ljava/lang/Object;)[Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">([TT;)[TT;"
        }
    .end annotation

    .line 185
    iget-object v0, p0, Lgroovy/util/ObservableSet;->delegate:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
