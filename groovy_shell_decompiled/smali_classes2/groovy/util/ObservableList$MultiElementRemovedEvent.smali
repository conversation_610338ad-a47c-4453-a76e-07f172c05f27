.class public Lgroovy/util/ObservableList$MultiElementRemovedEvent;
.super Lgroovy/util/ObservableList$ElementEvent;
.source "ObservableList.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableList;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "MultiElementRemovedEvent"
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x23f2609a329eab0cL


# instance fields
.field private values:Ljava/util/List;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/util/List;)V
    .locals 6

    .line 567
    sget-object v2, Lgroovy/util/ObservableList$ChangeType;->oldValue:Ljava/lang/Object;

    sget-object v3, Lgroovy/util/ObservableList$ChangeType;->newValue:Ljava/lang/Object;

    sget-object v5, Lgroovy/util/ObservableList$ChangeType;->MULTI_REMOVE:Lgroovy/util/ObservableList$ChangeType;

    const/4 v4, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lgroovy/util/ObservableList$ElementEvent;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;ILgroovy/util/ObservableList$ChangeType;)V

    .line 564
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObservableList$MultiElementRemovedEvent;->values:Ljava/util/List;

    if-eqz p2, :cond_0

    .line 569
    invoke-interface {p1, p2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    return-void
.end method


# virtual methods
.method public getValues()Ljava/util/List;
    .locals 1

    .line 574
    iget-object v0, p0, Lgroovy/util/ObservableList$MultiElementRemovedEvent;->values:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
