.class public Lgroovy/util/Proxy;
.super Lgroovy/lang/GroovyObjectSupport;
.source "Proxy.java"


# instance fields
.field private adaptee:Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 32
    invoke-direct {p0}, Lgroovy/lang/GroovyObjectSupport;-><init>()V

    const/4 v0, 0x0

    .line 34
    iput-object v0, p0, Lgroovy/util/Proxy;->adaptee:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public getAdaptee()Ljava/lang/Object;
    .locals 1

    .line 47
    iget-object v0, p0, Lgroovy/util/Proxy;->adaptee:Ljava/lang/Object;

    return-object v0
.end method

.method public invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 56
    :try_start_0
    invoke-super {p0, p1, p2}, Lgroovy/lang/GroovyObjectSupport;->invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lgroovy/lang/MissingMethodException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    .line 59
    :catch_0
    iget-object v0, p0, Lgroovy/util/Proxy;->adaptee:Ljava/lang/Object;

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 1

    .line 64
    iget-object v0, p0, Lgroovy/util/Proxy;->adaptee:Ljava/lang/Object;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/InvokerHelper;->asIterator(Ljava/lang/Object;)Ljava/util/Iterator;

    move-result-object v0

    return-object v0
.end method

.method public setAdaptee(Ljava/lang/Object;)V
    .locals 0

    .line 51
    iput-object p1, p0, Lgroovy/util/Proxy;->adaptee:Ljava/lang/Object;

    return-void
.end method

.method public wrap(Ljava/lang/Object;)Lgroovy/util/Proxy;
    .locals 0

    .line 42
    invoke-virtual {p0, p1}, Lgroovy/util/Proxy;->setAdaptee(Ljava/lang/Object;)V

    return-object p0
.end method
