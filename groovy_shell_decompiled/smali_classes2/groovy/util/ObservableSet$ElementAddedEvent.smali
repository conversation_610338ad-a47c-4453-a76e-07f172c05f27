.class public Lgroovy/util/ObservableSet$ElementAddedEvent;
.super Lgroovy/util/ObservableSet$ElementEvent;
.source "ObservableSet.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableSet;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ElementAddedEvent"
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x40ed2c7603abdb8cL


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 2

    .line 373
    sget-object v0, Lgroovy/util/ObservableSet$ChangeType;->ADDED:Lgroovy/util/ObservableSet$ChangeType;

    const/4 v1, 0x0

    invoke-direct {p0, p1, v1, p2, v0}, Lgroovy/util/ObservableSet$ElementEvent;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Lgroovy/util/ObservableSet$ChangeType;)V

    return-void
.end method
