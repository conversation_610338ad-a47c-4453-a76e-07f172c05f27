.class public Lgroovy/util/logging/Commons$CommonsLoggingStrategy;
.super Lorg/codehaus/groovy/transform/LogASTTransformation$AbstractLoggingStrategyV2;
.source "Commons.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/logging/Commons;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "CommonsLoggingStrategy"
.end annotation


# static fields
.field private static final LOGGERFACTORY_NAME:Ljava/lang/String; = "org.apache.commons.logging.LogFactory"

.field private static final LOGGER_NAME:Ljava/lang/String; = "org.apache.commons.logging.Log"


# direct methods
.method protected constructor <init>(Lgroovy/lang/GroovyClassLoader;)V
    .locals 0

    .line 83
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/LogASTTransformation$AbstractLoggingStrategyV2;-><init>(Lgroovy/lang/GroovyClassLoader;)V

    return-void
.end method


# virtual methods
.method public addLoggerFieldToClass(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/String;I)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 4

    const-string v0, "org.apache.commons.logging.Log"

    .line 90
    invoke-virtual {p0, v0}, Lgroovy/util/logging/Commons$CommonsLoggingStrategy;->classNode(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    new-instance v1, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    new-instance v2, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    const-string v3, "org.apache.commons.logging.LogFactory"

    .line 92
    invoke-virtual {p0, v3}, Lgroovy/util/logging/Commons$CommonsLoggingStrategy;->classNode(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-direct {v2, v3}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    new-instance v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    .line 94
    invoke-virtual {p0, p1, p3}, Lgroovy/util/logging/Commons$CommonsLoggingStrategy;->getCategoryName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p3

    invoke-direct {v3, p3}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    const-string p3, "getLog"

    invoke-direct {v1, v2, p3, v3}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 88
    invoke-virtual {p1, p2, p4, v0, v1}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p1

    return-object p1
.end method

.method public isLoggingMethod(Ljava/lang/String;)Z
    .locals 1

    const-string v0, "fatal|error|warn|info|debug|trace"

    .line 99
    invoke-virtual {p1, v0}, Ljava/lang/String;->matches(Ljava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public wrapLoggingMethodCall(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 6

    .line 104
    new-instance v0, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "is"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x1

    .line 106
    invoke-virtual {p2, v2, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v4

    sget-object v5, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v4, v5}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p2, v3}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v1, "Enabled"

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    sget-object v1, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->EMPTY_ARGUMENTS:Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    invoke-direct {v0, p1, p2, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 108
    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 110
    invoke-static {}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->nullX()Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    invoke-static {v0, p3, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ternaryX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TernaryExpression;

    move-result-object p1

    return-object p1
.end method
