.class public interface abstract annotation Lgroovy/util/logging/Commons;
.super Ljava/lang/Object;
.source "Commons.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/util/logging/Commons;
        category = "##default-category-name##"
        loggingStrategy = Lgroovy/util/logging/Commons$CommonsLoggingStrategy;
        value = "log"
        visibilityId = "<DummyUndefinedMarkerString-DoNotUse>"
    .end subannotation
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/util/logging/Commons$CommonsLoggingStrategy;
    }
.end annotation

.annotation runtime Ljava/lang/annotation/Documented;
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;
    }
.end annotation

.annotation runtime Lorg/codehaus/groovy/transform/GroovyASTTransformationClass;
    value = {
        "org.codehaus.groovy.transform.LogASTTransformation"
    }
.end annotation


# virtual methods
.method public abstract category()Ljava/lang/String;
.end method

.method public abstract loggingStrategy()Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/lang/Class<",
            "+",
            "Lorg/codehaus/groovy/transform/LogASTTransformation$LoggingStrategy;",
            ">;"
        }
    .end annotation
.end method

.method public abstract value()Ljava/lang/String;
.end method

.method public abstract visibilityId()Ljava/lang/String;
.end method
