.class public Lgroovy/util/logging/Log4j$Log4jLoggingStrategy;
.super Lorg/codehaus/groovy/transform/LogASTTransformation$AbstractLoggingStrategyV2;
.source "Log4j.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/logging/Log4j;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Log4jLoggingStrategy"
.end annotation


# static fields
.field private static final LOGGER_NAME:Ljava/lang/String; = "org.apache.log4j.Logger"

.field private static final PRIORITY_NAME:Ljava/lang/String; = "org.apache.log4j.Priority"


# direct methods
.method protected constructor <init>(Lgroovy/lang/GroovyClassLoader;)V
    .locals 0

    .line 84
    invoke-direct {p0, p1}, Lorg/codehaus/groovy/transform/LogASTTransformation$AbstractLoggingStrategyV2;-><init>(Lgroovy/lang/GroovyClassLoader;)V

    return-void
.end method


# virtual methods
.method public addLoggerFieldToClass(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/String;I)Lorg/codehaus/groovy/ast/FieldNode;
    .locals 4

    const-string v0, "org.apache.log4j.Logger"

    .line 91
    invoke-virtual {p0, v0}, Lgroovy/util/logging/Log4j$Log4jLoggingStrategy;->classNode(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v1

    new-instance v2, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    new-instance v3, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    .line 93
    invoke-virtual {p0, v0}, Lgroovy/util/logging/Log4j$Log4jLoggingStrategy;->classNode(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    invoke-direct {v3, v0}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    new-instance v0, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    .line 95
    invoke-virtual {p0, p1, p3}, Lgroovy/util/logging/Log4j$Log4jLoggingStrategy;->getCategoryName(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p3

    invoke-direct {v0, p3}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    const-string p3, "getLogger"

    invoke-direct {v2, v3, p3, v0}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 89
    invoke-virtual {p1, p2, p4, v1, v2}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object p1

    return-object p1
.end method

.method public isLoggingMethod(Ljava/lang/String;)Z
    .locals 1

    const-string v0, "fatal|error|warn|info|debug|trace"

    .line 100
    invoke-virtual {p1, v0}, Ljava/lang/String;->matches(Ljava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public wrapLoggingMethodCall(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/Expression;
    .locals 6

    const-string v0, "trace"

    .line 106
    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 107
    new-instance v0, Lorg/codehaus/groovy/ast/expr/AttributeExpression;

    new-instance v2, Lorg/codehaus/groovy/ast/expr/ClassExpression;

    const-string v3, "org.apache.log4j.Priority"

    .line 108
    invoke-virtual {p0, v3}, Lgroovy/util/logging/Log4j$Log4jLoggingStrategy;->classNode(Ljava/lang/String;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v3

    invoke-direct {v2, v3}, Lorg/codehaus/groovy/ast/expr/ClassExpression;-><init>(Lorg/codehaus/groovy/ast/ClassNode;)V

    new-instance v3, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    sget-object v4, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 109
    invoke-virtual {p2, v4}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object p2

    invoke-direct {v3, p2}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    invoke-direct {v0, v2, v3}, Lorg/codehaus/groovy/ast/expr/AttributeExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 110
    new-instance p2, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    invoke-direct {p2}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;-><init>()V

    .line 111
    invoke-virtual {p2, v0}, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->addExpression(Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TupleExpression;

    .line 112
    new-instance v0, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    const-string v2, "isEnabledFor"

    invoke-direct {v0, p1, v2, p2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    goto :goto_0

    .line 115
    :cond_0
    new-instance v0, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "is"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const/4 v3, 0x1

    .line 117
    invoke-virtual {p2, v1, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v4

    sget-object v5, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    invoke-virtual {v4, v5}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p2, v3}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v2, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v2, "Enabled"

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    sget-object v2, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->EMPTY_ARGUMENTS:Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;

    invoke-direct {v0, p1, p2, v2}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;-><init>(Lorg/codehaus/groovy/ast/expr/Expression;Ljava/lang/String;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 120
    :goto_0
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/expr/MethodCallExpression;->setImplicitThis(Z)V

    .line 122
    invoke-static {}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->nullX()Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    move-result-object p1

    invoke-static {v0, p3, p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->ternaryX(Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;Lorg/codehaus/groovy/ast/expr/Expression;)Lorg/codehaus/groovy/ast/expr/TernaryExpression;

    move-result-object p1

    return-object p1
.end method
