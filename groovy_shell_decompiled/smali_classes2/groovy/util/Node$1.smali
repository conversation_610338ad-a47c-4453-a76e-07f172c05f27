.class Lgroovy/util/Node$1;
.super Lgroovy/lang/DelegatingMetaClass;
.source "Node.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/util/Node;->setMetaClass(Lgroovy/lang/MetaClass;Ljava/lang/Class;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# direct methods
.method constructor <init>(Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 300
    invoke-direct {p0, p1}, Lgroovy/lang/DelegatingMetaClass;-><init>(Lgroovy/lang/MetaClass;)V

    return-void
.end method


# virtual methods
.method public getAttribute(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 2

    .line 303
    check-cast p1, Lgroovy/util/Node;

    .line 304
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "@"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Lgroovy/util/Node;->get(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    .line 315
    instance-of v0, p1, Lgroovy/util/Node;

    if-eqz v0, :cond_0

    .line 316
    check-cast p1, Lgroovy/util/Node;

    .line 317
    invoke-virtual {p1, p2}, Lgroovy/util/Node;->get(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 319
    :cond_0
    invoke-super {p0, p1, p2}, Lgroovy/lang/DelegatingMetaClass;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public setAttribute(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 0

    .line 309
    check-cast p1, Lgroovy/util/Node;

    .line 310
    invoke-virtual {p1}, Lgroovy/util/Node;->attributes()Ljava/util/Map;

    move-result-object p1

    invoke-interface {p1, p2, p3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    const-string v0, "@"

    .line 324
    invoke-virtual {p2, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    .line 325
    invoke-virtual {p2, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, p2, p3}, Lgroovy/util/Node$1;->setAttribute(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    return-void

    .line 328
    :cond_0
    iget-object v0, p0, Lgroovy/util/Node$1;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method
