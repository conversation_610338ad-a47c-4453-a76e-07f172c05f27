.class public Lgroovy/util/ScriptException;
.super Ljava/lang/Exception;
.source "ScriptException.java"


# static fields
.field private static final serialVersionUID:J = 0x2fd8266bfd6b60f7L


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 29
    invoke-direct {p0}, Ljava/lang/Exception;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 36
    invoke-direct {p0, p1}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 0

    .line 44
    invoke-direct {p0, p1, p2}, Ljava/lang/Exception;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    return-void
.end method

.method public constructor <init>(<PERSON>ja<PERSON>/lang/Throwable;)V
    .locals 0

    .line 51
    invoke-direct {p0, p1}, <PERSON><PERSON><PERSON>/lang/Exception;-><init>(Ljava/lang/Throwable;)V

    return-void
.end method
