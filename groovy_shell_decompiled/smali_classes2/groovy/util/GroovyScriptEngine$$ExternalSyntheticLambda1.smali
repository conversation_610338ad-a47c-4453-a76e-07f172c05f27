.class public final synthetic Lgroovy/util/GroovyScriptEngine$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/security/PrivilegedAction;


# static fields
.field public static final synthetic INSTANCE:Lgroovy/util/GroovyScriptEngine$$ExternalSyntheticLambda1;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lgroovy/util/GroovyScriptEngine$$ExternalSyntheticLambda1;

    invoke-direct {v0}, Lgroovy/util/GroovyScriptEngine$$ExternalSyntheticLambda1;-><init>()V

    sput-object v0, Lgroovy/util/GroovyScriptEngine$$ExternalSyntheticLambda1;->INSTANCE:Lgroovy/util/GroovyScriptEngine$$ExternalSyntheticLambda1;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final run()Ljava/lang/Object;
    .locals 1

    invoke-static {}, Lgroovy/util/GroovyScriptEngine;->lambda$static$0()Ljava/lang/ClassLoader;

    move-result-object v0

    return-object v0
.end method
