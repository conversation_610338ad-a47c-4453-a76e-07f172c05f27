.class public Lgroovy/util/ObservableSet$ObservableIterator;
.super Ljava/lang/Object;
.source "ObservableSet.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableSet;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "ObservableIterator"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "TE;>;"
    }
.end annotation


# instance fields
.field private final iterDelegate:Ljava/util/Iterator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Iterator<",
            "TE;>;"
        }
    .end annotation
.end field

.field private final stack:Ljava/util/Stack;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Stack<",
            "TE;>;"
        }
    .end annotation
.end field

.field final synthetic this$0:Lgroovy/util/ObservableSet;


# direct methods
.method public constructor <init>(Lgroovy/util/ObservableSet;Ljava/util/Iterator;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Iterator<",
            "TE;>;)V"
        }
    .end annotation

    .line 315
    iput-object p1, p0, Lgroovy/util/ObservableSet$ObservableIterator;->this$0:Lgroovy/util/ObservableSet;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 313
    new-instance p1, Ljava/util/Stack;

    invoke-direct {p1}, Ljava/util/Stack;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObservableSet$ObservableIterator;->stack:Ljava/util/Stack;

    .line 316
    iput-object p2, p0, Lgroovy/util/ObservableSet$ObservableIterator;->iterDelegate:Ljava/util/Iterator;

    return-void
.end method


# virtual methods
.method public getDelegate()Ljava/util/Iterator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "TE;>;"
        }
    .end annotation

    .line 320
    iget-object v0, p0, Lgroovy/util/ObservableSet$ObservableIterator;->iterDelegate:Ljava/util/Iterator;

    return-object v0
.end method

.method public hasNext()Z
    .locals 1

    .line 324
    iget-object v0, p0, Lgroovy/util/ObservableSet$ObservableIterator;->iterDelegate:Ljava/util/Iterator;

    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    return v0
.end method

.method public next()Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TE;"
        }
    .end annotation

    .line 328
    iget-object v0, p0, Lgroovy/util/ObservableSet$ObservableIterator;->stack:Ljava/util/Stack;

    iget-object v1, p0, Lgroovy/util/ObservableSet$ObservableIterator;->iterDelegate:Ljava/util/Iterator;

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/Stack;->push(Ljava/lang/Object;)Ljava/lang/Object;

    .line 329
    iget-object v0, p0, Lgroovy/util/ObservableSet$ObservableIterator;->stack:Ljava/util/Stack;

    invoke-virtual {v0}, Ljava/util/Stack;->peek()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public remove()V
    .locals 3

    .line 333
    iget-object v0, p0, Lgroovy/util/ObservableSet$ObservableIterator;->this$0:Lgroovy/util/ObservableSet;

    invoke-virtual {v0}, Lgroovy/util/ObservableSet;->size()I

    move-result v0

    .line 334
    iget-object v1, p0, Lgroovy/util/ObservableSet$ObservableIterator;->iterDelegate:Ljava/util/Iterator;

    invoke-interface {v1}, Ljava/util/Iterator;->remove()V

    .line 335
    iget-object v1, p0, Lgroovy/util/ObservableSet$ObservableIterator;->this$0:Lgroovy/util/ObservableSet;

    iget-object v2, p0, Lgroovy/util/ObservableSet$ObservableIterator;->stack:Ljava/util/Stack;

    invoke-virtual {v2}, Ljava/util/Stack;->pop()Ljava/lang/Object;

    move-result-object v2

    invoke-virtual {v1, v2}, Lgroovy/util/ObservableSet;->fireElementRemovedEvent(Ljava/lang/Object;)V

    .line 336
    iget-object v1, p0, Lgroovy/util/ObservableSet$ObservableIterator;->this$0:Lgroovy/util/ObservableSet;

    invoke-virtual {v1}, Lgroovy/util/ObservableSet;->size()I

    move-result v2

    invoke-virtual {v1, v0, v2}, Lgroovy/util/ObservableSet;->fireSizeChangedEvent(II)V

    return-void
.end method
