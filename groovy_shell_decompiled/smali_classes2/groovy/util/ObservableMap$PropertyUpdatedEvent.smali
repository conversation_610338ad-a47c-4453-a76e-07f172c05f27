.class public Lgroovy/util/ObservableMap$PropertyUpdatedEvent;
.super Lgroovy/util/ObservableMap$PropertyEvent;
.source "ObservableMap.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "PropertyUpdatedEvent"
.end annotation


# static fields
.field private static final serialVersionUID:J = -0xf54762809eedd32L


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 6

    .line 366
    sget-object v5, Lgroovy/util/ObservableMap$ChangeType;->UPDATED:Lgroovy/util/ObservableMap$ChangeType;

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    invoke-direct/range {v0 .. v5}, Lgroovy/util/ObservableMap$PropertyEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lgroovy/util/ObservableMap$ChangeType;)V

    return-void
.end method
