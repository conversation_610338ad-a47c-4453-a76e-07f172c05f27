.class public Lgroovy/util/IndentPrinter;
.super Ljava/lang/Object;
.source "IndentPrinter.java"


# instance fields
.field private final addNewlines:Z

.field private autoIndent:Z

.field private final indent:Ljava/lang/String;

.field private indentLevel:I

.field private final out:Ljava/io/Writer;


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 75
    new-instance v0, Ljava/io/PrintWriter;

    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-direct {v0, v1}, Ljava/io/PrintWriter;-><init>(Ljava/io/OutputStream;)V

    const-string v1, "  "

    invoke-direct {p0, v0, v1}, Lgroovy/util/IndentPrinter;-><init>(Ljava/io/Writer;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Lja<PERSON>/io/Writer;)V
    .locals 1

    const-string v0, "  "

    .line 85
    invoke-direct {p0, p1, v0}, L<PERSON>ov<PERSON>/util/IndentPrinter;-><init>(Lja<PERSON>/io/Writer;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/io/Writer;Ljava/lang/String;)V
    .locals 1

    const/4 v0, 0x1

    .line 96
    invoke-direct {p0, p1, p2, v0}, Lgroovy/util/IndentPrinter;-><init>(Ljava/io/Writer;Ljava/lang/String;Z)V

    return-void
.end method

.method public constructor <init>(Ljava/io/Writer;Ljava/lang/String;Z)V
    .locals 1

    const/4 v0, 0x0

    .line 109
    invoke-direct {p0, p1, p2, p3, v0}, Lgroovy/util/IndentPrinter;-><init>(Ljava/io/Writer;Ljava/lang/String;ZZ)V

    return-void
.end method

.method public constructor <init>(Ljava/io/Writer;Ljava/lang/String;ZZ)V
    .locals 0

    .line 119
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 120
    iput-boolean p3, p0, Lgroovy/util/IndentPrinter;->addNewlines:Z

    if-eqz p1, :cond_0

    .line 124
    iput-object p1, p0, Lgroovy/util/IndentPrinter;->out:Ljava/io/Writer;

    .line 125
    iput-object p2, p0, Lgroovy/util/IndentPrinter;->indent:Ljava/lang/String;

    .line 126
    iput-boolean p4, p0, Lgroovy/util/IndentPrinter;->autoIndent:Z

    return-void

    .line 122
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Must specify a Writer"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method public decrementIndent()V
    .locals 1

    .line 206
    iget v0, p0, Lgroovy/util/IndentPrinter;->indentLevel:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lgroovy/util/IndentPrinter;->indentLevel:I

    return-void
.end method

.method public flush()V
    .locals 2

    .line 227
    :try_start_0
    iget-object v0, p0, Lgroovy/util/IndentPrinter;->out:Ljava/io/Writer;

    invoke-virtual {v0}, Ljava/io/Writer;->flush()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    .line 229
    new-instance v1, Lgroovy/lang/GroovyRuntimeException;

    invoke-direct {v1, v0}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v1
.end method

.method public getAutoIndent()Z
    .locals 1

    .line 218
    iget-boolean v0, p0, Lgroovy/util/IndentPrinter;->autoIndent:Z

    return v0
.end method

.method public getIndentLevel()I
    .locals 1

    .line 210
    iget v0, p0, Lgroovy/util/IndentPrinter;->indentLevel:I

    return v0
.end method

.method public incrementIndent()V
    .locals 1

    .line 202
    iget v0, p0, Lgroovy/util/IndentPrinter;->indentLevel:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovy/util/IndentPrinter;->indentLevel:I

    return-void
.end method

.method public print(C)V
    .locals 1

    .line 164
    :try_start_0
    iget-object v0, p0, Lgroovy/util/IndentPrinter;->out:Ljava/io/Writer;

    invoke-virtual {v0, p1}, Ljava/io/Writer;->write(I)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    .line 166
    new-instance v0, Lgroovy/lang/GroovyRuntimeException;

    invoke-direct {v0, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public print(Ljava/lang/String;)V
    .locals 1

    .line 151
    :try_start_0
    iget-object v0, p0, Lgroovy/util/IndentPrinter;->out:Ljava/io/Writer;

    invoke-virtual {v0, p1}, Ljava/io/Writer;->write(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    .line 153
    new-instance v0, Lgroovy/lang/GroovyRuntimeException;

    invoke-direct {v0, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public printIndent()V
    .locals 3

    const/4 v0, 0x0

    .line 174
    :goto_0
    iget v1, p0, Lgroovy/util/IndentPrinter;->indentLevel:I

    if-ge v0, v1, :cond_0

    .line 176
    :try_start_0
    iget-object v1, p0, Lgroovy/util/IndentPrinter;->out:Ljava/io/Writer;

    iget-object v2, p0, Lgroovy/util/IndentPrinter;->indent:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/io/Writer;->write(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :catch_0
    move-exception v0

    .line 178
    new-instance v1, Lgroovy/lang/GroovyRuntimeException;

    invoke-direct {v1, v0}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v1

    :cond_0
    return-void
.end method

.method public println()V
    .locals 2

    .line 192
    iget-boolean v0, p0, Lgroovy/util/IndentPrinter;->addNewlines:Z

    if-eqz v0, :cond_0

    .line 194
    :try_start_0
    iget-object v0, p0, Lgroovy/util/IndentPrinter;->out:Ljava/io/Writer;

    const-string v1, "\n"

    invoke-virtual {v0, v1}, Ljava/io/Writer;->write(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 196
    new-instance v1, Lgroovy/lang/GroovyRuntimeException;

    invoke-direct {v1, v0}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v1

    :cond_0
    :goto_0
    return-void
.end method

.method public println(Ljava/lang/String;)V
    .locals 1

    .line 136
    :try_start_0
    iget-boolean v0, p0, Lgroovy/util/IndentPrinter;->autoIndent:Z

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lgroovy/util/IndentPrinter;->printIndent()V

    .line 137
    :cond_0
    iget-object v0, p0, Lgroovy/util/IndentPrinter;->out:Ljava/io/Writer;

    invoke-virtual {v0, p1}, Ljava/io/Writer;->write(Ljava/lang/String;)V

    .line 138
    invoke-virtual {p0}, Lgroovy/util/IndentPrinter;->println()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    .line 140
    new-instance v0, Lgroovy/lang/GroovyRuntimeException;

    invoke-direct {v0, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public setAutoIndent(Z)V
    .locals 0

    .line 222
    iput-boolean p1, p0, Lgroovy/util/IndentPrinter;->autoIndent:Z

    return-void
.end method

.method public setIndentLevel(I)V
    .locals 0

    .line 214
    iput p1, p0, Lgroovy/util/IndentPrinter;->indentLevel:I

    return-void
.end method
