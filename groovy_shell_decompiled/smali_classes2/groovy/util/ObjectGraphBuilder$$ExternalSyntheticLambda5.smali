.class public final synthetic Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;


# instance fields
.field public final synthetic f$0:Ljava/lang/Object;

.field public final synthetic f$1:Lgroovy/util/ObjectGraphBuilder;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda5;->f$0:Ljava/lang/Object;

    iput-object p2, p0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda5;->f$1:Lgroovy/util/ObjectGraphBuilder;

    return-void
.end method


# virtual methods
.method public final newInstance(Ljava/lang/Class;Ljava/util/Map;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda5;->f$0:Ljava/lang/Object;

    iget-object v1, p0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda5;->f$1:Lgroovy/util/ObjectGraphBuilder;

    invoke-static {v0, v1, p1, p2}, Lgroovy/util/ObjectGraphBuilder;->lambda$setNewInstanceResolver$5(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;Ljava/lang/Class;Ljava/util/Map;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
