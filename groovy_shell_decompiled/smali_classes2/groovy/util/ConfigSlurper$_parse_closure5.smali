.class public final Lgroovy/util/ConfigSlurper$_parse_closure5;
.super Lgroovy/lang/Closure;
.source "ConfigSlurper.groovy"

# interfaces
.implements Lorg/codehaus/groovy/runtime/GeneratedClosure;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/util/ConfigSlurper;->parse(Lgroovy/lang/Script;Ljava/net/URL;)Lgroovy/util/ConfigObject;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "_parse_closure5"
.end annotation


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private synthetic assignName:Lgroovy/lang/Reference;

.field private synthetic config:Lgroovy/lang/Reference;

.field private synthetic currentConditionalBlock:Lgroovy/lang/Reference;

.field private synthetic mc:Lgroovy/lang/Reference;

.field private synthetic overrides:Lgroovy/lang/Reference;

.field private synthetic prefix:Lgroovy/lang/Reference;

.field private synthetic pushStack:Lgroovy/lang/Reference;

.field private synthetic stack:Lgroovy/lang/Reference;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/16 v0, 0x43

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lgroovy/util/ConfigSlurper$_parse_closure5;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 14

    const/4 v0, 0x0

    const-string v1, "length"

    aput-object v1, p0, v0

    const/4 v0, 0x1

    const-string v2, "getAt"

    aput-object v2, p0, v0

    const/4 v0, 0x2

    const-string v3, "keySet"

    aput-object v3, p0, v0

    const/4 v0, 0x3

    const-string v3, "conditionValues"

    aput-object v3, p0, v0

    const/4 v0, 0x4

    const-string v4, "push"

    aput-object v4, p0, v0

    const/4 v0, 0x5

    aput-object v4, p0, v0

    const/4 v0, 0x6

    const-string v5, "conditionalBlocks"

    aput-object v5, p0, v0

    const/4 v0, 0x7

    const-string v6, "call"

    aput-object v6, p0, v0

    const/16 v0, 0x8

    aput-object v2, p0, v0

    const/16 v0, 0x9

    const-string v7, "pop"

    aput-object v7, p0, v0

    const/16 v0, 0xa

    const-string v8, "iterator"

    aput-object v8, p0, v0

    const/16 v0, 0xb

    const-string v9, "entrySet"

    aput-object v9, p0, v0

    const/16 v0, 0xc

    aput-object v7, p0, v0

    const/16 v0, 0xd

    aput-object v5, p0, v0

    const/16 v0, 0xe

    const-string v10, "config"

    aput-object v10, p0, v0

    const/16 v0, 0xf

    const-string v11, "last"

    aput-object v11, p0, v0

    const/16 v0, 0x10

    const-string v12, "merge"

    aput-object v12, p0, v0

    const/16 v0, 0x11

    const-string v13, "value"

    aput-object v13, p0, v0

    const/16 v0, 0x12

    aput-object v7, p0, v0

    const/16 v0, 0x13

    aput-object v8, p0, v0

    const/16 v0, 0x14

    aput-object v9, p0, v0

    const/16 v0, 0x15

    aput-object v7, p0, v0

    const/16 v0, 0x16

    aput-object v5, p0, v0

    const/16 v0, 0x17

    aput-object v10, p0, v0

    const/16 v0, 0x18

    aput-object v11, p0, v0

    const/16 v0, 0x19

    aput-object v12, p0, v0

    const/16 v0, 0x1a

    aput-object v13, p0, v0

    const/16 v0, 0x1b

    const-string v8, "size"

    aput-object v8, p0, v0

    const/16 v0, 0x1c

    const-string v8, "peek"

    aput-object v8, p0, v0

    const/16 v0, 0x1d

    aput-object v2, p0, v0

    const/16 v0, 0x1e

    aput-object v3, p0, v0

    const/16 v0, 0x1f

    const-string v3, "<$constructor$>"

    aput-object v3, p0, v0

    const/16 v0, 0x20

    const-string v9, "putAt"

    aput-object v9, p0, v0

    const/16 v0, 0x21

    aput-object v8, p0, v0

    const/16 v0, 0x22

    aput-object v5, p0, v0

    const/16 v0, 0x23

    aput-object v6, p0, v0

    const/16 v0, 0x24

    aput-object v7, p0, v0

    const/16 v0, 0x25

    aput-object v6, p0, v0

    const/16 v0, 0x26

    aput-object v2, p0, v0

    const/16 v0, 0x27

    aput-object v4, p0, v0

    const/16 v0, 0x28

    aput-object v4, p0, v0

    const/16 v0, 0x29

    const-string v4, "removeLast"

    aput-object v4, p0, v0

    const/16 v0, 0x2a

    const-string v5, "get"

    aput-object v5, p0, v0

    const/16 v0, 0x2b

    aput-object v10, p0, v0

    const/16 v0, 0x2c

    aput-object v11, p0, v0

    const/16 v0, 0x2d

    aput-object v5, p0, v0

    const/16 v0, 0x2e

    aput-object v10, p0, v0

    const/16 v0, 0x2f

    aput-object v11, p0, v0

    const/16 v0, 0x30

    aput-object v3, p0, v0

    const/16 v0, 0x31

    aput-object v6, p0, v0

    const/16 v0, 0x32

    aput-object v6, p0, v0

    const/16 v0, 0x33

    aput-object v6, p0, v0

    const/16 v0, 0x34

    aput-object v2, p0, v0

    const/16 v0, 0x35

    aput-object v4, p0, v0

    const/16 v0, 0x36

    aput-object v1, p0, v0

    const/16 v0, 0x37

    aput-object v2, p0, v0

    const/16 v0, 0x38

    const-string v1, "plus"

    aput-object v1, p0, v0

    const/16 v0, 0x39

    aput-object v6, p0, v0

    const/16 v0, 0x3a

    aput-object v2, p0, v0

    const/16 v0, 0x3b

    aput-object v6, p0, v0

    const/16 v0, 0x3c

    aput-object v2, p0, v0

    const/16 v0, 0x3d

    const-string v1, "getMetaMethod"

    aput-object v1, p0, v0

    const/16 v0, 0x3e

    const-string v1, "invoke"

    aput-object v1, p0, v0

    const/16 v0, 0x3f

    const-string v1, "delegate"

    aput-object v1, p0, v0

    const/16 v0, 0x40

    aput-object v3, p0, v0

    const/16 v0, 0x41

    const-string v1, "getClass"

    aput-object v1, p0, v0

    const/16 v0, 0x42

    const-string v1, "doCall"

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lgroovy/util/ConfigSlurper$_parse_closure5;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lgroovy/util/ConfigSlurper$_parse_closure5;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;Lgroovy/lang/Reference;)V
    .locals 0

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    iput-object p3, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    iput-object p4, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->stack:Lgroovy/lang/Reference;

    iput-object p5, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->config:Lgroovy/lang/Reference;

    iput-object p6, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->overrides:Lgroovy/lang/Reference;

    iput-object p7, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->pushStack:Lgroovy/lang/Reference;

    iput-object p8, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->assignName:Lgroovy/lang/Reference;

    iput-object p9, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->prefix:Lgroovy/lang/Reference;

    iput-object p10, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->mc:Lgroovy/lang/Reference;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/util/ConfigSlurper$_parse_closure5;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/util/ConfigSlurper$_parse_closure5;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/util/ConfigSlurper$_parse_closure5;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/16 v1, 0x42

    aget-object v0, v0, v1

    invoke-interface {v0, p0, p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public doCall(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    const-string v0, ""

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v1

    const/4 v2, 0x0

    .line 223
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    aget-object v4, v1, v2

    invoke-interface {v4, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    const/4 v5, 0x1

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    invoke-static {v4, v6}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_0

    aget-object v4, v1, v5

    invoke-interface {v4, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    instance-of v4, v4, Lgroovy/lang/Closure;

    if-eqz v4, :cond_0

    move v4, v5

    goto :goto_0

    :cond_0
    move v4, v2

    :goto_0
    const/4 v7, 0x2

    if-eqz v4, :cond_7

    .line 224
    aget-object v0, v1, v7

    const/4 v4, 0x3

    aget-object v4, v1, v4

    invoke-interface {v4, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v0, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->isCase(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    const/4 v0, 0x4

    .line 226
    :try_start_0
    aget-object v0, v1, v0

    iget-object v4, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v0, v4, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 p1, 0x5

    .line 227
    aget-object p1, v1, p1

    const/4 v0, 0x6

    aget-object v0, v1, v0

    invoke-interface {v0, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    new-array v2, v2, [Ljava/lang/Object;

    invoke-static {v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->createMap([Ljava/lang/Object;)Ljava/util/Map;

    move-result-object v2

    invoke-interface {p1, v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 p1, 0x7

    .line 228
    aget-object p1, v1, p1

    const/16 v0, 0x8

    aget-object v0, v1, v0

    invoke-interface {v0, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/16 p1, 0x9

    .line 230
    aget-object p1, v1, p1

    iget-object p2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    invoke-virtual {p2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    invoke-interface {p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0xa

    .line 231
    aget-object p1, v1, p1

    const/16 p2, 0xb

    aget-object p2, v1, p2

    const/16 v0, 0xc

    aget-object v0, v1, v0

    const/16 v2, 0xd

    aget-object v2, v1, v2

    invoke-interface {v2, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-class p2, Ljava/util/Iterator;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/util/Iterator;

    if-eqz p1, :cond_9

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_9

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    const/16 v0, 0xe

    .line 232
    aget-object v0, v1, v0

    const/16 v2, 0xf

    aget-object v2, v1, v2

    iget-object v3, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->stack:Lgroovy/lang/Reference;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const/16 v2, 0x10

    .line 233
    aget-object v2, v1, v2

    iget-object v3, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->config:Lgroovy/lang/Reference;

    invoke-virtual {v3}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    goto :goto_2

    :cond_1
    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->overrides:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    :goto_2
    const/16 v3, 0x11

    aget-object v3, v1, v3

    invoke-interface {v3, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {v2, v0, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    :catchall_0
    move-exception p1

    const/16 p2, 0x12

    .line 230
    aget-object p2, v1, p2

    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p2, 0x13

    .line 231
    aget-object p2, v1, p2

    const/16 v0, 0x14

    aget-object v0, v1, v0

    const/16 v2, 0x15

    aget-object v2, v1, v2

    const/16 v3, 0x16

    aget-object v3, v1, v3

    invoke-interface {v3, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {p2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    const-class v0, Ljava/util/Iterator;

    invoke-static {p2, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/util/Iterator;

    if-eqz p2, :cond_3

    :goto_3
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    const/16 v2, 0x17

    .line 232
    aget-object v2, v1, v2

    const/16 v3, 0x18

    aget-object v3, v1, v3

    iget-object v4, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->stack:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v3, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    const/16 v3, 0x19

    .line 233
    aget-object v3, v1, v3

    iget-object v4, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->config:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-static {v2, v4}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareNotEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_2

    goto :goto_4

    :cond_2
    iget-object v2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->overrides:Lgroovy/lang/Reference;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    :goto_4
    const/16 v4, 0x1a

    aget-object v4, v1, v4

    invoke-interface {v4, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-interface {v3, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_3

    .line 235
    :cond_3
    throw p1

    :cond_4
    const/16 v0, 0x1b

    .line 236
    aget-object v0, v1, v0

    iget-object v2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0, v3}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareGreaterThan(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_5

    const/16 v0, 0x1c

    .line 237
    aget-object v0, v1, v0

    iget-object v2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/ShortTypeHandling;->castToString(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    move-object v2, v0

    check-cast v2, Ljava/lang/String;

    const/16 v2, 0x1d

    .line 238
    aget-object v2, v1, v2

    const/16 v4, 0x1e

    aget-object v4, v1, v4

    invoke-interface {v4, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v2, v4, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-static {p1, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_9

    const/16 p1, 0x1f

    .line 239
    aget-object p1, v1, p1

    const-class v2, Lgroovy/util/ConfigObject;

    invoke-interface {p1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const/16 v2, 0x20

    .line 240
    aget-object v2, v1, v2

    const/16 v4, 0x21

    aget-object v4, v1, v4

    const/16 v5, 0x22

    aget-object v5, v1, v5

    invoke-interface {v5, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v2, v4, v0, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v2, 0x23

    .line 242
    aget-object v2, v1, v2

    iget-object v4, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->pushStack:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v2, v4, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x24

    .line 244
    :try_start_1
    aget-object p1, v1, p1

    iget-object v2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-interface {p1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x25

    .line 245
    aget-object p1, v1, p1

    const/16 v2, 0x26

    aget-object v2, v1, v2

    invoke-interface {v2, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    const/16 p1, 0x27

    .line 247
    aget-object p1, v1, p1

    iget-object p2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    invoke-virtual {p2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    invoke-interface {p1, p2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x29

    .line 249
    aget-object p1, v1, p1

    iget-object p2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->stack:Lgroovy/lang/Reference;

    invoke-virtual {p2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    invoke-interface {p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_6

    :catchall_1
    move-exception p1

    const/16 p2, 0x28

    .line 247
    aget-object p2, v1, p2

    iget-object v1, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    invoke-virtual {v1}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v1

    invoke-interface {p2, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 248
    throw p1

    :cond_5
    const/16 v0, 0x2a

    .line 253
    aget-object v0, v1, v0

    const/16 v2, 0x2b

    aget-object v2, v1, v2

    const/16 v4, 0x2c

    aget-object v4, v1, v4

    iget-object v5, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->stack:Lgroovy/lang/Reference;

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v2, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    instance-of v0, v0, Lgroovy/util/ConfigObject;

    if-eqz v0, :cond_6

    const/16 v0, 0x2d

    .line 254
    aget-object v0, v1, v0

    const/16 v2, 0x2e

    aget-object v2, v1, v2

    const/16 v4, 0x2f

    aget-object v4, v1, v4

    iget-object v5, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->stack:Lgroovy/lang/Reference;

    invoke-virtual {v5}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v5

    invoke-interface {v4, v5}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v2, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v2, p1}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    goto :goto_5

    :cond_6
    const/16 v0, 0x30

    .line 256
    aget-object v0, v1, v0

    const-class v2, Lgroovy/util/ConfigObject;

    invoke-interface {v0, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    :goto_5
    const/16 v2, 0x31

    .line 259
    aget-object v2, v1, v2

    iget-object v4, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->assignName:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v2, v4, p1, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x32

    .line 260
    aget-object p1, v1, p1

    iget-object v2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->pushStack:Lgroovy/lang/Reference;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-interface {p1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x33

    .line 261
    aget-object p1, v1, p1

    const/16 v0, 0x34

    aget-object v0, v1, v0

    invoke-interface {v0, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x35

    .line 262
    aget-object p1, v1, p1

    iget-object p2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->stack:Lgroovy/lang/Reference;

    invoke-virtual {p2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object p2

    invoke-interface {p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_6

    :cond_7
    const/16 v4, 0x36

    .line 264
    aget-object v4, v1, v4

    invoke-interface {v4, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v7

    invoke-static {v4, v7}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_8

    const/16 v4, 0x37

    aget-object v4, v1, v4

    invoke-interface {v4, p2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    instance-of v4, v4, Lgroovy/lang/Closure;

    if-eqz v4, :cond_8

    move v2, v5

    :cond_8
    if-eqz v2, :cond_a

    const/16 v2, 0x38

    .line 266
    :try_start_2
    aget-object v2, v1, v2

    const-string v4, "."

    invoke-interface {v2, p1, v4}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    iget-object v4, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->prefix:Lgroovy/lang/Reference;

    invoke-virtual {v4, v2}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    const/16 v2, 0x39

    .line 267
    aget-object v2, v1, v2

    iget-object v4, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->assignName:Lgroovy/lang/Reference;

    invoke-virtual {v4}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v4

    const/16 v5, 0x3a

    aget-object v5, v1, v5

    invoke-interface {v5, p2, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v4, p1, v3}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 p1, 0x3b

    .line 268
    aget-object p1, v1, p1

    const/16 v2, 0x3c

    aget-object v1, v1, v2

    invoke-interface {v1, p2, v6}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    invoke-interface {p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_2

    .line 269
    iget-object p1, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->prefix:Lgroovy/lang/Reference;

    invoke-virtual {p1, v0}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    :cond_9
    :goto_6
    const/4 p1, 0x0

    goto :goto_7

    :catchall_2
    move-exception p1

    iget-object p2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->prefix:Lgroovy/lang/Reference;

    invoke-virtual {p2, v0}, Lgroovy/lang/Reference;->set(Ljava/lang/Object;)V

    throw p1

    :cond_a
    const/16 v0, 0x3d

    .line 271
    aget-object v0, v1, v0

    iget-object v2, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->mc:Lgroovy/lang/Reference;

    invoke-virtual {v2}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v0, v2, p1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    const-class v2, Lgroovy/lang/MetaMethod;

    invoke-static {v0, v2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovy/lang/MetaMethod;

    .line 272
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->booleanUnbox(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_b

    const/16 p1, 0x3e

    .line 273
    aget-object p1, v1, p1

    const/16 v2, 0x3f

    aget-object v1, v1, v2

    invoke-interface {v1, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callGroovyObjectGetProperty(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {p1, v0, v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->call(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    :goto_7
    return-object p1

    :cond_b
    const/16 v0, 0x40

    .line 275
    aget-object v0, v1, v0

    const-class v2, Lgroovy/lang/MissingMethodException;

    const/16 v3, 0x41

    aget-object v1, v1, v3

    invoke-interface {v1, p0}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callCurrent(Lgroovy/lang/GroovyObject;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v0, v2, p1, v1, p2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Throwable;

    throw p1
.end method

.method public getAssignName()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->assignName:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public getConfig()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->config:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public getCurrentConditionalBlock()Ljava/util/Stack;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->currentConditionalBlock:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Ljava/util/Stack;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/Stack;

    return-object v0
.end method

.method public getMc()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->mc:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public getOverrides()Lgroovy/util/ConfigObject;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->overrides:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Lgroovy/util/ConfigObject;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovy/util/ConfigObject;

    return-object v0
.end method

.method public getPrefix()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->prefix:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public getPushStack()Ljava/lang/Object;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->pushStack:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public getStack()Ljava/util/LinkedList;
    .locals 2
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/ConfigSlurper$_parse_closure5;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    iget-object v0, p0, Lgroovy/util/ConfigSlurper$_parse_closure5;->stack:Lgroovy/lang/Reference;

    invoke-virtual {v0}, Lgroovy/lang/Reference;->get()Ljava/lang/Object;

    move-result-object v0

    const-class v1, Ljava/util/LinkedList;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/util/LinkedList;

    return-object v0
.end method
