.class public Lgroovy/util/PermutationGenerator;
.super Ljava/lang/Object;
.source "PermutationGenerator.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "Ljava/util/List<",
        "TE;>;>;"
    }
.end annotation


# instance fields
.field private final a:[I

.field private final items:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "TE;>;"
        }
    .end annotation
.end field

.field private numLeft:Ljava/math/BigInteger;

.field private final total:Ljava/math/BigInteger;


# direct methods
.method public constructor <init>(Ljava/lang/Iterable;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Iterable<",
            "TE;>;)V"
        }
    .end annotation

    .line 63
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->asCollection(Ljava/lang/Iterable;)Ljava/util/Collection;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovy/util/PermutationGenerator;-><init>(Ljava/util/Collection;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/Collection;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "TE;>;)V"
        }
    .end annotation

    .line 51
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 52
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0, p1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Lgroovy/util/PermutationGenerator;->items:Ljava/util/List;

    .line 53
    invoke-interface {p1}, Ljava/util/Collection;->size()I

    move-result p1

    const/4 v0, 0x1

    if-lt p1, v0, :cond_0

    .line 57
    new-array v0, p1, [I

    iput-object v0, p0, Lgroovy/util/PermutationGenerator;->a:[I

    .line 58
    invoke-static {p1}, Lgroovy/util/PermutationGenerator;->getFactorial(I)Ljava/math/BigInteger;

    move-result-object p1

    iput-object p1, p0, Lgroovy/util/PermutationGenerator;->total:Ljava/math/BigInteger;

    .line 59
    invoke-virtual {p0}, Lgroovy/util/PermutationGenerator;->reset()V

    return-void

    .line 55
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "At least one item required"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method private static getFactorial(I)Ljava/math/BigInteger;
    .locals 3

    .line 88
    sget-object v0, Ljava/math/BigInteger;->ONE:Ljava/math/BigInteger;

    :goto_0
    const/4 v1, 0x1

    if-le p0, v1, :cond_0

    .line 90
    new-instance v1, Ljava/math/BigInteger;

    invoke-static {p0}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Ljava/math/BigInteger;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Ljava/math/BigInteger;->multiply(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object v0

    add-int/lit8 p0, p0, -0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method


# virtual methods
.method public getTotal()Ljava/math/BigInteger;
    .locals 1

    .line 74
    iget-object v0, p0, Lgroovy/util/PermutationGenerator;->total:Ljava/math/BigInteger;

    return-object v0
.end method

.method public hasNext()Z
    .locals 2

    .line 78
    iget-object v0, p0, Lgroovy/util/PermutationGenerator;->numLeft:Ljava/math/BigInteger;

    sget-object v1, Ljava/math/BigInteger;->ZERO:Ljava/math/BigInteger;

    invoke-virtual {v0, v1}, Ljava/math/BigInteger;->compareTo(Ljava/math/BigInteger;)I

    move-result v0

    if-lez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public bridge synthetic next()Ljava/lang/Object;
    .locals 1

    .line 35
    invoke-virtual {p0}, Lgroovy/util/PermutationGenerator;->next()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public next()Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "TE;>;"
        }
    .end annotation

    .line 101
    iget-object v0, p0, Lgroovy/util/PermutationGenerator;->numLeft:Ljava/math/BigInteger;

    iget-object v1, p0, Lgroovy/util/PermutationGenerator;->total:Ljava/math/BigInteger;

    invoke-virtual {v0, v1}, Ljava/math/BigInteger;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 102
    iget-object v0, p0, Lgroovy/util/PermutationGenerator;->numLeft:Ljava/math/BigInteger;

    sget-object v1, Ljava/math/BigInteger;->ONE:Ljava/math/BigInteger;

    invoke-virtual {v0, v1}, Ljava/math/BigInteger;->subtract(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object v0

    iput-object v0, p0, Lgroovy/util/PermutationGenerator;->numLeft:Ljava/math/BigInteger;

    .line 103
    iget-object v0, p0, Lgroovy/util/PermutationGenerator;->items:Ljava/util/List;

    return-object v0

    .line 109
    :cond_0
    iget-object v0, p0, Lgroovy/util/PermutationGenerator;->a:[I

    array-length v0, v0

    add-int/lit8 v0, v0, -0x2

    .line 110
    :goto_0
    iget-object v1, p0, Lgroovy/util/PermutationGenerator;->a:[I

    aget v2, v1, v0

    add-int/lit8 v3, v0, 0x1

    aget v4, v1, v3

    if-le v2, v4, :cond_1

    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    .line 116
    :cond_1
    array-length v1, v1

    add-int/lit8 v1, v1, -0x1

    .line 117
    :goto_1
    iget-object v2, p0, Lgroovy/util/PermutationGenerator;->a:[I

    aget v4, v2, v0

    aget v5, v2, v1

    if-le v4, v5, :cond_2

    add-int/lit8 v1, v1, -0x1

    goto :goto_1

    .line 122
    :cond_2
    aget v4, v2, v1

    .line 123
    aget v5, v2, v0

    aput v5, v2, v1

    .line 124
    aput v4, v2, v0

    .line 127
    array-length v0, v2

    add-int/lit8 v0, v0, -0x1

    :goto_2
    if-le v0, v3, :cond_3

    .line 131
    iget-object v1, p0, Lgroovy/util/PermutationGenerator;->a:[I

    aget v2, v1, v3

    .line 132
    aget v4, v1, v0

    aput v4, v1, v3

    .line 133
    aput v2, v1, v0

    add-int/lit8 v0, v0, -0x1

    add-int/lit8 v3, v3, 0x1

    goto :goto_2

    .line 138
    :cond_3
    iget-object v0, p0, Lgroovy/util/PermutationGenerator;->numLeft:Ljava/math/BigInteger;

    sget-object v1, Ljava/math/BigInteger;->ONE:Ljava/math/BigInteger;

    invoke-virtual {v0, v1}, Ljava/math/BigInteger;->subtract(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object v0

    iput-object v0, p0, Lgroovy/util/PermutationGenerator;->numLeft:Ljava/math/BigInteger;

    .line 139
    new-instance v0, Ljava/util/ArrayList;

    iget-object v1, p0, Lgroovy/util/PermutationGenerator;->a:[I

    array-length v1, v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 140
    iget-object v1, p0, Lgroovy/util/PermutationGenerator;->a:[I

    array-length v2, v1

    const/4 v3, 0x0

    :goto_3
    if-ge v3, v2, :cond_4

    aget v4, v1, v3

    .line 141
    iget-object v5, p0, Lgroovy/util/PermutationGenerator;->items:Ljava/util/List;

    invoke-interface {v5, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    invoke-interface {v0, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v3, v3, 0x1

    goto :goto_3

    :cond_4
    return-object v0
.end method

.method public remove()V
    .locals 2

    .line 147
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "remove() not allowed for PermutationGenerator"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public reset()V
    .locals 3

    const/4 v0, 0x0

    .line 67
    :goto_0
    iget-object v1, p0, Lgroovy/util/PermutationGenerator;->a:[I

    array-length v2, v1

    if-ge v0, v2, :cond_0

    .line 68
    aput v0, v1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 70
    :cond_0
    new-instance v0, Ljava/math/BigInteger;

    iget-object v1, p0, Lgroovy/util/PermutationGenerator;->total:Ljava/math/BigInteger;

    invoke-virtual {v1}, Ljava/math/BigInteger;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/math/BigInteger;-><init>(Ljava/lang/String;)V

    iput-object v0, p0, Lgroovy/util/PermutationGenerator;->numLeft:Ljava/math/BigInteger;

    return-void
.end method
