.class public Lgroovy/util/ObjectGraphBuilder;
.super Lgroovy/util/FactoryBuilderSupport;
.source "ObjectGraphBuilder.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;,
        Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;,
        Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;,
        Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;,
        Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;,
        Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;,
        Lgroovy/util/ObjectGraphBuilder$ObjectFactory;,
        Lgroovy/util/ObjectGraphBuilder$ObjectBeanFactory;,
        Lgroovy/util/ObjectGraphBuilder$ObjectRefFactory;,
        Lgroovy/util/ObjectGraphBuilder$DefaultClassNameResolver;,
        Lgroovy/util/ObjectGraphBuilder$DefaultNewInstanceResolver;,
        Lgroovy/util/ObjectGraphBuilder$DefaultRelationNameResolver;,
        Lgroovy/util/ObjectGraphBuilder$DefaultChildPropertySetter;,
        Lgroovy/util/ObjectGraphBuilder$DefaultIdentifierResolver;,
        Lgroovy/util/ObjectGraphBuilder$DefaultReferenceResolver;,
        Lgroovy/util/ObjectGraphBuilder$ReflectionClassNameResolver;,
        Lgroovy/util/ObjectGraphBuilder$NodeReference;
    }
.end annotation


# static fields
.field public static final CLASSNAME_RESOLVER_KEY:Ljava/lang/String; = "name"

.field public static final CLASSNAME_RESOLVER_REFLECTION:Ljava/lang/String; = "reflection"

.field public static final CLASSNAME_RESOLVER_REFLECTION_ROOT:Ljava/lang/String; = "root"

.field public static final LAZY_REF:Ljava/lang/String; = "_LAZY_REF_"

.field public static final NODE_CLASS:Ljava/lang/String; = "_NODE_CLASS_"

.field public static final NODE_NAME:Ljava/lang/String; = "_NODE_NAME_"

.field public static final OBJECT_ID:Ljava/lang/String; = "_OBJECT_ID_"

.field private static final PLURAL_IES_PATTERN:Ljava/util/regex/Pattern;


# instance fields
.field private beanFactoryName:Ljava/lang/String;

.field private childPropertySetter:Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

.field private classLoader:Ljava/lang/ClassLoader;

.field private classNameResolver:Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

.field private identifierResolver:Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

.field private final lazyReferences:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovy/util/ObjectGraphBuilder$NodeReference;",
            ">;"
        }
    .end annotation
.end field

.field private lazyReferencesAllowed:Z

.field private newInstanceResolver:Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;

.field private final objectBeanFactory:Lgroovy/util/ObjectGraphBuilder$ObjectBeanFactory;

.field private final objectFactory:Lgroovy/util/ObjectGraphBuilder$ObjectFactory;

.field private final objectRefFactory:Lgroovy/util/ObjectGraphBuilder$ObjectRefFactory;

.field private referenceResolver:Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

.field private relationNameResolver:Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;

.field private final resolvedClasses:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    const-string v0, ".*[^aeiouy]y"

    const/4 v1, 0x2

    .line 52
    invoke-static {v0, v1}, Ljava/util/regex/Pattern;->compile(Ljava/lang/String;I)Ljava/util/regex/Pattern;

    move-result-object v0

    sput-object v0, Lgroovy/util/ObjectGraphBuilder;->PLURAL_IES_PATTERN:Ljava/util/regex/Pattern;

    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 69
    invoke-direct {p0}, Lgroovy/util/FactoryBuilderSupport;-><init>()V

    .line 58
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$ObjectFactory;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovy/util/ObjectGraphBuilder$ObjectFactory;-><init>(Lgroovy/util/ObjectGraphBuilder$1;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->objectFactory:Lgroovy/util/ObjectGraphBuilder$ObjectFactory;

    .line 59
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$ObjectBeanFactory;

    invoke-direct {v0, v1}, Lgroovy/util/ObjectGraphBuilder$ObjectBeanFactory;-><init>(Lgroovy/util/ObjectGraphBuilder$1;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->objectBeanFactory:Lgroovy/util/ObjectGraphBuilder$ObjectBeanFactory;

    .line 60
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$ObjectRefFactory;

    invoke-direct {v0, v1}, Lgroovy/util/ObjectGraphBuilder$ObjectRefFactory;-><init>(Lgroovy/util/ObjectGraphBuilder$1;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->objectRefFactory:Lgroovy/util/ObjectGraphBuilder$ObjectRefFactory;

    .line 63
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->resolvedClasses:Ljava/util/Map;

    const/4 v0, 0x1

    .line 65
    iput-boolean v0, p0, Lgroovy/util/ObjectGraphBuilder;->lazyReferencesAllowed:Z

    .line 66
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->lazyReferences:Ljava/util/List;

    const-string v0, "bean"

    .line 67
    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->beanFactoryName:Ljava/lang/String;

    .line 70
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$DefaultClassNameResolver;

    invoke-direct {v0}, Lgroovy/util/ObjectGraphBuilder$DefaultClassNameResolver;-><init>()V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->classNameResolver:Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    .line 71
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$DefaultNewInstanceResolver;

    invoke-direct {v0}, Lgroovy/util/ObjectGraphBuilder$DefaultNewInstanceResolver;-><init>()V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->newInstanceResolver:Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;

    .line 72
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$DefaultRelationNameResolver;

    invoke-direct {v0}, Lgroovy/util/ObjectGraphBuilder$DefaultRelationNameResolver;-><init>()V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->relationNameResolver:Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;

    .line 73
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$DefaultChildPropertySetter;

    invoke-direct {v0}, Lgroovy/util/ObjectGraphBuilder$DefaultChildPropertySetter;-><init>()V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->childPropertySetter:Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

    .line 74
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$DefaultIdentifierResolver;

    invoke-direct {v0}, Lgroovy/util/ObjectGraphBuilder$DefaultIdentifierResolver;-><init>()V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->identifierResolver:Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

    .line 75
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$DefaultReferenceResolver;

    invoke-direct {v0}, Lgroovy/util/ObjectGraphBuilder$DefaultReferenceResolver;-><init>()V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->referenceResolver:Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

    .line 77
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$1;

    invoke-direct {v0, p0, p0, p0}, Lgroovy/util/ObjectGraphBuilder$1;-><init>(Lgroovy/util/ObjectGraphBuilder;Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObjectGraphBuilder;->addPostNodeCompletionDelegate(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    return-void
.end method

.method static synthetic access$1000(Lgroovy/util/ObjectGraphBuilder;)Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder;->classNameResolver:Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    return-object p0
.end method

.method static synthetic access$1100(Lgroovy/util/ObjectGraphBuilder;)Ljava/util/Map;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder;->resolvedClasses:Ljava/util/Map;

    return-object p0
.end method

.method static synthetic access$1200(Lgroovy/util/ObjectGraphBuilder;)Ljava/lang/ClassLoader;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder;->classLoader:Ljava/lang/ClassLoader;

    return-object p0
.end method

.method static synthetic access$1300(Lgroovy/util/ObjectGraphBuilder;)Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder;->newInstanceResolver:Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;

    return-object p0
.end method

.method static synthetic access$1400(Lgroovy/util/ObjectGraphBuilder;)Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder;->relationNameResolver:Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;

    return-object p0
.end method

.method static synthetic access$1500(Lgroovy/util/ObjectGraphBuilder;)Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder;->childPropertySetter:Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

    return-object p0
.end method

.method static synthetic access$1600(Lgroovy/util/ObjectGraphBuilder;)Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder;->referenceResolver:Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

    return-object p0
.end method

.method static synthetic access$1700(Lgroovy/util/ObjectGraphBuilder;)Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder;->identifierResolver:Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

    return-object p0
.end method

.method static synthetic access$1900(Lgroovy/util/ObjectGraphBuilder;)Ljava/util/List;
    .locals 0

    .line 41
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder;->lazyReferences:Ljava/util/List;

    return-object p0
.end method

.method static synthetic access$300(Lgroovy/util/ObjectGraphBuilder;)V
    .locals 0

    .line 41
    invoke-direct {p0}, Lgroovy/util/ObjectGraphBuilder;->resolveLazyReferences()V

    return-void
.end method

.method static synthetic access$400(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 41
    invoke-static {p0, p1}, Lgroovy/util/ObjectGraphBuilder;->makeClassName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$500()Ljava/util/regex/Pattern;
    .locals 1

    .line 41
    sget-object v0, Lgroovy/util/ObjectGraphBuilder;->PLURAL_IES_PATTERN:Ljava/util/regex/Pattern;

    return-object v0
.end method

.method static synthetic lambda$setChildPropertySetter$0(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 156
    check-cast p0, Lgroovy/lang/Closure;

    .line 157
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    const/4 p1, 0x4

    new-array p1, p1, [Ljava/lang/Object;

    const/4 v0, 0x0

    aput-object p2, p1, v0

    const/4 p2, 0x1

    aput-object p3, p1, p2

    const/4 p2, 0x2

    aput-object p4, p1, p2

    const/4 p2, 0x3

    aput-object p5, p1, p2

    .line 158
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method static synthetic lambda$setClassNameResolver$1(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 181
    check-cast p0, Ljava/lang/String;

    invoke-static {p0, p1}, Lgroovy/util/ObjectGraphBuilder;->makeClassName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$setClassNameResolver$2(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    .line 185
    check-cast p0, Lgroovy/lang/Closure;

    .line 186
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    const/4 p1, 0x1

    new-array p1, p1, [Ljava/lang/Object;

    const/4 v0, 0x0

    aput-object p2, p1, v0

    .line 187
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/String;

    return-object p0
.end method

.method static synthetic lambda$setIdentifierResolver$3(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 223
    check-cast p0, Ljava/lang/String;

    return-object p0
.end method

.method static synthetic lambda$setIdentifierResolver$4(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    .line 227
    check-cast p0, Lgroovy/lang/Closure;

    .line 228
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    const/4 p1, 0x1

    new-array p1, p1, [Ljava/lang/Object;

    const/4 v0, 0x0

    aput-object p2, p1, v0

    .line 229
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/String;

    return-object p0
.end method

.method static synthetic lambda$setNewInstanceResolver$5(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;Ljava/lang/Class;Ljava/util/Map;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InstantiationException;,
            Ljava/lang/IllegalAccessException;
        }
    .end annotation

    .line 254
    check-cast p0, Lgroovy/lang/Closure;

    .line 255
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    const/4 p1, 0x2

    new-array p1, p1, [Ljava/lang/Object;

    const/4 v0, 0x0

    aput-object p2, p1, v0

    const/4 p2, 0x1

    aput-object p3, p1, p2

    .line 256
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$setReferenceResolver$6(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 272
    check-cast p0, Ljava/lang/String;

    return-object p0
.end method

.method static synthetic lambda$setReferenceResolver$7(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    .line 276
    check-cast p0, Lgroovy/lang/Closure;

    .line 277
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    const/4 p1, 0x1

    new-array p1, p1, [Ljava/lang/Object;

    const/4 v0, 0x0

    aput-object p2, p1, v0

    .line 278
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/String;

    return-object p0
.end method

.method private static makeClassName(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 594
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, "."

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const/4 v0, 0x0

    const/4 v1, 0x1

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->toUpperCase()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p1, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private resolveLazyReferences()V
    .locals 10

    .line 562
    iget-boolean v0, p0, Lgroovy/util/ObjectGraphBuilder;->lazyReferencesAllowed:Z

    if-nez v0, :cond_0

    return-void

    .line 563
    :cond_0
    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->lazyReferences:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/util/ObjectGraphBuilder$NodeReference;

    .line 564
    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$600(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/Object;

    move-result-object v2

    if-nez v2, :cond_2

    goto :goto_0

    :cond_2
    const/4 v2, 0x0

    .line 568
    :try_start_0
    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$700(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lgroovy/util/ObjectGraphBuilder;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v2
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    if-eqz v2, :cond_3

    .line 578
    iget-object v3, p0, Lgroovy/util/ObjectGraphBuilder;->childPropertySetter:Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$600(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/Object;

    move-result-object v4

    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$800(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;

    move-result-object v5

    iget-object v6, p0, Lgroovy/util/ObjectGraphBuilder;->relationNameResolver:Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;

    .line 579
    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$800(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;

    move-result-object v7

    .line 580
    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$600(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/Object;

    move-result-object v8

    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$900(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;

    move-result-object v9

    .line 579
    invoke-interface {v6, v7, v8, v9, v2}, Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;->resolveChildRelationName(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v6

    .line 578
    invoke-interface {v3, v4, v2, v5, v6}, Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;->setChild(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;)V

    .line 583
    iget-object v3, p0, Lgroovy/util/ObjectGraphBuilder;->relationNameResolver:Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;

    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$800(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;

    move-result-object v4

    .line 584
    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$600(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/Object;

    move-result-object v5

    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$900(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;

    move-result-object v6

    .line 583
    invoke-interface {v3, v4, v5, v6, v2}, Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;->resolveParentRelationName(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    .line 585
    invoke-static {v2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v4

    .line 586
    invoke-interface {v4, v2, v3}, Lgroovy/lang/MetaClass;->hasProperty(Ljava/lang/Object;Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object v3

    if-eqz v3, :cond_1

    .line 588
    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$600(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v3, v2, v1}, Lgroovy/lang/MetaProperty;->setProperty(Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_0

    .line 573
    :cond_3
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "There is no valid node for reference "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    .line 574
    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$800(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "."

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$900(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-static {v1}, Lgroovy/util/ObjectGraphBuilder$NodeReference;->access$700(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_4
    return-void
.end method


# virtual methods
.method public getBeanFactoryName()Ljava/lang/String;
    .locals 1

    .line 93
    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->beanFactoryName:Ljava/lang/String;

    return-object v0
.end method

.method public getChildPropertySetter()Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;
    .locals 1

    .line 100
    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->childPropertySetter:Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

    return-object v0
.end method

.method public getClassLoader()Ljava/lang/ClassLoader;
    .locals 1

    .line 107
    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->classLoader:Ljava/lang/ClassLoader;

    return-object v0
.end method

.method public getClassNameResolver()Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;
    .locals 1

    .line 114
    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->classNameResolver:Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    return-object v0
.end method

.method public getNewInstanceResolver()Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;
    .locals 1

    .line 121
    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->newInstanceResolver:Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;

    return-object v0
.end method

.method public getRelationNameResolver()Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;
    .locals 1

    .line 128
    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->relationNameResolver:Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;

    return-object v0
.end method

.method public isLazyReferencesAllowed()Z
    .locals 1

    .line 135
    iget-boolean v0, p0, Lgroovy/util/ObjectGraphBuilder;->lazyReferencesAllowed:Z

    return v0
.end method

.method protected postInstantiate(Ljava/lang/Object;Ljava/util/Map;Ljava/lang/Object;)V
    .locals 0

    .line 295
    invoke-super {p0, p1, p2, p3}, Lgroovy/util/FactoryBuilderSupport;->postInstantiate(Ljava/lang/Object;Ljava/util/Map;Ljava/lang/Object;)V

    .line 296
    invoke-virtual {p0}, Lgroovy/util/ObjectGraphBuilder;->getContext()Ljava/util/Map;

    move-result-object p1

    const-string p2, "_OBJECT_ID_"

    .line 297
    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    if-eqz p1, :cond_0

    if-eqz p3, :cond_0

    .line 299
    invoke-virtual {p0, p1, p3}, Lgroovy/util/ObjectGraphBuilder;->setVariable(Ljava/lang/String;Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method protected preInstantiate(Ljava/lang/Object;Ljava/util/Map;Ljava/lang/Object;)V
    .locals 1

    .line 304
    invoke-super {p0, p1, p2, p3}, Lgroovy/util/FactoryBuilderSupport;->preInstantiate(Ljava/lang/Object;Ljava/util/Map;Ljava/lang/Object;)V

    .line 305
    invoke-virtual {p0}, Lgroovy/util/ObjectGraphBuilder;->getContext()Ljava/util/Map;

    move-result-object p3

    .line 306
    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->identifierResolver:Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

    check-cast p1, Ljava/lang/String;

    .line 307
    invoke-interface {v0, p1}, Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;->getIdentifierFor(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-interface {p2, p1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    const-string p2, "_OBJECT_ID_"

    .line 306
    invoke-interface {p3, p2, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method protected resolveFactory(Ljava/lang/Object;Ljava/util/Map;Ljava/lang/Object;)Lgroovy/util/Factory;
    .locals 0

    .line 312
    invoke-super {p0, p1, p2, p3}, Lgroovy/util/FactoryBuilderSupport;->resolveFactory(Ljava/lang/Object;Ljava/util/Map;Ljava/lang/Object;)Lgroovy/util/Factory;

    move-result-object p3

    if-eqz p3, :cond_0

    return-object p3

    .line 316
    :cond_0
    iget-object p3, p0, Lgroovy/util/ObjectGraphBuilder;->referenceResolver:Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

    check-cast p1, Ljava/lang/String;

    invoke-interface {p3, p1}, Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;->getReferenceFor(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p3

    invoke-interface {p2, p3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    if-eqz p2, :cond_1

    .line 317
    iget-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->objectRefFactory:Lgroovy/util/ObjectGraphBuilder$ObjectRefFactory;

    return-object p1

    .line 319
    :cond_1
    iget-object p2, p0, Lgroovy/util/ObjectGraphBuilder;->beanFactoryName:Ljava/lang/String;

    if-eqz p2, :cond_2

    invoke-virtual {p2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    .line 320
    iget-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->objectBeanFactory:Lgroovy/util/ObjectGraphBuilder$ObjectBeanFactory;

    return-object p1

    .line 322
    :cond_2
    iget-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->objectFactory:Lgroovy/util/ObjectGraphBuilder$ObjectFactory;

    return-object p1
.end method

.method public setBeanFactoryName(Ljava/lang/String;)V
    .locals 0

    .line 142
    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->beanFactoryName:Ljava/lang/String;

    return-void
.end method

.method public setChildPropertySetter(Ljava/lang/Object;)V
    .locals 1

    .line 151
    instance-of v0, p1, Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

    if-eqz v0, :cond_0

    .line 152
    check-cast p1, Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->childPropertySetter:Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

    goto :goto_0

    .line 153
    :cond_0
    instance-of v0, p1, Lgroovy/lang/Closure;

    if-eqz v0, :cond_1

    .line 155
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda0;

    invoke-direct {v0, p1, p0}, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda0;-><init>(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->childPropertySetter:Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

    goto :goto_0

    .line 161
    :cond_1
    new-instance p1, Lgroovy/util/ObjectGraphBuilder$DefaultChildPropertySetter;

    invoke-direct {p1}, Lgroovy/util/ObjectGraphBuilder$DefaultChildPropertySetter;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->childPropertySetter:Lgroovy/util/ObjectGraphBuilder$ChildPropertySetter;

    :goto_0
    return-void
.end method

.method public setClassLoader(Ljava/lang/ClassLoader;)V
    .locals 0

    .line 169
    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->classLoader:Ljava/lang/ClassLoader;

    return-void
.end method

.method public setClassNameResolver(Ljava/lang/Object;)V
    .locals 3

    .line 178
    instance-of v0, p1, Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    if-eqz v0, :cond_0

    .line 179
    check-cast p1, Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->classNameResolver:Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    goto/16 :goto_0

    .line 180
    :cond_0
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_1

    .line 181
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda1;

    invoke-direct {v0, p1}, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda1;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->classNameResolver:Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    goto :goto_0

    .line 182
    :cond_1
    instance-of v0, p1, Lgroovy/lang/Closure;

    if-eqz v0, :cond_2

    .line 184
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda2;

    invoke-direct {v0, p1, p0}, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda2;-><init>(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->classNameResolver:Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    goto :goto_0

    .line 189
    :cond_2
    instance-of v0, p1, Ljava/util/Map;

    if-eqz v0, :cond_6

    .line 190
    check-cast p1, Ljava/util/Map;

    const-string v0, "name"

    .line 192
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    if-eqz v0, :cond_5

    const-string v1, "reflection"

    .line 198
    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_4

    const-string v0, "root"

    .line 199
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    if-eqz p1, :cond_3

    .line 205
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$ReflectionClassNameResolver;

    invoke-direct {v0, p0, p1}, Lgroovy/util/ObjectGraphBuilder$ReflectionClassNameResolver;-><init>(Lgroovy/util/ObjectGraphBuilder;Ljava/lang/String;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->classNameResolver:Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    goto :goto_0

    .line 202
    :cond_3
    new-instance p1, Ljava/lang/RuntimeException;

    const-string v0, "key \'root\' not defined"

    invoke-direct {p1, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 207
    :cond_4
    new-instance p1, Ljava/lang/RuntimeException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "unknown class name resolver "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 195
    :cond_5
    new-instance p1, Ljava/lang/RuntimeException;

    const-string v0, "key \'name\' not defined"

    invoke-direct {p1, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 210
    :cond_6
    new-instance p1, Lgroovy/util/ObjectGraphBuilder$DefaultClassNameResolver;

    invoke-direct {p1}, Lgroovy/util/ObjectGraphBuilder$DefaultClassNameResolver;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->classNameResolver:Lgroovy/util/ObjectGraphBuilder$ClassNameResolver;

    :goto_0
    return-void
.end method

.method public setIdentifierResolver(Ljava/lang/Object;)V
    .locals 1

    .line 220
    instance-of v0, p1, Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

    if-eqz v0, :cond_0

    .line 221
    check-cast p1, Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->identifierResolver:Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

    goto :goto_0

    .line 222
    :cond_0
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_1

    .line 223
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda3;

    invoke-direct {v0, p1}, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda3;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->identifierResolver:Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

    goto :goto_0

    .line 224
    :cond_1
    instance-of v0, p1, Lgroovy/lang/Closure;

    if-eqz v0, :cond_2

    .line 226
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda4;

    invoke-direct {v0, p1, p0}, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda4;-><init>(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->identifierResolver:Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

    goto :goto_0

    .line 232
    :cond_2
    new-instance p1, Lgroovy/util/ObjectGraphBuilder$DefaultIdentifierResolver;

    invoke-direct {p1}, Lgroovy/util/ObjectGraphBuilder$DefaultIdentifierResolver;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->identifierResolver:Lgroovy/util/ObjectGraphBuilder$IdentifierResolver;

    :goto_0
    return-void
.end method

.method public setLazyReferencesAllowed(Z)V
    .locals 0

    .line 240
    iput-boolean p1, p0, Lgroovy/util/ObjectGraphBuilder;->lazyReferencesAllowed:Z

    return-void
.end method

.method public setNewInstanceResolver(Ljava/lang/Object;)V
    .locals 1

    .line 249
    instance-of v0, p1, Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;

    if-eqz v0, :cond_0

    .line 250
    check-cast p1, Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->newInstanceResolver:Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;

    goto :goto_0

    .line 251
    :cond_0
    instance-of v0, p1, Lgroovy/lang/Closure;

    if-eqz v0, :cond_1

    .line 253
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda5;

    invoke-direct {v0, p1, p0}, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda5;-><init>(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->newInstanceResolver:Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;

    goto :goto_0

    .line 259
    :cond_1
    new-instance p1, Lgroovy/util/ObjectGraphBuilder$DefaultNewInstanceResolver;

    invoke-direct {p1}, Lgroovy/util/ObjectGraphBuilder$DefaultNewInstanceResolver;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->newInstanceResolver:Lgroovy/util/ObjectGraphBuilder$NewInstanceResolver;

    :goto_0
    return-void
.end method

.method public setReferenceResolver(Ljava/lang/Object;)V
    .locals 1

    .line 269
    instance-of v0, p1, Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

    if-eqz v0, :cond_0

    .line 270
    check-cast p1, Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->referenceResolver:Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

    goto :goto_0

    .line 271
    :cond_0
    instance-of v0, p1, Ljava/lang/String;

    if-eqz v0, :cond_1

    .line 272
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda6;

    invoke-direct {v0, p1}, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda6;-><init>(Ljava/lang/Object;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->referenceResolver:Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

    goto :goto_0

    .line 273
    :cond_1
    instance-of v0, p1, Lgroovy/lang/Closure;

    if-eqz v0, :cond_2

    .line 275
    new-instance v0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda7;

    invoke-direct {v0, p1, p0}, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda7;-><init>(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;)V

    iput-object v0, p0, Lgroovy/util/ObjectGraphBuilder;->referenceResolver:Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

    goto :goto_0

    .line 281
    :cond_2
    new-instance p1, Lgroovy/util/ObjectGraphBuilder$DefaultReferenceResolver;

    invoke-direct {p1}, Lgroovy/util/ObjectGraphBuilder$DefaultReferenceResolver;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->referenceResolver:Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;

    :goto_0
    return-void
.end method

.method public setRelationNameResolver(Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;)V
    .locals 0

    if-eqz p1, :cond_0

    goto :goto_0

    .line 291
    :cond_0
    new-instance p1, Lgroovy/util/ObjectGraphBuilder$DefaultRelationNameResolver;

    invoke-direct {p1}, Lgroovy/util/ObjectGraphBuilder$DefaultRelationNameResolver;-><init>()V

    :goto_0
    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder;->relationNameResolver:Lgroovy/util/ObjectGraphBuilder$RelationNameResolver;

    return-void
.end method
