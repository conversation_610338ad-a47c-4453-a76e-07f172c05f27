.class public Lgroovy/util/MapEntry;
.super Ljava/lang/Object;
.source "MapEntry.java"

# interfaces
.implements Ljava/util/Map$Entry;


# instance fields
.field private key:Ljava/lang/Object;

.field private value:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0

    .line 33
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 34
    iput-object p1, p0, Lgroovy/util/MapEntry;->key:Ljava/lang/Object;

    .line 35
    iput-object p2, p0, Lgroovy/util/MapEntry;->value:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public equals(Lgroovy/util/MapEntry;)Z
    .locals 2

    .line 46
    iget-object v0, p0, Lgroovy/util/MapEntry;->key:Ljava/lang/Object;

    iget-object v1, p1, Lgroovy/util/MapEntry;->key:Ljava/lang/Object;

    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lgroovy/util/MapEntry;->value:Ljava/lang/Object;

    iget-object p1, p1, Lgroovy/util/MapEntry;->value:Ljava/lang/Object;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 1

    .line 39
    instance-of v0, p1, Lgroovy/util/MapEntry;

    if-eqz v0, :cond_0

    .line 40
    check-cast p1, Lgroovy/util/MapEntry;

    invoke-virtual {p0, p1}, Lgroovy/util/MapEntry;->equals(Lgroovy/util/MapEntry;)Z

    move-result p1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public getKey()Ljava/lang/Object;
    .locals 1

    .line 58
    iget-object v0, p0, Lgroovy/util/MapEntry;->key:Ljava/lang/Object;

    return-object v0
.end method

.method public getValue()Ljava/lang/Object;
    .locals 1

    .line 66
    iget-object v0, p0, Lgroovy/util/MapEntry;->value:Ljava/lang/Object;

    return-object v0
.end method

.method protected hash(Ljava/lang/Object;)I
    .locals 0

    if-nez p1, :cond_0

    const p1, 0xbabe

    goto :goto_0

    .line 78
    :cond_0
    invoke-virtual {p1}, Ljava/lang/Object;->hashCode()I

    move-result p1

    :goto_0
    return p1
.end method

.method public hashCode()I
    .locals 2

    .line 50
    iget-object v0, p0, Lgroovy/util/MapEntry;->key:Ljava/lang/Object;

    invoke-virtual {p0, v0}, Lgroovy/util/MapEntry;->hash(Ljava/lang/Object;)I

    move-result v0

    iget-object v1, p0, Lgroovy/util/MapEntry;->value:Ljava/lang/Object;

    invoke-virtual {p0, v1}, Lgroovy/util/MapEntry;->hash(Ljava/lang/Object;)I

    move-result v1

    xor-int/2addr v0, v1

    return v0
.end method

.method public setKey(Ljava/lang/Object;)V
    .locals 0

    .line 62
    iput-object p1, p0, Lgroovy/util/MapEntry;->key:Ljava/lang/Object;

    return-void
.end method

.method public setValue(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 70
    iput-object p1, p0, Lgroovy/util/MapEntry;->value:Ljava/lang/Object;

    return-object p1
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 54
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, ""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovy/util/MapEntry;->key:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ":"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovy/util/MapEntry;->value:Ljava/lang/Object;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
