.class public final synthetic Lgroovy/util/GroovyScriptEngine$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/security/PrivilegedAction;


# instance fields
.field public final synthetic f$0:Lgroovy/util/GroovyScriptEngine;


# direct methods
.method public synthetic constructor <init>(Lgroovy/util/GroovyScriptEngine;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovy/util/GroovyScriptEngine$$ExternalSyntheticLambda0;->f$0:Lgroovy/util/GroovyScriptEngine;

    return-void
.end method


# virtual methods
.method public final run()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lgroovy/util/GroovyScriptEngine$$ExternalSyntheticLambda0;->f$0:Lgroovy/util/GroovyScriptEngine;

    invoke-virtual {v0}, Lgroovy/util/GroovyScriptEngine;->lambda$initGroovyLoader$1$groovy-util-GroovyScriptEngine()Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;

    move-result-object v0

    return-object v0
.end method
