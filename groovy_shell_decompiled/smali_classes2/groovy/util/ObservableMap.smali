.class public Lgroovy/util/ObservableMap;
.super Ljava/lang/Object;
.source "ObservableMap.java"

# interfaces
.implements Ljava/util/Map;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/util/ObservableMap$PropertyClearedEvent;,
        Lgroovy/util/ObservableMap$PropertyEvent;,
        Lgroovy/util/ObservableMap$PropertyAddedEvent;,
        Lgroovy/util/ObservableMap$PropertyUpdatedEvent;,
        Lgroovy/util/ObservableMap$MultiPropertyEvent;,
        Lgroovy/util/ObservableMap$PropertyRemovedEvent;,
        Lgroovy/util/ObservableMap$ChangeType;
    }
.end annotation


# static fields
.field public static final CLEARED_PROPERTY:Ljava/lang/String; = "cleared"

.field public static final CONTENT_PROPERTY:Ljava/lang/String; = "content"

.field public static final SIZE_PROPERTY:Ljava/lang/String; = "size"


# instance fields
.field private final delegate:Ljava/util/Map;

.field private final pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

.field private final test:Lgroovy/lang/Closure;


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 74
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    const/4 v1, 0x0

    invoke-direct {p0, v0, v1}, Lgroovy/util/ObservableMap;-><init>(Ljava/util/Map;Lgroovy/lang/Closure;)V

    return-void
.end method

.method public constructor <init>(Lgroovy/lang/Closure;)V
    .locals 1

    .line 78
    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    invoke-direct {p0, v0, p1}, Lgroovy/util/ObservableMap;-><init>(Ljava/util/Map;Lgroovy/lang/Closure;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/Map;)V
    .locals 1

    const/4 v0, 0x0

    .line 82
    invoke-direct {p0, p1, v0}, Lgroovy/util/ObservableMap;-><init>(Ljava/util/Map;Lgroovy/lang/Closure;)V

    return-void
.end method

.method public constructor <init>(Ljava/util/Map;Lgroovy/lang/Closure;)V
    .locals 0

    .line 85
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 86
    iput-object p1, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    .line 87
    iput-object p2, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    .line 88
    new-instance p1, Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-direct {p1, p0}, Lgroovyjarjaropenbeans/PropertyChangeSupport;-><init>(Ljava/lang/Object;)V

    iput-object p1, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    return-void
.end method


# virtual methods
.method public addPropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    .line 280
    iget-object v0, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->addPropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    return-void
.end method

.method public addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    .line 284
    iget-object v0, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->addPropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    return-void
.end method

.method public clear()V
    .locals 3

    .line 138
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result v0

    .line 139
    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    .line 140
    iget-object v2, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v2}, Ljava/util/Map;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_0

    .line 141
    iget-object v2, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v1, v2}, Ljava/util/Map;->putAll(Ljava/util/Map;)V

    .line 143
    :cond_0
    iget-object v2, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v2}, Ljava/util/Map;->clear()V

    .line 144
    invoke-virtual {p0, v1}, Lgroovy/util/ObservableMap;->firePropertyClearedEvent(Ljava/util/Map;)V

    .line 145
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result v1

    invoke-virtual {p0, v0, v1}, Lgroovy/util/ObservableMap;->fireSizeChangedEvent(II)V

    return-void
.end method

.method public containsKey(Ljava/lang/Object;)Z
    .locals 1

    .line 149
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public containsValue(Ljava/lang/Object;)Z
    .locals 1

    .line 153
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->containsValue(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public entrySet()Ljava/util/Set;
    .locals 1

    .line 157
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 1

    .line 161
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method protected fireMultiPropertyEvent(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lgroovy/util/ObservableMap$PropertyEvent;",
            ">;)V"
        }
    .end annotation

    .line 116
    new-instance v0, Lgroovy/util/ObservableMap$MultiPropertyEvent;

    const/4 v1, 0x0

    new-array v1, v1, [Lgroovy/util/ObservableMap$PropertyEvent;

    invoke-interface {p1, v1}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Lgroovy/util/ObservableMap$PropertyEvent;

    invoke-direct {v0, p0, p1}, Lgroovy/util/ObservableMap$MultiPropertyEvent;-><init>(Ljava/lang/Object;[Lgroovy/util/ObservableMap$PropertyEvent;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableMap;->firePropertyEvent(Lgroovy/util/ObservableMap$PropertyEvent;)V

    return-void
.end method

.method protected fireMultiPropertyEvent([Lgroovy/util/ObservableMap$PropertyEvent;)V
    .locals 1

    .line 120
    new-instance v0, Lgroovy/util/ObservableMap$MultiPropertyEvent;

    invoke-direct {v0, p0, p1}, Lgroovy/util/ObservableMap$MultiPropertyEvent;-><init>(Ljava/lang/Object;[Lgroovy/util/ObservableMap$PropertyEvent;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableMap;->firePropertyEvent(Lgroovy/util/ObservableMap$PropertyEvent;)V

    return-void
.end method

.method protected firePropertyAddedEvent(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 108
    new-instance v0, Lgroovy/util/ObservableMap$PropertyAddedEvent;

    invoke-static {p1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p0, p1, p2}, Lgroovy/util/ObservableMap$PropertyAddedEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableMap;->firePropertyEvent(Lgroovy/util/ObservableMap$PropertyEvent;)V

    return-void
.end method

.method protected firePropertyClearedEvent(Ljava/util/Map;)V
    .locals 1

    .line 104
    new-instance v0, Lgroovy/util/ObservableMap$PropertyClearedEvent;

    invoke-direct {v0, p0, p1}, Lgroovy/util/ObservableMap$PropertyClearedEvent;-><init>(Ljava/lang/Object;Ljava/util/Map;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableMap;->firePropertyEvent(Lgroovy/util/ObservableMap$PropertyEvent;)V

    return-void
.end method

.method protected firePropertyEvent(Lgroovy/util/ObservableMap$PropertyEvent;)V
    .locals 1

    .line 128
    iget-object v0, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->firePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void
.end method

.method protected firePropertyRemovedEvent(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 124
    new-instance v0, Lgroovy/util/ObservableMap$PropertyRemovedEvent;

    invoke-static {p1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p0, p1, p2}, Lgroovy/util/ObservableMap$PropertyRemovedEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableMap;->firePropertyEvent(Lgroovy/util/ObservableMap$PropertyEvent;)V

    return-void
.end method

.method protected firePropertyUpdatedEvent(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 112
    new-instance v0, Lgroovy/util/ObservableMap$PropertyUpdatedEvent;

    invoke-static {p1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p0, p1, p2, p3}, Lgroovy/util/ObservableMap$PropertyUpdatedEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-virtual {p0, v0}, Lgroovy/util/ObservableMap;->firePropertyEvent(Lgroovy/util/ObservableMap$PropertyEvent;)V

    return-void
.end method

.method protected fireSizeChangedEvent(II)V
    .locals 3

    .line 132
    iget-object v0, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    new-instance v1, Lgroovyjarjaropenbeans/PropertyChangeEvent;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    const-string v2, "size"

    invoke-direct {v1, p0, v2, p1, p2}, Lgroovyjarjaropenbeans/PropertyChangeEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-virtual {v0, v1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->firePropertyChange(Lgroovyjarjaropenbeans/PropertyChangeEvent;)V

    return-void
.end method

.method public get(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 165
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getContent()Ljava/util/Map;
    .locals 1

    .line 100
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method protected getMapDelegate()Ljava/util/Map;
    .locals 1

    .line 92
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    return-object v0
.end method

.method public getPropertyChangeListeners()[Lgroovyjarjaropenbeans/PropertyChangeListener;
    .locals 1

    .line 288
    iget-object v0, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->getPropertyChangeListeners()[Lgroovyjarjaropenbeans/PropertyChangeListener;

    move-result-object v0

    return-object v0
.end method

.method public getPropertyChangeListeners(Ljava/lang/String;)[Lgroovyjarjaropenbeans/PropertyChangeListener;
    .locals 1

    .line 292
    iget-object v0, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->getPropertyChangeListeners(Ljava/lang/String;)[Lgroovyjarjaropenbeans/PropertyChangeListener;

    move-result-object p1

    return-object p1
.end method

.method public getSize()I
    .locals 1

    .line 270
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result v0

    return v0
.end method

.method protected getTest()Lgroovy/lang/Closure;
    .locals 1

    .line 96
    iget-object v0, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    return-object v0
.end method

.method public hasListeners(Ljava/lang/String;)Z
    .locals 1

    .line 304
    iget-object v0, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->hasListeners(Ljava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public hashCode()I
    .locals 1

    .line 169
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->hashCode()I

    move-result v0

    return v0
.end method

.method public isEmpty()Z
    .locals 1

    .line 173
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    move-result v0

    return v0
.end method

.method public keySet()Ljava/util/Set;
    .locals 1

    .line 177
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v0

    return-object v0
.end method

.method public put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 181
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result v0

    .line 183
    iget-object v1, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v1, p1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x1

    xor-int/2addr v1, v2

    .line 184
    iget-object v3, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    if-eqz v3, :cond_2

    .line 185
    iget-object v3, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v3, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    .line 187
    iget-object v4, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    invoke-virtual {v4}, Lgroovy/lang/Closure;->getMaximumNumberOfParameters()I

    move-result v4

    const/4 v5, 0x2

    if-ne v4, v5, :cond_0

    .line 188
    iget-object v4, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    new-array v5, v5, [Ljava/lang/Object;

    const/4 v6, 0x0

    aput-object p1, v5, v6

    aput-object p2, v5, v2

    invoke-virtual {v4, v5}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    goto :goto_0

    .line 190
    :cond_0
    iget-object v2, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    invoke-virtual {v2, p2}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    .line 192
    :goto_0
    instance-of v4, v2, Ljava/lang/Boolean;

    if-eqz v4, :cond_4

    check-cast v2, Ljava/lang/Boolean;

    invoke-virtual {v2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v2

    if-eqz v2, :cond_4

    if-eqz v1, :cond_1

    .line 194
    invoke-virtual {p0, p1, p2}, Lgroovy/util/ObservableMap;->firePropertyAddedEvent(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 195
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Lgroovy/util/ObservableMap;->fireSizeChangedEvent(II)V

    goto :goto_1

    :cond_1
    if-eq v3, p2, :cond_4

    .line 197
    invoke-virtual {p0, p1, v3, p2}, Lgroovy/util/ObservableMap;->firePropertyUpdatedEvent(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V

    goto :goto_1

    .line 201
    :cond_2
    iget-object v2, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v2, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    if-eqz v1, :cond_3

    .line 203
    invoke-virtual {p0, p1, p2}, Lgroovy/util/ObservableMap;->firePropertyAddedEvent(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 204
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Lgroovy/util/ObservableMap;->fireSizeChangedEvent(II)V

    goto :goto_1

    :cond_3
    if-eq v3, p2, :cond_4

    .line 206
    invoke-virtual {p0, p1, v3, p2}, Lgroovy/util/ObservableMap;->firePropertyUpdatedEvent(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V

    :cond_4
    :goto_1
    return-object v3
.end method

.method public putAll(Ljava/util/Map;)V
    .locals 10

    .line 213
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result v0

    if-eqz p1, :cond_6

    .line 215
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 216
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_5

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 217
    check-cast v2, Ljava/util/Map$Entry;

    .line 219
    invoke-interface {v2}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v3

    invoke-static {v3}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    .line 220
    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    .line 223
    iget-object v4, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v4, v3}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v4

    const/4 v5, 0x1

    xor-int/2addr v4, v5

    .line 224
    iget-object v6, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    if-eqz v6, :cond_3

    .line 225
    iget-object v6, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v6, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    .line 227
    iget-object v7, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    invoke-virtual {v7}, Lgroovy/lang/Closure;->getMaximumNumberOfParameters()I

    move-result v7

    const/4 v8, 0x2

    if-ne v7, v8, :cond_1

    .line 228
    iget-object v7, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    new-array v8, v8, [Ljava/lang/Object;

    const/4 v9, 0x0

    aput-object v3, v8, v9

    aput-object v2, v8, v5

    invoke-virtual {v7, v8}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    goto :goto_1

    .line 230
    :cond_1
    iget-object v5, p0, Lgroovy/util/ObservableMap;->test:Lgroovy/lang/Closure;

    invoke-virtual {v5, v2}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    .line 232
    :goto_1
    instance-of v7, v5, Ljava/lang/Boolean;

    if-eqz v7, :cond_0

    check-cast v5, Ljava/lang/Boolean;

    invoke-virtual {v5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v5

    if-eqz v5, :cond_0

    if-eqz v4, :cond_2

    .line 234
    new-instance v4, Lgroovy/util/ObservableMap$PropertyAddedEvent;

    invoke-direct {v4, p0, v3, v2}, Lgroovy/util/ObservableMap$PropertyAddedEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    invoke-interface {v1, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_2
    if-eq v6, v2, :cond_0

    .line 236
    new-instance v4, Lgroovy/util/ObservableMap$PropertyUpdatedEvent;

    invoke-direct {v4, p0, v3, v6, v2}, Lgroovy/util/ObservableMap$PropertyUpdatedEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v1, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 240
    :cond_3
    iget-object v5, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v5, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    if-eqz v4, :cond_4

    .line 242
    new-instance v4, Lgroovy/util/ObservableMap$PropertyAddedEvent;

    invoke-direct {v4, p0, v3, v2}, Lgroovy/util/ObservableMap$PropertyAddedEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    invoke-interface {v1, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_4
    if-eq v5, v2, :cond_0

    .line 244
    new-instance v4, Lgroovy/util/ObservableMap$PropertyUpdatedEvent;

    invoke-direct {v4, p0, v3, v5, v2}, Lgroovy/util/ObservableMap$PropertyUpdatedEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    invoke-interface {v1, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    .line 248
    :cond_5
    invoke-interface {v1}, Ljava/util/List;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_6

    .line 249
    invoke-virtual {p0, v1}, Lgroovy/util/ObservableMap;->fireMultiPropertyEvent(Ljava/util/List;)V

    .line 250
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Lgroovy/util/ObservableMap;->fireSizeChangedEvent(II)V

    :cond_6
    return-void
.end method

.method public remove(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 256
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result v0

    .line 257
    iget-object v1, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v1, p1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    if-eqz p1, :cond_0

    .line 259
    invoke-virtual {p0, p1, v1}, Lgroovy/util/ObservableMap;->firePropertyRemovedEvent(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 260
    invoke-virtual {p0}, Lgroovy/util/ObservableMap;->size()I

    move-result p1

    invoke-virtual {p0, v0, p1}, Lgroovy/util/ObservableMap;->fireSizeChangedEvent(II)V

    :cond_0
    return-object v1
.end method

.method public removePropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    .line 296
    iget-object v0, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->removePropertyChangeListener(Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    return-void
.end method

.method public removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V
    .locals 1

    .line 300
    iget-object v0, p0, Lgroovy/util/ObservableMap;->pcs:Lgroovyjarjaropenbeans/PropertyChangeSupport;

    invoke-virtual {v0, p1, p2}, Lgroovyjarjaropenbeans/PropertyChangeSupport;->removePropertyChangeListener(Ljava/lang/String;Lgroovyjarjaropenbeans/PropertyChangeListener;)V

    return-void
.end method

.method public size()I
    .locals 1

    .line 266
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    return v0
.end method

.method public values()Ljava/util/Collection;
    .locals 1

    .line 274
    iget-object v0, p0, Lgroovy/util/ObservableMap;->delegate:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method
