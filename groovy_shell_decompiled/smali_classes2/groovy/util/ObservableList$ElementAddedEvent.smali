.class public Lgroovy/util/ObservableList$ElementAddedEvent;
.super Lgroovy/util/ObservableList$ElementEvent;
.source "ObservableList.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableList;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "ElementAddedEvent"
.end annotation


# static fields
.field private static final serialVersionUID:J = -0x5b859aaf18974554L


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;I)V
    .locals 6

    .line 510
    sget-object v5, Lgroovy/util/ObservableList$ChangeType;->ADDED:Lgroovy/util/ObservableList$ChangeType;

    const/4 v2, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v3, p2

    move v4, p3

    invoke-direct/range {v0 .. v5}, Lgroovy/util/ObservableList$ElementEvent;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;ILgroovy/util/ObservableList$ChangeType;)V

    return-void
.end method
