.class public Lgroovy/util/ObservableSet$MultiElementAddedEvent;
.super Lgroovy/util/ObservableSet$ElementEvent;
.source "ObservableSet.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableSet;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "MultiElementAddedEvent"
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x7fb89d41972d120L


# instance fields
.field private values:Ljava/util/List;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/util/List;)V
    .locals 3

    .line 406
    sget-object v0, Lgroovy/util/ObservableSet$ChangeType;->oldValue:Ljava/lang/Object;

    sget-object v1, Lgroovy/util/ObservableSet$ChangeType;->newValue:Ljava/lang/Object;

    sget-object v2, Lgroovy/util/ObservableSet$ChangeType;->MULTI_ADD:Lgroovy/util/ObservableSet$ChangeType;

    invoke-direct {p0, p1, v0, v1, v2}, Lgroovy/util/ObservableSet$ElementEvent;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Lgroovy/util/ObservableSet$ChangeType;)V

    .line 403
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObservableSet$MultiElementAddedEvent;->values:Ljava/util/List;

    if-eqz p2, :cond_0

    .line 408
    invoke-interface {p1, p2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    return-void
.end method


# virtual methods
.method public getValues()Ljava/util/List;
    .locals 1

    .line 413
    iget-object v0, p0, Lgroovy/util/ObservableSet$MultiElementAddedEvent;->values:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
