.class public final synthetic Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;


# instance fields
.field public final synthetic f$0:Lorg/codehaus/groovy/tools/gse/StringSetMap;

.field public final synthetic f$1:Ljava/util/Map;


# direct methods
.method public synthetic constructor <init>(Lorg/codehaus/groovy/tools/gse/StringSetMap;Ljava/util/Map;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$$ExternalSyntheticLambda1;->f$0:Lorg/codehaus/groovy/tools/gse/StringSetMap;

    iput-object p2, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$$ExternalSyntheticLambda1;->f$1:Ljava/util/Map;

    return-void
.end method


# virtual methods
.method public final call(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 2

    iget-object v0, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$$ExternalSyntheticLambda1;->f$0:Lorg/codehaus/groovy/tools/gse/StringSetMap;

    iget-object v1, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$$ExternalSyntheticLambda1;->f$1:Ljava/util/Map;

    invoke-static {v0, v1, p1, p2, p3}, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;->lambda$createCompilationUnit$1(Lorg/codehaus/groovy/tools/gse/StringSetMap;Ljava/util/Map;Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V

    return-void
.end method
