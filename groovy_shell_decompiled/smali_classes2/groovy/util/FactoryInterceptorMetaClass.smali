.class Lgroovy/util/FactoryInterceptorMetaClass;
.super Lgroovy/lang/DelegatingMetaClass;
.source "FactoryBuilderSupport.java"


# instance fields
.field builder:Lgroovy/util/FactoryBuilderSupport;


# direct methods
.method public constructor <init>(Lgroovy/lang/MetaClass;Lgroovy/util/FactoryBuilderSupport;)V
    .locals 0

    .line 1296
    invoke-direct {p0, p1}, Lgroovy/lang/DelegatingMetaClass;-><init>(Lgroovy/lang/MetaClass;)V

    .line 1297
    iput-object p2, p0, Lgroovy/util/FactoryInterceptorMetaClass;->builder:Lgroovy/util/FactoryBuilderSupport;

    return-void
.end method


# virtual methods
.method public invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1302
    :try_start_0
    iget-object v0, p0, Lgroovy/util/FactoryInterceptorMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lgroovy/lang/MissingMethodException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 1306
    :try_start_1
    iget-object v0, p0, Lgroovy/util/FactoryInterceptorMetaClass;->builder:Lgroovy/util/FactoryBuilderSupport;

    invoke-virtual {v0}, Lgroovy/util/FactoryBuilderSupport;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iget-object v1, p0, Lgroovy/util/FactoryInterceptorMetaClass;->builder:Lgroovy/util/FactoryBuilderSupport;

    invoke-interface {v0, v1, p2}, Lgroovy/lang/MetaClass;->respondsTo(Ljava/lang/Object;Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1308
    iget-object v0, p0, Lgroovy/util/FactoryInterceptorMetaClass;->builder:Lgroovy/util/FactoryBuilderSupport;

    invoke-virtual {v0, p2, p3}, Lgroovy/util/FactoryBuilderSupport;->invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 1310
    :cond_0
    iget-object v0, p0, Lgroovy/util/FactoryInterceptorMetaClass;->builder:Lgroovy/util/FactoryBuilderSupport;

    invoke-static {v0, p2, p3}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_1
    .catch Lgroovy/lang/MissingMethodException; {:try_start_1 .. :try_end_1} :catch_1

    return-object p1

    :catch_1
    move-exception p2

    move-object p3, p1

    .line 1315
    :goto_0
    invoke-virtual {p3}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 1316
    invoke-virtual {p3}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object p3

    goto :goto_0

    .line 1318
    :cond_1
    invoke-virtual {p3, p2}, Ljava/lang/Throwable;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    .line 1320
    throw p1
.end method

.method public invokeMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1327
    :try_start_0
    iget-object v0, p0, Lgroovy/util/FactoryInterceptorMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lgroovy/lang/MissingMethodException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 1331
    :try_start_1
    iget-object v0, p0, Lgroovy/util/FactoryInterceptorMetaClass;->builder:Lgroovy/util/FactoryBuilderSupport;

    invoke-virtual {v0}, Lgroovy/util/FactoryBuilderSupport;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iget-object v1, p0, Lgroovy/util/FactoryInterceptorMetaClass;->builder:Lgroovy/util/FactoryBuilderSupport;

    invoke-interface {v0, v1, p2}, Lgroovy/lang/MetaClass;->respondsTo(Ljava/lang/Object;Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1333
    iget-object v0, p0, Lgroovy/util/FactoryInterceptorMetaClass;->builder:Lgroovy/util/FactoryBuilderSupport;

    invoke-virtual {v0, p2, p3}, Lgroovy/util/FactoryBuilderSupport;->invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 1335
    :cond_0
    iget-object v0, p0, Lgroovy/util/FactoryInterceptorMetaClass;->builder:Lgroovy/util/FactoryBuilderSupport;

    invoke-static {v0, p2, p3}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_1
    .catch Lgroovy/lang/MissingMethodException; {:try_start_1 .. :try_end_1} :catch_1

    return-object p1

    :catch_1
    move-exception p2

    move-object p3, p1

    .line 1340
    :goto_0
    invoke-virtual {p3}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 1341
    invoke-virtual {p3}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object p3

    goto :goto_0

    .line 1343
    :cond_1
    invoke-virtual {p3, p2}, Ljava/lang/Throwable;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    .line 1345
    throw p1
.end method
