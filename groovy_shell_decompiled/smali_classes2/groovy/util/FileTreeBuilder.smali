.class public Lgroovy/util/FileTreeBuilder;
.super Ljava/lang/Object;
.source "FileTreeBuilder.groovy"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# static fields
.field private static synthetic $callSiteArray:Ljava/lang/ref/SoftReference;

.field private static synthetic $staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

.field private static synthetic $staticClassInfo$:Lorg/codehaus/groovy/reflection/ClassInfo;

.field public static transient synthetic __$stMC:Z


# instance fields
.field private baseDir:Ljava/io/File;

.field private transient synthetic metaClass:Lgroovy/lang/MetaClass;


# direct methods
.method private static synthetic $createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/String;

    invoke-static {v0}, Lgroovy/util/FileTreeBuilder;->$createCallSiteArray_1([Ljava/lang/String;)V

    new-instance v1, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    const-class v2, Lgroovy/util/FileTreeBuilder;

    invoke-direct {v1, v2, v0}, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;-><init>(Ljava/lang/Class;[Ljava/lang/String;)V

    return-object v1
.end method

.method private static synthetic $createCallSiteArray_1([Ljava/lang/String;)V
    .locals 2

    const/4 v0, 0x0

    const-string v1, "<$constructor$>"

    aput-object v1, p0, v0

    return-void
.end method

.method private static synthetic $getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 2

    sget-object v0, Lgroovy/util/FileTreeBuilder;->$callSiteArray:Ljava/lang/ref/SoftReference;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/ref/SoftReference;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    if-nez v0, :cond_1

    :cond_0
    invoke-static {}, Lgroovy/util/FileTreeBuilder;->$createCallSiteArray()Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;

    move-result-object v0

    new-instance v1, Ljava/lang/ref/SoftReference;

    invoke-direct {v1, v0}, Ljava/lang/ref/SoftReference;-><init>(Ljava/lang/Object;)V

    sput-object v1, Lgroovy/util/FileTreeBuilder;->$callSiteArray:Ljava/lang/ref/SoftReference;

    :cond_1
    iget-object v0, v0, Lorg/codehaus/groovy/runtime/callsite/CallSiteArray;->array:[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    return-object v0
.end method

.method public constructor <init>()V
    .locals 3
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    invoke-static {}, Lgroovy/util/FileTreeBuilder;->$getCallSiteArray()[Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object v0

    const/4 v1, 0x0

    .line 78
    aget-object v0, v0, v1

    const-class v1, Ljava/io/File;

    const-string v2, "."

    invoke-interface {v0, v1, v2}, Lorg/codehaus/groovy/runtime/callsite/CallSite;->callConstructor(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/io/File;

    invoke-direct {p0, v0}, Lgroovy/util/FileTreeBuilder;-><init>(Ljava/io/File;)V

    return-void
.end method

.method public constructor <init>(Ljava/io/File;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0}, Lgroovy/util/FileTreeBuilder;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/util/FileTreeBuilder;->metaClass:Lgroovy/lang/MetaClass;

    .line 79
    iput-object p1, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    return-void
.end method


# virtual methods
.method protected synthetic $getStaticMetaClass()Lgroovy/lang/MetaClass;
    .locals 2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    const-class v1, Lgroovy/util/FileTreeBuilder;

    if-eq v0, v1, :cond_0

    invoke-static {p0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->initMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0

    :cond_0
    sget-object v0, Lgroovy/util/FileTreeBuilder;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    if-nez v0, :cond_1

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getClassInfo(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/ClassInfo;

    move-result-object v0

    sput-object v0, Lgroovy/util/FileTreeBuilder;->$staticClassInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    :cond_1
    invoke-virtual {v0}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public call(Lgroovy/lang/Closure;)Ljava/io/File;
    .locals 1
    .param p1    # Lgroovy/lang/Closure;
        .annotation runtime Lgroovy/lang/DelegatesTo;
            strategy = 0x1
            value = Lgroovy/util/FileTreeBuilder;
        .end annotation
    .end param

    .line 161
    invoke-virtual {p1}, Lgroovy/lang/Closure;->clone()Ljava/lang/Object;

    move-result-object p1

    const-class v0, Lgroovy/lang/Closure;

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/Closure;

    .line 162
    invoke-virtual {p1, p0}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    .line 163
    sget v0, Lgroovy/lang/Closure;->DELEGATE_FIRST:I

    invoke-virtual {p1, v0}, Lgroovy/lang/Closure;->setResolveStrategy(I)V

    .line 164
    invoke-virtual {p1}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;

    .line 165
    iget-object p1, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    return-object p1
.end method

.method public dir(Ljava/lang/String;)Ljava/io/File;
    .locals 2

    .line 135
    new-instance v0, Ljava/io/File;

    iget-object v1, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    invoke-direct {v0, v1, p1}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 136
    invoke-virtual {v0}, Ljava/io/File;->mkdirs()Z

    return-object v0
.end method

.method public dir(Ljava/lang/String;Lgroovy/lang/Closure;)Ljava/io/File;
    .locals 2
    .param p2    # Lgroovy/lang/Closure;
        .annotation runtime Lgroovy/lang/DelegatesTo;
            strategy = 0x1
            value = Lgroovy/util/FileTreeBuilder;
        .end annotation
    .end param

    .line 147
    iget-object v0, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    .line 148
    invoke-virtual {p0, p1}, Lgroovy/util/FileTreeBuilder;->dir(Ljava/lang/String;)Ljava/io/File;

    move-result-object p1

    .line 150
    :try_start_0
    iput-object p1, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    .line 151
    invoke-virtual {p2, p0}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    .line 152
    sget v1, Lgroovy/lang/Closure;->DELEGATE_FIRST:I

    invoke-virtual {p2, v1}, Lgroovy/lang/Closure;->setResolveStrategy(I)V

    .line 153
    invoke-virtual {p2}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 155
    iput-object v0, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    return-object p1

    :catchall_0
    move-exception p1

    iput-object v0, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    .line 156
    throw p1
.end method

.method public file(Ljava/lang/String;Lgroovy/lang/Closure;)Ljava/io/File;
    .locals 2
    .param p2    # Lgroovy/lang/Closure;
        .annotation runtime Lgroovy/lang/DelegatesTo;
            strategy = 0x1
            value = Ljava/io/File;
        .end annotation
    .end param

    .line 121
    new-instance v0, Ljava/io/File;

    iget-object v1, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    invoke-direct {v0, v1, p1}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 122
    invoke-virtual {p2}, Lgroovy/lang/Closure;->clone()Ljava/lang/Object;

    move-result-object p1

    const-class p2, Lgroovy/lang/Closure;

    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/Closure;

    .line 123
    invoke-virtual {p1, v0}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    .line 124
    sget p2, Lgroovy/lang/Closure;->DELEGATE_FIRST:I

    invoke-virtual {p1, p2}, Lgroovy/lang/Closure;->setResolveStrategy(I)V

    .line 125
    invoke-virtual {p1, v0}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    return-object v0
.end method

.method public file(Ljava/lang/String;Ljava/io/File;)Ljava/io/File;
    .locals 0

    .line 110
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/ResourceGroovyMethods;->getBytes(Ljava/io/File;)[B

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lgroovy/util/FileTreeBuilder;->file(Ljava/lang/String;[B)Ljava/io/File;

    move-result-object p1

    return-object p1
.end method

.method public file(Ljava/lang/String;Ljava/lang/CharSequence;)Ljava/io/File;
    .locals 2

    .line 89
    new-instance v0, Ljava/io/File;

    iget-object v1, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    invoke-direct {v0, v1, p1}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    invoke-static {v0, p2}, Lorg/codehaus/groovy/runtime/ResourceGroovyMethods;->leftShift(Ljava/io/File;Ljava/lang/Object;)Ljava/io/File;

    move-result-object p1

    return-object p1
.end method

.method public varargs file(Ljava/lang/String;[B)Ljava/io/File;
    .locals 2

    .line 99
    new-instance v0, Ljava/io/File;

    iget-object v1, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    invoke-direct {v0, v1, p1}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    invoke-static {v0, p2}, Lorg/codehaus/groovy/runtime/ResourceGroovyMethods;->leftShift(Ljava/io/File;[B)Ljava/io/File;

    move-result-object p1

    return-object p1
.end method

.method public getBaseDir()Ljava/io/File;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iget-object v0, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    .annotation runtime Lgroovyjarjaropenbeans/Transient;
    .end annotation

    iget-object v0, p0, Lgroovy/util/FileTreeBuilder;->metaClass:Lgroovy/lang/MetaClass;

    if-eqz v0, :cond_0

    return-object v0

    :cond_0
    invoke-virtual {p0}, Lgroovy/util/FileTreeBuilder;->$getStaticMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    iput-object v0, p0, Lgroovy/util/FileTreeBuilder;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public methodMissing(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 170
    instance-of v0, p2, [Ljava/lang/Object;

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_1

    const-class v0, [Ljava/lang/Object;

    invoke-static {p2, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/lang/Object;

    array-length v0, v0

    if-ne v0, v1, :cond_0

    move v0, v1

    goto :goto_0

    :cond_0
    move v0, v2

    :goto_0
    if-eqz v0, :cond_1

    goto :goto_1

    :cond_1
    move v1, v2

    :goto_1
    const/4 v0, 0x0

    if-eqz v1, :cond_5

    .line 171
    const-class v1, [Ljava/lang/Object;

    invoke-static {p2, v1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [Ljava/lang/Object;

    invoke-static {p2, v2}, Lorg/codehaus/groovy/runtime/BytecodeInterface8;->objectArrayGet([Ljava/lang/Object;I)Ljava/lang/Object;

    move-result-object p2

    .line 172
    instance-of v1, p2, Lgroovy/lang/Closure;

    if-eqz v1, :cond_2

    .line 173
    const-class v0, Lgroovy/lang/Closure;

    invoke-static {p2, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovy/lang/Closure;

    invoke-virtual {p0, p1, p2}, Lgroovy/util/FileTreeBuilder;->dir(Ljava/lang/String;Lgroovy/lang/Closure;)Ljava/io/File;

    move-result-object p1

    return-object p1

    .line 174
    :cond_2
    instance-of v1, p2, Ljava/lang/CharSequence;

    if-eqz v1, :cond_3

    .line 175
    check-cast p2, Ljava/lang/CharSequence;

    invoke-interface {p2}, Ljava/lang/CharSequence;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, p2}, Lgroovy/util/FileTreeBuilder;->file(Ljava/lang/String;Ljava/lang/CharSequence;)Ljava/io/File;

    move-result-object p1

    return-object p1

    .line 176
    :cond_3
    instance-of v1, p2, [B

    if-eqz v1, :cond_4

    .line 177
    const-class v0, [B

    invoke-static {p2, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, [B

    invoke-virtual {p0, p1, p2}, Lgroovy/util/FileTreeBuilder;->file(Ljava/lang/String;[B)Ljava/io/File;

    move-result-object p1

    return-object p1

    .line 178
    :cond_4
    instance-of v1, p2, Ljava/io/File;

    if-eqz v1, :cond_5

    .line 179
    const-class v0, Ljava/io/File;

    invoke-static {p2, v0}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->castToType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/io/File;

    invoke-virtual {p0, p1, p2}, Lgroovy/util/FileTreeBuilder;->file(Ljava/lang/String;Ljava/io/File;)Ljava/io/File;

    move-result-object p1

    return-object p1

    :cond_5
    return-object v0
.end method

.method public setBaseDir(Ljava/io/File;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    iput-object p1, p0, Lgroovy/util/FileTreeBuilder;->baseDir:Ljava/io/File;

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0
    .annotation runtime Lgroovy/transform/Generated;
    .end annotation

    .annotation runtime Lgroovy/transform/Internal;
    .end annotation

    iput-object p1, p0, Lgroovy/util/FileTreeBuilder;->metaClass:Lgroovy/lang/MetaClass;

    return-void
.end method
