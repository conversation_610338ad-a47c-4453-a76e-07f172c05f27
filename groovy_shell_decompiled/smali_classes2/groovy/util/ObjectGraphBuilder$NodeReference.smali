.class final Lgroovy/util/ObjectGraphBuilder$NodeReference;
.super Ljava/lang/Object;
.source "ObjectGraphBuilder.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObjectGraphBuilder;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "NodeReference"
.end annotation


# instance fields
.field private final childName:Ljava/lang/String;

.field private final parent:Ljava/lang/Object;

.field private final parentName:Ljava/lang/String;

.field private final refId:Ljava/lang/String;


# direct methods
.method private constructor <init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 818
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 819
    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->parent:Lja<PERSON>/lang/Object;

    .line 820
    iput-object p2, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->parentName:Ljava/lang/String;

    .line 821
    iput-object p3, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->childName:Ljava/lang/String;

    .line 822
    iput-object p4, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->refId:Ljava/lang/String;

    return-void
.end method

.method synthetic constructor <init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lgroovy/util/ObjectGraphBuilder$1;)V
    .locals 0

    .line 812
    invoke-direct {p0, p1, p2, p3, p4}, Lgroovy/util/ObjectGraphBuilder$NodeReference;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method static synthetic access$600(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/Object;
    .locals 0

    .line 812
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->parent:Ljava/lang/Object;

    return-object p0
.end method

.method static synthetic access$700(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;
    .locals 0

    .line 812
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->refId:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$800(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;
    .locals 0

    .line 812
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->parentName:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$900(Lgroovy/util/ObjectGraphBuilder$NodeReference;)Ljava/lang/String;
    .locals 0

    .line 812
    iget-object p0, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->childName:Ljava/lang/String;

    return-object p0
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 2

    .line 826
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "[parentName="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->parentName:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", childName="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->childName:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", refId="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovy/util/ObjectGraphBuilder$NodeReference;->refId:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
