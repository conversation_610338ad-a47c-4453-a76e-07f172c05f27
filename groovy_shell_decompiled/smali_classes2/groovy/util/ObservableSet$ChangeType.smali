.class public final enum Lgroovy/util/ObservableSet$ChangeType;
.super Ljava/lang/Enum;
.source "ObservableSet.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableSet;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "ChangeType"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lgroovy/util/ObservableSet$ChangeType;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lgroovy/util/ObservableSet$ChangeType;

.field public static final enum ADDED:Lgroovy/util/ObservableSet$ChangeType;

.field public static final enum CLEARED:Lgroovy/util/ObservableSet$ChangeType;

.field public static final enum MULTI_ADD:Lgroovy/util/ObservableSet$ChangeType;

.field public static final enum MULTI_REMOVE:Lgroovy/util/ObservableSet$ChangeType;

.field public static final enum NONE:Lgroovy/util/ObservableSet$ChangeType;

.field public static final enum REMOVED:Lgroovy/util/ObservableSet$ChangeType;

.field public static final newValue:Ljava/lang/Object;

.field public static final oldValue:Ljava/lang/Object;


# direct methods
.method private static synthetic $values()[Lgroovy/util/ObservableSet$ChangeType;
    .locals 3

    const/4 v0, 0x6

    new-array v0, v0, [Lgroovy/util/ObservableSet$ChangeType;

    .line 340
    sget-object v1, Lgroovy/util/ObservableSet$ChangeType;->ADDED:Lgroovy/util/ObservableSet$ChangeType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableSet$ChangeType;->REMOVED:Lgroovy/util/ObservableSet$ChangeType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableSet$ChangeType;->CLEARED:Lgroovy/util/ObservableSet$ChangeType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableSet$ChangeType;->MULTI_ADD:Lgroovy/util/ObservableSet$ChangeType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableSet$ChangeType;->MULTI_REMOVE:Lgroovy/util/ObservableSet$ChangeType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lgroovy/util/ObservableSet$ChangeType;->NONE:Lgroovy/util/ObservableSet$ChangeType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 4

    .line 341
    new-instance v0, Lgroovy/util/ObservableSet$ChangeType;

    const-string v1, "ADDED"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lgroovy/util/ObservableSet$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableSet$ChangeType;->ADDED:Lgroovy/util/ObservableSet$ChangeType;

    new-instance v0, Lgroovy/util/ObservableSet$ChangeType;

    const-string v1, "REMOVED"

    const/4 v3, 0x1

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableSet$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableSet$ChangeType;->REMOVED:Lgroovy/util/ObservableSet$ChangeType;

    new-instance v0, Lgroovy/util/ObservableSet$ChangeType;

    const-string v1, "CLEARED"

    const/4 v3, 0x2

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableSet$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableSet$ChangeType;->CLEARED:Lgroovy/util/ObservableSet$ChangeType;

    new-instance v0, Lgroovy/util/ObservableSet$ChangeType;

    const-string v1, "MULTI_ADD"

    const/4 v3, 0x3

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableSet$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableSet$ChangeType;->MULTI_ADD:Lgroovy/util/ObservableSet$ChangeType;

    new-instance v0, Lgroovy/util/ObservableSet$ChangeType;

    const-string v1, "MULTI_REMOVE"

    const/4 v3, 0x4

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableSet$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableSet$ChangeType;->MULTI_REMOVE:Lgroovy/util/ObservableSet$ChangeType;

    new-instance v0, Lgroovy/util/ObservableSet$ChangeType;

    const-string v1, "NONE"

    const/4 v3, 0x5

    invoke-direct {v0, v1, v3}, Lgroovy/util/ObservableSet$ChangeType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lgroovy/util/ObservableSet$ChangeType;->NONE:Lgroovy/util/ObservableSet$ChangeType;

    .line 340
    invoke-static {}, Lgroovy/util/ObservableSet$ChangeType;->$values()[Lgroovy/util/ObservableSet$ChangeType;

    move-result-object v0

    sput-object v0, Lgroovy/util/ObservableSet$ChangeType;->$VALUES:[Lgroovy/util/ObservableSet$ChangeType;

    new-array v0, v2, [Ljava/lang/Object;

    .line 343
    sput-object v0, Lgroovy/util/ObservableSet$ChangeType;->oldValue:Ljava/lang/Object;

    new-array v0, v2, [Ljava/lang/Object;

    .line 344
    sput-object v0, Lgroovy/util/ObservableSet$ChangeType;->newValue:Ljava/lang/Object;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 340
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lgroovy/util/ObservableSet$ChangeType;
    .locals 1

    .line 340
    const-class v0, Lgroovy/util/ObservableSet$ChangeType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lgroovy/util/ObservableSet$ChangeType;

    return-object p0
.end method

.method public static values()[Lgroovy/util/ObservableSet$ChangeType;
    .locals 1

    .line 340
    sget-object v0, Lgroovy/util/ObservableSet$ChangeType;->$VALUES:[Lgroovy/util/ObservableSet$ChangeType;

    invoke-virtual {v0}, [Lgroovy/util/ObservableSet$ChangeType;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lgroovy/util/ObservableSet$ChangeType;

    return-object v0
.end method
