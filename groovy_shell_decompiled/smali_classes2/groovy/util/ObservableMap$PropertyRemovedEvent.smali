.class public Lgroovy/util/ObservableMap$PropertyRemovedEvent;
.super Lgroovy/util/ObservableMap$PropertyEvent;
.source "ObservableMap.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObservableMap;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "PropertyRemovedEvent"
.end annotation


# static fields
.field private static final serialVersionUID:J = 0x1a208a40f654c706L


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 6

    .line 374
    sget-object v5, Lgroovy/util/ObservableMap$ChangeType;->REMOVED:Lgroovy/util/ObservableMap$ChangeType;

    const/4 v4, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    invoke-direct/range {v0 .. v5}, Lgroovy/util/ObservableMap$PropertyEvent;-><init>(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lgroovy/util/ObservableMap$ChangeType;)V

    return-void
.end method
