.class public Lgroovy/util/ObjectGraphBuilder$DefaultReferenceResolver;
.super Ljava/lang/Object;
.source "ObjectGraphBuilder.java"

# interfaces
.implements Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/util/ObjectGraphBuilder;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "DefaultReferenceResolver"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 454
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getReferenceFor(Ljava/lang/String;)Ljava/lang/String;
    .locals 0

    const-string p1, "refId"

    return-object p1
.end method
