.class Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$1;
.super Lorg/codehaus/groovy/control/ClassNodeResolver;
.source "GroovyScriptEngine.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;->createCompilationUnit(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;)Lorg/codehaus/groovy/control/CompilationUnit;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$1:Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;

.field final synthetic val$precompiledEntries:Ljava/util/Map;


# direct methods
.method constructor <init>(Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;Ljava/util/Map;)V
    .locals 0

    .line 177
    iput-object p1, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$1;->this$1:Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;

    iput-object p2, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$1;->val$precompiledEntries:Ljava/util/Map;

    invoke-direct {p0}, Lorg/codehaus/groovy/control/ClassNodeResolver;-><init>()V

    return-void
.end method


# virtual methods
.method public findClassNode(Ljava/lang/String;Lorg/codehaus/groovy/control/CompilationUnit;)Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;
    .locals 9

    .line 180
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/CompilationUnit;->getConfiguration()Lorg/codehaus/groovy/control/CompilerConfiguration;

    move-result-object v0

    const/16 v1, 0x2e

    const/16 v2, 0x2f

    .line 181
    invoke-virtual {p1, v1, v2}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object v1

    .line 182
    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getScriptExtensions()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :catch_0
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    .line 184
    :try_start_0
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, "."

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 185
    iget-object v3, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$1;->this$1:Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;

    iget-object v3, v3, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;->this$0:Lgroovy/util/GroovyScriptEngine;

    invoke-static {v3}, Lgroovy/util/GroovyScriptEngine;->access$100(Lgroovy/util/GroovyScriptEngine;)Lgroovy/util/ResourceConnector;

    move-result-object v3

    invoke-interface {v3, v2}, Lgroovy/util/ResourceConnector;->getResourceConnection(Ljava/lang/String;)Ljava/net/URLConnection;

    move-result-object v2

    .line 186
    invoke-virtual {v2}, Ljava/net/URLConnection;->getURL()Ljava/net/URL;

    move-result-object v3

    .line 187
    invoke-virtual {v3}, Ljava/net/URL;->toExternalForm()Ljava/lang/String;

    move-result-object v4

    .line 188
    iget-object v5, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$1;->this$1:Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;

    iget-object v5, v5, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;->this$0:Lgroovy/util/GroovyScriptEngine;

    invoke-static {v5}, Lgroovy/util/GroovyScriptEngine;->access$200(Lgroovy/util/GroovyScriptEngine;)Ljava/util/Map;

    move-result-object v5

    invoke-interface {v5, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lgroovy/util/GroovyScriptEngine$ScriptCacheEntry;

    const/4 v6, 0x0

    if-eqz v5, :cond_1

    .line 190
    invoke-static {v5}, Lgroovy/util/GroovyScriptEngine$ScriptCacheEntry;->access$300(Lgroovy/util/GroovyScriptEngine$ScriptCacheEntry;)Ljava/lang/Class;

    move-result-object v7

    goto :goto_0

    :cond_1
    move-object v7, v6

    .line 191
    :goto_0
    iget-object v8, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$1;->this$1:Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;

    iget-object v8, v8, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader;->this$0:Lgroovy/util/GroovyScriptEngine;

    invoke-virtual {v8, v5}, Lgroovy/util/GroovyScriptEngine;->isSourceNewer(Lgroovy/util/GroovyScriptEngine$ScriptCacheEntry;)Z

    move-result v5
    :try_end_0
    .catch Lgroovy/util/ResourceException; {:try_start_0 .. :try_end_0} :catch_0

    if-eqz v5, :cond_2

    .line 193
    :try_start_1
    invoke-virtual {p2, v3}, Lorg/codehaus/groovy/control/CompilationUnit;->addSource(Ljava/net/URL;)Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object v3

    .line 194
    new-instance v4, Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;

    invoke-direct {v4, v3, v6}, Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/ClassNode;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 196
    :try_start_2
    invoke-static {v2}, Lgroovy/util/GroovyScriptEngine;->access$400(Ljava/net/URLConnection;)V

    return-object v4

    :catchall_0
    move-exception v3

    invoke-static {v2}, Lgroovy/util/GroovyScriptEngine;->access$400(Ljava/net/URLConnection;)V

    .line 197
    throw v3

    .line 199
    :cond_2
    iget-object v2, p0, Lgroovy/util/GroovyScriptEngine$ScriptClassLoader$1;->val$precompiledEntries:Ljava/util/Map;

    invoke-interface {v2, p1, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz v7, :cond_0

    .line 202
    invoke-static {v7}, Lorg/codehaus/groovy/ast/ClassHelper;->make(Ljava/lang/Class;)Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v2

    .line 203
    new-instance v3, Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;

    invoke-direct {v3, v6, v2}, Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;-><init>(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/ast/ClassNode;)V
    :try_end_2
    .catch Lgroovy/util/ResourceException; {:try_start_2 .. :try_end_2} :catch_0

    return-object v3

    .line 209
    :cond_3
    invoke-super {p0, p1, p2}, Lorg/codehaus/groovy/control/ClassNodeResolver;->findClassNode(Ljava/lang/String;Lorg/codehaus/groovy/control/CompilationUnit;)Lorg/codehaus/groovy/control/ClassNodeResolver$LookupResult;

    move-result-object p1

    return-object p1
.end method
