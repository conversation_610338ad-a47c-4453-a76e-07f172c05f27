.class public final synthetic Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda7;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lgroovy/util/ObjectGraphBuilder$ReferenceResolver;


# instance fields
.field public final synthetic f$0:Ljava/lang/Object;

.field public final synthetic f$1:Lgroovy/util/ObjectGraphBuilder;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda7;->f$0:Ljava/lang/Object;

    iput-object p2, p0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda7;->f$1:Lgroovy/util/ObjectGraphBuilder;

    return-void
.end method


# virtual methods
.method public final getReferenceFor(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    iget-object v0, p0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda7;->f$0:Ljava/lang/Object;

    iget-object v1, p0, Lgroovy/util/ObjectGraphBuilder$$ExternalSyntheticLambda7;->f$1:Lgroovy/util/ObjectGraphBuilder;

    invoke-static {v0, v1, p1}, Lgroovy/util/ObjectGraphBuilder;->lambda$setReferenceResolver$7(Ljava/lang/Object;Lgroovy/util/ObjectGraphBuilder;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
