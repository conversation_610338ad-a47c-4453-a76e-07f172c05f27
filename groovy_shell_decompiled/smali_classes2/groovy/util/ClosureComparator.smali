.class public Lgroovy/util/ClosureComparator;
.super Ljava/lang/Object;
.source "ClosureComparator.java"

# interfaces
.implements Ljava/util/Comparator;
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ljava/util/Comparator<",
        "TT;>;",
        "Ljava/io/Serializable;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = -0x3fbf777fc8e887d2L


# instance fields
.field closure:Lgroovy/lang/Closure;


# direct methods
.method public constructor <init>(Lgroovy/lang/Closure;)V
    .locals 0

    .line 35
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 36
    iput-object p1, p0, Lgroovy/util/ClosureComparator;->closure:Lgroovy/lang/Closure;

    return-void
.end method


# virtual methods
.method public compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;TT;)I"
        }
    .end annotation

    .line 40
    iget-object v0, p0, Lgroovy/util/ClosureComparator;->closure:Lgroovy/lang/Closure;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const/4 p1, 0x1

    aput-object p2, v1, p1

    invoke-virtual {v0, v1}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    .line 41
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->intUnbox(Ljava/lang/Object;)I

    move-result p1

    return p1
.end method
