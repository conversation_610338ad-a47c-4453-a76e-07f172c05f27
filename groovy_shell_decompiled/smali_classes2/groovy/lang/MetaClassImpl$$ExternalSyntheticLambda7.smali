.class public final synthetic Lgroovy/lang/MetaClassImpl$$ExternalSyntheticLambda7;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Predicate;


# instance fields
.field public final synthetic f$0:Ljava/lang/Class;

.field public final synthetic f$1:Ljava/util/function/Predicate;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Class;Ljava/util/function/Predicate;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovy/lang/MetaClassImpl$$ExternalSyntheticLambda7;->f$0:Ljava/lang/Class;

    iput-object p2, p0, Lgroovy/lang/MetaClassImpl$$ExternalSyntheticLambda7;->f$1:Ljava/util/function/Predicate;

    return-void
.end method


# virtual methods
.method public final test(Ljava/lang/Object;)Z
    .locals 2

    iget-object v0, p0, Lgroovy/lang/MetaClassImpl$$ExternalSyntheticLambda7;->f$0:Ljava/lang/Class;

    iget-object v1, p0, Lgroovy/lang/MetaClassImpl$$ExternalSyntheticLambda7;->f$1:Ljava/util/function/Predicate;

    check-cast p1, Lorg/codehaus/groovy/runtime/GroovyCategorySupport$CategoryMethod;

    invoke-static {v0, v1, p1}, Lgroovy/lang/MetaClassImpl;->lambda$findCategoryMethod$4(Ljava/lang/Class;Ljava/util/function/Predicate;Lorg/codehaus/groovy/runtime/GroovyCategorySupport$CategoryMethod;)Z

    move-result p1

    return p1
.end method
