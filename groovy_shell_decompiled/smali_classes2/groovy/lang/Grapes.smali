.class public interface abstract annotation Lgroovy/lang/Grapes;
.super Ljava/lang/Object;
.source "Grapes.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/AnnotationDefault;
    value = .subannotation Lgroovy/lang/Grapes;
        initClass = true
    .end subannotation
.end annotation


# virtual methods
.method public abstract initClass()Z
.end method

.method public abstract value()[Lgroovy/lang/Grab;
.end method
