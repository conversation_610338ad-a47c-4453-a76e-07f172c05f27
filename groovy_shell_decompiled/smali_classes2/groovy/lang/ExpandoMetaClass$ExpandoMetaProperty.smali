.class public Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;
.super Lgroovy/lang/GroovyObjectSupport;
.source "ExpandoMetaClass.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/lang/ExpandoMetaClass;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4
    name = "ExpandoMetaProperty"
.end annotation


# instance fields
.field protected isStatic:Z

.field protected propertyName:Ljava/lang/String;

.field final synthetic this$0:Lgroovy/lang/ExpandoMetaClass;


# direct methods
.method protected constructor <init>(Lgroovy/lang/ExpandoMetaClass;Ljava/lang/String;)V
    .locals 1

    const/4 v0, 0x0

    .line 579
    invoke-direct {p0, p1, p2, v0}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;-><init>(Lgroovy/lang/ExpandoMetaClass;Ljava/lang/String;Z)V

    return-void
.end method

.method protected constructor <init>(Lgroovy/lang/ExpandoMetaClass;Ljava/lang/String;Z)V
    .locals 0

    .line 582
    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->this$0:Lgroovy/lang/ExpandoMetaClass;

    invoke-direct {p0}, Lgroovy/lang/GroovyObjectSupport;-><init>()V

    .line 583
    iput-object p2, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    .line 584
    iput-boolean p3, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->isStatic:Z

    return-void
.end method

.method private checkIfMethodExists(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/reflect/Method;
    .locals 4

    .line 639
    invoke-virtual {p1}, Ljava/lang/Class;->getMethods()[Ljava/lang/reflect/Method;

    move-result-object p1

    .line 640
    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-object v2, p1, v1

    .line 641
    invoke-virtual {v2}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v3, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-virtual {v2}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result v3

    invoke-static {v3}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v3

    if-ne v3, p4, :cond_0

    .line 642
    invoke-virtual {v2}, Ljava/lang/reflect/Method;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v3

    invoke-static {p3, v3}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->parametersAreCompatible([Ljava/lang/Class;[Ljava/lang/Class;)Z

    move-result v3

    if-eqz v3, :cond_0

    goto :goto_1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    const/4 v2, 0x0

    :goto_1
    return-object v2
.end method

.method private registerIfClosure(Ljava/lang/Object;Z)V
    .locals 4

    .line 601
    instance-of v0, p1, Lgroovy/lang/Closure;

    if-eqz v0, :cond_3

    .line 602
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    const-string v1, "constructor"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string v0, "<init>"

    .line 603
    iput-object v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    .line 605
    :cond_0
    check-cast p1, Lgroovy/lang/Closure;

    .line 606
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->this$0:Lgroovy/lang/ExpandoMetaClass;

    iget-object v1, v1, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-static {v0, v1, p1}, Lorg/codehaus/groovy/runtime/metaclass/ClosureMetaMethod;->createMethodList(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/Closure;)Ljava/util/List;

    move-result-object v0

    .line 607
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_1

    iget-boolean v1, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->isStatic:Z

    if-eqz v1, :cond_1

    .line 608
    invoke-virtual {p1}, Lgroovy/lang/Closure;->getParameterTypes()[Ljava/lang/Class;

    move-result-object v0

    .line 609
    invoke-direct {p0, p1, p2, v0}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->registerStatic(Lgroovy/lang/Closure;Z[Ljava/lang/Class;)V

    return-void

    .line 612
    :cond_1
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/MetaMethod;

    .line 613
    invoke-virtual {v1}, Lgroovy/lang/MetaMethod;->getNativeParameterTypes()[Ljava/lang/Class;

    move-result-object v2

    .line 614
    iget-boolean v3, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->isStatic:Z

    if-eqz v3, :cond_2

    .line 615
    invoke-direct {p0, p1, p2, v2}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->registerStatic(Lgroovy/lang/Closure;Z[Ljava/lang/Class;)V

    goto :goto_0

    .line 617
    :cond_2
    invoke-direct {p0, v1, p2, v2}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->registerInstance(Lgroovy/lang/MetaMethod;Z[Ljava/lang/Class;)V

    goto :goto_0

    :cond_3
    return-void
.end method

.method private registerInstance(Lgroovy/lang/MetaMethod;Z[Ljava/lang/Class;)V
    .locals 3

    .line 631
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->this$0:Lgroovy/lang/ExpandoMetaClass;

    iget-object v0, v0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    const/4 v2, 0x0

    invoke-direct {p0, v0, v1, p3, v2}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->checkIfMethodExists(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/reflect/Method;

    move-result-object v0

    if-eqz v0, :cond_1

    if-eqz p2, :cond_0

    goto :goto_0

    .line 633
    :cond_0
    new-instance p1, Lgroovy/lang/GroovyRuntimeException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Cannot add new method ["

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "] for arguments ["

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-static {p3}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->inspect(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "]. It already exists!"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 634
    :cond_1
    :goto_0
    iget-object p2, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->this$0:Lgroovy/lang/ExpandoMetaClass;

    invoke-virtual {p2, p1}, Lgroovy/lang/ExpandoMetaClass;->registerInstanceMethod(Lgroovy/lang/MetaMethod;)V

    return-void
.end method

.method private registerStatic(Lgroovy/lang/Closure;Z[Ljava/lang/Class;)V
    .locals 3

    .line 624
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->this$0:Lgroovy/lang/ExpandoMetaClass;

    iget-object v0, v0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    const/4 v2, 0x1

    invoke-direct {p0, v0, v1, p3, v2}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->checkIfMethodExists(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/reflect/Method;

    move-result-object v0

    if-eqz v0, :cond_1

    if-eqz p2, :cond_0

    goto :goto_0

    .line 626
    :cond_0
    new-instance p1, Lgroovy/lang/GroovyRuntimeException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Cannot add new static method ["

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, "] for arguments ["

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-static {p3}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->inspect(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string p3, "]. It already exists!"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 627
    :cond_1
    :goto_0
    iget-object p2, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->this$0:Lgroovy/lang/ExpandoMetaClass;

    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    invoke-virtual {p2, v0, p1, p3}, Lgroovy/lang/ExpandoMetaClass;->registerStaticMethod(Ljava/lang/String;Lgroovy/lang/Closure;[Ljava/lang/Class;)V

    return-void
.end method


# virtual methods
.method public getProperty(Ljava/lang/String;)Ljava/lang/Object;
    .locals 0

    .line 656
    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    return-object p0
.end method

.method public getPropertyName()Ljava/lang/String;
    .locals 1

    .line 588
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    return-object v0
.end method

.method public isStatic()Z
    .locals 1

    .line 592
    iget-boolean v0, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->isStatic:Z

    return v0
.end method

.method public leftShift(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    .line 596
    invoke-direct {p0, p1, v0}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->registerIfClosure(Ljava/lang/Object;Z)V

    return-object p0
.end method

.method public setProperty(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 0

    .line 664
    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->propertyName:Ljava/lang/String;

    const/4 p1, 0x1

    .line 665
    invoke-direct {p0, p2, p1}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;->registerIfClosure(Ljava/lang/Object;Z)V

    return-void
.end method
