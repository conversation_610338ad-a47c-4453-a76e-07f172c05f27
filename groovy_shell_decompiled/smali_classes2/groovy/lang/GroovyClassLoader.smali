.class public Lgroovy/lang/GroovyClassLoader;
.super Ljava/net/URLClassLoader;
.source "GroovyClassLoader.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/lang/GroovyClassLoader$ClassCollector;,
        Lgroovy/lang/GroovyClassLoader$TimestampAdder;,
        Lgroovy/lang/GroovyClassLoader$InnerLoader;
    }
.end annotation


# static fields
.field private static final EMPTY_CLASS_ARRAY:[Ljava/lang/Class;

.field private static final EMPTY_URL_ARRAY:[Ljava/net/URL;

.field private static scriptNameCounter:I = 0xf4240


# instance fields
.field protected final classCache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/runtime/memoize/EvictableCache<",
            "Ljava/lang/String;",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field

.field private final config:Lorg/codehaus/groovy/control/CompilerConfiguration;

.field private recompile:Ljava/lang/Boolean;

.field private resourceLoader:Lgroovy/lang/GroovyResourceLoader;

.field protected final sourceCache:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache<",
            "Ljava/lang/String;",
            "Ljava/lang/Class;",
            ">;"
        }
    .end annotation
.end field

.field private sourceEncoding:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    const/4 v0, 0x0

    new-array v1, v0, [Ljava/net/URL;

    .line 91
    sput-object v1, Lgroovy/lang/GroovyClassLoader;->EMPTY_URL_ARRAY:[Ljava/net/URL;

    new-array v0, v0, [Ljava/lang/Class;

    .line 92
    sput-object v0, Lgroovy/lang/GroovyClassLoader;->EMPTY_CLASS_ARRAY:[Ljava/lang/Class;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 132
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Thread;->getContextClassLoader()Ljava/lang/ClassLoader;

    move-result-object v0

    invoke-direct {p0, v0}, Lgroovy/lang/GroovyClassLoader;-><init>(Ljava/lang/ClassLoader;)V

    return-void
.end method

.method public constructor <init>(Lgroovy/lang/GroovyClassLoader;)V
    .locals 2

    .line 147
    iget-object v0, p1, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    const/4 v1, 0x0

    invoke-direct {p0, p1, v0, v1}, Lgroovy/lang/GroovyClassLoader;-><init>(Ljava/lang/ClassLoader;Lorg/codehaus/groovy/control/CompilerConfiguration;Z)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/ClassLoader;)V
    .locals 1

    const/4 v0, 0x0

    .line 139
    invoke-direct {p0, p1, v0}, Lgroovy/lang/GroovyClassLoader;-><init>(Ljava/lang/ClassLoader;Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/ClassLoader;Lorg/codehaus/groovy/control/CompilerConfiguration;)V
    .locals 1

    const/4 v0, 0x1

    .line 183
    invoke-direct {p0, p1, p2, v0}, Lgroovy/lang/GroovyClassLoader;-><init>(Ljava/lang/ClassLoader;Lorg/codehaus/groovy/control/CompilerConfiguration;Z)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/ClassLoader;Lorg/codehaus/groovy/control/CompilerConfiguration;Z)V
    .locals 1

    .line 158
    sget-object v0, Lgroovy/lang/GroovyClassLoader;->EMPTY_URL_ARRAY:[Ljava/net/URL;

    invoke-direct {p0, v0, p1}, Ljava/net/URLClassLoader;-><init>([Ljava/net/URL;Ljava/lang/ClassLoader;)V

    .line 97
    new-instance p1, Lorg/codehaus/groovy/runtime/memoize/UnlimitedConcurrentCache;

    invoke-direct {p1}, Lorg/codehaus/groovy/runtime/memoize/UnlimitedConcurrentCache;-><init>()V

    iput-object p1, p0, Lgroovy/lang/GroovyClassLoader;->classCache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;

    .line 103
    new-instance p1, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;

    invoke-direct {p1}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;-><init>()V

    iput-object p1, p0, Lgroovy/lang/GroovyClassLoader;->sourceCache:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;

    .line 111
    new-instance p1, Lgroovy/lang/GroovyClassLoader$1;

    invoke-direct {p1, p0}, Lgroovy/lang/GroovyClassLoader$1;-><init>(Lgroovy/lang/GroovyClassLoader;)V

    iput-object p1, p0, Lgroovy/lang/GroovyClassLoader;->resourceLoader:Lgroovy/lang/GroovyResourceLoader;

    if-nez p2, :cond_0

    .line 159
    sget-object p2, Lorg/codehaus/groovy/control/CompilerConfiguration;->DEFAULT:Lorg/codehaus/groovy/control/CompilerConfiguration;

    .line 160
    :cond_0
    iput-object p2, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    if-eqz p3, :cond_1

    .line 162
    invoke-virtual {p2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getClasspath()Ljava/util/List;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Ljava/lang/String;

    .line 163
    invoke-virtual {p0, p3}, Lgroovy/lang/GroovyClassLoader;->addClasspath(Ljava/lang/String;)V

    goto :goto_0

    .line 167
    :cond_1
    invoke-direct {p0, p2}, Lgroovy/lang/GroovyClassLoader;->initSourceEncoding(Lorg/codehaus/groovy/control/CompilerConfiguration;)V

    return-void
.end method

.method static synthetic access$000(Lgroovy/lang/GroovyClassLoader;)Lorg/codehaus/groovy/control/CompilerConfiguration;
    .locals 0

    .line 90
    iget-object p0, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    return-object p0
.end method

.method static synthetic access$100(Lgroovy/lang/GroovyClassLoader;Ljava/lang/String;Ljava/lang/String;)Ljava/net/URL;
    .locals 0

    .line 90
    invoke-direct {p0, p1, p2}, Lgroovy/lang/GroovyClassLoader;->getSourceFile(Ljava/lang/String;Ljava/lang/String;)Ljava/net/URL;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$400(Lgroovy/lang/GroovyClassLoader;Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;
    .locals 0

    .line 90
    invoke-virtual/range {p0 .. p5}, Lgroovy/lang/GroovyClassLoader;->defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;

    move-result-object p0

    return-object p0
.end method

.method private static decodeFileName(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    :try_start_0
    const-string v0, "UTF-8"

    .line 1007
    invoke-static {p0, v0}, Ljava/net/URLDecoder;->decode(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0
    :try_end_0
    .catch Ljava/io/UnsupportedEncodingException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 1009
    :catch_0
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v1, "Encountered an invalid encoding scheme when trying to use URLDecoder.decode() inside of the GroovyClassLoader.decodeFileName() method.  Returning the unencoded URL."

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 1010
    sget-object v0, Ljava/lang/System;->err:Ljava/io/PrintStream;

    const-string v1, "Please note that if you encounter this error and you have spaces in your directory you will run into issues.  Refer to GROOVY-1787 for description of this bug."

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    :goto_0
    return-object p0
.end method

.method private definePackageInternal(Ljava/lang/String;)V
    .locals 11

    const/16 v0, 0x2e

    .line 412
    invoke-virtual {p1, v0}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    const/4 v1, 0x0

    .line 414
    invoke-virtual {p1, v1, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v3

    .line 415
    invoke-virtual {p0, v3}, Lgroovy/lang/GroovyClassLoader;->getPackage(Ljava/lang/String;)Ljava/lang/Package;

    move-result-object p1

    if-nez p1, :cond_0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    move-object v2, p0

    .line 417
    invoke-virtual/range {v2 .. v10}, Lgroovy/lang/GroovyClassLoader;->definePackage(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/net/URL;)Ljava/lang/Package;

    :cond_0
    return-void
.end method

.method private doParseClass(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/Class;
    .locals 4

    .line 366
    invoke-static {p1}, Lgroovy/lang/GroovyClassLoader;->validate(Lgroovy/lang/GroovyCodeSource;)V

    .line 368
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p1}, Lgroovy/lang/GroovyCodeSource;->getCodeSource()Ljava/security/CodeSource;

    move-result-object v1

    invoke-virtual {p0, v0, v1}, Lgroovy/lang/GroovyClassLoader;->createCompilationUnit(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;)Lorg/codehaus/groovy/control/CompilationUnit;

    move-result-object v0

    .line 369
    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->recompile:Ljava/lang/Boolean;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v1

    if-nez v1, :cond_1

    :cond_0
    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->recompile:Ljava/lang/Boolean;

    if-nez v1, :cond_2

    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getRecompileGroovySource()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 370
    :cond_1
    invoke-static {}, Lgroovy/lang/GroovyClassLoader$TimestampAdder;->access$300()Lgroovy/lang/GroovyClassLoader$TimestampAdder;

    move-result-object v1

    sget-object v2, Lorg/codehaus/groovy/control/CompilePhase;->CLASS_GENERATION:Lorg/codehaus/groovy/control/CompilePhase;

    invoke-virtual {v2}, Lorg/codehaus/groovy/control/CompilePhase;->getPhaseNumber()I

    move-result v2

    invoke-virtual {v0, v1, v2}, Lorg/codehaus/groovy/control/CompilationUnit;->addFirstPhaseOperation(Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;I)V

    .line 373
    :cond_2
    invoke-virtual {p1}, Lgroovy/lang/GroovyCodeSource;->getFile()Ljava/io/File;

    move-result-object v1

    if-eqz v1, :cond_3

    .line 375
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilationUnit;->addSource(Ljava/io/File;)Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object p1

    goto :goto_0

    .line 377
    :cond_3
    invoke-virtual {p1}, Lgroovy/lang/GroovyCodeSource;->getURL()Ljava/net/URL;

    move-result-object v1

    if-eqz v1, :cond_4

    .line 379
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilationUnit;->addSource(Ljava/net/URL;)Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object p1

    goto :goto_0

    .line 381
    :cond_4
    invoke-virtual {p1}, Lgroovy/lang/GroovyCodeSource;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lgroovy/lang/GroovyCodeSource;->getScriptText()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, v1, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->addSource(Ljava/lang/String;Ljava/lang/String;)Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object p1

    .line 385
    :goto_0
    invoke-virtual {p0, v0, p1}, Lgroovy/lang/GroovyClassLoader;->createCollector(Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/control/SourceUnit;)Lgroovy/lang/GroovyClassLoader$ClassCollector;

    move-result-object v1

    .line 386
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/control/CompilationUnit;->setClassgenCallback(Lorg/codehaus/groovy/control/CompilationUnit$ClassgenCallback;)V

    const/4 v2, 0x7

    .line 388
    iget-object v3, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    if-eqz v3, :cond_5

    invoke-virtual {v3}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getTargetDirectory()Ljava/io/File;

    move-result-object v3

    if-eqz v3, :cond_5

    const/16 v2, 0x8

    .line 389
    :cond_5
    invoke-virtual {v0, v2}, Lorg/codehaus/groovy/control/CompilationUnit;->compile(I)V

    .line 391
    invoke-static {v1}, Lgroovy/lang/GroovyClassLoader$ClassCollector;->access$200(Lgroovy/lang/GroovyClassLoader$ClassCollector;)Ljava/lang/Class;

    move-result-object v0

    .line 392
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/SourceUnit;->getAST()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object p1

    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ModuleNode;->getMainClassName()Ljava/lang/String;

    move-result-object p1

    .line 393
    invoke-virtual {v1}, Lgroovy/lang/GroovyClassLoader$ClassCollector;->getLoadedClasses()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_6
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_7

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 394
    check-cast v2, Ljava/lang/Class;

    .line 395
    invoke-virtual {v2}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    .line 396
    invoke-direct {p0, v3}, Lgroovy/lang/GroovyClassLoader;->definePackageInternal(Ljava/lang/String;)V

    .line 397
    invoke-virtual {p0, v2}, Lgroovy/lang/GroovyClassLoader;->setClassCacheEntry(Ljava/lang/Class;)V

    .line 398
    invoke-virtual {v3, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_6

    move-object v0, v2

    goto :goto_1

    :cond_7
    return-object v0
.end method

.method private static fileReallyExists(Ljava/net/URL;Ljava/lang/String;)Ljava/io/File;
    .locals 3

    .line 1033
    :try_start_0
    new-instance v0, Ljava/io/File;

    invoke-virtual {p0}, Ljava/net/URL;->toURI()Ljava/net/URI;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/net/URI;)V
    :try_end_0
    .catch Ljava/net/URISyntaxException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 1035
    :catch_0
    new-instance v0, Ljava/io/File;

    invoke-virtual {p0}, Ljava/net/URL;->getFile()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lgroovy/lang/GroovyClassLoader;->decodeFileName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 1037
    :goto_0
    invoke-virtual {v0}, Ljava/io/File;->getParentFile()Ljava/io/File;

    move-result-object p0

    .line 1039
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p0, p1}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 1040
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result p0

    if-eqz p0, :cond_1

    .line 1044
    :try_start_1
    invoke-virtual {v0}, Ljava/io/File;->getCanonicalPath()Ljava/lang/String;

    move-result-object p0

    .line 1045
    sget-object v1, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {p0, v1}, Ljava/lang/String;->lastIndexOf(Ljava/lang/String;)I

    move-result v1

    const/4 v2, -0x1

    if-eq v1, v2, :cond_0

    add-int/lit8 v1, v1, 0x1

    .line 1047
    invoke-virtual {p0, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p0

    .line 1049
    :cond_0
    invoke-virtual {p1, p0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_1

    if-eqz p0, :cond_1

    return-object v0

    :catch_1
    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method private genSourceCacheKey(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/String;
    .locals 6

    .line 340
    invoke-virtual {p1}, Lgroovy/lang/GroovyCodeSource;->getScriptText()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 342
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v2

    int-to-double v2, v2

    const-wide v4, 0x3ff3333333333333L    # 1.2

    mul-double/2addr v2, v4

    double-to-int v2, v2

    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(I)V

    const-string v2, "scriptText:"

    .line 343
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 345
    invoke-virtual {p1}, Lgroovy/lang/GroovyCodeSource;->getCodeSource()Ljava/security/CodeSource;

    move-result-object p1

    if-eqz p1, :cond_1

    const-string v0, "/codeSource:"

    .line 347
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 350
    :cond_0
    new-instance v1, Ljava/lang/StringBuilder;

    const/16 v0, 0x20

    invoke-direct {v1, v0}, Ljava/lang/StringBuilder;-><init>(I)V

    const-string v0, "name:"

    .line 355
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Lgroovy/lang/GroovyCodeSource;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 359
    :cond_1
    :goto_0
    :try_start_0
    invoke-static {v1}, Lorg/codehaus/groovy/runtime/EncodingGroovyMethods;->md5(Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1
    :try_end_0
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 361
    new-instance v0, Lgroovy/lang/GroovyRuntimeException;

    invoke-direct {v0, p1}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method private static getFileForUrl(Ljava/net/URL;Ljava/lang/String;)Ljava/io/File;
    .locals 3

    const/16 v0, 0x2f

    .line 1022
    invoke-virtual {p1, v0}, Ljava/lang/String;->indexOf(I)I

    move-result v1

    const/4 v2, -0x1

    if-eq v1, v2, :cond_0

    .line 1023
    invoke-virtual {p1, v0}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    .line 1024
    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1026
    :cond_0
    invoke-static {p0, p1}, Lgroovy/lang/GroovyClassLoader;->fileReallyExists(Ljava/net/URL;Ljava/lang/String;)Ljava/io/File;

    move-result-object p0

    return-object p0
.end method

.method private getSourceFile(Ljava/lang/String;Ljava/lang/String;)Ljava/net/URL;
    .locals 3

    .line 1063
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v1, 0x2e

    const/16 v2, 0x2f

    invoke-virtual {p1, v1, v2}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, "."

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 1064
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->getResource(Ljava/lang/String;)Ljava/net/URL;

    move-result-object p2

    .line 1065
    invoke-static {p2}, Lgroovy/lang/GroovyClassLoader;->isFile(Ljava/net/URL;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {p2, p1}, Lgroovy/lang/GroovyClassLoader;->getFileForUrl(Ljava/net/URL;Ljava/lang/String;)Ljava/io/File;

    move-result-object p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    return-object p2
.end method

.method private initSourceEncoding(Lorg/codehaus/groovy/control/CompilerConfiguration;)V
    .locals 0

    .line 171
    invoke-virtual {p1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getSourceEncoding()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovy/lang/GroovyClassLoader;->sourceEncoding:Ljava/lang/String;

    if-nez p1, :cond_0

    .line 175
    invoke-static {}, Lgroovy/util/CharsetToolkit;->getDefaultSystemCharset()Ljava/nio/charset/Charset;

    move-result-object p1

    invoke-virtual {p1}, Ljava/nio/charset/Charset;->name()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lgroovy/lang/GroovyClassLoader;->sourceEncoding:Ljava/lang/String;

    :cond_0
    return-void
.end method

.method private static isFile(Ljava/net/URL;)Z
    .locals 1

    if-eqz p0, :cond_0

    .line 1017
    invoke-virtual {p0}, Ljava/net/URL;->getProtocol()Ljava/lang/String;

    move-result-object p0

    const-string v0, "file"

    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-eqz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method static synthetic lambda$parseClass$0(Ljava/lang/String;Ljava/lang/String;)Lgroovy/lang/GroovyCodeSource;
    .locals 2

    .line 255
    new-instance v0, Lgroovy/lang/GroovyCodeSource;

    const-string v1, "/groovy/script"

    invoke-direct {v0, p0, p1, v1}, Lgroovy/lang/GroovyCodeSource;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    return-object v0
.end method

.method static synthetic lambda$parseClass$1(Ljava/io/Reader;Ljava/lang/String;)Lgroovy/lang/GroovyCodeSource;
    .locals 3

    .line 282
    :try_start_0
    invoke-static {p0}, Lorg/codehaus/groovy/runtime/IOGroovyMethods;->getText(Ljava/io/Reader;)Ljava/lang/String;

    move-result-object p0

    .line 283
    new-instance v0, Lgroovy/lang/GroovyCodeSource;

    const-string v1, "/groovy/script"

    invoke-direct {v0, p0, p1, v1}, Lgroovy/lang/GroovyCodeSource;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v0

    :catch_0
    move-exception p0

    .line 285
    new-instance v0, Ljava/lang/RuntimeException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Impossible to read the content of the reader for file named: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1, p0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0
.end method

.method private static validate(Lgroovy/lang/GroovyCodeSource;)V
    .locals 1

    .line 404
    invoke-virtual {p0}, Lgroovy/lang/GroovyCodeSource;->getFile()Ljava/io/File;

    move-result-object v0

    if-nez v0, :cond_1

    .line 405
    invoke-virtual {p0}, Lgroovy/lang/GroovyCodeSource;->getScriptText()Ljava/lang/String;

    move-result-object p0

    if-eqz p0, :cond_0

    goto :goto_0

    .line 406
    :cond_0
    new-instance p0, Ljava/lang/IllegalArgumentException;

    const-string v0, "Script text to compile cannot be null!"

    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p0

    :cond_1
    :goto_0
    return-void
.end method


# virtual methods
.method public addClasspath(Ljava/lang/String;)V
    .locals 1

    .line 1107
    new-instance v0, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda2;

    invoke-direct {v0, p0, p1}, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda2;-><init>(Lgroovy/lang/GroovyClassLoader;Ljava/lang/String;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    return-void
.end method

.method public addURL(Ljava/net/URL;)V
    .locals 0

    .line 793
    invoke-super {p0, p1}, Ljava/net/URLClassLoader;->addURL(Ljava/net/URL;)V

    return-void
.end method

.method public clearCache()V
    .locals 2

    .line 1165
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->classCache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;

    invoke-interface {v0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->clearAll()Ljava/util/Map;

    move-result-object v0

    .line 1167
    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->sourceCache:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;

    invoke-virtual {v1}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->clear()V

    .line 1169
    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 1175
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Class;

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->removeClass(Ljava/lang/Class;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public close()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1192
    invoke-super {p0}, Ljava/net/URLClassLoader;->close()V

    .line 1193
    invoke-virtual {p0}, Lgroovy/lang/GroovyClassLoader;->clearCache()V

    return-void
.end method

.method protected createCollector(Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/control/SourceUnit;)Lgroovy/lang/GroovyClassLoader$ClassCollector;
    .locals 2

    .line 671
    new-instance v0, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0}, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda0;-><init>(Lgroovy/lang/GroovyClassLoader;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovy/lang/GroovyClassLoader$InnerLoader;

    .line 672
    new-instance v1, Lgroovy/lang/GroovyClassLoader$ClassCollector;

    invoke-direct {v1, v0, p1, p2}, Lgroovy/lang/GroovyClassLoader$ClassCollector;-><init>(Lgroovy/lang/GroovyClassLoader$InnerLoader;Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/control/SourceUnit;)V

    return-object v1
.end method

.method protected createCompilationUnit(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;)Lorg/codehaus/groovy/control/CompilationUnit;
    .locals 1

    .line 660
    new-instance v0, Lorg/codehaus/groovy/control/CompilationUnit;

    invoke-direct {v0, p1, p2, p0}, Lorg/codehaus/groovy/control/CompilationUnit;-><init>(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;Lgroovy/lang/GroovyClassLoader;)V

    return-object v0
.end method

.method public defineClass(Ljava/lang/String;[B)Ljava/lang/Class;
    .locals 2

    .line 733
    array-length v0, p2

    const/4 v1, 0x0

    invoke-super {p0, p1, p2, v1, v0}, Ljava/net/URLClassLoader;->defineClass(Ljava/lang/String;[BII)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method public defineClass(Lorg/codehaus/groovy/ast/ClassNode;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/Class;
    .locals 4

    const/4 p2, 0x0

    .line 208
    :try_start_0
    new-instance v0, Ljava/security/CodeSource;

    new-instance v1, Ljava/net/URL;

    const-string v2, "file"

    const-string v3, ""

    invoke-direct {v1, v2, v3, p3}, Ljava/net/URL;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    move-object p3, p2

    check-cast p3, [Ljava/security/cert/Certificate;

    invoke-direct {v0, v1, p2}, Ljava/security/CodeSource;-><init>(Ljava/net/URL;[Ljava/security/cert/Certificate;)V
    :try_end_0
    .catch Ljava/net/MalformedURLException; {:try_start_0 .. :try_end_0} :catch_0

    move-object p2, v0

    .line 213
    :catch_0
    iget-object p3, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {p0, p3, p2}, Lgroovy/lang/GroovyClassLoader;->createCompilationUnit(Lorg/codehaus/groovy/control/CompilerConfiguration;Ljava/security/CodeSource;)Lorg/codehaus/groovy/control/CompilationUnit;

    move-result-object p2

    .line 214
    invoke-virtual {p1}, Lorg/codehaus/groovy/ast/ClassNode;->getModule()Lorg/codehaus/groovy/ast/ModuleNode;

    move-result-object p3

    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/ModuleNode;->getContext()Lorg/codehaus/groovy/control/SourceUnit;

    move-result-object p3

    invoke-virtual {p0, p2, p3}, Lgroovy/lang/GroovyClassLoader;->createCollector(Lorg/codehaus/groovy/control/CompilationUnit;Lorg/codehaus/groovy/control/SourceUnit;)Lgroovy/lang/GroovyClassLoader$ClassCollector;

    move-result-object p3

    .line 216
    :try_start_1
    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->addClassNode(Lorg/codehaus/groovy/ast/ClassNode;)V

    .line 217
    invoke-virtual {p2, p3}, Lorg/codehaus/groovy/control/CompilationUnit;->setClassgenCallback(Lorg/codehaus/groovy/control/CompilationUnit$ClassgenCallback;)V

    const/4 p1, 0x7

    .line 218
    invoke-virtual {p2, p1}, Lorg/codehaus/groovy/control/CompilationUnit;->compile(I)V

    .line 219
    invoke-static {p3}, Lgroovy/lang/GroovyClassLoader$ClassCollector;->access$200(Lgroovy/lang/GroovyClassLoader$ClassCollector;)Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovy/lang/GroovyClassLoader;->definePackageInternal(Ljava/lang/String;)V

    .line 220
    invoke-static {p3}, Lgroovy/lang/GroovyClassLoader$ClassCollector;->access$200(Lgroovy/lang/GroovyClassLoader$ClassCollector;)Ljava/lang/Class;

    move-result-object p1
    :try_end_1
    .catch Lorg/codehaus/groovy/control/CompilationFailedException; {:try_start_1 .. :try_end_1} :catch_1

    return-object p1

    :catch_1
    move-exception p1

    .line 222
    new-instance p2, Ljava/lang/RuntimeException;

    invoke-direct {p2, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw p2
.end method

.method public declared-synchronized generateScriptName()Ljava/lang/String;
    .locals 2

    monitor-enter p0

    .line 275
    :try_start_0
    sget v0, Lgroovy/lang/GroovyClassLoader;->scriptNameCounter:I

    add-int/lit8 v0, v0, 0x1

    sput v0, Lgroovy/lang/GroovyClassLoader;->scriptNameCounter:I

    .line 276
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "script"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget v1, Lgroovy/lang/GroovyClassLoader;->scriptNameCounter:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".groovy"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method protected getClassCacheEntry(Ljava/lang/String;)Ljava/lang/Class;
    .locals 1

    .line 760
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->classCache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Class;

    return-object p1
.end method

.method protected getClassPath()[Ljava/lang/String;
    .locals 5

    .line 430
    invoke-virtual {p0}, Lgroovy/lang/GroovyClassLoader;->getURLs()[Ljava/net/URL;

    move-result-object v0

    .line 431
    array-length v1, v0

    new-array v2, v1, [Ljava/lang/String;

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v1, :cond_0

    .line 433
    aget-object v4, v0, v3

    invoke-virtual {v4}, Ljava/net/URL;->getFile()Ljava/lang/String;

    move-result-object v4

    aput-object v4, v2, v3

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    return-object v2
.end method

.method public getLoadedClasses()[Ljava/lang/Class;
    .locals 2

    .line 1150
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->classCache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;

    invoke-interface {v0}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->values()Ljava/util/Collection;

    move-result-object v0

    sget-object v1, Lgroovy/lang/GroovyClassLoader;->EMPTY_CLASS_ARRAY:[Ljava/lang/Class;

    invoke-interface {v0, v1}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Ljava/lang/Class;

    return-object v0
.end method

.method protected getPermissions(Ljava/security/CodeSource;)Ljava/security/PermissionCollection;
    .locals 2

    .line 442
    :try_start_0
    invoke-super {p0, p1}, Ljava/net/URLClassLoader;->getPermissions(Ljava/security/CodeSource;)Ljava/security/PermissionCollection;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/SecurityException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    .line 445
    :catch_0
    :try_start_1
    new-instance p1, Ljava/security/Permissions;

    invoke-direct {p1}, Ljava/security/Permissions;-><init>()V

    .line 448
    :goto_0
    new-instance v0, Lgroovy/lang/GroovyClassLoader$2;

    invoke-direct {v0, p0}, Lgroovy/lang/GroovyClassLoader$2;-><init>(Lgroovy/lang/GroovyClassLoader;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/security/ProtectionDomain;

    .line 453
    invoke-virtual {v0}, Ljava/security/ProtectionDomain;->getPermissions()Ljava/security/PermissionCollection;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 455
    invoke-virtual {v0}, Ljava/security/PermissionCollection;->elements()Ljava/util/Enumeration;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Enumeration;->hasMoreElements()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 456
    invoke-interface {v0}, Ljava/util/Enumeration;->nextElement()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/security/Permission;

    invoke-virtual {p1, v1}, Ljava/security/PermissionCollection;->add(Ljava/security/Permission;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    .line 461
    :catchall_0
    new-instance p1, Ljava/security/Permissions;

    invoke-direct {p1}, Ljava/security/Permissions;-><init>()V

    .line 463
    :cond_0
    invoke-virtual {p1}, Ljava/security/PermissionCollection;->setReadOnly()V

    return-object p1
.end method

.method public getResourceLoader()Lgroovy/lang/GroovyResourceLoader;
    .locals 1

    .line 194
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->resourceLoader:Lgroovy/lang/GroovyResourceLoader;

    return-object v0
.end method

.method protected getTimeStamp(Ljava/lang/Class;)J
    .locals 2

    .line 992
    invoke-static {p1}, Lorg/codehaus/groovy/classgen/Verifier;->getTimestamp(Ljava/lang/Class;)J

    move-result-wide v0

    return-wide v0
.end method

.method public hasCompatibleConfiguration(Lorg/codehaus/groovy/control/CompilerConfiguration;)Z
    .locals 1

    .line 234
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    if-ne v0, p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method protected isRecompilable(Ljava/lang/Class;)Z
    .locals 7

    const/4 v0, 0x1

    if-nez p1, :cond_0

    return v0

    .line 814
    :cond_0
    invoke-virtual {p1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    const/4 v2, 0x0

    if-ne v1, p0, :cond_1

    return v2

    .line 815
    :cond_1
    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->recompile:Ljava/lang/Boolean;

    if-nez v1, :cond_2

    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getRecompileGroovySource()Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    .line 816
    :cond_2
    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->recompile:Ljava/lang/Boolean;

    if-eqz v1, :cond_3

    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    .line 817
    :cond_3
    const-class v1, Lgroovy/lang/GroovyObject;

    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v1

    if-nez v1, :cond_4

    return v2

    .line 818
    :cond_4
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->getTimeStamp(Ljava/lang/Class;)J

    move-result-wide v3

    const-wide v5, 0x7fffffffffffffffL

    cmp-long p1, v3, v5

    if-eqz p1, :cond_5

    goto :goto_0

    :cond_5
    move v0, v2

    :goto_0
    return v0
.end method

.method public isShouldRecompile()Ljava/lang/Boolean;
    .locals 1

    .line 843
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->recompile:Ljava/lang/Boolean;

    return-object v0
.end method

.method protected isSourceNewer(Ljava/net/URL;Ljava/lang/Class;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1084
    invoke-static {p1}, Lgroovy/lang/GroovyClassLoader;->isFile(Ljava/net/URL;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1088
    invoke-virtual {p1}, Ljava/net/URL;->getPath()Ljava/lang/String;

    move-result-object p1

    const/16 v0, 0x2f

    sget-char v1, Ljava/io/File;->separatorChar:C

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p1

    const/16 v0, 0x7c

    const/16 v1, 0x3a

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object p1

    .line 1089
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 1090
    invoke-virtual {v0}, Ljava/io/File;->lastModified()J

    move-result-wide v0

    goto :goto_0

    .line 1092
    :cond_0
    invoke-virtual {p1}, Ljava/net/URL;->openConnection()Ljava/net/URLConnection;

    move-result-object p1

    .line 1093
    invoke-virtual {p1}, Ljava/net/URLConnection;->getLastModified()J

    move-result-wide v0

    .line 1094
    invoke-virtual {p1}, Ljava/net/URLConnection;->getInputStream()Ljava/io/InputStream;

    move-result-object p1

    invoke-virtual {p1}, Ljava/io/InputStream;->close()V

    .line 1096
    :goto_0
    invoke-virtual {p0, p2}, Lgroovy/lang/GroovyClassLoader;->getTimeStamp(Ljava/lang/Class;)J

    move-result-wide p1

    .line 1097
    iget-object v2, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v2}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getMinimumRecompilationInterval()I

    move-result v2

    int-to-long v2, v2

    add-long/2addr p1, v2

    cmp-long p1, p1, v0

    if-gez p1, :cond_1

    const/4 p1, 0x1

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    :goto_1
    return p1
.end method

.method public synthetic lambda$addClasspath$5$groovy-lang-GroovyClassLoader(Ljava/lang/String;)Ljava/lang/Void;
    .locals 5

    .line 1111
    :try_start_0
    new-instance v0, Ljava/net/URI;

    invoke-direct {v0, p1}, Ljava/net/URI;-><init>(Ljava/lang/String;)V

    .line 1113
    invoke-virtual {v0}, Ljava/net/URI;->toURL()Ljava/net/URL;
    :try_end_0
    .catch Ljava/net/URISyntaxException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/net/MalformedURLException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 1116
    :catch_0
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/io/File;->toURI()Ljava/net/URI;

    move-result-object v0

    .line 1119
    :goto_0
    invoke-virtual {p0}, Lgroovy/lang/GroovyClassLoader;->getURLs()[Ljava/net/URL;

    move-result-object p1

    .line 1120
    array-length v1, p1

    const/4 v2, 0x0

    :goto_1
    const/4 v3, 0x0

    if-ge v2, v1, :cond_1

    aget-object v4, p1, v2

    .line 1128
    :try_start_1
    invoke-virtual {v4}, Ljava/net/URL;->toURI()Ljava/net/URI;

    move-result-object v4

    invoke-virtual {v0, v4}, Ljava/net/URI;->equals(Ljava/lang/Object;)Z

    move-result v4
    :try_end_1
    .catch Ljava/net/URISyntaxException; {:try_start_1 .. :try_end_1} :catch_1

    if-eqz v4, :cond_0

    return-object v3

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    :catch_1
    move-exception p1

    .line 1131
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0

    .line 1135
    :cond_1
    :try_start_2
    invoke-virtual {v0}, Ljava/net/URI;->toURL()Ljava/net/URL;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->addURL(Ljava/net/URL;)V
    :try_end_2
    .catch Ljava/net/MalformedURLException; {:try_start_2 .. :try_end_2} :catch_2

    return-object v3

    :catch_2
    move-exception p1

    .line 1138
    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public synthetic lambda$createCollector$4$groovy-lang-GroovyClassLoader()Lgroovy/lang/GroovyClassLoader$InnerLoader;
    .locals 1

    .line 671
    new-instance v0, Lgroovy/lang/GroovyClassLoader$InnerLoader;

    invoke-direct {v0, p0}, Lgroovy/lang/GroovyClassLoader$InnerLoader;-><init>(Lgroovy/lang/GroovyClassLoader;)V

    return-object v0
.end method

.method public synthetic lambda$parseClass$2$groovy-lang-GroovyClassLoader(Ljava/io/InputStream;Ljava/lang/String;)Lgroovy/lang/GroovyCodeSource;
    .locals 3

    .line 302
    :try_start_0
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getSourceEncoding()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 303
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v0}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getSourceEncoding()Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/IOGroovyMethods;->getText(Ljava/io/InputStream;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    .line 304
    :cond_0
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/IOGroovyMethods;->getText(Ljava/io/InputStream;)Ljava/lang/String;

    move-result-object p1

    .line 305
    :goto_0
    new-instance v0, Lgroovy/lang/GroovyCodeSource;

    const-string v1, "/groovy/script"

    invoke-direct {v0, p1, p2, v1}, Lgroovy/lang/GroovyCodeSource;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v0

    :catch_0
    move-exception p1

    .line 307
    new-instance v0, Ljava/lang/RuntimeException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Impossible to read the content of the input stream for file named: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {v0, p2, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0
.end method

.method public synthetic lambda$parseClass$3$groovy-lang-GroovyClassLoader(Lgroovy/lang/GroovyCodeSource;Ljava/lang/String;)Ljava/lang/Class;
    .locals 0

    .line 332
    invoke-direct {p0, p1}, Lgroovy/lang/GroovyClassLoader;->doParseClass(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method public loadClass(Ljava/lang/String;)Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/lang/Class<",
            "*>;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 967
    invoke-virtual {p0, p1, v0}, Lgroovy/lang/GroovyClassLoader;->loadClass(Ljava/lang/String;Z)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method protected loadClass(Ljava/lang/String;Z)Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/ClassNotFoundException;
        }
    .end annotation

    const/4 v0, 0x1

    .line 979
    invoke-virtual {p0, p1, v0, v0, p2}, Lgroovy/lang/GroovyClassLoader;->loadClass(Ljava/lang/String;ZZZ)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method public loadClass(Ljava/lang/String;ZZ)Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/ClassNotFoundException;,
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 745
    invoke-virtual {p0, p1, p2, p3, v0}, Lgroovy/lang/GroovyClassLoader;->loadClass(Ljava/lang/String;ZZZ)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method public loadClass(Ljava/lang/String;ZZZ)Ljava/lang/Class;
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/ClassNotFoundException;,
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 860
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->getClassCacheEntry(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    .line 863
    invoke-virtual {p0, v0}, Lgroovy/lang/GroovyClassLoader;->isRecompilable(Ljava/lang/Class;)Z

    move-result v1

    if-nez v1, :cond_0

    return-object v0

    :cond_0
    const/4 v1, 0x0

    .line 869
    :try_start_0
    invoke-super {p0, p1, p4}, Ljava/net/URLClassLoader;->loadClass(Ljava/lang/String;Z)Ljava/lang/Class;

    move-result-object p4
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/NoClassDefFoundError; {:try_start_0 .. :try_end_0} :catch_0

    if-eq v0, p4, :cond_1

    return-object p4

    :cond_1
    move-object p4, v1

    goto :goto_0

    :catch_0
    move-exception p4

    .line 875
    invoke-virtual {p4}, Ljava/lang/NoClassDefFoundError;->getMessage()Ljava/lang/String;

    move-result-object v2

    const-string v3, "wrong name"

    invoke-virtual {v2, v3}, Ljava/lang/String;->indexOf(Ljava/lang/String;)I

    move-result v2

    if-lez v2, :cond_2

    .line 876
    new-instance p4, Ljava/lang/ClassNotFoundException;

    invoke-direct {p4, p1}, Ljava/lang/ClassNotFoundException;-><init>(Ljava/lang/String;)V

    goto :goto_0

    .line 878
    :cond_2
    throw p4

    :catch_1
    move-exception p4

    .line 883
    :goto_0
    invoke-static {}, Ljava/lang/System;->getSecurityManager()Ljava/lang/SecurityManager;

    move-result-object v2

    if-eqz v2, :cond_3

    const/16 v3, 0x2f

    const/16 v4, 0x2e

    .line 885
    invoke-virtual {p1, v3, v4}, Ljava/lang/String;->replace(CC)Ljava/lang/String;

    move-result-object v3

    .line 886
    invoke-virtual {v3, v4}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v4

    const/4 v5, -0x1

    if-eq v4, v5, :cond_3

    const-string v5, "sun.reflect."

    .line 890
    invoke-virtual {v3, v5}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v5

    if-nez v5, :cond_3

    const/4 v5, 0x0

    .line 891
    invoke-virtual {v3, v5, v4}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/SecurityManager;->checkPackageAccess(Ljava/lang/String;)V

    :cond_3
    if-eqz v0, :cond_4

    if-eqz p3, :cond_4

    return-object v0

    :cond_4
    if-eqz p2, :cond_9

    .line 904
    :try_start_1
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->getClassCacheEntry(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p2
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_3
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    if-eq p2, v0, :cond_6

    if-nez v0, :cond_5

    .line 915
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->removeClassCacheEntry(Ljava/lang/String;)V

    goto :goto_1

    .line 917
    :cond_5
    invoke-virtual {p0, v0}, Lgroovy/lang/GroovyClassLoader;->setClassCacheEntry(Ljava/lang/Class;)V

    :goto_1
    return-object p2

    .line 906
    :cond_6
    :try_start_2
    iget-object p2, p0, Lgroovy/lang/GroovyClassLoader;->resourceLoader:Lgroovy/lang/GroovyResourceLoader;

    invoke-interface {p2, p1}, Lgroovy/lang/GroovyResourceLoader;->loadGroovySource(Ljava/lang/String;)Ljava/net/URL;

    move-result-object p2
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_3
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 910
    :try_start_3
    invoke-virtual {p0, p2, p1, v0}, Lgroovy/lang/GroovyClassLoader;->recompile(Ljava/net/URL;Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Class;

    move-result-object v0
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_2
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    if-nez v0, :cond_7

    .line 915
    :goto_2
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->removeClassCacheEntry(Ljava/lang/String;)V

    goto :goto_6

    .line 917
    :cond_7
    invoke-virtual {p0, v0}, Lgroovy/lang/GroovyClassLoader;->setClassCacheEntry(Ljava/lang/Class;)V

    goto :goto_6

    :catchall_0
    move-exception p2

    move-object v0, v1

    goto :goto_4

    :catch_2
    move-exception p2

    move-object v0, v1

    goto :goto_3

    :catchall_1
    move-exception p2

    goto :goto_4

    :catch_3
    move-exception p2

    .line 912
    :goto_3
    :try_start_4
    new-instance p4, Ljava/lang/ClassNotFoundException;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "IOException while opening groovy source: "

    invoke-virtual {p3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-direct {p4, p3, p2}, Ljava/lang/ClassNotFoundException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    if-nez v0, :cond_7

    goto :goto_2

    :goto_4
    if-nez v0, :cond_8

    .line 915
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->removeClassCacheEntry(Ljava/lang/String;)V

    goto :goto_5

    .line 917
    :cond_8
    invoke-virtual {p0, v0}, Lgroovy/lang/GroovyClassLoader;->setClassCacheEntry(Ljava/lang/Class;)V

    .line 919
    :goto_5
    throw p2

    :cond_9
    :goto_6
    if-nez v0, :cond_b

    if-nez p4, :cond_a

    .line 924
    new-instance p1, Ljava/lang/AssertionError;

    const/4 p2, 0x1

    invoke-direct {p1, p2}, Ljava/lang/AssertionError;-><init>(Z)V

    throw p1

    .line 925
    :cond_a
    throw p4

    :cond_b
    return-object v0
.end method

.method public parseClass(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 314
    invoke-virtual {p1}, Lgroovy/lang/GroovyCodeSource;->isCachable()Z

    move-result v0

    invoke-virtual {p0, p1, v0}, Lgroovy/lang/GroovyClassLoader;->parseClass(Lgroovy/lang/GroovyCodeSource;Z)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method public parseClass(Lgroovy/lang/GroovyCodeSource;Z)Ljava/lang/Class;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 328
    invoke-direct {p0, p1}, Lgroovy/lang/GroovyClassLoader;->genSourceCacheKey(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/String;

    move-result-object v0

    .line 330
    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->sourceCache:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;

    new-instance v2, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda5;

    invoke-direct {v2, p0, p1}, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda5;-><init>(Lgroovy/lang/GroovyClassLoader;Lgroovy/lang/GroovyCodeSource;)V

    invoke-virtual {v1, v0, v2, p2}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->getAndPut(Ljava/lang/Object;Lorg/codehaus/groovy/runtime/memoize/MemoizeCache$ValueProvider;Z)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Class;

    return-object p1
.end method

.method public parseClass(Ljava/io/File;)Ljava/lang/Class;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;,
            Ljava/io/IOException;
        }
    .end annotation

    .line 244
    new-instance v0, Lgroovy/lang/GroovyCodeSource;

    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->config:Lorg/codehaus/groovy/control/CompilerConfiguration;

    invoke-virtual {v1}, Lorg/codehaus/groovy/control/CompilerConfiguration;->getSourceEncoding()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, p1, v1}, Lgroovy/lang/GroovyCodeSource;-><init>(Ljava/io/File;Ljava/lang/String;)V

    invoke-virtual {p0, v0}, Lgroovy/lang/GroovyClassLoader;->parseClass(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method public parseClass(Ljava/io/InputStream;Ljava/lang/String;)Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 300
    new-instance v0, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda1;

    invoke-direct {v0, p0, p1, p2}, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda1;-><init>(Lgroovy/lang/GroovyClassLoader;Ljava/io/InputStream;Ljava/lang/String;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/GroovyCodeSource;

    .line 310
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->parseClass(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method public parseClass(Ljava/io/Reader;Ljava/lang/String;)Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 280
    new-instance v0, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda3;

    invoke-direct {v0, p1, p2}, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda3;-><init>(Ljava/io/Reader;Ljava/lang/String;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/GroovyCodeSource;

    .line 288
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->parseClass(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method public parseClass(Ljava/lang/String;)Ljava/lang/Class;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 268
    :try_start_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Script_"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/EncodingGroovyMethods;->md5(Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ".groovy"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, p1, v0}, Lgroovy/lang/GroovyClassLoader;->parseClass(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p1
    :try_end_0
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 270
    new-instance v0, Lorg/codehaus/groovy/GroovyBugError;

    const-string v1, "Failed to generate md5"

    invoke-direct {v0, v1, p1}, Lorg/codehaus/groovy/GroovyBugError;-><init>(Ljava/lang/String;Ljava/lang/Exception;)V

    throw v0
.end method

.method public parseClass(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/Class;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 255
    new-instance v0, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda4;

    invoke-direct {v0, p1, p2}, Lgroovy/lang/GroovyClassLoader$$ExternalSyntheticLambda4;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/GroovyCodeSource;

    const/4 p2, 0x0

    .line 256
    invoke-virtual {p1, p2}, Lgroovy/lang/GroovyCodeSource;->setCachable(Z)V

    .line 257
    invoke-virtual {p0, p1}, Lgroovy/lang/GroovyClassLoader;->parseClass(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/Class;

    move-result-object p1

    return-object p1
.end method

.method protected recompile(Ljava/net/URL;Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Class;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;,
            Ljava/io/IOException;
        }
    .end annotation

    if-eqz p1, :cond_2

    if-eqz p3, :cond_0

    .line 947
    invoke-virtual {p0, p1, p3}, Lgroovy/lang/GroovyClassLoader;->isSourceNewer(Ljava/net/URL;Ljava/lang/Class;)Z

    move-result p2

    if-eqz p2, :cond_2

    .line 948
    :cond_0
    invoke-virtual {p1}, Ljava/net/URL;->toExternalForm()Ljava/lang/String;

    move-result-object p2

    .line 950
    iget-object p3, p0, Lgroovy/lang/GroovyClassLoader;->sourceCache:Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;

    invoke-virtual {p3, p2}, Lorg/codehaus/groovy/runtime/memoize/StampedCommonCache;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 952
    invoke-static {p1}, Lgroovy/lang/GroovyClassLoader;->isFile(Ljava/net/URL;)Z

    move-result p3

    if-eqz p3, :cond_1

    .line 954
    :try_start_0
    new-instance p3, Lgroovy/lang/GroovyCodeSource;

    new-instance v0, Ljava/io/File;

    invoke-virtual {p1}, Ljava/net/URL;->toURI()Ljava/net/URI;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/net/URI;)V

    iget-object v1, p0, Lgroovy/lang/GroovyClassLoader;->sourceEncoding:Ljava/lang/String;

    invoke-direct {p3, v0, v1}, Lgroovy/lang/GroovyCodeSource;-><init>(Ljava/io/File;Ljava/lang/String;)V

    invoke-virtual {p0, p3}, Lgroovy/lang/GroovyClassLoader;->parseClass(Lgroovy/lang/GroovyCodeSource;)Ljava/lang/Class;

    move-result-object p1
    :try_end_0
    .catch Ljava/net/URISyntaxException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    .line 959
    :catch_0
    :cond_1
    new-instance p3, Ljava/io/InputStreamReader;

    invoke-static {p1}, Lorg/codehaus/groovy/util/URLStreams;->openUncachedStream(Ljava/net/URL;)Ljava/io/InputStream;

    move-result-object p1

    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->sourceEncoding:Ljava/lang/String;

    invoke-direct {p3, p1, v0}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;Ljava/lang/String;)V

    invoke-virtual {p0, p3, p2}, Lgroovy/lang/GroovyClassLoader;->parseClass(Ljava/io/Reader;Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p1

    return-object p1

    :cond_2
    return-object p3
.end method

.method protected removeClassCacheEntry(Ljava/lang/String;)V
    .locals 1

    .line 784
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->classCache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;

    invoke-interface {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method protected setClassCacheEntry(Ljava/lang/Class;)V
    .locals 2

    .line 772
    iget-object v0, p0, Lgroovy/lang/GroovyClassLoader;->classCache:Lorg/codehaus/groovy/runtime/memoize/EvictableCache;

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1, p1}, Lorg/codehaus/groovy/runtime/memoize/EvictableCache;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public setResourceLoader(Lgroovy/lang/GroovyResourceLoader;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 190
    iput-object p1, p0, Lgroovy/lang/GroovyClassLoader;->resourceLoader:Lgroovy/lang/GroovyResourceLoader;

    return-void

    .line 188
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "Resource loader must not be null!"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public setShouldRecompile(Ljava/lang/Boolean;)V
    .locals 0

    .line 832
    iput-object p1, p0, Lgroovy/lang/GroovyClassLoader;->recompile:Ljava/lang/Boolean;

    return-void
.end method
