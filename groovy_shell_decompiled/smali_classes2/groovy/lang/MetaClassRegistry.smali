.class public interface abstract Lgroovy/lang/MetaClassRegistry;
.super Ljava/lang/Object;
.source "MetaClassRegistry.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;
    }
.end annotation


# virtual methods
.method public abstract addMetaClassRegistryChangeEventListener(Lgroovy/lang/MetaClassRegistryChangeEventListener;)V
.end method

.method public abstract addNonRemovableMetaClassRegistryChangeEventListener(Lgroovy/lang/MetaClassRegistryChangeEventListener;)V
.end method

.method public abstract getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;
.end method

.method public abstract getMetaClassCreationHandler()Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;
.end method

.method public abstract getMetaClassRegistryChangeEventListeners()[Lgroovy/lang/MetaClassRegistryChangeEventListener;
.end method

.method public abstract iterator()Ljava/util/Iterator;
.end method

.method public abstract removeMetaClass(Ljava/lang/Class;)V
.end method

.method public abstract removeMetaClassRegistryChangeEventListener(Lgroovy/lang/MetaClassRegistryChangeEventListener;)V
.end method

.method public abstract setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;)V
.end method

.method public abstract setMetaClassCreationHandle(Lgroovy/lang/MetaClassRegistry$MetaClassCreationHandle;)V
.end method
