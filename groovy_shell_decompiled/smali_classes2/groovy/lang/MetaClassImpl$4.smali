.class Lgroovy/lang/MetaClassImpl$4;
.super Lgroovy/lang/MetaProperty;
.source "MetaClassImpl.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/lang/MetaClassImpl;->getEffectiveGetMetaProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Z)Lgroovy/lang/MetaProperty;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovy/lang/MetaClassImpl;


# direct methods
.method constructor <init>(Lgroovy/lang/MetaClassImpl;Ljava/lang/String;Ljava/lang/Class;)V
    .locals 0

    .line 1984
    iput-object p1, p0, Lgroovy/lang/MetaClassImpl$4;->this$0:Lgroovy/lang/MetaClassImpl;

    invoke-direct {p0, p2, p3}, Lgroovy/lang/MetaProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;)V

    return-void
.end method


# virtual methods
.method public getProperty(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1986
    check-cast p1, Ljava/util/Map;

    iget-object v0, p0, Lgroovy/lang/MetaClassImpl$4;->name:Ljava/lang/String;

    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public setProperty(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0

    .line 1990
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method
