.class final Lgroovy/lang/ObjectRange$StepIterator;
.super Ljava/lang/Object;
.source "ObjectRange.java"

# interfaces
.implements Ljava/util/Iterator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/lang/ObjectRange;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "StepIterator"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/Iterator<",
        "Ljava/lang/Comparable;",
        ">;"
    }
.end annotation


# instance fields
.field private index:I

.field private nextFetched:Z

.field private final range:Lgroovy/lang/ObjectRange;

.field private final step:I

.field private value:Ljava/lang/Comparable;


# direct methods
.method private constructor <init>(Lgroovy/lang/ObjectRange;I)V
    .locals 2

    .line 426
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 422
    iput v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->index:I

    const/4 v0, 0x1

    .line 424
    iput-boolean v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->nextFetched:Z

    if-nez p2, :cond_1

    .line 427
    invoke-virtual {p1}, Lgroovy/lang/ObjectRange;->getFrom()Ljava/lang/Comparable;

    move-result-object v0

    invoke-virtual {p1}, Lgroovy/lang/ObjectRange;->getTo()Ljava/lang/Comparable;

    move-result-object v1

    invoke-virtual {p1, v0, v1}, Lgroovy/lang/ObjectRange;->compareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 428
    :cond_0
    new-instance p1, Lgroovy/lang/GroovyRuntimeException;

    const-string p2, "Infinite loop detected due to step size of 0"

    invoke-direct {p1, p2}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 430
    :cond_1
    :goto_0
    iput-object p1, p0, Lgroovy/lang/ObjectRange$StepIterator;->range:Lgroovy/lang/ObjectRange;

    .line 431
    invoke-virtual {p1}, Lgroovy/lang/ObjectRange;->isReverse()Z

    move-result v0

    if-eqz v0, :cond_2

    neg-int p2, p2

    .line 432
    iput p2, p0, Lgroovy/lang/ObjectRange$StepIterator;->step:I

    goto :goto_1

    .line 434
    :cond_2
    iput p2, p0, Lgroovy/lang/ObjectRange$StepIterator;->step:I

    .line 436
    :goto_1
    iget p2, p0, Lgroovy/lang/ObjectRange$StepIterator;->step:I

    if-lez p2, :cond_3

    .line 437
    invoke-virtual {p1}, Lgroovy/lang/ObjectRange;->getFrom()Ljava/lang/Comparable;

    move-result-object p1

    iput-object p1, p0, Lgroovy/lang/ObjectRange$StepIterator;->value:Ljava/lang/Comparable;

    goto :goto_2

    .line 439
    :cond_3
    invoke-virtual {p1}, Lgroovy/lang/ObjectRange;->getTo()Ljava/lang/Comparable;

    move-result-object p1

    iput-object p1, p0, Lgroovy/lang/ObjectRange$StepIterator;->value:Ljava/lang/Comparable;

    :goto_2
    return-void
.end method

.method synthetic constructor <init>(Lgroovy/lang/ObjectRange;ILgroovy/lang/ObjectRange$1;)V
    .locals 0

    .line 418
    invoke-direct {p0, p1, p2}, Lgroovy/lang/ObjectRange$StepIterator;-><init>(Lgroovy/lang/ObjectRange;I)V

    return-void
.end method

.method private peek()Ljava/lang/Comparable;
    .locals 6

    .line 470
    iget v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->step:I

    const/4 v1, 0x0

    const/4 v2, 0x0

    if-lez v0, :cond_2

    .line 471
    iget-object v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->value:Ljava/lang/Comparable;

    .line 472
    :goto_0
    iget v3, p0, Lgroovy/lang/ObjectRange$StepIterator;->step:I

    if-ge v1, v3, :cond_1

    .line 473
    iget-object v3, p0, Lgroovy/lang/ObjectRange$StepIterator;->range:Lgroovy/lang/ObjectRange;

    invoke-virtual {v3, v0}, Lgroovy/lang/ObjectRange;->increment(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Comparable;

    .line 475
    iget-object v3, p0, Lgroovy/lang/ObjectRange$StepIterator;->range:Lgroovy/lang/ObjectRange;

    invoke-static {v3}, Lgroovy/lang/ObjectRange;->access$100(Lgroovy/lang/ObjectRange;)Ljava/lang/Comparable;

    move-result-object v4

    invoke-virtual {v3, v0, v4}, Lgroovy/lang/ObjectRange;->compareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I

    move-result v3

    if-gtz v3, :cond_0

    return-object v2

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 477
    :cond_1
    iget-object v1, p0, Lgroovy/lang/ObjectRange$StepIterator;->range:Lgroovy/lang/ObjectRange;

    invoke-static {v1}, Lgroovy/lang/ObjectRange;->access$200(Lgroovy/lang/ObjectRange;)Ljava/lang/Comparable;

    move-result-object v3

    invoke-virtual {v1, v0, v3}, Lgroovy/lang/ObjectRange;->compareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I

    move-result v1

    if-gtz v1, :cond_5

    return-object v0

    :cond_2
    neg-int v0, v0

    .line 482
    iget-object v3, p0, Lgroovy/lang/ObjectRange$StepIterator;->value:Ljava/lang/Comparable;

    :goto_1
    if-ge v1, v0, :cond_4

    .line 484
    iget-object v4, p0, Lgroovy/lang/ObjectRange$StepIterator;->range:Lgroovy/lang/ObjectRange;

    invoke-virtual {v4, v3}, Lgroovy/lang/ObjectRange;->decrement(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Comparable;

    .line 486
    iget-object v4, p0, Lgroovy/lang/ObjectRange$StepIterator;->range:Lgroovy/lang/ObjectRange;

    invoke-static {v4}, Lgroovy/lang/ObjectRange;->access$200(Lgroovy/lang/ObjectRange;)Ljava/lang/Comparable;

    move-result-object v5

    invoke-virtual {v4, v3, v5}, Lgroovy/lang/ObjectRange;->compareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I

    move-result v4

    if-ltz v4, :cond_3

    return-object v2

    :cond_3
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 488
    :cond_4
    iget-object v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->range:Lgroovy/lang/ObjectRange;

    invoke-static {v0}, Lgroovy/lang/ObjectRange;->access$100(Lgroovy/lang/ObjectRange;)Ljava/lang/Comparable;

    move-result-object v1

    invoke-virtual {v0, v3, v1}, Lgroovy/lang/ObjectRange;->compareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I

    move-result v0

    if-ltz v0, :cond_5

    return-object v3

    :cond_5
    return-object v2
.end method


# virtual methods
.method public hasNext()Z
    .locals 2

    .line 462
    iget-boolean v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->nextFetched:Z

    const/4 v1, 0x1

    if-nez v0, :cond_0

    .line 463
    invoke-direct {p0}, Lgroovy/lang/ObjectRange$StepIterator;->peek()Ljava/lang/Comparable;

    move-result-object v0

    iput-object v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->value:Ljava/lang/Comparable;

    .line 464
    iput-boolean v1, p0, Lgroovy/lang/ObjectRange$StepIterator;->nextFetched:Z

    .line 466
    :cond_0
    iget-object v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->value:Ljava/lang/Comparable;

    if-eqz v0, :cond_1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    return v1
.end method

.method public next()Ljava/lang/Comparable;
    .locals 1

    .line 451
    invoke-virtual {p0}, Lgroovy/lang/ObjectRange$StepIterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    .line 454
    iput-boolean v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->nextFetched:Z

    .line 455
    iget v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->index:I

    add-int/lit8 v0, v0, 0x1

    iput v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->index:I

    .line 456
    iget-object v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->value:Ljava/lang/Comparable;

    return-object v0

    .line 452
    :cond_0
    new-instance v0, Ljava/util/NoSuchElementException;

    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    throw v0
.end method

.method public bridge synthetic next()Ljava/lang/Object;
    .locals 1

    .line 418
    invoke-virtual {p0}, Lgroovy/lang/ObjectRange$StepIterator;->next()Ljava/lang/Comparable;

    move-result-object v0

    return-object v0
.end method

.method public remove()V
    .locals 2

    .line 445
    iget-object v0, p0, Lgroovy/lang/ObjectRange$StepIterator;->range:Lgroovy/lang/ObjectRange;

    iget v1, p0, Lgroovy/lang/ObjectRange$StepIterator;->index:I

    invoke-virtual {v0, v1}, Lgroovy/lang/ObjectRange;->remove(I)Ljava/lang/Object;

    return-void
.end method
