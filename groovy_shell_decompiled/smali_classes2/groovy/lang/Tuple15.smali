.class public final Lgroovy/lang/Tuple15;
.super Lgroovy/lang/Tuple;
.source "Tuple15.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T1:",
        "Ljava/lang/Object;",
        "T2:",
        "Ljava/lang/Object;",
        "T3:",
        "Ljava/lang/Object;",
        "T4:",
        "Ljava/lang/Object;",
        "T5:",
        "Ljava/lang/Object;",
        "T6:",
        "Ljava/lang/Object;",
        "T7:",
        "Ljava/lang/Object;",
        "T8:",
        "Ljava/lang/Object;",
        "T9:",
        "Ljava/lang/Object;",
        "T10:",
        "Ljava/lang/Object;",
        "T11:",
        "Ljava/lang/Object;",
        "T12:",
        "Ljava/lang/Object;",
        "T13:",
        "Ljava/lang/Object;",
        "T14:",
        "Ljava/lang/Object;",
        "T15:",
        "Ljava/lang/Object;",
        ">",
        "Lgroovy/lang/Tuple;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = -0x7b0bb7c90445bb50L


# instance fields
.field private final v1:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT1;"
        }
    .end annotation
.end field

.field private final v10:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT10;"
        }
    .end annotation
.end field

.field private final v11:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT11;"
        }
    .end annotation
.end field

.field private final v12:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT12;"
        }
    .end annotation
.end field

.field private final v13:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT13;"
        }
    .end annotation
.end field

.field private final v14:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT14;"
        }
    .end annotation
.end field

.field private final v15:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT15;"
        }
    .end annotation
.end field

.field private final v2:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT2;"
        }
    .end annotation
.end field

.field private final v3:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT3;"
        }
    .end annotation
.end field

.field private final v4:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT4;"
        }
    .end annotation
.end field

.field private final v5:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT5;"
        }
    .end annotation
.end field

.field private final v6:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT6;"
        }
    .end annotation
.end field

.field private final v7:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT7;"
        }
    .end annotation
.end field

.field private final v8:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT8;"
        }
    .end annotation
.end field

.field private final v9:Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT9;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lgroovy/lang/Tuple15;)V
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/lang/Tuple15<",
            "TT1;TT2;TT3;TT4;TT5;TT6;TT7;TT8;TT9;TT10;TT11;TT12;TT13;TT14;TT15;>;)V"
        }
    .end annotation

    move-object/from16 v0, p1

    .line 67
    iget-object v1, v0, Lgroovy/lang/Tuple15;->v1:Ljava/lang/Object;

    iget-object v2, v0, Lgroovy/lang/Tuple15;->v2:Ljava/lang/Object;

    iget-object v3, v0, Lgroovy/lang/Tuple15;->v3:Ljava/lang/Object;

    iget-object v4, v0, Lgroovy/lang/Tuple15;->v4:Ljava/lang/Object;

    iget-object v5, v0, Lgroovy/lang/Tuple15;->v5:Ljava/lang/Object;

    iget-object v6, v0, Lgroovy/lang/Tuple15;->v6:Ljava/lang/Object;

    iget-object v7, v0, Lgroovy/lang/Tuple15;->v7:Ljava/lang/Object;

    iget-object v8, v0, Lgroovy/lang/Tuple15;->v8:Ljava/lang/Object;

    iget-object v9, v0, Lgroovy/lang/Tuple15;->v9:Ljava/lang/Object;

    iget-object v10, v0, Lgroovy/lang/Tuple15;->v10:Ljava/lang/Object;

    iget-object v11, v0, Lgroovy/lang/Tuple15;->v11:Ljava/lang/Object;

    iget-object v12, v0, Lgroovy/lang/Tuple15;->v12:Ljava/lang/Object;

    iget-object v13, v0, Lgroovy/lang/Tuple15;->v13:Ljava/lang/Object;

    iget-object v14, v0, Lgroovy/lang/Tuple15;->v14:Ljava/lang/Object;

    iget-object v15, v0, Lgroovy/lang/Tuple15;->v15:Ljava/lang/Object;

    move-object/from16 v0, p0

    invoke-direct/range {v0 .. v15}, Lgroovy/lang/Tuple15;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 17
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT1;TT2;TT3;TT4;TT5;TT6;TT7;TT8;TT9;TT10;TT11;TT12;TT13;TT14;TT15;)V"
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p2

    move-object/from16 v3, p3

    move-object/from16 v4, p4

    move-object/from16 v5, p5

    move-object/from16 v6, p6

    move-object/from16 v7, p7

    move-object/from16 v8, p8

    move-object/from16 v9, p9

    move-object/from16 v10, p10

    move-object/from16 v11, p11

    move-object/from16 v12, p12

    move-object/from16 v13, p13

    move-object/from16 v14, p14

    move-object/from16 v15, p15

    const/16 v0, 0xf

    new-array v0, v0, [Ljava/lang/Object;

    const/16 v16, 0x0

    aput-object v1, v0, v16

    const/16 v16, 0x1

    aput-object v2, v0, v16

    const/16 v16, 0x2

    aput-object v3, v0, v16

    const/16 v16, 0x3

    aput-object v4, v0, v16

    const/16 v16, 0x4

    aput-object v5, v0, v16

    const/16 v16, 0x5

    aput-object v6, v0, v16

    const/16 v16, 0x6

    aput-object v7, v0, v16

    const/16 v16, 0x7

    aput-object v8, v0, v16

    const/16 v16, 0x8

    aput-object v9, v0, v16

    const/16 v16, 0x9

    aput-object v10, v0, v16

    const/16 v16, 0xa

    aput-object v11, v0, v16

    const/16 v16, 0xb

    aput-object v12, v0, v16

    const/16 v16, 0xc

    aput-object v13, v0, v16

    const/16 v16, 0xd

    aput-object v14, v0, v16

    const/16 v16, 0xe

    aput-object v15, v0, v16

    move-object/from16 v15, p0

    .line 47
    invoke-direct {v15, v0}, Lgroovy/lang/Tuple;-><init>([Ljava/lang/Object;)V

    .line 49
    iput-object v1, v15, Lgroovy/lang/Tuple15;->v1:Ljava/lang/Object;

    .line 50
    iput-object v2, v15, Lgroovy/lang/Tuple15;->v2:Ljava/lang/Object;

    .line 51
    iput-object v3, v15, Lgroovy/lang/Tuple15;->v3:Ljava/lang/Object;

    .line 52
    iput-object v4, v15, Lgroovy/lang/Tuple15;->v4:Ljava/lang/Object;

    .line 53
    iput-object v5, v15, Lgroovy/lang/Tuple15;->v5:Ljava/lang/Object;

    .line 54
    iput-object v6, v15, Lgroovy/lang/Tuple15;->v6:Ljava/lang/Object;

    .line 55
    iput-object v7, v15, Lgroovy/lang/Tuple15;->v7:Ljava/lang/Object;

    .line 56
    iput-object v8, v15, Lgroovy/lang/Tuple15;->v8:Ljava/lang/Object;

    .line 57
    iput-object v9, v15, Lgroovy/lang/Tuple15;->v9:Ljava/lang/Object;

    .line 58
    iput-object v10, v15, Lgroovy/lang/Tuple15;->v10:Ljava/lang/Object;

    .line 59
    iput-object v11, v15, Lgroovy/lang/Tuple15;->v11:Ljava/lang/Object;

    .line 60
    iput-object v12, v15, Lgroovy/lang/Tuple15;->v12:Ljava/lang/Object;

    .line 61
    iput-object v13, v15, Lgroovy/lang/Tuple15;->v13:Ljava/lang/Object;

    .line 62
    iput-object v14, v15, Lgroovy/lang/Tuple15;->v14:Ljava/lang/Object;

    move-object/from16 v1, p15

    move-object v0, v15

    .line 63
    iput-object v1, v0, Lgroovy/lang/Tuple15;->v15:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public clone()Lgroovy/lang/Tuple15;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovy/lang/Tuple15<",
            "TT1;TT2;TT3;TT4;TT5;TT6;TT7;TT8;TT9;TT10;TT11;TT12;TT13;TT14;TT15;>;"
        }
    .end annotation

    .line 132
    new-instance v0, Lgroovy/lang/Tuple15;

    invoke-direct {v0, p0}, Lgroovy/lang/Tuple15;-><init>(Lgroovy/lang/Tuple15;)V

    return-object v0
.end method

.method public bridge synthetic clone()Lgroovy/lang/Tuple;
    .locals 1

    .line 28
    invoke-virtual {p0}, Lgroovy/lang/Tuple15;->clone()Lgroovy/lang/Tuple15;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/CloneNotSupportedException;
        }
    .end annotation

    .line 28
    invoke-virtual {p0}, Lgroovy/lang/Tuple15;->clone()Lgroovy/lang/Tuple15;

    move-result-object v0

    return-object v0
.end method

.method public getV1()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT1;"
        }
    .end annotation

    .line 71
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v1:Ljava/lang/Object;

    return-object v0
.end method

.method public getV10()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT10;"
        }
    .end annotation

    .line 107
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v10:Ljava/lang/Object;

    return-object v0
.end method

.method public getV11()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT11;"
        }
    .end annotation

    .line 111
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v11:Ljava/lang/Object;

    return-object v0
.end method

.method public getV12()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT12;"
        }
    .end annotation

    .line 115
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v12:Ljava/lang/Object;

    return-object v0
.end method

.method public getV13()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT13;"
        }
    .end annotation

    .line 119
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v13:Ljava/lang/Object;

    return-object v0
.end method

.method public getV14()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT14;"
        }
    .end annotation

    .line 123
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v14:Ljava/lang/Object;

    return-object v0
.end method

.method public getV15()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT15;"
        }
    .end annotation

    .line 127
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v15:Ljava/lang/Object;

    return-object v0
.end method

.method public getV2()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT2;"
        }
    .end annotation

    .line 75
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v2:Ljava/lang/Object;

    return-object v0
.end method

.method public getV3()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT3;"
        }
    .end annotation

    .line 79
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v3:Ljava/lang/Object;

    return-object v0
.end method

.method public getV4()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT4;"
        }
    .end annotation

    .line 83
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v4:Ljava/lang/Object;

    return-object v0
.end method

.method public getV5()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT5;"
        }
    .end annotation

    .line 87
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v5:Ljava/lang/Object;

    return-object v0
.end method

.method public getV6()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT6;"
        }
    .end annotation

    .line 91
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v6:Ljava/lang/Object;

    return-object v0
.end method

.method public getV7()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT7;"
        }
    .end annotation

    .line 95
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v7:Ljava/lang/Object;

    return-object v0
.end method

.method public getV8()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT8;"
        }
    .end annotation

    .line 99
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v8:Ljava/lang/Object;

    return-object v0
.end method

.method public getV9()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT9;"
        }
    .end annotation

    .line 103
    iget-object v0, p0, Lgroovy/lang/Tuple15;->v9:Ljava/lang/Object;

    return-object v0
.end method
