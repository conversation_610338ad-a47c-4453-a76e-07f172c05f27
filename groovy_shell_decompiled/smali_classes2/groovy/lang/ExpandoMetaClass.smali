.class public Lgroovy/lang/ExpandoMetaClass;
.super Lgroovy/lang/MetaClassImpl;
.source "ExpandoMetaClass.java"

# interfaces
.implements Lgroovy/lang/GroovyObject;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/lang/ExpandoMetaClass$Callable;,
        Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;,
        Lgroovy/lang/ExpandoMetaClass$ExpandoMetaConstructor;,
        Lgroovy/lang/ExpandoMetaClass$DefiningClosure;,
        Lgroovy/lang/ExpandoMetaClass$MixedInAccessor;,
        Lgroovy/lang/ExpandoMetaClass$StaticDefiningClosure;,
        Lgroovy/lang/ExpandoMetaClass$SubClassDefiningClosure;
    }
.end annotation


# static fields
.field private static final CLASS:Ljava/lang/String; = "class"

.field private static final CLASS_PROPERTY:Ljava/lang/String; = "class"

.field public static final CONSTRUCTOR:Ljava/lang/String; = "constructor"

.field private static final EMPTY_CLASS_ARRAY:[Ljava/lang/Class;

.field private static final GROOVY_CONSTRUCTOR:Ljava/lang/String; = "<init>"

.field private static final META_CLASS:Ljava/lang/String; = "metaClass"

.field private static final META_CLASS_PROPERTY:Ljava/lang/String; = "metaClass"

.field private static final META_METHODS:Ljava/lang/String; = "metaMethods"

.field private static final METHODS:Ljava/lang/String; = "methods"

.field private static final PROPERTIES:Ljava/lang/String; = "properties"

.field public static final STATIC_QUALIFIER:Ljava/lang/String; = "static"


# instance fields
.field private final allowChangesAfterInit:Z

.field private final beanPropertyCache:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovy/lang/MetaProperty;",
            ">;"
        }
    .end annotation
.end field

.field private final expandoMethods:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lorg/codehaus/groovy/runtime/MethodKey;",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation
.end field

.field private final expandoProperties:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovy/lang/MetaProperty;",
            ">;"
        }
    .end annotation
.end field

.field private final expandoSubclassMethods:Ljava/util/concurrent/ConcurrentHashMap;

.field public inRegistry:Z

.field private final inheritedMetaMethods:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation
.end field

.field private initCalled:Z

.field private volatile initialized:Z

.field private invokeStaticMethodMethod:Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

.field private final mixinClasses:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Lorg/codehaus/groovy/reflection/MixinInMetaClass;",
            ">;"
        }
    .end annotation
.end field

.field private volatile modified:Z

.field private myMetaClass:Lgroovy/lang/MetaClass;

.field private final readLock:Ljava/util/concurrent/locks/Lock;

.field private final rwl:Ljava/util/concurrent/locks/ReentrantReadWriteLock;

.field private final staticBeanPropertyCache:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lgroovy/lang/MetaProperty;",
            ">;"
        }
    .end annotation
.end field

.field private final writeLock:Ljava/util/concurrent/locks/Lock;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/Class;

    .line 259
    sput-object v0, Lgroovy/lang/ExpandoMetaClass;->EMPTY_CLASS_ARRAY:[Ljava/lang/Class;

    return-void
.end method

.method public constructor <init>(Lgroovy/lang/MetaClassRegistry;Ljava/lang/Class;ZZ[Lgroovy/lang/MetaMethod;)V
    .locals 1

    .line 305
    invoke-direct {p0, p1, p2, p5}, Lgroovy/lang/MetaClassImpl;-><init>(Lgroovy/lang/MetaClassRegistry;Ljava/lang/Class;[Lgroovy/lang/MetaMethod;)V

    .line 279
    new-instance p1, Ljava/util/concurrent/locks/ReentrantReadWriteLock;

    invoke-direct {p1}, Ljava/util/concurrent/locks/ReentrantReadWriteLock;-><init>()V

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->rwl:Ljava/util/concurrent/locks/ReentrantReadWriteLock;

    .line 280
    invoke-virtual {p1}, Ljava/util/concurrent/locks/ReentrantReadWriteLock;->readLock()Ljava/util/concurrent/locks/ReentrantReadWriteLock$ReadLock;

    move-result-object p2

    iput-object p2, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    .line 281
    invoke-virtual {p1}, Ljava/util/concurrent/locks/ReentrantReadWriteLock;->writeLock()Ljava/util/concurrent/locks/ReentrantReadWriteLock$WriteLock;

    move-result-object p1

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->writeLock:Ljava/util/concurrent/locks/Lock;

    .line 286
    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->inheritedMetaMethods:Ljava/util/Set;

    .line 287
    new-instance p1, Ljava/util/concurrent/ConcurrentHashMap;

    const/16 p2, 0x10

    const/high16 p5, 0x3f400000    # 0.75f

    const/4 v0, 0x1

    invoke-direct {p1, p2, p5, v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>(IFI)V

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->beanPropertyCache:Ljava/util/Map;

    .line 288
    new-instance p1, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {p1, p2, p5, v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>(IFI)V

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->staticBeanPropertyCache:Ljava/util/Map;

    .line 289
    new-instance p1, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {p1, p2, p5, v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>(IFI)V

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoMethods:Ljava/util/Map;

    .line 295
    new-instance p1, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {p1, p2, p5, v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>(IFI)V

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoSubclassMethods:Ljava/util/concurrent/ConcurrentHashMap;

    .line 296
    new-instance p1, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {p1, p2, p5, v0}, Ljava/util/concurrent/ConcurrentHashMap;-><init>(IFI)V

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoProperties:Ljava/util/Map;

    .line 298
    new-instance p1, Ljava/util/LinkedHashSet;

    invoke-direct {p1}, Ljava/util/LinkedHashSet;-><init>()V

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->mixinClasses:Ljava/util/Set;

    .line 306
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object p1

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->myMetaClass:Lgroovy/lang/MetaClass;

    .line 307
    iput-boolean p3, p0, Lgroovy/lang/ExpandoMetaClass;->inRegistry:Z

    .line 308
    iput-boolean p4, p0, Lgroovy/lang/ExpandoMetaClass;->allowChangesAfterInit:Z

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;)V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x0

    .line 317
    invoke-direct {p0, p1, v0, v0, v1}, Lgroovy/lang/ExpandoMetaClass;-><init>(Ljava/lang/Class;ZZ[Lgroovy/lang/MetaMethod;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;Z)V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x0

    .line 332
    invoke-direct {p0, p1, p2, v0, v1}, Lgroovy/lang/ExpandoMetaClass;-><init>(Ljava/lang/Class;ZZ[Lgroovy/lang/MetaMethod;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;ZZ)V
    .locals 1

    const/4 v0, 0x0

    .line 348
    invoke-direct {p0, p1, p2, p3, v0}, Lgroovy/lang/ExpandoMetaClass;-><init>(Ljava/lang/Class;ZZ[Lgroovy/lang/MetaMethod;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;ZZ[Lgroovy/lang/MetaMethod;)V
    .locals 6

    .line 301
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v1

    move-object v0, p0

    move-object v2, p1

    move v3, p2

    move v4, p3

    move-object v5, p4

    invoke-direct/range {v0 .. v5}, Lgroovy/lang/ExpandoMetaClass;-><init>(Lgroovy/lang/MetaClassRegistry;Ljava/lang/Class;ZZ[Lgroovy/lang/MetaMethod;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;Z[Lgroovy/lang/MetaMethod;)V
    .locals 1

    const/4 v0, 0x0

    .line 336
    invoke-direct {p0, p1, p2, v0, p3}, Lgroovy/lang/ExpandoMetaClass;-><init>(Ljava/lang/Class;ZZ[Lgroovy/lang/MetaMethod;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;[Lgroovy/lang/MetaMethod;)V
    .locals 1

    const/4 v0, 0x0

    .line 321
    invoke-direct {p0, p1, v0, v0, p2}, Lgroovy/lang/ExpandoMetaClass;-><init>(Ljava/lang/Class;ZZ[Lgroovy/lang/MetaMethod;)V

    return-void
.end method

.method static synthetic access$000(Lgroovy/lang/ExpandoMetaClass;)Ljava/util/Set;
    .locals 0

    .line 257
    iget-object p0, p0, Lgroovy/lang/ExpandoMetaClass;->inheritedMetaMethods:Ljava/util/Set;

    return-object p0
.end method

.method static synthetic access$100(Lgroovy/lang/ExpandoMetaClass;)Ljava/util/Map;
    .locals 0

    .line 257
    iget-object p0, p0, Lgroovy/lang/ExpandoMetaClass;->expandoMethods:Ljava/util/Map;

    return-object p0
.end method

.method private addSuperMethodIfNotOverridden(Lgroovy/lang/MetaMethod;)V
    .locals 1

    .line 521
    new-instance v0, Lgroovy/lang/ExpandoMetaClass$1;

    invoke-direct {v0, p0, p1}, Lgroovy/lang/ExpandoMetaClass$1;-><init>(Lgroovy/lang/ExpandoMetaClass;Lgroovy/lang/MetaMethod;)V

    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->performOperationOnMetaClass(Lgroovy/lang/ExpandoMetaClass$Callable;)V

    return-void
.end method

.method public static disableGlobally()V
    .locals 1

    const/4 v0, 0x1

    .line 477
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/metaclass/DefaultMetaClassInfo;->setWithoutCustomMetaclassCreationHandle(Z)V

    .line 478
    invoke-static {}, Lgroovy/lang/ExpandoMetaClassCreationHandle;->disable()V

    return-void
.end method

.method public static enableGlobally()V
    .locals 1

    const/4 v0, 0x0

    .line 469
    invoke-static {v0}, Lorg/codehaus/groovy/runtime/metaclass/DefaultMetaClassInfo;->setWithoutCustomMetaclassCreationHandle(Z)V

    .line 470
    invoke-static {}, Lgroovy/lang/ExpandoMetaClassCreationHandle;->enable()V

    return-void
.end method

.method private getPropertyForGetter(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    const/4 v0, 0x0

    if-eqz p1, :cond_2

    .line 1253
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    const-string v1, "get"

    .line 1255
    invoke-virtual {p1, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_1

    const/4 v0, 0x3

    .line 1256
    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1257
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->convertPropertyName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_1
    const-string v1, "is"

    .line 1259
    invoke-virtual {p1, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_2

    const/4 v0, 0x2

    .line 1260
    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1261
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->convertPropertyName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_2
    :goto_0
    return-object v0
.end method

.method private hasOverrideGetProperty(Ljava/lang/String;)Z
    .locals 1

    .line 1166
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->getPropertyMethod:Lgroovy/lang/MetaMethod;

    if-eqz v0, :cond_0

    const-string v0, "metaClass"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "class"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method private isGetter(Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;)Z
    .locals 2

    const/4 v0, 0x0

    if-eqz p1, :cond_3

    .line 1232
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v1

    if-eqz v1, :cond_3

    if-nez p2, :cond_0

    goto :goto_0

    .line 1233
    :cond_0
    array-length p2, p2

    if-eqz p2, :cond_1

    return v0

    :cond_1
    const-string p2, "get"

    .line 1235
    invoke-virtual {p1, p2}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result p2

    if-eqz p2, :cond_2

    const/4 p2, 0x3

    .line 1236
    invoke-virtual {p1, p2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1237
    invoke-static {p1}, Lgroovy/lang/ExpandoMetaClass;->isPropertyName(Ljava/lang/String;)Z

    move-result p1

    return p1

    :cond_2
    const-string p2, "is"

    .line 1239
    invoke-virtual {p1, p2}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result p2

    if-eqz p2, :cond_3

    const/4 p2, 0x2

    .line 1240
    invoke-virtual {p1, p2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1241
    invoke-static {p1}, Lgroovy/lang/ExpandoMetaClass;->isPropertyName(Ljava/lang/String;)Z

    move-result p1

    return p1

    :cond_3
    :goto_0
    return v0
.end method

.method private static isPropertyName(Ljava/lang/String;)Z
    .locals 3

    .line 1221
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-lez v0, :cond_0

    invoke-virtual {p0, v1}, Ljava/lang/String;->charAt(I)C

    move-result v0

    invoke-static {v0}, Ljava/lang/Character;->isUpperCase(C)Z

    move-result v0

    if-nez v0, :cond_1

    :cond_0
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    if-le v0, v2, :cond_2

    invoke-virtual {p0, v2}, Ljava/lang/String;->charAt(I)C

    move-result p0

    invoke-static {p0}, Ljava/lang/Character;->isUpperCase(C)Z

    move-result p0

    if-eqz p0, :cond_2

    :cond_1
    move v1, v2

    :cond_2
    return v1
.end method

.method public static isValidExpandoProperty(Ljava/lang/String;)Z
    .locals 1

    const-string v0, "metaClass"

    .line 738
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "class"

    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "metaMethods"

    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "methods"

    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "properties"

    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-nez p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method private performRegistryCallbacks()V
    .locals 3

    .line 934
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v0

    .line 935
    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass;->incVersion()V

    .line 936
    iget-boolean v1, p0, Lgroovy/lang/ExpandoMetaClass;->modified:Z

    if-nez v1, :cond_1

    const/4 v1, 0x1

    .line 937
    iput-boolean v1, p0, Lgroovy/lang/ExpandoMetaClass;->modified:Z

    .line 942
    iget-boolean v1, p0, Lgroovy/lang/ExpandoMetaClass;->inRegistry:Z

    if-eqz v1, :cond_1

    .line 943
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-interface {v0, v1}, Lgroovy/lang/MetaClassRegistry;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object v1

    .line 944
    instance-of v2, v1, Lgroovy/lang/ExpandoMetaClass;

    if-nez v2, :cond_0

    instance-of v2, v1, Lgroovy/lang/AdaptingMetaClass;

    if-eqz v2, :cond_0

    .line 945
    check-cast v1, Lgroovy/lang/AdaptingMetaClass;

    invoke-interface {v1, p0}, Lgroovy/lang/AdaptingMetaClass;->setAdaptee(Lgroovy/lang/MetaClass;)V

    goto :goto_0

    .line 947
    :cond_0
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-interface {v0, v1, p0}, Lgroovy/lang/MetaClassRegistry;->setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;)V

    :cond_1
    :goto_0
    return-void
.end method

.method private refreshInheritedMethods(Lgroovy/lang/ExpandoMetaClass;)V
    .locals 4

    .line 1073
    invoke-virtual {p1}, Lgroovy/lang/ExpandoMetaClass;->getExpandoMethods()Ljava/util/List;

    move-result-object v0

    .line 1074
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/MetaMethod;

    .line 1075
    invoke-virtual {v1}, Lgroovy/lang/MetaMethod;->isStatic()Z

    move-result v2

    if-eqz v2, :cond_1

    .line 1076
    invoke-virtual {p1}, Lgroovy/lang/ExpandoMetaClass;->getTheClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass;->getTheClass()Ljava/lang/Class;

    move-result-object v3

    if-eq v2, v3, :cond_0

    goto :goto_0

    .line 1078
    :cond_0
    invoke-virtual {v1}, Lgroovy/lang/MetaMethod;->getName()Ljava/lang/String;

    move-result-object v2

    check-cast v1, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

    invoke-virtual {v1}, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;->getClosure()Lgroovy/lang/Closure;

    move-result-object v1

    invoke-virtual {v1}, Lgroovy/lang/Closure;->clone()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/Closure;

    invoke-virtual {p0, v2, v1}, Lgroovy/lang/ExpandoMetaClass;->registerStaticMethod(Ljava/lang/String;Lgroovy/lang/Closure;)V

    goto :goto_0

    .line 1080
    :cond_1
    invoke-direct {p0, v1}, Lgroovy/lang/ExpandoMetaClass;->addSuperMethodIfNotOverridden(Lgroovy/lang/MetaMethod;)V

    goto :goto_0

    .line 1082
    :cond_2
    invoke-virtual {p1}, Lgroovy/lang/ExpandoMetaClass;->getExpandoProperties()Ljava/util/Collection;

    move-result-object p1

    .line 1083
    invoke-interface {p1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    .line 1084
    check-cast v0, Lgroovy/lang/MetaBeanProperty;

    .line 1085
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoProperties:Ljava/util/Map;

    invoke-virtual {v0}, Lgroovy/lang/MetaBeanProperty;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 1086
    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->addMetaBeanProperty(Lgroovy/lang/MetaBeanProperty;)V

    goto :goto_1

    :cond_3
    return-void
.end method

.method private registerBeanPropertyForMethod(Lgroovy/lang/MetaMethod;Ljava/lang/String;ZZ)V
    .locals 4

    if-eqz p4, :cond_0

    .line 955
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->staticBeanPropertyCache:Ljava/util/Map;

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->beanPropertyCache:Ljava/util/Map;

    .line 956
    :goto_0
    invoke-interface {v0, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lgroovy/lang/MetaBeanProperty;

    if-nez v1, :cond_1

    .line 958
    invoke-super {p0, p2}, Lgroovy/lang/MetaClassImpl;->getMetaProperty(Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object v2

    .line 959
    instance-of v3, v2, Lgroovy/lang/MetaBeanProperty;

    if-eqz v3, :cond_1

    .line 960
    invoke-virtual {v2}, Lgroovy/lang/MetaProperty;->getModifiers()I

    move-result v3

    invoke-static {v3}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    move-result v3

    if-ne p4, v3, :cond_1

    .line 962
    move-object v1, v2

    check-cast v1, Lgroovy/lang/MetaBeanProperty;

    :cond_1
    if-nez v1, :cond_3

    const/4 p4, 0x0

    if-eqz p3, :cond_2

    .line 968
    new-instance p3, Lgroovy/lang/MetaBeanProperty;

    const-class v1, Ljava/lang/Object;

    invoke-direct {p3, p2, v1, p1, p4}, Lgroovy/lang/MetaBeanProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)V

    goto :goto_1

    .line 970
    :cond_2
    new-instance p3, Lgroovy/lang/MetaBeanProperty;

    const-class v1, Ljava/lang/Object;

    invoke-direct {p3, p2, v1, p4, p1}, Lgroovy/lang/MetaBeanProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)V

    .line 972
    :goto_1
    invoke-interface {v0, p2, p3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_4

    :cond_3
    const/4 p4, 0x0

    if-eqz p3, :cond_5

    .line 975
    invoke-virtual {v1}, Lgroovy/lang/MetaBeanProperty;->getSetter()Lgroovy/lang/MetaMethod;

    move-result-object p3

    if-eqz p3, :cond_4

    .line 976
    invoke-virtual {p3}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v1

    aget-object p4, v1, p4

    invoke-virtual {p4}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object p4

    goto :goto_2

    :cond_4
    const-class p4, Ljava/lang/Object;

    .line 977
    :goto_2
    new-instance v1, Lgroovy/lang/MetaBeanProperty;

    invoke-direct {v1, p2, p4, p1, p3}, Lgroovy/lang/MetaBeanProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)V

    .line 978
    invoke-interface {v0, p2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_3

    .line 980
    :cond_5
    invoke-virtual {v1}, Lgroovy/lang/MetaBeanProperty;->getGetter()Lgroovy/lang/MetaMethod;

    move-result-object p3

    .line 981
    new-instance v1, Lgroovy/lang/MetaBeanProperty;

    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v2

    aget-object p4, v2, p4

    invoke-virtual {p4}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object p4

    invoke-direct {v1, p2, p4, p3, p1}, Lgroovy/lang/MetaBeanProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/MetaMethod;Lgroovy/lang/MetaMethod;)V

    .line 982
    invoke-interface {v0, p2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_3
    move-object p3, v1

    .line 985
    :goto_4
    iget-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoProperties:Ljava/util/Map;

    invoke-virtual {p3}, Lgroovy/lang/MetaBeanProperty;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2, p3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 986
    invoke-virtual {p0, p3}, Lgroovy/lang/ExpandoMetaClass;->addMetaBeanProperty(Lgroovy/lang/MetaBeanProperty;)V

    return-void
.end method


# virtual methods
.method public addMixinClass(Lorg/codehaus/groovy/reflection/MixinInMetaClass;)V
    .locals 1

    .line 444
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->mixinClasses:Ljava/util/Set;

    invoke-interface {v0, p1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    return-void
.end method

.method public castToMixedType(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;
    .locals 3

    .line 448
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->mixinClasses:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/reflection/MixinInMetaClass;

    .line 449
    invoke-virtual {v1}, Lorg/codehaus/groovy/reflection/MixinInMetaClass;->getMixinClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {p2, v2}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 450
    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/reflection/MixinInMetaClass;->getMixinInstance(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method protected checkInitalised()V
    .locals 2

    .line 836
    :try_start_0
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->lock()V

    .line 837
    invoke-super {p0}, Lgroovy/lang/MetaClassImpl;->checkInitalised()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 839
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->unlock()V

    return-void

    :catchall_0
    move-exception v0

    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v1}, Ljava/util/concurrent/locks/Lock;->unlock()V

    .line 840
    throw v0
.end method

.method public createConstructorSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 4

    .line 1331
    invoke-static {p2}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->convertToTypeArray([Ljava/lang/Object;)[Ljava/lang/Class;

    move-result-object v0

    const-string v1, "<init>"

    .line 1332
    invoke-virtual {p0, v1, v0}, Lgroovy/lang/ExpandoMetaClass;->pickMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 1333
    invoke-virtual {v1}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v2

    array-length v2, v2

    array-length v3, p2

    if-ne v2, v3, :cond_0

    .line 1334
    invoke-virtual {v1}, Lgroovy/lang/MetaMethod;->getDeclaringClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v2

    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass;->getTheClass()Ljava/lang/Class;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    .line 1335
    new-instance p2, Lorg/codehaus/groovy/runtime/callsite/ConstructorMetaMethodSite;

    invoke-direct {p2, p1, p0, v1, v0}, Lorg/codehaus/groovy/runtime/callsite/ConstructorMetaMethodSite;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;[Ljava/lang/Class;)V

    return-object p2

    .line 1339
    :cond_0
    invoke-super {p0, p1, p2}, Lgroovy/lang/MetaClassImpl;->createConstructorSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1
.end method

.method public createPogoCallCurrentSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 0

    .line 1317
    iget-object p3, p0, Lgroovy/lang/ExpandoMetaClass;->invokeMethodMethod:Lgroovy/lang/MetaMethod;

    if-eqz p3, :cond_0

    .line 1318
    new-instance p2, Lorg/codehaus/groovy/runtime/callsite/PogoMetaClassSite;

    invoke-direct {p2, p1, p0}, Lorg/codehaus/groovy/runtime/callsite/PogoMetaClassSite;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClass;)V

    return-object p2

    .line 1319
    :cond_0
    invoke-super {p0, p1, p2, p4}, Lgroovy/lang/MetaClassImpl;->createPogoCallCurrentSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;Ljava/lang/Class;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1
.end method

.method public createPogoCallSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 1

    .line 1311
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->invokeMethodMethod:Lgroovy/lang/MetaMethod;

    if-eqz v0, :cond_0

    .line 1312
    new-instance p2, Lorg/codehaus/groovy/runtime/callsite/PogoMetaClassSite;

    invoke-direct {p2, p1, p0}, Lorg/codehaus/groovy/runtime/callsite/PogoMetaClassSite;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClass;)V

    return-object p2

    .line 1313
    :cond_0
    invoke-super {p0, p1, p2}, Lgroovy/lang/MetaClassImpl;->createPogoCallSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1
.end method

.method public createPojoCallSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 1

    .line 1295
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->invokeMethodMethod:Lgroovy/lang/MetaMethod;

    if-eqz v0, :cond_0

    .line 1296
    new-instance p2, Lorg/codehaus/groovy/runtime/callsite/PojoMetaClassSite;

    invoke-direct {p2, p1, p0}, Lorg/codehaus/groovy/runtime/callsite/PojoMetaClassSite;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClass;)V

    return-object p2

    .line 1298
    :cond_0
    invoke-super {p0, p1, p2, p3}, Lgroovy/lang/MetaClassImpl;->createPojoCallSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;Ljava/lang/Object;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1
.end method

.method public createStaticSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;
    .locals 1

    .line 1302
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->invokeStaticMethodMethod:Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

    if-eqz v0, :cond_0

    .line 1303
    new-instance p2, Lorg/codehaus/groovy/runtime/callsite/StaticMetaClassSite;

    invoke-direct {p2, p1, p0}, Lorg/codehaus/groovy/runtime/callsite/StaticMetaClassSite;-><init>(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/MetaClass;)V

    return-object p2

    .line 1305
    :cond_0
    invoke-super {p0, p1, p2}, Lgroovy/lang/MetaClassImpl;->createStaticSite(Lorg/codehaus/groovy/runtime/callsite/CallSite;[Ljava/lang/Object;)Lorg/codehaus/groovy/runtime/callsite/CallSite;

    move-result-object p1

    return-object p1
.end method

.method public define(Lgroovy/lang/Closure;)Lgroovy/lang/ExpandoMetaClass;
    .locals 4
    .param p1    # Lgroovy/lang/Closure;
        .annotation runtime Lgroovy/lang/DelegatesTo;
            strategy = 0x3
            value = Lgroovy/lang/ExpandoMetaClass$DefiningClosure;
        .end annotation

        .annotation runtime Lgroovy/transform/stc/ClosureParams;
            options = {
                "java.lang.Object"
            }
            value = Lgroovy/transform/stc/SimpleType;
        .end annotation
    .end param

    .line 804
    new-instance v0, Lgroovy/lang/ExpandoMetaClass$DefiningClosure;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lgroovy/lang/ExpandoMetaClass$DefiningClosure;-><init>(Lgroovy/lang/ExpandoMetaClass;Lgroovy/lang/ExpandoMetaClass$1;)V

    .line 805
    invoke-virtual {p1}, Lgroovy/lang/Closure;->getDelegate()Ljava/lang/Object;

    move-result-object v2

    .line 806
    invoke-virtual {p1, v0}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    const/4 v3, 0x3

    .line 807
    invoke-virtual {p1, v3}, Lgroovy/lang/Closure;->setResolveStrategy(I)V

    .line 808
    invoke-virtual {p1, v1}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    .line 809
    invoke-virtual {p1, v2}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    const/4 v1, 0x1

    .line 810
    invoke-virtual {p1, v1}, Lgroovy/lang/Closure;->setResolveStrategy(I)V

    const/4 p1, 0x0

    .line 811
    iput-boolean p1, v0, Lgroovy/lang/ExpandoMetaClass$DefiningClosure;->definition:Z

    return-object p0
.end method

.method public findMixinMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;
    .locals 6

    .line 352
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->mixinClasses:Ljava/util/Set;

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_5

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lorg/codehaus/groovy/reflection/MixinInMetaClass;

    .line 353
    invoke-virtual {v1}, Lorg/codehaus/groovy/reflection/MixinInMetaClass;->getMixinClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v2

    .line 354
    iget-object v3, v2, Lorg/codehaus/groovy/reflection/CachedClass;->classInfo:Lorg/codehaus/groovy/reflection/ClassInfo;

    invoke-virtual {v3}, Lorg/codehaus/groovy/reflection/ClassInfo;->getMetaClassForClass()Lgroovy/lang/MetaClass;

    move-result-object v3

    if-nez v3, :cond_1

    .line 356
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v3

    invoke-virtual {v2}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v2

    invoke-interface {v3, v2}, Lgroovy/lang/MetaClassRegistry;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object v3

    .line 359
    :cond_1
    invoke-interface {v3, p1, p2}, Lgroovy/lang/MetaClass;->pickMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;

    move-result-object v2

    const/4 v4, 0x0

    if-nez v2, :cond_3

    .line 360
    instance-of v5, v3, Lgroovy/lang/MetaClassImpl;

    if-eqz v5, :cond_3

    .line 361
    check-cast v3, Lgroovy/lang/MetaClassImpl;

    .line 362
    invoke-virtual {v3}, Lgroovy/lang/MetaClassImpl;->getTheCachedClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v5

    :cond_2
    invoke-virtual {v5}, Lorg/codehaus/groovy/reflection/CachedClass;->getCachedSuperClass()Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v5

    if-eqz v5, :cond_3

    .line 363
    invoke-virtual {v5}, Lorg/codehaus/groovy/reflection/CachedClass;->getTheClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {v3, v2, p1, p2, v4}, Lgroovy/lang/MetaClassImpl;->getMethodWithoutCaching(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Class;Z)Lgroovy/lang/MetaMethod;

    move-result-object v2

    if-eqz v2, :cond_2

    :cond_3
    if-eqz v2, :cond_0

    .line 369
    new-instance v0, Lorg/codehaus/groovy/runtime/metaclass/MixinInstanceMetaMethod;

    invoke-direct {v0, v2, v1}, Lorg/codehaus/groovy/runtime/metaclass/MixinInstanceMetaMethod;-><init>(Lgroovy/lang/MetaMethod;Lorg/codehaus/groovy/reflection/MixinInMetaClass;)V

    .line 371
    invoke-virtual {v0}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v1

    array-length v1, v1

    const/4 v2, 0x1

    if-ne v1, v2, :cond_4

    invoke-virtual {v0}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v1

    aget-object v1, v1, v4

    iget-boolean v1, v1, Lorg/codehaus/groovy/reflection/CachedClass;->isPrimitive:Z

    if-nez v1, :cond_4

    .line 372
    sget-object v1, Lgroovy/lang/ExpandoMetaClass;->EMPTY_CLASS_ARRAY:[Ljava/lang/Class;

    invoke-virtual {p0, p1, v1}, Lgroovy/lang/ExpandoMetaClass;->pickMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;

    move-result-object v2

    if-nez v2, :cond_4

    .line 374
    array-length p2, p2

    if-eqz p2, :cond_4

    .line 376
    :try_start_0
    invoke-virtual {p0, p1, v1}, Lgroovy/lang/ExpandoMetaClass;->findMixinMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;
    :try_end_0
    .catch Lorg/codehaus/groovy/runtime/metaclass/MethodSelectionException; {:try_start_0 .. :try_end_0} :catch_0

    .line 386
    :catch_0
    :cond_4
    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->registerInstanceMethod(Lgroovy/lang/MetaMethod;)V

    return-object v0

    :cond_5
    const/4 p1, 0x0

    return-object p1
.end method

.method public getExpandoMethods()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 1097
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->expandoMethods:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->toList(Ljava/util/Collection;)Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getExpandoProperties()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Lgroovy/lang/MetaProperty;",
            ">;"
        }
    .end annotation

    .line 1107
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->expandoProperties:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableCollection(Ljava/util/Collection;)Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method

.method public getExpandoSubclassMethods()Ljava/util/Collection;
    .locals 1

    .line 292
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->expandoSubclassMethods:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v0}, Ljava/util/concurrent/ConcurrentHashMap;->values()Ljava/util/Collection;

    move-result-object v0

    return-object v0
.end method

.method public getJavaClass()Ljava/lang/Class;
    .locals 1

    .line 1054
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1

    .line 713
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->myMetaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getMetaProperty(Ljava/lang/String;)Lgroovy/lang/MetaProperty;
    .locals 1

    .line 1190
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->expandoProperties:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovy/lang/MetaProperty;

    if-eqz v0, :cond_0

    return-object v0

    .line 1192
    :cond_0
    invoke-super {p0, p1}, Lgroovy/lang/MetaClassImpl;->getMetaProperty(Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object p1

    return-object p1
.end method

.method public getMethods()Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 921
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 922
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoMethods:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 923
    invoke-super {p0}, Lgroovy/lang/MetaClassImpl;->getMethods()Ljava/util/List;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    return-object v0
.end method

.method public getProperties()Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaProperty;",
            ">;"
        }
    .end annotation

    .line 928
    new-instance v0, Ljava/util/ArrayList;

    invoke-super {p0}, Lgroovy/lang/MetaClassImpl;->getProperties()Ljava/util/List;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    return-object v0
.end method

.method public getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;
    .locals 1

    .line 1142
    invoke-direct {p0, p3}, Lgroovy/lang/ExpandoMetaClass;->hasOverrideGetProperty(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass;->getJavaClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/Class;->isInstance(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1143
    iget-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->getPropertyMethod:Lgroovy/lang/MetaMethod;

    const/4 p4, 0x1

    new-array p4, p4, [Ljava/lang/Object;

    const/4 p5, 0x0

    aput-object p3, p4, p5

    invoke-virtual {p1, p2, p4}, Lgroovy/lang/MetaMethod;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    const-string v0, "mixedIn"

    .line 1146
    invoke-virtual {v0, p3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 1147
    new-instance p1, Lgroovy/lang/ExpandoMetaClass$MixedInAccessor;

    iget-object p3, p0, Lgroovy/lang/ExpandoMetaClass;->mixinClasses:Ljava/util/Set;

    invoke-direct {p1, p2, p3}, Lgroovy/lang/ExpandoMetaClass$MixedInAccessor;-><init>(Ljava/lang/Object;Ljava/util/Set;)V

    return-object p1

    .line 1150
    :cond_1
    invoke-super/range {p0 .. p5}, Lgroovy/lang/MetaClassImpl;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 3

    .line 1159
    invoke-direct {p0, p2}, Lgroovy/lang/ExpandoMetaClass;->hasOverrideGetProperty(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass;->getJavaClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/Class;->isInstance(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1160
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->getPropertyMethod:Lgroovy/lang/MetaMethod;

    const/4 v1, 0x1

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p2, v1, v2

    invoke-virtual {v0, p1, v1}, Lgroovy/lang/MetaMethod;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 1162
    :cond_0
    invoke-super {p0, p1, p2}, Lgroovy/lang/MetaClassImpl;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getProperty(Ljava/lang/String;)Ljava/lang/Object;
    .locals 2

    .line 721
    invoke-static {p1}, Lgroovy/lang/ExpandoMetaClass;->isValidExpandoProperty(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_3

    const-string v0, "static"

    .line 722
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 723
    new-instance v0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;

    const/4 v1, 0x1

    invoke-direct {v0, p0, p1, v1}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;-><init>(Lgroovy/lang/ExpandoMetaClass;Ljava/lang/String;Z)V

    return-object v0

    :cond_0
    const-string v0, "constructor"

    .line 724
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 725
    new-instance p1, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaConstructor;

    invoke-direct {p1, p0}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaConstructor;-><init>(Lgroovy/lang/ExpandoMetaClass;)V

    return-object p1

    .line 727
    :cond_1
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->myMetaClass:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p0, p1}, Lgroovy/lang/MetaClass;->hasProperty(Ljava/lang/Object;Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object v0

    if-nez v0, :cond_2

    .line 728
    new-instance v0, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;

    invoke-direct {v0, p0, p1}, Lgroovy/lang/ExpandoMetaClass$ExpandoMetaProperty;-><init>(Lgroovy/lang/ExpandoMetaClass;Ljava/lang/String;)V

    return-object v0

    .line 730
    :cond_2
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->myMetaClass:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p0, p1}, Lgroovy/lang/MetaClass;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 733
    :cond_3
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->myMetaClass:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p0, p1}, Lgroovy/lang/MetaClass;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getPropertyForSetter(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    const/4 v0, 0x0

    if-eqz p1, :cond_1

    .line 1273
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    const-string v1, "set"

    .line 1275
    invoke-virtual {p1, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_1

    const/4 v0, 0x3

    .line 1276
    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1277
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->convertPropertyName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_1
    :goto_0
    return-object v0
.end method

.method protected getSubclassMetaMethods(Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    .line 1044
    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass;->isModified()Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    .line 1047
    :cond_0
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->expandoSubclassMethods:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public hasCustomStaticInvokeMethod()Z
    .locals 1

    .line 1308
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->invokeStaticMethodMethod:Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public hasMetaMethod(Ljava/lang/String;[Ljava/lang/Class;)Z
    .locals 0

    .line 1213
    invoke-super {p0, p1, p2}, Lgroovy/lang/MetaClassImpl;->pickMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public hasMetaProperty(Ljava/lang/String;)Z
    .locals 0

    .line 1202
    invoke-virtual {p0, p1}, Lgroovy/lang/ExpandoMetaClass;->getMetaProperty(Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public initialize()V
    .locals 2

    .line 487
    :try_start_0
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->writeLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->lock()V

    .line 488
    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass;->isInitialized()Z

    move-result v0

    if-nez v0, :cond_0

    .line 489
    invoke-super {p0}, Lgroovy/lang/MetaClassImpl;->initialize()V

    const/4 v0, 0x1

    .line 490
    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->setInitialized(Z)V

    .line 491
    iput-boolean v0, p0, Lgroovy/lang/ExpandoMetaClass;->initCalled:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 495
    :cond_0
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->lock()V

    .line 496
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->writeLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->unlock()V

    .line 497
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->unlock()V

    return-void

    :catchall_0
    move-exception v0

    .line 495
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v1}, Ljava/util/concurrent/locks/Lock;->lock()V

    .line 496
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->writeLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v1}, Ljava/util/concurrent/locks/Lock;->unlock()V

    .line 497
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v1}, Ljava/util/concurrent/locks/Lock;->unlock()V

    .line 498
    throw v0
.end method

.method public invokeConstructor([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 679
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->convertToTypeArray([Ljava/lang/Object;)[Ljava/lang/Class;

    move-result-object v0

    const-string v1, "<init>"

    .line 680
    invoke-virtual {p0, v1, v0}, Lgroovy/lang/ExpandoMetaClass;->pickMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 681
    invoke-virtual {v0}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v1

    array-length v1, v1

    array-length v2, p1

    if-ne v1, v2, :cond_0

    .line 682
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-virtual {v0, v1, p1}, Lgroovy/lang/MetaMethod;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 684
    :cond_0
    invoke-super {p0, p1}, Lgroovy/lang/MetaClassImpl;->invokeConstructor([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)Ljava/lang/Object;
    .locals 1

    .line 1116
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->invokeMethodMethod:Lgroovy/lang/MetaMethod;

    if-eqz v0, :cond_0

    .line 1117
    invoke-static {p4}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->unwrap([Ljava/lang/Object;)V

    .line 1118
    iget-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->invokeMethodMethod:Lgroovy/lang/MetaMethod;

    const/4 p5, 0x2

    new-array p5, p5, [Ljava/lang/Object;

    const/4 p6, 0x0

    aput-object p3, p5, p6

    const/4 p3, 0x1

    aput-object p4, p5, p3

    invoke-virtual {p1, p2, p5}, Lgroovy/lang/MetaMethod;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 1120
    :cond_0
    invoke-super/range {p0 .. p6}, Lgroovy/lang/MetaClassImpl;->invokeMethod(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 746
    instance-of v0, p2, [Ljava/lang/Object;

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_0

    check-cast p2, [Ljava/lang/Object;

    goto :goto_0

    :cond_0
    new-array v0, v1, [Ljava/lang/Object;

    aput-object p2, v0, v2

    move-object p2, v0

    .line 747
    :goto_0
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->myMetaClass:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 753
    invoke-virtual {v0, p0, p2}, Lgroovy/lang/MetaMethod;->doMethodInvoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 756
    :cond_1
    array-length v0, p2

    const/4 v3, 0x2

    const/4 v4, 0x0

    if-ne v0, v3, :cond_3

    aget-object v0, p2, v2

    instance-of v0, v0, Ljava/lang/Class;

    if-eqz v0, :cond_3

    aget-object v0, p2, v1

    instance-of v0, v0, Lgroovy/lang/Closure;

    if-eqz v0, :cond_3

    .line 757
    aget-object v0, p2, v2

    iget-object v3, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    if-ne v0, v3, :cond_2

    .line 758
    aget-object p2, p2, v1

    check-cast p2, Lgroovy/lang/Closure;

    invoke-virtual {p0, p1, p2}, Lgroovy/lang/ExpandoMetaClass;->registerInstanceMethod(Ljava/lang/String;Lgroovy/lang/Closure;)V

    goto :goto_1

    .line 760
    :cond_2
    aget-object v0, p2, v2

    check-cast v0, Ljava/lang/Class;

    aget-object p2, p2, v1

    check-cast p2, Lgroovy/lang/Closure;

    invoke-virtual {p0, p1, v0, p2}, Lgroovy/lang/ExpandoMetaClass;->registerSubclassInstanceMethod(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/Closure;)V

    :goto_1
    return-object v4

    .line 765
    :cond_3
    array-length v0, p2

    if-ne v0, v1, :cond_4

    aget-object v0, p2, v2

    instance-of v0, v0, Lgroovy/lang/Closure;

    if-eqz v0, :cond_4

    .line 766
    aget-object p2, p2, v2

    check-cast p2, Lgroovy/lang/Closure;

    invoke-virtual {p0, p1, p2}, Lgroovy/lang/ExpandoMetaClass;->registerInstanceMethod(Ljava/lang/String;Lgroovy/lang/Closure;)V

    return-object v4

    .line 770
    :cond_4
    new-instance v0, Lgroovy/lang/MissingMethodException;

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-direct {v0, p1, v1, p2}, Lgroovy/lang/MissingMethodException;-><init>(Ljava/lang/String;Ljava/lang/Class;[Ljava/lang/Object;)V

    throw v0
.end method

.method public invokeStaticMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1129
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->invokeStaticMethodMethod:Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

    if-eqz v0, :cond_0

    .line 1130
    invoke-static {p3}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->unwrap([Ljava/lang/Object;)V

    .line 1131
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->invokeStaticMethodMethod:Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p2, v1, v2

    const/4 p2, 0x1

    aput-object p3, v1, p2

    invoke-virtual {v0, p1, v1}, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 1133
    :cond_0
    invoke-super {p0, p1, p2, p3}, Lgroovy/lang/MetaClassImpl;->invokeStaticMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method protected isInitialized()Z
    .locals 2

    .line 508
    :try_start_0
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->lock()V

    .line 509
    iget-boolean v0, p0, Lgroovy/lang/ExpandoMetaClass;->initialized:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 511
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v1}, Ljava/util/concurrent/locks/Lock;->unlock()V

    return v0

    :catchall_0
    move-exception v0

    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v1}, Ljava/util/concurrent/locks/Lock;->unlock()V

    .line 512
    throw v0
.end method

.method public isModified()Z
    .locals 1

    .line 414
    iget-boolean v0, p0, Lgroovy/lang/ExpandoMetaClass;->modified:Z

    return v0
.end method

.method public isSetter(Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;)Z
    .locals 2

    const/4 v0, 0x0

    if-eqz p1, :cond_2

    .line 1283
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v1

    if-eqz v1, :cond_2

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    const-string v1, "set"

    .line 1285
    invoke-virtual {p1, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_2

    .line 1286
    array-length p2, p2

    const/4 v1, 0x1

    if-eq p2, v1, :cond_1

    return v0

    :cond_1
    const/4 p2, 0x3

    .line 1287
    invoke-virtual {p1, p2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1288
    invoke-static {p1}, Lgroovy/lang/ExpandoMetaClass;->isPropertyName(Ljava/lang/String;)Z

    move-result p1

    return p1

    :cond_2
    :goto_0
    return v0
.end method

.method public synthetic lambda$registerBeanProperty$0$groovy-lang-ExpandoMetaClass(Ljava/lang/Object;Ljava/lang/String;)V
    .locals 7

    if-nez p1, :cond_0

    .line 851
    const-class v0, Ljava/lang/Object;

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    .line 853
    :goto_0
    instance-of v1, p1, Lgroovy/lang/MetaBeanProperty;

    if-eqz v1, :cond_1

    check-cast p1, Lgroovy/lang/MetaBeanProperty;

    goto :goto_1

    :cond_1
    new-instance v1, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;

    iget-object v2, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-direct {v1, v2, p2, v0, p1}, Lorg/codehaus/groovy/runtime/metaclass/ThreadManagedMetaBeanProperty;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Object;)V

    move-object p1, v1

    .line 855
    :goto_1
    invoke-virtual {p1}, Lgroovy/lang/MetaBeanProperty;->getGetter()Lgroovy/lang/MetaMethod;

    move-result-object p2

    .line 856
    new-instance v0, Lorg/codehaus/groovy/runtime/DefaultCachedMethodKey;

    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-virtual {p2}, Lgroovy/lang/MetaMethod;->getName()Ljava/lang/String;

    move-result-object v2

    sget-object v3, Lorg/codehaus/groovy/reflection/CachedClass;->EMPTY_ARRAY:[Lorg/codehaus/groovy/reflection/CachedClass;

    const/4 v4, 0x0

    invoke-direct {v0, v1, v2, v3, v4}, Lorg/codehaus/groovy/runtime/DefaultCachedMethodKey;-><init>(Ljava/lang/Class;Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;Z)V

    .line 857
    invoke-virtual {p1}, Lgroovy/lang/MetaBeanProperty;->getSetter()Lgroovy/lang/MetaMethod;

    move-result-object v1

    .line 858
    new-instance v2, Lorg/codehaus/groovy/runtime/DefaultCachedMethodKey;

    iget-object v3, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-virtual {v1}, Lgroovy/lang/MetaMethod;->getName()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v6

    invoke-direct {v2, v3, v5, v6, v4}, Lorg/codehaus/groovy/runtime/DefaultCachedMethodKey;-><init>(Ljava/lang/Class;Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;Z)V

    .line 859
    invoke-virtual {p0, p2}, Lgroovy/lang/ExpandoMetaClass;->addMetaMethod(Lgroovy/lang/MetaMethod;)V

    .line 860
    invoke-virtual {p0, v1}, Lgroovy/lang/ExpandoMetaClass;->addMetaMethod(Lgroovy/lang/MetaMethod;)V

    .line 862
    iget-object v3, p0, Lgroovy/lang/ExpandoMetaClass;->expandoMethods:Ljava/util/Map;

    invoke-interface {v3, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 863
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoMethods:Ljava/util/Map;

    invoke-interface {v1, v0, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 864
    iget-object p2, p0, Lgroovy/lang/ExpandoMetaClass;->expandoProperties:Ljava/util/Map;

    invoke-virtual {p1}, Lgroovy/lang/MetaBeanProperty;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-interface {p2, v0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 866
    invoke-virtual {p0, p1}, Lgroovy/lang/ExpandoMetaClass;->addMetaBeanProperty(Lgroovy/lang/MetaBeanProperty;)V

    .line 867
    invoke-direct {p0}, Lgroovy/lang/ExpandoMetaClass;->performRegistryCallbacks()V

    return-void
.end method

.method public synthetic lambda$registerInstanceMethod$1$groovy-lang-ExpandoMetaClass(Lgroovy/lang/MetaMethod;Z)V
    .locals 5

    .line 879
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getName()Ljava/lang/String;

    move-result-object v0

    .line 880
    invoke-virtual {p0, p1}, Lgroovy/lang/ExpandoMetaClass;->checkIfGroovyObjectMethod(Lgroovy/lang/MetaMethod;)V

    .line 881
    new-instance v1, Lorg/codehaus/groovy/runtime/DefaultCachedMethodKey;

    iget-object v2, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v3

    const/4 v4, 0x0

    invoke-direct {v1, v2, v0, v3, v4}, Lorg/codehaus/groovy/runtime/DefaultCachedMethodKey;-><init>(Ljava/lang/Class;Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;Z)V

    .line 883
    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass;->isInitialized()Z

    move-result v2

    if-nez v2, :cond_2

    .line 887
    iget-object v2, p0, Lgroovy/lang/ExpandoMetaClass;->metaMethodIndex:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;

    iget-object v3, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-virtual {v2, v3}, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->getHeader(Ljava/lang/Class;)Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    move-result-object v2

    invoke-virtual {p0, p1, v2}, Lgroovy/lang/ExpandoMetaClass;->addMetaMethodToIndex(Lgroovy/lang/MetaMethod;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;)V

    .line 889
    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->dropMethodCache(Ljava/lang/String;)V

    .line 890
    iget-object v2, p0, Lgroovy/lang/ExpandoMetaClass;->expandoMethods:Ljava/util/Map;

    invoke-interface {v2, v1, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    if-eqz p2, :cond_0

    .line 892
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object v1

    invoke-direct {p0, v0, v1}, Lgroovy/lang/ExpandoMetaClass;->isGetter(Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 893
    invoke-direct {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->getPropertyForGetter(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    const/4 v0, 0x1

    .line 894
    invoke-direct {p0, p1, p2, v0, v4}, Lgroovy/lang/ExpandoMetaClass;->registerBeanPropertyForMethod(Lgroovy/lang/MetaMethod;Ljava/lang/String;ZZ)V

    goto :goto_0

    :cond_0
    if-eqz p2, :cond_1

    .line 896
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p2

    invoke-virtual {p0, v0, p2}, Lgroovy/lang/ExpandoMetaClass;->isSetter(Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;)Z

    move-result p2

    if-eqz p2, :cond_1

    .line 897
    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->getPropertyForSetter(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    .line 898
    invoke-direct {p0, p1, p2, v4, v4}, Lgroovy/lang/ExpandoMetaClass;->registerBeanPropertyForMethod(Lgroovy/lang/MetaMethod;Ljava/lang/String;ZZ)V

    .line 900
    :cond_1
    :goto_0
    invoke-direct {p0}, Lgroovy/lang/ExpandoMetaClass;->performRegistryCallbacks()V

    return-void

    .line 884
    :cond_2
    new-instance p2, Ljava/lang/RuntimeException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Already initialized, cannot add new method: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw p2
.end method

.method public synthetic lambda$registerStaticMethod$2$groovy-lang-ExpandoMetaClass(Ljava/lang/String;[Ljava/lang/Class;Lgroovy/lang/Closure;)V
    .locals 4

    const-string v0, "methodMissing"

    .line 1002
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    const-string v2, "$static_methodMissing"

    if-eqz v1, :cond_0

    move-object p1, v2

    goto :goto_0

    :cond_0
    const-string v1, "propertyMissing"

    .line 1004
    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    const-string p1, "$static_propertyMissing"

    :cond_1
    :goto_0
    if-eqz p2, :cond_2

    .line 1012
    new-instance v1, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

    iget-object v3, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-direct {v1, p1, v3, p3, p2}, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;-><init>(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/Closure;[Ljava/lang/Class;)V

    goto :goto_1

    .line 1014
    :cond_2
    new-instance v1, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

    iget-object p2, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-direct {v1, p1, p2, p3}, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;-><init>(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/Closure;)V

    :goto_1
    const-string p2, "invokeMethod"

    .line 1017
    invoke-virtual {p1, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_3

    invoke-virtual {p3}, Lgroovy/lang/Closure;->getParameterTypes()[Ljava/lang/Class;

    move-result-object p2

    array-length p2, p2

    const/4 p3, 0x2

    if-ne p2, p3, :cond_3

    .line 1018
    iput-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->invokeStaticMethodMethod:Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;

    goto :goto_4

    .line 1020
    :cond_3
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-eqz p2, :cond_4

    goto :goto_2

    :cond_4
    move-object v2, p1

    .line 1023
    :goto_2
    new-instance p1, Lorg/codehaus/groovy/runtime/DefaultCachedMethodKey;

    iget-object p2, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-virtual {v1}, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p3

    const/4 v0, 0x0

    invoke-direct {p1, p2, v2, p3, v0}, Lorg/codehaus/groovy/runtime/DefaultCachedMethodKey;-><init>(Ljava/lang/Class;Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;Z)V

    .line 1025
    invoke-virtual {p0, v1}, Lgroovy/lang/ExpandoMetaClass;->addMetaMethod(Lgroovy/lang/MetaMethod;)V

    .line 1026
    invoke-virtual {p0, v2}, Lgroovy/lang/ExpandoMetaClass;->dropStaticMethodCache(Ljava/lang/String;)V

    .line 1029
    invoke-virtual {v1}, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p2

    invoke-direct {p0, v2, p2}, Lgroovy/lang/ExpandoMetaClass;->isGetter(Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;)Z

    move-result p2

    const/4 p3, 0x1

    if-eqz p2, :cond_5

    .line 1030
    invoke-direct {p0, v2}, Lgroovy/lang/ExpandoMetaClass;->getPropertyForGetter(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    .line 1031
    invoke-direct {p0, v1, p2, p3, p3}, Lgroovy/lang/ExpandoMetaClass;->registerBeanPropertyForMethod(Lgroovy/lang/MetaMethod;Ljava/lang/String;ZZ)V

    goto :goto_3

    .line 1033
    :cond_5
    invoke-virtual {v1}, Lorg/codehaus/groovy/runtime/metaclass/ClosureStaticMetaMethod;->getParameterTypes()[Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p2

    invoke-virtual {p0, v2, p2}, Lgroovy/lang/ExpandoMetaClass;->isSetter(Ljava/lang/String;[Lorg/codehaus/groovy/reflection/CachedClass;)Z

    move-result p2

    if-eqz p2, :cond_6

    .line 1034
    invoke-virtual {p0, v2}, Lgroovy/lang/ExpandoMetaClass;->getPropertyForSetter(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p2

    .line 1035
    invoke-direct {p0, v1, p2, v0, p3}, Lgroovy/lang/ExpandoMetaClass;->registerBeanPropertyForMethod(Lgroovy/lang/MetaMethod;Ljava/lang/String;ZZ)V

    .line 1037
    :cond_6
    :goto_3
    invoke-direct {p0}, Lgroovy/lang/ExpandoMetaClass;->performRegistryCallbacks()V

    .line 1038
    iget-object p2, p0, Lgroovy/lang/ExpandoMetaClass;->expandoMethods:Ljava/util/Map;

    invoke-interface {p2, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_4
    return-void
.end method

.method protected onGetPropertyFoundInHierarchy(Lgroovy/lang/MetaMethod;)V
    .locals 0

    .line 410
    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->getPropertyMethod:Lgroovy/lang/MetaMethod;

    return-void
.end method

.method protected onInvokeMethodFoundInHierarchy(Lgroovy/lang/MetaMethod;)V
    .locals 0

    .line 394
    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->invokeMethodMethod:Lgroovy/lang/MetaMethod;

    return-void
.end method

.method protected onSetPropertyFoundInHierarchy(Lgroovy/lang/MetaMethod;)V
    .locals 0

    .line 406
    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->setPropertyMethod:Lgroovy/lang/MetaMethod;

    return-void
.end method

.method protected onSuperMethodFoundInHierarchy(Lgroovy/lang/MetaMethod;)V
    .locals 0

    .line 398
    invoke-direct {p0, p1}, Lgroovy/lang/ExpandoMetaClass;->addSuperMethodIfNotOverridden(Lgroovy/lang/MetaMethod;)V

    return-void
.end method

.method protected onSuperPropertyFoundInHierarchy(Lgroovy/lang/MetaBeanProperty;)V
    .locals 0

    .line 402
    invoke-virtual {p0, p1}, Lgroovy/lang/ExpandoMetaClass;->addMetaBeanProperty(Lgroovy/lang/MetaBeanProperty;)V

    return-void
.end method

.method protected declared-synchronized performOperationOnMetaClass(Lgroovy/lang/ExpandoMetaClass$Callable;)V
    .locals 2

    monitor-enter p0

    const/4 v0, 0x1

    .line 817
    :try_start_0
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->writeLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v1}, Ljava/util/concurrent/locks/Lock;->lock()V

    .line 818
    iget-boolean v1, p0, Lgroovy/lang/ExpandoMetaClass;->allowChangesAfterInit:Z

    if-eqz v1, :cond_0

    const/4 v1, 0x0

    .line 819
    invoke-virtual {p0, v1}, Lgroovy/lang/ExpandoMetaClass;->setInitialized(Z)V

    .line 821
    :cond_0
    invoke-interface {p1}, Lgroovy/lang/ExpandoMetaClass$Callable;->call()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 824
    :try_start_1
    iget-boolean p1, p0, Lgroovy/lang/ExpandoMetaClass;->initCalled:Z

    if-eqz p1, :cond_1

    .line 825
    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->setInitialized(Z)V

    .line 828
    :cond_1
    iget-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {p1}, Ljava/util/concurrent/locks/Lock;->lock()V

    .line 829
    iget-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->writeLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {p1}, Ljava/util/concurrent/locks/Lock;->unlock()V

    .line 830
    iget-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {p1}, Ljava/util/concurrent/locks/Lock;->unlock()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 832
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    .line 824
    :try_start_2
    iget-boolean v1, p0, Lgroovy/lang/ExpandoMetaClass;->initCalled:Z

    if-eqz v1, :cond_2

    .line 825
    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->setInitialized(Z)V

    .line 828
    :cond_2
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->lock()V

    .line 829
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->writeLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->unlock()V

    .line 830
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->readLock:Ljava/util/concurrent/locks/Lock;

    invoke-interface {v0}, Ljava/util/concurrent/locks/Lock;->unlock()V

    .line 831
    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public refreshInheritedMethods(Ljava/util/Set;)V
    .locals 1

    .line 1064
    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    .line 1065
    check-cast v0, Lgroovy/lang/ExpandoMetaClass;

    if-eq v0, p0, :cond_0

    .line 1067
    invoke-direct {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->refreshInheritedMethods(Lgroovy/lang/ExpandoMetaClass;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method public registerBeanProperty(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    .line 850
    new-instance v0, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda1;

    invoke-direct {v0, p0, p2, p1}, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda1;-><init>(Lgroovy/lang/ExpandoMetaClass;Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->performOperationOnMetaClass(Lgroovy/lang/ExpandoMetaClass$Callable;)V

    return-void
.end method

.method public registerInstanceMethod(Lgroovy/lang/MetaMethod;)V
    .locals 2

    .line 877
    iget-boolean v0, p0, Lgroovy/lang/ExpandoMetaClass;->initCalled:Z

    .line 878
    new-instance v1, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0, p1, v0}, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda0;-><init>(Lgroovy/lang/ExpandoMetaClass;Lgroovy/lang/MetaMethod;Z)V

    invoke-virtual {p0, v1}, Lgroovy/lang/ExpandoMetaClass;->performOperationOnMetaClass(Lgroovy/lang/ExpandoMetaClass$Callable;)V

    return-void
.end method

.method public registerInstanceMethod(Ljava/lang/String;Lgroovy/lang/Closure;)V
    .locals 1

    const-string v0, "constructor"

    .line 905
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p1, "<init>"

    .line 908
    :cond_0
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-static {p1, v0, p2}, Lorg/codehaus/groovy/runtime/metaclass/ClosureMetaMethod;->createMethodList(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/Closure;)Ljava/util/List;

    move-result-object p1

    .line 909
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovy/lang/MetaMethod;

    .line 910
    invoke-virtual {p0, p2}, Lgroovy/lang/ExpandoMetaClass;->registerInstanceMethod(Lgroovy/lang/MetaMethod;)V

    goto :goto_0

    :cond_1
    return-void
.end method

.method protected registerStaticMethod(Ljava/lang/String;Lgroovy/lang/Closure;)V
    .locals 1

    const/4 v0, 0x0

    .line 990
    invoke-virtual {p0, p1, p2, v0}, Lgroovy/lang/ExpandoMetaClass;->registerStaticMethod(Ljava/lang/String;Lgroovy/lang/Closure;[Ljava/lang/Class;)V

    return-void
.end method

.method protected registerStaticMethod(Ljava/lang/String;Lgroovy/lang/Closure;[Ljava/lang/Class;)V
    .locals 1

    .line 1000
    new-instance v0, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda2;

    invoke-direct {v0, p0, p1, p3, p2}, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda2;-><init>(Lgroovy/lang/ExpandoMetaClass;Ljava/lang/String;[Ljava/lang/Class;Lgroovy/lang/Closure;)V

    invoke-virtual {p0, v0}, Lgroovy/lang/ExpandoMetaClass;->performOperationOnMetaClass(Lgroovy/lang/ExpandoMetaClass$Callable;)V

    return-void
.end method

.method public registerSubclassInstanceMethod(Lgroovy/lang/MetaMethod;)V
    .locals 4

    const/4 v0, 0x1

    .line 425
    iput-boolean v0, p0, Lgroovy/lang/ExpandoMetaClass;->modified:Z

    .line 427
    invoke-virtual {p1}, Lgroovy/lang/MetaMethod;->getName()Ljava/lang/String;

    move-result-object v0

    .line 428
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoSubclassMethods:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v1, v0}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    if-nez v1, :cond_0

    .line 430
    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoSubclassMethods:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {v1, v0, p1}, Ljava/util/concurrent/ConcurrentHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 432
    :cond_0
    instance-of v2, v1, Lgroovy/lang/MetaMethod;

    if-eqz v2, :cond_1

    .line 433
    new-instance v2, Lorg/codehaus/groovy/util/FastArray;

    const/4 v3, 0x2

    invoke-direct {v2, v3}, Lorg/codehaus/groovy/util/FastArray;-><init>(I)V

    .line 434
    invoke-virtual {v2, v1}, Lorg/codehaus/groovy/util/FastArray;->add(Ljava/lang/Object;)V

    .line 435
    invoke-virtual {v2, p1}, Lorg/codehaus/groovy/util/FastArray;->add(Ljava/lang/Object;)V

    .line 436
    iget-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->expandoSubclassMethods:Ljava/util/concurrent/ConcurrentHashMap;

    invoke-virtual {p1, v0, v2}, Ljava/util/concurrent/ConcurrentHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    .line 438
    :cond_1
    check-cast v1, Lorg/codehaus/groovy/util/FastArray;

    invoke-virtual {v1, p1}, Lorg/codehaus/groovy/util/FastArray;->add(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public registerSubclassInstanceMethod(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/Closure;)V
    .locals 0

    .line 418
    invoke-static {p1, p2, p3}, Lorg/codehaus/groovy/runtime/metaclass/ClosureMetaMethod;->createMethodList(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/Closure;)Ljava/util/List;

    move-result-object p1

    .line 419
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovy/lang/MetaMethod;

    .line 420
    invoke-virtual {p0, p2}, Lgroovy/lang/ExpandoMetaClass;->registerSubclassInstanceMethod(Lgroovy/lang/MetaMethod;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public retrieveConstructor([Ljava/lang/Object;)Lgroovy/lang/MetaMethod;
    .locals 2

    .line 1324
    invoke-static {p1}, Lorg/codehaus/groovy/runtime/MetaClassHelper;->convertToTypeArray([Ljava/lang/Object;)[Ljava/lang/Class;

    move-result-object v0

    const-string v1, "<init>"

    .line 1325
    invoke-virtual {p0, v1, v0}, Lgroovy/lang/ExpandoMetaClass;->pickMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;

    move-result-object v0

    if-eqz v0, :cond_0

    return-object v0

    .line 1327
    :cond_0
    invoke-super {p0, p1}, Lgroovy/lang/MetaClassImpl;->retrieveConstructor([Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    return-object p1
.end method

.method protected setInitialized(Z)V
    .locals 0

    .line 517
    iput-boolean p1, p0, Lgroovy/lang/ExpandoMetaClass;->initialized:Z

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 778
    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->myMetaClass:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V
    .locals 1

    .line 1176
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->setPropertyMethod:Lgroovy/lang/MetaMethod;

    if-eqz v0, :cond_0

    const-string v0, "metaClass"

    invoke-virtual {p3, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass;->getJavaClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/Class;->isInstance(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1177
    iget-object p1, p0, Lgroovy/lang/ExpandoMetaClass;->setPropertyMethod:Lgroovy/lang/MetaMethod;

    const/4 p5, 0x2

    new-array p5, p5, [Ljava/lang/Object;

    const/4 p6, 0x0

    aput-object p3, p5, p6

    const/4 p3, 0x1

    aput-object p4, p5, p3

    invoke-virtual {p1, p2, p5}, Lgroovy/lang/MetaMethod;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    .line 1180
    :cond_0
    invoke-super/range {p0 .. p6}, Lgroovy/lang/MetaClassImpl;->setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V

    return-void
.end method

.method public setProperty(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    .line 786
    instance-of v0, p2, Lgroovy/lang/Closure;

    if-eqz v0, :cond_1

    const-string v0, "constructor"

    .line 787
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p1, "<init>"

    .line 790
    :cond_0
    check-cast p2, Lgroovy/lang/Closure;

    .line 791
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass;->theClass:Ljava/lang/Class;

    invoke-static {p1, v0, p2}, Lorg/codehaus/groovy/runtime/metaclass/ClosureMetaMethod;->createMethodList(Ljava/lang/String;Ljava/lang/Class;Lgroovy/lang/Closure;)Ljava/util/List;

    move-result-object p1

    .line 792
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lgroovy/lang/MetaMethod;

    .line 795
    invoke-virtual {p0, p2}, Lgroovy/lang/ExpandoMetaClass;->registerInstanceMethod(Lgroovy/lang/MetaMethod;)V

    goto :goto_0

    .line 798
    :cond_1
    invoke-virtual {p0, p1, p2}, Lgroovy/lang/ExpandoMetaClass;->registerBeanProperty(Ljava/lang/String;Ljava/lang/Object;)V

    :cond_2
    return-void
.end method
