.class Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1$1;
.super Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;
.source "ExpandoMetaClass.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$1:Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1;


# direct methods
.method constructor <init>(Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1;Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 1460
    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1$1;->this$1:Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1;

    invoke-direct {p0, p2}, Lorg/codehaus/groovy/runtime/metaclass/OwnedMetaClass;-><init>(Lgroovy/lang/MetaClass;)V

    return-void
.end method


# virtual methods
.method protected getOwner()Ljava/lang/Object;
    .locals 1

    .line 1462
    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1$1;->this$1:Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1;

    iget-object v0, v0, Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1;->this$0:Lgroovy/lang/ExpandoMetaClass$MixedInAccessor;

    invoke-static {v0}, Lgroovy/lang/ExpandoMetaClass$MixedInAccessor;->access$300(Lgroovy/lang/ExpandoMetaClass$MixedInAccessor;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method protected getOwnerMetaClass(Ljava/lang/Object;)Lgroovy/lang/MetaClass;
    .locals 0

    .line 1466
    invoke-virtual {p0}, Lgroovy/lang/ExpandoMetaClass$MixedInAccessor$1$1;->getAdaptee()Lgroovy/lang/MetaClass;

    move-result-object p1

    return-object p1
.end method
