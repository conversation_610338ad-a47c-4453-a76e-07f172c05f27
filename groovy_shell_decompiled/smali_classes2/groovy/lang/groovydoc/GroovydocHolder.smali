.class public interface abstract Lgroovy/lang/groovydoc/GroovydocHolder;
.super Ljava/lang/Object;
.source "GroovydocHolder.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# static fields
.field public static final DOC_COMMENT:Ljava/lang/String; = "_DOC_COMMENT"


# virtual methods
.method public abstract getGroovydoc()Lgroovy/lang/groovydoc/Groovydoc;
.end method

.method public abstract getInstance()Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation
.end method
