.class public Lgroovy/lang/groovydoc/Groovydoc;
.super Ljava/lang/Object;
.source "Groovydoc.java"


# static fields
.field public static final EMPTY_GROOVYDOC:Lgroovy/lang/groovydoc/Groovydoc;


# instance fields
.field private final content:Ljava/lang/String;

.field private holder:Lgroovy/lang/groovydoc/GroovydocHolder;

.field private tagList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lgroovy/lang/groovydoc/GroovydocTag;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 33
    new-instance v0, Lgroovy/lang/groovydoc/Groovydoc$1;

    const-string v1, ""

    invoke-direct {v0, v1}, Lgroovy/lang/groovydoc/Groovydoc$1;-><init>(Ljava/lang/String;)V

    sput-object v0, Lgroovy/lang/groovydoc/Groovydoc;->EMPTY_GROOVYDOC:Lgroovy/lang/groovydoc/Groovydoc;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;)V
    .locals 1

    .line 40
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 31
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, Lgroovy/lang/groovydoc/Groovydoc;->tagList:Ljava/util/List;

    .line 41
    iput-object p1, p0, Lgroovy/lang/groovydoc/Groovydoc;->content:Ljava/lang/String;

    return-void
.end method

.method synthetic constructor <init>(Ljava/lang/String;Lgroovy/lang/groovydoc/Groovydoc$1;)V
    .locals 0

    .line 29
    invoke-direct {p0, p1}, Lgroovy/lang/groovydoc/Groovydoc;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Lgroovy/lang/groovydoc/GroovydocHolder;)V
    .locals 0

    .line 45
    invoke-direct {p0, p1}, Lgroovy/lang/groovydoc/Groovydoc;-><init>(Ljava/lang/String;)V

    .line 46
    iput-object p2, p0, Lgroovy/lang/groovydoc/Groovydoc;->holder:Lgroovy/lang/groovydoc/GroovydocHolder;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/reflect/AnnotatedElement;)V
    .locals 0

    .line 50
    invoke-direct {p0, p1}, Lgroovy/lang/groovydoc/Groovydoc;-><init>(Ljava/lang/String;)V

    .line 51
    new-instance p1, Lgroovy/lang/groovydoc/Groovydoc$2;

    invoke-direct {p1, p0, p2}, Lgroovy/lang/groovydoc/Groovydoc$2;-><init>(Lgroovy/lang/groovydoc/Groovydoc;Ljava/lang/reflect/AnnotatedElement;)V

    iput-object p1, p0, Lgroovy/lang/groovydoc/Groovydoc;->holder:Lgroovy/lang/groovydoc/GroovydocHolder;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x0

    if-eqz p1, :cond_3

    .line 100
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    if-eq v2, v3, :cond_1

    goto :goto_1

    .line 101
    :cond_1
    check-cast p1, Lgroovy/lang/groovydoc/Groovydoc;

    .line 102
    iget-object v2, p0, Lgroovy/lang/groovydoc/Groovydoc;->content:Ljava/lang/String;

    iget-object v3, p1, Lgroovy/lang/groovydoc/Groovydoc;->content:Ljava/lang/String;

    invoke-static {v2, v3}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Lgroovy/lang/groovydoc/Groovydoc;->holder:Lgroovy/lang/groovydoc/GroovydocHolder;

    iget-object p1, p1, Lgroovy/lang/groovydoc/Groovydoc;->holder:Lgroovy/lang/groovydoc/GroovydocHolder;

    .line 103
    invoke-static {v2, p1}, Ljava/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v1

    :goto_0
    return v0

    :cond_3
    :goto_1
    return v1
.end method

.method public getContent()Ljava/lang/String;
    .locals 1

    .line 77
    iget-object v0, p0, Lgroovy/lang/groovydoc/Groovydoc;->content:Ljava/lang/String;

    return-object v0
.end method

.method public getHolder()Lgroovy/lang/groovydoc/GroovydocHolder;
    .locals 1

    .line 94
    iget-object v0, p0, Lgroovy/lang/groovydoc/Groovydoc;->holder:Lgroovy/lang/groovydoc/GroovydocHolder;

    return-object v0
.end method

.method public getTagList()Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/groovydoc/GroovydocTag;",
            ">;"
        }
    .end annotation

    .line 85
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    const-string v1, "[TODO]parsing tags will be a new features of the next releases"

    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Object;

    .line 108
    iget-object v1, p0, Lgroovy/lang/groovydoc/Groovydoc;->content:Ljava/lang/String;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-object v1, p0, Lgroovy/lang/groovydoc/Groovydoc;->holder:Lgroovy/lang/groovydoc/GroovydocHolder;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    invoke-static {v0}, Ljava/util/Objects;->hash([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public isPresent()Z
    .locals 1

    .line 69
    sget-object v0, Lgroovy/lang/groovydoc/Groovydoc;->EMPTY_GROOVYDOC:Lgroovy/lang/groovydoc/Groovydoc;

    if-eq v0, p0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 113
    iget-object v0, p0, Lgroovy/lang/groovydoc/Groovydoc;->content:Ljava/lang/String;

    return-object v0
.end method
