.class Lgroovy/lang/groovydoc/Groovydoc$1;
.super Lgroovy/lang/groovydoc/Groovydoc;
.source "Groovydoc.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/lang/groovydoc/Groovydoc;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# direct methods
.method constructor <init>(Ljava/lang/String;)V
    .locals 1

    const/4 v0, 0x0

    .line 33
    invoke-direct {p0, p1, v0}, Lgroovy/lang/groovydoc/Groovydoc;-><init>(Ljava/lang/String;Lgroovy/lang/groovydoc/Groovydoc$1;)V

    return-void
.end method


# virtual methods
.method public getTagList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/groovydoc/GroovydocTag;",
            ">;"
        }
    .end annotation

    .line 36
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
