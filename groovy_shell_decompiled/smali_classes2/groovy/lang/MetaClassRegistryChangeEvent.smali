.class public Lgroovy/lang/MetaClassRegistryChangeEvent;
.super Ljava/util/EventObject;
.source "MetaClassRegistryChangeEvent.java"


# static fields
.field private static final serialVersionUID:J = 0x16de56187a31a538L


# instance fields
.field private final clazz:Ljava/lang/Class;

.field private final instance:Ljava/lang/Object;

.field private final metaClass:Lgroovy/lang/MetaClass;

.field private final oldMetaClass:Lgroovy/lang/MetaClass;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;Lgroovy/lang/MetaClass;Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 43
    invoke-direct {p0, p1}, Ljava/util/EventObject;-><init>(Ljava/lang/Object;)V

    .line 44
    iput-object p3, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->clazz:Ljava/lang/Class;

    .line 45
    iput-object p5, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->metaClass:Lgroovy/lang/MetaClass;

    .line 46
    iput-object p4, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->oldMetaClass:Lgroovy/lang/MetaClass;

    .line 47
    iput-object p2, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->instance:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public getClassToUpdate()Ljava/lang/Class;
    .locals 1

    .line 56
    iget-object v0, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->clazz:Ljava/lang/Class;

    return-object v0
.end method

.method public getInstance()Ljava/lang/Object;
    .locals 1

    .line 92
    iget-object v0, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->instance:Ljava/lang/Object;

    return-object v0
.end method

.method public getNewMetaClass()Lgroovy/lang/MetaClass;
    .locals 1

    .line 65
    iget-object v0, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->metaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getOldMetaClass()Lgroovy/lang/MetaClass;
    .locals 1

    .line 74
    iget-object v0, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->oldMetaClass:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getRegistry()Lgroovy/lang/MetaClassRegistry;
    .locals 1

    .line 101
    iget-object v0, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->source:Ljava/lang/Object;

    check-cast v0, Lgroovy/lang/MetaClassRegistry;

    return-object v0
.end method

.method public isPerInstanceMetaClassChange()Z
    .locals 1

    .line 83
    iget-object v0, p0, Lgroovy/lang/MetaClassRegistryChangeEvent;->instance:Ljava/lang/Object;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method
