.class public Lgroovy/lang/ProxyMetaClass;
.super Lgroovy/lang/MetaClassImpl;
.source "ProxyMetaClass.java"

# interfaces
.implements Lgroovy/lang/AdaptingMetaClass;


# instance fields
.field protected adaptee:Lgroovy/lang/MetaClass;

.field protected interceptor:Lgroovy/lang/Interceptor;


# direct methods
.method public constructor <init>(Lgroovy/lang/MetaClassRegistry;Ljava/lang/Class;Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 55
    invoke-direct {p0, p1, p2}, Lgroovy/lang/MetaClassImpl;-><init>(Lgroovy/lang/MetaClassRegistry;Ljava/lang/Class;)V

    const-string p1, "adaptee must not be null"

    .line 56
    invoke-static {p3, p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lgroovy/lang/MetaClass;

    iput-object p1, p0, Lgroovy/lang/ProxyMetaClass;->adaptee:Lgroovy/lang/MetaClass;

    .line 57
    invoke-super {p0}, Lgroovy/lang/MetaClassImpl;->initialize()V

    return-void
.end method

.method private doCall(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;Lgroovy/lang/Interceptor;Ljava/util/function/Supplier;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/String;",
            "[",
            "Ljava/lang/Object;",
            "Lgroovy/lang/Interceptor;",
            "Ljava/util/function/Supplier<",
            "Ljava/lang/Object;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    if-nez p4, :cond_0

    .line 217
    invoke-interface {p5}, Ljava/util/function/Supplier;->get()Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 219
    :cond_0
    invoke-interface {p4, p1, p2, p3}, Lgroovy/lang/Interceptor;->beforeInvoke(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    .line 220
    invoke-interface {p4}, Lgroovy/lang/Interceptor;->doInvoke()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 221
    invoke-interface {p5}, Ljava/util/function/Supplier;->get()Ljava/lang/Object;

    move-result-object v0

    .line 223
    :cond_1
    invoke-interface {p4, p1, p2, p3, v0}, Lgroovy/lang/Interceptor;->afterInvoke(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public static getInstance(Ljava/lang/Class;)Lgroovy/lang/ProxyMetaClass;
    .locals 3

    .line 46
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v0

    .line 47
    invoke-interface {v0, p0}, Lgroovy/lang/MetaClassRegistry;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object v1

    .line 48
    new-instance v2, Lgroovy/lang/ProxyMetaClass;

    invoke-direct {v2, v0, p0, v1}, Lgroovy/lang/ProxyMetaClass;-><init>(Lgroovy/lang/MetaClassRegistry;Ljava/lang/Class;Lgroovy/lang/MetaClass;)V

    return-object v2
.end method


# virtual methods
.method public getAdaptee()Lgroovy/lang/MetaClass;
    .locals 1

    .line 67
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->adaptee:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getInterceptor()Lgroovy/lang/Interceptor;
    .locals 1

    .line 79
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    return-object v0
.end method

.method public getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;
    .locals 2

    .line 176
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    if-nez v0, :cond_0

    .line 177
    invoke-super/range {p0 .. p5}, Lgroovy/lang/MetaClassImpl;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 179
    :cond_0
    instance-of v1, v0, Lgroovy/lang/PropertyAccessInterceptor;

    if-eqz v1, :cond_2

    .line 180
    check-cast v0, Lgroovy/lang/PropertyAccessInterceptor;

    .line 182
    invoke-interface {v0, p2, p3}, Lgroovy/lang/PropertyAccessInterceptor;->beforeGet(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    .line 183
    iget-object v1, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    invoke-interface {v1}, Lgroovy/lang/Interceptor;->doInvoke()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 184
    invoke-super/range {p0 .. p5}, Lgroovy/lang/MetaClassImpl;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;

    move-result-object v0

    :cond_1
    return-object v0

    .line 188
    :cond_2
    invoke-super/range {p0 .. p5}, Lgroovy/lang/MetaClassImpl;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public declared-synchronized initialize()V
    .locals 1

    monitor-enter p0

    .line 62
    :try_start_0
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->adaptee:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->initialize()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 63
    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public invokeConstructor([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 164
    iget-object v1, p0, Lgroovy/lang/ProxyMetaClass;->theClass:Ljava/lang/Class;

    iget-object v4, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    new-instance v5, Lgroovy/lang/ProxyMetaClass$$ExternalSyntheticLambda3;

    invoke-direct {v5, p0, p1}, Lgroovy/lang/ProxyMetaClass$$ExternalSyntheticLambda3;-><init>(Lgroovy/lang/ProxyMetaClass;[Ljava/lang/Object;)V

    const-string v2, "ctor"

    move-object v0, p0

    move-object v3, p1

    invoke-direct/range {v0 .. v5}, Lgroovy/lang/ProxyMetaClass;->doCall(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;Lgroovy/lang/Interceptor;Ljava/util/function/Supplier;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)Ljava/lang/Object;
    .locals 11

    move-object v8, p0

    .line 142
    iget-object v9, v8, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    new-instance v10, Lgroovy/lang/ProxyMetaClass$$ExternalSyntheticLambda0;

    move-object v0, v10

    move-object v1, p0

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    move-object v5, p4

    move/from16 v6, p5

    move/from16 v7, p6

    invoke-direct/range {v0 .. v7}, Lgroovy/lang/ProxyMetaClass$$ExternalSyntheticLambda0;-><init>(Lgroovy/lang/ProxyMetaClass;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)V

    move-object v0, p0

    move-object v1, p2

    move-object v2, p3

    move-object v3, p4

    move-object v4, v9

    move-object v5, v10

    invoke-direct/range {v0 .. v5}, Lgroovy/lang/ProxyMetaClass;->doCall(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;Lgroovy/lang/Interceptor;Ljava/util/function/Supplier;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public invokeMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 131
    iget-object v4, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    new-instance v5, Lgroovy/lang/ProxyMetaClass$$ExternalSyntheticLambda1;

    invoke-direct {v5, p0, p1, p2, p3}, Lgroovy/lang/ProxyMetaClass$$ExternalSyntheticLambda1;-><init>(Lgroovy/lang/ProxyMetaClass;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    invoke-direct/range {v0 .. v5}, Lgroovy/lang/ProxyMetaClass;->doCall(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;Lgroovy/lang/Interceptor;Ljava/util/function/Supplier;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeStaticMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 153
    iget-object v4, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    new-instance v5, Lgroovy/lang/ProxyMetaClass$$ExternalSyntheticLambda2;

    invoke-direct {v5, p0, p1, p2, p3}, Lgroovy/lang/ProxyMetaClass$$ExternalSyntheticLambda2;-><init>(Lgroovy/lang/ProxyMetaClass;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    invoke-direct/range {v0 .. v5}, Lgroovy/lang/ProxyMetaClass;->doCall(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;Lgroovy/lang/Interceptor;Ljava/util/function/Supplier;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic lambda$invokeConstructor$3$groovy-lang-ProxyMetaClass([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 164
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->adaptee:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1}, Lgroovy/lang/MetaClass;->invokeConstructor([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic lambda$invokeMethod$0$groovy-lang-ProxyMetaClass(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 131
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->adaptee:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic lambda$invokeMethod$1$groovy-lang-ProxyMetaClass(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)Ljava/lang/Object;
    .locals 7

    .line 142
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->adaptee:Lgroovy/lang/MetaClass;

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    invoke-interface/range {v0 .. v6}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public synthetic lambda$invokeStaticMethod$2$groovy-lang-ProxyMetaClass(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 153
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->adaptee:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeStaticMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public setAdaptee(Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 72
    iput-object p1, p0, Lgroovy/lang/ProxyMetaClass;->adaptee:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setInterceptor(Lgroovy/lang/Interceptor;)V
    .locals 0

    .line 86
    iput-object p1, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    return-void
.end method

.method public setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V
    .locals 2

    .line 200
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    if-nez v0, :cond_0

    .line 201
    invoke-super/range {p0 .. p6}, Lgroovy/lang/MetaClassImpl;->setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V

    .line 203
    :cond_0
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    instance-of v1, v0, Lgroovy/lang/PropertyAccessInterceptor;

    if-eqz v1, :cond_1

    .line 204
    check-cast v0, Lgroovy/lang/PropertyAccessInterceptor;

    .line 206
    invoke-interface {v0, p2, p3, p4}, Lgroovy/lang/PropertyAccessInterceptor;->beforeSet(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    .line 207
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->interceptor:Lgroovy/lang/Interceptor;

    invoke-interface {v0}, Lgroovy/lang/Interceptor;->doInvoke()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 208
    invoke-super/range {p0 .. p6}, Lgroovy/lang/MetaClassImpl;->setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V

    goto :goto_0

    .line 211
    :cond_1
    invoke-super/range {p0 .. p6}, Lgroovy/lang/MetaClassImpl;->setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V

    :cond_2
    :goto_0
    return-void
.end method

.method public use(Lgroovy/lang/Closure;)Ljava/lang/Object;
    .locals 3

    .line 97
    iget-object v0, p0, Lgroovy/lang/ProxyMetaClass;->registry:Lgroovy/lang/MetaClassRegistry;

    iget-object v1, p0, Lgroovy/lang/ProxyMetaClass;->theClass:Ljava/lang/Class;

    invoke-interface {v0, v1}, Lgroovy/lang/MetaClassRegistry;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 98
    iget-object v1, p0, Lgroovy/lang/ProxyMetaClass;->registry:Lgroovy/lang/MetaClassRegistry;

    iget-object v2, p0, Lgroovy/lang/ProxyMetaClass;->theClass:Ljava/lang/Class;

    invoke-interface {v1, v2, p0}, Lgroovy/lang/MetaClassRegistry;->setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;)V

    .line 100
    :try_start_0
    invoke-virtual {p1}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 102
    iget-object v1, p0, Lgroovy/lang/ProxyMetaClass;->registry:Lgroovy/lang/MetaClassRegistry;

    iget-object v2, p0, Lgroovy/lang/ProxyMetaClass;->theClass:Ljava/lang/Class;

    invoke-interface {v1, v2, v0}, Lgroovy/lang/MetaClassRegistry;->setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;)V

    return-object p1

    :catchall_0
    move-exception p1

    iget-object v1, p0, Lgroovy/lang/ProxyMetaClass;->registry:Lgroovy/lang/MetaClassRegistry;

    iget-object v2, p0, Lgroovy/lang/ProxyMetaClass;->theClass:Ljava/lang/Class;

    invoke-interface {v1, v2, v0}, Lgroovy/lang/MetaClassRegistry;->setMetaClass(Ljava/lang/Class;Lgroovy/lang/MetaClass;)V

    .line 103
    throw p1
.end method

.method public use(Lgroovy/lang/GroovyObject;Lgroovy/lang/Closure;)Ljava/lang/Object;
    .locals 1

    .line 114
    invoke-interface {p1}, Lgroovy/lang/GroovyObject;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    .line 115
    invoke-interface {p1, p0}, Lgroovy/lang/GroovyObject;->setMetaClass(Lgroovy/lang/MetaClass;)V

    .line 117
    :try_start_0
    invoke-virtual {p2}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;

    move-result-object p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 119
    invoke-interface {p1, v0}, Lgroovy/lang/GroovyObject;->setMetaClass(Lgroovy/lang/MetaClass;)V

    return-object p2

    :catchall_0
    move-exception p2

    invoke-interface {p1, v0}, Lgroovy/lang/GroovyObject;->setMetaClass(Lgroovy/lang/MetaClass;)V

    .line 120
    throw p2
.end method
