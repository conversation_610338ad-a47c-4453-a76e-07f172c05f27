.class public Lgroovy/lang/MetaArrayLengthProperty;
.super Lgroovy/lang/MetaProperty;
.source "MetaArrayLengthProperty.java"


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 31
    sget-object v0, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    const-string v1, "length"

    invoke-direct {p0, v1, v0}, Lgroovy/lang/MetaProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;)V

    return-void
.end method


# virtual methods
.method public getProperty(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 41
    invoke-static {p1}, Ljava/lang/reflect/Array;->getLength(Ljava/lang/Object;)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1
.end method

.method public setProperty(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 52
    new-instance p2, Lgroovy/lang/ReadOnlyPropertyException;

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    const-string v0, "length"

    invoke-direct {p2, v0, p1}, Lgroovy/lang/ReadOnlyPropertyException;-><init>(Ljava/lang/String;Ljava/lang/Class;)V

    throw p2
.end method
