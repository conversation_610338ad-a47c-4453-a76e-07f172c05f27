.class final Lgroovy/lang/TrampolineClosure;
.super Lgroovy/lang/Closure;
.source "TrampolineClosure.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<V:",
        "Ljava/lang/Object;",
        ">",
        "Lgroovy/lang/Closure<",
        "TV;>;"
    }
.end annotation


# static fields
.field private static final serialVersionUID:J = -0x38d927d89ea94745L


# instance fields
.field private final original:Lgroovy/lang/Closure;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>(Lgroovy/lang/Closure;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/lang/Closure<",
            "TV;>;)V"
        }
    .end annotation

    .line 35
    invoke-virtual {p1}, Lgroovy/lang/Closure;->getOwner()Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p1}, Lgroovy/lang/Closure;->getDelegate()Ljava/lang/Object;

    move-result-object v1

    invoke-direct {p0, v0, v1}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 36
    iput-object p1, p0, Lgroovy/lang/TrampolineClosure;->original:Lgroovy/lang/Closure;

    return-void
.end method

.method private loop(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 86
    :goto_0
    instance-of v0, p1, Lgroovy/lang/TrampolineClosure;

    if-eqz v0, :cond_0

    .line 87
    check-cast p1, Lgroovy/lang/TrampolineClosure;

    iget-object p1, p1, Lgroovy/lang/TrampolineClosure;->original:Lgroovy/lang/Closure;

    invoke-virtual {p1}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;

    move-result-object p1

    goto :goto_0

    :cond_0
    return-object p1
.end method


# virtual methods
.method public call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TV;"
        }
    .end annotation

    .line 61
    iget-object v0, p0, Lgroovy/lang/TrampolineClosure;->original:Lgroovy/lang/Closure;

    invoke-virtual {v0}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;

    move-result-object v0

    invoke-direct {p0, v0}, Lgroovy/lang/TrampolineClosure;->loop(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public call(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 70
    iget-object v0, p0, Lgroovy/lang/TrampolineClosure;->original:Lgroovy/lang/Closure;

    invoke-virtual {v0, p1}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovy/lang/TrampolineClosure;->loop(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public varargs call([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 79
    iget-object v0, p0, Lgroovy/lang/TrampolineClosure;->original:Lgroovy/lang/Closure;

    invoke-virtual {v0, p1}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovy/lang/TrampolineClosure;->loop(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getMaximumNumberOfParameters()I
    .locals 1

    .line 44
    iget-object v0, p0, Lgroovy/lang/TrampolineClosure;->original:Lgroovy/lang/Closure;

    iget v0, v0, Lgroovy/lang/Closure;->maximumNumberOfParameters:I

    return v0
.end method

.method public getParameterTypes()[Ljava/lang/Class;
    .locals 1

    .line 52
    iget-object v0, p0, Lgroovy/lang/TrampolineClosure;->original:Lgroovy/lang/Closure;

    iget-object v0, v0, Lgroovy/lang/Closure;->parameterTypes:[Ljava/lang/Class;

    return-object v0
.end method

.method public trampoline()Lgroovy/lang/Closure;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    return-object p0
.end method

.method public varargs trampoline([Ljava/lang/Object;)Lgroovy/lang/Closure;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 99
    new-instance v0, Lgroovy/lang/TrampolineClosure;

    iget-object v1, p0, Lgroovy/lang/TrampolineClosure;->original:Lgroovy/lang/Closure;

    invoke-virtual {v1, p1}, Lgroovy/lang/Closure;->curry([Ljava/lang/Object;)Lgroovy/lang/Closure;

    move-result-object p1

    invoke-direct {v0, p1}, Lgroovy/lang/TrampolineClosure;-><init>(Lgroovy/lang/Closure;)V

    return-object v0
.end method
