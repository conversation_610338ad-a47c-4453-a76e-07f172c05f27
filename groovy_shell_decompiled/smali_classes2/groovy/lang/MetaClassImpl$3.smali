.class Lgroovy/lang/MetaClassImpl$3;
.super Lgroovy/lang/MetaProperty;
.source "MetaClassImpl.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/lang/MetaClassImpl;->getEffectiveGetMetaProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Z)Lgroovy/lang/MetaProperty;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final mc:Lgroovy/lang/MetaClass;

.field final synthetic this$0:Lgroovy/lang/MetaClassImpl;

.field final synthetic val$object:Ljava/lang/Object;

.field final synthetic val$sender:Ljava/lang/Class;

.field final synthetic val$useSuper:Z


# direct methods
.method constructor <init>(Lgroovy/lang/MetaClassImpl;Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Class;Z)V
    .locals 0

    .line 1965
    iput-object p1, p0, Lgroovy/lang/MetaClassImpl$3;->this$0:Lgroovy/lang/MetaClassImpl;

    iput-object p4, p0, Lgroovy/lang/MetaClassImpl$3;->val$object:Ljava/lang/Object;

    iput-object p5, p0, Lgroovy/lang/MetaClassImpl$3;->val$sender:Ljava/lang/Class;

    iput-boolean p6, p0, Lgroovy/lang/MetaClassImpl$3;->val$useSuper:Z

    invoke-direct {p0, p2, p3}, Lgroovy/lang/MetaProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;)V

    .line 1966
    iget-object p1, p1, Lgroovy/lang/MetaClassImpl;->registry:Lgroovy/lang/MetaClassRegistry;

    check-cast p4, Ljava/lang/Class;

    invoke-interface {p1, p4}, Lgroovy/lang/MetaClassRegistry;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object p1

    iput-object p1, p0, Lgroovy/lang/MetaClassImpl$3;->mc:Lgroovy/lang/MetaClass;

    return-void
.end method


# virtual methods
.method public getProperty(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1969
    iget-object v0, p0, Lgroovy/lang/MetaClassImpl$3;->mc:Lgroovy/lang/MetaClass;

    iget-object v1, p0, Lgroovy/lang/MetaClassImpl$3;->val$sender:Ljava/lang/Class;

    iget-object v3, p0, Lgroovy/lang/MetaClassImpl$3;->name:Ljava/lang/String;

    iget-boolean v4, p0, Lgroovy/lang/MetaClassImpl$3;->val$useSuper:Z

    const/4 v5, 0x0

    move-object v2, p1

    invoke-interface/range {v0 .. v5}, Lgroovy/lang/MetaClass;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public setProperty(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0

    .line 1973
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method
