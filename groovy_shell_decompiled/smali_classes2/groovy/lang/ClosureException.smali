.class public Lgroovy/lang/ClosureException;
.super Ljava/lang/RuntimeException;
.source "ClosureException.java"


# static fields
.field private static final serialVersionUID:J = -0xd1f52688c0acfcfL


# instance fields
.field private final closure:Lgroovy/lang/Closure;


# direct methods
.method public constructor <init>(Lgroovy/lang/Closure;Ljava/lang/Throwable;)V
    .locals 2

    .line 30
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Exception thrown by call to closure: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " reason: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0, p2}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    .line 31
    iput-object p1, p0, Lgroovy/lang/ClosureException;->closure:Lgroovy/lang/Closure;

    return-void
.end method


# virtual methods
.method public getClosure()Lgroovy/lang/Closure;
    .locals 1

    .line 35
    iget-object v0, p0, Lgroovy/lang/ClosureException;->closure:Lgroovy/lang/Closure;

    return-object v0
.end method
