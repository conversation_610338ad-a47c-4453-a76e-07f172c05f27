.class Lgroovy/lang/MetaClassImpl$MethodIndex;
.super Lgroovy/lang/MetaClassImpl$Index;
.source "MetaClassImpl.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/lang/MetaClassImpl;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "MethodIndex"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 3928
    invoke-direct {p0}, Lgroovy/lang/MetaClassImpl$Index;-><init>()V

    return-void
.end method

.method public constructor <init>(I)V
    .locals 0

    .line 3924
    invoke-direct {p0, p1}, Lgroovy/lang/MetaClassImpl$Index;-><init>(I)V

    return-void
.end method

.method public constructor <init>(Z)V
    .locals 0

    const/4 p1, 0x0

    .line 3920
    invoke-direct {p0, p1}, Lgroovy/lang/MetaClassImpl$Index;-><init>(Z)V

    return-void
.end method


# virtual methods
.method protected clone()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/CloneNotSupportedException;
        }
    .end annotation

    .line 3936
    invoke-super {p0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method copy()Lgroovy/lang/MetaClassImpl$MethodIndex;
    .locals 2

    .line 3932
    new-instance v0, Lgroovy/lang/MetaClassImpl$MethodIndex;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovy/lang/MetaClassImpl$MethodIndex;-><init>(Z)V

    invoke-static {}, Lgroovy/lang/MetaClassImpl;->access$400()Lorg/codehaus/groovy/util/SingleKeyHashMap$Copier;

    move-result-object v1

    invoke-static {v0, p0, v1}, Lorg/codehaus/groovy/util/SingleKeyHashMap;->copy(Lorg/codehaus/groovy/util/SingleKeyHashMap;Lorg/codehaus/groovy/util/SingleKeyHashMap;Lorg/codehaus/groovy/util/SingleKeyHashMap$Copier;)Lorg/codehaus/groovy/util/SingleKeyHashMap;

    move-result-object v0

    check-cast v0, Lgroovy/lang/MetaClassImpl$MethodIndex;

    return-object v0
.end method
