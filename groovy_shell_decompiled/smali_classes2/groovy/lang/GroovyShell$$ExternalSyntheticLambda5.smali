.class public final synthetic Lgroovy/lang/GroovyShell$$ExternalSyntheticLambda5;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/security/PrivilegedExceptionAction;


# instance fields
.field public final synthetic f$0:Lgroovy/lang/GroovyShell;

.field public final synthetic f$1:Ljava/io/File;


# direct methods
.method public synthetic constructor <init>(Lgroovy/lang/GroovyShell;Ljava/io/File;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovy/lang/GroovyShell$$ExternalSyntheticLambda5;->f$0:Lgroovy/lang/GroovyShell;

    iput-object p2, p0, Lgroovy/lang/GroovyShell$$ExternalSyntheticLambda5;->f$1:Ljava/io/File;

    return-void
.end method


# virtual methods
.method public final run()Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lgroovy/lang/GroovyShell$$ExternalSyntheticLambda5;->f$0:Lgroovy/lang/GroovyShell;

    iget-object v1, p0, Lgroovy/lang/GroovyShell$$ExternalSyntheticLambda5;->f$1:Ljava/io/File;

    invoke-virtual {v0, v1}, Lgroovy/lang/GroovyShell;->lambda$run$1$groovy-lang-GroovyShell(Ljava/io/File;)Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method
