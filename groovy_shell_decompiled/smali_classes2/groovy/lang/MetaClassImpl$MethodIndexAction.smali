.class abstract Lgroovy/lang/MetaClassImpl$MethodIndexAction;
.super Ljava/lang/Object;
.source "MetaClassImpl.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/lang/MetaClassImpl;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x402
    name = "MethodIndexAction"
.end annotation


# instance fields
.field final synthetic this$0:Lgroovy/lang/MetaClassImpl;


# direct methods
.method private constructor <init>(Lgroovy/lang/MetaClassImpl;)V
    .locals 0

    .line 3791
    iput-object p1, p0, Lgroovy/lang/MetaClassImpl$MethodIndexAction;->this$0:Lgroovy/lang/MetaClassImpl;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaClassImpl$1;)V
    .locals 0

    .line 3791
    invoke-direct {p0, p1}, Lgroovy/lang/MetaClassImpl$MethodIndexAction;-><init>(Lgroovy/lang/MetaClassImpl;)V

    return-void
.end method


# virtual methods
.method public iterate()V
    .locals 6

    .line 3793
    iget-object v0, p0, Lgroovy/lang/MetaClassImpl$MethodIndexAction;->this$0:Lgroovy/lang/MetaClassImpl;

    iget-object v0, v0, Lgroovy/lang/MetaClassImpl;->metaMethodIndex:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;

    iget-object v0, v0, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex;->methodHeaders:Lorg/codehaus/groovy/util/SingleKeyHashMap;

    invoke-virtual {v0}, Lorg/codehaus/groovy/util/SingleKeyHashMap;->getTable()[Lorg/codehaus/groovy/util/ComplexKeyHashMap$Entry;

    move-result-object v0

    .line 3794
    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-eq v2, v1, :cond_3

    .line 3796
    aget-object v3, v0, v2

    check-cast v3, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;

    :goto_1
    if-eqz v3, :cond_2

    .line 3800
    invoke-virtual {v3}, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;->getKey()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Class;

    .line 3802
    invoke-virtual {p0, v4}, Lgroovy/lang/MetaClassImpl$MethodIndexAction;->skipClass(Ljava/lang/Class;)Z

    move-result v5

    if-eqz v5, :cond_0

    goto :goto_3

    .line 3804
    :cond_0
    invoke-virtual {v3}, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;->getValue()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;

    .line 3805
    iget-object v5, v5, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header;->head:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    :goto_2
    if-eqz v5, :cond_1

    .line 3806
    invoke-virtual {p0, v4, v5}, Lgroovy/lang/MetaClassImpl$MethodIndexAction;->methodNameAction(Ljava/lang/Class;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;)V

    .line 3805
    iget-object v5, v5, Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;->nextClassEntry:Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;

    goto :goto_2

    .line 3798
    :cond_1
    :goto_3
    iget-object v3, v3, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;->next:Lorg/codehaus/groovy/util/ComplexKeyHashMap$Entry;

    check-cast v3, Lorg/codehaus/groovy/util/SingleKeyHashMap$Entry;

    goto :goto_1

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method public abstract methodNameAction(Ljava/lang/Class;Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;",
            "Lorg/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry;",
            ")V"
        }
    .end annotation
.end method

.method public skipClass(Ljava/lang/Class;)Z
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)Z"
        }
    .end annotation

    const/4 p1, 0x0

    return p1
.end method
