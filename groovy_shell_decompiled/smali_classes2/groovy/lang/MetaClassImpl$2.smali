.class Lgroovy/lang/MetaClassImpl$2;
.super Lorg/codehaus/groovy/runtime/metaclass/TransformMetaMethod;
.source "MetaClassImpl.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/lang/MetaClassImpl;->createTransformMetaMethod(Lgroovy/lang/MetaMethod;)Lgroovy/lang/MetaMethod;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovy/lang/MetaClassImpl;


# direct methods
.method constructor <init>(Lgroovy/lang/MetaClassImpl;Lgroovy/lang/MetaMethod;)V
    .locals 0

    .line 1293
    iput-object p1, p0, Lgroovy/lang/MetaClassImpl$2;->this$0:Lgroovy/lang/MetaClassImpl;

    invoke-direct {p0, p2}, Lorg/codehaus/groovy/runtime/metaclass/TransformMetaMethod;-><init>(Lgroovy/lang/MetaMethod;)V

    return-void
.end method


# virtual methods
.method public invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    .line 1295
    aget-object p2, p2, v0

    .line 1296
    check-cast p2, Ljava/util/List;

    .line 1297
    invoke-interface {p2}, Ljava/util/List;->toArray()[Ljava/lang/Object;

    move-result-object p2

    .line 1298
    invoke-super {p0, p1, p2}, Lorg/codehaus/groovy/runtime/metaclass/TransformMetaMethod;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
