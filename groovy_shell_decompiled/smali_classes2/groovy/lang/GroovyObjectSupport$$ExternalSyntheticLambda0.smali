.class public final synthetic Lgroovy/lang/GroovyObjectSupport$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/Supplier;


# instance fields
.field public final synthetic f$0:Lgroovy/lang/GroovyObjectSupport;


# direct methods
.method public synthetic constructor <init>(Lgroovy/lang/GroovyObjectSupport;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovy/lang/GroovyObjectSupport$$ExternalSyntheticLambda0;->f$0:Lgroovy/lang/GroovyObjectSupport;

    return-void
.end method


# virtual methods
.method public final get()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lgroovy/lang/GroovyObjectSupport$$ExternalSyntheticLambda0;->f$0:Lgroovy/lang/GroovyObjectSupport;

    invoke-static {v0}, Lgroovy/lang/GroovyObjectSupport;->$r8$lambda$n9WI3X_msQEG_pP9hZ5U5DG__0s(Lgroovy/lang/GroovyObjectSupport;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method
