.class public Lgroovy/lang/ObjectRange;
.super Ljava/util/AbstractList;
.source "ObjectRange.java"

# interfaces
.implements Lgroovy/lang/Range;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/lang/ObjectRange$StepIterator;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/util/AbstractList<",
        "Ljava/lang/Comparable;",
        ">;",
        "Lgroovy/lang/Range<",
        "Ljava/lang/Comparable;",
        ">;"
    }
.end annotation


# instance fields
.field private final from:Ljava/lang/Comparable;

.field private final reverse:Z

.field private size:I

.field private final to:Ljava/lang/Comparable;


# direct methods
.method public constructor <init>(Ljava/lang/Comparable;Ljava/lang/Comparable;)V
    .locals 1

    const/4 v0, 0x0

    .line 71
    invoke-direct {p0, p1, p2, v0}, Lgroovy/lang/ObjectRange;-><init>(Ljava/lang/Comparable;Ljava/lang/Comparable;Ljava/lang/Boolean;)V

    return-void
.end method

.method private constructor <init>(Ljava/lang/Comparable;Ljava/lang/Comparable;Ljava/lang/Boolean;)V
    .locals 3

    .line 92
    invoke-direct {p0}, Ljava/util/AbstractList;-><init>()V

    const/4 v0, -0x1

    .line 56
    iput v0, p0, Lgroovy/lang/ObjectRange;->size:I

    if-eqz p1, :cond_e

    if-eqz p2, :cond_d

    if-nez p3, :cond_1

    .line 100
    invoke-static {p1, p2}, Lgroovy/lang/ObjectRange;->areReversed(Ljava/lang/Comparable;Ljava/lang/Comparable;)Z

    move-result p3

    if-eqz p3, :cond_0

    move-object v2, p2

    move-object p2, p1

    move-object p1, v2

    .line 107
    :cond_0
    iput-boolean p3, p0, Lgroovy/lang/ObjectRange;->reverse:Z

    goto :goto_0

    .line 109
    :cond_1
    invoke-virtual {p3}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p3

    iput-boolean p3, p0, Lgroovy/lang/ObjectRange;->reverse:Z

    .line 112
    :goto_0
    instance-of p3, p1, Ljava/lang/Short;

    if-eqz p3, :cond_2

    .line 113
    check-cast p1, Ljava/lang/Short;

    invoke-virtual {p1}, Ljava/lang/Short;->intValue()I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_1

    .line 114
    :cond_2
    instance-of p3, p1, Ljava/lang/Float;

    if-eqz p3, :cond_3

    .line 115
    check-cast p1, Ljava/lang/Float;

    invoke-virtual {p1}, Ljava/lang/Float;->doubleValue()D

    move-result-wide v0

    invoke-static {v0, v1}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p1

    .line 117
    :cond_3
    :goto_1
    instance-of p3, p2, Ljava/lang/Short;

    if-eqz p3, :cond_4

    .line 118
    check-cast p2, Ljava/lang/Short;

    invoke-virtual {p2}, Ljava/lang/Short;->intValue()I

    move-result p2

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    goto :goto_2

    .line 119
    :cond_4
    instance-of p3, p2, Ljava/lang/Float;

    if-eqz p3, :cond_5

    .line 120
    check-cast p2, Ljava/lang/Float;

    invoke-virtual {p2}, Ljava/lang/Float;->doubleValue()D

    move-result-wide p2

    invoke-static {p2, p3}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object p2

    .line 123
    :cond_5
    :goto_2
    instance-of p3, p1, Ljava/lang/Integer;

    if-eqz p3, :cond_6

    instance-of p3, p2, Ljava/lang/Long;

    if-eqz p3, :cond_6

    .line 124
    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->longValue()J

    move-result-wide v0

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p1

    goto :goto_3

    .line 125
    :cond_6
    instance-of p3, p2, Ljava/lang/Integer;

    if-eqz p3, :cond_7

    instance-of p3, p1, Ljava/lang/Long;

    if-eqz p3, :cond_7

    .line 126
    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->longValue()J

    move-result-wide p2

    invoke-static {p2, p3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p2

    .line 141
    :cond_7
    :goto_3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p3

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    if-eq p3, v0, :cond_c

    instance-of p3, p1, Ljava/lang/Number;

    if-eqz p3, :cond_8

    instance-of p3, p2, Ljava/lang/Number;

    if-eqz p3, :cond_8

    goto :goto_5

    .line 147
    :cond_8
    invoke-static {p1}, Lgroovy/lang/ObjectRange;->normaliseStringType(Ljava/lang/Comparable;)Ljava/lang/Comparable;

    move-result-object p3

    .line 148
    invoke-static {p2}, Lgroovy/lang/ObjectRange;->normaliseStringType(Ljava/lang/Comparable;)Ljava/lang/Comparable;

    move-result-object v0

    .line 150
    instance-of v1, p3, Ljava/lang/Number;

    if-eqz v1, :cond_9

    instance-of v1, v0, Ljava/lang/Number;

    if-eqz v1, :cond_9

    .line 151
    iput-object p3, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    .line 152
    iput-object v0, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    goto :goto_6

    .line 158
    :cond_9
    iget-boolean p3, p0, Lgroovy/lang/ObjectRange;->reverse:Z

    if-eqz p3, :cond_a

    move-object p3, p2

    goto :goto_4

    :cond_a
    move-object p3, p1

    .line 159
    :goto_4
    instance-of v0, p3, Ljava/lang/String;

    if-nez v0, :cond_b

    instance-of p3, p3, Ljava/lang/Number;

    if-nez p3, :cond_b

    .line 164
    iput-object p1, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    .line 165
    iput-object p2, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    goto :goto_6

    .line 161
    :cond_b
    new-instance p3, Ljava/lang/IllegalArgumentException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Incompatible Argument classes for ObjectRange "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, ", "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p3, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p3

    .line 143
    :cond_c
    :goto_5
    iput-object p1, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    .line 144
    iput-object p2, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    .line 168
    :goto_6
    invoke-virtual {p0}, Lgroovy/lang/ObjectRange;->checkBoundaryCompatibility()V

    return-void

    .line 97
    :cond_d
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Must specify a non-null value for the \'to\' index in a Range"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 94
    :cond_e
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "Must specify a non-null value for the \'from\' index in a Range"

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public constructor <init>(Ljava/lang/Comparable;Ljava/lang/Comparable;Z)V
    .locals 0

    .line 81
    invoke-static {p3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p3

    invoke-direct {p0, p1, p2, p3}, Lgroovy/lang/ObjectRange;-><init>(Ljava/lang/Comparable;Ljava/lang/Comparable;Ljava/lang/Boolean;)V

    return-void
.end method

.method static synthetic access$100(Lgroovy/lang/ObjectRange;)Ljava/lang/Comparable;
    .locals 0

    .line 42
    iget-object p0, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    return-object p0
.end method

.method static synthetic access$200(Lgroovy/lang/ObjectRange;)Ljava/lang/Comparable;
    .locals 0

    .line 42
    iget-object p0, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    return-object p0
.end method

.method private static areReversed(Ljava/lang/Comparable;Ljava/lang/Comparable;)Z
    .locals 4

    .line 200
    :try_start_0
    invoke-static {p0, p1}, Lorg/codehaus/groovy/runtime/ScriptBytecodeAdapter;->compareGreaterThan(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p0
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_0

    return p0

    :catch_0
    move-exception v0

    .line 202
    new-instance v1, Ljava/lang/IllegalArgumentException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unable to create range due to incompatible types: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v2, ".."

    invoke-virtual {p0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string p1, " (possible missing brackets around range?)"

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v1, p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v1
.end method

.method private static normaliseStringType(Ljava/lang/Comparable;)Ljava/lang/Comparable;
    .locals 2

    .line 527
    instance-of v0, p0, Ljava/lang/Character;

    if-eqz v0, :cond_0

    .line 528
    check-cast p0, Ljava/lang/Character;

    invoke-virtual {p0}, Ljava/lang/Character;->charValue()C

    move-result p0

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    return-object p0

    .line 530
    :cond_0
    instance-of v0, p0, Ljava/lang/String;

    if-eqz v0, :cond_1

    .line 531
    check-cast p0, Ljava/lang/String;

    .line 533
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_1

    const/4 v0, 0x0

    .line 534
    invoke-virtual {p0, v0}, Ljava/lang/String;->charAt(I)C

    move-result p0

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    :cond_1
    return-object p0
.end method

.method private setSize(I)V
    .locals 1

    .line 279
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    const-string v0, "size must not be changed"

    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method protected checkBoundaryCompatibility()V
    .locals 6

    .line 176
    iget-object v0, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    instance-of v1, v0, Ljava/lang/String;

    if-eqz v1, :cond_4

    iget-object v1, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    instance-of v1, v1, Ljava/lang/String;

    if-eqz v1, :cond_4

    .line 179
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    .line 180
    iget-object v1, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    .line 181
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v2

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v3

    if-ne v2, v3, :cond_3

    .line 184
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v2

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_1

    .line 187
    invoke-virtual {v0, v3}, Ljava/lang/String;->charAt(I)C

    move-result v4

    invoke-virtual {v1, v3}, Ljava/lang/String;->charAt(I)C

    move-result v5

    if-eq v4, v5, :cond_0

    goto :goto_1

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    add-int/lit8 v2, v2, -0x1

    if-lt v3, v2, :cond_2

    goto :goto_2

    .line 193
    :cond_2
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "Incompatible Strings for Range: String#next() will not reach the expected value"

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 182
    :cond_3
    new-instance v0, Ljava/lang/IllegalArgumentException;

    const-string v1, "Incompatible Strings for Range: different length"

    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_4
    :goto_2
    return-void
.end method

.method protected compareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I
    .locals 0

    .line 271
    invoke-static {p1, p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->numberAwareCompareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I

    move-result p1

    return p1
.end method

.method public contains(Ljava/lang/Object;)Z
    .locals 4

    .line 385
    new-instance v0, Lgroovy/lang/ObjectRange$StepIterator;

    const/4 v1, 0x1

    const/4 v2, 0x0

    invoke-direct {v0, p0, v1, v2}, Lgroovy/lang/ObjectRange$StepIterator;-><init>(Lgroovy/lang/ObjectRange;ILgroovy/lang/ObjectRange$1;)V

    const/4 v2, 0x0

    if-nez p1, :cond_0

    return v2

    .line 389
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    .line 390
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    invoke-static {p1, v3}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    return v1

    :cond_1
    return v2
.end method

.method public containsWithinBounds(Ljava/lang/Object;)Z
    .locals 1

    .line 263
    instance-of v0, p1, Ljava/lang/Comparable;

    if-eqz v0, :cond_2

    .line 264
    iget-object v0, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    check-cast p1, Ljava/lang/Comparable;

    invoke-virtual {p0, v0, p1}, Lgroovy/lang/ObjectRange;->compareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I

    move-result v0

    if-eqz v0, :cond_1

    if-gez v0, :cond_0

    .line 265
    iget-object v0, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    invoke-virtual {p0, v0, p1}, Lgroovy/lang/ObjectRange;->compareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I

    move-result p1

    if-ltz p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1

    .line 267
    :cond_2
    invoke-virtual {p0, p1}, Lgroovy/lang/ObjectRange;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method protected decrement(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    const-string v0, "previous"

    const/4 v1, 0x0

    .line 520
    invoke-static {p1, v0, v1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public equals(Lgroovy/lang/ObjectRange;)Z
    .locals 2

    if-eqz p1, :cond_0

    .line 217
    iget-boolean v0, p0, Lgroovy/lang/ObjectRange;->reverse:Z

    iget-boolean v1, p1, Lgroovy/lang/ObjectRange;->reverse:Z

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    iget-object v1, p1, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    .line 219
    invoke-static {v0, v1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    iget-object p1, p1, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    .line 220
    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/typehandling/DefaultTypeTransformation;->compareEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 1

    .line 207
    instance-of v0, p1, Lgroovy/lang/ObjectRange;

    if-eqz v0, :cond_0

    check-cast p1, Lgroovy/lang/ObjectRange;

    invoke-virtual {p0, p1}, Lgroovy/lang/ObjectRange;->equals(Lgroovy/lang/ObjectRange;)Z

    move-result p1

    goto :goto_0

    :cond_0
    invoke-super {p0, p1}, Ljava/util/AbstractList;->equals(Ljava/lang/Object;)Z

    move-result p1

    :goto_0
    return p1
.end method

.method public get(I)Ljava/lang/Comparable;
    .locals 4

    const-string v0, "Index: "

    if-ltz p1, :cond_2

    .line 243
    new-instance v1, Lgroovy/lang/ObjectRange$StepIterator;

    const/4 v2, 0x0

    const/4 v3, 0x1

    invoke-direct {v1, p0, v3, v2}, Lgroovy/lang/ObjectRange$StepIterator;-><init>(Lgroovy/lang/ObjectRange;ILgroovy/lang/ObjectRange$1;)V

    .line 245
    invoke-virtual {v1}, Lgroovy/lang/ObjectRange$StepIterator;->next()Ljava/lang/Comparable;

    move-result-object v2

    const/4 v3, 0x0

    :goto_0
    if-ge v3, p1, :cond_1

    .line 247
    invoke-virtual {v1}, Lgroovy/lang/ObjectRange$StepIterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    .line 250
    invoke-virtual {v1}, Lgroovy/lang/ObjectRange$StepIterator;->next()Ljava/lang/Comparable;

    move-result-object v2

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 248
    :cond_0
    new-instance v1, Ljava/lang/IndexOutOfBoundsException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " is too big for range: "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, p1}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_1
    return-object v2

    .line 241
    :cond_2
    new-instance v1, Ljava/lang/IndexOutOfBoundsException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " should not be negative"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, p1}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw v1
.end method

.method public bridge synthetic get(I)Ljava/lang/Object;
    .locals 0

    .line 42
    invoke-virtual {p0, p1}, Lgroovy/lang/ObjectRange;->get(I)Ljava/lang/Comparable;

    move-result-object p1

    return-object p1
.end method

.method public getFrom()Ljava/lang/Comparable;
    .locals 1

    .line 225
    iget-object v0, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    return-object v0
.end method

.method public getTo()Ljava/lang/Comparable;
    .locals 1

    .line 230
    iget-object v0, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    return-object v0
.end method

.method protected increment(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    const-string v0, "next"

    const/4 v1, 0x0

    .line 510
    invoke-static {p1, v0, v1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public inspect()Ljava/lang/String;
    .locals 5

    .line 373
    iget-object v0, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/InvokerHelper;->inspect(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    .line 374
    iget-object v1, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->inspect(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    .line 375
    iget-boolean v2, p0, Lgroovy/lang/ObjectRange;->reverse:Z

    const-string v3, ".."

    const-string v4, ""

    if-eqz v2, :cond_0

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    goto :goto_0

    :cond_0
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    :goto_0
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public isReverse()Z
    .locals 1

    .line 235
    iget-boolean v0, p0, Lgroovy/lang/ObjectRange;->reverse:Z

    return v0
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "Ljava/lang/Comparable;",
            ">;"
        }
    .end annotation

    .line 412
    new-instance v0, Lgroovy/lang/ObjectRange$StepIterator;

    const/4 v1, 0x1

    const/4 v2, 0x0

    invoke-direct {v0, p0, v1, v2}, Lgroovy/lang/ObjectRange$StepIterator;-><init>(Lgroovy/lang/ObjectRange;ILgroovy/lang/ObjectRange$1;)V

    return-object v0
.end method

.method public size()I
    .locals 6

    .line 284
    iget v0, p0, Lgroovy/lang/ObjectRange;->size:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_d

    const/4 v0, 0x0

    .line 286
    iget-object v1, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    instance-of v2, v1, Ljava/lang/Integer;

    const v3, 0x7fffffff

    if-nez v2, :cond_0

    instance-of v2, v1, Ljava/lang/Long;

    if-eqz v2, :cond_1

    :cond_0
    iget-object v2, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    instance-of v4, v2, Ljava/lang/Integer;

    if-nez v4, :cond_a

    instance-of v2, v2, Ljava/lang/Long;

    if-eqz v2, :cond_1

    goto/16 :goto_4

    .line 296
    :cond_1
    instance-of v2, v1, Ljava/lang/Character;

    const/4 v4, 0x1

    if-eqz v2, :cond_2

    iget-object v2, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    instance-of v2, v2, Ljava/lang/Character;

    if-eqz v2, :cond_2

    .line 298
    check-cast v1, Ljava/lang/Character;

    invoke-virtual {v1}, Ljava/lang/Character;->charValue()C

    move-result v0

    .line 299
    iget-object v1, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    check-cast v1, Ljava/lang/Character;

    invoke-virtual {v1}, Ljava/lang/Character;->charValue()C

    move-result v1

    sub-int/2addr v1, v0

    add-int/2addr v1, v4

    goto/16 :goto_5

    .line 301
    :cond_2
    instance-of v2, v1, Ljava/math/BigDecimal;

    if-nez v2, :cond_3

    instance-of v2, v1, Ljava/math/BigInteger;

    if-eqz v2, :cond_4

    :cond_3
    iget-object v2, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    instance-of v2, v2, Ljava/lang/Number;

    if-nez v2, :cond_9

    :cond_4
    iget-object v2, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    instance-of v5, v2, Ljava/math/BigDecimal;

    if-nez v5, :cond_5

    instance-of v2, v2, Ljava/math/BigInteger;

    if-eqz v2, :cond_6

    :cond_5
    instance-of v2, v1, Ljava/lang/Number;

    if-eqz v2, :cond_6

    goto :goto_2

    .line 313
    :cond_6
    new-instance v1, Lgroovy/lang/ObjectRange$StepIterator;

    const/4 v2, 0x0

    invoke-direct {v1, p0, v4, v2}, Lgroovy/lang/ObjectRange$StepIterator;-><init>(Lgroovy/lang/ObjectRange;ILgroovy/lang/ObjectRange$1;)V

    .line 314
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_8

    add-int/lit8 v0, v0, 0x1

    if-gez v0, :cond_7

    goto :goto_1

    .line 320
    :cond_7
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    goto :goto_0

    :cond_8
    :goto_1
    move v1, v0

    goto :goto_5

    .line 304
    :cond_9
    :goto_2
    check-cast v1, Ljava/lang/Number;

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object v0

    .line 305
    iget-object v1, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    check-cast v1, Ljava/lang/Number;

    invoke-static {v1}, Lorg/codehaus/groovy/runtime/typehandling/NumberMath;->toBigDecimal(Ljava/lang/Number;)Ljava/math/BigDecimal;

    move-result-object v1

    .line 306
    invoke-virtual {v1, v0}, Ljava/math/BigDecimal;->subtract(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    move-result-object v0

    sget-object v1, Ljava/math/BigDecimal;->ONE:Ljava/math/BigDecimal;

    invoke-virtual {v0, v1}, Ljava/math/BigDecimal;->add(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    move-result-object v0

    invoke-virtual {v0}, Ljava/math/BigDecimal;->toBigInteger()Ljava/math/BigInteger;

    move-result-object v0

    .line 307
    invoke-virtual {v0}, Ljava/math/BigInteger;->intValue()I

    move-result v1

    int-to-long v4, v1

    .line 308
    invoke-static {v4, v5}, Ljava/math/BigInteger;->valueOf(J)Ljava/math/BigInteger;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/math/BigInteger;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_b

    :goto_3
    move v1, v3

    goto :goto_5

    .line 289
    :cond_a
    :goto_4
    new-instance v0, Ljava/math/BigInteger;

    iget-object v1, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/math/BigInteger;-><init>(Ljava/lang/String;)V

    .line 290
    new-instance v1, Ljava/math/BigInteger;

    iget-object v2, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Ljava/math/BigInteger;-><init>(Ljava/lang/String;)V

    .line 291
    invoke-virtual {v1, v0}, Ljava/math/BigInteger;->subtract(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object v0

    new-instance v1, Ljava/math/BigInteger;

    const-string v2, "1"

    invoke-direct {v1, v2}, Ljava/math/BigInteger;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Ljava/math/BigInteger;->add(Ljava/math/BigInteger;)Ljava/math/BigInteger;

    move-result-object v0

    .line 292
    invoke-virtual {v0}, Ljava/math/BigInteger;->intValue()I

    move-result v1

    int-to-long v4, v1

    .line 293
    invoke-static {v4, v5}, Ljava/math/BigInteger;->valueOf(J)Ljava/math/BigInteger;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/math/BigInteger;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_b

    goto :goto_3

    :cond_b
    :goto_5
    if-gez v1, :cond_c

    goto :goto_6

    :cond_c
    move v3, v1

    .line 327
    :goto_6
    iput v3, p0, Lgroovy/lang/ObjectRange;->size:I

    .line 329
    :cond_d
    iget v0, p0, Lgroovy/lang/ObjectRange;->size:I

    return v0
.end method

.method public step(I)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Ljava/lang/Comparable;",
            ">;"
        }
    .end annotation

    .line 498
    new-instance v0, Lorg/codehaus/groovy/runtime/IteratorClosureAdapter;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/runtime/IteratorClosureAdapter;-><init>(Ljava/lang/Object;)V

    .line 499
    invoke-virtual {p0, p1, v0}, Lgroovy/lang/ObjectRange;->step(ILgroovy/lang/Closure;)V

    .line 500
    invoke-virtual {v0}, Lorg/codehaus/groovy/runtime/IteratorClosureAdapter;->asList()Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public step(ILgroovy/lang/Closure;)V
    .locals 2

    if-nez p1, :cond_0

    .line 397
    iget-object v0, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    iget-object v1, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    invoke-virtual {p0, v0, v1}, Lgroovy/lang/ObjectRange;->compareTo(Ljava/lang/Comparable;Ljava/lang/Comparable;)I

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 400
    :cond_0
    new-instance v0, Lgroovy/lang/ObjectRange$StepIterator;

    const/4 v1, 0x0

    invoke-direct {v0, p0, p1, v1}, Lgroovy/lang/ObjectRange$StepIterator;-><init>(Lgroovy/lang/ObjectRange;ILgroovy/lang/ObjectRange$1;)V

    .line 401
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 402
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {p2, p1}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_1
    return-void
.end method

.method public subList(II)Ljava/util/List;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)",
            "Ljava/util/List<",
            "Ljava/lang/Comparable;",
            ">;"
        }
    .end annotation

    if-ltz p1, :cond_6

    if-gt p1, p2, :cond_5

    if-ne p1, p2, :cond_0

    .line 341
    new-instance p1, Lgroovy/lang/EmptyRange;

    iget-object p2, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    invoke-direct {p1, p2}, Lgroovy/lang/EmptyRange;-><init>(Ljava/lang/Comparable;)V

    return-object p1

    .line 346
    :cond_0
    new-instance v0, Lgroovy/lang/ObjectRange$StepIterator;

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-direct {v0, p0, v2, v1}, Lgroovy/lang/ObjectRange$StepIterator;-><init>(Lgroovy/lang/ObjectRange;ILgroovy/lang/ObjectRange$1;)V

    .line 348
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Comparable;

    const/4 v3, 0x0

    :goto_0
    const-string v4, " is too big for range: "

    const-string v5, "Index: "

    if-ge v3, p1, :cond_2

    .line 351
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 354
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Comparable;

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 352
    :cond_1
    new-instance p1, Ljava/lang/IndexOutOfBoundsException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_2
    move-object p1, v1

    :goto_1
    add-int/lit8 v6, p2, -0x1

    if-ge v3, v6, :cond_4

    .line 358
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result p1

    if-eqz p1, :cond_3

    .line 361
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Comparable;

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 359
    :cond_3
    new-instance p1, Ljava/lang/IndexOutOfBoundsException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 364
    :cond_4
    new-instance p2, Lgroovy/lang/ObjectRange;

    iget-boolean v0, p0, Lgroovy/lang/ObjectRange;->reverse:Z

    invoke-direct {p2, v1, p1, v0}, Lgroovy/lang/ObjectRange;-><init>(Ljava/lang/Comparable;Ljava/lang/Comparable;Z)V

    return-object p2

    .line 338
    :cond_5
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "fromIndex("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ") > toIndex("

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string p2, ")"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 335
    :cond_6
    new-instance p2, Ljava/lang/IndexOutOfBoundsException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "fromIndex = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/IndexOutOfBoundsException;-><init>(Ljava/lang/String;)V

    throw p2
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 368
    iget-boolean v0, p0, Lgroovy/lang/ObjectRange;->reverse:Z

    const-string v1, ".."

    const-string v2, ""

    if-eqz v0, :cond_0

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v2, p0, Lgroovy/lang/ObjectRange;->from:Ljava/lang/Comparable;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovy/lang/ObjectRange;->to:Ljava/lang/Comparable;

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
