.class public Lgroovy/lang/DelegatingMetaClass;
.super Ljava/lang/Object;
.source "DelegatingMetaClass.java"

# interfaces
.implements Lgroovy/lang/MetaClass;
.implements Lgroovy/lang/MutableMetaClass;
.implements Lgroovy/lang/GroovyObject;


# instance fields
.field protected delegate:Lgroovy/lang/MetaClass;


# direct methods
.method public constructor <init>(Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 30
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 31
    iput-object p1, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;)V
    .locals 1

    .line 35
    invoke-static {}, Lgroovy/lang/GroovySystem;->getMetaClassRegistry()Lgroovy/lang/MetaClassRegistry;

    move-result-object v0

    invoke-interface {v0, p1}, Lgroovy/lang/MetaClassRegistry;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object p1

    invoke-direct {p0, p1}, Lgroovy/lang/DelegatingMetaClass;-><init>(Lgroovy/lang/MetaClass;)V

    return-void
.end method


# virtual methods
.method public addMetaBeanProperty(Lgroovy/lang/MetaBeanProperty;)V
    .locals 2

    .line 64
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    instance-of v1, v0, Lgroovy/lang/MutableMetaClass;

    if-eqz v1, :cond_0

    .line 65
    check-cast v0, Lgroovy/lang/MutableMetaClass;

    invoke-interface {v0, p1}, Lgroovy/lang/MutableMetaClass;->addMetaBeanProperty(Lgroovy/lang/MetaBeanProperty;)V

    :cond_0
    return-void
.end method

.method public addMetaMethod(Lgroovy/lang/MetaMethod;)V
    .locals 2

    .line 59
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    instance-of v1, v0, Lgroovy/lang/MutableMetaClass;

    if-eqz v1, :cond_0

    .line 60
    check-cast v0, Lgroovy/lang/MutableMetaClass;

    invoke-interface {v0, p1}, Lgroovy/lang/MutableMetaClass;->addMetaMethod(Lgroovy/lang/MetaMethod;)V

    :cond_0
    return-void
.end method

.method public addNewInstanceMethod(Ljava/lang/reflect/Method;)V
    .locals 2

    .line 46
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    instance-of v1, v0, Lgroovy/lang/MutableMetaClass;

    if-eqz v1, :cond_0

    .line 47
    check-cast v0, Lgroovy/lang/MutableMetaClass;

    invoke-interface {v0, p1}, Lgroovy/lang/MutableMetaClass;->addNewInstanceMethod(Ljava/lang/reflect/Method;)V

    :cond_0
    return-void
.end method

.method public addNewStaticMethod(Ljava/lang/reflect/Method;)V
    .locals 2

    .line 54
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    instance-of v1, v0, Lgroovy/lang/MutableMetaClass;

    if-eqz v1, :cond_0

    .line 55
    check-cast v0, Lgroovy/lang/MutableMetaClass;

    invoke-interface {v0, p1}, Lgroovy/lang/MutableMetaClass;->addNewStaticMethod(Ljava/lang/reflect/Method;)V

    :cond_0
    return-void
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 1

    .line 175
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-virtual {v0, p1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public getAdaptee()Lgroovy/lang/MetaClass;
    .locals 1

    .line 258
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    return-object v0
.end method

.method public getAttribute(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Z)Ljava/lang/Object;
    .locals 1

    .line 198
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3, p4}, Lgroovy/lang/MetaClass;->getAttribute(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Z)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getAttribute(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    .line 79
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getAttribute(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getClassNode()Lorg/codehaus/groovy/ast/ClassNode;
    .locals 1

    .line 86
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getClassNode()Lorg/codehaus/groovy/ast/ClassNode;

    move-result-object v0

    return-object v0
.end method

.method public getMetaClass()Lgroovy/lang/MetaClass;
    .locals 1

    .line 298
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-static {v0}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getMetaClass(Ljava/lang/Class;)Lgroovy/lang/MetaClass;

    move-result-object v0

    return-object v0
.end method

.method public getMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;
    .locals 1

    .line 218
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    return-object p1
.end method

.method public getMetaMethods()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 93
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getMetaMethods()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getMetaProperty(Ljava/lang/String;)Lgroovy/lang/MetaProperty;
    .locals 1

    .line 206
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1}, Lgroovy/lang/MetaClass;->getMetaProperty(Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object p1

    return-object p1
.end method

.method public getMethods()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 100
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getMethods()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getProperties()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaProperty;",
            ">;"
        }
    .end annotation

    .line 119
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getProperties()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;
    .locals 6

    .line 202
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move v4, p4

    move v5, p5

    invoke-interface/range {v0 .. v5}, Lgroovy/lang/MetaClass;->getProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    .line 126
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getProperty(Ljava/lang/String;)Ljava/lang/Object;
    .locals 3

    .line 275
    :try_start_0
    invoke-virtual {p0}, Lgroovy/lang/DelegatingMetaClass;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    invoke-interface {v0, p0, p1}, Lgroovy/lang/MetaClass;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception v0

    .line 278
    iget-object v1, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    instance-of v2, v1, Lgroovy/lang/GroovyObject;

    if-eqz v2, :cond_0

    .line 279
    check-cast v1, Lgroovy/lang/GroovyObject;

    invoke-interface {v1, p1}, Lgroovy/lang/GroovyObject;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 281
    :cond_0
    throw v0
.end method

.method public getStaticMetaMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;
    .locals 1

    .line 214
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getStaticMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    return-object p1
.end method

.method public getStaticMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;
    .locals 1

    .line 210
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->getStaticMetaMethod(Ljava/lang/String;[Ljava/lang/Object;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    return-object p1
.end method

.method public getTheClass()Ljava/lang/Class;
    .locals 1

    .line 222
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->getTheClass()Ljava/lang/Class;

    move-result-object v0

    return-object v0
.end method

.method public hasProperty(Ljava/lang/Object;Ljava/lang/String;)Lgroovy/lang/MetaProperty;
    .locals 1

    .line 112
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->hasProperty(Ljava/lang/Object;Ljava/lang/String;)Lgroovy/lang/MetaProperty;

    move-result-object p1

    return-object p1
.end method

.method public hashCode()I
    .locals 1

    .line 182
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    return v0
.end method

.method public initialize()V
    .locals 1

    .line 72
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0}, Lgroovy/lang/MetaClass;->initialize()V

    return-void
.end method

.method public invokeConstructor([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 133
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1}, Lgroovy/lang/MetaClass;->invokeConstructor([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)Ljava/lang/Object;
    .locals 7

    .line 226
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    invoke-interface/range {v0 .. v6}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;ZZ)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 140
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 147
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 263
    :try_start_0
    invoke-virtual {p0}, Lgroovy/lang/DelegatingMetaClass;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    invoke-interface {v0, p0, p1, p2}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lgroovy/lang/MissingMethodException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception v0

    .line 266
    iget-object v1, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    instance-of v2, v1, Lgroovy/lang/GroovyObject;

    if-eqz v2, :cond_0

    .line 267
    check-cast v1, Lgroovy/lang/GroovyObject;

    invoke-interface {v1, p1, p2}, Lgroovy/lang/GroovyObject;->invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 269
    :cond_0
    throw v0
.end method

.method public invokeMissingMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 230
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeMissingMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMissingProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Z)Ljava/lang/Object;
    .locals 1

    .line 234
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3, p4}, Lgroovy/lang/MetaClass;->invokeMissingProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;Z)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeStaticMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 154
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->invokeStaticMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public isGroovyObject()Z
    .locals 2

    .line 238
    const-class v0, Lgroovy/lang/GroovyObject;

    iget-object v1, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v1}, Lgroovy/lang/MetaClass;->getTheClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v0

    return v0
.end method

.method public isModified()Z
    .locals 2

    .line 39
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    instance-of v1, v0, Lgroovy/lang/MutableMetaClass;

    if-eqz v1, :cond_0

    check-cast v0, Lgroovy/lang/MutableMetaClass;

    invoke-interface {v0}, Lgroovy/lang/MutableMetaClass;->isModified()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public pickMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;
    .locals 1
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 194
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->pickMethod(Ljava/lang/String;[Ljava/lang/Class;)Lgroovy/lang/MetaMethod;

    move-result-object p1

    return-object p1
.end method

.method public respondsTo(Ljava/lang/Object;Ljava/lang/String;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 108
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->respondsTo(Ljava/lang/Object;Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public respondsTo(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/String;",
            "[",
            "Ljava/lang/Object;",
            ")",
            "Ljava/util/List<",
            "Lgroovy/lang/MetaMethod;",
            ">;"
        }
    .end annotation

    .line 104
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->respondsTo(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public selectConstructorAndTransformArguments(I[Ljava/lang/Object;)I
    .locals 1

    .line 250
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2}, Lgroovy/lang/MetaClass;->selectConstructorAndTransformArguments(I[Ljava/lang/Object;)I

    move-result p1

    return p1
.end method

.method public setAdaptee(Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 254
    iput-object p1, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    return-void
.end method

.method public setAttribute(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V
    .locals 7

    .line 242
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    invoke-interface/range {v0 .. v6}, Lgroovy/lang/MetaClass;->setAttribute(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V

    return-void
.end method

.method public setAttribute(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    .line 161
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->setAttribute(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method

.method public setMetaClass(Lgroovy/lang/MetaClass;)V
    .locals 0

    .line 302
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method

.method public setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V
    .locals 7

    .line 246
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p4

    move v5, p5

    move v6, p6

    invoke-interface/range {v0 .. v6}, Lgroovy/lang/MetaClass;->setProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;ZZ)V

    return-void
.end method

.method public setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    .line 168
    iget-object v0, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-interface {v0, p1, p2, p3}, Lgroovy/lang/MetaClass;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method

.method public setProperty(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 3

    .line 287
    :try_start_0
    invoke-virtual {p0}, Lgroovy/lang/DelegatingMetaClass;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    invoke-interface {v0, p0, p1, p2}, Lgroovy/lang/MetaClass;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 290
    iget-object v1, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    instance-of v2, v1, Lgroovy/lang/GroovyObject;

    if-eqz v2, :cond_0

    .line 291
    check-cast v1, Lgroovy/lang/GroovyObject;

    invoke-interface {v1, p1, p2}, Lgroovy/lang/GroovyObject;->setProperty(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_0
    return-void

    .line 293
    :cond_0
    throw v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 186
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-super {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lgroovy/lang/DelegatingMetaClass;->delegate:Lgroovy/lang/MetaClass;

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "]"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
