.class Lgroovy/lang/GroovyClassLoader$TimestampAdder;
.super Ljava/lang/Object;
.source "GroovyClassLoader.java"

# interfaces
.implements Lorg/codehaus/groovy/control/CompilationUnit$IPrimaryClassNodeOperation;
.implements Lgroovyjarjarasm/asm/Opcodes;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lgroovy/lang/GroovyClassLoader;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "TimestampAdder"
.end annotation


# static fields
.field private static final INSTANCE:Lgroovy/lang/GroovyClassLoader$TimestampAdder;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1197
    new-instance v0, Lgroovy/lang/GroovyClassLoader$TimestampAdder;

    invoke-direct {v0}, Lgroovy/lang/GroovyClassLoader$TimestampAdder;-><init>()V

    sput-object v0, Lgroovy/lang/GroovyClassLoader$TimestampAdder;->INSTANCE:Lgroovy/lang/GroovyClassLoader$TimestampAdder;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1199
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic access$300()Lgroovy/lang/GroovyClassLoader$TimestampAdder;
    .locals 1

    .line 1196
    sget-object v0, Lgroovy/lang/GroovyClassLoader$TimestampAdder;->INSTANCE:Lgroovy/lang/GroovyClassLoader$TimestampAdder;

    return-object v0
.end method


# virtual methods
.method protected addTimeStamp(Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 10

    const-string v0, "__timeStamp"

    .line 1202
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->getDeclaredField(Ljava/lang/String;)Lorg/codehaus/groovy/ast/FieldNode;

    move-result-object v0

    if-nez v0, :cond_0

    .line 1203
    new-instance v0, Lorg/codehaus/groovy/ast/FieldNode;

    const/16 v3, 0x1009

    sget-object v4, Lorg/codehaus/groovy/ast/ClassHelper;->long_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-instance v6, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    .line 1209
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    invoke-direct {v6, v1}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    const-string v2, "__timeStamp"

    move-object v1, v0

    move-object v5, p1

    invoke-direct/range {v1 .. v6}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    const/4 v1, 0x1

    .line 1211
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/FieldNode;->setSynthetic(Z)V

    .line 1212
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    .line 1214
    new-instance v0, Lorg/codehaus/groovy/ast/FieldNode;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "__timeStamp__239_neverHappen"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    .line 1215
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    invoke-virtual {v2, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const/16 v4, 0x1009

    sget-object v5, Lorg/codehaus/groovy/ast/ClassHelper;->long_TYPE:Lorg/codehaus/groovy/ast/ClassNode;

    new-instance v7, Lorg/codehaus/groovy/ast/expr/ConstantExpression;

    const-wide/16 v8, 0x0

    .line 1220
    invoke-static {v8, v9}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    invoke-direct {v7, v2}, Lorg/codehaus/groovy/ast/expr/ConstantExpression;-><init>(Ljava/lang/Object;)V

    move-object v2, v0

    move-object v6, p1

    invoke-direct/range {v2 .. v7}, Lorg/codehaus/groovy/ast/FieldNode;-><init>(Ljava/lang/String;ILorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/ClassNode;Lorg/codehaus/groovy/ast/expr/Expression;)V

    .line 1222
    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/ast/FieldNode;->setSynthetic(Z)V

    .line 1223
    invoke-virtual {p1, v0}, Lorg/codehaus/groovy/ast/ClassNode;->addField(Lorg/codehaus/groovy/ast/FieldNode;)V

    :cond_0
    return-void
.end method

.method public call(Lorg/codehaus/groovy/control/SourceUnit;Lorg/codehaus/groovy/classgen/GeneratorContext;Lorg/codehaus/groovy/ast/ClassNode;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 1229
    invoke-virtual {p3}, Lorg/codehaus/groovy/ast/ClassNode;->getModifiers()I

    move-result p1

    and-int/lit16 p1, p1, 0x200

    if-lez p1, :cond_0

    return-void

    .line 1233
    :cond_0
    instance-of p1, p3, Lorg/codehaus/groovy/ast/InnerClassNode;

    if-nez p1, :cond_1

    .line 1234
    invoke-virtual {p0, p3}, Lgroovy/lang/GroovyClassLoader$TimestampAdder;->addTimeStamp(Lorg/codehaus/groovy/ast/ClassNode;)V

    :cond_1
    return-void
.end method
