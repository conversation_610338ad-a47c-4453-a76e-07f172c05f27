.class public abstract Lgroovy/lang/Script;
.super Lgroovy/lang/GroovyObjectSupport;
.source "Script.java"


# instance fields
.field private binding:Lgroovy/lang/Binding;


# direct methods
.method protected constructor <init>()V
    .locals 1

    .line 38
    new-instance v0, Lgroovy/lang/Binding;

    invoke-direct {v0}, Lgroovy/lang/Binding;-><init>()V

    invoke-direct {p0, v0}, Lgroovy/lang/Script;-><init>(Lgroovy/lang/Binding;)V

    return-void
.end method

.method protected constructor <init>(Lgroovy/lang/Binding;)V
    .locals 0

    .line 41
    invoke-direct {p0}, Lgroovy/lang/GroovyObjectSupport;-><init>()V

    .line 42
    iput-object p1, p0, Lgroovy/lang/Script;->binding:Lgroovy/lang/Binding;

    return-void
.end method

.method private hasSetterMethodFor(Ljava/lang/String;)Z
    .locals 7

    .line 76
    invoke-static {p1}, Lorg/codehaus/groovy/ast/tools/GeneralUtils;->getSetterName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 77
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Class;->getDeclaredMethods()[Ljava/lang/reflect/Method;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 78
    invoke-virtual {v4}, Ljava/lang/reflect/Method;->getParameterCount()I

    move-result v5

    const/4 v6, 0x1

    if-ne v5, v6, :cond_0

    .line 80
    invoke-virtual {v4}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v4, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_0

    return v6

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    return v2
.end method


# virtual methods
.method public evaluate(Ljava/io/File;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;,
            Ljava/io/IOException;
        }
    .end annotation

    .line 236
    new-instance v0, Lgroovy/lang/GroovyShell;

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    iget-object v2, p0, Lgroovy/lang/Script;->binding:Lgroovy/lang/Binding;

    invoke-direct {v0, v1, v2}, Lgroovy/lang/GroovyShell;-><init>(Ljava/lang/ClassLoader;Lgroovy/lang/Binding;)V

    .line 237
    invoke-virtual {v0, p1}, Lgroovy/lang/GroovyShell;->evaluate(Ljava/io/File;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public evaluate(Ljava/lang/String;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;
        }
    .end annotation

    .line 225
    new-instance v0, Lgroovy/lang/GroovyShell;

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    iget-object v2, p0, Lgroovy/lang/Script;->binding:Lgroovy/lang/Binding;

    invoke-direct {v0, v1, v2}, Lgroovy/lang/GroovyShell;-><init>(Ljava/lang/ClassLoader;Lgroovy/lang/Binding;)V

    .line 226
    invoke-virtual {v0, p1}, Lgroovy/lang/GroovyShell;->evaluate(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getBinding()Lgroovy/lang/Binding;
    .locals 1

    .line 46
    iget-object v0, p0, Lgroovy/lang/Script;->binding:Lgroovy/lang/Binding;

    return-object v0
.end method

.method public getProperty(Ljava/lang/String;)Ljava/lang/Object;
    .locals 1

    .line 55
    :try_start_0
    iget-object v0, p0, Lgroovy/lang/Script;->binding:Lgroovy/lang/Binding;

    invoke-virtual {v0, p1}, Lgroovy/lang/Binding;->getVariable(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    .line 57
    :catch_0
    invoke-super {p0, p1}, Lgroovy/lang/GroovyObjectSupport;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 96
    :try_start_0
    invoke-super {p0, p1, p2}, Lgroovy/lang/GroovyObjectSupport;->invokeMethod(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lgroovy/lang/MissingMethodException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception v0

    .line 102
    :try_start_1
    invoke-virtual {v0}, Lgroovy/lang/MissingMethodException;->getMethod()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 103
    invoke-virtual {p0, p1}, Lgroovy/lang/Script;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    .line 104
    instance-of v1, p1, Lgroovy/lang/Closure;

    if-eqz v1, :cond_0

    .line 105
    check-cast p1, Lgroovy/lang/Closure;

    check-cast p2, [Ljava/lang/Object;

    invoke-virtual {p1, p2}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 107
    :cond_0
    throw v0

    .line 110
    :cond_1
    throw v0
    :try_end_1
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_1 .. :try_end_1} :catch_1

    .line 113
    :catch_1
    throw v0
.end method

.method public print(Ljava/lang/Object;)V
    .locals 3

    :try_start_0
    const-string v0, "out"

    .line 153
    invoke-virtual {p0, v0}, Lgroovy/lang/Script;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v1, 0x1

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const-string p1, "print"

    .line 159
    invoke-static {v0, p1, v1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    .line 155
    :catch_0
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->print(Ljava/io/PrintStream;Ljava/lang/Object;)V

    return-void
.end method

.method public printf(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 3

    :try_start_0
    const-string v0, "out"

    .line 190
    invoke-virtual {p0, v0}, Lgroovy/lang/Script;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const/4 p1, 0x1

    aput-object p2, v1, p1

    const-string p1, "printf"

    .line 196
    invoke-static {v0, p1, v1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    .line 192
    :catch_0
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->printf(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method

.method public printf(Ljava/lang/String;[Ljava/lang/Object;)V
    .locals 3

    :try_start_0
    const-string v0, "out"

    .line 209
    invoke-virtual {p0, v0}, Lgroovy/lang/Script;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const/4 p1, 0x1

    aput-object p2, v1, p1

    const-string p1, "printf"

    .line 215
    invoke-static {v0, p1, v1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    .line 211
    :catch_0
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->printf(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)V

    return-void
.end method

.method public println()V
    .locals 3

    :try_start_0
    const-string v0, "out"

    .line 135
    invoke-virtual {p0, v0}, Lgroovy/lang/Script;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_0

    .line 141
    sget-object v1, Lorg/codehaus/groovy/ast/expr/ArgumentListExpression;->EMPTY_ARRAY:[Ljava/lang/Object;

    const-string v2, "println"

    invoke-static {v0, v2, v1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    .line 137
    :catch_0
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {v0}, Ljava/io/PrintStream;->println()V

    return-void
.end method

.method public println(Ljava/lang/Object;)V
    .locals 3

    :try_start_0
    const-string v0, "out"

    .line 171
    invoke-virtual {p0, v0}, Lgroovy/lang/Script;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v1, 0x1

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    const-string p1, "println"

    .line 177
    invoke-static {v0, p1, v1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;

    return-void

    .line 173
    :catch_0
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->println(Ljava/io/PrintStream;Ljava/lang/Object;)V

    return-void
.end method

.method public abstract run()Ljava/lang/Object;
.end method

.method public run(Ljava/io/File;[Ljava/lang/String;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lorg/codehaus/groovy/control/CompilationFailedException;,
            Ljava/io/IOException;
        }
    .end annotation

    .line 244
    new-instance v0, Lgroovy/lang/GroovyShell;

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    iget-object v2, p0, Lgroovy/lang/Script;->binding:Lgroovy/lang/Binding;

    invoke-direct {v0, v1, v2}, Lgroovy/lang/GroovyShell;-><init>(Ljava/lang/ClassLoader;Lgroovy/lang/Binding;)V

    .line 245
    invoke-virtual {v0, p1, p2}, Lgroovy/lang/GroovyShell;->run(Ljava/io/File;[Ljava/lang/String;)Ljava/lang/Object;

    return-void
.end method

.method public setBinding(Lgroovy/lang/Binding;)V
    .locals 0

    .line 50
    iput-object p1, p0, Lgroovy/lang/Script;->binding:Lgroovy/lang/Binding;

    return-void
.end method

.method public setProperty(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    const-string v0, "binding"

    .line 62
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 63
    check-cast p2, Lgroovy/lang/Binding;

    invoke-virtual {p0, p2}, Lgroovy/lang/Script;->setBinding(Lgroovy/lang/Binding;)V

    goto :goto_0

    :cond_0
    const-string v0, "metaClass"

    .line 64
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 65
    check-cast p2, Lgroovy/lang/MetaClass;

    invoke-virtual {p0, p2}, Lgroovy/lang/Script;->setMetaClass(Lgroovy/lang/MetaClass;)V

    goto :goto_0

    .line 66
    :cond_1
    iget-object v0, p0, Lgroovy/lang/Script;->binding:Lgroovy/lang/Binding;

    invoke-virtual {v0, p1}, Lgroovy/lang/Binding;->hasVariable(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_2

    .line 68
    invoke-direct {p0, p1}, Lgroovy/lang/Script;->hasSetterMethodFor(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 69
    invoke-super {p0, p1, p2}, Lgroovy/lang/GroovyObjectSupport;->setProperty(Ljava/lang/String;Ljava/lang/Object;)V

    goto :goto_0

    .line 71
    :cond_2
    iget-object v0, p0, Lgroovy/lang/Script;->binding:Lgroovy/lang/Binding;

    invoke-virtual {v0, p1, p2}, Lgroovy/lang/Binding;->setVariable(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_0
    return-void
.end method
