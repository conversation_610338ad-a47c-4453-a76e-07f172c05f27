.class public final synthetic Lgroovy/lang/GroovyCodeSource$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/security/PrivilegedExceptionAction;


# instance fields
.field public final synthetic f$0:Lgroovy/lang/GroovyCodeSource;

.field public final synthetic f$1:Ljava/lang/String;

.field public final synthetic f$2:Ljava/io/File;

.field public final synthetic f$3:Ljava/io/File;


# direct methods
.method public synthetic constructor <init>(Lgroovy/lang/GroovyCodeSource;Ljava/lang/String;Ljava/io/File;Ljava/io/File;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovy/lang/GroovyCodeSource$$ExternalSyntheticLambda0;->f$0:Lgroovy/lang/GroovyCodeSource;

    iput-object p2, p0, Lgroovy/lang/GroovyCodeSource$$ExternalSyntheticLambda0;->f$1:Ljava/lang/String;

    iput-object p3, p0, Lgroovy/lang/GroovyCodeSource$$ExternalSyntheticLambda0;->f$2:Ljava/io/File;

    iput-object p4, p0, Lgroovy/lang/GroovyCodeSource$$ExternalSyntheticLambda0;->f$3:Ljava/io/File;

    return-void
.end method


# virtual methods
.method public final run()Ljava/lang/Object;
    .locals 4

    iget-object v0, p0, Lgroovy/lang/GroovyCodeSource$$ExternalSyntheticLambda0;->f$0:Lgroovy/lang/GroovyCodeSource;

    iget-object v1, p0, Lgroovy/lang/GroovyCodeSource$$ExternalSyntheticLambda0;->f$1:Ljava/lang/String;

    iget-object v2, p0, Lgroovy/lang/GroovyCodeSource$$ExternalSyntheticLambda0;->f$2:Ljava/io/File;

    iget-object v3, p0, Lgroovy/lang/GroovyCodeSource$$ExternalSyntheticLambda0;->f$3:Ljava/io/File;

    invoke-virtual {v0, v1, v2, v3}, Lgroovy/lang/GroovyCodeSource;->lambda$new$0$groovy-lang-GroovyCodeSource(Ljava/lang/String;Ljava/io/File;Ljava/io/File;)[Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
