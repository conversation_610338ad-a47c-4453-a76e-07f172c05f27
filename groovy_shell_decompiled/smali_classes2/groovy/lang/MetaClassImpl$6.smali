.class Lgroovy/lang/MetaClassImpl$6;
.super Lgroovy/lang/MetaProperty;
.source "MetaClassImpl.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lgroovy/lang/MetaClassImpl;->getEffectiveGetMetaProperty(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/String;Z)Lgroovy/lang/MetaProperty;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lgroovy/lang/MetaClassImpl;


# direct methods
.method constructor <init>(Lgroovy/lang/MetaClassImpl;Ljava/lang/String;Ljava/lang/Class;)V
    .locals 0

    .line 2053
    iput-object p1, p0, Lgroovy/lang/MetaClassImpl$6;->this$0:Lgroovy/lang/MetaClassImpl;

    invoke-direct {p0, p2, p3}, Lgroovy/lang/MetaProperty;-><init>(Ljava/lang/String;Ljava/lang/Class;)V

    return-void
.end method


# virtual methods
.method public getProperty(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 2055
    check-cast p1, Ljava/util/Collection;

    iget-object v0, p0, Lgroovy/lang/MetaClassImpl$6;->name:Ljava/lang/String;

    invoke-static {p1, v0}, Lorg/codehaus/groovy/runtime/DefaultGroovyMethods;->getAt(Ljava/util/Collection;Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public setProperty(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0

    .line 2059
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    invoke-direct {p1}, Ljava/lang/UnsupportedOperationException;-><init>()V

    throw p1
.end method
