.class public abstract Lgroovy/lang/Closure;
.super Lgroovy/lang/GroovyObjectSupport;
.source "Closure.java"

# interfaces
.implements Ljava/lang/Cloneable;
.implements Ljava/lang/Runnable;
.implements Lgroovy/lang/GroovyCallable;
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lgroovy/lang/Closure$WritableClosure;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<V:",
        "Ljava/lang/Object;",
        ">",
        "Lgroovy/lang/GroovyObjectSupport;",
        "Ljava/lang/Cloneable;",
        "Ljava/lang/Runnable;",
        "Lgroovy/lang/GroovyCallable<",
        "TV;>;",
        "Ljava/io/Serializable;"
    }
.end annotation


# static fields
.field public static final DELEGATE_FIRST:I = 0x1

.field public static final DELEGATE_ONLY:I = 0x3

.field public static final DONE:I = 0x1

.field private static final EMPTY_OBJECT_ARRAY:[Ljava/lang/Object;

.field public static final IDENTITY:Lgroovy/lang/Closure;

.field public static final OWNER_FIRST:I = 0x0

.field public static final OWNER_ONLY:I = 0x2

.field public static final SKIP:I = 0x2

.field public static final TO_SELF:I = 0x4

.field private static final serialVersionUID:J = 0x3ca0c76616126c5aL


# instance fields
.field private bcw:Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;

.field private delegate:Ljava/lang/Object;

.field private directive:I

.field protected maximumNumberOfParameters:I

.field private owner:Ljava/lang/Object;

.field protected parameterTypes:[Ljava/lang/Class;

.field private resolveStrategy:I

.field private thisObject:Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    const/4 v0, 0x0

    new-array v0, v0, [Ljava/lang/Object;

    .line 192
    sput-object v0, Lgroovy/lang/Closure;->EMPTY_OBJECT_ARRAY:[Ljava/lang/Object;

    .line 193
    new-instance v0, Lgroovy/lang/Closure$1;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lgroovy/lang/Closure$1;-><init>(Ljava/lang/Object;)V

    sput-object v0, Lgroovy/lang/Closure;->IDENTITY:Lgroovy/lang/Closure;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;)V
    .locals 1

    const/4 v0, 0x0

    .line 228
    invoke-direct {p0, p1, v0}, Lgroovy/lang/Closure;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 211
    invoke-direct {p0}, Lgroovy/lang/GroovyObjectSupport;-><init>()V

    const/4 v0, 0x0

    .line 204
    iput v0, p0, Lgroovy/lang/Closure;->resolveStrategy:I

    .line 212
    iput-object p1, p0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    .line 213
    iput-object p1, p0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    .line 214
    iput-object p2, p0, Lgroovy/lang/Closure;->thisObject:Ljava/lang/Object;

    .line 216
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-static {p1}, Lorg/codehaus/groovy/reflection/ReflectionCache;->getCachedClass(Ljava/lang/Class;)Lorg/codehaus/groovy/reflection/CachedClass;

    move-result-object p1

    check-cast p1, Lorg/codehaus/groovy/reflection/stdclasses/CachedClosureClass;

    .line 217
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/stdclasses/CachedClosureClass;->getParameterTypes()[Ljava/lang/Class;

    move-result-object p2

    iput-object p2, p0, Lgroovy/lang/Closure;->parameterTypes:[Ljava/lang/Class;

    .line 218
    invoke-virtual {p1}, Lorg/codehaus/groovy/reflection/stdclasses/CachedClosureClass;->getMaximumNumberOfParameters()I

    move-result p1

    iput p1, p0, Lgroovy/lang/Closure;->maximumNumberOfParameters:I

    return-void
.end method

.method private getPropertyDelegateFirst(Ljava/lang/String;)Ljava/lang/Object;
    .locals 2

    .line 309
    iget-object v0, p0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    if-nez v0, :cond_0

    invoke-direct {p0, p1}, Lgroovy/lang/Closure;->getPropertyOwnerFirst(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 310
    :cond_0
    iget-object v1, p0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    invoke-direct {p0, p1, v0, v1}, Lgroovy/lang/Closure;->getPropertyTryThese(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method private getPropertyOwnerFirst(Ljava/lang/String;)Ljava/lang/Object;
    .locals 2

    .line 314
    iget-object v0, p0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    iget-object v1, p0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    invoke-direct {p0, p1, v0, v1}, Lgroovy/lang/Closure;->getPropertyTryThese(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method private getPropertyTryThese(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 320
    :try_start_0
    invoke-static {p2, p1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lgroovy/lang/MissingPropertyException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Lgroovy/lang/MissingFieldException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception v0

    goto :goto_0

    :catch_1
    move-exception v0

    :goto_0
    if-eqz p3, :cond_0

    if-eq p2, p0, :cond_0

    if-eq p2, p3, :cond_0

    .line 326
    :try_start_1
    invoke-static {p3, p1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1
    :try_end_1
    .catch Lgroovy/lang/GroovyRuntimeException; {:try_start_1 .. :try_end_1} :catch_2

    return-object p1

    .line 331
    :catch_2
    :cond_0
    throw v0
.end method

.method private setPropertyDelegateFirst(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2

    .line 366
    iget-object v0, p0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    if-nez v0, :cond_0

    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;->setPropertyOwnerFirst(Ljava/lang/String;Ljava/lang/Object;)V

    goto :goto_0

    .line 367
    :cond_0
    iget-object v1, p0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    invoke-direct {p0, p1, p2, v0, v1}, Lgroovy/lang/Closure;->setPropertyTryThese(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method private setPropertyOwnerFirst(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2

    .line 371
    iget-object v0, p0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    iget-object v1, p0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    invoke-direct {p0, p1, p2, v0, v1}, Lgroovy/lang/Closure;->setPropertyTryThese(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method

.method private setPropertyTryThese(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1

    .line 377
    :try_start_0
    invoke-static {p3, p1, p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    :try_end_0
    .catch Lgroovy/lang/GroovyRuntimeException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception v0

    if-eqz p3, :cond_0

    if-eq p3, p0, :cond_0

    if-eq p3, p4, :cond_0

    .line 382
    :try_start_1
    invoke-static {p4, p1, p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V
    :try_end_1
    .catch Lgroovy/lang/GroovyRuntimeException; {:try_start_1 .. :try_end_1} :catch_1

    return-void

    .line 388
    :catch_1
    :cond_0
    throw v0
.end method

.method protected static throwRuntimeException(Ljava/lang/Throwable;)Ljava/lang/Object;
    .locals 2

    .line 432
    instance-of v0, p0, Ljava/lang/RuntimeException;

    if-eqz v0, :cond_0

    .line 433
    check-cast p0, Ljava/lang/RuntimeException;

    throw p0

    .line 435
    :cond_0
    new-instance v0, Lgroovy/lang/GroovyRuntimeException;

    invoke-virtual {p0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1, p0}, Lgroovy/lang/GroovyRuntimeException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    throw v0
.end method


# virtual methods
.method public andThen(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<W:",
            "Ljava/lang/Object;",
            ">(",
            "Lgroovy/lang/Closure<",
            "TW;>;)",
            "Lgroovy/lang/Closure<",
            "TW;>;"
        }
    .end annotation

    .line 672
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->rightShift(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1
.end method

.method public andThenSelf()Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 681
    invoke-virtual {p0, p0}, Lgroovy/lang/Closure;->andThen(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object v0

    return-object v0
.end method

.method public andThenSelf(I)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    if-nez p1, :cond_0

    return-object p0

    :cond_0
    const/4 v0, 0x1

    if-ne p1, v0, :cond_1

    .line 692
    invoke-virtual {p0, p0}, Lgroovy/lang/Closure;->andThen(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1

    :cond_1
    sub-int/2addr p1, v0

    .line 693
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->andThenSelf(I)Lgroovy/lang/Closure;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->andThen(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1
.end method

.method public asWritable()Lgroovy/lang/Closure;
    .locals 1

    .line 486
    new-instance v0, Lgroovy/lang/Closure$WritableClosure;

    invoke-direct {v0, p0}, Lgroovy/lang/Closure$WritableClosure;-><init>(Lgroovy/lang/Closure;)V

    return-object v0
.end method

.method public call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TV;"
        }
    .end annotation

    .line 405
    sget-object v0, Lgroovy/lang/Closure;->EMPTY_OBJECT_ARRAY:[Ljava/lang/Object;

    .line 406
    invoke-virtual {p0, v0}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public call(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    aput-object p1, v0, v1

    .line 428
    invoke-virtual {p0, v0}, Lgroovy/lang/Closure;->call([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public varargs call([Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 412
    :try_start_0
    invoke-virtual {p0}, Lgroovy/lang/Closure;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object v0

    const-string v1, "doCall"

    invoke-interface {v0, p0, v1, p1}, Lgroovy/lang/MetaClass;->invokeMethod(Ljava/lang/Object;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Lorg/codehaus/groovy/runtime/InvokerInvocationException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    .line 417
    invoke-static {p1}, Lgroovy/lang/Closure;->throwRuntimeException(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :catch_1
    move-exception p1

    .line 414
    invoke-virtual {p1}, Lorg/codehaus/groovy/runtime/InvokerInvocationException;->getCause()Ljava/lang/Throwable;

    move-result-object p1

    invoke-static {p1}, Lorg/apache/groovy/internal/util/UncheckedThrow;->rethrow(Ljava/lang/Throwable;)V

    const/4 p1, 0x0

    return-object p1
.end method

.method public clone()Ljava/lang/Object;
    .locals 1

    .line 885
    :try_start_0
    invoke-super {p0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/CloneNotSupportedException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v0

    :catch_0
    const/4 v0, 0x0

    return-object v0
.end method

.method public compose(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/lang/Closure;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 702
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->leftShift(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1
.end method

.method public composeSelf()Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 711
    invoke-virtual {p0, p0}, Lgroovy/lang/Closure;->compose(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object v0

    return-object v0
.end method

.method public composeSelf(I)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    if-nez p1, :cond_0

    return-object p0

    :cond_0
    const/4 v0, 0x1

    if-ne p1, v0, :cond_1

    .line 722
    invoke-virtual {p0, p0}, Lgroovy/lang/Closure;->compose(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1

    :cond_1
    sub-int/2addr p1, v0

    .line 723
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->composeSelf(I)Lgroovy/lang/Closure;

    move-result-object p1

    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->compose(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1
.end method

.method public curry(Ljava/lang/Object;)Lgroovy/lang/Closure;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    aput-object p1, v0, v1

    .line 543
    invoke-virtual {p0, v0}, Lgroovy/lang/Closure;->curry([Ljava/lang/Object;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1
.end method

.method public varargs curry([Ljava/lang/Object;)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 532
    new-instance v0, Lorg/codehaus/groovy/runtime/CurriedClosure;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/runtime/CurriedClosure;-><init>(Lgroovy/lang/Closure;[Ljava/lang/Object;)V

    return-object v0
.end method

.method public dehydrate()Lgroovy/lang/Closure;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 1084
    invoke-virtual {p0}, Lgroovy/lang/Closure;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovy/lang/Closure;

    const/4 v1, 0x0

    .line 1085
    iput-object v1, v0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    .line 1086
    iput-object v1, v0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    .line 1087
    iput-object v1, v0, Lgroovy/lang/Closure;->thisObject:Ljava/lang/Object;

    return-object v0
.end method

.method public getDelegate()Ljava/lang/Object;
    .locals 1

    .line 452
    iget-object v0, p0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    return-object v0
.end method

.method public getDirective()I
    .locals 1

    .line 1064
    iget v0, p0, Lgroovy/lang/Closure;->directive:I

    return v0
.end method

.method public getMaximumNumberOfParameters()I
    .locals 1

    .line 477
    iget v0, p0, Lgroovy/lang/Closure;->maximumNumberOfParameters:I

    return v0
.end method

.method public getOwner()Ljava/lang/Object;
    .locals 1

    .line 444
    iget-object v0, p0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    return-object v0
.end method

.method public getParameterTypes()[Ljava/lang/Class;
    .locals 1

    .line 469
    iget-object v0, p0, Lgroovy/lang/Closure;->parameterTypes:[Ljava/lang/Class;

    return-object v0
.end method

.method public getProperty(Ljava/lang/String;)Ljava/lang/Object;
    .locals 2

    const-string v0, "delegate"

    .line 267
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 268
    invoke-virtual {p0}, Lgroovy/lang/Closure;->getDelegate()Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_0
    const-string v0, "owner"

    .line 270
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 271
    invoke-virtual {p0}, Lgroovy/lang/Closure;->getOwner()Ljava/lang/Object;

    move-result-object p1

    return-object p1

    :cond_1
    const-string v0, "maximumNumberOfParameters"

    .line 273
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 274
    invoke-virtual {p0}, Lgroovy/lang/Closure;->getMaximumNumberOfParameters()I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1

    :cond_2
    const-string v0, "parameterTypes"

    .line 276
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 277
    invoke-virtual {p0}, Lgroovy/lang/Closure;->getParameterTypes()[Ljava/lang/Class;

    move-result-object p1

    return-object p1

    :cond_3
    const-string v0, "metaClass"

    .line 279
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 280
    invoke-virtual {p0}, Lgroovy/lang/Closure;->getMetaClass()Lgroovy/lang/MetaClass;

    move-result-object p1

    return-object p1

    :cond_4
    const-string v0, "class"

    .line 282
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 283
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    return-object p1

    :cond_5
    const-string v0, "directive"

    .line 285
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_6

    .line 286
    invoke-virtual {p0}, Lgroovy/lang/Closure;->getDirective()I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1

    :cond_6
    const-string v0, "resolveStrategy"

    .line 288
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_7

    .line 289
    invoke-virtual {p0}, Lgroovy/lang/Closure;->getResolveStrategy()I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    return-object p1

    :cond_7
    const-string v0, "thisObject"

    .line 291
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_8

    .line 292
    invoke-virtual {p0}, Lgroovy/lang/Closure;->getThisObject()Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 294
    :cond_8
    iget v0, p0, Lgroovy/lang/Closure;->resolveStrategy:I

    const/4 v1, 0x1

    if-eq v0, v1, :cond_c

    const/4 v1, 0x2

    if-eq v0, v1, :cond_b

    const/4 v1, 0x3

    if-eq v0, v1, :cond_a

    const/4 v1, 0x4

    if-eq v0, v1, :cond_9

    .line 304
    invoke-direct {p0, p1}, Lgroovy/lang/Closure;->getPropertyOwnerFirst(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 302
    :cond_9
    invoke-super {p0, p1}, Lgroovy/lang/GroovyObjectSupport;->getProperty(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 298
    :cond_a
    iget-object v0, p0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 300
    :cond_b
    iget-object v0, p0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    invoke-static {v0, p1}, Lorg/codehaus/groovy/runtime/InvokerHelper;->getProperty(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1

    .line 296
    :cond_c
    invoke-direct {p0, p1}, Lgroovy/lang/Closure;->getPropertyDelegateFirst(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public getResolveStrategy()I
    .locals 1

    .line 259
    iget v0, p0, Lgroovy/lang/Closure;->resolveStrategy:I

    return v0
.end method

.method public getThisObject()Ljava/lang/Object;
    .locals 1

    .line 263
    iget-object v0, p0, Lgroovy/lang/Closure;->thisObject:Ljava/lang/Object;

    return-object v0
.end method

.method public isCase(Ljava/lang/Object;)Z
    .locals 3

    .line 393
    iget-object v0, p0, Lgroovy/lang/Closure;->bcw:Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;

    if-nez v0, :cond_0

    .line 394
    new-instance v0, Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;

    invoke-direct {v0, p0}, Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;-><init>(Lgroovy/lang/Closure;)V

    iput-object v0, p0, Lgroovy/lang/Closure;->bcw:Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;

    .line 396
    :cond_0
    iget-object v0, p0, Lgroovy/lang/Closure;->bcw:Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;

    const/4 v1, 0x1

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    invoke-virtual {v0, v1}, Lorg/codehaus/groovy/runtime/callsite/BooleanClosureWrapper;->call([Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public leftShift(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lgroovy/lang/Closure;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 663
    new-instance v0, Lorg/codehaus/groovy/runtime/ComposedClosure;

    invoke-direct {v0, p1, p0}, Lorg/codehaus/groovy/runtime/ComposedClosure;-><init>(Lgroovy/lang/Closure;Lgroovy/lang/Closure;)V

    return-object v0
.end method

.method public leftShift(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")TV;"
        }
    .end annotation

    .line 740
    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->call(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public memoize()Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 758
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/ConcurrentCommonCache;

    invoke-direct {v0}, Lorg/codehaus/groovy/runtime/memoize/ConcurrentCommonCache;-><init>()V

    invoke-static {v0, p0}, Lorg/codehaus/groovy/runtime/memoize/Memoize;->buildMemoizeFunction(Lorg/codehaus/groovy/runtime/memoize/MemoizeCache;Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object v0

    return-object v0
.end method

.method public memoizeAtLeast(I)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    if-ltz p1, :cond_0

    .line 807
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/ConcurrentSoftCache;

    invoke-direct {v0}, Lorg/codehaus/groovy/runtime/memoize/ConcurrentSoftCache;-><init>()V

    invoke-static {p1, v0, p0}, Lorg/codehaus/groovy/runtime/memoize/Memoize;->buildSoftReferenceMemoizeFunction(ILorg/codehaus/groovy/runtime/memoize/MemoizeCache;Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1

    .line 805
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "A non-negative number is required as the protectedCacheSize parameter for memoizeAtLeast."

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public memoizeAtMost(I)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    if-ltz p1, :cond_0

    .line 780
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/LRUCache;

    invoke-direct {v0, p1}, Lorg/codehaus/groovy/runtime/memoize/LRUCache;-><init>(I)V

    invoke-static {v0, p0}, Lorg/codehaus/groovy/runtime/memoize/Memoize;->buildMemoizeFunction(Lorg/codehaus/groovy/runtime/memoize/MemoizeCache;Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1

    .line 778
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "A non-negative number is required as the maxCacheSize parameter for memoizeAtMost."

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public memoizeBetween(II)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II)",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    if-ltz p1, :cond_2

    if-ltz p2, :cond_1

    if-gt p1, p2, :cond_0

    .line 839
    new-instance v0, Lorg/codehaus/groovy/runtime/memoize/ConcurrentSoftCache;

    invoke-direct {v0, p2}, Lorg/codehaus/groovy/runtime/memoize/ConcurrentSoftCache;-><init>(I)V

    invoke-static {p1, v0, p0}, Lorg/codehaus/groovy/runtime/memoize/Memoize;->buildSoftReferenceMemoizeFunction(ILorg/codehaus/groovy/runtime/memoize/MemoizeCache;Lgroovy/lang/Closure;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1

    .line 837
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "The maxCacheSize parameter to memoizeBetween is required to be greater or equal to the protectedCacheSize parameter."

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 836
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "A non-negative number is required as the maxCacheSize parameter for memoizeBetween."

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 835
    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "A non-negative number is required as the protectedCacheSize parameter for memoizeBetween."

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public ncurry(ILjava/lang/Object;)Lgroovy/lang/Closure;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    aput-object p2, v0, v1

    .line 625
    invoke-virtual {p0, p1, v0}, Lgroovy/lang/Closure;->ncurry(I[Ljava/lang/Object;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1
.end method

.method public varargs ncurry(I[Ljava/lang/Object;)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I[",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 614
    new-instance v0, Lorg/codehaus/groovy/runtime/CurriedClosure;

    invoke-direct {v0, p1, p0, p2}, Lorg/codehaus/groovy/runtime/CurriedClosure;-><init>(ILgroovy/lang/Closure;[Ljava/lang/Object;)V

    return-object v0
.end method

.method public rcurry(Ljava/lang/Object;)Lgroovy/lang/Closure;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    const/4 v1, 0x0

    aput-object p1, v0, v1

    .line 576
    invoke-virtual {p0, v0}, Lgroovy/lang/Closure;->rcurry([Ljava/lang/Object;)Lgroovy/lang/Closure;

    move-result-object p1

    return-object p1
.end method

.method public varargs rcurry([Ljava/lang/Object;)Lgroovy/lang/Closure;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 565
    new-instance v0, Lorg/codehaus/groovy/runtime/CurriedClosure;

    array-length v1, p1

    neg-int v1, v1

    invoke-direct {v0, v1, p0, p1}, Lorg/codehaus/groovy/runtime/CurriedClosure;-><init>(ILgroovy/lang/Closure;[Ljava/lang/Object;)V

    return-object v0
.end method

.method public rehydrate(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 1105
    invoke-virtual {p0}, Lgroovy/lang/Closure;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lgroovy/lang/Closure;

    .line 1106
    iput-object p1, v0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    .line 1107
    iput-object p2, v0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    .line 1108
    iput-object p3, v0, Lgroovy/lang/Closure;->thisObject:Ljava/lang/Object;

    return-object v0
.end method

.method public rightShift(Lgroovy/lang/Closure;)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<W:",
            "Ljava/lang/Object;",
            ">(",
            "Lgroovy/lang/Closure<",
            "TW;>;)",
            "Lgroovy/lang/Closure<",
            "TW;>;"
        }
    .end annotation

    .line 644
    new-instance v0, Lorg/codehaus/groovy/runtime/ComposedClosure;

    invoke-direct {v0, p0, p1}, Lorg/codehaus/groovy/runtime/ComposedClosure;-><init>(Lgroovy/lang/Closure;Lgroovy/lang/Closure;)V

    return-object v0
.end method

.method public run()V
    .locals 0

    .line 493
    invoke-virtual {p0}, Lgroovy/lang/Closure;->call()Ljava/lang/Object;

    return-void
.end method

.method public setDelegate(Ljava/lang/Object;)V
    .locals 0

    .line 461
    iput-object p1, p0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    return-void
.end method

.method public setDirective(I)V
    .locals 0

    .line 1071
    iput p1, p0, Lgroovy/lang/Closure;->directive:I

    return-void
.end method

.method public setProperty(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2

    const-string v0, "delegate"

    .line 337
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 338
    invoke-virtual {p0, p2}, Lgroovy/lang/Closure;->setDelegate(Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    const-string v0, "metaClass"

    .line 339
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 340
    check-cast p2, Lgroovy/lang/MetaClass;

    invoke-virtual {p0, p2}, Lgroovy/lang/Closure;->setMetaClass(Lgroovy/lang/MetaClass;)V

    goto :goto_0

    :cond_1
    const-string v0, "resolveStrategy"

    .line 341
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 342
    check-cast p2, Ljava/lang/Number;

    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->setResolveStrategy(I)V

    goto :goto_0

    :cond_2
    const-string v0, "directive"

    .line 343
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 344
    check-cast p2, Ljava/lang/Number;

    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p1

    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->setDirective(I)V

    goto :goto_0

    .line 346
    :cond_3
    iget v0, p0, Lgroovy/lang/Closure;->resolveStrategy:I

    const/4 v1, 0x1

    if-eq v0, v1, :cond_7

    const/4 v1, 0x2

    if-eq v0, v1, :cond_6

    const/4 v1, 0x3

    if-eq v0, v1, :cond_5

    const/4 v1, 0x4

    if-eq v0, v1, :cond_4

    .line 360
    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;->setPropertyOwnerFirst(Ljava/lang/String;Ljava/lang/Object;)V

    goto :goto_0

    .line 357
    :cond_4
    invoke-super {p0, p1, p2}, Lgroovy/lang/GroovyObjectSupport;->setProperty(Ljava/lang/String;Ljava/lang/Object;)V

    goto :goto_0

    .line 351
    :cond_5
    iget-object v0, p0, Lgroovy/lang/Closure;->delegate:Ljava/lang/Object;

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    goto :goto_0

    .line 354
    :cond_6
    iget-object v0, p0, Lgroovy/lang/Closure;->owner:Ljava/lang/Object;

    invoke-static {v0, p1, p2}, Lorg/codehaus/groovy/runtime/InvokerHelper;->setProperty(Ljava/lang/Object;Ljava/lang/String;Ljava/lang/Object;)V

    goto :goto_0

    .line 348
    :cond_7
    invoke-direct {p0, p1, p2}, Lgroovy/lang/Closure;->setPropertyDelegateFirst(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public setResolveStrategy(I)V
    .locals 0

    .line 244
    iput p1, p0, Lgroovy/lang/Closure;->resolveStrategy:I

    return-void
.end method

.method public trampoline()Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 877
    new-instance v0, Lgroovy/lang/TrampolineClosure;

    invoke-direct {v0, p0}, Lgroovy/lang/TrampolineClosure;-><init>(Lgroovy/lang/Closure;)V

    return-object v0
.end method

.method public varargs trampoline([Ljava/lang/Object;)Lgroovy/lang/Closure;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Ljava/lang/Object;",
            ")",
            "Lgroovy/lang/Closure<",
            "TV;>;"
        }
    .end annotation

    .line 863
    new-instance v0, Lgroovy/lang/TrampolineClosure;

    invoke-virtual {p0, p1}, Lgroovy/lang/Closure;->curry([Ljava/lang/Object;)Lgroovy/lang/Closure;

    move-result-object p1

    invoke-direct {v0, p1}, Lgroovy/lang/TrampolineClosure;-><init>(Lgroovy/lang/Closure;)V

    return-object v0
.end method
