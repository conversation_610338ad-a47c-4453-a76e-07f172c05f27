.class public final synthetic Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda1;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lgroovy/lang/ExpandoMetaClass$Callable;


# instance fields
.field public final synthetic f$0:Lgroovy/lang/ExpandoMetaClass;

.field public final synthetic f$1:Ljava/lang/Object;

.field public final synthetic f$2:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lgroovy/lang/ExpandoMetaClass;Ljava/lang/Object;Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda1;->f$0:Lgroovy/lang/ExpandoMetaClass;

    iput-object p2, p0, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda1;->f$1:Ljava/lang/Object;

    iput-object p3, p0, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda1;->f$2:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final call()V
    .locals 3

    iget-object v0, p0, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda1;->f$0:Lgroovy/lang/ExpandoMetaClass;

    iget-object v1, p0, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda1;->f$1:Ljava/lang/Object;

    iget-object v2, p0, Lgroovy/lang/ExpandoMetaClass$$ExternalSyntheticLambda1;->f$2:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Lgroovy/lang/ExpandoMetaClass;->lambda$registerBeanProperty$0$groovy-lang-ExpandoMetaClass(Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method
